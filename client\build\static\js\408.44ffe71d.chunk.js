"use strict";(self.webpackChunkclient=self.webpackChunkclient||[]).push([[408],{9941:(e,s,a)=>{a.d(s,{q:()=>l});const{default:t}=a(3371),l=async e=>{try{return(await t.get("/api/plans",e)).data}catch(s){return s.response.data}}},272:(e,s,a)=>{a.d(s,{$s:()=>c,I1:()=>n,Ss:()=>r,cq:()=>l,dM:()=>o,uH:()=>i});const{default:t}=a(3371),l=async e=>{try{return(await t.post("/api/reports/add-report",e)).data}catch(s){return s.response.data}},n=async e=>{try{return(await t.post("/api/reports/get-all-reports",e)).data}catch(s){return s.response.data}},r=async e=>{try{return(await t.post("/api/reports/get-all-reports-by-user",e)).data}catch(s){return s.response.data}},i=async e=>{try{return(await t.get("/api/reports/get-all-reports-for-ranking")).data}catch(s){return s.response.data}},o=async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};try{const s=new URLSearchParams;e.limit&&s.append("limit",e.limit),e.classFilter&&s.append("classFilter",e.classFilter),e.levelFilter&&s.append("levelFilter",e.levelFilter),e.seasonFilter&&s.append("seasonFilter",e.seasonFilter),e.includeInactive&&s.append("includeInactive",e.includeInactive);return(await t.get("/api/quiz/xp-leaderboard?".concat(s.toString()))).data}catch(s){return s.response.data}},c=async function(e){let s=arguments.length>1&&void 0!==arguments[1]?arguments[1]:5;try{return(await t.get("/api/quiz/user-ranking/".concat(e,"?context=").concat(s))).data}catch(a){return a.response.data}}},640:(e,s,a)=>{a.d(s,{Z:()=>n});var t=a(2791),l=a(184);const n=function(e){let{title:s}=e;const[a,n]=(0,t.useState)(!1);return(0,t.useEffect)((()=>{window.innerWidth<768&&n(!0)}),[]),(0,l.jsx)("div",{className:"mt-2",children:(0,l.jsx)("h1",{className:a?"text-lg":"",children:s})})}},8155:(e,s,a)=>{a.d(s,{Z:()=>g});var t=a(2791),l=a(6275),n=a(5273),r=a(7309),i=a(7455),o=a(7462);const c={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M699 353h-46.9c-10.2 0-19.9 4.9-25.9 13.3L469 584.3l-71.2-98.8c-6-8.3-15.6-13.3-25.9-13.3H325c-6.5 0-10.3 7.4-6.5 12.7l124.6 172.8a31.8 31.8 0 0051.7 0l210.6-292c3.9-5.3.1-12.7-6.4-12.7z"}},{tag:"path",attrs:{d:"M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372z"}}]},name:"check-circle",theme:"outlined"};var d=a(4291),m=function(e,s){return t.createElement(d.Z,(0,o.Z)({},e,{ref:s,icon:c}))};const u=t.forwardRef(m);var x=a(9168),h=a(50),p=a(184);const g=e=>{var s;let{visible:a,onClose:t,currentPlan:o,subscription:c,user:d}=e;const m=(()=>{if(null===c||void 0===c||!c.endDate)return 0;const e=new Date(c.endDate)-new Date,s=Math.ceil(e/864e5);return Math.max(0,s)})(),g=(()=>{if(null===c||void 0===c||!c.startDate||null===c||void 0===c||!c.endDate)return 0;const e=new Date(c.startDate),s=(new Date(c.endDate)-e)/864e5,a=(new Date-e)/864e5;return Math.min(100,Math.max(0,a/s*100))})(),v=(null===o||void 0===o?void 0:o.title)||(null===c||void 0===c?void 0:c.planTitle)||"Premium Plan",b=null!==c&&void 0!==c&&c.endDate?new Date(c.endDate).toLocaleDateString():"N/A";return(0,p.jsx)(l.Z,{open:a,onCancel:t,footer:null,width:500,centered:!0,className:"upgrade-restriction-modal",maskStyle:{backgroundColor:"rgba(0, 0, 0, 0.7)"},children:(0,p.jsxs)("div",{className:"upgrade-restriction-content",children:[(0,p.jsxs)("div",{className:"modal-header",children:[(0,p.jsx)("div",{className:"crown-icon",children:(0,p.jsx)(i.Z,{})}),(0,p.jsx)("h2",{className:"modal-title",children:"Already Premium Member!"}),(0,p.jsx)("p",{className:"modal-subtitle",children:"You're currently enjoying premium features"})]}),(0,p.jsxs)("div",{className:"current-plan-card",children:[(0,p.jsxs)("div",{className:"plan-header",children:[(0,p.jsx)("div",{className:"plan-icon",children:(0,p.jsx)(u,{})}),(0,p.jsxs)("div",{className:"plan-info",children:[(0,p.jsx)("h3",{className:"plan-name",children:v}),(0,p.jsx)("span",{className:"plan-status",children:"Active Subscription"})]})]}),(0,p.jsxs)("div",{className:"progress-section",children:[(0,p.jsxs)("div",{className:"progress-header",children:[(0,p.jsx)("span",{className:"progress-label",children:"Subscription Progress"}),(0,p.jsxs)("span",{className:"days-remaining",children:[m," days remaining"]})]}),(0,p.jsx)(n.Z,{percent:g,strokeColor:{"0%":"#52c41a","50%":"#faad14","100%":"#ff4d4f"},trailColor:"#f0f0f0",strokeWidth:8,showInfo:!1})]}),(0,p.jsxs)("div",{className:"subscription-details",children:[(0,p.jsxs)("div",{className:"detail-item",children:[(0,p.jsx)(x.Z,{className:"detail-icon"}),(0,p.jsxs)("div",{className:"detail-content",children:[(0,p.jsx)("span",{className:"detail-label",children:"Expires On"}),(0,p.jsx)("span",{className:"detail-value",children:b})]})]}),(0,p.jsxs)("div",{className:"detail-item",children:[(0,p.jsx)(h.Z,{className:"detail-icon"}),(0,p.jsxs)("div",{className:"detail-content",children:[(0,p.jsx)("span",{className:"detail-label",children:"Time Remaining"}),(0,p.jsx)("span",{className:"detail-value",children:m>0?"".concat(m," days"):"Expired"})]})]})]})]}),(0,p.jsxs)("div",{className:"message-section",children:[(0,p.jsxs)("div",{className:"message-card",children:[(0,p.jsx)("h4",{className:"message-title",children:"\ud83c\udf89 You're All Set!"}),(0,p.jsxs)("p",{className:"message-text",children:["You're currently enjoying all premium features with your ",(0,p.jsx)("strong",{children:v}),". To upgrade to a different plan, please wait until your current subscription expires."]})]}),(0,p.jsxs)("div",{className:"benefits-list",children:[(0,p.jsx)("h5",{className:"benefits-title",children:"Your Current Benefits:"}),(0,p.jsx)("ul",{className:"benefits",children:(null===o||void 0===o||null===(s=o.features)||void 0===s?void 0:s.slice(0,4).map(((e,s)=>(0,p.jsxs)("li",{className:"benefit-item",children:[(0,p.jsx)(u,{className:"benefit-icon"}),e]},s))))||[(0,p.jsxs)("li",{className:"benefit-item",children:[(0,p.jsx)(u,{className:"benefit-icon"}),"Full access to all features"]},"1"),(0,p.jsxs)("li",{className:"benefit-item",children:[(0,p.jsx)(u,{className:"benefit-icon"}),"Unlimited quizzes and practice"]},"2"),(0,p.jsxs)("li",{className:"benefit-item",children:[(0,p.jsx)(u,{className:"benefit-icon"}),"AI chat assistance"]},"3"),(0,p.jsxs)("li",{className:"benefit-item",children:[(0,p.jsx)(u,{className:"benefit-icon"}),"Premium study materials"]},"4")]})]})]}),(0,p.jsxs)("div",{className:"action-buttons",children:[(0,p.jsx)(r.ZP,{type:"primary",size:"large",onClick:t,className:"continue-button",children:"Continue Learning"}),(0,p.jsx)(r.ZP,{type:"default",size:"large",onClick:t,className:"close-button",children:"Close"})]}),(0,p.jsx)("div",{className:"footer-note",children:(0,p.jsxs)("p",{children:["\ud83d\udca1 ",(0,p.jsx)("strong",{children:"Tip:"})," You can upgrade to a different plan after your current subscription expires on ",(0,p.jsx)("strong",{children:b})]})})]})})}},5408:(e,s,a)=>{a.r(s),a.d(s,{default:()=>b});var t=a(1413),l=a(2791),n=(a(640),a(8262)),r=a(7027),i=a(6275),o=a(9434),c=a(8247),d=a(272),m=a(2048),u=a(9941),x=a(1169),h=(a(3371),a(4085)),p=(a(9114),a(8155)),g=a(184);const v=e=>{var s,a;let{isOpen:t,onClose:n,onSuccess:i}=e;const[d,m]=(0,l.useState)([]),[v,b]=(0,l.useState)(null),[j,f]=(0,l.useState)(!1),[N,y]=(0,l.useState)(!1),[w,P]=(0,l.useState)("plans"),[k,S]=(0,l.useState)(!1),[C,Z]=(0,l.useState)(null),[D,M]=(0,l.useState)(!1),I=()=>{const e=null===E||void 0===E?void 0:E.phoneNumber;return e&&/^(06|07)\d{8}$/.test(e)},{user:E}=(0,o.v9)((e=>e.user)),{subscriptionData:A}=(0,o.v9)((e=>e.subscription)),z=(0,o.I0)();(0,l.useEffect)((()=>{t&&L()}),[t]);const L=async()=>{try{f(!0);const e=await(0,u.q)();m(Array.isArray(e)?e:[])}catch(e){console.error("Error fetching plans:",e),r.ZP.error("Failed to load subscription plans")}finally{f(!1)}},T=async()=>{if(v)if(I())try{var e;y(!0),M(!1),Z(Date.now()),z((0,c.YC)());setTimeout((()=>{M(!0)}),1e4);const s={plan:v,userId:E._id,userPhone:E.phoneNumber,userEmail:E.email||"".concat(null===(e=E.name)||void 0===e?void 0:e.replace(/\s+/g,"").toLowerCase(),"@brainwave.temp")},a=await(0,x._)(s);if(!a.success)throw new Error(a.message||"Payment failed");r.ZP.success("Payment initiated! Please check your phone for SMS confirmation."),n(),U(a.order_id)}catch(s){console.error("Payment error:",s),r.ZP.error(s.message||"Payment failed. Please try again.")}finally{y(!1),z((0,c.Ir)())}else r.ZP.error("Please update your phone number in your profile first. Go to Profile \u2192 Edit \u2192 Phone Number");else r.ZP.error("Please select a plan first")},U=async e=>{let s=0;const a=async()=>{try{s++,console.log("\ud83d\udd0d Checking payment status... Attempt ".concat(s,"/").concat(150));const e=await(0,x.W)();if(console.log("\ud83d\udce5 Payment status response:",e),e&&e.error){if("ENDPOINT_NOT_FOUND"===e.error)return console.error("\u274c Payment status endpoint not found"),r.ZP.error("Payment verification service is temporarily unavailable. Please contact support."),void P("plans");if("AUTH_REQUIRED"===e.error)return console.error("\u274c Authentication required for payment status"),r.ZP.error("Please login again to check payment status."),void P("plans")}if(e&&!e.error&&("paid"===e.paymentStatus&&"active"===e.status||"completed"===e.status&&!0===e.success))return console.log("\u2705 Payment confirmed! Showing success instantly..."),z((0,h.k)(e)),r.ZP.success({content:"\ud83c\udf89 Payment Confirmed! Welcome to Premium!",duration:5,style:{marginTop:"20vh",fontSize:"16px",fontWeight:"600"}}),i&&i(),n(),!0;if(s>=150)return console.log("\u23f0 Payment check timeout reached"),r.ZP.warning({content:"Payment is still processing. Your subscription will activate automatically when payment is complete.",duration:8}),!1;setTimeout(a,2e3)}catch(e){console.error("\u274c Error checking payment status:",e),s>=150?r.ZP.error("Unable to verify payment. Please contact support if payment was completed."):setTimeout(a,2e3)}};a()};return t?(0,g.jsxs)("div",{className:"subscription-modal-overlay",children:[(0,g.jsxs)("div",{className:"subscription-modal",children:[(0,g.jsxs)("div",{className:"modal-header",children:[(0,g.jsxs)("h2",{className:"modal-title",children:["plans"===w&&"\ud83d\ude80 Choose Your Learning Plan","payment"===w&&"\ud83d\udcb3 Complete Your Payment"]}),(0,g.jsx)("button",{className:"close-button",onClick:()=>{P("plans"),b(null),y(!1),n()},children:"\xd7"})]}),(0,g.jsxs)("div",{className:"modal-content",children:["plans"===w&&(0,g.jsx)("div",{className:"plans-grid",children:j?(0,g.jsxs)("div",{className:"loading-state",children:[(0,g.jsx)("div",{className:"spinner"}),(0,g.jsx)("p",{children:"Loading plans..."})]}):d.map((e=>{var s,a,t,l;return(0,g.jsxs)("div",{className:"plan-card",onClick:()=>(e=>{if(A&&"active"===A.status&&"paid"===A.paymentStatus)return console.log("\ud83d\udeab User already has active subscription:",A),void S(!0);b(e),P("payment")})(e),children:[(0,g.jsxs)("div",{className:"plan-header",children:[(0,g.jsx)("h3",{className:"plan-title",children:e.title}),(null===(s=e.title)||void 0===s?void 0:s.toLowerCase().includes("standard"))&&(0,g.jsx)("span",{className:"plan-badge",children:"\ud83d\udd25 Popular"})]}),(0,g.jsxs)("div",{className:"plan-price",children:[(0,g.jsxs)("span",{className:"price-amount",children:[null===(a=e.discountedPrice)||void 0===a?void 0:a.toLocaleString()," TZS"]}),e.actualPrice&&e.actualPrice!==e.discountedPrice&&(0,g.jsxs)("span",{className:"price-original",children:[e.actualPrice.toLocaleString()," TZS"]}),(0,g.jsxs)("span",{className:"price-period",children:[e.duration," month",e.duration>1?"s":""]})]}),(0,g.jsxs)("div",{className:"plan-features",children:[null===(t=e.features)||void 0===t?void 0:t.slice(0,4).map(((e,s)=>(0,g.jsxs)("div",{className:"feature",children:[(0,g.jsx)("span",{className:"feature-icon",children:"\u2713"}),(0,g.jsx)("span",{className:"feature-text",children:e})]},s))),(null===(l=e.features)||void 0===l?void 0:l.length)>4&&(0,g.jsxs)("div",{className:"feature",children:[(0,g.jsx)("span",{className:"feature-icon",children:"+"}),(0,g.jsxs)("span",{className:"feature-text",children:[e.features.length-4," more features"]})]})]}),(0,g.jsxs)("button",{className:"select-plan-btn",children:["Choose ",e.title]})]},e._id)}))}),"payment"===w&&v&&(0,g.jsxs)("div",{className:"payment-step",children:[(0,g.jsxs)("div",{className:"selected-plan-summary",children:[(0,g.jsxs)("h3",{children:["Selected Plan: ",v.title]}),(0,g.jsxs)("p",{className:"plan-price-summary",children:[null===(s=v.discountedPrice)||void 0===s?void 0:s.toLocaleString()," TZS for ",v.duration," month",v.duration>1?"s":""]})]}),(0,g.jsxs)("div",{className:"payment-info",children:[(0,g.jsxs)("div",{className:"info-item",children:[(0,g.jsx)("span",{className:"info-label",children:"Phone Number:"}),(0,g.jsx)("div",{className:"phone-display-simple",children:I()?(0,g.jsxs)("span",{className:"info-value valid-phone",children:[E.phoneNumber," \u2705"]}):(0,g.jsxs)("div",{className:"invalid-phone-warning",children:[(0,g.jsxs)("span",{className:"info-value invalid-phone",children:[(null===E||void 0===E?void 0:E.phoneNumber)||"No phone number set"," \u274c"]}),(0,g.jsx)("button",{className:"update-phone-btn",onClick:()=>{r.ZP.info("Redirecting to profile to update phone number..."),setTimeout((()=>{window.open("/user/profile","_blank")}),1e3)},children:"Update in Profile"})]})})]}),(0,g.jsxs)("div",{className:"info-item",children:[(0,g.jsx)("span",{className:"info-label",children:"Payment Method:"}),(0,g.jsx)("span",{className:"info-value",children:"Mobile Money (M-Pesa, Tigo Pesa, Airtel Money)"})]}),I()&&(0,g.jsx)("div",{className:"payment-note",children:(0,g.jsx)("p",{children:"\ud83d\udca1 Payment SMS will be sent to your phone number above."})})]}),(0,g.jsxs)("div",{className:"payment-actions",children:[(0,g.jsx)("button",{className:"back-btn",onClick:()=>P("plans"),children:"\u2190 Back to Plans"}),(0,g.jsx)("button",{className:"pay-btn",onClick:T,disabled:N||!I(),children:N?(0,g.jsxs)(g.Fragment,{children:[(0,g.jsx)("span",{className:"btn-spinner"}),"Processing..."]}):I()?"Pay ".concat(null===(a=v.discountedPrice)||void 0===a?void 0:a.toLocaleString()," TZS"):"Update phone number first"})]})]})]})]}),(0,g.jsx)(p.Z,{visible:k,onClose:()=>S(!1),currentPlan:d.find((e=>e._id===(null===A||void 0===A?void 0:A.activePlan)))||(null===A||void 0===A?void 0:A.plan),subscription:A,user:E})]}):null},b=()=>{var e,s,a,u,x,h,p,b,j;const[f,N]=(0,l.useState)(null),[y,w]=(0,l.useState)(null),[P,k]=(0,l.useState)(null),[S,C]=(0,l.useState)(null),[Z,D]=(0,l.useState)(!1),[M,I]=(0,l.useState)(null),[E,A]=(0,l.useState)({name:"",email:"",school:"",level:"",class_:"",phoneNumber:""}),[z,L]=(0,l.useState)(null),[T,U]=(0,l.useState)(!1),[F,_]=(0,l.useState)(!1),[Y,R]=(0,l.useState)(null),O=(0,o.I0)(),{subscriptionData:B}=(0,o.v9)((e=>e.subscription));(0,l.useEffect)((()=>{y&&f&&(()=>{const e=y.map(((e,s)=>({user:e,ranking:s+1}))).filter((e=>e.user.userId.includes(f._id)));k(e)})()}),[y,f]);const H=async()=>{O((0,c.YC)());try{const e=await(0,n.bG)();e.success?(N(e.data),A({name:e.data.name||"",email:e.data.email||"",school:e.data.school||"",class_:e.data.class||"",level:e.data.level||"",phoneNumber:e.data.phoneNumber||""}),e.data.profileImage&&L(e.data.profileImage),(async()=>{try{const e=await(0,d.uH)();e.success?w(e.data):r.ZP.error(e.message),O((0,c.Ir)())}catch(e){r.ZP.error(e.message),O((0,c.Ir)())}})()):r.ZP.error(e.message)}catch(e){r.ZP.error(e.message)}finally{O((0,c.Ir)())}};(0,l.useEffect)((()=>{localStorage.getItem("token")&&H()}),[]);const q=e=>{const{name:s,value:a}=e.target;if(!("phoneNumber"===s&&a.length>10))return"level"===s&&a!==(null===f||void 0===f?void 0:f.level)&&""!==a?(R(a),void _(!0)):void A((e=>(0,t.Z)((0,t.Z)({},e),{},{[s]:a},"level"===s?{class_:""}:{})))},V=async e=>{const s=e.target.files[0];if(s){if(!s.type.startsWith("image/"))return void r.ZP.error("Please select a valid image file");if(s.size>5242880)return void r.ZP.error("Image size should be less than 5MB");L(s);const e=new FileReader;e.onloadend=()=>I(e.result),e.readAsDataURL(s);const t=new FormData;t.append("profileImage",s),O((0,c.YC)());try{const e=await(0,n.DO)(t);O((0,c.Ir)()),e.success?(r.ZP.success("Profile picture updated successfully!"),H()):r.ZP.error(e.message)}catch(a){O((0,c.Ir)()),r.ZP.error(a.message||"Failed to update profile picture")}}};return(0,l.useEffect)((()=>{H()}),[]),(0,l.useEffect)((()=>{f&&(async()=>{if(null!==f&&void 0!==f&&f._id)try{O((0,c.YC)());const e=await(0,d.$s)(f._id,5);e.success&&C(e.data);const s=await(0,d.dM)({limit:1e3,levelFilter:(null===f||void 0===f?void 0:f.level)||"all"});if(s.success){const e=s.data.findIndex((e=>e._id===f._id));if(e>=0){const a=(0,t.Z)((0,t.Z)({},s.data[e]),{},{rank:e+1,totalUsers:s.data.length});C((l=>(0,t.Z)((0,t.Z)({},l),{},{userRank:e+1,totalUsers:s.data.length,user:a})))}}O((0,c.Ir)())}catch(e){O((0,c.Ir)()),console.error("Error fetching ranking data:",e)}})()}),[f]),(0,l.useEffect)((()=>{f&&A({name:f.name||"",email:f.email||"",school:f.school||"",class_:f.class||"",level:f.level||"",phoneNumber:f.phoneNumber||""})}),[f]),(0,g.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50",children:[(0,g.jsx)("div",{className:"container mx-auto px-4 py-8",children:(0,g.jsxs)("div",{className:"max-w-4xl mx-auto",children:[(0,g.jsxs)("div",{className:"text-center mb-8",children:[(0,g.jsx)("h1",{className:"text-4xl font-bold text-gray-900 mb-2",children:"Profile"}),(0,g.jsx)("p",{className:"text-gray-600",children:"Manage your account settings and preferences"}),(0,g.jsx)("div",{className:"relative mt-8 flex justify-center",children:(0,g.jsxs)("div",{className:"relative",children:[(0,g.jsx)(m.Z,{user:f,size:"3xl",showOnlineStatus:!0,onClick:()=>document.getElementById("profileImageInput").click(),className:"hover:scale-105 transition-transform duration-200",style:{width:"120px",height:"120px",border:"4px solid #BFDBFE",boxShadow:"0 4px 12px rgba(0,0,0,0.15)"}}),(0,g.jsx)("div",{className:"absolute bottom-2 right-2 bg-blue-600 rounded-full p-2 shadow-lg cursor-pointer hover:bg-blue-700 transition-colors duration-200",onClick:()=>document.getElementById("profileImageInput").click(),children:(0,g.jsxs)("svg",{className:"w-5 h-5 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:[(0,g.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M3 9a2 2 0 012-2h.93a2 2 0 001.664-.89l.812-1.22A2 2 0 0110.07 4h3.86a2 2 0 011.664.89l.812 1.22A2 2 0 0018.07 7H19a2 2 0 012 2v9a2 2 0 01-2 2H5a2 2 0 01-2-2V9z"}),(0,g.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15 13a3 3 0 11-6 0 3 3 0 016 0z"})]})}),(0,g.jsx)("input",{id:"profileImageInput",type:"file",accept:"image/*",className:"hidden",onChange:V})]})})]}),(0,g.jsx)("div",{className:"bg-white rounded-2xl shadow-xl overflow-hidden",children:(0,g.jsxs)("div",{className:"p-8",children:[(0,g.jsxs)("div",{className:"flex flex-col items-center mb-8",children:[(0,g.jsxs)("div",{className:"flex flex-wrap justify-center gap-4 text-center mb-6",children:[(0,g.jsxs)("div",{className:"bg-blue-50 rounded-lg px-4 py-3 border border-blue-200 min-w-[120px]",children:[(0,g.jsx)("p",{className:"text-sm text-blue-600 font-medium",children:"Name"}),(0,g.jsx)("p",{className:"text-lg font-bold text-gray-900",children:(null===f||void 0===f?void 0:f.name)||"User"})]}),(0,g.jsxs)("div",{className:"bg-green-50 rounded-lg px-4 py-3 border border-green-200 min-w-[120px]",children:[(0,g.jsx)("p",{className:"text-sm text-green-600 font-medium",children:"Username"}),(0,g.jsx)("p",{className:"text-lg font-bold text-gray-900 truncate max-w-[150px]",children:(null===f||void 0===f?void 0:f.username)||"username"})]}),(0,g.jsxs)("div",{className:"bg-purple-50 rounded-lg px-4 py-3 border border-purple-200 min-w-[120px]",children:[(0,g.jsx)("p",{className:"text-sm text-purple-600 font-medium",children:"Class"}),(0,g.jsx)("p",{className:"text-lg font-bold text-gray-900",children:(null===f||void 0===f?void 0:f.class)||"N/A"})]})]}),S&&(0,g.jsxs)("div",{className:"flex flex-wrap justify-center gap-4 text-center",children:[(0,g.jsxs)("div",{className:"bg-yellow-50 rounded-lg px-4 py-3 border border-yellow-200 min-w-[120px]",children:[(0,g.jsx)("p",{className:"text-sm text-yellow-600 font-medium",children:"Rank"}),(0,g.jsxs)("p",{className:"text-lg font-bold text-gray-900",children:["#",S.userRank||"N/A",S.totalUsers&&(0,g.jsxs)("span",{className:"text-sm text-gray-500",children:["/",S.totalUsers]})]})]}),(0,g.jsxs)("div",{className:"bg-orange-50 rounded-lg px-4 py-3 border border-orange-200 min-w-[120px]",children:[(0,g.jsx)("p",{className:"text-sm text-orange-600 font-medium",children:"Total XP"}),(0,g.jsx)("p",{className:"text-lg font-bold text-gray-900",children:(null===(e=S.user)||void 0===e||null===(s=e.totalXP)||void 0===s?void 0:s.toLocaleString())||"0"})]}),(0,g.jsxs)("div",{className:"bg-indigo-50 rounded-lg px-4 py-3 border border-indigo-200 min-w-[120px]",children:[(0,g.jsx)("p",{className:"text-sm text-indigo-600 font-medium",children:"Avg Score"}),(0,g.jsxs)("p",{className:"text-lg font-bold text-gray-900",children:[(null===(a=S.user)||void 0===a?void 0:a.averageScore)||"0","%"]})]}),(0,g.jsxs)("div",{className:"bg-pink-50 rounded-lg px-4 py-3 border border-pink-200 min-w-[120px]",children:[(0,g.jsx)("p",{className:"text-sm text-pink-600 font-medium",children:"Quizzes"}),(0,g.jsx)("p",{className:"text-lg font-bold text-gray-900",children:(null===(u=S.user)||void 0===u?void 0:u.totalQuizzesTaken)||"0"})]}),(0,g.jsxs)("div",{className:"bg-teal-50 rounded-lg px-4 py-3 border border-teal-200 min-w-[120px]",children:[(0,g.jsx)("p",{className:"text-sm text-teal-600 font-medium",children:"Streak"}),(0,g.jsx)("p",{className:"text-lg font-bold text-gray-900",children:(null===(x=S.user)||void 0===x?void 0:x.currentStreak)||"0"})]})]})]}),Z?(0,g.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,g.jsxs)("div",{className:"space-y-4",children:[(0,g.jsxs)("div",{children:[(0,g.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Name *"}),(0,g.jsx)("input",{type:"text",name:"name",value:E.name,onChange:q,className:"w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors",placeholder:"Enter your name",required:!0})]}),(0,g.jsxs)("div",{children:[(0,g.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Username"}),(0,g.jsxs)("div",{className:"p-3 bg-gray-100 rounded-lg border text-gray-600",children:[(null===f||void 0===f?void 0:f.username)||"Not available",(0,g.jsx)("span",{className:"text-xs text-gray-500 block mt-1",children:"Username cannot be changed"})]})]}),(0,g.jsxs)("div",{children:[(0,g.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Email (Optional)"}),(0,g.jsxs)("div",{className:"relative",children:[(0,g.jsx)("input",{type:"email",name:"email",value:E.email||"",onChange:q,className:"w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors pr-24",placeholder:"Enter your email (optional)"}),(!E.email||""===E.email)&&(0,g.jsx)("button",{type:"button",onClick:()=>{const e=Date.now(),s="".concat(f.username,".").concat(e,"@brainwave.temp");A((e=>(0,t.Z)((0,t.Z)({},e),{},{email:s}))),r.ZP.success("Auto-generated email created!")},className:"absolute right-2 top-1/2 transform -translate-y-1/2 text-xs bg-blue-100 text-blue-600 px-2 py-1 rounded hover:bg-blue-200 transition-colors",children:"Auto-Gen"})]}),E.email&&E.email.includes("@brainwave.temp")&&(0,g.jsx)("p",{className:"text-xs text-blue-600 mt-1",children:"\ud83d\udce7 This is an auto-generated email. You can change it to your real email if you prefer."})]}),(0,g.jsxs)("div",{children:[(0,g.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"School"}),(0,g.jsx)("input",{type:"text",name:"school",value:E.school,onChange:q,className:"w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors",placeholder:"Enter your school"})]})]}),(0,g.jsxs)("div",{className:"space-y-4",children:[(0,g.jsxs)("div",{children:[(0,g.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Level *"}),(0,g.jsxs)("select",{name:"level",value:E.level,onChange:q,className:"w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors",required:!0,children:[(0,g.jsx)("option",{value:"",children:"Select Level"}),(0,g.jsx)("option",{value:"Primary",children:"Primary"}),(0,g.jsx)("option",{value:"Secondary",children:"Secondary"}),(0,g.jsx)("option",{value:"Advance",children:"Advance"})]})]}),(0,g.jsxs)("div",{children:[(0,g.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Class *"}),(0,g.jsxs)("select",{name:"class_",value:E.class_,onChange:q,className:"w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors",required:!0,children:[(0,g.jsx)("option",{value:"",children:"Select Class"}),"Primary"===E.level&&(0,g.jsxs)(g.Fragment,{children:[(0,g.jsx)("option",{value:"1",children:"1"}),(0,g.jsx)("option",{value:"2",children:"2"}),(0,g.jsx)("option",{value:"3",children:"3"}),(0,g.jsx)("option",{value:"4",children:"4"}),(0,g.jsx)("option",{value:"5",children:"5"}),(0,g.jsx)("option",{value:"6",children:"6"}),(0,g.jsx)("option",{value:"7",children:"7"})]}),"Secondary"===E.level&&(0,g.jsxs)(g.Fragment,{children:[(0,g.jsx)("option",{value:"1",children:"1"}),(0,g.jsx)("option",{value:"2",children:"2"}),(0,g.jsx)("option",{value:"3",children:"3"}),(0,g.jsx)("option",{value:"4",children:"4"})]}),"Advance"===E.level&&(0,g.jsxs)(g.Fragment,{children:[(0,g.jsx)("option",{value:"5",children:"5"}),(0,g.jsx)("option",{value:"6",children:"6"})]})]})]}),(0,g.jsxs)("div",{children:[(0,g.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Phone Number"}),(0,g.jsx)("input",{type:"tel",name:"phoneNumber",value:E.phoneNumber,onChange:q,className:"w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors",placeholder:"Enter phone number",maxLength:"10"})]})]})]}):(0,g.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,g.jsxs)("div",{className:"space-y-4",children:[(0,g.jsxs)("div",{children:[(0,g.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Name"}),(0,g.jsx)("div",{className:"p-3 bg-gray-50 rounded-lg border",children:(null===f||void 0===f?void 0:f.name)||"Not provided"})]}),(0,g.jsxs)("div",{children:[(0,g.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Username"}),(0,g.jsx)("div",{className:"p-3 bg-gray-50 rounded-lg border",children:(null===f||void 0===f?void 0:f.username)||"Not provided"})]}),(0,g.jsxs)("div",{children:[(0,g.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Email"}),(0,g.jsx)("div",{className:"p-3 bg-gray-50 rounded-lg border",children:null!==f&&void 0!==f&&f.email?(0,g.jsxs)("div",{className:"flex items-center justify-between",children:[(0,g.jsx)("span",{children:f.email}),f.email.includes("@brainwave.temp")&&(0,g.jsx)("span",{className:"text-xs bg-blue-100 text-blue-600 px-2 py-1 rounded-full",children:"Auto-generated"})]}):(0,g.jsxs)("div",{className:"flex items-center justify-between",children:[(0,g.jsx)("span",{className:"text-gray-500",children:"No email set"}),(0,g.jsx)("button",{onClick:async()=>{const e=Date.now(),s="".concat(f.username,".").concat(e,"@brainwave.temp");A((e=>(0,t.Z)((0,t.Z)({},e),{},{email:s}))),r.ZP.info('Auto-generated email created. Click "Save Changes" to update.')},className:"text-xs bg-green-100 text-green-600 px-2 py-1 rounded-full hover:bg-green-200 transition-colors",children:"Generate Email"})]})})]}),(0,g.jsxs)("div",{children:[(0,g.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"School"}),(0,g.jsx)("div",{className:"p-3 bg-gray-50 rounded-lg border",children:(null===f||void 0===f?void 0:f.school)||"Not provided"})]})]}),(0,g.jsxs)("div",{className:"space-y-4",children:[(0,g.jsxs)("div",{children:[(0,g.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Level"}),(0,g.jsx)("div",{className:"p-3 bg-gray-50 rounded-lg border",children:(null===f||void 0===f?void 0:f.level)||"Not provided"})]}),(0,g.jsxs)("div",{children:[(0,g.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Class"}),(0,g.jsx)("div",{className:"p-3 bg-gray-50 rounded-lg border",children:(null===f||void 0===f?void 0:f.class)||"Not provided"})]}),(0,g.jsxs)("div",{children:[(0,g.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Phone Number"}),(0,g.jsx)("div",{className:"p-3 bg-gray-50 rounded-lg border",children:(null===f||void 0===f?void 0:f.phoneNumber)||"Not provided"})]})]})]}),(0,g.jsxs)("div",{className:"mt-8 p-6 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-2xl border border-blue-200",children:[(0,g.jsxs)("h3",{className:"text-xl font-bold text-gray-900 mb-4 flex items-center",children:[(0,g.jsx)("span",{className:"mr-2",children:"\ud83d\udc8e"}),"Subscription Plan"]}),B&&"active"===B.status?(0,g.jsxs)("div",{className:"space-y-6",children:[(0,g.jsxs)("div",{className:"flex items-center justify-between",children:[(0,g.jsxs)("div",{children:[(0,g.jsx)("h4",{className:"text-xl font-bold text-blue-700",children:B.planTitle||(null===(h=B.plan)||void 0===h?void 0:h.title)||"Premium Plan"}),(0,g.jsx)("p",{className:"text-gray-600 mt-1",children:(0,g.jsxs)("span",{className:"inline-flex items-center",children:[(0,g.jsx)("span",{className:"w-2 h-2 bg-green-500 rounded-full mr-2 animate-pulse"}),"Active Subscription"]})})]}),(0,g.jsx)("div",{className:"text-right",children:(0,g.jsx)("span",{className:"inline-flex items-center px-4 py-2 rounded-full text-sm font-medium bg-green-100 text-green-800 border border-green-200",children:"\u2705 Active"})})]}),(0,g.jsxs)("div",{className:"bg-gradient-to-r from-blue-50 to-green-50 p-4 rounded-xl border border-blue-200",children:[(0,g.jsxs)("h5",{className:"text-sm font-semibold text-gray-700 mb-3 flex items-center",children:[(0,g.jsx)("span",{className:"mr-2",children:"\ud83d\udcc5"}),"Subscription Timeline"]}),(0,g.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,g.jsxs)("div",{children:[(0,g.jsx)("p",{className:"text-xs text-gray-500 uppercase tracking-wide",children:"Started On"}),(0,g.jsx)("p",{className:"text-sm font-semibold text-gray-900",children:B.startDate?new Date(B.startDate).toLocaleDateString("en-US",{weekday:"short",year:"numeric",month:"short",day:"numeric"}):new Date(B.createdAt).toLocaleDateString("en-US",{weekday:"short",year:"numeric",month:"short",day:"numeric"})})]}),(0,g.jsxs)("div",{children:[(0,g.jsx)("p",{className:"text-xs text-gray-500 uppercase tracking-wide",children:"Expires On"}),(0,g.jsx)("p",{className:"text-sm font-semibold text-red-600",children:new Date(B.endDate).toLocaleDateString("en-US",{weekday:"short",year:"numeric",month:"short",day:"numeric"})})]})]})]}),(0,g.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[(0,g.jsx)("div",{className:"bg-white p-4 rounded-xl border border-gray-200 shadow-sm",children:(0,g.jsxs)("div",{className:"flex items-center justify-between",children:[(0,g.jsxs)("div",{children:[(0,g.jsx)("p",{className:"text-xs text-gray-500 uppercase tracking-wide",children:"Total Duration"}),(0,g.jsxs)("p",{className:"text-lg font-bold text-gray-900",children:[B.duration||(null===(p=B.plan)||void 0===p?void 0:p.duration)||1," month",(B.duration||(null===(b=B.plan)||void 0===b?void 0:b.duration)||1)>1?"s":""]})]}),(0,g.jsx)("div",{className:"text-blue-500",children:(0,g.jsx)("span",{className:"text-2xl",children:"\ud83d\udcc6"})})]})}),(0,g.jsx)("div",{className:"bg-white p-4 rounded-xl border border-gray-200 shadow-sm",children:(0,g.jsxs)("div",{className:"flex items-center justify-between",children:[(0,g.jsxs)("div",{children:[(0,g.jsx)("p",{className:"text-xs text-gray-500 uppercase tracking-wide",children:"Amount Paid"}),(0,g.jsxs)("p",{className:"text-lg font-bold text-green-600",children:[(B.amount||B.discountedPrice||0).toLocaleString()," TZS"]})]}),(0,g.jsx)("div",{className:"text-green-500",children:(0,g.jsx)("span",{className:"text-2xl",children:"\ud83d\udcb0"})})]})}),(0,g.jsx)("div",{className:"bg-white p-4 rounded-xl border border-gray-200 shadow-sm",children:(0,g.jsxs)("div",{className:"flex items-center justify-between",children:[(0,g.jsxs)("div",{children:[(0,g.jsx)("p",{className:"text-xs text-gray-500 uppercase tracking-wide",children:"Days Remaining"}),(0,g.jsxs)("p",{className:"text-lg font-bold text-orange-600",children:[Math.max(0,Math.ceil((new Date(B.endDate)-new Date)/864e5))," days"]})]}),(0,g.jsx)("div",{className:"text-orange-500",children:(0,g.jsx)("span",{className:"text-2xl",children:"\u23f0"})})]})}),(0,g.jsx)("div",{className:"bg-white p-4 rounded-xl border border-gray-200 shadow-sm",children:(0,g.jsxs)("div",{className:"flex items-center justify-between",children:[(0,g.jsxs)("div",{children:[(0,g.jsx)("p",{className:"text-xs text-gray-500 uppercase tracking-wide",children:"Total Days"}),(0,g.jsxs)("p",{className:"text-lg font-bold text-purple-600",children:[30*(B.duration||(null===(j=B.plan)||void 0===j?void 0:j.duration)||1)," days"]})]}),(0,g.jsx)("div",{className:"text-purple-500",children:(0,g.jsx)("span",{className:"text-2xl",children:"\ud83d\udcca"})})]})})]}),(0,g.jsxs)("div",{className:"bg-white p-4 rounded-xl border border-gray-200",children:[(0,g.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,g.jsx)("p",{className:"text-sm font-semibold text-gray-700",children:"Subscription Progress"}),(0,g.jsx)("p",{className:"text-xs text-gray-500",children:(e=>{const s=30*(B.duration||(null===(e=B.plan)||void 0===e?void 0:e.duration)||1),a=s-Math.max(0,Math.ceil((new Date(B.endDate)-new Date)/864e5)),t=Math.min(100,Math.max(0,a/s*100));return"".concat(t.toFixed(1),"% completed")})()})]}),(0,g.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-3",children:(0,g.jsx)("div",{className:"bg-gradient-to-r from-blue-500 to-green-500 h-3 rounded-full transition-all duration-500",style:{width:"".concat((e=>{const s=30*(B.duration||(null===(e=B.plan)||void 0===e?void 0:e.duration)||1),a=s-Math.max(0,Math.ceil((new Date(B.endDate)-new Date)/864e5));return Math.min(100,Math.max(0,a/s*100))})(),"%")}})}),(0,g.jsxs)("div",{className:"flex justify-between text-xs text-gray-500 mt-1",children:[(0,g.jsx)("span",{children:"Started"}),(0,g.jsx)("span",{children:(()=>{const e=Math.max(0,Math.ceil((new Date(B.endDate)-new Date)/864e5));return e>7?"".concat(e," days left"):e>0?"\u26a0\ufe0f ".concat(e," days left"):"\u274c Expired"})()})]})]}),(0,g.jsxs)("div",{className:"flex gap-3",children:[(0,g.jsx)("button",{onClick:()=>U(!0),className:"px-6 py-3 bg-gradient-to-r from-blue-600 to-blue-700 text-white rounded-lg hover:from-blue-700 hover:to-blue-800 transition-all font-medium shadow-md",children:"\ud83d\ude80 Upgrade Plan"}),(0,g.jsx)("button",{onClick:()=>{const e=new Date(B.endDate),s=new Date,a=Math.ceil((e-s)/864e5);a<=7&&a>0?r.ZP.warning("Your subscription expires in ".concat(a," days. Consider renewing soon!")):a<=0?r.ZP.error("Your subscription has expired. Please renew to continue accessing premium features."):r.ZP.info("Your subscription is active for ".concat(a," more days."))},className:"px-6 py-3 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors font-medium",children:"\ud83d\udcca Check Status"})]})]}):(0,g.jsxs)("div",{className:"text-center py-8",children:[(0,g.jsxs)("div",{className:"mb-6",children:[(0,g.jsx)("div",{className:"w-20 h-20 mx-auto bg-gray-100 rounded-full flex items-center justify-center mb-4",children:(0,g.jsx)("span",{className:"text-4xl",children:"\ud83d\udd12"})}),(0,g.jsx)("h4",{className:"text-xl font-bold text-gray-900 mb-2",children:"No Active Subscription"}),(0,g.jsx)("p",{className:"text-gray-600 mb-6 max-w-md mx-auto",children:"Unlock premium features and get unlimited access to all educational content with a subscription plan."})]}),(0,g.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4 mb-6 max-w-2xl mx-auto",children:[(0,g.jsxs)("div",{className:"bg-gradient-to-br from-blue-50 to-indigo-50 p-4 rounded-lg border border-blue-200",children:[(0,g.jsx)("div",{className:"text-blue-600 text-2xl mb-2",children:"\ud83d\udcda"}),(0,g.jsx)("h5",{className:"font-semibold text-gray-900 mb-1",children:"Unlimited Quizzes"}),(0,g.jsx)("p",{className:"text-sm text-gray-600",children:"Access all quizzes without restrictions"})]}),(0,g.jsxs)("div",{className:"bg-gradient-to-br from-green-50 to-emerald-50 p-4 rounded-lg border border-green-200",children:[(0,g.jsx)("div",{className:"text-green-600 text-2xl mb-2",children:"\ud83e\udd16"}),(0,g.jsx)("h5",{className:"font-semibold text-gray-900 mb-1",children:"AI Study Assistant"}),(0,g.jsx)("p",{className:"text-sm text-gray-600",children:"Get instant help with your studies"})]}),(0,g.jsxs)("div",{className:"bg-gradient-to-br from-purple-50 to-violet-50 p-4 rounded-lg border border-purple-200",children:[(0,g.jsx)("div",{className:"text-purple-600 text-2xl mb-2",children:"\ud83d\udcca"}),(0,g.jsx)("h5",{className:"font-semibold text-gray-900 mb-1",children:"Progress Tracking"}),(0,g.jsx)("p",{className:"text-sm text-gray-600",children:"Monitor your learning progress"})]}),(0,g.jsxs)("div",{className:"bg-gradient-to-br from-orange-50 to-amber-50 p-4 rounded-lg border border-orange-200",children:[(0,g.jsx)("div",{className:"text-orange-600 text-2xl mb-2",children:"\ud83c\udfc6"}),(0,g.jsx)("h5",{className:"font-semibold text-gray-900 mb-1",children:"Rankings & Badges"}),(0,g.jsx)("p",{className:"text-sm text-gray-600",children:"Compete and earn achievements"})]})]}),(0,g.jsxs)("div",{className:"space-y-3",children:[(0,g.jsx)("button",{onClick:()=>U(!0),className:"px-8 py-4 bg-gradient-to-r from-blue-600 to-blue-700 text-white rounded-xl hover:from-blue-700 hover:to-blue-800 transition-all font-semibold text-lg shadow-lg hover:shadow-xl transform hover:scale-105",children:"\ud83d\ude80 Choose Subscription Plan"}),(0,g.jsx)("p",{className:"text-xs text-gray-500",children:"Plans start from as low as 13,000 TZS per month"})]})]})]}),(0,g.jsx)("div",{className:"mt-8 flex justify-center gap-4",children:Z?(0,g.jsxs)(g.Fragment,{children:[(0,g.jsx)("button",{onClick:()=>{A({name:f.name,email:f.email,school:f.school,class_:f.class,level:f.level,phoneNumber:f.phoneNumber}),D(!1)},className:"px-6 py-3 bg-gray-500 text-white rounded-lg hover:bg-gray-600 transition-colors duration-200 font-medium",children:"Cancel"}),(0,g.jsx)("button",{onClick:async function(){let{skipOTP:e}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};if(console.log("\ud83d\udd0d Current formData:",E),console.log("\ud83d\udd0d Current userDetails:",f),!E.name||""===E.name.trim())return console.log("\u274c Validation failed: name is empty"),r.ZP.error("Please enter your name.");if(!E.class_||""===E.class_.trim())return console.log("\u274c Validation failed: class is empty"),r.ZP.error("Please select a class.");if(!E.level||""===E.level.trim())return console.log("\u274c Validation failed: level is empty"),r.ZP.error("Please select a level.");if(E.email&&""!==E.email.trim()){if(!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(E.email))return r.ZP.error("Please enter a valid email address.")}O((0,c.YC)());try{const e=(0,t.Z)((0,t.Z)({},E),{},{userId:f._id});E.email&&""!==E.email.trim()?e.email=E.email.trim():null!==f&&void 0!==f&&f.email&&(e.email=f.email),console.log("\ud83d\udce4 Sending update data:",e);const s=await(0,n.gS)(e);console.log("\ud83d\udce5 Server response:",s),s.success?(r.ZP.success(s.message),D(!1),H(),s.levelChanged&&setTimeout((()=>window.location.reload()),2e3)):(console.error("\u274c Update failed:",s),r.ZP.error(s.message||"Failed to update profile. Please try again."))}catch(l){var s,a;console.error("\u274c Update error:",l);const e=(null===(s=l.response)||void 0===s||null===(a=s.data)||void 0===a?void 0:a.message)||l.message||"An unexpected error occurred.";r.ZP.error("Update failed: ".concat(e))}finally{O((0,c.Ir)())}},className:"px-6 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors duration-200 font-medium",children:"Save Changes"}),(0,g.jsx)("button",{onClick:()=>{console.log("\ud83d\udd0d Debug - Current formData:",E),console.log("\ud83d\udd0d Debug - Current userDetails:",f),alert("FormData: ".concat(JSON.stringify(E,null,2)))},className:"px-4 py-2 bg-gray-400 text-white rounded-lg hover:bg-gray-500 transition-colors duration-200 font-medium text-sm",children:"Debug"})]}):(0,g.jsx)("button",{onClick:()=>{A({name:(null===f||void 0===f?void 0:f.name)||"",email:(null===f||void 0===f?void 0:f.email)||"",school:(null===f||void 0===f?void 0:f.school)||"",class_:(null===f||void 0===f?void 0:f.class)||"",level:(null===f||void 0===f?void 0:f.level)||"",phoneNumber:(null===f||void 0===f?void 0:f.phoneNumber)||""}),D(!0)},className:"px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors duration-200 font-medium",children:"Edit Profile"})})]})})]})}),(0,g.jsx)("input",{type:"file",id:"profileImageInput",accept:"image/*",onChange:V,style:{display:"none"}}),(0,g.jsxs)(i.Z,{title:"Confirm Level Change",open:F,onOk:()=>{A((e=>(0,t.Z)((0,t.Z)({},e),{},{level:Y,class_:""}))),_(!1),R(null)},onCancel:()=>{_(!1),R(null)},okText:"Confirm",cancelText:"Cancel",children:[(0,g.jsxs)("p",{children:["Are you sure you want to change your level to ",(0,g.jsx)("strong",{children:Y}),"?"]}),(0,g.jsx)("p",{className:"text-orange-600 text-sm mt-2",children:"Note: Changing your level will reset your class selection and you'll only have access to content for the new level."})]}),(0,g.jsx)(v,{isOpen:T,onClose:()=>U(!1),onSuccess:()=>{U(!1),H(),r.ZP.success("Subscription updated successfully!")}})]})}},9168:(e,s,a)=>{a.d(s,{Z:()=>o});var t=a(7462),l=a(2791);const n={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M880 184H712v-64c0-4.4-3.6-8-8-8h-56c-4.4 0-8 3.6-8 8v64H384v-64c0-4.4-3.6-8-8-8h-56c-4.4 0-8 3.6-8 8v64H144c-17.7 0-32 14.3-32 32v664c0 17.7 14.3 32 32 32h736c17.7 0 32-14.3 32-32V216c0-17.7-14.3-32-32-32zm-40 656H184V460h656v380zM184 392V256h128v48c0 4.4 3.6 8 8 8h56c4.4 0 8-3.6 8-8v-48h256v48c0 4.4 3.6 8 8 8h56c4.4 0 8-3.6 8-8v-48h128v136H184z"}}]},name:"calendar",theme:"outlined"};var r=a(4291),i=function(e,s){return l.createElement(r.Z,(0,t.Z)({},e,{ref:s,icon:n}))};const o=l.forwardRef(i)},50:(e,s,a)=>{a.d(s,{Z:()=>o});var t=a(7462),l=a(2791);const n={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372z"}},{tag:"path",attrs:{d:"M686.7 638.6L544.1 535.5V288c0-4.4-3.6-8-8-8H488c-4.4 0-8 3.6-8 8v275.4c0 2.6 1.2 5 3.3 6.5l165.4 120.6c3.6 2.6 8.6 1.8 11.2-1.7l28.6-39c2.6-3.7 1.8-8.7-1.8-11.2z"}}]},name:"clock-circle",theme:"outlined"};var r=a(4291),i=function(e,s){return l.createElement(r.Z,(0,t.Z)({},e,{ref:s,icon:n}))};const o=l.forwardRef(i)},7455:(e,s,a)=>{a.d(s,{Z:()=>o});var t=a(7462),l=a(2791);const n={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M899.6 276.5L705 396.4 518.4 147.5a8.06 8.06 0 00-12.9 0L319 396.4 124.3 276.5c-5.7-3.5-13.1 1.2-12.2 7.9L188.5 865c1.1 7.9 7.9 14 16 14h615.1c8 0 14.9-6 15.9-14l76.4-580.6c.8-6.7-6.5-11.4-12.3-7.9zm-126 534.1H250.3l-53.8-409.4 139.8 86.1L512 252.9l175.7 234.4 139.8-86.1-53.9 409.4zM512 509c-62.1 0-112.6 50.5-112.6 112.6S449.9 734.2 512 734.2s112.6-50.5 112.6-112.6S574.1 509 512 509zm0 160.9c-26.6 0-48.2-21.6-48.2-48.3 0-26.6 21.6-48.3 48.2-48.3s48.2 21.6 48.2 48.3c0 26.6-21.6 48.3-48.2 48.3z"}}]},name:"crown",theme:"outlined"};var r=a(4291),i=function(e,s){return l.createElement(r.Z,(0,t.Z)({},e,{ref:s,icon:n}))};const o=l.forwardRef(i)}}]);
//# sourceMappingURL=408.44ffe71d.chunk.js.map