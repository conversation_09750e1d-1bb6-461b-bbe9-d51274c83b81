{"version": 3, "file": "static/css/754.a50802f8.chunk.css", "mappings": "AAeA,kGACE,uDACF,CAGA,WACE,uBACF,CAEA,iBAEE,gCAA0C,CAD1C,0BAEF,CAGA,gBACE,kDAIF,CAGA,4BAJE,wBAAyB,CAFzB,kBAAmB,CACnB,cAYF,CAPA,YACE,eAAiB,CAGjB,8BAAwC,CAExC,uBACF,CAEA,kBACE,+BAA0C,CAC1C,0BACF,CAGA,sBACE,kBAAmB,CAInB,6BAA8B,CAH9B,iBAAkB,CAElB,gBAAkB,CADlB,cAGF,CAEA,8BAEE,kBAAmB,CADnB,yBAEF,CAEA,8BAEE,kBAAmB,CADnB,yBAEF,CAGA,aACE,0EAA0E,CAC1E,kBAAmB,CAEnB,wBAAyB,CADzB,aAEF,CAGA,yBAKE,4BACE,YACF,CAEA,cACE,gBAAkB,CAClB,oBACF,CACF,CAGA,kBAGE,+BAAgC,CAFhC,qEAAyE,CACzE,yBAEF,CAEA,mBACE,GACE,0BACF,CACA,GACE,2BACF,CACF,CAGA,eACE,uBACF,CAEA,qBACE,qBACF,CAEA,sBACE,oBACF,CAGA,cACE,iBACF,CAEA,qBAOE,mEAAiG,CACjG,qBAAsB,CAFtB,QAAS,CALT,UAAW,CAGX,MAAO,CAKP,SAAU,CAPV,iBAAkB,CAGlB,OAAQ,CAFR,KAAM,CAON,2BACF,CAEA,kCACE,SACF,CAGA,iBAEE,+BAAgC,CADhC,oBAEF,CAEA,oCACE,SACF,CAEA,0CACE,kBAAmB,CACnB,iBACF,CAEA,0CACE,kBAAmB,CACnB,iBACF,CAEA,gDACE,kBACF,CCvKA,gBACE,uBAAwB,CACxB,oBACF,CAEA,mCACE,YACF,CAGA,gBACE,sBACF,CAGA,yBACE,gBACE,kBACF,CACF", "sources": ["pages/admin/Users/<USER>", "styles/admin-navigation.css"], "sourcesContent": ["/* Admin Users Page Styles */\n\n.badge-modern {\n  @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;\n}\n\n/* Subscription Status Badges */\n.subscription-badge-on-plan {\n  @apply bg-green-100 text-green-800 border border-green-200;\n}\n\n.subscription-badge-expired {\n  @apply bg-orange-100 text-orange-800 border border-orange-200;\n}\n\n.subscription-badge-no-plan {\n  @apply bg-gray-100 text-gray-800 border border-gray-200;\n}\n\n/* User Card Enhancements */\n.user-card {\n  transition: all 0.3s ease;\n}\n\n.user-card:hover {\n  transform: translateY(-2px);\n  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);\n}\n\n/* Filter Section */\n.filter-section {\n  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);\n  border-radius: 12px;\n  padding: 1.5rem;\n  border: 1px solid #e2e8f0;\n}\n\n/* Stats Cards */\n.stats-card {\n  background: white;\n  border-radius: 12px;\n  padding: 1.5rem;\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);\n  border: 1px solid #e2e8f0;\n  transition: all 0.3s ease;\n}\n\n.stats-card:hover {\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\n  transform: translateY(-1px);\n}\n\n/* Subscription Details */\n.subscription-details {\n  background: #f8fafc;\n  border-radius: 8px;\n  padding: 0.75rem;\n  margin-top: 0.5rem;\n  border-left: 3px solid #3b82f6;\n}\n\n.subscription-details.expired {\n  border-left-color: #f59e0b;\n  background: #fffbeb;\n}\n\n.subscription-details.no-plan {\n  border-left-color: #6b7280;\n  background: #f9fafb;\n}\n\n/* Filter Pills */\n.filter-pill {\n  @apply inline-flex items-center px-3 py-1 rounded-full text-sm font-medium;\n  background: #dbeafe;\n  color: #1e40af;\n  border: 1px solid #bfdbfe;\n}\n\n/* Responsive Design */\n@media (max-width: 768px) {\n  .stats-card {\n    padding: 1rem;\n  }\n  \n  .filter-section {\n    padding: 1rem;\n  }\n  \n  .badge-modern {\n    font-size: 0.75rem;\n    padding: 0.25rem 0.5rem;\n  }\n}\n\n/* Loading States */\n.loading-skeleton {\n  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);\n  background-size: 200% 100%;\n  animation: loading 1.5s infinite;\n}\n\n@keyframes loading {\n  0% {\n    background-position: 200% 0;\n  }\n  100% {\n    background-position: -200% 0;\n  }\n}\n\n/* Action Buttons */\n.action-button {\n  transition: all 0.2s ease;\n}\n\n.action-button:hover {\n  transform: scale(1.05);\n}\n\n.action-button:active {\n  transform: scale(0.95);\n}\n\n/* Search Input Enhancement */\n.search-input {\n  position: relative;\n}\n\n.search-input::before {\n  content: '';\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: linear-gradient(45deg, transparent 30%, rgba(59, 130, 246, 0.1) 50%, transparent 70%);\n  border-radius: inherit;\n  opacity: 0;\n  transition: opacity 0.3s ease;\n}\n\n.search-input:focus-within::before {\n  opacity: 1;\n}\n\n/* Custom Scrollbar */\n.users-container {\n  scrollbar-width: thin;\n  scrollbar-color: #cbd5e1 #f1f5f9;\n}\n\n.users-container::-webkit-scrollbar {\n  width: 6px;\n}\n\n.users-container::-webkit-scrollbar-track {\n  background: #f1f5f9;\n  border-radius: 3px;\n}\n\n.users-container::-webkit-scrollbar-thumb {\n  background: #cbd5e1;\n  border-radius: 3px;\n}\n\n.users-container::-webkit-scrollbar-thumb:hover {\n  background: #94a3b8;\n}\n", "/* Hide scrollbar for admin navigation */\n.scrollbar-hide {\n  -ms-overflow-style: none;  /* Internet Explorer 10+ */\n  scrollbar-width: none;  /* Firefox */\n}\n\n.scrollbar-hide::-webkit-scrollbar { \n  display: none;  /* Safari and Chrome */\n}\n\n/* Smooth scrolling for navigation */\n.scrollbar-hide {\n  scroll-behavior: smooth;\n}\n\n/* Add padding for mobile scroll */\n@media (max-width: 640px) {\n  .scrollbar-hide {\n    padding-right: 1rem;\n  }\n}\n"], "names": [], "sourceRoot": ""}