{"version": 3, "file": "static/js/915.7f3541a8.chunk.js", "mappings": "6JAAA,MAAQA,QAASC,GAAkBC,EAAQ,MAG9BC,EAAYC,UACrB,IAEI,aADuBH,EAAcI,KAAK,0BAA2BC,IACrDC,IACpB,CAAE,MAAOC,GACL,OAAOA,EAAMC,SAASF,IAC1B,GAISG,EAAgBN,UACzB,IAEI,aADuBH,EAAcI,KAAK,+BAAgCM,IAC1DJ,IACpB,CAAE,MAAOC,GACL,OAAOA,EAAMC,SAASF,IAC1B,GAISK,EAAsBR,UAC/B,IAEI,aADuBH,EAAcI,KAAK,uCAAwCC,IAClEC,IACpB,CAAE,MAAOC,GACL,OAAOA,EAAMC,SAASF,IAC1B,GAISM,EAA0BT,UACnC,IAEI,aADuBH,EAAca,IAAI,6CACzBP,IACpB,CAAE,MAAOC,GACL,OAAOA,EAAMC,SAASF,IAC1B,GAaSQ,EAAmBX,iBAAyB,IAAlBY,EAAOC,UAAAC,OAAA,QAAAC,IAAAF,UAAA,GAAAA,UAAA,GAAG,CAAC,EAC9C,IACI,MAAMG,EAAS,IAAIC,gBACfL,EAAQM,OAAOF,EAAOG,OAAO,QAASP,EAAQM,OAC9CN,EAAQQ,aAAaJ,EAAOG,OAAO,cAAeP,EAAQQ,aAC1DR,EAAQS,aAAaL,EAAOG,OAAO,cAAeP,EAAQS,aAC1DT,EAAQU,cAAcN,EAAOG,OAAO,eAAgBP,EAAQU,cAC5DV,EAAQW,iBAAiBP,EAAOG,OAAO,kBAAmBP,EAAQW,iBAGtE,aADuB1B,EAAca,IAAI,4BAADc,OAA6BR,EAAOS,cAC5DtB,IACpB,CAAE,MAAOC,GACL,OAAOA,EAAMC,SAASF,IAC1B,CACJ,EAEauB,EAAiB1B,eAAO2B,GAAyB,IAAjBC,EAAOf,UAAAC,OAAA,QAAAC,IAAAF,UAAA,GAAAA,UAAA,GAAG,EACnD,IAEI,aADuBhB,EAAca,IAAI,0BAADc,OAA2BG,EAAM,aAAAH,OAAYI,KACrEzB,IACpB,CAAE,MAAOC,GACL,OAAOA,EAAMC,SAASF,IAC1B,CACJ,C,iECxEA,MAwDA,EAxDkB0B,IAQX,IARY,SACjBC,EAAQ,MACRC,EAAK,SACLC,EAAQ,QACRC,EAAO,UACPC,EAAY,GAAE,UACdC,GAAY,EAAK,QACjBC,GAAU,GACXP,EACC,OACEQ,EAAAA,EAAAA,MAACC,EAAAA,EAAOC,IAAG,CACTC,QAAS,CAAEC,QAAS,EAAGC,EAAG,IAC1BC,QAAS,CAAEF,QAAS,EAAGC,EAAG,GAC1BE,WAAY,CAAEC,SAAU,IACxBX,UAAS,uHAAAV,OAAyHU,GAAYJ,SAAA,EAG5IC,GAASE,KACTa,EAAAA,EAAAA,KAAA,OAAKZ,UAAU,sDAAqDJ,UAClEO,EAAAA,EAAAA,MAAA,OAAKH,UAAU,qEAAoEJ,SAAA,CAChFC,IACCM,EAAAA,EAAAA,MAAA,OAAAP,SAAA,EACEgB,EAAAA,EAAAA,KAAA,MAAIZ,UAAU,kDAAiDJ,SAC5DC,IAEFC,IACCc,EAAAA,EAAAA,KAAA,KAAGZ,UAAU,8BAA6BJ,SACvCE,OAMRC,IACCa,EAAAA,EAAAA,KAAA,OAAKZ,UAAU,uBAAsBJ,SAClCG,UAQXa,EAAAA,EAAAA,KAAA,OAAKZ,UAAWC,EAAY,GAAK,aAAaL,SAC3CM,GACCU,EAAAA,EAAAA,KAAA,OAAKZ,UAAU,iDAAgDJ,UAC7DgB,EAAAA,EAAAA,KAAA,OAAKZ,UAAU,mFAGjBJ,MAGO,C,+FCnCjB,MAwKA,EAxK2BiB,KACzB,MAAMC,GAAWC,EAAAA,EAAAA,MACXC,GAAWC,EAAAA,EAAAA,OACX,KAAEC,IAASC,EAAAA,EAAAA,KAAaC,GAAUA,EAAMF,OAExCG,EAAiB,CACrB,CACExB,MAAO,YACPyB,KAAMC,EAAAA,IACNC,KAAM,mBACNC,MAAO,iBAET,CACE5B,MAAO,QACPyB,KAAMI,EAAAA,IACNF,KAAM,eACNC,MAAO,kBAET,CACE5B,MAAO,QACPyB,KAAMK,EAAAA,IACNH,KAAM,eACNC,MAAO,mBAET,CACE5B,MAAO,kBACPyB,KAAMM,EAAAA,IACNJ,KAAM,yBACNC,MAAO,mBAET,CACE5B,MAAO,UACPyB,KAAMO,EAAAA,IACNL,KAAM,iBACNC,MAAO,mBAET,CACE5B,MAAO,gBACPyB,KAAMQ,EAAAA,IACNN,KAAM,uBACNC,MAAO,oBAwBLM,EApBqBC,MACzB,MAAMC,EAAcjB,EAASkB,SAE7B,OADoBb,EAAec,MAAKC,GAAQH,EAAYI,WAAWD,EAAKZ,SACtD,CAAE3B,MAAO,cAAeyB,KAAMC,EAAAA,IAAa,EAiB/CS,GACdM,EAAoC,qBAAtBtB,EAASkB,SAE7B,OACEtB,EAAAA,EAAAA,KAAA,OAAKZ,UAAU,uDAAsDJ,UACnEO,EAAAA,EAAAA,MAAA,OAAKH,UAAU,uBAAsBJ,SAAA,EACnCO,EAAAA,EAAAA,MAAA,OAAKH,UAAU,yCAAwCJ,SAAA,EAErDO,EAAAA,EAAAA,MAAA,OAAKH,UAAU,8BAA6BJ,SAAA,EACxC0C,IACA1B,EAAAA,EAAAA,KAACR,EAAAA,EAAOmC,OAAM,CACZC,WAAY,CAAEC,MAAO,MACrBC,SAAU,CAAED,MAAO,KACnBE,QAASA,IAAM7B,EAAS,oBACxBd,UAAU,gFAA+EJ,UAEzFgB,EAAAA,EAAAA,KAACgC,EAAAA,IAAW,CAAC5C,UAAU,8BAI3BG,EAAAA,EAAAA,MAAA,OAAKH,UAAU,8BAA6BJ,SAAA,EAC1CgB,EAAAA,EAAAA,KAAA,OAAKZ,UAAU,oGAAmGJ,UAChHgB,EAAAA,EAAAA,KAACmB,EAAYT,KAAI,CAACtB,UAAU,0BAE9BG,EAAAA,EAAAA,MAAA,OAAAP,SAAA,EACEgB,EAAAA,EAAAA,KAAA,MAAIZ,UAAU,uCAAsCJ,SAAEmC,EAAYlC,SAClEe,EAAAA,EAAAA,KAAA,KAAGZ,UAAU,yBAAwBJ,SAAC,8BAM5CO,EAAAA,EAAAA,MAAA,OAAKH,UAAU,8BAA6BJ,SAAA,EAC1CO,EAAAA,EAAAA,MAAA,OAAKH,UAAU,wCAAuCJ,SAAA,EACpDgB,EAAAA,EAAAA,KAAA,OAAKZ,UAAU,sGAAqGJ,UAClHgB,EAAAA,EAAAA,KAACiC,EAAAA,IAAM,CAAC7C,UAAU,0BAEpBG,EAAAA,EAAAA,MAAA,OAAKH,UAAU,aAAYJ,SAAA,EACzBgB,EAAAA,EAAAA,KAAA,KAAGZ,UAAU,qCAAoCJ,SAAM,OAAJsB,QAAI,IAAJA,OAAI,EAAJA,EAAM4B,QACzDlC,EAAAA,EAAAA,KAAA,KAAGZ,UAAU,yBAAwBJ,SAAC,yBAI1CO,EAAAA,EAAAA,MAAA,OAAKH,UAAU,8BAA6BJ,SAAA,EAC1CgB,EAAAA,EAAAA,KAACR,EAAAA,EAAOmC,OAAM,CACZC,WAAY,CAAEC,MAAO,MACrBC,SAAU,CAAED,MAAO,KACnBE,QAASA,IAAM7B,EAAS,KACxBd,UAAU,gFACVH,MAAM,YAAWD,UAEjBgB,EAAAA,EAAAA,KAACmC,EAAAA,IAAM,CAAC/C,UAAU,8BAGpBY,EAAAA,EAAAA,KAACR,EAAAA,EAAOmC,OAAM,CACZC,WAAY,CAAEC,MAAO,MACrBC,SAAU,CAAED,MAAO,KACnBE,QAnEOK,KACnB,IACEC,aAAaC,WAAW,SACxBD,aAAaC,WAAW,QACxBpC,EAAS,SACX,CAAE,MAAO5C,GACPiF,QAAQjF,MAAM,qBAAsBA,EACtC,GA6DY8B,UAAU,4EACVH,MAAM,SAAQD,UAEdgB,EAAAA,EAAAA,KAACwC,EAAAA,IAAQ,CAACpD,UAAU,qCAO5BG,EAAAA,EAAAA,MAAA,OAAKH,UAAU,OAAMJ,SAAA,EACnBgB,EAAAA,EAAAA,KAAA,OAAKZ,UAAU,6DAA4DJ,SACxEyB,EAAegC,KAAKjB,IACnB,MAAMkB,EAAgBlB,EAAKd,KACrBiC,GAtFI/B,EAsFoBY,EAAKZ,KArFtCR,EAASkB,SAASG,WAAWb,IADhBA,MAwFV,OACErB,EAAAA,EAAAA,MAACC,EAAAA,EAAOmC,OAAM,CAEZC,WAAY,CAAEC,MAAO,MACrBC,SAAU,CAAED,MAAO,KACnBE,QAASA,IAAM7B,EAASsB,EAAKZ,MAC7BxB,UAAS,oIAAAV,OACPiE,EACI,6DACA,0DACH3D,SAAA,EAEHgB,EAAAA,EAAAA,KAAC0C,EAAa,CAACtD,UAAS,WAAAV,OAAaiE,EAAW,gBAAkBnB,EAAKX,UACvEb,EAAAA,EAAAA,KAAA,QAAMZ,UAAU,mBAAkBJ,SAAEwC,EAAKvC,SACzCe,EAAAA,EAAAA,KAAA,QAAMZ,UAAU,oBAAmBJ,SAAEwC,EAAKvC,MAAM2D,MAAM,KAAK,OAZtDpB,EAAKZ,KAaI,OAMtBZ,EAAAA,EAAAA,KAAA,OAAKZ,UAAU,qCAAoCJ,UACjDgB,EAAAA,EAAAA,KAAA,OAAKZ,UAAU,yBAAwBJ,SAAC,qDAI1C,ECzHV,EA1DoBD,IAAgE,IAA/D,SAAEC,EAAQ,MAAEC,EAAK,SAAEC,EAAQ,QAAEC,EAAO,WAAE0D,GAAa,GAAM9D,EAC5E,MAAM,KAAEuB,IAASC,EAAAA,EAAAA,KAAaC,GAAUA,EAAMF,OAE9C,OACEf,EAAAA,EAAAA,MAAA,OAAKH,UAAU,wEAAuEJ,SAAA,EAEpFgB,EAAAA,EAAAA,KAACC,EAAkB,KAGnBV,EAAAA,EAAAA,MAAA,OAAKH,UAAU,sCAAqCJ,SAAA,CAEjD6D,IAAe5D,GAASC,GAAYC,KACnCa,EAAAA,EAAAA,KAACR,EAAAA,EAAOC,IAAG,CACTC,QAAS,CAAEC,QAAS,EAAGC,GAAI,IAC3BC,QAAS,CAAEF,QAAS,EAAGC,EAAG,GAC1BE,WAAY,CAAEC,SAAU,IACxBX,UAAU,eAAcJ,UAExBgB,EAAAA,EAAAA,KAAA,OAAKZ,UAAU,uEAAsEJ,UACnFO,EAAAA,EAAAA,MAAA,OAAKH,UAAU,qEAAoEJ,SAAA,EACjFO,EAAAA,EAAAA,MAAA,OAAKH,UAAU,SAAQJ,SAAA,CACpBC,IACCe,EAAAA,EAAAA,KAAA,MAAIZ,UAAU,4HAA2HJ,SACtIC,IAGJC,IACCc,EAAAA,EAAAA,KAAA,KAAGZ,UAAU,iDAAgDJ,SAC1DE,OAMNC,IACCa,EAAAA,EAAAA,KAAA,OAAKZ,UAAU,gCAA+BJ,SAC3CG,YASba,EAAAA,EAAAA,KAACR,EAAAA,EAAOC,IAAG,CACTC,QAAS,CAAEC,QAAS,EAAGC,EAAG,IAC1BC,QAAS,CAAEF,QAAS,EAAGC,EAAG,GAC1BE,WAAY,CAAEC,SAAU,GAAK+C,MAAO,IACpC1D,UAAU,YAAWJ,SAEpBA,SAGD,C,kLChCV,MAgTA,EAhTuB+D,KACrB,MAAMC,GAAWC,EAAAA,EAAAA,MACX/C,GAAWC,EAAAA,EAAAA,OACX,KAAEG,IAASC,EAAAA,EAAAA,KAAaC,GAAUA,EAAMF,QACvC4C,EAAOC,IAAYC,EAAAA,EAAAA,UAAS,CACjCC,WAAY,EACZC,YAAa,EACbC,WAAY,EACZC,aAAc,EACdC,aAAc,EACdC,eAAgB,KAEXpE,EAASqE,IAAcP,EAAAA,EAAAA,WAAS,IAEvCQ,EAAAA,EAAAA,YAAU,KACRC,GAAoB,GACnB,IAEH,MAAMA,EAAqB3G,UACzB,IACEyG,GAAW,GACXX,GAASc,EAAAA,EAAAA,OAGT,MAAMC,QAAsBC,EAAAA,EAAAA,MACtBC,EAAQF,EAAcG,QAAUH,EAAcE,MAAQ,GAGtDE,QAAsBC,EAAAA,EAAAA,MACtBC,EAAQF,EAAcD,QAAUC,EAAc9G,KAAO,GAGrDiH,QAAwB9G,EAAAA,EAAAA,IAAc,CAAE+G,SAAU,GAAIC,SAAU,GAAIC,KAAM,EAAGrG,MAAO,MACpFsG,EAAUJ,EAAgBJ,QAAUI,EAAgBjH,KAAO,GAG3DgG,EAAaY,EAAMjG,OACnBsF,EAAcW,EAAMU,QAAOC,IAAMA,EAAEC,YAAW7G,OAC9CuF,EAAac,EAAMrG,OACnBwF,EAAekB,EAAQ1G,OAGvByF,EAAeiB,EAAQ1G,OAAS,EAClC0G,EAAQI,QAAO,CAACC,EAAKC,IAAWD,GAAOC,EAAOC,YAAc,IAAI,GAAKP,EAAQ1G,OAC7E,EAGE0F,EAAiBL,EAAa,EAAKG,EAAeH,EAAc,IAAM,EAE5EF,EAAS,CACPE,aACAC,cACAC,aACAC,eACAC,aAAcyB,KAAKC,MAAM1B,GACzBC,eAAgBwB,KAAKC,MAAMzB,KAG7BC,GAAW,GACXX,GAASoC,EAAAA,EAAAA,MACX,CAAE,MAAO9H,GACPiF,QAAQjF,MAAM,iCAAkCA,GAChDqG,GAAW,GACXX,GAASoC,EAAAA,EAAAA,MACX,GAKIC,EAAe,CACnB,CACEpG,MAAO,eACPqG,YAAa,mCACb5E,KAAMI,EAAAA,IACNF,KAAM,eACNC,MAAO,eAET,CACE5B,MAAO,cACPqG,YAAa,8BACb5E,KAAMK,EAAAA,IACNH,KAAM,mBACNC,MAAO,gBAET,CACE5B,MAAO,kBACPqG,YAAa,4BACb5E,KAAMM,EAAAA,IACNJ,KAAM,yBACNC,MAAO,iBAET,CACE5B,MAAO,eACPqG,YAAa,4BACb5E,KAAMO,EAAAA,IACNL,KAAM,iBACNC,MAAO,iBAET,CACE5B,MAAO,gBACPqG,YAAa,qBACb5E,KAAMQ,EAAAA,IACNN,KAAM,uBACNC,MAAO,eAET,CACE5B,MAAO,mBACPqG,YAAa,yBACb5E,KAAM6E,EAAAA,IACN3E,KAAM,eACNC,MAAO,kBAIL2E,EAAqB,EACzBjG,EAAAA,EAAAA,MAACC,EAAAA,EAAOmC,OAAM,CAEZC,WAAY,CAAEC,MAAO,MACrBC,SAAU,CAAED,MAAO,KACnBE,QAASA,IAAM7B,EAAS,gBACxBd,UAAU,uHAAsHJ,SAAA,EAEhIgB,EAAAA,EAAAA,KAACc,EAAAA,IAAO,CAAC1B,UAAU,aACnBY,EAAAA,EAAAA,KAAA,QAAMZ,UAAU,mBAAkBJ,SAAC,mBAP/B,UASNO,EAAAA,EAAAA,MAACC,EAAAA,EAAOmC,OAAM,CAEZC,WAAY,CAAEC,MAAO,MACrBC,SAAU,CAAED,MAAO,KACnBE,QAASA,IAAM7B,EAAS,oBACxBd,UAAU,yHAAwHJ,SAAA,EAElIgB,EAAAA,EAAAA,KAACyF,EAAAA,IAAM,CAACrG,UAAU,aAClBY,EAAAA,EAAAA,KAAA,QAAMZ,UAAU,mBAAkBJ,SAAC,kBAP/B,UASNO,EAAAA,EAAAA,MAACC,EAAAA,EAAOmC,OAAM,CAEZC,WAAY,CAAEC,MAAO,MACrBC,SAAU,CAAED,MAAO,KACnBE,QAASA,IAAM7B,EAAS,uBACxBd,UAAU,2HAA0HJ,SAAA,EAEpIgB,EAAAA,EAAAA,KAAC0F,EAAAA,IAAO,CAACtG,UAAU,aACnBY,EAAAA,EAAAA,KAAA,QAAMZ,UAAU,mBAAkBJ,SAAC,mBAP/B,OAWR,OACEO,EAAAA,EAAAA,MAACoG,EAAAA,EAAW,CAAC9C,YAAY,EAAM7D,SAAA,EAE7BgB,EAAAA,EAAAA,KAAA,OAAKZ,UAAU,eAAcJ,UAC3BgB,EAAAA,EAAAA,KAAA,OAAKZ,UAAU,iFAAgFJ,UAC7FO,EAAAA,EAAAA,MAAA,OAAKH,UAAU,qEAAoEJ,SAAA,EACjFO,EAAAA,EAAAA,MAAA,OAAAP,SAAA,EACEO,EAAAA,EAAAA,MAAA,MAAIH,UAAU,sCAAqCJ,SAAA,CAAC,iBAC/B,OAAJsB,QAAI,IAAJA,OAAI,EAAJA,EAAM4B,KAAK,qBAE5BlC,EAAAA,EAAAA,KAAA,KAAGZ,UAAU,qCAAoCJ,SAAC,sEAIpDgB,EAAAA,EAAAA,KAAA,OAAKZ,UAAU,uBAAsBJ,SAClCwG,YAOTjG,EAAAA,EAAAA,MAAA,OAAKH,UAAU,6EAA4EJ,SAAA,EACzFgB,EAAAA,EAAAA,KAAC4F,EAAAA,EAAS,CAACxG,UAAU,kEAAiEJ,UACpFO,EAAAA,EAAAA,MAAA,OAAKH,UAAU,oCAAmCJ,SAAA,EAChDO,EAAAA,EAAAA,MAAA,OAAAP,SAAA,EACEgB,EAAAA,EAAAA,KAAA,KAAGZ,UAAU,yCAAwCJ,SAAC,oBACtDgB,EAAAA,EAAAA,KAAA,KAAGZ,UAAU,iCAAgCJ,SAAEkE,EAAMG,cACrDrD,EAAAA,EAAAA,KAAA,KAAGZ,UAAU,6BAA4BJ,SAAC,yBAE5CgB,EAAAA,EAAAA,KAAA,OAAKZ,UAAU,oEAAmEJ,UAChFgB,EAAAA,EAAAA,KAACc,EAAAA,IAAO,CAAC1B,UAAU,oBAKzBY,EAAAA,EAAAA,KAAC4F,EAAAA,EAAS,CAACxG,UAAU,oEAAmEJ,UACtFO,EAAAA,EAAAA,MAAA,OAAKH,UAAU,oCAAmCJ,SAAA,EAChDO,EAAAA,EAAAA,MAAA,OAAAP,SAAA,EACEgB,EAAAA,EAAAA,KAAA,KAAGZ,UAAU,0CAAyCJ,SAAC,kBACvDgB,EAAAA,EAAAA,KAAA,KAAGZ,UAAU,iCAAgCJ,SAAEkE,EAAMI,eACrDtD,EAAAA,EAAAA,KAAA,KAAGZ,UAAU,8BAA6BJ,SAAC,yBAE7CgB,EAAAA,EAAAA,KAAA,OAAKZ,UAAU,oEAAmEJ,UAChFgB,EAAAA,EAAAA,KAAC6F,EAAAA,IAAY,CAACzG,UAAU,oBAK9BY,EAAAA,EAAAA,KAAC4F,EAAAA,EAAS,CAACxG,UAAU,sEAAqEJ,UACxFO,EAAAA,EAAAA,MAAA,OAAKH,UAAU,oCAAmCJ,SAAA,EAChDO,EAAAA,EAAAA,MAAA,OAAAP,SAAA,EACEgB,EAAAA,EAAAA,KAAA,KAAGZ,UAAU,2CAA0CJ,SAAC,iBACxDgB,EAAAA,EAAAA,KAAA,KAAGZ,UAAU,iCAAgCJ,SAAEkE,EAAMK,cACrDvD,EAAAA,EAAAA,KAAA,KAAGZ,UAAU,+BAA8BJ,SAAC,wBAE9CgB,EAAAA,EAAAA,KAAA,OAAKZ,UAAU,oEAAmEJ,UAChFgB,EAAAA,EAAAA,KAACe,EAAAA,IAAU,CAAC3B,UAAU,oBAK5BY,EAAAA,EAAAA,KAAC4F,EAAAA,EAAS,CAACxG,UAAU,sEAAqEJ,UACxFO,EAAAA,EAAAA,MAAA,OAAKH,UAAU,oCAAmCJ,SAAA,EAChDO,EAAAA,EAAAA,MAAA,OAAAP,SAAA,EACEgB,EAAAA,EAAAA,KAAA,KAAGZ,UAAU,2CAA0CJ,SAAC,eACxDO,EAAAA,EAAAA,MAAA,KAAGH,UAAU,iCAAgCJ,SAAA,CAAEkE,EAAMO,aAAa,QAClEzD,EAAAA,EAAAA,KAAA,KAAGZ,UAAU,+BAA8BJ,SAAC,4BAE9CgB,EAAAA,EAAAA,KAAA,OAAKZ,UAAU,oEAAmEJ,UAChFgB,EAAAA,EAAAA,KAAC8F,EAAAA,IAAO,CAAC1G,UAAU,uBAO3BG,EAAAA,EAAAA,MAAA,OAAKH,UAAU,qDAAoDJ,SAAA,EAEjEgB,EAAAA,EAAAA,KAAC4F,EAAAA,EAAS,CACR3G,MAAM,gBACNC,SAAS,8BAA6BF,UAEtCgB,EAAAA,EAAAA,KAAA,OAAKZ,UAAU,wCAAuCJ,SACnDqG,EAAa5C,KAAI,CAACsD,EAAQC,KACzB,MAAMtD,EAAgBqD,EAAOrF,KAC7B,OACEnB,EAAAA,EAAAA,MAACC,EAAAA,EAAOmC,OAAM,CAEZjC,QAAS,CAAEC,QAAS,EAAGC,EAAG,IAC1BC,QAAS,CAAEF,QAAS,EAAGC,EAAG,GAC1BE,WAAY,CAAEC,SAAU,GAAK+C,MAAe,GAARkD,GACpCjE,QAASA,IAAM7B,EAAS6F,EAAOnF,MAC/BxB,UAAS,kBAAAV,OAAoBqH,EAAOlF,MAAK,+FAA8F7B,SAAA,EAEvIgB,EAAAA,EAAAA,KAAC0C,EAAa,CAACtD,UAAU,kBACzBY,EAAAA,EAAAA,KAAA,MAAIZ,UAAU,0CAAyCJ,SAAE+G,EAAO9G,SAChEe,EAAAA,EAAAA,KAAA,KAAGZ,UAAU,gCAA+BJ,SAAE+G,EAAOT,gBAThDS,EAAO9G,MAUE,SAOxBe,EAAAA,EAAAA,KAAC4F,EAAAA,EAAS,CACR3G,MAAM,wBACNC,SAAS,8BAA6BF,UAEtCO,EAAAA,EAAAA,MAAA,OAAKH,UAAU,YAAWJ,SAAA,EACxBO,EAAAA,EAAAA,MAAA,OAAAP,SAAA,EACEO,EAAAA,EAAAA,MAAA,OAAKH,UAAU,yCAAwCJ,SAAA,EACrDgB,EAAAA,EAAAA,KAAA,QAAMZ,UAAU,qCAAoCJ,SAAC,qBACrDO,EAAAA,EAAAA,MAAA,QAAMH,UAAU,mCAAkCJ,SAAA,CAAEkE,EAAMQ,eAAe,WAE3E1D,EAAAA,EAAAA,KAACiG,EAAAA,EAAQ,CACPC,QAAShD,EAAMQ,eACfyC,YAAa,CACX,KAAM,UACN,OAAQ,WAEV/G,UAAU,aAIdG,EAAAA,EAAAA,MAAA,OAAAP,SAAA,EACEO,EAAAA,EAAAA,MAAA,OAAKH,UAAU,yCAAwCJ,SAAA,EACrDgB,EAAAA,EAAAA,KAAA,QAAMZ,UAAU,qCAAoCJ,SAAC,mBACrDO,EAAAA,EAAAA,MAAA,QAAMH,UAAU,mCAAkCJ,SAAA,CAAEkE,EAAMO,aAAa,WAEzEzD,EAAAA,EAAAA,KAACiG,EAAAA,EAAQ,CACPC,QAAShD,EAAMO,aACf0C,YAAa,CACX,KAAM,UACN,OAAQ,WAEV/G,UAAU,aAIdG,EAAAA,EAAAA,MAAA,OAAKH,UAAU,wDAAuDJ,SAAA,EACpEO,EAAAA,EAAAA,MAAA,OAAKH,UAAU,cAAaJ,SAAA,EAC1BgB,EAAAA,EAAAA,KAAA,KAAGZ,UAAU,oCAAmCJ,SAAEkE,EAAMM,gBACxDxD,EAAAA,EAAAA,KAAA,KAAGZ,UAAU,yBAAwBJ,SAAC,uBAExCO,EAAAA,EAAAA,MAAA,OAAKH,UAAU,cAAaJ,SAAA,EAC1BO,EAAAA,EAAAA,MAAA,KAAGH,UAAU,oCAAmCJ,SAAA,CAAEkG,KAAKC,MAAOjC,EAAMI,YAAcJ,EAAMG,WAAc,MAAQ,EAAE,QAChHrD,EAAAA,EAAAA,KAAA,KAAGZ,UAAU,yBAAwBJ,SAAC,kCAMpC,C", "sources": ["apicalls/reports.js", "components/AdminCard.js", "components/AdminTopNavigation.js", "components/AdminLayout.js", "pages/admin/Dashboard/index.js"], "sourcesContent": ["const { default: axiosInstance } = require(\".\");\r\n\r\n// add report\r\nexport const addReport = async (payload) => {\r\n    try {\r\n        const response = await axiosInstance.post(\"/api/reports/add-report\", payload);\r\n        return response.data;\r\n    } catch (error) {\r\n        return error.response.data;\r\n    }\r\n}\r\n\r\n// get all reports\r\nexport const getAllReports = async (filters) => {\r\n    try {\r\n        const response = await axiosInstance.post(\"/api/reports/get-all-reports\", filters);\r\n        return response.data;\r\n    } catch (error) {\r\n        return error.response.data;\r\n    }\r\n}\r\n\r\n// get all reports by user\r\nexport const getAllReportsByUser = async (payload) => {\r\n    try {\r\n        const response = await axiosInstance.post(\"/api/reports/get-all-reports-by-user\", payload);\r\n        return response.data;\r\n    } catch (error) {\r\n        return error.response.data;\r\n    }\r\n}\r\n\r\n// get all reports for ranking (legacy)\r\nexport const getAllReportsForRanking = async (filters) => {\r\n    try {\r\n        const response = await axiosInstance.get(\"/api/reports/get-all-reports-for-ranking\");\r\n        return response.data;\r\n    } catch (error) {\r\n        return error.response.data;\r\n    }\r\n}\r\n\r\n// Enhanced XP-based ranking endpoints\r\nexport const getEnhancedLeaderboard = async (level = 'all', limit = 1000) => {\r\n    try {\r\n        const response = await axiosInstance.get(`/api/quiz/enhanced-leaderboard?level=${level}&limit=${limit}`);\r\n        return response.data;\r\n    } catch (error) {\r\n        return error.response.data;\r\n    }\r\n}\r\n\r\nexport const getXPLeaderboard = async (options = {}) => {\r\n    try {\r\n        const params = new URLSearchParams();\r\n        if (options.limit) params.append('limit', options.limit);\r\n        if (options.classFilter) params.append('classFilter', options.classFilter);\r\n        if (options.levelFilter) params.append('levelFilter', options.levelFilter);\r\n        if (options.seasonFilter) params.append('seasonFilter', options.seasonFilter);\r\n        if (options.includeInactive) params.append('includeInactive', options.includeInactive);\r\n\r\n        const response = await axiosInstance.get(`/api/quiz/xp-leaderboard?${params.toString()}`);\r\n        return response.data;\r\n    } catch (error) {\r\n        return error.response.data;\r\n    }\r\n}\r\n\r\nexport const getUserRanking = async (userId, context = 5) => {\r\n    try {\r\n        const response = await axiosInstance.get(`/api/quiz/user-ranking/${userId}?context=${context}`);\r\n        return response.data;\r\n    } catch (error) {\r\n        return error.response.data;\r\n    }\r\n}\r\n\r\nexport const getClassRankings = async (className, limit = 50) => {\r\n    try {\r\n        const response = await axiosInstance.get(`/api/quiz/class-rankings/${className}?limit=${limit}`);\r\n        return response.data;\r\n    } catch (error) {\r\n        return error.response.data;\r\n    }\r\n}\r\n\r\n// XP Dashboard endpoints\r\nexport const getXPDashboard = async () => {\r\n    try {\r\n        const response = await axiosInstance.get(\"/api/xp-dashboard/dashboard\");\r\n        return response.data;\r\n    } catch (error) {\r\n        return error.response.data;\r\n    }\r\n}\r\n\r\nexport const getClassLeaderboard = async () => {\r\n    try {\r\n        const response = await axiosInstance.get(\"/api/xp-dashboard/class-leaderboard\");\r\n        return response.data;\r\n    } catch (error) {\r\n        return error.response.data;\r\n    }\r\n}\r\n", "import React from 'react';\nimport { motion } from 'framer-motion';\n\nconst AdminCard = ({ \n  children, \n  title, \n  subtitle, \n  actions, \n  className = '', \n  noPadding = false,\n  loading = false \n}) => {\n  return (\n    <motion.div\n      initial={{ opacity: 0, y: 20 }}\n      animate={{ opacity: 1, y: 0 }}\n      transition={{ duration: 0.3 }}\n      className={`bg-white rounded-xl sm:rounded-2xl shadow-lg border border-slate-200/50 hover:shadow-xl transition-all duration-300 ${className}`}\n    >\n      {/* Card Header */}\n      {(title || actions) && (\n        <div className=\"px-4 sm:px-6 py-4 sm:py-5 border-b border-slate-100\">\n          <div className=\"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3\">\n            {title && (\n              <div>\n                <h3 className=\"text-lg sm:text-xl font-semibold text-slate-800\">\n                  {title}\n                </h3>\n                {subtitle && (\n                  <p className=\"text-sm text-slate-600 mt-1\">\n                    {subtitle}\n                  </p>\n                )}\n              </div>\n            )}\n            \n            {actions && (\n              <div className=\"flex flex-wrap gap-2\">\n                {actions}\n              </div>\n            )}\n          </div>\n        </div>\n      )}\n\n      {/* Card Content */}\n      <div className={noPadding ? '' : 'p-4 sm:p-6'}>\n        {loading ? (\n          <div className=\"flex items-center justify-center py-8 sm:py-12\">\n            <div className=\"animate-spin rounded-full h-8 w-8 sm:h-12 sm:w-12 border-b-2 border-blue-600\"></div>\n          </div>\n        ) : (\n          children\n        )}\n      </div>\n    </motion.div>\n  );\n};\n\nexport default AdminCard;\n", "import React from 'react';\nimport { useNavigate, useLocation } from 'react-router-dom';\nimport { motion } from 'framer-motion';\nimport { useSelector } from 'react-redux';\nimport '../styles/admin-navigation.css';\nimport {\n  TbArrowLeft,\n  TbUsers,\n  TbBook,\n  TbFileText,\n  TbChartBar,\n  TbRobot,\n  TbBell,\n  TbSettings,\n  TbDashboard,\n  TbLogout,\n  TbHome,\n  TbUser\n} from 'react-icons/tb';\n\nconst AdminTopNavigation = () => {\n  const navigate = useNavigate();\n  const location = useLocation();\n  const { user } = useSelector((state) => state.user);\n\n  const adminMenuItems = [\n    {\n      title: 'Dashboard',\n      icon: TbDashboard,\n      path: '/admin/dashboard',\n      color: 'text-blue-500'\n    },\n    {\n      title: 'Users',\n      icon: TbUsers,\n      path: '/admin/users',\n      color: 'text-green-500'\n    },\n    {\n      title: 'Exams',\n      icon: TbFileText,\n      path: '/admin/exams',\n      color: 'text-purple-500'\n    },\n    {\n      title: 'Study Materials',\n      icon: TbBook,\n      path: '/admin/study-materials',\n      color: 'text-orange-500'\n    },\n    {\n      title: 'Reports',\n      icon: TbChartBar,\n      path: '/admin/reports',\n      color: 'text-indigo-500'\n    },\n    {\n      title: 'Notifications',\n      icon: TbBell,\n      path: '/admin/notifications',\n      color: 'text-yellow-500'\n    }\n  ];\n\n  const getCurrentPageInfo = () => {\n    const currentPath = location.pathname;\n    const currentItem = adminMenuItems.find(item => currentPath.startsWith(item.path));\n    return currentItem || { title: 'Admin Panel', icon: TbDashboard };\n  };\n\n  const isActivePath = (path) => {\n    return location.pathname.startsWith(path);\n  };\n\n  const handleLogout = () => {\n    try {\n      localStorage.removeItem('token');\n      localStorage.removeItem('user');\n      navigate('/login');\n    } catch (error) {\n      console.error('Error logging out:', error);\n    }\n  };\n\n  const currentPage = getCurrentPageInfo();\n  const isDashboard = location.pathname === '/admin/dashboard';\n\n  return (\n    <div className=\"bg-white border-b border-slate-200 sticky top-0 z-50\">\n      <div className=\"px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex items-center justify-between h-16\">\n          {/* Left side - Back button and current page */}\n          <div className=\"flex items-center space-x-4\">\n            {!isDashboard && (\n              <motion.button\n                whileHover={{ scale: 1.05 }}\n                whileTap={{ scale: 0.95 }}\n                onClick={() => navigate('/admin/dashboard')}\n                className=\"p-2 rounded-lg bg-slate-100 hover:bg-slate-200 transition-colors duration-200\"\n              >\n                <TbArrowLeft className=\"w-5 h-5 text-slate-600\" />\n              </motion.button>\n            )}\n            \n            <div className=\"flex items-center space-x-3\">\n              <div className=\"w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center\">\n                <currentPage.icon className=\"w-5 h-5 text-white\" />\n              </div>\n              <div>\n                <h1 className=\"text-lg font-semibold text-slate-900\">{currentPage.title}</h1>\n                <p className=\"text-xs text-slate-500\">BrainWave Admin</p>\n              </div>\n            </div>\n          </div>\n\n          {/* Right side - User info and logout */}\n          <div className=\"flex items-center space-x-4\">\n            <div className=\"hidden sm:flex items-center space-x-3\">\n              <div className=\"w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center\">\n                <TbUser className=\"w-4 h-4 text-white\" />\n              </div>\n              <div className=\"text-right\">\n                <p className=\"text-sm font-medium text-slate-900\">{user?.name}</p>\n                <p className=\"text-xs text-slate-500\">Administrator</p>\n              </div>\n            </div>\n            \n            <div className=\"flex items-center space-x-2\">\n              <motion.button\n                whileHover={{ scale: 1.05 }}\n                whileTap={{ scale: 0.95 }}\n                onClick={() => navigate('/')}\n                className=\"p-2 rounded-lg bg-slate-100 hover:bg-slate-200 transition-colors duration-200\"\n                title=\"View Site\"\n              >\n                <TbHome className=\"w-4 h-4 text-slate-600\" />\n              </motion.button>\n              \n              <motion.button\n                whileHover={{ scale: 1.05 }}\n                whileTap={{ scale: 0.95 }}\n                onClick={handleLogout}\n                className=\"p-2 rounded-lg bg-red-100 hover:bg-red-200 transition-colors duration-200\"\n                title=\"Logout\"\n              >\n                <TbLogout className=\"w-4 h-4 text-red-600\" />\n              </motion.button>\n            </div>\n          </div>\n        </div>\n\n        {/* Navigation options below title */}\n        <div className=\"pb-4\">\n          <div className=\"flex items-center space-x-1 overflow-x-auto scrollbar-hide\">\n            {adminMenuItems.map((item) => {\n              const IconComponent = item.icon;\n              const isActive = isActivePath(item.path);\n\n              return (\n                <motion.button\n                  key={item.path}\n                  whileHover={{ scale: 1.02 }}\n                  whileTap={{ scale: 0.98 }}\n                  onClick={() => navigate(item.path)}\n                  className={`flex items-center space-x-2 px-3 py-2 rounded-lg text-sm font-medium transition-all duration-200 whitespace-nowrap flex-shrink-0 ${\n                    isActive\n                      ? 'bg-blue-100 text-blue-700 shadow-sm border border-blue-200'\n                      : 'text-slate-600 hover:bg-slate-100 hover:text-slate-900'\n                  }`}\n                >\n                  <IconComponent className={`w-4 h-4 ${isActive ? 'text-blue-600' : item.color}`} />\n                  <span className=\"hidden sm:inline\">{item.title}</span>\n                  <span className=\"sm:hidden text-xs\">{item.title.split(' ')[0]}</span>\n                </motion.button>\n              );\n            })}\n          </div>\n\n          {/* Mobile scroll indicator */}\n          <div className=\"sm:hidden mt-2 flex justify-center\">\n            <div className=\"text-xs text-slate-400\">← Swipe to see more options →</div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default AdminTopNavigation;\n", "import React from 'react';\nimport { motion } from 'framer-motion';\nimport { useSelector } from 'react-redux';\nimport AdminTopNavigation from './AdminTopNavigation';\n\nconst AdminLayout = ({ children, title, subtitle, actions, showHeader = true }) => {\n  const { user } = useSelector((state) => state.user);\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50\">\n      {/* Top Navigation */}\n      <AdminTopNavigation />\n\n      {/* Admin Content Container */}\n      <div className=\"p-4 sm:p-6 lg:p-8 max-w-7xl mx-auto\">\n        {/* Page Header - Optional */}\n        {showHeader && (title || subtitle || actions) && (\n          <motion.div\n            initial={{ opacity: 0, y: -20 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.5 }}\n            className=\"mb-6 sm:mb-8\"\n          >\n            <div className=\"bg-white rounded-2xl shadow-lg border border-slate-200/50 p-6 sm:p-8\">\n              <div className=\"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4\">\n                <div className=\"flex-1\">\n                  {title && (\n                    <h1 className=\"text-2xl sm:text-3xl lg:text-4xl font-bold bg-gradient-to-r from-slate-800 to-blue-600 bg-clip-text text-transparent mb-2\">\n                      {title}\n                    </h1>\n                  )}\n                  {subtitle && (\n                    <p className=\"text-slate-600 text-sm sm:text-base lg:text-lg\">\n                      {subtitle}\n                    </p>\n                  )}\n                </div>\n\n                {/* Actions */}\n                {actions && (\n                  <div className=\"flex flex-wrap gap-2 sm:gap-3\">\n                    {actions}\n                  </div>\n                )}\n              </div>\n            </div>\n          </motion.div>\n        )}\n\n        {/* Main Content */}\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.5, delay: 0.1 }}\n          className=\"space-y-6\"\n        >\n          {children}\n        </motion.div>\n      </div>\n    </div>\n  );\n};\n\nexport default AdminLayout;\n", "import React, { useState, useEffect } from 'react';\nimport { motion } from 'framer-motion';\nimport { useDispatch, useSelector } from 'react-redux';\nimport { useNavigate } from 'react-router-dom';\nimport { Progress } from 'antd';\nimport {\n  TbU<PERSON><PERSON>,\n  TbB<PERSON>,\n  TbFileText,\n  TbChartBar,\n  TbTrendingUp,\n  TbTarget,\n  TbAward,\n  TbClock,\n  TbPlus,\n  TbEye,\n  TbRobot,\n  TbBell,\n  TbMessageCircle\n} from 'react-icons/tb';\nimport { getAllUsers } from '../../../apicalls/users';\nimport { getAllExams } from '../../../apicalls/exams';\nimport { getAllReports } from '../../../apicalls/reports';\nimport { HideLoading, ShowLoading } from '../../../redux/loaderSlice';\nimport AdminLayout from '../../../components/AdminLayout';\nimport AdminCard from '../../../components/AdminCard';\n\nconst AdminDashboard = () => {\n  const dispatch = useDispatch();\n  const navigate = useNavigate();\n  const { user } = useSelector((state) => state.user);\n  const [stats, setStats] = useState({\n    totalUsers: 0,\n    activeUsers: 0,\n    totalExams: 0,\n    totalReports: 0,\n    averageScore: 0,\n    completionRate: 0\n  });\n  const [loading, setLoading] = useState(true);\n\n  useEffect(() => {\n    fetchDashboardData();\n  }, []);\n\n  const fetchDashboardData = async () => {\n    try {\n      setLoading(true);\n      dispatch(ShowLoading());\n      \n      // Fetch users data\n      const usersResponse = await getAllUsers();\n      const users = usersResponse.success ? usersResponse.users : [];\n\n      // Fetch exams data\n      const examsResponse = await getAllExams();\n      const exams = examsResponse.success ? examsResponse.data : [];\n\n      // Fetch reports data (with empty filters to get all reports)\n      const reportsResponse = await getAllReports({ examName: '', userName: '', page: 1, limit: 1000 });\n      const reports = reportsResponse.success ? reportsResponse.data : [];\n\n      // Calculate statistics\n      const totalUsers = users.length;\n      const activeUsers = users.filter(u => !u.isBlocked).length;\n      const totalExams = exams.length;\n      const totalReports = reports.length;\n      \n      // Calculate average score from reports\n      const averageScore = reports.length > 0 \n        ? reports.reduce((sum, report) => sum + (report.percentage || 0), 0) / reports.length\n        : 0;\n      \n      // Calculate completion rate\n      const completionRate = totalUsers > 0 ? (totalReports / totalUsers) * 100 : 0;\n\n      setStats({\n        totalUsers,\n        activeUsers,\n        totalExams,\n        totalReports,\n        averageScore: Math.round(averageScore),\n        completionRate: Math.round(completionRate)\n      });\n\n      setLoading(false);\n      dispatch(HideLoading());\n    } catch (error) {\n      console.error('Error fetching dashboard data:', error);\n      setLoading(false);\n      dispatch(HideLoading());\n    }\n  };\n\n\n\n  const quickActions = [\n    {\n      title: 'Manage Users',\n      description: 'View and manage student accounts',\n      icon: TbUsers,\n      path: '/admin/users',\n      color: 'bg-blue-500'\n    },\n    {\n      title: 'Create Exam',\n      description: 'Add new exams and questions',\n      icon: TbFileText,\n      path: '/admin/exams/add',\n      color: 'bg-green-500'\n    },\n    {\n      title: 'Study Materials',\n      description: 'Manage learning resources',\n      icon: TbBook,\n      path: '/admin/study-materials',\n      color: 'bg-orange-500'\n    },\n    {\n      title: 'View Reports',\n      description: 'Analytics and performance',\n      icon: TbChartBar,\n      path: '/admin/reports',\n      color: 'bg-indigo-500'\n    },\n    {\n      title: 'Notifications',\n      description: 'Send announcements',\n      icon: TbBell,\n      path: '/admin/notifications',\n      color: 'bg-pink-500'\n    },\n    {\n      title: 'Forum Management',\n      description: 'Manage community forum',\n      icon: TbMessageCircle,\n      path: '/admin/forum',\n      color: 'bg-purple-500'\n    }\n  ];\n\n  const quickActionButtons = [\n    <motion.button\n      key=\"users\"\n      whileHover={{ scale: 1.02 }}\n      whileTap={{ scale: 0.98 }}\n      onClick={() => navigate('/admin/users')}\n      className=\"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors duration-200 flex items-center gap-2\"\n    >\n      <TbUsers className=\"w-4 h-4\" />\n      <span className=\"hidden sm:inline\">Manage Users</span>\n    </motion.button>,\n    <motion.button\n      key=\"exams\"\n      whileHover={{ scale: 1.02 }}\n      whileTap={{ scale: 0.98 }}\n      onClick={() => navigate('/admin/exams/add')}\n      className=\"px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors duration-200 flex items-center gap-2\"\n    >\n      <TbPlus className=\"w-4 h-4\" />\n      <span className=\"hidden sm:inline\">Create Exam</span>\n    </motion.button>,\n    <motion.button\n      key=\"ai\"\n      whileHover={{ scale: 1.02 }}\n      whileTap={{ scale: 0.98 }}\n      onClick={() => navigate('/admin/ai-questions')}\n      className=\"px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors duration-200 flex items-center gap-2\"\n    >\n      <TbRobot className=\"w-4 h-4\" />\n      <span className=\"hidden sm:inline\">AI Questions</span>\n    </motion.button>,\n  ];\n\n  return (\n    <AdminLayout showHeader={false}>\n      {/* Welcome Section */}\n      <div className=\"mb-6 sm:mb-8\">\n        <div className=\"bg-gradient-to-r from-blue-600 to-purple-600 rounded-2xl p-6 sm:p-8 text-white\">\n          <div className=\"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4\">\n            <div>\n              <h1 className=\"text-2xl sm:text-3xl font-bold mb-2\">\n                Welcome back, {user?.name}! 👋\n              </h1>\n              <p className=\"text-blue-100 text-sm sm:text-base\">\n                Here's what's happening with your educational platform today.\n              </p>\n            </div>\n            <div className=\"flex flex-wrap gap-2\">\n              {quickActionButtons}\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Statistics Cards */}\n      <div className=\"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6 mb-6 sm:mb-8\">\n        <AdminCard className=\"bg-gradient-to-br from-blue-500 to-blue-600 text-white border-0\">\n          <div className=\"flex items-center justify-between\">\n            <div>\n              <p className=\"text-blue-100 text-sm font-medium mb-1\">Total Students</p>\n              <p className=\"text-2xl sm:text-3xl font-bold\">{stats.totalUsers}</p>\n              <p className=\"text-blue-200 text-xs mt-1\">Registered users</p>\n            </div>\n            <div className=\"w-12 h-12 bg-white/20 rounded-xl flex items-center justify-center\">\n              <TbUsers className=\"w-6 h-6\" />\n            </div>\n          </div>\n        </AdminCard>\n\n        <AdminCard className=\"bg-gradient-to-br from-green-500 to-green-600 text-white border-0\">\n          <div className=\"flex items-center justify-between\">\n            <div>\n              <p className=\"text-green-100 text-sm font-medium mb-1\">Active Users</p>\n              <p className=\"text-2xl sm:text-3xl font-bold\">{stats.activeUsers}</p>\n              <p className=\"text-green-200 text-xs mt-1\">Currently active</p>\n            </div>\n            <div className=\"w-12 h-12 bg-white/20 rounded-xl flex items-center justify-center\">\n              <TbTrendingUp className=\"w-6 h-6\" />\n            </div>\n          </div>\n        </AdminCard>\n\n        <AdminCard className=\"bg-gradient-to-br from-purple-500 to-purple-600 text-white border-0\">\n          <div className=\"flex items-center justify-between\">\n            <div>\n              <p className=\"text-purple-100 text-sm font-medium mb-1\">Total Exams</p>\n              <p className=\"text-2xl sm:text-3xl font-bold\">{stats.totalExams}</p>\n              <p className=\"text-purple-200 text-xs mt-1\">Available exams</p>\n            </div>\n            <div className=\"w-12 h-12 bg-white/20 rounded-xl flex items-center justify-center\">\n              <TbFileText className=\"w-6 h-6\" />\n            </div>\n          </div>\n        </AdminCard>\n\n        <AdminCard className=\"bg-gradient-to-br from-orange-500 to-orange-600 text-white border-0\">\n          <div className=\"flex items-center justify-between\">\n            <div>\n              <p className=\"text-orange-100 text-sm font-medium mb-1\">Avg Score</p>\n              <p className=\"text-2xl sm:text-3xl font-bold\">{stats.averageScore}%</p>\n              <p className=\"text-orange-200 text-xs mt-1\">Overall performance</p>\n            </div>\n            <div className=\"w-12 h-12 bg-white/20 rounded-xl flex items-center justify-center\">\n              <TbAward className=\"w-6 h-6\" />\n            </div>\n          </div>\n        </AdminCard>\n      </div>\n\n      {/* Quick Actions and Analytics */}\n      <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6 sm:mb-8\">\n        {/* Quick Actions */}\n        <AdminCard \n          title=\"Quick Actions\" \n          subtitle=\"Common administrative tasks\"\n        >\n          <div className=\"grid grid-cols-1 sm:grid-cols-2 gap-4\">\n            {quickActions.map((action, index) => {\n              const IconComponent = action.icon;\n              return (\n                <motion.button\n                  key={action.title}\n                  initial={{ opacity: 0, y: 20 }}\n                  animate={{ opacity: 1, y: 0 }}\n                  transition={{ duration: 0.3, delay: index * 0.1 }}\n                  onClick={() => navigate(action.path)}\n                  className={`p-4 rounded-xl ${action.color} text-white hover:shadow-lg transition-all duration-300 transform hover:scale-105 text-left`}\n                >\n                  <IconComponent className=\"w-8 h-8 mb-3\" />\n                  <h3 className=\"font-semibold text-sm sm:text-base mb-1\">{action.title}</h3>\n                  <p className=\"text-xs sm:text-sm opacity-90\">{action.description}</p>\n                </motion.button>\n              );\n            })}\n          </div>\n        </AdminCard>\n\n        {/* Performance Analytics */}\n        <AdminCard \n          title=\"Performance Analytics\" \n          subtitle=\"System performance overview\"\n        >\n          <div className=\"space-y-6\">\n            <div>\n              <div className=\"flex justify-between items-center mb-2\">\n                <span className=\"text-sm font-medium text-slate-700\">Completion Rate</span>\n                <span className=\"text-sm font-bold text-slate-900\">{stats.completionRate}%</span>\n              </div>\n              <Progress \n                percent={stats.completionRate} \n                strokeColor={{\n                  '0%': '#3B82F6',\n                  '100%': '#8B5CF6',\n                }}\n                className=\"mb-4\"\n              />\n            </div>\n            \n            <div>\n              <div className=\"flex justify-between items-center mb-2\">\n                <span className=\"text-sm font-medium text-slate-700\">Average Score</span>\n                <span className=\"text-sm font-bold text-slate-900\">{stats.averageScore}%</span>\n              </div>\n              <Progress \n                percent={stats.averageScore} \n                strokeColor={{\n                  '0%': '#10B981',\n                  '100%': '#059669',\n                }}\n                className=\"mb-4\"\n              />\n            </div>\n\n            <div className=\"grid grid-cols-2 gap-4 pt-4 border-t border-slate-100\">\n              <div className=\"text-center\">\n                <p className=\"text-2xl font-bold text-slate-900\">{stats.totalReports}</p>\n                <p className=\"text-xs text-slate-600\">Total Attempts</p>\n              </div>\n              <div className=\"text-center\">\n                <p className=\"text-2xl font-bold text-slate-900\">{Math.round((stats.activeUsers / stats.totalUsers) * 100) || 0}%</p>\n                <p className=\"text-xs text-slate-600\">User Activity</p>\n              </div>\n            </div>\n          </div>\n        </AdminCard>\n      </div>\n    </AdminLayout>\n  );\n};\n\nexport default AdminDashboard;\n"], "names": ["default", "axiosInstance", "require", "addReport", "async", "post", "payload", "data", "error", "response", "getAllReports", "filters", "getAllReportsByUser", "getAllReportsForRanking", "get", "getXPLeaderboard", "options", "arguments", "length", "undefined", "params", "URLSearchParams", "limit", "append", "classFilter", "levelFilter", "seasonFilter", "includeInactive", "concat", "toString", "getUserRanking", "userId", "context", "_ref", "children", "title", "subtitle", "actions", "className", "noPadding", "loading", "_jsxs", "motion", "div", "initial", "opacity", "y", "animate", "transition", "duration", "_jsx", "AdminTopNavigation", "navigate", "useNavigate", "location", "useLocation", "user", "useSelector", "state", "adminMenuItems", "icon", "TbDashboard", "path", "color", "TbUsers", "TbFileText", "TbBook", "TbChartBar", "TbBell", "currentPage", "getCurrentPageInfo", "currentPath", "pathname", "find", "item", "startsWith", "isDashboard", "button", "whileHover", "scale", "whileTap", "onClick", "TbArrowLeft", "TbUser", "name", "TbHome", "handleLogout", "localStorage", "removeItem", "console", "TbLogout", "map", "IconComponent", "isActive", "split", "showHeader", "delay", "AdminDashboard", "dispatch", "useDispatch", "stats", "setStats", "useState", "totalUsers", "activeUsers", "totalExams", "totalReports", "averageScore", "completionRate", "setLoading", "useEffect", "fetchDashboardData", "ShowLoading", "usersResponse", "getAllUsers", "users", "success", "examsResponse", "getAllExams", "exams", "reportsResponse", "examName", "userName", "page", "reports", "filter", "u", "isBlocked", "reduce", "sum", "report", "percentage", "Math", "round", "HideLoading", "quickActions", "description", "TbMessageCircle", "quickActionButtons", "TbPlus", "TbRobot", "AdminLayout", "AdminCard", "TbTrendingUp", "TbAward", "action", "index", "Progress", "percent", "strokeColor"], "sourceRoot": ""}