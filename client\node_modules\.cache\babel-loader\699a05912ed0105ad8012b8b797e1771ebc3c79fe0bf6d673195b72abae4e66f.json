{"ast": null, "code": "import _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport classNames from 'classnames';\nimport KeyCode from \"rc-util/es/KeyCode\";\nimport * as React from 'react';\nimport { genDataNodeKey, getRemovable } from \"../util\";\nfunction TabNode(_ref) {\n  var _classNames;\n  var prefixCls = _ref.prefixCls,\n    id = _ref.id,\n    active = _ref.active,\n    _ref$tab = _ref.tab,\n    key = _ref$tab.key,\n    label = _ref$tab.label,\n    disabled = _ref$tab.disabled,\n    closeIcon = _ref$tab.closeIcon,\n    closable = _ref.closable,\n    renderWrapper = _ref.renderWrapper,\n    removeAriaLabel = _ref.removeAriaLabel,\n    editable = _ref.editable,\n    onClick = _ref.onClick,\n    onFocus = _ref.onFocus,\n    style = _ref.style;\n  var tabPrefix = \"\".concat(prefixCls, \"-tab\");\n  var removable = getRemovable(closable, closeIcon, editable, disabled);\n  function onInternalClick(e) {\n    if (disabled) {\n      return;\n    }\n    onClick(e);\n  }\n  function onRemoveTab(event) {\n    event.preventDefault();\n    event.stopPropagation();\n    editable.onEdit('remove', {\n      key: key,\n      event: event\n    });\n  }\n  var node = /*#__PURE__*/React.createElement(\"div\", {\n    key: key\n    // ref={ref}\n    ,\n\n    \"data-node-key\": genDataNodeKey(key),\n    className: classNames(tabPrefix, (_classNames = {}, _defineProperty(_classNames, \"\".concat(tabPrefix, \"-with-remove\"), removable), _defineProperty(_classNames, \"\".concat(tabPrefix, \"-active\"), active), _defineProperty(_classNames, \"\".concat(tabPrefix, \"-disabled\"), disabled), _classNames)),\n    style: style,\n    onClick: onInternalClick\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    role: \"tab\",\n    \"aria-selected\": active,\n    id: id && \"\".concat(id, \"-tab-\").concat(key),\n    className: \"\".concat(tabPrefix, \"-btn\"),\n    \"aria-controls\": id && \"\".concat(id, \"-panel-\").concat(key),\n    \"aria-disabled\": disabled,\n    tabIndex: disabled ? null : 0,\n    onClick: function onClick(e) {\n      e.stopPropagation();\n      onInternalClick(e);\n    },\n    onKeyDown: function onKeyDown(e) {\n      if ([KeyCode.SPACE, KeyCode.ENTER].includes(e.which)) {\n        e.preventDefault();\n        onInternalClick(e);\n      }\n    },\n    onFocus: onFocus\n  }, label), removable && /*#__PURE__*/React.createElement(\"button\", {\n    type: \"button\",\n    \"aria-label\": removeAriaLabel || 'remove',\n    tabIndex: 0,\n    className: \"\".concat(tabPrefix, \"-remove\"),\n    onClick: function onClick(e) {\n      e.stopPropagation();\n      onRemoveTab(e);\n    }\n  }, closeIcon || editable.removeIcon || '×'));\n  return renderWrapper ? renderWrapper(node) : node;\n}\nexport default TabNode;", "map": {"version": 3, "names": ["_defineProperty", "classNames", "KeyCode", "React", "genDataNodeKey", "getRemovable", "TabNode", "_ref", "_classNames", "prefixCls", "id", "active", "_ref$tab", "tab", "key", "label", "disabled", "closeIcon", "closable", "renderWrapper", "removeAriaLabel", "editable", "onClick", "onFocus", "style", "tabPrefix", "concat", "removable", "onInternalClick", "e", "onRemoveTab", "event", "preventDefault", "stopPropagation", "onEdit", "node", "createElement", "className", "role", "tabIndex", "onKeyDown", "SPACE", "ENTER", "includes", "which", "type", "removeIcon"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/node_modules/rc-tabs/es/TabNavList/TabNode.js"], "sourcesContent": ["import _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport classNames from 'classnames';\nimport KeyCode from \"rc-util/es/KeyCode\";\nimport * as React from 'react';\nimport { genDataNodeKey, getRemovable } from \"../util\";\nfunction TabNode(_ref) {\n  var _classNames;\n  var prefixCls = _ref.prefixCls,\n    id = _ref.id,\n    active = _ref.active,\n    _ref$tab = _ref.tab,\n    key = _ref$tab.key,\n    label = _ref$tab.label,\n    disabled = _ref$tab.disabled,\n    closeIcon = _ref$tab.closeIcon,\n    closable = _ref.closable,\n    renderWrapper = _ref.renderWrapper,\n    removeAriaLabel = _ref.removeAriaLabel,\n    editable = _ref.editable,\n    onClick = _ref.onClick,\n    onFocus = _ref.onFocus,\n    style = _ref.style;\n  var tabPrefix = \"\".concat(prefixCls, \"-tab\");\n  var removable = getRemovable(closable, closeIcon, editable, disabled);\n  function onInternalClick(e) {\n    if (disabled) {\n      return;\n    }\n    onClick(e);\n  }\n  function onRemoveTab(event) {\n    event.preventDefault();\n    event.stopPropagation();\n    editable.onEdit('remove', {\n      key: key,\n      event: event\n    });\n  }\n  var node = /*#__PURE__*/React.createElement(\"div\", {\n    key: key\n    // ref={ref}\n    ,\n    \"data-node-key\": genDataNodeKey(key),\n    className: classNames(tabPrefix, (_classNames = {}, _defineProperty(_classNames, \"\".concat(tabPrefix, \"-with-remove\"), removable), _defineProperty(_classNames, \"\".concat(tabPrefix, \"-active\"), active), _defineProperty(_classNames, \"\".concat(tabPrefix, \"-disabled\"), disabled), _classNames)),\n    style: style,\n    onClick: onInternalClick\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    role: \"tab\",\n    \"aria-selected\": active,\n    id: id && \"\".concat(id, \"-tab-\").concat(key),\n    className: \"\".concat(tabPrefix, \"-btn\"),\n    \"aria-controls\": id && \"\".concat(id, \"-panel-\").concat(key),\n    \"aria-disabled\": disabled,\n    tabIndex: disabled ? null : 0,\n    onClick: function onClick(e) {\n      e.stopPropagation();\n      onInternalClick(e);\n    },\n    onKeyDown: function onKeyDown(e) {\n      if ([KeyCode.SPACE, KeyCode.ENTER].includes(e.which)) {\n        e.preventDefault();\n        onInternalClick(e);\n      }\n    },\n    onFocus: onFocus\n  }, label), removable && /*#__PURE__*/React.createElement(\"button\", {\n    type: \"button\",\n    \"aria-label\": removeAriaLabel || 'remove',\n    tabIndex: 0,\n    className: \"\".concat(tabPrefix, \"-remove\"),\n    onClick: function onClick(e) {\n      e.stopPropagation();\n      onRemoveTab(e);\n    }\n  }, closeIcon || editable.removeIcon || '×'));\n  return renderWrapper ? renderWrapper(node) : node;\n}\nexport default TabNode;"], "mappings": "AAAA,OAAOA,eAAe,MAAM,2CAA2C;AACvE,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAOC,OAAO,MAAM,oBAAoB;AACxC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,cAAc,EAAEC,YAAY,QAAQ,SAAS;AACtD,SAASC,OAAOA,CAACC,IAAI,EAAE;EACrB,IAAIC,WAAW;EACf,IAAIC,SAAS,GAAGF,IAAI,CAACE,SAAS;IAC5BC,EAAE,GAAGH,IAAI,CAACG,EAAE;IACZC,MAAM,GAAGJ,IAAI,CAACI,MAAM;IACpBC,QAAQ,GAAGL,IAAI,CAACM,GAAG;IACnBC,GAAG,GAAGF,QAAQ,CAACE,GAAG;IAClBC,KAAK,GAAGH,QAAQ,CAACG,KAAK;IACtBC,QAAQ,GAAGJ,QAAQ,CAACI,QAAQ;IAC5BC,SAAS,GAAGL,QAAQ,CAACK,SAAS;IAC9BC,QAAQ,GAAGX,IAAI,CAACW,QAAQ;IACxBC,aAAa,GAAGZ,IAAI,CAACY,aAAa;IAClCC,eAAe,GAAGb,IAAI,CAACa,eAAe;IACtCC,QAAQ,GAAGd,IAAI,CAACc,QAAQ;IACxBC,OAAO,GAAGf,IAAI,CAACe,OAAO;IACtBC,OAAO,GAAGhB,IAAI,CAACgB,OAAO;IACtBC,KAAK,GAAGjB,IAAI,CAACiB,KAAK;EACpB,IAAIC,SAAS,GAAG,EAAE,CAACC,MAAM,CAACjB,SAAS,EAAE,MAAM,CAAC;EAC5C,IAAIkB,SAAS,GAAGtB,YAAY,CAACa,QAAQ,EAAED,SAAS,EAAEI,QAAQ,EAAEL,QAAQ,CAAC;EACrE,SAASY,eAAeA,CAACC,CAAC,EAAE;IAC1B,IAAIb,QAAQ,EAAE;MACZ;IACF;IACAM,OAAO,CAACO,CAAC,CAAC;EACZ;EACA,SAASC,WAAWA,CAACC,KAAK,EAAE;IAC1BA,KAAK,CAACC,cAAc,CAAC,CAAC;IACtBD,KAAK,CAACE,eAAe,CAAC,CAAC;IACvBZ,QAAQ,CAACa,MAAM,CAAC,QAAQ,EAAE;MACxBpB,GAAG,EAAEA,GAAG;MACRiB,KAAK,EAAEA;IACT,CAAC,CAAC;EACJ;EACA,IAAII,IAAI,GAAG,aAAahC,KAAK,CAACiC,aAAa,CAAC,KAAK,EAAE;IACjDtB,GAAG,EAAEA;IACL;IAAA;;IAEA,eAAe,EAAEV,cAAc,CAACU,GAAG,CAAC;IACpCuB,SAAS,EAAEpC,UAAU,CAACwB,SAAS,GAAGjB,WAAW,GAAG,CAAC,CAAC,EAAER,eAAe,CAACQ,WAAW,EAAE,EAAE,CAACkB,MAAM,CAACD,SAAS,EAAE,cAAc,CAAC,EAAEE,SAAS,CAAC,EAAE3B,eAAe,CAACQ,WAAW,EAAE,EAAE,CAACkB,MAAM,CAACD,SAAS,EAAE,SAAS,CAAC,EAAEd,MAAM,CAAC,EAAEX,eAAe,CAACQ,WAAW,EAAE,EAAE,CAACkB,MAAM,CAACD,SAAS,EAAE,WAAW,CAAC,EAAET,QAAQ,CAAC,EAAER,WAAW,CAAC,CAAC;IAClSgB,KAAK,EAAEA,KAAK;IACZF,OAAO,EAAEM;EACX,CAAC,EAAE,aAAazB,KAAK,CAACiC,aAAa,CAAC,KAAK,EAAE;IACzCE,IAAI,EAAE,KAAK;IACX,eAAe,EAAE3B,MAAM;IACvBD,EAAE,EAAEA,EAAE,IAAI,EAAE,CAACgB,MAAM,CAAChB,EAAE,EAAE,OAAO,CAAC,CAACgB,MAAM,CAACZ,GAAG,CAAC;IAC5CuB,SAAS,EAAE,EAAE,CAACX,MAAM,CAACD,SAAS,EAAE,MAAM,CAAC;IACvC,eAAe,EAAEf,EAAE,IAAI,EAAE,CAACgB,MAAM,CAAChB,EAAE,EAAE,SAAS,CAAC,CAACgB,MAAM,CAACZ,GAAG,CAAC;IAC3D,eAAe,EAAEE,QAAQ;IACzBuB,QAAQ,EAAEvB,QAAQ,GAAG,IAAI,GAAG,CAAC;IAC7BM,OAAO,EAAE,SAASA,OAAOA,CAACO,CAAC,EAAE;MAC3BA,CAAC,CAACI,eAAe,CAAC,CAAC;MACnBL,eAAe,CAACC,CAAC,CAAC;IACpB,CAAC;IACDW,SAAS,EAAE,SAASA,SAASA,CAACX,CAAC,EAAE;MAC/B,IAAI,CAAC3B,OAAO,CAACuC,KAAK,EAAEvC,OAAO,CAACwC,KAAK,CAAC,CAACC,QAAQ,CAACd,CAAC,CAACe,KAAK,CAAC,EAAE;QACpDf,CAAC,CAACG,cAAc,CAAC,CAAC;QAClBJ,eAAe,CAACC,CAAC,CAAC;MACpB;IACF,CAAC;IACDN,OAAO,EAAEA;EACX,CAAC,EAAER,KAAK,CAAC,EAAEY,SAAS,IAAI,aAAaxB,KAAK,CAACiC,aAAa,CAAC,QAAQ,EAAE;IACjES,IAAI,EAAE,QAAQ;IACd,YAAY,EAAEzB,eAAe,IAAI,QAAQ;IACzCmB,QAAQ,EAAE,CAAC;IACXF,SAAS,EAAE,EAAE,CAACX,MAAM,CAACD,SAAS,EAAE,SAAS,CAAC;IAC1CH,OAAO,EAAE,SAASA,OAAOA,CAACO,CAAC,EAAE;MAC3BA,CAAC,CAACI,eAAe,CAAC,CAAC;MACnBH,WAAW,CAACD,CAAC,CAAC;IAChB;EACF,CAAC,EAAEZ,SAAS,IAAII,QAAQ,CAACyB,UAAU,IAAI,GAAG,CAAC,CAAC;EAC5C,OAAO3B,aAAa,GAAGA,aAAa,CAACgB,IAAI,CAAC,GAAGA,IAAI;AACnD;AACA,eAAe7B,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}