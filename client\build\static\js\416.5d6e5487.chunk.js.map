{"version": 3, "file": "static/js/416.5d6e5487.chunk.js", "mappings": "gHACA,IAAIA,EAAS,CAAC,GAKb,SAASC,EAAKC,EAAQF,EAAQG,EAAUC,GACvC,IAAIC,KACFH,EAAOI,QACPJ,EAAOK,MACPL,EAAOM,SACPN,EAAOO,iBACPP,EAAOQ,mCACPR,EAAOS,mBACPT,EAAOS,kBAAkBC,UAAUC,4BACnCX,EAAOY,KACPZ,EAAOY,IAAIC,iBAETC,EAAgC,oBAAXC,QAA8C,oBAAdC,UACrDC,EAAiB,WAEnB,IAAKjB,EAAOO,gBACV,OAAO,EAGT,IAAIW,EAAS,IAAIX,gBAAgB,EAAG,GAChCY,EAAMD,EAAOE,WAAW,MAC5BD,EAAIE,SAAS,EAAG,EAAG,EAAG,GACtB,IAAIC,EAASJ,EAAOK,wBAEpB,IACEJ,EAAIK,cAAcF,EAAQ,YAC5B,CAAE,MAAOG,GACP,OAAO,CACT,CAEA,OAAO,CACT,CAlBqB,GAoBrB,SAASC,IAAQ,CAIjB,SAASC,EAAQC,GACf,IAAIC,EAAgB/B,EAAOgC,QAAQxB,QAC/ByB,OAAyB,IAAlBF,EAA2BA,EAAgB7B,EAAOM,QAE7D,MAAoB,oBAATyB,EACF,IAAIA,EAAKH,IAGlBA,EAAKF,EAAMA,GAEJ,KACT,CAEA,IAA8BM,EAAeC,EA2pBzCC,EA3pBAC,GAA0BH,EA4B3Bf,EA5B0CgB,EA4B3B,IAAIG,IAtBb,CACLC,UAAW,SAASf,GAClB,GAAIU,EACF,OAAOV,EAGT,GAAIW,EAAIK,IAAIhB,GACV,OAAOW,EAAIM,IAAIjB,GAGjB,IAAIJ,EAAS,IAAIX,gBAAgBe,EAAOkB,MAAOlB,EAAOmB,QAMtD,OALUvB,EAAOE,WAAW,MACxBsB,UAAUpB,EAAQ,EAAG,GAEzBW,EAAIU,IAAIrB,EAAQJ,GAETA,CACT,EACA0B,MAAO,WACLX,EAAIW,OACN,IAIAC,EAAO,WACT,IACIC,EAAOC,EADPC,EAAOC,KAAKC,MAAM,IAAO,IAEzBC,EAAS,CAAC,EACVC,EAAgB,EAiCpB,MA/BqC,oBAA1BC,uBAAwE,oBAAzBC,sBACxDR,EAAQ,SAAUS,GAChB,IAAIC,EAAKP,KAAKQ,SAad,OAXAN,EAAOK,GAAMH,uBAAsB,SAASK,EAAQC,GAC9CP,IAAkBO,GAAQP,EAAgBJ,EAAO,EAAIW,GACvDP,EAAgBO,SACTR,EAAOK,GAEdD,KAEAJ,EAAOK,GAAMH,sBAAsBK,EAEvC,IAEOF,CACT,EACAT,EAAS,SAAUS,GACbL,EAAOK,IACTF,qBAAqBH,EAAOK,GAEhC,IAEAV,EAAQ,SAAUS,GAChB,OAAOK,WAAWL,EAAIP,EACxB,EACAD,EAAS,SAAUc,GACjB,OAAOC,aAAaD,EACtB,GAGK,CAAEf,MAAOA,EAAOC,OAAQA,EACjC,CAtCW,GAwCPgB,EAAa,WACf,IAAIC,EACAC,EACAC,EAAW,CAAC,EAuDhB,OAAO,WACL,GAAIF,EACF,OAAOA,EAGT,IAAK/D,GAAYE,EAAc,CAC7B,IAAIgE,EAAO,CACT,wCACA,IAAMpE,EAAKqE,WAAa,+BACxB,8BACA,4BACA,oDACA,iCACA,wDACA,UACA,UACA,iCACA,oCACA,kCACA,0CACA,4CACA,kCACA,0CACA,4CACA,yDACA,MACA,KACAC,KAAK,MACP,IACEL,EAAS,IAAI5D,OAAOQ,IAAIC,gBAAgB,IAAIR,KAAK,CAAC8D,KACpD,CAAE,MAAO1C,GAIP,YAFmB6C,WAAZC,SAAiD,oBAAjBA,QAAQC,MAAsBD,QAAQC,KAAK,qCAA4B/C,GAEvG,IACT,EAxFJ,SAAkBuC,GAChB,SAASS,EAAQC,EAASC,GACxBX,EAAOY,YAAY,CAAEF,QAASA,GAAW,CAAC,EAAGC,SAAUA,GACzD,CACAX,EAAOa,KAAO,SAAoB3D,GAChC,IAAI4D,EAAY5D,EAAOP,6BACvBqD,EAAOY,YAAY,CAAE1D,OAAQ4D,GAAa,CAACA,GAC7C,EAEAd,EAAOe,KAAO,SAAoBL,EAASM,EAAMC,GAC/C,GAAIhB,EAEF,OADAQ,EAAQC,EAAS,MACVT,EAGT,IAAIT,EAAKP,KAAKQ,SAASW,SAAS,IAAIc,MAAM,GAyB1C,OAvBAjB,EAAOtC,GAAQ,SAAUwD,GACvB,SAASC,EAAWC,GACdA,EAAIC,KAAKX,WAAanB,WAInBU,EAASV,GAChBQ,EAAOuB,oBAAoB,UAAWH,GAEtCnB,EAAO,KAEP9B,EAAaS,QAEbqC,IACAE,IACF,CAEAnB,EAAOwB,iBAAiB,UAAWJ,GACnCX,EAAQC,EAASlB,GAEjBU,EAASV,GAAM4B,EAAWK,KAAK,KAAM,CAAEH,KAAM,CAAEX,SAAUnB,IAC3D,GAGF,EAEAQ,EAAO0B,MAAQ,WAGb,IAAK,IAAIlC,KAFTQ,EAAOY,YAAY,CAAEc,OAAO,IAEbxB,EACbA,EAASV,YACFU,EAASV,EAEpB,CACF,CAuCImC,CAAS3B,EACX,CAEA,OAAOA,CACT,CACF,CApGiB,GAsGb4B,EAAW,CACbC,cAAe,GACfC,MAAO,GACPC,OAAQ,GACRC,cAAe,GACfC,MAAO,GACPC,QAAS,EACTC,MAAO,EACPC,MAAO,IACPC,EAAG,GACHC,EAAG,GACHC,OAAQ,CAAC,SAAU,UACnBC,OAAQ,IACRC,OAAQ,CACN,UACA,UACA,UACA,UACA,UACA,UACA,WAGFC,yBAAyB,EACzBC,OAAQ,GAWV,SAASC,EAAKlC,EAASmC,EAAMxE,GAC3B,OATF,SAAiByE,EAAKzE,GACpB,OAAOA,EAAYA,EAAUyE,GAAOA,CACtC,CAOSC,CACLrC,IALe,QADLoC,EAMMpC,EAAQmC,UALOvC,IAARwC,GAKUpC,EAAQmC,GAAQjB,EAASiB,GAC1DxE,GAPJ,IAAcyE,CASd,CAEA,SAASE,EAAgBC,GACvB,OAAOA,EAAS,EAAI,EAAIhE,KAAKC,MAAM+D,EACrC,CAOA,SAASC,EAAUC,GACjB,OAAOC,SAASD,EAAK,GACvB,CAEA,SAASE,EAAYZ,GACnB,OAAOA,EAAOxE,IAAIqF,EACpB,CAEA,SAASA,EAASH,GAChB,IAAIL,EAAMS,OAAOJ,GAAKK,QAAQ,cAAe,IAM7C,OAJIV,EAAIW,OAAS,IACbX,EAAMA,EAAI,GAAGA,EAAI,GAAGA,EAAI,GAAGA,EAAI,GAAGA,EAAI,GAAGA,EAAI,IAG1C,CACLY,EAAGR,EAAUJ,EAAIa,UAAU,EAAE,IAC7BC,EAAGV,EAAUJ,EAAIa,UAAU,EAAE,IAC7BE,EAAGX,EAAUJ,EAAIa,UAAU,EAAE,IAEjC,CAUA,SAASG,EAAoB5G,GAC3BA,EAAOsB,MAAQuF,SAASC,gBAAgBC,YACxC/G,EAAOuB,OAASsF,SAASC,gBAAgBE,YAC3C,CAEA,SAASC,EAAkBjH,GACzB,IAAIkH,EAAOlH,EAAOmH,wBAClBnH,EAAOsB,MAAQ4F,EAAK5F,MACpBtB,EAAOuB,OAAS2F,EAAK3F,MACvB,CAuBA,SAAS6F,EAAcC,GACrB,IAAIC,EAAWD,EAAKzC,OAAS7C,KAAKwF,GAAK,KACnCC,EAAYH,EAAKxC,QAAU9C,KAAKwF,GAAK,KAEzC,MAAO,CACLpC,EAAGkC,EAAKlC,EACRC,EAAGiC,EAAKjC,EACRqC,OAAwB,GAAhB1F,KAAKQ,SACbmF,YAAa3F,KAAK4F,IAAI,IAAsB,GAAhB5F,KAAKQ,SAAiB,KAClDqF,SAAgC,GAArBP,EAAKvC,cAAwB/C,KAAKQ,SAAW8E,EAAKvC,cAC7D+C,SAAUP,GAAa,GAAME,EAAczF,KAAKQ,SAAWiF,GAC3DM,WAA4B,GAAhB/F,KAAKQ,SAA2B,KAAQR,KAAKwF,GACzDQ,MAAOV,EAAKU,MACZC,MAAOX,EAAKW,MACZC,KAAM,EACNC,WAAYb,EAAKnC,MACjBH,MAAOsC,EAAKtC,MACZE,MAAOoC,EAAKpC,MACZ1C,OAAQR,KAAKQ,SAAW,EACxB4F,QAAS,EACTC,QAAS,EACTC,QAAS,EACTC,QAAS,EACTtD,QAAwB,EAAfqC,EAAKrC,QACduD,WAAY,GACZ9C,OAAQ4B,EAAK5B,OACb+C,KAAMnB,EAAKmB,KAEf,CAEA,SAASC,EAAYC,EAASC,GAC5BA,EAAMxD,GAAKpD,KAAK6G,IAAID,EAAMd,SAAWc,EAAMf,SAAWe,EAAM1D,MAC5D0D,EAAMvD,GAAKrD,KAAK8G,IAAIF,EAAMd,SAAWc,EAAMf,SAAWe,EAAM3D,QAC5D2D,EAAMf,UAAYe,EAAM5D,MAEpB4D,EAAMH,MACRG,EAAMlB,OAAS,EACfkB,EAAMN,QAAUM,EAAMxD,EAAK,GAAKwD,EAAMlD,OACtCkD,EAAML,QAAUK,EAAMvD,EAAK,GAAKuD,EAAMlD,OAEtCkD,EAAMR,QAAU,EAChBQ,EAAMP,QAAU,EAChBO,EAAMpG,OAAS,IAEfoG,EAAMlB,QAAUkB,EAAMjB,YACtBiB,EAAMN,QAAUM,EAAMxD,EAAM,GAAKwD,EAAMlD,OAAU1D,KAAK6G,IAAID,EAAMlB,QAChEkB,EAAML,QAAUK,EAAMvD,EAAM,GAAKuD,EAAMlD,OAAU1D,KAAK8G,IAAIF,EAAMlB,QAEhEkB,EAAMb,WAAa,GACnBa,EAAMR,QAAUpG,KAAK8G,IAAIF,EAAMb,WAC/Ba,EAAMP,QAAUrG,KAAK6G,IAAID,EAAMb,WAC/Ba,EAAMpG,OAASR,KAAKQ,SAAW,GAGjC,IAAIuG,EAAYH,EAAMV,OAAUU,EAAMT,WAElCa,EAAKJ,EAAMxD,EAAKwD,EAAMpG,OAASoG,EAAMP,QACrCY,EAAKL,EAAMvD,EAAKuD,EAAMpG,OAASoG,EAAMR,QACrCc,EAAKN,EAAMN,QAAWM,EAAMpG,OAASoG,EAAMP,QAC3Cc,EAAKP,EAAML,QAAWK,EAAMpG,OAASoG,EAAMR,QAM/C,GAJAO,EAAQS,UAAY,QAAUR,EAAMZ,MAAMvB,EAAI,KAAOmC,EAAMZ,MAAMrB,EAAI,KAAOiC,EAAMZ,MAAMpB,EAAI,MAAQ,EAAImC,GAAY,IAEpHJ,EAAQU,YAEJxJ,GAAoC,SAArB+I,EAAMX,MAAMqB,MAA+C,kBAArBV,EAAMX,MAAMsB,MAAqBC,MAAMC,QAAQb,EAAMX,MAAMyB,QAClHf,EAAQgB,KAoUZ,SAAyBC,EAAYC,EAAYzE,EAAGC,EAAGyE,EAAQC,EAAQC,GACrE,IAAIC,EAAS,IAAInK,OAAO8J,GAEpBM,EAAK,IAAIpK,OACboK,EAAGC,QAAQF,EAAQ,IAAIlK,UAAU8J,IAEjC,IAAIO,EAAK,IAAItK,OAWb,OATAsK,EAAGD,QAAQD,EAAI,IAAInK,UAAU,CAC3BiC,KAAK6G,IAAImB,GAAYF,EACrB9H,KAAK8G,IAAIkB,GAAYF,GACpB9H,KAAK8G,IAAIkB,GAAYD,EACtB/H,KAAK6G,IAAImB,GAAYD,EACrB3E,EACAC,KAGK+E,CACT,CAtViBC,CACXzB,EAAMX,MAAMsB,KACZX,EAAMX,MAAMyB,OACZd,EAAMxD,EACNwD,EAAMvD,EACc,GAApBrD,KAAKsI,IAAIpB,EAAKF,GACM,GAApBhH,KAAKsI,IAAInB,EAAKF,GACdjH,KAAKwF,GAAK,GAAKoB,EAAMlB,cAElB,GAAyB,WAArBkB,EAAMX,MAAMqB,KAAmB,CACxC,IAAIU,EAAWhI,KAAKwF,GAAK,GAAKoB,EAAMlB,OAChCoC,EAA6B,GAApB9H,KAAKsI,IAAIpB,EAAKF,GACvBe,EAA6B,GAApB/H,KAAKsI,IAAInB,EAAKF,GACvB1H,EAAQqH,EAAMX,MAAM5H,OAAOkB,MAAQqH,EAAMlD,OACzClE,EAASoH,EAAMX,MAAM5H,OAAOmB,OAASoH,EAAMlD,OAE3CgE,EAAS,IAAI3J,UAAU,CACzBiC,KAAK6G,IAAImB,GAAYF,EACrB9H,KAAK8G,IAAIkB,GAAYF,GACpB9H,KAAK8G,IAAIkB,GAAYD,EACtB/H,KAAK6G,IAAImB,GAAYD,EACrBnB,EAAMxD,EACNwD,EAAMvD,IAIRqE,EAAOa,aAAa,IAAIxK,UAAU6I,EAAMX,MAAMyB,SAE9C,IAAIc,EAAU7B,EAAQpI,cAAcW,EAAaE,UAAUwH,EAAMX,MAAM5H,QAAS,aAChFmK,EAAQC,aAAaf,GAErBf,EAAQ+B,YAAe,EAAI3B,EAC3BJ,EAAQS,UAAYoB,EACpB7B,EAAQvI,SACNwI,EAAMxD,EAAK7D,EAAQ,EACnBqH,EAAMvD,EAAK7D,EAAS,EACpBD,EACAC,GAEFmH,EAAQ+B,YAAc,CACxB,MAAO,GAAoB,WAAhB9B,EAAMX,MACfU,EAAQgC,QACNhC,EAAQgC,QAAQ/B,EAAMxD,EAAGwD,EAAMvD,EAAGrD,KAAKsI,IAAIpB,EAAKF,GAAMJ,EAAMJ,WAAYxG,KAAKsI,IAAInB,EAAKF,GAAML,EAAMJ,WAAYxG,KAAKwF,GAAK,GAAKoB,EAAMlB,OAAQ,EAAG,EAAI1F,KAAKwF,IArH7J,SAAiBmB,EAASvD,EAAGC,EAAGuF,EAASC,EAASb,EAAUc,EAAYC,EAAUC,GAChFrC,EAAQsC,OACRtC,EAAQuC,UAAU9F,EAAGC,GACrBsD,EAAQwC,OAAOnB,GACfrB,EAAQyC,MAAMR,EAASC,GACvBlC,EAAQ0C,IAAI,EAAG,EAAG,EAAGP,EAAYC,EAAUC,GAC3CrC,EAAQ2C,SACV,CA+GMX,CAAQhC,EAASC,EAAMxD,EAAGwD,EAAMvD,EAAGrD,KAAKsI,IAAIpB,EAAKF,GAAMJ,EAAMJ,WAAYxG,KAAKsI,IAAInB,EAAKF,GAAML,EAAMJ,WAAYxG,KAAKwF,GAAK,GAAKoB,EAAMlB,OAAQ,EAAG,EAAI1F,KAAKwF,SACrJ,GAAoB,SAAhBoB,EAAMX,MASf,IARA,IAAIsD,EAAMvJ,KAAKwF,GAAK,EAAI,EACpBgE,EAAc,EAAI5C,EAAMlD,OACxB+F,EAAc,EAAI7C,EAAMlD,OACxBN,EAAIwD,EAAMxD,EACVC,EAAIuD,EAAMvD,EACVqG,EAAS,EACTC,EAAO3J,KAAKwF,GAAKkE,EAEdA,KACLtG,EAAIwD,EAAMxD,EAAIpD,KAAK6G,IAAI0C,GAAOE,EAC9BpG,EAAIuD,EAAMvD,EAAIrD,KAAK8G,IAAIyC,GAAOE,EAC9B9C,EAAQiD,OAAOxG,EAAGC,GAClBkG,GAAOI,EAEPvG,EAAIwD,EAAMxD,EAAIpD,KAAK6G,IAAI0C,GAAOC,EAC9BnG,EAAIuD,EAAMvD,EAAIrD,KAAK8G,IAAIyC,GAAOC,EAC9B7C,EAAQiD,OAAOxG,EAAGC,GAClBkG,GAAOI,OAGThD,EAAQkD,OAAO7J,KAAKC,MAAM2G,EAAMxD,GAAIpD,KAAKC,MAAM2G,EAAMvD,IACrDsD,EAAQiD,OAAO5J,KAAKC,MAAM2G,EAAMN,SAAUtG,KAAKC,MAAMgH,IACrDN,EAAQiD,OAAO5J,KAAKC,MAAMiH,GAAKlH,KAAKC,MAAMkH,IAC1CR,EAAQiD,OAAO5J,KAAKC,MAAM+G,GAAKhH,KAAKC,MAAM2G,EAAML,UAMlD,OAHAI,EAAQmD,YACRnD,EAAQgB,OAEDf,EAAMV,KAAOU,EAAMT,UAC5B,CAoEA,SAAS4D,EAAe9L,EAAQ+L,GAC9B,IASIC,EATAC,GAAejM,EACfkM,IAAgBxG,EAAKqG,GAAc,CAAC,EAAG,UACvCI,GAA2B,EAC3BC,EAAgC1G,EAAKqG,EAAY,0BAA2BM,SAE5EvJ,EADkB7D,KAAkByG,EAAKqG,GAAc,CAAC,EAAG,aAChClJ,IAAc,KACzCyJ,EAAUL,EAAcrF,EAAsBK,EAC9CsF,KAAevM,IAAU8C,MAAY9C,EAAOwM,uBAC5CC,EAAyC,oBAAfC,YAA6BA,WAAW,4BAA4BC,QAGlG,SAASC,EAAUpJ,EAASM,EAAMC,GAqBhC,IApBA,IAjSe4D,EAAKkF,EAiShBlI,EAAgBe,EAAKlC,EAAS,gBAAiBsC,GAC/ClB,EAAQc,EAAKlC,EAAS,QAASsJ,QAC/BjI,EAASa,EAAKlC,EAAS,SAAUsJ,QACjChI,EAAgBY,EAAKlC,EAAS,gBAAiBsJ,QAC/C/H,EAAQW,EAAKlC,EAAS,QAASsJ,QAC/B9H,EAAUU,EAAKlC,EAAS,UAAWsJ,QACnC7H,EAAQS,EAAKlC,EAAS,QAASsJ,QAC/BvH,EAASG,EAAKlC,EAAS,SAAU2C,GACjCjB,EAAQQ,EAAKlC,EAAS,QAASsJ,QAC/BzH,EAASK,EAAKlC,EAAS,UACvBiC,EAASC,EAAKlC,EAAS,UACvBgF,IAAS9C,EAAKlC,EAAS,QACvBuJ,EAlRR,SAAmBvJ,GACjB,IAAIuJ,EAASrH,EAAKlC,EAAS,SAAUwJ,QAIrC,OAHAD,EAAO5H,EAAIO,EAAKqH,EAAQ,IAAKD,QAC7BC,EAAO3H,EAAIM,EAAKqH,EAAQ,IAAKD,QAEtBC,CACT,CA4QiBE,CAAUzJ,GAEnB0J,EAAOvI,EACPwI,EAAS,GAETC,EAASpN,EAAOsB,MAAQyL,EAAO5H,EAC/BkI,EAASrN,EAAOuB,OAASwL,EAAO3H,EAE7B8H,KACLC,EAAOG,KACLlG,EAAc,CACZjC,EAAGiI,EACHhI,EAAGiI,EACHzI,MAAOA,EACPC,OAAQA,EACRC,cAAeA,EACfiD,MAAOxC,EAAO2H,EAAO3H,EAAOgB,QAC5ByB,MAAO3C,GA9TEsC,EA8Te,EA9TVkF,EA8TaxH,EAAOkB,OA5TnCxE,KAAKC,MAAMD,KAAKQ,UAAYsK,EAAMlF,IAAQA,IA6TzCzC,MAAOA,EACPH,MAAOA,EACPC,QAASA,EACTC,MAAOA,EACPQ,OAAQA,EACR+C,KAAMA,KAOZ,OAAIwD,EACKA,EAAauB,UAAUJ,IAGhCnB,EA7HJ,SAAiBhM,EAAQmN,EAAQb,EAASxI,EAAMC,GAC9C,IAEIyJ,EACAC,EAHAC,EAAkBP,EAAOnJ,QACzB0E,EAAU1I,EAAOE,WAAW,MAI5B6C,EAAOtC,GAAQ,SAAUwD,GAC3B,SAAS0J,IACPH,EAAiBC,EAAU,KAE3B/E,EAAQkF,UAAU,EAAG,EAAG9J,EAAKxC,MAAOwC,EAAKvC,QACzCN,EAAaS,QAEbqC,IACAE,GACF,CA2BAuJ,EAAiB7L,EAAIC,OAzBrB,SAASiM,KACH9O,GAAc+E,EAAKxC,QAAUtC,EAAWsC,OAASwC,EAAKvC,SAAWvC,EAAWuC,SAC9EuC,EAAKxC,MAAQtB,EAAOsB,MAAQtC,EAAWsC,MACvCwC,EAAKvC,OAASvB,EAAOuB,OAASvC,EAAWuC,QAGtCuC,EAAKxC,OAAUwC,EAAKvC,SACvB+K,EAAQtM,GACR8D,EAAKxC,MAAQtB,EAAOsB,MACpBwC,EAAKvC,OAASvB,EAAOuB,QAGvBmH,EAAQkF,UAAU,EAAG,EAAG9J,EAAKxC,MAAOwC,EAAKvC,SAEzCmM,EAAkBA,EAAgBI,QAAO,SAAUnF,GACjD,OAAOF,EAAYC,EAASC,EAC9B,KAEoBpC,OAClBiH,EAAiB7L,EAAIC,MAAMiM,GAE3BF,GAEJ,IAGAF,EAAUE,CACZ,IAEA,MAAO,CACLJ,UAAW,SAAUJ,GAGnB,OAFAO,EAAkBA,EAAgBK,OAAOZ,GAElCpK,CACT,EACA/C,OAAQA,EACRS,QAASsC,EACTyB,MAAO,WACDgJ,GACF7L,EAAIE,OAAO2L,GAGTC,GACFA,GAEJ,EAEJ,CA6DmBO,CAAQhO,EAAQmN,EAAQb,EAASxI,EAAOC,GAEhDiI,EAAavL,QACtB,CAEA,SAASoD,EAAKL,GACZ,IAAIgC,EAA0B4G,GAAiC1G,EAAKlC,EAAS,0BAA2B6I,SACpG/G,EAASI,EAAKlC,EAAS,SAAUsJ,QAErC,GAAItH,GAA2BiH,EAC7B,OAAOhM,GAAQ,SAAUwD,GACvBA,GACF,IAGEgI,GAAeD,EAEjBhM,EAASgM,EAAahM,OACbiM,IAAgBjM,IAEzBA,EArTN,SAAmBsF,GACjB,IAAItF,EAAS6G,SAASoH,cAAc,UAQpC,OANAjO,EAAOkO,MAAMC,SAAW,QACxBnO,EAAOkO,MAAME,IAAM,MACnBpO,EAAOkO,MAAMG,KAAO,MACpBrO,EAAOkO,MAAMI,cAAgB,OAC7BtO,EAAOkO,MAAM5I,OAASA,EAEftF,CACT,CA2SeuO,CAAUjJ,GACnBuB,SAAS2H,KAAKC,YAAYzO,IAGxBkM,IAAgBK,GAElBD,EAAQtM,GAGV,IAAI8D,EAAO,CACTxC,MAAOtB,EAAOsB,MACdC,OAAQvB,EAAOuB,QAajB,SAASmN,IACP,GAAI5L,EAAQ,CAEV,IAAI6L,EAAM,CACRxH,sBAAuB,WACrB,IAAK8E,EACH,OAAOjM,EAAOmH,uBAElB,GAWF,OARAmF,EAAQqC,QAER7L,EAAOY,YAAY,CACjBkL,OAAQ,CACNtN,MAAOqN,EAAIrN,MACXC,OAAQoN,EAAIpN,SAIlB,CAIAuC,EAAKxC,MAAQwC,EAAKvC,OAAS,IAC7B,CAEA,SAASwC,IACPiI,EAAe,KAEXE,IACFC,GAA2B,EAC3BrN,EAAOuF,oBAAoB,SAAUqK,IAGnCzC,GAAejM,IACb6G,SAAS2H,KAAKK,SAAS7O,IACzB6G,SAAS2H,KAAKM,YAAY9O,GAE5BA,EAAS,KACTuM,GAAc,EAElB,CAOA,OA3DIzJ,IAAWyJ,GACbzJ,EAAOa,KAAK3D,GAGduM,GAAc,EAEVzJ,IACF9C,EAAOwM,wBAAyB,GA+C9BN,IAAgBC,IAClBA,GAA2B,EAC3BrN,EAAOwF,iBAAiB,SAAUoK,GAAU,IAG1C5L,EACKA,EAAOe,KAAKL,EAASM,EAAMC,GAG7B6I,EAAUpJ,EAASM,EAAMC,EAClC,CAYA,OAVAF,EAAKW,MAAQ,WACP1B,GACFA,EAAO0B,QAGLwH,GACFA,EAAaxH,OAEjB,EAEOX,CACT,CAIA,SAASkL,IAIP,OAHK/N,IACHA,EAAc8K,EAAe,KAAM,CAAEkD,WAAW,EAAMJ,QAAQ,KAEzD5N,CACT,CAsIApC,EAAOgC,QAAU,WACf,OAAOmO,IAAiBE,MAAMC,KAAMC,UACtC,EACAvQ,EAAOgC,QAAQ4D,MAAQ,WACrBuK,IAAiBvK,OACnB,EACA5F,EAAOgC,QAAQwO,OAAStD,EACxBlN,EAAOgC,QAAQyO,cAvHf,SAAuBC,GACrB,IAAK1P,EACH,MAAM,IAAI2P,MAAM,mDAGlB,IAAIjG,EAAMG,EAEc,kBAAb6F,EACThG,EAAOgG,GAEPhG,EAAOgG,EAAShG,KAChBG,EAAS6F,EAAS7F,QAGpB,IAAIO,EAAS,IAAInK,OAAOyJ,GAEpBkG,EADa3I,SAASoH,cAAc,UACf/N,WAAW,MAEpC,IAAKuJ,EAAQ,CAWX,IATA,IAKInI,EAAOC,EALPkO,EAAU,IACVC,EAAOD,EACPE,EAAOF,EACPG,EAAO,EACPC,EAAO,EAKF1K,EAAI,EAAGA,EAAIsK,EAAStK,GAAK,EAChC,IAAK,IAAIC,EAAI,EAAGA,EAAIqK,EAASrK,GAAK,EAC5BoK,EAAQM,cAAc9F,EAAQ7E,EAAGC,EAAG,aACtCsK,EAAO3N,KAAK4F,IAAI+H,EAAMvK,GACtBwK,EAAO5N,KAAK4F,IAAIgI,EAAMvK,GACtBwK,EAAO7N,KAAK8K,IAAI+C,EAAMzK,GACtB0K,EAAO9N,KAAK8K,IAAIgD,EAAMzK,IAK5B9D,EAAQsO,EAAOF,EACfnO,EAASsO,EAAOF,EAEhB,IACIxE,EAAQpJ,KAAK4F,IADI,GACerG,EADf,GACqCC,GAE1DkI,EAAS,CACP0B,EAAO,EAAG,EAAGA,GACZpJ,KAAKgO,MAAOzO,EAAM,EAAKoO,GAAQvE,GAC/BpJ,KAAKgO,MAAOxO,EAAO,EAAKoO,GAAQxE,EAErC,CAEA,MAAO,CACL9B,KAAM,OACNC,KAAMA,EACNG,OAAQA,EAEZ,EA8DA7K,EAAOgC,QAAQoP,cA5Df,SAAuBC,GACrB,IAAIC,EACAzK,EAAS,EACTsC,EAAQ,UAERoI,EAAa,iKAEO,kBAAbF,EACTC,EAAOD,GAEPC,EAAOD,EAASC,KAChBzK,EAAS,WAAYwK,EAAWA,EAASxK,OAASA,EAClD0K,EAAa,eAAgBF,EAAWA,EAASE,WAAaA,EAC9DpI,EAAQ,UAAWkI,EAAWA,EAASlI,MAAQA,GAKjD,IAAIqI,EAAW,GAAK3K,EAChB4K,EAAYD,EAAW,MAAQD,EAE/BnQ,EAAS,IAAIX,gBAAgB+Q,EAAUA,GACvCnQ,EAAMD,EAAOE,WAAW,MAE5BD,EAAIoQ,KAAOA,EACX,IAAIvM,EAAO7D,EAAIqQ,YAAYJ,GACvB5O,EAAQS,KAAKwO,KAAKzM,EAAK0M,uBAAyB1M,EAAK2M,uBACrDlP,EAASQ,KAAKwO,KAAKzM,EAAK4M,wBAA0B5M,EAAK6M,0BAGvDxL,EAAIrB,EAAK2M,sBADC,EAEVrL,EAAItB,EAAK4M,wBAFC,EAGdpP,GAASsP,EACTrP,GAAUqP,GAGV3Q,GADAD,EAAS,IAAIX,gBAAgBiC,EAAOC,IACvBrB,WAAW,OACpBmQ,KAAOA,EACXpQ,EAAIkJ,UAAYpB,EAEhB9H,EAAI4Q,SAASX,EAAM/K,EAAGC,GAEtB,IAAI+F,EAAQ,EAAI1F,EAEhB,MAAO,CACL4D,KAAM,SAENjJ,OAAQJ,EAAOK,wBACfoJ,OAAQ,CAAC0B,EAAO,EAAG,EAAGA,GAAQ7J,EAAQ6J,EAAQ,GAAI5J,EAAS4J,EAAQ,GAEvE,CAWD,CAl2BA,CAk2BE,WACD,MAAsB,qBAAX2F,OACFA,OAGW,qBAATC,KACFA,KAGF7B,MAAQ,CAAC,CAClB,CAVG,GAUGtQ,GAAQ,GAId,QAAeA,EAAc,QACTA,EAAOgC,QAAQwO,M,0DCp3BnC,SAAS4B,IACL,MAAMC,GAAYC,EAAAA,EAAAA,SAAO,GAOzB,OANAC,EAAAA,EAAAA,IAA0B,KACtBF,EAAUG,SAAU,EACb,KACHH,EAAUG,SAAU,CAAK,IAE9B,IACIH,CACX,C,qCCLA,MAAMI,UAAwBC,EAAAA,UAC1BC,uBAAAA,CAAwBC,GACpB,MAAMC,EAAUvC,KAAKwC,MAAMC,SAASP,QACpC,GAAIK,GAAWD,EAAUI,YAAc1C,KAAKwC,MAAME,UAAW,CACzD,MAAM9N,EAAOoL,KAAKwC,MAAMG,QAAQT,QAChCtN,EAAKvC,OAASkQ,EAAQK,cAAgB,EACtChO,EAAKxC,MAAQmQ,EAAQM,aAAe,EACpCjO,EAAKsK,IAAMqD,EAAQO,UACnBlO,EAAKuK,KAAOoD,EAAQQ,UACxB,CACA,OAAO,IACX,CAIAC,kBAAAA,GAAuB,CACvBC,MAAAA,GACI,OAAOjD,KAAKwC,MAAMU,QACtB,EAEJ,SAASC,EAAQC,GAA0B,IAAzB,SAAEF,EAAQ,UAAER,GAAWU,EACrC,MAAMhQ,GAAKiQ,EAAAA,EAAAA,SACLC,GAAMtB,EAAAA,EAAAA,QAAO,MACbpN,GAAOoN,EAAAA,EAAAA,QAAO,CAChB5P,MAAO,EACPC,OAAQ,EACR6M,IAAK,EACLC,KAAM,IAiCV,OAtBAoE,EAAAA,EAAAA,qBAAmB,KACf,MAAM,MAAEnR,EAAK,OAAEC,EAAM,IAAE6M,EAAG,KAAEC,GAASvK,EAAKsN,QAC1C,GAAIQ,IAAcY,EAAIpB,UAAY9P,IAAUC,EACxC,OACJiR,EAAIpB,QAAQsB,QAAQC,YAAcrQ,EAClC,MAAM4L,EAAQrH,SAASoH,cAAc,SAarC,OAZApH,SAAS+L,KAAKnE,YAAYP,GACtBA,EAAM2E,OACN3E,EAAM2E,MAAMC,WAAW,oCAAD/E,OACDzL,EAAE,yEAAAyL,OAEdzM,EAAK,wCAAAyM,OACJxM,EAAM,qCAAAwM,OACTK,EAAG,sCAAAL,OACFM,EAAI,0CAIT,KACHxH,SAAS+L,KAAK9D,YAAYZ,EAAM,CACnC,GACF,CAAC0D,IACIN,EAAAA,cAAoBD,EAAiB,CAAEO,UAAWA,EAAWD,SAAUa,EAAKX,QAAS/N,GAAQwN,EAAAA,aAAmBc,EAAU,CAAEI,QACxI,CC9DA,MAAMO,EAAgBT,IAA4F,IAA3F,SAAEF,EAAQ,QAAEY,EAAO,UAAEpB,EAAS,eAAEqB,EAAc,OAAEC,EAAM,sBAAEC,EAAqB,KAAEC,GAAOd,EACzG,MAAMe,GAAmBC,EAAAA,EAAAA,GAAYC,GAC/BjR,GAAKiQ,EAAAA,EAAAA,SACL7J,GAAU8K,EAAAA,EAAAA,UAAQ,KAAM,CAC1BlR,KACA0Q,UACApB,YACAsB,SACAD,eAAiBQ,IACbJ,EAAiB5R,IAAIgS,GAAS,GAC9B,IAAK,MAAMC,KAAcL,EAAiBM,SACtC,IAAKD,EACD,OAERT,GAAkBA,GAAgB,EAEtCW,SAAWH,IACPJ,EAAiB5R,IAAIgS,GAAS,GACvB,IAAMJ,EAAiBQ,OAAOJ,OAQ7CN,OAAwB/P,EAAY,CAACwO,IAiBrC,OAhBA4B,EAAAA,EAAAA,UAAQ,KACJH,EAAiBS,SAAQ,CAACC,EAAGC,IAAQX,EAAiB5R,IAAIuS,GAAK,IAAO,GACvE,CAACpC,IAKJN,EAAAA,WAAgB,MACXM,IACIyB,EAAiBvP,MAClBmP,GACAA,GAAgB,GACrB,CAACrB,IACS,cAATwB,IACAhB,EAAWd,EAAAA,cAAoBe,EAAU,CAAET,UAAWA,GAAaQ,IAE/Dd,EAAAA,cAAoB2C,EAAAA,EAAgBC,SAAU,CAAEC,MAAOzL,GAAW0J,EAAS,EAEvF,SAASmB,IACL,OAAO,IAAIrS,GACf,C,4BC3CA,MAAMkT,EAAeC,GAAUA,EAAML,KAAO,GAiD5C,MAAMM,EAAkBhC,IAAyH,IAAxH,SAAEF,EAAQ,OAAEc,EAAM,QAAEF,GAAU,EAAI,eAAEC,EAAc,gBAAEsB,EAAe,sBAAEpB,GAAwB,EAAI,KAAEC,EAAO,QAASd,GACxIkC,EAAAA,EAAAA,IAAWD,EAAiB,4CAG5B,MAAME,GAAcC,EAAAA,EAAAA,YAAWC,EAAAA,GAAoBF,aC3DvD,WACI,MAAMxD,EAAYD,KACX4D,EAAmBC,IAAwBC,EAAAA,EAAAA,UAAS,GACrDL,GAAcM,EAAAA,EAAAA,cAAY,KAC5B9D,EAAUG,SAAWyD,EAAqBD,EAAoB,EAAE,GACjE,CAACA,IAMJ,MAAO,EADqBG,EAAAA,EAAAA,cAAY,IAAMnT,EAAAA,GAAMoT,WAAWP,IAAc,CAACA,IACjDG,EACjC,CD+CsEK,GAAiB,GAC7EhE,EAAYD,IAEZkE,EAjDV,SAAsB9C,GAClB,MAAM+C,EAAW,GAMjB,OAJAC,EAAAA,SAAStB,QAAQ1B,GAAWiC,KACpBgB,EAAAA,EAAAA,gBAAehB,IACfc,EAAS7H,KAAK+G,EAAM,IAErBc,CACX,CAyC6BG,CAAalD,GACtC,IAAImD,EAAmBL,EACvB,MAAMM,GAAkBtE,EAAAA,EAAAA,QAAO,IAAIhQ,KAAOkQ,QAGpCqE,GAAkBvE,EAAAA,EAAAA,QAAOqE,GAEzBG,GAAcxE,EAAAA,EAAAA,QAAO,IAAIhQ,KAAOkQ,QAGhCuE,GAAkBzE,EAAAA,EAAAA,SAAO,GE1EnC,IAA0BzN,EFqFtB,IAVA0N,EAAAA,EAAAA,IAA0B,KACtBwE,EAAgBvE,SAAU,EAnElC,SAA2BgB,EAAUsD,GACjCtD,EAAS0B,SAASO,IACd,MAAML,EAAMI,EAAYC,GACxBqB,EAAYjU,IAAIuS,EAAKK,EAAM,GAEnC,CA+DQuB,CAAkBV,EAAkBQ,GACpCD,EAAgBrE,QAAUmE,CAAgB,IE9ExB9R,EFgFL,KACbkS,EAAgBvE,SAAU,EAC1BsE,EAAYhU,QACZ8T,EAAgB9T,OAAO,GElFpBmU,EAAAA,EAAAA,YAAU,IAAM,IAAMpS,KAAY,IFoFrCkS,EAAgBvE,QAChB,OAAQE,EAAAA,cAAoBA,EAAAA,SAAgB,KAAMiE,EAAiBxU,KAAKsT,GAAW/C,EAAAA,cAAoByB,EAAe,CAAEiB,IAAKI,EAAYC,GAAQzC,WAAW,EAAMoB,UAASA,QAAU5P,EAAmB+P,sBAAuBA,EAAuBC,KAAMA,GAAQiB,MAGxQkB,EAAmB,IAAIA,GAGvB,MAAMO,EAAcL,EAAgBrE,QAAQrQ,IAAIqT,GAC1C2B,EAAab,EAAiBnU,IAAIqT,GAElC4B,EAAaF,EAAYvP,OAC/B,IAAK,IAAI0P,EAAI,EAAGA,EAAID,EAAYC,IAAK,CACjC,MAAMjC,EAAM8B,EAAYG,IACS,IAA7BF,EAAWG,QAAQlC,IAAgBwB,EAAgBpU,IAAI4S,IACvDwB,EAAgB/T,IAAIuS,OAAK5Q,EAEjC,CA4DA,MAzDa,SAATgQ,GAAmBoC,EAAgB1R,OACnCyR,EAAmB,IAIvBC,EAAgB1B,SAAQ,CAACqC,EAAWnC,KAEhC,IAAiC,IAA7B+B,EAAWG,QAAQlC,GACnB,OACJ,MAAMK,EAAQqB,EAAYrU,IAAI2S,GAC9B,IAAKK,EACD,OACJ,MAAM+B,EAAiBN,EAAYI,QAAQlC,GAC3C,IAAIqC,EAAmBF,EACvB,IAAKE,EAAkB,CACnB,MAAMC,EAASA,KAEXd,EAAgB3B,OAAOG,GAIvB,MAAMuC,EAAehN,MAAMiN,KAAKd,EAAYe,QAAQ3I,QAAQ4I,IAAcX,EAAWY,SAASD,KAa9F,GAXAH,EAAazC,SAAS8C,GAAgBlB,EAAY7B,OAAO+C,KAEzDnB,EAAgBrE,QAAU8D,EAAiBpH,QAAQ+I,IAC/C,MAAMC,EAAkB1C,EAAYyC,GACpC,OAEAC,IAAoB9C,GAEhBuC,EAAaI,SAASG,EAAiB,KAG1CtB,EAAgB1R,KAAM,CACvB,IAA0B,IAAtBmN,EAAUG,QACV,OACJqD,IACAxB,GAAkBA,GACtB,GAEJoD,EAAoB/E,EAAAA,cAAoByB,EAAe,CAAEiB,IAAKI,EAAYC,GAAQzC,WAAW,EAAOqB,eAAgBqD,EAAQpD,OAAQA,EAAQC,sBAAuBA,EAAuBC,KAAMA,GAAQiB,GACxMmB,EAAgB/T,IAAIuS,EAAKqC,EAC7B,CACAd,EAAiBwB,OAAOX,EAAgB,EAAGC,EAAiB,IAIhEd,EAAmBA,EAAiBxU,KAAKsT,IACrC,MAAML,EAAMK,EAAML,IAClB,OAAOwB,EAAgBpU,IAAI4S,GAAQK,EAAU/C,EAAAA,cAAoByB,EAAe,CAAEiB,IAAKI,EAAYC,GAAQzC,WAAW,EAAMuB,sBAAuBA,EAAuBC,KAAMA,GAAQiB,EAAO,IAO3L/C,EAAAA,cAAoBA,EAAAA,SAAgB,KAAMkE,EAAgB1R,KAC5DyR,EACAA,EAAiBxU,KAAKsT,IAAU2C,EAAAA,EAAAA,cAAa3C,KAAQ,C", "sources": ["../node_modules/canvas-confetti/dist/confetti.module.mjs", "../node_modules/framer-motion/dist/es/utils/use-is-mounted.mjs", "../node_modules/framer-motion/dist/es/components/AnimatePresence/PopChild.mjs", "../node_modules/framer-motion/dist/es/components/AnimatePresence/PresenceChild.mjs", "../node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs", "../node_modules/framer-motion/dist/es/utils/use-force-update.mjs", "../node_modules/framer-motion/dist/es/utils/use-unmount-effect.mjs"], "sourcesContent": ["// canvas-confetti v1.9.3 built on 2024-04-30T22:19:17.794Z\nvar module = {};\n\n// source content\n/* globals Map */\n\n(function main(global, module, isWorker, workerSize) {\n  var canUseWorker = !!(\n    global.Worker &&\n    global.Blob &&\n    global.Promise &&\n    global.OffscreenCanvas &&\n    global.OffscreenCanvasRenderingContext2D &&\n    global.HTMLCanvasElement &&\n    global.HTMLCanvasElement.prototype.transferControlToOffscreen &&\n    global.URL &&\n    global.URL.createObjectURL);\n\n  var canUsePaths = typeof Path2D === 'function' && typeof DOMMatrix === 'function';\n  var canDrawBitmap = (function () {\n    // this mostly supports ssr\n    if (!global.OffscreenCanvas) {\n      return false;\n    }\n\n    var canvas = new OffscreenCanvas(1, 1);\n    var ctx = canvas.getContext('2d');\n    ctx.fillRect(0, 0, 1, 1);\n    var bitmap = canvas.transferToImageBitmap();\n\n    try {\n      ctx.createPattern(bitmap, 'no-repeat');\n    } catch (e) {\n      return false;\n    }\n\n    return true;\n  })();\n\n  function noop() {}\n\n  // create a promise if it exists, otherwise, just\n  // call the function directly\n  function promise(func) {\n    var ModulePromise = module.exports.Promise;\n    var Prom = ModulePromise !== void 0 ? ModulePromise : global.Promise;\n\n    if (typeof Prom === 'function') {\n      return new Prom(func);\n    }\n\n    func(noop, noop);\n\n    return null;\n  }\n\n  var bitmapMapper = (function (skipTransform, map) {\n    // see https://github.com/catdad/canvas-confetti/issues/209\n    // creating canvases is actually pretty expensive, so we should create a\n    // 1:1 map for bitmap:canvas, so that we can animate the confetti in\n    // a performant manner, but also not store them forever so that we don't\n    // have a memory leak\n    return {\n      transform: function(bitmap) {\n        if (skipTransform) {\n          return bitmap;\n        }\n\n        if (map.has(bitmap)) {\n          return map.get(bitmap);\n        }\n\n        var canvas = new OffscreenCanvas(bitmap.width, bitmap.height);\n        var ctx = canvas.getContext('2d');\n        ctx.drawImage(bitmap, 0, 0);\n\n        map.set(bitmap, canvas);\n\n        return canvas;\n      },\n      clear: function () {\n        map.clear();\n      }\n    };\n  })(canDrawBitmap, new Map());\n\n  var raf = (function () {\n    var TIME = Math.floor(1000 / 60);\n    var frame, cancel;\n    var frames = {};\n    var lastFrameTime = 0;\n\n    if (typeof requestAnimationFrame === 'function' && typeof cancelAnimationFrame === 'function') {\n      frame = function (cb) {\n        var id = Math.random();\n\n        frames[id] = requestAnimationFrame(function onFrame(time) {\n          if (lastFrameTime === time || lastFrameTime + TIME - 1 < time) {\n            lastFrameTime = time;\n            delete frames[id];\n\n            cb();\n          } else {\n            frames[id] = requestAnimationFrame(onFrame);\n          }\n        });\n\n        return id;\n      };\n      cancel = function (id) {\n        if (frames[id]) {\n          cancelAnimationFrame(frames[id]);\n        }\n      };\n    } else {\n      frame = function (cb) {\n        return setTimeout(cb, TIME);\n      };\n      cancel = function (timer) {\n        return clearTimeout(timer);\n      };\n    }\n\n    return { frame: frame, cancel: cancel };\n  }());\n\n  var getWorker = (function () {\n    var worker;\n    var prom;\n    var resolves = {};\n\n    function decorate(worker) {\n      function execute(options, callback) {\n        worker.postMessage({ options: options || {}, callback: callback });\n      }\n      worker.init = function initWorker(canvas) {\n        var offscreen = canvas.transferControlToOffscreen();\n        worker.postMessage({ canvas: offscreen }, [offscreen]);\n      };\n\n      worker.fire = function fireWorker(options, size, done) {\n        if (prom) {\n          execute(options, null);\n          return prom;\n        }\n\n        var id = Math.random().toString(36).slice(2);\n\n        prom = promise(function (resolve) {\n          function workerDone(msg) {\n            if (msg.data.callback !== id) {\n              return;\n            }\n\n            delete resolves[id];\n            worker.removeEventListener('message', workerDone);\n\n            prom = null;\n\n            bitmapMapper.clear();\n\n            done();\n            resolve();\n          }\n\n          worker.addEventListener('message', workerDone);\n          execute(options, id);\n\n          resolves[id] = workerDone.bind(null, { data: { callback: id }});\n        });\n\n        return prom;\n      };\n\n      worker.reset = function resetWorker() {\n        worker.postMessage({ reset: true });\n\n        for (var id in resolves) {\n          resolves[id]();\n          delete resolves[id];\n        }\n      };\n    }\n\n    return function () {\n      if (worker) {\n        return worker;\n      }\n\n      if (!isWorker && canUseWorker) {\n        var code = [\n          'var CONFETTI, SIZE = {}, module = {};',\n          '(' + main.toString() + ')(this, module, true, SIZE);',\n          'onmessage = function(msg) {',\n          '  if (msg.data.options) {',\n          '    CONFETTI(msg.data.options).then(function () {',\n          '      if (msg.data.callback) {',\n          '        postMessage({ callback: msg.data.callback });',\n          '      }',\n          '    });',\n          '  } else if (msg.data.reset) {',\n          '    CONFETTI && CONFETTI.reset();',\n          '  } else if (msg.data.resize) {',\n          '    SIZE.width = msg.data.resize.width;',\n          '    SIZE.height = msg.data.resize.height;',\n          '  } else if (msg.data.canvas) {',\n          '    SIZE.width = msg.data.canvas.width;',\n          '    SIZE.height = msg.data.canvas.height;',\n          '    CONFETTI = module.exports.create(msg.data.canvas);',\n          '  }',\n          '}',\n        ].join('\\n');\n        try {\n          worker = new Worker(URL.createObjectURL(new Blob([code])));\n        } catch (e) {\n          // eslint-disable-next-line no-console\n          typeof console !== undefined && typeof console.warn === 'function' ? console.warn('🎊 Could not load worker', e) : null;\n\n          return null;\n        }\n\n        decorate(worker);\n      }\n\n      return worker;\n    };\n  })();\n\n  var defaults = {\n    particleCount: 50,\n    angle: 90,\n    spread: 45,\n    startVelocity: 45,\n    decay: 0.9,\n    gravity: 1,\n    drift: 0,\n    ticks: 200,\n    x: 0.5,\n    y: 0.5,\n    shapes: ['square', 'circle'],\n    zIndex: 100,\n    colors: [\n      '#26ccff',\n      '#a25afd',\n      '#ff5e7e',\n      '#88ff5a',\n      '#fcff42',\n      '#ffa62d',\n      '#ff36ff'\n    ],\n    // probably should be true, but back-compat\n    disableForReducedMotion: false,\n    scalar: 1\n  };\n\n  function convert(val, transform) {\n    return transform ? transform(val) : val;\n  }\n\n  function isOk(val) {\n    return !(val === null || val === undefined);\n  }\n\n  function prop(options, name, transform) {\n    return convert(\n      options && isOk(options[name]) ? options[name] : defaults[name],\n      transform\n    );\n  }\n\n  function onlyPositiveInt(number){\n    return number < 0 ? 0 : Math.floor(number);\n  }\n\n  function randomInt(min, max) {\n    // [min, max)\n    return Math.floor(Math.random() * (max - min)) + min;\n  }\n\n  function toDecimal(str) {\n    return parseInt(str, 16);\n  }\n\n  function colorsToRgb(colors) {\n    return colors.map(hexToRgb);\n  }\n\n  function hexToRgb(str) {\n    var val = String(str).replace(/[^0-9a-f]/gi, '');\n\n    if (val.length < 6) {\n        val = val[0]+val[0]+val[1]+val[1]+val[2]+val[2];\n    }\n\n    return {\n      r: toDecimal(val.substring(0,2)),\n      g: toDecimal(val.substring(2,4)),\n      b: toDecimal(val.substring(4,6))\n    };\n  }\n\n  function getOrigin(options) {\n    var origin = prop(options, 'origin', Object);\n    origin.x = prop(origin, 'x', Number);\n    origin.y = prop(origin, 'y', Number);\n\n    return origin;\n  }\n\n  function setCanvasWindowSize(canvas) {\n    canvas.width = document.documentElement.clientWidth;\n    canvas.height = document.documentElement.clientHeight;\n  }\n\n  function setCanvasRectSize(canvas) {\n    var rect = canvas.getBoundingClientRect();\n    canvas.width = rect.width;\n    canvas.height = rect.height;\n  }\n\n  function getCanvas(zIndex) {\n    var canvas = document.createElement('canvas');\n\n    canvas.style.position = 'fixed';\n    canvas.style.top = '0px';\n    canvas.style.left = '0px';\n    canvas.style.pointerEvents = 'none';\n    canvas.style.zIndex = zIndex;\n\n    return canvas;\n  }\n\n  function ellipse(context, x, y, radiusX, radiusY, rotation, startAngle, endAngle, antiClockwise) {\n    context.save();\n    context.translate(x, y);\n    context.rotate(rotation);\n    context.scale(radiusX, radiusY);\n    context.arc(0, 0, 1, startAngle, endAngle, antiClockwise);\n    context.restore();\n  }\n\n  function randomPhysics(opts) {\n    var radAngle = opts.angle * (Math.PI / 180);\n    var radSpread = opts.spread * (Math.PI / 180);\n\n    return {\n      x: opts.x,\n      y: opts.y,\n      wobble: Math.random() * 10,\n      wobbleSpeed: Math.min(0.11, Math.random() * 0.1 + 0.05),\n      velocity: (opts.startVelocity * 0.5) + (Math.random() * opts.startVelocity),\n      angle2D: -radAngle + ((0.5 * radSpread) - (Math.random() * radSpread)),\n      tiltAngle: (Math.random() * (0.75 - 0.25) + 0.25) * Math.PI,\n      color: opts.color,\n      shape: opts.shape,\n      tick: 0,\n      totalTicks: opts.ticks,\n      decay: opts.decay,\n      drift: opts.drift,\n      random: Math.random() + 2,\n      tiltSin: 0,\n      tiltCos: 0,\n      wobbleX: 0,\n      wobbleY: 0,\n      gravity: opts.gravity * 3,\n      ovalScalar: 0.6,\n      scalar: opts.scalar,\n      flat: opts.flat\n    };\n  }\n\n  function updateFetti(context, fetti) {\n    fetti.x += Math.cos(fetti.angle2D) * fetti.velocity + fetti.drift;\n    fetti.y += Math.sin(fetti.angle2D) * fetti.velocity + fetti.gravity;\n    fetti.velocity *= fetti.decay;\n\n    if (fetti.flat) {\n      fetti.wobble = 0;\n      fetti.wobbleX = fetti.x + (10 * fetti.scalar);\n      fetti.wobbleY = fetti.y + (10 * fetti.scalar);\n\n      fetti.tiltSin = 0;\n      fetti.tiltCos = 0;\n      fetti.random = 1;\n    } else {\n      fetti.wobble += fetti.wobbleSpeed;\n      fetti.wobbleX = fetti.x + ((10 * fetti.scalar) * Math.cos(fetti.wobble));\n      fetti.wobbleY = fetti.y + ((10 * fetti.scalar) * Math.sin(fetti.wobble));\n\n      fetti.tiltAngle += 0.1;\n      fetti.tiltSin = Math.sin(fetti.tiltAngle);\n      fetti.tiltCos = Math.cos(fetti.tiltAngle);\n      fetti.random = Math.random() + 2;\n    }\n\n    var progress = (fetti.tick++) / fetti.totalTicks;\n\n    var x1 = fetti.x + (fetti.random * fetti.tiltCos);\n    var y1 = fetti.y + (fetti.random * fetti.tiltSin);\n    var x2 = fetti.wobbleX + (fetti.random * fetti.tiltCos);\n    var y2 = fetti.wobbleY + (fetti.random * fetti.tiltSin);\n\n    context.fillStyle = 'rgba(' + fetti.color.r + ', ' + fetti.color.g + ', ' + fetti.color.b + ', ' + (1 - progress) + ')';\n\n    context.beginPath();\n\n    if (canUsePaths && fetti.shape.type === 'path' && typeof fetti.shape.path === 'string' && Array.isArray(fetti.shape.matrix)) {\n      context.fill(transformPath2D(\n        fetti.shape.path,\n        fetti.shape.matrix,\n        fetti.x,\n        fetti.y,\n        Math.abs(x2 - x1) * 0.1,\n        Math.abs(y2 - y1) * 0.1,\n        Math.PI / 10 * fetti.wobble\n      ));\n    } else if (fetti.shape.type === 'bitmap') {\n      var rotation = Math.PI / 10 * fetti.wobble;\n      var scaleX = Math.abs(x2 - x1) * 0.1;\n      var scaleY = Math.abs(y2 - y1) * 0.1;\n      var width = fetti.shape.bitmap.width * fetti.scalar;\n      var height = fetti.shape.bitmap.height * fetti.scalar;\n\n      var matrix = new DOMMatrix([\n        Math.cos(rotation) * scaleX,\n        Math.sin(rotation) * scaleX,\n        -Math.sin(rotation) * scaleY,\n        Math.cos(rotation) * scaleY,\n        fetti.x,\n        fetti.y\n      ]);\n\n      // apply the transform matrix from the confetti shape\n      matrix.multiplySelf(new DOMMatrix(fetti.shape.matrix));\n\n      var pattern = context.createPattern(bitmapMapper.transform(fetti.shape.bitmap), 'no-repeat');\n      pattern.setTransform(matrix);\n\n      context.globalAlpha = (1 - progress);\n      context.fillStyle = pattern;\n      context.fillRect(\n        fetti.x - (width / 2),\n        fetti.y - (height / 2),\n        width,\n        height\n      );\n      context.globalAlpha = 1;\n    } else if (fetti.shape === 'circle') {\n      context.ellipse ?\n        context.ellipse(fetti.x, fetti.y, Math.abs(x2 - x1) * fetti.ovalScalar, Math.abs(y2 - y1) * fetti.ovalScalar, Math.PI / 10 * fetti.wobble, 0, 2 * Math.PI) :\n        ellipse(context, fetti.x, fetti.y, Math.abs(x2 - x1) * fetti.ovalScalar, Math.abs(y2 - y1) * fetti.ovalScalar, Math.PI / 10 * fetti.wobble, 0, 2 * Math.PI);\n    } else if (fetti.shape === 'star') {\n      var rot = Math.PI / 2 * 3;\n      var innerRadius = 4 * fetti.scalar;\n      var outerRadius = 8 * fetti.scalar;\n      var x = fetti.x;\n      var y = fetti.y;\n      var spikes = 5;\n      var step = Math.PI / spikes;\n\n      while (spikes--) {\n        x = fetti.x + Math.cos(rot) * outerRadius;\n        y = fetti.y + Math.sin(rot) * outerRadius;\n        context.lineTo(x, y);\n        rot += step;\n\n        x = fetti.x + Math.cos(rot) * innerRadius;\n        y = fetti.y + Math.sin(rot) * innerRadius;\n        context.lineTo(x, y);\n        rot += step;\n      }\n    } else {\n      context.moveTo(Math.floor(fetti.x), Math.floor(fetti.y));\n      context.lineTo(Math.floor(fetti.wobbleX), Math.floor(y1));\n      context.lineTo(Math.floor(x2), Math.floor(y2));\n      context.lineTo(Math.floor(x1), Math.floor(fetti.wobbleY));\n    }\n\n    context.closePath();\n    context.fill();\n\n    return fetti.tick < fetti.totalTicks;\n  }\n\n  function animate(canvas, fettis, resizer, size, done) {\n    var animatingFettis = fettis.slice();\n    var context = canvas.getContext('2d');\n    var animationFrame;\n    var destroy;\n\n    var prom = promise(function (resolve) {\n      function onDone() {\n        animationFrame = destroy = null;\n\n        context.clearRect(0, 0, size.width, size.height);\n        bitmapMapper.clear();\n\n        done();\n        resolve();\n      }\n\n      function update() {\n        if (isWorker && !(size.width === workerSize.width && size.height === workerSize.height)) {\n          size.width = canvas.width = workerSize.width;\n          size.height = canvas.height = workerSize.height;\n        }\n\n        if (!size.width && !size.height) {\n          resizer(canvas);\n          size.width = canvas.width;\n          size.height = canvas.height;\n        }\n\n        context.clearRect(0, 0, size.width, size.height);\n\n        animatingFettis = animatingFettis.filter(function (fetti) {\n          return updateFetti(context, fetti);\n        });\n\n        if (animatingFettis.length) {\n          animationFrame = raf.frame(update);\n        } else {\n          onDone();\n        }\n      }\n\n      animationFrame = raf.frame(update);\n      destroy = onDone;\n    });\n\n    return {\n      addFettis: function (fettis) {\n        animatingFettis = animatingFettis.concat(fettis);\n\n        return prom;\n      },\n      canvas: canvas,\n      promise: prom,\n      reset: function () {\n        if (animationFrame) {\n          raf.cancel(animationFrame);\n        }\n\n        if (destroy) {\n          destroy();\n        }\n      }\n    };\n  }\n\n  function confettiCannon(canvas, globalOpts) {\n    var isLibCanvas = !canvas;\n    var allowResize = !!prop(globalOpts || {}, 'resize');\n    var hasResizeEventRegistered = false;\n    var globalDisableForReducedMotion = prop(globalOpts, 'disableForReducedMotion', Boolean);\n    var shouldUseWorker = canUseWorker && !!prop(globalOpts || {}, 'useWorker');\n    var worker = shouldUseWorker ? getWorker() : null;\n    var resizer = isLibCanvas ? setCanvasWindowSize : setCanvasRectSize;\n    var initialized = (canvas && worker) ? !!canvas.__confetti_initialized : false;\n    var preferLessMotion = typeof matchMedia === 'function' && matchMedia('(prefers-reduced-motion)').matches;\n    var animationObj;\n\n    function fireLocal(options, size, done) {\n      var particleCount = prop(options, 'particleCount', onlyPositiveInt);\n      var angle = prop(options, 'angle', Number);\n      var spread = prop(options, 'spread', Number);\n      var startVelocity = prop(options, 'startVelocity', Number);\n      var decay = prop(options, 'decay', Number);\n      var gravity = prop(options, 'gravity', Number);\n      var drift = prop(options, 'drift', Number);\n      var colors = prop(options, 'colors', colorsToRgb);\n      var ticks = prop(options, 'ticks', Number);\n      var shapes = prop(options, 'shapes');\n      var scalar = prop(options, 'scalar');\n      var flat = !!prop(options, 'flat');\n      var origin = getOrigin(options);\n\n      var temp = particleCount;\n      var fettis = [];\n\n      var startX = canvas.width * origin.x;\n      var startY = canvas.height * origin.y;\n\n      while (temp--) {\n        fettis.push(\n          randomPhysics({\n            x: startX,\n            y: startY,\n            angle: angle,\n            spread: spread,\n            startVelocity: startVelocity,\n            color: colors[temp % colors.length],\n            shape: shapes[randomInt(0, shapes.length)],\n            ticks: ticks,\n            decay: decay,\n            gravity: gravity,\n            drift: drift,\n            scalar: scalar,\n            flat: flat\n          })\n        );\n      }\n\n      // if we have a previous canvas already animating,\n      // add to it\n      if (animationObj) {\n        return animationObj.addFettis(fettis);\n      }\n\n      animationObj = animate(canvas, fettis, resizer, size , done);\n\n      return animationObj.promise;\n    }\n\n    function fire(options) {\n      var disableForReducedMotion = globalDisableForReducedMotion || prop(options, 'disableForReducedMotion', Boolean);\n      var zIndex = prop(options, 'zIndex', Number);\n\n      if (disableForReducedMotion && preferLessMotion) {\n        return promise(function (resolve) {\n          resolve();\n        });\n      }\n\n      if (isLibCanvas && animationObj) {\n        // use existing canvas from in-progress animation\n        canvas = animationObj.canvas;\n      } else if (isLibCanvas && !canvas) {\n        // create and initialize a new canvas\n        canvas = getCanvas(zIndex);\n        document.body.appendChild(canvas);\n      }\n\n      if (allowResize && !initialized) {\n        // initialize the size of a user-supplied canvas\n        resizer(canvas);\n      }\n\n      var size = {\n        width: canvas.width,\n        height: canvas.height\n      };\n\n      if (worker && !initialized) {\n        worker.init(canvas);\n      }\n\n      initialized = true;\n\n      if (worker) {\n        canvas.__confetti_initialized = true;\n      }\n\n      function onResize() {\n        if (worker) {\n          // TODO this really shouldn't be immediate, because it is expensive\n          var obj = {\n            getBoundingClientRect: function () {\n              if (!isLibCanvas) {\n                return canvas.getBoundingClientRect();\n              }\n            }\n          };\n\n          resizer(obj);\n\n          worker.postMessage({\n            resize: {\n              width: obj.width,\n              height: obj.height\n            }\n          });\n          return;\n        }\n\n        // don't actually query the size here, since this\n        // can execute frequently and rapidly\n        size.width = size.height = null;\n      }\n\n      function done() {\n        animationObj = null;\n\n        if (allowResize) {\n          hasResizeEventRegistered = false;\n          global.removeEventListener('resize', onResize);\n        }\n\n        if (isLibCanvas && canvas) {\n          if (document.body.contains(canvas)) {\n            document.body.removeChild(canvas); \n          }\n          canvas = null;\n          initialized = false;\n        }\n      }\n\n      if (allowResize && !hasResizeEventRegistered) {\n        hasResizeEventRegistered = true;\n        global.addEventListener('resize', onResize, false);\n      }\n\n      if (worker) {\n        return worker.fire(options, size, done);\n      }\n\n      return fireLocal(options, size, done);\n    }\n\n    fire.reset = function () {\n      if (worker) {\n        worker.reset();\n      }\n\n      if (animationObj) {\n        animationObj.reset();\n      }\n    };\n\n    return fire;\n  }\n\n  // Make default export lazy to defer worker creation until called.\n  var defaultFire;\n  function getDefaultFire() {\n    if (!defaultFire) {\n      defaultFire = confettiCannon(null, { useWorker: true, resize: true });\n    }\n    return defaultFire;\n  }\n\n  function transformPath2D(pathString, pathMatrix, x, y, scaleX, scaleY, rotation) {\n    var path2d = new Path2D(pathString);\n\n    var t1 = new Path2D();\n    t1.addPath(path2d, new DOMMatrix(pathMatrix));\n\n    var t2 = new Path2D();\n    // see https://developer.mozilla.org/en-US/docs/Web/API/DOMMatrix/DOMMatrix\n    t2.addPath(t1, new DOMMatrix([\n      Math.cos(rotation) * scaleX,\n      Math.sin(rotation) * scaleX,\n      -Math.sin(rotation) * scaleY,\n      Math.cos(rotation) * scaleY,\n      x,\n      y\n    ]));\n\n    return t2;\n  }\n\n  function shapeFromPath(pathData) {\n    if (!canUsePaths) {\n      throw new Error('path confetti are not supported in this browser');\n    }\n\n    var path, matrix;\n\n    if (typeof pathData === 'string') {\n      path = pathData;\n    } else {\n      path = pathData.path;\n      matrix = pathData.matrix;\n    }\n\n    var path2d = new Path2D(path);\n    var tempCanvas = document.createElement('canvas');\n    var tempCtx = tempCanvas.getContext('2d');\n\n    if (!matrix) {\n      // attempt to figure out the width of the path, up to 1000x1000\n      var maxSize = 1000;\n      var minX = maxSize;\n      var minY = maxSize;\n      var maxX = 0;\n      var maxY = 0;\n      var width, height;\n\n      // do some line skipping... this is faster than checking\n      // every pixel and will be mostly still correct\n      for (var x = 0; x < maxSize; x += 2) {\n        for (var y = 0; y < maxSize; y += 2) {\n          if (tempCtx.isPointInPath(path2d, x, y, 'nonzero')) {\n            minX = Math.min(minX, x);\n            minY = Math.min(minY, y);\n            maxX = Math.max(maxX, x);\n            maxY = Math.max(maxY, y);\n          }\n        }\n      }\n\n      width = maxX - minX;\n      height = maxY - minY;\n\n      var maxDesiredSize = 10;\n      var scale = Math.min(maxDesiredSize/width, maxDesiredSize/height);\n\n      matrix = [\n        scale, 0, 0, scale,\n        -Math.round((width/2) + minX) * scale,\n        -Math.round((height/2) + minY) * scale\n      ];\n    }\n\n    return {\n      type: 'path',\n      path: path,\n      matrix: matrix\n    };\n  }\n\n  function shapeFromText(textData) {\n    var text,\n        scalar = 1,\n        color = '#000000',\n        // see https://nolanlawson.com/2022/04/08/the-struggle-of-using-native-emoji-on-the-web/\n        fontFamily = '\"Apple Color Emoji\", \"Segoe UI Emoji\", \"Segoe UI Symbol\", \"Noto Color Emoji\", \"EmojiOne Color\", \"Android Emoji\", \"Twemoji Mozilla\", \"system emoji\", sans-serif';\n\n    if (typeof textData === 'string') {\n      text = textData;\n    } else {\n      text = textData.text;\n      scalar = 'scalar' in textData ? textData.scalar : scalar;\n      fontFamily = 'fontFamily' in textData ? textData.fontFamily : fontFamily;\n      color = 'color' in textData ? textData.color : color;\n    }\n\n    // all other confetti are 10 pixels,\n    // so this pixel size is the de-facto 100% scale confetti\n    var fontSize = 10 * scalar;\n    var font = '' + fontSize + 'px ' + fontFamily;\n\n    var canvas = new OffscreenCanvas(fontSize, fontSize);\n    var ctx = canvas.getContext('2d');\n\n    ctx.font = font;\n    var size = ctx.measureText(text);\n    var width = Math.ceil(size.actualBoundingBoxRight + size.actualBoundingBoxLeft);\n    var height = Math.ceil(size.actualBoundingBoxAscent + size.actualBoundingBoxDescent);\n\n    var padding = 2;\n    var x = size.actualBoundingBoxLeft + padding;\n    var y = size.actualBoundingBoxAscent + padding;\n    width += padding + padding;\n    height += padding + padding;\n\n    canvas = new OffscreenCanvas(width, height);\n    ctx = canvas.getContext('2d');\n    ctx.font = font;\n    ctx.fillStyle = color;\n\n    ctx.fillText(text, x, y);\n\n    var scale = 1 / scalar;\n\n    return {\n      type: 'bitmap',\n      // TODO these probably need to be transfered for workers\n      bitmap: canvas.transferToImageBitmap(),\n      matrix: [scale, 0, 0, scale, -width * scale / 2, -height * scale / 2]\n    };\n  }\n\n  module.exports = function() {\n    return getDefaultFire().apply(this, arguments);\n  };\n  module.exports.reset = function() {\n    getDefaultFire().reset();\n  };\n  module.exports.create = confettiCannon;\n  module.exports.shapeFromPath = shapeFromPath;\n  module.exports.shapeFromText = shapeFromText;\n}((function () {\n  if (typeof window !== 'undefined') {\n    return window;\n  }\n\n  if (typeof self !== 'undefined') {\n    return self;\n  }\n\n  return this || {};\n})(), module, false));\n\n// end source content\n\nexport default module.exports;\nexport var create = module.exports.create;\n", "import { useRef } from 'react';\nimport { useIsomorphicLayoutEffect } from './use-isomorphic-effect.mjs';\n\nfunction useIsMounted() {\n    const isMounted = useRef(false);\n    useIsomorphicLayoutEffect(() => {\n        isMounted.current = true;\n        return () => {\n            isMounted.current = false;\n        };\n    }, []);\n    return isMounted;\n}\n\nexport { useIsMounted };\n", "import * as React from 'react';\nimport { useId, useRef, useInsertionEffect } from 'react';\n\n/**\n * Measurement functionality has to be within a separate component\n * to leverage snapshot lifecycle.\n */\nclass PopChildMeasure extends React.Component {\n    getSnapshotBeforeUpdate(prevProps) {\n        const element = this.props.childRef.current;\n        if (element && prevProps.isPresent && !this.props.isPresent) {\n            const size = this.props.sizeRef.current;\n            size.height = element.offsetHeight || 0;\n            size.width = element.offsetWidth || 0;\n            size.top = element.offsetTop;\n            size.left = element.offsetLeft;\n        }\n        return null;\n    }\n    /**\n     * Required with getSnapshotBeforeUpdate to stop React complaining.\n     */\n    componentDidUpdate() { }\n    render() {\n        return this.props.children;\n    }\n}\nfunction PopChild({ children, isPresent }) {\n    const id = useId();\n    const ref = useRef(null);\n    const size = useRef({\n        width: 0,\n        height: 0,\n        top: 0,\n        left: 0,\n    });\n    /**\n     * We create and inject a style block so we can apply this explicit\n     * sizing in a non-destructive manner by just deleting the style block.\n     *\n     * We can't apply size via render as the measurement happens\n     * in getSnapshotBeforeUpdate (post-render), likewise if we apply the\n     * styles directly on the DOM node, we might be overwriting\n     * styles set via the style prop.\n     */\n    useInsertionEffect(() => {\n        const { width, height, top, left } = size.current;\n        if (isPresent || !ref.current || !width || !height)\n            return;\n        ref.current.dataset.motionPopId = id;\n        const style = document.createElement(\"style\");\n        document.head.appendChild(style);\n        if (style.sheet) {\n            style.sheet.insertRule(`\n          [data-motion-pop-id=\"${id}\"] {\n            position: absolute !important;\n            width: ${width}px !important;\n            height: ${height}px !important;\n            top: ${top}px !important;\n            left: ${left}px !important;\n          }\n        `);\n        }\n        return () => {\n            document.head.removeChild(style);\n        };\n    }, [isPresent]);\n    return (React.createElement(PopChildMeasure, { isPresent: isPresent, childRef: ref, sizeRef: size }, React.cloneElement(children, { ref })));\n}\n\nexport { PopChild };\n", "import * as React from 'react';\nimport { useId, useMemo } from 'react';\nimport { PresenceContext } from '../../context/PresenceContext.mjs';\nimport { useConstant } from '../../utils/use-constant.mjs';\nimport { PopChild } from './PopChild.mjs';\n\nconst PresenceChild = ({ children, initial, isPresent, onExitComplete, custom, presenceAffectsLayout, mode, }) => {\n    const presenceChildren = useConstant(newChildrenMap);\n    const id = useId();\n    const context = useMemo(() => ({\n        id,\n        initial,\n        isPresent,\n        custom,\n        onExitComplete: (childId) => {\n            presenceChildren.set(childId, true);\n            for (const isComplete of presenceChildren.values()) {\n                if (!isComplete)\n                    return; // can stop searching when any is incomplete\n            }\n            onExitComplete && onExitComplete();\n        },\n        register: (childId) => {\n            presenceChildren.set(childId, false);\n            return () => presenceChildren.delete(childId);\n        },\n    }), \n    /**\n     * If the presence of a child affects the layout of the components around it,\n     * we want to make a new context value to ensure they get re-rendered\n     * so they can detect that layout change.\n     */\n    presenceAffectsLayout ? undefined : [isPresent]);\n    useMemo(() => {\n        presenceChildren.forEach((_, key) => presenceChildren.set(key, false));\n    }, [isPresent]);\n    /**\n     * If there's no `motion` components to fire exit animations, we want to remove this\n     * component immediately.\n     */\n    React.useEffect(() => {\n        !isPresent &&\n            !presenceChildren.size &&\n            onExitComplete &&\n            onExitComplete();\n    }, [isPresent]);\n    if (mode === \"popLayout\") {\n        children = React.createElement(PopChild, { isPresent: isPresent }, children);\n    }\n    return (React.createElement(PresenceContext.Provider, { value: context }, children));\n};\nfunction newChildrenMap() {\n    return new Map();\n}\n\nexport { PresenceChild };\n", "import * as React from 'react';\nimport { useContext, useRef, cloneElement, Children, isValidElement } from 'react';\nimport { useForceUpdate } from '../../utils/use-force-update.mjs';\nimport { useIsMounted } from '../../utils/use-is-mounted.mjs';\nimport { PresenceChild } from './PresenceChild.mjs';\nimport { LayoutGroupContext } from '../../context/LayoutGroupContext.mjs';\nimport { useIsomorphicLayoutEffect } from '../../utils/use-isomorphic-effect.mjs';\nimport { useUnmountEffect } from '../../utils/use-unmount-effect.mjs';\nimport { invariant } from '../../utils/errors.mjs';\n\nconst getChildKey = (child) => child.key || \"\";\nfunction updateChildLookup(children, allChildren) {\n    children.forEach((child) => {\n        const key = getChildKey(child);\n        allChildren.set(key, child);\n    });\n}\nfunction onlyElements(children) {\n    const filtered = [];\n    // We use forEach here instead of map as map mutates the component key by preprending `.$`\n    Children.forEach(children, (child) => {\n        if (isValidElement(child))\n            filtered.push(child);\n    });\n    return filtered;\n}\n/**\n * `AnimatePresence` enables the animation of components that have been removed from the tree.\n *\n * When adding/removing more than a single child, every child **must** be given a unique `key` prop.\n *\n * Any `motion` components that have an `exit` property defined will animate out when removed from\n * the tree.\n *\n * ```jsx\n * import { motion, AnimatePresence } from 'framer-motion'\n *\n * export const Items = ({ items }) => (\n *   <AnimatePresence>\n *     {items.map(item => (\n *       <motion.div\n *         key={item.id}\n *         initial={{ opacity: 0 }}\n *         animate={{ opacity: 1 }}\n *         exit={{ opacity: 0 }}\n *       />\n *     ))}\n *   </AnimatePresence>\n * )\n * ```\n *\n * You can sequence exit animations throughout a tree using variants.\n *\n * If a child contains multiple `motion` components with `exit` props, it will only unmount the child\n * once all `motion` components have finished animating out. Likewise, any components using\n * `usePresence` all need to call `safeToRemove`.\n *\n * @public\n */\nconst AnimatePresence = ({ children, custom, initial = true, onExitComplete, exitBeforeEnter, presenceAffectsLayout = true, mode = \"sync\", }) => {\n    invariant(!exitBeforeEnter, \"Replace exitBeforeEnter with mode='wait'\");\n    // We want to force a re-render once all exiting animations have finished. We\n    // either use a local forceRender function, or one from a parent context if it exists.\n    const forceRender = useContext(LayoutGroupContext).forceRender || useForceUpdate()[0];\n    const isMounted = useIsMounted();\n    // Filter out any children that aren't ReactElements. We can only track ReactElements with a props.key\n    const filteredChildren = onlyElements(children);\n    let childrenToRender = filteredChildren;\n    const exitingChildren = useRef(new Map()).current;\n    // Keep a living record of the children we're actually rendering so we\n    // can diff to figure out which are entering and exiting\n    const presentChildren = useRef(childrenToRender);\n    // A lookup table to quickly reference components by key\n    const allChildren = useRef(new Map()).current;\n    // If this is the initial component render, just deal with logic surrounding whether\n    // we play onMount animations or not.\n    const isInitialRender = useRef(true);\n    useIsomorphicLayoutEffect(() => {\n        isInitialRender.current = false;\n        updateChildLookup(filteredChildren, allChildren);\n        presentChildren.current = childrenToRender;\n    });\n    useUnmountEffect(() => {\n        isInitialRender.current = true;\n        allChildren.clear();\n        exitingChildren.clear();\n    });\n    if (isInitialRender.current) {\n        return (React.createElement(React.Fragment, null, childrenToRender.map((child) => (React.createElement(PresenceChild, { key: getChildKey(child), isPresent: true, initial: initial ? undefined : false, presenceAffectsLayout: presenceAffectsLayout, mode: mode }, child)))));\n    }\n    // If this is a subsequent render, deal with entering and exiting children\n    childrenToRender = [...childrenToRender];\n    // Diff the keys of the currently-present and target children to update our\n    // exiting list.\n    const presentKeys = presentChildren.current.map(getChildKey);\n    const targetKeys = filteredChildren.map(getChildKey);\n    // Diff the present children with our target children and mark those that are exiting\n    const numPresent = presentKeys.length;\n    for (let i = 0; i < numPresent; i++) {\n        const key = presentKeys[i];\n        if (targetKeys.indexOf(key) === -1 && !exitingChildren.has(key)) {\n            exitingChildren.set(key, undefined);\n        }\n    }\n    // If we currently have exiting children, and we're deferring rendering incoming children\n    // until after all current children have exiting, empty the childrenToRender array\n    if (mode === \"wait\" && exitingChildren.size) {\n        childrenToRender = [];\n    }\n    // Loop through all currently exiting components and clone them to overwrite `animate`\n    // with any `exit` prop they might have defined.\n    exitingChildren.forEach((component, key) => {\n        // If this component is actually entering again, early return\n        if (targetKeys.indexOf(key) !== -1)\n            return;\n        const child = allChildren.get(key);\n        if (!child)\n            return;\n        const insertionIndex = presentKeys.indexOf(key);\n        let exitingComponent = component;\n        if (!exitingComponent) {\n            const onExit = () => {\n                // clean up the exiting children map\n                exitingChildren.delete(key);\n                // compute the keys of children that were rendered once but are no longer present\n                // this could happen in case of too many fast consequent renderings\n                // @link https://github.com/framer/motion/issues/2023\n                const leftOverKeys = Array.from(allChildren.keys()).filter((childKey) => !targetKeys.includes(childKey));\n                // clean up the all children map\n                leftOverKeys.forEach((leftOverKey) => allChildren.delete(leftOverKey));\n                // make sure to render only the children that are actually visible\n                presentChildren.current = filteredChildren.filter((presentChild) => {\n                    const presentChildKey = getChildKey(presentChild);\n                    return (\n                    // filter out the node exiting\n                    presentChildKey === key ||\n                        // filter out the leftover children\n                        leftOverKeys.includes(presentChildKey));\n                });\n                // Defer re-rendering until all exiting children have indeed left\n                if (!exitingChildren.size) {\n                    if (isMounted.current === false)\n                        return;\n                    forceRender();\n                    onExitComplete && onExitComplete();\n                }\n            };\n            exitingComponent = (React.createElement(PresenceChild, { key: getChildKey(child), isPresent: false, onExitComplete: onExit, custom: custom, presenceAffectsLayout: presenceAffectsLayout, mode: mode }, child));\n            exitingChildren.set(key, exitingComponent);\n        }\n        childrenToRender.splice(insertionIndex, 0, exitingComponent);\n    });\n    // Add `MotionContext` even to children that don't need it to ensure we're rendering\n    // the same tree between renders\n    childrenToRender = childrenToRender.map((child) => {\n        const key = child.key;\n        return exitingChildren.has(key) ? (child) : (React.createElement(PresenceChild, { key: getChildKey(child), isPresent: true, presenceAffectsLayout: presenceAffectsLayout, mode: mode }, child));\n    });\n    if (process.env.NODE_ENV !== \"production\" &&\n        mode === \"wait\" &&\n        childrenToRender.length > 1) {\n        console.warn(`You're attempting to animate multiple children within AnimatePresence, but its mode is set to \"wait\". This will lead to odd visual behaviour.`);\n    }\n    return (React.createElement(React.Fragment, null, exitingChildren.size\n        ? childrenToRender\n        : childrenToRender.map((child) => cloneElement(child))));\n};\n\nexport { AnimatePresence };\n", "import { useState, useCallback } from 'react';\nimport { useIsMounted } from './use-is-mounted.mjs';\nimport { frame } from '../frameloop/frame.mjs';\n\nfunction useForceUpdate() {\n    const isMounted = useIsMounted();\n    const [forcedRenderCount, setForcedRenderCount] = useState(0);\n    const forceRender = useCallback(() => {\n        isMounted.current && setForcedRenderCount(forcedRenderCount + 1);\n    }, [forcedRenderCount]);\n    /**\n     * Defer this to the end of the next animation frame in case there are multiple\n     * synchronous calls.\n     */\n    const deferredForceRender = useCallback(() => frame.postRender(forceRender), [forceRender]);\n    return [deferredForceRender, forcedRenderCount];\n}\n\nexport { useForceUpdate };\n", "import { useEffect } from 'react';\n\nfunction useUnmountEffect(callback) {\n    return useEffect(() => () => callback(), []);\n}\n\nexport { useUnmountEffect };\n"], "names": ["module", "main", "global", "isWorker", "workerSize", "canUseWorker", "Worker", "Blob", "Promise", "OffscreenCanvas", "OffscreenCanvasRenderingContext2D", "HTMLCanvasElement", "prototype", "transferControlToOffscreen", "URL", "createObjectURL", "canUsePaths", "Path2D", "DOMMatrix", "canDrawBitmap", "canvas", "ctx", "getContext", "fillRect", "bitmap", "transferToImageBitmap", "createPattern", "e", "noop", "promise", "func", "ModulePromise", "exports", "Prom", "skipTransform", "map", "defaultFire", "bitmapMapper", "Map", "transform", "has", "get", "width", "height", "drawImage", "set", "clear", "raf", "frame", "cancel", "TIME", "Math", "floor", "frames", "lastFrameTime", "requestAnimationFrame", "cancelAnimationFrame", "cb", "id", "random", "onFrame", "time", "setTimeout", "timer", "clearTimeout", "getWorker", "worker", "prom", "resolves", "code", "toString", "join", "undefined", "console", "warn", "execute", "options", "callback", "postMessage", "init", "offscreen", "fire", "size", "done", "slice", "resolve", "workerDone", "msg", "data", "removeEventListener", "addEventListener", "bind", "reset", "decorate", "defaults", "particleCount", "angle", "spread", "startVelocity", "decay", "gravity", "drift", "ticks", "x", "y", "shapes", "zIndex", "colors", "disableForReducedMotion", "scalar", "prop", "name", "val", "convert", "onlyPositiveInt", "number", "toDecimal", "str", "parseInt", "colorsToRgb", "hexToRgb", "String", "replace", "length", "r", "substring", "g", "b", "setCanvasWindowSize", "document", "documentElement", "clientWidth", "clientHeight", "setCanvasRectSize", "rect", "getBoundingClientRect", "randomPhysics", "opts", "radAngle", "PI", "radSpread", "wobble", "wobbleSpeed", "min", "velocity", "angle2D", "tiltAngle", "color", "shape", "tick", "totalTicks", "tiltSin", "tiltCos", "wobbleX", "wobbleY", "ovalScalar", "flat", "updateFetti", "context", "fetti", "cos", "sin", "progress", "x1", "y1", "x2", "y2", "fillStyle", "beginPath", "type", "path", "Array", "isArray", "matrix", "fill", "pathString", "pathMatrix", "scaleX", "scaleY", "rotation", "path2d", "t1", "addPath", "t2", "transformPath2D", "abs", "multiplySelf", "pattern", "setTransform", "globalAlpha", "ellipse", "radiusX", "radiusY", "startAngle", "endAngle", "antiClockwise", "save", "translate", "rotate", "scale", "arc", "restore", "rot", "innerRadius", "outerRadius", "spikes", "step", "lineTo", "moveTo", "closePath", "confettiCannon", "globalOpts", "animationObj", "isLibCanvas", "allowResize", "hasResizeEventRegistered", "globalDisableForReducedMotion", "Boolean", "resizer", "initialized", "__confetti_initialized", "preferLessMotion", "matchMedia", "matches", "fireLocal", "max", "Number", "origin", "Object", "<PERSON><PERSON><PERSON><PERSON>", "temp", "fettis", "startX", "startY", "push", "addFettis", "animationFrame", "destroy", "animating<PERSON>ettis", "onDone", "clearRect", "update", "filter", "concat", "animate", "createElement", "style", "position", "top", "left", "pointerEvents", "get<PERSON>anvas", "body", "append<PERSON><PERSON><PERSON>", "onResize", "obj", "resize", "contains", "<PERSON><PERSON><PERSON><PERSON>", "getDefaultFire", "useWorker", "apply", "this", "arguments", "create", "shapeFromPath", "pathData", "Error", "tempCtx", "maxSize", "minX", "minY", "maxX", "maxY", "isPointInPath", "round", "shapeFromText", "textData", "text", "fontFamily", "fontSize", "font", "measureText", "ceil", "actualBoundingBoxRight", "actualBoundingBoxLeft", "actualBoundingBoxAscent", "actualBoundingBoxDescent", "padding", "fillText", "window", "self", "useIsMounted", "isMounted", "useRef", "useIsomorphicLayoutEffect", "current", "PopChildMeasure", "React", "getSnapshotBeforeUpdate", "prevProps", "element", "props", "childRef", "isPresent", "sizeRef", "offsetHeight", "offsetWidth", "offsetTop", "offsetLeft", "componentDidUpdate", "render", "children", "PopChild", "_ref", "useId", "ref", "useInsertionEffect", "dataset", "motionPopId", "head", "sheet", "insertRule", "Presence<PERSON><PERSON><PERSON>", "initial", "onExitComplete", "custom", "presenceAffectsLayout", "mode", "presenceC<PERSON><PERSON>n", "useConstant", "newChildrenMap", "useMemo", "childId", "isComplete", "values", "register", "delete", "for<PERSON>ach", "_", "key", "PresenceContext", "Provider", "value", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "child", "AnimatePresence", "exitBeforeEnter", "invariant", "forceRender", "useContext", "LayoutGroupContext", "forcedRenderCount", "setForcedRenderCount", "useState", "useCallback", "postRender", "useForceUpdate", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "filtered", "Children", "isValidElement", "onlyElements", "children<PERSON><PERSON><PERSON><PERSON>", "exiting<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "allChildren", "isInitialRender", "updateChildLookup", "useEffect", "present<PERSON><PERSON>s", "targetKeys", "numPresent", "i", "indexOf", "component", "insertionIndex", "exitingComponent", "onExit", "leftOverKeys", "from", "keys", "<PERSON><PERSON><PERSON>", "includes", "leftOverKey", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "splice", "cloneElement"], "sourceRoot": ""}