{"ast": null, "code": "import _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport * as React from 'react';\n// recursion (flat tree structure)\nfunction flatRecord(record, indent, childrenColumnName, expandedKeys, getRowKey, index) {\n  var arr = [];\n  arr.push({\n    record: record,\n    indent: indent,\n    index: index\n  });\n  var key = getRowKey(record);\n  var expanded = expandedKeys === null || expandedKeys === void 0 ? void 0 : expandedKeys.has(key);\n  if (record && Array.isArray(record[childrenColumnName]) && expanded) {\n    // expanded state, flat record\n    for (var i = 0; i < record[childrenColumnName].length; i += 1) {\n      var tempArr = flatRecord(record[childrenColumnName][i], indent + 1, childrenColumnName, expandedKeys, getRowKey, i);\n      arr.push.apply(arr, _toConsumableArray(tempArr));\n    }\n  }\n  return arr;\n}\n\n/**\n * flat tree data on expanded state\n *\n * @export\n * @template T\n * @param {*} data : table data\n * @param {string} childrenColumnName : 指定树形结构的列名\n * @param {Set<Key>} expandedKeys : 展开的行对应的keys\n * @param {GetRowKey<T>} getRowKey  : 获取当前rowKey的方法\n * @returns flattened data\n */\nexport default function useFlattenRecords(data, childrenColumnName, expandedKeys, getRowKey) {\n  var arr = React.useMemo(function () {\n    if (expandedKeys !== null && expandedKeys !== void 0 && expandedKeys.size) {\n      var temp = [];\n\n      // collect flattened record\n      for (var i = 0; i < (data === null || data === void 0 ? void 0 : data.length); i += 1) {\n        var _temp;\n        var record = data[i];\n\n        // using array.push or spread operator may cause \"Maximum call stack size exceeded\" exception if array size is big enough.\n        temp = (_temp = temp).concat.apply(_temp, _toConsumableArray(flatRecord(record, 0, childrenColumnName, expandedKeys, getRowKey, i)));\n      }\n      return temp;\n    }\n    return data === null || data === void 0 ? void 0 : data.map(function (item, index) {\n      return {\n        record: item,\n        indent: 0,\n        index: index\n      };\n    });\n  }, [data, childrenColumnName, expandedKeys, getRowKey]);\n  return arr;\n}", "map": {"version": 3, "names": ["_toConsumableArray", "React", "flatRecord", "record", "indent", "childrenColumnName", "expandedKeys", "getRowKey", "index", "arr", "push", "key", "expanded", "has", "Array", "isArray", "i", "length", "tempArr", "apply", "useFlattenRecords", "data", "useMemo", "size", "temp", "_temp", "concat", "map", "item"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/node_modules/rc-table/es/hooks/useFlattenRecords.js"], "sourcesContent": ["import _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport * as React from 'react';\n// recursion (flat tree structure)\nfunction flatRecord(record, indent, childrenColumnName, expandedKeys, getRowKey, index) {\n  var arr = [];\n  arr.push({\n    record: record,\n    indent: indent,\n    index: index\n  });\n  var key = getRowKey(record);\n  var expanded = expandedKeys === null || expandedKeys === void 0 ? void 0 : expandedKeys.has(key);\n  if (record && Array.isArray(record[childrenColumnName]) && expanded) {\n    // expanded state, flat record\n    for (var i = 0; i < record[childrenColumnName].length; i += 1) {\n      var tempArr = flatRecord(record[childrenColumnName][i], indent + 1, childrenColumnName, expandedKeys, getRowKey, i);\n      arr.push.apply(arr, _toConsumableArray(tempArr));\n    }\n  }\n  return arr;\n}\n\n/**\n * flat tree data on expanded state\n *\n * @export\n * @template T\n * @param {*} data : table data\n * @param {string} childrenColumnName : 指定树形结构的列名\n * @param {Set<Key>} expandedKeys : 展开的行对应的keys\n * @param {GetRowKey<T>} getRowKey  : 获取当前rowKey的方法\n * @returns flattened data\n */\nexport default function useFlattenRecords(data, childrenColumnName, expandedKeys, getRowKey) {\n  var arr = React.useMemo(function () {\n    if (expandedKeys !== null && expandedKeys !== void 0 && expandedKeys.size) {\n      var temp = [];\n\n      // collect flattened record\n      for (var i = 0; i < (data === null || data === void 0 ? void 0 : data.length); i += 1) {\n        var _temp;\n        var record = data[i];\n\n        // using array.push or spread operator may cause \"Maximum call stack size exceeded\" exception if array size is big enough.\n        temp = (_temp = temp).concat.apply(_temp, _toConsumableArray(flatRecord(record, 0, childrenColumnName, expandedKeys, getRowKey, i)));\n      }\n      return temp;\n    }\n    return data === null || data === void 0 ? void 0 : data.map(function (item, index) {\n      return {\n        record: item,\n        indent: 0,\n        index: index\n      };\n    });\n  }, [data, childrenColumnName, expandedKeys, getRowKey]);\n  return arr;\n}"], "mappings": "AAAA,OAAOA,kBAAkB,MAAM,8CAA8C;AAC7E,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B;AACA,SAASC,UAAUA,CAACC,MAAM,EAAEC,MAAM,EAAEC,kBAAkB,EAAEC,YAAY,EAAEC,SAAS,EAAEC,KAAK,EAAE;EACtF,IAAIC,GAAG,GAAG,EAAE;EACZA,GAAG,CAACC,IAAI,CAAC;IACPP,MAAM,EAAEA,MAAM;IACdC,MAAM,EAAEA,MAAM;IACdI,KAAK,EAAEA;EACT,CAAC,CAAC;EACF,IAAIG,GAAG,GAAGJ,SAAS,CAACJ,MAAM,CAAC;EAC3B,IAAIS,QAAQ,GAAGN,YAAY,KAAK,IAAI,IAAIA,YAAY,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,YAAY,CAACO,GAAG,CAACF,GAAG,CAAC;EAChG,IAAIR,MAAM,IAAIW,KAAK,CAACC,OAAO,CAACZ,MAAM,CAACE,kBAAkB,CAAC,CAAC,IAAIO,QAAQ,EAAE;IACnE;IACA,KAAK,IAAII,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGb,MAAM,CAACE,kBAAkB,CAAC,CAACY,MAAM,EAAED,CAAC,IAAI,CAAC,EAAE;MAC7D,IAAIE,OAAO,GAAGhB,UAAU,CAACC,MAAM,CAACE,kBAAkB,CAAC,CAACW,CAAC,CAAC,EAAEZ,MAAM,GAAG,CAAC,EAAEC,kBAAkB,EAAEC,YAAY,EAAEC,SAAS,EAAES,CAAC,CAAC;MACnHP,GAAG,CAACC,IAAI,CAACS,KAAK,CAACV,GAAG,EAAET,kBAAkB,CAACkB,OAAO,CAAC,CAAC;IAClD;EACF;EACA,OAAOT,GAAG;AACZ;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,SAASW,iBAAiBA,CAACC,IAAI,EAAEhB,kBAAkB,EAAEC,YAAY,EAAEC,SAAS,EAAE;EAC3F,IAAIE,GAAG,GAAGR,KAAK,CAACqB,OAAO,CAAC,YAAY;IAClC,IAAIhB,YAAY,KAAK,IAAI,IAAIA,YAAY,KAAK,KAAK,CAAC,IAAIA,YAAY,CAACiB,IAAI,EAAE;MACzE,IAAIC,IAAI,GAAG,EAAE;;MAEb;MACA,KAAK,IAAIR,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAIK,IAAI,KAAK,IAAI,IAAIA,IAAI,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,IAAI,CAACJ,MAAM,CAAC,EAAED,CAAC,IAAI,CAAC,EAAE;QACrF,IAAIS,KAAK;QACT,IAAItB,MAAM,GAAGkB,IAAI,CAACL,CAAC,CAAC;;QAEpB;QACAQ,IAAI,GAAG,CAACC,KAAK,GAAGD,IAAI,EAAEE,MAAM,CAACP,KAAK,CAACM,KAAK,EAAEzB,kBAAkB,CAACE,UAAU,CAACC,MAAM,EAAE,CAAC,EAAEE,kBAAkB,EAAEC,YAAY,EAAEC,SAAS,EAAES,CAAC,CAAC,CAAC,CAAC;MACtI;MACA,OAAOQ,IAAI;IACb;IACA,OAAOH,IAAI,KAAK,IAAI,IAAIA,IAAI,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,IAAI,CAACM,GAAG,CAAC,UAAUC,IAAI,EAAEpB,KAAK,EAAE;MACjF,OAAO;QACLL,MAAM,EAAEyB,IAAI;QACZxB,MAAM,EAAE,CAAC;QACTI,KAAK,EAAEA;MACT,CAAC;IACH,CAAC,CAAC;EACJ,CAAC,EAAE,CAACa,IAAI,EAAEhB,kBAAkB,EAAEC,YAAY,EAAEC,SAAS,CAAC,CAAC;EACvD,OAAOE,GAAG;AACZ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}