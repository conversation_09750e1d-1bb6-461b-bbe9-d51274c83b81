{"ast": null, "code": "import _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport * as React from 'react';\nimport { useRef, useEffect } from 'react';\nimport findDOMNode from \"rc-util/es/Dom/findDOMNode\";\nimport raf from \"rc-util/es/raf\";\nimport CacheMap from '../utils/CacheMap';\nexport default function useHeights(getKey, onItemAdd, onItemRemove) {\n  var _React$useState = React.useState(0),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    updatedMark = _React$useState2[0],\n    setUpdatedMark = _React$useState2[1];\n  var instanceRef = useRef(new Map());\n  var heightsRef = useRef(new CacheMap());\n  var collectRafRef = useRef();\n  function cancelRaf() {\n    raf.cancel(collectRafRef.current);\n  }\n  function collectHeight() {\n    cancelRaf();\n    collectRafRef.current = raf(function () {\n      instanceRef.current.forEach(function (element, key) {\n        if (element && element.offsetParent) {\n          var htmlElement = findDOMNode(element);\n          var offsetHeight = htmlElement.offsetHeight;\n          if (heightsRef.current.get(key) !== offsetHeight) {\n            heightsRef.current.set(key, htmlElement.offsetHeight);\n          }\n        }\n      });\n      // Always trigger update mark to tell parent that should re-calculate heights when resized\n      setUpdatedMark(function (c) {\n        return c + 1;\n      });\n    });\n  }\n  function setInstanceRef(item, instance) {\n    var key = getKey(item);\n    var origin = instanceRef.current.get(key);\n    if (instance) {\n      instanceRef.current.set(key, instance);\n      collectHeight();\n    } else {\n      instanceRef.current.delete(key);\n    }\n    // Instance changed\n    if (!origin !== !instance) {\n      if (instance) {\n        onItemAdd === null || onItemAdd === void 0 ? void 0 : onItemAdd(item);\n      } else {\n        onItemRemove === null || onItemRemove === void 0 ? void 0 : onItemRemove(item);\n      }\n    }\n  }\n  useEffect(function () {\n    return cancelRaf;\n  }, []);\n  return [setInstanceRef, collectHeight, heightsRef.current, updatedMark];\n}", "map": {"version": 3, "names": ["_slicedToArray", "React", "useRef", "useEffect", "findDOMNode", "raf", "CacheMap", "useHeights", "<PERSON><PERSON><PERSON>", "onItemAdd", "onItemRemove", "_React$useState", "useState", "_React$useState2", "updatedMark", "setUpdatedMark", "instanceRef", "Map", "heightsRef", "collectRafRef", "cancelRaf", "cancel", "current", "collectHeight", "for<PERSON>ach", "element", "key", "offsetParent", "htmlElement", "offsetHeight", "get", "set", "c", "setInstanceRef", "item", "instance", "origin", "delete"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/node_modules/rc-virtual-list/es/hooks/useHeights.js"], "sourcesContent": ["import _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport * as React from 'react';\nimport { useRef, useEffect } from 'react';\nimport findDOMNode from \"rc-util/es/Dom/findDOMNode\";\nimport raf from \"rc-util/es/raf\";\nimport CacheMap from '../utils/CacheMap';\nexport default function useHeights(getKey, onItemAdd, onItemRemove) {\n  var _React$useState = React.useState(0),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    updatedMark = _React$useState2[0],\n    setUpdatedMark = _React$useState2[1];\n  var instanceRef = useRef(new Map());\n  var heightsRef = useRef(new CacheMap());\n  var collectRafRef = useRef();\n  function cancelRaf() {\n    raf.cancel(collectRafRef.current);\n  }\n  function collectHeight() {\n    cancelRaf();\n    collectRafRef.current = raf(function () {\n      instanceRef.current.forEach(function (element, key) {\n        if (element && element.offsetParent) {\n          var htmlElement = findDOMNode(element);\n          var offsetHeight = htmlElement.offsetHeight;\n          if (heightsRef.current.get(key) !== offsetHeight) {\n            heightsRef.current.set(key, htmlElement.offsetHeight);\n          }\n        }\n      });\n      // Always trigger update mark to tell parent that should re-calculate heights when resized\n      setUpdatedMark(function (c) {\n        return c + 1;\n      });\n    });\n  }\n  function setInstanceRef(item, instance) {\n    var key = getKey(item);\n    var origin = instanceRef.current.get(key);\n    if (instance) {\n      instanceRef.current.set(key, instance);\n      collectHeight();\n    } else {\n      instanceRef.current.delete(key);\n    }\n    // Instance changed\n    if (!origin !== !instance) {\n      if (instance) {\n        onItemAdd === null || onItemAdd === void 0 ? void 0 : onItemAdd(item);\n      } else {\n        onItemRemove === null || onItemRemove === void 0 ? void 0 : onItemRemove(item);\n      }\n    }\n  }\n  useEffect(function () {\n    return cancelRaf;\n  }, []);\n  return [setInstanceRef, collectHeight, heightsRef.current, updatedMark];\n}"], "mappings": "AAAA,OAAOA,cAAc,MAAM,0CAA0C;AACrE,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,MAAM,EAAEC,SAAS,QAAQ,OAAO;AACzC,OAAOC,WAAW,MAAM,4BAA4B;AACpD,OAAOC,GAAG,MAAM,gBAAgB;AAChC,OAAOC,QAAQ,MAAM,mBAAmB;AACxC,eAAe,SAASC,UAAUA,CAACC,MAAM,EAAEC,SAAS,EAAEC,YAAY,EAAE;EAClE,IAAIC,eAAe,GAAGV,KAAK,CAACW,QAAQ,CAAC,CAAC,CAAC;IACrCC,gBAAgB,GAAGb,cAAc,CAACW,eAAe,EAAE,CAAC,CAAC;IACrDG,WAAW,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IACjCE,cAAc,GAAGF,gBAAgB,CAAC,CAAC,CAAC;EACtC,IAAIG,WAAW,GAAGd,MAAM,CAAC,IAAIe,GAAG,CAAC,CAAC,CAAC;EACnC,IAAIC,UAAU,GAAGhB,MAAM,CAAC,IAAII,QAAQ,CAAC,CAAC,CAAC;EACvC,IAAIa,aAAa,GAAGjB,MAAM,CAAC,CAAC;EAC5B,SAASkB,SAASA,CAAA,EAAG;IACnBf,GAAG,CAACgB,MAAM,CAACF,aAAa,CAACG,OAAO,CAAC;EACnC;EACA,SAASC,aAAaA,CAAA,EAAG;IACvBH,SAAS,CAAC,CAAC;IACXD,aAAa,CAACG,OAAO,GAAGjB,GAAG,CAAC,YAAY;MACtCW,WAAW,CAACM,OAAO,CAACE,OAAO,CAAC,UAAUC,OAAO,EAAEC,GAAG,EAAE;QAClD,IAAID,OAAO,IAAIA,OAAO,CAACE,YAAY,EAAE;UACnC,IAAIC,WAAW,GAAGxB,WAAW,CAACqB,OAAO,CAAC;UACtC,IAAII,YAAY,GAAGD,WAAW,CAACC,YAAY;UAC3C,IAAIX,UAAU,CAACI,OAAO,CAACQ,GAAG,CAACJ,GAAG,CAAC,KAAKG,YAAY,EAAE;YAChDX,UAAU,CAACI,OAAO,CAACS,GAAG,CAACL,GAAG,EAAEE,WAAW,CAACC,YAAY,CAAC;UACvD;QACF;MACF,CAAC,CAAC;MACF;MACAd,cAAc,CAAC,UAAUiB,CAAC,EAAE;QAC1B,OAAOA,CAAC,GAAG,CAAC;MACd,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ;EACA,SAASC,cAAcA,CAACC,IAAI,EAAEC,QAAQ,EAAE;IACtC,IAAIT,GAAG,GAAGlB,MAAM,CAAC0B,IAAI,CAAC;IACtB,IAAIE,MAAM,GAAGpB,WAAW,CAACM,OAAO,CAACQ,GAAG,CAACJ,GAAG,CAAC;IACzC,IAAIS,QAAQ,EAAE;MACZnB,WAAW,CAACM,OAAO,CAACS,GAAG,CAACL,GAAG,EAAES,QAAQ,CAAC;MACtCZ,aAAa,CAAC,CAAC;IACjB,CAAC,MAAM;MACLP,WAAW,CAACM,OAAO,CAACe,MAAM,CAACX,GAAG,CAAC;IACjC;IACA;IACA,IAAI,CAACU,MAAM,KAAK,CAACD,QAAQ,EAAE;MACzB,IAAIA,QAAQ,EAAE;QACZ1B,SAAS,KAAK,IAAI,IAAIA,SAAS,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,SAAS,CAACyB,IAAI,CAAC;MACvE,CAAC,MAAM;QACLxB,YAAY,KAAK,IAAI,IAAIA,YAAY,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,YAAY,CAACwB,IAAI,CAAC;MAChF;IACF;EACF;EACA/B,SAAS,CAAC,YAAY;IACpB,OAAOiB,SAAS;EAClB,CAAC,EAAE,EAAE,CAAC;EACN,OAAO,CAACa,cAAc,EAAEV,aAAa,EAAEL,UAAU,CAACI,OAAO,EAAER,WAAW,CAAC;AACzE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}