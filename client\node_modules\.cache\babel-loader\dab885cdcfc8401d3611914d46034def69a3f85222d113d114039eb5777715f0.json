{"ast": null, "code": "import _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport { warning } from 'rc-util';\nimport { getShadowRoot } from \"rc-util/es/Dom/shadow\";\nimport raf from \"rc-util/es/raf\";\nimport * as React from 'react';\nimport { getWin } from \"../util\";\nexport default function useWinClick(open, clickToHide, targetEle, popupEle, mask, maskClosable, inPopupOrChild, triggerOpen) {\n  var openRef = React.useRef(open);\n\n  // Window click to hide should be lock to avoid trigger lock immediately\n  var lockRef = React.useRef(false);\n  if (openRef.current !== open) {\n    lockRef.current = true;\n    openRef.current = open;\n  }\n  React.useEffect(function () {\n    var id = raf(function () {\n      lockRef.current = false;\n    });\n    return function () {\n      raf.cancel(id);\n    };\n  }, [open]);\n\n  // Click to hide is special action since click popup element should not hide\n  React.useEffect(function () {\n    if (clickToHide && popupEle && (!mask || maskClosable)) {\n      var genClickEvents = function genClickEvents() {\n        var clickInside = false;\n\n        // User may mouseDown inside and drag out of popup and mouse up\n        // Record here to prevent close\n        var onWindowMouseDown = function onWindowMouseDown(_ref) {\n          var target = _ref.target;\n          clickInside = inPopupOrChild(target);\n        };\n        var onWindowClick = function onWindowClick(_ref2) {\n          var target = _ref2.target;\n          if (!lockRef.current && openRef.current && !clickInside && !inPopupOrChild(target)) {\n            triggerOpen(false);\n          }\n        };\n        return [onWindowMouseDown, onWindowClick];\n      };\n\n      // Events\n      var _genClickEvents = genClickEvents(),\n        _genClickEvents2 = _slicedToArray(_genClickEvents, 2),\n        onWinMouseDown = _genClickEvents2[0],\n        onWinClick = _genClickEvents2[1];\n      var _genClickEvents3 = genClickEvents(),\n        _genClickEvents4 = _slicedToArray(_genClickEvents3, 2),\n        onShadowMouseDown = _genClickEvents4[0],\n        onShadowClick = _genClickEvents4[1];\n      var win = getWin(popupEle);\n      win.addEventListener('mousedown', onWinMouseDown, true);\n      win.addEventListener('click', onWinClick, true);\n      win.addEventListener('contextmenu', onWinClick, true);\n\n      // shadow root\n      var targetShadowRoot = getShadowRoot(targetEle);\n      if (targetShadowRoot) {\n        targetShadowRoot.addEventListener('mousedown', onShadowMouseDown, true);\n        targetShadowRoot.addEventListener('click', onShadowClick, true);\n        targetShadowRoot.addEventListener('contextmenu', onShadowClick, true);\n      }\n\n      // Warning if target and popup not in same root\n      if (process.env.NODE_ENV !== 'production') {\n        var _targetEle$getRootNod, _popupEle$getRootNode;\n        var targetRoot = targetEle === null || targetEle === void 0 ? void 0 : (_targetEle$getRootNod = targetEle.getRootNode) === null || _targetEle$getRootNod === void 0 ? void 0 : _targetEle$getRootNod.call(targetEle);\n        var popupRoot = (_popupEle$getRootNode = popupEle.getRootNode) === null || _popupEle$getRootNode === void 0 ? void 0 : _popupEle$getRootNode.call(popupEle);\n        warning(targetRoot === popupRoot, \"trigger element and popup element should in same shadow root.\");\n      }\n      return function () {\n        win.removeEventListener('mousedown', onWinMouseDown, true);\n        win.removeEventListener('click', onWinClick, true);\n        win.removeEventListener('contextmenu', onWinClick, true);\n        if (targetShadowRoot) {\n          targetShadowRoot.removeEventListener('mousedown', onShadowMouseDown, true);\n          targetShadowRoot.removeEventListener('click', onShadowClick, true);\n          targetShadowRoot.removeEventListener('contextmenu', onShadowClick, true);\n        }\n      };\n    }\n  }, [clickToHide, targetEle, popupEle, mask, maskClosable]);\n}", "map": {"version": 3, "names": ["_slicedToArray", "warning", "getShadowRoot", "raf", "React", "getWin", "useWinClick", "open", "clickToHide", "targetEle", "popup<PERSON>le", "mask", "maskClosable", "inPopupOrChild", "triggerOpen", "openRef", "useRef", "lockRef", "current", "useEffect", "id", "cancel", "genClickEvents", "clickInside", "onWindowMouseDown", "_ref", "target", "onWindowClick", "_ref2", "_genClickEvents", "_genClickEvents2", "onWinMouseDown", "onWinClick", "_genClickEvents3", "_genClickEvents4", "onShadowMouseDown", "onShadowClick", "win", "addEventListener", "targetShadowRoot", "process", "env", "NODE_ENV", "_targetEle$getRootNod", "_popupEle$getRootNode", "targetRoot", "getRootNode", "call", "popupRoot", "removeEventListener"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/node_modules/@rc-component/trigger/es/hooks/useWinClick.js"], "sourcesContent": ["import _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport { warning } from 'rc-util';\nimport { getShadowRoot } from \"rc-util/es/Dom/shadow\";\nimport raf from \"rc-util/es/raf\";\nimport * as React from 'react';\nimport { getWin } from \"../util\";\nexport default function useWinClick(open, clickToHide, targetEle, popupEle, mask, maskClosable, inPopupOrChild, triggerOpen) {\n  var openRef = React.useRef(open);\n\n  // Window click to hide should be lock to avoid trigger lock immediately\n  var lockRef = React.useRef(false);\n  if (openRef.current !== open) {\n    lockRef.current = true;\n    openRef.current = open;\n  }\n  React.useEffect(function () {\n    var id = raf(function () {\n      lockRef.current = false;\n    });\n    return function () {\n      raf.cancel(id);\n    };\n  }, [open]);\n\n  // Click to hide is special action since click popup element should not hide\n  React.useEffect(function () {\n    if (clickToHide && popupEle && (!mask || maskClosable)) {\n      var genClickEvents = function genClickEvents() {\n        var clickInside = false;\n\n        // User may mouseDown inside and drag out of popup and mouse up\n        // Record here to prevent close\n        var onWindowMouseDown = function onWindowMouseDown(_ref) {\n          var target = _ref.target;\n          clickInside = inPopupOrChild(target);\n        };\n        var onWindowClick = function onWindowClick(_ref2) {\n          var target = _ref2.target;\n          if (!lockRef.current && openRef.current && !clickInside && !inPopupOrChild(target)) {\n            triggerOpen(false);\n          }\n        };\n        return [onWindowMouseDown, onWindowClick];\n      };\n\n      // Events\n      var _genClickEvents = genClickEvents(),\n        _genClickEvents2 = _slicedToArray(_genClickEvents, 2),\n        onWinMouseDown = _genClickEvents2[0],\n        onWinClick = _genClickEvents2[1];\n      var _genClickEvents3 = genClickEvents(),\n        _genClickEvents4 = _slicedToArray(_genClickEvents3, 2),\n        onShadowMouseDown = _genClickEvents4[0],\n        onShadowClick = _genClickEvents4[1];\n      var win = getWin(popupEle);\n      win.addEventListener('mousedown', onWinMouseDown, true);\n      win.addEventListener('click', onWinClick, true);\n      win.addEventListener('contextmenu', onWinClick, true);\n\n      // shadow root\n      var targetShadowRoot = getShadowRoot(targetEle);\n      if (targetShadowRoot) {\n        targetShadowRoot.addEventListener('mousedown', onShadowMouseDown, true);\n        targetShadowRoot.addEventListener('click', onShadowClick, true);\n        targetShadowRoot.addEventListener('contextmenu', onShadowClick, true);\n      }\n\n      // Warning if target and popup not in same root\n      if (process.env.NODE_ENV !== 'production') {\n        var _targetEle$getRootNod, _popupEle$getRootNode;\n        var targetRoot = targetEle === null || targetEle === void 0 ? void 0 : (_targetEle$getRootNod = targetEle.getRootNode) === null || _targetEle$getRootNod === void 0 ? void 0 : _targetEle$getRootNod.call(targetEle);\n        var popupRoot = (_popupEle$getRootNode = popupEle.getRootNode) === null || _popupEle$getRootNode === void 0 ? void 0 : _popupEle$getRootNode.call(popupEle);\n        warning(targetRoot === popupRoot, \"trigger element and popup element should in same shadow root.\");\n      }\n      return function () {\n        win.removeEventListener('mousedown', onWinMouseDown, true);\n        win.removeEventListener('click', onWinClick, true);\n        win.removeEventListener('contextmenu', onWinClick, true);\n        if (targetShadowRoot) {\n          targetShadowRoot.removeEventListener('mousedown', onShadowMouseDown, true);\n          targetShadowRoot.removeEventListener('click', onShadowClick, true);\n          targetShadowRoot.removeEventListener('contextmenu', onShadowClick, true);\n        }\n      };\n    }\n  }, [clickToHide, targetEle, popupEle, mask, maskClosable]);\n}"], "mappings": "AAAA,OAAOA,cAAc,MAAM,0CAA0C;AACrE,SAASC,OAAO,QAAQ,SAAS;AACjC,SAASC,aAAa,QAAQ,uBAAuB;AACrD,OAAOC,GAAG,MAAM,gBAAgB;AAChC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,MAAM,QAAQ,SAAS;AAChC,eAAe,SAASC,WAAWA,CAACC,IAAI,EAAEC,WAAW,EAAEC,SAAS,EAAEC,QAAQ,EAAEC,IAAI,EAAEC,YAAY,EAAEC,cAAc,EAAEC,WAAW,EAAE;EAC3H,IAAIC,OAAO,GAAGX,KAAK,CAACY,MAAM,CAACT,IAAI,CAAC;;EAEhC;EACA,IAAIU,OAAO,GAAGb,KAAK,CAACY,MAAM,CAAC,KAAK,CAAC;EACjC,IAAID,OAAO,CAACG,OAAO,KAAKX,IAAI,EAAE;IAC5BU,OAAO,CAACC,OAAO,GAAG,IAAI;IACtBH,OAAO,CAACG,OAAO,GAAGX,IAAI;EACxB;EACAH,KAAK,CAACe,SAAS,CAAC,YAAY;IAC1B,IAAIC,EAAE,GAAGjB,GAAG,CAAC,YAAY;MACvBc,OAAO,CAACC,OAAO,GAAG,KAAK;IACzB,CAAC,CAAC;IACF,OAAO,YAAY;MACjBf,GAAG,CAACkB,MAAM,CAACD,EAAE,CAAC;IAChB,CAAC;EACH,CAAC,EAAE,CAACb,IAAI,CAAC,CAAC;;EAEV;EACAH,KAAK,CAACe,SAAS,CAAC,YAAY;IAC1B,IAAIX,WAAW,IAAIE,QAAQ,KAAK,CAACC,IAAI,IAAIC,YAAY,CAAC,EAAE;MACtD,IAAIU,cAAc,GAAG,SAASA,cAAcA,CAAA,EAAG;QAC7C,IAAIC,WAAW,GAAG,KAAK;;QAEvB;QACA;QACA,IAAIC,iBAAiB,GAAG,SAASA,iBAAiBA,CAACC,IAAI,EAAE;UACvD,IAAIC,MAAM,GAAGD,IAAI,CAACC,MAAM;UACxBH,WAAW,GAAGV,cAAc,CAACa,MAAM,CAAC;QACtC,CAAC;QACD,IAAIC,aAAa,GAAG,SAASA,aAAaA,CAACC,KAAK,EAAE;UAChD,IAAIF,MAAM,GAAGE,KAAK,CAACF,MAAM;UACzB,IAAI,CAACT,OAAO,CAACC,OAAO,IAAIH,OAAO,CAACG,OAAO,IAAI,CAACK,WAAW,IAAI,CAACV,cAAc,CAACa,MAAM,CAAC,EAAE;YAClFZ,WAAW,CAAC,KAAK,CAAC;UACpB;QACF,CAAC;QACD,OAAO,CAACU,iBAAiB,EAAEG,aAAa,CAAC;MAC3C,CAAC;;MAED;MACA,IAAIE,eAAe,GAAGP,cAAc,CAAC,CAAC;QACpCQ,gBAAgB,GAAG9B,cAAc,CAAC6B,eAAe,EAAE,CAAC,CAAC;QACrDE,cAAc,GAAGD,gBAAgB,CAAC,CAAC,CAAC;QACpCE,UAAU,GAAGF,gBAAgB,CAAC,CAAC,CAAC;MAClC,IAAIG,gBAAgB,GAAGX,cAAc,CAAC,CAAC;QACrCY,gBAAgB,GAAGlC,cAAc,CAACiC,gBAAgB,EAAE,CAAC,CAAC;QACtDE,iBAAiB,GAAGD,gBAAgB,CAAC,CAAC,CAAC;QACvCE,aAAa,GAAGF,gBAAgB,CAAC,CAAC,CAAC;MACrC,IAAIG,GAAG,GAAGhC,MAAM,CAACK,QAAQ,CAAC;MAC1B2B,GAAG,CAACC,gBAAgB,CAAC,WAAW,EAAEP,cAAc,EAAE,IAAI,CAAC;MACvDM,GAAG,CAACC,gBAAgB,CAAC,OAAO,EAAEN,UAAU,EAAE,IAAI,CAAC;MAC/CK,GAAG,CAACC,gBAAgB,CAAC,aAAa,EAAEN,UAAU,EAAE,IAAI,CAAC;;MAErD;MACA,IAAIO,gBAAgB,GAAGrC,aAAa,CAACO,SAAS,CAAC;MAC/C,IAAI8B,gBAAgB,EAAE;QACpBA,gBAAgB,CAACD,gBAAgB,CAAC,WAAW,EAAEH,iBAAiB,EAAE,IAAI,CAAC;QACvEI,gBAAgB,CAACD,gBAAgB,CAAC,OAAO,EAAEF,aAAa,EAAE,IAAI,CAAC;QAC/DG,gBAAgB,CAACD,gBAAgB,CAAC,aAAa,EAAEF,aAAa,EAAE,IAAI,CAAC;MACvE;;MAEA;MACA,IAAII,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;QACzC,IAAIC,qBAAqB,EAAEC,qBAAqB;QAChD,IAAIC,UAAU,GAAGpC,SAAS,KAAK,IAAI,IAAIA,SAAS,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAG,CAACkC,qBAAqB,GAAGlC,SAAS,CAACqC,WAAW,MAAM,IAAI,IAAIH,qBAAqB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,qBAAqB,CAACI,IAAI,CAACtC,SAAS,CAAC;QACpN,IAAIuC,SAAS,GAAG,CAACJ,qBAAqB,GAAGlC,QAAQ,CAACoC,WAAW,MAAM,IAAI,IAAIF,qBAAqB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,qBAAqB,CAACG,IAAI,CAACrC,QAAQ,CAAC;QAC3JT,OAAO,CAAC4C,UAAU,KAAKG,SAAS,EAAE,+DAA+D,CAAC;MACpG;MACA,OAAO,YAAY;QACjBX,GAAG,CAACY,mBAAmB,CAAC,WAAW,EAAElB,cAAc,EAAE,IAAI,CAAC;QAC1DM,GAAG,CAACY,mBAAmB,CAAC,OAAO,EAAEjB,UAAU,EAAE,IAAI,CAAC;QAClDK,GAAG,CAACY,mBAAmB,CAAC,aAAa,EAAEjB,UAAU,EAAE,IAAI,CAAC;QACxD,IAAIO,gBAAgB,EAAE;UACpBA,gBAAgB,CAACU,mBAAmB,CAAC,WAAW,EAAEd,iBAAiB,EAAE,IAAI,CAAC;UAC1EI,gBAAgB,CAACU,mBAAmB,CAAC,OAAO,EAAEb,aAAa,EAAE,IAAI,CAAC;UAClEG,gBAAgB,CAACU,mBAAmB,CAAC,aAAa,EAAEb,aAAa,EAAE,IAAI,CAAC;QAC1E;MACF,CAAC;IACH;EACF,CAAC,EAAE,CAAC5B,WAAW,EAAEC,SAAS,EAAEC,QAAQ,EAAEC,IAAI,EAAEC,YAAY,CAAC,CAAC;AAC5D", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}