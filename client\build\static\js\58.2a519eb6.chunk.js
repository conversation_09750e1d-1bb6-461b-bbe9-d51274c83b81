"use strict";(self.webpackChunkclient=self.webpackChunkclient||[]).push([[58],{5058:(t,e,n)=>{n.d(e,{default:()=>Lt});var a=n(732),o=n(5033),c=n(7462),r=n(2791);const i={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"defs",attrs:{},children:[{tag:"style",attrs:{}}]},{tag:"path",attrs:{d:"M482 152h60q8 0 8 8v704q0 8-8 8h-60q-8 0-8-8V160q0-8 8-8z"}},{tag:"path",attrs:{d:"M176 474h672q8 0 8 8v60q0 8-8 8H176q-8 0-8-8v-60q0-8 8-8z"}}]},name:"plus",theme:"outlined"};var l=n(4291),d=function(t,e){return r.createElement(l.Z,(0,c.Z)({},t,{ref:e,icon:i}))};const s=r.forwardRef(d);var u=n(1694),p=n.n(u),v=n(4942),f=n(1413),b=n(9439),m=n(1002),h=n(4925),g=n(3786),k=n(5179),x=n(8568);const y=(0,r.createContext)(null);var _=r.forwardRef((function(t,e){var n=t.prefixCls,a=t.className,o=t.style,c=t.id,i=t.active,l=t.tabKey,d=t.children;return r.createElement("div",{id:c&&"".concat(c,"-panel-").concat(l),role:"tabpanel",tabIndex:i?0:-1,"aria-labelledby":c&&"".concat(c,"-tab-").concat(l),"aria-hidden":!i,style:o,className:p()(n,i&&"".concat(n,"-active"),a),ref:e},d)}));const w=_;var S=["key","forceRender","style","className"];function C(t){var e=t.id,n=t.activeKey,a=t.animated,o=t.tabPosition,i=t.destroyInactiveTabPane,l=r.useContext(y),d=l.prefixCls,s=l.tabs,u=a.tabPane,b="".concat(d,"-tabpane");return r.createElement("div",{className:p()("".concat(d,"-content-holder"))},r.createElement("div",{className:p()("".concat(d,"-content"),"".concat(d,"-content-").concat(o),(0,v.Z)({},"".concat(d,"-content-animated"),u))},s.map((function(t){var o=t.key,l=t.forceRender,d=t.style,s=t.className,v=(0,h.Z)(t,S),m=o===n;return r.createElement(x.ZP,(0,c.Z)({key:o,visible:m,forceRender:l,removeOnLeave:!!i,leavedClassName:"".concat(b,"-hidden")},a.tabPaneMotion),(function(t,n){var a=t.style,i=t.className;return r.createElement(w,(0,c.Z)({},v,{prefixCls:b,id:e,tabKey:o,animated:u,active:m,style:(0,f.Z)((0,f.Z)({},d),a),className:p()(s,i),ref:n}))}))}))))}var E=n(3433),Z=n(1143),P=n(3739),R=n(5314),T=n(8834),I={width:0,height:0,left:0,top:0};function L(t,e){var n=r.useRef(t),a=r.useState({}),o=(0,b.Z)(a,2)[1];return[n.current,function(t){var a="function"===typeof t?t(n.current):t;a!==n.current&&e(a,n.current),n.current=a,o({})}]}var N=.1,M=.01,B=20,z=Math.pow(.995,B);var O=n(1605);function D(t){var e=(0,r.useState)(0),n=(0,b.Z)(e,2),a=n[0],o=n[1],c=(0,r.useRef)(0),i=(0,r.useRef)();return i.current=t,(0,O.o)((function(){var t;null===(t=i.current)||void 0===t||t.call(i)}),[a]),function(){c.current===a&&(c.current+=1,o(c.current))}}var j={width:0,height:0,left:0,top:0,right:0};function G(t){var e;return t instanceof Map?(e={},t.forEach((function(t,n){e[n]=t}))):e=t,JSON.stringify(e)}var A="TABS_DQ";function H(t){return String(t).replace(/"/g,A)}function W(t,e,n,a){return!(!n||a||!1===t||void 0===t&&(!1===e||null===e))}function X(t,e){var n=t.prefixCls,a=t.editable,o=t.locale,c=t.style;return a&&!1!==a.showAdd?r.createElement("button",{ref:e,type:"button",className:"".concat(n,"-nav-add"),style:c,"aria-label":(null===o||void 0===o?void 0:o.addAriaLabel)||"Add tab",onClick:function(t){a.onEdit("add",{event:t})}},a.addIcon||"+"):null}const K=r.forwardRef(X);const F=r.forwardRef((function(t,e){var n,a=t.position,o=t.prefixCls,c=t.extra;if(!c)return null;var i={};return"object"!==(0,m.Z)(c)||r.isValidElement(c)?i.right=c:i=c,"right"===a&&(n=i.right),"left"===a&&(n=i.left),n?r.createElement("div",{className:"".concat(o,"-extra-content"),ref:e},n):null}));var q=n(353),V=n(2894),Y=n(1354);function Q(t,e){var n=t.prefixCls,a=t.id,o=t.tabs,c=t.locale,i=t.mobile,l=t.moreIcon,d=void 0===l?"More":l,s=t.moreTransitionName,u=t.style,f=t.className,m=t.editable,h=t.tabBarGutter,g=t.rtl,k=t.removeAriaLabel,x=t.onTabClick,y=t.getPopupContainer,_=t.popupClassName,w=(0,r.useState)(!1),S=(0,b.Z)(w,2),C=S[0],E=S[1],Z=(0,r.useState)(null),P=(0,b.Z)(Z,2),R=P[0],T=P[1],I="".concat(a,"-more-popup"),L="".concat(n,"-dropdown"),N=null!==R?"".concat(I,"-").concat(R):null,M=null===c||void 0===c?void 0:c.dropdownAriaLabel;var B=r.createElement(V.ZP,{onClick:function(t){var e=t.key,n=t.domEvent;x(e,n),E(!1)},prefixCls:"".concat(L,"-menu"),id:I,tabIndex:-1,role:"listbox","aria-activedescendant":N,selectedKeys:[R],"aria-label":void 0!==M?M:"expanded dropdown"},o.map((function(t){var e=t.closable,n=t.disabled,o=t.closeIcon,c=t.key,i=t.label,l=W(e,o,m,n);return r.createElement(V.sN,{key:c,id:"".concat(I,"-").concat(c),role:"option","aria-controls":a&&"".concat(a,"-panel-").concat(c),disabled:n},r.createElement("span",null,i),l&&r.createElement("button",{type:"button","aria-label":k||"remove",tabIndex:0,className:"".concat(L,"-menu-item-remove"),onClick:function(t){t.stopPropagation(),function(t,e){t.preventDefault(),t.stopPropagation(),m.onEdit("remove",{key:e,event:t})}(t,c)}},o||m.removeIcon||"\xd7"))})));function z(t){for(var e=o.filter((function(t){return!t.disabled})),n=e.findIndex((function(t){return t.key===R}))||0,a=e.length,c=0;c<a;c+=1){var r=e[n=(n+t+a)%a];if(!r.disabled)return void T(r.key)}}(0,r.useEffect)((function(){var t=document.getElementById(N);t&&t.scrollIntoView&&t.scrollIntoView(!1)}),[R]),(0,r.useEffect)((function(){C||T(null)}),[C]);var O=(0,v.Z)({},g?"marginRight":"marginLeft",h);o.length||(O.visibility="hidden",O.order=1);var D=p()((0,v.Z)({},"".concat(L,"-rtl"),g)),j=i?null:r.createElement(q.Z,{prefixCls:L,overlay:B,trigger:["hover"],visible:!!o.length&&C,transitionName:s,onVisibleChange:E,overlayClassName:p()(D,_),mouseEnterDelay:.1,mouseLeaveDelay:.1,getPopupContainer:y},r.createElement("button",{type:"button",className:"".concat(n,"-nav-more"),style:O,tabIndex:-1,"aria-hidden":"true","aria-haspopup":"listbox","aria-controls":I,id:"".concat(a,"-more"),"aria-expanded":C,onKeyDown:function(t){var e=t.which;if(C)switch(e){case Y.Z.UP:z(-1),t.preventDefault();break;case Y.Z.DOWN:z(1),t.preventDefault();break;case Y.Z.ESC:E(!1);break;case Y.Z.SPACE:case Y.Z.ENTER:null!==R&&x(R,t)}else[Y.Z.DOWN,Y.Z.SPACE,Y.Z.ENTER].includes(e)&&(E(!0),t.preventDefault())}},d));return r.createElement("div",{className:p()("".concat(n,"-nav-operations"),f),style:u,ref:e},j,r.createElement(K,{prefixCls:n,locale:c,editable:m}))}const J=r.memo(r.forwardRef(Q),(function(t,e){return e.tabMoving}));const U=function(t){var e,n=t.prefixCls,a=t.id,o=t.active,c=t.tab,i=c.key,l=c.label,d=c.disabled,s=c.closeIcon,u=t.closable,f=t.renderWrapper,b=t.removeAriaLabel,m=t.editable,h=t.onClick,g=t.onFocus,k=t.style,x="".concat(n,"-tab"),y=W(u,s,m,d);function _(t){d||h(t)}var w=r.createElement("div",{key:i,"data-node-key":H(i),className:p()(x,(e={},(0,v.Z)(e,"".concat(x,"-with-remove"),y),(0,v.Z)(e,"".concat(x,"-active"),o),(0,v.Z)(e,"".concat(x,"-disabled"),d),e)),style:k,onClick:_},r.createElement("div",{role:"tab","aria-selected":o,id:a&&"".concat(a,"-tab-").concat(i),className:"".concat(x,"-btn"),"aria-controls":a&&"".concat(a,"-panel-").concat(i),"aria-disabled":d,tabIndex:d?null:0,onClick:function(t){t.stopPropagation(),_(t)},onKeyDown:function(t){[Y.Z.SPACE,Y.Z.ENTER].includes(t.which)&&(t.preventDefault(),_(t))},onFocus:g},l),y&&r.createElement("button",{type:"button","aria-label":b||"remove",tabIndex:0,className:"".concat(x,"-remove"),onClick:function(t){var e;t.stopPropagation(),(e=t).preventDefault(),e.stopPropagation(),m.onEdit("remove",{key:i,event:e})}},s||m.removeIcon||"\xd7"));return f?f(w):w};var $=function(t){var e=t.current||{},n=e.offsetWidth,a=void 0===n?0:n,o=e.offsetHeight;return[a,void 0===o?0:o]},tt=function(t,e){return t[e?0:1]};function et(t,e){var n,a=r.useContext(y),o=a.prefixCls,i=a.tabs,l=t.className,d=t.style,s=t.id,u=t.animated,m=t.activeKey,h=t.rtl,g=t.extra,k=t.editable,x=t.locale,_=t.tabPosition,w=t.tabBarGutter,S=t.children,C=t.onTabClick,O=t.onTabScroll,A=(0,r.useRef)(),W=(0,r.useRef)(),X=(0,r.useRef)(),q=(0,r.useRef)(),V=(0,r.useRef)(),Y=(0,r.useRef)(),Q=(0,r.useRef)(),et="top"===_||"bottom"===_,nt=L(0,(function(t,e){et&&O&&O({direction:t>e?"left":"right"})})),at=(0,b.Z)(nt,2),ot=at[0],ct=at[1],rt=L(0,(function(t,e){!et&&O&&O({direction:t>e?"top":"bottom"})})),it=(0,b.Z)(rt,2),lt=it[0],dt=it[1],st=(0,r.useState)([0,0]),ut=(0,b.Z)(st,2),pt=ut[0],vt=ut[1],ft=(0,r.useState)([0,0]),bt=(0,b.Z)(ft,2),mt=bt[0],ht=bt[1],gt=(0,r.useState)([0,0]),kt=(0,b.Z)(gt,2),xt=kt[0],yt=kt[1],_t=(0,r.useState)([0,0]),wt=(0,b.Z)(_t,2),St=wt[0],Ct=wt[1],Et=function(t){var e=(0,r.useRef)([]),n=(0,r.useState)({}),a=(0,b.Z)(n,2)[1],o=(0,r.useRef)("function"===typeof t?t():t),c=D((function(){var t=o.current;e.current.forEach((function(e){t=e(t)})),e.current=[],o.current=t,a({})}));return[o.current,function(t){e.current.push(t),c()}]}(new Map),Zt=(0,b.Z)(Et,2),Pt=Zt[0],Rt=Zt[1],Tt=function(t,e,n){return(0,r.useMemo)((function(){for(var n,a=new Map,o=e.get(null===(n=t[0])||void 0===n?void 0:n.key)||I,c=o.left+o.width,r=0;r<t.length;r+=1){var i,l=t[r].key,d=e.get(l);d||(d=e.get(null===(i=t[r-1])||void 0===i?void 0:i.key)||I);var s=a.get(l)||(0,f.Z)({},d);s.right=c-s.left-s.width,a.set(l,s)}return a}),[t.map((function(t){return t.key})).join("_"),e,n])}(i,Pt,mt[0]),It=tt(pt,et),Lt=tt(mt,et),Nt=tt(xt,et),Mt=tt(St,et),Bt=It<Lt+Nt,zt=Bt?It-Mt:It-Nt,Ot="".concat(o,"-nav-operations-hidden"),Dt=0,jt=0;function Gt(t){return t<Dt?Dt:t>jt?jt:t}et&&h?(Dt=0,jt=Math.max(0,Lt-zt)):(Dt=Math.min(0,zt-Lt),jt=0);var At=(0,r.useRef)(),Ht=(0,r.useState)(),Wt=(0,b.Z)(Ht,2),Xt=Wt[0],Kt=Wt[1];function Ft(){Kt(Date.now())}function qt(){window.clearTimeout(At.current)}!function(t,e){var n=(0,r.useState)(),a=(0,b.Z)(n,2),o=a[0],c=a[1],i=(0,r.useState)(0),l=(0,b.Z)(i,2),d=l[0],s=l[1],u=(0,r.useState)(0),p=(0,b.Z)(u,2),v=p[0],f=p[1],m=(0,r.useState)(),h=(0,b.Z)(m,2),g=h[0],k=h[1],x=(0,r.useRef)(),y=(0,r.useRef)(),_=(0,r.useRef)(null);_.current={onTouchStart:function(t){var e=t.touches[0],n=e.screenX,a=e.screenY;c({x:n,y:a}),window.clearInterval(x.current)},onTouchMove:function(t){if(o){t.preventDefault();var n=t.touches[0],a=n.screenX,r=n.screenY;c({x:a,y:r});var i=a-o.x,l=r-o.y;e(i,l);var u=Date.now();s(u),f(u-d),k({x:i,y:l})}},onTouchEnd:function(){if(o&&(c(null),k(null),g)){var t=g.x/v,n=g.y/v,a=Math.abs(t),r=Math.abs(n);if(Math.max(a,r)<N)return;var i=t,l=n;x.current=window.setInterval((function(){Math.abs(i)<M&&Math.abs(l)<M?window.clearInterval(x.current):e((i*=z)*B,(l*=z)*B)}),B)}},onWheel:function(t){var n=t.deltaX,a=t.deltaY,o=0,c=Math.abs(n),r=Math.abs(a);c===r?o="x"===y.current?n:a:c>r?(o=n,y.current="x"):(o=a,y.current="y"),e(-o,-o)&&t.preventDefault()}},r.useEffect((function(){function e(t){_.current.onTouchMove(t)}function n(t){_.current.onTouchEnd(t)}return document.addEventListener("touchmove",e,{passive:!1}),document.addEventListener("touchend",n,{passive:!1}),t.current.addEventListener("touchstart",(function(t){_.current.onTouchStart(t)}),{passive:!1}),t.current.addEventListener("wheel",(function(t){_.current.onWheel(t)})),function(){document.removeEventListener("touchmove",e),document.removeEventListener("touchend",n)}}),[])}(q,(function(t,e){function n(t,e){t((function(t){return Gt(t+e)}))}return!!Bt&&(et?n(ct,t):n(dt,e),qt(),Ft(),!0)})),(0,r.useEffect)((function(){return qt(),Xt&&(At.current=window.setTimeout((function(){Kt(0)}),100)),qt}),[Xt]);var Vt=function(t,e,n,a,o,c,i){var l,d,s,u=i.tabs,p=i.tabPosition,v=i.rtl;return["top","bottom"].includes(p)?(l="width",d=v?"right":"left",s=Math.abs(n)):(l="height",d="top",s=-n),(0,r.useMemo)((function(){if(!u.length)return[0,0];for(var n=u.length,a=n,o=0;o<n;o+=1){var c=t.get(u[o].key)||j;if(c[d]+c[l]>s+e){a=o-1;break}}for(var r=0,i=n-1;i>=0;i-=1)if((t.get(u[i].key)||j)[d]<s){r=i+1;break}return r>=a?[0,0]:[r,a]}),[t,e,a,o,c,s,p,u.map((function(t){return t.key})).join("_"),v])}(Tt,zt,et?ot:lt,Lt,Nt,Mt,(0,f.Z)((0,f.Z)({},t),{},{tabs:i})),Yt=(0,b.Z)(Vt,2),Qt=Yt[0],Jt=Yt[1],Ut=(0,P.Z)((function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:m,e=Tt.get(t)||{width:0,height:0,left:0,right:0,top:0};if(et){var n=ot;h?e.right<ot?n=e.right:e.right+e.width>ot+zt&&(n=e.right+e.width-zt):e.left<-ot?n=-e.left:e.left+e.width>-ot+zt&&(n=-(e.left+e.width-zt)),dt(0),ct(Gt(n))}else{var a=lt;e.top<-lt?a=-e.top:e.top+e.height>-lt+zt&&(a=-(e.top+e.height-zt)),ct(0),dt(Gt(a))}})),$t={};"top"===_||"bottom"===_?$t[h?"marginRight":"marginLeft"]=w:$t.marginTop=w;var te=i.map((function(t,e){var n=t.key;return r.createElement(U,{id:s,prefixCls:o,key:n,tab:t,style:0===e?void 0:$t,closable:t.closable,editable:k,active:n===m,renderWrapper:S,removeAriaLabel:null===x||void 0===x?void 0:x.removeAriaLabel,onClick:function(t){C(n,t)},onFocus:function(){Ut(n),Ft(),q.current&&(h||(q.current.scrollLeft=0),q.current.scrollTop=0)}})})),ee=function(){return Rt((function(){var t=new Map;return i.forEach((function(e){var n,a=e.key,o=null===(n=V.current)||void 0===n?void 0:n.querySelector('[data-node-key="'.concat(H(a),'"]'));o&&t.set(a,{width:o.offsetWidth,height:o.offsetHeight,left:o.offsetLeft,top:o.offsetTop})})),t}))};(0,r.useEffect)((function(){ee()}),[i.map((function(t){return t.key})).join("_")]);var ne=D((function(){var t=$(A),e=$(W),n=$(X);vt([t[0]-e[0]-n[0],t[1]-e[1]-n[1]]);var a=$(Q);yt(a);var o=$(Y);Ct(o);var c=$(V);ht([c[0]-a[0],c[1]-a[1]]),ee()})),ae=i.slice(0,Qt),oe=i.slice(Jt+1),ce=[].concat((0,E.Z)(ae),(0,E.Z)(oe)),re=(0,r.useState)(),ie=(0,b.Z)(re,2),le=ie[0],de=ie[1],se=Tt.get(m),ue=(0,r.useRef)();function pe(){R.Z.cancel(ue.current)}(0,r.useEffect)((function(){var t={};return se&&(et?(h?t.right=se.right:t.left=se.left,t.width=se.width):(t.top=se.top,t.height=se.height)),pe(),ue.current=(0,R.Z)((function(){de(t)})),pe}),[se,et,h]),(0,r.useEffect)((function(){Ut()}),[m,Dt,jt,G(se),G(Tt),et]),(0,r.useEffect)((function(){ne()}),[h]);var ve,fe,be,me,he=!!ce.length,ge="".concat(o,"-nav-wrap");return et?h?(fe=ot>0,ve=ot!==jt):(ve=ot<0,fe=ot!==Dt):(be=lt<0,me=lt!==Dt),r.createElement(Z.Z,{onResize:ne},r.createElement("div",{ref:(0,T.x1)(e,A),role:"tablist",className:p()("".concat(o,"-nav"),l),style:d,onKeyDown:function(){Ft()}},r.createElement(F,{ref:W,position:"left",extra:g,prefixCls:o}),r.createElement("div",{className:p()(ge,(n={},(0,v.Z)(n,"".concat(ge,"-ping-left"),ve),(0,v.Z)(n,"".concat(ge,"-ping-right"),fe),(0,v.Z)(n,"".concat(ge,"-ping-top"),be),(0,v.Z)(n,"".concat(ge,"-ping-bottom"),me),n)),ref:q},r.createElement(Z.Z,{onResize:ne},r.createElement("div",{ref:V,className:"".concat(o,"-nav-list"),style:{transform:"translate(".concat(ot,"px, ").concat(lt,"px)"),transition:Xt?"none":void 0}},te,r.createElement(K,{ref:Q,prefixCls:o,locale:x,editable:k,style:(0,f.Z)((0,f.Z)({},0===te.length?void 0:$t),{},{visibility:he?"hidden":null})}),r.createElement("div",{className:p()("".concat(o,"-ink-bar"),(0,v.Z)({},"".concat(o,"-ink-bar-animated"),u.inkBar)),style:le})))),r.createElement(J,(0,c.Z)({},t,{removeAriaLabel:null===x||void 0===x?void 0:x.removeAriaLabel,ref:Y,prefixCls:o,tabs:ce,className:!he&&Ot,tabMoving:!!Xt})),r.createElement(F,{ref:X,position:"right",extra:g,prefixCls:o})))}const nt=r.forwardRef(et);var at=["renderTabBar"],ot=["label","key"];function ct(t){var e=t.renderTabBar,n=(0,h.Z)(t,at),a=r.useContext(y).tabs;return e?e((0,f.Z)((0,f.Z)({},n),{},{panes:a.map((function(t){var e=t.label,n=t.key,a=(0,h.Z)(t,ot);return r.createElement(w,(0,c.Z)({tab:e,key:n,tabKey:n},a))}))}),nt):r.createElement(nt,n)}n(632);var rt=["id","prefixCls","className","items","direction","activeKey","defaultActiveKey","editable","animated","tabPosition","tabBarGutter","tabBarStyle","tabBarExtraContent","locale","moreIcon","moreTransitionName","destroyInactiveTabPane","renderTabBar","onChange","onTabClick","onTabScroll","getPopupContainer","popupClassName"],it=0;function lt(t,e){var n,a=t.id,o=t.prefixCls,i=void 0===o?"rc-tabs":o,l=t.className,d=t.items,s=t.direction,u=t.activeKey,x=t.defaultActiveKey,_=t.editable,w=t.animated,S=t.tabPosition,E=void 0===S?"top":S,Z=t.tabBarGutter,P=t.tabBarStyle,R=t.tabBarExtraContent,T=t.locale,I=t.moreIcon,L=t.moreTransitionName,N=t.destroyInactiveTabPane,M=t.renderTabBar,B=t.onChange,z=t.onTabClick,O=t.onTabScroll,D=t.getPopupContainer,j=t.popupClassName,G=(0,h.Z)(t,rt),A=r.useMemo((function(){return(d||[]).filter((function(t){return t&&"object"===(0,m.Z)(t)&&"key"in t}))}),[d]),H="rtl"===s,W=function(){var t,e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{inkBar:!0,tabPane:!1};return(t=!1===e?{inkBar:!1,tabPane:!1}:!0===e?{inkBar:!0,tabPane:!1}:(0,f.Z)({inkBar:!0},"object"===(0,m.Z)(e)?e:{})).tabPaneMotion&&void 0===t.tabPane&&(t.tabPane=!0),!t.tabPaneMotion&&t.tabPane&&(t.tabPane=!1),t}(w),X=(0,r.useState)(!1),K=(0,b.Z)(X,2),F=K[0],q=K[1];(0,r.useEffect)((function(){q((0,g.Z)())}),[]);var V=(0,k.Z)((function(){var t;return null===(t=A[0])||void 0===t?void 0:t.key}),{value:u,defaultValue:x}),Y=(0,b.Z)(V,2),Q=Y[0],J=Y[1],U=(0,r.useState)((function(){return A.findIndex((function(t){return t.key===Q}))})),$=(0,b.Z)(U,2),tt=$[0],et=$[1];(0,r.useEffect)((function(){var t,e=A.findIndex((function(t){return t.key===Q}));-1===e&&(e=Math.max(0,Math.min(tt,A.length-1)),J(null===(t=A[e])||void 0===t?void 0:t.key));et(e)}),[A.map((function(t){return t.key})).join("_"),Q,tt]);var nt=(0,k.Z)(null,{value:a}),at=(0,b.Z)(nt,2),ot=at[0],lt=at[1];(0,r.useEffect)((function(){a||(lt("rc-tabs-".concat(it)),it+=1)}),[]);var dt={id:ot,activeKey:Q,animated:W,tabPosition:E,rtl:H,mobile:F},st=(0,f.Z)((0,f.Z)({},dt),{},{editable:_,locale:T,moreIcon:I,moreTransitionName:L,tabBarGutter:Z,onTabClick:function(t,e){null===z||void 0===z||z(t,e);var n=t!==Q;J(t),n&&(null===B||void 0===B||B(t))},onTabScroll:O,extra:R,style:P,panes:null,getPopupContainer:D,popupClassName:j});return r.createElement(y.Provider,{value:{tabs:A,prefixCls:i}},r.createElement("div",(0,c.Z)({ref:e,id:a,className:p()(i,"".concat(i,"-").concat(E),(n={},(0,v.Z)(n,"".concat(i,"-mobile"),F),(0,v.Z)(n,"".concat(i,"-editable"),_),(0,v.Z)(n,"".concat(i,"-rtl"),H),n),l)},G),undefined,r.createElement(ct,(0,c.Z)({},st,{renderTabBar:M})),r.createElement(C,(0,c.Z)({destroyInactiveTabPane:N},dt,{animated:W}))))}const dt=r.forwardRef(lt);var st=n(1929),ut=n(4107);const pt=()=>null;var vt=n(9464);const ft={motionAppear:!1,motionEnter:!0,motionLeave:!0};var bt=n(5501),mt=function(t,e){var n={};for(var a in t)Object.prototype.hasOwnProperty.call(t,a)&&e.indexOf(a)<0&&(n[a]=t[a]);if(null!=t&&"function"===typeof Object.getOwnPropertySymbols){var o=0;for(a=Object.getOwnPropertySymbols(t);o<a.length;o++)e.indexOf(a[o])<0&&Object.prototype.propertyIsEnumerable.call(t,a[o])&&(n[a[o]]=t[a[o]])}return n};var ht=n(7521),gt=n(5564),kt=n(9922),xt=n(5541);const yt=t=>{const{componentCls:e,motionDurationSlow:n}=t;return[{[e]:{["".concat(e,"-switch")]:{"&-appear, &-enter":{transition:"none","&-start":{opacity:0},"&-active":{opacity:1,transition:"opacity ".concat(n)}},"&-leave":{position:"absolute",transition:"none",inset:0,"&-start":{opacity:1},"&-active":{opacity:0,transition:"opacity ".concat(n)}}}}},[(0,xt.oN)(t,"slide-up"),(0,xt.oN)(t,"slide-down")]]},_t=t=>{const{componentCls:e,tabsCardPadding:n,cardBg:a,cardGutter:o,colorBorderSecondary:c,itemSelectedColor:r}=t;return{["".concat(e,"-card")]:{["> ".concat(e,"-nav, > div > ").concat(e,"-nav")]:{["".concat(e,"-tab")]:{margin:0,padding:n,background:a,border:"".concat(t.lineWidth,"px ").concat(t.lineType," ").concat(c),transition:"all ".concat(t.motionDurationSlow," ").concat(t.motionEaseInOut)},["".concat(e,"-tab-active")]:{color:r,background:t.colorBgContainer},["".concat(e,"-ink-bar")]:{visibility:"hidden"}},["&".concat(e,"-top, &").concat(e,"-bottom")]:{["> ".concat(e,"-nav, > div > ").concat(e,"-nav")]:{["".concat(e,"-tab + ").concat(e,"-tab")]:{marginLeft:{_skip_check_:!0,value:"".concat(o,"px")}}}},["&".concat(e,"-top")]:{["> ".concat(e,"-nav, > div > ").concat(e,"-nav")]:{["".concat(e,"-tab")]:{borderRadius:"".concat(t.borderRadiusLG,"px ").concat(t.borderRadiusLG,"px 0 0")},["".concat(e,"-tab-active")]:{borderBottomColor:t.colorBgContainer}}},["&".concat(e,"-bottom")]:{["> ".concat(e,"-nav, > div > ").concat(e,"-nav")]:{["".concat(e,"-tab")]:{borderRadius:"0 0 ".concat(t.borderRadiusLG,"px ").concat(t.borderRadiusLG,"px")},["".concat(e,"-tab-active")]:{borderTopColor:t.colorBgContainer}}},["&".concat(e,"-left, &").concat(e,"-right")]:{["> ".concat(e,"-nav, > div > ").concat(e,"-nav")]:{["".concat(e,"-tab + ").concat(e,"-tab")]:{marginTop:"".concat(o,"px")}}},["&".concat(e,"-left")]:{["> ".concat(e,"-nav, > div > ").concat(e,"-nav")]:{["".concat(e,"-tab")]:{borderRadius:{_skip_check_:!0,value:"".concat(t.borderRadiusLG,"px 0 0 ").concat(t.borderRadiusLG,"px")}},["".concat(e,"-tab-active")]:{borderRightColor:{_skip_check_:!0,value:t.colorBgContainer}}}},["&".concat(e,"-right")]:{["> ".concat(e,"-nav, > div > ").concat(e,"-nav")]:{["".concat(e,"-tab")]:{borderRadius:{_skip_check_:!0,value:"0 ".concat(t.borderRadiusLG,"px ").concat(t.borderRadiusLG,"px 0")}},["".concat(e,"-tab-active")]:{borderLeftColor:{_skip_check_:!0,value:t.colorBgContainer}}}}}}},wt=t=>{const{componentCls:e,itemHoverColor:n,dropdownEdgeChildVerticalPadding:a}=t;return{["".concat(e,"-dropdown")]:Object.assign(Object.assign({},(0,ht.Wf)(t)),{position:"absolute",top:-9999,left:{_skip_check_:!0,value:-9999},zIndex:t.zIndexPopup,display:"block","&-hidden":{display:"none"},["".concat(e,"-dropdown-menu")]:{maxHeight:t.tabsDropdownHeight,margin:0,padding:"".concat(a,"px 0"),overflowX:"hidden",overflowY:"auto",textAlign:{_skip_check_:!0,value:"left"},listStyleType:"none",backgroundColor:t.colorBgContainer,backgroundClip:"padding-box",borderRadius:t.borderRadiusLG,outline:"none",boxShadow:t.boxShadowSecondary,"&-item":Object.assign(Object.assign({},ht.vS),{display:"flex",alignItems:"center",minWidth:t.tabsDropdownWidth,margin:0,padding:"".concat(t.paddingXXS,"px ").concat(t.paddingSM,"px"),color:t.colorText,fontWeight:"normal",fontSize:t.fontSize,lineHeight:t.lineHeight,cursor:"pointer",transition:"all ".concat(t.motionDurationSlow),"> span":{flex:1,whiteSpace:"nowrap"},"&-remove":{flex:"none",marginLeft:{_skip_check_:!0,value:t.marginSM},color:t.colorTextDescription,fontSize:t.fontSizeSM,background:"transparent",border:0,cursor:"pointer","&:hover":{color:n}},"&:hover":{background:t.controlItemBgHover},"&-disabled":{"&, &:hover":{color:t.colorTextDisabled,background:"transparent",cursor:"not-allowed"}}})}})}},St=t=>{const{componentCls:e,margin:n,colorBorderSecondary:a,horizontalMargin:o,verticalItemPadding:c,verticalItemMargin:r}=t;return{["".concat(e,"-top, ").concat(e,"-bottom")]:{flexDirection:"column",["> ".concat(e,"-nav, > div > ").concat(e,"-nav")]:{margin:o,"&::before":{position:"absolute",right:{_skip_check_:!0,value:0},left:{_skip_check_:!0,value:0},borderBottom:"".concat(t.lineWidth,"px ").concat(t.lineType," ").concat(a),content:"''"},["".concat(e,"-ink-bar")]:{height:t.lineWidthBold,"&-animated":{transition:"width ".concat(t.motionDurationSlow,", left ").concat(t.motionDurationSlow,",\n            right ").concat(t.motionDurationSlow)}},["".concat(e,"-nav-wrap")]:{"&::before, &::after":{top:0,bottom:0,width:t.controlHeight},"&::before":{left:{_skip_check_:!0,value:0},boxShadow:t.boxShadowTabsOverflowLeft},"&::after":{right:{_skip_check_:!0,value:0},boxShadow:t.boxShadowTabsOverflowRight},["&".concat(e,"-nav-wrap-ping-left::before")]:{opacity:1},["&".concat(e,"-nav-wrap-ping-right::after")]:{opacity:1}}}},["".concat(e,"-top")]:{["> ".concat(e,"-nav,\n        > div > ").concat(e,"-nav")]:{"&::before":{bottom:0},["".concat(e,"-ink-bar")]:{bottom:0}}},["".concat(e,"-bottom")]:{["> ".concat(e,"-nav, > div > ").concat(e,"-nav")]:{order:1,marginTop:"".concat(n,"px"),marginBottom:0,"&::before":{top:0},["".concat(e,"-ink-bar")]:{top:0}},["> ".concat(e,"-content-holder, > div > ").concat(e,"-content-holder")]:{order:0}},["".concat(e,"-left, ").concat(e,"-right")]:{["> ".concat(e,"-nav, > div > ").concat(e,"-nav")]:{flexDirection:"column",minWidth:1.25*t.controlHeight,["".concat(e,"-tab")]:{padding:c,textAlign:"center"},["".concat(e,"-tab + ").concat(e,"-tab")]:{margin:r},["".concat(e,"-nav-wrap")]:{flexDirection:"column","&::before, &::after":{right:{_skip_check_:!0,value:0},left:{_skip_check_:!0,value:0},height:t.controlHeight},"&::before":{top:0,boxShadow:t.boxShadowTabsOverflowTop},"&::after":{bottom:0,boxShadow:t.boxShadowTabsOverflowBottom},["&".concat(e,"-nav-wrap-ping-top::before")]:{opacity:1},["&".concat(e,"-nav-wrap-ping-bottom::after")]:{opacity:1}},["".concat(e,"-ink-bar")]:{width:t.lineWidthBold,"&-animated":{transition:"height ".concat(t.motionDurationSlow,", top ").concat(t.motionDurationSlow)}},["".concat(e,"-nav-list, ").concat(e,"-nav-operations")]:{flex:"1 0 auto",flexDirection:"column"}}},["".concat(e,"-left")]:{["> ".concat(e,"-nav, > div > ").concat(e,"-nav")]:{["".concat(e,"-ink-bar")]:{right:{_skip_check_:!0,value:0}}},["> ".concat(e,"-content-holder, > div > ").concat(e,"-content-holder")]:{marginLeft:{_skip_check_:!0,value:"-".concat(t.lineWidth,"px")},borderLeft:{_skip_check_:!0,value:"".concat(t.lineWidth,"px ").concat(t.lineType," ").concat(t.colorBorder)},["> ".concat(e,"-content > ").concat(e,"-tabpane")]:{paddingLeft:{_skip_check_:!0,value:t.paddingLG}}}},["".concat(e,"-right")]:{["> ".concat(e,"-nav, > div > ").concat(e,"-nav")]:{order:1,["".concat(e,"-ink-bar")]:{left:{_skip_check_:!0,value:0}}},["> ".concat(e,"-content-holder, > div > ").concat(e,"-content-holder")]:{order:0,marginRight:{_skip_check_:!0,value:-t.lineWidth},borderRight:{_skip_check_:!0,value:"".concat(t.lineWidth,"px ").concat(t.lineType," ").concat(t.colorBorder)},["> ".concat(e,"-content > ").concat(e,"-tabpane")]:{paddingRight:{_skip_check_:!0,value:t.paddingLG}}}}}},Ct=t=>{const{componentCls:e,cardPaddingSM:n,cardPaddingLG:a,horizontalItemPaddingSM:o,horizontalItemPaddingLG:c}=t;return{[e]:{"&-small":{["> ".concat(e,"-nav")]:{["".concat(e,"-tab")]:{padding:o,fontSize:t.titleFontSizeSM}}},"&-large":{["> ".concat(e,"-nav")]:{["".concat(e,"-tab")]:{padding:c,fontSize:t.titleFontSizeLG}}}},["".concat(e,"-card")]:{["&".concat(e,"-small")]:{["> ".concat(e,"-nav")]:{["".concat(e,"-tab")]:{padding:n}},["&".concat(e,"-bottom")]:{["> ".concat(e,"-nav ").concat(e,"-tab")]:{borderRadius:"0 0 ".concat(t.borderRadius,"px ").concat(t.borderRadius,"px")}},["&".concat(e,"-top")]:{["> ".concat(e,"-nav ").concat(e,"-tab")]:{borderRadius:"".concat(t.borderRadius,"px ").concat(t.borderRadius,"px 0 0")}},["&".concat(e,"-right")]:{["> ".concat(e,"-nav ").concat(e,"-tab")]:{borderRadius:{_skip_check_:!0,value:"0 ".concat(t.borderRadius,"px ").concat(t.borderRadius,"px 0")}}},["&".concat(e,"-left")]:{["> ".concat(e,"-nav ").concat(e,"-tab")]:{borderRadius:{_skip_check_:!0,value:"".concat(t.borderRadius,"px 0 0 ").concat(t.borderRadius,"px")}}}},["&".concat(e,"-large")]:{["> ".concat(e,"-nav")]:{["".concat(e,"-tab")]:{padding:a}}}}}},Et=t=>{const{componentCls:e,itemActiveColor:n,itemHoverColor:a,iconCls:o,tabsHorizontalItemMargin:c,horizontalItemPadding:r,itemSelectedColor:i}=t,l="".concat(e,"-tab");return{[l]:{position:"relative",display:"inline-flex",alignItems:"center",padding:r,fontSize:t.titleFontSize,background:"transparent",border:0,outline:"none",cursor:"pointer","&-btn, &-remove":Object.assign({"&:focus:not(:focus-visible), &:active":{color:n}},(0,ht.Qy)(t)),"&-btn":{outline:"none",transition:"all 0.3s"},"&-remove":{flex:"none",marginRight:{_skip_check_:!0,value:-t.marginXXS},marginLeft:{_skip_check_:!0,value:t.marginXS},color:t.colorTextDescription,fontSize:t.fontSizeSM,background:"transparent",border:"none",outline:"none",cursor:"pointer",transition:"all ".concat(t.motionDurationSlow),"&:hover":{color:t.colorTextHeading}},"&:hover":{color:a},["&".concat(l,"-active ").concat(l,"-btn")]:{color:i,textShadow:t.tabsActiveTextShadow},["&".concat(l,"-disabled")]:{color:t.colorTextDisabled,cursor:"not-allowed"},["&".concat(l,"-disabled ").concat(l,"-btn, &").concat(l,"-disabled ").concat(e,"-remove")]:{"&:focus, &:active":{color:t.colorTextDisabled}},["& ".concat(l,"-remove ").concat(o)]:{margin:0},[o]:{marginRight:{_skip_check_:!0,value:t.marginSM}}},["".concat(l," + ").concat(l)]:{margin:{_skip_check_:!0,value:c}}}},Zt=t=>{const{componentCls:e,tabsHorizontalItemMarginRTL:n,iconCls:a,cardGutter:o}=t;return{["".concat(e,"-rtl")]:{direction:"rtl",["".concat(e,"-nav")]:{["".concat(e,"-tab")]:{margin:{_skip_check_:!0,value:n},["".concat(e,"-tab:last-of-type")]:{marginLeft:{_skip_check_:!0,value:0}},[a]:{marginRight:{_skip_check_:!0,value:0},marginLeft:{_skip_check_:!0,value:"".concat(t.marginSM,"px")}},["".concat(e,"-tab-remove")]:{marginRight:{_skip_check_:!0,value:"".concat(t.marginXS,"px")},marginLeft:{_skip_check_:!0,value:"-".concat(t.marginXXS,"px")},[a]:{margin:0}}}},["&".concat(e,"-left")]:{["> ".concat(e,"-nav")]:{order:1},["> ".concat(e,"-content-holder")]:{order:0}},["&".concat(e,"-right")]:{["> ".concat(e,"-nav")]:{order:0},["> ".concat(e,"-content-holder")]:{order:1}},["&".concat(e,"-card").concat(e,"-top, &").concat(e,"-card").concat(e,"-bottom")]:{["> ".concat(e,"-nav, > div > ").concat(e,"-nav")]:{["".concat(e,"-tab + ").concat(e,"-tab")]:{marginRight:{_skip_check_:!0,value:o},marginLeft:{_skip_check_:!0,value:0}}}}},["".concat(e,"-dropdown-rtl")]:{direction:"rtl"},["".concat(e,"-menu-item")]:{["".concat(e,"-dropdown-rtl")]:{textAlign:{_skip_check_:!0,value:"right"}}}}},Pt=t=>{const{componentCls:e,tabsCardPadding:n,cardHeight:a,cardGutter:o,itemHoverColor:c,itemActiveColor:r,colorBorderSecondary:i}=t;return{[e]:Object.assign(Object.assign(Object.assign(Object.assign({},(0,ht.Wf)(t)),{display:"flex",["> ".concat(e,"-nav, > div > ").concat(e,"-nav")]:{position:"relative",display:"flex",flex:"none",alignItems:"center",["".concat(e,"-nav-wrap")]:{position:"relative",display:"flex",flex:"auto",alignSelf:"stretch",overflow:"hidden",whiteSpace:"nowrap",transform:"translate(0)","&::before, &::after":{position:"absolute",zIndex:1,opacity:0,transition:"opacity ".concat(t.motionDurationSlow),content:"''",pointerEvents:"none"}},["".concat(e,"-nav-list")]:{position:"relative",display:"flex",transition:"opacity ".concat(t.motionDurationSlow)},["".concat(e,"-nav-operations")]:{display:"flex",alignSelf:"stretch"},["".concat(e,"-nav-operations-hidden")]:{position:"absolute",visibility:"hidden",pointerEvents:"none"},["".concat(e,"-nav-more")]:{position:"relative",padding:n,background:"transparent",border:0,color:t.colorText,"&::after":{position:"absolute",right:{_skip_check_:!0,value:0},bottom:0,left:{_skip_check_:!0,value:0},height:t.controlHeightLG/8,transform:"translateY(100%)",content:"''"}},["".concat(e,"-nav-add")]:Object.assign({minWidth:a,marginLeft:{_skip_check_:!0,value:o},padding:"0 ".concat(t.paddingXS,"px"),background:"transparent",border:"".concat(t.lineWidth,"px ").concat(t.lineType," ").concat(i),borderRadius:"".concat(t.borderRadiusLG,"px ").concat(t.borderRadiusLG,"px 0 0"),outline:"none",cursor:"pointer",color:t.colorText,transition:"all ".concat(t.motionDurationSlow," ").concat(t.motionEaseInOut),"&:hover":{color:c},"&:active, &:focus:not(:focus-visible)":{color:r}},(0,ht.Qy)(t))},["".concat(e,"-extra-content")]:{flex:"none"},["".concat(e,"-ink-bar")]:{position:"absolute",background:t.inkBarColor,pointerEvents:"none"}}),Et(t)),{["".concat(e,"-content")]:{position:"relative",width:"100%"},["".concat(e,"-content-holder")]:{flex:"auto",minWidth:0,minHeight:0},["".concat(e,"-tabpane")]:{outline:"none","&-hidden":{display:"none"}}}),["".concat(e,"-centered")]:{["> ".concat(e,"-nav, > div > ").concat(e,"-nav")]:{["".concat(e,"-nav-wrap")]:{["&:not([class*='".concat(e,"-nav-wrap-ping'])")]:{justifyContent:"center"}}}}}},Rt=(0,gt.Z)("Tabs",(t=>{const e=(0,kt.TS)(t,{tabsCardPadding:t.cardPadding||"".concat((t.cardHeight-Math.round(t.fontSize*t.lineHeight))/2-t.lineWidth,"px ").concat(t.padding,"px"),dropdownEdgeChildVerticalPadding:t.paddingXXS,tabsActiveTextShadow:"0 0 0.25px currentcolor",tabsDropdownHeight:200,tabsDropdownWidth:120,tabsHorizontalItemMargin:"0 0 0 ".concat(t.horizontalItemGutter,"px"),tabsHorizontalItemMarginRTL:"0 0 0 ".concat(t.horizontalItemGutter,"px")});return[Ct(e),Zt(e),St(e),wt(e),_t(e),Pt(e),yt(e)]}),(t=>{const e=t.controlHeightLG;return{zIndexPopup:t.zIndexPopupBase+50,cardBg:t.colorFillAlter,cardHeight:e,cardPadding:"",cardPaddingSM:"".concat(1.5*t.paddingXXS,"px ").concat(t.padding,"px"),cardPaddingLG:"".concat(t.paddingXS,"px ").concat(t.padding,"px ").concat(1.5*t.paddingXXS,"px"),titleFontSize:t.fontSize,titleFontSizeLG:t.fontSizeLG,titleFontSizeSM:t.fontSize,inkBarColor:t.colorPrimary,horizontalMargin:"0 0 ".concat(t.margin,"px 0"),horizontalItemGutter:32,horizontalItemMargin:"",horizontalItemMarginRTL:"",horizontalItemPadding:"".concat(t.paddingSM,"px 0"),horizontalItemPaddingSM:"".concat(t.paddingXS,"px 0"),horizontalItemPaddingLG:"".concat(t.padding,"px 0"),verticalItemPadding:"".concat(t.paddingXS,"px ").concat(t.paddingLG,"px"),verticalItemMargin:"".concat(t.margin,"px 0 0 0"),itemSelectedColor:t.colorPrimary,itemHoverColor:t.colorPrimaryHover,itemActiveColor:t.colorPrimaryActive,cardGutter:t.marginXXS/2}}));var Tt=function(t,e){var n={};for(var a in t)Object.prototype.hasOwnProperty.call(t,a)&&e.indexOf(a)<0&&(n[a]=t[a]);if(null!=t&&"function"===typeof Object.getOwnPropertySymbols){var o=0;for(a=Object.getOwnPropertySymbols(t);o<a.length;o++)e.indexOf(a[o])<0&&Object.prototype.propertyIsEnumerable.call(t,a[o])&&(n[a[o]]=t[a[o]])}return n};const It=t=>{const{type:e,className:n,rootClassName:c,size:i,onEdit:l,hideAdd:d,centered:u,addIcon:v,popupClassName:f,children:b,items:m,animated:h,style:g}=t,k=Tt(t,["type","className","rootClassName","size","onEdit","hideAdd","centered","addIcon","popupClassName","children","items","animated","style"]),{prefixCls:x,moreIcon:y=r.createElement(o.Z,null)}=k,{direction:_,tabs:w,getPrefixCls:S,getPopupContainer:C}=r.useContext(st.E_),E=S("tabs",x),[Z,P]=Rt(E);let R;"editable-card"===e&&(R={onEdit:(t,e)=>{let{key:n,event:a}=e;null===l||void 0===l||l("add"===t?a:n,t)},removeIcon:r.createElement(a.Z,null),addIcon:v||r.createElement(s,null),showAdd:!0!==d});const T=S(),I=function(t,e){return t||function(t){return t.filter((t=>t))}((0,bt.Z)(e).map((t=>{if(r.isValidElement(t)){const{key:e,props:n}=t,a=n||{},{tab:o}=a,c=mt(a,["tab"]);return Object.assign(Object.assign({key:String(e)},c),{label:o})}return null})))}(m,b),L=function(t){let e,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{inkBar:!0,tabPane:!1};return e=!1===n?{inkBar:!1,tabPane:!1}:!0===n?{inkBar:!0,tabPane:!0}:Object.assign({inkBar:!0},"object"===typeof n?n:{}),e.tabPane&&(e.tabPaneMotion=Object.assign(Object.assign({},ft),{motionName:(0,vt.m)(t,"switch")})),e}(E,h),N=(0,ut.Z)(i),M=Object.assign(Object.assign({},null===w||void 0===w?void 0:w.style),g);return Z(r.createElement(dt,Object.assign({direction:_,getPopupContainer:C,moreTransitionName:"".concat(T,"-slide-up")},k,{items:I,className:p()({["".concat(E,"-").concat(N)]:N,["".concat(E,"-card")]:["card","editable-card"].includes(e),["".concat(E,"-editable-card")]:"editable-card"===e,["".concat(E,"-centered")]:u},null===w||void 0===w?void 0:w.className,n,c,P),popupClassName:p()(f,P),style:M,editable:R,moreIcon:y,prefixCls:E,animated:L})))};It.TabPane=pt;const Lt=It}}]);
//# sourceMappingURL=58.2a519eb6.chunk.js.map