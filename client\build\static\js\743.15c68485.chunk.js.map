{"version": 3, "file": "static/js/743.15c68485.chunk.js", "mappings": "wKAIA,MAAMA,UAA6BC,EAAAA,UACjCC,WAAAA,CAAYC,GACVC,MAAMD,GACNE,KAAKC,MAAQ,CAAEC,UAAU,EAAOC,MAAO,KAAMC,UAAW,KAC1D,CAEA,+BAAOC,CAAyBF,GAC9B,MAAO,CAAED,UAAU,EACrB,CAEAI,iBAAAA,CAAkBH,EAAOC,GACvBJ,KAAKO,SAAS,CACZJ,MAAOA,EACPC,UAAWA,IAIbI,QAAQL,MAAM,iBAAkBA,EAAOC,EACzC,CAEAK,MAAAA,GACE,OAAIT,KAAKC,MAAMC,UACNQ,EAAAA,EAAAA,KAACC,EAAoB,CAC1BR,MAAOH,KAAKC,MAAME,MAClBS,WAAYA,IAAMZ,KAAKO,SAAS,CAAEL,UAAU,EAAOC,MAAO,KAAMC,UAAW,SAIxEJ,KAAKF,MAAMe,QACpB,EAGF,MAAMF,EAAuBG,IAA4B,IAA3B,MAAEX,EAAK,WAAES,GAAYE,EACjD,MAAMC,GAAWC,EAAAA,EAAAA,MAWjB,OACEN,EAAAA,EAAAA,KAAA,OAAKO,UAAU,gGAA+FJ,UAC5GK,EAAAA,EAAAA,MAAA,OAAKD,UAAU,iEAAgEJ,SAAA,EAC7EH,EAAAA,EAAAA,KAAA,OAAKO,UAAU,kFAAiFJ,UAC9FH,EAAAA,EAAAA,KAACS,EAAAA,IAAe,CAACF,UAAU,8BAG7BP,EAAAA,EAAAA,KAAA,MAAIO,UAAU,wCAAuCJ,SAAC,0BAItDH,EAAAA,EAAAA,KAAA,KAAGO,UAAU,qBAAoBJ,SAAC,qHAIlCK,EAAAA,EAAAA,MAAA,OAAKD,UAAU,YAAWJ,SAAA,EACxBK,EAAAA,EAAAA,MAAA,UACEE,QA1BUC,KAClBT,IACAU,OAAOC,SAASC,QAAQ,EAyBhBP,UAAU,+JAA8JJ,SAAA,EAExKH,EAAAA,EAAAA,KAACe,EAAAA,IAAS,CAACR,UAAU,aACrBP,EAAAA,EAAAA,KAAA,QAAAG,SAAM,yBAGRK,EAAAA,EAAAA,MAAA,UACEE,QA7BWM,KACnBX,EAAS,YAAY,EA6BbE,UAAU,kKAAiKJ,SAAA,EAE3KH,EAAAA,EAAAA,KAACiB,EAAAA,IAAM,CAACV,UAAU,aAClBP,EAAAA,EAAAA,KAAA,QAAAG,SAAM,qBAITe,MAWC,EAIV,G", "sources": ["components/RankingErrorBoundary.js"], "sourcesContent": ["import React from 'react';\nimport { TbAlertTriangle, TbRefresh, TbHome } from 'react-icons/tb';\nimport { useNavigate } from 'react-router-dom';\n\nclass RankingErrorBoundary extends React.Component {\n  constructor(props) {\n    super(props);\n    this.state = { hasError: false, error: null, errorInfo: null };\n  }\n\n  static getDerivedStateFromError(error) {\n    return { hasError: true };\n  }\n\n  componentDidCatch(error, errorInfo) {\n    this.setState({\n      error: error,\n      errorInfo: errorInfo\n    });\n    \n    // Log error to console for debugging\n    console.error('Ranking Error:', error, errorInfo);\n  }\n\n  render() {\n    if (this.state.hasError) {\n      return <RankingErrorFallback \n        error={this.state.error} \n        resetError={() => this.setState({ hasError: false, error: null, errorInfo: null })}\n      />;\n    }\n\n    return this.props.children;\n  }\n}\n\nconst RankingErrorFallback = ({ error, resetError }) => {\n  const navigate = useNavigate();\n\n  const handleRetry = () => {\n    resetError();\n    window.location.reload();\n  };\n\n  const handleGoHome = () => {\n    navigate('/user/hub');\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-blue-50 to-purple-50 flex items-center justify-center p-4\">\n      <div className=\"max-w-md w-full bg-white rounded-2xl shadow-xl p-8 text-center\">\n        <div className=\"w-20 h-20 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-6\">\n          <TbAlertTriangle className=\"w-10 h-10 text-red-600\" />\n        </div>\n        \n        <h1 className=\"text-2xl font-bold text-gray-900 mb-4\">\n          Ranking System Error\n        </h1>\n        \n        <p className=\"text-gray-600 mb-6\">\n          We encountered an error while loading the rankings. This might be a temporary issue with the server connection.\n        </p>\n        \n        <div className=\"space-y-3\">\n          <button\n            onClick={handleRetry}\n            className=\"w-full bg-blue-600 hover:bg-blue-700 text-white font-semibold py-3 px-4 rounded-lg transition-colors duration-200 flex items-center justify-center space-x-2\"\n          >\n            <TbRefresh className=\"w-5 h-5\" />\n            <span>Refresh Rankings</span>\n          </button>\n          \n          <button\n            onClick={handleGoHome}\n            className=\"w-full bg-gray-100 hover:bg-gray-200 text-gray-700 font-semibold py-3 px-4 rounded-lg transition-colors duration-200 flex items-center justify-center space-x-2\"\n          >\n            <TbHome className=\"w-5 h-5\" />\n            <span>Go to Hub</span>\n          </button>\n        </div>\n        \n        {process.env.NODE_ENV === 'development' && error && (\n          <details className=\"mt-6 text-left\">\n            <summary className=\"cursor-pointer text-sm text-gray-500 hover:text-gray-700\">\n              Error Details (Development)\n            </summary>\n            <pre className=\"mt-2 text-xs bg-gray-100 p-3 rounded overflow-auto max-h-32\">\n              {error.toString()}\n            </pre>\n          </details>\n        )}\n      </div>\n    </div>\n  );\n};\n\nexport default RankingErrorBoundary;\n"], "names": ["RankingError<PERSON><PERSON><PERSON>ry", "React", "constructor", "props", "super", "this", "state", "<PERSON><PERSON><PERSON><PERSON>", "error", "errorInfo", "getDerivedStateFromError", "componentDidCatch", "setState", "console", "render", "_jsx", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "resetError", "children", "_ref", "navigate", "useNavigate", "className", "_jsxs", "TbAlertTriangle", "onClick", "handleRetry", "window", "location", "reload", "TbRefresh", "handleGoHome", "TbHome", "process"], "sourceRoot": ""}