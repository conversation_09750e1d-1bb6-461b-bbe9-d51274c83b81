const axios = require('axios');

async function testVideoAPI() {
  try {
    console.log('🧪 Testing Video API...');
    
    const response = await axios.post('http://localhost:5000/api/study/get-study-content', {
      content: 'videos',
      level: 'primary',
      className: '',
      subject: ''
    });
    
    console.log('✅ API Response Status:', response.status);
    console.log('✅ Success:', response.data.success);
    console.log('✅ Message:', response.data.message);
    console.log('✅ Videos Count:', response.data.data?.length || 0);
    
    if (response.data.data && response.data.data.length > 0) {
      console.log('\n📹 Sample Videos:');
      response.data.data.slice(0, 3).forEach((video, index) => {
        console.log(`${index + 1}. ${video.title}`);
        console.log(`   Level: ${video.level}`);
        console.log(`   Class: ${video.className}`);
        console.log(`   Subject: ${video.subject}`);
        console.log(`   VideoID: ${video.videoID}`);
        console.log('');
      });
    }
    
  } catch (error) {
    console.error('❌ API Test Failed:', error.message);
    if (error.response) {
      console.error('Response Status:', error.response.status);
      console.error('Response Data:', error.response.data);
    }
  }
}

testVideoAPI();
