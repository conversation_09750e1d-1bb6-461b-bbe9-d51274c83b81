"use strict";(self.webpackChunkclient=self.webpackChunkclient||[]).push([[901],{272:(e,t,a)=>{a.d(t,{$s:()=>c,I1:()=>r,Ss:()=>i,cq:()=>s,dM:()=>o,uH:()=>n});const{default:l}=a(3371),s=async e=>{try{return(await l.post("/api/reports/add-report",e)).data}catch(t){return t.response.data}},r=async e=>{try{return(await l.post("/api/reports/get-all-reports",e)).data}catch(t){return t.response.data}},i=async e=>{try{return(await l.post("/api/reports/get-all-reports-by-user",e)).data}catch(t){return t.response.data}},n=async e=>{try{return(await l.get("/api/reports/get-all-reports-for-ranking")).data}catch(t){return t.response.data}},o=async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};try{const t=new URLSearchParams;e.limit&&t.append("limit",e.limit),e.classFilter&&t.append("classFilter",e.classFilter),e.levelFilter&&t.append("levelFilter",e.levelFilter),e.seasonFilter&&t.append("seasonFilter",e.seasonFilter),e.includeInactive&&t.append("includeInactive",e.includeInactive);return(await l.get("/api/quiz/xp-leaderboard?".concat(t.toString()))).data}catch(t){return t.response.data}},c=async function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:5;try{return(await l.get("/api/quiz/user-ranking/".concat(e,"?context=").concat(t))).data}catch(a){return a.response.data}}},640:(e,t,a)=>{a.d(t,{Z:()=>r});var l=a(2791),s=a(184);const r=function(e){let{title:t}=e;const[a,r]=(0,l.useState)(!1);return(0,l.useEffect)((()=>{window.innerWidth<768&&r(!0)}),[]),(0,s.jsx)("div",{className:"mt-2",children:(0,s.jsx)("h1",{className:a?"text-lg":"",children:t})})}},4871:(e,t,a)=>{a.r(t),a.d(t,{default:()=>k});var l=a(1413),s=a(2791),r=a(6042),i=a(7689),n=a(5526),o=a(640),c=a(5725),d=a(8839),x=a(9389),u=a(5273),m=a(2339),h=a(7309),g=a(7027),v=a(6473),f=a(1429),p=a(222),j=a(9434),b=a(8247),w=a(272),y=a(2426),N=a.n(y),S=a(184);const{Option:Z}=c.default,{RangePicker:z}=d.default,{Search:R}=x.default;const k=function(){(0,i.s0)();const[e,t]=(0,s.useState)([]),[a,d]=(0,s.useState)({current:1,pageSize:10,total:0}),[x,y]=(0,s.useState)({totalReports:0,totalStudents:0,averageScore:0,passRate:0,totalExams:0,activeToday:0}),k=(0,j.I0)(),[A,C]=(0,s.useState)({examName:"",userName:"",verdict:"",dateRange:null}),E=[{title:"Student",dataIndex:"userName",render:(e,t)=>{var a,l;return(0,S.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,S.jsx)("div",{className:"w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center",children:(0,S.jsx)(n.HLl,{className:"w-4 h-4 text-blue-600"})}),(0,S.jsxs)("div",{children:[(0,S.jsx)("div",{className:"font-medium text-gray-900",children:(null===(a=t.user)||void 0===a?void 0:a.name)||"N/A"}),(0,S.jsx)("div",{className:"text-sm text-gray-500",children:(null===(l=t.user)||void 0===l?void 0:l.email)||""})]})]})},width:200},{title:"Exam",dataIndex:"examName",render:(e,t)=>{var a,l;return(0,S.jsxs)("div",{children:[(0,S.jsx)("div",{className:"font-medium text-gray-900",children:(null===(a=t.exam)||void 0===a?void 0:a.name)||"N/A"}),(0,S.jsx)("div",{className:"text-sm text-gray-500",children:(null===(l=t.exam)||void 0===l?void 0:l.subject)||"General"})]})},width:200},{title:"Date & Time",dataIndex:"date",render:(e,t)=>(0,S.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,S.jsx)(n.RgR,{className:"w-4 h-4 text-gray-400"}),(0,S.jsxs)("div",{children:[(0,S.jsx)("div",{className:"text-sm font-medium",children:N()(t.createdAt).format("MMM DD, YYYY")}),(0,S.jsx)("div",{className:"text-xs text-gray-500",children:N()(t.createdAt).format("HH:mm")})]})]}),width:150},{title:"Score",dataIndex:"score",render:(e,t)=>{var a,l,s;const r=(null===(a=t.result)||void 0===a||null===(l=a.correctAnswers)||void 0===l?void 0:l.length)||0,i=(null===(s=t.exam)||void 0===s?void 0:s.totalMarks)||1,n=Math.round(r/i*100);return(0,S.jsxs)("div",{className:"text-center",children:[(0,S.jsxs)("div",{className:"text-lg font-bold text-gray-900",children:[r,"/",i]}),(0,S.jsx)(u.Z,{percent:n,size:"small",strokeColor:n>=60?"#10b981":"#ef4444",showInfo:!1}),(0,S.jsxs)("div",{className:"text-sm font-medium ".concat(n>=60?"text-green-600":"text-red-600"),children:[n,"%"]})]})},width:120},{title:"Result",dataIndex:"verdict",render:(e,t)=>{var a;const l=null===(a=t.result)||void 0===a?void 0:a.verdict,s="Pass"===l;return(0,S.jsx)(m.Z,{icon:s?(0,S.jsx)(n.e6w,{}):(0,S.jsx)(n.lhV,{}),color:s?"success":"error",className:"font-medium",children:l||"N/A"})},width:100},{title:"Actions",key:"actions",render:(e,t)=>(0,S.jsx)(h.ZP,{type:"primary",size:"small",icon:(0,S.jsx)(n.f7Q,{}),onClick:()=>{},className:"bg-blue-500 hover:bg-blue-600",children:"View"}),width:80}],I=async function(e){let s=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1,r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:10;try{k((0,b.YC)());const i=await(0,w.I1)((0,l.Z)((0,l.Z)({},e),{},{page:s,limit:r}));i.success?(t(i.data),(e=>{if(!e||0===e.length)return;const t=e.length,a=new Set(e.map((e=>{var t;return null===(t=e.user)||void 0===t?void 0:t._id}))).size,l=e.filter((e=>{var t;return"Pass"===(null===(t=e.result)||void 0===t?void 0:t.verdict)})).length,s=t>0?Math.round(l/t*100):0,r=e.map((e=>{var t,a,l;return((null===(t=e.result)||void 0===t||null===(a=t.correctAnswers)||void 0===a?void 0:a.length)||0)/((null===(l=e.exam)||void 0===l?void 0:l.totalMarks)||1)*100})),i=r.length>0?Math.round(r.reduce(((e,t)=>e+t),0)/r.length):0,n=new Set(e.map((e=>{var t;return null===(t=e.exam)||void 0===t?void 0:t._id}))).size,o=N()().startOf("day"),c=e.filter((e=>N()(e.createdAt).isSame(o,"day"))).length;y({totalReports:t,totalStudents:a,averageScore:i,passRate:s,totalExams:n,activeToday:c})})(i.data),d((0,l.Z)((0,l.Z)({},a),{},{current:s,total:i.pagination.totalReports}))):g.ZP.error(i.message),k((0,b.Ir)())}catch(i){k((0,b.Ir)()),g.ZP.error(i.message)}},F=(e,t)=>{C((a=>(0,l.Z)((0,l.Z)({},a),{},{[t]:e}))),d((e=>(0,l.Z)((0,l.Z)({},e),{},{current:1})))};return(0,s.useEffect)((()=>{I(A,a.current,a.pageSize)}),[A,a.current]),(0,S.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50",children:[(0,S.jsx)(o.Z,{title:"Admin Reports"}),(0,S.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,S.jsxs)(r.E.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:"text-center mb-12",children:[(0,S.jsx)("div",{className:"inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-blue-600 to-indigo-600 rounded-2xl mb-6 shadow-lg",children:(0,S.jsx)(n.hWk,{className:"w-8 h-8 text-white"})}),(0,S.jsxs)("h1",{className:"text-4xl font-bold text-gray-900 mb-4",children:["Student ",(0,S.jsx)("span",{className:"text-transparent bg-clip-text bg-gradient-to-r from-blue-600 to-indigo-600",children:"Performance"})," Analytics"]}),(0,S.jsx)("p",{className:"text-lg text-gray-600 max-w-2xl mx-auto",children:"Comprehensive insights into student performance and exam analytics"})]}),(0,S.jsxs)(r.E.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.1},className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-6 mb-8",children:[(0,S.jsx)(v.Z,{className:"text-center hover:shadow-lg transition-all duration-300 border-0 bg-gradient-to-br from-blue-50 to-blue-100",children:(0,S.jsxs)("div",{className:"flex flex-col items-center",children:[(0,S.jsx)("div",{className:"w-12 h-12 bg-blue-500 rounded-full flex items-center justify-center mb-3",children:(0,S.jsx)(n.RxU,{className:"w-6 h-6 text-white"})}),(0,S.jsx)(f.Z,{title:"Total Reports",value:x.totalReports,valueStyle:{color:"#1e40af",fontSize:"24px",fontWeight:"bold"}})]})}),(0,S.jsx)(v.Z,{className:"text-center hover:shadow-lg transition-all duration-300 border-0 bg-gradient-to-br from-green-50 to-green-100",children:(0,S.jsxs)("div",{className:"flex flex-col items-center",children:[(0,S.jsx)("div",{className:"w-12 h-12 bg-green-500 rounded-full flex items-center justify-center mb-3",children:(0,S.jsx)(n.HLl,{className:"w-6 h-6 text-white"})}),(0,S.jsx)(f.Z,{title:"Active Students",value:x.totalStudents,valueStyle:{color:"#059669",fontSize:"24px",fontWeight:"bold"}})]})}),(0,S.jsx)(v.Z,{className:"text-center hover:shadow-lg transition-all duration-300 border-0 bg-gradient-to-br from-purple-50 to-purple-100",children:(0,S.jsxs)("div",{className:"flex flex-col items-center",children:[(0,S.jsx)("div",{className:"w-12 h-12 bg-purple-500 rounded-full flex items-center justify-center mb-3",children:(0,S.jsx)(n.ehl,{className:"w-6 h-6 text-white"})}),(0,S.jsx)(f.Z,{title:"Average Score",value:x.averageScore,suffix:"%",valueStyle:{color:"#7c3aed",fontSize:"24px",fontWeight:"bold"}})]})}),(0,S.jsx)(v.Z,{className:"text-center hover:shadow-lg transition-all duration-300 border-0 bg-gradient-to-br from-orange-50 to-orange-100",children:(0,S.jsxs)("div",{className:"flex flex-col items-center",children:[(0,S.jsx)("div",{className:"w-12 h-12 bg-orange-500 rounded-full flex items-center justify-center mb-3",children:(0,S.jsx)(n.Chd,{className:"w-6 h-6 text-white"})}),(0,S.jsx)(f.Z,{title:"Pass Rate",value:x.passRate,suffix:"%",valueStyle:{color:"#ea580c",fontSize:"24px",fontWeight:"bold"}})]})}),(0,S.jsx)(v.Z,{className:"text-center hover:shadow-lg transition-all duration-300 border-0 bg-gradient-to-br from-pink-50 to-pink-100",children:(0,S.jsxs)("div",{className:"flex flex-col items-center",children:[(0,S.jsx)("div",{className:"w-12 h-12 bg-pink-500 rounded-full flex items-center justify-center mb-3",children:(0,S.jsx)(n.RxU,{className:"w-6 h-6 text-white"})}),(0,S.jsx)(f.Z,{title:"Total Exams",value:x.totalExams,valueStyle:{color:"#db2777",fontSize:"24px",fontWeight:"bold"}})]})}),(0,S.jsx)(v.Z,{className:"text-center hover:shadow-lg transition-all duration-300 border-0 bg-gradient-to-br from-indigo-50 to-indigo-100",children:(0,S.jsxs)("div",{className:"flex flex-col items-center",children:[(0,S.jsx)("div",{className:"w-12 h-12 bg-indigo-500 rounded-full flex items-center justify-center mb-3",children:(0,S.jsx)(n.rfE,{className:"w-6 h-6 text-white"})}),(0,S.jsx)(f.Z,{title:"Today's Activity",value:x.activeToday,valueStyle:{color:"#4338ca",fontSize:"24px",fontWeight:"bold"}})]})})]}),(0,S.jsx)(r.E.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.2},className:"bg-white rounded-2xl shadow-lg p-6 mb-8 border border-gray-100",children:(0,S.jsxs)("div",{className:"flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4",children:[(0,S.jsxs)("div",{className:"flex items-center gap-2",children:[(0,S.jsx)(n.a9n,{className:"w-5 h-5 text-gray-600"}),(0,S.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"Filter Reports"})]}),(0,S.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4",children:[(0,S.jsx)(R,{placeholder:"Search by exam name",value:A.examName,onChange:e=>F(e.target.value,"examName"),className:"w-full sm:w-48",size:"large"}),(0,S.jsx)(R,{placeholder:"Search by student name",value:A.userName,onChange:e=>F(e.target.value,"userName"),className:"w-full sm:w-48",size:"large"}),(0,S.jsxs)(c.default,{placeholder:"Select Result",value:A.verdict,onChange:e=>F(e,"verdict"),className:"w-full sm:w-48",size:"large",children:[(0,S.jsx)(Z,{value:"",children:"All Results"}),(0,S.jsx)(Z,{value:"Pass",children:"Passed"}),(0,S.jsx)(Z,{value:"Fail",children:"Failed"})]}),(0,S.jsx)(z,{value:A.dateRange,onChange:e=>{C((t=>(0,l.Z)((0,l.Z)({},t),{},{dateRange:e}))),d((e=>(0,l.Z)((0,l.Z)({},e),{},{current:1})))},className:"w-full sm:w-64",size:"large",placeholder:["Start Date","End Date"]}),(0,S.jsx)(h.ZP,{onClick:()=>{C({examName:"",userName:"",verdict:"",dateRange:null}),d((e=>(0,l.Z)((0,l.Z)({},e),{},{current:1})))},size:"large",className:"w-full sm:w-auto",children:"Clear Filters"}),(0,S.jsx)(h.ZP,{type:"primary",icon:(0,S.jsx)(n.HXz,{}),size:"large",className:"w-full sm:w-auto bg-gradient-to-r from-blue-600 to-indigo-600 border-none",children:"Export"})]})]})}),(0,S.jsx)(r.E.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.3},className:"bg-white rounded-2xl shadow-lg overflow-hidden border border-gray-100",children:(0,S.jsx)(p.Z,{columns:E,dataSource:e,pagination:{current:a.current,pageSize:a.pageSize,total:a.total,showSizeChanger:!0,showQuickJumper:!0,showTotal:(e,t)=>"".concat(t[0],"-").concat(t[1]," of ").concat(e," reports"),className:"px-6 py-4"},onChange:e=>{I(A,e.current,e.pageSize)},rowKey:e=>e._id,scroll:{x:1200},className:"modern-table",size:"large"})})]})]})}},922:(e,t,a)=>{a.d(t,{Z:()=>r});var l=a(732),s=a(2791);function r(e,t,a){let r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:s.createElement(l.Z,null);const i=function(e,t,a){return"boolean"===typeof e?e:void 0===t?!!a:!1!==t&&null!==t}(e,t,arguments.length>4&&void 0!==arguments[4]&&arguments[4]);if(!i)return[!1,null];const n="boolean"===typeof t||void 0===t||null===t?r:t;return[!0,a?a(n):n]}}}]);
//# sourceMappingURL=901.67ceb25f.chunk.js.map