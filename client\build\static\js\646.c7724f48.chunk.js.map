{"version": 3, "file": "static/js/646.c7724f48.chunk.js", "mappings": "gNAOA,MA0tCA,EA1tCmBA,KACjB,MAAMC,GAAWC,EAAAA,EAAAA,MACXC,GAAWC,EAAAA,EAAAA,OACX,GAAEC,IAAOC,EAAAA,EAAAA,OACT,KAAEC,IAASC,EAAAA,EAAAA,KAAaC,GAAUA,EAAMF,OAExCG,EAAaP,EAASM,OAAS,CACnCE,WAAY,EACZC,eAAgB,EAChBC,eAAgB,EAChBC,UAAW,EACXC,cAAe,GACfC,OAAQ,KACRC,SAAU,OACVC,YAAa,UACbC,kBAAmB,GACnBC,QAAS,SAGL,WACJT,EAAU,eACVC,EAAc,eACdC,EAAc,UACdC,EAAS,OACTE,EAAM,SACNC,EAAQ,YACRC,EAAW,kBACXC,EAAiB,QACjBC,EAAO,cACPL,GACEL,EACEW,EAAuB,SAAZD,GAAsBT,IAAeQ,GAAqB,KAEpEG,EAAUC,IAAeC,EAAAA,EAAAA,UAAS,KAClCC,EAAcC,IAAmBF,EAAAA,EAAAA,UAAS,CAAC,IAC3CG,EAAqBC,IAA0BJ,EAAAA,EAAAA,UAAS,CAAC,IACzDK,EAAYC,IAAiBN,EAAAA,EAAAA,WAAS,IAG7CO,EAAAA,EAAAA,YAAU,KA6GR,GAAIV,EAAU,CAEZ,MAAMW,EAAkB,GAGxB,IAAK,IAAIC,EAAI,EAAGA,EAAI,IAAKA,IAAK,CAC5B,MAAMC,EAAS,CACb,UAAW,UAAW,UAAW,UAAW,UAC5C,UAAW,UAAW,UAAW,UAAW,UAC5C,UAAW,UAAW,UAAW,UAAW,WAG9CF,EAAgBG,KAAK,CACnB9B,GAAG,YAAD+B,OAAcH,GAChBI,KAAM,GAAqB,GAAhBC,KAAKC,SAChBC,MAAuB,EAAhBF,KAAKC,SACZE,SAAU,EAAoB,EAAhBH,KAAKC,SACnBG,MAAOR,EAAOI,KAAKK,MAAML,KAAKC,SAAWL,EAAOU,SAChDC,MAAO,CAAC,SAAU,SAAU,YAAYP,KAAKK,MAAsB,EAAhBL,KAAKC,WACxDO,KAAM,EAAoB,EAAhBR,KAAKC,SACfQ,KAAM,WACNC,QAAiC,KAAvBV,KAAKC,SAAW,KAE9B,CAGA,IAAK,IAAIN,EAAI,EAAGA,EAAI,GAAIA,IACtBD,EAAgBG,KAAK,CACnB9B,GAAG,WAAD+B,OAAaH,GACfI,KAAsB,IAAhBC,KAAKC,SACXC,MAAuB,EAAhBF,KAAKC,SACZE,SAAU,EAAoB,EAAhBH,KAAKC,SACnBG,MAAO,CAAC,UAAW,UAAW,UAAW,WAAWJ,KAAKK,MAAsB,EAAhBL,KAAKC,WACpEO,KAAM,EAAoB,EAAhBR,KAAKC,SACfQ,KAAM,YAKV,IAAK,IAAId,EAAI,EAAGA,EAAI,IAAKA,IACvBD,EAAgBG,KAAK,CACnB9B,GAAG,SAAD+B,OAAWH,GACbI,KAAM,GAAqB,GAAhBC,KAAKC,SAChBC,MAAuB,GAAhBF,KAAKC,SACZE,SAAU,EAAoB,EAAhBH,KAAKC,SACnBG,MAAO,CAAC,UAAW,UAAW,UAAW,WAAWJ,KAAKK,MAAsB,EAAhBL,KAAKC,WACpEM,MAAO,SACPC,KAAM,EAAoB,EAAhBR,KAAKC,SACfQ,KAAM,QACNC,QAAiC,KAAvBV,KAAKC,SAAW,MAI9BhB,EAAYS,GAGZiB,YAAW,IAAM1B,EAAY,KAAK,IACpC,KAAO,CAEL,MAAM2B,EAAuB,GACvBC,EAAqB,CAAC,eAAM,eAAM,eAAM,eAAM,eAAM,eAAM,SAAK,SAAK,eAAM,gBAEhF,IAAK,IAAIlB,EAAI,EAAGA,EAAI,GAAIA,IACtBiB,EAAqBf,KAAK,CACxB9B,GAAG,YAAD+B,OAAcH,GAChBI,KAAsB,IAAhBC,KAAKC,SACXC,MAAuB,EAAhBF,KAAKC,SACZE,SAAU,EAAoB,EAAhBH,KAAKC,SACnBa,MAAOD,EAAmBb,KAAKK,MAAML,KAAKC,SAAWY,EAAmBP,SACxES,gBAAgB,EAChBP,KAAM,EAAoB,EAAhBR,KAAKC,WAGnBhB,EAAY2B,GAGZD,YAAW,IAAM1B,EAAY,KAAK,IACpC,CAvLkB+B,MAEhBxB,GAAc,GACdmB,YAAW,IAAMnB,GAAc,IAAQ,MAEvC,IACE,GAAIT,EAAU,CAEZ,MAAMkC,EAAe,IAAKC,OAAOC,cAAgBD,OAAOE,oBAGlDC,EAAW,SAACC,EAAWC,EAAWpB,GAA6B,IAAnBM,EAAIe,UAAAlB,OAAA,QAAAmB,IAAAD,UAAA,GAAAA,UAAA,GAAG,OACvD,MAAME,EAAaT,EAAaU,mBAC1BC,EAAWX,EAAaY,aAE9BH,EAAWI,QAAQF,GACnBA,EAASE,QAAQb,EAAac,aAE9BL,EAAWJ,UAAUU,eAAeV,EAAWC,GAC/CG,EAAWjB,KAAOA,EAElBmB,EAASK,KAAKD,eAAe,EAAGT,GAChCK,EAASK,KAAKC,wBAAwB,GAAKX,EAAY,KACvDK,EAASK,KAAKE,6BAA6B,IAAMZ,EAAYpB,GAE7DuB,EAAWU,MAAMb,GACjBG,EAAWW,KAAKd,EAAYpB,EAC9B,EAGMmC,EAAcf,IAClB,MAAMgB,EAAQtB,EAAauB,qBACrBC,EAASxB,EAAayB,aAAa,EAA6B,GAA1BzB,EAAa0B,WAAkB1B,EAAa0B,YAClFC,EAAOH,EAAOI,eAAe,GAGnC,IAAK,IAAIlD,EAAI,EAAGA,EAAIiD,EAAKtC,OAAQX,IAC/BiD,EAAKjD,GAAqB,EAAhBK,KAAKC,SAAe,EAGhCsC,EAAME,OAASA,EAEf,MAAMK,EAAS7B,EAAa8B,qBAC5BD,EAAOrC,KAAO,WACdqC,EAAOxB,UAAUU,eAAe,IAAMT,GAEtC,MAAMK,EAAWX,EAAaY,aAC9BD,EAASK,KAAKD,eAAe,EAAGT,GAChCK,EAASK,KAAKC,wBAAwB,GAAKX,EAAY,KACvDK,EAASK,KAAKE,6BAA6B,IAAMZ,EAAY,IAE7DgB,EAAMT,QAAQgB,GACdA,EAAOhB,QAAQF,GACfA,EAASE,QAAQb,EAAac,aAE9BQ,EAAMH,MAAMb,GACZgB,EAAMF,KAAKd,EAAY,GAAI,EAGvByB,EAAM/B,EAAagC,YAGzB5B,EAAS,OAAQ2B,EAAK,IACtB3B,EAAS,OAAQ2B,EAAM,GAAK,IAC5B3B,EAAS,OAAQ2B,EAAM,GAAK,IAC5B3B,EAAS,OAAQ2B,EAAM,GAAK,IAG5BV,EAAWU,EAAM,IACjBV,EAAWU,EAAM,IACjBV,EAAWU,EAAM,IACjBV,EAAWU,EAAM,IAEnB,KAAO,CAEL,MAAM/B,EAAe,IAAKC,OAAOC,cAAgBD,OAAOE,oBAElDC,EAAWA,CAACC,EAAWC,EAAWpB,KACtC,MAAMuB,EAAaT,EAAaU,mBAC1BC,EAAWX,EAAaY,aAE9BH,EAAWI,QAAQF,GACnBA,EAASE,QAAQb,EAAac,aAE9BL,EAAWJ,UAAUU,eAAeV,EAAWC,GAC/CG,EAAWjB,KAAO,OAElBmB,EAASK,KAAKD,eAAe,EAAGT,GAChCK,EAASK,KAAKC,wBAAwB,IAAMX,EAAY,KACxDK,EAASK,KAAKE,6BAA6B,IAAMZ,EAAYpB,GAE7DuB,EAAWU,MAAMb,GACjBG,EAAWW,KAAKd,EAAYpB,EAAS,EAIjC6C,EAAM/B,EAAagC,YACzB5B,EAAS,IAAK2B,EAAK,IACnB3B,EAAS,OAAQ2B,EAAM,GAAK,GAC9B,CACF,CAAE,MAAOE,GACPC,QAAQC,IAAI,sBACd,GAmFFpC,EAAW,GACV,CAACjC,IAqEJ,OACEsE,EAAAA,EAAAA,MAAA,OAAKC,UAAS,0EAAAxD,OACZf,EACI,6DACA,0DAAyD,KAAAe,OAC3DP,EAAcR,EAAW,cAAgB,YAAe,IAC5DwE,MAAO,CACLC,QAAStC,OAAOuC,YAAc,IAAM,MAAQvC,OAAOuC,YAAc,KAAO,OAAS,QACjFC,SAAA,CAGC1E,EAAS2E,KAAKC,GACTA,EAAM7C,gBAEN8C,EAAAA,EAAAA,KAAA,OAEEP,UAAU,sBACVC,MAAO,CACLxD,KAAK,GAADD,OAAK8D,EAAM7D,KAAI,KACnB+D,IAAI,GAADhE,OAAK,GAAqB,GAAhBE,KAAKC,SAAa,KAC/B8D,SAAS,GAADjE,OAAK8D,EAAMpD,MAAQ,EAAC,OAC5BwD,UAAU,cAADlE,OAAgB8D,EAAMzD,SAAQ,kBAAAL,OAAiB8D,EAAM1D,MAAK,cACnE+D,OAAQ,KACRP,SAEDE,EAAM9C,OAVF8C,EAAM7F,IAeE,YAAf6F,EAAMnD,MAENoD,EAAAA,EAAAA,KAAA,OAEEP,UAAU,WACVC,MAAO,CACLxD,KAAK,GAADD,OAAK8D,EAAM7D,KAAI,KACnBmE,MAAM,GAADpE,OAAK8D,EAAMpD,KAAI,MACpB2D,OAAO,GAADrE,OAAK8D,EAAMpD,KAAI,MACrB4D,WAAW,2BAADtE,OAA6B8D,EAAMxD,MAAK,kBAClDiE,aAAc,MACdL,UAAU,mBAADlE,OAAqB8D,EAAMzD,SAAQ,kBAAAL,OAAiB8D,EAAM1D,MAAK,cACxE4D,IAAI,GAADhE,OAAqB,IAAhBE,KAAKC,SAAc,KAC3BqE,UAAU,OAADxE,OAAsB,EAAb8D,EAAMpD,KAAQ,OAAAV,OAAM8D,EAAMxD,OAC5C6D,OAAQ,MAXLL,EAAM7F,IAiBE,UAAf6F,EAAMnD,MAENoD,EAAAA,EAAAA,KAAA,OAEEP,UAAU,sBACVC,MAAO,CACLxD,KAAK,GAADD,OAAK8D,EAAM7D,KAAI,KACnBmE,MAAM,GAADpE,OAAK8D,EAAMpD,KAAI,MACpB2D,OAAO,GAADrE,OAAK8D,EAAMpD,KAAI,MACrB+D,gBAAiBX,EAAMxD,MACvBiE,aAA8B,WAAhBT,EAAMrD,MAAqB,MAAwB,aAAhBqD,EAAMrD,MAAuB,IAAM,KACpFiE,SAA0B,aAAhBZ,EAAMrD,MAAuB,sCAAwC,OAC/EyD,UAAU,kBAADlE,OAAoB8D,EAAMzD,SAAQ,eAAAL,OAAc8D,EAAM1D,MAAK,cACpE4D,IAAK,MACL,aAAa,GAADhE,OAAK8D,EAAMlD,QAAO,MAC9B4D,UAAU,OAADxE,OAAS8D,EAAMpD,KAAI,OAAAV,OAAM8D,EAAMxD,MAAK,MAC7C6D,OAAQ,MAbLL,EAAM7F,KAqBf8F,EAAAA,EAAAA,KAAA,OAEEP,UAAU,sBACVC,MAAO,CACLxD,KAAK,GAADD,OAAK8D,EAAM7D,KAAI,KACnBmE,MAAM,GAADpE,OAAK8D,EAAMpD,KAAI,MACpB2D,OAAO,GAADrE,OAAK8D,EAAMpD,KAAI,MACrB+D,gBAAiBX,EAAMxD,MACvBiE,aAA8B,WAAhBT,EAAMrD,MAAqB,MAAwB,aAAhBqD,EAAMrD,MAAuB,IAAM,KACpFiE,SAA0B,aAAhBZ,EAAMrD,MAAuB,sCAAwC,OAC/EyD,UAAU,iBAADlE,OAAmB8D,EAAMzD,SAAQ,eAAAL,OAAc8D,EAAM1D,MAAK,cACnE4D,IAAK,QACLQ,UAAU,OAADxE,OAAS8D,EAAMpD,KAAI,OAAAV,OAAM8D,EAAMxD,MAAK,MAC7CqE,OAAO,aAAD3E,OAAe8D,EAAMxD,OAC3BgE,WAAW,0BAADtE,OAA4B8D,EAAMxD,MAAK,MAAAN,OAAK8D,EAAMxD,MAAK,OACjE6D,OAAQ,MAdLL,EAAM7F,OAqBjB8F,EAAAA,EAAAA,KAAA,SAAOa,KAAG,EAAAhB,SAAA,ytUAqUTnE,IACCsE,EAAAA,EAAAA,KAAA,OACEP,UAAU,oCACVC,MAAO,CACLa,WAAYrF,EACR,8GACA,8GACJiF,UAAWjF,EAAW,gDAAkD,8CACxEkF,OAAQ,MAKdZ,EAAAA,EAAAA,MAAA,OAAKC,UAAS,4DAAAxD,OACZf,EAAW,oCAAsC,gCAA+B,KAAAe,OAC9EP,EAAa,aAAe,IAChCgE,MAAO,CACLa,WAAY7E,EACPR,EACG,2EACA,2EACJ,QACJuF,UAAW/E,EACNR,EACG,uEACA,uEACJ,+BACJkF,OAAQ,GACRT,QAAStC,OAAOuC,YAAc,IAAM,OAASvC,OAAOuC,YAAc,KAAO,OAAS,OAClFkB,SAAUzD,OAAOuC,YAAc,IAAM,OAASvC,OAAOuC,YAAc,KAAO,MAAQ,SAClFC,SAAA,EAEAL,EAAAA,EAAAA,MAAA,OACEC,UAAU,cACVC,MAAO,CAAEqB,aAAc1D,OAAOuC,YAAc,IAAM,OAAS,QAASC,SAAA,EAEpEG,EAAAA,EAAAA,KAAA,OACEP,UAAS,sEAAAxD,OACPf,EAAW,kDAAoD,8CAEjEwE,MAAO,CACLW,MAAOhD,OAAOuC,YAAc,IAAM,OAAS,OAC3CU,OAAQjD,OAAOuC,YAAc,IAAM,OAAS,QAC5CC,UAEFG,EAAAA,EAAAA,KAACgB,EAAAA,IAAQ,CACPvB,UAAS,GAAAxD,OAAKf,EAAW,kBAAoB,iBAC7CwE,MAAO,CACLW,MAAOhD,OAAOuC,YAAc,IAAM,OAAS,OAC3CU,OAAQjD,OAAOuC,YAAc,IAAM,OAAS,aAKlDI,EAAAA,EAAAA,KAAA,MACEP,UAAS,kBAAAxD,OACPf,EACI,qDACA,0CAENwE,MAAO,CACLQ,SAAU7C,OAAOuC,YAAc,IAAM,OAASvC,OAAOuC,YAAc,KAAO,OAAS,QACnFC,SAED3E,GACCsE,EAAAA,EAAAA,MAAA,QACEC,UAAU,mCACVC,MAAO,CAAEuB,IAAK5D,OAAOuC,YAAc,IAAM,MAAQ,OAAQsB,SAAU,QAASrB,SAAA,EAE5EG,EAAAA,EAAAA,KAAA,QACEP,UAAU,sBACVC,MAAO,CAAEQ,SAAU7C,OAAOuC,YAAc,IAAM,OAAS,QAASC,SACjE,kBACDG,EAAAA,EAAAA,KAAA,QAAMP,UAAU,uCAAsCI,SAAC,sBACvDG,EAAAA,EAAAA,KAAA,QACEP,UAAU,sBACVC,MAAO,CAAEQ,SAAU7C,OAAOuC,YAAc,IAAM,OAAS,QAASC,SACjE,qBAGHL,EAAAA,EAAAA,MAAA,QACEC,UAAU,mCACVC,MAAO,CAAEuB,IAAK5D,OAAOuC,YAAc,IAAM,MAAQ,OAAQsB,SAAU,QAASrB,SAAA,EAE5EG,EAAAA,EAAAA,KAAA,QACEP,UAAU,wBACVC,MAAO,CAAEQ,SAAU7C,OAAOuC,YAAc,IAAM,OAAS,QAASC,SACjE,kBACDG,EAAAA,EAAAA,KAAA,QAAMP,UAAU,mCAAkCI,SAAC,iBACnDG,EAAAA,EAAAA,KAAA,QACEP,UAAU,wBACVC,MAAO,CAAEQ,SAAU7C,OAAOuC,YAAc,IAAM,OAAS,QAASC,SACjE,uBAKPG,EAAAA,EAAAA,KAAA,OAAKP,UAAS,2BAAAxD,OACZf,EACI,2CACA,0CACH2E,SACA3E,GACC8E,EAAAA,EAAAA,KAAA,QAAMP,UAAU,uCAAsCI,SAAC,+BAEvDG,EAAAA,EAAAA,KAAA,QAAMP,UAAU,mCAAkCI,SAAC,gDAIvDL,EAAAA,EAAAA,MAAA,KAAGC,UAAU,6BAA4BI,SAAA,CAAC,gBACpC/E,EAAS,MAAIC,SAKrBiF,EAAAA,EAAAA,KAAA,OAAKP,UAAU,mBAAkBI,UAC/BL,EAAAA,EAAAA,MAAA,OAAKC,UAAS,sCAAAxD,OACZf,EAAW,wCAA0C,qCACpD2E,SAAA,EACDL,EAAAA,EAAAA,MAAA,OAAKC,UAAS,2BAAAxD,OACZf,EAAW,iBAAmB,gBAC7B2E,SAAA,CACArF,EAAW,QAEdwF,EAAAA,EAAAA,KAAA,OAAKP,UAAU,gBAAeI,SAAC,qBASnCL,EAAAA,EAAAA,MAAA,OACEC,UAAU,8CACVC,MAAO,CAAEwB,SAAU,QAASrB,SAAA,EAG5BL,EAAAA,EAAAA,MAAA,OACEE,MAAO,CACLgB,gBAAiB,UACjBf,QAAS,UACTiB,OAAQ,oBACRJ,aAAc,MACdW,QAAS,OACTC,WAAY,SACZH,IAAK,MACLf,SAAU,QACVL,SAAA,EAEFL,EAAAA,EAAAA,MAAA,OAAKE,MAAO,CAAEyB,QAAS,OAAQC,WAAY,SAAUH,IAAK,OAAQpB,SAAA,EAChEG,EAAAA,EAAAA,KAAA,QAAMN,MAAO,CAAEQ,SAAU,QAASL,SAAC,YACnCG,EAAAA,EAAAA,KAAA,QAAMN,MAAO,CAAEQ,SAAU,OAAQmB,WAAY,OAAQ9E,MAAO,WAAYsD,SAAEpF,QAE5E+E,EAAAA,EAAAA,MAAA,OAAKE,MAAO,CAAEyB,QAAS,OAAQC,WAAY,SAAUH,IAAK,OAAQpB,SAAA,EAChEG,EAAAA,EAAAA,KAAA,QAAMN,MAAO,CAAEQ,SAAU,QAASL,SAAC,YACnCG,EAAAA,EAAAA,KAAA,QAAMN,MAAO,CAAEQ,SAAU,OAAQmB,WAAY,OAAQ9E,MAAO,WAAYsD,SAAEnF,EAAiBD,WAK/F+E,EAAAA,EAAAA,MAAA,OACEE,MAAO,CACLgB,gBAAiB,UACjBf,QAAS,UACTiB,OAAQ,oBACRJ,aAAc,MACdW,QAAS,OACTC,WAAY,SACZH,IAAK,MACLf,SAAU,QACVL,SAAA,EAEFG,EAAAA,EAAAA,KAAA,QAAMN,MAAO,CAAEQ,SAAU,QAASL,SAAC,kBACnCL,EAAAA,EAAAA,MAAA,QAAME,MAAO,CAAEQ,SAAU,OAAQmB,WAAY,OAAQ9E,MAAOrB,EAAW,UAAY,WAAY2E,SAAA,CAAErF,EAAW,UAI7GG,GAAaA,EAAY,IACxB6E,EAAAA,EAAAA,MAAA,OACEE,MAAO,CACLgB,gBAAiB,UACjBf,QAAS,UACTiB,OAAQ,oBACRJ,aAAc,MACdW,QAAS,OACTC,WAAY,SACZH,IAAK,MACLf,SAAU,QACVL,SAAA,EAEFG,EAAAA,EAAAA,KAAA,QAAMN,MAAO,CAAEQ,SAAU,QAASL,SAAC,kBACnCL,EAAAA,EAAAA,MAAA,QAAME,MAAO,CAAEQ,SAAU,OAAQmB,WAAY,OAAQ9E,MAAO,WAAYsD,SAAA,CACrE1D,KAAKK,MAAM7B,EAAY,IAAI,KAAGA,EAAY,IAAI2G,WAAWC,SAAS,EAAG,cAO7E1G,IACCmF,EAAAA,EAAAA,KAAA,OAAKP,UAAU,4FAA2FI,UACxGL,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oCAAmCI,SAAA,EAChDL,EAAAA,EAAAA,MAAA,OAAKC,UAAU,0BAAyBI,SAAA,EACtCG,EAAAA,EAAAA,KAAA,OAAKP,UAAU,wEAAuEI,UACpFG,EAAAA,EAAAA,KAACwB,EAAAA,IAAM,CAAC/B,UAAU,0BAEpBD,EAAAA,EAAAA,MAAA,OAAAK,SAAA,EACEL,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oCAAmCI,SAAA,CAAC,IAAEhF,EAAO4G,WAAa,EAAE,UAC3EzB,EAAAA,EAAAA,KAAA,OAAKP,UAAU,wBAAuBI,SAAC,kBAI3CL,EAAAA,EAAAA,MAAA,OAAKC,UAAU,aAAYI,SAAA,EACzBG,EAAAA,EAAAA,KAAA,OAAKP,UAAU,qCAAoCI,YAC1C,OAAJzF,QAAI,IAAJA,OAAI,EAAJA,EAAMsH,UAAW,IAAM7G,EAAO4G,WAAa,IAAIE,oBAEpDnC,EAAAA,EAAAA,MAAA,OAAKC,UAAU,wBAAuBI,SAAA,CAAC,0BAAsB,OAAJzF,QAAI,IAAJA,OAAI,EAAJA,EAAMwH,eAAgB,cAOrF/G,GAAUT,IACV4F,EAAAA,EAAAA,KAAA,OAAKP,UAAU,4FAA2FI,UACxGL,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oCAAmCI,SAAA,EAChDL,EAAAA,EAAAA,MAAA,OAAKC,UAAU,0BAAyBI,SAAA,EACtCG,EAAAA,EAAAA,KAAA,OAAKP,UAAU,wEAAuEI,UACpFG,EAAAA,EAAAA,KAACwB,EAAAA,IAAM,CAAC/B,UAAU,0BAEpBO,EAAAA,EAAAA,KAAA,OAAAH,UACEG,EAAAA,EAAAA,KAAA,OAAKP,UAAU,wBAAuBI,SAAC,wBAI3CL,EAAAA,EAAAA,MAAA,OAAKC,UAAU,aAAYI,SAAA,EACzBG,EAAAA,EAAAA,KAAA,OAAKP,UAAU,qCAAoCI,UAC/CzF,EAAKsH,SAAW,GAAGC,oBAEvBnC,EAAAA,EAAAA,MAAA,OAAKC,UAAU,wBAAuBI,SAAA,CAAC,yBAAkBzF,EAAKwH,cAAgB,cAOtF5B,EAAAA,EAAAA,KAAA,OAAKP,UAAU,sFAAqFI,UAClGL,EAAAA,EAAAA,MAAA,OAAKC,UAAU,+BAA8BI,SAAA,EAC3CG,EAAAA,EAAAA,KAAA,OAAKP,UAAU,sEAAqEI,UAClFG,EAAAA,EAAAA,KAAC6B,EAAAA,IAAO,CAACpC,UAAU,0BAErBO,EAAAA,EAAAA,KAAA,MAAIP,UAAU,sCAAqCI,SAAC,uCAOvDjF,GAAiBA,EAAc6B,OAAS,IACvC+C,EAAAA,EAAAA,MAAA,OAAKC,UAAU,uFAAsFI,SAAA,EACnGL,EAAAA,EAAAA,MAAA,OAAKC,UAAU,+BAA8BI,SAAA,EAC3CG,EAAAA,EAAAA,KAAA,OAAKP,UAAU,sEAAqEI,UAClFG,EAAAA,EAAAA,KAAC8B,EAAAA,IAAU,CAACrC,UAAU,0BAExBO,EAAAA,EAAAA,KAAA,MAAIP,UAAU,sCAAqCI,SAAC,iDAGtDG,EAAAA,EAAAA,KAAA,OAAKP,UAAU,qCAAoCI,SAChDjF,EAAckF,KAAI,CAACiC,EAAQC,KAE1B1C,QAAQC,IAAI,YAADtD,OAAa+F,EAAQ,EAAC,UAAUD,IAE3CvC,EAAAA,EAAAA,MAAA,OAEEC,UAAS,0EAAAxD,OACP8F,EAAOE,UACH,4EACA,sEAENvC,MAAO,CACLkB,OAAQmB,EAAOE,UACX,oBACA,oBACJxB,UAAWsB,EAAOE,UACd,uEACA,wEACJpC,SAAA,EAGFG,EAAAA,EAAAA,KAAA,OAAKP,UAAS,OAAAxD,OACZ8F,EAAOE,UACH,2CACA,wCACHpC,UACDL,EAAAA,EAAAA,MAAA,OAAKC,UAAU,0BAAyBI,SAAA,EACtCG,EAAAA,EAAAA,KAAA,OAAKP,UAAS,qEAAAxD,OACZ8F,EAAOE,UACH,oCACA,mCACHpC,SACAkC,EAAOE,WAAYjC,EAAAA,EAAAA,KAACkC,EAAAA,IAAO,CAACzC,UAAU,aAAeO,EAAAA,EAAAA,KAACmC,EAAAA,IAAG,CAAC1C,UAAU,eAGvED,EAAAA,EAAAA,MAAA,OAAKC,UAAU,SAAQI,SAAA,EACrBL,EAAAA,EAAAA,MAAA,MAAIC,UAAU,kCAAiCI,SAAA,CAAC,YACpCmC,EAAQ,MAEpBhC,EAAAA,EAAAA,KAAA,OAAKP,UAAU,+BAA8BI,UAC3CG,EAAAA,EAAAA,KAAA,QAAMP,UAAS,4CAAAxD,OACb8F,EAAOE,UACH,0BACA,yBACHpC,SACAkC,EAAOE,UAAY,iBAAc,4BAQ5CzC,EAAAA,EAAAA,MAAA,OAAKC,UAAU,MAAKI,SAAA,EAClBG,EAAAA,EAAAA,KAAA,OAAKP,UAAU,OAAMI,UACnBG,EAAAA,EAAAA,KAAA,KAAGP,UAAU,+CAA8CI,SACxDkC,EAAOK,cAAgBL,EAAOM,kBAKT,UAAxBN,EAAOO,cAA4BP,EAAOQ,eAAiBR,EAAOS,OAAST,EAAOU,YAAcV,EAAOQ,eAAiBR,EAAOS,OAAST,EAAOU,YAC/IzC,EAAAA,EAAAA,KAAA,OAAKP,UAAU,OAAMI,UACnBL,EAAAA,EAAAA,MAAA,OAAKC,UAAS,2BAAAxD,OACZ8F,EAAOE,UACH,+BACA,4BACHpC,SAAA,EACDL,EAAAA,EAAAA,MAAA,OAAKC,UAAU,+BAA8BI,SAAA,EAC3CG,EAAAA,EAAAA,KAAA,QAAMP,UAAU,sCAAqCI,SAAC,kCACtDG,EAAAA,EAAAA,KAAA,QAAMP,UAAS,4CAAAxD,OACb8F,EAAOE,UACH,0BACA,yBACHpC,SACAkC,EAAOE,UAAY,iBAAc,qBAGtCzC,EAAAA,EAAAA,MAAA,OAAKC,UAAU,iCAAgCI,SAAA,EAC7CG,EAAAA,EAAAA,KAAA,OACE0C,IAAKX,EAAOQ,eAAiBR,EAAOS,OAAST,EAAOU,SACpDE,IAAI,iBACJlD,UAAU,uDACVC,MAAO,CAAEkD,UAAW,SACpBC,QAAUC,IACRA,EAAEC,OAAOrD,MAAMyB,QAAU,OACzB2B,EAAEC,OAAOC,YAAYtD,MAAMyB,QAAU,OAAO,KAGhDnB,EAAAA,EAAAA,KAAA,OACEP,UAAU,+DACVC,MAAO,CAAEyB,QAAS,QAAStB,SAC5B,oDASTL,EAAAA,EAAAA,MAAA,OAAKC,UAAU,YAAWI,SAAA,EACxBL,EAAAA,EAAAA,MAAA,OACEC,UAAS,kBAAAxD,OACP8F,EAAOE,UACH,cACA,aAENvC,MAAO,CACLkB,OAAQmB,EAAOE,UACX,oBACA,qBACJpC,SAAA,EAEFL,EAAAA,EAAAA,MAAA,OAAKC,UAAU,+BAA8BI,SAAA,EAC3CG,EAAAA,EAAAA,KAAA,OAAKP,UAAS,yDAAAxD,OACZ8F,EAAOE,UAAY,eAAiB,cACnCpC,SACAkC,EAAOE,WACNjC,EAAAA,EAAAA,KAACkC,EAAAA,IAAO,CAACzC,UAAU,wBAEnBO,EAAAA,EAAAA,KAACmC,EAAAA,IAAG,CAAC1C,UAAU,0BAGnBO,EAAAA,EAAAA,KAAA,QAAMP,UAAU,8BAA6BI,SAAC,qBAEhDG,EAAAA,EAAAA,KAAA,OAAKP,UAAS,oCAAAxD,OACZ8F,EAAOE,UACH,sDACA,iDACHpC,SACAkC,EAAOkB,YAAc,2BAIxBlB,EAAOE,YACPzC,EAAAA,EAAAA,MAAA,OAAKC,UAAU,uDAAsDI,SAAA,EACnEL,EAAAA,EAAAA,MAAA,OAAKC,UAAU,+BAA8BI,SAAA,EAC3CG,EAAAA,EAAAA,KAAA,OAAKP,UAAU,qEAAoEI,UACjFG,EAAAA,EAAAA,KAACkC,EAAAA,IAAO,CAACzC,UAAU,0BAErBO,EAAAA,EAAAA,KAAA,QAAMP,UAAU,8BAA6BI,SAAC,wBAEhDG,EAAAA,EAAAA,KAAA,OAAKP,UAAU,yFAAwFI,SACpGkC,EAAOmB,oBAMZnB,EAAOE,YACPzC,EAAAA,EAAAA,MAAA,OAAKC,UAAU,OAAMI,SAAA,EACnBG,EAAAA,EAAAA,KAAA,UACEP,UAAS,gHAAAxD,OACPT,EAAoB,YAADS,OAAa+F,IAC5B,iCACA,6HAENmB,QAASA,IAh5BVC,OAAOC,EAAetB,KAC7C,MAAMuB,EAAW,YAAArH,OAAeoH,GAGhC,IAAI7H,EAAoB8H,KAAgBhI,EAAagI,GAIrD,IACE7H,GAAuB8H,IAAIC,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GAAUD,GAAI,IAAE,CAACD,IAAc,MAE1D,MAAMG,QAAiBC,EAAAA,EAAAA,IAA4B,CACjDC,SAAU5B,EAAOK,cAAgBL,EAAOM,aACxCuB,eAAgB7B,EAAOmB,cACvBD,WAAYlB,EAAOkB,WACnBR,SAAUV,EAAOQ,eAAiBR,EAAOS,OAAST,EAAOU,UAAY,OAGnEgB,EAASI,QACXtI,GAAgBgI,IAAIC,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACfD,GAAI,IACP,CAACD,GAAcG,EAASK,iBAG1BxE,QAAQD,MAAM,+BAAgCoE,EAASpE,OACvD9D,GAAgBgI,IAAIC,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACfD,GAAI,IACP,CAACD,GAAc,wFAGrB,CAAE,MAAOjE,GACPC,QAAQD,MAAM,8BAA+BA,GAC7C9D,GAAgBgI,IAAIC,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACfD,GAAI,IACP,CAACD,GAAc,uFAEnB,CAAC,QACC7H,GAAuB8H,IAAIC,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GAAUD,GAAI,IAAE,CAACD,IAAc,KAC5D,GA02BuCS,CAAiB/B,EAAOD,GACvCiC,SAAUxI,EAAoB,YAADS,OAAa+F,IAASnC,SAElDrE,EAAoB,YAADS,OAAa+F,KAC/BxC,EAAAA,EAAAA,MAAAyE,EAAAA,SAAA,CAAApE,SAAA,EACEG,EAAAA,EAAAA,KAAA,OAAKP,UAAU,iFAAqF,6BAItGD,EAAAA,EAAAA,MAAAyE,EAAAA,SAAA,CAAApE,SAAA,EACEG,EAAAA,EAAAA,KAAC6B,EAAAA,IAAO,CAACpC,UAAU,YAAY,uBAOpCnE,EAAa,YAADW,OAAa+F,MACxBxC,EAAAA,EAAAA,MAAA,OAAKC,UAAU,0FAAyFI,SAAA,EACtGL,EAAAA,EAAAA,MAAA,OAAKC,UAAU,+BAA8BI,SAAA,EAC3CG,EAAAA,EAAAA,KAAC6B,EAAAA,IAAO,CAACpC,UAAU,2BACnBO,EAAAA,EAAAA,KAAA,MAAIP,UAAU,0BAAyBI,SAAC,kCAE1CG,EAAAA,EAAAA,KAAA,OAAKP,UAAU,gCAA+BI,UAC5CG,EAAAA,EAAAA,KAACkE,EAAAA,EAAe,CAACC,KAAM7I,EAAa,YAADW,OAAa+F,sBAjLzDD,EAAOqC,YAAcpC,YAmMpCxC,EAAAA,EAAAA,MAAA,OACEC,UAAU,aACVC,MAAO,CACL2E,cAAehH,OAAOuC,YAAc,IAAM,SAAW,OACrDC,SAAA,EAEFL,EAAAA,EAAAA,MAAA,UACE2D,QAAUL,IACRA,EAAEwB,iBACFhF,QAAQC,IAAI,6CAv5BtBD,QAAQC,IAAI,+CACZgF,EAAAA,EAAAA,kBAAgB,KACdzK,EAAS,aAAa,GAs5BO,EAEvB2F,UAAU,sNACVC,MAAO,CACLC,QAAStC,OAAOuC,YAAc,IAAM,YAAc,YAClDM,SAAU7C,OAAOuC,YAAc,IAAM,OAAS,QAEhDhD,KAAK,SAAQiD,SAAA,EAEbG,EAAAA,EAAAA,KAACwE,EAAAA,IAAM,CACL9E,MAAO,CACLW,MAAOhD,OAAOuC,YAAc,IAAM,OAAS,OAC3CU,OAAQjD,OAAOuC,YAAc,IAAM,OAAS,UAE9C,mBAIJJ,EAAAA,EAAAA,MAAA,UACE2D,QAAUL,IACRA,EAAEwB,iBACFhF,QAAQC,IAAI,4CAt6BtBD,QAAQC,IAAI,sCAA6BrF,GACrCA,GACFqK,EAAAA,EAAAA,kBAAgB,KACdzK,EAAS,SAADmC,OAAU/B,EAAE,SAAQ,KAG9BoF,QAAQC,IAAI,uDACZgF,EAAAA,EAAAA,kBAAgB,KACdzK,EAAS,aAAa,IA+5BE,EAEpB2F,UAAU,yNACVC,MAAO,CACLC,QAAStC,OAAOuC,YAAc,IAAM,YAAc,YAClDM,SAAU7C,OAAOuC,YAAc,IAAM,OAAS,QAEhDhD,KAAK,SAAQiD,SAAA,EAEbG,EAAAA,EAAAA,KAACgB,EAAAA,IAAQ,CACPtB,MAAO,CACLW,MAAOhD,OAAOuC,YAAc,IAAM,OAAS,OAC3CU,OAAQjD,OAAOuC,YAAc,IAAM,OAAS,UAE9C,yBAKJ,C", "sources": ["pages/user/Quiz/QuizResult.js"], "sourcesContent": ["import React, { startTransition, useEffect, useState } from 'react';\nimport { useNavigate, useLocation, useParams } from 'react-router-dom';\nimport { useSelector } from 'react-redux';\nimport { TbTrophy, TbCheck, TbX, TbHome, TbStar, TbBrain, TbChartBar } from 'react-icons/tb';\nimport { chatWithChatGPTToExplainAns } from '../../../apicalls/chat';\nimport ContentRenderer from '../../../components/ContentRenderer';\n\nconst QuizResult = () => {\n  const navigate = useNavigate();\n  const location = useLocation();\n  const { id } = useParams();\n  const { user } = useSelector((state) => state.user);\n  // Get result data from navigation state first\n  const resultData = location.state || {\n    percentage: 0,\n    correctAnswers: 0,\n    totalQuestions: 0,\n    timeTaken: 0,\n    resultDetails: [],\n    xpData: null,\n    quizName: 'Quiz',\n    quizSubject: 'General',\n    passingPercentage: 60,\n    verdict: 'Fail'\n  };\n\n  const {\n    percentage,\n    correctAnswers,\n    totalQuestions,\n    timeTaken,\n    xpData,\n    quizName,\n    quizSubject,\n    passingPercentage,\n    verdict,\n    resultDetails\n  } = resultData;\n  const isPassed = verdict === 'Pass' || percentage >= (passingPercentage || 60);\n\n  const [confetti, setConfetti] = useState([]);\n  const [explanations, setExplanations] = useState({});\n  const [loadingExplanations, setLoadingExplanations] = useState({});\n  const [isFlashing, setIsFlashing] = useState(false);\n\n  // Play sound and trigger animations when component loads\n  useEffect(() => {\n\n    // Play enhanced sound effects and trigger flash animation\n    const playSound = () => {\n      // Trigger premium flash animation\n      setIsFlashing(true);\n      setTimeout(() => setIsFlashing(false), 3200); // Flash for 3.2 seconds (2 cycles of 0.8s each)\n\n      try {\n        if (isPassed) {\n          // Success sound with clapping\n          const audioContext = new (window.AudioContext || window.webkitAudioContext)();\n\n          // Create a success melody\n          const playTone = (frequency, startTime, duration, type = 'sine') => {\n            const oscillator = audioContext.createOscillator();\n            const gainNode = audioContext.createGain();\n\n            oscillator.connect(gainNode);\n            gainNode.connect(audioContext.destination);\n\n            oscillator.frequency.setValueAtTime(frequency, startTime);\n            oscillator.type = type;\n\n            gainNode.gain.setValueAtTime(0, startTime);\n            gainNode.gain.linearRampToValueAtTime(0.1, startTime + 0.01);\n            gainNode.gain.exponentialRampToValueAtTime(0.01, startTime + duration);\n\n            oscillator.start(startTime);\n            oscillator.stop(startTime + duration);\n          };\n\n          // Create clapping sound effect\n          const createClap = (startTime) => {\n            const noise = audioContext.createBufferSource();\n            const buffer = audioContext.createBuffer(1, audioContext.sampleRate * 0.1, audioContext.sampleRate);\n            const data = buffer.getChannelData(0);\n\n            // Generate white noise for clap\n            for (let i = 0; i < data.length; i++) {\n              data[i] = Math.random() * 2 - 1;\n            }\n\n            noise.buffer = buffer;\n\n            const filter = audioContext.createBiquadFilter();\n            filter.type = 'highpass';\n            filter.frequency.setValueAtTime(1000, startTime);\n\n            const gainNode = audioContext.createGain();\n            gainNode.gain.setValueAtTime(0, startTime);\n            gainNode.gain.linearRampToValueAtTime(0.3, startTime + 0.01);\n            gainNode.gain.exponentialRampToValueAtTime(0.01, startTime + 0.1);\n\n            noise.connect(filter);\n            filter.connect(gainNode);\n            gainNode.connect(audioContext.destination);\n\n            noise.start(startTime);\n            noise.stop(startTime + 0.1);\n          };\n\n          const now = audioContext.currentTime;\n\n          // Play success melody: C-E-G-C (major chord progression)\n          playTone(523.25, now, 0.2); // C5\n          playTone(659.25, now + 0.1, 0.2); // E5\n          playTone(783.99, now + 0.2, 0.2); // G5\n          playTone(1046.5, now + 0.3, 0.4); // C6\n\n          // Add clapping sounds\n          createClap(now + 0.5);\n          createClap(now + 0.7);\n          createClap(now + 0.9);\n          createClap(now + 1.1);\n\n        } else {\n          // Fail sound - create a gentle, encouraging tone\n          const audioContext = new (window.AudioContext || window.webkitAudioContext)();\n\n          const playTone = (frequency, startTime, duration) => {\n            const oscillator = audioContext.createOscillator();\n            const gainNode = audioContext.createGain();\n\n            oscillator.connect(gainNode);\n            gainNode.connect(audioContext.destination);\n\n            oscillator.frequency.setValueAtTime(frequency, startTime);\n            oscillator.type = 'sine';\n\n            gainNode.gain.setValueAtTime(0, startTime);\n            gainNode.gain.linearRampToValueAtTime(0.08, startTime + 0.01);\n            gainNode.gain.exponentialRampToValueAtTime(0.01, startTime + duration);\n\n            oscillator.start(startTime);\n            oscillator.stop(startTime + duration);\n          };\n\n          // Play gentle fail tone: A-F (not harsh, encouraging)\n          const now = audioContext.currentTime;\n          playTone(440, now, 0.3); // A4\n          playTone(349.23, now + 0.2, 0.4); // F4\n        }\n      } catch (error) {\n        console.log('Audio not supported');\n      }\n    };\n\n    // Generate premium animations based on pass/fail\n    if (isPassed) {\n      // Premium confetti explosion\n      const premiumConfetti = [];\n\n      // Main confetti burst (200 pieces)\n      for (let i = 0; i < 200; i++) {\n        const colors = [\n          '#FFD700', '#FFA500', '#FF6B6B', '#4ECDC4', '#45B7D1',\n          '#96CEB4', '#FF69B4', '#32CD32', '#FF4500', '#9370DB',\n          '#00CED1', '#FF1493', '#00FF7F', '#FF8C00', '#DA70D6'\n        ];\n\n        premiumConfetti.push({\n          id: `confetti_${i}`,\n          left: 20 + Math.random() * 60, // More centered\n          delay: Math.random() * 2,\n          duration: 4 + Math.random() * 3,\n          color: colors[Math.floor(Math.random() * colors.length)],\n          shape: ['circle', 'square', 'triangle'][Math.floor(Math.random() * 3)],\n          size: 3 + Math.random() * 6,\n          type: 'confetti',\n          randomX: (Math.random() - 0.5) * 200 // For burst effect\n        });\n      }\n\n      // Premium sparkles (50 pieces)\n      for (let i = 0; i < 50; i++) {\n        premiumConfetti.push({\n          id: `sparkle_${i}`,\n          left: Math.random() * 100,\n          delay: Math.random() * 3,\n          duration: 2 + Math.random() * 2,\n          color: ['#FFD700', '#FFFFFF', '#FFF700', '#FFFF00'][Math.floor(Math.random() * 4)],\n          size: 1 + Math.random() * 3,\n          type: 'sparkle'\n        });\n      }\n\n      // Burst confetti (100 pieces from center)\n      for (let i = 0; i < 100; i++) {\n        premiumConfetti.push({\n          id: `burst_${i}`,\n          left: 45 + Math.random() * 10, // Center burst\n          delay: Math.random() * 0.5,\n          duration: 3 + Math.random() * 2,\n          color: ['#FFD700', '#FF6B6B', '#4ECDC4', '#FF69B4'][Math.floor(Math.random() * 4)],\n          shape: 'circle',\n          size: 2 + Math.random() * 4,\n          type: 'burst',\n          randomX: (Math.random() - 0.5) * 300\n        });\n      }\n\n      setConfetti(premiumConfetti);\n\n      // Remove all animations after 10 seconds\n      setTimeout(() => setConfetti([]), 10000);\n    } else {\n      // Premium motivational elements\n      const motivationalElements = [];\n      const motivationalEmojis = ['💪', '🌟', '📚', '🎯', '🚀', '💡', '⭐', '✨', '🔥', '💎'];\n\n      for (let i = 0; i < 30; i++) {\n        motivationalElements.push({\n          id: `motivate_${i}`,\n          left: Math.random() * 100,\n          delay: Math.random() * 4,\n          duration: 5 + Math.random() * 3,\n          emoji: motivationalEmojis[Math.floor(Math.random() * motivationalEmojis.length)],\n          isMotivational: true,\n          size: 2 + Math.random() * 2\n        });\n      }\n      setConfetti(motivationalElements);\n\n      // Remove motivational elements after 8 seconds\n      setTimeout(() => setConfetti([]), 8000);\n    }\n\n    playSound();\n  }, [isPassed]);\n\n\n\n  // Function to fetch explanation\n  const fetchExplanation = async (questionIndex, detail) => {\n    const questionKey = `question_${questionIndex}`;\n\n    // Don't fetch if already loading or already have explanation\n    if (loadingExplanations[questionKey] || explanations[questionKey]) {\n      return;\n    }\n\n    try {\n      setLoadingExplanations(prev => ({ ...prev, [questionKey]: true }));\n\n      const response = await chatWithChatGPTToExplainAns({\n        question: detail.questionText || detail.questionName,\n        expectedAnswer: detail.correctAnswer,\n        userAnswer: detail.userAnswer,\n        imageUrl: detail.questionImage || detail.image || detail.imageUrl || null\n      });\n\n      if (response.success) {\n        setExplanations(prev => ({\n          ...prev,\n          [questionKey]: response.explanation\n        }));\n      } else {\n        console.error('Failed to fetch explanation:', response.error);\n        setExplanations(prev => ({\n          ...prev,\n          [questionKey]: 'Sorry, we could not generate an explanation at this time. Please try again later.'\n        }));\n      }\n    } catch (error) {\n      console.error('Error fetching explanation:', error);\n      setExplanations(prev => ({\n        ...prev,\n        [questionKey]: 'Sorry, we could not generate an explanation at this time. Please try again later.'\n      }));\n    } finally {\n      setLoadingExplanations(prev => ({ ...prev, [questionKey]: false }));\n    }\n  };\n\n\n\n  const handleBackToQuizzes = () => {\n    console.log('🏠 Navigating to quiz listing...');\n    startTransition(() => {\n      navigate('/user/quiz');\n    });\n  };\n\n  const handleRetakeQuiz = () => {\n    console.log('🔄 Retaking quiz with ID:', id);\n    if (id) {\n      startTransition(() => {\n        navigate(`/quiz/${id}/play`);\n      });\n    } else {\n      console.log('❌ No quiz ID available, going to quiz listing');\n      startTransition(() => {\n        navigate('/user/quiz');\n      });\n    }\n  };\n\n  return (\n    <div className={`min-h-screen flex items-center justify-center relative overflow-hidden ${\n      isPassed\n        ? 'bg-gradient-to-br from-green-50 via-emerald-50 to-teal-100'\n        : 'bg-gradient-to-br from-red-50 via-pink-50 to-orange-100'\n    } ${isFlashing ? (isPassed ? 'flash-green' : 'flash-red') : ''}`}\n    style={{\n      padding: window.innerWidth <= 768 ? '8px' : window.innerWidth <= 1024 ? '16px' : '24px'\n    }}>\n\n      {/* Premium Confetti System */}\n      {confetti.map((piece) => {\n        if (piece.isMotivational) {\n          return (\n            <div\n              key={piece.id}\n              className=\"absolute opacity-90\"\n              style={{\n                left: `${piece.left}%`,\n                top: `${20 + Math.random() * 60}%`,\n                fontSize: `${piece.size || 2}rem`,\n                animation: `heart-beat ${piece.duration}s ease-in-out ${piece.delay}s infinite`,\n                zIndex: 100\n              }}\n            >\n              {piece.emoji}\n            </div>\n          );\n        }\n\n        if (piece.type === 'sparkle') {\n          return (\n            <div\n              key={piece.id}\n              className=\"absolute\"\n              style={{\n                left: `${piece.left}%`,\n                width: `${piece.size}px`,\n                height: `${piece.size}px`,\n                background: `radial-gradient(circle, ${piece.color}, transparent)`,\n                borderRadius: '50%',\n                animation: `premium-sparkle ${piece.duration}s ease-in-out ${piece.delay}s infinite`,\n                top: `${Math.random() * 100}%`,\n                boxShadow: `0 0 ${piece.size * 2}px ${piece.color}`,\n                zIndex: 100\n              }}\n            />\n          );\n        }\n\n        if (piece.type === 'burst') {\n          return (\n            <div\n              key={piece.id}\n              className=\"absolute opacity-90\"\n              style={{\n                left: `${piece.left}%`,\n                width: `${piece.size}px`,\n                height: `${piece.size}px`,\n                backgroundColor: piece.color,\n                borderRadius: piece.shape === 'circle' ? '50%' : piece.shape === 'triangle' ? '0' : '0%',\n                clipPath: piece.shape === 'triangle' ? 'polygon(50% 0%, 0% 100%, 100% 100%)' : 'none',\n                animation: `confetti-burst ${piece.duration}s ease-out ${piece.delay}s forwards`,\n                top: '40%',\n                '--random-x': `${piece.randomX}px`,\n                boxShadow: `0 0 ${piece.size}px ${piece.color}40`,\n                zIndex: 100\n              }}\n            />\n          );\n        }\n\n        // Regular premium confetti\n        return (\n          <div\n            key={piece.id}\n            className=\"absolute opacity-90\"\n            style={{\n              left: `${piece.left}%`,\n              width: `${piece.size}px`,\n              height: `${piece.size}px`,\n              backgroundColor: piece.color,\n              borderRadius: piece.shape === 'circle' ? '50%' : piece.shape === 'triangle' ? '0' : '0%',\n              clipPath: piece.shape === 'triangle' ? 'polygon(50% 0%, 0% 100%, 100% 100%)' : 'none',\n              animation: `confetti-fall ${piece.duration}s ease-out ${piece.delay}s forwards`,\n              top: '-20px',\n              boxShadow: `0 0 ${piece.size}px ${piece.color}60`,\n              border: `1px solid ${piece.color}`,\n              background: `linear-gradient(45deg, ${piece.color}, ${piece.color}80)`,\n              zIndex: 100\n            }}\n          />\n        );\n      })}\n\n      {/* CSS Animations */}\n      <style jsx>{`\n        @keyframes confetti-fall {\n          0% {\n            transform: translateY(-20px) rotate(0deg) scale(0);\n            opacity: 0;\n          }\n          10% {\n            opacity: 1;\n            transform: translateY(0px) rotate(36deg) scale(1);\n          }\n          100% {\n            transform: translateY(100vh) rotate(1080deg) scale(0.3);\n            opacity: 0;\n          }\n        }\n\n        @keyframes confetti-burst {\n          0% {\n            transform: translateY(-10px) translateX(0px) rotate(0deg) scale(0);\n            opacity: 0;\n          }\n          15% {\n            transform: translateY(-50px) translateX(var(--random-x)) rotate(180deg) scale(1.2);\n            opacity: 1;\n          }\n          100% {\n            transform: translateY(100vh) translateX(calc(var(--random-x) * 2)) rotate(1440deg) scale(0);\n            opacity: 0;\n          }\n        }\n\n        @keyframes premium-sparkle {\n          0%, 100% {\n            transform: scale(0) rotate(0deg);\n            opacity: 0;\n          }\n          50% {\n            transform: scale(1.5) rotate(180deg);\n            opacity: 1;\n          }\n        }\n\n\n\n        @keyframes heart-beat {\n          0%, 100% { transform: scale(1); }\n          50% { transform: scale(1.3); }\n        }\n\n        @keyframes text-bounce {\n          0%, 20%, 50%, 80%, 100% { transform: translateY(0) scale(1); }\n          40% { transform: translateY(-20px) scale(1.1); }\n          60% { transform: translateY(-10px) scale(1.05); }\n        }\n\n        @keyframes text-glow {\n          0%, 100% {\n            text-shadow: 0 0 10px currentColor, 0 0 20px currentColor, 0 0 30px currentColor;\n            transform: scale(1);\n          }\n          50% {\n            text-shadow: 0 0 20px currentColor, 0 0 40px currentColor, 0 0 60px currentColor;\n            transform: scale(1.05);\n          }\n        }\n\n        @keyframes rainbow-text {\n          0% {\n            color: #FF6B6B;\n            text-shadow: 0 0 20px #FF6B6B, 0 0 40px #FF6B6B;\n          }\n          16% {\n            color: #FFD93D;\n            text-shadow: 0 0 20px #FFD93D, 0 0 40px #FFD93D;\n          }\n          33% {\n            color: #6BCF7F;\n            text-shadow: 0 0 20px #6BCF7F, 0 0 40px #6BCF7F;\n          }\n          50% {\n            color: #4D96FF;\n            text-shadow: 0 0 20px #4D96FF, 0 0 40px #4D96FF;\n          }\n          66% {\n            color: #9B59B6;\n            text-shadow: 0 0 20px #9B59B6, 0 0 40px #9B59B6;\n          }\n          83% {\n            color: #FF69B4;\n            text-shadow: 0 0 20px #FF69B4, 0 0 40px #FF69B4;\n          }\n          100% {\n            color: #FF6B6B;\n            text-shadow: 0 0 20px #FF6B6B, 0 0 40px #FF6B6B;\n          }\n        }\n\n        @keyframes shake-celebrate {\n          0%, 100% { transform: translateX(0) rotate(0deg); }\n          10% { transform: translateX(-5px) rotate(-1deg); }\n          20% { transform: translateX(5px) rotate(1deg); }\n          30% { transform: translateX(-5px) rotate(-1deg); }\n          40% { transform: translateX(5px) rotate(1deg); }\n          50% { transform: translateX(-3px) rotate(-0.5deg); }\n          60% { transform: translateX(3px) rotate(0.5deg); }\n          70% { transform: translateX(-2px) rotate(-0.5deg); }\n          80% { transform: translateX(2px) rotate(0.5deg); }\n          90% { transform: translateX(-1px) rotate(0deg); }\n        }\n\n        @keyframes zoom-in-out {\n          0%, 100% { transform: scale(1); }\n          50% { transform: scale(1.2); }\n        }\n\n        .animate-text-bounce {\n          animation: text-bounce 2s ease-in-out infinite;\n        }\n\n        .animate-text-glow {\n          animation: text-glow 2s ease-in-out infinite;\n        }\n\n        .animate-rainbow {\n          animation: rainbow-text 3s linear infinite;\n        }\n\n        .animate-shake-celebrate {\n          animation: shake-celebrate 1s ease-in-out infinite;\n        }\n\n        .animate-zoom {\n          animation: zoom-in-out 2s ease-in-out infinite;\n        }\n\n        @keyframes elegant-scale {\n          0%, 100% {\n            transform: scale(1) rotateY(0deg);\n            text-shadow: 0 0 20px currentColor;\n          }\n          50% {\n            transform: scale(1.1) rotateY(5deg);\n            text-shadow: 0 0 40px currentColor, 0 0 60px currentColor;\n          }\n        }\n\n        @keyframes premium-pulse {\n          0%, 100% {\n            transform: scale(1);\n            opacity: 1;\n          }\n          50% {\n            transform: scale(1.05);\n            opacity: 0.9;\n          }\n        }\n\n        @keyframes smooth-glow {\n          0%, 100% {\n            text-shadow: 0 0 15px currentColor, 0 0 30px currentColor, 0 0 45px currentColor;\n            filter: brightness(1) saturate(1.2);\n          }\n          50% {\n            text-shadow: 0 0 40px currentColor, 0 0 60px currentColor, 0 0 80px currentColor, 0 0 100px currentColor;\n            filter: brightness(1.3) saturate(1.5);\n          }\n        }\n\n        @keyframes rainbow-glow {\n          0% {\n            color: #FF6B6B;\n            text-shadow: 0 0 20px #FF6B6B, 0 0 40px #FF6B6B, 0 0 60px #FF6B6B;\n            filter: brightness(1.2) saturate(1.3);\n          }\n          16% {\n            color: #FFD93D;\n            text-shadow: 0 0 20px #FFD93D, 0 0 40px #FFD93D, 0 0 60px #FFD93D;\n            filter: brightness(1.2) saturate(1.3);\n          }\n          33% {\n            color: #6BCF7F;\n            text-shadow: 0 0 20px #6BCF7F, 0 0 40px #6BCF7F, 0 0 60px #6BCF7F;\n            filter: brightness(1.2) saturate(1.3);\n          }\n          50% {\n            color: #4D96FF;\n            text-shadow: 0 0 20px #4D96FF, 0 0 40px #4D96FF, 0 0 60px #4D96FF;\n            filter: brightness(1.2) saturate(1.3);\n          }\n          66% {\n            color: #9B59B6;\n            text-shadow: 0 0 20px #9B59B6, 0 0 40px #9B59B6, 0 0 60px #9B59B6;\n            filter: brightness(1.2) saturate(1.3);\n          }\n          83% {\n            color: #FF69B4;\n            text-shadow: 0 0 20px #FF69B4, 0 0 40px #FF69B4, 0 0 60px #FF69B4;\n            filter: brightness(1.2) saturate(1.3);\n          }\n          100% {\n            color: #FF6B6B;\n            text-shadow: 0 0 20px #FF6B6B, 0 0 40px #FF6B6B, 0 0 60px #FF6B6B;\n            filter: brightness(1.2) saturate(1.3);\n          }\n        }\n\n        @keyframes celebration-bounce {\n          0%, 20%, 50%, 80%, 100% {\n            transform: translateY(0) scale(1);\n          }\n          40% {\n            transform: translateY(-15px) scale(1.05);\n          }\n          60% {\n            transform: translateY(-8px) scale(1.02);\n          }\n        }\n\n        .animate-elegant {\n          animation: elegant-scale 3s ease-in-out infinite;\n        }\n\n        .animate-premium-pulse {\n          animation: premium-pulse 2s ease-in-out infinite;\n        }\n\n        .animate-smooth-glow {\n          animation: smooth-glow 2.5s ease-in-out infinite;\n        }\n\n        .animate-celebration {\n          animation: celebration-bounce 2s ease-in-out infinite;\n        }\n\n        .animate-rainbow {\n          animation: rainbow-text 3s linear infinite;\n        }\n\n        .animate-rainbow-glow {\n          animation: rainbow-glow 3s linear infinite;\n        }\n\n        @keyframes red-glow {\n          0%, 100% {\n            color: #EF4444;\n            text-shadow: 0 0 20px #EF4444, 0 0 40px #EF4444, 0 0 60px #EF4444;\n            filter: brightness(1.2) saturate(1.3);\n          }\n          25% {\n            color: #DC2626;\n            text-shadow: 0 0 25px #DC2626, 0 0 50px #DC2626, 0 0 75px #DC2626;\n            filter: brightness(1.3) saturate(1.4);\n          }\n          50% {\n            color: #B91C1C;\n            text-shadow: 0 0 30px #B91C1C, 0 0 60px #B91C1C, 0 0 90px #B91C1C;\n            filter: brightness(1.4) saturate(1.5);\n          }\n          75% {\n            color: #DC2626;\n            text-shadow: 0 0 25px #DC2626, 0 0 50px #DC2626, 0 0 75px #DC2626;\n            filter: brightness(1.3) saturate(1.4);\n          }\n        }\n\n        .animate-red-glow {\n          animation: red-glow 2.5s ease-in-out infinite;\n        }\n\n        @keyframes premium-green-flash {\n          0% {\n            background: linear-gradient(45deg, transparent, transparent);\n            box-shadow: none;\n          }\n          25% {\n            background: linear-gradient(45deg, rgba(34, 197, 94, 0.1), rgba(16, 185, 129, 0.1));\n            box-shadow: inset 0 0 100px rgba(34, 197, 94, 0.2);\n          }\n          50% {\n            background: linear-gradient(45deg, rgba(34, 197, 94, 0.4), rgba(16, 185, 129, 0.4));\n            box-shadow: inset 0 0 200px rgba(34, 197, 94, 0.5), 0 0 50px rgba(34, 197, 94, 0.3);\n          }\n          75% {\n            background: linear-gradient(45deg, rgba(34, 197, 94, 0.2), rgba(16, 185, 129, 0.2));\n            box-shadow: inset 0 0 150px rgba(34, 197, 94, 0.3);\n          }\n          100% {\n            background: linear-gradient(45deg, transparent, transparent);\n            box-shadow: none;\n          }\n        }\n\n        @keyframes premium-red-flash {\n          0% {\n            background: linear-gradient(45deg, transparent, transparent);\n            box-shadow: none;\n          }\n          25% {\n            background: linear-gradient(45deg, rgba(239, 68, 68, 0.1), rgba(220, 38, 127, 0.1));\n            box-shadow: inset 0 0 100px rgba(239, 68, 68, 0.2);\n          }\n          50% {\n            background: linear-gradient(45deg, rgba(239, 68, 68, 0.4), rgba(220, 38, 127, 0.4));\n            box-shadow: inset 0 0 200px rgba(239, 68, 68, 0.5), 0 0 50px rgba(239, 68, 68, 0.3);\n          }\n          75% {\n            background: linear-gradient(45deg, rgba(239, 68, 68, 0.2), rgba(220, 38, 127, 0.2));\n            box-shadow: inset 0 0 150px rgba(239, 68, 68, 0.3);\n          }\n          100% {\n            background: linear-gradient(45deg, transparent, transparent);\n            box-shadow: none;\n          }\n        }\n\n        .flash-green {\n          animation: premium-green-flash 0.8s ease-in-out 2;\n        }\n\n        .flash-red {\n          animation: premium-red-flash 0.8s ease-in-out 2;\n        }\n      `}</style>\n\n      {/* Premium Overlay Effect */}\n      {isFlashing && (\n        <div\n          className=\"fixed inset-0 pointer-events-none\"\n          style={{\n            background: isPassed\n              ? 'radial-gradient(circle at center, rgba(34, 197, 94, 0.3) 0%, rgba(16, 185, 129, 0.2) 50%, transparent 100%)'\n              : 'radial-gradient(circle at center, rgba(239, 68, 68, 0.3) 0%, rgba(220, 38, 127, 0.2) 50%, transparent 100%)',\n            animation: isPassed ? 'premium-green-flash 0.8s ease-in-out infinite' : 'premium-red-flash 0.8s ease-in-out infinite',\n            zIndex: 5\n          }}\n        />\n      )}\n\n      <div className={`bg-white rounded-2xl shadow-2xl border-2 w-full relative ${\n        isPassed ? 'border-green-400 shadow-green-200' : 'border-red-400 shadow-red-200'\n      } ${isFlashing ? 'shadow-3xl' : ''}`}\n      style={{\n        background: isFlashing\n          ? (isPassed\n              ? 'linear-gradient(135deg, rgba(255,255,255,0.95), rgba(34, 197, 94, 0.05))'\n              : 'linear-gradient(135deg, rgba(255,255,255,0.95), rgba(239, 68, 68, 0.05))')\n          : 'white',\n        boxShadow: isFlashing\n          ? (isPassed\n              ? '0 25px 50px rgba(34, 197, 94, 0.3), 0 0 100px rgba(34, 197, 94, 0.2)'\n              : '0 25px 50px rgba(239, 68, 68, 0.3), 0 0 100px rgba(239, 68, 68, 0.2)')\n          : '0 25px 50px rgba(0,0,0,0.15)',\n        zIndex: 10,\n        padding: window.innerWidth <= 768 ? '16px' : window.innerWidth <= 1024 ? '24px' : '32px',\n        maxWidth: window.innerWidth <= 768 ? '100%' : window.innerWidth <= 1024 ? '90%' : '800px'\n      }}>\n        {/* Header */}\n        <div\n          className=\"text-center\"\n          style={{ marginBottom: window.innerWidth <= 768 ? '16px' : '32px' }}\n        >\n          <div\n            className={`inline-flex items-center justify-center rounded-full mb-4 relative ${\n              isPassed ? 'bg-gradient-to-br from-green-100 to-emerald-100' : 'bg-gradient-to-br from-red-100 to-pink-100'\n            }`}\n            style={{\n              width: window.innerWidth <= 768 ? '60px' : '96px',\n              height: window.innerWidth <= 768 ? '60px' : '96px'\n            }}\n          >\n            <TbTrophy\n              className={`${isPassed ? 'text-yellow-500' : 'text-gray-500'}`}\n              style={{\n                width: window.innerWidth <= 768 ? '30px' : '48px',\n                height: window.innerWidth <= 768 ? '30px' : '48px'\n              }}\n            />\n          </div>\n\n          <h1\n            className={`font-bold mb-4 ${\n              isPassed\n                ? 'text-green-600 animate-elegant animate-smooth-glow'\n                : 'animate-premium-pulse animate-red-glow'\n            }`}\n            style={{\n              fontSize: window.innerWidth <= 768 ? '24px' : window.innerWidth <= 1024 ? '36px' : '48px'\n            }}\n          >\n            {isPassed ? (\n              <span\n                className=\"flex items-center justify-center\"\n                style={{ gap: window.innerWidth <= 768 ? '8px' : '16px', flexWrap: 'wrap' }}\n              >\n                <span\n                  className=\"animate-celebration\"\n                  style={{ fontSize: window.innerWidth <= 768 ? '32px' : '56px' }}\n                >🎉</span>\n                <span className=\"animate-rainbow-glow animate-elegant\">Congratulations!</span>\n                <span\n                  className=\"animate-celebration\"\n                  style={{ fontSize: window.innerWidth <= 768 ? '32px' : '56px' }}\n                >🎉</span>\n              </span>\n            ) : (\n              <span\n                className=\"flex items-center justify-center\"\n                style={{ gap: window.innerWidth <= 768 ? '8px' : '16px', flexWrap: 'wrap' }}\n              >\n                <span\n                  className=\"animate-premium-pulse\"\n                  style={{ fontSize: window.innerWidth <= 768 ? '32px' : '56px' }}\n                >💪</span>\n                <span className=\"animate-red-glow animate-elegant\">Keep Going!</span>\n                <span\n                  className=\"animate-premium-pulse\"\n                  style={{ fontSize: window.innerWidth <= 768 ? '32px' : '56px' }}\n                >💪</span>\n              </span>\n            )}\n          </h1>\n\n          <div className={`text-3xl font-bold mb-4 ${\n            isPassed\n              ? 'animate-celebration animate-rainbow-glow'\n              : 'animate-premium-pulse animate-red-glow'\n          }`}>\n            {isPassed ? (\n              <span className=\"animate-elegant animate-rainbow-glow\">✨ You Passed! ✨</span>\n            ) : (\n              <span className=\"animate-red-glow animate-elegant\">🌟 You Can Do It! 🌟</span>\n            )}\n          </div>\n\n          <p className=\"text-gray-600 mt-2 text-lg\">\n            📚 {quizName} - {quizSubject}\n          </p>\n        </div>\n\n        {/* Score Display */}\n        <div className=\"text-center mb-8\">\n          <div className={`inline-block px-8 py-4 rounded-2xl ${\n            isPassed ? 'bg-green-50 border-2 border-green-200' : 'bg-red-50 border-2 border-red-200'\n          }`}>\n            <div className={`text-5xl font-bold mb-2 ${\n              isPassed ? 'text-green-600' : 'text-red-600'\n            }`}>\n              {percentage}%\n            </div>\n            <div className=\"text-gray-600\">\n              Your Score\n            </div>\n          </div>\n        </div>\n\n\n\n        {/* Horizontal Compact Results */}\n        <div\n          className=\"flex gap-2 mb-2 justify-center items-center\"\n          style={{ flexWrap: 'wrap' }}\n        >\n          {/* Correct and Wrong - Horizontal */}\n          <div\n            style={{\n              backgroundColor: '#ffffff',\n              padding: '4px 8px',\n              border: '1px solid #e5e7eb',\n              borderRadius: '6px',\n              display: 'flex',\n              alignItems: 'center',\n              gap: '8px',\n              fontSize: '12px'\n            }}\n          >\n            <div style={{ display: 'flex', alignItems: 'center', gap: '2px' }}>\n              <span style={{ fontSize: '10px' }}>✅</span>\n              <span style={{ fontSize: '14px', fontWeight: 'bold', color: '#16a34a' }}>{correctAnswers}</span>\n            </div>\n            <div style={{ display: 'flex', alignItems: 'center', gap: '2px' }}>\n              <span style={{ fontSize: '10px' }}>❌</span>\n              <span style={{ fontSize: '14px', fontWeight: 'bold', color: '#dc2626' }}>{totalQuestions - correctAnswers}</span>\n            </div>\n          </div>\n\n          {/* Score */}\n          <div\n            style={{\n              backgroundColor: '#ffffff',\n              padding: '4px 8px',\n              border: '1px solid #e5e7eb',\n              borderRadius: '6px',\n              display: 'flex',\n              alignItems: 'center',\n              gap: '4px',\n              fontSize: '12px'\n            }}\n          >\n            <span style={{ fontSize: '10px' }}>📊</span>\n            <span style={{ fontSize: '14px', fontWeight: 'bold', color: isPassed ? '#16a34a' : '#dc2626' }}>{percentage}%</span>\n          </div>\n\n          {/* Time - Only if available */}\n          {timeTaken && timeTaken > 0 && (\n            <div\n              style={{\n                backgroundColor: '#ffffff',\n                padding: '4px 8px',\n                border: '1px solid #e5e7eb',\n                borderRadius: '6px',\n                display: 'flex',\n                alignItems: 'center',\n                gap: '4px',\n                fontSize: '12px'\n              }}\n            >\n              <span style={{ fontSize: '10px' }}>⏱️</span>\n              <span style={{ fontSize: '14px', fontWeight: 'bold', color: '#2563eb' }}>\n                {Math.floor(timeTaken / 60)}:{(timeTaken % 60).toString().padStart(2, '0')}\n              </span>\n            </div>\n          )}\n        </div>\n\n        {/* Streamlined XP Section */}\n        {xpData && (\n          <div className=\"bg-gradient-to-r from-purple-50 to-indigo-50 rounded-xl p-4 mb-6 border border-purple-200\">\n            <div className=\"flex items-center justify-between\">\n              <div className=\"flex items-center gap-3\">\n                <div className=\"w-12 h-12 bg-purple-500 rounded-full flex items-center justify-center\">\n                  <TbStar className=\"w-6 h-6 text-white\" />\n                </div>\n                <div>\n                  <div className=\"text-purple-600 font-bold text-xl\">+{xpData.xpAwarded || 0} XP</div>\n                  <div className=\"text-sm text-gray-600\">Earned</div>\n                </div>\n              </div>\n\n              <div className=\"text-right\">\n                <div className=\"text-2xl font-bold text-indigo-600\">\n                  {((user?.totalXP || 0) + (xpData.xpAwarded || 0)).toLocaleString()}\n                </div>\n                <div className=\"text-sm text-gray-600\">Total XP • Level {user?.currentLevel || 1}</div>\n              </div>\n            </div>\n          </div>\n        )}\n\n        {/* Streamlined XP Section (for users without XP data) */}\n        {!xpData && user && (\n          <div className=\"bg-gradient-to-r from-indigo-50 to-purple-50 rounded-xl p-4 mb-6 border border-indigo-200\">\n            <div className=\"flex items-center justify-between\">\n              <div className=\"flex items-center gap-3\">\n                <div className=\"w-12 h-12 bg-indigo-500 rounded-full flex items-center justify-center\">\n                  <TbStar className=\"w-6 h-6 text-white\" />\n                </div>\n                <div>\n                  <div className=\"text-sm text-gray-600\">Your Progress</div>\n                </div>\n              </div>\n\n              <div className=\"text-right\">\n                <div className=\"text-2xl font-bold text-indigo-600\">\n                  {(user.totalXP || 0).toLocaleString()}\n                </div>\n                <div className=\"text-sm text-gray-600\">Total XP • Level {user.currentLevel || 1}</div>\n              </div>\n            </div>\n          </div>\n        )}\n\n        {/* Learning Summary Section */}\n        <div className=\"bg-gradient-to-r from-blue-50 to-cyan-50 rounded-xl p-6 mb-6 border border-blue-200\">\n          <div className=\"flex items-center gap-3 mb-4\">\n            <div className=\"w-10 h-10 bg-blue-500 rounded-full flex items-center justify-center\">\n              <TbBrain className=\"w-6 h-6 text-white\" />\n            </div>\n            <h3 className=\"text-lg font-semibold text-gray-900\">📚 Learning Summary</h3>\n          </div>\n\n          {/* Simplified Learning Summary - Only Question Review */}\n        </div>\n\n        {/* Detailed Question Breakdown */}\n        {resultDetails && resultDetails.length > 0 && (\n          <div className=\"bg-gradient-to-r from-gray-50 to-slate-50 rounded-xl p-6 mb-6 border border-gray-200\">\n            <div className=\"flex items-center gap-3 mb-4\">\n              <div className=\"w-10 h-10 bg-gray-600 rounded-full flex items-center justify-center\">\n                <TbChartBar className=\"w-6 h-6 text-white\" />\n              </div>\n              <h3 className=\"text-lg font-semibold text-gray-900\">📋 Question by Question Review</h3>\n            </div>\n\n            <div className=\"space-y-4 max-h-96 overflow-y-auto\">\n              {resultDetails.map((detail, index) => {\n                // Debug: Log question data to see what's available\n                console.log(`Question ${index + 1} data:`, detail);\n                return (\n                <div\n                  key={detail.questionId || index}\n                  className={`rounded-xl overflow-hidden transition-all duration-300 hover:shadow-xl ${\n                    detail.isCorrect\n                      ? 'bg-gradient-to-r from-green-100 to-emerald-100 shadow-lg shadow-green-300'\n                      : 'bg-gradient-to-r from-red-100 to-pink-100 shadow-lg shadow-red-300'\n                  }`}\n                  style={{\n                    border: detail.isCorrect\n                      ? '4px solid #16a34a' // Green border for correct answers\n                      : '4px solid #dc2626', // Red border for wrong answers\n                    boxShadow: detail.isCorrect\n                      ? '0 10px 25px rgba(34, 197, 94, 0.4), 0 0 0 2px rgba(34, 197, 94, 0.2)'\n                      : '0 10px 25px rgba(239, 68, 68, 0.4), 0 0 0 2px rgba(239, 68, 68, 0.2)'\n                  }}\n                >\n                  {/* Question Header */}\n                  <div className={`p-4 ${\n                    detail.isCorrect\n                      ? 'bg-green-300 border-b-4 border-green-500'\n                      : 'bg-red-300 border-b-4 border-red-500'\n                  }`}>\n                    <div className=\"flex items-center gap-3\">\n                      <div className={`w-10 h-10 rounded-full flex items-center justify-center font-bold ${\n                        detail.isCorrect\n                          ? 'bg-green-500 text-white shadow-lg'\n                          : 'bg-red-500 text-white shadow-lg'\n                      }`}>\n                        {detail.isCorrect ? <TbCheck className=\"w-5 h-5\" /> : <TbX className=\"w-5 h-5\" />}\n                      </div>\n\n                      <div className=\"flex-1\">\n                        <h4 className=\"font-bold text-gray-900 text-lg\">\n                          Question {index + 1}\n                        </h4>\n                        <div className=\"flex items-center gap-2 mt-1\">\n                          <span className={`px-3 py-1 rounded-full text-xs font-bold ${\n                            detail.isCorrect\n                              ? 'bg-green-500 text-white'\n                              : 'bg-red-500 text-white'\n                          }`}>\n                            {detail.isCorrect ? '✅ CORRECT' : '❌ WRONG'}\n                          </span>\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n\n                  {/* Question Content */}\n                  <div className=\"p-4\">\n                    <div className=\"mb-4\">\n                      <p className=\"text-gray-700 bg-white p-3 rounded-lg border\">\n                        {detail.questionText || detail.questionName}\n                      </p>\n                    </div>\n\n                    {/* Show Image for image questions or any question with an image */}\n                    {(detail.questionType === 'image' || detail.questionImage || detail.image || detail.imageUrl) && (detail.questionImage || detail.image || detail.imageUrl) && (\n                      <div className=\"mb-4\">\n                        <div className={`p-3 rounded-lg border-2 ${\n                          detail.isCorrect\n                            ? 'bg-green-50 border-green-200'\n                            : 'bg-red-50 border-red-200'\n                        }`}>\n                          <div className=\"flex items-center gap-2 mb-2\">\n                            <span className=\"text-sm font-semibold text-gray-700\">📷 Question Image:</span>\n                            <span className={`px-2 py-1 rounded-full text-xs font-bold ${\n                              detail.isCorrect\n                                ? 'bg-green-500 text-white'\n                                : 'bg-red-500 text-white'\n                            }`}>\n                              {detail.isCorrect ? '✅ CORRECT' : '❌ WRONG'}\n                            </span>\n                          </div>\n                          <div className=\"bg-white p-2 rounded-lg border\">\n                            <img\n                              src={detail.questionImage || detail.image || detail.imageUrl}\n                              alt=\"Question Image\"\n                              className=\"max-w-full h-auto rounded-lg shadow-sm mx-auto block\"\n                              style={{ maxHeight: '300px' }}\n                              onError={(e) => {\n                                e.target.style.display = 'none';\n                                e.target.nextSibling.style.display = 'block';\n                              }}\n                            />\n                            <div\n                              className=\"text-center text-gray-500 text-sm p-4 bg-gray-100 rounded-lg\"\n                              style={{ display: 'none' }}\n                            >\n                              📷 Image could not be loaded\n                            </div>\n                          </div>\n                        </div>\n                      </div>\n                    )}\n\n                    {/* Enhanced Answer Section with Color Indicators */}\n                    <div className=\"space-y-3\">\n                      <div\n                        className={`p-4 rounded-lg ${\n                          detail.isCorrect\n                            ? 'bg-green-50'\n                            : 'bg-red-50'\n                        }`}\n                        style={{\n                          border: detail.isCorrect\n                            ? '3px solid #16a34a' // Green border for correct answers\n                            : '3px solid #dc2626'  // Red border for wrong answers\n                        }}\n                      >\n                        <div className=\"flex items-center gap-3 mb-2\">\n                          <div className={`w-6 h-6 rounded-full flex items-center justify-center ${\n                            detail.isCorrect ? 'bg-green-500' : 'bg-red-500'\n                          }`}>\n                            {detail.isCorrect ? (\n                              <TbCheck className=\"w-4 h-4 text-white\" />\n                            ) : (\n                              <TbX className=\"w-4 h-4 text-white\" />\n                            )}\n                          </div>\n                          <span className=\"font-semibold text-gray-700\">Your Answer:</span>\n                        </div>\n                        <div className={`p-3 rounded-lg font-bold text-lg ${\n                          detail.isCorrect\n                            ? 'bg-green-100 text-green-700 border border-green-200'\n                            : 'bg-red-100 text-red-700 border border-red-200'\n                        }`}>\n                          {detail.userAnswer || 'No answer provided'}\n                        </div>\n                      </div>\n\n                      {!detail.isCorrect && (\n                        <div className=\"bg-green-50 p-4 rounded-lg border-4 border-green-500\">\n                          <div className=\"flex items-center gap-3 mb-2\">\n                            <div className=\"w-6 h-6 rounded-full bg-green-500 flex items-center justify-center\">\n                              <TbCheck className=\"w-4 h-4 text-white\" />\n                            </div>\n                            <span className=\"font-semibold text-gray-700\">Correct Answer:</span>\n                          </div>\n                          <div className=\"bg-green-100 p-3 rounded-lg border-2 border-green-400 font-bold text-lg text-green-700\">\n                            {detail.correctAnswer}\n                          </div>\n                        </div>\n                      )}\n\n                      {/* Explanation Button for Wrong Answers */}\n                      {!detail.isCorrect && (\n                        <div className=\"mt-3\">\n                          <button\n                            className={`w-full font-semibold py-3 px-4 rounded-lg transition-all duration-200 flex items-center justify-center gap-2 ${\n                              loadingExplanations[`question_${index}`]\n                                ? 'bg-gray-400 cursor-not-allowed'\n                                : 'bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white shadow-lg hover:shadow-xl'\n                            }`}\n                            onClick={() => fetchExplanation(index, detail)}\n                            disabled={loadingExplanations[`question_${index}`]}\n                          >\n                            {loadingExplanations[`question_${index}`] ? (\n                              <>\n                                <div className=\"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin\"></div>\n                                Getting Explanation...\n                              </>\n                            ) : (\n                              <>\n                                <TbBrain className=\"w-5 h-5\" />\n                                Get Explanation\n                              </>\n                            )}\n                          </button>\n\n                          {/* Explanation Display with Math Support */}\n                          {explanations[`question_${index}`] && (\n                            <div className=\"mt-3 p-4 bg-gradient-to-r from-blue-50 to-purple-50 border-2 border-blue-200 rounded-lg\">\n                              <div className=\"flex items-center gap-2 mb-3\">\n                                <TbBrain className=\"w-5 h-5 text-blue-600\" />\n                                <h6 className=\"font-bold text-blue-800\">💡 Explanation:</h6>\n                              </div>\n                              <div className=\"text-blue-700 leading-relaxed\">\n                                <ContentRenderer text={explanations[`question_${index}`]} />\n                              </div>\n                            </div>\n                          )}\n                        </div>\n                      )}\n                    </div>\n                  </div>\n                </div>\n                );\n              })}\n            </div>\n\n\n          </div>\n        )}\n\n        {/* Actions */}\n        <div\n          className=\"flex gap-4\"\n          style={{\n            flexDirection: window.innerWidth <= 768 ? 'column' : 'row'\n          }}\n        >\n          <button\n            onClick={(e) => {\n              e.preventDefault();\n              console.log('🔥 More Quizzes button clicked!');\n              handleBackToQuizzes();\n            }}\n            className=\"flex-1 flex items-center justify-center gap-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-all font-medium cursor-pointer\"\n            style={{\n              padding: window.innerWidth <= 768 ? '12px 16px' : '12px 24px',\n              fontSize: window.innerWidth <= 768 ? '14px' : '16px'\n            }}\n            type=\"button\"\n          >\n            <TbHome\n              style={{\n                width: window.innerWidth <= 768 ? '16px' : '20px',\n                height: window.innerWidth <= 768 ? '16px' : '20px'\n              }}\n            />\n            More Quizzes\n          </button>\n\n          <button\n            onClick={(e) => {\n              e.preventDefault();\n              console.log('🔥 Retake Quiz button clicked!');\n              handleRetakeQuiz();\n            }}\n            className=\"flex-1 flex items-center justify-center gap-2 bg-green-600 text-white rounded-lg hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 transition-all font-medium cursor-pointer\"\n            style={{\n              padding: window.innerWidth <= 768 ? '12px 16px' : '12px 24px',\n              fontSize: window.innerWidth <= 768 ? '14px' : '16px'\n            }}\n            type=\"button\"\n          >\n            <TbTrophy\n              style={{\n                width: window.innerWidth <= 768 ? '16px' : '20px',\n                height: window.innerWidth <= 768 ? '16px' : '20px'\n              }}\n            />\n            Retake Quiz\n          </button>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default QuizResult;\n"], "names": ["QuizResult", "navigate", "useNavigate", "location", "useLocation", "id", "useParams", "user", "useSelector", "state", "resultData", "percentage", "correctAnswers", "totalQuestions", "timeTaken", "resultDetails", "xpData", "quizName", "quizSubject", "passingPercentage", "verdict", "isPassed", "confetti", "set<PERSON>on<PERSON>tti", "useState", "explanations", "setExplanations", "loadingExplanations", "setLoadingExplanations", "isFlashing", "setIsFlashing", "useEffect", "premiumConfetti", "i", "colors", "push", "concat", "left", "Math", "random", "delay", "duration", "color", "floor", "length", "shape", "size", "type", "randomX", "setTimeout", "motivationalElements", "motivationalEmojis", "emoji", "isMotivational", "playSound", "audioContext", "window", "AudioContext", "webkitAudioContext", "playTone", "frequency", "startTime", "arguments", "undefined", "oscillator", "createOscillator", "gainNode", "createGain", "connect", "destination", "setValueAtTime", "gain", "linearRampToValueAtTime", "exponentialRampToValueAtTime", "start", "stop", "createClap", "noise", "createBufferSource", "buffer", "createBuffer", "sampleRate", "data", "getChannelData", "filter", "createBiquadFilter", "now", "currentTime", "error", "console", "log", "_jsxs", "className", "style", "padding", "innerWidth", "children", "map", "piece", "_jsx", "top", "fontSize", "animation", "zIndex", "width", "height", "background", "borderRadius", "boxShadow", "backgroundColor", "clipPath", "border", "jsx", "max<PERSON><PERSON><PERSON>", "marginBottom", "TbTrophy", "gap", "flexWrap", "display", "alignItems", "fontWeight", "toString", "padStart", "TbStar", "xpAwarded", "totalXP", "toLocaleString", "currentLevel", "TbBrain", "TbChartBar", "detail", "index", "isCorrect", "TbCheck", "TbX", "questionText", "questionName", "questionType", "questionImage", "image", "imageUrl", "src", "alt", "maxHeight", "onError", "e", "target", "nextS<PERSON>ling", "userAnswer", "<PERSON><PERSON><PERSON><PERSON>", "onClick", "async", "questionIndex", "<PERSON><PERSON><PERSON>", "prev", "_objectSpread", "response", "chatWithChatGPTToExplainAns", "question", "expectedAnswer", "success", "explanation", "fetchExplanation", "disabled", "_Fragment", "Content<PERSON><PERSON><PERSON>", "text", "questionId", "flexDirection", "preventDefault", "startTransition", "TbHome"], "sourceRoot": ""}