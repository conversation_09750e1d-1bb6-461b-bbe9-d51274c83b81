"use strict";(self.webpackChunkclient=self.webpackChunkclient||[]).push([[319],{640:(e,t,a)=>{a.d(t,{Z:()=>o});var n=a(2791),r=a(184);const o=function(e){let{title:t}=e;const[a,o]=(0,n.useState)(!1);return(0,n.useEffect)((()=>{window.innerWidth<768&&o(!0)}),[]),(0,r.jsx)("div",{className:"mt-2",children:(0,r.jsx)("h1",{className:a?"text-lg":"",children:t})})}},4319:(e,t,a)=>{a.r(t),a.d(t,{default:()=>W});var n=a(2791),r=a(8262),o=a(7027),i=a(7462);const l={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M908.1 353.1l-253.9-36.9L540.7 86.1c-3.1-6.3-8.2-11.4-14.5-14.5-15.8-7.8-35-1.3-42.9 14.5L369.8 316.2l-253.9 36.9c-7 1-13.4 4.3-18.3 9.3a32.05 32.05 0 00.6 45.3l183.7 179.1-43.4 252.9a31.95 31.95 0 0046.4 33.7L512 754l227.1 119.4c6.2 3.3 13.4 4.4 20.3 3.2 17.4-3 29.1-19.5 26.1-36.9l-43.4-252.9 183.7-179.1c5-4.9 8.3-11.3 9.3-18.3 2.7-17.5-9.5-33.7-27-36.3z"}}]},name:"star",theme:"filled"};var s=a(4291),c=function(e,t){return n.createElement(s.Z,(0,i.Z)({},e,{ref:t,icon:l}))};const u=n.forwardRef(c);var d=a(1694),f=a.n(d),m=a(4942),v=a(9439),p=a(4925),h=a(5179),g=a(1354),x=a(4170);function b(e,t){var a=e.disabled,r=e.prefixCls,o=e.character,i=e.characterRender,l=e.index,s=e.count,c=e.value,u=e.allowHalf,d=e.focused,m=e.onHover,v=e.onClick,p=l+1,h=new Set([r]);0===c&&0===l&&d?h.add("".concat(r,"-focused")):u&&c+.5>=p&&c<p?(h.add("".concat(r,"-half")),h.add("".concat(r,"-active")),d&&h.add("".concat(r,"-focused"))):(p<=c?h.add("".concat(r,"-full")):h.add("".concat(r,"-zero")),p===c&&d&&h.add("".concat(r,"-focused")));var x="function"===typeof o?o(e):o,b=n.createElement("li",{className:f()(Array.from(h)),ref:t},n.createElement("div",{onClick:a?null:function(e){v(e,l)},onKeyDown:a?null:function(e){e.keyCode===g.Z.ENTER&&v(e,l)},onMouseMove:a?null:function(e){m(e,l)},role:"radio","aria-checked":c>l?"true":"false","aria-posinset":l+1,"aria-setsize":s,tabIndex:a?-1:0},n.createElement("div",{className:"".concat(r,"-first")},x),n.createElement("div",{className:"".concat(r,"-second")},x)));return i&&(b=i(b,e)),b}const j=n.forwardRef(b);var w=["prefixCls","className","defaultValue","value","count","allowHalf","allowClear","character","characterRender","disabled","direction","tabIndex","autoFocus","onHoverChange","onChange","onFocus","onBlur","onKeyDown","onMouseLeave"];function y(e,t){var a,r=e.prefixCls,o=void 0===r?"rc-rate":r,l=e.className,s=e.defaultValue,c=e.value,u=e.count,d=void 0===u?5:u,b=e.allowHalf,y=void 0!==b&&b,C=e.allowClear,N=void 0===C||C,S=e.character,Z=void 0===S?"\u2605":S,E=e.characterRender,k=e.disabled,I=e.direction,O=void 0===I?"ltr":I,R=e.tabIndex,H=void 0===R?0:R,L=e.autoFocus,D=e.onHoverChange,F=e.onChange,P=e.onFocus,q=e.onBlur,z=e.onKeyDown,M=e.onMouseLeave,T=(0,p.Z)(e,w),B=function(){var e=n.useRef({});return[function(t){return e.current[t]},function(t){return function(a){e.current[t]=a}}]}(),W=(0,v.Z)(B,2),A=W[0],V=W[1],_=n.useRef(null),X=function(){var e;k||(null===(e=_.current)||void 0===e||e.focus())};n.useImperativeHandle(t,(function(){return{focus:X,blur:function(){var e;k||(null===(e=_.current)||void 0===e||e.blur())}}}));var G=(0,h.Z)(s||0,{value:c}),K=(0,v.Z)(G,2),U=K[0],Y=K[1],J=(0,h.Z)(null),Q=(0,v.Z)(J,2),$=Q[0],ee=Q[1],te=function(e,t){var a="rtl"===O,n=e+1;if(y){var r=A(e),o=function(e){var t=function(e){var t,a,n=e.ownerDocument,r=n.body,o=n&&n.documentElement,i=e.getBoundingClientRect();return t=i.left,a=i.top,{left:t-=o.clientLeft||r.clientLeft||0,top:a-=o.clientTop||r.clientTop||0}}(e),a=e.ownerDocument,n=a.defaultView||a.parentWindow;return t.left+=function(e){var t=e.pageXOffset,a="scrollLeft";if("number"!==typeof t){var n=e.document;"number"!==typeof(t=n.documentElement[a])&&(t=n.body[a])}return t}(n),t.left}(r),i=r.clientWidth;(a&&t-o>i/2||!a&&t-o<i/2)&&(n-=.5)}return n},ae=function(e){Y(e),null===F||void 0===F||F(e)},ne=n.useState(!1),re=(0,v.Z)(ne,2),oe=re[0],ie=re[1],le=n.useState(null),se=(0,v.Z)(le,2),ce=se[0],ue=se[1],de=function(e,t){var a=te(t,e.pageX);a!==$&&(ue(a),ee(null)),null===D||void 0===D||D(a)},fe=function(e){k||(ue(null),ee(null),null===D||void 0===D||D(void 0)),e&&(null===M||void 0===M||M(e))},me=function(e,t){var a=te(t,e.pageX),n=!1;N&&(n=a===U),fe(),ae(n?0:a),ee(n?a:null)};n.useEffect((function(){L&&!k&&X()}),[]);var ve=new Array(d).fill(0).map((function(e,t){return n.createElement(j,{ref:V(t),index:t,count:d,disabled:k,prefixCls:"".concat(o,"-star"),allowHalf:y,value:null===ce?U:ce,onClick:me,onHover:de,key:e||t,character:Z,characterRender:E,focused:oe})})),pe=f()(o,l,(a={},(0,m.Z)(a,"".concat(o,"-disabled"),k),(0,m.Z)(a,"".concat(o,"-rtl"),"rtl"===O),a));return n.createElement("ul",(0,i.Z)({className:pe,onMouseLeave:fe,tabIndex:k?-1:H,onFocus:k?null:function(){ie(!0),null===P||void 0===P||P()},onBlur:k?null:function(){ie(!1),null===q||void 0===q||q()},onKeyDown:k?null:function(e){var t=e.keyCode,a="rtl"===O,n=U;t===g.Z.RIGHT&&n<d&&!a?(ae(n+=y?.5:1),e.preventDefault()):t===g.Z.LEFT&&n>0&&!a||t===g.Z.RIGHT&&n>0&&a?(ae(n-=y?.5:1),e.preventDefault()):t===g.Z.LEFT&&n<d&&a&&(ae(n+=y?.5:1),e.preventDefault()),null===z||void 0===z||z(e)},ref:_,role:"radiogroup"},(0,x.Z)(T,{aria:!0,data:!0,attr:!0})),ve)}const C=n.forwardRef(y);var N=a(1929),S=a(2879),Z=a(7521),E=a(5564),k=a(9922);const I=e=>{const{componentCls:t}=e;return{["".concat(t,"-star")]:{position:"relative",display:"inline-block",color:"inherit",cursor:"pointer","&:not(:last-child)":{marginInlineEnd:e.marginXS},"> div":{transition:"all ".concat(e.motionDurationMid,", outline 0s"),"&:hover":{transform:e.starHoverScale},"&:focus":{outline:0},"&:focus-visible":{outline:"".concat(e.lineWidth,"px dashed ").concat(e.starColor),transform:e.starHoverScale}},"&-first, &-second":{color:e.starBg,transition:"all ".concat(e.motionDurationMid),userSelect:"none",[e.iconCls]:{verticalAlign:"middle"}},"&-first":{position:"absolute",top:0,insetInlineStart:0,width:"50%",height:"100%",overflow:"hidden",opacity:0},["&-half ".concat(t,"-star-first, &-half ").concat(t,"-star-second")]:{opacity:1},["&-half ".concat(t,"-star-first, &-full ").concat(t,"-star-second")]:{color:"inherit"}}}},O=e=>({["&-rtl".concat(e.componentCls)]:{direction:"rtl"}}),R=e=>{const{componentCls:t}=e;return{[t]:Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({},(0,Z.Wf)(e)),{display:"inline-block",margin:0,padding:0,color:e.starColor,fontSize:e.starSize,lineHeight:"unset",listStyle:"none",outline:"none",["&-disabled".concat(t," ").concat(t,"-star")]:{cursor:"default","> div:hover":{transform:"scale(1)"}}}),I(e)),{["+ ".concat(t,"-text")]:{display:"inline-block",marginInlineStart:e.marginXS,fontSize:e.fontSize}}),O(e))}},H=(0,E.Z)("Rate",(e=>{const t=(0,k.TS)(e,{});return[R(t)]}),(e=>({starColor:e.yellow6,starSize:.5*e.controlHeightLG,starHoverScale:"scale(1.1)",starBg:e.colorFillContent})));var L=function(e,t){var a={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&t.indexOf(n)<0&&(a[n]=e[n]);if(null!=e&&"function"===typeof Object.getOwnPropertySymbols){var r=0;for(n=Object.getOwnPropertySymbols(e);r<n.length;r++)t.indexOf(n[r])<0&&Object.prototype.propertyIsEnumerable.call(e,n[r])&&(a[n[r]]=e[n[r]])}return a};const D=n.forwardRef(((e,t)=>{const{prefixCls:a,className:r,rootClassName:o,style:i,tooltips:l,character:s=n.createElement(u,null)}=e,c=L(e,["prefixCls","className","rootClassName","style","tooltips","character"]),{getPrefixCls:d,direction:m,rate:v}=n.useContext(N.E_),p=d("rate",a),[h,g]=H(p),x=Object.assign(Object.assign({},null===v||void 0===v?void 0:v.style),i);return h(n.createElement(C,Object.assign({ref:t,character:s,characterRender:(e,t)=>{let{index:a}=t;return l?n.createElement(S.Z,{title:l[a]},e):e}},c,{className:f()(r,o,g,null===v||void 0===v?void 0:v.className),style:x,prefixCls:p,direction:m})))}));const F=D;var P=a(640),q=a(9434),z=a(8247);const{default:M}=a(3371);var T=a(1641),B=a(184);const W=()=>{const[e,t]=(0,n.useState)(!1),[a,i]=(0,n.useState)(""),[l,s]=(0,n.useState)(""),[c,u]=(0,n.useState)(""),[d,f]=(0,n.useState)(""),[m,v]=(0,n.useState)(null),p=(0,q.I0)(),h=async()=>{try{const e=await(async()=>{try{return(await M.get("/api/reviews/get-all-reviews")).data}catch(e){return e.response.data}})();e.success?f(e.data.reverse()):o.ZP.error(e.message)}catch(e){o.ZP.error(e.message)}};(0,n.useEffect)((()=>{localStorage.getItem("token")&&(p((0,z.YC)()),(async()=>{try{const e=await(0,r.bG)();e.success?e.data.isAdmin?t(!0):(t(!1),i(e.data),await h()):o.ZP.error(e.message)}catch(e){o.ZP.error(e.message)}p((0,z.Ir)())})())}),[]);return(0,n.useEffect)((()=>{if(d){const e=d.find((e=>e.user._id===a._id));v(e)}}),[d,a]),(0,B.jsx)("div",{className:"AboutUs",children:!e&&(0,B.jsxs)(B.Fragment,{children:[(0,B.jsx)(P.Z,{title:"About Us"}),(0,B.jsx)("div",{className:"divider"}),(0,B.jsx)("p",{className:"info-para",children:"Welcome to our web application! Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum. Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum."}),m?(0,B.jsxs)(B.Fragment,{children:[(0,B.jsx)("h2",{children:"Your Feedback"}),(0,B.jsxs)("div",{className:"p-rating-div",children:[(0,B.jsxs)("div",{className:"profile-row",children:[(0,B.jsx)("img",{className:"profile",src:m.user.profileImage?m.user.profileImage:T,alt:"profile",onError:e=>{e.target.src=T}}),(0,B.jsx)("p",{children:m.user.name})]}),(0,B.jsx)(F,{defaultValue:m.rating,className:"rate",disabled:!0}),(0,B.jsx)("br",{}),(0,B.jsx)("div",{className:"text",children:m.text})]})]}):(0,B.jsxs)(B.Fragment,{children:[(0,B.jsx)("h1",{children:"Feedback"}),(0,B.jsxs)("p",{children:["We strive to provide an exceptional user experience and value your feedback.",(0,B.jsx)("br",{}),"Please take a moment to rate our web app:"]}),(0,B.jsx)("div",{children:(0,B.jsx)("b",{children:"Rate Your Experience:"})}),(0,B.jsxs)("div",{className:"rating",children:[(0,B.jsxs)("div",{children:[(0,B.jsx)(F,{defaultValue:0,onChange:e=>{s(e)}}),(0,B.jsx)("br",{}),(0,B.jsx)("textarea",{className:"rating-text",placeholder:"Share your thoughts...",rows:4,value:c,onChange:e=>u(e.target.value)})]}),(0,B.jsx)("button",{onClick:async()=>{if(""!==l&&0!==l&&""!==c)try{const e={rating:l,text:c},t=await(async e=>{try{return(await M.post("/api/reviews/add-review",e)).data}catch(t){return t.response.data}})(e);t.success?(o.ZP.success(t.message),h()):o.ZP.error(t.message),p((0,z.Ir)())}catch(e){o.ZP.error(e.message)}},children:"Submit"})]})]}),(0,B.jsx)("h2",{children:"Previous Reviews"}),d?(0,B.jsx)("div",{className:"p-ratings",children:d.map(((e,t)=>{var a,n;return(0,B.jsx)("div",{children:(null===m||void 0===m?void 0:m.user._id)!==(null===(a=e.user)||void 0===a?void 0:a._id)&&(null===(n=e.user)||void 0===n?void 0:n._id)&&(0,B.jsxs)("div",{className:"p-rating-div",children:[(0,B.jsxs)("div",{className:"profile-row",children:[(0,B.jsx)("img",{className:"profile",src:e.user.profileImage?e.user.profileImage:T,alt:"profile",onError:e=>{e.target.src=T}}),(0,B.jsx)("p",{children:e.user.name})]}),(0,B.jsx)(F,{defaultValue:e.rating,className:"rate",disabled:!0}),(0,B.jsx)("br",{}),(0,B.jsx)("div",{className:"text",children:e.text})]})},t)}))}):(0,B.jsx)("div",{children:"No reviews yet."})]})})}},1641:(e,t,a)=>{e.exports=a.p+"static/media/person.b330f873e0d44db684c9.png"}}]);
//# sourceMappingURL=319.bee3e745.chunk.js.map