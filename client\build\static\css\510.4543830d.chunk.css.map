{"version": 3, "file": "static/css/510.4543830d.chunk.css", "mappings": "AAGA,WAGE,mCACF,CAGA,yBACE,WACE,mCACF,CACF,CAGA,0BACE,WACE,mCACF,CACF,CAGA,0BACE,WACE,mCACF,CACF,CAEA,kBACE,GACE,SAAU,CACV,2BACF,CACA,GACE,SAAU,CACV,uBACF,CACF,CAEA,uBACE,GACE,SAAU,CACV,0BACF,CACA,GACE,SAAU,CACV,uBACF,CACF,CAEA,yBACE,GACE,SAAU,CACV,0BACF,CACA,GACE,SAAU,CACV,uBACF,CACF,CAEA,iBACE,sCACF,CAEA,uBACE,+CACF,CAEA,yBACE,+CACF,CAEA,yBACE,6CACF,CAGA,mBACE,qBACF,CAEA,oBACE,oBACF,CAGA,cAEE,oBAAqB,CACrB,2BAA4B,CAF5B,mBAAoB,CAGpB,eACF,CAGA,WAGE,eAAW,CAFX,YAAa,CAEb,UAAW,CADX,yDAEF,CAEA,yBACE,WAEE,QAAS,CADT,yBAEF,CACF,CAGA,EACE,8BACF,CAGA,sCAIE,8BAA6C,CAD7C,YAEF,CAGA,SAIE,iCAAkC,CAFlC,wBAA6B,CAC7B,iBAAkB,CADlB,wBAGF,CAEA,gBACE,GAAK,sBAAyB,CAC9B,GAAO,uBAA2B,CACpC", "sources": ["pages/user/Quiz/animations.css"], "sourcesContent": ["/* Simple CSS animations to replace Framer Motion */\n\n/* Responsive Quiz Grid */\n.quiz-grid {\n  display: grid;\n  gap: 1.5rem;\n  grid-template-columns: repeat(2, 1fr); /* Mobile: 2 cards */\n}\n\n/* Tablets: 3 cards */\n@media (min-width: 768px) {\n  .quiz-grid {\n    grid-template-columns: repeat(3, 1fr);\n  }\n}\n\n/* Laptops: 4 cards */\n@media (min-width: 1024px) {\n  .quiz-grid {\n    grid-template-columns: repeat(4, 1fr);\n  }\n}\n\n/* Large screens: 5 cards */\n@media (min-width: 1280px) {\n  .quiz-grid {\n    grid-template-columns: repeat(5, 1fr);\n  }\n}\n\n@keyframes fadeIn {\n  from {\n    opacity: 0;\n    transform: translateY(-10px);\n  }\n  to {\n    opacity: 1;\n    transform: translateY(0);\n  }\n}\n\n@keyframes fadeInDelay {\n  from {\n    opacity: 0;\n    transform: translateY(10px);\n  }\n  to {\n    opacity: 1;\n    transform: translateY(0);\n  }\n}\n\n@keyframes fadeInStagger {\n  from {\n    opacity: 0;\n    transform: translateY(20px);\n  }\n  to {\n    opacity: 1;\n    transform: translateY(0);\n  }\n}\n\n.animate-fade-in {\n  animation: fadeIn 0.6s ease-out forwards;\n}\n\n.animate-fade-in-delay {\n  animation: fadeInDelay 0.6s ease-out 0.1s forwards;\n}\n\n.animate-fade-in-delay-2 {\n  animation: fadeInDelay 0.6s ease-out 0.2s forwards;\n}\n\n.animate-fade-in-stagger {\n  animation: fadeInStagger 0.6s ease-out forwards;\n}\n\n/* Hover effects */\n.hover-scale:hover {\n  transform: scale(1.05);\n}\n\n.hover-scale:active {\n  transform: scale(0.95);\n}\n\n/* Line clamp utility */\n.line-clamp-2 {\n  display: -webkit-box;\n  -webkit-line-clamp: 2;\n  -webkit-box-orient: vertical;\n  overflow: hidden;\n}\n\n/* Responsive grid */\n.quiz-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));\n  gap: 1.5rem;\n}\n\n@media (max-width: 768px) {\n  .quiz-grid {\n    grid-template-columns: 1fr;\n    gap: 1rem;\n  }\n}\n\n/* Smooth transitions */\n* {\n  transition: all 0.2s ease-in-out;\n}\n\n/* Focus states */\nbutton:focus,\ninput:focus,\nselect:focus {\n  outline: none;\n  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);\n}\n\n/* Loading spinner */\n.spinner {\n  border: 2px solid #f3f4f6;\n  border-top: 2px solid #3b82f6;\n  border-radius: 50%;\n  animation: spin 1s linear infinite;\n}\n\n@keyframes spin {\n  0% { transform: rotate(0deg); }\n  100% { transform: rotate(360deg); }\n}\n"], "names": [], "sourceRoot": ""}