{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport classNames from 'classnames';\nimport CSSMotion from 'rc-motion';\nimport ResizeObserver from 'rc-resize-observer';\nimport useLayoutEffect from \"rc-util/es/hooks/useLayoutEffect\";\nimport { composeRef } from \"rc-util/es/ref\";\nimport * as React from 'react';\nimport Arrow from \"./Arrow\";\nimport Mask from \"./Mask\";\nimport PopupContent from \"./PopupContent\";\nvar Popup = /*#__PURE__*/React.forwardRef(function (props, ref) {\n  var popup = props.popup,\n    className = props.className,\n    prefixCls = props.prefixCls,\n    style = props.style,\n    target = props.target,\n    _onVisibleChanged = props.onVisibleChanged,\n    open = props.open,\n    keepDom = props.keepDom,\n    onClick = props.onClick,\n    mask = props.mask,\n    arrow = props.arrow,\n    arrowPos = props.arrowPos,\n    align = props.align,\n    motion = props.motion,\n    maskMotion = props.maskMotion,\n    forceRender = props.forceRender,\n    getPopupContainer = props.getPopupContainer,\n    autoDestroy = props.autoDestroy,\n    Portal = props.portal,\n    zIndex = props.zIndex,\n    onMouseEnter = props.onMouseEnter,\n    onMouseLeave = props.onMouseLeave,\n    onPointerEnter = props.onPointerEnter,\n    ready = props.ready,\n    offsetX = props.offsetX,\n    offsetY = props.offsetY,\n    offsetR = props.offsetR,\n    offsetB = props.offsetB,\n    onAlign = props.onAlign,\n    onPrepare = props.onPrepare,\n    stretch = props.stretch,\n    targetWidth = props.targetWidth,\n    targetHeight = props.targetHeight;\n  var childNode = typeof popup === 'function' ? popup() : popup;\n\n  // We can not remove holder only when motion finished.\n  var isNodeVisible = open || keepDom;\n\n  // ======================= Container ========================\n  var getPopupContainerNeedParams = (getPopupContainer === null || getPopupContainer === void 0 ? void 0 : getPopupContainer.length) > 0;\n  var _React$useState = React.useState(!getPopupContainer || !getPopupContainerNeedParams),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    show = _React$useState2[0],\n    setShow = _React$useState2[1];\n\n  // Delay to show since `getPopupContainer` need target element\n  useLayoutEffect(function () {\n    if (!show && getPopupContainerNeedParams && target) {\n      setShow(true);\n    }\n  }, [show, getPopupContainerNeedParams, target]);\n\n  // ========================= Render =========================\n  if (!show) {\n    return null;\n  }\n\n  // >>>>> Offset\n  var AUTO = 'auto';\n  var offsetStyle = {\n    left: '-1000vw',\n    top: '-1000vh',\n    right: AUTO,\n    bottom: AUTO\n  };\n\n  // Set align style\n  if (ready || !open) {\n    var points = align.points,\n      _experimental = align._experimental;\n    var dynamicInset = _experimental === null || _experimental === void 0 ? void 0 : _experimental.dynamicInset;\n    var alignRight = dynamicInset && points[0][1] === 'r';\n    var alignBottom = dynamicInset && points[0][0] === 'b';\n    if (alignRight) {\n      offsetStyle.right = offsetR;\n      offsetStyle.left = AUTO;\n    } else {\n      offsetStyle.left = offsetX;\n      offsetStyle.right = AUTO;\n    }\n    if (alignBottom) {\n      offsetStyle.bottom = offsetB;\n      offsetStyle.top = AUTO;\n    } else {\n      offsetStyle.top = offsetY;\n      offsetStyle.bottom = AUTO;\n    }\n  }\n\n  // >>>>> Misc\n  var miscStyle = {};\n  if (stretch) {\n    if (stretch.includes('height') && targetHeight) {\n      miscStyle.height = targetHeight;\n    } else if (stretch.includes('minHeight') && targetHeight) {\n      miscStyle.minHeight = targetHeight;\n    }\n    if (stretch.includes('width') && targetWidth) {\n      miscStyle.width = targetWidth;\n    } else if (stretch.includes('minWidth') && targetWidth) {\n      miscStyle.minWidth = targetWidth;\n    }\n  }\n  if (!open) {\n    miscStyle.pointerEvents = 'none';\n  }\n  return /*#__PURE__*/React.createElement(Portal, {\n    open: forceRender || isNodeVisible,\n    getContainer: getPopupContainer && function () {\n      return getPopupContainer(target);\n    },\n    autoDestroy: autoDestroy\n  }, /*#__PURE__*/React.createElement(Mask, {\n    prefixCls: prefixCls,\n    open: open,\n    zIndex: zIndex,\n    mask: mask,\n    motion: maskMotion\n  }), /*#__PURE__*/React.createElement(ResizeObserver, {\n    onResize: onAlign,\n    disabled: !open\n  }, function (resizeObserverRef) {\n    return /*#__PURE__*/React.createElement(CSSMotion, _extends({\n      motionAppear: true,\n      motionEnter: true,\n      motionLeave: true,\n      removeOnLeave: false,\n      forceRender: forceRender,\n      leavedClassName: \"\".concat(prefixCls, \"-hidden\")\n    }, motion, {\n      onAppearPrepare: onPrepare,\n      onEnterPrepare: onPrepare,\n      visible: open,\n      onVisibleChanged: function onVisibleChanged(nextVisible) {\n        var _motion$onVisibleChan;\n        motion === null || motion === void 0 ? void 0 : (_motion$onVisibleChan = motion.onVisibleChanged) === null || _motion$onVisibleChan === void 0 ? void 0 : _motion$onVisibleChan.call(motion, nextVisible);\n        _onVisibleChanged(nextVisible);\n      }\n    }), function (_ref, motionRef) {\n      var motionClassName = _ref.className,\n        motionStyle = _ref.style;\n      var cls = classNames(prefixCls, motionClassName, className);\n      return /*#__PURE__*/React.createElement(\"div\", {\n        ref: composeRef(resizeObserverRef, ref, motionRef),\n        className: cls,\n        style: _objectSpread(_objectSpread(_objectSpread(_objectSpread({\n          '--arrow-x': \"\".concat(arrowPos.x || 0, \"px\"),\n          '--arrow-y': \"\".concat(arrowPos.y || 0, \"px\")\n        }, offsetStyle), miscStyle), motionStyle), {}, {\n          boxSizing: 'border-box',\n          zIndex: zIndex\n        }, style),\n        onMouseEnter: onMouseEnter,\n        onMouseLeave: onMouseLeave,\n        onPointerEnter: onPointerEnter,\n        onClick: onClick\n      }, arrow && /*#__PURE__*/React.createElement(Arrow, {\n        prefixCls: prefixCls,\n        arrow: arrow,\n        arrowPos: arrowPos,\n        align: align\n      }), /*#__PURE__*/React.createElement(PopupContent, {\n        cache: !open\n      }, childNode));\n    });\n  }));\n});\nif (process.env.NODE_ENV !== 'production') {\n  Popup.displayName = 'Popup';\n}\nexport default Popup;", "map": {"version": 3, "names": ["_extends", "_objectSpread", "_slicedToArray", "classNames", "CSSMotion", "ResizeObserver", "useLayoutEffect", "composeRef", "React", "Arrow", "Mask", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Popup", "forwardRef", "props", "ref", "popup", "className", "prefixCls", "style", "target", "_onVisibleChanged", "onVisibleChanged", "open", "keepDom", "onClick", "mask", "arrow", "arrowPos", "align", "motion", "maskMotion", "forceRender", "getPopupContainer", "autoDestroy", "Portal", "portal", "zIndex", "onMouseEnter", "onMouseLeave", "onPointerEnter", "ready", "offsetX", "offsetY", "offsetR", "offsetB", "onAlign", "onPrepare", "stretch", "targetWidth", "targetHeight", "childNode", "isNodeVisible", "getPopupContainerNeedParams", "length", "_React$useState", "useState", "_React$useState2", "show", "setShow", "AUTO", "offsetStyle", "left", "top", "right", "bottom", "points", "_experimental", "dynamicInset", "alignRight", "alignBottom", "miscStyle", "includes", "height", "minHeight", "width", "min<PERSON><PERSON><PERSON>", "pointerEvents", "createElement", "getContainer", "onResize", "disabled", "resizeObserverRef", "motionAppear", "motionEnter", "motionLeave", "removeOnLeave", "leavedClassName", "concat", "onAppearPrepare", "onEnterPrepare", "visible", "nextVisible", "_motion$onVisibleChan", "call", "_ref", "motionRef", "motionClassName", "motionStyle", "cls", "x", "y", "boxSizing", "cache", "process", "env", "NODE_ENV", "displayName"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/node_modules/@rc-component/trigger/es/Popup/index.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport classNames from 'classnames';\nimport CSSMotion from 'rc-motion';\nimport ResizeObserver from 'rc-resize-observer';\nimport useLayoutEffect from \"rc-util/es/hooks/useLayoutEffect\";\nimport { composeRef } from \"rc-util/es/ref\";\nimport * as React from 'react';\nimport Arrow from \"./Arrow\";\nimport Mask from \"./Mask\";\nimport PopupContent from \"./PopupContent\";\nvar Popup = /*#__PURE__*/React.forwardRef(function (props, ref) {\n  var popup = props.popup,\n    className = props.className,\n    prefixCls = props.prefixCls,\n    style = props.style,\n    target = props.target,\n    _onVisibleChanged = props.onVisibleChanged,\n    open = props.open,\n    keepDom = props.keepDom,\n    onClick = props.onClick,\n    mask = props.mask,\n    arrow = props.arrow,\n    arrowPos = props.arrowPos,\n    align = props.align,\n    motion = props.motion,\n    maskMotion = props.maskMotion,\n    forceRender = props.forceRender,\n    getPopupContainer = props.getPopupContainer,\n    autoDestroy = props.autoDestroy,\n    Portal = props.portal,\n    zIndex = props.zIndex,\n    onMouseEnter = props.onMouseEnter,\n    onMouseLeave = props.onMouseLeave,\n    onPointerEnter = props.onPointerEnter,\n    ready = props.ready,\n    offsetX = props.offsetX,\n    offsetY = props.offsetY,\n    offsetR = props.offsetR,\n    offsetB = props.offsetB,\n    onAlign = props.onAlign,\n    onPrepare = props.onPrepare,\n    stretch = props.stretch,\n    targetWidth = props.targetWidth,\n    targetHeight = props.targetHeight;\n  var childNode = typeof popup === 'function' ? popup() : popup;\n\n  // We can not remove holder only when motion finished.\n  var isNodeVisible = open || keepDom;\n\n  // ======================= Container ========================\n  var getPopupContainerNeedParams = (getPopupContainer === null || getPopupContainer === void 0 ? void 0 : getPopupContainer.length) > 0;\n  var _React$useState = React.useState(!getPopupContainer || !getPopupContainerNeedParams),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    show = _React$useState2[0],\n    setShow = _React$useState2[1];\n\n  // Delay to show since `getPopupContainer` need target element\n  useLayoutEffect(function () {\n    if (!show && getPopupContainerNeedParams && target) {\n      setShow(true);\n    }\n  }, [show, getPopupContainerNeedParams, target]);\n\n  // ========================= Render =========================\n  if (!show) {\n    return null;\n  }\n\n  // >>>>> Offset\n  var AUTO = 'auto';\n  var offsetStyle = {\n    left: '-1000vw',\n    top: '-1000vh',\n    right: AUTO,\n    bottom: AUTO\n  };\n\n  // Set align style\n  if (ready || !open) {\n    var points = align.points,\n      _experimental = align._experimental;\n    var dynamicInset = _experimental === null || _experimental === void 0 ? void 0 : _experimental.dynamicInset;\n    var alignRight = dynamicInset && points[0][1] === 'r';\n    var alignBottom = dynamicInset && points[0][0] === 'b';\n    if (alignRight) {\n      offsetStyle.right = offsetR;\n      offsetStyle.left = AUTO;\n    } else {\n      offsetStyle.left = offsetX;\n      offsetStyle.right = AUTO;\n    }\n    if (alignBottom) {\n      offsetStyle.bottom = offsetB;\n      offsetStyle.top = AUTO;\n    } else {\n      offsetStyle.top = offsetY;\n      offsetStyle.bottom = AUTO;\n    }\n  }\n\n  // >>>>> Misc\n  var miscStyle = {};\n  if (stretch) {\n    if (stretch.includes('height') && targetHeight) {\n      miscStyle.height = targetHeight;\n    } else if (stretch.includes('minHeight') && targetHeight) {\n      miscStyle.minHeight = targetHeight;\n    }\n    if (stretch.includes('width') && targetWidth) {\n      miscStyle.width = targetWidth;\n    } else if (stretch.includes('minWidth') && targetWidth) {\n      miscStyle.minWidth = targetWidth;\n    }\n  }\n  if (!open) {\n    miscStyle.pointerEvents = 'none';\n  }\n  return /*#__PURE__*/React.createElement(Portal, {\n    open: forceRender || isNodeVisible,\n    getContainer: getPopupContainer && function () {\n      return getPopupContainer(target);\n    },\n    autoDestroy: autoDestroy\n  }, /*#__PURE__*/React.createElement(Mask, {\n    prefixCls: prefixCls,\n    open: open,\n    zIndex: zIndex,\n    mask: mask,\n    motion: maskMotion\n  }), /*#__PURE__*/React.createElement(ResizeObserver, {\n    onResize: onAlign,\n    disabled: !open\n  }, function (resizeObserverRef) {\n    return /*#__PURE__*/React.createElement(CSSMotion, _extends({\n      motionAppear: true,\n      motionEnter: true,\n      motionLeave: true,\n      removeOnLeave: false,\n      forceRender: forceRender,\n      leavedClassName: \"\".concat(prefixCls, \"-hidden\")\n    }, motion, {\n      onAppearPrepare: onPrepare,\n      onEnterPrepare: onPrepare,\n      visible: open,\n      onVisibleChanged: function onVisibleChanged(nextVisible) {\n        var _motion$onVisibleChan;\n        motion === null || motion === void 0 ? void 0 : (_motion$onVisibleChan = motion.onVisibleChanged) === null || _motion$onVisibleChan === void 0 ? void 0 : _motion$onVisibleChan.call(motion, nextVisible);\n        _onVisibleChanged(nextVisible);\n      }\n    }), function (_ref, motionRef) {\n      var motionClassName = _ref.className,\n        motionStyle = _ref.style;\n      var cls = classNames(prefixCls, motionClassName, className);\n      return /*#__PURE__*/React.createElement(\"div\", {\n        ref: composeRef(resizeObserverRef, ref, motionRef),\n        className: cls,\n        style: _objectSpread(_objectSpread(_objectSpread(_objectSpread({\n          '--arrow-x': \"\".concat(arrowPos.x || 0, \"px\"),\n          '--arrow-y': \"\".concat(arrowPos.y || 0, \"px\")\n        }, offsetStyle), miscStyle), motionStyle), {}, {\n          boxSizing: 'border-box',\n          zIndex: zIndex\n        }, style),\n        onMouseEnter: onMouseEnter,\n        onMouseLeave: onMouseLeave,\n        onPointerEnter: onPointerEnter,\n        onClick: onClick\n      }, arrow && /*#__PURE__*/React.createElement(Arrow, {\n        prefixCls: prefixCls,\n        arrow: arrow,\n        arrowPos: arrowPos,\n        align: align\n      }), /*#__PURE__*/React.createElement(PopupContent, {\n        cache: !open\n      }, childNode));\n    });\n  }));\n});\nif (process.env.NODE_ENV !== 'production') {\n  Popup.displayName = 'Popup';\n}\nexport default Popup;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,aAAa,MAAM,0CAA0C;AACpE,OAAOC,cAAc,MAAM,0CAA0C;AACrE,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAOC,SAAS,MAAM,WAAW;AACjC,OAAOC,cAAc,MAAM,oBAAoB;AAC/C,OAAOC,eAAe,MAAM,kCAAkC;AAC9D,SAASC,UAAU,QAAQ,gBAAgB;AAC3C,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,KAAK,MAAM,SAAS;AAC3B,OAAOC,IAAI,MAAM,QAAQ;AACzB,OAAOC,YAAY,MAAM,gBAAgB;AACzC,IAAIC,KAAK,GAAG,aAAaJ,KAAK,CAACK,UAAU,CAAC,UAAUC,KAAK,EAAEC,GAAG,EAAE;EAC9D,IAAIC,KAAK,GAAGF,KAAK,CAACE,KAAK;IACrBC,SAAS,GAAGH,KAAK,CAACG,SAAS;IAC3BC,SAAS,GAAGJ,KAAK,CAACI,SAAS;IAC3BC,KAAK,GAAGL,KAAK,CAACK,KAAK;IACnBC,MAAM,GAAGN,KAAK,CAACM,MAAM;IACrBC,iBAAiB,GAAGP,KAAK,CAACQ,gBAAgB;IAC1CC,IAAI,GAAGT,KAAK,CAACS,IAAI;IACjBC,OAAO,GAAGV,KAAK,CAACU,OAAO;IACvBC,OAAO,GAAGX,KAAK,CAACW,OAAO;IACvBC,IAAI,GAAGZ,KAAK,CAACY,IAAI;IACjBC,KAAK,GAAGb,KAAK,CAACa,KAAK;IACnBC,QAAQ,GAAGd,KAAK,CAACc,QAAQ;IACzBC,KAAK,GAAGf,KAAK,CAACe,KAAK;IACnBC,MAAM,GAAGhB,KAAK,CAACgB,MAAM;IACrBC,UAAU,GAAGjB,KAAK,CAACiB,UAAU;IAC7BC,WAAW,GAAGlB,KAAK,CAACkB,WAAW;IAC/BC,iBAAiB,GAAGnB,KAAK,CAACmB,iBAAiB;IAC3CC,WAAW,GAAGpB,KAAK,CAACoB,WAAW;IAC/BC,MAAM,GAAGrB,KAAK,CAACsB,MAAM;IACrBC,MAAM,GAAGvB,KAAK,CAACuB,MAAM;IACrBC,YAAY,GAAGxB,KAAK,CAACwB,YAAY;IACjCC,YAAY,GAAGzB,KAAK,CAACyB,YAAY;IACjCC,cAAc,GAAG1B,KAAK,CAAC0B,cAAc;IACrCC,KAAK,GAAG3B,KAAK,CAAC2B,KAAK;IACnBC,OAAO,GAAG5B,KAAK,CAAC4B,OAAO;IACvBC,OAAO,GAAG7B,KAAK,CAAC6B,OAAO;IACvBC,OAAO,GAAG9B,KAAK,CAAC8B,OAAO;IACvBC,OAAO,GAAG/B,KAAK,CAAC+B,OAAO;IACvBC,OAAO,GAAGhC,KAAK,CAACgC,OAAO;IACvBC,SAAS,GAAGjC,KAAK,CAACiC,SAAS;IAC3BC,OAAO,GAAGlC,KAAK,CAACkC,OAAO;IACvBC,WAAW,GAAGnC,KAAK,CAACmC,WAAW;IAC/BC,YAAY,GAAGpC,KAAK,CAACoC,YAAY;EACnC,IAAIC,SAAS,GAAG,OAAOnC,KAAK,KAAK,UAAU,GAAGA,KAAK,CAAC,CAAC,GAAGA,KAAK;;EAE7D;EACA,IAAIoC,aAAa,GAAG7B,IAAI,IAAIC,OAAO;;EAEnC;EACA,IAAI6B,2BAA2B,GAAG,CAACpB,iBAAiB,KAAK,IAAI,IAAIA,iBAAiB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,iBAAiB,CAACqB,MAAM,IAAI,CAAC;EACtI,IAAIC,eAAe,GAAG/C,KAAK,CAACgD,QAAQ,CAAC,CAACvB,iBAAiB,IAAI,CAACoB,2BAA2B,CAAC;IACtFI,gBAAgB,GAAGvD,cAAc,CAACqD,eAAe,EAAE,CAAC,CAAC;IACrDG,IAAI,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IAC1BE,OAAO,GAAGF,gBAAgB,CAAC,CAAC,CAAC;;EAE/B;EACAnD,eAAe,CAAC,YAAY;IAC1B,IAAI,CAACoD,IAAI,IAAIL,2BAA2B,IAAIjC,MAAM,EAAE;MAClDuC,OAAO,CAAC,IAAI,CAAC;IACf;EACF,CAAC,EAAE,CAACD,IAAI,EAAEL,2BAA2B,EAAEjC,MAAM,CAAC,CAAC;;EAE/C;EACA,IAAI,CAACsC,IAAI,EAAE;IACT,OAAO,IAAI;EACb;;EAEA;EACA,IAAIE,IAAI,GAAG,MAAM;EACjB,IAAIC,WAAW,GAAG;IAChBC,IAAI,EAAE,SAAS;IACfC,GAAG,EAAE,SAAS;IACdC,KAAK,EAAEJ,IAAI;IACXK,MAAM,EAAEL;EACV,CAAC;;EAED;EACA,IAAInB,KAAK,IAAI,CAAClB,IAAI,EAAE;IAClB,IAAI2C,MAAM,GAAGrC,KAAK,CAACqC,MAAM;MACvBC,aAAa,GAAGtC,KAAK,CAACsC,aAAa;IACrC,IAAIC,YAAY,GAAGD,aAAa,KAAK,IAAI,IAAIA,aAAa,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,aAAa,CAACC,YAAY;IAC3G,IAAIC,UAAU,GAAGD,YAAY,IAAIF,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,GAAG;IACrD,IAAII,WAAW,GAAGF,YAAY,IAAIF,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,GAAG;IACtD,IAAIG,UAAU,EAAE;MACdR,WAAW,CAACG,KAAK,GAAGpB,OAAO;MAC3BiB,WAAW,CAACC,IAAI,GAAGF,IAAI;IACzB,CAAC,MAAM;MACLC,WAAW,CAACC,IAAI,GAAGpB,OAAO;MAC1BmB,WAAW,CAACG,KAAK,GAAGJ,IAAI;IAC1B;IACA,IAAIU,WAAW,EAAE;MACfT,WAAW,CAACI,MAAM,GAAGpB,OAAO;MAC5BgB,WAAW,CAACE,GAAG,GAAGH,IAAI;IACxB,CAAC,MAAM;MACLC,WAAW,CAACE,GAAG,GAAGpB,OAAO;MACzBkB,WAAW,CAACI,MAAM,GAAGL,IAAI;IAC3B;EACF;;EAEA;EACA,IAAIW,SAAS,GAAG,CAAC,CAAC;EAClB,IAAIvB,OAAO,EAAE;IACX,IAAIA,OAAO,CAACwB,QAAQ,CAAC,QAAQ,CAAC,IAAItB,YAAY,EAAE;MAC9CqB,SAAS,CAACE,MAAM,GAAGvB,YAAY;IACjC,CAAC,MAAM,IAAIF,OAAO,CAACwB,QAAQ,CAAC,WAAW,CAAC,IAAItB,YAAY,EAAE;MACxDqB,SAAS,CAACG,SAAS,GAAGxB,YAAY;IACpC;IACA,IAAIF,OAAO,CAACwB,QAAQ,CAAC,OAAO,CAAC,IAAIvB,WAAW,EAAE;MAC5CsB,SAAS,CAACI,KAAK,GAAG1B,WAAW;IAC/B,CAAC,MAAM,IAAID,OAAO,CAACwB,QAAQ,CAAC,UAAU,CAAC,IAAIvB,WAAW,EAAE;MACtDsB,SAAS,CAACK,QAAQ,GAAG3B,WAAW;IAClC;EACF;EACA,IAAI,CAAC1B,IAAI,EAAE;IACTgD,SAAS,CAACM,aAAa,GAAG,MAAM;EAClC;EACA,OAAO,aAAarE,KAAK,CAACsE,aAAa,CAAC3C,MAAM,EAAE;IAC9CZ,IAAI,EAAES,WAAW,IAAIoB,aAAa;IAClC2B,YAAY,EAAE9C,iBAAiB,IAAI,YAAY;MAC7C,OAAOA,iBAAiB,CAACb,MAAM,CAAC;IAClC,CAAC;IACDc,WAAW,EAAEA;EACf,CAAC,EAAE,aAAa1B,KAAK,CAACsE,aAAa,CAACpE,IAAI,EAAE;IACxCQ,SAAS,EAAEA,SAAS;IACpBK,IAAI,EAAEA,IAAI;IACVc,MAAM,EAAEA,MAAM;IACdX,IAAI,EAAEA,IAAI;IACVI,MAAM,EAAEC;EACV,CAAC,CAAC,EAAE,aAAavB,KAAK,CAACsE,aAAa,CAACzE,cAAc,EAAE;IACnD2E,QAAQ,EAAElC,OAAO;IACjBmC,QAAQ,EAAE,CAAC1D;EACb,CAAC,EAAE,UAAU2D,iBAAiB,EAAE;IAC9B,OAAO,aAAa1E,KAAK,CAACsE,aAAa,CAAC1E,SAAS,EAAEJ,QAAQ,CAAC;MAC1DmF,YAAY,EAAE,IAAI;MAClBC,WAAW,EAAE,IAAI;MACjBC,WAAW,EAAE,IAAI;MACjBC,aAAa,EAAE,KAAK;MACpBtD,WAAW,EAAEA,WAAW;MACxBuD,eAAe,EAAE,EAAE,CAACC,MAAM,CAACtE,SAAS,EAAE,SAAS;IACjD,CAAC,EAAEY,MAAM,EAAE;MACT2D,eAAe,EAAE1C,SAAS;MAC1B2C,cAAc,EAAE3C,SAAS;MACzB4C,OAAO,EAAEpE,IAAI;MACbD,gBAAgB,EAAE,SAASA,gBAAgBA,CAACsE,WAAW,EAAE;QACvD,IAAIC,qBAAqB;QACzB/D,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAG,CAAC+D,qBAAqB,GAAG/D,MAAM,CAACR,gBAAgB,MAAM,IAAI,IAAIuE,qBAAqB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,qBAAqB,CAACC,IAAI,CAAChE,MAAM,EAAE8D,WAAW,CAAC;QACzMvE,iBAAiB,CAACuE,WAAW,CAAC;MAChC;IACF,CAAC,CAAC,EAAE,UAAUG,IAAI,EAAEC,SAAS,EAAE;MAC7B,IAAIC,eAAe,GAAGF,IAAI,CAAC9E,SAAS;QAClCiF,WAAW,GAAGH,IAAI,CAAC5E,KAAK;MAC1B,IAAIgF,GAAG,GAAGhG,UAAU,CAACe,SAAS,EAAE+E,eAAe,EAAEhF,SAAS,CAAC;MAC3D,OAAO,aAAaT,KAAK,CAACsE,aAAa,CAAC,KAAK,EAAE;QAC7C/D,GAAG,EAAER,UAAU,CAAC2E,iBAAiB,EAAEnE,GAAG,EAAEiF,SAAS,CAAC;QAClD/E,SAAS,EAAEkF,GAAG;QACdhF,KAAK,EAAElB,aAAa,CAACA,aAAa,CAACA,aAAa,CAACA,aAAa,CAAC;UAC7D,WAAW,EAAE,EAAE,CAACuF,MAAM,CAAC5D,QAAQ,CAACwE,CAAC,IAAI,CAAC,EAAE,IAAI,CAAC;UAC7C,WAAW,EAAE,EAAE,CAACZ,MAAM,CAAC5D,QAAQ,CAACyE,CAAC,IAAI,CAAC,EAAE,IAAI;QAC9C,CAAC,EAAExC,WAAW,CAAC,EAAEU,SAAS,CAAC,EAAE2B,WAAW,CAAC,EAAE,CAAC,CAAC,EAAE;UAC7CI,SAAS,EAAE,YAAY;UACvBjE,MAAM,EAAEA;QACV,CAAC,EAAElB,KAAK,CAAC;QACTmB,YAAY,EAAEA,YAAY;QAC1BC,YAAY,EAAEA,YAAY;QAC1BC,cAAc,EAAEA,cAAc;QAC9Bf,OAAO,EAAEA;MACX,CAAC,EAAEE,KAAK,IAAI,aAAanB,KAAK,CAACsE,aAAa,CAACrE,KAAK,EAAE;QAClDS,SAAS,EAAEA,SAAS;QACpBS,KAAK,EAAEA,KAAK;QACZC,QAAQ,EAAEA,QAAQ;QAClBC,KAAK,EAAEA;MACT,CAAC,CAAC,EAAE,aAAarB,KAAK,CAACsE,aAAa,CAACnE,YAAY,EAAE;QACjD4F,KAAK,EAAE,CAAChF;MACV,CAAC,EAAE4B,SAAS,CAAC,CAAC;IAChB,CAAC,CAAC;EACJ,CAAC,CAAC,CAAC;AACL,CAAC,CAAC;AACF,IAAIqD,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzC9F,KAAK,CAAC+F,WAAW,GAAG,OAAO;AAC7B;AACA,eAAe/F,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}