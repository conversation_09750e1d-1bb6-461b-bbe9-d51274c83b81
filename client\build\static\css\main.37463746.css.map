{"version": 3, "file": "static/css/main.37463746.css", "mappings": "kJAIA,EACE,4EAEF,CAGA,KAKE,wBAAyC,CAAzC,uCAAyC,CADzC,6BAA+B,CAF/B,QAAS,CADT,SAQF,CAGA,kCACE,gDAAmD,CACnD,iCACF,CAGA,6BACE,QAAS,CACT,SACF,CAGA,kBAGE,aAA+B,CAA/B,6BAA+B,CAF/B,eAAgB,CAChB,gBAEF,CAEA,GAAK,iBAAoB,CACzB,GAAK,kBAAqB,CAC1B,GAAK,gBAAmB,CACxB,GAAK,iBAAoB,CACzB,GAAK,kBAAqB,CAC1B,GAAK,cAAiB,CAGtB,EACE,aAA8B,CAA9B,4BAA8B,CAC9B,oBAAqB,CACrB,yBACF,CAEA,QACE,aAAmC,CAAnC,iCAEF,CAGA,MACE,eACF,CAGA,IAGE,aACF,CAEA,qBACE,iBACF,CAEA,sBACE,4BACF,CAMA,MACE,2BAA6B,CAC7B,gCAAkC,CAClC,iCAAmC,CACnC,2BAA6B,CAC7B,0BAA4B,CAC5B,2BAA6B,CAC7B,sBAA2B,CAC3B,2BAA6B,CAC7B,4BAA8B,CAC9B,4BAA8B,CAC9B,4BAA8B,CAC9B,4BAA8B,CAC9B,4BAA8B,CAC9B,4BAA8B,CAC9B,4BACF,CAGA,EACE,+BACF,CAGA,OACE,4EACF,CAaA,yBACE,KAAO,wBAA4B,CACnC,GAAK,2BAA+B,CACpC,GAAK,0BAA8B,CACnC,GAAK,2BAA+B,CACpC,KAAO,2BAA8B,CAAE,4BAAiC,CAC1E,CAEA,+CACE,KAAO,wBAA4B,CACnC,GAAK,wBAA4B,CACjC,GAAK,2BAA+B,CACpC,GAAK,0BAA8B,CACrC,CAGA,qBAEE,+BACF,CAEA,yBACE,qBACE,wBACF,CACF,CAGA,YACE,4BAAqC,CAArC,mCAAqC,CAErC,qBAAuB,CACvB,6BAAgC,CAFhC,oBAA8B,CAA9B,4BAA8B,CAK9B,wBAA0B,CAD1B,yBAA2B,CAD3B,+BAAkC,CAGlC,iCACF,CAEA,wBACE,4BAA0C,CAA1C,wCAA0C,CAC1C,oCACF,CAGA,MACE,yBAAmC,CAAnC,iCAAmC,CACnC,kCAA4C,CAC5C,8BAAiC,CAEjC,6CAAwD,CACxD,4BAA8B,CAF9B,sBAGF,CAEA,YAGE,8BAAuC,CAFvC,+CAGF,CAGA,SACE,4BAAqC,CAArC,mCAAqC,CACrC,oBAA8B,CAA9B,4BACF,CAEA,WAGE,6BAAgC,CAIhC,oBAAuB,CAHvB,yBAA4B,CAF5B,6BAAgC,CAOhC,iCACF,CAOA,mCAEE,4BACF,CAGA,yBACE,QACE,+BACF,CAEA,SAEE,qBAAuB,CADvB,oBAEF,CAMA,oBACE,uBACF,CACF,CAGA,aAIE,yBAAmC,CAAnC,iCAAmC,CAFnC,kCAA4C,CAA5C,0CAA4C,CAC5C,6BAAgC,CAGhC,wBAA0B,CAD1B,6BAAgC,CAJhC,wBAA2B,CAM3B,iCACF,CAEA,mBAEE,8BACF,CAEA,yCAJE,8BAAuC,CAAvC,qCAQF,CAJA,sBACE,4BAAqC,CAArC,mCAGF,CAEA,2CAJE,oBAA8B,CAA9B,4BAQF,CAJA,qBACE,4BAAqC,CAArC,mCAAqC,CAErC,8BAAuC,CAAvC,qCACF,CAEA,uBACE,4BAAoC,CAApC,kCAAoC,CAEpC,8BAAsC,CAAtC,oCAAsC,CADtC,oBAA8B,CAA9B,4BAEF,CCnQA,MAKE,mBAAoB,CAKpB,eAAgB,CAChB,cAAe,CAGf,iBAAkB,CAClB,kBAAmB,CACnB,kBAAmB,CACnB,kBAAmB,CACnB,kBAAmB,CAGnB,qFAAwF,CACxF,yEAA4E,CAC5E,yEAA4E,CAC5E,uEACF,CAYA,YACI,wBAAgC,CAAhC,+BACJ,CAEA,UACI,wBAAgC,CAAhC,+BACJ,CAGA,cACI,oBAAqC,CAErC,sBACJ,CAEA,kBACI,kDAAgF,CAAhF,4EACJ,CAEA,eACI,kDAA6E,CAA7E,yEACJ,CAEA,eACI,4DACJ,CAEA,kBACI,8DACJ,CAEA,aACI,iCACJ,CAEA,eACI,wBAAyB,CACzB,kBACJ,CAEA,eACI,kDAA4E,CAA5E,wEAIJ,CAEA,mBACI,0CACJ,CAEA,kBACI,0BACJ,CAEA,mBACI,qBACJ,CCjGA,UACI,YACJ,CAEA,OACI,WACJ,CAEA,MACI,UACJ,CAEA,MACI,UACJ,CAEA,MACI,UACJ,CAEA,UACI,WACJ,CAEA,MACI,SACJ,CAEA,MACI,SACJ,CAEA,MACI,SACJ,CAEA,OACI,WACJ,CAEA,MACI,YACJ,CAEA,UACI,qBACJ,CAEA,WACI,cACJ,CAEA,gBACI,sBACJ,CAEA,iBACI,6BACJ,CAEA,eACI,0BACJ,CAEA,aACI,wBACJ,CAEA,cACI,kBACJ,CAEA,aACI,sBACJ,CAEA,WACI,oBACJ,CAEA,OACI,QACJ,CAEA,OACI,QACJ,CAEA,OACI,QACJ,CAEA,OACI,QACJ,CAEA,OACI,QACJ,CAEA,KACI,YACJ,CAEA,KACI,YACJ,CAEA,KACI,YACJ,CAEA,KACI,YACJ,CAEA,KACI,YACJ,CAEA,MACI,mBACJ,CAEA,MACI,kBACJ,CC9HA,UACI,gBAAiB,CACjB,gBACJ,CAEA,SACI,iBAAkB,CAClB,mBACJ,CAEA,SACI,kBAAmB,CACnB,kBACJ,CAEA,SACI,cAAe,CACf,mBACJ,CAEA,SACI,iBAAmB,CACnB,gBACJ,CAEA,SACI,gBAAkB,CAClB,mBACJ,CAEA,GACI,yBAA6B,CAC7B,0BACJ,CAEA,aACI,iBACJ,CAOA,UAJI,oBAOJ,CAHA,QAEI,yBACJ,CAEA,EAEI,wBAA0B,CAD1B,wBAEJ,CAEA,WACI,mCACJ,CCvDA,MAGE,0CAA4C,CAC5C,wCAA0C,CAO1C,+BAAiC,CAFjC,+BAAiC,CACjC,6BAA+B,CAJ/B,uCAAyC,CALzC,qBAAuB,CACvB,+CAAiD,CAKjD,6CAA+C,CAF/C,oBAOF,CAEA,kBANE,uCAYF,CANA,YAEE,qCAAuC,CAEvC,wCAAuD,CAHvD,sBAAwB,CAIxB,0BACF,CAEA,wBACE,4BACF,CAEA,eACE,+BAAgC,CAChC,qBAAsB,CACtB,kBAAmB,CACnB,UACF,CAEA,mBACE,qBAAsB,CACtB,eACF,CAGA,kEAEE,uBAAwB,CACxB,QACF,CAGA,mBACE,yBACF,CAEA,eACE,2BACF,CAGA,OAWE,kBAAmB,CAJnB,WAAY,CALZ,8BAA+B,CAE/B,cAAe,CAMf,mBAAoB,CADpB,mBAAoB,CADpB,6BAA8B,CAH9B,eAAgB,CAQhB,kBAAmB,CAVnB,qBAAuB,CASvB,sBAAuB,CAGvB,eAAgB,CAdhB,qCAAsC,CAatC,iBAAkB,CARlB,mCAUF,CAEA,gBAEE,kBAAmB,CADnB,UAAY,CAEZ,wBACF,CAEA,uBACE,kCAAmC,CAEnC,gCAAiC,CADjC,kBAEF,CAEA,4CAEE,oCAAkD,CADlD,0BAEF,CAEA,8BACE,uBACF,CAEA,6BACE,oCAAqC,CAErC,8BAA4C,CAD5C,0BAEF,CAEA,YAEE,eAAgB,CADhB,cAEF,CAEA,sBACE,qBAAuB,CAEvB,+BAAgC,CADhC,oBAEF,CAEA,OAME,uBAAgB,CAAhB,eAAgB,CAChB,wBAAyB,CAKzB,gQAAmP,CACnP,qCAAsC,CACtC,2BAA4B,CAC5B,oBAAqB,CAZrB,wBAAyB,CACzB,iBAAkB,CAIlB,aAAc,CAEd,cAAe,CADf,cAAe,CARf,WAAY,CAeZ,2BAAmB,CALnB,8BAAgC,CANhC,UAYF,CAEA,aAGE,qBAAyB,CADzB,oBAAqB,CAErB,8BAA6C,CAH7C,YAIF,CCvIA,MAIE,uCAAyC,CADzC,0CAA4C,CAD5C,wCAA0C,CAD1C,qCAAuC,CAQvC,sCAAwC,CADxC,gCAAkC,CADlC,iBAAkB,CAFlB,6CAKF,CAEA,aAOE,kDAAsF,CAAtF,iFAAsF,CANtF,UAAW,CAKX,UAAW,CAFX,MAAO,CAIP,SAAU,CANV,iBAAkB,CAGlB,OAAQ,CAFR,KAAM,CAMN,sCAAwE,CAAxE,+DACF,CAEA,YAGE,qCAAuC,CAFvC,qCAAuC,CACvC,oCAEF,CAEA,mBACE,SACF,CAEA,SAGE,wBAA0C,CAA1C,wCAA0C,CAD1C,oBAAwC,CAAxC,sCAAwC,CADxC,qCAAiE,CAAjE,sDAAiE,CAGjE,YAA6B,CAA7B,2BACF,CAEA,SAEE,oBAAwC,CAAxC,qCAAwC,CADxC,YAA6B,CAA7B,2BAEF,CAQA,SACE,+BAAgC,CAChC,aACF,CAGA,eASE,kBAAmB,CAEnB,6BAA+B,CAP/B,iCAA0B,CAA1B,yBAA0B,CAD1B,sDAAkF,CAGlF,YAAa,CACb,qBAAsB,CAGtB,WAAY,CARZ,OAAQ,CAMR,sBAAuB,CAPvB,cAAe,CAIf,YAA6B,CAA7B,2BAOF,CAaA,QAOE,qEAA4E,CAH5E,0BAA6C,CAE7C,iBAAsC,CAAtC,oCAAsC,CADtC,8BAAoD,CAApD,kDAAoD,CADpD,wBAA6C,CAA7C,2CAA6C,CAK7C,6BAA2C,CAR3C,WAAY,CAOZ,iBAAkB,CANlB,UAQF,CAEA,eAUE,4CAA6C,CAH7C,kDAA0F,CAA1F,qFAA0F,CAC1F,iBAAsC,CAAtC,oCAAsC,CAGtC,6BAA2C,CAL3C,WAAY,CADZ,UAOF,CAEA,6BAbE,UAAW,CAGX,QAAS,CAFT,iBAAkB,CAClB,OAAQ,CAMR,8BAgBF,CAXA,cAUE,sCAAuC,CAHvC,0BAAwC,CACxC,iBAAsC,CAAtC,oCAAsC,CAFtC,WAAY,CADZ,UAMF,CAGA,qBAUE,6CAA8C,CAP9C,UAAW,CAGX,aAA8B,CAA9B,4BAA8B,CAL9B,uBAAwB,CAMxB,gBAAiB,CACjB,eAAgB,CAJhB,QAAS,CAKT,mBAAqB,CAPrB,iBAAkB,CAGlB,0BAMF,CAEA,wBACE,GAEE,uBAAwB,CADxB,+BAEF,CACA,IAEE,wBAAyB,CADzB,mCAEF,CACA,GAEE,uBAAwB,CADxB,gCAEF,CACF,CAEA,sBACE,MACE,SAAU,CACV,uCACF,CACA,IACE,UAAY,CACZ,wCACF,CACF,CAEA,qBACE,GAEE,SAAU,CADV,oDAEF,CACA,IAEE,UAAY,CADZ,wDAEF,CACA,GAEE,SAAU,CADV,qDAEF,CACF,CAEA,qBACE,MACE,SAAU,CACV,wCACF,CACA,IACE,UAAY,CACZ,2CACF,CACF,CAGA,QAIE,iCAAmC,CAFnC,0CAA4C,CAC5C,wCAA0C,CAE1C,wBAA0B,CAE1B,sCAAwC,CADxC,6CAEF,CAEA,yBATE,gCAeF,CANA,iBAIE,mCAAqC,CAFrC,yCAA2C,CAC3C,wCAA0C,CAE1C,4BACF,CAEA,QACE,wBAAyB,CAIzB,iBAAkB,CADlB,oBAAuB,CAFvB,6BAAsB,CAAtB,qBAAsB,CACtB,YAGF,CAEA,kBACE,YACF,CAGA,OAKE,kBAAmB,CAJnB,+BAAgC,CAQhC,gCAAiC,CAPjC,4BAA8B,CAE9B,YAAa,CAMb,eAAgB,CAHhB,WAAY,CADZ,sBAAuB,CAHvB,sBAAuB,CAQvB,mCAAoC,CAHpC,UAIF,CAEA,kBACE,GACE,sBACF,CAEA,GACE,uBACF,CACF,CClPA,QAKE,uBAAwB,CAExB,YAAa,CADb,kEAA8E,CAH9E,YAAa,CACb,eAAgB,CAHhB,SAAU,CACV,WAMF,CAGA,yBACE,QACE,qBAAsB,CACtB,WAAY,CACZ,gBAAiB,CACjB,SACF,CACF,CAGA,yBACE,QAEE,iBAAkB,CADlB,SAEF,CACF,CAEA,+CACE,QACE,SACF,CACF,CAEA,gDACE,QACE,SACF,CACF,CAEA,SACE,yBAA0B,CAE1B,eAAgB,CAMhB,0CAA2C,CAD3C,2BAA4B,CAF5B,qBAAsB,CAMtB,aAAc,CARd,YAAa,CAOb,eAAgB,CANhB,eAAgB,CAHhB,sBAAuB,CAQvB,iBAAkB,CAHlB,oBAMF,CAGA,yBACE,SAKE,2CAA4C,CAD5C,iBAAkB,CAFlB,WAAY,CACZ,eAAgB,CAGhB,sBAAuB,CALvB,UAMF,CACF,CAEA,gBAOE,4EAAgF,CADhF,QAAS,CALT,UAAW,CAGX,MAAO,CAIP,SAAU,CANV,iBAAkB,CAGlB,OAAQ,CAFR,KAAM,CAMN,UACF,CAEA,gBAEE,sBAAuB,CADvB,UAEF,CAEA,yBACE,gBAEE,sBAAuB,CADvB,UAEF,CACF,CAEA,MACE,0BAAmB,CAAnB,kBACF,CAEA,2BAGE,sBAAuB,CADvB,kBAAmB,CADnB,gBAGF,CAEA,WAEE,4BAA8B,CAa9B,0BAAkC,CADlC,qBAAuB,CALvB,wCAA0C,CAD1C,yBAA0C,CAH1C,cAAe,CAJf,sBAAwB,CAexB,2BAA8B,CAN9B,yBAA2B,CAH3B,4BAA8B,CAH9B,iCAAmC,CAQnC,eAAgB,CAThB,+CAAiD,CAQjD,iBAAkB,CAElB,8BAAgC,CAPhC,mCAAoC,CAWpC,kBACF,CAGA,yBACE,WAEE,yBAA4B,CAC5B,4BAA8B,CAF9B,+CAGF,CAEA,2BACE,sBAAuB,CACvB,gCACF,CAEA,gCACE,YACF,CACF,CAEA,kBAOE,uDAAsF,CANtF,UAAW,CAKX,WAAY,CAFZ,UAAW,CAFX,iBAAkB,CAClB,KAAM,CAKN,mCAAoC,CAHpC,UAIF,CAEA,iBAIE,kCAA2B,CAA3B,0BAA2B,CAH3B,8BAA+C,CAC/C,oBAAuB,CACvB,yBAEF,CAEA,kBACE,0BAA+C,CAG/C,wCAA0C,CAD1C,yCAAqD,CADrD,oBAGF,CAEA,yBAOE,eAAiB,CACjB,yBAA0B,CAH1B,QAAS,CAJT,UAAW,CAEX,MAAO,CADP,iBAAkB,CAElB,KAAM,CAEN,SAGF,CAEA,MAGE,iCAAmC,CADnC,QAAO,CAEP,WAAY,CAHZ,eAIF,CAGA,yBACE,MAEE,gBAAiB,CADjB,UAEF,CACF,CAcA,SAEE,iCAAmC,CACnC,gBAAiB,CAFjB,eAAgB,CAGhB,gCACF,CAGA,yBACE,SAIE,eAAgB,CADhB,QAAS,CADT,eAAgB,CADhB,gCAIF,CACF,CAEA,+CACE,SAEE,QAAS,CADT,gCAEF,CACF,CAEA,gDACE,SACE,gCACF,CACF,CAEA,0BACE,SACE,gCACF,CACF,CAEA,QAKE,4BAA8B,CAJ9B,yCAA2C,CAG3C,wCAA0C,CAK1C,qCAAuC,CAHvC,qBAAsB,CAJtB,oBAAuB,CAMvB,sCAAwC,CALxC,gCAAkC,CAIlC,UAGF,CAGA,yBACE,uBACF,CAEA,0BACE,sBACF,CAEA,uBAEE,uBAAyB,CADzB,qBAEF,CAEA,yBAGE,4BAA8B,CAE9B,yCAA2C,CAC3C,yBAA2B,CAO3B,+BAAiC,CAZjC,sBAAwB,CAWxB,qBAAuB,CAVvB,gCAAkC,CAOlC,gBAAkB,CALlB,2BAA6B,CAG7B,wBAA0B,CAG1B,iBAAmB,CAFnB,eAAiB,CAGjB,sBAGF,CAEA,iFAEE,sBACF,CAEA,kDAEE,4BAA8B,CAD9B,sBAAwB,CAExB,iBACF,CAEA,0BAEE,mCAAqC,CACrC,yBAA2B,CAC3B,eAAgB,CAHhB,mBAIF,CClSA,iBACE,qBAAsB,CACtB,QAAS,CACT,SACF,CAOA,KAIE,+BAAoC,CADpC,uBAAyB,CAFzB,4EAAyF,CACzF,yBAA2B,CAM3B,kBAAoB,CADpB,iBAAkB,CAElB,mBACF,CAGA,MAEE,iBAAkB,CAClB,sBAAuB,CACvB,uBAAwB,CACxB,oBAAqB,CACrB,qBAAsB,CACtB,qBAAsB,CACtB,qBAAsB,CACtB,qBAAsB,CACtB,qBAAsB,CACtB,qBAAsB,CACtB,qBAAsB,CACtB,qBAAsB,CACtB,qBAAsB,CAGtB,qBAAsB,CACtB,mBAAoB,CACpB,kBAAmB,CAGnB,iBAAkB,CAClB,uBAAwB,CACxB,sBAAuB,CACvB,iBAAkB,CAClB,uBAAwB,CACxB,sBAAuB,CACvB,gBAAiB,CACjB,sBAAuB,CACvB,qBAAsB,CACtB,cAAe,CACf,oBAAqB,CACrB,mBAAoB,CAGpB,YAAgB,CAChB,iBAAkB,CAClB,kBAAmB,CACnB,kBAAmB,CACnB,kBAAmB,CACnB,kBAAmB,CACnB,kBAAmB,CACnB,kBAAmB,CACnB,kBAAmB,CACnB,kBAAmB,CACnB,kBAAmB,CAGnB,iBAAkB,CAClB,gBAAiB,CACjB,iBAAkB,CAClB,cAAe,CACf,iBAAkB,CAClB,gBAAiB,CACjB,cAAe,CACf,iBAAkB,CAClB,eAAgB,CAChB,eAAgB,CAChB,eAAgB,CAQhB,oBAAqB,CAGrB,iCAAgD,CAChD,6DAA0F,CAC1F,+DAA4F,CAC5F,iEAA+F,CAC/F,wCAAuD,CAGvD,oBAAqC,CACrC,wBAAsC,CACtC,uBAAwB,CAGxB,4BAA6B,CAC7B,kDAAsD,CACtD,gDAAoD,CACpD,6DAAiE,CAEjE,uCAAsD,CAGtD,iDAAqD,CACrD,mDAAuD,CACvD,iDAAqD,CAGrD,sBAAuB,CACvB,uBAAwB,CACxB,qBAAsB,CACtB,uBAAwB,CACxB,sBAAuB,CACvB,sBAAuB,CACvB,wBAAyB,CACzB,uBAAwB,CAGxB,oBAAqB,CACrB,oBAAqB,CACrB,uBAAwB,CACxB,iBAAkB,CAGlB,iBAAkB,CAClB,eAAgB,CAChB,cAAe,CACf,uBAAwB,CACxB,cAAe,CACf,gBAAiB,CACjB,gBACF,CASA,QAAU,sBAA0B,CAGpC,aAAe,+BAAmC,CAClD,UAAY,4BAAgC,CAC5C,gBAAkB,gCAAoC,CACtD,iBAAmB,uCAA2C,CAC9D,eAAiB,oCAAwC,CACzD,aAAe,kCAAsC,CACrD,cAAgB,4BAAgC,CAChD,aAAe,gCAAoC,CACnD,WAAa,8BAAkC,CAG/C,QAAU,kBAAoB,CAY9B,OAAS,kBAA8B,CAA9B,4BAAgC,CAUzC,KAAO,qBAAiC,CAAjC,+BAAmC,CAC1C,QAAU,qBAAyB,CAEnC,SAAW,0BAA4B,CAAE,2BAA+B,CACxE,SAAwC,4BAA8B,CAA3D,yBAA6D,CAQxE,MAAQ,yBAAqC,CAArC,mCAAuC,CAQ/C,MAAQ,4BAAwC,CAAxC,sCAA0C,CAsBlD,KAAO,sBAAkC,CAAlC,gCAAoC,CAE3C,MAAQ,6BAAuC,CAAvC,qCAAuC,CAAE,8BAAwC,CAAxC,sCAA0C,CAC3F,MAAQ,4BAAuC,CAAvC,qCAAuC,CAAE,6BAAwC,CAAxC,sCAA0C,CAC3F,MAAQ,6BAAuC,CAAvC,qCAAuC,CAAE,8BAAwC,CAAxC,sCAA0C,CAC3F,MAAQ,2BAAuC,CAAvC,qCAAuC,CAAE,4BAAwC,CAAxC,sCAA0C,CAC3F,MAAQ,6BAAuC,CAAvC,qCAAuC,CAAE,8BAAwC,CAAxC,sCAA0C,CAC3F,MAAQ,2BAAuC,CAAvC,qCAAuC,CAAE,4BAAwC,CAAxC,sCAA0C,CAE3F,MAAgD,+BAAyC,CAAzC,uCAAyC,CAAjF,4BAAsC,CAAtC,oCAAmF,CAC3F,MAAgD,8BAAyC,CAAzC,uCAAyC,CAAjF,2BAAsC,CAAtC,oCAAmF,CAC3F,MAAgD,+BAAyC,CAAzC,uCAAyC,CAAjF,4BAAsC,CAAtC,oCAAmF,CAC3F,MAAgD,6BAAyC,CAAzC,uCAAyC,CAAjF,0BAAsC,CAAtC,oCAAmF,CAC3F,MAAgD,+BAAyC,CAAzC,uCAAyC,CAAjF,4BAAsC,CAAtC,oCAAmF,CAC3F,MAAgD,6BAAyC,CAAzC,uCAAyC,CAAjF,0BAAsC,CAAtC,oCAAmF,CAO3F,MAAQ,4BAAsC,CAAtC,oCAAwC,CAChD,MAAQ,0BAAsC,CAAtC,oCAAwC,CAOhD,MAAQ,+BAAyC,CAAzC,uCAA2C,CACnD,MAAQ,6BAAyC,CAAzC,uCAA2C,CAGnD,QAAU,oBAAwB,CAElC,OAAS,mCAA6B,CAA7B,2BAA+B,CACxC,QAAU,qBAAyB,CAEnC,OAAS,oCAA8B,CAA9B,4BAAgC,CACzC,UAAY,sBAA0B,CACtC,cAAgB,0BAA8B,CAG9C,UAAY,2BAA+B,CAC3C,UAAY,2BAA+B,CAC3C,OAAS,wBAA4B,CACrC,QAAU,iCAA2B,CAA3B,yBAA6B,CAMvC,cAAgB,4BAAgC,CAGhD,YAAc,yBAA6B,CAC3C,aAAe,yBAA6B,CAC5C,aAAe,yBAA6B,CAC5C,eAAiB,yBAA6B,CAC9C,WAAa,yBAA6B,CAC1C,gBAAkB,yBAA6B,CAG/C,SAAW,0BAAyC,CAAzC,uCAA2C,CAMtD,UAAY,4BAA0C,CAA1C,wCAA4C,CACxD,UAAY,2BAA0C,CAA1C,wCAA4C,CAGxD,eAAiB,0BAA4C,CAA5C,0CAA8C,CAC/D,gBAAkB,yBAA6C,CAA7C,2CAA+C,CACjE,iBAAmB,2BAA8C,CAA9C,4CAAgD,CACnE,eAAiB,uBAA4C,CAA5C,0CAA8C,CAK/D,mBAAqB,uBAAqC,CAArC,mCAAuC,CAK5D,eAAiB,uBAAiC,CAAjC,+BAAmC,CACpD,eAAiB,uBAAiC,CAAjC,+BAAmC,CACpD,eAAiB,uBAAiC,CAAjC,+BAAmC,CACpD,eAAiB,uBAAiC,CAAjC,+BAAmC,CACpD,eAAiB,uBAAiC,CAAjC,+BAAmC,CACpD,eAAiB,uBAAiC,CAAjC,+BAAmC,CAKpD,iBAAmB,kCAAgD,CAAhD,8CAAkD,CACrE,kBAAoB,kCAAiD,CAAjD,+CAAmD,CACvE,eAAiB,kCAA8C,CAA9C,4CAAgD,CACjE,gBAAkB,kCAA+C,CAA/C,6CAAiD,CAEnE,kBAAoB,kCAAiD,CAAjD,+CAAmD,CAEvE,kBAAoB,kCAAiD,CAAjD,+CAAmD,CAEvE,iBAAmB,kCAAgD,CAAhD,8CAAkD,CAErE,eAAiB,kCAA8C,CAA9C,4CAAgD,CACjE,YAAc,kCAA2C,CAA3C,yCAA6C,CAC3D,aAAe,kCAA4C,CAA5C,0CAA8C,CAC7D,aAAe,kCAA4C,CAA5C,0CAA8C,CAC7D,aAAe,kCAA4C,CAA5C,0CAA8C,CAG7D,cAAgB,yBAA6B,CAG7C,YAAc,6BAA0C,CAA1C,wCAA4C,CAG1D,aAAe,8BAA2C,CAA3C,yCAA6C,CAM5D,UAAY,kCAA4C,CAA5C,0CAA8C,CAG1D,gBAAkB,8BAAuC,CAAvC,qCAAyC,CAE3D,iBAAmB,8BAAwC,CAAxC,sCAA0C,CAC7D,iBAAmB,8BAAwC,CAAxC,sCAA0C,CAM7D,WAAa,sEAAuC,CAAvC,qCAAyC,CAGtD,aAAe,2CAAyC,CAAzC,uCAA2C,CAC1D,gBAAkB,2CAA4C,CAA5C,0CAA8C,CAGhE,YAAc,oCAA+C,CAA/C,6CAAiD,CAC/D,iBAAmB,qCAA6C,CAA7C,2CAA+C,CAClE,iBAAmB,oCAA6C,CAA7C,2CAA+C,CAClE,gBAAkB,wCAAmD,CAAnD,iDAAqD,CACvE,mBAAqB,wGAAuI,CAAvI,mIAAyI,CAG9J,WAAa,iCAAqC,CAClD,wBAA0B,+BAAmC,CAC7D,wBAA0B,8BAAkC,CAC5D,6BAA+B,uCAA4C,CAC3E,6BAA+B,sCAA2C,CAG1E,WAAa,mBAAuB,CACpC,YAAc,qBAA0B,CACxC,YAAc,oBAAyB,CACvC,YAAc,qBAA0B,CACxC,aAAe,mBAAuB,CAGtC,iBAAmB,yBAA6B,CAChD,eAAiB,uBAA2B,CAC5C,iBAAmB,yBAA6B,CAChD,mBAAqB,2BAA+B,CACpD,mBAAqB,2BAA+B,CACpD,iBAAmB,yBAA6B,CAChD,iBAAmB,yBAA6B,CAGhD,KAAO,mBAAuB,CAC9B,MAAQ,oBAAwB,CAChC,MAAQ,oBAAwB,CAChC,MAAQ,oBAAwB,CAChC,MAAQ,oBAAwB,CAChC,MAAQ,oBAAwB,CAKhC,MACE,eAAwB,CAAxB,uBAAwB,CAGxB,wBAAiC,CAAjC,gCAAiC,CAFjC,kBAA+B,CAA/B,8BAA+B,CAC/B,8DAA4B,CAA5B,2BAA4B,CAG5B,eAAgB,CADhB,0BAAoC,CAApC,mCAEF,CAEA,YACE,gEAA4B,CAA5B,2BAA4B,CAC5B,0BACF,CAEA,aAGE,kBAA0B,CAA1B,yBAA0B,CAD1B,+BAAwC,CAAxC,uCAEF,CAMA,qCATE,cAAuB,CAAvB,sBAaF,CAJA,aAGE,kBAA0B,CAA1B,yBAA0B,CAD1B,4BAAqC,CAArC,oCAEF,CAGA,YAEE,kCAA2B,CAA3B,0BAA2B,CAD3B,oBAAqC,CAIrC,gEAA4B,CAA5B,2BACF,CAoCA,aACE,4EAGF,CAEA,mBAEE,oCACF,CAEA,eACE,kBAA2B,CAA3B,0BAA2B,CAE3B,wBAAiC,CAAjC,gCAAiC,CADjC,aAAsB,CAAtB,qBAEF,CAEA,qBACE,kBAA2B,CAA3B,0BAA2B,CAC3B,0BACF,CAEA,aACE,kDAAoE,CAApE,gEAGF,CAEA,mBAEE,oCAAmD,CADnD,0BAEF,CAEA,YACE,kDAAmE,CAAnE,+DAGF,CAEA,kBAEE,oCAAkD,CADlD,0BAEF,CAEA,aACE,gBAAuB,CACvB,wBAAgC,CAAhC,+BAAgC,CAChC,aAAqB,CAArB,oBACF,CAEA,mBACE,kBAA0B,CAA1B,yBAA0B,CAC1B,UAAmB,CAAnB,kBAAmB,CACnB,0BACF,CAGA,YACE,kBAA6B,CAA7B,4BACF,CAEA,YAGE,qBAAsB,CAFtB,aAAc,CAId,6BAA8B,CAH9B,eAAgB,CAEhB,4BAEF,CAEA,wCAQE,qBAA8B,CAA9B,6BAA8B,CAH9B,wBAAiC,CAAjC,gCAAiC,CACjC,oBAA+B,CAA/B,8BAA+B,CAK/B,mBAAoB,CAJpB,iBAA8B,CAA9B,6BAA8B,CAG9B,YAAa,CANb,mBAAsC,CAAtC,qCAAsC,CAKtC,0BAAoC,CAApC,mCAAoC,CANpC,UASF,CAEA,0DAKE,qBAA8B,CAA9B,6BAA8B,CAF9B,oBAA4B,CAA5B,2BAA4B,CAC5B,8BAEF,CAEA,mEAGE,wBAAgC,CAAhC,+BAAgC,CAChC,aAAsB,CAAtB,qBAAsB,CACtB,kBACF,CAEA,yEAGE,oBAA2B,CAA3B,0BACF,CAEA,2FAGE,oBAA2B,CAA3B,0BAA2B,CAC3B,8BACF,CAEA,mEAGE,oBAA4B,CAA5B,2BACF,CAEA,qFAGE,oBAA4B,CAA5B,2BAA4B,CAC5B,8BACF,CAEA,WAEE,aAAsB,CAAtB,qBAEF,CAEA,uBALE,gBAA8B,CAA9B,6BAA8B,CAE9B,iBAA0B,CAA1B,yBAOF,CAJA,YAEE,aAAoB,CAApB,mBAEF,CAEA,cAEE,aAAqB,CAArB,oBAAqB,CADrB,gBAA8B,CAA9B,6BAA8B,CAE9B,iBAA0B,CAA1B,yBACF,CAGA,aAME,qBAA8B,CAA9B,6BAA8B,CAC9B,gQAAmP,CACnP,uCAAgD,CAAhD,+CAAgD,CAChD,2BAA4B,CAC5B,oBAAqB,CAPrB,wBAAiC,CAAjC,gCAAiC,CACjC,oBAA+B,CAA/B,8BAA+B,CAU/B,cAAe,CATf,iBAA8B,CAA9B,6BAA8B,CAQ9B,YAAa,CAXb,mBAAsC,CAAtC,qCAAsC,CAStC,oBAAqB,CACrB,0BAAoC,CAApC,mCAAoC,CAXpC,UAcF,CAEA,mBACE,oBAA4B,CAA5B,2BAA4B,CAC5B,8BACF,CAGA,YAEE,kBAAmB,CADnB,YAAa,CAEb,SAAmB,CAAnB,kBAAmB,CACnB,mBAA6B,CAA7B,4BACF,CAEA,kBAKE,qBAA8B,CAA9B,6BAA8B,CAF9B,wBAAiC,CAAjC,gCAAiC,CACjC,qBAA+B,CAA/B,8BAA+B,CAE/B,cAAe,CAJf,eAAgB,CAKhB,0BAAoC,CAApC,mCAAoC,CANpC,cAOF,CAEA,0BACE,wBAAgC,CAAhC,+BAAgC,CAChC,oBAA4B,CAA5B,2BACF,CAEA,8BACE,oBAAiC,CAAjC,gCACF,CAEA,kBAEE,aAAsB,CAAtB,qBAAsB,CACtB,cAAe,CAFf,iBAA8B,CAA9B,6BAGF,CAGA,WAIE,wBACF,CAEA,cACE,eACF,CAEA,cACE,eACF,CAEA,cACE,gBACF,CAEA,cACE,gBACF,CAEA,KACE,YAAa,CACb,cAAe,CACf,gBAAmC,CAAnC,gCACF,CAEA,KACE,QAAO,CACP,gBAAyB,CAAzB,wBACF,CAEA,UACE,aAAc,CACd,UACF,CAEA,OAAS,kBAAmB,CAAE,mBAAsB,CACpD,OAAS,mBAAoB,CAAE,oBAAuB,CACtD,OAAS,YAAa,CAAE,aAAgB,CACxC,OAAS,mBAAoB,CAAE,oBAAuB,CACtD,OAAS,mBAAoB,CAAE,oBAAuB,CACtD,OAAS,YAAa,CAAE,aAAgB,CACxC,OAAS,mBAAoB,CAAE,oBAAuB,CACtD,OAAS,mBAAoB,CAAE,oBAAuB,CACtD,OAAS,YAAa,CAAE,aAAgB,CACxC,QAAU,mBAAoB,CAAE,oBAAuB,CACvD,QAAU,mBAAoB,CAAE,oBAAuB,CACvD,QAAU,aAAc,CAAE,cAAiB,CAG3C,QACE,eAAwB,CAAxB,uBAAwB,CACxB,+BAAwC,CAAxC,uCAAwC,CACxC,gCAA4B,CAA5B,2BAA4B,CAC5B,cAAyB,CAAzB,wBACF,CAEA,cAGE,aAAqB,CAArB,oBAAqB,CAFrB,iBAA8B,CAA9B,6BAA8B,CAC9B,eAAgB,CAEhB,oBACF,CAEA,YAEE,kBAAmB,CADnB,YAAa,CAEb,UAAmB,CAAnB,kBAAmB,CACnB,eAAgB,CAChB,QAAS,CACT,SACF,CAEA,UAME,mBAA+B,CAA/B,8BAA+B,CAL/B,aAAsB,CAAtB,qBAAsB,CAEtB,eAAgB,CAEhB,oBAAsC,CAAtC,qCAAsC,CAHtC,oBAAqB,CAErB,0BAAoC,CAApC,mCAGF,CAEA,iCAGE,kBAA6B,CAA7B,4BAA6B,CAD7B,aAAqB,CAArB,oBAEF,CAGA,SACE,kDAAgF,CAAhF,4EAAgF,CAKhF,gCAAgD,CADhD,gEAA4B,CAA5B,2BAA4B,CAH5B,UAAmB,CAAnB,kBAAmB,CACnB,gBAAiB,CACjB,cAAuB,CAAvB,sBAGF,CAEA,eAEE,kBAAmB,CAInB,iCAAiD,CALjD,YAAa,CAEb,UAAmB,CAAnB,kBAAmB,CACnB,kBAA6B,CAA7B,4BAA6B,CAC7B,qBAA8B,CAA9B,6BAEF,CAEA,mBAGE,oBAA+B,CAA/B,8BAA+B,CAD/B,WAAY,CADZ,UAGF,CAEA,kBACE,UAAmB,CAAnB,kBAAmB,CACnB,eAAgB,CAChB,QACF,CAEA,cACE,eAAgB,CAEhB,QAAS,CADT,SAEF,CAEA,mBACE,mBAA6B,CAA7B,4BACF,CAEA,mBAEE,kBAAmB,CAKnB,oBAA+B,CAA/B,8BAA+B,CAF/B,eAA+B,CAJ/B,YAAa,CAQb,eAAgB,CANhB,UAAmB,CAAnB,kBAAmB,CACnB,mBAAsC,CAAtC,qCAAsC,CAEtC,oBAAqB,CAErB,0BAAoC,CAApC,mCAEF,CAEA,mDAEE,oBAAoC,CACpC,UAAmB,CAAnB,kBAAmB,CACnB,yBACF,CAEA,mBAEE,WAAY,CACZ,UAAY,CAFZ,UAGF,CAGA,OAGE,eAAwB,CAAxB,uBAAwB,CADxB,wBAAyB,CAEzB,oBAA+B,CAA/B,8BAA+B,CAE/B,4DAA4B,CAA5B,2BAA4B,CAD5B,eAAgB,CAJhB,UAMF,CAEA,UACE,kBAA0B,CAA1B,yBAA0B,CAK1B,+BAAwC,CAAxC,uCAAwC,CADxC,aAAsB,CAAtB,qBAAsB,CADtB,eAAgB,CADhB,eAIF,CAEA,oBAPE,YAAuB,CAAvB,sBAWF,CAJA,UAEE,+BAAwC,CAAxC,uCAAwC,CACxC,aAAsB,CAAtB,qBACF,CAEA,sBACE,kBAA0B,CAA1B,yBACF,CAEA,8BACE,kBACF,CAGA,OAME,kBAAmB,CAHnB,sBAA6B,CAD7B,oBAA+B,CAA/B,8BAA+B,CAG/B,YAAa,CAEb,UAAmB,CAAnB,kBAAmB,CAHnB,kBAA6B,CAA7B,4BAA6B,CAH7B,YAAuB,CAAvB,sBAOF,CAEA,eACE,kBAAgC,CAAhC,+BAAgC,CAChC,oBAA4B,CAA5B,2BAA4B,CAC5B,aACF,CAEA,eACE,kBAAgC,CAAhC,+BAAgC,CAChC,oBAA4B,CAA5B,2BAA4B,CAC5B,aACF,CAEA,cACE,kBAA+B,CAA/B,8BAA+B,CAC/B,oBAA2B,CAA3B,0BAA2B,CAC3B,aACF,CAEA,YACE,kBAA6B,CAA7B,4BAA6B,CAC7B,oBAAyB,CAAzB,wBAAyB,CACzB,aACF,CAGA,OAEE,kBAAmB,CAInB,oBAAiC,CAAjC,gCAAiC,CALjC,mBAAoB,CAGpB,gBAA8B,CAA9B,6BAA8B,CAC9B,eAAgB,CAGhB,qBAAuB,CALvB,qBAAsC,CAAtC,qCAAsC,CAItC,wBAEF,CAiBA,cACE,kBAA+B,CAA/B,8BAA+B,CAC/B,aACF,CAEA,iBACE,kBAA2B,CAA3B,0BAA2B,CAC3B,aAAsB,CAAtB,qBACF,CAGA,UAGE,kBAA2B,CAA3B,0BAA2B,CAC3B,oBAAiC,CAAjC,gCAAiC,CAFjC,UAAW,CAGX,eAAgB,CAJhB,UAKF,CAEA,cAEE,iDAAgF,CAAhF,4EAAgF,CADhF,WAAY,CAGZ,gCAAwC,CAAxC,uCACF,CAEA,sBACE,iDAAmE,CAAnE,+DACF,CAEA,sBACE,iDAAmE,CAAnE,+DACF,CAEA,qBACE,iDAAkE,CAAlE,8DACF,CAGA,gBAUE,kBAAmB,CAHnB,iCAA0B,CAA1B,yBAA0B,CAD1B,oBAA8B,CAG9B,YAAa,CAJb,WAAY,CAMZ,sBAAuB,CARvB,MAAO,CASP,YAAuB,CAAvB,sBAAuB,CAXvB,cAAe,CACf,KAAM,CAEN,UAAW,CAIX,YAAgC,CAAhC,+BAKF,CAEA,OASE,mCAAqC,CARrC,eAAwB,CAAxB,uBAAwB,CACxB,kBAA+B,CAA/B,8BAA+B,CAC/B,gEAA4B,CAA5B,2BAA4B,CAG5B,eAAgB,CAFhB,eAAgB,CAGhB,eAAgB,CAFhB,UAAW,CAGX,YAAuB,CAAvB,sBAEF,CAEA,wBACE,GACE,SAAU,CACV,sCACF,CACA,GACE,SAAU,CACV,gCACF,CACF,CAEA,cAIE,kBAAmB,CAFnB,+BAAwC,CAAxC,uCAAwC,CACxC,YAAa,CAEb,6BAA8B,CAJ9B,cAAuB,CAAvB,sBAKF,CAEA,aAGE,aAAsB,CAAtB,qBAAsB,CAFtB,kBAA8B,CAA9B,6BAA8B,CAC9B,eAAgB,CAEhB,QACF,CAEA,aACE,eAAgB,CAChB,WAAY,CAKZ,mBAA+B,CAA/B,8BAA+B,CAH/B,aAAsB,CAAtB,qBAAsB,CACtB,cAAe,CAFf,iBAA8B,CAA9B,6BAA8B,CAG9B,cAAuB,CAAvB,sBAAuB,CAEvB,0BAAoC,CAApC,mCACF,CAEA,mBAEE,kBAA2B,CAA3B,0BAA2B,CAD3B,aAAsB,CAAtB,qBAEF,CAMA,0BAHE,cAAuB,CAAvB,sBASF,CANA,cAEE,4BAAqC,CAArC,oCAAqC,CACrC,YAAa,CACb,UAAmB,CAAnB,kBAAmB,CACnB,wBACF,CAGA,SAME,iCAAkC,CAHlC,wBAAiC,CACjC,4BAAoC,CAApC,gCAAoC,CACpC,oBAAiC,CAAjC,gCAAiC,CADjC,+BAAoC,CAFpC,WAAY,CADZ,UAMF,CAEA,YAGE,gBAAiB,CADjB,WAAY,CADZ,UAGF,CAEA,YAGE,gBAAiB,CADjB,WAAY,CADZ,UAGF,CAQA,SAEE,oBAAqB,CADrB,iBAEF,CAEA,cAGE,kBAA2B,CAA3B,0BAA2B,CAG3B,mBAA+B,CAA/B,8BAA+B,CAI/B,WAAY,CANZ,UAAmB,CAAnB,kBAAmB,CAWnB,gBAA8B,CAA9B,6BAA8B,CAJ9B,QAAS,CACT,iBAAkB,CAClB,SAAU,CANV,aAAuB,CAAvB,sBAAuB,CACvB,iBAAkB,CAHlB,iBAAkB,CASlB,kCAA4C,CAA5C,2CAA4C,CAb5C,iBAAkB,CAClB,WAAY,CAOZ,YAAyB,CAAzB,wBAOF,CAEA,6BAEE,SAAU,CADV,kBAEF,CAGA,UAEE,oBAAqB,CADrB,iBAEF,CAEA,eAIE,eAAwB,CAAxB,uBAAwB,CACxB,wBAAiC,CAAjC,gCAAiC,CACjC,oBAA+B,CAA/B,8BAA+B,CAC/B,8DAA4B,CAA5B,2BAA4B,CAJ5B,MAAO,CAKP,eAAgB,CAEhB,SAAU,CATV,iBAAkB,CAClB,QAAS,CAUT,2BAA4B,CAC5B,0BAAoC,CAApC,mCAAoC,CAFpC,iBAAkB,CAFlB,YAA0B,CAA1B,yBAKF,CAEA,gCACE,SAAU,CAEV,uBAAwB,CADxB,kBAEF,CAEA,eAME,+BAAwC,CAAxC,uCAAwC,CAHxC,aAAsB,CAAtB,qBAAsB,CAFtB,aAAc,CACd,mBAAsC,CAAtC,qCAAsC,CAEtC,oBAAqB,CACrB,0BAAoC,CAApC,mCAEF,CAEA,0BACE,kBACF,CAEA,qBACE,kBAA0B,CAA1B,yBAA0B,CAC1B,aAAqB,CAArB,oBACF,CAKA,yBACE,WACE,gBAAyB,CAAzB,wBACF,CAEA,MAEE,oBAA+B,CAA/B,8BAA+B,CAD/B,aAAsB,CAAtB,qBAEF,CAEA,KAEE,sBAAuB,CADvB,UAEF,CAEA,OACE,aAAsB,CAAtB,qBAAsB,CACtB,6BAAsC,CAAtC,qCACF,CAEA,OACE,iBAA8B,CAA9B,6BACF,CAEA,oBAEE,aAAuB,CAAvB,sBACF,CAEA,SACE,YAAuB,CAAvB,sBACF,CAEA,mBACE,oBAAsC,CAAtC,qCACF,CAGA,wCACE,YACF,CAEA,mCACE,sBACF,CAGA,KACE,qBACF,CAEA,4FAGE,aAAc,CAEd,kBAA6B,CAA7B,4BAA6B,CAD7B,cAEF,CACF,CAGA,gDACE,WACE,cAAyB,CAAzB,wBACF,CAEA,MACE,WAAsB,CAAtB,qBACF,CAEA,OACE,eACF,CACF,CAGA,0BACE,WACE,gBAAyB,CAAzB,wBACF,CAEA,YACE,0BACF,CAEA,WACE,0BACF,CACF,CAKA,OACE,yBAAiC,CAAjC,gCAAiC,CACjC,kBACF,CAEA,kDAIE,8BAA6C,CAD7C,YAEF,CAGA,uCACE,iBAGE,kCAAqC,CACrC,qCAAuC,CAEvC,8BAAgC,CADhC,mCAEF,CACF,CAGA,+BACE,MACE,eAAmB,CACnB,eAAmB,CACnB,eACF,CAEA,MACE,wBAAiC,CAAjC,gCACF,CAEA,KACE,gBACF,CACF,CAGA,aACE,+BAIE,sBACF,CAEA,MAEE,wBAAiC,CAAjC,gCAAiC,CADjC,eAEF,CAEA,KACE,eAAiB,CACjB,UACF,CACF,CAGA,oBAEE,UAAW,CADX,SAEF,CAEA,0BACE,kBAA2B,CAA3B,0BAEF,CAEA,oDAHE,oBAAiC,CAAjC,gCAMF,CAHA,0BACE,kBAA2B,CAA3B,0BAEF,CAEA,gCACE,kBAA2B,CAA3B,0BACF,CAGA,EAEE,+BAAgD,CAAhD,+CAAgD,CADhD,oBAEF,CAKA,iBAQE,yBAA4B,CAF5B,kBAAoB,CAFpB,gBAAkB,CAHlB,yBAA2B,CAC3B,wBAA0B,CAG1B,iBAAmB,CAFnB,eAAiB,CAIjB,sBAEF,CAEA,4KAME,sBAAwB,CAExB,mBAAqB,CACrB,6BAA+B,CAF/B,2BAGF,CAQA,sDAHE,kBAAoB,CADpB,mBASF,CALA,sBAIE,sBAAwB,CADxB,qBAEF,CAGA,WAIE,wBAAiC,CAAjC,gCAAiC,CAFjC,oBAA+B,CAA/B,8BAA+B,CAM/B,oBAA6B,CAA7B,4BAA6B,CAF7B,eAAgB,CAChB,iBAEF,CAEA,iBAGE,oBAA4B,CAA5B,2BAA4B,CAF5B,8DAA4B,CAA5B,2BAA4B,CAC5B,0BAEF,CAGA,wCAGE,4BAAqC,CAArC,mCAAqC,CADrC,oBAA4B,CAA5B,2BAA4B,CAE5B,oBAA8B,CAA9B,4BACF,CAEA,4CAGE,4BAAoC,CAApC,kCAAoC,CADpC,oBAA2B,CAA3B,0BAA2B,CAE3B,oBAA8B,CAA9B,4BACF,CAEA,0CAGE,4BAAqC,CAArC,mCAAqC,CADrC,oBAA4B,CAA5B,2BAA4B,CAE5B,oBAA8B,CAA9B,4BACF,CAGA,yBACE,eAAwB,CAAxB,uBAAwB,CAKxB,wBAAiC,CAAjC,gCAAiC,CAJjC,oBAA+B,CAA/B,8BAA+B,CAG/B,gCAA4B,CAA5B,2BAA4B,CAD5B,oBAA6B,CAA7B,4BAA6B,CAD7B,YAAuB,CAAvB,sBAIF,CAEA,sBAGE,aAAqB,CAArB,oBAAqB,CAFrB,iBAA8B,CAA9B,6BAA8B,CAC9B,eAAgB,CAEhB,mBAA6B,CAA7B,4BACF,CAEA,oBAEE,aAAsB,CAAtB,qBAAsB,CADtB,cAAgC,CAAhC,+BAAgC,CAGhC,eAAgB,CADhB,kBAA6B,CAA7B,4BAEF,CAEA,wBAEE,qBAEF,CAEA,qCALE,YAAa,CAEb,SAAmB,CAAnB,kBAcF,CAXA,aASE,kBAAmB,CALnB,eAAwB,CAAxB,uBAAwB,CADxB,mBAA+B,CAA/B,8BAA+B,CAI/B,iBAA8B,CAA9B,6BAA8B,CAN9B,cAAuB,CAAvB,sBAUF,CAOA,oBAEE,aAAsB,CAAtB,qBAAsB,CADtB,eAAgB,CAEhB,cAAe,CACf,iBACF,CAMA,8HAEE,UAAmB,CAAnB,kBACF,CAGA,iBAGE,kBAAmB,CAGnB,eAAwB,CAAxB,uBAAwB,CACxB,4BAAqC,CAArC,oCAAqC,CAErC,QAAS,CART,YAAa,CAGb,SAAmB,CAAnB,kBAAmB,CAFnB,sBAAuB,CAGvB,YAAuB,CAAvB,sBAAuB,CAGvB,uBAAgB,CAAhB,eAAgB,CAEhB,UACF,CAGA,yBACE,iBAGE,qBAAsB,CADtB,UAAmB,CAAnB,kBAAmB,CADnB,aAAuB,CAAvB,sBAGF,CACF,CAEA,+CACE,iBAEE,SAAmB,CAAnB,kBAAmB,CADnB,cAAuB,CAAvB,sBAEF,CACF,CAEA,cAEE,WAAY,CACZ,oBAA+B,CAA/B,8BAA+B,CAE/B,cAAe,CAEf,iBAA8B,CAA9B,6BAA8B,CAH9B,eAAgB,CAHhB,qBAAsC,CAAtC,qCAAsC,CAKtC,0BAAoC,CAApC,mCAEF,CAGA,yBACE,cAEE,gBAA8B,CAA9B,6BAA8B,CAE9B,oBAA6B,CAA7B,4BAA6B,CAH7B,kBAAsC,CAAtC,qCAAsC,CAEtC,UAEF,CACF,CAEA,+CACE,cAEE,gBAA8B,CAA9B,6BAA8B,CAD9B,qBAAsC,CAAtC,qCAEF,CACF,CAEA,sBACE,kBAA0B,CAA1B,yBAA0B,CAC1B,UAAmB,CAAnB,kBACF,CAEA,4BACE,kBAA+B,CAA/B,8BAA+B,CAC/B,0BACF,CAEA,wBACE,kBAA2B,CAA3B,0BAA2B,CAC3B,aAAsB,CAAtB,qBACF,CAEA,8BACE,kBAA2B,CAA3B,0BACF,CAGA,YAEE,kBAAmB,CAEnB,kBAA0B,CAA1B,yBAA0B,CAG1B,oBAA+B,CAA/B,8BAA+B,CAF/B,UAAmB,CAAnB,kBAAmB,CAJnB,YAAa,CAQb,kBAA8B,CAA9B,6BAA8B,CAD9B,eAAgB,CALhB,sBAAuB,CAOvB,eAAgB,CAJhB,cAAuB,CAAvB,sBAAuB,CAKvB,0BAAoC,CAApC,mCACF,CAGA,yBACE,YAEE,iBAA8B,CAA9B,6BAA8B,CAC9B,eAAgB,CAFhB,aAAuB,CAAvB,sBAGF,CACF,CAEA,+CACE,YAEE,cAAgC,CAAhC,+BAAgC,CAChC,eAAgB,CAFhB,aAAuB,CAAvB,sBAGF,CACF,CAEA,oBAEE,2BAA4B,CAD5B,kBAAyB,CAAzB,wBAEF,CAQA,6BACE,oBAA6B,CAA7B,4BAA6B,CAC7B,iBACF,CAEA,oBAKE,wBAAiC,CAAjC,gCAAiC,CAHjC,oBAA+B,CAA/B,8BAA+B,CAE/B,8DAA4B,CAA5B,2BAA4B,CAH5B,oBAAqB,CAErB,eAGF,CAEA,mBAKE,aAAc,CAHd,WAAY,CACZ,gBAAiB,CAFjB,cAAe,CAGf,kBAEF,CAGA,yBAEE,eAAwB,CAAxB,uBAAwB,CACxB,+BAAwC,CAAxC,uCAAwC,CAFxC,mBAAsC,CAAtC,qCAGF,CAEA,oBAGE,kBAA2B,CAA3B,0BAA2B,CAD3B,UAAW,CAGX,eAAgB,CAJhB,UAKF,CAEA,wCAJE,oBAAiC,CAAjC,gCASF,CALA,oBAEE,kBAA0B,CAA1B,yBAA0B,CAD1B,WAAY,CAGZ,gCAAwC,CAAxC,uCACF,CAEA,uBAGE,aAAsB,CAAtB,qBAAsB,CADtB,iBAA8B,CAA9B,6BAA8B,CAE9B,gBAA0B,CAA1B,yBAA0B,CAH1B,iBAIF,CAGA,YACE,eAAwB,CAAxB,uBAAwB,CAGxB,wBAAiC,CAAjC,gCAAiC,CAFjC,oBAA+B,CAA/B,8BAA+B,CAC/B,4DAA4B,CAA5B,2BAA4B,CAI5B,kBAA6B,CAA7B,4BAA6B,CAD7B,eAAgB,CADhB,0BAAoC,CAApC,mCAGF,CAEA,kBAGE,oBAA4B,CAA5B,2BAA4B,CAF5B,8DAA4B,CAA5B,2BAA4B,CAC5B,0BAEF,CAEA,mBACE,kBAA0B,CAA1B,yBAA0B,CAG1B,+BAA4C,CAA5C,2CAA4C,CAF5C,UAAmB,CAAnB,kBAGF,CAEA,oCAJE,YAAuB,CAAvB,sBAMF,CAEA,kBAGE,UAAmB,CAAnB,kBAAmB,CAFnB,kBAA8B,CAA9B,6BAA8B,CAC9B,eAAgB,CAEhB,mBAA6B,CAA7B,4BACF,CAEA,iBAEE,kBAAmB,CAGnB,eAA+B,CAJ/B,YAAa,CAEb,UAAmB,CAAnB,kBAGF,CAEA,yCAJE,iBAA8B,CAA9B,6BASF,CALA,wBACE,aAAsB,CAAtB,qBAAsB,CAEtB,eAAgB,CAChB,oBAA6B,CAA7B,4BACF,CAEA,oBACE,YAAa,CACb,SAAmB,CAAnB,kBAAmB,CACnB,wBACF,CAGA,YAGE,kBAA2B,CAA3B,0BAA2B,CAE3B,oBAA+B,CAA/B,8BAA+B,CAJ/B,YAAa,CACb,UAAmB,CAAnB,kBAAmB,CAInB,kBAA6B,CAA7B,4BAA6B,CAF7B,cAAuB,CAAvB,sBAGF,CAGA,yBACE,YACE,qBAAsB,CACtB,UAAmB,CAAnB,kBAAmB,CACnB,cAAuB,CAAvB,sBACF,CACF,CAEA,+CACE,YACE,cAAe,CACf,UAAmB,CAAnB,kBACF,CACF,CAEA,WAWE,kBAAmB,CANnB,gBAAuB,CAFvB,WAAY,CACZ,mBAA+B,CAA/B,8BAA+B,CAE/B,aAAsB,CAAtB,qBAAsB,CAEtB,cAAe,CAEf,YAAa,CATb,QAAO,CAMP,eAAgB,CAMhB,SAAmB,CAAnB,kBAAmB,CADnB,sBAAuB,CAVvB,mBAAsC,CAAtC,qCAAsC,CAOtC,0BAAoC,CAApC,mCAKF,CAGA,yBACE,WAEE,gBAA8B,CAA9B,6BAA8B,CAC9B,UAAmB,CAAnB,kBAAmB,CAFnB,oBAAsC,CAAtC,qCAGF,CACF,CAEA,+CACE,WAEE,iBAA8B,CAA9B,6BAA8B,CAD9B,oBAAsC,CAAtC,qCAEF,CACF,CAEA,kBACE,kBAA0B,CAA1B,yBAA0B,CAE1B,gCAA4B,CAA5B,2BAA4B,CAD5B,UAAmB,CAAnB,kBAEF,CAEA,8BACE,kBAA2B,CAA3B,0BAA2B,CAC3B,aAAsB,CAAtB,qBACF,CAGA,eAEE,2DAAmG,CAAnG,8FAAmG,CADnG,gBAAiB,CAGjB,eAAgB,CADhB,iBAEF,CAEA,sBAOE,sWAA4U,CAD5U,QAAS,CALT,UAAW,CAGX,MAAO,CAIP,UAAY,CANZ,iBAAkB,CAGlB,OAAQ,CAFR,KAAM,CAMN,SACF,CAEA,aAEE,iBAAsC,CAAtC,qCAAsC,CACtC,iBAAkB,CAFlB,iBAAkB,CAGlB,SACF,CAEA,gBAKE,6BAAoC,CAGpC,+BAAiC,CALjC,8DAAoG,CAApG,+FAAoG,CACpG,4BAA6B,CAE7B,oBAAqB,CALrB,gCAAmC,CACnC,eAAgB,CAKhB,kBAA6B,CAA7B,4BAEF,CAEA,eAIE,wCAA2C,CAF3C,aAAsB,CAAtB,qBAAsB,CADtB,iBAA8B,CAA9B,6BAA8B,CAE9B,oBAA6B,CAA7B,4BAEF,CAEA,WASE,wCAA2C,CAP3C,kCAA2B,CAA3B,0BAA2B,CAD3B,oBAAoC,CAEpC,sBAA0C,CAC1C,kBAA+B,CAA/B,8BAA+B,CAI/B,8DAA4B,CAA5B,2BAA4B,CAF5B,kBAA2B,CAA3B,0BAA2B,CAC3B,eAAgB,CAFhB,cAAuB,CAAvB,sBAKF,CAEA,gBAGE,aAAsB,CAAtB,qBAAsB,CAFtB,kBAA8B,CAA9B,6BAA8B,CAC9B,iBAAkB,CAElB,mBAA6B,CAA7B,4BACF,CAEA,kBAEE,aAAqB,CAArB,oBAAqB,CADrB,iBAA8B,CAA9B,6BAA8B,CAE9B,eACF,CAGA,UAGE,aAAmB,CAAnB,uBAAmB,CAFnB,YAAa,CAEb,QAAmB,CAAnB,kBAAmB,CADnB,wDAA2D,CAI3D,aAAc,CADd,gBAAiB,CADjB,YAAuB,CAAvB,sBAAuB,CAGvB,iBAAkB,CAClB,SACF,CAGA,yBACE,UAEE,UAAmB,CAAnB,kBAAmB,CADnB,yBAA0B,CAE1B,aAAuB,CAAvB,sBACF,CACF,CAEA,+CACE,UAEE,UAAmB,CAAnB,kBAAmB,CADnB,wDAA2D,CAE3D,cAAuB,CAAvB,sBACF,CACF,CAEA,gDACE,UAEE,QAAmB,CAAnB,kBAAmB,CADnB,wDAEF,CACF,CAEA,UAUE,+BAAiC,CATjC,eAAwB,CAAxB,uBAAwB,CAIxB,wBAAiC,CAAjC,gCAAiC,CAHjC,kBAA+B,CAA/B,8BAA+B,CAE/B,8DAA4B,CAA5B,2BAA4B,CAG5B,cAAe,CAEf,eAAgB,CANhB,cAAuB,CAAvB,sBAAuB,CAKvB,iBAAkB,CAFlB,0BAAoC,CAApC,mCAKF,CAEA,iBAOE,kBAA0B,CAA1B,yBAA0B,CAN1B,UAAW,CAKX,UAAW,CAFX,MAAO,CAFP,iBAAkB,CAGlB,OAAQ,CAFR,KAAM,CAKN,mBAAoB,CACpB,oCAA8C,CAA9C,6CACF,CAEA,gBAGE,oBAA4B,CAA5B,2BAA4B,CAD5B,gEAA4B,CAA5B,2BAA4B,CAD5B,0BAGF,CAEA,uBACE,mBACF,CAEA,eAME,kBAAmB,CAHnB,kBAA0B,CAA1B,yBAA0B,CAC1B,kBAA+B,CAA/B,8BAA+B,CAK/B,UAAmB,CAAnB,kBAAmB,CAJnB,YAAa,CAKb,gBAAiB,CARjB,WAAY,CAKZ,sBAAuB,CACvB,kBAA6B,CAA7B,4BAA6B,CAG7B,0BAAoC,CAApC,mCAAoC,CAVpC,UAWF,CAGA,yBACE,eAGE,iBAAkB,CADlB,WAAY,CAEZ,oBAA6B,CAA7B,4BAA6B,CAH7B,UAIF,CACF,CAEA,+CACE,eAGE,kBAAmB,CADnB,WAAY,CADZ,UAGF,CACF,CAEA,+BAEE,kBAA+B,CAA/B,8BAA+B,CAD/B,iCAEF,CAEA,gBAGE,aAAsB,CAAtB,qBAAsB,CAFtB,iBAA8B,CAA9B,6BAA8B,CAC9B,eAAgB,CAEhB,mBAA6B,CAA7B,4BACF,CAEA,sBACE,aAAsB,CAAtB,qBAAsB,CACtB,iBAA8B,CAA9B,6BAA8B,CAC9B,eACF,CAGA,yBACE,gBACE,kBAA8B,CAA9B,6BAA8B,CAC9B,oBAA6B,CAA7B,4BACF,CAEA,sBACE,gBAA8B,CAA9B,6BACF,CACF,CAEA,+CACE,gBACE,kBAA8B,CAA9B,6BACF,CAEA,sBACE,gBAA8B,CAA9B,6BACF,CACF,CA4CA,gBAAkB,6BAAiC,CACnD,sBAAwB,mCAAuC,CAE/D,gBAAkB,4BAA+B,CAGjD,mBAAqB,mBAAuB,CAC5C,mBAAqB,mBAAuB,CAC5C,mBAAqB,mBAAuB,CAC5C,mBAAqB,mBAAuB,CAC5C,mBAAqB,mBAAuB,CAG5C,KAME,8BAA+B,CAE/B,iBAA8B,CAA9B,6BAA8B,CAD9B,eAAgB,CAHhB,SAAmB,CAAnB,kBAAmB,CAKnB,aAAc,CAJd,qBAAsC,CAAtC,qCAAsC,CAKtC,oBAAqB,CAGrB,0BAAoC,CAApC,mCAIF,CAEA,cAEE,kBAAmB,CADnB,UAAY,CAEZ,wBACF,CAEA,aACE,kBAA0B,CAA1B,yBAA0B,CAE1B,iCAAiC,CAAjC,gCACF,CAEA,kCACE,kBAA+B,CAA/B,8BAA+B,CAE/B,oCAAkD,CADlD,0BAEF,CAEA,eAGE,+BAAgC,CADhC,oBAEF,CAEA,oCACE,kBAA0B,CAA1B,yBAA0B,CAC1B,UAAmB,CAAnB,kBAAmB,CACnB,0BACF,CAEA,aACE,kBAA0B,CAA1B,yBAA0B,CAE1B,iCAAiD,CADjD,UAAmB,CAAnB,kBAEF,CAEA,kCACE,kBAA+B,CAA/B,8BAA+B,CAC/B,0BACF,CAEA,YACE,kBAAyB,CAAzB,wBAAyB,CAEzB,iCAAgD,CADhD,UAAmB,CAAnB,kBAEF,CAEA,iCACE,kBAA8B,CAA9B,6BAA8B,CAC9B,0BACF,CAEA,QAEE,gBAA8B,CAA9B,6BAA8B,CAD9B,kBAAsC,CAAtC,qCAEF,CAEA,QAEE,cAAgC,CAAhC,+BAAgC,CADhC,iBAAsC,CAAtC,qCAEF,CAKA,QAAU,sBAA0B,CACpC,SAAW,uBAA2B,CACtC,UAAY,wBAA4B,CACxC,gBAAkB,8BAAkC,CACpD,QAAU,sBAA0B,CAGpC,wBAA0B,gCAAoC,CAC9D,yBAA2B,uCAA2C,CACtE,wBAA0B,sCAA0C,CACpE,uBAAyB,oCAAwC,CACjE,qBAAuB,kCAAsC,CAE7D,oBAAsB,4BAAgC,CACtD,mBAAqB,gCAAoC,CACzD,iBAAmB,8BAAkC,CACrD,qBAAuB,6BAAiC,CAExD,uBAAyB,+BAAmC,CAC5D,oBAAsB,4BAAgC,CACtD,WAAa,wBAA4B,CACzC,aAAe,0BAA8B,CAG7C,OAAS,oBAA8B,CAA9B,4BAAgC,CACzC,OAAS,mBAA8B,CAA9B,4BAAgC,CACzC,OAAS,oBAA8B,CAA9B,4BAAgC,CACzC,OAAS,kBAA8B,CAA9B,4BAAgC,CACzC,OAAS,qBAA8B,CAA9B,4BAAgC,CACzC,OAAS,oBAA8B,CAA9B,4BAAgC,CAGzC,KAAO,kBAAsB,CAC7B,KAAO,uBAAiC,CAAjC,+BAAmC,CAC1C,KAAO,sBAAiC,CAAjC,+BAAmC,CAC1C,KAAO,uBAAiC,CAAjC,+BAAmC,CAC1C,KAAO,qBAAiC,CAAjC,+BAAmC,CAC1C,KAAO,wBAAiC,CAAjC,+BAAmC,CAC1C,KAAO,uBAAiC,CAAjC,+BAAmC,CAE1C,MAAQ,sBAA0B,CAClC,MAAQ,2BAAqC,CAArC,mCAAuC,CAC/C,MAAQ,0BAAqC,CAArC,mCAAuC,CAC/C,MAAQ,2BAAqC,CAArC,mCAAuC,CAC/C,MAAQ,yBAAqC,CAArC,mCAAuC,CAC/C,MAAQ,4BAAqC,CAArC,mCAAuC,CAC/C,MAAQ,2BAAqC,CAArC,mCAAuC,CAE/C,MAAQ,yBAA6B,CACrC,MAAQ,8BAAwC,CAAxC,sCAA0C,CAClD,MAAQ,6BAAwC,CAAxC,sCAA0C,CAClD,MAAQ,8BAAwC,CAAxC,sCAA0C,CAClD,MAAQ,4BAAwC,CAAxC,sCAA0C,CAClD,MAAQ,+BAAwC,CAAxC,sCAA0C,CAClD,MAAQ,8BAAwC,CAAxC,sCAA0C,CAElD,MAAQ,uBAA2B,CACnC,MAAQ,4BAAsC,CAAtC,oCAAwC,CAChD,MAAQ,2BAAsC,CAAtC,oCAAwC,CAChD,MAAQ,4BAAsC,CAAtC,oCAAwC,CAChD,MAAQ,0BAAsC,CAAtC,oCAAwC,CAEhD,MAAQ,wBAA4B,CACpC,MAAQ,6BAAuC,CAAvC,qCAAyC,CACjD,MAAQ,4BAAuC,CAAvC,qCAAyC,CACjD,MAAQ,6BAAuC,CAAvC,qCAAyC,CACjD,MAAQ,2BAAuC,CAAvC,qCAAyC,CAGjD,KAAO,mBAAuB,CAC9B,KAAO,wBAAkC,CAAlC,gCAAoC,CAC3C,KAAO,uBAAkC,CAAlC,gCAAoC,CAC3C,KAAO,wBAAkC,CAAlC,gCAAoC,CAC3C,KAAO,sBAAkC,CAAlC,gCAAoC,CAC3C,KAAO,yBAAkC,CAAlC,gCAAoC,CAC3C,KAAO,wBAAkC,CAAlC,gCAAoC,CAE3C,MAAQ,uBAA2B,CACnC,MAAQ,4BAAsC,CAAtC,oCAAwC,CAChD,MAAQ,2BAAsC,CAAtC,oCAAwC,CAChD,MAAQ,4BAAsC,CAAtC,oCAAwC,CAChD,MAAQ,0BAAsC,CAAtC,oCAAwC,CAEhD,MAAQ,0BAA8B,CACtC,MAAQ,+BAAyC,CAAzC,uCAA2C,CACnD,MAAQ,8BAAyC,CAAzC,uCAA2C,CACnD,MAAQ,+BAAyC,CAAzC,uCAA2C,CACnD,MAAQ,6BAAyC,CAAzC,uCAA2C,CAEnD,MAAQ,wBAA4B,CACpC,MAAQ,6BAAuC,CAAvC,qCAAyC,CACjD,MAAQ,4BAAuC,CAAvC,qCAAyC,CACjD,MAAQ,6BAAuC,CAAvC,qCAAyC,CACjD,MAAQ,2BAAuC,CAAvC,qCAAyC,CAEjD,MAAQ,yBAA6B,CACrC,MAAQ,8BAAwC,CAAxC,sCAA0C,CAClD,MAAQ,6BAAwC,CAAxC,sCAA0C,CAClD,MAAQ,8BAAwC,CAAxC,sCAA0C,CAClD,MAAQ,4BAAwC,CAAxC,sCAA0C,CAGlD,aAAe,2BAA+B,CAC9C,WAAa,yBAA6B,CAC1C,YAAc,0BAA8B,CAE5C,cAAgB,uBAAgC,CAAhC,8BAAkC,CAClD,gBAAkB,uBAAiC,CAAjC,+BAAmC,CACrD,cAAgB,uBAAgC,CAAhC,8BAAkC,CAClD,aAAe,uBAA+B,CAA/B,6BAAiC,CAChD,cAAgB,uBAAgC,CAAhC,8BAAkC,CAClD,WAAa,uBAA6B,CAA7B,2BAA+B,CAC5C,YAAc,oBAA8B,CAA9B,4BAAgC,CAC9C,WAAa,uBAAiC,CAAjC,+BAAmC,CAChD,YAAc,uBAAiC,CAAjC,+BAAmC,CAEjD,oBAAsB,yBAA6B,CACnD,oBAAsB,yBAA6B,CACnD,sBAAwB,yBAA6B,CACrD,kBAAoB,yBAA6B,CAEjD,SAAW,2BAAyC,CAAzC,uCAA2C,CACtD,WAAa,wBAA2C,CAA3C,yCAA6C,CAC1D,SAAW,4BAAyC,CAAzC,uCAA2C,CACtD,SAAW,2BAAyC,CAAzC,uCAA2C,CACtD,UAAY,0BAA0C,CAA1C,wCAA4C,CAGxD,YAAc,kCAA2C,CAA3C,yCAA6C,CAC3D,cAAgB,kCAA4C,CAA5C,0CAA8C,CAC9D,YAAc,kCAA2C,CAA3C,yCAA6C,CAC3D,WAAa,kCAA0C,CAA1C,wCAA4C,CACzD,YAAc,kCAA2C,CAA3C,yCAA6C,CAC3D,SAAW,kCAAwC,CAAxC,sCAA0C,CACrD,UAAY,+BAAyC,CAAzC,uCAA2C,CACvD,UAAY,kCAA4C,CAA5C,0CAA8C,CAC1D,SAAW,kCAA4C,CAA5C,0CAA8C,CAGzD,QAAU,kCAA4C,CAA5C,0CAA8C,CACxD,UAAY,kBAAsB,CAClC,gBAAkB,8BAAuC,CAAvC,qCAAyC,CAC3D,kBAAoB,8BAAwC,CAAxC,sCAA0C,CAC9D,gBAAkB,8BAAuC,CAAvC,qCAAyC,CAC3D,eAAiB,8BAAsC,CAAtC,oCAAwC,CAEzD,SAAW,6BAA0C,CAA1C,wCAA4C,CACvD,YAAc,+BAA0C,CAA1C,wCAA4C,CAC1D,YAAc,8BAA0C,CAA1C,wCAA4C,CAC1D,YAAc,4BAA0C,CAA1C,wCAA4C,CAC1D,cAAgB,8BAA4C,CAA5C,0CAA8C,CAG9D,MAAQ,mBAAuB,CAC/B,MAAQ,mBAAuB,CAC/B,MAAQ,mBAAuB,CAC/B,OAAS,oBAAwB,CACjC,QAAU,oBAAwB,CAGlC,MAAQ,sBAA0B,CAClC,aAAe,uDAA6D,CAC5E,aAAe,uDAA6D,CAC5E,aAAe,uDAA6D,CAC5E,aAAe,uDAA6D,CAC5E,gBAAkB,kEAAwE,CAG1F,yBACE,uDACE,mCACF,CACF,CAEA,+CACE,0BACE,uDACF,CACA,gBACE,kEACF,CACF,CAEA,gDACE,aACE,uDACF,CACF,CAGA,MAAQ,oBAAwB,CAChC,MAAQ,oBAAwB,CAChC,MAAQ,oBAAwB,CAChC,OAAS,qBAAyB,CAClC,QAAU,qBAAyB,CAGnC,mBAAqB,2BAA+B,CACpD,mBAAqB,2BAA+B,CACpD,gBAAkB,wBAA4B,CAC9C,iBAAmB,iCAA2B,CAA3B,yBAA6B,CAGhD,WAAa,0CAAuC,CAAvC,qCAAyC,CACtD,QAAU,sEAAuC,CAAvC,qCAAyC,CACnD,WAAa,wEAAuC,CAAvC,qCAAyC,CACtD,WAAa,0EAAuC,CAAvC,qCAAyC,CACtD,aAAe,yBAA6B,CAK5C,4BACE,yBAAmC,CAAnC,iCAAmC,CAEnC,0BAA4B,CAC5B,2BAA6B,CAC7B,2BAAuC,CAAvC,qCAAuC,CACvC,4BAAwC,CAAxC,sCAAwC,CAJxC,oBAKF,CAEA,WACE,0BACF,CAGA,yBACE,4BACE,4BAAuC,CAAvC,qCAAuC,CACvC,6BAAwC,CAAxC,sCACF,CACF,CAEA,+CACE,4BACE,6BAAuC,CAAvC,qCAAuC,CACvC,8BAAwC,CAAxC,sCACF,CACF,CAEA,yBACE,WACE,0BACF,CACF,CAEA,0BACE,WACE,0BACF,CACF,CAGA,OAEE,qBAAuB,CACvB,wBAA0B,CAF1B,6BAGF,CAGA,KACE,kBACF,CAEA,mBACE,uBAAkC,CAAlC,gCACF,CAGA,kBACE,uBAAiC,CAAjC,+BACF,CAEA,EACE,uBAAiC,CAAjC,+BACF,CAGA,EACE,uBAAgC,CAAhC,8BAAgC,CAChC,8BACF,CAEA,QACE,uBAAqC,CAArC,mCACF,CAGA,MACE,yBAA2B,CAE3B,kBAAoB,CADpB,mBAEF,CAGA,MAEE,kCAAoC,CADpC,oBAEF,CAEA,MAEE,yCAAmD,CAAnD,iDAAmD,CADnD,wBAAkC,CAAlC,gCAEF,CAEA,GACE,4BAAqC,CAArC,mCAAqC,CAErC,uBAAiC,CAAjC,+BAAiC,CADjC,yBAEF,CAKA,KACE,cACF,CAGA,yBACE,KAAO,cAAiB,CAExB,GAAK,2BAA+B,CACpC,GAAK,0BAA8B,CACnC,GAAK,2BAA+B,CACpC,GAAK,4BAAgC,CACrC,GAAK,wBAA4B,CACjC,GAAK,2BAAgC,CACvC,CAEA,+CACE,KAAO,cAAiB,CAExB,GAAK,wBAA4B,CACjC,GAAK,2BAA+B,CACpC,GAAK,0BAA8B,CACnC,GAAK,2BAA+B,CACpC,GAAK,4BAAgC,CACrC,GAAK,wBAA4B,CACnC,CAEA,gDACE,KAAO,cAAiB,CAC1B,CAEA,0BACE,KAAO,cAAiB,CAC1B,CAGA,SAAW,0BAA+B,CAC1C,SAAW,wBAA4B,CACvC,SAAW,2BAA+B,CAC1C,SAAW,0BAA8B,CACzC,SAAW,wBAA4B,CACvC,UAAY,0BAA8B,CAC1C,UAAY,wBAA4B,CAGxC,yBACE,SAAW,2BAAgC,CAC3C,SAAW,2BAAgC,CAC3C,SAAW,wBAA4B,CACvC,SAAW,2BAA+B,CAC1C,SAAW,0BAA8B,CACzC,UAAY,wBAA4B,CACxC,UAAY,0BAA8B,CAC5C,CAEA,+CACE,SAAW,0BAA+B,CAC1C,SAAW,2BAAgC,CAC3C,SAAW,4BAAgC,CAC3C,SAAW,4BAAgC,CAC3C,SAAW,2BAA+B,CAC1C,UAAY,2BAA+B,CAC3C,UAAY,2BAA+B,CAC7C,CAGA,yBACE,WAEE,kBAAoB,CADpB,uBAAkC,CAAlC,gCAEF,CAEA,MACE,8BAAwC,CAAxC,sCAAwC,CACxC,wBAAkC,CAAlC,gCACF,CAEA,KAIE,2BAA8B,CAF9B,6BAAwC,CAAxC,sCAAwC,CACxC,6BAAiD,CAAjD,+CAEF,CAEA,cANE,oBAUF,CAJA,SAEE,qBAAuB,CACvB,2BACF,CAEA,QACE,+BACF,CAEA,SACE,uBAAkC,CAAlC,gCACF,CAEA,QAEE,6BAAwC,CAAxC,sCAAwC,CADxC,wBAAkC,CAAlC,gCAEF,CACF,CAEA,+CACE,WACE,wBAAkC,CAAlC,gCACF,CAEA,MACE,8BAAwC,CAAxC,sCAAwC,CACxC,sBAAkC,CAAlC,gCACF,CAEA,KAEE,2BAA8B,CAD9B,gCAAiD,CAAjD,+CAEF,CAEA,SACE,wBAAkC,CAAlC,gCACF,CAEA,QACE,sBAAkC,CAAlC,gCACF,CACF,CAEA,gDASE,0BACE,sBAAkC,CAAlC,gCACF,CACF,CAaA,4DACE,6BACF,CAGA,kCACE,4EACF,CAGA,0CACE,8BAAgC,CAEhC,uBAAyB,CADzB,+BAEF,CAGA,qBACE,2BAA6B,CAE7B,wBAA0B,CAD1B,uBAEF,CAGA,gDAAsD,0BAA6B,CAA6B,uBAA0B,CAArD,sBAAuD,CAC5I,gDAAsD,wBAA0B,CAA0B,qBAAuB,CAA/C,oBAAiD,CACnI,sDAA4D,2BAA6B,CAA6B,wBAA0B,CAArD,uBAAuD,CAClJ,gDAAsD,0BAA4B,CAA4B,uBAAyB,CAAnD,sBAAqD,CACzI,gDAAsD,wBAA0B,CAA0B,qBAAuB,CAA/C,oBAAiD,CACnI,mDAAyD,0BAA4B,CAA4B,uBAAyB,CAAnD,sBAAqD,CAC5I,mDAAyD,wBAA0B,CAA0B,qBAAuB,CAA/C,oBAAiD,CACtI,mDAAyD,0BAA4B,CAA4B,uBAAyB,CAAnD,sBAAqD,CAG5I,sDACE,2BAA6B,CAE7B,wBAA0B,CAC1B,6BAAuC,CAAvC,qCAAuC,CAFvC,uBAGF,CAGA,kEACE,wBAA0B,CAE1B,qBAAuB,CADvB,oBAEF,CAGA,6CACE,0BAA4B,CAE5B,uBAAyB,CADzB,sBAEF,CAGA,oCACE,wBAA0B,CAE1B,qBAAuB,CACvB,4BAAuC,CAAvC,qCAAuC,CAFvC,oBAGF,CAGA,4DACE,2BAA6B,CAE7B,wBAA0B,CAD1B,uBAEF,CAGA,sBAEE,qBAAuB,CADvB,8BAA0C,CAA1C,wCAA0C,CAE1C,0EAAuC,CAAvC,qCACF,CAEA,cACE,4BAAqC,CAArC,mCAAqC,CAErC,4BAA8B,CAC9B,yCAA+D,CAA/D,6DAA+D,CAF/D,oBAGF,CAEA,cAEE,4BAAqC,CAArC,mCAAqC,CACrC,yCAA+D,CAA/D,6DAA+D,CAF/D,sCAAgD,CAAhD,8CAGF,CAGA,kBACE,uBAAgC,CAAhC,8BACF,CAGA,OAEE,qBAAuB,CADvB,8BAA0C,CAA1C,wCAA0C,CAE1C,sBAAkC,CAAlC,gCACF,CAEA,eACE,4BAA2C,CAA3C,yCAA2C,CAC3C,uBAAqC,CAArC,mCACF,CAEA,cACE,4BAA0C,CAA1C,wCAA0C,CAC1C,uBAAoC,CAApC,kCACF,CAEA,eACE,4BAA2C,CAA3C,yCAA2C,CAC3C,uBAAqC,CAArC,mCACF,CAEA,YACE,4BAAwC,CAAxC,sCAAwC,CACxC,uBAAkC,CAAlC,gCACF,CAKA,yBAEE,sBAA0B,2BAAgC,CAC1D,SAAW,0BAA+B,CAC1C,sBAA2B,2BAAgC,CAC3D,SAAW,wBAA4B,CACvC,SAAW,4BAAgC,CAC3C,UAAY,2BAA+B,CAG3C,KAAO,0BAA+B,CACtC,QAAU,2BAAgC,CAC1C,QAAU,2BAAgC,CAG1C,WAAa,0BAA+B,CAG5C,YAAc,wBAA4B,CAC1C,WAAa,0BAA+B,CAC9C,CAEA,+CAEE,sBAA0B,0BAA+B,CAEzD,+BAA2B,2BAAgC,CAC3D,SAAW,wBAA4B,CACvC,SAAW,4BAAgC,CAC3C,UAAY,4BAAgC,CAG5C,KAAO,2BAAgC,CACvC,QAAU,0BAA+B,CACzC,QAAU,wBAA4B,CAGtC,WAAa,2BAAgC,CAC/C,CAGA,yBACE,qBACE,+BACF,CAGA,gDAAsD,0BAA+B,CACrF,gDAAsD,2BAAgC,CACtF,sDAA4D,wBAA4B,CACxF,gDAAsD,4BAAgC,CACtF,gDAAsD,2BAA+B,CACvF,CAGA,uBAEE,kBAAmB,CAGnB,oBAAiC,CAAjC,gCAAiC,CAJjC,mBAAoB,CAKpB,iBAA8B,CAA9B,6BAA8B,CAC9B,eAAgB,CAJhB,SAAmB,CAAnB,kBAAmB,CACnB,oBAAsC,CAAtC,qCAIF,CAEA,uBACE,kBAAgC,CAAhC,+BAAgC,CAChC,aACF,CAEA,kBACE,kBAA8B,CAA9B,6BAA8B,CAC9B,aAAyB,CAAzB,wBACF,CAEA,qBACE,kBAAgC,CAAhC,+BAAgC,CAChC,aACF,CAEA,kBACE,kBAA+B,CAA/B,8BAA+B,CAC/B,aACF,CAGA,gBAIE,kBAAmB,CAFnB,kDAAkF,CAAlF,8EAAkF,CAClF,YAAa,CAEb,sBAAuB,CAJvB,gBAAiB,CAKjB,YAAuB,CAAvB,sBACF,CAEA,WAEE,kCAA2B,CAA3B,0BAA2B,CAD3B,oBAAqC,CAErC,sBAA0C,CAC1C,oBAAgC,CAAhC,+BAAgC,CAChC,gEAA4B,CAA5B,2BAA4B,CAG5B,eAAgB,CAFhB,YAAuB,CAAvB,sBAAuB,CACvB,UAEF,CAEA,WAKE,kBAA+B,CAA/B,8BAA+B,CAD/B,aAAc,CAFd,WAAY,CACZ,oBAA6B,CAA7B,4BAA6B,CAF7B,UAKF,CAEA,YAIE,aAAsB,CAAtB,qBAAsB,CAFtB,gBAA+B,CAA/B,8BAA+B,CAC/B,eAAgB,CAEhB,oBAA6B,CAA7B,4BAA6B,CAJ7B,iBAKF,CAEA,eAEE,aAAsB,CAAtB,qBAAsB,CACtB,kBAA6B,CAA7B,4BAA6B,CAF7B,iBAGF,CAGA,YACE,eAAwB,CAAxB,uBAAwB,CAGxB,wBAAiC,CAAjC,gCAAiC,CAFjC,kBAA+B,CAA/B,8BAA+B,CAC/B,4DAA4B,CAA5B,2BAA4B,CAK5B,eAAgB,CAHhB,cAAuB,CAAvB,sBAAuB,CAEvB,iBAAkB,CADlB,0BAAoC,CAApC,mCAGF,CAEA,mBAOE,iDAAgF,CAAhF,4EAAgF,CANhF,UAAW,CAKX,UAAW,CAFX,MAAO,CAFP,iBAAkB,CAGlB,OAAQ,CAFR,KAKF,CAEA,kBACE,8DAA4B,CAA5B,2BAA4B,CAC5B,0BACF,CAEA,aAGE,aAAqB,CAArB,oBAAqB,CAFrB,kBAA+B,CAA/B,8BAA+B,CAC/B,eAAgB,CAEhB,mBAA6B,CAA7B,4BACF,CAEA,aACE,aAAsB,CAAtB,qBAAsB,CAItB,iBAA8B,CAA9B,6BAA8B,CAH9B,eAAgB,CAEhB,qBAAuB,CADvB,wBAGF,CAGA,eAEE,kBAAgC,CAAhC,+BAAgC,CAChC,oBAA4B,CAA5B,2BAA4B,CAF5B,aAAqB,CAArB,oBAGF,CAEA,aAEE,kBAA+B,CAA/B,8BAA+B,CAC/B,oBAA2B,CAA3B,0BAA2B,CAF3B,aAAoB,CAApB,mBAGF,CAEA,eAEE,kBAAgC,CAAhC,+BAAgC,CAChC,oBAA4B,CAA5B,2BAA4B,CAF5B,aAAqB,CAArB,oBAGF,CAEA,YAEE,kBAA6B,CAA7B,4BAA6B,CAC7B,oBAAyB,CAAzB,wBAAyB,CAFzB,aAAkB,CAAlB,iBAGF,CAGA,MAEE,0BAA2B,CAC3B,2BAA4B,CAC5B,2BAA4B,CAC5B,2BAA4B,CAC5B,2BAA4B,CAC5B,2BAA4B,CAC5B,2BAA4B,CAC5B,2BAA4B,CAC5B,2BAA4B,CAC5B,2BAA4B,CAG5B,0DAAqE,CACrE,2DAAsE,CACtE,6DAAwE,CAGxE,kEAAwF,CACxF,oEAAyF,CACzF,mEAAwF,CACxF,oCAAoD,CACpD,4CAA4D,CAG5D,oBAAqB,CACrB,kBAAmB,CACnB,mBAAoB,CACpB,gBAAiB,CACjB,mBAAoB,CAGpB,mCAAoC,CACpC,oCAAqC,CACrC,kCACF,CAGA,MACE,0BAA2B,CAC3B,2BAA4B,CAC5B,2BAA4B,CAC5B,2BAA4B,CAC5B,2BAA4B,CAC5B,2BAA4B,CAC5B,2BAA4B,CAC5B,2BAA4B,CAC5B,2BAA4B,CAC5B,2BACF,CAGA,EACE,qBACF,CAEA,KACE,sBACF,CAEA,KAKE,kCAAmC,CACnC,iCAAkC,CAFlC,qBAAyB,CADzB,aAAc,CAFd,kEAA8E,CAC9E,eAKF,CAIA,YAEE,kBAAmB,CAQnB,WAAY,CAHZ,oBAA+B,CAA/B,8BAA+B,CAI/B,cAAe,CAVf,mBAAoB,CAIpB,iBAA8B,CAA9B,6BAA8B,CAC9B,eAAgB,CAHhB,sBAAuB,CAMvB,YAAa,CALb,qBAAsC,CAAtC,qCAAsC,CAItC,0BAAoC,CAApC,mCAIF,CAEA,kBACE,gDAA2E,CAA3E,2DACF,CAEA,aACE,kBAA8B,CAA9B,6BAA8B,CAE9B,4DAA4B,CAA5B,2BAA4B,CAD5B,kBAEF,CAEA,mBACE,kBAA8B,CAA9B,6BAA8B,CAC9B,8DAA4B,CAA5B,2BAA4B,CAC5B,0BACF,CAEA,eACE,uBAAwB,CAExB,wBAAoC,CAApC,mCAAoC,CACpC,gCAA4B,CAA5B,2BAA4B,CAF5B,aAAyB,CAAzB,wBAGF,CAEA,qBACE,kBAA6B,CAA7B,4BAA6B,CAC7B,4DAA4B,CAA5B,2BACF,CAEA,WACE,gBAAuB,CACvB,aAAyB,CAAzB,wBACF,CAEA,iBACE,kBAA6B,CAA7B,4BACF,CAGA,aACE,eAAwB,CAAxB,uBAAwB,CAGxB,wBAAiC,CAAjC,gCAAiC,CADjC,4DAA4B,CAA5B,2BAGF,CAEA,mBACE,8DAA4B,CAA5B,2BACF,CAEA,kBACE,cACF,CAEA,wBACE,gEAA4B,CAA5B,2BAA4B,CAC5B,0BACF,CAEA,YAEE,iCAA0B,CAA1B,yBAA0B,CAD1B,gBAAoC,CAEpC,sBAA0C,CAC1C,kBAA+B,CAA/B,8BAA+B,CAC/B,4DAA4B,CAA5B,2BACF,CAGA,cAIE,eAAwB,CAAxB,uBAAwB,CACxB,wBAAiC,CAAjC,gCAAiC,CACjC,oBAA+B,CAA/B,8BAA+B,CAH/B,aAAsB,CAAtB,qBAAsB,CADtB,mBAAsC,CAAtC,qCAAsC,CAKtC,0BAAoC,CAApC,mCAAoC,CANpC,UAOF,CAEA,oBAEE,oBAAgC,CAAhC,+BAAgC,CAChC,8BAA6C,CAF7C,YAGF,CAEA,2BACE,aAAsB,CAAtB,qBACF,CAGA,YACE,oBAAqC,CAErC,+BAAwC,CAAxC,uCAAwC,CAGxC,UACF,CAGE,YAGE,eAAgB,CADhB,iBAAkB,CADlB,0CAGF,CAEA,kBAEE,sCAAqD,CADrD,sCAEF,CAEA,mBACE,sCACF,CAEA,mBACE,kEAA8E,CAG9E,eAAgB,CAFhB,qBAAuB,CACvB,cAAgB,CAEhB,+BACF,CAEA,aAEE,0BAEF,CAEA,mBAEE,+DACF,CAGA,qCAEE,uBACF,CAEA,iDAEE,0BACF,CAGA,wBACE,0BAAkC,CAElC,YACF,CAGA,uDAGE,0BAAkC,CAClC,iBAAkB,CAClB,YACF,CAGA,uCAEE,0BAAkC,CAClC,qBACF,CAQA,kDACE,0BACF,CAGA,yBACE,UAAY,mBAAqB,CAAE,oBAAwB,CAC3D,UAAY,iBAAkB,CAAE,kBAAqB,CACrD,UAAiC,oBAAsB,CAA3C,iBAA6C,CACzD,UAAY,aAAgB,CAC5B,SAAW,cAAiB,CAC5B,SAAW,aAAgB,CAC3B,aAAe,iBAAmB,CAAE,mBAAsB,CAC1D,eAAiB,cAAe,CAAE,kBAAqB,CACvD,aAAe,iBAAkB,CAAE,mBAAsB,CACzD,6CAAmD,iBAAqB,CACxE,cAAgB,cAAiB,CACjC,YAAc,cAAiB,CACjC,CAGA,yBACE,wBAEE,gBAAkB,CADlB,qBAEF,CAEA,+BACE,cAAe,CACf,cACF,CAEA,yBAEE,cAAe,CADf,aAEF,CAEA,8BACE,iBACF,CACF,CAGA,4EACE,YACE,WAAY,CACZ,iBACF,CAEA,+BACE,gBACF,CACF,CAGA,0BACE,+BACE,gBACF,CAEA,yBAEE,aAAc,CADd,YAEF,CACF,CAGA,UACE,gBACF,CAEA,MACE,aACF,CAEA,MACE,cACF,CAEA,MACE,YACF,CAEA,MACE,aACF,CAGA,cAEE,kCAA2B,CAA3B,0BAA2B,CAD3B,oBAAqC,CAErC,0BACF,CAGA,wBAGE,6BAAoC,CAFpC,kDAA6D,CAC7D,4BAA6B,CAE7B,oBACF,CAEA,UAGE,oBAA+B,CAA/B,8BAA+B,CAD/B,aAAsB,CAAtB,qBAAsB,CAKtB,aAAc,CAFd,eAAgB,CAJhB,kBAAsC,CAAtC,qCAAsC,CAKtC,oBAAqB,CAFrB,0BAAoC,CAApC,mCAIF,CAOA,iCAEE,kBAA6B,CAA7B,4BAA6B,CAD7B,aAAyB,CAAzB,wBAEF,CAGA,gBACE,kDAAmF,CAAnF,+EAAmF,CAEnF,gEAA4B,CAA5B,2BAA4B,CAD5B,UAAmB,CAAnB,kBAEF,CAEA,cAEE,kBAAmB,CAGnB,oBAA+B,CAA/B,8BAA+B,CAD/B,WAA+B,CAG/B,cAAe,CANf,YAAa,CAEb,mBAAsC,CAAtC,qCAAsC,CAKtC,oBAAqB,CAFrB,0BAAoC,CAApC,mCAGF,CAEA,oBAEE,oBAAoC,CADpC,UAAmB,CAAnB,kBAEF,CAEA,qBAEE,gBAAoC,CACpC,gCAA4B,CAA5B,2BAA4B,CAF5B,UAAmB,CAAnB,kBAGF,CAGA,eAEE,kDAA8E,CAA9E,0EAA8E,CAD9E,gBAEF,CAEA,kBAEE,gBAAiB,CACjB,iBAAkB,CAFlB,eAAgB,CAGhB,iBAAkB,CAClB,kBACF,CAEA,yBACE,kBACE,mBAAoB,CACpB,oBACF,CACF,CAEA,0BACE,kBACE,iBAAkB,CAClB,kBACF,CACF,CAGA,WAGE,aAAsB,CAAtB,qBAAsB,CAFtB,iBAAkB,CAClB,eAAgB,CAEhB,gBACF,CAEA,yBACE,WACE,cACF,CACF,CAEA,0BACE,WACE,iBACF,CACF,CAEA,WAGE,aAAsB,CAAtB,qBAAsB,CAFtB,kBAAmB,CACnB,eAAgB,CAEhB,gBACF,CAEA,yBACE,WACE,iBACF,CACF,CAEA,WAGE,aAAsB,CAAtB,qBAAsB,CAFtB,gBAAiB,CACjB,eAAgB,CAEhB,gBACF,CAEA,yBACE,WACE,kBACF,CACF,CAEA,eACE,iDAAuE,CAAvE,mEAIF,CAGA,oBACE,wCACF,CAEA,wBACE,4CACF,CAOA,UACE,mDAAyD,CACzD,kBAA2B,CAA3B,0BAA2B,CAC3B,mBAA+B,CAA/B,8BACF,CAEA,iBACE,iCAAkC,CAGlC,wBAAoC,CAApC,wBAAoC,CAApC,gCAAoC,CAApC,mCACF,CAGA,WACE,eAAwB,CAAxB,uBAAwB,CAGxB,wBAAiC,CAAjC,gCAAiC,CAFjC,kBAA+B,CAA/B,8BAA+B,CAC/B,4DAA4B,CAA5B,2BAA4B,CAE5B,cAAuB,CAAvB,sBAAuB,CACvB,0BAAoC,CAApC,mCACF,CAEA,iBACE,iCAA8B,CAA9B,6BAA8B,CAC9B,qBACF,CAEA,aAEE,wBAAiC,CAAjC,gCAAiC,CACjC,oBAA+B,CAA/B,8BAA+B,CAC/B,cAAe,CAHf,YAAuB,CAAvB,sBAAuB,CAIvB,0BAAoC,CAApC,mCACF,CAEA,mBACE,oBAAgC,CAAhC,+BAEF,CAEA,yCAHE,kBAA6B,CAA7B,4BAOF,CAJA,sBACE,oBAAgC,CAAhC,+BAAgC,CAEhC,aAAyB,CAAzB,wBACF,CAEA,qBAEE,kBAAgC,CAAhC,+BAAgC,CADhC,oBAA4B,CAA5B,2BAA4B,CAE5B,aACF,CAEA,uBAEE,kBAA+B,CAA/B,8BAA+B,CAD/B,oBAA2B,CAA3B,0BAA2B,CAE3B,aACF,CAGA,cAEE,kBAA2B,CAA3B,0BAA2B,CAE3B,UAAW,CACX,eAAgB,CAJhB,UAKF,CAEA,6BALE,oBAAiC,CAAjC,gCAUF,CALA,eAEE,iDAAuE,CAAvE,mEAAuE,CADvE,WAAY,CAGZ,2BACF,CAGA,cAEE,kBAAmB,CAEnB,oBAAiC,CAAjC,gCAAiC,CAHjC,mBAAoB,CAIpB,gBAA8B,CAA9B,6BAA8B,CAC9B,eAAgB,CAHhB,qBAAsC,CAAtC,qCAIF,CAEA,eACE,kBAA8B,CAA9B,6BAA8B,CAC9B,aAAyB,CAAzB,wBACF,CAEA,eACE,kBAAgC,CAAhC,+BAAgC,CAChC,aACF,CAEA,eACE,kBAAgC,CAAhC,+BAAgC,CAChC,aACF,CAEA,aACE,kBAA+B,CAA/B,8BAA+B,CAC/B,aACF,CAGF,iBACE,iBAA8B,CAA9B,6BACF,CAEA,yBACE,iBACE,cAAgC,CAAhC,+BACF,CACF,CAEA,yBACE,iBACE,kBAA8B,CAA9B,6BACF,CACF,CAEA,oBACE,YAAuB,CAAvB,sBACF,CAEA,yBACE,oBACE,cAAuB,CAAvB,sBACF,CACF,CAEA,yBACE,oBACE,YAAuB,CAAvB,sBACF,CACF,CAEA,mBACE,WAAsB,CAAtB,qBACF,CAEA,yBACE,mBACE,aAAsB,CAAtB,qBACF,CACF,CAEA,yBACE,mBACE,WAAsB,CAAtB,qBACF,CACF,CAEA,iBAGE,aAAmB,CAAnB,uBAAmB,CAFnB,YAAa,CAEb,QAAmB,CAAnB,kBAAmB,CADnB,yBAEF,CAEA,yBACE,iBAEE,UAAmB,CAAnB,kBAAmB,CADnB,mCAEF,CACF,CAEA,0BACE,iBACE,mCACF,CACF,CAEA,0BACE,iBACE,mCACF,CACF,CAGA,oBACE,SACF,CAEA,0BACE,kBACF,CAEA,0BACE,kBAAmB,CACnB,iBACF,CAEA,gCACE,kBACF,CAGA,aACE,UACE,sBACF,CACF,CAKA,OAEE,0BAAkC,CAAlC,iCAAkC,CAClC,yCAA0C,CAF1C,oBAA2B,CAA3B,0BAA2B,CAG3B,0BAAqC,CAArC,oCACF,CAGA,oBALE,kCAYF,CAPA,aAKE,0BAAkC,CAAlC,yCAAkC,CAAlC,iCAAkC,CAJlC,sDAAgG,CAGhG,0BAAqC,CAArC,oCAAqC,CAFrC,kBAA+B,CAA/B,8BAA+B,CAC/B,8DAA4B,CAA5B,2BAA4B,CAG5B,0BAAoC,CAApC,mCACF,CAEA,mBAEE,sCAA6B,CAA7B,4BAA6B,CAD7B,sCAEF,CAGA,qBACE,kDAAgF,CAAhF,4EACF,CAEA,kBACE,8DACF,CAEA,kBACE,uEACF,CAGA,uBAGE,6BAAoC,CAFpC,kDAAgF,CAAhF,4EAAgF,CAChF,4BAA6B,CAE7B,oBACF,CAGA,iBACE,6BACF,CAEA,kBACE,8BACF,CAEA,uBACE,8CACF,CAOA,mBACE,GACE,SAAU,CACV,0BACF,CACA,GACE,SAAU,CACV,uBACF,CACF,CAEA,wBACE,MAAW,uBAA0B,CACrC,IAAM,0BAA6B,CACrC,CChoHA,oBACI,GACI,SAAU,CACV,0BACJ,CACA,GACI,SAAU,CACV,uBACJ,CACJ,CAEA,uBACI,GACI,SAAU,CACV,2BACJ,CACA,GACI,SAAU,CACV,uBACJ,CACJ,CAEA,wBACI,GACI,SAAU,CACV,0BACJ,CACA,GACI,SAAU,CACV,uBACJ,CACJ,CAEA,kBACI,GACI,SACJ,CACA,GACI,SACJ,CACJ,CAEA,mBACI,GACI,SAAU,CACV,mBACJ,CACA,GACI,SAAU,CACV,kBACJ,CACJ,CAEA,kBACI,kBACI,uBACJ,CACA,QACI,+BACJ,CACA,IACI,+BACJ,CACA,IACI,+BACJ,CACJ,CAEA,iBACI,MAEI,SAAU,CADV,kBAEJ,CACA,IAEI,UAAY,CADZ,qBAEJ,CACJ,CAoBA,kBACI,MACI,mBACJ,CACA,IACI,wBACJ,CACA,IACI,uBACJ,CACA,IACI,wBACJ,CACA,IACI,sBACJ,CACA,IACI,uBACJ,CACA,OACI,mBACJ,CACJ,CAEA,iBACI,MACI,uBACJ,CACA,IACI,2BACJ,CACJ,CAGA,kBACI,+BACJ,CAEA,qBACI,kCACJ,CAEA,sBACI,mCACJ,CAEA,gBACI,6BACJ,CAEA,iBACI,8BACJ,CAEA,gBACI,mBACJ,CAEA,eACI,2BACJ,CAEA,cACI,iCACJ,CAEA,gBACI,+BACJ,CAEA,eACI,uCACJ,CAGA,mBAAqB,mBAAuB,CAC5C,mBAAqB,mBAAuB,CAC5C,mBAAqB,mBAAuB,CAC5C,mBAAqB,mBAAuB,CAC5C,mBAAqB,mBAAuB,CAC5C,mBAAqB,mBAAuB,CAC5C,mBAAqB,mBAAuB,CAC5C,mBAAqB,mBAAuB,CAG5C,YACI,0CACJ,CAEA,kBAEI,gEAAqF,CADrF,0BAEJ,CAEA,aACI,6BACJ,CAEA,mBACI,qBACJ,CAEA,YACI,uBACJ,CAEA,kBACI,6BACJ,CAGA,aAEI,cAAe,CADf,0CAEJ,CAEA,mBACI,0BACJ,CAEA,oBACI,uBAAwB,CACxB,wBACJ,CAGA,iBAGI,+BAAgC,CAFhC,qEAAyE,CACzE,yBAEJ,CAEA,eACI,yCACJ,CAGA,aAGI,eAAgB,CADhB,iBAAkB,CADlB,0CAGJ,CAEA,oBAOI,mDAAsF,CANtF,UAAW,CAKX,WAAY,CAFZ,UAAW,CAFX,iBAAkB,CAClB,KAAM,CAKN,mBAAqB,CAHrB,UAIJ,CAEA,0BACI,SACJ,CAEA,mBAEI,sCAAiD,CADjD,sCAEJ,CAGA,OACI,oBAAoC,CAEpC,sBACJ,CAEA,mBAJI,kCAA2B,CAA3B,0BAQJ,CAJA,YACI,oBAA8B,CAE9B,0BACJ,CAGA,eAGI,6BAAoC,CAFpC,kDAA6D,CAC7D,4BAA6B,CAE7B,oBACJ,CAEA,oBAGI,6BAAoC,CAFpC,kDAA6D,CAC7D,4BAA6B,CAE7B,oBACJ,CAGA,uCACI,yOAeI,cAAe,CACf,eACJ,CAEA,mBACI,cACJ,CACJ,CAGA,yBACI,kBACI,0BACJ,CAEA,mBACI,sCACJ,CAEA,mBACI,0BACJ,CACJ,CC1UA,iBACE,UAAY,CACZ,mBACF,CAGA,YAEE,0BAA2B,CAC3B,kCAAmC,CACnC,eAAgB,CAEhB,eAAgB,CADhB,iBAAkB,CAJlB,0CAMF,CAEA,mBAOE,sDAAgG,CADhG,QAAS,CALT,UAAW,CAGX,MAAO,CAIP,mBAAoB,CANpB,iBAAkB,CAGlB,OAAQ,CAFR,KAAM,CAMN,SACF,CAEA,cACE,iBAAkB,CAClB,SACF,CAEA,kBACE,+BAA8C,CAC9C,0BACF,CAGA,aAEE,8BAA2C,CAD3C,uBAEF,CAEA,mBACE,+BACF,CAEA,oBACE,oBACF,CAGA,mBAGE,6BAAoC,CAFpC,kDAA6D,CAC7D,4BAA6B,CAE7B,oBAAqB,CACrB,eAAgB,CAChB,qBAAuB,CACvB,+BACF,CAGA,4BAKE,oBAAqB,CAJrB,eAAgB,CAChB,qBAAuB,CAEvB,iBAAkB,CADlB,+BAGF,CAKA,sBACE,GACE,6BACF,CACA,IACE,iDACF,CACA,GACE,6BACF,CACF,CAGA,mBAWE,4BAA8B,CAV9B,kCAAoC,CACpC,4BAA8B,CAC9B,+BAAiC,CAEjC,kCAAoC,CAEpC,2BAA6B,CAQ7B,wCAAuD,CAXvD,oBAAyB,CAKzB,wBAA0B,CAC1B,sBAAwB,CAFxB,yBAA2B,CAI3B,iBAAmB,CACnB,wBAA0B,CAC1B,sBAAwB,CARxB,0BAUF,CAEA,yBACE,kCAAoC,CACpC,4BACF,CAEA,qBAEE,0BAAkC,CADlC,oBAEF,CAGA,sBACE,GACE,yBAA2B,CAC3B,gCACF,CACA,IACE,2BAA4B,CAC5B,oCACF,CACA,IACE,4BAA6B,CAC7B,oCACF,CACA,IACE,2BAA4B,CAC5B,qCACF,CACA,GACE,yBAA2B,CAC3B,gCACF,CACF,CAGA,gBACE,sDAAwD,CACxD,uBAAyB,CACzB,yBACF,CA2BA,qBACE,GACE,4BAA2C,CAC3C,UACF,CACA,IACE,gDAA8E,CAC9E,SACF,CACA,GACE,4BAA2C,CAC3C,UACF,CACF,CAGA,uCACE,uBACF,CAEA,sCACE,uBACF,CAGA,uCACE,sBACE,MACE,yBACF,CACF,CAEA,oBACE,MACE,yBACF,CACF,CAEA,qBACE,MACE,UACF,CACF,CACF,CAEA,YACE,8DAA0E,CAC1E,4BAA6B,CAE7B,oBAEF,CAEA,uBALE,6BAAoC,CAEpC,eAUF,CAPA,WACE,8DAA0E,CAC1E,4BAA6B,CAE7B,oBAAqB,CAErB,kBACF,CAGA,iDAIE,sCAAyC,CAFzC,yBAA2B,CAC3B,uCAAyC,CAEzC,iCAAiC,CACjC,oBAAuB,CACvB,wCACF,CAGA,4FAIE,sCAAyC,CADzC,oBAEF,CAEA,oBAGE,6BAAoC,CAGpC,yCAA0C,CAL1C,0EAAuF,CACvF,4BAA6B,CAE7B,oBAAqB,CACrB,eAEF,CAEA,mBACE,MACE,yBACF,CACA,IACE,4BACF,CACF,CAEA,oBACE,yBACF,CAGA,wBAEE,kBAAmB,CADnB,YAAa,CAEb,sBAAuB,CACvB,iBACF,CAEA,aAEE,+BAA8C,CAE9C,eAAgB,CADhB,iBAAkB,CAFlB,0CAIF,CAEA,mBAEE,+BAA6C,CAD7C,qCAEF,CAEA,oBAIE,iEAAsE,CACtE,iBAAkB,CAJlB,UAAW,CAEX,UAAW,CAGX,SAAU,CAJV,iBAAkB,CAKlB,2BAA6B,CAC7B,UACF,CAEA,0BAEE,mCAAoC,CADpC,UAEF,CAEA,kBACE,GACE,sBACF,CACA,GACE,uBACF,CACF,CAGA,yBACE,YACE,eACF,CAEA,yBAEE,gBAAiB,CADjB,aAEF,CAEA,8BACE,YACF,CAEA,4BACE,kBAAmB,CACnB,eACF,CAEA,aAEE,WAAY,CADZ,UAEF,CACF,CAEA,+CACE,8BACE,YACF,CAEA,4BACE,iBACF,CAEA,aAEE,cAAe,CADf,aAEF,CACF,CAEA,+CACE,4BACE,gBACF,CAEA,aAEE,aAAc,CADd,YAEF,CACF,CAEA,yBACE,4BACE,cACF,CAEA,aAEE,WAAY,CADZ,UAEF,CACF,CAEA,+CACE,mBACE,gBACF,CACF,CAEA,yBACE,mBACE,kBACF,CACF,CAKA,qBACE,MACE,8BAA6C,CAC7C,kBACF,CACA,IACE,8BAA6C,CAC7C,qBACF,CACF,CAEA,YACE,2CAA4C,CAC5C,6BACF,CAEA,kBAEE,uBAAwB,CADxB,+BAEF,CAGA,sBACE,MAGE,0BAA2B,CAF3B,UAAY,CACZ,mBAEF,CACA,IAGE,2BAA4B,CAF5B,SAAU,CACV,oBAEF,CACF,CAEA,gBACE,4CACF,CAGA,oBACE,MACE,8BAA6C,CAC7C,gCACF,CACA,IACE,sCACF,CACA,IACE,8BAA6C,CAC7C,mCACF,CACA,IACE,qCACF,CACF,CAEA,WACE,0CAA2C,CAC3C,6BACF,CAEA,iBAEE,uBAAwB,CADxB,+BAEF,CAGA,wBACE,GACE,SAAU,CACV,+CACF,CACA,IACE,SAAU,CACV,oDACF,CACA,IACE,SAAU,CACV,mDACF,CACA,IACE,SAAU,CACV,mDACF,CACA,GACE,SAAU,CACV,kDACF,CACF,CAEA,eACE,8CAA+C,CAC/C,kBACF,CAGA,yBACE,MACE,6BAA4C,CAC5C,UACF,CACA,IACE,6BAA4C,CAC5C,SACF,CACF,CAEA,mBACE,+CAAgD,CAChD,oBACF,CAGA,2BACE,GACE,SAAU,CACV,2BACF,CACA,GACE,SAAU,CACV,uBACF,CACF,CAEA,uBACE,sCACF,CAGA,0BACE,GACE,SAAU,CACV,oBACF,CACA,GACE,SAAU,CACV,kBACF,CACF,CAEA,uBACE,8CACF,CAGA,+BACE,GACE,SAAU,CACV,mBACF,CACA,GACE,SAAU,CACV,kBACF,CACF,CAEA,6BACE,mDACF,CAGA,uDAIE,kBAAmB,CADnB,YAAa,CAGb,aAAc,CAJd,WAAY,CAGZ,sBAAuB,CAJvB,UAMF,CAEA,iDAEE,qBAAuB,CACvB,kBAAoB,CAFpB,2BAA6B,CAG7B,oCACF,CAGA,iCAEE,aAAc,CADd,6BAAsB,CAAtB,qBAEF,CAGA,yBACE,eACE,wBACF,CAEA,iCACE,mBACF,CACF,CAEA,+CACE,eACE,4BACF,CACF,CAEA,yBACE,eACE,2BACF,CACF,CAGA,4BACE,GACE,SAAU,CACV,mBACF,CACA,GACE,SAAU,CACV,kBACF,CACF,CAEA,wBACE,gDACF,CAGA,2BACE,GACE,SAAU,CACV,0BACF,CACA,GACE,SAAU,CACV,uBACF,CACF,CAEA,wBACE,+CACF,CAGA,uCACE,oMAUE,wBACF,CAEA,mCAEE,wBACF,CACF,CC/oBA,WAAW,qBAAqB,CAAC,iBAAiB,CAAC,eAAe,CAAC,kPAAuN,YAAgB,iUAAsP,sVAA2P,CAA8J,WAAC,yBAAW,CAAyB,iBAAC,CAAiB,eAAC,CAAe,qPAA4O,CAAC,qUAA0N,iTAA4O,oDAAuK,+QAA0P,YAAkB,sBAAgB,qRAA4O,0TAAuO,mUAAgP,YAA2K,sBAAW,kBAA8B,gBAAkB,mPAAoP,yCAAgB,8RAA0O,6UAAuO,yCAA6K,kBAAuB,CAAC,oRAAyP,YAAgB,sTAA4O,8TAAyW,YAA4B,uBAAkB,kBAAqB,gBAA0B,CAAC,wPAAoQ,YAAqB,uBAAe,kBAAgB,gBAAe,yPAAmQ,YAAe,uBAAkB,kBAAe,gBAAuB,yPAA8O,wCAA0B,kBAA0C,wRAA0R,CAAC,0DAAmD,8BAA4B,oBAAkB,UAAe,uCAAyC,0BAAgB,6BAAyB,iBAAqB,sBAAmC,0BAAkB,SAAc,2BAAmB,UAAkB,kBAAsB,uCAAiC,aAAS,cAAkB,kBAAwB,4CAAqB,kBAA2B,CAAe,2BAA0B,oBAAkB,gBAAgB,eAAkB,CAAC,eAAc,iBAAc,sCAAgC,gBAAa,2BAAwC,CAAC,2CAAmC,oBAAW,sBAA6B,kBAAoB,gBAAmB,sBAA2B,CAAC,iBAAQ,gBAAgB,iBAAgB,gBAAwB,sBAAkB,oCAAyB,sBAA0B,kBAA+B,CAAC,kKAAuJ,yBAAoC,2CAAuC,yBAA0B,4DAA4D,iCAAkB,uDAAsD,2BAAyC,uCAAQ,2BAA+C,gBAAyB,oDAA+C,2BAAoC,kBAAkB,iGAAgF,qBAA0B,mBAAgC,iBAAkB,gCAA2B,kBAAqB,kBAAW,sBAAmB,oBAA0B,sBAA4B,2FAA0F,2EAA4E,kBAAgB,0EAA4E,oBAAgB,gGAA4F,mEAA4E,wBAAgB,mFAA0F,kMAAyL,0FAA8F,sFAA8E,iBAAkB,2EAA8E,qBAAkB,gFAA4E,gBAAwB,mEAA4E,iBAAc,iFAA4E,yBAAyB,6EAA4E,qBAAyB,oFAA4E,6EAAgB,aAA4E,6EAAyB,eAA4E,6EAAc,eAA4E,6EAAgB,eAA4E,6EAAiB,eAA8E,6EAAyB,0FAAuG,4FAAoG,6FAAoG,gGAA0F,gGAAqG,8FAAqG,oGAAqG,0FAAqG,qGAAqG,qGAAqG,4FAA8E,wBAAyB,6EAA8E,aAAyB,6EAA4E,eAAiB,6EAA4E,gBAAgB,+EAA4E,wBAAiB,+EAA0F,qGAA8F,oGAA6F,oGAA4F,0FAA4F,qGAA6F,qGAAiG,qGAA+F,qGAAoG,qGAAoG,uGAAoG,uGAAoG,wBAA4E,6EAAc,gBAA4E,6EAAyB,4FAAqG,6FAA4F,0FAA6F,8FAAuG,6FAAuG,4FAA2F,4FAA2F,+FAA2F,iGAA2F,6FAA2F,oGAA0F,oGAA4F,uBAA4E,6EAAiB,uBAA4E,6EAAkB,aAA8E,6EAAkB,wBAA8E,6EAAkB,wBAA4E,6EAAwB,eAA4E,6EAAe,gBAA4E,+EAAwB,wBAA4E,+EAAwB,wBAA4F,6EAA4E,2FAAwB,cAA4E,6EAAc,cAA4E,6EAAgB,cAA4E,6EAAiB,cAA8E,6EAAyB,0FAAuG,4FAAoG,6FAAoG,gGAAoG,gGAAoG,8FAA6F,oGAAoG,2FAAoG,oGAA0F,oGAA4F,4FAAuG,oGAAuG,0FAAoG,4FAA4E,gBAAwB,+EAA4E,wBAAwB,+EAA4E,wBAAuB,6EAA4E,uBAAwB,6EAA4E,uBAAwB,6EAA4E,uBAAwB,6EAA4E,uBAAwB,6EAA0F,6FAA8E,uBAAyB,6EAA8E,uBAAyB,6EAA8E,aAAwB,6EAA8E,8FAAwB,wBAA8E,+EAAuB,wBAA8E,CAAuB,4EAAC,uBAA8E,6EAAwB,uBAA8E,6EAAwB,uBAA8E,6EAAwB,sBAA8E,6EAAwB,uBAA8E,6EAAwB,uBAAgF,6EAAc,uBAAgF,6EAAyB,uBAA8E,6EAAwB,4FAAsG,yBAA8E,8EAAwB,wBAA8E,+EAAwB,uBAA8E,+EAAwB,uBAA8E,+EAAwB,uBAA8E,8EAAwB,wBAA8E,8EAAuB,uBAA8E,+EAAwB,sGAAwG,sGAAgJ,uBAA0B,+EAA0E,uBAA0B,iFAA2F,8FAAyF,wBAAqB,+EAAoE,uBAA2B,+EAA2E,sGAAmH,sGAAoF,uBAAmC,+EAAoF,sGAA4G,sGAAmG,uBAAkB,+EAAwF,sBAAsB,+EAA+E,uBAAc,iFAAmF,uBAAkB,iFAAuF,aAAgB,2BAA6B,kDAA+C,uBAAW,2BAAkC,uBAA6B,2BAA8B,uBAAgB,4CAA8B,uBAAwC,4CAAsC,uBAA0B,uBAAgB,oBAA8B,mDAA8C,iBAAkB,CAAS,2BAAqB,uBAAgB,4BAAsC,uBAAoB,oDAAoC,iBAA8B,8CAA6C,+CAAgD,wBAA+B,iDAAyC,oBAAmB,cAAe,6BAAqC,oBAAmB,sCAAuC,uDAAuC,eAA0B,sCAA6C,gBAAC,mBAAsB,eAAgB,YAAmB,qCAAuB,kBAAoC,8BAA6B,0CAA2B,oBAAkC,0CAA4C,iBAAU,eAAsB,gCAAqB,WAAkB,4BAAsB,YAAqB,iBAAkB,+BAAuB,aAAgB,8BAAuB,aAAqB,iBAAqB,iBAAC,CAAiB,UAAC,gDAA8C,UAAa,mBAAkB,eAAsB,kBAAc,WAAiB,CAAC,uBAAmB,wCAAkC,YAAc,yBAAkB,iCAAuC,QAAiB,CAAC,WAAQ,0CAA6C,kBAAkB,kCAA4B,wBAAiB,kBAAqB,+BAAiC,s9CCEtztB,iBAII,kBAAmB,CAFnB,+CAA6D,CAC7D,YAAa,CAIb,kEAA8E,CAF9E,sBAAuB,CAJvB,gBAAiB,CAKjB,YAAa,CAEb,iBACJ,CAEA,wBAOI,4HACyF,CAFzF,QAAS,CALT,UAAW,CAGX,MAAO,CAFP,iBAAkB,CAGlB,OAAQ,CAFR,KAAM,CAMN,SACJ,CAEA,YAII,kCAA2B,CAA3B,0BAA2B,CAD3B,oBAAqC,CAErC,sBAA0C,CAC1C,oBAAqB,CACrB,gEAAqF,CALrF,eAAgB,CAMhB,YAAa,CACb,iBAAkB,CAElB,uBAAyB,CAVzB,UAAW,CASX,SAEJ,CAEA,kBAEI,sCAAiD,CADjD,0BAEJ,CAGA,cAEI,kBAAmB,CADnB,iBAEJ,CAEA,YAKI,kBAAmB,CACnB,qCAA+C,CAF/C,aAAc,CAFd,WAAY,CACZ,oBAAqB,CAIrB,uBAAyB,CANzB,UAOJ,CAEA,kBAEI,qCAAgD,CADhD,qBAEJ,CAEA,aAOI,6BAAoC,CAFpC,kDAA6D,CAC7D,4BAA6B,CAE7B,oBAAqB,CALrB,aAAc,CAFd,kBAAmB,CACnB,eAAgB,CAOhB,eAAgB,CALhB,mBAMJ,CAEA,gBACI,aAAc,CAEd,iBAAmB,CACnB,eAAgB,CAFhB,kBAGJ,CAOA,uCACI,oBACJ,CAEA,uCAEI,aAAc,CACd,iBAAmB,CAFnB,eAAgB,CAGhB,mBACJ,CA4BA,WAEI,kDAA6D,CAG7D,WAAY,CACZ,oBAAsB,CAMtB,iCAAgD,CAThD,UAAY,CAMZ,cAAe,CAEf,mBAAoB,CAHpB,iBAAmB,CADnB,eAAgB,CAMhB,gBAAkB,CATlB,mBAAoB,CAMpB,uBAAyB,CATzB,UAaJ,CAEA,iBAGI,kDAA6D,CAD7D,iCAAgD,CADhD,0BAGJ,CAEA,kBACI,uBACJ,CAGA,cAGI,4BAA6B,CAD7B,kBAAmB,CADnB,iBAGJ,CAEA,gBACI,aAAc,CACd,iBAAmB,CACnB,QACJ,CAEA,YACI,aAAc,CACd,eAAgB,CAChB,oBAAqB,CACrB,uBACJ,CAEA,kBACI,aAAc,CACd,yBACJ,CAKA,yBACI,iBAEI,sBAAuB,CACvB,0BACJ,CAEA,YAGI,kBAAmB,CACnB,QAAS,CAHT,cAAe,CACf,cAGJ,CAEA,YAEI,WAAY,CACZ,kBAAmB,CAFnB,UAGJ,CAEA,aACI,gBAAiB,CACjB,mBACJ,CAEA,gBACI,eAAiB,CACjB,oBACJ,CAEA,2BACI,qBACJ,CAEA,YACI,YAGJ,CAEA,uBAHI,mBAAqB,CADrB,cAWJ,CAPA,WAMI,eAAgB,CADhB,eAAgB,CAJhB,uBAAwB,CAGxB,UAGJ,CAEA,cACI,mBACJ,CAEA,gBACI,eACJ,CACJ,CAGA,+CACI,iBACI,YACJ,CAEA,YACI,eAAgB,CAChB,YACJ,CAEA,YAEI,WAAY,CADZ,UAEJ,CAEA,aACI,iBACJ,CAEA,YAEI,kBAAoB,CADpB,qBAEJ,CAEA,WAEI,kBAAoB,CADpB,wBAEJ,CACJ,CAGA,gDACI,YACI,eAAgB,CAChB,cACJ,CAEA,YAEI,WAAY,CADZ,UAEJ,CACJ,CAGA,0BACI,YACI,eAAgB,CAChB,YACJ,CAEA,YAEI,WAAY,CADZ,UAEJ,CAEA,aACI,cACJ,CACJ,CAGA,qDACI,iBAEI,kBAAmB,CADnB,YAEJ,CAEA,YACI,cACJ,CAEA,YAEI,WAAY,CACZ,oBAAsB,CAFtB,UAGJ,CAEA,aACI,kBAAmB,CACnB,qBACJ,CAEA,gBACI,qBACJ,CAEA,2BACI,kBACJ,CACJ,CAEA,wBACI,qBAAsC,CAAtC,oCACJ,CAEA,0BACI,gBAAoC,CACpC,wBAA0C,CAA1C,wCAA0C,CAC1C,sCAAwE,CAAxE,+DACJ,CAEA,gCACI,eAAkC,CAClC,oBAAqC,CAArC,mCAAqC,CACrC,8BAA6C,CAC7C,0BACJ,CAEA,iBAII,eAAgB,CAEhB,qBAAuB,CAJvB,eAAgC,CAAhC,8BAAgC,CAChC,mBAAoD,CAApD,iDAAoD,CAEpD,wBAAyB,CAJzB,UAMJ,CAEA,aAII,4BAA8C,CAA9C,4CAA8C,CAF9C,iBAAkC,CAAlC,gCAAkC,CAClC,kBAAmC,CAAnC,iCAAmC,CAFnC,iBAIJ,CAEA,eACI,aAA8B,CAA9B,4BAA8B,CAC9B,eAAgB,CAChB,sCAAwE,CAAxE,+DACJ,CAEA,qBACI,aAAmC,CAAnC,iCAAmC,CACnC,yBACJ,CAGA,yCACI,iBACI,cAAgC,CAAhC,6BACJ,CAEA,MAEI,aAA+B,CAA/B,4BAA+B,CAD/B,cAA+B,CAA/B,6BAEJ,CAEA,YAEI,WAAY,CADZ,UAEJ,CAEA,aACI,iBAAuC,CAAvC,qCACJ,CACJ,CAEA,yCACI,MACI,YAA6B,CAA7B,2BACJ,CAEA,0BACI,cAAgC,CAAhC,6BACJ,CAEA,iBACI,mBAAqD,CAArD,iDACJ,CACJ,CCpZA,uBAKI,4BAA8B,CAJ9B,uBAAyB,CAGzB,sBAAwB,CAFxB,2BAA8B,CAI9B,oBAAuB,CAHvB,0BAIJ,CAGA,qBACI,kCAAoC,CACpC,8BAAgC,CAChC,uBAAyB,CACzB,4BACJ,CAGA,iCACI,uBAAyB,CACzB,yBACJ,CAEA,oBAII,kBAAmB,CAFnB,+CAA6D,CAC7D,YAAa,CAIb,kEAA8E,CAF9E,sBAAuB,CAJvB,gBAAiB,CAKjB,YAAa,CAEb,iBACJ,CAEA,2BAOI,4HACyF,CAFzF,QAAS,CALT,UAAW,CAGX,MAAO,CAFP,iBAAkB,CAGlB,OAAQ,CAFR,KAAM,CAMN,SACJ,CAEA,eAII,kCAA2B,CAA3B,0BAA2B,CAD3B,oBAAqC,CAErC,sBAA0C,CAC1C,oBAAqB,CACrB,gEAAqF,CAKrF,eAAgB,CAVhB,eAAgB,CAWhB,eAAgB,CALhB,YAAa,CACb,iBAAkB,CAElB,uBAAyB,CAVzB,UAAW,CASX,SAIJ,CAEA,qBAEI,sCAAiD,CADjD,0BAEJ,CAGA,iBAEI,kBAAmB,CADnB,iBAEJ,CAEA,eAKI,kBAAmB,CACnB,qCAA+C,CAF/C,aAAc,CAFd,WAAY,CACZ,oBAAqB,CAIrB,uBAAyB,CANzB,UAOJ,CAEA,qBAEI,qCAAgD,CADhD,qBAEJ,CAEA,mBAMI,kBAAmB,CAHnB,oBAAkC,CAClC,iBAAkB,CAClB,YAAa,CAHb,WAAY,CAKZ,sBAAuB,CACvB,oBAAqB,CAPrB,UAQJ,CAEA,qBAEI,aAAc,CADd,gBAEJ,CAEA,gBAOI,6BAAoC,CAFpC,kDAA6D,CAC7D,4BAA6B,CAE7B,oBAAqB,CALrB,aAAc,CAFd,kBAAmB,CACnB,eAAgB,CAOhB,eAAgB,CALhB,mBAMJ,CAEA,mBACI,aAAc,CAEd,iBAAmB,CACnB,eAAgB,CAFhB,kBAGJ,CAGA,kBACI,kBAAmB,CACnB,UACJ,CAEA,eACI,kDAA6D,CAC7D,wBAAyB,CACzB,kBAAmB,CAEnB,oBAAqB,CADrB,cAEJ,CAEA,gBAMI,kBAAmB,CAHnB,aAAc,CAEd,YAAa,CAJb,kBAAmB,CACnB,eAAgB,CAKhB,SAAW,CAHX,oBAIJ,CAEA,eACI,aAAc,CACd,iBAAmB,CAEnB,eACJ,CAEA,0BAJI,kBAMJ,CAEA,UAMI,gBAAoC,CAEpC,0BAAyC,CADzC,mBAAqB,CAJrB,UAAY,CACZ,oBAAsB,CACtB,aAIJ,CAEA,uBATI,kBAAmB,CADnB,YAsBJ,CAZA,aAGI,kBAAmB,CAEnB,iBAAkB,CADlB,UAAY,CAOZ,aAAc,CAFd,gBAAkB,CAClB,eAAgB,CARhB,aAAc,CAMd,sBAAuB,CAPvB,YAWJ,CAEA,WACI,aAAc,CACd,iBAAmB,CACnB,eACJ,CAEA,UACI,gBAAoC,CAGpC,0BAAyC,CAFzC,mBAAqB,CACrB,cAEJ,CAEA,WACI,aAAc,CACd,eAAiB,CAEjB,eAAgB,CADhB,QAEJ,CAEA,WAEI,iBAAkB,CAClB,eAAgB,CAChB,qBACJ,CAGA,gBAII,kBAAmB,CAEnB,wBAAyB,CADzB,oBAAsB,CAJtB,iBAAkB,CAElB,YAAa,CADb,iBAKJ,CAEA,aACI,aAAc,CACd,iBAAmB,CAEnB,eAAgB,CADhB,oBAEJ,CAEA,YACI,kDAA6D,CAE7D,WAAY,CAEZ,mBAAqB,CAKrB,8BAA6C,CAR7C,UAAY,CAMZ,cAAe,CAFf,iBAAmB,CACnB,eAAgB,CAHhB,qBAAuB,CAKvB,uBAEJ,CAEA,iCACI,kDAA6D,CAE7D,8BAA6C,CAD7C,0BAEJ,CAEA,qBACI,kBAAmB,CAInB,eAAgB,CAHhB,aAAc,CACd,kBAAmB,CACnB,cAEJ,CAGA,eACI,oBACJ,CAEA,8BACI,qBACJ,CAEA,0CAEI,aAAc,CACd,iBAAmB,CAFnB,eAAgB,CAGhB,mBACJ,CAEA,YAOI,kBAAmB,CAHnB,oBAAsB,CAItB,aAAc,CACd,mBAAoB,CACpB,eAAgB,CAThB,UAUJ,CAEA,kBAGI,eAAmB,CAEnB,0BACJ,CAEA,yBACI,aAAc,CACd,iBACJ,CAEA,gBAII,iBAAkB,CAClB,eAAgB,CAJhB,kBAKJ,CAMA,oBACI,gBACJ,CAEA,gBAEI,aAAc,CADd,gBAAkB,CAElB,iBACJ,CAEA,WAEI,kBAAmB,CAEnB,eAAgB,CADhB,mBAAqB,CAFrB,iBAIJ,CAEA,cAEI,kDAA6D,CAG7D,WAAY,CACZ,oBAAsB,CAMtB,iCAAgD,CAThD,UAAY,CAMZ,cAAe,CAEf,mBAAoB,CAHpB,iBAAmB,CADnB,eAAgB,CAMhB,gBAAkB,CATlB,mBAAoB,CAMpB,uBAAyB,CATzB,UAaJ,CAEA,oBAGI,kDAA6D,CAD7D,iCAAgD,CADhD,0BAGJ,CAEA,qBACI,uBACJ,CAEA,uBAEI,kBAAmB,CADnB,UAAY,CAEZ,cACJ,CAGA,iBAOI,sCAAuC,CADvC,0BAAyB,CADzB,iBAAkB,CAClB,qBAAyB,CALzB,oBAAqB,CAErB,WAAY,CAKZ,gBAAiB,CANjB,UAOJ,CAEA,gBACI,GACI,uBACJ,CACJ,CAKA,iBAGI,4BAA6B,CAD7B,kBAAmB,CADnB,iBAGJ,CAEA,mBACI,aAAc,CACd,iBAAmB,CACnB,QACJ,CAEA,eACI,aAAc,CACd,eAAgB,CAChB,oBAAqB,CACrB,uBACJ,CAEA,qBACI,aAAc,CACd,yBACJ,CAKA,yBACI,oBAEI,sBAAuB,CACvB,0BACJ,CAEA,eAGI,kBAAmB,CACnB,QAAS,CACT,eAAgB,CAJhB,cAAe,CACf,cAIJ,CAEA,eAEI,WAAY,CACZ,kBAAmB,CAFnB,UAGJ,CAEA,mBAEI,WAAY,CACZ,kBAAmB,CAFnB,UAGJ,CAEA,qBACI,iBACJ,CAEA,gBACI,gBAAiB,CACjB,mBACJ,CAEA,mBACI,eAAiB,CACjB,oBACJ,CAEA,8BACI,kBACJ,CAEA,YACI,YAGJ,CAEA,0BAHI,mBAAqB,CADrB,cAWJ,CAPA,cAMI,eAAgB,CADhB,eAAgB,CAJhB,uBAAwB,CAGxB,UAGJ,CAEA,iBACI,mBACJ,CAEA,mBACI,eACJ,CAEA,gBACI,eACJ,CACJ,CAGA,+CACI,oBACI,YACJ,CAEA,eACI,eAAgB,CAChB,YACJ,CAEA,eAEI,WAAY,CADZ,UAEJ,CAEA,mBAEI,WAAY,CADZ,UAEJ,CAEA,gBACI,iBACJ,CAEA,YAEI,kBAAoB,CADpB,qBAEJ,CAEA,cAEI,kBAAoB,CADpB,wBAEJ,CACJ,CAGA,gDACI,eACI,eAAgB,CAChB,cACJ,CAEA,eAEI,WAAY,CADZ,UAEJ,CAEA,mBAEI,WAAY,CADZ,UAEJ,CACJ,CAGA,0BACI,eACI,eAAgB,CAChB,YACJ,CAEA,eAEI,WAAY,CADZ,UAEJ,CAEA,mBAEI,WAAY,CADZ,UAEJ,CAEA,gBACI,cACJ,CACJ,CAGA,qDACI,oBAEI,kBAAmB,CADnB,cAEJ,CAEA,eAEI,eAAgB,CADhB,cAEJ,CAEA,eAEI,WAAY,CACZ,oBAAsB,CAFtB,UAGJ,CAEA,mBAEI,WAAY,CACZ,oBAAsB,CAFtB,UAGJ,CAEA,gBACI,kBAAmB,CACnB,qBACJ,CAEA,mBACI,qBACJ,CAEA,8BACI,qBACJ,CACJ,CAGA,yBACI,oBACI,aACJ,CAEA,eAEI,oBAAsB,CADtB,YAEJ,CAEA,eAEI,WAAY,CADZ,UAEJ,CAEA,mBAEI,WAAY,CADZ,UAEJ,CAEA,gBACI,iBACJ,CAEA,YAEI,iBAAmB,CADnB,eAEJ,CAEA,cAEI,iBAAmB,CADnB,YAEJ,CACJ,CCnnBA,MACI,uEAAuF,CAEvF,kEAA8E,CAI9E,kBAAoB,CALpB,gBAAiB,CAIjB,mBAAqB,CAFrB,iBAIJ,CAGA,cAGI,kBAAoB,CADpB,6BAA+B,CAD/B,0BAGJ,CAEA,iBAEI,8BAAiC,CADjC,2BAEJ,CAGA,yBACI,cAEI,+BAAkC,CADlC,4BAEJ,CAEA,iBAEI,+BAAkC,CADlC,4BAEJ,CACJ,CAGA,gDACI,cAEI,gCAAkC,CADlC,6BAEJ,CAEA,iBAEI,+BAAkC,CADlC,4BAEJ,CACJ,CAGA,0BACI,cAEI,+BAAiC,CADjC,4BAEJ,CAEA,iBAEI,6BAA+B,CAD/B,0BAEJ,CACJ,CAGA,YAA8C,8BAAiC,CAAjE,2BAAmE,CACjF,YAA+C,+BAAkC,CAAnE,4BAAqE,CACnF,YAA4C,6BAA+B,CAA7D,0BAA+D,CAK7E,YAAc,8BAAmC,CACjD,YAAc,6BAAkC,CAChD,YAAc,8BAAmC,CACjD,YAAc,4BAAgC,CAC9C,YAAc,+BAAmC,CACjD,aAAe,8BAAkC,CAEjD,YAAc,yBAA6B,CAE3C,qBAA2B,0BAA+B,CAC1D,qBAA2B,2BAAgC,CAC3D,qBAA2B,yBAA6B,CACxD,qBAA2B,4BAAgC,CAE3D,aAAe,oBAAyB,CACxC,aAAe,kBAAsB,CACrC,aAAe,qBAAyB,CAGxC,yBACI,2EAOI,8BAAiC,CADjC,2BAEJ,CAEA,qCAGI,6BACJ,CAEA,oFAII,0BACJ,CAEA,uCAGI,mBACJ,CACJ,CAGA,MACI,yBACJ,CAEA,QAEI,yBAA2B,CAD3B,sBAEJ,CAMA,iDAHI,2BAMJ,CAHA,qBACI,sBAEJ,CAGA,wDAII,kBACJ,CAGA,mDAII,yBAA2B,CAD3B,sBAEJ,CAGA,YAII,0BAA2B,CAC3B,kCAAmC,CACnC,iCAAgD,CALhD,uBAAgB,CAAhB,eAAgB,CAChB,KAAM,CACN,UAIJ,CAGA,yBACI,YACI,kBACJ,CAEA,kBACI,aACJ,CACJ,CAEA,+CACI,YACI,qBACJ,CAEA,kBACI,WACJ,CACJ,CAEA,yBACI,YACI,iBACJ,CAEA,kBACI,WACJ,CACJ,CAGA,WAQI,4BAA8B,CAJ9B,8BAAiC,CACjC,yCAAwD,CAExD,6BAA+B,CAL/B,4BAA+B,CAC/B,yBAA2B,CAM3B,gCAAkC,CARlC,+BAAkC,CAKlC,oDAIJ,CAGA,yBACI,WAEI,2BAA8B,CAE9B,8BAAiC,CAHjC,iCAAoC,CAEpC,oBAEJ,CACJ,CAEA,+CACI,WAEI,yBAA4B,CAD5B,iCAEJ,CACJ,CAEA,iBAEI,yCAAwD,CADxD,oCAEJ,CAEA,aACI,eACJ,CAGA,yBACI,WAGI,gCAAmC,CADnC,wBAA0B,CAD1B,iCAGJ,CAEA,aACI,gBACJ,CACJ,CAEA,yBACI,WAGI,+BAAkC,CADlC,4BAA8B,CAD9B,2BAGJ,CAEA,aACI,aACJ,CACJ,CAGA,cACI,iBAAkB,CAClB,iBAAkB,CAElB,SACJ,CAGA,yBACI,cACI,iBACJ,CACJ,CAEA,+CACI,cACI,mBACJ,CACJ,CAEA,gDACI,cACI,mBACJ,CACJ,CAEA,0BACI,cACI,mBACJ,CACJ,CAEA,YAKI,6BAAoC,CAFpC,8DAA0E,CAC1E,4BAA6B,CAE7B,oBAAqB,CALrB,gBAAiB,CAOjB,eAAgB,CADhB,oBAEJ,CAGA,yBACI,YACI,iBAAkB,CAElB,eAAgB,CADhB,kBAEJ,CACJ,CAEA,+CACI,YACI,iBAAkB,CAClB,qBACJ,CACJ,CAEA,gDACI,YACI,cACJ,CACJ,CAEA,0BACI,YACI,iBACJ,CACJ,CAEA,eAEI,aAAc,CADd,iBAAkB,CAElB,kBAAmB,CAEnB,gBAAiB,CACjB,iBAAkB,CAFlB,eAIJ,CAGA,yBACI,eACI,cAAe,CACf,oBAAqB,CACrB,cAAe,CACf,eACJ,CACJ,CAEA,+CACI,eACI,kBAAmB,CACnB,qBAAsB,CACtB,eACJ,CACJ,CAEA,gDACI,eACI,mBAAoB,CACpB,eACJ,CACJ,CAKA,MACI,iBAAkB,CAClB,UACJ,CAGA,QACI,iBAAkB,CAClB,kBACJ,CAEA,yBACI,QACI,mBAAoB,CACpB,oBACJ,CACJ,CAEA,yBACI,QACI,iBAAkB,CAClB,kBACJ,CACJ,CAEA,0BACI,QACI,iBAAkB,CAClB,kBACJ,CACJ,CAGA,MACI,QACJ,CAEA,yBACI,MACI,UACJ,CACJ,CAEA,yBACI,MACI,QACJ,CACJ,CAEA,0BACI,MACI,QACJ,CACJ,CAGA,oBACI,kBAAmB,CACnB,mBACJ,CAEA,yBACI,oBACI,iBAAkB,CAClB,mBACJ,CACJ,CAEA,yBACI,oBACI,gBAAiB,CACjB,gBACJ,CACJ,CAGA,IAEI,WAAY,CADZ,cAEJ,CAGA,YACI,eAAgB,CAChB,yBACJ,CAEA,yBACI,YAEI,mBAAqB,CADrB,UAEJ,CACJ,CAGA,wBACI,eACJ,CAEA,yBACI,wBACI,iBACJ,CACJ,CAEA,yBACI,wBACI,eACJ,CACJ,CAGA,yBAEI,eACI,wBAA0B,CAC1B,yBACJ,CAGA,uBAII,kBAAmB,CADnB,YAAa,CAEb,sBAAuB,CAJvB,eAAgB,CAChB,cAIJ,CAGA,EACI,gCAAmC,CACnC,iCACJ,CAGA,kBAEI,oBAAqB,CACrB,oBAAa,CAAb,YAAa,CAFb,eAGJ,CAGA,WACI,iBAAkB,CAClB,kBACJ,CAGA,iBACI,qBACJ,CAEA,mBAEI,oBAAsB,CADtB,UAEJ,CACJ,CAGA,gDAEI,WACI,iBAAkB,CAClB,kBACJ,CAGA,eACI,mCACJ,CAGA,GAAK,gBAAmB,CACxB,GAAK,cAAiB,CACtB,GAAK,iBAAoB,CAGzB,YAEI,cAAe,CADf,qBAEJ,CACJ,CAGA,0BAEI,WACI,iBAAkB,CAClB,kBACJ,CAGA,GAAK,gBAAmB,CACxB,GAAK,gBAAmB,CACxB,GAAK,cAAiB,CAGtB,YAEI,kBAAmB,CADnB,iBAEJ,CAGA,wBAEI,gCAA2C,CAD3C,0BAEJ,CACJ,CAKA,gBACI,sBACJ,CAGA,cAEI,mBAAoB,CADpB,gBAEJ,CAEA,yBACI,cAEI,mBAAoB,CADpB,gBAEJ,CACJ,CAEA,yBACI,cAEI,mBAAoB,CADpB,gBAEJ,CACJ,CAEA,0BACI,cAEI,mBAAoB,CADpB,gBAEJ,CACJ,CAGA,qBACI,YAAa,CACb,gBACJ,CAEA,yBACI,qBACI,kBACJ,CACJ,CAEA,yBACI,qBACI,gBACJ,CACJ,CAGA,4BACI,eAAgB,CAEhB,mBAAoB,CADpB,gBAEJ,CAEA,yBACI,4BACI,eAAgB,CAEhB,qBAAsB,CADtB,kBAEJ,CACJ,CAEA,yBACI,4BACI,eAAgB,CAEhB,mBAAoB,CADpB,gBAEJ,CACJ,CAEA,0BACI,4BACI,eAAgB,CAEhB,qBAAsB,CADtB,kBAEJ,CACJ,CAGA,qBACI,yBACJ,CAEA,qBACI,4BACJ,CAEA,qBACI,2BACJ,CAGA,YACI,QACJ,CAEA,yBACI,YACI,UACJ,CACJ,CAEA,yBACI,YACI,QACJ,CACJ,CAGA,iBAEI,gBAAiB,CADjB,aAEJ,CAGA,2BACI,mBACJ,CAEA,yBACI,2BACI,oBACJ,CACJ,CAEA,yBACI,2BACI,kBACJ,CACJ,CAGA,yBAEI,MAEI,QAAS,CADT,SAEJ,CAEA,cAGI,kBAAoB,CADpB,6BAA+B,CAD/B,0BAGJ,CAEA,iBAII,wBACJ,CAGA,qCACI,4BACJ,CAEA,qCACI,yBACJ,CAEA,+DAEI,6BAA+B,CAD/B,0BAEJ,CAGA,4BACI,yBAA2B,CAC3B,uBACJ,CAGA,oFAII,2BACJ,CACJ,CAGA,gDACI,cAEI,+BAAiC,CADjC,4BAEJ,CAEA,iBACI,6BAA+B,CAC/B,8BACJ,CAGA,aACI,8BACJ,CAEA,0BAEI,6BAA+B,CAD/B,0BAEJ,CAEA,4BACI,yBACJ,CACJ,CAGA,QACI,qBACJ,CAGA,qCAII,eAAgB,CADhB,YAEJ,CAGA,aAA+C,+BAAiC,CAAjE,4BAAmE,CAClF,aAA6C,6BAA+B,CAA7D,0BAA+D,CAC9E,aAA+C,+BAAiC,CAAjE,4BAAmE,CAElF,yBACI,uCAII,6BAA+B,CAD/B,0BAEJ,CACJ,CAGA,mBAEI,aAAc,CADd,cAAe,CAEf,YAAa,CAEb,0CAAiD,CADjD,YAEJ,CAEA,yBAEI,qCAAoD,CADpD,oBAEJ,CAGA,yBACI,mBACI,WAAY,CAGZ,aAAc,CAFd,UAAW,CACX,YAEJ,CAEA,uBAEI,cAAe,CADf,aAEJ,CAGA,4BACI,YACJ,CACJ,CAEA,gDACI,mBACI,cAAe,CACf,aACJ,CACJ,CAGA,aACI,+CAA6D,CAC7D,0BAAyC,CACzC,0CACJ,CAEA,mBAGI,sBAAqC,CADrC,sCAEJ,CAEA,4BACI,uBACJ,CAEA,kCACI,qBACJ,CAGA,oBACI,MAAW,+CAAsD,CACjE,IAAM,+CAAsD,CAChE,CAEA,0BACI,0CACJ,CAGA,yBACI,MAAW,SAAY,CACvB,IAAM,UAAc,CACxB,CAEA,gBACI,+CACJ,CAGA,MACI,eACJ,CAEA,cACI,QAAS,CACT,cACJ,CAEA,iBACI,aAAc,CACd,cACJ,CAGA,yBACI,cACI,cACJ,CAEA,iBACI,gBACJ,CACJ,CAEA,yBACI,cACI,cACJ,CAEA,iBACI,cACJ,CACJ,CAEA,0BACI,cACI,cACJ,CAEA,iBACI,gBACJ,CACJ,CAGA,4BACI,mBACJ,CAEA,yBACI,4BACI,mBACJ,CACJ,CAEA,yBACI,4BACI,mBACJ,CACJ,CAEA,0BACI,4BACI,mBACJ,CACJ,CAGA,qBAA2B,eAAkB,CAC7C,qBAA2B,kBAAqB,CAChD,qBAA2B,iBAAoB,CAC/C,qBAA2B,kBAAqB,CAKhD,yBACI,MACI,cACJ,CAEA,iBACI,6BAAgC,CAChC,8BACJ,CAEA,SACI,2BAA6B,CAC7B,yBACJ,CAEA,SACI,0BACJ,CAEA,QAEI,yBACJ,CAEA,qBAJI,2BAQJ,CAJA,aACI,yBAA2B,CAE3B,6BACJ,CAEA,YACI,oBACJ,CACJ,CAGA,+CACI,iBACI,2BAA6B,CAC7B,4BACJ,CAEA,SACI,wBAA0B,CAC1B,yBACJ,CAEA,SACI,2BACJ,CAEA,QAEI,yBACJ,CAEA,qBAJI,wBAQJ,CAJA,aACI,yBAA2B,CAE3B,iCACJ,CACJ,CAGA,+CACI,iBACI,8BAAgC,CAChC,+BACJ,CAEA,SACI,0BAA4B,CAC5B,yBACJ,CAEA,SACI,wBACJ,CAEA,QACI,4BAA8B,CAC9B,yBACJ,CACJ,CAGA,gDACI,iBACI,6BAA+B,CAC/B,8BACJ,CAEA,SACI,wBAA0B,CAC1B,yBACJ,CAEA,SACI,2BACJ,CAEA,QACI,2BAA6B,CAC7B,yBACJ,CACJ,CAGA,0BACI,iBACI,2BAA6B,CAC7B,4BACJ,CAEA,SACI,0BAA4B,CAC5B,yBACJ,CAEA,SACI,0BACJ,CAEA,QACI,4BAA8B,CAC9B,yBACJ,CACJ,CAGA,yCACI,qBAEI,yBAA2B,CAC3B,wBACJ,CAEA,8BACI,wBACJ,CACJ,CAGA,kEACI,UACI,yCAA0C,CAC1C,2BACJ,CACJ,CAGA,sDACI,cAEI,8BAAiC,CADjC,2BAEJ,CAEA,SACI,wBACJ,CAEA,oFAII,0BACJ,CACJ,CAGA,cACI,+CAA6D,CAC7D,0BAAyC,CACzC,0CACJ,CAEA,oBAGI,sBAAqC,CADrC,qCAAoD,CADpD,0BAGJ,CAEA,aACI,kDAA6D,CAC7D,+BAA8C,CAC9C,0CACJ,CAEA,mBACI,kDAA6D,CAC7D,+BAA8C,CAC9C,0BACJ,CAGA,YAEI,0BAA2B,CAC3B,kCAAmC,CAFnC,sDAAgG,CAGhG,iCAAgD,CAEhD,+BAA8C,CAK9C,eAAgB,CAJhB,uBAAgB,CAAhB,eAAgB,CAGhB,iBAAkB,CAFlB,KAAM,CAHN,0CAAiD,CAIjD,YAGJ,CAEA,mBAOI,uDAAsF,CADtF,QAAS,CALT,UAAW,CAGX,MAAO,CAIP,mBAAoB,CANpB,iBAAkB,CAGlB,OAAQ,CAFR,KAAM,CAMN,SACJ,CAEA,cACI,iBAAkB,CAClB,SACJ,CAGA,2BACI,WACJ,CAEA,yBACI,2BACI,aACJ,CACJ,CAEA,0BACI,2BACI,WACJ,CACJ,CAEA,WAEI,aAAc,CADd,gBAAiB,CAEjB,cAAe,CACf,UACJ,CAGA,yBACI,WACI,gBACJ,CACJ,CAEA,yBACI,WACI,cACJ,CACJ,CAEA,0BACI,WACI,gBACJ,CACJ,CAEA,0BACI,WACI,cACJ,CACJ,CAGA,UAOI,gBAAuB,CADvB,WAAY,CAFZ,mBAAqB,CAHrB,aAAc,CAOd,cAAe,CAGf,oBAAqB,CACrB,mBAAoB,CAHpB,iBAAmB,CAPnB,eAAgB,CAYhB,eAAgB,CAXhB,mBAAqB,CAUrB,iBAAkB,CAHlB,8BAAgC,CALhC,0CAUJ,CAGA,yBACI,UAEI,qBAAuB,CACvB,kBAAoB,CAFpB,uBAGJ,CACJ,CAGA,0BACI,UAEI,oBAAsB,CACtB,cAAe,CAFf,sBAGJ,CACJ,CAEA,iBAOI,uDAAoF,CANpF,UAAW,CAKX,WAAY,CAFZ,UAAW,CAFX,iBAAkB,CAClB,KAAM,CAKN,wBAA0B,CAH1B,UAIJ,CAEA,uBACI,SACJ,CAEA,gBAEI,oBAAmC,CAGnC,+BAA8C,CAJ9C,uBAAyB,CAGzB,8BAAgC,CADhC,0BAGJ,CAEA,iBAEI,oBAAmC,CADnC,0BAEJ,CAEA,gBACI,2BAAyC,CACzC,kBACJ,CAGA,WAKI,6BAAoC,CAFpC,8DAA0E,CAC1E,4BAA6B,CAE7B,oBAAqB,CALrB,iBAAkB,CAClB,eAAgB,CAKhB,qBAAuB,CACvB,uBACJ,CAGA,yBACI,WACI,gBACJ,CACJ,CAGA,yBACI,WACI,iBACJ,CACJ,CAEA,0BACI,WACI,kBACJ,CACJ,CAEA,iBAGI,6BAAoC,CAFpC,8DAA0E,CAC1E,4BAA6B,CAE7B,oBAAqB,CACrB,qBACJ,CAEA,aAGI,6BAAoC,CAFpC,kDAA6D,CAC7D,4BAA6B,CAE7B,oBACJ,CAGA,YACI,eAAiB,CAKjB,0BAAwC,CAJxC,kBAAmB,CACnB,gCAA2C,CAC3C,eAAgB,CAChB,cAEJ,CAEA,sBASI,oBAAmC,CACnC,0BAAwC,CAJxC,oBAAsB,CAKtB,aAAc,CAVd,aAAc,CAMd,kBAAmB,CACnB,eAAgB,CAHhB,oBAAsB,CADtB,oBAAqB,CADrB,iBAAkB,CADlB,UAUJ,CAEA,4BACI,oBAAkC,CAClC,aAAc,CACd,0BACJ,CAEA,iCACI,eACJ,CAGA,YAII,aAAc,CAHd,kBAAmB,CACnB,eAAgB,CAChB,eAAgB,CAEhB,kBAAmB,CACnB,iBACJ,CAEA,eAGI,aAAc,CAFd,cAAe,CAIf,eAAgB,CAHhB,eAAgB,CAEhB,oBAAqB,CAErB,iBAAkB,CAClB,+BACJ,CAGA,yBACI,YACI,iBAAkB,CAClB,qBACJ,CAEA,eACI,kBAAmB,CACnB,qBACJ,CACJ,CAEA,yBACI,YACI,iBAAkB,CAClB,oBACJ,CAEA,eACI,iBAAkB,CAClB,kBACJ,CACJ,CAGA,0BACI,YACI,iBAAkB,CAClB,eACJ,CAEA,eACI,kBAAmB,CACnB,eACJ,CACJ,CAEA,0BACI,YACI,iBACJ,CAEA,eACI,gBACJ,CACJ,CAEA,eAGI,6BAAoC,CAFpC,kDAA6D,CAC7D,4BAA6B,CAE7B,oBAAqB,CACrB,oBACJ,CAGA,cAII,kBAAmB,CAEnB,kCAA2B,CAA3B,0BAA2B,CAD3B,sDAA+F,CAF/F,YAAa,CADb,eAAgB,CAOhB,eAAgB,CARhB,sBAAuB,CAOvB,iBAAkB,CADlB,sBAGJ,CAEA,qBAOI,4HAEkF,CAHlF,QAAS,CALT,UAAW,CAGX,MAAO,CAMP,mBAAoB,CARpB,iBAAkB,CAGlB,OAAQ,CAFR,KAAM,CAQN,SACJ,CAEA,gBACI,iBAAkB,CAClB,SACJ,CAEA,WAGI,aAAS,CACT,kBAAmB,CAHnB,YAAa,CAEb,QAAS,CADT,yBAA0B,CAI1B,aAAc,CADd,gBAAiB,CAGjB,iBAAkB,CADlB,UAEJ,CAEA,cAEI,OAAQ,CADR,cAEJ,CAGA,yBACI,cAEI,eAAgB,CADhB,wBAEJ,CAEA,WACI,QACJ,CAEA,cACI,gBACJ,CACJ,CAGA,0BACI,cAEI,gBAAiB,CADjB,sBAEJ,CAEA,WAEI,QAAS,CADT,6BAA8B,CAE9B,eACJ,CAEA,cAEI,OAAQ,CADR,cAEJ,CACJ,CAEA,YAEI,kBAAmB,CAInB,kCAA2B,CAS3B,0BAA2B,CAX3B,sDAA2F,CAS3F,0BAAwC,CAJxC,oBAAqB,CAFrB,+BAA6C,CAC7C,aAAc,CAPd,mBAAoB,CASpB,gBAAkB,CAClB,eAAgB,CAChB,oBAAqB,CATrB,sBAAwB,CAWxB,uBAEJ,CAGA,yBACI,YAEI,kBAAoB,CACpB,qBAAsB,CAFtB,uBAGJ,CACJ,CAEA,yBACI,YAII,kBAAmB,CAFnB,iBAAmB,CACnB,kBAAmB,CAFnB,qBAIJ,CACJ,CAGA,aACI,YAAa,CAGb,cAAe,CAFf,QAAS,CACT,eAGJ,CAEA,kBAHI,kBAuBJ,CApBA,KAWI,WAAY,CALZ,oBAAsB,CAMtB,cAAe,CAXf,mBAAoB,CAiBpB,mBAAoB,CAVpB,cAAe,CADf,eAAgB,CAHhB,UAAY,CADZ,sBAAuB,CAgBvB,eAAgB,CALhB,eAAgB,CAChB,eAAgB,CAEhB,eAAgB,CAZhB,iBAAkB,CAWlB,iBAAkB,CANlB,8BAAgC,CADhC,uBAAyB,CAIzB,kBAOJ,CAEA,aACI,kDAA6D,CAG7D,sBAA6B,CAD7B,+BAA6C,CAD7C,UAGJ,CAEA,mBAEI,+BAA6C,CAC7C,UAAY,CAFZ,0BAGJ,CAEA,eACI,eAAiB,CAEjB,wBAAyB,CACzB,+BAAyC,CAFzC,aAGJ,CAEA,qBACI,kBAAmB,CAGnB,+BAA6C,CAF7C,UAAY,CACZ,0BAEJ,CAGA,SAEI,cAAe,CADf,aAEJ,CAGA,kBAEI,kBAAmB,CADnB,YAAa,CAKb,cAAe,CAFf,QAAS,CADT,sBAAuB,CAEvB,iBAEJ,CAEA,iBAEI,kBAAmB,CAMnB,oBAAmC,CAEnC,0BAAwC,CADxC,oBAAqB,CALrB,aAAc,CAHd,YAAa,CAIb,gBAAkB,CAClB,eAAgB,CAHhB,SAAW,CAIX,oBAIJ,CAEA,qBAEI,WAAY,CADZ,UAEJ,CAGA,8BACI,YACJ,CAGA,yBACI,kBACI,WAAY,CACZ,kBACJ,CAEA,iBAEI,kBAAoB,CADpB,WAAa,CAEb,uBACJ,CAEA,qBAEI,eAAgB,CADhB,cAEJ,CAGA,8BACI,YACJ,CACJ,CAEA,yBACI,kBACI,UAAW,CACX,eACJ,CAEA,iBAII,qBAAsB,CAFtB,iBAAmB,CADnB,UAAY,CAEZ,uBAEJ,CAEA,qBAEI,cAAe,CADf,aAEJ,CACJ,CAGA,0BACI,kBAEI,QAAS,CADT,0BAA2B,CAE3B,iBACJ,CAEA,iBAEI,kBAAmB,CACnB,eAAgB,CAFhB,mBAGJ,CACJ,CAGA,YAGI,WAAY,CAGZ,eAAgB,CAJhB,cAAe,CAGf,OAAQ,CADR,aAAe,CAHf,iBAMJ,CAEA,gBAGI,kBAAmB,CACnB,iCAA+C,CAF/C,WAAY,CAGZ,6BAA+B,CAJ/B,UAKJ,CAEA,sBACI,qBACJ,CAGA,yBACI,YAII,gBAAiB,CACjB,iBAAkB,CAHlB,iBAAkB,CAClB,aAAc,CAFd,cAKJ,CAEA,gBACI,qBAAsB,CACtB,sCACJ,CACJ,CAEA,yBACI,YAEI,eAAgB,CAChB,aAAc,CAFd,YAGJ,CAEA,gBACI,oBAAqB,CACrB,sCACJ,CACJ,CAGA,0BACI,YAEI,YAAa,CACb,cAAe,CAFf,OAAQ,CAGR,YACJ,CACJ,CAGA,kBAEI,eAAiB,CACjB,kBAAmB,CAEnB,gCAA2C,CAD3C,YAAa,CAHb,iBAAkB,CAKlB,UACJ,CAEA,sBAEI,WAAY,CADZ,UAEJ,CAGA,eACI,kDAA6D,CAE7D,UAAY,CADZ,iBAAkB,CAElB,sBACJ,CAEA,YAGI,aAAS,CAFT,YAAa,CAEb,QAAS,CADT,wDAA2D,CAG3D,aAAc,CADd,gBAAiB,CAEjB,iBACJ,CAEA,WAGI,kBAAmB,CAFnB,YAAa,CACb,qBAAsB,CAEtB,SACJ,CAEA,aACI,8BAAiC,CACjC,eAAgB,CAChB,aACJ,CAEA,WACI,iBAAmB,CAEnB,eAAgB,CADhB,UAEJ,CAGA,eAEI,eAAiB,CADjB,iBAAkB,CAElB,sBACJ,CAEA,YAGI,aAAS,CACT,kBAAmB,CAHnB,YAAa,CAEb,QAAS,CADT,6BAA8B,CAI9B,aAAc,CADd,gBAEJ,CAEA,eACI,OACJ,CAEA,aACI,OACJ,CAEA,aAGI,aAAc,CAFd,8BAAiC,CACjC,eAAgB,CAGhB,eAAgB,CADhB,oBAEJ,CAEA,YAEI,aAAc,CADd,kBAAmB,CAEnB,eAAgB,CAChB,kBACJ,CAEA,iBAGI,kBAAmB,CACnB,qCAA+C,CAF/C,WAAY,CADZ,UAIJ,CAGA,iBAEI,kBAAmB,CADnB,iBAAkB,CAElB,sBACJ,CAEA,mBAEI,aAAc,CADd,gBAEJ,CAEA,eAII,aAAc,CAHd,8BAAiC,CACjC,eAAgB,CAGhB,kBAAmB,CAFnB,iBAGJ,CAEA,cAGI,aAAS,CAFT,YAAa,CAEb,QAAS,CADT,wDAEJ,CAEA,aACI,eAAiB,CACjB,kBAAmB,CAEnB,mCAA6C,CAE7C,YAAa,CACb,qBAAsB,CACtB,WAAY,CALZ,YAAa,CAEb,uBAIJ,CAEA,mBAEI,qCAA+C,CAD/C,0BAEJ,CAEA,eACI,kBACJ,CAEA,aAII,aAAc,CAHd,QAAO,CACP,iBAAmB,CACnB,eAAgB,CAEhB,oBACJ,CAEA,gBAEI,kBAAmB,CADnB,UAAW,CAEX,kBACJ,CAEA,eAEI,aAAc,CACd,iBAAmB,CAFnB,eAGJ,CAGA,iBAEI,eAAiB,CADjB,iBAAkB,CAElB,sBACJ,CAEA,mBAEI,aAAc,CADd,eAAgB,CAEhB,iBACJ,CAEA,eAGI,aAAc,CAFd,8BAAiC,CACjC,eAAgB,CAEhB,kBACJ,CAEA,kBAEI,aAAc,CADd,kBAAmB,CAEnB,kBACJ,CAEA,cAEI,eAAW,CADX,YAAa,CACb,UAAW,CAEX,aAAc,CADd,eAEJ,CAEA,YACI,YAAa,CACb,qBAAsB,CACtB,eACJ,CAEA,YAEI,aAAc,CAEd,iBAAmB,CAHnB,eAAgB,CAEhB,mBAEJ,CAEA,YAMI,eAAiB,CAJjB,wBAAyB,CACzB,mBAAqB,CACrB,iBAAmB,CAHnB,oBAAsB,CAItB,uBAEJ,CAEA,kBAEI,oBAAqB,CACrB,8BAA4C,CAF5C,YAGJ,CAEA,eACI,gBAAiB,CACjB,eACJ,CAEA,aACI,kDAA6D,CAG7D,WAAY,CACZ,mBAAqB,CAHrB,UAAY,CAKZ,cAAe,CADf,eAAgB,CAGhB,eAAgB,CANhB,iBAAkB,CAKlB,uBAEJ,CAEA,mBAEI,iCAAgD,CADhD,0BAEJ,CAGA,QACI,kBAAmB,CACnB,UAAY,CACZ,sBAAuB,CACvB,iBACJ,CAEA,gBAEI,aAAc,CADd,gBAEJ,CAEA,aAEI,aAAc,CADd,iBAEJ", "sources": ["index.css", "stylesheets/theme.css", "stylesheets/alignments.css", "stylesheets/textelements.css", "stylesheets/form-elements.css", "stylesheets/custom-components.css", "stylesheets/layout.css", "styles/modern.css", "styles/animations.css", "components/ProtectedRoute.css", "../node_modules/katex/dist/katex.min.css", "pages/common/Login/index.css", "pages/common/Register/index.css", "pages/common/Home/index.css"], "sourcesContent": ["/* Base styles - enhanced for modern design */\r\n@import url(\"https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&family=Poppins:wght@300;400;500;600;700;800&display=swap\");\r\n\r\n/* Global font family */\r\n* {\r\n  font-family: \"Inter\", \"Roboto\", \"Nunito\", system-ui, -apple-system, sans-serif !important;\r\n  box-sizing: border-box;\r\n}\r\n\r\n/* Enhanced body styles */\r\nbody {\r\n  padding: 0;\r\n  margin: 0;\r\n  line-height: 1.6;\r\n  color: var(--gray-800, #1f2937);\r\n  background-color: var(--gray-50, #f9fafb);\r\n  -webkit-font-smoothing: antialiased;\r\n  -moz-osx-font-smoothing: grayscale;\r\n  overflow-x: hidden;\r\n}\r\n\r\n/* Improved text rendering */\r\nbody, input, button, select, textarea {\r\n  font-feature-settings: 'kern' 1, 'liga' 1, 'calt' 1;\r\n  text-rendering: optimizeLegibility;\r\n}\r\n\r\n/* Remove default margins and paddings */\r\nh1, h2, h3, h4, h5, h6, p, ul, ol, li {\r\n  margin: 0;\r\n  padding: 0;\r\n}\r\n\r\n/* Improved heading styles */\r\nh1, h2, h3, h4, h5, h6 {\r\n  font-weight: 600;\r\n  line-height: 1.25;\r\n  color: var(--gray-900, #111827);\r\n}\r\n\r\nh1 { font-size: 2.25rem; }\r\nh2 { font-size: 1.875rem; }\r\nh3 { font-size: 1.5rem; }\r\nh4 { font-size: 1.25rem; }\r\nh5 { font-size: 1.125rem; }\r\nh6 { font-size: 1rem; }\r\n\r\n/* Improved link styles */\r\na {\r\n  color: var(--primary, #007BFF);\r\n  text-decoration: none;\r\n  transition: color 0.2s ease;\r\n}\r\n\r\na:hover {\r\n  color: var(--primary-dark, #0056D2);\r\n  text-decoration: underline;\r\n}\r\n\r\n/* List styles */\r\nul, ol {\r\n  list-style: none;\r\n}\r\n\r\n/* Image styles */\r\nimg {\r\n  max-width: 100%;\r\n  height: auto;\r\n  display: block;\r\n}\r\n\r\n.katex .katex-mathml {\r\n  width: 0 !important;\r\n}\r\n\r\n.katex-display>.katex {\r\n  white-space: normal !important;\r\n}\r\n\r\n/* ===== FORCE APPLY MODERN STYLES ===== */\r\n/* This CSS is definitely being applied - you should see the blue theme! */\r\n\r\n/* Force blue theme */\r\n:root {\r\n  --primary: #007BFF !important;\r\n  --primary-dark: #0056D2 !important;\r\n  --primary-light: #3b82f6 !important;\r\n  --success: #10b981 !important;\r\n  --danger: #ef4444 !important;\r\n  --warning: #f59e0b !important;\r\n  --white: #ffffff !important;\r\n  --gray-50: #f9fafb !important;\r\n  --gray-100: #f3f4f6 !important;\r\n  --gray-200: #e5e7eb !important;\r\n  --gray-300: #d1d5db !important;\r\n  --gray-600: #4b5563 !important;\r\n  --gray-700: #374151 !important;\r\n  --gray-800: #1f2937 !important;\r\n  --gray-900: #111827 !important;\r\n}\r\n\r\n/* Force responsive design */\r\n* {\r\n  box-sizing: border-box !important;\r\n}\r\n\r\n/* Force consistent font family */\r\nbody, * {\r\n  font-family: 'Inter', 'Roboto', 'Nunito', system-ui, -apple-system, sans-serif !important;\r\n}\r\n\r\n/* Force white background */\r\nbody {\r\n  background-color: #ffffff !important;\r\n  color: #1f2937 !important;\r\n  margin: 0 !important;\r\n  padding: 0 !important;\r\n}\r\n\r\n\r\n\r\n/* Force responsive text sizes */\r\n@media (max-width: 480px) {\r\n  html { font-size: 14px !important; }\r\n  h1 { font-size: 1.75rem !important; }\r\n  h2 { font-size: 1.5rem !important; }\r\n  h3 { font-size: 1.25rem !important; }\r\n  .btn { font-size: 0.875rem !important; padding: 0.5rem 1rem !important; }\r\n}\r\n\r\n@media (min-width: 481px) and (max-width: 768px) {\r\n  html { font-size: 15px !important; }\r\n  h1 { font-size: 2rem !important; }\r\n  h2 { font-size: 1.75rem !important; }\r\n  h3 { font-size: 1.5rem !important; }\r\n}\r\n\r\n/* Force icon consistency */\r\n.fa, [class*=\"fa-\"], svg {\r\n  font-size: 1.25rem !important;\r\n  vertical-align: middle !important;\r\n}\r\n\r\n@media (max-width: 768px) {\r\n  .fa, [class*=\"fa-\"], svg {\r\n    font-size: 1rem !important;\r\n  }\r\n}\r\n\r\n/* Force button styling */\r\n.btn, button {\r\n  background: var(--primary) !important;\r\n  color: var(--white) !important;\r\n  border: none !important;\r\n  border-radius: 0.5rem !important;\r\n  padding: 0.75rem 1.5rem !important;\r\n  font-weight: 500 !important;\r\n  cursor: pointer !important;\r\n  transition: all 0.2s ease !important;\r\n}\r\n\r\n.btn:hover, button:hover {\r\n  background: var(--primary-dark) !important;\r\n  transform: translateY(-1px) !important;\r\n}\r\n\r\n/* Force card styling */\r\n.card {\r\n  background: var(--white) !important;\r\n  border: 1px solid var(--gray-200) !important;\r\n  border-radius: 0.75rem !important;\r\n  padding: 1rem !important;\r\n  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1) !important;\r\n  margin-bottom: 1rem !important;\r\n}\r\n\r\n.card:hover {\r\n  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1) !important;\r\n  transform: translateY(-2px) !important;\r\n  border-color: var(--primary) !important;\r\n}\r\n\r\n/* Force sidebar styling */\r\n.sidebar {\r\n  background: var(--primary) !important;\r\n  color: var(--white) !important;\r\n}\r\n\r\n.menu-item {\r\n  color: rgba(255, 255, 255, 0.9) !important;\r\n  padding: 0.75rem 1rem !important;\r\n  border-radius: 0.5rem !important;\r\n  margin: 0.25rem 0 !important;\r\n  display: flex !important;\r\n  align-items: center !important;\r\n  gap: 0.75rem !important;\r\n  text-decoration: none !important;\r\n  transition: all 0.2s ease !important;\r\n}\r\n\r\n.menu-item:hover {\r\n  background: rgba(255, 255, 255, 0.1) !important;\r\n  color: var(--white) !important;\r\n}\r\n\r\n.active-menu-item {\r\n  background: rgba(255, 255, 255, 0.2) !important;\r\n  color: var(--white) !important;\r\n}\r\n\r\n/* Force responsive layout */\r\n@media (max-width: 768px) {\r\n  .layout {\r\n    flex-direction: column !important;\r\n  }\r\n\r\n  .sidebar {\r\n    width: 100% !important;\r\n    height: auto !important;\r\n  }\r\n\r\n  .content {\r\n    padding: 0.5rem !important;\r\n  }\r\n\r\n  .container {\r\n    padding: 0.5rem !important;\r\n  }\r\n}\r\n\r\n/* Force quiz styling */\r\n.quiz-option {\r\n  padding: 0.75rem !important;\r\n  border: 2px solid var(--gray-200) !important;\r\n  border-radius: 0.5rem !important;\r\n  background: var(--white) !important;\r\n  margin-bottom: 0.5rem !important;\r\n  cursor: pointer !important;\r\n  transition: all 0.2s ease !important;\r\n}\r\n\r\n.quiz-option:hover {\r\n  border-color: var(--primary) !important;\r\n  background: rgba(0, 123, 255, 0.05) !important;\r\n}\r\n\r\n.quiz-option.selected {\r\n  background: var(--primary) !important;\r\n  color: var(--white) !important;\r\n  border-color: var(--primary) !important;\r\n}\r\n\r\n.quiz-option.correct {\r\n  background: var(--success) !important;\r\n  color: var(--white) !important;\r\n  border-color: var(--success) !important;\r\n}\r\n\r\n.quiz-option.incorrect {\r\n  background: var(--danger) !important;\r\n  color: var(--white) !important;\r\n  border-color: var(--danger) !important;\r\n}", "/* Theme variables are now defined in modern.css */\r\n/* This file provides legacy support and additional theme utilities */\r\n\r\n:root {\r\n  /* Legacy support - these are also defined in modern.css */\r\n  --primary: #007BFF;\r\n  --primary-dark: #0056D2;\r\n  --primary-light: #3b82f6;\r\n  --secondary: #6c757d;\r\n  --success: #10b981;\r\n  --info: #06b6d4;\r\n  --warning: #f59e0b;\r\n  --danger: #ef4444;\r\n  --light: #f8f9fa;\r\n  --dark: #1f2937;\r\n\r\n  /* Additional theme colors */\r\n  --blue-50: #eff6ff;\r\n  --blue-100: #dbeafe;\r\n  --blue-500: #3b82f6;\r\n  --blue-600: #2563eb;\r\n  --blue-700: #1d4ed8;\r\n\r\n  /* Theme-specific gradients */\r\n  --gradient-primary: linear-gradient(135deg, var(--primary) 0%, var(--primary-dark) 100%);\r\n  --gradient-success: linear-gradient(135deg, var(--success) 0%, #059669 100%);\r\n  --gradient-warning: linear-gradient(135deg, var(--warning) 0%, #d97706 100%);\r\n  --gradient-danger: linear-gradient(135deg, var(--danger) 0%, #dc2626 100%);\r\n}\r\n\r\n.bg-primary{\r\n    background-color: var(--primary) !important;\r\n}\r\n.bg-white{\r\n    background-color: #fff !important;\r\n}\r\n.text-white{\r\n    color: white !important;\r\n}\r\n\r\n.bg-success{\r\n    background-color: var(--success);\r\n}\r\n\r\n.bg-error{\r\n    background-color: var(--warning);\r\n}\r\n\r\n/* Modern Utility Classes */\r\n.glass-effect {\r\n    background: rgba(255, 255, 255, 0.95);\r\n    backdrop-filter: blur(10px);\r\n    border: 1px solid rgba(255, 255, 255, 0.2);\r\n}\r\n\r\n.gradient-primary {\r\n    background: linear-gradient(135deg, var(--primary) 0%, var(--primary-dark) 100%);\r\n}\r\n\r\n.gradient-blue {\r\n    background: linear-gradient(135deg, var(--blue-500) 0%, var(--blue-700) 100%);\r\n}\r\n\r\n.shadow-modern {\r\n    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);\r\n}\r\n\r\n.shadow-modern-lg {\r\n    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);\r\n}\r\n\r\n.shadow-blue {\r\n    box-shadow: 0 4px 14px 0 rgba(59, 130, 246, 0.15);\r\n}\r\n\r\n.border-modern {\r\n    border: 1px solid #e5e7eb;\r\n    border-radius: 12px;\r\n}\r\n\r\n.text-gradient {\r\n    background: linear-gradient(135deg, var(--primary) 0%, var(--blue-600) 100%);\r\n    -webkit-background-clip: text;\r\n    -webkit-text-fill-color: transparent;\r\n    background-clip: text;\r\n}\r\n\r\n.transition-modern {\r\n    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\r\n}\r\n\r\n.hover-lift:hover {\r\n    transform: translateY(-2px);\r\n}\r\n\r\n.hover-scale:hover {\r\n    transform: scale(1.02);\r\n}", ".h-screen {\r\n    height: 100vh;\r\n}\r\n\r\n.h-100 {\r\n    height: 100%;\r\n}\r\n\r\n.h-75 {\r\n    height: 75%;\r\n}\r\n\r\n.h-50 {\r\n    height: 50%;\r\n}\r\n\r\n.h-25 {\r\n    height: 25%;\r\n}\r\n\r\n.w-screen {\r\n    width: 100vw;\r\n}\r\n\r\n.w-75 {\r\n    width: 75%;\r\n}\r\n\r\n.w-50 {\r\n    width: 50%;\r\n}\r\n\r\n.w-25 {\r\n    width: 25%;\r\n}\r\n\r\n.w-400 {\r\n    width: 400px;\r\n}\r\n\r\n.flex {\r\n    display: flex;\r\n}\r\n\r\n.flex-col {\r\n    flex-direction: column;\r\n}\r\n\r\n.flex-wrap {\r\n    flex-wrap: wrap;\r\n}\r\n\r\n.justify-center {\r\n    justify-content: center;\r\n}\r\n\r\n.justify-between {\r\n    justify-content: space-between;\r\n}\r\n\r\n.justify-start {\r\n    justify-content: flex-start;\r\n}\r\n\r\n.justify-end {\r\n    justify-content: flex-end;\r\n}\r\n\r\n.items-center {\r\n    align-items: center;\r\n}\r\n\r\n.items-start {\r\n    align-items: flex-start;\r\n}\r\n\r\n.items-end {\r\n    align-items: flex-end;\r\n}\r\n\r\n.gap-1 {\r\n    gap: 10px;\r\n}\r\n\r\n.gap-2 {\r\n    gap: 20px;\r\n}\r\n\r\n.gap-3 {\r\n    gap: 30px;\r\n}\r\n\r\n.gap-4 {\r\n    gap: 40px;\r\n}\r\n\r\n.gap-5 {\r\n    gap: 50px;\r\n}\r\n\r\n.p-5 {\r\n    padding: 50px;\r\n}\r\n\r\n.p-4 {\r\n    padding: 40px;\r\n}\r\n\r\n.p-3 {\r\n    padding: 30px;\r\n}\r\n\r\n.p-2 {\r\n    padding: 20px;\r\n}\r\n\r\n.p-1 {\r\n    padding: 10px;\r\n}\r\n\r\n.pb-2 {\r\n    padding-bottom: 10px;\r\n}\r\n\r\n.mb-2 {\r\n    margin-bottom: 10px;\r\n}", ".text-2xl {\r\n    font-size: 1.5rem;\r\n    line-height: 2rem;\r\n}\r\n\r\n.text-xl {\r\n    font-size: 1.25rem;\r\n    line-height: 1.75rem;\r\n}\r\n\r\n.text-lg {\r\n    font-size: 1.125rem;\r\n    line-height: 1.5rem;\r\n}\r\n\r\n.text-md {\r\n    font-size: 1rem;\r\n    line-height: 1.25rem;\r\n}\r\n\r\n.text-sm {\r\n    font-size: 0.875rem;\r\n    line-height: 1rem;\r\n}\r\n\r\n.text-xs {\r\n    font-size: 0.75rem;\r\n    line-height: 0.875rem;\r\n}\r\n\r\nh1 {\r\n    margin-bottom: 0px !important;\r\n    padding-bottom: 0 !important;\r\n}\r\n\r\n.text-center {\r\n    text-align: center;\r\n}\r\n\r\na {\r\n    color: var(--primary);\r\n\r\n}\r\n\r\na:hover {\r\n    color: var(--primary);\r\n    text-decoration: underline;\r\n}\r\n\r\ni {\r\n    font-size: 18px !important;\r\n    cursor: pointer !important;\r\n}\r\n\r\n.underline {\r\n    text-decoration: underline !important;\r\n}", "/* Modern form elements - enhanced for better UX */\r\ninput {\r\n  height: auto !important;\r\n  padding: var(--space-3) var(--space-4) !important;\r\n  border: 2px solid var(--gray-200) !important;\r\n  border-radius: var(--radius-lg) !important;\r\n  width: 100% !important;\r\n  font-size: var(--font-size-sm) !important;\r\n  transition: var(--transition-normal) !important;\r\n  background-color: var(--white) !important;\r\n  color: var(--gray-700) !important;\r\n  font-family: inherit !important;\r\n  box-sizing: border-box !important;\r\n}\r\n\r\ninput:focus {\r\n  outline: none !important;\r\n  border-color: var(--primary) !important;\r\n  background-color: var(--white) !important;\r\n  box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1) !important;\r\n  transform: translateY(-1px);\r\n}\r\n\r\ninput:hover:not(:focus) {\r\n  border-color: var(--gray-300);\r\n}\r\n\r\ninput:disabled {\r\n  background-color: var(--gray-50);\r\n  color: var(--gray-500);\r\n  cursor: not-allowed;\r\n  opacity: 0.7;\r\n}\r\n\r\ninput::placeholder {\r\n  color: var(--gray-400);\r\n  font-weight: 400;\r\n}\r\n\r\n/* Chrome, Safari, Edge, Opera */\r\ninput::-webkit-outer-spin-button,\r\ninput::-webkit-inner-spin-button {\r\n  -webkit-appearance: none;\r\n  margin: 0;\r\n}\r\n\r\n/* Firefox */\r\ninput[type=number] {\r\n  -moz-appearance: textfield;\r\n}\r\n\r\n.ant-form-item {\r\n  margin-bottom: 5px !important;\r\n}\r\n\r\n/* Modern button styles */\r\nbutton {\r\n  padding: var(--space-3) var(--space-6);\r\n  border-radius: var(--radius-lg);\r\n  height: auto !important;\r\n  cursor: pointer;\r\n  font-weight: 500;\r\n  transition: var(--transition-normal);\r\n  border: none;\r\n  font-size: var(--font-size-sm);\r\n  font-family: inherit;\r\n  display: inline-flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  gap: var(--space-2);\r\n  position: relative;\r\n  overflow: hidden;\r\n}\r\n\r\nbutton:disabled {\r\n  opacity: 0.6;\r\n  cursor: not-allowed;\r\n  transform: none !important;\r\n}\r\n\r\n.primary-contained-btn {\r\n  background: var(--gradient-primary);\r\n  color: var(--white);\r\n  box-shadow: var(--shadow-primary);\r\n}\r\n\r\n.primary-contained-btn:hover:not(:disabled) {\r\n  transform: translateY(-1px);\r\n  box-shadow: 0 8px 25px -5px rgba(0, 123, 255, 0.3);\r\n}\r\n\r\n.primary-contained-btn:active {\r\n  transform: translateY(0);\r\n}\r\n\r\n.primary-contained-btn:hover {\r\n  background-color: var(--primary-dark);\r\n  transform: translateY(-1px);\r\n  box-shadow: 0 4px 8px rgba(0, 123, 255, 0.3);\r\n}\r\n\r\n.mobile-btn {\r\n  padding: 0px 10px;\r\n  font-size: small;\r\n}\r\n\r\n.primary-outlined-btn {\r\n  background-color: white;\r\n  color: var(--primary);\r\n  border: 2px solid var(--primary);\r\n}\r\n\r\nselect {\r\n  height: auto;\r\n  padding: 12px 16px;\r\n  border: 2px solid #e5e7eb;\r\n  border-radius: 8px;\r\n  width: 100%;\r\n  appearance: none;\r\n  background-color: #f9fafb;\r\n  color: #374151;\r\n  font-size: 14px;\r\n  cursor: pointer;\r\n  transition: all 0.2s ease-in-out;\r\n  background-image: url(\"data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e\");\r\n  background-position: right 12px center;\r\n  background-repeat: no-repeat;\r\n  background-size: 16px;\r\n  padding-right: 40px;\r\n}\r\n\r\nselect:focus {\r\n  outline: none;\r\n  border-color: #3b82f6;\r\n  background-color: #ffffff;\r\n  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);\r\n}\r\n", "/* Enhanced card components */\r\n.card {\r\n  box-shadow: var(--shadow-md) !important;\r\n  border-radius: var(--radius-lg) !important;\r\n  border: 1px solid var(--gray-200) !important;\r\n  background-color: var(--white) !important;\r\n  transition: var(--transition-normal) !important;\r\n  overflow: hidden;\r\n  position: relative;\r\n  padding: var(--space-4) !important;\r\n  margin-bottom: var(--space-4) !important;\r\n}\r\n\r\n.card::before {\r\n  content: '';\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  height: 3px;\r\n  background: var(--gradient-primary, linear-gradient(135deg, #007BFF 0%, #0056D2 100%));\r\n  opacity: 0;\r\n  transition: var(--transition-normal, 300ms cubic-bezier(0.4, 0, 0.2, 1));\r\n}\r\n\r\n.card:hover {\r\n  box-shadow: var(--shadow-lg) !important;\r\n  transform: translateY(-2px) !important;\r\n  border-color: var(--primary) !important;\r\n}\r\n\r\n.card:hover::before {\r\n  opacity: 1;\r\n}\r\n\r\n.card-lg {\r\n  box-shadow: var(--shadow-xl, 0 20px 25px -5px rgba(0, 0, 0, 0.1));\r\n  border-radius: var(--radius-2xl, 1.5rem);\r\n  border: 1px solid var(--gray-200, #e5e7eb);\r\n  padding: var(--space-8, 2rem);\r\n}\r\n\r\n.card-sm {\r\n  padding: var(--space-4, 1rem);\r\n  border-radius: var(--radius-lg, 0.75rem);\r\n}\r\n\r\n.card-glass {\r\n  background: rgba(255, 255, 255, 0.95);\r\n  backdrop-filter: blur(10px);\r\n  border: 1px solid rgba(255, 255, 255, 0.2);\r\n}\r\n\r\n.divider {\r\n  border-bottom: 1px solid #e5e7eb;\r\n  margin: 16px 0;\r\n}\r\n\r\n/* Enhanced Modern loader styles */\r\n.loader-parent {\r\n  position: fixed;\r\n  inset: 0;\r\n  background: linear-gradient(135deg, rgba(0, 123, 255, 0.1), rgba(0, 86, 210, 0.1));\r\n  backdrop-filter: blur(8px);\r\n  z-index: var(--z-modal, 1050);\r\n  display: flex;\r\n  flex-direction: column;\r\n  justify-content: center;\r\n  align-items: center;\r\n  height: 100%;\r\n  animation: fadeIn 0.4s ease-out;\r\n}\r\n\r\n@keyframes fadeIn {\r\n  from {\r\n    opacity: 0;\r\n    transform: scale(0.95);\r\n  }\r\n  to {\r\n    opacity: 1;\r\n    transform: scale(1);\r\n  }\r\n}\r\n\r\n.loader {\r\n  height: 80px;\r\n  width: 80px;\r\n  border: 5px solid rgba(255, 255, 255, 0.3);\r\n  border-top: 5px solid var(--primary, #007BFF);\r\n  border-right: 5px solid var(--primary-dark, #0056D2);\r\n  border-radius: var(--radius-full, 50%);\r\n  animation: enhancedSpin 1.2s cubic-bezier(0.68, -0.55, 0.265, 1.55) infinite;\r\n  position: relative;\r\n  box-shadow: 0 0 30px rgba(0, 123, 255, 0.3);\r\n}\r\n\r\n.loader::before {\r\n  content: '';\r\n  position: absolute;\r\n  top: 50%;\r\n  left: 50%;\r\n  width: 40px;\r\n  height: 40px;\r\n  background: linear-gradient(135deg, var(--primary, #007BFF), var(--primary-dark, #0056D2));\r\n  border-radius: var(--radius-full, 50%);\r\n  transform: translate(-50%, -50%);\r\n  animation: innerPulse 2s ease-in-out infinite;\r\n  box-shadow: 0 0 20px rgba(0, 123, 255, 0.5);\r\n}\r\n\r\n.loader::after {\r\n  content: '';\r\n  position: absolute;\r\n  top: 50%;\r\n  left: 50%;\r\n  width: 60px;\r\n  height: 60px;\r\n  border: 2px solid rgba(0, 123, 255, 0.2);\r\n  border-radius: var(--radius-full, 50%);\r\n  transform: translate(-50%, -50%);\r\n  animation: outerRing 3s linear infinite;\r\n}\r\n\r\n/* Loading text */\r\n.loader-parent::after {\r\n  content: 'Processing...';\r\n  position: absolute;\r\n  bottom: 40%;\r\n  left: 50%;\r\n  transform: translateX(-50%);\r\n  color: var(--primary, #007BFF);\r\n  font-size: 1.1rem;\r\n  font-weight: 600;\r\n  letter-spacing: 0.5px;\r\n  animation: textPulse 1.5s ease-in-out infinite;\r\n}\r\n\r\n@keyframes enhancedSpin {\r\n  0% {\r\n    transform: rotate(0deg) scale(1);\r\n    filter: hue-rotate(0deg);\r\n  }\r\n  50% {\r\n    transform: rotate(180deg) scale(1.1);\r\n    filter: hue-rotate(90deg);\r\n  }\r\n  100% {\r\n    transform: rotate(360deg) scale(1);\r\n    filter: hue-rotate(0deg);\r\n  }\r\n}\r\n\r\n@keyframes innerPulse {\r\n  0%, 100% {\r\n    opacity: 1;\r\n    transform: translate(-50%, -50%) scale(1);\r\n  }\r\n  50% {\r\n    opacity: 0.7;\r\n    transform: translate(-50%, -50%) scale(0.8);\r\n  }\r\n}\r\n\r\n@keyframes outerRing {\r\n  0% {\r\n    transform: translate(-50%, -50%) rotate(0deg) scale(1);\r\n    opacity: 1;\r\n  }\r\n  50% {\r\n    transform: translate(-50%, -50%) rotate(180deg) scale(1.2);\r\n    opacity: 0.5;\r\n  }\r\n  100% {\r\n    transform: translate(-50%, -50%) rotate(360deg) scale(1);\r\n    opacity: 1;\r\n  }\r\n}\r\n\r\n@keyframes textPulse {\r\n  0%, 100% {\r\n    opacity: 1;\r\n    transform: translateX(-50%) translateY(0);\r\n  }\r\n  50% {\r\n    opacity: 0.7;\r\n    transform: translateX(-50%) translateY(-2px);\r\n  }\r\n}\r\n\r\n/* Legacy option styles - replaced by quiz-option classes */\r\n.option {\r\n  padding: var(--space-3) !important;\r\n  border: 2px solid var(--gray-200) !important;\r\n  border-radius: var(--radius-md) !important;\r\n  background: var(--white) !important;\r\n  cursor: pointer !important;\r\n  transition: var(--transition-normal) !important;\r\n  margin-bottom: var(--space-2) !important;\r\n}\r\n\r\n.selected-option {\r\n  padding: var(--space-3) !important;\r\n  border: 2px solid var(--primary) !important;\r\n  border-radius: var(--radius-md) !important;\r\n  background: var(--primary) !important;\r\n  color: var(--white) !important;\r\n}\r\n\r\n.result {\r\n  background-color: #a5c8c9;\r\n  max-width: max-content;\r\n  padding: 20px;\r\n  color: black !important;\r\n  border-radius: 5px;\r\n}\r\n\r\n.lottie-animation {\r\n  height: 300px;\r\n}\r\n\r\n/* Legacy timer styles - replaced by quiz-timer classes */\r\n.timer {\r\n  background-color: var(--primary);\r\n  color: var(--white) !important;\r\n  padding: var(--space-3);\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  height: 80px;\r\n  width: 80px;\r\n  border-radius: var(--radius-full);\r\n  font-weight: 600;\r\n  transition: var(--transition-normal);\r\n}\r\n\r\n@keyframes loader {\r\n  0% {\r\n    transform: rotate(0deg);\r\n  }\r\n\r\n  100% {\r\n    transform: rotate(360deg);\r\n  }\r\n}", "/* Modern responsive layout system */\r\n.layout {\r\n  padding: 0;\r\n  width: 100vw;\r\n  height: 100vh;\r\n  overflow: hidden;\r\n  background: var(--white);\r\n  font-family: 'Inter', 'Roboto', 'Nunito', system-ui, -apple-system, sans-serif;\r\n  display: flex;\r\n}\r\n\r\n/* Enhanced responsive layout adjustments */\r\n@media (max-width: 768px) {\r\n  .layout {\r\n    flex-direction: column;\r\n    height: auto;\r\n    min-height: 100vh;\r\n    padding: 0;\r\n  }\r\n}\r\n\r\n/* Mobile-first responsive breakpoints */\r\n@media (max-width: 480px) {\r\n  .layout {\r\n    padding: 0;\r\n    overflow-x: hidden;\r\n  }\r\n}\r\n\r\n@media (min-width: 481px) and (max-width: 768px) {\r\n  .layout {\r\n    padding: 0;\r\n  }\r\n}\r\n\r\n@media (min-width: 769px) and (max-width: 1024px) {\r\n  .layout {\r\n    padding: 0;\r\n  }\r\n}\r\n\r\n.sidebar {\r\n  background: var(--primary);\r\n  padding: var(--space-4);\r\n  border-radius: 0;\r\n  height: 100vh;\r\n  overflow-y: auto;\r\n  box-sizing: border-box;\r\n  scrollbar-width: thin;\r\n  box-shadow: var(--shadow-lg);\r\n  border-right: 1px solid var(--primary-dark);\r\n  position: relative;\r\n  min-width: 250px;\r\n  flex-shrink: 0;\r\n}\r\n\r\n/* Responsive sidebar */\r\n@media (max-width: 768px) {\r\n  .sidebar {\r\n    width: 100%;\r\n    height: auto;\r\n    min-height: auto;\r\n    border-right: none;\r\n    border-bottom: 1px solid var(--primary-dark);\r\n    padding: var(--space-3);\r\n  }\r\n}\r\n\r\n.sidebar::before {\r\n  content: '';\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  background: linear-gradient(135deg, var(--primary) 0%, var(--primary-dark) 100%);\r\n  opacity: 1;\r\n  z-index: -1;\r\n}\r\n\r\n.mobile-sidebar {\r\n  width: 100%;\r\n  padding: var(--space-2);\r\n}\r\n\r\n@media (min-width: 769px) {\r\n  .mobile-sidebar {\r\n    width: 15vw;\r\n    padding: var(--space-2);\r\n  }\r\n}\r\n\r\n.menu {\r\n  height: fit-content;\r\n}\r\n\r\n.mobile-sidebar .menu-item {\r\n  padding: 10px 5px;\r\n  margin-bottom: 15px;\r\n  justify-content: center;\r\n}\r\n\r\n.menu-item {\r\n  display: flex !important;\r\n  align-items: center !important;\r\n  padding: var(--space-3) var(--space-4) !important;\r\n  margin: var(--space-1) 0 !important;\r\n  cursor: pointer;\r\n  transition: var(--transition-normal);\r\n  gap: var(--space-3) !important;\r\n  color: rgba(255, 255, 255, 0.9) !important;\r\n  border-radius: var(--radius-lg) !important;\r\n  font-weight: 500 !important;\r\n  position: relative;\r\n  overflow: hidden;\r\n  text-decoration: none !important;\r\n  border: none !important;\r\n  background: transparent !important;\r\n  font-size: 0.875rem !important;\r\n  white-space: nowrap;\r\n}\r\n\r\n/* Responsive menu items */\r\n@media (max-width: 768px) {\r\n  .menu-item {\r\n    padding: var(--space-2) var(--space-3) !important;\r\n    font-size: 0.8rem !important;\r\n    gap: var(--space-2) !important;\r\n  }\r\n\r\n  .mobile-sidebar .menu-item {\r\n    justify-content: center;\r\n    padding: var(--space-2) !important;\r\n  }\r\n\r\n  .mobile-sidebar .menu-item span {\r\n    display: none;\r\n  }\r\n}\r\n\r\n.menu-item::before {\r\n  content: '';\r\n  position: absolute;\r\n  top: 0;\r\n  left: -100%;\r\n  width: 100%;\r\n  height: 100%;\r\n  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);\r\n  transition: var(--transition-normal);\r\n}\r\n\r\n.menu-item:hover {\r\n  background: rgba(255, 255, 255, 0.1) !important;\r\n  color: white !important;\r\n  transform: translateX(2px);\r\n  backdrop-filter: blur(10px);\r\n}\r\n\r\n.active-menu-item {\r\n  background: rgba(255, 255, 255, 0.2) !important;\r\n  color: white !important;\r\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;\r\n  border-radius: var(--radius-lg) !important;\r\n}\r\n\r\n.active-menu-item::before {\r\n  content: '';\r\n  position: absolute;\r\n  left: 0;\r\n  top: 0;\r\n  bottom: 0;\r\n  width: 4px;\r\n  background: white;\r\n  border-radius: 0 4px 4px 0;\r\n}\r\n\r\n.body {\r\n  overflow: hidden;\r\n  flex: 1;\r\n  background: var(--white) !important;\r\n  min-width: 0;\r\n}\r\n\r\n/* Responsive body */\r\n@media (max-width: 768px) {\r\n  .body {\r\n    width: 100%;\r\n    overflow: visible;\r\n  }\r\n}\r\n\r\n.collapsed-body {\r\n  /* margin-left: 102px; */\r\n}\r\n\r\n.mobile-collapsed-body {\r\n  /* margin-left: 20vw; */\r\n}\r\n\r\n.no-collapse-body {\r\n  /* margin-left: 230px; */\r\n}\r\n\r\n.content {\r\n  overflow-y: auto;\r\n  background: var(--white) !important;\r\n  min-height: 100vh;\r\n  padding: var(--space-4) !important;\r\n}\r\n\r\n/* Enhanced responsive content */\r\n@media (max-width: 480px) {\r\n  .content {\r\n    padding: var(--space-2) !important;\r\n    min-height: auto;\r\n    margin: 0;\r\n    border-radius: 0;\r\n  }\r\n}\r\n\r\n@media (min-width: 481px) and (max-width: 768px) {\r\n  .content {\r\n    padding: var(--space-3) !important;\r\n    margin: 0;\r\n  }\r\n}\r\n\r\n@media (min-width: 769px) and (max-width: 1024px) {\r\n  .content {\r\n    padding: var(--space-4) !important;\r\n  }\r\n}\r\n\r\n@media (min-width: 1025px) {\r\n  .content {\r\n    padding: var(--space-6) !important;\r\n  }\r\n}\r\n\r\n.header {\r\n  background-color: var(--primary) !important;\r\n  color: white !important;\r\n  padding: var(--space-4) !important;\r\n  border-radius: var(--radius-lg) !important;\r\n  align-items: center !important;\r\n  box-sizing: border-box;\r\n  width: 100%;\r\n  margin-bottom: var(--space-4) !important;\r\n  box-shadow: var(--shadow-md) !important;\r\n}\r\n\r\n/* Quiz Fullscreen Mode - Instructions, Questions, and Results */\r\n.quiz-fullscreen .layout {\r\n  display: block !important;\r\n}\r\n\r\n.quiz-fullscreen .sidebar {\r\n  display: none !important;\r\n}\r\n\r\n.quiz-fullscreen .body {\r\n  width: 100vw !important;\r\n  margin-left: 0 !important;\r\n}\r\n\r\n.quiz-fullscreen .header {\r\n  display: flex !important;\r\n  justify-content: center !important;\r\n  align-items: center !important;\r\n  padding: 10px 20px !important;\r\n  background-color: var(--primary) !important;\r\n  border-radius: 0 !important;\r\n  position: fixed !important;\r\n  top: 0 !important;\r\n  left: 0 !important;\r\n  right: 0 !important;\r\n  z-index: 1000 !important;\r\n  height: 60px !important;\r\n  box-sizing: border-box !important;\r\n}\r\n\r\n.quiz-fullscreen .header > div:first-child,\r\n.quiz-fullscreen .header > div:last-child {\r\n  display: none !important;\r\n}\r\n\r\n.quiz-fullscreen .header .flex.items-center.gap-1 {\r\n  display: flex !important;\r\n  align-items: center !important;\r\n  gap: 8px !important;\r\n}\r\n\r\n.quiz-fullscreen .content {\r\n  padding: 0 !important;\r\n  height: calc(100vh - 60px) !important;\r\n  margin-top: 60px !important;\r\n  overflow-y: auto;\r\n}", "/* ===== BRAINWAVE MODERN DESIGN SYSTEM ===== */\n/* Professional Educational Platform Styling with Blue Theme */\n\n/* Import Google Fonts */\n@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&family=Poppins:wght@300;400;500;600;700;800&display=swap');\n\n/* ===== CSS RESET & BASE STYLES ===== */\n*, *::before, *::after {\n  box-sizing: border-box;\n  margin: 0;\n  padding: 0;\n}\n\nhtml {\n  font-size: 16px;\n  scroll-behavior: smooth;\n}\n\nbody {\n  font-family: 'Inter', 'Roboto', 'Nunito', system-ui, -apple-system, sans-serif !important;\n  line-height: 1.6 !important;\n  color: #1f2937 !important;\n  background-color: #ffffff !important;\n  -webkit-font-smoothing: antialiased;\n  -moz-osx-font-smoothing: grayscale;\n  overflow-x: hidden;\n  margin: 0 !important;\n  padding: 0 !important;\n}\n\n/* ===== CSS CUSTOM PROPERTIES - BLUE THEME ===== */\n:root {\n  /* Primary Blue Colors - User Preferred */\n  --primary: #007BFF;\n  --primary-dark: #0056D2;\n  --primary-light: #3b82f6;\n  --primary-50: #eff6ff;\n  --primary-100: #dbeafe;\n  --primary-200: #bfdbfe;\n  --primary-300: #93c5fd;\n  --primary-400: #60a5fa;\n  --primary-500: #3b82f6;\n  --primary-600: #2563eb;\n  --primary-700: #1d4ed8;\n  --primary-800: #1e40af;\n  --primary-900: #1e3a8a;\n\n  /* Additional color variables for components */\n  --success-500: #10b981;\n  --error-500: #ef4444;\n  --error-50: #fef2f2;\n\n  /* Semantic Colors - Enhanced for Educational Platform */\n  --success: #10b981;\n  --success-light: #d1fae5;\n  --success-dark: #059669;\n  --warning: #f59e0b;\n  --warning-light: #fef3c7;\n  --warning-dark: #d97706;\n  --danger: #ef4444;\n  --danger-light: #fee2e2;\n  --danger-dark: #dc2626;\n  --info: #06b6d4;\n  --info-light: #cffafe;\n  --info-dark: #0891b2;\n\n  /* Neutral Colors - Optimized for Readability */\n  --white: #ffffff;\n  --gray-50: #f9fafb;\n  --gray-100: #f3f4f6;\n  --gray-200: #e5e7eb;\n  --gray-300: #d1d5db;\n  --gray-400: #9ca3af;\n  --gray-500: #6b7280;\n  --gray-600: #4b5563;\n  --gray-700: #374151;\n  --gray-800: #1f2937;\n  --gray-900: #111827;\n\n  /* Spacing - Optimized for User Preference (Small Gaps) */\n  --space-1: 0.25rem;    /* 4px */\n  --space-2: 0.5rem;     /* 8px */\n  --space-3: 0.75rem;    /* 12px */\n  --space-4: 1rem;       /* 16px */\n  --space-5: 1.25rem;    /* 20px */\n  --space-6: 1.5rem;     /* 24px */\n  --space-8: 2rem;       /* 32px */\n  --space-10: 2.5rem;    /* 40px */\n  --space-12: 3rem;      /* 48px */\n  --space-16: 4rem;      /* 64px */\n  --space-20: 5rem;      /* 80px */\n\n  /* Border Radius */\n  --radius-sm: 0.375rem;\n  --radius-md: 0.5rem;\n  --radius-lg: 0.75rem;\n  --radius-xl: 1rem;\n  --radius-2xl: 1.5rem;\n  --radius-full: 9999px;\n\n  /* Enhanced Shadows with Blue Tint */\n  --shadow-sm: 0 1px 2px 0 rgba(0, 123, 255, 0.05);\n  --shadow-md: 0 4px 6px -1px rgba(0, 123, 255, 0.1), 0 2px 4px -1px rgba(0, 123, 255, 0.06);\n  --shadow-lg: 0 10px 15px -3px rgba(0, 123, 255, 0.1), 0 4px 6px -2px rgba(0, 123, 255, 0.05);\n  --shadow-xl: 0 20px 25px -5px rgba(0, 123, 255, 0.15), 0 10px 10px -5px rgba(0, 123, 255, 0.08);\n  --shadow-2xl: 0 25px 50px -12px rgba(0, 123, 255, 0.25);\n\n  /* Glassmorphism Effects */\n  --glass-bg: rgba(255, 255, 255, 0.95);\n  --glass-border: rgba(0, 123, 255, 0.1);\n  --glass-blur: blur(20px);\n\n  /* Transitions */\n  --transition-fast: 0.15s ease;\n  --transition-normal: 0.3s cubic-bezier(0.4, 0, 0.2, 1);\n  --transition-slow: 0.5s cubic-bezier(0.4, 0, 0.2, 1);\n  --transition-bounce: 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);\n  --shadow-blue: 0 4px 14px 0 rgba(59, 130, 246, 0.15);\n  --shadow-primary: 0 4px 14px 0 rgba(0, 123, 255, 0.15);\n\n  /* Transitions */\n  --transition-fast: 150ms cubic-bezier(0.4, 0, 0.2, 1);\n  --transition-normal: 300ms cubic-bezier(0.4, 0, 0.2, 1);\n  --transition-slow: 500ms cubic-bezier(0.4, 0, 0.2, 1);\n\n  /* Typography */\n  --font-size-xs: 0.75rem;\n  --font-size-sm: 0.875rem;\n  --font-size-base: 1rem;\n  --font-size-lg: 1.125rem;\n  --font-size-xl: 1.25rem;\n  --font-size-2xl: 1.5rem;\n  --font-size-3xl: 1.875rem;\n  --font-size-4xl: 2.25rem;\n\n  /* Line Heights */\n  --leading-tight: 1.25;\n  --leading-normal: 1.5;\n  --leading-relaxed: 1.625;\n  --leading-loose: 2;\n\n  /* Z-Index */\n  --z-dropdown: 1000;\n  --z-sticky: 1020;\n  --z-fixed: 1030;\n  --z-modal-backdrop: 1040;\n  --z-modal: 1050;\n  --z-popover: 1060;\n  --z-tooltip: 1070;\n}\n\n/* ===== UTILITY CLASSES ===== */\n\n/* Display */\n.d-flex { display: flex !important; }\n.d-block { display: block !important; }\n.d-inline-block { display: inline-block !important; }\n.d-none { display: none !important; }\n.d-grid { display: grid !important; }\n\n/* Flexbox */\n.flex-column { flex-direction: column !important; }\n.flex-row { flex-direction: row !important; }\n.justify-center { justify-content: center !important; }\n.justify-between { justify-content: space-between !important; }\n.justify-start { justify-content: flex-start !important; }\n.justify-end { justify-content: flex-end !important; }\n.align-center { align-items: center !important; }\n.align-start { align-items: flex-start !important; }\n.align-end { align-items: flex-end !important; }\n.flex-wrap { flex-wrap: wrap !important; }\n.flex-nowrap { flex-wrap: nowrap !important; }\n.flex-1 { flex: 1 !important; }\n\n/* Grid */\n.grid-cols-1 { grid-template-columns: repeat(1, minmax(0, 1fr)) !important; }\n.grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)) !important; }\n.grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)) !important; }\n.grid-cols-4 { grid-template-columns: repeat(4, minmax(0, 1fr)) !important; }\n.gap-1 { gap: var(--space-1) !important; }\n.gap-2 { gap: var(--space-2) !important; }\n.gap-3 { gap: var(--space-3) !important; }\n.gap-4 { gap: var(--space-4) !important; }\n.gap-6 { gap: var(--space-6) !important; }\n.gap-8 { gap: var(--space-8) !important; }\n\n/* Spacing - Margin */\n.m-0 { margin: 0 !important; }\n.m-1 { margin: var(--space-1) !important; }\n.m-2 { margin: var(--space-2) !important; }\n.m-3 { margin: var(--space-3) !important; }\n.m-4 { margin: var(--space-4) !important; }\n.m-5 { margin: var(--space-5) !important; }\n.m-6 { margin: var(--space-6) !important; }\n.m-8 { margin: var(--space-8) !important; }\n.m-auto { margin: auto !important; }\n\n.mx-auto { margin-left: auto !important; margin-right: auto !important; }\n.my-auto { margin-top: auto !important; margin-bottom: auto !important; }\n\n.mt-0 { margin-top: 0 !important; }\n.mt-1 { margin-top: var(--space-1) !important; }\n.mt-2 { margin-top: var(--space-2) !important; }\n.mt-3 { margin-top: var(--space-3) !important; }\n.mt-4 { margin-top: var(--space-4) !important; }\n.mt-6 { margin-top: var(--space-6) !important; }\n.mt-8 { margin-top: var(--space-8) !important; }\n\n.mb-0 { margin-bottom: 0 !important; }\n.mb-1 { margin-bottom: var(--space-1) !important; }\n.mb-2 { margin-bottom: var(--space-2) !important; }\n.mb-3 { margin-bottom: var(--space-3) !important; }\n.mb-4 { margin-bottom: var(--space-4) !important; }\n.mb-6 { margin-bottom: var(--space-6) !important; }\n.mb-8 { margin-bottom: var(--space-8) !important; }\n\n.ml-0 { margin-left: 0 !important; }\n.ml-1 { margin-left: var(--space-1) !important; }\n.ml-2 { margin-left: var(--space-2) !important; }\n.ml-3 { margin-left: var(--space-3) !important; }\n.ml-4 { margin-left: var(--space-4) !important; }\n\n.mr-0 { margin-right: 0 !important; }\n.mr-1 { margin-right: var(--space-1) !important; }\n.mr-2 { margin-right: var(--space-2) !important; }\n.mr-3 { margin-right: var(--space-3) !important; }\n.mr-4 { margin-right: var(--space-4) !important; }\n\n/* Spacing - Padding */\n.p-0 { padding: 0 !important; }\n.p-1 { padding: var(--space-1) !important; }\n.p-2 { padding: var(--space-2) !important; }\n.p-3 { padding: var(--space-3) !important; }\n.p-4 { padding: var(--space-4) !important; }\n.p-5 { padding: var(--space-5) !important; }\n.p-6 { padding: var(--space-6) !important; }\n.p-8 { padding: var(--space-8) !important; }\n\n.px-1 { padding-left: var(--space-1) !important; padding-right: var(--space-1) !important; }\n.px-2 { padding-left: var(--space-2) !important; padding-right: var(--space-2) !important; }\n.px-3 { padding-left: var(--space-3) !important; padding-right: var(--space-3) !important; }\n.px-4 { padding-left: var(--space-4) !important; padding-right: var(--space-4) !important; }\n.px-6 { padding-left: var(--space-6) !important; padding-right: var(--space-6) !important; }\n.px-8 { padding-left: var(--space-8) !important; padding-right: var(--space-8) !important; }\n\n.py-1 { padding-top: var(--space-1) !important; padding-bottom: var(--space-1) !important; }\n.py-2 { padding-top: var(--space-2) !important; padding-bottom: var(--space-2) !important; }\n.py-3 { padding-top: var(--space-3) !important; padding-bottom: var(--space-3) !important; }\n.py-4 { padding-top: var(--space-4) !important; padding-bottom: var(--space-4) !important; }\n.py-6 { padding-top: var(--space-6) !important; padding-bottom: var(--space-6) !important; }\n.py-8 { padding-top: var(--space-8) !important; padding-bottom: var(--space-8) !important; }\n\n.pt-0 { padding-top: 0 !important; }\n.pt-1 { padding-top: var(--space-1) !important; }\n.pt-2 { padding-top: var(--space-2) !important; }\n.pt-3 { padding-top: var(--space-3) !important; }\n.pt-4 { padding-top: var(--space-4) !important; }\n.pt-6 { padding-top: var(--space-6) !important; }\n.pt-8 { padding-top: var(--space-8) !important; }\n\n.pb-0 { padding-bottom: 0 !important; }\n.pb-1 { padding-bottom: var(--space-1) !important; }\n.pb-2 { padding-bottom: var(--space-2) !important; }\n.pb-3 { padding-bottom: var(--space-3) !important; }\n.pb-4 { padding-bottom: var(--space-4) !important; }\n.pb-6 { padding-bottom: var(--space-6) !important; }\n.pb-8 { padding-bottom: var(--space-8) !important; }\n\n/* Width & Height */\n.w-full { width: 100% !important; }\n.w-auto { width: auto !important; }\n.w-fit { width: fit-content !important; }\n.h-full { height: 100% !important; }\n.h-auto { height: auto !important; }\n.h-fit { height: fit-content !important; }\n.h-screen { height: 100vh !important; }\n.min-h-screen { min-height: 100vh !important; }\n\n/* Position */\n.relative { position: relative !important; }\n.absolute { position: absolute !important; }\n.fixed { position: fixed !important; }\n.sticky { position: sticky !important; }\n\n/* Text Alignment */\n.text-left { text-align: left !important; }\n.text-center { text-align: center !important; }\n.text-right { text-align: right !important; }\n.text-justify { text-align: justify !important; }\n\n/* Font Weight */\n.font-light { font-weight: 300 !important; }\n.font-normal { font-weight: 400 !important; }\n.font-medium { font-weight: 500 !important; }\n.font-semibold { font-weight: 600 !important; }\n.font-bold { font-weight: 700 !important; }\n.font-extrabold { font-weight: 800 !important; }\n\n/* Font Size */\n.text-xs { font-size: var(--font-size-xs) !important; }\n.text-sm { font-size: var(--font-size-sm) !important; }\n.text-base { font-size: var(--font-size-base) !important; }\n.text-lg { font-size: var(--font-size-lg) !important; }\n.text-xl { font-size: var(--font-size-xl) !important; }\n.text-2xl { font-size: var(--font-size-2xl) !important; }\n.text-3xl { font-size: var(--font-size-3xl) !important; }\n.text-4xl { font-size: var(--font-size-4xl) !important; }\n\n/* Line Height */\n.leading-tight { line-height: var(--leading-tight) !important; }\n.leading-normal { line-height: var(--leading-normal) !important; }\n.leading-relaxed { line-height: var(--leading-relaxed) !important; }\n.leading-loose { line-height: var(--leading-loose) !important; }\n\n/* Colors - Text */\n.text-white { color: var(--white) !important; }\n.text-primary { color: var(--primary) !important; }\n.text-primary-dark { color: var(--primary-dark) !important; }\n.text-success { color: var(--success) !important; }\n.text-warning { color: var(--warning) !important; }\n.text-danger { color: var(--danger) !important; }\n.text-info { color: var(--info) !important; }\n.text-gray-400 { color: var(--gray-400) !important; }\n.text-gray-500 { color: var(--gray-500) !important; }\n.text-gray-600 { color: var(--gray-600) !important; }\n.text-gray-700 { color: var(--gray-700) !important; }\n.text-gray-800 { color: var(--gray-800) !important; }\n.text-gray-900 { color: var(--gray-900) !important; }\n\n/* Colors - Background */\n.bg-white { background-color: var(--white) !important; }\n.bg-primary { background-color: var(--primary) !important; }\n.bg-primary-dark { background-color: var(--primary-dark) !important; }\n.bg-primary-light { background-color: var(--primary-light) !important; }\n.bg-primary-50 { background-color: var(--primary-50) !important; }\n.bg-primary-100 { background-color: var(--primary-100) !important; }\n.bg-success { background-color: var(--success) !important; }\n.bg-success-light { background-color: var(--success-light) !important; }\n.bg-warning { background-color: var(--warning) !important; }\n.bg-warning-light { background-color: var(--warning-light) !important; }\n.bg-danger { background-color: var(--danger) !important; }\n.bg-danger-light { background-color: var(--danger-light) !important; }\n.bg-info { background-color: var(--info) !important; }\n.bg-info-light { background-color: var(--info-light) !important; }\n.bg-gray-50 { background-color: var(--gray-50) !important; }\n.bg-gray-100 { background-color: var(--gray-100) !important; }\n.bg-gray-200 { background-color: var(--gray-200) !important; }\n.bg-gray-300 { background-color: var(--gray-300) !important; }\n\n/* Border Radius */\n.rounded-none { border-radius: 0 !important; }\n.rounded-sm { border-radius: var(--radius-sm) !important; }\n.rounded { border-radius: var(--radius-md) !important; }\n.rounded-md { border-radius: var(--radius-md) !important; }\n.rounded-lg { border-radius: var(--radius-lg) !important; }\n.rounded-xl { border-radius: var(--radius-xl) !important; }\n.rounded-2xl { border-radius: var(--radius-2xl) !important; }\n.rounded-full { border-radius: var(--radius-full) !important; }\n\n/* Borders */\n.border { border: 1px solid var(--gray-200) !important; }\n.border-0 { border: 0 !important; }\n.border-2 { border: 2px solid var(--gray-200) !important; }\n.border-primary { border-color: var(--primary) !important; }\n.border-success { border-color: var(--success) !important; }\n.border-warning { border-color: var(--warning) !important; }\n.border-danger { border-color: var(--danger) !important; }\n.border-gray-200 { border-color: var(--gray-200) !important; }\n.border-gray-300 { border-color: var(--gray-300) !important; }\n\n/* Shadows */\n.shadow-none { box-shadow: none !important; }\n.shadow-sm { box-shadow: var(--shadow-sm) !important; }\n.shadow { box-shadow: var(--shadow-md) !important; }\n.shadow-md { box-shadow: var(--shadow-md) !important; }\n.shadow-lg { box-shadow: var(--shadow-lg) !important; }\n.shadow-xl { box-shadow: var(--shadow-xl) !important; }\n.shadow-blue { box-shadow: var(--shadow-blue) !important; }\n.shadow-primary { box-shadow: var(--shadow-primary) !important; }\n\n/* Transitions */\n.transition { transition: var(--transition-normal) !important; }\n.transition-fast { transition: var(--transition-fast) !important; }\n.transition-slow { transition: var(--transition-slow) !important; }\n.transition-all { transition: all var(--transition-normal) !important; }\n.transition-colors { transition: color var(--transition-normal), background-color var(--transition-normal), border-color var(--transition-normal) !important; }\n\n/* Transform */\n.transform { transform: translateZ(0) !important; }\n.hover\\:scale-105:hover { transform: scale(1.05) !important; }\n.hover\\:scale-110:hover { transform: scale(1.1) !important; }\n.hover\\:-translate-y-1:hover { transform: translateY(-0.25rem) !important; }\n.hover\\:-translate-y-2:hover { transform: translateY(-0.5rem) !important; }\n\n/* Opacity */\n.opacity-0 { opacity: 0 !important; }\n.opacity-25 { opacity: 0.25 !important; }\n.opacity-50 { opacity: 0.5 !important; }\n.opacity-75 { opacity: 0.75 !important; }\n.opacity-100 { opacity: 1 !important; }\n\n/* Overflow */\n.overflow-hidden { overflow: hidden !important; }\n.overflow-auto { overflow: auto !important; }\n.overflow-scroll { overflow: scroll !important; }\n.overflow-x-hidden { overflow-x: hidden !important; }\n.overflow-y-hidden { overflow-y: hidden !important; }\n.overflow-x-auto { overflow-x: auto !important; }\n.overflow-y-auto { overflow-y: auto !important; }\n\n/* Z-Index */\n.z-0 { z-index: 0 !important; }\n.z-10 { z-index: 10 !important; }\n.z-20 { z-index: 20 !important; }\n.z-30 { z-index: 30 !important; }\n.z-40 { z-index: 40 !important; }\n.z-50 { z-index: 50 !important; }\n\n/* ===== COMPONENT STYLES ===== */\n\n/* Modern Card Component */\n.card {\n  background: var(--white);\n  border-radius: var(--radius-xl);\n  box-shadow: var(--shadow-lg);\n  border: 1px solid var(--gray-100);\n  transition: var(--transition-normal);\n  overflow: hidden;\n}\n\n.card:hover {\n  box-shadow: var(--shadow-xl);\n  transform: translateY(-2px);\n}\n\n.card-header {\n  padding: var(--space-6);\n  border-bottom: 1px solid var(--gray-100);\n  background: var(--gray-50);\n}\n\n.card-body {\n  padding: var(--space-6);\n}\n\n.card-footer {\n  padding: var(--space-6);\n  border-top: 1px solid var(--gray-100);\n  background: var(--gray-50);\n}\n\n/* Glass Effect Card */\n.card-glass {\n  background: rgba(255, 255, 255, 0.95);\n  backdrop-filter: blur(10px);\n  border: 1px solid rgba(255, 255, 255, 0.2);\n  border-radius: var(--radius-xl);\n  box-shadow: var(--shadow-xl);\n}\n\n/* Modern Button Styles */\n.btn {\n  display: inline-flex;\n  align-items: center;\n  justify-content: center;\n  gap: var(--space-2);\n  padding: var(--space-3) var(--space-6);\n  border-radius: var(--radius-lg);\n  font-weight: 500;\n  font-size: var(--font-size-sm);\n  line-height: 1;\n  text-decoration: none;\n  border: none;\n  cursor: pointer;\n  transition: var(--transition-normal);\n  position: relative;\n  overflow: hidden;\n}\n\n.btn:disabled {\n  opacity: 0.6;\n  cursor: not-allowed;\n}\n\n.btn-sm {\n  padding: var(--space-2) var(--space-4);\n  font-size: var(--font-size-xs);\n}\n\n.btn-lg {\n  padding: var(--space-4) var(--space-8);\n  font-size: var(--font-size-base);\n}\n\n.btn-primary {\n  background: linear-gradient(135deg, var(--primary) 0%, var(--primary-dark) 100%);\n  color: var(--white);\n  box-shadow: var(--shadow-primary);\n}\n\n.btn-primary:hover {\n  transform: translateY(-1px);\n  box-shadow: 0 8px 25px -5px rgba(0, 123, 255, 0.3);\n}\n\n.btn-secondary {\n  background: var(--gray-100);\n  color: var(--gray-700);\n  border: 1px solid var(--gray-200);\n}\n\n.btn-secondary:hover {\n  background: var(--gray-200);\n  transform: translateY(-1px);\n}\n\n.btn-success {\n  background: linear-gradient(135deg, var(--success) 0%, #059669 100%);\n  color: var(--white);\n  box-shadow: 0 4px 14px 0 rgba(16, 185, 129, 0.15);\n}\n\n.btn-success:hover {\n  transform: translateY(-1px);\n  box-shadow: 0 8px 25px -5px rgba(16, 185, 129, 0.3);\n}\n\n.btn-danger {\n  background: linear-gradient(135deg, var(--danger) 0%, #dc2626 100%);\n  color: var(--white);\n  box-shadow: 0 4px 14px 0 rgba(239, 68, 68, 0.15);\n}\n\n.btn-danger:hover {\n  transform: translateY(-1px);\n  box-shadow: 0 8px 25px -5px rgba(239, 68, 68, 0.3);\n}\n\n.btn-outline {\n  background: transparent;\n  border: 2px solid var(--primary);\n  color: var(--primary);\n}\n\n.btn-outline:hover {\n  background: var(--primary);\n  color: var(--white);\n  transform: translateY(-1px);\n}\n\n/* Modern Form Elements - Blue Theme */\n.form-group {\n  margin-bottom: var(--space-4);\n}\n\n.form-label {\n  display: block;\n  font-weight: 500;\n  color: var(--gray-700);\n  margin-bottom: var(--space-2);\n  font-size: var(--font-size-sm);\n}\n\n.form-control,\n.form-input,\n.input-modern {\n  width: 100%;\n  padding: var(--space-3) var(--space-4);\n  border: 2px solid var(--gray-200);\n  border-radius: var(--radius-lg);\n  font-size: var(--font-size-sm);\n  background-color: var(--white);\n  transition: var(--transition-normal);\n  outline: none;\n  font-family: inherit;\n}\n\n.form-control:focus,\n.form-input:focus,\n.input-modern:focus {\n  border-color: var(--primary);\n  box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);\n  background-color: var(--white);\n}\n\n.form-control:disabled,\n.form-input:disabled,\n.input-modern:disabled {\n  background-color: var(--gray-50);\n  color: var(--gray-500);\n  cursor: not-allowed;\n}\n\n.form-control.is-invalid,\n.form-input.is-invalid,\n.input-modern.is-invalid {\n  border-color: var(--danger);\n}\n\n.form-control.is-invalid:focus,\n.form-input.is-invalid:focus,\n.input-modern.is-invalid:focus {\n  border-color: var(--danger);\n  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);\n}\n\n.form-control.is-valid,\n.form-input.is-valid,\n.input-modern.is-valid {\n  border-color: var(--success);\n}\n\n.form-control.is-valid:focus,\n.form-input.is-valid:focus,\n.input-modern.is-valid:focus {\n  border-color: var(--success);\n  box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1);\n}\n\n.form-text {\n  font-size: var(--font-size-xs);\n  color: var(--gray-500);\n  margin-top: var(--space-1);\n}\n\n.form-error {\n  font-size: var(--font-size-xs);\n  color: var(--danger);\n  margin-top: var(--space-1);\n}\n\n.form-success {\n  font-size: var(--font-size-xs);\n  color: var(--success);\n  margin-top: var(--space-1);\n}\n\n/* Select Dropdown */\n.form-select {\n  width: 100%;\n  padding: var(--space-3) var(--space-4);\n  border: 2px solid var(--gray-200);\n  border-radius: var(--radius-lg);\n  font-size: var(--font-size-sm);\n  background-color: var(--white);\n  background-image: url(\"data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e\");\n  background-position: right var(--space-3) center;\n  background-repeat: no-repeat;\n  background-size: 1rem;\n  padding-right: 2.5rem;\n  transition: var(--transition-normal);\n  outline: none;\n  cursor: pointer;\n}\n\n.form-select:focus {\n  border-color: var(--primary);\n  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);\n}\n\n/* Checkbox and Radio */\n.form-check {\n  display: flex;\n  align-items: center;\n  gap: var(--space-2);\n  margin-bottom: var(--space-2);\n}\n\n.form-check-input {\n  width: 1.125rem;\n  height: 1.125rem;\n  border: 2px solid var(--gray-300);\n  border-radius: var(--radius-sm);\n  background-color: var(--white);\n  cursor: pointer;\n  transition: var(--transition-normal);\n}\n\n.form-check-input:checked {\n  background-color: var(--primary);\n  border-color: var(--primary);\n}\n\n.form-check-input[type=\"radio\"] {\n  border-radius: var(--radius-full);\n}\n\n.form-check-label {\n  font-size: var(--font-size-sm);\n  color: var(--gray-700);\n  cursor: pointer;\n}\n\n/* Modern Layout Components */\n.container {\n  width: 100%;\n  max-width: 1200px;\n  margin: 0 auto;\n  padding: 0 var(--space-4);\n}\n\n.container-sm {\n  max-width: 640px;\n}\n\n.container-md {\n  max-width: 768px;\n}\n\n.container-lg {\n  max-width: 1024px;\n}\n\n.container-xl {\n  max-width: 1280px;\n}\n\n.row {\n  display: flex;\n  flex-wrap: wrap;\n  margin: 0 calc(-1 * var(--space-3));\n}\n\n.col {\n  flex: 1;\n  padding: 0 var(--space-3);\n}\n\n.col-auto {\n  flex: 0 0 auto;\n  width: auto;\n}\n\n.col-1 { flex: 0 0 8.333333%; max-width: 8.333333%; }\n.col-2 { flex: 0 0 16.666667%; max-width: 16.666667%; }\n.col-3 { flex: 0 0 25%; max-width: 25%; }\n.col-4 { flex: 0 0 33.333333%; max-width: 33.333333%; }\n.col-5 { flex: 0 0 41.666667%; max-width: 41.666667%; }\n.col-6 { flex: 0 0 50%; max-width: 50%; }\n.col-7 { flex: 0 0 58.333333%; max-width: 58.333333%; }\n.col-8 { flex: 0 0 66.666667%; max-width: 66.666667%; }\n.col-9 { flex: 0 0 75%; max-width: 75%; }\n.col-10 { flex: 0 0 83.333333%; max-width: 83.333333%; }\n.col-11 { flex: 0 0 91.666667%; max-width: 91.666667%; }\n.col-12 { flex: 0 0 100%; max-width: 100%; }\n\n/* Modern Navigation */\n.navbar {\n  background: var(--white);\n  border-bottom: 1px solid var(--gray-200);\n  box-shadow: var(--shadow-sm);\n  padding: var(--space-4) 0;\n}\n\n.navbar-brand {\n  font-size: var(--font-size-xl);\n  font-weight: 700;\n  color: var(--primary);\n  text-decoration: none;\n}\n\n.navbar-nav {\n  display: flex;\n  align-items: center;\n  gap: var(--space-6);\n  list-style: none;\n  margin: 0;\n  padding: 0;\n}\n\n.nav-link {\n  color: var(--gray-600);\n  text-decoration: none;\n  font-weight: 500;\n  transition: var(--transition-normal);\n  padding: var(--space-2) var(--space-3);\n  border-radius: var(--radius-md);\n}\n\n.nav-link:hover,\n.nav-link.active {\n  color: var(--primary);\n  background: var(--primary-50);\n}\n\n/* Modern Sidebar */\n.sidebar {\n  background: linear-gradient(135deg, var(--primary) 0%, var(--primary-dark) 100%);\n  color: var(--white);\n  min-height: 100vh;\n  padding: var(--space-6);\n  box-shadow: var(--shadow-xl);\n  border-right: 1px solid rgba(255, 255, 255, 0.1);\n}\n\n.sidebar-brand {\n  display: flex;\n  align-items: center;\n  gap: var(--space-3);\n  margin-bottom: var(--space-8);\n  padding-bottom: var(--space-6);\n  border-bottom: 1px solid rgba(255, 255, 255, 0.1);\n}\n\n.sidebar-brand img {\n  width: 40px;\n  height: 40px;\n  border-radius: var(--radius-lg);\n}\n\n.sidebar-brand h3 {\n  color: var(--white);\n  font-weight: 700;\n  margin: 0;\n}\n\n.sidebar-menu {\n  list-style: none;\n  padding: 0;\n  margin: 0;\n}\n\n.sidebar-menu-item {\n  margin-bottom: var(--space-2);\n}\n\n.sidebar-menu-link {\n  display: flex;\n  align-items: center;\n  gap: var(--space-3);\n  padding: var(--space-3) var(--space-4);\n  color: rgba(255, 255, 255, 0.9);\n  text-decoration: none;\n  border-radius: var(--radius-lg);\n  transition: var(--transition-normal);\n  font-weight: 500;\n}\n\n.sidebar-menu-link:hover,\n.sidebar-menu-link.active {\n  background: rgba(255, 255, 255, 0.1);\n  color: var(--white);\n  transform: translateX(4px);\n}\n\n.sidebar-menu-icon {\n  width: 20px;\n  height: 20px;\n  opacity: 0.8;\n}\n\n/* Modern Table */\n.table {\n  width: 100%;\n  border-collapse: collapse;\n  background: var(--white);\n  border-radius: var(--radius-lg);\n  overflow: hidden;\n  box-shadow: var(--shadow-md);\n}\n\n.table th {\n  background: var(--gray-50);\n  padding: var(--space-4);\n  text-align: left;\n  font-weight: 600;\n  color: var(--gray-700);\n  border-bottom: 1px solid var(--gray-200);\n}\n\n.table td {\n  padding: var(--space-4);\n  border-bottom: 1px solid var(--gray-100);\n  color: var(--gray-600);\n}\n\n.table tbody tr:hover {\n  background: var(--gray-50);\n}\n\n.table tbody tr:last-child td {\n  border-bottom: none;\n}\n\n/* Modern Alert/Notification */\n.alert {\n  padding: var(--space-4);\n  border-radius: var(--radius-lg);\n  border: 1px solid transparent;\n  margin-bottom: var(--space-4);\n  display: flex;\n  align-items: center;\n  gap: var(--space-3);\n}\n\n.alert-success {\n  background: var(--success-light);\n  border-color: var(--success);\n  color: #065f46;\n}\n\n.alert-warning {\n  background: var(--warning-light);\n  border-color: var(--warning);\n  color: #92400e;\n}\n\n.alert-danger {\n  background: var(--danger-light);\n  border-color: var(--danger);\n  color: #991b1b;\n}\n\n.alert-info {\n  background: var(--info-light);\n  border-color: var(--info);\n  color: #155e75;\n}\n\n/* Modern Badge */\n.badge {\n  display: inline-flex;\n  align-items: center;\n  padding: var(--space-1) var(--space-3);\n  font-size: var(--font-size-xs);\n  font-weight: 600;\n  border-radius: var(--radius-full);\n  text-transform: uppercase;\n  letter-spacing: 0.025em;\n}\n\n.badge-primary {\n  background: var(--primary-100);\n  color: var(--primary-800);\n}\n\n.badge-success {\n  background: var(--success-light);\n  color: #065f46;\n}\n\n.badge-warning {\n  background: var(--warning-light);\n  color: #92400e;\n}\n\n.badge-danger {\n  background: var(--danger-light);\n  color: #991b1b;\n}\n\n.badge-secondary {\n  background: var(--gray-100);\n  color: var(--gray-700);\n}\n\n/* Modern Progress Bar */\n.progress {\n  width: 100%;\n  height: 8px;\n  background: var(--gray-200);\n  border-radius: var(--radius-full);\n  overflow: hidden;\n}\n\n.progress-bar {\n  height: 100%;\n  background: linear-gradient(90deg, var(--primary) 0%, var(--primary-light) 100%);\n  border-radius: var(--radius-full);\n  transition: width var(--transition-slow);\n}\n\n.progress-bar-success {\n  background: linear-gradient(90deg, var(--success) 0%, #10b981 100%);\n}\n\n.progress-bar-warning {\n  background: linear-gradient(90deg, var(--warning) 0%, #f59e0b 100%);\n}\n\n.progress-bar-danger {\n  background: linear-gradient(90deg, var(--danger) 0%, #ef4444 100%);\n}\n\n/* Modern Modal */\n.modal-backdrop {\n  position: fixed;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n  background: rgba(0, 0, 0, 0.5);\n  backdrop-filter: blur(4px);\n  z-index: var(--z-modal-backdrop);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  padding: var(--space-4);\n}\n\n.modal {\n  background: var(--white);\n  border-radius: var(--radius-xl);\n  box-shadow: var(--shadow-xl);\n  max-width: 500px;\n  width: 100%;\n  max-height: 90vh;\n  overflow-y: auto;\n  z-index: var(--z-modal);\n  animation: modalSlideIn 0.3s ease-out;\n}\n\n@keyframes modalSlideIn {\n  from {\n    opacity: 0;\n    transform: translateY(-20px) scale(0.95);\n  }\n  to {\n    opacity: 1;\n    transform: translateY(0) scale(1);\n  }\n}\n\n.modal-header {\n  padding: var(--space-6);\n  border-bottom: 1px solid var(--gray-200);\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n}\n\n.modal-title {\n  font-size: var(--font-size-lg);\n  font-weight: 600;\n  color: var(--gray-900);\n  margin: 0;\n}\n\n.modal-close {\n  background: none;\n  border: none;\n  font-size: var(--font-size-xl);\n  color: var(--gray-400);\n  cursor: pointer;\n  padding: var(--space-1);\n  border-radius: var(--radius-md);\n  transition: var(--transition-normal);\n}\n\n.modal-close:hover {\n  color: var(--gray-600);\n  background: var(--gray-100);\n}\n\n.modal-body {\n  padding: var(--space-6);\n}\n\n.modal-footer {\n  padding: var(--space-6);\n  border-top: 1px solid var(--gray-200);\n  display: flex;\n  gap: var(--space-3);\n  justify-content: flex-end;\n}\n\n/* Modern Loading Spinner */\n.spinner {\n  width: 40px;\n  height: 40px;\n  border: 4px solid var(--gray-200);\n  border-top: 4px solid var(--primary);\n  border-radius: var(--radius-full);\n  animation: spin 1s linear infinite;\n}\n\n.spinner-sm {\n  width: 20px;\n  height: 20px;\n  border-width: 2px;\n}\n\n.spinner-lg {\n  width: 60px;\n  height: 60px;\n  border-width: 6px;\n}\n\n@keyframes spin {\n  0% { transform: rotate(0deg); }\n  100% { transform: rotate(360deg); }\n}\n\n/* Modern Tooltip */\n.tooltip {\n  position: relative;\n  display: inline-block;\n}\n\n.tooltip-text {\n  visibility: hidden;\n  width: 120px;\n  background: var(--gray-900);\n  color: var(--white);\n  text-align: center;\n  border-radius: var(--radius-md);\n  padding: var(--space-2);\n  position: absolute;\n  z-index: var(--z-tooltip);\n  bottom: 125%;\n  left: 50%;\n  margin-left: -60px;\n  opacity: 0;\n  transition: opacity var(--transition-normal);\n  font-size: var(--font-size-xs);\n}\n\n.tooltip:hover .tooltip-text {\n  visibility: visible;\n  opacity: 1;\n}\n\n/* Modern Dropdown */\n.dropdown {\n  position: relative;\n  display: inline-block;\n}\n\n.dropdown-menu {\n  position: absolute;\n  top: 100%;\n  left: 0;\n  background: var(--white);\n  border: 1px solid var(--gray-200);\n  border-radius: var(--radius-lg);\n  box-shadow: var(--shadow-lg);\n  min-width: 200px;\n  z-index: var(--z-dropdown);\n  opacity: 0;\n  visibility: hidden;\n  transform: translateY(-10px);\n  transition: var(--transition-normal);\n}\n\n.dropdown.active .dropdown-menu {\n  opacity: 1;\n  visibility: visible;\n  transform: translateY(0);\n}\n\n.dropdown-item {\n  display: block;\n  padding: var(--space-3) var(--space-4);\n  color: var(--gray-700);\n  text-decoration: none;\n  transition: var(--transition-normal);\n  border-bottom: 1px solid var(--gray-100);\n}\n\n.dropdown-item:last-child {\n  border-bottom: none;\n}\n\n.dropdown-item:hover {\n  background: var(--gray-50);\n  color: var(--primary);\n}\n\n/* ===== RESPONSIVE DESIGN ===== */\n\n/* Mobile First Approach */\n@media (max-width: 640px) {\n  .container {\n    padding: 0 var(--space-3);\n  }\n\n  .card {\n    margin: var(--space-3);\n    border-radius: var(--radius-lg);\n  }\n\n  .btn {\n    width: 100%;\n    justify-content: center;\n  }\n\n  .modal {\n    margin: var(--space-3);\n    max-width: calc(100% - var(--space-6));\n  }\n\n  .table {\n    font-size: var(--font-size-sm);\n  }\n\n  .table th,\n  .table td {\n    padding: var(--space-2);\n  }\n\n  .sidebar {\n    padding: var(--space-4);\n  }\n\n  .sidebar-menu-link {\n    padding: var(--space-2) var(--space-3);\n  }\n\n  /* Hide text on mobile sidebar */\n  .mobile-sidebar .sidebar-menu-link span {\n    display: none;\n  }\n\n  .mobile-sidebar .sidebar-menu-link {\n    justify-content: center;\n  }\n\n  /* Stack columns on mobile */\n  .row {\n    flex-direction: column;\n  }\n\n  .col,\n  .col-1, .col-2, .col-3, .col-4, .col-5, .col-6,\n  .col-7, .col-8, .col-9, .col-10, .col-11, .col-12 {\n    flex: 0 0 100%;\n    max-width: 100%;\n    margin-bottom: var(--space-4);\n  }\n}\n\n/* Tablet */\n@media (min-width: 641px) and (max-width: 1024px) {\n  .container {\n    padding: 0 var(--space-4);\n  }\n\n  .card {\n    margin: var(--space-4);\n  }\n\n  .modal {\n    max-width: 600px;\n  }\n}\n\n/* Desktop */\n@media (min-width: 1025px) {\n  .container {\n    padding: 0 var(--space-6);\n  }\n\n  .card:hover {\n    transform: translateY(-4px);\n  }\n\n  .btn:hover {\n    transform: translateY(-2px);\n  }\n}\n\n/* ===== ACCESSIBILITY ===== */\n\n/* Focus styles */\n*:focus {\n  outline: 2px solid var(--primary);\n  outline-offset: 2px;\n}\n\n.btn:focus,\n.form-control:focus,\n.form-select:focus {\n  outline: none;\n  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);\n}\n\n/* Reduced motion */\n@media (prefers-reduced-motion: reduce) {\n  *,\n  *::before,\n  *::after {\n    animation-duration: 0.01ms !important;\n    animation-iteration-count: 1 !important;\n    transition-duration: 0.01ms !important;\n    scroll-behavior: auto !important;\n  }\n}\n\n/* High contrast mode */\n@media (prefers-contrast: high) {\n  :root {\n    --gray-100: #000000;\n    --gray-200: #000000;\n    --gray-300: #000000;\n  }\n\n  .card {\n    border: 2px solid var(--gray-900);\n  }\n\n  .btn {\n    border: 2px solid currentColor;\n  }\n}\n\n/* ===== PRINT STYLES ===== */\n@media print {\n  .sidebar,\n  .btn,\n  .modal,\n  .dropdown {\n    display: none !important;\n  }\n\n  .card {\n    box-shadow: none;\n    border: 1px solid var(--gray-300);\n  }\n\n  body {\n    background: white;\n    color: black;\n  }\n}\n\n/* ===== CUSTOM SCROLLBAR ===== */\n::-webkit-scrollbar {\n  width: 8px;\n  height: 8px;\n}\n\n::-webkit-scrollbar-track {\n  background: var(--gray-100);\n  border-radius: var(--radius-full);\n}\n\n::-webkit-scrollbar-thumb {\n  background: var(--gray-300);\n  border-radius: var(--radius-full);\n}\n\n::-webkit-scrollbar-thumb:hover {\n  background: var(--gray-400);\n}\n\n/* Firefox scrollbar */\n* {\n  scrollbar-width: thin;\n  scrollbar-color: var(--gray-300) var(--gray-100);\n}\n\n/* ===== EDUCATIONAL PLATFORM SPECIFIC STYLES ===== */\n\n/* Quiz Fullscreen Mode */\n.quiz-fullscreen {\n  overflow: hidden !important;\n  position: fixed !important;\n  top: 0 !important;\n  left: 0 !important;\n  right: 0 !important;\n  bottom: 0 !important;\n  z-index: 9999 !important;\n  background: white !important;\n}\n\n.quiz-fullscreen .sidebar,\n.quiz-fullscreen .navbar,\n.quiz-fullscreen .header,\n.quiz-fullscreen .nav-modern,\n.quiz-fullscreen .modern-sidebar,\n.quiz-fullscreen [class*=\"sidebar\"] {\n  display: none !important;\n  visibility: hidden !important;\n  opacity: 0 !important;\n  pointer-events: none !important;\n}\n\n/* Ensure quiz container takes full screen */\n.quiz-fullscreen .layout-modern {\n  padding: 0 !important;\n  margin: 0 !important;\n}\n\n.quiz-fullscreen main {\n  padding: 0 !important;\n  margin: 0 !important;\n  width: 100vw !important;\n  height: 100vh !important;\n}\n\n/* Quiz Card Styles - User Preferred Design */\n.quiz-card {\n  background: var(--white);\n  border-radius: var(--radius-lg);\n  box-shadow: var(--shadow-md);\n  border: 1px solid var(--gray-200);\n  transition: var(--transition-normal);\n  overflow: hidden;\n  position: relative;\n  margin-bottom: var(--space-3);\n}\n\n.quiz-card:hover {\n  box-shadow: var(--shadow-lg);\n  transform: translateY(-1px);\n  border-color: var(--primary);\n}\n\n/* Quiz Answer States - User Preferred Green/Red Backgrounds */\n.quiz-card.correct,\n.quiz-option.correct {\n  border-color: var(--success);\n  background: var(--success) !important;\n  color: var(--white) !important;\n}\n\n.quiz-card.incorrect,\n.quiz-option.incorrect {\n  border-color: var(--danger);\n  background: var(--danger) !important;\n  color: var(--white) !important;\n}\n\n.quiz-card.selected,\n.quiz-option.selected {\n  border-color: var(--primary);\n  background: var(--primary) !important;\n  color: var(--white) !important;\n}\n\n/* Quiz Interface - User Preferred Styling */\n.quiz-question-container {\n  background: var(--white);\n  border-radius: var(--radius-lg);\n  padding: var(--space-4);\n  margin-bottom: var(--space-3);\n  box-shadow: var(--shadow-sm);\n  border: 1px solid var(--gray-200);\n}\n\n.quiz-question-number {\n  font-size: var(--font-size-sm);\n  font-weight: 600;\n  color: var(--primary);\n  margin-bottom: var(--space-2);\n}\n\n.quiz-question-text {\n  font-size: var(--font-size-base);\n  color: var(--gray-800);\n  margin-bottom: var(--space-4);\n  line-height: 1.6;\n}\n\n.quiz-options-container {\n  display: flex;\n  flex-direction: column;\n  gap: var(--space-2);\n}\n\n.quiz-option {\n  padding: var(--space-3);\n  border: 2px solid var(--gray-200);\n  border-radius: var(--radius-md);\n  background: var(--white);\n  cursor: pointer;\n  transition: var(--transition-normal);\n  font-size: var(--font-size-sm);\n  display: flex;\n  align-items: center;\n  gap: var(--space-2);\n}\n\n.quiz-option:hover {\n  border-color: var(--primary-300);\n  background: var(--primary-50);\n}\n\n.quiz-option-letter {\n  font-weight: 600;\n  color: var(--gray-600);\n  min-width: 24px;\n  text-align: center;\n}\n\n.quiz-option.selected .quiz-option-letter {\n  color: var(--white);\n}\n\n.quiz-option.correct .quiz-option-letter,\n.quiz-option.incorrect .quiz-option-letter {\n  color: var(--white);\n}\n\n/* Quiz Navigation - User Preferred Bottom-Centered */\n.quiz-navigation {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  gap: var(--space-2);\n  padding: var(--space-4);\n  background: var(--white);\n  border-top: 1px solid var(--gray-200);\n  position: sticky;\n  bottom: 0;\n  z-index: 10;\n}\n\n/* Responsive quiz navigation */\n@media (max-width: 480px) {\n  .quiz-navigation {\n    padding: var(--space-2);\n    gap: var(--space-1);\n    flex-direction: column;\n  }\n}\n\n@media (min-width: 481px) and (max-width: 768px) {\n  .quiz-navigation {\n    padding: var(--space-3);\n    gap: var(--space-2);\n  }\n}\n\n.quiz-nav-btn {\n  padding: var(--space-3) var(--space-6);\n  border: none;\n  border-radius: var(--radius-lg);\n  font-weight: 500;\n  cursor: pointer;\n  transition: var(--transition-normal);\n  font-size: var(--font-size-sm);\n}\n\n/* Responsive quiz navigation buttons */\n@media (max-width: 480px) {\n  .quiz-nav-btn {\n    padding: var(--space-2) var(--space-4);\n    font-size: var(--font-size-xs);\n    width: 100%;\n    margin-bottom: var(--space-1);\n  }\n}\n\n@media (min-width: 481px) and (max-width: 768px) {\n  .quiz-nav-btn {\n    padding: var(--space-2) var(--space-5);\n    font-size: var(--font-size-xs);\n  }\n}\n\n.quiz-nav-btn.primary {\n  background: var(--primary);\n  color: var(--white);\n}\n\n.quiz-nav-btn.primary:hover {\n  background: var(--primary-dark);\n  transform: translateY(-1px);\n}\n\n.quiz-nav-btn.secondary {\n  background: var(--gray-200);\n  color: var(--gray-700);\n}\n\n.quiz-nav-btn.secondary:hover {\n  background: var(--gray-300);\n}\n\n/* Quiz Timer - User Preferred Blue Theme with Red Warning */\n.quiz-timer {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  background: var(--primary);\n  color: var(--white);\n  padding: var(--space-3);\n  border-radius: var(--radius-lg);\n  font-weight: 600;\n  font-size: var(--font-size-lg);\n  min-width: 120px;\n  transition: var(--transition-normal);\n}\n\n/* Responsive quiz timer */\n@media (max-width: 480px) {\n  .quiz-timer {\n    padding: var(--space-2);\n    font-size: var(--font-size-sm);\n    min-width: 100px;\n  }\n}\n\n@media (min-width: 481px) and (max-width: 768px) {\n  .quiz-timer {\n    padding: var(--space-2);\n    font-size: var(--font-size-base);\n    min-width: 110px;\n  }\n}\n\n.quiz-timer.warning {\n  background: var(--danger);\n  animation: pulse 1s infinite;\n}\n\n@keyframes pulse {\n  0%, 100% { opacity: 1; }\n  50% { opacity: 0.8; }\n}\n\n/* Quiz Image Styles */\n.quiz-image-container-modern {\n  margin-bottom: var(--space-6);\n  text-align: center;\n}\n\n.quiz-image-wrapper {\n  display: inline-block;\n  border-radius: var(--radius-lg);\n  overflow: hidden;\n  box-shadow: var(--shadow-lg);\n  border: 1px solid var(--gray-200);\n}\n\n.quiz-image-modern {\n  max-width: 100%;\n  height: auto;\n  max-height: 400px;\n  object-fit: contain;\n  display: block;\n}\n\n/* Quiz Progress Line - User Preferred Blue */\n.quiz-progress-container {\n  padding: var(--space-3) var(--space-4);\n  background: var(--white);\n  border-bottom: 1px solid var(--gray-200);\n}\n\n.quiz-progress-line {\n  width: 100%;\n  height: 4px;\n  background: var(--gray-200);\n  border-radius: var(--radius-full);\n  overflow: hidden;\n}\n\n.quiz-progress-fill {\n  height: 100%;\n  background: var(--primary);\n  border-radius: var(--radius-full);\n  transition: width var(--transition-slow);\n}\n\n.quiz-question-counter {\n  text-align: center;\n  font-size: var(--font-size-sm);\n  color: var(--gray-600);\n  margin-top: var(--space-2);\n}\n\n/* Study Material Cards - Modern Blue Theme */\n.study-card {\n  background: var(--white);\n  border-radius: var(--radius-lg);\n  box-shadow: var(--shadow-md);\n  border: 1px solid var(--gray-200);\n  transition: var(--transition-normal);\n  overflow: hidden;\n  margin-bottom: var(--space-4);\n}\n\n.study-card:hover {\n  box-shadow: var(--shadow-lg);\n  transform: translateY(-2px);\n  border-color: var(--primary);\n}\n\n.study-card-header {\n  background: var(--primary);\n  color: var(--white);\n  padding: var(--space-4);\n  border-bottom: 1px solid var(--primary-dark);\n}\n\n.study-card-body {\n  padding: var(--space-4);\n}\n\n.study-card-title {\n  font-size: var(--font-size-lg);\n  font-weight: 600;\n  color: var(--white);\n  margin-bottom: var(--space-2);\n}\n\n.study-card-meta {\n  display: flex;\n  align-items: center;\n  gap: var(--space-3);\n  font-size: var(--font-size-sm);\n  color: rgba(255, 255, 255, 0.9);\n}\n\n.study-card-description {\n  color: var(--gray-600);\n  font-size: var(--font-size-sm);\n  line-height: 1.5;\n  margin-bottom: var(--space-3);\n}\n\n.study-card-actions {\n  display: flex;\n  gap: var(--space-2);\n  justify-content: flex-end;\n}\n\n/* Study Material Tabs */\n.study-tabs {\n  display: flex;\n  gap: var(--space-1);\n  background: var(--gray-100);\n  padding: var(--space-1);\n  border-radius: var(--radius-lg);\n  margin-bottom: var(--space-4);\n}\n\n/* Responsive study tabs */\n@media (max-width: 480px) {\n  .study-tabs {\n    flex-direction: column;\n    gap: var(--space-1);\n    padding: var(--space-1);\n  }\n}\n\n@media (min-width: 481px) and (max-width: 768px) {\n  .study-tabs {\n    flex-wrap: wrap;\n    gap: var(--space-1);\n  }\n}\n\n.study-tab {\n  flex: 1;\n  padding: var(--space-3) var(--space-4);\n  border: none;\n  border-radius: var(--radius-md);\n  background: transparent;\n  color: var(--gray-600);\n  font-weight: 500;\n  cursor: pointer;\n  transition: var(--transition-normal);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  gap: var(--space-2);\n}\n\n/* Responsive study tab */\n@media (max-width: 480px) {\n  .study-tab {\n    padding: var(--space-2) var(--space-3);\n    font-size: var(--font-size-xs);\n    gap: var(--space-1);\n  }\n}\n\n@media (min-width: 481px) and (max-width: 768px) {\n  .study-tab {\n    padding: var(--space-2) var(--space-3);\n    font-size: var(--font-size-sm);\n  }\n}\n\n.study-tab.active {\n  background: var(--primary);\n  color: var(--white);\n  box-shadow: var(--shadow-sm);\n}\n\n.study-tab:hover:not(.active) {\n  background: var(--gray-200);\n  color: var(--gray-700);\n}\n\n/* Hub/Dashboard Styles - Amazing Icons & Cool Animations */\n.hub-container {\n  min-height: 100vh;\n  background: linear-gradient(135deg, var(--primary-50) 0%, var(--white) 50%, var(--primary-50) 100%);\n  position: relative;\n  overflow: hidden;\n}\n\n.hub-container::before {\n  content: '';\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: url('data:image/svg+xml,<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 100 100\"><defs><pattern id=\"grid\" width=\"10\" height=\"10\" patternUnits=\"userSpaceOnUse\"><path d=\"M 10 0 L 0 0 0 10\" fill=\"none\" stroke=\"%23e5e7eb\" stroke-width=\"0.5\"/></pattern></defs><rect width=\"100\" height=\"100\" fill=\"url(%23grid)\"/></svg>');\n  opacity: 0.3;\n  z-index: 1;\n}\n\n.hub-welcome {\n  text-align: center;\n  padding: var(--space-8) var(--space-4);\n  position: relative;\n  z-index: 2;\n}\n\n.hub-welcome h1 {\n  font-size: clamp(2rem, 5vw, 3.5rem);\n  font-weight: 700;\n  background: linear-gradient(135deg, var(--primary) 0%, var(--primary-dark) 50%, var(--primary) 100%);\n  -webkit-background-clip: text;\n  -webkit-text-fill-color: transparent;\n  background-clip: text;\n  margin-bottom: var(--space-4);\n  animation: fadeInUp 0.8s ease-out;\n}\n\n.hub-welcome p {\n  font-size: var(--font-size-xl);\n  color: var(--gray-600);\n  margin-bottom: var(--space-6);\n  animation: fadeInUp 0.8s ease-out 0.2s both;\n}\n\n.hub-quote {\n  background: rgba(255, 255, 255, 0.9);\n  backdrop-filter: blur(10px);\n  border: 1px solid rgba(255, 255, 255, 0.2);\n  border-radius: var(--radius-xl);\n  padding: var(--space-6);\n  margin: var(--space-6) auto;\n  max-width: 600px;\n  box-shadow: var(--shadow-lg);\n  animation: fadeInUp 0.8s ease-out 0.4s both;\n}\n\n.hub-quote-text {\n  font-size: var(--font-size-lg);\n  font-style: italic;\n  color: var(--gray-700);\n  margin-bottom: var(--space-2);\n}\n\n.hub-quote-author {\n  font-size: var(--font-size-sm);\n  color: var(--primary);\n  font-weight: 600;\n}\n\n/* Hub Navigation Cards - Responsive */\n.hub-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));\n  gap: var(--space-4);\n  padding: var(--space-4);\n  max-width: 1200px;\n  margin: 0 auto;\n  position: relative;\n  z-index: 2;\n}\n\n/* Responsive hub grid */\n@media (max-width: 480px) {\n  .hub-grid {\n    grid-template-columns: 1fr;\n    gap: var(--space-3);\n    padding: var(--space-2);\n  }\n}\n\n@media (min-width: 481px) and (max-width: 768px) {\n  .hub-grid {\n    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\n    gap: var(--space-3);\n    padding: var(--space-3);\n  }\n}\n\n@media (min-width: 769px) and (max-width: 1024px) {\n  .hub-grid {\n    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));\n    gap: var(--space-4);\n  }\n}\n\n.hub-card {\n  background: var(--white);\n  border-radius: var(--radius-xl);\n  padding: var(--space-6);\n  box-shadow: var(--shadow-lg);\n  border: 1px solid var(--gray-200);\n  transition: var(--transition-normal);\n  cursor: pointer;\n  position: relative;\n  overflow: hidden;\n  animation: fadeInUp 0.6s ease-out;\n}\n\n.hub-card::before {\n  content: '';\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  height: 4px;\n  background: var(--primary);\n  transform: scaleX(0);\n  transition: transform var(--transition-normal);\n}\n\n.hub-card:hover {\n  transform: translateY(-4px);\n  box-shadow: var(--shadow-xl);\n  border-color: var(--primary);\n}\n\n.hub-card:hover::before {\n  transform: scaleX(1);\n}\n\n.hub-card-icon {\n  width: 60px;\n  height: 60px;\n  background: var(--primary);\n  border-radius: var(--radius-xl);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  margin-bottom: var(--space-4);\n  color: var(--white);\n  font-size: 1.5rem;\n  transition: var(--transition-normal);\n}\n\n/* Responsive hub card icons */\n@media (max-width: 480px) {\n  .hub-card-icon {\n    width: 50px;\n    height: 50px;\n    font-size: 1.25rem;\n    margin-bottom: var(--space-3);\n  }\n}\n\n@media (min-width: 481px) and (max-width: 768px) {\n  .hub-card-icon {\n    width: 55px;\n    height: 55px;\n    font-size: 1.375rem;\n  }\n}\n\n.hub-card:hover .hub-card-icon {\n  transform: scale(1.1) rotate(5deg);\n  background: var(--primary-dark);\n}\n\n.hub-card-title {\n  font-size: var(--font-size-xl);\n  font-weight: 600;\n  color: var(--gray-900);\n  margin-bottom: var(--space-2);\n}\n\n.hub-card-description {\n  color: var(--gray-600);\n  font-size: var(--font-size-sm);\n  line-height: 1.5;\n}\n\n/* Responsive hub card text */\n@media (max-width: 480px) {\n  .hub-card-title {\n    font-size: var(--font-size-lg);\n    margin-bottom: var(--space-1);\n  }\n\n  .hub-card-description {\n    font-size: var(--font-size-xs);\n  }\n}\n\n@media (min-width: 481px) and (max-width: 768px) {\n  .hub-card-title {\n    font-size: var(--font-size-lg);\n  }\n\n  .hub-card-description {\n    font-size: var(--font-size-xs);\n  }\n}\n\n/* Animations */\n@keyframes fadeInUp {\n  from {\n    opacity: 0;\n    transform: translateY(30px);\n  }\n  to {\n    opacity: 1;\n    transform: translateY(0);\n  }\n}\n\n@keyframes fadeIn {\n  from { opacity: 0; }\n  to { opacity: 1; }\n}\n\n@keyframes slideInRight {\n  from {\n    opacity: 0;\n    transform: translateX(30px);\n  }\n  to {\n    opacity: 1;\n    transform: translateX(0);\n  }\n}\n\n@keyframes float {\n  0%, 100% { transform: translateY(0px); }\n  50% { transform: translateY(-10px); }\n}\n\n@keyframes bounce {\n  0%, 20%, 53%, 80%, 100% { transform: translateY(0); }\n  40%, 43% { transform: translateY(-10px); }\n  70% { transform: translateY(-5px); }\n  90% { transform: translateY(-2px); }\n}\n\n/* Animation Classes */\n.animate-fadeInUp { animation: fadeInUp 0.6s ease-out; }\n.animate-fadeIn { animation: fadeIn 0.6s ease-out; }\n.animate-slideInRight { animation: slideInRight 0.5s ease-out; }\n.animate-float { animation: float 3s ease-in-out infinite; }\n.animate-bounce { animation: bounce 2s infinite; }\n\n/* Delay Classes */\n.animate-delay-100 { animation-delay: 0.1s; }\n.animate-delay-200 { animation-delay: 0.2s; }\n.animate-delay-300 { animation-delay: 0.3s; }\n.animate-delay-400 { animation-delay: 0.4s; }\n.animate-delay-500 { animation-delay: 0.5s; }\n\n/* Enhanced Button Styles - Blue Theme */\n.btn {\n  display: inline-flex;\n  align-items: center;\n  justify-content: center;\n  gap: var(--space-2);\n  padding: var(--space-3) var(--space-6);\n  border-radius: var(--radius-lg);\n  font-weight: 500;\n  font-size: var(--font-size-sm);\n  line-height: 1;\n  text-decoration: none;\n  border: none;\n  cursor: pointer;\n  transition: var(--transition-normal);\n  position: relative;\n  overflow: hidden;\n  white-space: nowrap;\n}\n\n.btn:disabled {\n  opacity: 0.6;\n  cursor: not-allowed;\n  transform: none !important;\n}\n\n.btn-primary {\n  background: var(--primary);\n  color: var(--white);\n  box-shadow: var(--shadow-primary);\n}\n\n.btn-primary:hover:not(:disabled) {\n  background: var(--primary-dark);\n  transform: translateY(-1px);\n  box-shadow: 0 8px 25px -5px rgba(0, 123, 255, 0.3);\n}\n\n.btn-secondary {\n  background: var(--white);\n  color: var(--primary);\n  border: 2px solid var(--primary);\n}\n\n.btn-secondary:hover:not(:disabled) {\n  background: var(--primary);\n  color: var(--white);\n  transform: translateY(-1px);\n}\n\n.btn-success {\n  background: var(--success);\n  color: var(--white);\n  box-shadow: 0 4px 14px 0 rgba(16, 185, 129, 0.15);\n}\n\n.btn-success:hover:not(:disabled) {\n  background: var(--success-dark);\n  transform: translateY(-1px);\n}\n\n.btn-danger {\n  background: var(--danger);\n  color: var(--white);\n  box-shadow: 0 4px 14px 0 rgba(239, 68, 68, 0.15);\n}\n\n.btn-danger:hover:not(:disabled) {\n  background: var(--danger-dark);\n  transform: translateY(-1px);\n}\n\n.btn-sm {\n  padding: var(--space-2) var(--space-4);\n  font-size: var(--font-size-xs);\n}\n\n.btn-lg {\n  padding: var(--space-4) var(--space-8);\n  font-size: var(--font-size-base);\n}\n\n/* ===== UTILITY CLASSES ===== */\n\n/* Display utilities */\n.d-flex { display: flex !important; }\n.d-block { display: block !important; }\n.d-inline { display: inline !important; }\n.d-inline-block { display: inline-block !important; }\n.d-none { display: none !important; }\n\n/* Flexbox utilities */\n.justify-content-center { justify-content: center !important; }\n.justify-content-between { justify-content: space-between !important; }\n.justify-content-around { justify-content: space-around !important; }\n.justify-content-start { justify-content: flex-start !important; }\n.justify-content-end { justify-content: flex-end !important; }\n\n.align-items-center { align-items: center !important; }\n.align-items-start { align-items: flex-start !important; }\n.align-items-end { align-items: flex-end !important; }\n.align-items-stretch { align-items: stretch !important; }\n\n.flex-direction-column { flex-direction: column !important; }\n.flex-direction-row { flex-direction: row !important; }\n.flex-wrap { flex-wrap: wrap !important; }\n.flex-nowrap { flex-wrap: nowrap !important; }\n\n/* Spacing utilities */\n.gap-1 { gap: var(--space-1) !important; }\n.gap-2 { gap: var(--space-2) !important; }\n.gap-3 { gap: var(--space-3) !important; }\n.gap-4 { gap: var(--space-4) !important; }\n.gap-5 { gap: var(--space-5) !important; }\n.gap-6 { gap: var(--space-6) !important; }\n\n/* Margin utilities */\n.m-0 { margin: 0 !important; }\n.m-1 { margin: var(--space-1) !important; }\n.m-2 { margin: var(--space-2) !important; }\n.m-3 { margin: var(--space-3) !important; }\n.m-4 { margin: var(--space-4) !important; }\n.m-5 { margin: var(--space-5) !important; }\n.m-6 { margin: var(--space-6) !important; }\n\n.mt-0 { margin-top: 0 !important; }\n.mt-1 { margin-top: var(--space-1) !important; }\n.mt-2 { margin-top: var(--space-2) !important; }\n.mt-3 { margin-top: var(--space-3) !important; }\n.mt-4 { margin-top: var(--space-4) !important; }\n.mt-5 { margin-top: var(--space-5) !important; }\n.mt-6 { margin-top: var(--space-6) !important; }\n\n.mb-0 { margin-bottom: 0 !important; }\n.mb-1 { margin-bottom: var(--space-1) !important; }\n.mb-2 { margin-bottom: var(--space-2) !important; }\n.mb-3 { margin-bottom: var(--space-3) !important; }\n.mb-4 { margin-bottom: var(--space-4) !important; }\n.mb-5 { margin-bottom: var(--space-5) !important; }\n.mb-6 { margin-bottom: var(--space-6) !important; }\n\n.ml-0 { margin-left: 0 !important; }\n.ml-1 { margin-left: var(--space-1) !important; }\n.ml-2 { margin-left: var(--space-2) !important; }\n.ml-3 { margin-left: var(--space-3) !important; }\n.ml-4 { margin-left: var(--space-4) !important; }\n\n.mr-0 { margin-right: 0 !important; }\n.mr-1 { margin-right: var(--space-1) !important; }\n.mr-2 { margin-right: var(--space-2) !important; }\n.mr-3 { margin-right: var(--space-3) !important; }\n.mr-4 { margin-right: var(--space-4) !important; }\n\n/* Padding utilities */\n.p-0 { padding: 0 !important; }\n.p-1 { padding: var(--space-1) !important; }\n.p-2 { padding: var(--space-2) !important; }\n.p-3 { padding: var(--space-3) !important; }\n.p-4 { padding: var(--space-4) !important; }\n.p-5 { padding: var(--space-5) !important; }\n.p-6 { padding: var(--space-6) !important; }\n\n.pt-0 { padding-top: 0 !important; }\n.pt-1 { padding-top: var(--space-1) !important; }\n.pt-2 { padding-top: var(--space-2) !important; }\n.pt-3 { padding-top: var(--space-3) !important; }\n.pt-4 { padding-top: var(--space-4) !important; }\n\n.pb-0 { padding-bottom: 0 !important; }\n.pb-1 { padding-bottom: var(--space-1) !important; }\n.pb-2 { padding-bottom: var(--space-2) !important; }\n.pb-3 { padding-bottom: var(--space-3) !important; }\n.pb-4 { padding-bottom: var(--space-4) !important; }\n\n.pl-0 { padding-left: 0 !important; }\n.pl-1 { padding-left: var(--space-1) !important; }\n.pl-2 { padding-left: var(--space-2) !important; }\n.pl-3 { padding-left: var(--space-3) !important; }\n.pl-4 { padding-left: var(--space-4) !important; }\n\n.pr-0 { padding-right: 0 !important; }\n.pr-1 { padding-right: var(--space-1) !important; }\n.pr-2 { padding-right: var(--space-2) !important; }\n.pr-3 { padding-right: var(--space-3) !important; }\n.pr-4 { padding-right: var(--space-4) !important; }\n\n/* Text utilities */\n.text-center { text-align: center !important; }\n.text-left { text-align: left !important; }\n.text-right { text-align: right !important; }\n\n.text-primary { color: var(--primary) !important; }\n.text-secondary { color: var(--gray-600) !important; }\n.text-success { color: var(--success) !important; }\n.text-danger { color: var(--danger) !important; }\n.text-warning { color: var(--warning) !important; }\n.text-info { color: var(--info) !important; }\n.text-white { color: var(--white) !important; }\n.text-dark { color: var(--gray-900) !important; }\n.text-muted { color: var(--gray-500) !important; }\n\n.font-weight-normal { font-weight: 400 !important; }\n.font-weight-medium { font-weight: 500 !important; }\n.font-weight-semibold { font-weight: 600 !important; }\n.font-weight-bold { font-weight: 700 !important; }\n\n.text-sm { font-size: var(--font-size-sm) !important; }\n.text-base { font-size: var(--font-size-base) !important; }\n.text-lg { font-size: var(--font-size-lg) !important; }\n.text-xl { font-size: var(--font-size-xl) !important; }\n.text-2xl { font-size: var(--font-size-2xl) !important; }\n\n/* Background utilities */\n.bg-primary { background-color: var(--primary) !important; }\n.bg-secondary { background-color: var(--gray-600) !important; }\n.bg-success { background-color: var(--success) !important; }\n.bg-danger { background-color: var(--danger) !important; }\n.bg-warning { background-color: var(--warning) !important; }\n.bg-info { background-color: var(--info) !important; }\n.bg-white { background-color: var(--white) !important; }\n.bg-light { background-color: var(--gray-100) !important; }\n.bg-dark { background-color: var(--gray-900) !important; }\n\n/* Border utilities */\n.border { border: 1px solid var(--gray-200) !important; }\n.border-0 { border: 0 !important; }\n.border-primary { border-color: var(--primary) !important; }\n.border-secondary { border-color: var(--gray-300) !important; }\n.border-success { border-color: var(--success) !important; }\n.border-danger { border-color: var(--danger) !important; }\n\n.rounded { border-radius: var(--radius-md) !important; }\n.rounded-sm { border-radius: var(--radius-sm) !important; }\n.rounded-lg { border-radius: var(--radius-lg) !important; }\n.rounded-xl { border-radius: var(--radius-xl) !important; }\n.rounded-full { border-radius: var(--radius-full) !important; }\n\n/* Width utilities */\n.w-25 { width: 25% !important; }\n.w-50 { width: 50% !important; }\n.w-75 { width: 75% !important; }\n.w-100 { width: 100% !important; }\n.w-auto { width: auto !important; }\n\n/* Grid utilities */\n.grid { display: grid !important; }\n.grid-cols-1 { grid-template-columns: repeat(1, minmax(0, 1fr)) !important; }\n.grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)) !important; }\n.grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)) !important; }\n.grid-cols-4 { grid-template-columns: repeat(4, minmax(0, 1fr)) !important; }\n.grid-cols-auto { grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)) !important; }\n\n/* Responsive grid utilities */\n@media (max-width: 480px) {\n  .grid-cols-2, .grid-cols-3, .grid-cols-4, .grid-cols-auto {\n    grid-template-columns: 1fr !important;\n  }\n}\n\n@media (min-width: 481px) and (max-width: 768px) {\n  .grid-cols-3, .grid-cols-4 {\n    grid-template-columns: repeat(2, minmax(0, 1fr)) !important;\n  }\n  .grid-cols-auto {\n    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)) !important;\n  }\n}\n\n@media (min-width: 769px) and (max-width: 1024px) {\n  .grid-cols-4 {\n    grid-template-columns: repeat(3, minmax(0, 1fr)) !important;\n  }\n}\n\n/* Height utilities */\n.h-25 { height: 25% !important; }\n.h-50 { height: 50% !important; }\n.h-75 { height: 75% !important; }\n.h-100 { height: 100% !important; }\n.h-auto { height: auto !important; }\n\n/* Position utilities */\n.position-relative { position: relative !important; }\n.position-absolute { position: absolute !important; }\n.position-fixed { position: fixed !important; }\n.position-sticky { position: sticky !important; }\n\n/* Shadow utilities */\n.shadow-sm { box-shadow: var(--shadow-sm) !important; }\n.shadow { box-shadow: var(--shadow-md) !important; }\n.shadow-lg { box-shadow: var(--shadow-lg) !important; }\n.shadow-xl { box-shadow: var(--shadow-xl) !important; }\n.shadow-none { box-shadow: none !important; }\n\n/* ===== GLOBAL OVERRIDES FOR CONSISTENCY ===== */\n\n/* ===== RESPONSIVE CONTAINER SYSTEM ===== */\n.container, .container-fluid {\n  background: var(--white) !important;\n  width: 100% !important;\n  margin-left: auto !important;\n  margin-right: auto !important;\n  padding-left: var(--space-4) !important;\n  padding-right: var(--space-4) !important;\n}\n\n.container {\n  max-width: 1200px !important;\n}\n\n/* Responsive container padding */\n@media (max-width: 480px) {\n  .container, .container-fluid {\n    padding-left: var(--space-2) !important;\n    padding-right: var(--space-2) !important;\n  }\n}\n\n@media (min-width: 481px) and (max-width: 768px) {\n  .container, .container-fluid {\n    padding-left: var(--space-3) !important;\n    padding-right: var(--space-3) !important;\n  }\n}\n\n@media (min-width: 769px) {\n  .container {\n    max-width: 1200px !important;\n  }\n}\n\n@media (min-width: 1200px) {\n  .container {\n    max-width: 1400px !important;\n  }\n}\n\n/* Override any conflicting button styles */\nbutton {\n  font-family: inherit !important;\n  border: none !important;\n  cursor: pointer !important;\n}\n\n/* Ensure consistent spacing */\n.row {\n  margin: 0 !important;\n}\n\n.col, [class*=\"col-\"] {\n  padding: var(--space-2) !important;\n}\n\n/* Override any conflicting text colors */\nh1, h2, h3, h4, h5, h6 {\n  color: var(--gray-900) !important;\n}\n\np {\n  color: var(--gray-700) !important;\n}\n\n/* Ensure consistent link styling */\na {\n  color: var(--primary) !important;\n  text-decoration: none !important;\n}\n\na:hover {\n  color: var(--primary-dark) !important;\n}\n\n/* Override any conflicting list styles */\nul, ol {\n  list-style: none !important;\n  padding: 0 !important;\n  margin: 0 !important;\n}\n\n/* Ensure consistent table styling */\ntable {\n  width: 100% !important;\n  border-collapse: collapse !important;\n}\n\nth, td {\n  padding: var(--space-3) !important;\n  border-bottom: 1px solid var(--gray-200) !important;\n}\n\nth {\n  background: var(--gray-50) !important;\n  font-weight: 600 !important;\n  color: var(--gray-900) !important;\n}\n\n/* ===== RESPONSIVE DESIGN SYSTEM ===== */\n\n/* Base responsive typography */\nhtml {\n  font-size: 16px;\n}\n\n/* Responsive font sizes */\n@media (max-width: 480px) {\n  html { font-size: 14px; }\n\n  h1 { font-size: 1.75rem !important; }\n  h2 { font-size: 1.5rem !important; }\n  h3 { font-size: 1.25rem !important; }\n  h4 { font-size: 1.125rem !important; }\n  h5 { font-size: 1rem !important; }\n  h6 { font-size: 0.875rem !important; }\n}\n\n@media (min-width: 481px) and (max-width: 768px) {\n  html { font-size: 15px; }\n\n  h1 { font-size: 2rem !important; }\n  h2 { font-size: 1.75rem !important; }\n  h3 { font-size: 1.5rem !important; }\n  h4 { font-size: 1.25rem !important; }\n  h5 { font-size: 1.125rem !important; }\n  h6 { font-size: 1rem !important; }\n}\n\n@media (min-width: 769px) and (max-width: 1024px) {\n  html { font-size: 16px; }\n}\n\n@media (min-width: 1025px) {\n  html { font-size: 16px; }\n}\n\n/* Responsive icon sizes */\n.icon-xs { font-size: 0.75rem !important; }\n.icon-sm { font-size: 1rem !important; }\n.icon-md { font-size: 1.25rem !important; }\n.icon-lg { font-size: 1.5rem !important; }\n.icon-xl { font-size: 2rem !important; }\n.icon-2xl { font-size: 2.5rem !important; }\n.icon-3xl { font-size: 3rem !important; }\n\n/* Responsive icon scaling */\n@media (max-width: 480px) {\n  .icon-xs { font-size: 0.625rem !important; }\n  .icon-sm { font-size: 0.875rem !important; }\n  .icon-md { font-size: 1rem !important; }\n  .icon-lg { font-size: 1.25rem !important; }\n  .icon-xl { font-size: 1.5rem !important; }\n  .icon-2xl { font-size: 2rem !important; }\n  .icon-3xl { font-size: 2.5rem !important; }\n}\n\n@media (min-width: 481px) and (max-width: 768px) {\n  .icon-xs { font-size: 0.75rem !important; }\n  .icon-sm { font-size: 0.875rem !important; }\n  .icon-md { font-size: 1.125rem !important; }\n  .icon-lg { font-size: 1.375rem !important; }\n  .icon-xl { font-size: 1.75rem !important; }\n  .icon-2xl { font-size: 2.25rem !important; }\n  .icon-3xl { font-size: 2.75rem !important; }\n}\n\n/* Mobile-first responsive utilities */\n@media (max-width: 480px) {\n  .container {\n    padding: var(--space-2) !important;\n    margin: 0 !important;\n  }\n\n  .card {\n    margin-bottom: var(--space-3) !important;\n    padding: var(--space-3) !important;\n  }\n\n  .btn {\n    width: 100% !important;\n    margin-bottom: var(--space-2) !important;\n    padding: var(--space-3) var(--space-4) !important;\n    font-size: 0.875rem !important;\n  }\n\n  .sidebar {\n    width: 100% !important;\n    height: auto !important;\n    position: relative !important;\n  }\n\n  .layout {\n    flex-direction: column !important;\n  }\n\n  .content {\n    padding: var(--space-2) !important;\n  }\n\n  .header {\n    padding: var(--space-3) !important;\n    margin-bottom: var(--space-2) !important;\n  }\n}\n\n@media (min-width: 481px) and (max-width: 768px) {\n  .container {\n    padding: var(--space-3) !important;\n  }\n\n  .card {\n    margin-bottom: var(--space-3) !important;\n    padding: var(--space-4) !important;\n  }\n\n  .btn {\n    padding: var(--space-3) var(--space-5) !important;\n    font-size: 0.875rem !important;\n  }\n\n  .content {\n    padding: var(--space-3) !important;\n  }\n\n  .header {\n    padding: var(--space-4) !important;\n  }\n}\n\n@media (min-width: 769px) and (max-width: 1024px) {\n  .container {\n    padding: var(--space-4) !important;\n  }\n\n  .card {\n    padding: var(--space-4) !important;\n  }\n\n  .content {\n    padding: var(--space-4) !important;\n  }\n}\n\n/* Ensure quiz components use modern styling */\n.quiz-container * {\n  font-family: inherit !important;\n}\n\n/* Ensure study material components use modern styling */\n.study-material-modern * {\n  font-family: inherit !important;\n}\n\n/* Ensure hub components use modern styling */\n.hub-container * {\n  font-family: inherit !important;\n}\n\n/* Force modern styling on all major components */\n.layout, .sidebar, .content, .header {\n  font-family: 'Inter', 'Roboto', 'Nunito', system-ui, -apple-system, sans-serif !important;\n}\n\n/* ===== CONSISTENT ICON SYSTEM ===== */\n.icon, [class*=\"icon-\"], .fa, [class*=\"fa-\"], svg {\n  display: inline-block !important;\n  vertical-align: middle !important;\n  line-height: 1 !important;\n}\n\n/* Standard icon sizes */\n.fa, [class*=\"fa-\"], svg {\n  font-size: 1.25rem !important;\n  width: 1.25rem !important;\n  height: 1.25rem !important;\n}\n\n/* Specific icon size classes */\n.text-xs .fa, .text-xs [class*=\"fa-\"], .text-xs svg { font-size: 0.75rem !important; width: 0.75rem !important; height: 0.75rem !important; }\n.text-sm .fa, .text-sm [class*=\"fa-\"], .text-sm svg { font-size: 1rem !important; width: 1rem !important; height: 1rem !important; }\n.text-base .fa, .text-base [class*=\"fa-\"], .text-base svg { font-size: 1.25rem !important; width: 1.25rem !important; height: 1.25rem !important; }\n.text-lg .fa, .text-lg [class*=\"fa-\"], .text-lg svg { font-size: 1.5rem !important; width: 1.5rem !important; height: 1.5rem !important; }\n.text-xl .fa, .text-xl [class*=\"fa-\"], .text-xl svg { font-size: 2rem !important; width: 2rem !important; height: 2rem !important; }\n.text-2xl .fa, .text-2xl [class*=\"fa-\"], .text-2xl svg { font-size: 2.5rem !important; width: 2.5rem !important; height: 2.5rem !important; }\n.text-3xl .fa, .text-3xl [class*=\"fa-\"], .text-3xl svg { font-size: 3rem !important; width: 3rem !important; height: 3rem !important; }\n.text-4xl .fa, .text-4xl [class*=\"fa-\"], .text-4xl svg { font-size: 3.5rem !important; width: 3.5rem !important; height: 3.5rem !important; }\n\n/* Menu item icons */\n.menu-item .fa, .menu-item [class*=\"fa-\"], .menu-item svg {\n  font-size: 1.25rem !important;\n  width: 1.25rem !important;\n  height: 1.25rem !important;\n  margin-right: var(--space-3) !important;\n}\n\n/* Hub card icons */\n.hub-card-icon .fa, .hub-card-icon [class*=\"fa-\"], .hub-card-icon svg {\n  font-size: 2rem !important;\n  width: 2rem !important;\n  height: 2rem !important;\n}\n\n/* Header icons */\n.header .fa, .header [class*=\"fa-\"], .header svg {\n  font-size: 1.5rem !important;\n  width: 1.5rem !important;\n  height: 1.5rem !important;\n}\n\n/* Button icons */\n.btn .fa, .btn [class*=\"fa-\"], .btn svg {\n  font-size: 1rem !important;\n  width: 1rem !important;\n  height: 1rem !important;\n  margin-right: var(--space-2) !important;\n}\n\n/* Card header icons */\n.card-header .fa, .card-header [class*=\"fa-\"], .card-header svg {\n  font-size: 1.25rem !important;\n  width: 1.25rem !important;\n  height: 1.25rem !important;\n}\n\n/* Override any conflicting modal styles */\n.modal, .modal-content {\n  border-radius: var(--radius-lg) !important;\n  border: none !important;\n  box-shadow: var(--shadow-xl) !important;\n}\n\n.modal-header {\n  background: var(--primary) !important;\n  color: white !important;\n  border-bottom: none !important;\n  border-radius: var(--radius-lg) var(--radius-lg) 0 0 !important;\n}\n\n.modal-footer {\n  border-top: 1px solid var(--gray-200) !important;\n  background: var(--gray-50) !important;\n  border-radius: 0 0 var(--radius-lg) var(--radius-lg) !important;\n}\n\n/* Ensure consistent loading states */\n.loading, .spinner {\n  color: var(--primary) !important;\n}\n\n/* Override any conflicting alert styles */\n.alert {\n  border-radius: var(--radius-lg) !important;\n  border: none !important;\n  padding: var(--space-4) !important;\n}\n\n.alert-success {\n  background: var(--success-light) !important;\n  color: var(--success-dark) !important;\n}\n\n.alert-danger {\n  background: var(--danger-light) !important;\n  color: var(--danger-dark) !important;\n}\n\n.alert-warning {\n  background: var(--warning-light) !important;\n  color: var(--warning-dark) !important;\n}\n\n.alert-info {\n  background: var(--info-light) !important;\n  color: var(--info-dark) !important;\n}\n\n/* ===== FINAL RESPONSIVE TEXT SCALING ===== */\n\n/* Ensure consistent text scaling across all components */\n@media (max-width: 480px) {\n  /* Small text elements */\n  .text-xs, small, .small { font-size: 0.625rem !important; }\n  .text-sm { font-size: 0.75rem !important; }\n  .text-base, p, span, div { font-size: 0.875rem !important; }\n  .text-lg { font-size: 1rem !important; }\n  .text-xl { font-size: 1.125rem !important; }\n  .text-2xl { font-size: 1.25rem !important; }\n\n  /* Button text */\n  .btn { font-size: 0.75rem !important; }\n  .btn-sm { font-size: 0.625rem !important; }\n  .btn-lg { font-size: 0.875rem !important; }\n\n  /* Menu items */\n  .menu-item { font-size: 0.75rem !important; }\n\n  /* Card text */\n  .card-title { font-size: 1rem !important; }\n  .card-text { font-size: 0.75rem !important; }\n}\n\n@media (min-width: 481px) and (max-width: 768px) {\n  /* Medium screen text scaling */\n  .text-xs, small, .small { font-size: 0.75rem !important; }\n  .text-sm { font-size: 0.875rem !important; }\n  .text-base, p, span, div { font-size: 0.875rem !important; }\n  .text-lg { font-size: 1rem !important; }\n  .text-xl { font-size: 1.125rem !important; }\n  .text-2xl { font-size: 1.375rem !important; }\n\n  /* Button text */\n  .btn { font-size: 0.875rem !important; }\n  .btn-sm { font-size: 0.75rem !important; }\n  .btn-lg { font-size: 1rem !important; }\n\n  /* Menu items */\n  .menu-item { font-size: 0.875rem !important; }\n}\n\n/* Ensure all icons scale properly with text */\n@media (max-width: 768px) {\n  .fa, [class*=\"fa-\"], svg {\n    vertical-align: middle !important;\n  }\n\n  /* Scale icons with their parent text size */\n  .text-xs .fa, .text-xs [class*=\"fa-\"], .text-xs svg { font-size: 0.75rem !important; }\n  .text-sm .fa, .text-sm [class*=\"fa-\"], .text-sm svg { font-size: 0.875rem !important; }\n  .text-base .fa, .text-base [class*=\"fa-\"], .text-base svg { font-size: 1rem !important; }\n  .text-lg .fa, .text-lg [class*=\"fa-\"], .text-lg svg { font-size: 1.125rem !important; }\n  .text-xl .fa, .text-xl [class*=\"fa-\"], .text-xl svg { font-size: 1.25rem !important; }\n}\n\n/* Performance Indicators */\n.performance-indicator {\n  display: inline-flex;\n  align-items: center;\n  gap: var(--space-2);\n  padding: var(--space-2) var(--space-3);\n  border-radius: var(--radius-full);\n  font-size: var(--font-size-sm);\n  font-weight: 600;\n}\n\n.performance-excellent {\n  background: var(--success-light);\n  color: #065f46;\n}\n\n.performance-good {\n  background: var(--primary-100);\n  color: var(--primary-800);\n}\n\n.performance-average {\n  background: var(--warning-light);\n  color: #92400e;\n}\n\n.performance-poor {\n  background: var(--danger-light);\n  color: #991b1b;\n}\n\n/* Login/Register Page Styles */\n.auth-container {\n  min-height: 100vh;\n  background: linear-gradient(135deg, var(--primary-50) 0%, var(--primary-100) 100%);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  padding: var(--space-4);\n}\n\n.auth-card {\n  background: rgba(255, 255, 255, 0.95);\n  backdrop-filter: blur(10px);\n  border: 1px solid rgba(255, 255, 255, 0.2);\n  border-radius: var(--radius-2xl);\n  box-shadow: var(--shadow-xl);\n  padding: var(--space-8);\n  width: 100%;\n  max-width: 400px;\n}\n\n.auth-logo {\n  width: 80px;\n  height: 80px;\n  margin: 0 auto var(--space-6);\n  display: block;\n  border-radius: var(--radius-xl);\n}\n\n.auth-title {\n  text-align: center;\n  font-size: var(--font-size-2xl);\n  font-weight: 700;\n  color: var(--gray-900);\n  margin-bottom: var(--space-6);\n}\n\n.auth-subtitle {\n  text-align: center;\n  color: var(--gray-600);\n  margin-bottom: var(--space-8);\n}\n\n/* Dashboard Stats Cards */\n.stats-card {\n  background: var(--white);\n  border-radius: var(--radius-xl);\n  box-shadow: var(--shadow-md);\n  border: 1px solid var(--gray-100);\n  padding: var(--space-6);\n  transition: var(--transition-normal);\n  position: relative;\n  overflow: hidden;\n}\n\n.stats-card::before {\n  content: '';\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  height: 4px;\n  background: linear-gradient(90deg, var(--primary) 0%, var(--primary-light) 100%);\n}\n\n.stats-card:hover {\n  box-shadow: var(--shadow-lg);\n  transform: translateY(-2px);\n}\n\n.stats-value {\n  font-size: var(--font-size-3xl);\n  font-weight: 700;\n  color: var(--primary);\n  margin-bottom: var(--space-2);\n}\n\n.stats-label {\n  color: var(--gray-600);\n  font-weight: 500;\n  text-transform: uppercase;\n  letter-spacing: 0.025em;\n  font-size: var(--font-size-sm);\n}\n\n/* Success/Error States */\n.success-state {\n  color: var(--success);\n  background: var(--success-light);\n  border-color: var(--success);\n}\n\n.error-state {\n  color: var(--danger);\n  background: var(--danger-light);\n  border-color: var(--danger);\n}\n\n.warning-state {\n  color: var(--warning);\n  background: var(--warning-light);\n  border-color: var(--warning);\n}\n\n.info-state {\n  color: var(--info);\n  background: var(--info-light);\n  border-color: var(--info);\n}\n\n/* CSS Variables for Theme */\n:root {\n  /* Primary Blue Theme */\n  --color-primary-50: #eff6ff;\n  --color-primary-100: #dbeafe;\n  --color-primary-200: #bfdbfe;\n  --color-primary-300: #93c5fd;\n  --color-primary-400: #60a5fa;\n  --color-primary-500: #3b82f6;\n  --color-primary-600: #2563eb;\n  --color-primary-700: #1d4ed8;\n  --color-primary-800: #1e40af;\n  --color-primary-900: #1e3a8a;\n  \n  /* Gradients */\n  --gradient-primary: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);\n  --gradient-blue-sky: linear-gradient(135deg, #0ea5e9 0%, #3b82f6 100%);\n  --gradient-blue-ocean: linear-gradient(135deg, #1e40af 0%, #1e3a8a 100%);\n  \n  /* Shadows */\n  --shadow-soft: 0 2px 15px -3px rgba(0, 0, 0, 0.07), 0 10px 20px -2px rgba(0, 0, 0, 0.04);\n  --shadow-medium: 0 4px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);\n  --shadow-large: 0 10px 40px -10px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);\n  --shadow-blue: 0 4px 14px 0 rgba(59, 130, 246, 0.15);\n  --shadow-blue-lg: 0 10px 40px -10px rgba(59, 130, 246, 0.25);\n  \n  /* Border Radius */\n  --radius-sm: 0.375rem;\n  --radius-md: 0.5rem;\n  --radius-lg: 0.75rem;\n  --radius-xl: 1rem;\n  --radius-2xl: 1.5rem;\n  \n  /* Transitions */\n  --transition-fast: 0.15s ease-in-out;\n  --transition-normal: 0.3s ease-in-out;\n  --transition-slow: 0.5s ease-in-out;\n}\n\n/* Dark Mode Variables */\n.dark {\n  --color-primary-50: #172554;\n  --color-primary-100: #1e3a8a;\n  --color-primary-200: #1e40af;\n  --color-primary-300: #1d4ed8;\n  --color-primary-400: #2563eb;\n  --color-primary-500: #3b82f6;\n  --color-primary-600: #60a5fa;\n  --color-primary-700: #93c5fd;\n  --color-primary-800: #bfdbfe;\n  --color-primary-900: #dbeafe;\n}\n\n/* Base Styles */\n* {\n  box-sizing: border-box;\n}\n\nhtml {\n  scroll-behavior: smooth;\n}\n\nbody {\n  font-family: 'Inter', 'Roboto', 'Nunito', system-ui, -apple-system, sans-serif;\n  line-height: 1.6;\n  color: #1f2937;\n  background-color: #ffffff;\n  -webkit-font-smoothing: antialiased;\n  -moz-osx-font-smoothing: grayscale;\n}\n\n/* Modern Component Classes */\n/* Modern Button Styles */\n.btn-modern {\n  display: inline-flex;\n  align-items: center;\n  justify-content: center;\n  padding: var(--space-3) var(--space-6);\n  font-size: var(--font-size-sm);\n  font-weight: 500;\n  border-radius: var(--radius-lg);\n  transition: var(--transition-normal);\n  outline: none;\n  border: none;\n  cursor: pointer;\n}\n\n.btn-modern:focus {\n  box-shadow: 0 0 0 2px var(--primary-500), 0 0 0 4px rgba(59, 130, 246, 0.1);\n}\n\n.btn-primary {\n  background: var(--primary-600);\n  color: var(--white);\n  box-shadow: var(--shadow-md);\n}\n\n.btn-primary:hover {\n  background: var(--primary-700);\n  box-shadow: var(--shadow-lg);\n  transform: translateY(-1px);\n}\n\n.btn-secondary {\n  background: var(--white);\n  color: var(--primary-600);\n  border: 1px solid var(--primary-200);\n  box-shadow: var(--shadow-sm);\n}\n\n.btn-secondary:hover {\n  background: var(--primary-50);\n  box-shadow: var(--shadow-md);\n}\n\n.btn-ghost {\n  background: transparent;\n  color: var(--primary-600);\n}\n\n.btn-ghost:hover {\n  background: var(--primary-50);\n}\n\n/* Modern Card Styles */\n.card-modern {\n  background: var(--white);\n  border-radius: var(--radius-xl);\n  box-shadow: var(--shadow-md);\n  border: 1px solid var(--gray-100);\n  transition: var(--transition-normal);\n}\n\n.card-modern:hover {\n  box-shadow: var(--shadow-lg);\n}\n\n.card-interactive {\n  cursor: pointer;\n}\n\n.card-interactive:hover {\n  box-shadow: var(--shadow-xl);\n  transform: translateY(-2px);\n}\n\n.card-glass {\n  background: rgba(255, 255, 255, 0.8);\n  backdrop-filter: blur(8px);\n  border: 1px solid rgba(255, 255, 255, 0.2);\n  border-radius: var(--radius-xl);\n  box-shadow: var(--shadow-md);\n}\n\n/* Modern Input Styles */\n.input-modern {\n  width: 100%;\n  padding: var(--space-3) var(--space-4);\n  color: var(--gray-900);\n  background: var(--white);\n  border: 1px solid var(--gray-200);\n  border-radius: var(--radius-lg);\n  transition: var(--transition-normal);\n}\n\n.input-modern:focus {\n  outline: none;\n  border-color: var(--primary-500);\n  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);\n}\n\n.input-modern::placeholder {\n  color: var(--gray-400);\n}\n\n/* Modern Navigation */\n.nav-modern {\n  background: rgba(255, 255, 255, 0.95);\n  backdrop-filter: blur(12px);\n  border-bottom: 1px solid var(--gray-100);\n  position: sticky;\n  top: 0;\n  z-index: 50;\n}\n\n  /* Enhanced Header Styles */\n  .hub-button {\n    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\n    position: relative;\n    overflow: hidden;\n  }\n\n  .hub-button:hover {\n    transform: translateY(-2px) scale(1.05);\n    box-shadow: 0 20px 40px -10px rgba(59, 130, 246, 0.5);\n  }\n\n  .hub-button:active {\n    transform: translateY(-1px) scale(1.02);\n  }\n\n  .brainwave-heading {\n    font-family: 'Inter', 'Roboto', 'Nunito', system-ui, -apple-system, sans-serif;\n    letter-spacing: -0.05em;\n    line-height: 0.9;\n    font-weight: 900;\n    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\n  }\n\n  .user-avatar {\n    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\n    background: transparent !important;\n    position: relative;\n  }\n\n  .user-avatar:hover {\n    transform: scale(1.1) translateY(-2px);\n    box-shadow: 0 15px 35px -5px rgba(59, 130, 246, 0.4), 0 5px 15px -5px rgba(0, 0, 0, 0.1);\n  }\n\n  /* Enhanced Profile Styles */\n  .user-info-desktop,\n  .user-info-tablet {\n    transition: all 0.2s ease;\n  }\n\n  .user-info-desktop:hover,\n  .user-info-tablet:hover {\n    transform: translateY(-1px);\n  }\n\n  /* Ensure user profile container has no background */\n  .user-profile-container {\n    background: transparent !important;\n    position: relative;\n    z-index: auto;\n  }\n\n  /* Ensure user info sections are properly styled */\n  .user-info-desktop,\n  .user-info-tablet,\n  .user-info-mobile {\n    background: transparent !important;\n    position: relative;\n    z-index: auto;\n  }\n\n  /* Remove any potential debugging overlays or blue boxes */\n  .nav-modern *::before,\n  .nav-modern *::after {\n    background: transparent !important;\n    border: none !important;\n  }\n\n  /* Ensure header elements don't have unwanted backgrounds */\n  .nav-modern .flex {\n    background: transparent !important;\n  }\n\n  /* Remove any potential browser debugging styles */\n  .nav-modern [style*=\"background\"] {\n    background: transparent !important;\n  }\n\n  /* Extra small screens (xs) - Custom breakpoint */\n  @media (min-width: 375px) {\n    .xs\\:px-3 { padding-left: 0.75rem; padding-right: 0.75rem; }\n    .xs\\:px-4 { padding-left: 1rem; padding-right: 1rem; }\n    .xs\\:py-2 { padding-top: 0.5rem; padding-bottom: 0.5rem; }\n    .xs\\:h-14 { height: 3.5rem; }\n    .xs\\:h-9 { height: 2.25rem; }\n    .xs\\:w-9 { width: 2.25rem; }\n    .xs\\:text-sm { font-size: 0.875rem; line-height: 1.25rem; }\n    .xs\\:text-base { font-size: 1rem; line-height: 1.5rem; }\n    .xs\\:text-xl { font-size: 1.25rem; line-height: 1.75rem; }\n    .xs\\:space-x-2 > :not([hidden]) ~ :not([hidden]) { margin-left: 0.5rem; }\n    .xs\\:max-w-20 { max-width: 5rem; }\n    .xs\\:inline { display: inline; }\n  }\n\n  /* Mobile-first responsive header adjustments */\n  @media (max-width: 374px) {\n    .nav-modern .hub-button {\n      padding: 0.375rem 0.5rem;\n      font-size: 0.75rem;\n    }\n\n    .nav-modern .brainwave-heading {\n      font-size: 1rem;\n      max-width: 8rem;\n    }\n\n    .nav-modern .user-avatar {\n      width: 1.75rem;\n      height: 1.75rem;\n    }\n\n    .nav-modern .user-info-mobile {\n      font-size: 0.625rem;\n    }\n  }\n\n  /* Tablet landscape optimizations */\n  @media (min-width: 768px) and (max-width: 1024px) and (orientation: landscape) {\n    .nav-modern {\n      height: auto;\n      min-height: 3.5rem;\n    }\n\n    .nav-modern .brainwave-heading {\n      font-size: 1.5rem;\n    }\n  }\n\n  /* Large screen optimizations */\n  @media (min-width: 1440px) {\n    .nav-modern .brainwave-heading {\n      font-size: 2.5rem;\n    }\n\n    .nav-modern .user-avatar {\n      width: 3.5rem;\n      height: 3.5rem;\n    }\n  }\n\n  /* Custom utility classes for enhanced styling */\n  .border-3 {\n    border-width: 3px;\n  }\n\n  .w-13 {\n    width: 3.25rem;\n  }\n\n  .h-13 {\n    height: 3.25rem;\n  }\n\n  .w-14 {\n    width: 3.5rem;\n  }\n\n  .h-14 {\n    height: 3.5rem;\n  }\n\n  /* Glassmorphism effects */\n  .glass-effect {\n    background: rgba(255, 255, 255, 0.25);\n    backdrop-filter: blur(10px);\n    border: 1px solid rgba(255, 255, 255, 0.18);\n  }\n\n  /* Enhanced gradient text */\n  .gradient-text-enhanced {\n    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n    -webkit-background-clip: text;\n    -webkit-text-fill-color: transparent;\n    background-clip: text;\n  }\n  \n  .nav-item {\n    padding: var(--space-2) var(--space-4);\n    color: var(--gray-600);\n    border-radius: var(--radius-lg);\n    transition: var(--transition-normal);\n    font-weight: 500;\n    text-decoration: none;\n    display: block;\n  }\n\n  .nav-item:hover {\n    color: var(--primary-600);\n    background: var(--primary-50);\n  }\n\n  .nav-item-active {\n    color: var(--primary-600);\n    background: var(--primary-50);\n  }\n\n  /* Modern Sidebar */\n  .sidebar-modern {\n    background: linear-gradient(180deg, var(--primary-600) 0%, var(--primary-800) 100%);\n    color: var(--white);\n    box-shadow: var(--shadow-xl);\n  }\n\n  .sidebar-item {\n    display: flex;\n    align-items: center;\n    padding: var(--space-3) var(--space-4);\n    color: rgba(255, 255, 255, 0.8);\n    border-radius: var(--radius-lg);\n    transition: var(--transition-normal);\n    cursor: pointer;\n    text-decoration: none;\n  }\n\n  .sidebar-item:hover {\n    color: var(--white);\n    background: rgba(255, 255, 255, 0.1);\n  }\n\n  .sidebar-item-active {\n    color: var(--white);\n    background: rgba(255, 255, 255, 0.2);\n    box-shadow: var(--shadow-sm);\n  }\n  \n  /* Modern Layout */\n  .layout-modern {\n    min-height: 100vh;\n    background: linear-gradient(135deg, var(--gray-50) 0%, var(--primary-50) 100%);\n  }\n\n  .container-modern {\n    max-width: 80rem;\n    margin-left: auto;\n    margin-right: auto;\n    padding-left: 1rem;\n    padding-right: 1rem;\n  }\n\n  @media (min-width: 640px) {\n    .container-modern {\n      padding-left: 1.5rem;\n      padding-right: 1.5rem;\n    }\n  }\n\n  @media (min-width: 1024px) {\n    .container-modern {\n      padding-left: 2rem;\n      padding-right: 2rem;\n    }\n  }\n  \n  /* Modern Typography */\n  .heading-1 {\n    font-size: 2.25rem;\n    font-weight: 700;\n    color: var(--gray-900);\n    line-height: 1.25;\n  }\n\n  @media (min-width: 768px) {\n    .heading-1 {\n      font-size: 3rem;\n    }\n  }\n\n  @media (min-width: 1024px) {\n    .heading-1 {\n      font-size: 3.75rem;\n    }\n  }\n\n  .heading-2 {\n    font-size: 1.875rem;\n    font-weight: 700;\n    color: var(--gray-900);\n    line-height: 1.25;\n  }\n\n  @media (min-width: 768px) {\n    .heading-2 {\n      font-size: 2.25rem;\n    }\n  }\n\n  .heading-3 {\n    font-size: 1.5rem;\n    font-weight: 600;\n    color: var(--gray-900);\n    line-height: 1.25;\n  }\n\n  @media (min-width: 768px) {\n    .heading-3 {\n      font-size: 1.875rem;\n    }\n  }\n\n  .text-gradient {\n    background: linear-gradient(90deg, var(--primary-600) 0%, #2563eb 100%);\n    -webkit-background-clip: text;\n    -webkit-text-fill-color: transparent;\n    background-clip: text;\n  }\n  \n  /* Modern Animations */\n  .animate-fade-in-up {\n    animation: fadeInUp 0.6s ease-out forwards;\n  }\n  \n  .animate-slide-in-right {\n    animation: slideInRight 0.5s ease-out forwards;\n  }\n  \n  .animate-float {\n    animation: float 3s ease-in-out infinite;\n  }\n  \n  /* Modern Loading States */\n  .skeleton {\n    animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;\n    background: var(--gray-200);\n    border-radius: var(--radius-md);\n  }\n\n  .loading-spinner {\n    animation: spin 1s linear infinite;\n    border-radius: 50%;\n    border: 2px solid var(--gray-200);\n    border-top-color: var(--primary-600);\n  }\n\n  /* Modern Quiz Styles */\n  .quiz-card {\n    background: var(--white);\n    border-radius: var(--radius-xl);\n    box-shadow: var(--shadow-md);\n    border: 1px solid var(--gray-100);\n    padding: var(--space-6);\n    transition: var(--transition-normal);\n  }\n\n  .quiz-card:hover {\n    box-shadow: var(--shadow-blue);\n    transform: scale(1.02);\n  }\n\n  .quiz-option {\n    padding: var(--space-4);\n    border: 2px solid var(--gray-200);\n    border-radius: var(--radius-lg);\n    cursor: pointer;\n    transition: var(--transition-normal);\n  }\n\n  .quiz-option:hover {\n    border-color: var(--primary-300);\n    background: var(--primary-50);\n  }\n\n  .quiz-option-selected {\n    border-color: var(--primary-500);\n    background: var(--primary-50);\n    color: var(--primary-700);\n  }\n\n  .quiz-option-correct {\n    border-color: var(--success);\n    background: var(--success-light);\n    color: #065f46;\n  }\n\n  .quiz-option-incorrect {\n    border-color: var(--danger);\n    background: var(--danger-light);\n    color: #991b1b;\n  }\n\n  /* Modern Progress Bar */\n  .progress-bar {\n    width: 100%;\n    background: var(--gray-200);\n    border-radius: var(--radius-full);\n    height: 8px;\n    overflow: hidden;\n  }\n\n  .progress-fill {\n    height: 100%;\n    background: linear-gradient(90deg, var(--primary-500) 0%, #2563eb 100%);\n    border-radius: var(--radius-full);\n    transition: all 0.5s ease-out;\n  }\n\n  /* Modern Badge */\n  .badge-modern {\n    display: inline-flex;\n    align-items: center;\n    padding: var(--space-1) var(--space-3);\n    border-radius: var(--radius-full);\n    font-size: var(--font-size-xs);\n    font-weight: 500;\n  }\n\n  .badge-primary {\n    background: var(--primary-100);\n    color: var(--primary-800);\n  }\n\n  .badge-success {\n    background: var(--success-light);\n    color: #065f46;\n  }\n\n  .badge-warning {\n    background: var(--warning-light);\n    color: #92400e;\n  }\n\n  .badge-error {\n    background: var(--danger-light);\n    color: #991b1b;\n  }\n\n/* Responsive Design Utilities */\n.text-responsive {\n  font-size: var(--font-size-sm);\n}\n\n@media (min-width: 640px) {\n  .text-responsive {\n    font-size: var(--font-size-base);\n  }\n}\n\n@media (min-width: 768px) {\n  .text-responsive {\n    font-size: var(--font-size-lg);\n  }\n}\n\n.padding-responsive {\n  padding: var(--space-4);\n}\n\n@media (min-width: 640px) {\n  .padding-responsive {\n    padding: var(--space-6);\n  }\n}\n\n@media (min-width: 768px) {\n  .padding-responsive {\n    padding: var(--space-8);\n  }\n}\n\n.margin-responsive {\n  margin: var(--space-4);\n}\n\n@media (min-width: 640px) {\n  .margin-responsive {\n    margin: var(--space-6);\n  }\n}\n\n@media (min-width: 768px) {\n  .margin-responsive {\n    margin: var(--space-8);\n  }\n}\n\n.grid-responsive {\n  display: grid;\n  grid-template-columns: 1fr;\n  gap: var(--space-4);\n}\n\n@media (min-width: 640px) {\n  .grid-responsive {\n    grid-template-columns: repeat(2, 1fr);\n    gap: var(--space-6);\n  }\n}\n\n@media (min-width: 1024px) {\n  .grid-responsive {\n    grid-template-columns: repeat(3, 1fr);\n  }\n}\n\n@media (min-width: 1280px) {\n  .grid-responsive {\n    grid-template-columns: repeat(4, 1fr);\n  }\n}\n\n/* Custom Scrollbar */\n::-webkit-scrollbar {\n  width: 6px;\n}\n\n::-webkit-scrollbar-track {\n  background: #f1f5f9;\n}\n\n::-webkit-scrollbar-thumb {\n  background: #cbd5e1;\n  border-radius: 3px;\n}\n\n::-webkit-scrollbar-thumb:hover {\n  background: #94a3b8;\n}\n\n/* Print Styles */\n@media print {\n  .no-print {\n    display: none !important;\n  }\n}\n\n/* ===== MODERN UTILITY CLASSES ===== */\n\n/* Glassmorphism */\n.glass {\n  background: var(--glass-bg);\n  backdrop-filter: var(--glass-blur);\n  -webkit-backdrop-filter: var(--glass-blur);\n  border: 1px solid var(--glass-border);\n}\n\n/* Modern Cards */\n.card-modern {\n  background: linear-gradient(145deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 250, 252, 0.9) 100%);\n  border-radius: var(--radius-xl);\n  box-shadow: var(--shadow-lg);\n  border: 1px solid var(--glass-border);\n  backdrop-filter: var(--glass-blur);\n  transition: var(--transition-normal);\n}\n\n.card-modern:hover {\n  transform: translateY(-4px) scale(1.02);\n  box-shadow: var(--shadow-2xl);\n}\n\n/* Gradient Backgrounds */\n.bg-gradient-primary {\n  background: linear-gradient(135deg, var(--primary) 0%, var(--primary-dark) 100%);\n}\n\n.bg-gradient-blue {\n  background: linear-gradient(135deg, #007BFF 0%, #0056D2 50%, #4338CA 100%);\n}\n\n.bg-gradient-page {\n  background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 25%, #f8fafc 50%, #ffffff 100%);\n}\n\n/* Text Gradients */\n.text-gradient-primary {\n  background: linear-gradient(135deg, var(--primary) 0%, var(--primary-dark) 100%);\n  -webkit-background-clip: text;\n  -webkit-text-fill-color: transparent;\n  background-clip: text;\n}\n\n/* Animations */\n.animate-fade-in {\n  animation: fadeIn 0.6s ease-out;\n}\n\n.animate-slide-up {\n  animation: slideUp 0.6s ease-out;\n}\n\n.animate-bounce-gentle {\n  animation: bounceGentle 2s ease-in-out infinite;\n}\n\n@keyframes fadeIn {\n  from { opacity: 0; }\n  to { opacity: 1; }\n}\n\n@keyframes slideUp {\n  from {\n    opacity: 0;\n    transform: translateY(30px);\n  }\n  to {\n    opacity: 1;\n    transform: translateY(0);\n  }\n}\n\n@keyframes bounceGentle {\n  0%, 100% { transform: translateY(0); }\n  50% { transform: translateY(-5px); }\n}\n\n/* ===== END OF BRAINWAVE MODERN DESIGN SYSTEM ===== */\n", "/* Global CSS Animations for Modern UI */\n\n/* Keyframe Animations */\n@keyframes fadeInUp {\n    from {\n        opacity: 0;\n        transform: translateY(30px);\n    }\n    to {\n        opacity: 1;\n        transform: translateY(0);\n    }\n}\n\n@keyframes slideInLeft {\n    from {\n        opacity: 0;\n        transform: translateX(-30px);\n    }\n    to {\n        opacity: 1;\n        transform: translateX(0);\n    }\n}\n\n@keyframes slideInRight {\n    from {\n        opacity: 0;\n        transform: translateX(30px);\n    }\n    to {\n        opacity: 1;\n        transform: translateX(0);\n    }\n}\n\n@keyframes fadeIn {\n    from {\n        opacity: 0;\n    }\n    to {\n        opacity: 1;\n    }\n}\n\n@keyframes scaleIn {\n    from {\n        opacity: 0;\n        transform: scale(0.9);\n    }\n    to {\n        opacity: 1;\n        transform: scale(1);\n    }\n}\n\n@keyframes bounce {\n    0%, 20%, 53%, 80%, 100% {\n        transform: translate3d(0, 0, 0);\n    }\n    40%, 43% {\n        transform: translate3d(0, -8px, 0);\n    }\n    70% {\n        transform: translate3d(0, -4px, 0);\n    }\n    90% {\n        transform: translate3d(0, -2px, 0);\n    }\n}\n\n@keyframes pulse {\n    0%, 100% {\n        transform: scale(1);\n        opacity: 1;\n    }\n    50% {\n        transform: scale(1.05);\n        opacity: 0.8;\n    }\n}\n\n@keyframes shimmer {\n    0% {\n        background-position: -200px 0;\n    }\n    100% {\n        background-position: calc(200px + 100%) 0;\n    }\n}\n\n@keyframes spin {\n    from {\n        transform: rotate(0deg);\n    }\n    to {\n        transform: rotate(360deg);\n    }\n}\n\n@keyframes wiggle {\n    0%, 7% {\n        transform: rotateZ(0);\n    }\n    15% {\n        transform: rotateZ(-15deg);\n    }\n    20% {\n        transform: rotateZ(10deg);\n    }\n    25% {\n        transform: rotateZ(-10deg);\n    }\n    30% {\n        transform: rotateZ(6deg);\n    }\n    35% {\n        transform: rotateZ(-4deg);\n    }\n    40%, 100% {\n        transform: rotateZ(0);\n    }\n}\n\n@keyframes float {\n    0%, 100% {\n        transform: translateY(0px);\n    }\n    50% {\n        transform: translateY(-10px);\n    }\n}\n\n/* Animation Classes */\n.animate-fadeInUp {\n    animation: fadeInUp 0.6s ease-out;\n}\n\n.animate-slideInLeft {\n    animation: slideInLeft 0.6s ease-out;\n}\n\n.animate-slideInRight {\n    animation: slideInRight 0.6s ease-out;\n}\n\n.animate-fadeIn {\n    animation: fadeIn 0.5s ease-out;\n}\n\n.animate-scaleIn {\n    animation: scaleIn 0.5s ease-out;\n}\n\n.animate-bounce {\n    animation: bounce 1s;\n}\n\n.animate-pulse {\n    animation: pulse 2s infinite;\n}\n\n.animate-spin {\n    animation: spin 1s linear infinite;\n}\n\n.animate-wiggle {\n    animation: wiggle 1s ease-in-out;\n}\n\n.animate-float {\n    animation: float 3s ease-in-out infinite;\n}\n\n/* Staggered Animations */\n.animate-stagger-1 { animation-delay: 0.1s; }\n.animate-stagger-2 { animation-delay: 0.2s; }\n.animate-stagger-3 { animation-delay: 0.3s; }\n.animate-stagger-4 { animation-delay: 0.4s; }\n.animate-stagger-5 { animation-delay: 0.5s; }\n.animate-stagger-6 { animation-delay: 0.6s; }\n.animate-stagger-7 { animation-delay: 0.7s; }\n.animate-stagger-8 { animation-delay: 0.8s; }\n\n/* Hover Effects */\n.hover-lift {\n    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\n}\n\n.hover-lift:hover {\n    transform: translateY(-4px);\n    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);\n}\n\n.hover-scale {\n    transition: transform 0.2s ease;\n}\n\n.hover-scale:hover {\n    transform: scale(1.05);\n}\n\n.hover-glow {\n    transition: all 0.3s ease;\n}\n\n.hover-glow:hover {\n    box-shadow: 0 0 20px rgba(59, 130, 246, 0.4);\n}\n\n/* Interactive Elements */\n.interactive {\n    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);\n    cursor: pointer;\n}\n\n.interactive:hover {\n    transform: translateY(-2px);\n}\n\n.interactive:active {\n    transform: translateY(0);\n    transition: transform 0.1s;\n}\n\n/* Loading States */\n.loading-shimmer {\n    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);\n    background-size: 200% 100%;\n    animation: shimmer 1.5s infinite;\n}\n\n.loading-pulse {\n    animation: pulse 1.5s ease-in-out infinite;\n}\n\n/* Modern Card Effects */\n.modern-card {\n    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\n    position: relative;\n    overflow: hidden;\n}\n\n.modern-card::before {\n    content: '';\n    position: absolute;\n    top: 0;\n    left: -100%;\n    width: 100%;\n    height: 100%;\n    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);\n    transition: left 0.6s;\n}\n\n.modern-card:hover::before {\n    left: 100%;\n}\n\n.modern-card:hover {\n    transform: translateY(-4px) scale(1.02);\n    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.15);\n}\n\n/* Glassmorphism Effects */\n.glass {\n    background: rgba(255, 255, 255, 0.1);\n    backdrop-filter: blur(10px);\n    border: 1px solid rgba(255, 255, 255, 0.2);\n}\n\n.glass-dark {\n    background: rgba(0, 0, 0, 0.1);\n    backdrop-filter: blur(10px);\n    border: 1px solid rgba(255, 255, 255, 0.1);\n}\n\n/* Gradient Text */\n.gradient-text {\n    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n    -webkit-background-clip: text;\n    -webkit-text-fill-color: transparent;\n    background-clip: text;\n}\n\n.gradient-text-blue {\n    background: linear-gradient(135deg, #007BFF 0%, #0056D2 100%);\n    -webkit-background-clip: text;\n    -webkit-text-fill-color: transparent;\n    background-clip: text;\n}\n\n/* Responsive Animations */\n@media (prefers-reduced-motion: reduce) {\n    .animate-fadeInUp,\n    .animate-slideInLeft,\n    .animate-slideInRight,\n    .animate-fadeIn,\n    .animate-scaleIn,\n    .animate-bounce,\n    .animate-pulse,\n    .animate-spin,\n    .animate-wiggle,\n    .animate-float,\n    .hover-lift,\n    .hover-scale,\n    .hover-glow,\n    .interactive,\n    .modern-card {\n        animation: none;\n        transition: none;\n    }\n    \n    .modern-card:hover {\n        transform: none;\n    }\n}\n\n/* Mobile Optimizations */\n@media (max-width: 640px) {\n    .hover-lift:hover {\n        transform: translateY(-2px);\n    }\n    \n    .modern-card:hover {\n        transform: translateY(-2px) scale(1.01);\n    }\n    \n    .interactive:hover {\n        transform: translateY(-1px);\n    }\n}\n", ".button-disabled {\r\n  opacity: 0.5;\r\n  pointer-events: none; /* This will disable clicking on the buttons */\r\n}\r\n\r\n/* ===== MODERN NAVIGATION ===== */\r\n.nav-modern {\r\n  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);\r\n  backdrop-filter: blur(20px);\r\n  -webkit-backdrop-filter: blur(20px);\r\n  min-height: 3rem; /* Reduced from 3.5rem to 3rem for more compact header */\r\n  position: relative;\r\n  overflow: hidden;\r\n}\r\n\r\n.nav-modern::before {\r\n  content: '';\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);\r\n  pointer-events: none;\r\n  z-index: 0;\r\n}\r\n\r\n.nav-modern > * {\r\n  position: relative;\r\n  z-index: 1;\r\n}\r\n\r\n.nav-modern:hover {\r\n  box-shadow: 0 8px 32px rgba(0, 123, 255, 0.15);\r\n  transform: translateY(-1px);\r\n}\r\n\r\n/* ===== BACK BUTTON ===== */\r\n.back-button {\r\n  transition: all 0.2s ease;\r\n  box-shadow: 0 2px 8px rgba(75, 85, 99, 0.3);\r\n}\r\n\r\n.back-button:hover {\r\n  box-shadow: 0 4px 16px rgba(75, 85, 99, 0.4);\r\n}\r\n\r\n.back-button:active {\r\n  transform: scale(0.95);\r\n}\r\n\r\n/* ===== BRAINWAVE HEADING ===== */\r\n.brainwave-heading {\r\n  background: linear-gradient(135deg, #007BFF 0%, #0056D2 100%);\r\n  -webkit-background-clip: text;\r\n  -webkit-text-fill-color: transparent;\r\n  background-clip: text;\r\n  font-weight: 800;\r\n  letter-spacing: -0.02em;\r\n  text-shadow: 0 2px 4px rgba(0, 123, 255, 0.1);\r\n}\r\n\r\n/* ===== ENHANCED BRAINWAVE HEADING ===== */\r\n.brainwave-heading-enhanced {\r\n  font-weight: 900;\r\n  letter-spacing: -0.03em;\r\n  text-shadow: 0 4px 8px rgba(0, 123, 255, 0.15);\r\n  position: relative;\r\n  display: inline-block;\r\n}\r\n\r\n/* ===== AMAZING BRAINWAVE ANIMATIONS ===== */\r\n\r\n/* Simple test animation to make sure CSS is working */\r\n@keyframes simpleGlow {\r\n  0% {\r\n    text-shadow: 0 0 5px rgba(59, 130, 246, 0.5);\r\n  }\r\n  50% {\r\n    text-shadow: 0 0 20px rgba(59, 130, 246, 0.8), 0 0 30px rgba(16, 185, 129, 0.6);\r\n  }\r\n  100% {\r\n    text-shadow: 0 0 5px rgba(59, 130, 246, 0.5);\r\n  }\r\n}\r\n\r\n/* ===== FORCE BLUE LOGOUT BUTTON ===== */\r\n.force-blue-logout {\r\n  background-color: #2563eb !important;\r\n  background: #2563eb !important;\r\n  background-image: none !important;\r\n  color: #ffffff !important;\r\n  border: 2px solid #1d4ed8 !important;\r\n  padding: 8px 16px !important;\r\n  border-radius: 8px !important;\r\n  font-weight: 600 !important;\r\n  cursor: pointer !important;\r\n  display: flex !important;\r\n  align-items: center !important;\r\n  gap: 6px !important;\r\n  min-width: 80px !important;\r\n  outline: none !important;\r\n  box-shadow: 0 2px 8px rgba(37, 99, 235, 0.3) !important;\r\n}\r\n\r\n.force-blue-logout:hover {\r\n  background-color: #1d4ed8 !important;\r\n  background: #1d4ed8 !important;\r\n}\r\n\r\n.force-blue-logout * {\r\n  color: #ffffff !important;\r\n  background: transparent !important;\r\n}\r\n\r\n/* Brain pulse animation - electric energy effect */\r\n@keyframes brainPulse {\r\n  0% {\r\n    background-position: 0% 50%;\r\n    filter: brightness(1) contrast(1);\r\n  }\r\n  25% {\r\n    background-position: 50% 50%;\r\n    filter: brightness(1.2) contrast(1.1);\r\n  }\r\n  50% {\r\n    background-position: 100% 50%;\r\n    filter: brightness(1.3) contrast(1.2);\r\n  }\r\n  75% {\r\n    background-position: 50% 50%;\r\n    filter: brightness(1.1) contrast(1.05);\r\n  }\r\n  100% {\r\n    background-position: 0% 50%;\r\n    filter: brightness(1) contrast(1);\r\n  }\r\n}\r\n\r\n/* Visible test class */\r\n.brainwave-test {\r\n  animation: simpleGlow 2s ease-in-out infinite !important;\r\n  color: #3b82f6 !important;\r\n  font-weight: 900 !important;\r\n}\r\n\r\n/* Wave flow animation - flowing water effect */\r\n@keyframes waveFlow {\r\n  0% {\r\n    background-position: 0% 50%;\r\n    transform: translateY(0px);\r\n  }\r\n  25% {\r\n    background-position: 25% 25%;\r\n    transform: translateY(-1px);\r\n  }\r\n  50% {\r\n    background-position: 100% 50%;\r\n    transform: translateY(0px);\r\n  }\r\n  75% {\r\n    background-position: 75% 75%;\r\n    transform: translateY(1px);\r\n  }\r\n  100% {\r\n    background-position: 0% 50%;\r\n    transform: translateY(0px);\r\n  }\r\n}\r\n\r\n/* Glowing underline pulse */\r\n@keyframes glowPulse {\r\n  0% {\r\n    box-shadow: 0 0 5px rgba(16, 185, 129, 0.3);\r\n    opacity: 0.7;\r\n  }\r\n  50% {\r\n    box-shadow: 0 0 20px rgba(16, 185, 129, 0.8), 0 0 30px rgba(59, 130, 246, 0.4);\r\n    opacity: 1;\r\n  }\r\n  100% {\r\n    box-shadow: 0 0 5px rgba(16, 185, 129, 0.3);\r\n    opacity: 0.7;\r\n  }\r\n}\r\n\r\n/* Hover effects for the entire brainwave container */\r\n.brainwave-container:hover .brain-text {\r\n  animation-duration: 1.5s;\r\n}\r\n\r\n.brainwave-container:hover .wave-text {\r\n  animation-duration: 1.8s;\r\n}\r\n\r\n/* Responsive animations */\r\n@media (prefers-reduced-motion: reduce) {\r\n  @keyframes brainPulse {\r\n    0%, 100% {\r\n      background-position: 0% 50%;\r\n    }\r\n  }\r\n\r\n  @keyframes waveFlow {\r\n    0%, 100% {\r\n      background-position: 0% 50%;\r\n    }\r\n  }\r\n\r\n  @keyframes glowPulse {\r\n    0%, 100% {\r\n      opacity: 0.8;\r\n    }\r\n  }\r\n}\r\n\r\n.brain-text {\r\n  background: linear-gradient(135deg, #007BFF 0%, #0056D2 50%, #4338CA 100%);\r\n  -webkit-background-clip: text;\r\n  -webkit-text-fill-color: transparent;\r\n  background-clip: text;\r\n  font-weight: 900;\r\n}\r\n\r\n.wave-text {\r\n  background: linear-gradient(135deg, #059669 0%, #10b981 50%, #34d399 100%);\r\n  -webkit-background-clip: text;\r\n  -webkit-text-fill-color: transparent;\r\n  background-clip: text;\r\n  font-weight: 900;\r\n  margin-left: -0.05em;\r\n}\r\n\r\n/* Fix for quiz pages - white text on blue background */\r\n.quiz-header .brain-text,\r\n.quiz-header .wave-text {\r\n  background: none !important;\r\n  -webkit-background-clip: unset !important;\r\n  -webkit-text-fill-color: white !important;\r\n  background-clip: unset !important;\r\n  color: white !important;\r\n  text-shadow: 0 0 10px rgba(255, 255, 255, 0.3) !important;\r\n}\r\n\r\n/* Ensure quiz header text is always white */\r\n.quiz-header h1,\r\n.quiz-header .brainwave-container h1,\r\n.quiz-header .brainwave-container span {\r\n  color: white !important;\r\n  -webkit-text-fill-color: white !important;\r\n}\r\n\r\n.hub-title-gradient {\r\n  background: linear-gradient(135deg, #007BFF 0%, #0056D2 30%, #4338CA 60%, #7C3AED 100%);\r\n  -webkit-background-clip: text;\r\n  -webkit-text-fill-color: transparent;\r\n  background-clip: text;\r\n  font-weight: 900;\r\n  animation: shimmer 3s ease-in-out infinite;\r\n}\r\n\r\n@keyframes shimmer {\r\n  0%, 100% {\r\n    background-position: 0% 50%;\r\n  }\r\n  50% {\r\n    background-position: 100% 50%;\r\n  }\r\n}\r\n\r\n.hub-title-gradient {\r\n  background-size: 200% 200%;\r\n}\r\n\r\n/* ===== USER PROFILE SECTION ===== */\r\n.user-profile-container {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  position: relative;\r\n}\r\n\r\n.user-avatar {\r\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\r\n  box-shadow: 0 4px 12px rgba(0, 123, 255, 0.25);\r\n  position: relative;\r\n  overflow: hidden;\r\n}\r\n\r\n.user-avatar:hover {\r\n  transform: scale(1.1) translateY(-2px);\r\n  box-shadow: 0 8px 25px rgba(0, 123, 255, 0.4);\r\n}\r\n\r\n.user-avatar::before {\r\n  content: '';\r\n  position: absolute;\r\n  inset: -2px;\r\n  background: linear-gradient(45deg, #007BFF, #4338CA, #7C3AED, #A855F7);\r\n  border-radius: 50%;\r\n  opacity: 0;\r\n  transition: opacity 0.3s ease;\r\n  z-index: -1;\r\n}\r\n\r\n.user-avatar:hover::before {\r\n  opacity: 0.3;\r\n  animation: rotate 2s linear infinite;\r\n}\r\n\r\n@keyframes rotate {\r\n  from {\r\n    transform: rotate(0deg);\r\n  }\r\n  to {\r\n    transform: rotate(360deg);\r\n  }\r\n}\r\n\r\n/* ===== RESPONSIVE HEADER ===== */\r\n@media (max-width: 480px) {\r\n  .nav-modern {\r\n    padding: 0 0.5rem; /* Reduced from 0.75rem to 0.5rem for more compact mobile header */\r\n  }\r\n\r\n  .nav-modern .back-button {\r\n    padding: 0.5rem;\r\n    min-width: 2.5rem;\r\n  }\r\n\r\n  .nav-modern .back-button span {\r\n    display: none;\r\n  }\r\n\r\n  .brainwave-heading-enhanced {\r\n    font-size: 1.125rem;\r\n    font-weight: 800;\r\n  }\r\n\r\n  .user-avatar {\r\n    width: 2rem;\r\n    height: 2rem;\r\n  }\r\n}\r\n\r\n@media (min-width: 481px) and (max-width: 640px) {\r\n  .nav-modern .back-button span {\r\n    display: none;\r\n  }\r\n\r\n  .brainwave-heading-enhanced {\r\n    font-size: 1.25rem;\r\n  }\r\n\r\n  .user-avatar {\r\n    width: 2.25rem;\r\n    height: 2.25rem;\r\n  }\r\n}\r\n\r\n@media (min-width: 641px) and (max-width: 768px) {\r\n  .brainwave-heading-enhanced {\r\n    font-size: 1.5rem;\r\n  }\r\n\r\n  .user-avatar {\r\n    width: 2.5rem;\r\n    height: 2.5rem;\r\n  }\r\n}\r\n\r\n@media (min-width: 769px) {\r\n  .brainwave-heading-enhanced {\r\n    font-size: 2rem;\r\n  }\r\n\r\n  .user-avatar {\r\n    width: 3rem;\r\n    height: 3rem;\r\n  }\r\n}\r\n\r\n@media (min-width: 641px) and (max-width: 768px) {\r\n  .brainwave-heading {\r\n    font-size: 1.5rem;\r\n  }\r\n}\r\n\r\n@media (min-width: 769px) {\r\n  .brainwave-heading {\r\n    font-size: 1.875rem;\r\n  }\r\n}\r\n\r\n/* ===== SAFE CSS ANIMATIONS TO REPLACE FRAMER MOTION ===== */\r\n\r\n/* Brain text glow animation */\r\n@keyframes brainGlow {\r\n  0%, 100% {\r\n    text-shadow: 0 0 10px rgba(59, 130, 246, 0.5);\r\n    transform: scale(1);\r\n  }\r\n  50% {\r\n    text-shadow: 0 0 20px rgba(59, 130, 246, 0.8);\r\n    transform: scale(1.02);\r\n  }\r\n}\r\n\r\n.brain-text {\r\n  animation: brainGlow 3s ease-in-out infinite;\r\n  transition: transform 0.3s ease;\r\n}\r\n\r\n.brain-text:hover {\r\n  transform: scale(1.05) !important;\r\n  animation-duration: 1.5s;\r\n}\r\n\r\n/* Electric spark animation */\r\n@keyframes sparkPulse {\r\n  0%, 100% {\r\n    opacity: 0.3;\r\n    transform: scale(0.8);\r\n    box-shadow: 0 0 8px #3b82f6;\r\n  }\r\n  50% {\r\n    opacity: 1;\r\n    transform: scale(1.2);\r\n    box-shadow: 0 0 15px #3b82f6;\r\n  }\r\n}\r\n\r\n.electric-spark {\r\n  animation: sparkPulse 2s ease-in-out infinite;\r\n}\r\n\r\n/* Wave text flow animation */\r\n@keyframes waveFlow {\r\n  0%, 100% {\r\n    text-shadow: 0 0 10px rgba(16, 185, 129, 0.5);\r\n    transform: translateY(0) scale(1);\r\n  }\r\n  25% {\r\n    transform: translateY(-2px) scale(1.01);\r\n  }\r\n  50% {\r\n    text-shadow: 0 0 20px rgba(16, 185, 129, 0.8);\r\n    transform: translateY(0) scale(1.02);\r\n  }\r\n  75% {\r\n    transform: translateY(2px) scale(1.01);\r\n  }\r\n}\r\n\r\n.wave-text {\r\n  animation: waveFlow 3s ease-in-out infinite;\r\n  transition: transform 0.3s ease;\r\n}\r\n\r\n.wave-text:hover {\r\n  transform: scale(1.05) !important;\r\n  animation-duration: 1.5s;\r\n}\r\n\r\n/* Wave particle animation */\r\n@keyframes waveParticle {\r\n  0% {\r\n    opacity: 0;\r\n    transform: translateX(0) translateY(0) scale(0.5);\r\n  }\r\n  25% {\r\n    opacity: 1;\r\n    transform: translateX(20px) translateY(-3px) scale(1);\r\n  }\r\n  50% {\r\n    opacity: 1;\r\n    transform: translateX(40px) translateY(0) scale(1.2);\r\n  }\r\n  75% {\r\n    opacity: 1;\r\n    transform: translateX(60px) translateY(3px) scale(1);\r\n  }\r\n  100% {\r\n    opacity: 0;\r\n    transform: translateX(80px) translateY(0) scale(0.5);\r\n  }\r\n}\r\n\r\n.wave-particle {\r\n  animation: waveParticle 3s ease-in-out infinite;\r\n  animation-delay: 1s;\r\n}\r\n\r\n/* Glowing underline animation */\r\n@keyframes underlineGlow {\r\n  0%, 100% {\r\n    box-shadow: 0 0 10px rgba(16, 185, 129, 0.5);\r\n    opacity: 0.8;\r\n  }\r\n  50% {\r\n    box-shadow: 0 0 20px rgba(59, 130, 246, 0.8);\r\n    opacity: 1;\r\n  }\r\n}\r\n\r\n.glowing-underline {\r\n  animation: underlineGlow 3s ease-in-out infinite;\r\n  animation-delay: 1.2s;\r\n}\r\n\r\n/* Safe header animation */\r\n@keyframes safeHeaderSlide {\r\n  from {\r\n    opacity: 0;\r\n    transform: translateY(-10px);\r\n  }\r\n  to {\r\n    opacity: 1;\r\n    transform: translateY(0);\r\n  }\r\n}\r\n\r\n.safe-header-animation {\r\n  animation: safeHeaderSlide 0.5s ease-out;\r\n}\r\n\r\n/* Safe center animation */\r\n@keyframes safeCenterFade {\r\n  from {\r\n    opacity: 0;\r\n    transform: scale(0.95);\r\n  }\r\n  to {\r\n    opacity: 1;\r\n    transform: scale(1);\r\n  }\r\n}\r\n\r\n.safe-center-animation {\r\n  animation: safeCenterFade 0.6s ease-out 0.2s both;\r\n}\r\n\r\n/* Safe notification animation */\r\n@keyframes safeNotificationPop {\r\n  from {\r\n    opacity: 0;\r\n    transform: scale(0.9);\r\n  }\r\n  to {\r\n    opacity: 1;\r\n    transform: scale(1);\r\n  }\r\n}\r\n\r\n.safe-notification-animation {\r\n  animation: safeNotificationPop 0.5s ease-out 0.2s both;\r\n}\r\n\r\n/* Fix notification bell sizing */\r\n.safe-notification-animation .notification-bell-button {\r\n  width: 40px;\r\n  height: 40px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  flex-shrink: 0;\r\n}\r\n\r\n.safe-notification-animation .notification-badge {\r\n  position: absolute !important;\r\n  bottom: -2px !important;\r\n  left: 50% !important;\r\n  transform: translateX(-50%) !important;\r\n}\r\n\r\n/* Header centering improvements */\r\n.nav-modern .brainwave-container {\r\n  max-width: fit-content;\r\n  margin: 0 auto;\r\n}\r\n\r\n/* Responsive header adjustments */\r\n@media (max-width: 640px) {\r\n  .nav-modern h1 {\r\n    font-size: 1rem !important;\r\n  }\r\n\r\n  .nav-modern .brainwave-container {\r\n    transform: scale(0.9);\r\n  }\r\n}\r\n\r\n@media (min-width: 641px) and (max-width: 768px) {\r\n  .nav-modern h1 {\r\n    font-size: 1.125rem !important;\r\n  }\r\n}\r\n\r\n@media (min-width: 769px) {\r\n  .nav-modern h1 {\r\n    font-size: 1.25rem !important;\r\n  }\r\n}\r\n\r\n/* Safe profile animation */\r\n@keyframes safeProfileSlide {\r\n  from {\r\n    opacity: 0;\r\n    transform: scale(0.9);\r\n  }\r\n  to {\r\n    opacity: 1;\r\n    transform: scale(1);\r\n  }\r\n}\r\n\r\n.safe-profile-animation {\r\n  animation: safeProfileSlide 0.5s ease-out 0.3s both;\r\n}\r\n\r\n/* Safe content animation */\r\n@keyframes safeContentFade {\r\n  from {\r\n    opacity: 0;\r\n    transform: translateY(10px);\r\n  }\r\n  to {\r\n    opacity: 1;\r\n    transform: translateY(0);\r\n  }\r\n}\r\n\r\n.safe-content-animation {\r\n  animation: safeContentFade 0.3s ease-out 0.1s both;\r\n}\r\n\r\n/* Reduced motion preferences */\r\n@media (prefers-reduced-motion: reduce) {\r\n  .brain-text,\r\n  .wave-text,\r\n  .electric-spark,\r\n  .wave-particle,\r\n  .glowing-underline,\r\n  .safe-header-animation,\r\n  .safe-center-animation,\r\n  .safe-notification-animation,\r\n  .safe-profile-animation,\r\n  .safe-content-animation {\r\n    animation: none !important;\r\n  }\r\n\r\n  .brain-text:hover,\r\n  .wave-text:hover {\r\n    transform: none !important;\r\n  }\r\n}\r\n", "@font-face{font-family:KaTeX_AMS;font-style:normal;font-weight:400;src:url(fonts/KaTeX_AMS-Regular.woff2) format(\"woff2\"),url(fonts/KaTeX_AMS-Regular.woff) format(\"woff\"),url(fonts/KaTeX_AMS-Regular.ttf) format(\"truetype\")}@font-face{font-family:KaTeX_Caligraphic;font-style:normal;font-weight:700;src:url(fonts/KaTeX_Caligraphic-Bold.woff2) format(\"woff2\"),url(fonts/KaTeX_Caligraphic-Bold.woff) format(\"woff\"),url(fonts/KaTeX_Caligraphic-Bold.ttf) format(\"truetype\")}@font-face{font-family:KaTeX_Caligraphic;font-style:normal;font-weight:400;src:url(fonts/KaTeX_Caligraphic-Regular.woff2) format(\"woff2\"),url(fonts/KaTeX_Caligraphic-Regular.woff) format(\"woff\"),url(fonts/KaTeX_Caligraphic-Regular.ttf) format(\"truetype\")}@font-face{font-family:KaTeX_Fraktur;font-style:normal;font-weight:700;src:url(fonts/KaTeX_Fraktur-Bold.woff2) format(\"woff2\"),url(fonts/KaTeX_Fraktur-Bold.woff) format(\"woff\"),url(fonts/KaTeX_Fraktur-Bold.ttf) format(\"truetype\")}@font-face{font-family:KaTeX_Fraktur;font-style:normal;font-weight:400;src:url(fonts/KaTeX_Fraktur-Regular.woff2) format(\"woff2\"),url(fonts/KaTeX_Fraktur-Regular.woff) format(\"woff\"),url(fonts/KaTeX_Fraktur-Regular.ttf) format(\"truetype\")}@font-face{font-family:KaTeX_Main;font-style:normal;font-weight:700;src:url(fonts/KaTeX_Main-Bold.woff2) format(\"woff2\"),url(fonts/KaTeX_Main-Bold.woff) format(\"woff\"),url(fonts/KaTeX_Main-Bold.ttf) format(\"truetype\")}@font-face{font-family:KaTeX_Main;font-style:italic;font-weight:700;src:url(fonts/KaTeX_Main-BoldItalic.woff2) format(\"woff2\"),url(fonts/KaTeX_Main-BoldItalic.woff) format(\"woff\"),url(fonts/KaTeX_Main-BoldItalic.ttf) format(\"truetype\")}@font-face{font-family:KaTeX_Main;font-style:italic;font-weight:400;src:url(fonts/KaTeX_Main-Italic.woff2) format(\"woff2\"),url(fonts/KaTeX_Main-Italic.woff) format(\"woff\"),url(fonts/KaTeX_Main-Italic.ttf) format(\"truetype\")}@font-face{font-family:KaTeX_Main;font-style:normal;font-weight:400;src:url(fonts/KaTeX_Main-Regular.woff2) format(\"woff2\"),url(fonts/KaTeX_Main-Regular.woff) format(\"woff\"),url(fonts/KaTeX_Main-Regular.ttf) format(\"truetype\")}@font-face{font-family:KaTeX_Math;font-style:italic;font-weight:700;src:url(fonts/KaTeX_Math-BoldItalic.woff2) format(\"woff2\"),url(fonts/KaTeX_Math-BoldItalic.woff) format(\"woff\"),url(fonts/KaTeX_Math-BoldItalic.ttf) format(\"truetype\")}@font-face{font-family:KaTeX_Math;font-style:italic;font-weight:400;src:url(fonts/KaTeX_Math-Italic.woff2) format(\"woff2\"),url(fonts/KaTeX_Math-Italic.woff) format(\"woff\"),url(fonts/KaTeX_Math-Italic.ttf) format(\"truetype\")}@font-face{font-family:\"KaTeX_SansSerif\";font-style:normal;font-weight:700;src:url(fonts/KaTeX_SansSerif-Bold.woff2) format(\"woff2\"),url(fonts/KaTeX_SansSerif-Bold.woff) format(\"woff\"),url(fonts/KaTeX_SansSerif-Bold.ttf) format(\"truetype\")}@font-face{font-family:\"KaTeX_SansSerif\";font-style:italic;font-weight:400;src:url(fonts/KaTeX_SansSerif-Italic.woff2) format(\"woff2\"),url(fonts/KaTeX_SansSerif-Italic.woff) format(\"woff\"),url(fonts/KaTeX_SansSerif-Italic.ttf) format(\"truetype\")}@font-face{font-family:\"KaTeX_SansSerif\";font-style:normal;font-weight:400;src:url(fonts/KaTeX_SansSerif-Regular.woff2) format(\"woff2\"),url(fonts/KaTeX_SansSerif-Regular.woff) format(\"woff\"),url(fonts/KaTeX_SansSerif-Regular.ttf) format(\"truetype\")}@font-face{font-family:KaTeX_Script;font-style:normal;font-weight:400;src:url(fonts/KaTeX_Script-Regular.woff2) format(\"woff2\"),url(fonts/KaTeX_Script-Regular.woff) format(\"woff\"),url(fonts/KaTeX_Script-Regular.ttf) format(\"truetype\")}@font-face{font-family:KaTeX_Size1;font-style:normal;font-weight:400;src:url(fonts/KaTeX_Size1-Regular.woff2) format(\"woff2\"),url(fonts/KaTeX_Size1-Regular.woff) format(\"woff\"),url(fonts/KaTeX_Size1-Regular.ttf) format(\"truetype\")}@font-face{font-family:KaTeX_Size2;font-style:normal;font-weight:400;src:url(fonts/KaTeX_Size2-Regular.woff2) format(\"woff2\"),url(fonts/KaTeX_Size2-Regular.woff) format(\"woff\"),url(fonts/KaTeX_Size2-Regular.ttf) format(\"truetype\")}@font-face{font-family:KaTeX_Size3;font-style:normal;font-weight:400;src:url(fonts/KaTeX_Size3-Regular.woff2) format(\"woff2\"),url(fonts/KaTeX_Size3-Regular.woff) format(\"woff\"),url(fonts/KaTeX_Size3-Regular.ttf) format(\"truetype\")}@font-face{font-family:KaTeX_Size4;font-style:normal;font-weight:400;src:url(fonts/KaTeX_Size4-Regular.woff2) format(\"woff2\"),url(fonts/KaTeX_Size4-Regular.woff) format(\"woff\"),url(fonts/KaTeX_Size4-Regular.ttf) format(\"truetype\")}@font-face{font-family:KaTeX_Typewriter;font-style:normal;font-weight:400;src:url(fonts/KaTeX_Typewriter-Regular.woff2) format(\"woff2\"),url(fonts/KaTeX_Typewriter-Regular.woff) format(\"woff\"),url(fonts/KaTeX_Typewriter-Regular.ttf) format(\"truetype\")}.katex{font:normal 1.21em KaTeX_Main,Times New Roman,serif;line-height:1.2;text-indent:0;text-rendering:auto}.katex *{-ms-high-contrast-adjust:none!important;border-color:currentColor}.katex .katex-version:after{content:\"0.16.19\"}.katex .katex-mathml{clip:rect(1px,1px,1px,1px);border:0;height:1px;overflow:hidden;padding:0;position:absolute;width:1px}.katex .katex-html>.newline{display:block}.katex .base{position:relative;white-space:nowrap;width:-webkit-min-content;width:-moz-min-content;width:min-content}.katex .base,.katex .strut{display:inline-block}.katex .textbf{font-weight:700}.katex .textit{font-style:italic}.katex .textrm{font-family:KaTeX_Main}.katex .textsf{font-family:KaTeX_SansSerif}.katex .texttt{font-family:KaTeX_Typewriter}.katex .mathnormal{font-family:KaTeX_Math;font-style:italic}.katex .mathit{font-family:KaTeX_Main;font-style:italic}.katex .mathrm{font-style:normal}.katex .mathbf{font-family:KaTeX_Main;font-weight:700}.katex .boldsymbol{font-family:KaTeX_Math;font-style:italic;font-weight:700}.katex .amsrm,.katex .mathbb,.katex .textbb{font-family:KaTeX_AMS}.katex .mathcal{font-family:KaTeX_Caligraphic}.katex .mathfrak,.katex .textfrak{font-family:KaTeX_Fraktur}.katex .mathboldfrak,.katex .textboldfrak{font-family:KaTeX_Fraktur;font-weight:700}.katex .mathtt{font-family:KaTeX_Typewriter}.katex .mathscr,.katex .textscr{font-family:KaTeX_Script}.katex .mathsf,.katex .textsf{font-family:KaTeX_SansSerif}.katex .mathboldsf,.katex .textboldsf{font-family:KaTeX_SansSerif;font-weight:700}.katex .mathitsf,.katex .mathsfit,.katex .textitsf{font-family:KaTeX_SansSerif;font-style:italic}.katex .mainrm{font-family:KaTeX_Main;font-style:normal}.katex .vlist-t{border-collapse:collapse;display:inline-table;table-layout:fixed}.katex .vlist-r{display:table-row}.katex .vlist{display:table-cell;position:relative;vertical-align:bottom}.katex .vlist>span{display:block;height:0;position:relative}.katex .vlist>span>span{display:inline-block}.katex .vlist>span>.pstrut{overflow:hidden;width:0}.katex .vlist-t2{margin-right:-2px}.katex .vlist-s{display:table-cell;font-size:1px;min-width:2px;vertical-align:bottom;width:2px}.katex .vbox{align-items:baseline;display:inline-flex;flex-direction:column}.katex .hbox{width:100%}.katex .hbox,.katex .thinbox{display:inline-flex;flex-direction:row}.katex .thinbox{max-width:0;width:0}.katex .msupsub{text-align:left}.katex .mfrac>span>span{text-align:center}.katex .mfrac .frac-line{border-bottom-style:solid;display:inline-block;width:100%}.katex .hdashline,.katex .hline,.katex .mfrac .frac-line,.katex .overline .overline-line,.katex .rule,.katex .underline .underline-line{min-height:1px}.katex .mspace{display:inline-block}.katex .clap,.katex .llap,.katex .rlap{position:relative;width:0}.katex .clap>.inner,.katex .llap>.inner,.katex .rlap>.inner{position:absolute}.katex .clap>.fix,.katex .llap>.fix,.katex .rlap>.fix{display:inline-block}.katex .llap>.inner{right:0}.katex .clap>.inner,.katex .rlap>.inner{left:0}.katex .clap>.inner>span{margin-left:-50%;margin-right:50%}.katex .rule{border:0 solid;display:inline-block;position:relative}.katex .hline,.katex .overline .overline-line,.katex .underline .underline-line{border-bottom-style:solid;display:inline-block;width:100%}.katex .hdashline{border-bottom-style:dashed;display:inline-block;width:100%}.katex .sqrt>.root{margin-left:.2777777778em;margin-right:-.5555555556em}.katex .fontsize-ensurer.reset-size1.size1,.katex .sizing.reset-size1.size1{font-size:1em}.katex .fontsize-ensurer.reset-size1.size2,.katex .sizing.reset-size1.size2{font-size:1.2em}.katex .fontsize-ensurer.reset-size1.size3,.katex .sizing.reset-size1.size3{font-size:1.4em}.katex .fontsize-ensurer.reset-size1.size4,.katex .sizing.reset-size1.size4{font-size:1.6em}.katex .fontsize-ensurer.reset-size1.size5,.katex .sizing.reset-size1.size5{font-size:1.8em}.katex .fontsize-ensurer.reset-size1.size6,.katex .sizing.reset-size1.size6{font-size:2em}.katex .fontsize-ensurer.reset-size1.size7,.katex .sizing.reset-size1.size7{font-size:2.4em}.katex .fontsize-ensurer.reset-size1.size8,.katex .sizing.reset-size1.size8{font-size:2.88em}.katex .fontsize-ensurer.reset-size1.size9,.katex .sizing.reset-size1.size9{font-size:3.456em}.katex .fontsize-ensurer.reset-size1.size10,.katex .sizing.reset-size1.size10{font-size:4.148em}.katex .fontsize-ensurer.reset-size1.size11,.katex .sizing.reset-size1.size11{font-size:4.976em}.katex .fontsize-ensurer.reset-size2.size1,.katex .sizing.reset-size2.size1{font-size:.8333333333em}.katex .fontsize-ensurer.reset-size2.size2,.katex .sizing.reset-size2.size2{font-size:1em}.katex .fontsize-ensurer.reset-size2.size3,.katex .sizing.reset-size2.size3{font-size:1.1666666667em}.katex .fontsize-ensurer.reset-size2.size4,.katex .sizing.reset-size2.size4{font-size:1.3333333333em}.katex .fontsize-ensurer.reset-size2.size5,.katex .sizing.reset-size2.size5{font-size:1.5em}.katex .fontsize-ensurer.reset-size2.size6,.katex .sizing.reset-size2.size6{font-size:1.6666666667em}.katex .fontsize-ensurer.reset-size2.size7,.katex .sizing.reset-size2.size7{font-size:2em}.katex .fontsize-ensurer.reset-size2.size8,.katex .sizing.reset-size2.size8{font-size:2.4em}.katex .fontsize-ensurer.reset-size2.size9,.katex .sizing.reset-size2.size9{font-size:2.88em}.katex .fontsize-ensurer.reset-size2.size10,.katex .sizing.reset-size2.size10{font-size:3.4566666667em}.katex .fontsize-ensurer.reset-size2.size11,.katex .sizing.reset-size2.size11{font-size:4.1466666667em}.katex .fontsize-ensurer.reset-size3.size1,.katex .sizing.reset-size3.size1{font-size:.7142857143em}.katex .fontsize-ensurer.reset-size3.size2,.katex .sizing.reset-size3.size2{font-size:.8571428571em}.katex .fontsize-ensurer.reset-size3.size3,.katex .sizing.reset-size3.size3{font-size:1em}.katex .fontsize-ensurer.reset-size3.size4,.katex .sizing.reset-size3.size4{font-size:1.1428571429em}.katex .fontsize-ensurer.reset-size3.size5,.katex .sizing.reset-size3.size5{font-size:1.2857142857em}.katex .fontsize-ensurer.reset-size3.size6,.katex .sizing.reset-size3.size6{font-size:1.4285714286em}.katex .fontsize-ensurer.reset-size3.size7,.katex .sizing.reset-size3.size7{font-size:1.7142857143em}.katex .fontsize-ensurer.reset-size3.size8,.katex .sizing.reset-size3.size8{font-size:2.0571428571em}.katex .fontsize-ensurer.reset-size3.size9,.katex .sizing.reset-size3.size9{font-size:2.4685714286em}.katex .fontsize-ensurer.reset-size3.size10,.katex .sizing.reset-size3.size10{font-size:2.9628571429em}.katex .fontsize-ensurer.reset-size3.size11,.katex .sizing.reset-size3.size11{font-size:3.5542857143em}.katex .fontsize-ensurer.reset-size4.size1,.katex .sizing.reset-size4.size1{font-size:.625em}.katex .fontsize-ensurer.reset-size4.size2,.katex .sizing.reset-size4.size2{font-size:.75em}.katex .fontsize-ensurer.reset-size4.size3,.katex .sizing.reset-size4.size3{font-size:.875em}.katex .fontsize-ensurer.reset-size4.size4,.katex .sizing.reset-size4.size4{font-size:1em}.katex .fontsize-ensurer.reset-size4.size5,.katex .sizing.reset-size4.size5{font-size:1.125em}.katex .fontsize-ensurer.reset-size4.size6,.katex .sizing.reset-size4.size6{font-size:1.25em}.katex .fontsize-ensurer.reset-size4.size7,.katex .sizing.reset-size4.size7{font-size:1.5em}.katex .fontsize-ensurer.reset-size4.size8,.katex .sizing.reset-size4.size8{font-size:1.8em}.katex .fontsize-ensurer.reset-size4.size9,.katex .sizing.reset-size4.size9{font-size:2.16em}.katex .fontsize-ensurer.reset-size4.size10,.katex .sizing.reset-size4.size10{font-size:2.5925em}.katex .fontsize-ensurer.reset-size4.size11,.katex .sizing.reset-size4.size11{font-size:3.11em}.katex .fontsize-ensurer.reset-size5.size1,.katex .sizing.reset-size5.size1{font-size:.5555555556em}.katex .fontsize-ensurer.reset-size5.size2,.katex .sizing.reset-size5.size2{font-size:.6666666667em}.katex .fontsize-ensurer.reset-size5.size3,.katex .sizing.reset-size5.size3{font-size:.7777777778em}.katex .fontsize-ensurer.reset-size5.size4,.katex .sizing.reset-size5.size4{font-size:.8888888889em}.katex .fontsize-ensurer.reset-size5.size5,.katex .sizing.reset-size5.size5{font-size:1em}.katex .fontsize-ensurer.reset-size5.size6,.katex .sizing.reset-size5.size6{font-size:1.1111111111em}.katex .fontsize-ensurer.reset-size5.size7,.katex .sizing.reset-size5.size7{font-size:1.3333333333em}.katex .fontsize-ensurer.reset-size5.size8,.katex .sizing.reset-size5.size8{font-size:1.6em}.katex .fontsize-ensurer.reset-size5.size9,.katex .sizing.reset-size5.size9{font-size:1.92em}.katex .fontsize-ensurer.reset-size5.size10,.katex .sizing.reset-size5.size10{font-size:2.3044444444em}.katex .fontsize-ensurer.reset-size5.size11,.katex .sizing.reset-size5.size11{font-size:2.7644444444em}.katex .fontsize-ensurer.reset-size6.size1,.katex .sizing.reset-size6.size1{font-size:.5em}.katex .fontsize-ensurer.reset-size6.size2,.katex .sizing.reset-size6.size2{font-size:.6em}.katex .fontsize-ensurer.reset-size6.size3,.katex .sizing.reset-size6.size3{font-size:.7em}.katex .fontsize-ensurer.reset-size6.size4,.katex .sizing.reset-size6.size4{font-size:.8em}.katex .fontsize-ensurer.reset-size6.size5,.katex .sizing.reset-size6.size5{font-size:.9em}.katex .fontsize-ensurer.reset-size6.size6,.katex .sizing.reset-size6.size6{font-size:1em}.katex .fontsize-ensurer.reset-size6.size7,.katex .sizing.reset-size6.size7{font-size:1.2em}.katex .fontsize-ensurer.reset-size6.size8,.katex .sizing.reset-size6.size8{font-size:1.44em}.katex .fontsize-ensurer.reset-size6.size9,.katex .sizing.reset-size6.size9{font-size:1.728em}.katex .fontsize-ensurer.reset-size6.size10,.katex .sizing.reset-size6.size10{font-size:2.074em}.katex .fontsize-ensurer.reset-size6.size11,.katex .sizing.reset-size6.size11{font-size:2.488em}.katex .fontsize-ensurer.reset-size7.size1,.katex .sizing.reset-size7.size1{font-size:.4166666667em}.katex .fontsize-ensurer.reset-size7.size2,.katex .sizing.reset-size7.size2{font-size:.5em}.katex .fontsize-ensurer.reset-size7.size3,.katex .sizing.reset-size7.size3{font-size:.5833333333em}.katex .fontsize-ensurer.reset-size7.size4,.katex .sizing.reset-size7.size4{font-size:.6666666667em}.katex .fontsize-ensurer.reset-size7.size5,.katex .sizing.reset-size7.size5{font-size:.75em}.katex .fontsize-ensurer.reset-size7.size6,.katex .sizing.reset-size7.size6{font-size:.8333333333em}.katex .fontsize-ensurer.reset-size7.size7,.katex .sizing.reset-size7.size7{font-size:1em}.katex .fontsize-ensurer.reset-size7.size8,.katex .sizing.reset-size7.size8{font-size:1.2em}.katex .fontsize-ensurer.reset-size7.size9,.katex .sizing.reset-size7.size9{font-size:1.44em}.katex .fontsize-ensurer.reset-size7.size10,.katex .sizing.reset-size7.size10{font-size:1.7283333333em}.katex .fontsize-ensurer.reset-size7.size11,.katex .sizing.reset-size7.size11{font-size:2.0733333333em}.katex .fontsize-ensurer.reset-size8.size1,.katex .sizing.reset-size8.size1{font-size:.3472222222em}.katex .fontsize-ensurer.reset-size8.size2,.katex .sizing.reset-size8.size2{font-size:.4166666667em}.katex .fontsize-ensurer.reset-size8.size3,.katex .sizing.reset-size8.size3{font-size:.4861111111em}.katex .fontsize-ensurer.reset-size8.size4,.katex .sizing.reset-size8.size4{font-size:.5555555556em}.katex .fontsize-ensurer.reset-size8.size5,.katex .sizing.reset-size8.size5{font-size:.625em}.katex .fontsize-ensurer.reset-size8.size6,.katex .sizing.reset-size8.size6{font-size:.6944444444em}.katex .fontsize-ensurer.reset-size8.size7,.katex .sizing.reset-size8.size7{font-size:.8333333333em}.katex .fontsize-ensurer.reset-size8.size8,.katex .sizing.reset-size8.size8{font-size:1em}.katex .fontsize-ensurer.reset-size8.size9,.katex .sizing.reset-size8.size9{font-size:1.2em}.katex .fontsize-ensurer.reset-size8.size10,.katex .sizing.reset-size8.size10{font-size:1.4402777778em}.katex .fontsize-ensurer.reset-size8.size11,.katex .sizing.reset-size8.size11{font-size:1.7277777778em}.katex .fontsize-ensurer.reset-size9.size1,.katex .sizing.reset-size9.size1{font-size:.2893518519em}.katex .fontsize-ensurer.reset-size9.size2,.katex .sizing.reset-size9.size2{font-size:.3472222222em}.katex .fontsize-ensurer.reset-size9.size3,.katex .sizing.reset-size9.size3{font-size:.4050925926em}.katex .fontsize-ensurer.reset-size9.size4,.katex .sizing.reset-size9.size4{font-size:.462962963em}.katex .fontsize-ensurer.reset-size9.size5,.katex .sizing.reset-size9.size5{font-size:.5208333333em}.katex .fontsize-ensurer.reset-size9.size6,.katex .sizing.reset-size9.size6{font-size:.5787037037em}.katex .fontsize-ensurer.reset-size9.size7,.katex .sizing.reset-size9.size7{font-size:.6944444444em}.katex .fontsize-ensurer.reset-size9.size8,.katex .sizing.reset-size9.size8{font-size:.8333333333em}.katex .fontsize-ensurer.reset-size9.size9,.katex .sizing.reset-size9.size9{font-size:1em}.katex .fontsize-ensurer.reset-size9.size10,.katex .sizing.reset-size9.size10{font-size:1.2002314815em}.katex .fontsize-ensurer.reset-size9.size11,.katex .sizing.reset-size9.size11{font-size:1.4398148148em}.katex .fontsize-ensurer.reset-size10.size1,.katex .sizing.reset-size10.size1{font-size:.2410800386em}.katex .fontsize-ensurer.reset-size10.size2,.katex .sizing.reset-size10.size2{font-size:.2892960463em}.katex .fontsize-ensurer.reset-size10.size3,.katex .sizing.reset-size10.size3{font-size:.337512054em}.katex .fontsize-ensurer.reset-size10.size4,.katex .sizing.reset-size10.size4{font-size:.3857280617em}.katex .fontsize-ensurer.reset-size10.size5,.katex .sizing.reset-size10.size5{font-size:.4339440694em}.katex .fontsize-ensurer.reset-size10.size6,.katex .sizing.reset-size10.size6{font-size:.4821600771em}.katex .fontsize-ensurer.reset-size10.size7,.katex .sizing.reset-size10.size7{font-size:.5785920926em}.katex .fontsize-ensurer.reset-size10.size8,.katex .sizing.reset-size10.size8{font-size:.6943105111em}.katex .fontsize-ensurer.reset-size10.size9,.katex .sizing.reset-size10.size9{font-size:.8331726133em}.katex .fontsize-ensurer.reset-size10.size10,.katex .sizing.reset-size10.size10{font-size:1em}.katex .fontsize-ensurer.reset-size10.size11,.katex .sizing.reset-size10.size11{font-size:1.1996142719em}.katex .fontsize-ensurer.reset-size11.size1,.katex .sizing.reset-size11.size1{font-size:.2009646302em}.katex .fontsize-ensurer.reset-size11.size2,.katex .sizing.reset-size11.size2{font-size:.2411575563em}.katex .fontsize-ensurer.reset-size11.size3,.katex .sizing.reset-size11.size3{font-size:.2813504823em}.katex .fontsize-ensurer.reset-size11.size4,.katex .sizing.reset-size11.size4{font-size:.3215434084em}.katex .fontsize-ensurer.reset-size11.size5,.katex .sizing.reset-size11.size5{font-size:.3617363344em}.katex .fontsize-ensurer.reset-size11.size6,.katex .sizing.reset-size11.size6{font-size:.4019292605em}.katex .fontsize-ensurer.reset-size11.size7,.katex .sizing.reset-size11.size7{font-size:.4823151125em}.katex .fontsize-ensurer.reset-size11.size8,.katex .sizing.reset-size11.size8{font-size:.578778135em}.katex .fontsize-ensurer.reset-size11.size9,.katex .sizing.reset-size11.size9{font-size:.6945337621em}.katex .fontsize-ensurer.reset-size11.size10,.katex .sizing.reset-size11.size10{font-size:.8336012862em}.katex .fontsize-ensurer.reset-size11.size11,.katex .sizing.reset-size11.size11{font-size:1em}.katex .delimsizing.size1{font-family:KaTeX_Size1}.katex .delimsizing.size2{font-family:KaTeX_Size2}.katex .delimsizing.size3{font-family:KaTeX_Size3}.katex .delimsizing.size4{font-family:KaTeX_Size4}.katex .delimsizing.mult .delim-size1>span{font-family:KaTeX_Size1}.katex .delimsizing.mult .delim-size4>span{font-family:KaTeX_Size4}.katex .nulldelimiter{display:inline-block;width:.12em}.katex .delimcenter,.katex .op-symbol{position:relative}.katex .op-symbol.small-op{font-family:KaTeX_Size1}.katex .op-symbol.large-op{font-family:KaTeX_Size2}.katex .accent>.vlist-t,.katex .op-limits>.vlist-t{text-align:center}.katex .accent .accent-body{position:relative}.katex .accent .accent-body:not(.accent-full){width:0}.katex .overlay{display:block}.katex .mtable .vertical-separator{display:inline-block;min-width:1px}.katex .mtable .arraycolsep{display:inline-block}.katex .mtable .col-align-c>.vlist-t{text-align:center}.katex .mtable .col-align-l>.vlist-t{text-align:left}.katex .mtable .col-align-r>.vlist-t{text-align:right}.katex .svg-align{text-align:left}.katex svg{fill:currentColor;stroke:currentColor;fill-rule:nonzero;fill-opacity:1;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke-miterlimit:4;stroke-dasharray:none;stroke-dashoffset:0;stroke-opacity:1;display:block;height:inherit;position:absolute;width:100%}.katex svg path{stroke:none}.katex img{border-style:none;max-height:none;max-width:none;min-height:0;min-width:0}.katex .stretchy{display:block;overflow:hidden;position:relative;width:100%}.katex .stretchy:after,.katex .stretchy:before{content:\"\"}.katex .hide-tail{overflow:hidden;position:relative;width:100%}.katex .halfarrow-left{left:0;overflow:hidden;position:absolute;width:50.2%}.katex .halfarrow-right{overflow:hidden;position:absolute;right:0;width:50.2%}.katex .brace-left{left:0;overflow:hidden;position:absolute;width:25.1%}.katex .brace-center{left:25%;overflow:hidden;position:absolute;width:50%}.katex .brace-right{overflow:hidden;position:absolute;right:0;width:25.1%}.katex .x-arrow-pad{padding:0 .5em}.katex .cd-arrow-pad{padding:0 .55556em 0 .27778em}.katex .mover,.katex .munder,.katex .x-arrow{text-align:center}.katex .boxpad{padding:0 .3em}.katex .fbox,.katex .fcolorbox{border:.04em solid;box-sizing:border-box}.katex .cancel-pad{padding:0 .2em}.katex .cancel-lap{margin-left:-.2em;margin-right:-.2em}.katex .sout{border-bottom-style:solid;border-bottom-width:.08em}.katex .angl{border-right:.049em solid;border-top:.049em solid;box-sizing:border-box;margin-right:.03889em}.katex .anglpad{padding:0 .03889em}.katex .eqn-num:before{content:\"(\" counter(katexEqnNo) \")\";counter-increment:katexEqnNo}.katex .mml-eqn-num:before{content:\"(\" counter(mmlEqnNo) \")\";counter-increment:mmlEqnNo}.katex .mtr-glue{width:50%}.katex .cd-vert-arrow{display:inline-block;position:relative}.katex .cd-label-left{display:inline-block;position:absolute;right:calc(50% + .3em);text-align:left}.katex .cd-label-right{display:inline-block;left:calc(50% + .3em);position:absolute;text-align:right}.katex-display{display:block;margin:1em 0;text-align:center}.katex-display>.katex{display:block;text-align:center;white-space:nowrap}.katex-display>.katex>.katex-html{display:block;position:relative}.katex-display>.katex>.katex-html>.tag{position:absolute;right:0}.katex-display.leqno>.katex>.katex-html>.tag{left:0;right:auto}.katex-display.fleqn>.katex{padding-left:2em;text-align:left}body{counter-reset:katexEqnNo mmlEqnNo}\n", "/* ===== RESPONSIVE LOGIN PAGE ===== */\r\n\r\n.login-container {\r\n    min-height: 100vh;\r\n    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    padding: 1rem;\r\n    font-family: 'Inter', 'Roboto', 'Nunito', system-ui, -apple-system, sans-serif;\r\n    position: relative;\r\n}\r\n\r\n.login-container::before {\r\n    content: '';\r\n    position: absolute;\r\n    top: 0;\r\n    left: 0;\r\n    right: 0;\r\n    bottom: 0;\r\n    background: radial-gradient(circle at 30% 20%, rgba(0, 123, 255, 0.1) 0%, transparent 50%),\r\n                radial-gradient(circle at 70% 80%, rgba(0, 86, 210, 0.1) 0%, transparent 50%);\r\n    z-index: 0;\r\n}\r\n\r\n.login-card {\r\n    width: 100%;\r\n    max-width: 400px;\r\n    background: rgba(255, 255, 255, 0.95);\r\n    backdrop-filter: blur(10px);\r\n    border: 1px solid rgba(255, 255, 255, 0.2);\r\n    border-radius: 1.5rem;\r\n    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);\r\n    padding: 2rem;\r\n    position: relative;\r\n    z-index: 1;\r\n    transition: all 0.3s ease;\r\n}\r\n\r\n.login-card:hover {\r\n    transform: translateY(-2px);\r\n    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.15);\r\n}\r\n\r\n/* ===== HEADER SECTION ===== */\r\n.login-header {\r\n    text-align: center;\r\n    margin-bottom: 2rem;\r\n}\r\n\r\n.login-logo {\r\n    width: 64px;\r\n    height: 64px;\r\n    margin: 0 auto 1.5rem;\r\n    display: block;\r\n    border-radius: 1rem;\r\n    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);\r\n    transition: all 0.3s ease;\r\n}\r\n\r\n.login-logo:hover {\r\n    transform: scale(1.05);\r\n    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.15);\r\n}\r\n\r\n.login-title {\r\n    font-size: 1.875rem;\r\n    font-weight: 800;\r\n    color: #1f2937;\r\n    margin-bottom: 0.5rem;\r\n    background: linear-gradient(135deg, #007BFF 0%, #0056D2 100%);\r\n    -webkit-background-clip: text;\r\n    -webkit-text-fill-color: transparent;\r\n    background-clip: text;\r\n    line-height: 1.2;\r\n}\r\n\r\n.login-subtitle {\r\n    color: #6b7280;\r\n    margin-bottom: 2rem;\r\n    font-size: 0.875rem;\r\n    line-height: 1.5;\r\n}\r\n\r\n/* ===== FORM STYLING ===== */\r\n.login-form {\r\n    margin-bottom: 1.5rem;\r\n}\r\n\r\n.login-form .ant-form-item {\r\n    margin-bottom: 1.5rem;\r\n}\r\n\r\n.login-form .ant-form-item-label > label {\r\n    font-weight: 600;\r\n    color: #374151;\r\n    font-size: 0.875rem;\r\n    margin-bottom: 0.5rem;\r\n}\r\n\r\n.form-input {\r\n    width: 100%;\r\n    padding: 0.875rem 1rem;\r\n    border: 2px solid #e5e7eb;\r\n    border-radius: 0.75rem;\r\n    font-size: 0.875rem;\r\n    transition: all 0.2s ease;\r\n    background: #f9fafb;\r\n    color: #1f2937;\r\n    font-family: inherit;\r\n    line-height: 1.5;\r\n}\r\n\r\n.form-input:focus {\r\n    outline: none;\r\n    border-color: #007BFF;\r\n    background: #ffffff;\r\n    box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);\r\n    transform: translateY(-1px);\r\n}\r\n\r\n.form-input::placeholder {\r\n    color: #9ca3af;\r\n    font-size: 0.875rem;\r\n}\r\n\r\n.login-btn {\r\n    width: 100%;\r\n    background: linear-gradient(135deg, #007BFF 0%, #0056D2 100%);\r\n    color: white;\r\n    padding: 1rem 1.5rem;\r\n    border: none;\r\n    border-radius: 0.75rem;\r\n    font-weight: 600;\r\n    font-size: 0.875rem;\r\n    cursor: pointer;\r\n    transition: all 0.3s ease;\r\n    font-family: inherit;\r\n    box-shadow: 0 4px 14px 0 rgba(0, 123, 255, 0.25);\r\n    margin-top: 0.5rem;\r\n}\r\n\r\n.login-btn:hover {\r\n    transform: translateY(-2px);\r\n    box-shadow: 0 8px 25px 0 rgba(0, 123, 255, 0.35);\r\n    background: linear-gradient(135deg, #0056D2 0%, #004494 100%);\r\n}\r\n\r\n.login-btn:active {\r\n    transform: translateY(0);\r\n}\r\n\r\n/* ===== FOOTER SECTION ===== */\r\n.login-footer {\r\n    text-align: center;\r\n    padding-top: 1.5rem;\r\n    border-top: 1px solid #e5e7eb;\r\n}\r\n\r\n.login-footer p {\r\n    color: #6b7280;\r\n    font-size: 0.875rem;\r\n    margin: 0;\r\n}\r\n\r\n.login-link {\r\n    color: #007BFF;\r\n    font-weight: 600;\r\n    text-decoration: none;\r\n    transition: all 0.2s ease;\r\n}\r\n\r\n.login-link:hover {\r\n    color: #0056D2;\r\n    text-decoration: underline;\r\n}\r\n\r\n/* ===== RESPONSIVE DESIGN ===== */\r\n\r\n/* Mobile Devices (320px - 480px) */\r\n@media (max-width: 480px) {\r\n    .login-container {\r\n        padding: 0.75rem;\r\n        align-items: flex-start;\r\n        padding-top: 2rem;\r\n    }\r\n\r\n    .login-card {\r\n        max-width: 100%;\r\n        padding: 1.5rem;\r\n        border-radius: 1rem;\r\n        margin: 0;\r\n    }\r\n\r\n    .login-logo {\r\n        width: 56px;\r\n        height: 56px;\r\n        margin-bottom: 1rem;\r\n    }\r\n\r\n    .login-title {\r\n        font-size: 1.5rem;\r\n        margin-bottom: 0.5rem;\r\n    }\r\n\r\n    .login-subtitle {\r\n        font-size: 0.8rem;\r\n        margin-bottom: 1.5rem;\r\n    }\r\n\r\n    .login-form .ant-form-item {\r\n        margin-bottom: 1.25rem;\r\n    }\r\n\r\n    .form-input {\r\n        padding: 1rem;\r\n        font-size: 1rem;\r\n        border-radius: 0.5rem;\r\n    }\r\n\r\n    .login-btn {\r\n        padding: 1.125rem 1.5rem;\r\n        font-size: 1rem;\r\n        border-radius: 0.5rem;\r\n        width: 100%;\r\n        min-height: 48px; /* Touch-friendly minimum */\r\n        font-weight: 600;\r\n    }\r\n\r\n    .login-footer {\r\n        padding-top: 1.25rem;\r\n    }\r\n\r\n    .login-footer p {\r\n        font-size: 0.8rem;\r\n    }\r\n}\r\n\r\n/* Tablet Devices (481px - 768px) */\r\n@media (min-width: 481px) and (max-width: 768px) {\r\n    .login-container {\r\n        padding: 1rem;\r\n    }\r\n\r\n    .login-card {\r\n        max-width: 420px;\r\n        padding: 2rem;\r\n    }\r\n\r\n    .login-logo {\r\n        width: 60px;\r\n        height: 60px;\r\n    }\r\n\r\n    .login-title {\r\n        font-size: 1.75rem;\r\n    }\r\n\r\n    .form-input {\r\n        padding: 0.9375rem 1rem;\r\n        font-size: 0.9375rem;\r\n    }\r\n\r\n    .login-btn {\r\n        padding: 1.0625rem 1.5rem;\r\n        font-size: 0.9375rem;\r\n    }\r\n}\r\n\r\n/* Desktop Devices (769px - 1024px) */\r\n@media (min-width: 769px) and (max-width: 1024px) {\r\n    .login-card {\r\n        max-width: 440px;\r\n        padding: 2.5rem;\r\n    }\r\n\r\n    .login-logo {\r\n        width: 68px;\r\n        height: 68px;\r\n    }\r\n}\r\n\r\n/* Large Desktop (1025px+) */\r\n@media (min-width: 1025px) {\r\n    .login-card {\r\n        max-width: 460px;\r\n        padding: 3rem;\r\n    }\r\n\r\n    .login-logo {\r\n        width: 72px;\r\n        height: 72px;\r\n    }\r\n\r\n    .login-title {\r\n        font-size: 2rem;\r\n    }\r\n}\r\n\r\n/* Landscape Mobile */\r\n@media (max-width: 768px) and (orientation: landscape) {\r\n    .login-container {\r\n        padding: 1rem;\r\n        align-items: center;\r\n    }\r\n\r\n    .login-card {\r\n        padding: 1.5rem;\r\n    }\r\n\r\n    .login-logo {\r\n        width: 48px;\r\n        height: 48px;\r\n        margin-bottom: 0.75rem;\r\n    }\r\n\r\n    .login-title {\r\n        font-size: 1.375rem;\r\n        margin-bottom: 0.375rem;\r\n    }\r\n\r\n    .login-subtitle {\r\n        margin-bottom: 1.25rem;\r\n    }\r\n\r\n    .login-form .ant-form-item {\r\n        margin-bottom: 1rem;\r\n    }\r\n}\r\n\r\n.login-form .form-group {\r\n    margin-bottom: var(--space-5, 1.25rem);\r\n}\r\n\r\n.login-form .form-control {\r\n    background: rgba(255, 255, 255, 0.8);\r\n    border: 2px solid var(--gray-200, #e5e7eb);\r\n    transition: var(--transition-normal, 300ms cubic-bezier(0.4, 0, 0.2, 1));\r\n}\r\n\r\n.login-form .form-control:focus {\r\n    background: rgba(255, 255, 255, 1);\r\n    border-color: var(--primary, #007BFF);\r\n    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);\r\n    transform: translateY(-1px);\r\n}\r\n\r\n.login-form .btn {\r\n    width: 100%;\r\n    margin-top: var(--space-4, 1rem);\r\n    padding: var(--space-4, 1rem) var(--space-6, 1.5rem);\r\n    font-weight: 600;\r\n    text-transform: uppercase;\r\n    letter-spacing: 0.025em;\r\n}\r\n\r\n.login-links {\r\n    text-align: center;\r\n    margin-top: var(--space-6, 1.5rem);\r\n    padding-top: var(--space-6, 1.5rem);\r\n    border-top: 1px solid var(--gray-200, #e5e7eb);\r\n}\r\n\r\n.login-links a {\r\n    color: var(--primary, #007BFF);\r\n    font-weight: 500;\r\n    transition: var(--transition-normal, 300ms cubic-bezier(0.4, 0, 0.2, 1));\r\n}\r\n\r\n.login-links a:hover {\r\n    color: var(--primary-dark, #0056D2);\r\n    text-decoration: underline;\r\n}\r\n\r\n/* Responsive Design */\r\n@media only screen and (max-width: 640px) {\r\n    .login-container {\r\n        padding: var(--space-3, 0.75rem);\r\n    }\r\n\r\n    .card {\r\n        padding: var(--space-6, 1.5rem);\r\n        margin: var(--space-3, 0.75rem);\r\n    }\r\n\r\n    .login-logo {\r\n        width: 60px;\r\n        height: 60px;\r\n    }\r\n\r\n    .login-title {\r\n        font-size: var(--font-size-xl, 1.25rem);\r\n    }\r\n}\r\n\r\n@media only screen and (max-width: 480px) {\r\n    .card {\r\n        padding: var(--space-4, 1rem);\r\n    }\r\n\r\n    .login-form .form-control {\r\n        padding: var(--space-3, 0.75rem);\r\n    }\r\n\r\n    .login-form .btn {\r\n        padding: var(--space-3, 0.75rem) var(--space-4, 1rem);\r\n    }\r\n}", "/* ===== RESPONSIVE REGISTER PAGE ===== */\r\n\r\n/* Help text styling */\r\n.ant-form-item-explain {\r\n    color: #6b7280 !important;\r\n    font-size: 0.875rem !important;\r\n    margin-top: 0.5rem !important;\r\n    display: flex !important;\r\n    align-items: center !important;\r\n    gap: 0.25rem !important;\r\n}\r\n\r\n/* Auto-generated email field styling */\r\n.form-input:disabled {\r\n    background-color: #f9fafb !important;\r\n    border-color: #e5e7eb !important;\r\n    color: #6b7280 !important;\r\n    cursor: not-allowed !important;\r\n}\r\n\r\n/* Payment description styling */\r\n.ant-form-item-explain-connected {\r\n    color: #059669 !important;\r\n    font-weight: 500 !important;\r\n}\r\n\r\n.register-container {\r\n    min-height: 100vh;\r\n    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    padding: 1rem;\r\n    font-family: 'Inter', 'Roboto', 'Nunito', system-ui, -apple-system, sans-serif;\r\n    position: relative;\r\n}\r\n\r\n.register-container::before {\r\n    content: '';\r\n    position: absolute;\r\n    top: 0;\r\n    left: 0;\r\n    right: 0;\r\n    bottom: 0;\r\n    background: radial-gradient(circle at 30% 20%, rgba(0, 123, 255, 0.1) 0%, transparent 50%),\r\n                radial-gradient(circle at 70% 80%, rgba(0, 86, 210, 0.1) 0%, transparent 50%);\r\n    z-index: 0;\r\n}\r\n\r\n.register-card {\r\n    width: 100%;\r\n    max-width: 480px;\r\n    background: rgba(255, 255, 255, 0.95);\r\n    backdrop-filter: blur(10px);\r\n    border: 1px solid rgba(255, 255, 255, 0.2);\r\n    border-radius: 1.5rem;\r\n    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);\r\n    padding: 2rem;\r\n    position: relative;\r\n    z-index: 1;\r\n    transition: all 0.3s ease;\r\n    max-height: 90vh;\r\n    overflow-y: auto;\r\n}\r\n\r\n.register-card:hover {\r\n    transform: translateY(-2px);\r\n    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.15);\r\n}\r\n\r\n/* ===== HEADER SECTION ===== */\r\n.register-header {\r\n    text-align: center;\r\n    margin-bottom: 2rem;\r\n}\r\n\r\n.register-logo {\r\n    width: 80px;\r\n    height: 80px;\r\n    margin: 0 auto 1.5rem;\r\n    display: block;\r\n    border-radius: 1rem;\r\n    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);\r\n    transition: all 0.3s ease;\r\n}\r\n\r\n.register-logo:hover {\r\n    transform: scale(1.05);\r\n    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.15);\r\n}\r\n\r\n.verification-icon {\r\n    width: 64px;\r\n    height: 64px;\r\n    background: rgba(0, 123, 255, 0.1);\r\n    border-radius: 50%;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    margin: 0 auto 1.5rem;\r\n}\r\n\r\n.verification-icon i {\r\n    font-size: 1.5rem;\r\n    color: #007BFF;\r\n}\r\n\r\n.register-title {\r\n    font-size: 1.875rem;\r\n    font-weight: 800;\r\n    color: #1f2937;\r\n    margin-bottom: 0.5rem;\r\n    background: linear-gradient(135deg, #007BFF 0%, #0056D2 100%);\r\n    -webkit-background-clip: text;\r\n    -webkit-text-fill-color: transparent;\r\n    background-clip: text;\r\n    line-height: 1.2;\r\n}\r\n\r\n.register-subtitle {\r\n    color: #6b7280;\r\n    margin-bottom: 2rem;\r\n    font-size: 0.875rem;\r\n    line-height: 1.5;\r\n}\r\n\r\n/* ===== OTP VERIFICATION STYLING ===== */\r\n.otp-instructions {\r\n    margin-bottom: 2rem;\r\n    width: 100%;\r\n}\r\n\r\n.otp-info-card {\r\n    background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);\r\n    border: 2px solid #bae6fd;\r\n    border-radius: 1rem;\r\n    padding: 1.5rem;\r\n    margin-bottom: 1.5rem;\r\n}\r\n\r\n.otp-info-title {\r\n    font-size: 1.125rem;\r\n    font-weight: 700;\r\n    color: #0c4a6e;\r\n    margin-bottom: 0.75rem;\r\n    display: flex;\r\n    align-items: center;\r\n    gap: 0.5rem;\r\n}\r\n\r\n.otp-info-text {\r\n    color: #0369a1;\r\n    font-size: 0.875rem;\r\n    margin-bottom: 1rem;\r\n    line-height: 1.5;\r\n}\r\n\r\n.otp-steps {\r\n    margin-bottom: 1rem;\r\n}\r\n\r\n.otp-step {\r\n    display: flex;\r\n    align-items: center;\r\n    gap: 0.75rem;\r\n    margin-bottom: 0.75rem;\r\n    padding: 0.5rem;\r\n    background: rgba(255, 255, 255, 0.6);\r\n    border-radius: 0.5rem;\r\n    border: 1px solid rgba(14, 165, 233, 0.2);\r\n}\r\n\r\n.step-number {\r\n    width: 1.5rem;\r\n    height: 1.5rem;\r\n    background: #0ea5e9;\r\n    color: white;\r\n    border-radius: 50%;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    font-size: 0.75rem;\r\n    font-weight: 700;\r\n    flex-shrink: 0;\r\n}\r\n\r\n.step-text {\r\n    color: #0c4a6e;\r\n    font-size: 0.875rem;\r\n    font-weight: 500;\r\n}\r\n\r\n.otp-help {\r\n    background: rgba(255, 255, 255, 0.8);\r\n    border-radius: 0.5rem;\r\n    padding: 0.75rem;\r\n    border: 1px solid rgba(14, 165, 233, 0.3);\r\n}\r\n\r\n.help-text {\r\n    color: #0369a1;\r\n    font-size: 0.8rem;\r\n    margin: 0;\r\n    line-height: 1.4;\r\n}\r\n\r\n.otp-input {\r\n    text-align: center;\r\n    font-size: 1.25rem;\r\n    font-weight: 700;\r\n    letter-spacing: 0.25rem;\r\n}\r\n\r\n/* ===== RESEND SECTION STYLING ===== */\r\n.resend-section {\r\n    margin-top: 1.5rem;\r\n    text-align: center;\r\n    padding: 1rem;\r\n    background: #f8fafc;\r\n    border-radius: 0.75rem;\r\n    border: 1px solid #e2e8f0;\r\n}\r\n\r\n.resend-text {\r\n    color: #64748b;\r\n    font-size: 0.875rem;\r\n    margin-bottom: 0.75rem;\r\n    font-weight: 500;\r\n}\r\n\r\n.resend-btn {\r\n    background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);\r\n    color: white;\r\n    border: none;\r\n    padding: 0.75rem 1.5rem;\r\n    border-radius: 0.5rem;\r\n    font-size: 0.875rem;\r\n    font-weight: 600;\r\n    cursor: pointer;\r\n    transition: all 0.3s ease;\r\n    box-shadow: 0 2px 4px rgba(245, 158, 11, 0.2);\r\n}\r\n\r\n.resend-btn:hover:not(:disabled) {\r\n    background: linear-gradient(135deg, #d97706 0%, #b45309 100%);\r\n    transform: translateY(-1px);\r\n    box-shadow: 0 4px 8px rgba(245, 158, 11, 0.3);\r\n}\r\n\r\n.resend-btn:disabled {\r\n    background: #d1d5db;\r\n    color: #9ca3af;\r\n    cursor: not-allowed;\r\n    transform: none;\r\n    box-shadow: none;\r\n}\r\n\r\n/* ===== FORM STYLING ===== */\r\n.register-form {\r\n    margin-bottom: 1.5rem;\r\n}\r\n\r\n.register-form .ant-form-item {\r\n    margin-bottom: 1.25rem;\r\n}\r\n\r\n.register-form .ant-form-item-label > label {\r\n    font-weight: 600;\r\n    color: #374151;\r\n    font-size: 0.875rem;\r\n    margin-bottom: 0.5rem;\r\n}\r\n\r\n.form-input {\r\n    width: 100%;\r\n    padding: 0.875rem 1rem;\r\n    border: 2px solid #e5e7eb;\r\n    border-radius: 0.75rem;\r\n    font-size: 0.875rem;\r\n    transition: all 0.2s ease;\r\n    background: #f9fafb;\r\n    color: #1f2937;\r\n    font-family: inherit;\r\n    line-height: 1.5;\r\n}\r\n\r\n.form-input:focus {\r\n    outline: none;\r\n    border-color: #007BFF;\r\n    background: #ffffff;\r\n    box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);\r\n    transform: translateY(-1px);\r\n}\r\n\r\n.form-input::placeholder {\r\n    color: #9ca3af;\r\n    font-size: 0.875rem;\r\n}\r\n\r\n.form-help-text {\r\n    margin-top: 0.375rem;\r\n    font-size: 0.75rem;\r\n    color: #6b7280;\r\n    font-style: italic;\r\n    line-height: 1.4;\r\n}\r\n\r\n/* ===== CLEAN FORM STYLING ===== */\r\n/* Remove all validation styling to prevent red X issues */\r\n\r\n/* ===== PHONE NUMBER HELP SECTION ===== */\r\n.phone-help-section {\r\n    margin-top: 0.5rem;\r\n}\r\n\r\n.form-help-text {\r\n    font-size: 0.75rem;\r\n    color: #6b7280;\r\n    margin-top: 0.25rem;\r\n}\r\n\r\n.otp-input {\r\n    text-align: center;\r\n    font-size: 1.125rem;\r\n    letter-spacing: 0.1em;\r\n    font-weight: 600;\r\n}\r\n\r\n.register-btn {\r\n    width: 100%;\r\n    background: linear-gradient(135deg, #007BFF 0%, #0056D2 100%);\r\n    color: white;\r\n    padding: 1rem 1.5rem;\r\n    border: none;\r\n    border-radius: 0.75rem;\r\n    font-weight: 600;\r\n    font-size: 0.875rem;\r\n    cursor: pointer;\r\n    transition: all 0.3s ease;\r\n    font-family: inherit;\r\n    box-shadow: 0 4px 14px 0 rgba(0, 123, 255, 0.25);\r\n    margin-top: 0.5rem;\r\n}\r\n\r\n.register-btn:hover {\r\n    transform: translateY(-2px);\r\n    box-shadow: 0 8px 25px 0 rgba(0, 123, 255, 0.35);\r\n    background: linear-gradient(135deg, #0056D2 0%, #004494 100%);\r\n}\r\n\r\n.register-btn:active {\r\n    transform: translateY(0);\r\n}\r\n\r\n.register-btn:disabled {\r\n    opacity: 0.6;\r\n    cursor: not-allowed;\r\n    transform: none;\r\n}\r\n\r\n/* ===== LOADING SPINNER ===== */\r\n.loading-spinner {\r\n    display: inline-block;\r\n    width: 16px;\r\n    height: 16px;\r\n    border: 2px solid rgba(255, 255, 255, 0.3);\r\n    border-radius: 50%;\r\n    border-top-color: #ffffff;\r\n    animation: spin 1s ease-in-out infinite;\r\n    margin-right: 8px;\r\n}\r\n\r\n@keyframes spin {\r\n    to {\r\n        transform: rotate(360deg);\r\n    }\r\n}\r\n\r\n\r\n\r\n/* ===== FOOTER SECTION ===== */\r\n.register-footer {\r\n    text-align: center;\r\n    padding-top: 1.5rem;\r\n    border-top: 1px solid #e5e7eb;\r\n}\r\n\r\n.register-footer p {\r\n    color: #6b7280;\r\n    font-size: 0.875rem;\r\n    margin: 0;\r\n}\r\n\r\n.register-link {\r\n    color: #007BFF;\r\n    font-weight: 600;\r\n    text-decoration: none;\r\n    transition: all 0.2s ease;\r\n}\r\n\r\n.register-link:hover {\r\n    color: #0056D2;\r\n    text-decoration: underline;\r\n}\r\n\r\n/* ===== RESPONSIVE DESIGN ===== */\r\n\r\n/* Mobile Devices (320px - 480px) */\r\n@media (max-width: 480px) {\r\n    .register-container {\r\n        padding: 0.75rem;\r\n        align-items: flex-start;\r\n        padding-top: 1rem;\r\n    }\r\n\r\n    .register-card {\r\n        max-width: 100%;\r\n        padding: 1.5rem;\r\n        border-radius: 1rem;\r\n        margin: 0;\r\n        max-height: 95vh;\r\n    }\r\n\r\n    .register-logo {\r\n        width: 64px;\r\n        height: 64px;\r\n        margin-bottom: 1rem;\r\n    }\r\n\r\n    .verification-icon {\r\n        width: 56px;\r\n        height: 56px;\r\n        margin-bottom: 1rem;\r\n    }\r\n\r\n    .verification-icon i {\r\n        font-size: 1.25rem;\r\n    }\r\n\r\n    .register-title {\r\n        font-size: 1.5rem;\r\n        margin-bottom: 0.5rem;\r\n    }\r\n\r\n    .register-subtitle {\r\n        font-size: 0.8rem;\r\n        margin-bottom: 1.5rem;\r\n    }\r\n\r\n    .register-form .ant-form-item {\r\n        margin-bottom: 1rem;\r\n    }\r\n\r\n    .form-input {\r\n        padding: 1rem;\r\n        font-size: 1rem;\r\n        border-radius: 0.5rem;\r\n    }\r\n\r\n    .register-btn {\r\n        padding: 1.125rem 1.5rem;\r\n        font-size: 1rem;\r\n        border-radius: 0.5rem;\r\n        width: 100%;\r\n        min-height: 48px; /* Touch-friendly minimum */\r\n        font-weight: 600;\r\n    }\r\n\r\n    .register-footer {\r\n        padding-top: 1.25rem;\r\n    }\r\n\r\n    .register-footer p {\r\n        font-size: 0.8rem;\r\n    }\r\n\r\n    .form-help-text {\r\n        font-size: 0.7rem;\r\n    }\r\n}\r\n\r\n/* Tablet Devices (481px - 768px) */\r\n@media (min-width: 481px) and (max-width: 768px) {\r\n    .register-container {\r\n        padding: 1rem;\r\n    }\r\n\r\n    .register-card {\r\n        max-width: 500px;\r\n        padding: 2rem;\r\n    }\r\n\r\n    .register-logo {\r\n        width: 72px;\r\n        height: 72px;\r\n    }\r\n\r\n    .verification-icon {\r\n        width: 60px;\r\n        height: 60px;\r\n    }\r\n\r\n    .register-title {\r\n        font-size: 1.75rem;\r\n    }\r\n\r\n    .form-input {\r\n        padding: 0.9375rem 1rem;\r\n        font-size: 0.9375rem;\r\n    }\r\n\r\n    .register-btn {\r\n        padding: 1.0625rem 1.5rem;\r\n        font-size: 0.9375rem;\r\n    }\r\n}\r\n\r\n/* Desktop Devices (769px - 1024px) */\r\n@media (min-width: 769px) and (max-width: 1024px) {\r\n    .register-card {\r\n        max-width: 520px;\r\n        padding: 2.5rem;\r\n    }\r\n\r\n    .register-logo {\r\n        width: 84px;\r\n        height: 84px;\r\n    }\r\n\r\n    .verification-icon {\r\n        width: 68px;\r\n        height: 68px;\r\n    }\r\n}\r\n\r\n/* Large Desktop (1025px+) */\r\n@media (min-width: 1025px) {\r\n    .register-card {\r\n        max-width: 540px;\r\n        padding: 3rem;\r\n    }\r\n\r\n    .register-logo {\r\n        width: 96px;\r\n        height: 96px;\r\n    }\r\n\r\n    .verification-icon {\r\n        width: 72px;\r\n        height: 72px;\r\n    }\r\n\r\n    .register-title {\r\n        font-size: 2rem;\r\n    }\r\n}\r\n\r\n/* Landscape Mobile */\r\n@media (max-width: 768px) and (orientation: landscape) {\r\n    .register-container {\r\n        padding: 0.75rem;\r\n        align-items: center;\r\n    }\r\n\r\n    .register-card {\r\n        padding: 1.5rem;\r\n        max-height: 85vh;\r\n    }\r\n\r\n    .register-logo {\r\n        width: 56px;\r\n        height: 56px;\r\n        margin-bottom: 0.75rem;\r\n    }\r\n\r\n    .verification-icon {\r\n        width: 48px;\r\n        height: 48px;\r\n        margin-bottom: 0.75rem;\r\n    }\r\n\r\n    .register-title {\r\n        font-size: 1.375rem;\r\n        margin-bottom: 0.375rem;\r\n    }\r\n\r\n    .register-subtitle {\r\n        margin-bottom: 1.25rem;\r\n    }\r\n\r\n    .register-form .ant-form-item {\r\n        margin-bottom: 0.875rem;\r\n    }\r\n}\r\n\r\n/* Very Small Screens */\r\n@media (max-width: 320px) {\r\n    .register-container {\r\n        padding: 0.5rem;\r\n    }\r\n\r\n    .register-card {\r\n        padding: 1rem;\r\n        border-radius: 0.75rem;\r\n    }\r\n\r\n    .register-logo {\r\n        width: 56px;\r\n        height: 56px;\r\n    }\r\n\r\n    .verification-icon {\r\n        width: 48px;\r\n        height: 48px;\r\n    }\r\n\r\n    .register-title {\r\n        font-size: 1.25rem;\r\n    }\r\n\r\n    .form-input {\r\n        padding: 0.875rem;\r\n        font-size: 0.875rem;\r\n    }\r\n\r\n    .register-btn {\r\n        padding: 1rem;\r\n        font-size: 0.875rem;\r\n    }\r\n}", "/* ===== RESPONSIVE HOME PAGE ===== */\r\n\r\n.Home {\r\n    background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 25%, #f8fafc 50%, #ffffff 100%);\r\n    min-height: 100vh;\r\n    font-family: 'Inter', 'Roboto', 'Nunito', system-ui, -apple-system, sans-serif;\r\n    position: relative;\r\n    overflow-x: hidden;\r\n    padding: 0 !important;\r\n    margin: 0 !important;\r\n}\r\n\r\n/* FORCE COMPACT SPACING FOR ALL SECTIONS */\r\n.Home section {\r\n    padding-top: 1rem !important;\r\n    padding-bottom: 1rem !important;\r\n    margin: 0 !important;\r\n}\r\n\r\n.Home .container {\r\n    padding-top: 0.5rem !important;\r\n    padding-bottom: 0.5rem !important;\r\n}\r\n\r\n/* Mobile compact spacing */\r\n@media (max-width: 640px) {\r\n    .Home section {\r\n        padding-top: 0.75rem !important;\r\n        padding-bottom: 0.75rem !important;\r\n    }\r\n\r\n    .Home .container {\r\n        padding-top: 0.25rem !important;\r\n        padding-bottom: 0.25rem !important;\r\n    }\r\n}\r\n\r\n/* Tablet compact spacing */\r\n@media (min-width: 641px) and (max-width: 1024px) {\r\n    .Home section {\r\n        padding-top: 1.25rem !important;\r\n        padding-bottom: 1.25rem !important;\r\n    }\r\n\r\n    .Home .container {\r\n        padding-top: 0.75rem !important;\r\n        padding-bottom: 0.75rem !important;\r\n    }\r\n}\r\n\r\n/* Desktop compact spacing */\r\n@media (min-width: 1025px) {\r\n    .Home section {\r\n        padding-top: 1.5rem !important;\r\n        padding-bottom: 1.5rem !important;\r\n    }\r\n\r\n    .Home .container {\r\n        padding-top: 1rem !important;\r\n        padding-bottom: 1rem !important;\r\n    }\r\n}\r\n\r\n/* AGGRESSIVE SPACING OVERRIDES */\r\n.Home .py-4 { padding-top: 0.5rem !important; padding-bottom: 0.5rem !important; }\r\n.Home .py-6 { padding-top: 0.75rem !important; padding-bottom: 0.75rem !important; }\r\n.Home .py-8 { padding-top: 1rem !important; padding-bottom: 1rem !important; }\r\n.Home .py-12 { padding-top: 1.5rem !important; padding-bottom: 1.5rem !important; }\r\n.Home .py-16 { padding-top: 2rem !important; padding-bottom: 2rem !important; }\r\n.Home .py-20 { padding-top: 2.5rem !important; padding-bottom: 2.5rem !important; }\r\n\r\n.Home .mb-2 { margin-bottom: 0.25rem !important; }\r\n.Home .mb-3 { margin-bottom: 0.5rem !important; }\r\n.Home .mb-4 { margin-bottom: 0.75rem !important; }\r\n.Home .mb-6 { margin-bottom: 1rem !important; }\r\n.Home .mb-8 { margin-bottom: 1.25rem !important; }\r\n.Home .mb-12 { margin-bottom: 1.5rem !important; }\r\n\r\n.Home .mt-8 { margin-top: 1rem !important; }\r\n\r\n.Home .space-y-3 > * + * { margin-top: 0.5rem !important; }\r\n.Home .space-y-4 > * + * { margin-top: 0.75rem !important; }\r\n.Home .space-y-5 > * + * { margin-top: 1rem !important; }\r\n.Home .space-y-6 > * + * { margin-top: 1.25rem !important; }\r\n\r\n.Home .gap-4 { gap: 0.75rem !important; }\r\n.Home .gap-6 { gap: 1rem !important; }\r\n.Home .gap-8 { gap: 1.25rem !important; }\r\n\r\n/* Mobile overrides */\r\n@media (max-width: 640px) {\r\n    .Home .py-4,\r\n    .Home .py-6,\r\n    .Home .py-8,\r\n    .Home .py-12,\r\n    .Home .py-16,\r\n    .Home .py-20 {\r\n        padding-top: 0.5rem !important;\r\n        padding-bottom: 0.5rem !important;\r\n    }\r\n\r\n    .Home .mb-6,\r\n    .Home .mb-8,\r\n    .Home .mb-12 {\r\n        margin-bottom: 0.5rem !important;\r\n    }\r\n\r\n    .Home .space-y-3 > * + *,\r\n    .Home .space-y-4 > * + *,\r\n    .Home .space-y-5 > * + *,\r\n    .Home .space-y-6 > * + * {\r\n        margin-top: 0.5rem !important;\r\n    }\r\n\r\n    .Home .gap-4,\r\n    .Home .gap-6,\r\n    .Home .gap-8 {\r\n        gap: 0.5rem !important;\r\n    }\r\n}\r\n\r\n/* FINAL SPACING FIXES */\r\n.Home {\r\n    line-height: 1.2 !important;\r\n}\r\n\r\n.Home * {\r\n    margin-top: 0 !important;\r\n    margin-bottom: 0 !important;\r\n}\r\n\r\n.Home section:first-of-type {\r\n    padding-top: 0.5rem !important;\r\n}\r\n\r\n.Home header + section {\r\n    margin-top: 0 !important;\r\n    padding-top: 0.5rem !important;\r\n}\r\n\r\n/* Remove all default margins and paddings that might be causing issues */\r\n.Home .relative,\r\n.Home .absolute,\r\n.Home .flex,\r\n.Home .grid {\r\n    margin: 0 !important;\r\n}\r\n\r\n/* Ensure containers don't add extra space */\r\n.Home .max-w-7xl,\r\n.Home .max-w-4xl,\r\n.Home .container {\r\n    margin-top: 0 !important;\r\n    margin-bottom: 0 !important;\r\n}\r\n\r\n/* ===== ENHANCED RESPONSIVE HEADER ===== */\r\n.nav-modern {\r\n    position: sticky;\r\n    top: 0;\r\n    z-index: 30;\r\n    backdrop-filter: blur(12px);\r\n    -webkit-backdrop-filter: blur(12px);\r\n    border-bottom: 1px solid rgba(59, 130, 246, 0.1);\r\n}\r\n\r\n/* Mobile-first responsive header */\r\n@media (max-width: 640px) {\r\n    .nav-modern {\r\n        padding: 0.5rem 1rem;\r\n    }\r\n\r\n    .nav-modern .flex {\r\n        height: 3.5rem;\r\n    }\r\n}\r\n\r\n@media (min-width: 641px) and (max-width: 768px) {\r\n    .nav-modern {\r\n        padding: 0.75rem 1.5rem;\r\n    }\r\n\r\n    .nav-modern .flex {\r\n        height: 4rem;\r\n    }\r\n}\r\n\r\n@media (min-width: 769px) {\r\n    .nav-modern {\r\n        padding: 1rem 2rem;\r\n    }\r\n\r\n    .nav-modern .flex {\r\n        height: 5rem;\r\n    }\r\n}\r\n\r\n/* ===== ENHANCED BUTTONS ===== */\r\n.btn-large {\r\n    padding: 0.75rem 1.5rem !important;\r\n    font-size: 0.9375rem !important;\r\n    font-weight: 600 !important;\r\n    border-radius: 0.75rem !important;\r\n    box-shadow: 0 4px 15px rgba(0, 123, 255, 0.3) !important;\r\n    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;\r\n    display: inline-flex !important;\r\n    align-items: center !important;\r\n    justify-content: center !important;\r\n}\r\n\r\n/* Responsive button sizing */\r\n@media (max-width: 640px) {\r\n    .btn-large {\r\n        padding: 0.875rem 1.25rem !important;\r\n        font-size: 0.875rem !important;\r\n        width: 100% !important;\r\n        margin-bottom: 0.75rem !important;\r\n    }\r\n}\r\n\r\n@media (min-width: 641px) and (max-width: 768px) {\r\n    .btn-large {\r\n        padding: 0.75rem 1.375rem !important;\r\n        font-size: 0.9rem !important;\r\n    }\r\n}\r\n\r\n.btn-large:hover {\r\n    transform: translateY(-2px) !important;\r\n    box-shadow: 0 8px 25px rgba(0, 123, 255, 0.4) !important;\r\n}\r\n\r\n.cta-section {\r\n    margin: 1.5rem 0;\r\n}\r\n\r\n/* Tablet Buttons */\r\n@media (min-width: 640px) {\r\n    .btn-large {\r\n        padding: 0.875rem 1.75rem !important;\r\n        font-size: 1rem !important;\r\n        border-radius: 0.8125rem !important;\r\n    }\r\n\r\n    .cta-section {\r\n        margin: 1.75rem 0;\r\n    }\r\n}\r\n\r\n@media (min-width: 768px) {\r\n    .btn-large {\r\n        padding: 1rem 2rem !important;\r\n        font-size: 1.125rem !important;\r\n        border-radius: 0.875rem !important;\r\n    }\r\n\r\n    .cta-section {\r\n        margin: 2rem 0;\r\n    }\r\n}\r\n\r\n/* ===== RESPONSIVE HERO SECTION ===== */\r\n.hero-section {\r\n    padding: 4rem 2rem;\r\n    text-align: center;\r\n    position: relative;\r\n    z-index: 2;\r\n}\r\n\r\n/* Mobile hero section */\r\n@media (max-width: 640px) {\r\n    .hero-section {\r\n        padding: 2rem 1rem;\r\n    }\r\n}\r\n\r\n@media (min-width: 641px) and (max-width: 768px) {\r\n    .hero-section {\r\n        padding: 3rem 1.5rem;\r\n    }\r\n}\r\n\r\n@media (min-width: 769px) and (max-width: 1024px) {\r\n    .hero-section {\r\n        padding: 3.5rem 2rem;\r\n    }\r\n}\r\n\r\n@media (min-width: 1025px) {\r\n    .hero-section {\r\n        padding: 4.5rem 2rem;\r\n    }\r\n}\r\n\r\n.hero-title {\r\n    font-size: 3.5rem;\r\n    font-weight: 800;\r\n    background: linear-gradient(135deg, #1e40af 0%, #3b82f6 50%, #06b6d4 100%);\r\n    -webkit-background-clip: text;\r\n    -webkit-text-fill-color: transparent;\r\n    background-clip: text;\r\n    margin-bottom: 1.5rem;\r\n    line-height: 1.1;\r\n}\r\n\r\n/* Responsive hero title */\r\n@media (max-width: 640px) {\r\n    .hero-title {\r\n        font-size: 2.25rem;\r\n        margin-bottom: 1rem;\r\n        line-height: 1.2;\r\n    }\r\n}\r\n\r\n@media (min-width: 641px) and (max-width: 768px) {\r\n    .hero-title {\r\n        font-size: 2.75rem;\r\n        margin-bottom: 1.25rem;\r\n    }\r\n}\r\n\r\n@media (min-width: 769px) and (max-width: 1024px) {\r\n    .hero-title {\r\n        font-size: 3rem;\r\n    }\r\n}\r\n\r\n@media (min-width: 1025px) {\r\n    .hero-title {\r\n        font-size: 3.75rem;\r\n    }\r\n}\r\n\r\n.hero-subtitle {\r\n    font-size: 1.25rem;\r\n    color: #64748b;\r\n    margin-bottom: 2rem;\r\n    max-width: 600px;\r\n    margin-left: auto;\r\n    margin-right: auto;\r\n    line-height: 1.6;\r\n}\r\n\r\n/* Responsive hero subtitle */\r\n@media (max-width: 640px) {\r\n    .hero-subtitle {\r\n        font-size: 1rem;\r\n        margin-bottom: 1.5rem;\r\n        max-width: 100%;\r\n        padding: 0 0.5rem;\r\n    }\r\n}\r\n\r\n@media (min-width: 641px) and (max-width: 768px) {\r\n    .hero-subtitle {\r\n        font-size: 1.125rem;\r\n        margin-bottom: 1.75rem;\r\n        max-width: 500px;\r\n    }\r\n}\r\n\r\n@media (min-width: 769px) and (max-width: 1024px) {\r\n    .hero-subtitle {\r\n        font-size: 1.1875rem;\r\n        max-width: 550px;\r\n    }\r\n}\r\n\r\n/* ===== COMPREHENSIVE RESPONSIVE LAYOUT ===== */\r\n\r\n/* Mobile-first container adjustments */\r\n.Home {\r\n    overflow-x: hidden;\r\n    width: 100%;\r\n}\r\n\r\n/* Section spacing responsive */\r\nsection {\r\n    padding-left: 1rem;\r\n    padding-right: 1rem;\r\n}\r\n\r\n@media (min-width: 640px) {\r\n    section {\r\n        padding-left: 1.5rem;\r\n        padding-right: 1.5rem;\r\n    }\r\n}\r\n\r\n@media (min-width: 768px) {\r\n    section {\r\n        padding-left: 2rem;\r\n        padding-right: 2rem;\r\n    }\r\n}\r\n\r\n@media (min-width: 1024px) {\r\n    section {\r\n        padding-left: 3rem;\r\n        padding-right: 3rem;\r\n    }\r\n}\r\n\r\n/* Grid responsive adjustments */\r\n.grid {\r\n    gap: 1rem;\r\n}\r\n\r\n@media (min-width: 640px) {\r\n    .grid {\r\n        gap: 1.5rem;\r\n    }\r\n}\r\n\r\n@media (min-width: 768px) {\r\n    .grid {\r\n        gap: 2rem;\r\n    }\r\n}\r\n\r\n@media (min-width: 1024px) {\r\n    .grid {\r\n        gap: 3rem;\r\n    }\r\n}\r\n\r\n/* Text responsive scaling */\r\n.text-responsive-xl {\r\n    font-size: 1.125rem;\r\n    line-height: 1.75rem;\r\n}\r\n\r\n@media (min-width: 640px) {\r\n    .text-responsive-xl {\r\n        font-size: 1.25rem;\r\n        line-height: 1.75rem;\r\n    }\r\n}\r\n\r\n@media (min-width: 768px) {\r\n    .text-responsive-xl {\r\n        font-size: 1.5rem;\r\n        line-height: 2rem;\r\n    }\r\n}\r\n\r\n/* Image responsive adjustments */\r\nimg {\r\n    max-width: 100%;\r\n    height: auto;\r\n}\r\n\r\n/* Button responsive improvements */\r\nbutton, .btn {\r\n    min-height: 44px; /* Touch-friendly minimum */\r\n    touch-action: manipulation;\r\n}\r\n\r\n@media (max-width: 640px) {\r\n    button, .btn {\r\n        width: 100%;\r\n        margin-bottom: 0.5rem;\r\n    }\r\n}\r\n\r\n/* Spacing utilities responsive */\r\n.space-y-responsive > * + * {\r\n    margin-top: 1rem;\r\n}\r\n\r\n@media (min-width: 640px) {\r\n    .space-y-responsive > * + * {\r\n        margin-top: 1.5rem;\r\n    }\r\n}\r\n\r\n@media (min-width: 768px) {\r\n    .space-y-responsive > * + * {\r\n        margin-top: 2rem;\r\n    }\r\n}\r\n\r\n/* ===== MOBILE SPECIFIC IMPROVEMENTS ===== */\r\n@media (max-width: 640px) {\r\n    /* Hide complex animations on mobile for performance */\r\n    .motion-reduce {\r\n        animation: none !important;\r\n        transition: none !important;\r\n    }\r\n\r\n    /* Improve touch targets */\r\n    a, button, [role=\"button\"] {\r\n        min-height: 48px;\r\n        min-width: 48px;\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: center;\r\n    }\r\n\r\n    /* Reduce motion for better mobile experience */\r\n    * {\r\n        animation-duration: 0.3s !important;\r\n        transition-duration: 0.3s !important;\r\n    }\r\n\r\n    /* Better mobile typography */\r\n    h1, h2, h3, h4, h5, h6 {\r\n        line-height: 1.2;\r\n        word-wrap: break-word;\r\n        hyphens: auto;\r\n    }\r\n\r\n    /* Mobile-friendly spacing */\r\n    .container {\r\n        padding-left: 1rem;\r\n        padding-right: 1rem;\r\n    }\r\n\r\n    /* Stack elements vertically on mobile */\r\n    .flex-col-mobile {\r\n        flex-direction: column;\r\n    }\r\n\r\n    .flex-col-mobile > * {\r\n        width: 100%;\r\n        margin-bottom: 0.75rem;\r\n    }\r\n}\r\n\r\n/* ===== TABLET SPECIFIC IMPROVEMENTS ===== */\r\n@media (min-width: 641px) and (max-width: 1024px) {\r\n    /* Tablet-optimized spacing */\r\n    .container {\r\n        padding-left: 2rem;\r\n        padding-right: 2rem;\r\n    }\r\n\r\n    /* Better tablet grid layouts */\r\n    .grid-tablet-2 {\r\n        grid-template-columns: repeat(2, 1fr);\r\n    }\r\n\r\n    /* Tablet typography scaling */\r\n    h1 { font-size: 2.5rem; }\r\n    h2 { font-size: 2rem; }\r\n    h3 { font-size: 1.75rem; }\r\n\r\n    /* Tablet button sizing */\r\n    button, .btn {\r\n        padding: 0.75rem 1.5rem;\r\n        font-size: 1rem;\r\n    }\r\n}\r\n\r\n/* ===== LAPTOP/DESKTOP IMPROVEMENTS ===== */\r\n@media (min-width: 1025px) {\r\n    /* Desktop-optimized spacing */\r\n    .container {\r\n        padding-left: 3rem;\r\n        padding-right: 3rem;\r\n    }\r\n\r\n    /* Desktop typography scaling */\r\n    h1 { font-size: 3.5rem; }\r\n    h2 { font-size: 2.5rem; }\r\n    h3 { font-size: 2rem; }\r\n\r\n    /* Desktop button sizing */\r\n    button, .btn {\r\n        padding: 1rem 2rem;\r\n        font-size: 1.125rem;\r\n    }\r\n\r\n    /* Better hover effects on desktop */\r\n    button:hover, .btn:hover {\r\n        transform: translateY(-2px);\r\n        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);\r\n    }\r\n}\r\n\r\n/* ===== SPACING FIXES FOR ALL DEVICES ===== */\r\n\r\n/* Remove excessive spacing between sections */\r\nsection + section {\r\n    margin-top: 0 !important;\r\n}\r\n\r\n/* Compact section spacing */\r\n.Home section {\r\n    padding-top: 2rem;\r\n    padding-bottom: 2rem;\r\n}\r\n\r\n@media (min-width: 640px) {\r\n    .Home section {\r\n        padding-top: 3rem;\r\n        padding-bottom: 3rem;\r\n    }\r\n}\r\n\r\n@media (min-width: 768px) {\r\n    .Home section {\r\n        padding-top: 4rem;\r\n        padding-bottom: 4rem;\r\n    }\r\n}\r\n\r\n@media (min-width: 1024px) {\r\n    .Home section {\r\n        padding-top: 5rem;\r\n        padding-bottom: 5rem;\r\n    }\r\n}\r\n\r\n/* Reduce header to content gap */\r\n.Home header + section {\r\n    margin-top: 0;\r\n    padding-top: 1rem;\r\n}\r\n\r\n@media (min-width: 640px) {\r\n    .Home header + section {\r\n        padding-top: 1.5rem;\r\n    }\r\n}\r\n\r\n@media (min-width: 768px) {\r\n    .Home header + section {\r\n        padding-top: 2rem;\r\n    }\r\n}\r\n\r\n/* Compact hero section */\r\n.Home section:first-of-type {\r\n    min-height: 50vh;\r\n    padding-top: 1rem;\r\n    padding-bottom: 1rem;\r\n}\r\n\r\n@media (min-width: 640px) {\r\n    .Home section:first-of-type {\r\n        min-height: 60vh;\r\n        padding-top: 1.5rem;\r\n        padding-bottom: 1.5rem;\r\n    }\r\n}\r\n\r\n@media (min-width: 768px) {\r\n    .Home section:first-of-type {\r\n        min-height: 70vh;\r\n        padding-top: 2rem;\r\n        padding-bottom: 2rem;\r\n    }\r\n}\r\n\r\n@media (min-width: 1024px) {\r\n    .Home section:first-of-type {\r\n        min-height: 75vh;\r\n        padding-top: 2.5rem;\r\n        padding-bottom: 2.5rem;\r\n    }\r\n}\r\n\r\n/* Reduce margins between content blocks */\r\n.Home .space-y-4 > * + * {\r\n    margin-top: 1rem !important;\r\n}\r\n\r\n.Home .space-y-5 > * + * {\r\n    margin-top: 1.25rem !important;\r\n}\r\n\r\n.Home .space-y-6 > * + * {\r\n    margin-top: 1.5rem !important;\r\n}\r\n\r\n/* Compact grid gaps */\r\n.Home .grid {\r\n    gap: 1rem;\r\n}\r\n\r\n@media (min-width: 640px) {\r\n    .Home .grid {\r\n        gap: 1.5rem;\r\n    }\r\n}\r\n\r\n@media (min-width: 768px) {\r\n    .Home .grid {\r\n        gap: 2rem;\r\n    }\r\n}\r\n\r\n/* Remove excessive padding from containers */\r\n.Home .container {\r\n    padding-top: 0;\r\n    padding-bottom: 0;\r\n}\r\n\r\n/* Compact text spacing */\r\n.Home h1, .Home h2, .Home h3 {\r\n    margin-bottom: 0.5rem;\r\n}\r\n\r\n@media (min-width: 640px) {\r\n    .Home h1, .Home h2, .Home h3 {\r\n        margin-bottom: 0.75rem;\r\n    }\r\n}\r\n\r\n@media (min-width: 768px) {\r\n    .Home h1, .Home h2, .Home h3 {\r\n        margin-bottom: 1rem;\r\n    }\r\n}\r\n\r\n/* ===== MOBILE SPACING OPTIMIZATIONS ===== */\r\n@media (max-width: 640px) {\r\n    /* Ultra-compact mobile spacing */\r\n    .Home {\r\n        padding: 0;\r\n        margin: 0;\r\n    }\r\n\r\n    .Home section {\r\n        padding-top: 1rem !important;\r\n        padding-bottom: 1rem !important;\r\n        margin: 0 !important;\r\n    }\r\n\r\n    .Home .container {\r\n        padding-left: 1rem !important;\r\n        padding-right: 1rem !important;\r\n        padding-top: 0 !important;\r\n        padding-bottom: 0 !important;\r\n    }\r\n\r\n    /* Remove all excessive margins */\r\n    .Home .mb-6, .Home .mb-8, .Home .mb-12 {\r\n        margin-bottom: 1rem !important;\r\n    }\r\n\r\n    .Home .mt-6, .Home .mt-8, .Home .mt-12 {\r\n        margin-top: 1rem !important;\r\n    }\r\n\r\n    .Home .py-6, .Home .py-8, .Home .py-12, .Home .py-16, .Home .py-20 {\r\n        padding-top: 1rem !important;\r\n        padding-bottom: 1rem !important;\r\n    }\r\n\r\n    /* Compact hero section for mobile */\r\n    .Home section:first-of-type {\r\n        min-height: 80vh !important;\r\n        padding: 0.5rem !important;\r\n    }\r\n\r\n    /* Reduce space between elements */\r\n    .Home .space-y-4 > * + *,\r\n    .Home .space-y-5 > * + *,\r\n    .Home .space-y-6 > * + *,\r\n    .Home .space-y-8 > * + * {\r\n        margin-top: 0.75rem !important;\r\n    }\r\n}\r\n\r\n/* ===== TABLET SPACING OPTIMIZATIONS ===== */\r\n@media (min-width: 641px) and (max-width: 1024px) {\r\n    .Home section {\r\n        padding-top: 1.5rem !important;\r\n        padding-bottom: 1.5rem !important;\r\n    }\r\n\r\n    .Home .container {\r\n        padding-left: 1.5rem !important;\r\n        padding-right: 1.5rem !important;\r\n    }\r\n\r\n    /* Moderate spacing for tablets */\r\n    .Home .mb-12 {\r\n        margin-bottom: 1.5rem !important;\r\n    }\r\n\r\n    .Home .py-16, .Home .py-20 {\r\n        padding-top: 2rem !important;\r\n        padding-bottom: 2rem !important;\r\n    }\r\n\r\n    .Home section:first-of-type {\r\n        min-height: 70vh !important;\r\n    }\r\n}\r\n\r\n/* ===== REMOVE UNWANTED GAPS ===== */\r\n.Home * {\r\n    box-sizing: border-box;\r\n}\r\n\r\n/* Ensure no unexpected margins */\r\n.Home section,\r\n.Home div,\r\n.Home header {\r\n    margin-top: 0;\r\n    margin-bottom: 0;\r\n}\r\n\r\n/* Override any conflicting Tailwind spacing */\r\n.Home .py-12 { padding-top: 1.5rem !important; padding-bottom: 1.5rem !important; }\r\n.Home .py-16 { padding-top: 2rem !important; padding-bottom: 2rem !important; }\r\n.Home .py-20 { padding-top: 2.5rem !important; padding-bottom: 2.5rem !important; }\r\n\r\n@media (max-width: 640px) {\r\n    .Home .py-12,\r\n    .Home .py-16,\r\n    .Home .py-20 {\r\n        padding-top: 1rem !important;\r\n        padding-bottom: 1rem !important;\r\n    }\r\n}\r\n\r\n/* ===== FLOATING WHATSAPP BUTTON - BLUE & WHITE ===== */\r\n.floating-whatsapp {\r\n    position: fixed;\r\n    bottom: 1.5rem;\r\n    right: 1.5rem;\r\n    z-index: 1000;\r\n    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\r\n}\r\n\r\n.floating-whatsapp:hover {\r\n    transform: scale(1.1);\r\n    box-shadow: 0 20px 25px -5px rgba(59, 130, 246, 0.4);\r\n}\r\n\r\n/* Responsive positioning */\r\n@media (max-width: 640px) {\r\n    .floating-whatsapp {\r\n        bottom: 1rem;\r\n        right: 1rem;\r\n        width: 3.5rem;\r\n        height: 3.5rem;\r\n    }\r\n\r\n    .floating-whatsapp svg {\r\n        width: 1.75rem;\r\n        height: 1.75rem;\r\n    }\r\n\r\n    /* Hide tooltip on mobile */\r\n    .floating-whatsapp .tooltip {\r\n        display: none;\r\n    }\r\n}\r\n\r\n@media (min-width: 641px) and (max-width: 1024px) {\r\n    .floating-whatsapp {\r\n        bottom: 1.25rem;\r\n        right: 1.25rem;\r\n    }\r\n}\r\n\r\n/* ===== PREMIUM REVIEW CARDS ===== */\r\n.review-card {\r\n    background: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%);\r\n    border: 1px solid rgba(59, 130, 246, 0.1);\r\n    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);\r\n}\r\n\r\n.review-card:hover {\r\n    transform: translateY(-4px);\r\n    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.15);\r\n    border-color: rgba(59, 130, 246, 0.2);\r\n}\r\n\r\n.review-card .profile-image {\r\n    transition: all 0.3s ease;\r\n}\r\n\r\n.review-card:hover .profile-image {\r\n    transform: scale(1.05);\r\n}\r\n\r\n/* Star animation */\r\n@keyframes starGlow {\r\n    0%, 100% { filter: drop-shadow(0 0 2px rgba(251, 191, 36, 0.5)); }\r\n    50% { filter: drop-shadow(0 0 8px rgba(251, 191, 36, 0.8)); }\r\n}\r\n\r\n.review-card .star-rating {\r\n    animation: starGlow 2s ease-in-out infinite;\r\n}\r\n\r\n/* Verified badge pulse */\r\n@keyframes verifiedPulse {\r\n    0%, 100% { opacity: 1; }\r\n    50% { opacity: 0.7; }\r\n}\r\n\r\n.verified-badge {\r\n    animation: verifiedPulse 3s ease-in-out infinite;\r\n}\r\n\r\n/* ===== PROFESSIONAL SPACING SYSTEM ===== */\r\n.Home {\r\n    line-height: 1.6;\r\n}\r\n\r\n.Home section {\r\n    margin: 0;\r\n    padding: 3rem 0;\r\n}\r\n\r\n.Home .container {\r\n    margin: 0 auto;\r\n    padding: 0 1rem;\r\n}\r\n\r\n/* Professional section spacing */\r\n@media (min-width: 640px) {\r\n    .Home section {\r\n        padding: 4rem 0;\r\n    }\r\n\r\n    .Home .container {\r\n        padding: 0 1.5rem;\r\n    }\r\n}\r\n\r\n@media (min-width: 768px) {\r\n    .Home section {\r\n        padding: 5rem 0;\r\n    }\r\n\r\n    .Home .container {\r\n        padding: 0 2rem;\r\n    }\r\n}\r\n\r\n@media (min-width: 1024px) {\r\n    .Home section {\r\n        padding: 6rem 0;\r\n    }\r\n\r\n    .Home .container {\r\n        padding: 0 2.5rem;\r\n    }\r\n}\r\n\r\n/* Hero section specific spacing */\r\n.Home section:first-of-type {\r\n    padding: 2rem 0 4rem 0;\r\n}\r\n\r\n@media (min-width: 640px) {\r\n    .Home section:first-of-type {\r\n        padding: 3rem 0 5rem 0;\r\n    }\r\n}\r\n\r\n@media (min-width: 768px) {\r\n    .Home section:first-of-type {\r\n        padding: 4rem 0 6rem 0;\r\n    }\r\n}\r\n\r\n@media (min-width: 1024px) {\r\n    .Home section:first-of-type {\r\n        padding: 5rem 0 7rem 0;\r\n    }\r\n}\r\n\r\n/* Professional element spacing */\r\n.Home .space-y-4 > * + * { margin-top: 1rem; }\r\n.Home .space-y-5 > * + * { margin-top: 1.25rem; }\r\n.Home .space-y-6 > * + * { margin-top: 1.5rem; }\r\n.Home .space-y-7 > * + * { margin-top: 1.75rem; }\r\n\r\n/* ===== SUPER RESPONSIVE DESIGN ===== */\r\n\r\n/* Extra Small Devices (320px - 479px) */\r\n@media (max-width: 479px) {\r\n    .Home {\r\n        font-size: 14px;\r\n    }\r\n\r\n    .Home .container {\r\n        padding-left: 0.75rem !important;\r\n        padding-right: 0.75rem !important;\r\n    }\r\n\r\n    .Home h1 {\r\n        font-size: 1.75rem !important;\r\n        line-height: 1.2 !important;\r\n    }\r\n\r\n    .Home h2 {\r\n        font-size: 1.5rem !important;\r\n    }\r\n\r\n    .Home p {\r\n        font-size: 0.875rem !important;\r\n        line-height: 1.5 !important;\r\n    }\r\n\r\n    .Home button {\r\n        min-height: 48px !important;\r\n        font-size: 0.875rem !important;\r\n        padding: 0.75rem 1rem !important;\r\n    }\r\n\r\n    .Home .grid {\r\n        gap: 0.75rem !important;\r\n    }\r\n}\r\n\r\n/* Small Devices (480px - 639px) */\r\n@media (min-width: 480px) and (max-width: 639px) {\r\n    .Home .container {\r\n        padding-left: 1rem !important;\r\n        padding-right: 1rem !important;\r\n    }\r\n\r\n    .Home h1 {\r\n        font-size: 2rem !important;\r\n        line-height: 1.2 !important;\r\n    }\r\n\r\n    .Home h2 {\r\n        font-size: 1.75rem !important;\r\n    }\r\n\r\n    .Home p {\r\n        font-size: 1rem !important;\r\n        line-height: 1.6 !important;\r\n    }\r\n\r\n    .Home button {\r\n        min-height: 48px !important;\r\n        font-size: 1rem !important;\r\n        padding: 0.875rem 1.25rem !important;\r\n    }\r\n}\r\n\r\n/* Medium Devices (640px - 767px) */\r\n@media (min-width: 640px) and (max-width: 767px) {\r\n    .Home .container {\r\n        padding-left: 1.25rem !important;\r\n        padding-right: 1.25rem !important;\r\n    }\r\n\r\n    .Home h1 {\r\n        font-size: 2.5rem !important;\r\n        line-height: 1.1 !important;\r\n    }\r\n\r\n    .Home h2 {\r\n        font-size: 2rem !important;\r\n    }\r\n\r\n    .Home p {\r\n        font-size: 1.125rem !important;\r\n        line-height: 1.6 !important;\r\n    }\r\n}\r\n\r\n/* Large Devices (768px - 1023px) */\r\n@media (min-width: 768px) and (max-width: 1023px) {\r\n    .Home .container {\r\n        padding-left: 1.5rem !important;\r\n        padding-right: 1.5rem !important;\r\n    }\r\n\r\n    .Home h1 {\r\n        font-size: 3rem !important;\r\n        line-height: 1.1 !important;\r\n    }\r\n\r\n    .Home h2 {\r\n        font-size: 2.25rem !important;\r\n    }\r\n\r\n    .Home p {\r\n        font-size: 1.25rem !important;\r\n        line-height: 1.6 !important;\r\n    }\r\n}\r\n\r\n/* Extra Large Devices (1024px+) */\r\n@media (min-width: 1024px) {\r\n    .Home .container {\r\n        padding-left: 2rem !important;\r\n        padding-right: 2rem !important;\r\n    }\r\n\r\n    .Home h1 {\r\n        font-size: 3.5rem !important;\r\n        line-height: 1.1 !important;\r\n    }\r\n\r\n    .Home h2 {\r\n        font-size: 2.5rem !important;\r\n    }\r\n\r\n    .Home p {\r\n        font-size: 1.375rem !important;\r\n        line-height: 1.6 !important;\r\n    }\r\n}\r\n\r\n/* Touch-friendly interactions */\r\n@media (hover: none) and (pointer: coarse) {\r\n    .Home button,\r\n    .Home a {\r\n        min-height: 48px !important;\r\n        min-width: 48px !important;\r\n    }\r\n\r\n    .Home .hover\\:scale-105:hover {\r\n        transform: none !important;\r\n    }\r\n}\r\n\r\n/* High DPI displays */\r\n@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {\r\n    .Home img {\r\n        image-rendering: -webkit-optimize-contrast;\r\n        image-rendering: crisp-edges;\r\n    }\r\n}\r\n\r\n/* Landscape orientation on mobile */\r\n@media (max-height: 500px) and (orientation: landscape) {\r\n    .Home section {\r\n        padding-top: 0.5rem !important;\r\n        padding-bottom: 0.5rem !important;\r\n    }\r\n\r\n    .Home h1 {\r\n        font-size: 2rem !important;\r\n    }\r\n\r\n    .Home .space-y-4 > * + *,\r\n    .Home .space-y-5 > * + *,\r\n    .Home .space-y-6 > * + *,\r\n    .Home .space-y-7 > * + * {\r\n        margin-top: 0.5rem !important;\r\n    }\r\n}\r\n\r\n/* ===== PROFESSIONAL CONTACT BUTTONS ===== */\r\n.contact-card {\r\n    background: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%);\r\n    border: 1px solid rgba(59, 130, 246, 0.1);\r\n    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\r\n}\r\n\r\n.contact-card:hover {\r\n    transform: translateY(-2px);\r\n    box-shadow: 0 10px 25px -5px rgba(59, 130, 246, 0.2);\r\n    border-color: rgba(59, 130, 246, 0.3);\r\n}\r\n\r\n.call-button {\r\n    background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);\r\n    box-shadow: 0 4px 15px rgba(59, 130, 246, 0.3);\r\n    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\r\n}\r\n\r\n.call-button:hover {\r\n    background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);\r\n    box-shadow: 0 8px 25px rgba(59, 130, 246, 0.4);\r\n    transform: translateY(-2px);\r\n}\r\n\r\n/* ===== NAVIGATION ===== */\r\n.nav-header {\r\n    background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 250, 252, 0.9) 100%);\r\n    backdrop-filter: blur(25px);\r\n    -webkit-backdrop-filter: blur(25px);\r\n    border-bottom: 1px solid rgba(0, 123, 255, 0.15);\r\n    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);\r\n    box-shadow: 0 8px 32px rgba(0, 123, 255, 0.12);\r\n    position: sticky;\r\n    top: 0;\r\n    z-index: 1000;\r\n    position: relative;\r\n    overflow: hidden;\r\n}\r\n\r\n.nav-header::before {\r\n    content: '';\r\n    position: absolute;\r\n    top: 0;\r\n    left: 0;\r\n    right: 0;\r\n    bottom: 0;\r\n    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);\r\n    pointer-events: none;\r\n    z-index: 0;\r\n}\r\n\r\n.nav-header > * {\r\n    position: relative;\r\n    z-index: 1;\r\n}\r\n\r\n/* Navigation responsive height */\r\n.nav-header .container > div {\r\n    height: 4rem; /* 64px */\r\n}\r\n\r\n@media (min-width: 768px) {\r\n    .nav-header .container > div {\r\n        height: 4.5rem; /* 72px */\r\n    }\r\n}\r\n\r\n@media (min-width: 1024px) {\r\n    .nav-header .container > div {\r\n        height: 5rem; /* 80px */\r\n    }\r\n}\r\n\r\n.container {\r\n    max-width: 1200px;\r\n    margin: 0 auto;\r\n    padding: 0 1rem;\r\n    width: 100%;\r\n}\r\n\r\n/* ===== RESPONSIVE CONTAINERS ===== */\r\n@media (min-width: 640px) {\r\n    .container {\r\n        padding: 0 1.5rem;\r\n    }\r\n}\r\n\r\n@media (min-width: 768px) {\r\n    .container {\r\n        padding: 0 2rem;\r\n    }\r\n}\r\n\r\n@media (min-width: 1024px) {\r\n    .container {\r\n        padding: 0 2.5rem;\r\n    }\r\n}\r\n\r\n@media (min-width: 1280px) {\r\n    .container {\r\n        padding: 0 3rem;\r\n    }\r\n}\r\n\r\n/* Navigation Items */\r\n.nav-item {\r\n    color: #374151;\r\n    font-weight: 600;\r\n    padding: 0.75rem 1rem;\r\n    border-radius: 0.5rem;\r\n    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\r\n    border: none;\r\n    background: transparent;\r\n    cursor: pointer;\r\n    font-size: 0.875rem;\r\n    text-decoration: none !important;\r\n    display: inline-block;\r\n    font-family: inherit;\r\n    position: relative;\r\n    overflow: hidden;\r\n}\r\n\r\n/* Tablet navigation items */\r\n@media (min-width: 768px) {\r\n    .nav-item {\r\n        padding: 0.875rem 1.25rem;\r\n        border-radius: 0.625rem;\r\n        font-size: 0.9375rem;\r\n    }\r\n}\r\n\r\n/* Desktop navigation items */\r\n@media (min-width: 1024px) {\r\n    .nav-item {\r\n        padding: 0.875rem 1.5rem;\r\n        border-radius: 0.75rem;\r\n        font-size: 1rem;\r\n    }\r\n}\r\n\r\n.nav-item::before {\r\n    content: '';\r\n    position: absolute;\r\n    top: 0;\r\n    left: -100%;\r\n    width: 100%;\r\n    height: 100%;\r\n    background: linear-gradient(90deg, transparent, rgba(0, 123, 255, 0.1), transparent);\r\n    transition: left 0.5s ease;\r\n}\r\n\r\n.nav-item:hover::before {\r\n    left: 100%;\r\n}\r\n\r\n.nav-item:hover {\r\n    color: #007BFF !important;\r\n    background: rgba(0, 123, 255, 0.08);\r\n    transform: translateY(-2px);\r\n    text-decoration: none !important;\r\n    box-shadow: 0 4px 12px rgba(0, 123, 255, 0.15);\r\n}\r\n\r\n.nav-item:active {\r\n    transform: translateY(-1px);\r\n    background: rgba(0, 123, 255, 0.12);\r\n}\r\n\r\n.nav-item:focus {\r\n    outline: 2px solid rgba(0, 123, 255, 0.4);\r\n    outline-offset: 2px;\r\n}\r\n\r\n/* Logo Styling */\r\n.logo-text {\r\n    font-size: 1.25rem;\r\n    font-weight: 900;\r\n    background: linear-gradient(135deg, #1f2937 0%, #374151 50%, #007BFF 100%);\r\n    -webkit-background-clip: text;\r\n    -webkit-text-fill-color: transparent;\r\n    background-clip: text;\r\n    letter-spacing: -0.02em;\r\n    transition: all 0.3s ease;\r\n}\r\n\r\n/* Tablet logo */\r\n@media (min-width: 640px) {\r\n    .logo-text {\r\n        font-size: 1.5rem;\r\n    }\r\n}\r\n\r\n/* Desktop logo */\r\n@media (min-width: 768px) {\r\n    .logo-text {\r\n        font-size: 1.75rem;\r\n    }\r\n}\r\n\r\n@media (min-width: 1024px) {\r\n    .logo-text {\r\n        font-size: 1.875rem;\r\n    }\r\n}\r\n\r\n.logo-text:hover {\r\n    background: linear-gradient(135deg, #007BFF 0%, #0056D2 50%, #4338CA 100%);\r\n    -webkit-background-clip: text;\r\n    -webkit-text-fill-color: transparent;\r\n    background-clip: text;\r\n    transform: scale(1.05);\r\n}\r\n\r\n.logo-accent {\r\n    background: linear-gradient(135deg, #007BFF 0%, #0056D2 100%);\r\n    -webkit-background-clip: text;\r\n    -webkit-text-fill-color: transparent;\r\n    background-clip: text;\r\n}\r\n\r\n/* Mobile Navigation */\r\n.mobile-nav {\r\n    background: white;\r\n    border-radius: 1rem;\r\n    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);\r\n    margin-top: 1rem;\r\n    padding: 1.5rem;\r\n    border: 1px solid rgba(0, 123, 255, 0.1);\r\n}\r\n\r\n.mobile-nav .nav-item {\r\n    display: block;\r\n    width: 100%;\r\n    text-align: center;\r\n    padding: 1.25rem 1rem;\r\n    margin-bottom: 0.75rem;\r\n    border-radius: 0.75rem;\r\n    font-size: 1.125rem;\r\n    font-weight: 600;\r\n    background: rgba(0, 123, 255, 0.05);\r\n    border: 1px solid rgba(0, 123, 255, 0.1);\r\n    color: #1f2937;\r\n}\r\n\r\n.mobile-nav .nav-item:hover {\r\n    background: rgba(0, 123, 255, 0.1);\r\n    color: #007BFF;\r\n    transform: translateY(-2px);\r\n}\r\n\r\n.mobile-nav .nav-item:last-child {\r\n    margin-bottom: 0;\r\n}\r\n\r\n/* ===== TYPOGRAPHY ===== */\r\n.hero-title {\r\n    font-size: 1.875rem;\r\n    font-weight: 800;\r\n    line-height: 1.2;\r\n    color: #1f2937;\r\n    margin-bottom: 1rem;\r\n    text-align: center;\r\n}\r\n\r\n.hero-subtitle {\r\n    font-size: 1rem;\r\n    line-height: 1.6;\r\n    color: #374151;\r\n    margin-bottom: 1.5rem;\r\n    font-weight: 500;\r\n    text-align: center;\r\n    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);\r\n}\r\n\r\n/* Tablet Typography */\r\n@media (min-width: 640px) {\r\n    .hero-title {\r\n        font-size: 2.25rem;\r\n        margin-bottom: 1.25rem;\r\n    }\r\n\r\n    .hero-subtitle {\r\n        font-size: 1.125rem;\r\n        margin-bottom: 1.75rem;\r\n    }\r\n}\r\n\r\n@media (min-width: 768px) {\r\n    .hero-title {\r\n        font-size: 2.75rem;\r\n        margin-bottom: 1.5rem;\r\n    }\r\n\r\n    .hero-subtitle {\r\n        font-size: 1.25rem;\r\n        margin-bottom: 2rem;\r\n    }\r\n}\r\n\r\n/* Desktop Typography */\r\n@media (min-width: 1024px) {\r\n    .hero-title {\r\n        font-size: 3.25rem;\r\n        text-align: left;\r\n    }\r\n\r\n    .hero-subtitle {\r\n        font-size: 1.375rem;\r\n        text-align: left;\r\n    }\r\n}\r\n\r\n@media (min-width: 1280px) {\r\n    .hero-title {\r\n        font-size: 3.75rem;\r\n    }\r\n\r\n    .hero-subtitle {\r\n        font-size: 1.5rem;\r\n    }\r\n}\r\n\r\n.text-gradient {\r\n    background: linear-gradient(135deg, #007BFF 0%, #0056D2 100%);\r\n    -webkit-background-clip: text;\r\n    -webkit-text-fill-color: transparent;\r\n    background-clip: text;\r\n    display: inline-block;\r\n}\r\n\r\n/* ===== HERO SECTION ===== */\r\n.hero-section {\r\n    padding: 5rem 1rem 3rem;\r\n    min-height: 90vh;\r\n    display: flex;\r\n    align-items: center;\r\n    background: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(248, 250, 252, 0.8) 100%);\r\n    backdrop-filter: blur(10px);\r\n    scroll-margin-top: 80px;\r\n    position: relative;\r\n    overflow: hidden;\r\n}\r\n\r\n.hero-section::before {\r\n    content: '';\r\n    position: absolute;\r\n    top: 0;\r\n    left: 0;\r\n    right: 0;\r\n    bottom: 0;\r\n    background:\r\n        radial-gradient(circle at 30% 70%, rgba(0, 123, 255, 0.1) 0%, transparent 40%),\r\n        radial-gradient(circle at 70% 30%, rgba(0, 86, 210, 0.08) 0%, transparent 40%);\r\n    pointer-events: none;\r\n    z-index: 0;\r\n}\r\n\r\n.hero-section > * {\r\n    position: relative;\r\n    z-index: 1;\r\n}\r\n\r\n.hero-grid {\r\n    display: grid;\r\n    grid-template-columns: 1fr;\r\n    gap: 2rem;\r\n    align-items: center;\r\n    max-width: 1200px;\r\n    margin: 0 auto;\r\n    width: 100%;\r\n    text-align: center;\r\n}\r\n\r\n.hero-content {\r\n    padding: 1rem 0;\r\n    order: 1;\r\n}\r\n\r\n/* Tablet Hero */\r\n@media (min-width: 768px) {\r\n    .hero-section {\r\n        padding: 6rem 1.5rem 4rem;\r\n        min-height: 95vh;\r\n    }\r\n\r\n    .hero-grid {\r\n        gap: 3rem;\r\n    }\r\n\r\n    .hero-content {\r\n        padding: 1.5rem 0;\r\n    }\r\n}\r\n\r\n/* Desktop Hero */\r\n@media (min-width: 1024px) {\r\n    .hero-section {\r\n        padding: 8rem 2rem 4rem;\r\n        min-height: 100vh;\r\n    }\r\n\r\n    .hero-grid {\r\n        grid-template-columns: 1fr 1fr;\r\n        gap: 4rem;\r\n        text-align: left;\r\n    }\r\n\r\n    .hero-content {\r\n        padding: 2rem 0;\r\n        order: 0;\r\n    }\r\n}\r\n\r\n.hero-badge {\r\n    display: inline-flex;\r\n    align-items: center;\r\n    padding: 0.75rem 1.25rem;\r\n    background: linear-gradient(135deg, rgba(0, 123, 255, 0.1) 0%, rgba(0, 86, 210, 0.15) 100%);\r\n    border: 1px solid rgba(0, 123, 255, 0.2);\r\n    backdrop-filter: blur(10px);\r\n    box-shadow: 0 4px 15px rgba(0, 123, 255, 0.1);\r\n    color: #007BFF;\r\n    border-radius: 1.5rem;\r\n    font-size: 0.75rem;\r\n    font-weight: 600;\r\n    margin-bottom: 1.5rem;\r\n    border: 1px solid rgba(0, 123, 255, 0.2);\r\n    transition: all 0.3s ease;\r\n    backdrop-filter: blur(10px);\r\n}\r\n\r\n/* Tablet Badge */\r\n@media (min-width: 640px) {\r\n    .hero-badge {\r\n        padding: 0.625rem 1.25rem;\r\n        font-size: 0.8125rem;\r\n        margin-bottom: 1.75rem;\r\n    }\r\n}\r\n\r\n@media (min-width: 768px) {\r\n    .hero-badge {\r\n        padding: 0.75rem 1.5rem;\r\n        font-size: 0.875rem;\r\n        margin-bottom: 2rem;\r\n        border-radius: 2rem;\r\n    }\r\n}\r\n\r\n/* ===== BUTTONS ===== */\r\n.cta-buttons {\r\n    display: flex;\r\n    gap: 1rem;\r\n    margin-top: 2rem;\r\n    flex-wrap: wrap;\r\n    align-items: center;\r\n}\r\n\r\n.btn {\r\n    display: inline-flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    gap: 0.75rem;\r\n    padding: 1rem 2rem;\r\n    border-radius: 0.75rem;\r\n    font-weight: 600;\r\n    font-size: 1rem;\r\n    transition: all 0.3s ease;\r\n    text-decoration: none !important;\r\n    border: none;\r\n    cursor: pointer;\r\n    white-space: nowrap;\r\n    min-height: 52px;\r\n    min-width: 160px;\r\n    position: relative;\r\n    overflow: hidden;\r\n    font-family: inherit;\r\n    line-height: 1.2;\r\n}\r\n\r\n.btn-primary {\r\n    background: linear-gradient(135deg, #007BFF 0%, #0056D2 100%);\r\n    color: white;\r\n    box-shadow: 0 4px 15px rgba(0, 123, 255, 0.3);\r\n    border: 2px solid transparent;\r\n}\r\n\r\n.btn-primary:hover {\r\n    transform: translateY(-3px);\r\n    box-shadow: 0 8px 25px rgba(0, 123, 255, 0.4);\r\n    color: white;\r\n}\r\n\r\n.btn-secondary {\r\n    background: white;\r\n    color: #007BFF;\r\n    border: 2px solid #007BFF;\r\n    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.btn-secondary:hover {\r\n    background: #007BFF;\r\n    color: white;\r\n    transform: translateY(-3px);\r\n    box-shadow: 0 8px 25px rgba(0, 123, 255, 0.3);\r\n}\r\n\r\n/* Button Icons */\r\n.btn svg {\r\n    width: 1.25rem;\r\n    height: 1.25rem;\r\n}\r\n\r\n/* ===== TRUST INDICATORS ===== */\r\n.trust-indicators {\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    gap: 1rem;\r\n    margin-top: 1.5rem;\r\n    flex-wrap: wrap;\r\n}\r\n\r\n.trust-indicator {\r\n    display: flex;\r\n    align-items: center;\r\n    gap: 0.5rem;\r\n    color: #6b7280;\r\n    font-size: 0.75rem;\r\n    font-weight: 600;\r\n    padding: 0.625rem 1rem;\r\n    background: rgba(0, 123, 255, 0.05);\r\n    border-radius: 1.5rem;\r\n    border: 1px solid rgba(0, 123, 255, 0.1);\r\n}\r\n\r\n.trust-indicator svg {\r\n    width: 1rem;\r\n    height: 1rem;\r\n}\r\n\r\n/* Hide third indicator on mobile */\r\n.trust-indicator:nth-child(3) {\r\n    display: none;\r\n}\r\n\r\n/* Tablet Trust Indicators */\r\n@media (min-width: 640px) {\r\n    .trust-indicators {\r\n        gap: 1.25rem;\r\n        margin-top: 1.75rem;\r\n    }\r\n\r\n    .trust-indicator {\r\n        gap: 0.625rem;\r\n        font-size: 0.8125rem;\r\n        padding: 0.75rem 1.125rem;\r\n    }\r\n\r\n    .trust-indicator svg {\r\n        width: 1.125rem;\r\n        height: 1.125rem;\r\n    }\r\n\r\n    /* Show third indicator on tablet */\r\n    .trust-indicator:nth-child(3) {\r\n        display: flex;\r\n    }\r\n}\r\n\r\n@media (min-width: 768px) {\r\n    .trust-indicators {\r\n        gap: 1.5rem;\r\n        margin-top: 2rem;\r\n    }\r\n\r\n    .trust-indicator {\r\n        gap: 0.75rem;\r\n        font-size: 0.875rem;\r\n        padding: 0.875rem 1.25rem;\r\n        border-radius: 1.75rem;\r\n    }\r\n\r\n    .trust-indicator svg {\r\n        width: 1.25rem;\r\n        height: 1.25rem;\r\n    }\r\n}\r\n\r\n/* Desktop Trust Indicators */\r\n@media (min-width: 1024px) {\r\n    .trust-indicators {\r\n        justify-content: flex-start;\r\n        gap: 2rem;\r\n        margin-top: 2.5rem;\r\n    }\r\n\r\n    .trust-indicator {\r\n        padding: 1rem 1.5rem;\r\n        border-radius: 2rem;\r\n        font-weight: 500;\r\n    }\r\n}\r\n\r\n/* ===== HERO IMAGE ===== */\r\n.hero-image {\r\n    position: relative;\r\n    max-width: 100%;\r\n    height: auto;\r\n    padding: 0.5rem;\r\n    order: 2;\r\n    margin-top: 1rem;\r\n}\r\n\r\n.hero-image img {\r\n    width: 100%;\r\n    height: auto;\r\n    border-radius: 1rem;\r\n    box-shadow: 0 15px 35px -8px rgba(0, 0, 0, 0.2);\r\n    transition: transform 0.3s ease;\r\n}\r\n\r\n.hero-image:hover img {\r\n    transform: scale(1.02);\r\n}\r\n\r\n/* Tablet Hero Image */\r\n@media (min-width: 640px) {\r\n    .hero-image {\r\n        padding: 0.75rem;\r\n        margin-top: 1.5rem;\r\n        max-width: 90%;\r\n        margin-left: auto;\r\n        margin-right: auto;\r\n    }\r\n\r\n    .hero-image img {\r\n        border-radius: 1.25rem;\r\n        box-shadow: 0 20px 40px -10px rgba(0, 0, 0, 0.22);\r\n    }\r\n}\r\n\r\n@media (min-width: 768px) {\r\n    .hero-image {\r\n        padding: 1rem;\r\n        margin-top: 2rem;\r\n        max-width: 85%;\r\n    }\r\n\r\n    .hero-image img {\r\n        border-radius: 1.5rem;\r\n        box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);\r\n    }\r\n}\r\n\r\n/* Desktop Hero Image */\r\n@media (min-width: 1024px) {\r\n    .hero-image {\r\n        order: 0;\r\n        margin-top: 0;\r\n        max-width: 100%;\r\n        padding: 1rem;\r\n    }\r\n}\r\n\r\n/* Floating elements */\r\n.floating-element {\r\n    position: absolute;\r\n    background: white;\r\n    border-radius: 1rem;\r\n    padding: 1rem;\r\n    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);\r\n    z-index: 10;\r\n}\r\n\r\n.floating-element svg {\r\n    width: 2rem;\r\n    height: 2rem;\r\n}\r\n\r\n/* ===== STATS SECTION ===== */\r\n.stats-section {\r\n    background: linear-gradient(135deg, #007BFF 0%, #0056D2 100%);\r\n    padding: 4rem 1rem;\r\n    color: white;\r\n    scroll-margin-top: 80px;\r\n}\r\n\r\n.stats-grid {\r\n    display: grid;\r\n    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\r\n    gap: 2rem;\r\n    max-width: 1200px;\r\n    margin: 0 auto;\r\n    text-align: center;\r\n}\r\n\r\n.stat-item {\r\n    display: flex;\r\n    flex-direction: column;\r\n    align-items: center;\r\n    gap: 0.5rem;\r\n}\r\n\r\n.stat-number {\r\n    font-size: clamp(2rem, 4vw, 3rem);\r\n    font-weight: 700;\r\n    line-height: 1;\r\n}\r\n\r\n.stat-text {\r\n    font-size: 0.875rem;\r\n    opacity: 0.9;\r\n    font-weight: 500;\r\n}\r\n\r\n/* ===== ABOUT SECTION ===== */\r\n.about-section {\r\n    padding: 6rem 1rem;\r\n    background: white;\r\n    scroll-margin-top: 80px;\r\n}\r\n\r\n.about-grid {\r\n    display: grid;\r\n    grid-template-columns: 1fr 1fr;\r\n    gap: 4rem;\r\n    align-items: center;\r\n    max-width: 1200px;\r\n    margin: 0 auto;\r\n}\r\n\r\n.about-content {\r\n    order: 2;\r\n}\r\n\r\n.about-image {\r\n    order: 1;\r\n}\r\n\r\n.about-title {\r\n    font-size: clamp(2rem, 4vw, 3rem);\r\n    font-weight: 800;\r\n    color: #1f2937;\r\n    margin-bottom: 1.5rem;\r\n    line-height: 1.2;\r\n}\r\n\r\n.about-text {\r\n    font-size: 1.125rem;\r\n    color: #6b7280;\r\n    line-height: 1.7;\r\n    margin-bottom: 2rem;\r\n}\r\n\r\n.about-image img {\r\n    width: 100%;\r\n    height: auto;\r\n    border-radius: 1rem;\r\n    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n/* ===== REVIEWS SECTION ===== */\r\n.reviews-section {\r\n    padding: 6rem 1rem;\r\n    background: #f8fafc;\r\n    scroll-margin-top: 80px;\r\n}\r\n\r\n.reviews-container {\r\n    max-width: 1200px;\r\n    margin: 0 auto;\r\n}\r\n\r\n.reviews-title {\r\n    font-size: clamp(2rem, 4vw, 3rem);\r\n    font-weight: 800;\r\n    text-align: center;\r\n    color: #1f2937;\r\n    margin-bottom: 3rem;\r\n}\r\n\r\n.reviews-grid {\r\n    display: grid;\r\n    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));\r\n    gap: 2rem;\r\n}\r\n\r\n.review-card {\r\n    background: white;\r\n    border-radius: 1rem;\r\n    padding: 2rem;\r\n    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);\r\n    transition: all 0.3s ease;\r\n    display: flex;\r\n    flex-direction: column;\r\n    height: 100%;\r\n}\r\n\r\n.review-card:hover {\r\n    transform: translateY(-4px);\r\n    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.review-rating {\r\n    margin-bottom: 1rem;\r\n}\r\n\r\n.review-text {\r\n    flex: 1;\r\n    font-size: 0.875rem;\r\n    line-height: 1.6;\r\n    color: #6b7280;\r\n    margin-bottom: 1.5rem;\r\n}\r\n\r\n.review-divider {\r\n    height: 1px;\r\n    background: #e5e7eb;\r\n    margin-bottom: 1rem;\r\n}\r\n\r\n.review-author {\r\n    font-weight: 600;\r\n    color: #007BFF;\r\n    font-size: 0.875rem;\r\n}\r\n\r\n/* ===== CONTACT SECTION ===== */\r\n.contact-section {\r\n    padding: 6rem 1rem;\r\n    background: white;\r\n    scroll-margin-top: 80px;\r\n}\r\n\r\n.contact-container {\r\n    max-width: 800px;\r\n    margin: 0 auto;\r\n    text-align: center;\r\n}\r\n\r\n.contact-title {\r\n    font-size: clamp(2rem, 4vw, 3rem);\r\n    font-weight: 800;\r\n    color: #1f2937;\r\n    margin-bottom: 1rem;\r\n}\r\n\r\n.contact-subtitle {\r\n    font-size: 1.125rem;\r\n    color: #6b7280;\r\n    margin-bottom: 3rem;\r\n}\r\n\r\n.contact-form {\r\n    display: grid;\r\n    gap: 1.5rem;\r\n    max-width: 600px;\r\n    margin: 0 auto;\r\n}\r\n\r\n.form-group {\r\n    display: flex;\r\n    flex-direction: column;\r\n    text-align: left;\r\n}\r\n\r\n.form-label {\r\n    font-weight: 600;\r\n    color: #374151;\r\n    margin-bottom: 0.5rem;\r\n    font-size: 0.875rem;\r\n}\r\n\r\n.form-input {\r\n    padding: 0.875rem 1rem;\r\n    border: 2px solid #e5e7eb;\r\n    border-radius: 0.5rem;\r\n    font-size: 0.875rem;\r\n    transition: all 0.2s ease;\r\n    background: white;\r\n}\r\n\r\n.form-input:focus {\r\n    outline: none;\r\n    border-color: #007BFF;\r\n    box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);\r\n}\r\n\r\n.form-textarea {\r\n    min-height: 120px;\r\n    resize: vertical;\r\n}\r\n\r\n.form-submit {\r\n    background: linear-gradient(135deg, #007BFF 0%, #0056D2 100%);\r\n    color: white;\r\n    padding: 1rem 2rem;\r\n    border: none;\r\n    border-radius: 0.5rem;\r\n    font-weight: 600;\r\n    cursor: pointer;\r\n    transition: all 0.2s ease;\r\n    margin-top: 1rem;\r\n}\r\n\r\n.form-submit:hover {\r\n    transform: translateY(-2px);\r\n    box-shadow: 0 8px 25px 0 rgba(0, 123, 255, 0.35);\r\n}\r\n\r\n/* ===== FOOTER ===== */\r\n.footer {\r\n    background: #1f2937;\r\n    color: white;\r\n    padding: 3rem 1rem 2rem;\r\n    text-align: center;\r\n}\r\n\r\n.footer-content {\r\n    max-width: 1200px;\r\n    margin: 0 auto;\r\n}\r\n\r\n.footer-text {\r\n    font-size: 0.875rem;\r\n    color: #9ca3af;\r\n}\r\n\r\n/* ===== RESPONSIVE DESIGN ===== */\r\n/* Note: Mobile-first responsive design implemented above */\r\n"], "names": [], "sourceRoot": ""}