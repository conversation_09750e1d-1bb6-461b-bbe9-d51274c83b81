"use strict";(self.webpackChunkclient=self.webpackChunkclient||[]).push([[743],{1743:(e,r,s)=>{s.r(r),s.d(r,{default:()=>c});var t=s(2791),n=s(5526),o=s(7689),l=s(184);class a extends t.Component{constructor(e){super(e),this.state={hasError:!1,error:null,errorInfo:null}}static getDerivedStateFromError(e){return{hasError:!0}}componentDidCatch(e,r){this.setState({error:e,errorInfo:r}),console.error("Ranking Error:",e,r)}render(){return this.state.hasError?(0,l.jsx)(i,{error:this.state.error,resetError:()=>this.setState({hasError:!1,error:null,errorInfo:null})}):this.props.children}}const i=e=>{let{error:r,resetError:s}=e;const t=(0,o.s0)();return(0,l.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 to-purple-50 flex items-center justify-center p-4",children:(0,l.jsxs)("div",{className:"max-w-md w-full bg-white rounded-2xl shadow-xl p-8 text-center",children:[(0,l.jsx)("div",{className:"w-20 h-20 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-6",children:(0,l.jsx)(n.bS7,{className:"w-10 h-10 text-red-600"})}),(0,l.jsx)("h1",{className:"text-2xl font-bold text-gray-900 mb-4",children:"Ranking System Error"}),(0,l.jsx)("p",{className:"text-gray-600 mb-6",children:"We encountered an error while loading the rankings. This might be a temporary issue with the server connection."}),(0,l.jsxs)("div",{className:"space-y-3",children:[(0,l.jsxs)("button",{onClick:()=>{s(),window.location.reload()},className:"w-full bg-blue-600 hover:bg-blue-700 text-white font-semibold py-3 px-4 rounded-lg transition-colors duration-200 flex items-center justify-center space-x-2",children:[(0,l.jsx)(n.T8D,{className:"w-5 h-5"}),(0,l.jsx)("span",{children:"Refresh Rankings"})]}),(0,l.jsxs)("button",{onClick:()=>{t("/user/hub")},className:"w-full bg-gray-100 hover:bg-gray-200 text-gray-700 font-semibold py-3 px-4 rounded-lg transition-colors duration-200 flex items-center justify-center space-x-2",children:[(0,l.jsx)(n.diY,{className:"w-5 h-5"}),(0,l.jsx)("span",{children:"Go to Hub"})]})]}),!1]})})},c=a}}]);
//# sourceMappingURL=743.15c68485.chunk.js.map