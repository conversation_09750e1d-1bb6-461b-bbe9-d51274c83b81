"use strict";(self.webpackChunkclient=self.webpackChunkclient||[]).push([[419],{272:(e,t,s)=>{s.d(t,{$s:()=>d,I1:()=>n,Ss:()=>i,cq:()=>a,dM:()=>l,uH:()=>o});const{default:r}=s(3371),a=async e=>{try{return(await r.post("/api/reports/add-report",e)).data}catch(t){return t.response.data}},n=async e=>{try{return(await r.post("/api/reports/get-all-reports",e)).data}catch(t){return t.response.data}},i=async e=>{try{return(await r.post("/api/reports/get-all-reports-by-user",e)).data}catch(t){return t.response.data}},o=async e=>{try{return(await r.get("/api/reports/get-all-reports-for-ranking")).data}catch(t){return t.response.data}},l=async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};try{const t=new URLSearchParams;e.limit&&t.append("limit",e.limit),e.classFilter&&t.append("classFilter",e.classFilter),e.levelFilter&&t.append("levelFilter",e.levelFilter),e.seasonFilter&&t.append("seasonFilter",e.seasonFilter),e.includeInactive&&t.append("includeInactive",e.includeInactive);return(await r.get("/api/quiz/xp-leaderboard?".concat(t.toString()))).data}catch(t){return t.response.data}},d=async function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:5;try{return(await r.get("/api/quiz/user-ranking/".concat(e,"?context=").concat(t))).data}catch(s){return s.response.data}}},2150:(e,t,s)=>{s.r(t),s.d(t,{default:()=>S});var r=s(1413),a=s(7027),n=s(2791),i=s(9434),o=s(7689),l=s(1652),d=s(272),c=s(8247),x=s(184);const m=function(e){let{examData:t,setView:s,startTimer:r,questions:a=[]}=e;const n=(0,o.s0)();return(0,x.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100",children:[(0,x.jsx)("div",{className:"bg-gradient-to-r from-blue-600 to-blue-700 shadow-lg",children:(0,x.jsx)("div",{className:"max-w-4xl mx-auto px-4 py-8",children:(0,x.jsx)("div",{className:"text-center",children:(0,x.jsx)("h1",{className:"text-3xl font-bold text-white",children:"Instructions"})})})}),(0,x.jsx)("div",{className:"max-w-2xl mx-auto px-4 py-12",children:(0,x.jsxs)("div",{className:"bg-white rounded-2xl shadow-xl border-0 p-8 mb-8",children:[(0,x.jsxs)("div",{className:"text-center mb-8",children:[(0,x.jsx)("h1",{className:"text-4xl font-bold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent mb-4",children:t.name}),(0,x.jsx)("p",{className:"text-lg text-gray-600 font-medium",children:"Challenge your brain, Beat the rest"}),(0,x.jsxs)("div",{className:"mt-4 p-3 bg-gray-100 rounded-lg text-sm text-gray-600",children:[(0,x.jsxs)("p",{children:[(0,x.jsx)("strong",{children:"Questions Available:"})," ",a.length]}),(0,x.jsxs)("p",{children:[(0,x.jsx)("strong",{children:"Exam ID:"})," ",t._id]})]})]}),(0,x.jsxs)("div",{className:"grid grid-cols-2 gap-6 mb-8",children:[(0,x.jsxs)("div",{className:"text-center p-6 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl shadow-lg",children:[(0,x.jsxs)("div",{className:"text-3xl font-bold text-white mb-2",children:[Math.round(t.duration/60)," min"]}),(0,x.jsx)("div",{className:"text-sm font-semibold text-blue-100",children:"Duration"})]}),(0,x.jsxs)("div",{className:"text-center p-6 bg-gradient-to-br from-green-500 to-green-600 rounded-xl shadow-lg",children:[(0,x.jsx)("div",{className:"text-3xl font-bold text-white mb-2",children:t.totalMarks}),(0,x.jsx)("div",{className:"text-sm font-semibold text-green-100",children:"Total Marks"})]})]}),(0,x.jsxs)("div",{className:"flex justify-center gap-4",children:[(0,x.jsx)("button",{className:"px-6 py-3 bg-gradient-to-r from-gray-500 to-gray-600 text-white rounded-xl font-bold hover:from-gray-600 hover:to-gray-700 transform hover:scale-105 transition-all duration-300 shadow-lg",onClick:()=>n("/user/quiz"),children:"Cancel"}),(0,x.jsx)("button",{className:"px-8 py-3 rounded-xl font-bold transform transition-all duration-300 shadow-lg ".concat(a.length>0?"bg-gradient-to-r from-blue-600 to-indigo-600 text-white hover:from-blue-700 hover:to-indigo-700 hover:scale-105":"bg-gray-400 text-gray-600 cursor-not-allowed"),onClick:()=>{a.length>0?(console.log("Starting quiz with",a.length,"questions"),r(),s("questions")):alert("Cannot start quiz: No questions available!")},disabled:0===a.length,children:a.length>0?"Start Quiz":"No Questions Available"})]})]})})]})},p=s.p+"static/media/pass.c05c9976422211f31258.gif";var u=s(1425),h=s.n(u),g=s(3339);const b=s.p+"static/media/pass.38072284fdf8e62f7bec.mp3",f=s.p+"static/media/fail.7b762844f8cf7b9c907f.mp3";var v=s(7870),y=s(3791),j=s(6042),w=s(5526);const N=e=>{let{xpData:t,className:s=""}=e;const[r,a]=(0,n.useState)(!1),[i,o]=(0,n.useState)(0);if((0,n.useEffect)((()=>{if(null!==t&&void 0!==t&&t.xpAwarded){const e=2e3,s=60,r=t.xpAwarded/s;let a=0;const n=setInterval((()=>{a+=r,a>=t.xpAwarded?(o(t.xpAwarded),clearInterval(n)):o(Math.floor(a))}),e/s);return()=>clearInterval(n)}}),[t]),(0,n.useEffect)((()=>{if(null!==t&&void 0!==t&&t.levelUp){const e=()=>{try{const e=new window.AudioContext;(()=>{[[262,330,392],[294,370,440],[330,415,494],[349,440,523]].forEach(((t,s)=>{setTimeout((()=>{t.forEach((t=>{const s=e.createOscillator(),r=e.createGain();s.connect(r),r.connect(e.destination),s.frequency.setValueAtTime(t,e.currentTime),s.type="triangle",r.gain.setValueAtTime(0,e.currentTime),r.gain.linearRampToValueAtTime(.15,e.currentTime+.02),r.gain.exponentialRampToValueAtTime(.001,e.currentTime+.6),s.start(e.currentTime),s.stop(e.currentTime+.6)}))}),200*s)})),setTimeout((()=>{[659,784,988,1175].forEach(((t,s)=>{setTimeout((()=>{const s=e.createOscillator(),r=e.createGain();s.connect(r),r.connect(e.destination),s.frequency.setValueAtTime(t,e.currentTime),s.type="sine",r.gain.setValueAtTime(0,e.currentTime),r.gain.linearRampToValueAtTime(.2,e.currentTime+.01),r.gain.exponentialRampToValueAtTime(.001,e.currentTime+.3),s.start(e.currentTime),s.stop(e.currentTime+.3)}),100*s)}))}),800)})(),console.log("\ud83c\udfb5 Level Up sound played!")}catch(e){console.log("Level-up audio not supported:",e)}};setTimeout(e,600)}}),[null===t||void 0===t?void 0:t.levelUp]),!t)return null;const{xpAwarded:l=0,xpBreakdown:d={},levelUp:c=!1,newLevel:m=1,newTotalXP:p=0,currentStreak:u=0,achievements:h=[]}=t;return(0,x.jsxs)("div",{className:"bg-gradient-to-br from-purple-50 to-indigo-50 rounded-2xl p-6 border-2 border-purple-200 shadow-lg ".concat(s),children:[(0,x.jsx)("style",{jsx:!0,children:"\n        @keyframes levelUpGlow {\n          0% { box-shadow: 0 25px 50px -12px rgba(251, 191, 36, 0.5), 0 0 20px rgba(251, 191, 36, 0.3); }\n          100% { box-shadow: 0 25px 50px -12px rgba(251, 191, 36, 0.8), 0 0 40px rgba(251, 191, 36, 0.6); }\n        }\n      "}),(0,x.jsx)(y.M,{children:c&&(0,x.jsxs)(j.E.div,{initial:{opacity:0,scale:.3,y:-50,rotateY:-180},animate:{opacity:1,scale:[.3,1.2,1],y:0,rotateY:0,transition:{duration:1.2,ease:"easeOut",scale:{times:[0,.6,1],duration:1.2}}},exit:{opacity:0,scale:.8,y:-20},className:"mb-6 p-6 bg-gradient-to-r from-yellow-400 via-orange-500 to-red-500 rounded-2xl text-white text-center shadow-2xl relative overflow-hidden",style:{background:"linear-gradient(45deg, #fbbf24, #f59e0b, #ea580c, #dc2626)",boxShadow:"0 25px 50px -12px rgba(251, 191, 36, 0.5), 0 0 0 1px rgba(255, 255, 255, 0.1)",animation:"levelUpGlow 2s infinite alternate"},children:[(0,x.jsx)(j.E.div,{className:"absolute inset-0 bg-gradient-to-r from-transparent via-white to-transparent opacity-20",animate:{x:["-100%","100%"],transition:{duration:2,repeat:1/0,ease:"linear"}}}),[...Array(8)].map(((e,t)=>(0,x.jsx)(j.E.div,{className:"absolute w-2 h-2 bg-white rounded-full opacity-80",style:{left:"".concat(20+10*t,"%"),top:"".concat(20+t%3*20,"%")},animate:{y:[-10,-30,-10],opacity:[.8,.3,.8],scale:[1,1.5,1],transition:{duration:2+.2*t,repeat:1/0,ease:"easeInOut"}}},t))),(0,x.jsxs)(j.E.div,{className:"relative z-10",animate:{scale:[1,1.05,1],transition:{duration:1.5,repeat:1/0,ease:"easeInOut"}},children:[(0,x.jsxs)(j.E.div,{className:"flex items-center justify-center mb-3",animate:{rotateY:[0,360],transition:{duration:3,repeat:1/0,ease:"linear"}},children:[(0,x.jsx)(w.gBl,{className:"w-12 h-12 mr-3 drop-shadow-lg"}),(0,x.jsx)(j.E.span,{className:"text-3xl font-black tracking-wider",style:{textShadow:"2px 2px 4px rgba(0,0,0,0.5)",background:"linear-gradient(45deg, #fff, #fbbf24)",WebkitBackgroundClip:"text",WebkitTextFillColor:"transparent"},animate:{scale:[1,1.1,1],transition:{duration:1,repeat:1/0,ease:"easeInOut"}},children:"LEVEL UP!"})]}),(0,x.jsxs)(j.E.p,{className:"text-xl font-bold",style:{textShadow:"1px 1px 2px rgba(0,0,0,0.5)"},initial:{opacity:0,y:20},animate:{opacity:1,y:0,transition:{delay:.5,duration:.8}},children:["\ud83c\udf89 You reached Level ",m,"! \ud83c\udf89"]}),(0,x.jsx)(j.E.div,{className:"mt-3 text-sm font-semibold opacity-90",initial:{opacity:0},animate:{opacity:1,transition:{delay:1,duration:.8}},children:"Keep up the amazing work! \ud83d\ude80"})]})]})}),(0,x.jsxs)("div",{className:"text-center mb-6",children:[(0,x.jsxs)("div",{className:"flex items-center justify-center mb-3",children:[(0,x.jsx)(w.lbY,{className:"w-8 h-8 text-purple-600 mr-2"}),(0,x.jsx)("h3",{className:"text-2xl font-bold text-gray-800",children:"XP Earned"})]}),(0,x.jsxs)(j.E.div,{initial:{scale:0},animate:{scale:1},transition:{type:"spring",stiffness:200,damping:10},className:"text-6xl font-bold text-purple-600 mb-2",children:["+",i]}),(0,x.jsxs)("p",{className:"text-gray-600",children:["Total XP: ",p.toLocaleString()," | Level ",m]})]}),(0,x.jsxs)("div",{className:"grid grid-cols-2 gap-4 mb-6",children:[u>0&&(0,x.jsxs)("div",{className:"bg-white rounded-lg p-3 text-center shadow-sm",children:[(0,x.jsx)(w.p8m,{className:"w-6 h-6 text-orange-500 mx-auto mb-1"}),(0,x.jsx)("div",{className:"text-lg font-bold text-gray-800",children:u}),(0,x.jsx)("div",{className:"text-sm text-gray-600",children:"Streak"})]}),h.length>0&&(0,x.jsxs)("div",{className:"bg-white rounded-lg p-3 text-center shadow-sm",children:[(0,x.jsx)(w.Vh6,{className:"w-6 h-6 text-yellow-500 mx-auto mb-1"}),(0,x.jsx)("div",{className:"text-lg font-bold text-gray-800",children:h.length}),(0,x.jsx)("div",{className:"text-sm text-gray-600",children:"New Badges"})]})]}),Object.keys(d).length>0&&(0,x.jsxs)("div",{children:[(0,x.jsxs)("button",{onClick:()=>a(!r),className:"w-full flex items-center justify-between p-3 bg-white rounded-lg shadow-sm hover:shadow-md transition-shadow duration-200",children:[(0,x.jsx)("span",{className:"font-medium text-gray-800",children:"XP Breakdown"}),r?(0,x.jsx)(w.YeS,{className:"w-5 h-5 text-gray-600"}):(0,x.jsx)(w.YRR,{className:"w-5 h-5 text-gray-600"})]}),(0,x.jsx)(y.M,{children:r&&(0,x.jsx)(j.E.div,{initial:{opacity:0,height:0},animate:{opacity:1,height:"auto"},exit:{opacity:0,height:0},className:"mt-3 bg-white rounded-lg p-4 shadow-sm",children:(0,x.jsxs)("div",{className:"space-y-3",children:[d.baseXP&&(0,x.jsxs)("div",{className:"flex justify-between items-center",children:[(0,x.jsxs)("div",{className:"flex items-center",children:[(0,x.jsx)(w.Chd,{className:"w-4 h-4 text-blue-500 mr-2"}),(0,x.jsx)("span",{className:"text-sm text-gray-700",children:"Base XP"})]}),(0,x.jsxs)("span",{className:"font-medium text-gray-800",children:["+",d.baseXP]})]}),d.difficultyBonus>0&&(0,x.jsxs)("div",{className:"flex justify-between items-center",children:[(0,x.jsxs)("div",{className:"flex items-center",children:[(0,x.jsx)(w.ehl,{className:"w-4 h-4 text-green-500 mr-2"}),(0,x.jsx)("span",{className:"text-sm text-gray-700",children:"Difficulty Bonus"})]}),(0,x.jsxs)("span",{className:"font-medium text-green-600",children:["+",d.difficultyBonus]})]}),d.speedBonus>0&&(0,x.jsxs)("div",{className:"flex justify-between items-center",children:[(0,x.jsxs)("div",{className:"flex items-center",children:[(0,x.jsx)(w.rfE,{className:"w-4 h-4 text-yellow-500 mr-2"}),(0,x.jsx)("span",{className:"text-sm text-gray-700",children:"Speed Bonus"})]}),(0,x.jsxs)("span",{className:"font-medium text-yellow-600",children:["+",d.speedBonus]})]}),d.perfectScoreBonus>0&&(0,x.jsxs)("div",{className:"flex justify-between items-center",children:[(0,x.jsxs)("div",{className:"flex items-center",children:[(0,x.jsx)(w.jsT,{className:"w-4 h-4 text-purple-500 mr-2"}),(0,x.jsx)("span",{className:"text-sm text-gray-700",children:"Perfect Score"})]}),(0,x.jsxs)("span",{className:"font-medium text-purple-600",children:["+",d.perfectScoreBonus]})]}),d.streakBonus>0&&(0,x.jsxs)("div",{className:"flex justify-between items-center",children:[(0,x.jsxs)("div",{className:"flex items-center",children:[(0,x.jsx)(w.p8m,{className:"w-4 h-4 text-orange-500 mr-2"}),(0,x.jsx)("span",{className:"text-sm text-gray-700",children:"Streak Bonus"})]}),(0,x.jsxs)("span",{className:"font-medium text-orange-600",children:["+",d.streakBonus]})]}),d.firstAttemptBonus>0&&(0,x.jsxs)("div",{className:"flex justify-between items-center",children:[(0,x.jsxs)("div",{className:"flex items-center",children:[(0,x.jsx)(w.Vh6,{className:"w-4 h-4 text-indigo-500 mr-2"}),(0,x.jsx)("span",{className:"text-sm text-gray-700",children:"First Attempt"})]}),(0,x.jsxs)("span",{className:"font-medium text-indigo-600",children:["+",d.firstAttemptBonus]})]})]})})})]}),h.length>0&&(0,x.jsxs)("div",{className:"mt-6",children:[(0,x.jsxs)("h4",{className:"text-lg font-bold text-gray-800 mb-3 flex items-center",children:[(0,x.jsx)(w.Vh6,{className:"w-5 h-5 mr-2 text-yellow-500"}),"New Achievements"]}),(0,x.jsx)("div",{className:"space-y-2",children:h.map(((e,t)=>(0,x.jsxs)(j.E.div,{initial:{opacity:0,x:-20},animate:{opacity:1,x:0},transition:{delay:.1*t},className:"flex items-center p-3 bg-gradient-to-r from-yellow-50 to-orange-50 rounded-lg border border-yellow-200",children:[(0,x.jsx)(w.gBl,{className:"w-6 h-6 text-yellow-500 mr-3"}),(0,x.jsxs)("div",{children:[(0,x.jsx)("div",{className:"font-medium text-gray-800",children:String(e.name||"Achievement")}),(0,x.jsx)("div",{className:"text-sm text-gray-600",children:String(e.description||"Achievement unlocked!")})]})]},t)))})]})]})},A=e=>{let{question:t,questionIndex:s,totalQuestions:r,selectedAnswer:a,onAnswerSelect:n,onNext:i,onPrevious:o,timeLeft:l,examTitle:d}=e;if(!t)return(0,x.jsx)("div",{children:"Loading question..."});const c=t.name?String(t.name):"Question text not available",m=t.answerType?String(t.answerType):"Options";let p=[];t.options&&(Array.isArray(t.options)?p=t.options.map((e=>String(e||""))):"object"===typeof t.options&&(p=Object.values(t.options).map((e=>String(e||"")))));return(0,x.jsxs)("div",{style:{minHeight:"100vh",background:"linear-gradient(135deg, #f0f9ff 0%, #e0e7ff 100%)"},children:[(0,x.jsx)("div",{style:{background:"white",borderBottom:"1px solid #e5e7eb",padding:"16px",position:"sticky",top:0,zIndex:50},children:(0,x.jsxs)("div",{style:{maxWidth:"1200px",margin:"0 auto",display:"flex",justifyContent:"space-between",alignItems:"center"},children:[(0,x.jsxs)("div",{children:[(0,x.jsx)("h1",{style:{fontSize:"18px",fontWeight:"600",color:"#111827",margin:0},children:d?String(d):"Quiz"}),(0,x.jsxs)("p",{style:{fontSize:"14px",color:"#6b7280",margin:0},children:["Question ",s+1," of ",r]})]}),(0,x.jsxs)("div",{style:{background:l<=60?"#dc2626":"#2563eb",color:"white",padding:"12px 24px",borderRadius:"12px",fontFamily:"monospace",fontWeight:"bold"},children:["TIME: ",(e=>{const t=e%60;return Math.floor(e/60)+":"+(t<10?"0":"")+t})(l)]})]})}),(0,x.jsxs)("div",{style:{maxWidth:"800px",margin:"0 auto",padding:"32px 16px"},children:[(0,x.jsxs)("div",{style:{background:"white",borderRadius:"16px",boxShadow:"0 25px 50px -12px rgba(0, 0, 0, 0.25)",padding:"32px",marginBottom:"24px"},children:[(0,x.jsx)("div",{style:{marginBottom:"24px"},children:(0,x.jsxs)("span",{style:{background:"#dbeafe",color:"#1e40af",padding:"8px 16px",borderRadius:"20px",fontSize:"14px",fontWeight:"600"},children:["Question ",s+1," of ",r]})}),(0,x.jsx)("div",{style:{marginBottom:"32px"},children:(0,x.jsx)("h2",{style:{fontSize:"20px",fontWeight:"500",color:"#111827",lineHeight:"1.6",margin:0},children:c})}),(t.image||t.imageUrl)&&(0,x.jsx)("div",{style:{marginBottom:"32px",textAlign:"center"},children:(0,x.jsx)("img",{src:t.image||t.imageUrl,alt:"Question",style:{maxWidth:"100%",maxHeight:"400px",objectFit:"contain",borderRadius:"12px",border:"1px solid #e5e7eb"}})}),(0,x.jsx)("div",{style:{display:"flex",flexDirection:"column",gap:"16px"},children:"Options"===m&&p.length>0?p.map(((e,t)=>{const s=String.fromCharCode(65+t),r=a===t;return(0,x.jsxs)("button",{onClick:()=>n(t),style:{width:"100%",textAlign:"left",padding:"16px",borderRadius:"12px",border:r?"2px solid #2563eb":"2px solid #e5e7eb",background:r?"#eff6ff":"white",color:r?"#1e40af":"#111827",cursor:"pointer",transition:"all 0.3s",display:"flex",alignItems:"center",gap:"16px"},children:[(0,x.jsx)("div",{style:{width:"32px",height:"32px",borderRadius:"50%",background:r?"#2563eb":"#f3f4f6",color:r?"white":"#6b7280",display:"flex",alignItems:"center",justifyContent:"center",fontWeight:"bold",fontSize:"14px"},children:s}),(0,x.jsx)("span",{style:{flex:1,fontWeight:"500"},children:e}),r&&(0,x.jsx)("div",{style:{width:"24px",height:"24px",borderRadius:"50%",background:"#2563eb",color:"white",display:"flex",alignItems:"center",justifyContent:"center"},children:"\u2713"})]},t)})):(0,x.jsxs)("div",{children:[(0,x.jsx)("label",{style:{display:"block",fontSize:"14px",fontWeight:"500",color:"#374151",marginBottom:"8px"},children:"Your Answer:"}),(0,x.jsx)("input",{type:"text",value:a||"",onChange:e=>n(e.target.value),placeholder:"Type your answer here...",style:{width:"100%",padding:"16px",border:"2px solid #e5e7eb",borderRadius:"12px",fontSize:"16px",outline:"none"}})]})})]}),(0,x.jsxs)("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center"},children:[(0,x.jsx)("button",{onClick:o,disabled:0===s,style:{padding:"12px 24px",borderRadius:"12px",fontWeight:"600",border:"none",cursor:0===s?"not-allowed":"pointer",background:0===s?"#e5e7eb":"#4b5563",color:0===s?"#9ca3af":"white"},children:"\u2190 Previous"}),(0,x.jsxs)("div",{style:{textAlign:"center"},children:[(0,x.jsx)("div",{style:{fontSize:"14px",color:"#6b7280",marginBottom:"8px"},children:"Progress"}),(0,x.jsx)("div",{style:{width:"200px",height:"8px",background:"#e5e7eb",borderRadius:"4px",overflow:"hidden"},children:(0,x.jsx)("div",{style:{height:"100%",background:"#2563eb",borderRadius:"4px",width:(s+1)/r*100+"%",transition:"width 0.3s"}})})]}),(0,x.jsx)("button",{onClick:i,style:{padding:"12px 24px",borderRadius:"12px",fontWeight:"600",border:"none",cursor:"pointer",background:"#2563eb",color:"white"},children:s===r-1?"Submit Quiz":"Next \u2192"})]})]})]})},k=e=>{let{questions:t,selectedOptions:s,explanations:r,fetchExplanation:a,setView:n,examData:i,setSelectedQuestionIndex:o,setSelectedOptions:l,setResult:d,setTimeUp:c,setSecondsLeft:m,setExplanations:p}=e;return t&&Array.isArray(t)?(0,x.jsx)("div",{style:{minHeight:"100vh",background:"linear-gradient(135deg, #f8fafc 0%, #e0e7ff 100%)",padding:"24px"},children:(0,x.jsxs)("div",{style:{maxWidth:"800px",margin:"0 auto"},children:[(0,x.jsx)("div",{style:{textAlign:"center",marginBottom:"24px"},children:(0,x.jsxs)("div",{style:{background:"white",borderRadius:"16px",padding:"24px",boxShadow:"0 10px 25px -5px rgba(0, 0, 0, 0.1)",border:"1px solid #e2e8f0"},children:[(0,x.jsx)("h2",{style:{fontSize:"24px",fontWeight:"bold",color:"#2563eb",margin:"0 0 8px 0"},children:"Answer Review"}),(0,x.jsx)("p",{style:{color:"#64748b",margin:0},children:"Review your answers and get explanations"})]})}),(0,x.jsx)("div",{style:{display:"flex",flexDirection:"column",gap:"16px",marginBottom:"24px"},children:t.map(((e,t)=>{if(!e)return null;const n=e.name?String(e.name):"Question not available",i=e.answerType?String(e.answerType):"Options",o=s[t];let l=!1,d="Unknown",c="Not answered";if("Options"===i){if(l=e.correctOption===o,e.options&&void 0!==e.correctOption){const t=e.options[e.correctOption];d=t?String(t):"Unknown"}if(e.options&&void 0!==o){const t=e.options[o];c=t?String(t):"Not answered"}}else l=e.correctAnswer===o,d=e.correctAnswer?String(e.correctAnswer):"Unknown",c=o?String(o):"Not answered";return(0,x.jsxs)("div",{style:{borderRadius:"12px",boxShadow:"0 4px 6px -1px rgba(0, 0, 0, 0.1)",border:"2px solid "+(l?"#10b981":"#ef4444"),padding:"16px",background:l?"#f0fdf4":"#fef2f2"},children:[(0,x.jsx)("div",{style:{marginBottom:"12px"},children:(0,x.jsxs)("div",{style:{display:"flex",alignItems:"flex-start",gap:"12px"},children:[(0,x.jsx)("div",{style:{width:"32px",height:"32px",borderRadius:"8px",background:"#2563eb",color:"white",display:"flex",alignItems:"center",justifyContent:"center",fontWeight:"bold",fontSize:"14px",flexShrink:0,marginTop:"4px"},children:t+1}),(0,x.jsx)("div",{style:{flex:1},children:(0,x.jsx)("p",{style:{color:"#1e293b",fontWeight:"500",lineHeight:"1.6",margin:0},children:n})})]})}),(0,x.jsxs)("div",{style:{marginBottom:"8px"},children:[(0,x.jsx)("span",{style:{fontSize:"14px",fontWeight:"600",color:"#64748b"},children:"Your Answer: "}),(0,x.jsx)("span",{style:{fontWeight:"500",color:l?"#059669":"#dc2626"},children:c}),(0,x.jsx)("span",{style:{marginLeft:"12px",fontSize:"18px",color:l?"#10b981":"#ef4444"},children:l?"\u2713":"\u2717"})]}),!l&&(0,x.jsxs)("div",{style:{marginBottom:"8px"},children:[(0,x.jsx)("span",{style:{fontSize:"14px",fontWeight:"600",color:"#64748b"},children:"Correct Answer: "}),(0,x.jsx)("span",{style:{fontWeight:"500",color:"#059669"},children:d}),(0,x.jsx)("span",{style:{marginLeft:"12px",fontSize:"18px",color:"#10b981"},children:"\u2713"})]}),!l&&(0,x.jsx)("div",{style:{marginTop:"8px"},children:(0,x.jsxs)("button",{onClick:()=>{a(n,d,c,e.image||e.imageUrl||"")},style:{padding:"8px 16px",background:"#2563eb",color:"white",border:"none",borderRadius:"8px",fontSize:"14px",fontWeight:"500",cursor:"pointer",display:"flex",alignItems:"center",gap:"8px"},children:[(0,x.jsx)("span",{children:"\ud83d\udca1"}),(0,x.jsx)("span",{children:"Get Explanation"})]})}),r[n]&&(0,x.jsxs)("div",{style:{marginTop:"12px",padding:"12px",background:"white",borderRadius:"8px",borderLeft:"4px solid #2563eb",boxShadow:"0 1px 3px 0 rgba(0, 0, 0, 0.1)",border:"1px solid #e5e7eb"},children:[(0,x.jsxs)("div",{style:{display:"flex",alignItems:"center",marginBottom:"8px"},children:[(0,x.jsx)("span",{style:{fontSize:"18px",marginRight:"8px"},children:"\ud83d\udca1"}),(0,x.jsx)("h6",{style:{fontWeight:"bold",color:"#1f2937",fontSize:"16px",margin:0},children:"Explanation"})]}),(e.image||e.imageUrl)&&(0,x.jsxs)("div",{style:{marginBottom:"12px",padding:"8px",background:"#f9fafb",borderRadius:"6px",border:"1px solid #e5e7eb"},children:[(0,x.jsx)("div",{style:{marginBottom:"4px"},children:(0,x.jsx)("span",{style:{color:"#374151",fontSize:"14px",fontWeight:"500"},children:"\ud83d\udcca Reference Diagram:"})}),(0,x.jsx)("div",{style:{textAlign:"center"},children:(0,x.jsx)("img",{src:e.image||e.imageUrl,alt:"Question diagram",style:{maxWidth:"100%",maxHeight:"200px",objectFit:"contain",borderRadius:"6px",border:"1px solid #d1d5db"}})})]}),(0,x.jsx)("div",{style:{fontSize:"14px",color:"#1f2937",lineHeight:"1.6",background:"#f9fafb",padding:"8px",borderRadius:"6px"},children:r[n]?String(r[n]):""})]})]},e._id||t)}))}),(0,x.jsxs)("div",{style:{display:"flex",flexDirection:window.innerWidth<640?"column":"row",gap:"16px",justifyContent:"center",alignItems:"center"},children:[(0,x.jsx)("button",{onClick:()=>n("result"),style:{padding:"16px 32px",background:"#4b5563",color:"white",border:"none",borderRadius:"12px",fontWeight:"bold",cursor:"pointer",boxShadow:"0 4px 6px -1px rgba(0, 0, 0, 0.1)"},children:"\u2190 Back to Results"}),(0,x.jsx)("button",{onClick:()=>{n("instructions"),o(0),l({}),d({}),c(!1),m((null===i||void 0===i?void 0:i.duration)||0),p({})},style:{padding:"16px 32px",background:"#059669",color:"white",border:"none",borderRadius:"12px",fontWeight:"bold",cursor:"pointer",boxShadow:"0 4px 6px -1px rgba(0, 0, 0, 0.1)"},children:"\ud83d\udd04 Retake Quiz"})]})]})}):(0,x.jsx)("div",{children:"No questions to review"})};const S=function(){var e,t;const[s,u]=(0,n.useState)(null),[y,j]=(0,n.useState)([]),[w,S]=(0,n.useState)(0),[C,T]=(0,n.useState)({}),[q,I]=(0,n.useState)({}),P=(0,o.UO)(),B=(0,i.I0)(),R=(0,o.s0)(),[z,W]=(0,n.useState)("instructions"),[E,O]=(0,n.useState)(0),[Q,F]=(0,n.useState)(!1),[L,Z]=(0,n.useState)(null),[D,U]=(0,n.useState)(!0),[Y,V]=(0,n.useState)(null),{user:X}=(0,i.v9)((e=>e.user)),{width:H,height:M}=(0,g.Z)(),[K,J]=(0,n.useState)({}),G=(0,n.useCallback)((async()=>{try{U(!0),B((0,c.YC)()),console.log("Fetching exam data for ID:",P.id);const e=await(0,l.gw)({examId:P.id});if(console.log("Exam API Response:",e),B((0,c.Ir)()),U(!1),e.success){const t=e.data;let s=[];null!==t&&void 0!==t&&t.questions&&Array.isArray(t.questions)?s=t.questions:null!==t&&void 0!==t&&t.question&&Array.isArray(t.question)?s=t.question:t&&Array.isArray(t)&&(s=t),console.log("Exam Data:",t),console.log("Questions found:",s.length),console.log("Exam Data structure:",Object.keys(t||{})),j(s),u(t),O((null===t||void 0===t?void 0:t.duration)||0),0===s.length&&(console.warn("No questions found in exam data"),console.log("Full response for debugging:",e),a.ZP.warning("This exam has no questions. Please contact your instructor."))}else console.error("API Error:",e.message),console.log("Full error response:",e),a.ZP.error(e.message||"Failed to load exam data")}catch(e){B((0,c.Ir)()),U(!1),console.error("Exception in getExamData:",e),a.ZP.error(e.message||"Failed to load exam. Please try again.")}}),[P.id,B]),_=(0,n.useCallback)((async()=>{try{if(!X||!X._id)return a.ZP.error("User not found. Please log in again."),void R("/login");B((0,c.YC)());const e=[],t=[];y.forEach(((s,r)=>{"Free Text"!==s.answerType&&"Fill in the Blank"!==s.answerType||(t.push(r),e.push({question:s.name,expectedAnswer:s.correctAnswer||s.correctOption,userAnswer:C[r]||""}))}));const n=await(async e=>{if(!e.length)return[];const{data:t}=await(0,v.mT)(e);return t})(e),i={};n.forEach((e=>{e.result&&"boolean"===typeof e.result.isCorrect?i[e.question]=e.result:"boolean"===typeof e.isCorrect&&(i[e.question]={isCorrect:e.isCorrect,reason:e.reason||""})}));const o=[],l=[],x=[];y.forEach(((e,t)=>{const s=C[t]||"";if("Free Text"===e.answerType||"Fill in the Blank"===e.answerType){const{isCorrect:t=!1,reason:a=""}=i[e.name]||{},n=(0,r.Z)((0,r.Z)({},e),{},{userAnswer:s,reason:a});t?o.push(n):(l.push(n),x.push({question:e.name,expectedAnswer:e.correctAnswer||e.correctOption,userAnswer:s}))}else if("Options"===e.answerType){const t=e.correctOption,a=e.options&&e.options[t]||t,n=e.options&&e.options[s]||s||"",i=t===s,d=(0,r.Z)((0,r.Z)({},e),{},{userAnswer:s});i?o.push(d):(l.push(d),x.push({question:e.name,expectedAnswer:a,userAnswer:n}))}}));const m=Y?Math.floor((Date.now()-Y)/1e3):0,p=60*((null===s||void 0===s?void 0:s.duration)||0),u=y.length,h=o.length,g=Math.round(h/u*100),j=10*h,w=g>=(s.passingMarks||70)?"Pass":"Fail",N={correctAnswers:o||[],wrongAnswers:l||[],verdict:w||"Fail",score:g,points:j,totalQuestions:u,timeSpent:m,totalTimeAllowed:p};I(N);const A=await(0,d.cq)({exam:P.id,result:N,user:X._id});if(A.success){const e=(0,r.Z)((0,r.Z)({},N),{},{xpData:A.xpData});I(e),W("result"),window.scrollTo(0,0),new Audio("Pass"===w?b:f).play()}else a.ZP.error(A.message);B((0,c.Ir)())}catch(e){B((0,c.Ir)()),a.ZP.error(e.message)}}),[y,C,s,P.id,X,R,B]);return(0,n.useEffect)((()=>{Q&&"questions"===z&&(clearInterval(L),_())}),[Q,z,L,_]),(0,n.useEffect)((()=>{console.log("WriteExam useEffect - params.id:",P.id),P.id?G():(console.error("No exam ID provided in URL parameters"),a.ZP.error("Invalid exam ID. Please select a quiz from the list."),R("/user/quiz"))}),[P.id,G,R]),(0,n.useEffect)((()=>()=>{L&&clearInterval(L)}),[L]),(0,n.useEffect)((()=>("instructions"===z||"questions"===z||"result"===z?document.body.classList.add("quiz-fullscreen"):document.body.classList.remove("quiz-fullscreen"),()=>{document.body.classList.remove("quiz-fullscreen")})),[z]),X?s?(0,x.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50",children:["instructions"===z&&(0,x.jsx)(m,{examData:s,setView:W,startTimer:()=>{const e=(null===s||void 0===s?void 0:s.duration)||0;O(e),V(Date.now());const t=setInterval((()=>{O((e=>e>0?e-1:(F(!0),0)))}),1e3);Z(t)},questions:y}),"questions"===z&&(D?(0,x.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50 flex items-center justify-center",children:(0,x.jsxs)("div",{className:"bg-white/90 backdrop-blur-sm rounded-3xl p-12 shadow-2xl border border-blue-200 max-w-lg mx-4 text-center",children:[(0,x.jsx)("div",{className:"w-24 h-24 bg-gradient-to-r from-blue-500 to-indigo-500 rounded-full flex items-center justify-center mx-auto mb-6 shadow-lg animate-pulse",children:(0,x.jsxs)("svg",{className:"w-12 h-12 text-white animate-spin",fill:"none",viewBox:"0 0 24 24",children:[(0,x.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,x.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]})}),(0,x.jsx)("h3",{className:"text-2xl font-bold text-blue-800 mb-4",children:"Loading Quiz..."}),(0,x.jsx)("p",{className:"text-blue-600 text-lg",children:"Please wait while we prepare your questions."})]})}):0===y.length?(0,x.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-amber-50 via-white to-orange-50 flex items-center justify-center",children:(0,x.jsxs)("div",{className:"bg-white/90 backdrop-blur-sm rounded-3xl p-12 shadow-2xl border border-amber-200 max-w-lg mx-4 text-center",children:[(0,x.jsx)("div",{className:"w-24 h-24 bg-gradient-to-r from-amber-500 to-orange-500 rounded-full flex items-center justify-center mx-auto mb-6 shadow-lg",children:(0,x.jsx)("svg",{className:"w-12 h-12 text-white",fill:"currentColor",viewBox:"0 0 20 20",children:(0,x.jsx)("path",{fillRule:"evenodd",d:"M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z",clipRule:"evenodd"})})}),(0,x.jsx)("h3",{className:"text-2xl font-bold text-amber-800 mb-4",children:"No Questions Found"}),(0,x.jsx)("p",{className:"text-amber-700 mb-6 text-lg leading-relaxed",children:"This exam appears to have no questions. This could be due to:"}),(0,x.jsxs)("ul",{className:"text-left text-amber-700 mb-8 space-y-2",children:[(0,x.jsx)("li",{children:"\u2022 Questions not properly linked to this exam"}),(0,x.jsx)("li",{children:"\u2022 Database connection issues"}),(0,x.jsx)("li",{children:"\u2022 Exam configuration problems"})]}),(0,x.jsxs)("div",{className:"space-y-3",children:[(0,x.jsx)("button",{onClick:async()=>{try{B((0,c.YC)());const e=await fetch("/api/exams/repair-exam-questions",{method:"POST",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(localStorage.getItem("token"))},body:JSON.stringify({examId:P.id})}),t=await e.json();t.success?(a.ZP.success(t.message),G()):a.ZP.error(t.message)}catch(e){a.ZP.error("Failed to repair exam questions")}finally{B((0,c.Ir)())}},className:"w-full px-8 py-4 bg-gradient-to-r from-amber-500 to-orange-500 text-white rounded-xl font-bold text-lg hover:from-amber-600 hover:to-orange-600 transform hover:scale-105 transition-all duration-300 shadow-lg",children:"\ud83d\udd27 Repair Questions"}),(0,x.jsx)("button",{onClick:()=>{console.log("Retrying exam data fetch..."),G()},className:"w-full px-8 py-4 bg-gradient-to-r from-blue-500 to-blue-600 text-white rounded-xl font-bold text-lg hover:from-blue-600 hover:to-blue-700 transform hover:scale-105 transition-all duration-300 shadow-lg",children:"\ud83d\udd04 Retry Loading"}),(0,x.jsx)("button",{onClick:()=>R("/user/quiz"),className:"w-full px-8 py-4 bg-gradient-to-r from-gray-500 to-gray-600 text-white rounded-xl font-bold text-lg hover:from-gray-600 hover:to-gray-700 transform hover:scale-105 transition-all duration-300 shadow-lg",children:"\u2190 Back to Quiz List"})]})]})}):(0,x.jsx)(A,{question:y[w],questionIndex:w,totalQuestions:y.length,selectedAnswer:C[w],onAnswerSelect:e=>T((0,r.Z)((0,r.Z)({},C),{},{[w]:e})),onNext:()=>{w===y.length-1?_():S(w+1)},onPrevious:()=>{w>0&&S(w-1)},timeLeft:E,examTitle:(null===s||void 0===s?void 0:s.name)||"Quiz"})),"result"===z&&(0,x.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 py-8",children:["Pass"===q.verdict&&(0,x.jsx)(h(),{width:H,height:M}),(0,x.jsx)("div",{className:"max-w-4xl mx-auto px-4",children:(0,x.jsxs)("div",{className:"bg-white/95 backdrop-blur-md rounded-2xl shadow-xl border border-slate-200/50 overflow-hidden",children:[(0,x.jsx)("div",{className:"px-8 py-10 text-center relative ".concat("Pass"===q.verdict?"bg-gradient-to-br from-emerald-500/10 via-green-500/5 to-teal-500/10":"bg-gradient-to-br from-amber-500/10 via-orange-500/5 to-red-500/10"),children:(0,x.jsxs)("div",{className:"relative",children:[(0,x.jsx)("div",{className:"w-20 h-20 mx-auto mb-6 rounded-2xl flex items-center justify-center shadow-lg ".concat("Pass"===q.verdict?"bg-gradient-to-br from-emerald-500 to-green-600":"bg-gradient-to-br from-amber-500 to-orange-600"),children:(0,x.jsx)("img",{src:"Pass"===q.verdict?p:"data:image/gif;base64,R0lGODlhyACHAPABAP8AAAAAACH/C05FVFNDQVBFMi4wAwEAAAAh+QQJRgABACwAAAAAyACHAAAC/4yPqcvtD6OctD6As7a8+w+GIqWVJjCm6sq21wln7kzXdhPn8s33vqcLon7EovEgTB6XTFfyiWlKpxxoADqkarcK4RVb4oq14HJ0jD6avmC2Lg33mefnuN1JX7vf9/7IvLeD5OVXWDUXmIXwZNgYkQeDE+RIyQCJsvGSc1XZmZgnwehpeBlGQjh6V5ppoZQaB4lkqSg5+Sq2+rWg9/IpeCu1ikmLqskLvCRcNyjaVYxMpOzcNm0LLRfjm50AWr15zSPNfen9DY4nXH7CaWC++Hz+J0OubrobOQ4fDyR+j+/w7521ffzSAQx48Fg7VwQP5YKA0NgvZnwaTlBGy029i//r8q2baLFWKXUbQ3X0uC2kP4MCT1JkBdHlwoEqZ25q1lKmTZgx7b1MWbNfTp07eSb0WTTiPozeEh01KmlPPYXxmKJUehXkLKraoCKzOpSrSK8lsxK9JfSn2KdaV3plCCytWrI9kW61m/RsJblz25pcGzZjX7yO+AYW3GqY37ljgVIybJbjMsYSJ08lrGoeFpunAL+8u/hTZaywwtBJ7FmpXl91HZdmiZqwu8BTR5PGBbsDQpq0ByPu6pkK5NiZ9PUFDhr564fy1lS0zRv56mAbrOwUGGJz5x1YT4+JpfEnCM2uH5mO2A038EhQp0On25ooPeGbo4v+UL7CbOXGjYD/BR2aeZjpx1Vuagx3HH7BdXHXZdXBdWB9rv12m4DwoSQZd8X1h01uBVZom4UXIhgOX2u5N9p7bKUXoYF5NfabiFrZ91+LbaR2FnkxykhMdCTeYAWHvUWmoB761OjfG9Zl2BaIxuT1IXPJ/FjWkLppZ5Z3TFDpVpMLvofXTVhO6SKBUQ7I45mIXAikh5bF96ZT45XHJQ2A3KeWmWF+uSJidaJTRiBdBmhlWBlimCUiTcQiW34AvvngjnIuMqiWWzIao32VbrVQn5L+CehInrL54jQzVboYkkWU6SCheI7DmW8UOnfnpay26mp3UkHY1Xp81jCfqQCyBqef1OC6JJmn/2FCUpeGFovqhIqBR12wlJWTFbTR7inloqLKOkunUNbFxraWsWfLr21SC+6wCf4V7aOW0jfvemxlGWKVUCqKW6CtyngZjMZC+s2YXNQqa5zYBrzpdtkk+53B+yrsLsP6CszvcjTux6O8BAenasS8vkvcuT7qha6/fYxcqJ6/QHxqnCHDIWSpVwqi8meJIkxKzSDO2K24styamabXujXoq45OnPNeNb96NNMfY8bIw66ubPQgUl38XNLIpitpIyxHdkZ3KG79jtQpV+WzmGKdzYy5MH/1tMRDZQfqY23vfbXcOFk09mG5oomx1UufU/fh2TosipPgBE5RuYyHrTY+6uheAznUcKZZb02CEwp3UqNm7Pk9veb711t5N5S5eExmtHpIrU9qkt9/l845yI7XokvnuFtILOqoiTb376//vHvCxRt/PIrJx8688p866fbt0d/ctbxgTqv49SUrThrR3jvUEcWuOyj9+C1kTpWRPKufwuwm03os/Os//fn79q8wu6/L788/vn0LgDYI3MwIOIMjQQ+B2NuP4f7HwAQypHr9iyALlMQ9QJDKgvejILs4mCTLDRCENsKI+UjYIROikAwerOAKSxS0F1arbPWT4cF8Z0MW1jCHaYAgD/uluR8W4nJCjFgRj1AAACH5BAVGAAAALAAAAAABAAEAAAICRAEAOw==",alt:q.verdict,className:"w-12 h-12 object-contain"})}),(0,x.jsx)("h1",{className:"text-4xl font-black mb-4 tracking-tight ".concat("Pass"===q.verdict?"text-emerald-700":"text-amber-700"),children:"Pass"===q.verdict?"Excellent Work!":"Keep Pushing!"}),(0,x.jsx)("p",{className:"text-xl text-slate-600 font-medium max-w-md mx-auto leading-relaxed",children:"Pass"===q.verdict?"You've mastered this exam with flying colors!":"Every challenge makes you stronger. Try again!"})]})}),(0,x.jsxs)("div",{className:"p-8",children:[(0,x.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6 mb-8",children:[(0,x.jsxs)("div",{className:"group relative overflow-hidden bg-gradient-to-br from-blue-500/5 to-indigo-500/10 rounded-2xl border border-blue-200/50 p-6 hover:shadow-lg transition-all duration-300",children:[(0,x.jsx)("div",{className:"absolute inset-0 bg-gradient-to-br from-blue-500/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"}),(0,x.jsxs)("div",{className:"relative text-center",children:[(0,x.jsxs)("div",{className:"text-4xl font-black text-blue-600 mb-2 tracking-tight",children:[Math.round(((null===(e=q.correctAnswers)||void 0===e?void 0:e.length)||0)/y.length*100),"%"]}),(0,x.jsx)("div",{className:"text-sm font-bold text-blue-700/80 uppercase tracking-wider",children:"Your Score"})]})]}),(0,x.jsxs)("div",{className:"group relative overflow-hidden bg-gradient-to-br from-emerald-500/5 to-green-500/10 rounded-2xl border border-emerald-200/50 p-6 hover:shadow-lg transition-all duration-300",children:[(0,x.jsx)("div",{className:"absolute inset-0 bg-gradient-to-br from-emerald-500/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"}),(0,x.jsxs)("div",{className:"relative text-center",children:[(0,x.jsxs)("div",{className:"text-4xl font-black text-emerald-600 mb-2 tracking-tight",children:[Array.isArray(q.correctAnswers)?q.correctAnswers.length:0,"/",y.length]}),(0,x.jsx)("div",{className:"text-sm font-bold text-emerald-700/80 uppercase tracking-wider",children:"Correct"})]})]}),(0,x.jsxs)("div",{className:"group relative overflow-hidden rounded-2xl border p-6 hover:shadow-lg transition-all duration-300 ".concat("Pass"===q.verdict?"bg-gradient-to-br from-emerald-500/5 to-green-500/10 border-emerald-200/50":"bg-gradient-to-br from-amber-500/5 to-orange-500/10 border-amber-200/50"),children:[(0,x.jsx)("div",{className:"absolute inset-0 bg-gradient-to-br opacity-0 group-hover:opacity-100 transition-opacity duration-300 ".concat("Pass"===q.verdict?"from-emerald-500/5":"from-amber-500/5"," to-transparent")}),(0,x.jsxs)("div",{className:"relative text-center",children:[(0,x.jsx)("div",{className:"text-4xl font-black mb-2 tracking-tight ".concat("Pass"===q.verdict?"text-emerald-600":"text-amber-600"),children:"Pass"===q.verdict?"PASS":"RETRY"}),(0,x.jsx)("div",{className:"text-sm font-bold uppercase tracking-wider ".concat("Pass"===q.verdict?"text-emerald-700/80":"text-amber-700/80"),children:"Pass"===q.verdict?"Success!":"Need ".concat(s.passingMarks)})]})]})]}),(0,x.jsx)("div",{className:"mb-8",children:(0,x.jsxs)("div",{className:"relative bg-slate-100 rounded-2xl p-6",children:[(0,x.jsxs)("div",{className:"text-center mb-4",children:[(0,x.jsx)("h3",{className:"text-lg font-bold text-slate-700 mb-1",children:"Performance Overview"}),(0,x.jsx)("p",{className:"text-sm text-slate-500",children:"Your achievement level"})]}),(0,x.jsxs)("div",{className:"relative",children:[(0,x.jsx)("div",{className:"w-full bg-slate-200 rounded-full h-4 shadow-inner overflow-hidden",children:(0,x.jsx)("div",{className:"h-full rounded-full transition-all duration-1000 shadow-sm relative overflow-hidden ".concat("Pass"===q.verdict?"bg-gradient-to-r from-emerald-500 via-green-500 to-teal-500":"bg-gradient-to-r from-amber-500 via-orange-500 to-red-500"),style:{width:"".concat(((null===(t=q.correctAnswers)||void 0===t?void 0:t.length)||0)/y.length*100,"%")},children:(0,x.jsx)("div",{className:"absolute inset-0 bg-gradient-to-r from-white/20 to-transparent"})})}),(0,x.jsxs)("div",{className:"flex justify-between items-center mt-3",children:[(0,x.jsx)("span",{className:"text-xs font-medium text-slate-500",children:"0%"}),(0,x.jsxs)("span",{className:"text-lg font-black tracking-tight ".concat("Pass"===q.verdict?"text-emerald-600":"text-amber-600"),children:[Math.round((Array.isArray(q.correctAnswers)?q.correctAnswers.length:0)/y.length*100),"%"]}),(0,x.jsx)("span",{className:"text-xs font-medium text-slate-500",children:"100%"})]})]})]})}),q.xpData&&(0,x.jsx)("div",{className:"mb-8",children:(0,x.jsx)(N,{xpData:q.xpData})}),(0,x.jsx)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center",children:(0,x.jsxs)("button",{className:"group relative overflow-hidden px-8 py-4 bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white rounded-xl font-bold transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5",onClick:()=>W("review"),children:[(0,x.jsx)("div",{className:"absolute inset-0 bg-gradient-to-r from-white/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"}),(0,x.jsx)("span",{className:"relative",children:"Review Answers"})]})})]})]})})]}),"review"===z&&(0,x.jsx)(k,{questions:y,selectedOptions:C,explanations:K,fetchExplanation:async(e,t,s,n)=>{try{B((0,c.YC)());const i=await(0,v.u$)({question:e,expectedAnswer:t,userAnswer:s,imageUrl:n});B((0,c.Ir)()),i.success?J((t=>(0,r.Z)((0,r.Z)({},t),{},{[e]:i.explanation}))):a.ZP.error(i.error||"Failed to fetch explanation.")}catch(i){B((0,c.Ir)()),a.ZP.error(i.message)}},setView:W,examData:s,setSelectedQuestionIndex:S,setSelectedOptions:T,setResult:I,setTimeUp:F,setSecondsLeft:O,setExplanations:J})]}):null:(0,x.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50 flex justify-center items-center",children:(0,x.jsxs)("div",{className:"bg-white/90 backdrop-blur-sm rounded-2xl shadow-2xl border border-blue-100 p-12 text-center max-w-md mx-4",children:[(0,x.jsx)("div",{className:"w-20 h-20 bg-blue-600 rounded-full flex items-center justify-center mx-auto mb-6",children:(0,x.jsx)("svg",{className:"w-10 h-10 text-white",fill:"currentColor",viewBox:"0 0 20 20",children:(0,x.jsx)("path",{fillRule:"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-6-3a2 2 0 11-4 0 2 2 0 014 0zm-2 4a5 5 0 00-4.546 2.916A5.986 5.986 0 0010 16a5.986 5.986 0 004.546-2.084A5 5 0 0010 11z",clipRule:"evenodd"})})}),(0,x.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-4",children:"Authentication Required"}),(0,x.jsx)("p",{className:"text-gray-600 mb-8",children:"Please log in to access the exam and start your learning journey."}),(0,x.jsx)("button",{className:"w-full px-6 py-4 bg-gradient-to-r from-blue-600 to-indigo-600 text-white rounded-xl font-semibold text-lg hover:from-blue-700 hover:to-indigo-700 transform hover:scale-105 transition-all duration-300 shadow-lg",onClick:()=>R("/login"),children:"Go to Login"})]})})}}}]);
//# sourceMappingURL=419.495c73fd.chunk.js.map