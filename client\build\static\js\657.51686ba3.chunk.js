"use strict";(self.webpackChunkclient=self.webpackChunkclient||[]).push([[657],{9657:(e,t,n)=>{n.r(t),n.d(t,{default:()=>q});var a=n(1413),c=n(2791),o=n(9389),i=n(5725),r=n(7027),l=n(7309),s=n(6473),d=n(3433),m=n(1694),g=n.n(m),p=n(9585),h=n(635),u=n(1929),f=n(7908),x=n(7545),v=n(2832),y=n(1632),b=n(43),S=n(1113),C=n(9752);const j=c.createContext({});j.Consumer;var k=function(e,t){var n={};for(var a in e)Object.prototype.hasOwnProperty.call(e,a)&&t.indexOf(a)<0&&(n[a]=e[a]);if(null!=e&&"function"===typeof Object.getOwnPropertySymbols){var c=0;for(a=Object.getOwnPropertySymbols(e);c<a.length;c++)t.indexOf(a[c])<0&&Object.prototype.propertyIsEnumerable.call(e,a[c])&&(n[a[c]]=e[a[c]])}return n};const N=(e,t)=>{var{prefixCls:n,children:a,actions:o,extra:i,className:r,colStyle:l}=e,s=k(e,["prefixCls","children","actions","extra","className","colStyle"]);const{grid:d,itemLayout:m}=(0,c.useContext)(j),{getPrefixCls:p}=(0,c.useContext)(u.E_),h=p("list",n),f=o&&o.length>0&&c.createElement("ul",{className:"".concat(h,"-item-action"),key:"actions"},o.map(((e,t)=>c.createElement("li",{key:"".concat(h,"-item-action-").concat(t)},e,t!==o.length-1&&c.createElement("em",{className:"".concat(h,"-item-action-split")}))))),x=d?"div":"li",v=c.createElement(x,Object.assign({},s,d?{}:{ref:t},{className:g()("".concat(h,"-item"),{["".concat(h,"-item-no-flex")]:!("vertical"===m?i:!(()=>{let e;return c.Children.forEach(a,(t=>{"string"===typeof t&&(e=!0)})),e&&c.Children.count(a)>1})())},r)}),"vertical"===m&&i?[c.createElement("div",{className:"".concat(h,"-item-main"),key:"content"},a,f),c.createElement("div",{className:"".concat(h,"-item-extra"),key:"extra"},i)]:[a,f,(0,S.Tm)(i,{key:"extra"})]);return d?c.createElement(C.Z,{ref:t,flex:1,style:l},v):v},E=(0,c.forwardRef)(N);E.Meta=e=>{var{prefixCls:t,className:n,avatar:a,title:o,description:i}=e,r=k(e,["prefixCls","className","avatar","title","description"]);const{getPrefixCls:l}=(0,c.useContext)(u.E_),s=l("list",t),d=g()("".concat(s,"-item-meta"),n),m=c.createElement("div",{className:"".concat(s,"-item-meta-content")},o&&c.createElement("h4",{className:"".concat(s,"-item-meta-title")},o),i&&c.createElement("div",{className:"".concat(s,"-item-meta-description")},i));return c.createElement("div",Object.assign({},r,{className:d}),a&&c.createElement("div",{className:"".concat(s,"-item-meta-avatar")},a),(o||i)&&m)};const w=E;var O=n(7521),P=n(5564),Z=n(9922);const z=e=>{const{listBorderedCls:t,componentCls:n,paddingLG:a,margin:c,itemPaddingSM:o,itemPaddingLG:i,marginLG:r,borderRadiusLG:l}=e;return{["".concat(t)]:{border:"".concat(e.lineWidth,"px ").concat(e.lineType," ").concat(e.colorBorder),borderRadius:l,["".concat(n,"-header,").concat(n,"-footer,").concat(n,"-item")]:{paddingInline:a},["".concat(n,"-pagination")]:{margin:"".concat(c,"px ").concat(r,"px")}},["".concat(t).concat(n,"-sm")]:{["".concat(n,"-item,").concat(n,"-header,").concat(n,"-footer")]:{padding:o}},["".concat(t).concat(n,"-lg")]:{["".concat(n,"-item,").concat(n,"-header,").concat(n,"-footer")]:{padding:i}}}},L=e=>{const{componentCls:t,screenSM:n,screenMD:a,marginLG:c,marginSM:o,margin:i}=e;return{["@media screen and (max-width:".concat(a,")")]:{["".concat(t)]:{["".concat(t,"-item")]:{["".concat(t,"-item-action")]:{marginInlineStart:c}}},["".concat(t,"-vertical")]:{["".concat(t,"-item")]:{["".concat(t,"-item-extra")]:{marginInlineStart:c}}}},["@media screen and (max-width: ".concat(n,")")]:{["".concat(t)]:{["".concat(t,"-item")]:{flexWrap:"wrap",["".concat(t,"-action")]:{marginInlineStart:o}}},["".concat(t,"-vertical")]:{["".concat(t,"-item")]:{flexWrap:"wrap-reverse",["".concat(t,"-item-main")]:{minWidth:e.contentWidth},["".concat(t,"-item-extra")]:{margin:"auto auto ".concat(i,"px")}}}}}},I=e=>{const{componentCls:t,antCls:n,controlHeight:a,minHeight:c,paddingSM:o,marginLG:i,padding:r,itemPadding:l,colorPrimary:s,itemPaddingSM:d,itemPaddingLG:m,paddingXS:g,margin:p,colorText:h,colorTextDescription:u,motionDurationSlow:f,lineWidth:x,headerBg:v,footerBg:y,emptyTextPadding:b,metaMarginBottom:S,avatarMarginRight:C,titleMarginBottom:j,descriptionFontSize:k}=e,N={};return["start","center","end"].forEach((e=>{N["&-align-".concat(e)]={textAlign:e}})),{["".concat(t)]:Object.assign(Object.assign({},(0,O.Wf)(e)),{position:"relative","*":{outline:"none"},["".concat(t,"-header")]:{background:v},["".concat(t,"-footer")]:{background:y},["".concat(t,"-header, ").concat(t,"-footer")]:{paddingBlock:o},["".concat(t,"-pagination")]:Object.assign(Object.assign({marginBlockStart:i},N),{["".concat(n,"-pagination-options")]:{textAlign:"start"}}),["".concat(t,"-spin")]:{minHeight:c,textAlign:"center"},["".concat(t,"-items")]:{margin:0,padding:0,listStyle:"none"},["".concat(t,"-item")]:{display:"flex",alignItems:"center",justifyContent:"space-between",padding:l,color:h,["".concat(t,"-item-meta")]:{display:"flex",flex:1,alignItems:"flex-start",maxWidth:"100%",["".concat(t,"-item-meta-avatar")]:{marginInlineEnd:C},["".concat(t,"-item-meta-content")]:{flex:"1 0",width:0,color:h},["".concat(t,"-item-meta-title")]:{margin:"0 0 ".concat(e.marginXXS,"px 0"),color:h,fontSize:e.fontSize,lineHeight:e.lineHeight,"> a":{color:h,transition:"all ".concat(f),"&:hover":{color:s}}},["".concat(t,"-item-meta-description")]:{color:u,fontSize:k,lineHeight:e.lineHeight}},["".concat(t,"-item-action")]:{flex:"0 0 auto",marginInlineStart:e.marginXXL,padding:0,fontSize:0,listStyle:"none","& > li":{position:"relative",display:"inline-block",padding:"0 ".concat(g,"px"),color:u,fontSize:e.fontSize,lineHeight:e.lineHeight,textAlign:"center","&:first-child":{paddingInlineStart:0}},["".concat(t,"-item-action-split")]:{position:"absolute",insetBlockStart:"50%",insetInlineEnd:0,width:x,height:Math.ceil(e.fontSize*e.lineHeight)-2*e.marginXXS,transform:"translateY(-50%)",backgroundColor:e.colorSplit}}},["".concat(t,"-empty")]:{padding:"".concat(r,"px 0"),color:u,fontSize:e.fontSizeSM,textAlign:"center"},["".concat(t,"-empty-text")]:{padding:b,color:e.colorTextDisabled,fontSize:e.fontSize,textAlign:"center"},["".concat(t,"-item-no-flex")]:{display:"block"}}),["".concat(t,"-grid ").concat(n,"-col > ").concat(t,"-item")]:{display:"block",maxWidth:"100%",marginBlockEnd:p,paddingBlock:0,borderBlockEnd:"none"},["".concat(t,"-vertical ").concat(t,"-item")]:{alignItems:"initial",["".concat(t,"-item-main")]:{display:"block",flex:1},["".concat(t,"-item-extra")]:{marginInlineStart:i},["".concat(t,"-item-meta")]:{marginBlockEnd:S,["".concat(t,"-item-meta-title")]:{marginBlockStart:0,marginBlockEnd:j,color:h,fontSize:e.fontSizeLG,lineHeight:e.lineHeightLG}},["".concat(t,"-item-action")]:{marginBlockStart:r,marginInlineStart:"auto","> li":{padding:"0 ".concat(r,"px"),"&:first-child":{paddingInlineStart:0}}}},["".concat(t,"-split ").concat(t,"-item")]:{borderBlockEnd:"".concat(e.lineWidth,"px ").concat(e.lineType," ").concat(e.colorSplit),"&:last-child":{borderBlockEnd:"none"}},["".concat(t,"-split ").concat(t,"-header")]:{borderBlockEnd:"".concat(e.lineWidth,"px ").concat(e.lineType," ").concat(e.colorSplit)},["".concat(t,"-split").concat(t,"-empty ").concat(t,"-footer")]:{borderTop:"".concat(e.lineWidth,"px ").concat(e.lineType," ").concat(e.colorSplit)},["".concat(t,"-loading ").concat(t,"-spin-nested-loading")]:{minHeight:a},["".concat(t,"-split").concat(t,"-something-after-last-item ").concat(n,"-spin-container > ").concat(t,"-items > ").concat(t,"-item:last-child")]:{borderBlockEnd:"".concat(e.lineWidth,"px ").concat(e.lineType," ").concat(e.colorSplit)},["".concat(t,"-lg ").concat(t,"-item")]:{padding:m},["".concat(t,"-sm ").concat(t,"-item")]:{padding:d},["".concat(t,":not(").concat(t,"-vertical)")]:{["".concat(t,"-item-no-flex")]:{["".concat(t,"-item-action")]:{float:"right"}}}}},T=(0,P.Z)("List",(e=>{const t=(0,Z.TS)(e,{listBorderedCls:"".concat(e.componentCls,"-bordered"),minHeight:e.controlHeightLG});return[I(t),z(t),L(t)]}),(e=>({contentWidth:220,itemPadding:"".concat(e.paddingContentVertical,"px 0"),itemPaddingSM:"".concat(e.paddingContentVerticalSM,"px ").concat(e.paddingContentHorizontal,"px"),itemPaddingLG:"".concat(e.paddingContentVerticalLG,"px ").concat(e.paddingContentHorizontalLG,"px"),headerBg:"transparent",footerBg:"transparent",emptyTextPadding:e.padding,metaMarginBottom:e.padding,avatarMarginRight:e.padding,titleMarginBottom:e.paddingSM,descriptionFontSize:e.fontSize})));var B=function(e,t){var n={};for(var a in e)Object.prototype.hasOwnProperty.call(e,a)&&t.indexOf(a)<0&&(n[a]=e[a]);if(null!=e&&"function"===typeof Object.getOwnPropertySymbols){var c=0;for(a=Object.getOwnPropertySymbols(e);c<a.length;c++)t.indexOf(a[c])<0&&Object.prototype.propertyIsEnumerable.call(e,a[c])&&(n[a[c]]=e[a[c]])}return n};function M(e){var t,{pagination:n=!1,prefixCls:a,bordered:o=!1,split:i=!0,className:r,rootClassName:l,style:s,children:m,itemLayout:S,loadMore:C,grid:k,dataSource:N=[],size:E,header:w,footer:O,loading:P=!1,rowKey:Z,renderItem:z,locale:L}=e,I=B(e,["pagination","prefixCls","bordered","split","className","rootClassName","style","children","itemLayout","loadMore","grid","dataSource","size","header","footer","loading","rowKey","renderItem","locale"]);const M=n&&"object"===typeof n?n:{},[H,W]=c.useState(M.defaultCurrent||1),[A,F]=c.useState(M.defaultPageSize||10),{getPrefixCls:G,renderEmpty:U,direction:D,list:X}=c.useContext(u.E_),R=e=>(t,a)=>{var c;W(t),F(a),n&&n[e]&&(null===(c=null===n||void 0===n?void 0:n[e])||void 0===c||c.call(n,t,a))},_=R("onChange"),Q=R("onShowSizeChange"),V=G("list",a),[K,Y]=T(V);let q=P;"boolean"===typeof q&&(q={spinning:q});const J=q&&q.spinning;let $="";switch(E){case"large":$="lg";break;case"small":$="sm"}const ee=g()(V,{["".concat(V,"-vertical")]:"vertical"===S,["".concat(V,"-").concat($)]:$,["".concat(V,"-split")]:i,["".concat(V,"-bordered")]:o,["".concat(V,"-loading")]:J,["".concat(V,"-grid")]:!!k,["".concat(V,"-something-after-last-item")]:!!(C||n||O),["".concat(V,"-rtl")]:"rtl"===D},null===X||void 0===X?void 0:X.className,r,l,Y),te=(0,p.Z)({current:1,total:0},{total:N.length,current:H,pageSize:A},n||{}),ne=Math.ceil(te.total/te.pageSize);te.current>ne&&(te.current=ne);const ae=n?c.createElement("div",{className:g()("".concat(V,"-pagination"),"".concat(V,"-pagination-align-").concat(null!==(t=null===te||void 0===te?void 0:te.align)&&void 0!==t?t:"end"))},c.createElement(y.Z,Object.assign({},te,{onChange:_,onShowSizeChange:Q}))):null;let ce=(0,d.Z)(N);n&&N.length>(te.current-1)*te.pageSize&&(ce=(0,d.Z)(N).splice((te.current-1)*te.pageSize,te.pageSize));const oe=Object.keys(k||{}).some((e=>["xs","sm","md","lg","xl","xxl"].includes(e))),ie=(0,v.Z)(oe),re=c.useMemo((()=>{for(let e=0;e<h.c.length;e+=1){const t=h.c[e];if(ie[t])return t}}),[ie]),le=c.useMemo((()=>{if(!k)return;const e=re&&k[re]?k[re]:k.column;return e?{width:"".concat(100/e,"%"),maxWidth:"".concat(100/e,"%")}:void 0}),[null===k||void 0===k?void 0:k.column,re]);let se=J&&c.createElement("div",{style:{minHeight:53}});if(ce.length>0){const e=ce.map(((e,t)=>((e,t)=>{if(!z)return null;let n;return n="function"===typeof Z?Z(e):Z?e[Z]:e.key,n||(n="list-item-".concat(t)),c.createElement(c.Fragment,{key:n},z(e,t))})(e,t)));se=k?c.createElement(x.Z,{gutter:k.gutter},c.Children.map(e,(e=>c.createElement("div",{key:null===e||void 0===e?void 0:e.key,style:le},e)))):c.createElement("ul",{className:"".concat(V,"-items")},e)}else m||J||(se=c.createElement("div",{className:"".concat(V,"-empty-text")},L&&L.emptyText||(null===U||void 0===U?void 0:U("List"))||c.createElement(f.Z,{componentName:"List"})));const de=te.position||"bottom",me=c.useMemo((()=>({grid:k,itemLayout:S})),[JSON.stringify(k),S]);return K(c.createElement(j.Provider,{value:me},c.createElement("div",Object.assign({style:Object.assign(Object.assign({},null===X||void 0===X?void 0:X.style),s),className:ee},I),("top"===de||"both"===de)&&ae,w&&c.createElement("div",{className:"".concat(V,"-header")},w),c.createElement(b.Z,Object.assign({},q),se,m),O&&c.createElement("div",{className:"".concat(V,"-footer")},O),C||("bottom"===de||"both"===de)&&ae)))}M.Item=w;const H=M;var W=n(1046),A=n(2339),F=n(6275),G=n(6042),U=n(7689),D=n(5526),X=n(9434),R=n(8247),_=n(9728),Q=n(8262),V=n(184);const{TextArea:K}=o.default,{Option:Y}=i.default,q=()=>{const e=(0,U.s0)(),[t,n]=(0,c.useState)(!1),[d,m]=(0,c.useState)({title:"",message:"",recipients:"all",specificUsers:[],level:"",class:"",priority:"medium"}),[g,p]=(0,c.useState)([]),[h,u]=(0,c.useState)([]),[f,x]=(0,c.useState)(!1),v=(0,X.I0)();(0,c.useEffect)((()=>{y(),b()}),[]);const y=async()=>{try{v((0,R.YC)());const e=await(0,Q.AW)();e.success&&p(e.data.filter((e=>!e.isAdmin)))}catch(e){r.ZP.error("Failed to fetch users")}finally{v((0,R.Ir)())}},b=async()=>{try{const e=await(0,_._w)();e.success&&u(e.data)}catch(e){console.error("Failed to fetch sent notifications:",e)}},S=()=>{m({title:"",message:"",recipients:"all",specificUsers:[],level:"",class:"",priority:"medium"})},C=e=>"all"===e.recipientType?"All Users":"level"===e.recipientType?"Level: ".concat(e.targetLevel):"class"===e.recipientType?"Class: ".concat(e.targetClass):"specific"===e.recipientType?"".concat(e.recipientCount," specific users"):"Unknown",j=e=>{switch(e){case"low":default:return"blue";case"medium":return"orange";case"high":return"red";case"urgent":return"purple"}};return(0,V.jsxs)("div",{className:"admin-notifications",children:[(0,V.jsxs)("div",{className:"admin-notifications-header",children:[(0,V.jsxs)("div",{className:"flex items-center gap-4 mb-4",children:[(0,V.jsxs)(G.E.button,{whileHover:{scale:1.05},whileTap:{scale:.95},onClick:()=>e("/admin/dashboard"),className:"flex items-center gap-2 px-3 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors duration-200 shadow-md",children:[(0,V.jsx)(D.Qlt,{className:"w-4 h-4"}),(0,V.jsx)("span",{className:"hidden sm:inline text-sm font-medium",children:"Dashboard"})]}),(0,V.jsxs)("div",{children:[(0,V.jsxs)("h1",{className:"page-title",children:[(0,V.jsx)(D.CMq,{className:"title-icon"}),"Send Notifications"]}),(0,V.jsx)("p",{className:"page-description",children:"Send notifications to users that will appear in their notification dashboard"})]})]}),(0,V.jsx)(l.ZP,{type:"primary",icon:(0,V.jsx)(D.rIf,{}),onClick:()=>n(!0),size:"large",children:"Send New Notification"})]}),(0,V.jsx)(s.Z,{title:"Recently Sent Notifications",className:"sent-notifications-card",children:(0,V.jsx)(H,{dataSource:h,renderItem:e=>(0,V.jsx)(H.Item,{actions:[(0,V.jsx)(l.ZP,{type:"text",icon:(0,V.jsx)(D.EF5,{}),danger:!0,onClick:()=>(async e=>{try{(await(0,_.ZQ)(e)).success&&(r.ZP.success("Notification deleted"),b())}catch(t){r.ZP.error("Failed to delete notification")}})(e._id),children:"Delete"})],children:(0,V.jsx)(H.Item.Meta,{title:(0,V.jsxs)(W.Z,{children:[e.title,(0,V.jsx)(A.Z,{color:j(e.priority),children:e.priority})]}),description:(0,V.jsxs)("div",{children:[(0,V.jsx)("p",{children:e.message}),(0,V.jsxs)(W.Z,{size:"large",className:"notification-meta",children:[(0,V.jsxs)("span",{children:[(0,V.jsx)(D.HLl,{className:"meta-icon"}),C(e)]}),(0,V.jsxs)("span",{children:["Sent: ",new Date(e.createdAt).toLocaleString()]})]})]})})}),locale:{emptyText:"No notifications sent yet"}})}),(0,V.jsx)(F.Z,{title:"Send New Notification",open:t,onOk:async()=>{if(d.title.trim()&&d.message.trim())try{x(!0);const e=await(0,_.xs)(d);e.success?(r.ZP.success("Notification sent to ".concat(e.data.recipientCount," users")),n(!1),S(),b()):r.ZP.error(e.message||"Failed to send notification")}catch(e){r.ZP.error("Failed to send notification")}finally{x(!1)}else r.ZP.error("Please fill in title and message")},onCancel:()=>{n(!1),S()},confirmLoading:f,width:600,okText:"Send Notification",okButtonProps:{icon:(0,V.jsx)(D.Oe$,{})},children:(0,V.jsxs)("div",{className:"notification-form",children:[(0,V.jsxs)("div",{className:"form-group",children:[(0,V.jsx)("label",{children:"Title *"}),(0,V.jsx)(o.default,{placeholder:"Enter notification title",value:d.title,onChange:e=>m((0,a.Z)((0,a.Z)({},d),{},{title:e.target.value})),maxLength:100})]}),(0,V.jsxs)("div",{className:"form-group",children:[(0,V.jsx)("label",{children:"Message *"}),(0,V.jsx)(K,{placeholder:"Enter notification message",value:d.message,onChange:e=>m((0,a.Z)((0,a.Z)({},d),{},{message:e.target.value})),rows:4,maxLength:500})]}),(0,V.jsxs)("div",{className:"form-group",children:[(0,V.jsx)("label",{children:"Priority"}),(0,V.jsxs)(i.default,{value:d.priority,onChange:e=>m((0,a.Z)((0,a.Z)({},d),{},{priority:e})),style:{width:"100%"},children:[(0,V.jsx)(Y,{value:"low",children:"Low"}),(0,V.jsx)(Y,{value:"medium",children:"Medium"}),(0,V.jsx)(Y,{value:"high",children:"High"}),(0,V.jsx)(Y,{value:"urgent",children:"Urgent"})]})]}),(0,V.jsxs)("div",{className:"form-group",children:[(0,V.jsx)("label",{children:"Send To"}),(0,V.jsxs)(i.default,{value:d.recipients,onChange:e=>m((0,a.Z)((0,a.Z)({},d),{},{recipients:e})),style:{width:"100%"},children:[(0,V.jsx)(Y,{value:"all",children:"All Users"}),(0,V.jsx)(Y,{value:"level",children:"Specific Level"}),(0,V.jsx)(Y,{value:"class",children:"Specific Class"}),(0,V.jsx)(Y,{value:"specific",children:"Specific Users"})]})]}),"level"===d.recipients&&(0,V.jsxs)("div",{className:"form-group",children:[(0,V.jsx)("label",{children:"Level"}),(0,V.jsxs)(i.default,{value:d.level,onChange:e=>m((0,a.Z)((0,a.Z)({},d),{},{level:e})),style:{width:"100%"},placeholder:"Select level",children:[(0,V.jsx)(Y,{value:"primary",children:"Primary"}),(0,V.jsx)(Y,{value:"secondary",children:"Secondary"}),(0,V.jsx)(Y,{value:"advance",children:"Advance"})]})]}),"class"===d.recipients&&(0,V.jsxs)("div",{className:"form-group",children:[(0,V.jsx)("label",{children:"Class"}),(0,V.jsx)(i.default,{value:d.class,onChange:e=>m((0,a.Z)((0,a.Z)({},d),{},{class:e})),style:{width:"100%"},placeholder:"Select class",children:[1,2,3,4,5,6,7].map((e=>(0,V.jsx)(Y,{value:e.toString(),children:e},e)))})]}),"specific"===d.recipients&&(0,V.jsxs)("div",{className:"form-group",children:[(0,V.jsx)("label",{children:"Select Users"}),(0,V.jsx)(i.default,{mode:"multiple",value:d.specificUsers,onChange:e=>m((0,a.Z)((0,a.Z)({},d),{},{specificUsers:e})),style:{width:"100%"},placeholder:"Select users",showSearch:!0,filterOption:(e,t)=>t.children.toLowerCase().indexOf(e.toLowerCase())>=0,children:g.map((e=>(0,V.jsxs)(Y,{value:e._id,children:[e.name," (",e.email,")"]},e._id)))})]})]})})]})}},2339:(e,t,n)=>{n.d(t,{Z:()=>k});var a=n(732),c=n(1694),o=n.n(c),i=n(2791),r=n(4466),l=n(922),s=n(117),d=n(1929);var m=n(7521),g=n(6356),p=n(5564),h=n(9922);const u=(e,t,n)=>{const a="string"!==typeof(c=n)?c:c.charAt(0).toUpperCase()+c.slice(1);var c;return{["".concat(e.componentCls,"-").concat(t)]:{color:e["color".concat(n)],background:e["color".concat(a,"Bg")],borderColor:e["color".concat(a,"Border")],["&".concat(e.componentCls,"-borderless")]:{borderColor:"transparent"}}}},f=e=>(0,g.Z)(e,((t,n)=>{let{textColor:a,lightBorderColor:c,lightColor:o,darkColor:i}=n;return{["".concat(e.componentCls,"-").concat(t)]:{color:a,background:o,borderColor:c,"&-inverse":{color:e.colorTextLightSolid,background:i,borderColor:i},["&".concat(e.componentCls,"-borderless")]:{borderColor:"transparent"}}}})),x=e=>{const{paddingXXS:t,lineWidth:n,tagPaddingHorizontal:a,componentCls:c}=e,o=a-n,i=t-n;return{[c]:Object.assign(Object.assign({},(0,m.Wf)(e)),{display:"inline-block",height:"auto",marginInlineEnd:e.marginXS,paddingInline:o,fontSize:e.tagFontSize,lineHeight:e.tagLineHeight,whiteSpace:"nowrap",background:e.defaultBg,border:"".concat(e.lineWidth,"px ").concat(e.lineType," ").concat(e.colorBorder),borderRadius:e.borderRadiusSM,opacity:1,transition:"all ".concat(e.motionDurationMid),textAlign:"start",position:"relative",["&".concat(c,"-rtl")]:{direction:"rtl"},"&, a, a:hover":{color:e.defaultColor},["".concat(c,"-close-icon")]:{marginInlineStart:i,color:e.colorTextDescription,fontSize:e.tagIconSize,cursor:"pointer",transition:"all ".concat(e.motionDurationMid),"&:hover":{color:e.colorTextHeading}},["&".concat(c,"-has-color")]:{borderColor:"transparent",["&, a, a:hover, ".concat(e.iconCls,"-close, ").concat(e.iconCls,"-close:hover")]:{color:e.colorTextLightSolid}},"&-checkable":{backgroundColor:"transparent",borderColor:"transparent",cursor:"pointer",["&:not(".concat(c,"-checkable-checked):hover")]:{color:e.colorPrimary,backgroundColor:e.colorFillSecondary},"&:active, &-checked":{color:e.colorTextLightSolid},"&-checked":{backgroundColor:e.colorPrimary,"&:hover":{backgroundColor:e.colorPrimaryHover}},"&:active":{backgroundColor:e.colorPrimaryActive}},"&-hidden":{display:"none"},["> ".concat(e.iconCls," + span, > span + ").concat(e.iconCls)]:{marginInlineStart:o}}),["".concat(c,"-borderless")]:{borderColor:"transparent",background:e.tagBorderlessBg}}},v=(0,p.Z)("Tag",(e=>{const{lineWidth:t,fontSizeIcon:n}=e,a=e.fontSizeSM,c="".concat(e.lineHeightSM*a,"px"),o=(0,h.TS)(e,{tagFontSize:a,tagLineHeight:c,tagIconSize:n-2*t,tagPaddingHorizontal:8,tagBorderlessBg:e.colorFillTertiary});return[x(o),f(o),u(o,"success","Success"),u(o,"processing","Info"),u(o,"error","Error"),u(o,"warning","Warning")]}),(e=>({defaultBg:e.colorFillQuaternary,defaultColor:e.colorText})));var y=function(e,t){var n={};for(var a in e)Object.prototype.hasOwnProperty.call(e,a)&&t.indexOf(a)<0&&(n[a]=e[a]);if(null!=e&&"function"===typeof Object.getOwnPropertySymbols){var c=0;for(a=Object.getOwnPropertySymbols(e);c<a.length;c++)t.indexOf(a[c])<0&&Object.prototype.propertyIsEnumerable.call(e,a[c])&&(n[a[c]]=e[a[c]])}return n};const b=e=>{const{prefixCls:t,className:n,checked:a,onChange:c,onClick:r}=e,l=y(e,["prefixCls","className","checked","onChange","onClick"]),{getPrefixCls:s}=i.useContext(d.E_),m=s("tag",t),[g,p]=v(m),h=o()(m,"".concat(m,"-checkable"),{["".concat(m,"-checkable-checked")]:a},n,p);return g(i.createElement("span",Object.assign({},l,{className:h,onClick:e=>{null===c||void 0===c||c(!a),null===r||void 0===r||r(e)}})))};var S=function(e,t){var n={};for(var a in e)Object.prototype.hasOwnProperty.call(e,a)&&t.indexOf(a)<0&&(n[a]=e[a]);if(null!=e&&"function"===typeof Object.getOwnPropertySymbols){var c=0;for(a=Object.getOwnPropertySymbols(e);c<a.length;c++)t.indexOf(a[c])<0&&Object.prototype.propertyIsEnumerable.call(e,a[c])&&(n[a[c]]=e[a[c]])}return n};const C=(e,t)=>{const{prefixCls:n,className:c,rootClassName:m,style:g,children:p,icon:h,color:u,onClose:f,closeIcon:x,closable:y,bordered:b=!0}=e,C=S(e,["prefixCls","className","rootClassName","style","children","icon","color","onClose","closeIcon","closable","bordered"]),{getPrefixCls:j,direction:k,tag:N}=i.useContext(d.E_),[E,w]=i.useState(!0);i.useEffect((()=>{"visible"in C&&w(C.visible)}),[C.visible]);const O=(0,r.o2)(u)||(0,r.yT)(u),P=Object.assign(Object.assign({backgroundColor:u&&!O?u:void 0},null===N||void 0===N?void 0:N.style),g),Z=j("tag",n),[z,L]=v(Z),I=o()(Z,null===N||void 0===N?void 0:N.className,{["".concat(Z,"-").concat(u)]:O,["".concat(Z,"-has-color")]:u&&!O,["".concat(Z,"-hidden")]:!E,["".concat(Z,"-rtl")]:"rtl"===k,["".concat(Z,"-borderless")]:!b},c,m,L),T=e=>{e.stopPropagation(),null===f||void 0===f||f(e),e.defaultPrevented||w(!1)},[,B]=(0,l.Z)(y,x,(e=>null===e?i.createElement(a.Z,{className:"".concat(Z,"-close-icon"),onClick:T}):i.createElement("span",{className:"".concat(Z,"-close-icon"),onClick:T},e)),null,!1),M="function"===typeof C.onClick||p&&"a"===p.type,H=h||null,W=H?i.createElement(i.Fragment,null,H,p&&i.createElement("span",null,p)):p,A=i.createElement("span",Object.assign({},C,{ref:t,className:I,style:P}),W,B);return z(M?i.createElement(s.Z,{component:"Tag"},A):A)},j=i.forwardRef(C);j.CheckableTag=b;const k=j}}]);
//# sourceMappingURL=657.51686ba3.chunk.js.map