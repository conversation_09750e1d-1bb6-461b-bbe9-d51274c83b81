{"version": 3, "file": "static/css/723.d095fb3c.chunk.css", "mappings": "AAGA,eAEE,0EAAuF,CAEvF,kEAA8E,CAH9E,gBAAiB,CAKjB,iBAAkB,CAHlB,YAAa,CAEb,iBAAkB,CAElB,sBACF,CAEA,sBAYE,kDAAmD,CALnD,qLAGiF,CAJjF,QAAS,CALT,UAAW,CAGX,MAAO,CAFP,iBAAkB,CAGlB,OAAQ,CAFR,KAAM,CAQN,SAEF,CAEA,2BACE,MAAW,qCAAwC,CACnD,IAAM,4CAA+C,CACrD,IAAM,2CAA8C,CACpD,IAAM,4CAA+C,CACvD,CAEA,aAOE,gCAAiC,CAHjC,aAAc,CADd,gBAAiB,CAEjB,eAAgB,CAJhB,iBAAkB,CAKlB,sBAAuB,CAJvB,SAMF,CAGA,YAQE,kCAA2B,CAA3B,0BAA2B,CAJ3B,oBAAqC,CAGrC,sBAA0C,CAF1C,oBAAqB,CACrB,qCAA+C,CAJ/C,kBAAmB,CASnB,gBAAiB,CACjB,iBAAkB,CAFlB,eAAgB,CAPhB,cAAe,CAMf,iBAAkB,CARlB,iBAYF,CAGA,sBACE,iBAAkB,CAElB,YAAa,CADb,UAAW,CAEX,UACF,CAEA,gBAEE,kBAAmB,CAYnB,kCAA2B,CAA3B,0BAA2B,CAT3B,kDAA6D,CAE7D,WAAY,CACZ,kBAAmB,CAKnB,+BAA6C,CAP7C,UAAY,CAKZ,cAAe,CAVf,YAAa,CASb,iBAAmB,CADnB,eAAgB,CANhB,SAAW,CACX,sBAAwB,CAQxB,uBAGF,CAEA,sBACE,kDAA6D,CAC7D,+BAA6C,CAC7C,0BACF,CAEA,uBAEE,8BAA4C,CAD5C,uBAEF,CAEA,aACE,cAAe,CACf,6BACF,CAEA,mCACE,wBACF,CAEA,aACE,eAAgB,CAChB,qBACF,CAGA,yBACE,sBAEE,UAAW,CADX,QAEF,CAEA,gBAEE,eAAiB,CADjB,kBAEF,CAEA,aACE,YACF,CAEA,aACE,gBACF,CACF,CAEA,qBAKE,6BAAoC,CAFpC,8DAA0E,CAC1E,4BAA6B,CAE7B,oBAAqB,CALrB,cAAe,CACf,eAAgB,CAOhB,qBAAuB,CADvB,eAAgB,CADhB,kBAAmB,CAGnB,+BACF,CAEA,aAKE,6BAAoC,CAFpC,8DAA0E,CAC1E,4BAA6B,CAE7B,oBAAqB,CALrB,iBAAkB,CAClB,eAAgB,CAOhB,sBAAwB,CADxB,eAAgB,CADhB,kBAAmB,CAGnB,iBACF,CAEA,cAEE,aAAc,CADd,kBAAmB,CAKnB,eAAgB,CAFhB,eAAgB,CADhB,oBAAqB,CAErB,iBAEF,CAEA,WAKE,oBAAmC,CACnC,6BAA8B,CAC9B,mBAAqB,CALrB,aAAc,CADd,iBAAmB,CAEnB,iBAAkB,CAKlB,aAAc,CACd,eAAgB,CALhB,qBAMF,CAGA,oBACE,kBACF,CAEA,UAGE,aAAS,CAFT,YAAa,CAEb,QAAS,CADT,wDAA2D,CAE3D,kBACF,CAGA,yBACE,UAEE,UAAW,CADX,yBAEF,CAEA,eACE,qBACF,CAEA,YAEE,kBAAmB,CADnB,cAEF,CAEA,aACE,gBACF,CAMA,yBAHE,cAMF,CAHA,WAEE,mBACF,CAEA,UACE,mBACF,CAEA,eAGE,cAAe,CADf,WAAY,CADZ,UAGF,CAEA,gBACE,iBACF,CAEA,sBACE,eACF,CACF,CAGA,gDACE,UAEE,WAAY,CADZ,mCAEF,CAEA,eACE,oBACF,CAEA,YAEE,oBAAqB,CADrB,mBAEF,CAEA,aACE,iBACF,CAEA,UACE,oBACF,CAEA,eAGE,kBAAmB,CADnB,WAAY,CADZ,UAGF,CACF,CAGA,0BACE,UAEE,UAAW,CADX,wDAEF,CAEA,eACE,mBACF,CAEA,YAEE,kBAAmB,CADnB,mBAEF,CACF,CAGA,UAUE,kCAA2B,CAA3B,0BAA2B,CAT3B,sDAAgG,CAQhG,0BAAyC,CAPzC,kBAAmB,CAGnB,oFAGqC,CAGrC,cAAe,CAGf,eAAgB,CAXhB,cAAe,CAUf,iBAAkB,CATlB,iBAAkB,CAQlB,oDAGF,CAEA,iBAOE,8DAA0E,CAG1E,2BAA4B,CAJ5B,UAAW,CAEX,mBAAoB,CACpB,gDAEF,CAEA,iCAZE,UAAW,CAGX,MAAO,CAFP,iBAAkB,CAGlB,OAAQ,CAFR,KAsBF,CAZA,gBAOE,sDAA4F,CAG5F,kBAAmB,CAJnB,QAAS,CAET,SAAU,CACV,2BAA6B,CAE7B,SACF,CAEA,uBACE,mBACF,CAEA,sBACE,SACF,CAEA,gBAME,sBAAoC,CAJpC,qFAGkC,CAJlC,qDAMF,CAEA,YACE,iBAAkB,CAClB,SACF,CAEA,eAOE,kBAAmB,CAHnB,8DAA0E,CAC1E,iBAAkB,CAMlB,+DAEwC,CAHxC,UAAY,CAJZ,YAAa,CAGb,iBAAkB,CAPlB,WAAY,CAMZ,sBAAuB,CALvB,kBAAmB,CAanB,eAAgB,CADhB,iBAAkB,CADlB,oDAA4D,CAb5D,UAgBF,CAEA,sBAOE,kDAAiF,CACjF,iBAAkB,CAFlB,QAAS,CALT,UAAW,CAGX,MAAO,CAKP,SAAU,CAPV,iBAAkB,CAGlB,OAAQ,CAFR,KAAM,CAON,2BACF,CAEA,+BAEE,iEAEyC,CAHzC,mDAIF,CAEA,sCACE,SACF,CAEA,gBAGE,aAAc,CAFd,gBAAiB,CACjB,eAAgB,CAGhB,eAAgB,CADhB,oBAEF,CAEA,sBAEE,aAAc,CADd,cAAe,CAEf,eAAgB,CAChB,QACF,CAyCA,mBAAqB,mBAAuB,CAC5C,mBAAqB,mBAAuB,CAC5C,mBAAqB,mBAAuB,CAG5C,iBACE,MACE,uBACF,CACA,IACE,2BACF,CACF,CAEA,eACE,uCACF,CAGA,kBACE,MACE,uBACF,CACA,IACE,0BACF,CACF,CAEA,gBACE,4BACF,CAGA,uBAME,kCAA2B,CAA3B,0BAA2B,CAF3B,oBAAoC,CACpC,oBAAqB,CAHrB,eAAgB,CAChB,YAAa,CAFb,iBAMF,CAEA,2CAEE,kBAAmB,CAEnB,aAAc,CAHd,mBAAoB,CAIpB,kBAAmB,CACnB,eAAgB,CAHhB,QAIF,CAEA,wCAEE,aAAc,CADd,gBAEF,CAKA,yBACE,eACE,oBACF,CAEA,YAGE,kBAAmB,CADnB,oBAAqB,CADrB,YAGF,CAEA,aACE,iBAAkB,CAClB,oBACF,CAEA,cACE,cAAe,CACf,eACF,CAEA,WACE,gBAAkB,CAElB,QAAS,CACT,cAAe,CAFf,oBAGF,CAEA,UAEE,QAAS,CADT,yBAA0B,CAE1B,oBACF,CAEA,UAOE,kBAAmB,CALnB,kBAAmB,CAEnB,YAAa,CACb,qBAAsB,CACtB,sBAAuB,CAHvB,gBAAiB,CAFjB,cAOF,CAEA,eAGE,gBAAiB,CADjB,WAAY,CAEZ,kBAAmB,CAHnB,UAIF,CAEA,gBACE,iBAAkB,CAClB,mBACF,CAEA,sBACE,iBACF,CAEA,uBAGE,kBAAmB,CAFnB,eAAgB,CAChB,cAEF,CAEA,2CACE,qBAAsB,CAEtB,cAAe,CADf,SAEF,CAEA,wCACE,iBACF,CACF,CAGA,+CACE,eACE,mBACF,CAEA,YAEE,qBAAsB,CADtB,eAEF,CAEA,aACE,cACF,CAEA,cACE,mBACF,CAEA,WACE,kBAAoB,CACpB,oBACF,CAEA,UAEE,UAAW,CADX,mCAEF,CAEA,UACE,eACF,CAEA,eAGE,iBAAkB,CADlB,WAAY,CAEZ,qBAAsB,CAHtB,UAIF,CAEA,gBACE,kBACF,CAEA,sBACE,kBACF,CAEA,uBACE,eAAgB,CAChB,eACF,CACF,CAGA,gDACE,eACE,mBACF,CAEA,YAEE,kBAAmB,CADnB,YAEF,CAEA,UAEE,WAAY,CADZ,mCAEF,CAEA,UACE,YACF,CAEA,eAGE,kBAAmB,CADnB,WAAY,CADZ,UAGF,CACF,CAGA,iDACE,eACE,mBACF,CAEA,YAEE,oBAAqB,CADrB,cAEF,CAEA,aACE,gBACF,CAEA,cACE,kBACF,CAEA,WACE,mBAAoB,CACpB,sBACF,CAEA,UAEE,QAAS,CADT,mCAEF,CAEA,UACE,cACF,CAEA,eAGE,kBAAmB,CADnB,WAAY,CAEZ,qBAAsB,CAHtB,UAIF,CAEA,gBACE,kBAAmB,CACnB,kBACF,CAEA,sBACE,mBACF,CAEA,uBACE,iBAAkB,CAClB,cACF,CAEA,2CACE,iBACF,CAEA,wCACE,iBACF,CACF,CAGA,0BACE,eACE,mBACF,CAEA,aACE,gBACF,CAEA,YAEE,kBAAmB,CADnB,YAEF,CAEA,aACE,cACF,CAEA,cACE,gBACF,CAEA,WACE,iBAAkB,CAElB,eAAgB,CADhB,mBAEF,CAEA,UAEE,UAAW,CADX,mCAEF,CAEA,UACE,YACF,CAEA,eAGE,gBAAiB,CADjB,YAAa,CAEb,kBAAmB,CAHnB,WAIF,CAEA,gBACE,iBAAkB,CAClB,qBACF,CAEA,sBACE,kBACF,CAEA,uBACE,eAAgB,CAChB,YACF,CAEA,2CACE,kBAAmB,CACnB,UACF,CAEA,wCACE,cACF,CACF,CAGA,yBACE,MACE,uBACF,CACA,IACE,0BACF,CACF,CAEA,uBACE,+CACF,CAGA,yBACE,GACE,sBACF,CACA,IACE,sBACF,CACA,IACE,uBACF,CACA,GACE,sBACF,CACF,CAEA,uBACE,+CACF,CAGA,uBACE,MACE,kBACF,CACA,IACE,qBACF,CACF,CAEA,qBACE,6CACF,CAKA,sDACE,eACE,mBACF,CAEA,YAEE,oBAAqB,CADrB,YAEF,CAEA,aACE,iBAAkB,CAClB,mBACF,CAEA,cACE,iBAAmB,CACnB,oBACF,CAEA,WACE,eAAiB,CACjB,mBACF,CAEA,UAEE,QAAS,CADT,wDAA2D,CAE3D,kBACF,CAEA,UACE,YACF,CAEA,eAGE,iBAAkB,CADlB,WAAY,CAEZ,oBAAsB,CAHtB,UAIF,CAEA,gBACE,cAAe,CACf,qBACF,CAEA,sBACE,eACF,CAEA,uBACE,iBAAkB,CAClB,YACF,CACF,CAGA,yBACE,eACE,oBACF,CAEA,YAEE,oBAAsB,CADtB,YAEF,CAEA,aACE,iBACF,CAEA,cACE,iBACF,CAEA,WACE,kBAAoB,CACpB,cACF,CAEA,UAEE,oBAAsB,CADtB,eAEF,CAEA,eAGE,iBAAkB,CADlB,WAAY,CADZ,UAGF,CAEA,gBACE,kBACF,CAEA,sBACE,kBACF,CACF,CAKA,gBACE,yBAA0B,CAC1B,kBACF,CAEA,oCACE,YACF,CAGA,uCACE,kEAIE,wBAA0B,CAC1B,yBACF,CAEA,gBACE,wBACF,CACF,CAGA,uBACE,MACE,yBACF,CACA,IACE,4BACF,CACF,CAEA,qBAEE,sCAAuC,CADvC,yBAEF,CAGA,oBACE,GACE,SAAU,CACV,0BACF,CACA,GACE,SAAU,CACV,uBACF,CACF,CAEA,kBACE,+BACF,CAGA,mBACE,mBACF,CAEA,mBACE,mBACF,CAEA,mBACE,mBACF,CAEA,mBACE,mBACF,CAEA,mBACE,mBACF,CAEA,mBACE,mBACF,CAGA,yBACE,cACE,sBACF,CAEA,gBACE,sCACF,CACF,CAGA,mCACE,oBACE,4CACF,CACF", "sources": ["pages/user/Hub/Hub.css"], "sourcesContent": ["/* ===== RESPONSIVE HUB PAGE ===== */\n\n/* ===== BASE STYLES ===== */\n.hub-container {\n  min-height: 100vh;\n  background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 25%, #f8fafc 50%, #e2e8f0 100%);\n  padding: 1rem;\n  font-family: 'Inter', 'Roboto', 'Nunito', system-ui, -apple-system, sans-serif;\n  position: relative;\n  overflow-x: hidden;\n  scroll-behavior: smooth;\n}\n\n.hub-container::before {\n  content: '';\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background:\n    radial-gradient(circle at 20% 30%, rgba(0, 123, 255, 0.12) 0%, transparent 50%),\n    radial-gradient(circle at 80% 70%, rgba(0, 86, 210, 0.1) 0%, transparent 50%),\n    radial-gradient(circle at 50% 50%, rgba(0, 212, 255, 0.05) 0%, transparent 60%);\n  z-index: 0;\n  animation: backgroundShift 20s ease-in-out infinite;\n}\n\n@keyframes backgroundShift {\n  0%, 100% { transform: translateX(0) translateY(0); }\n  25% { transform: translateX(10px) translateY(-10px); }\n  50% { transform: translateX(-5px) translateY(10px); }\n  75% { transform: translateX(-10px) translateY(-5px); }\n}\n\n.hub-content {\n  position: relative;\n  z-index: 1;\n  max-width: 1200px;\n  margin: 0 auto;\n  overflow-y: auto;\n  scroll-behavior: smooth;\n  -webkit-overflow-scrolling: touch;\n}\n\n/* ===== HEADER SECTION ===== */\n.hub-header {\n  text-align: center;\n  margin-bottom: 2rem;\n  padding: 1.5rem;\n  background: rgba(255, 255, 255, 0.95);\n  border-radius: 1.5rem;\n  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);\n  border: 1px solid rgba(255, 255, 255, 0.2);\n  backdrop-filter: blur(10px);\n  position: relative;\n  max-width: 800px;\n  margin-left: auto;\n  margin-right: auto;\n}\n\n/* ===== LOGOUT BUTTON STYLES ===== */\n.hub-logout-container {\n  position: absolute;\n  top: 1.5rem;\n  right: 1.5rem;\n  z-index: 10;\n}\n\n.hub-logout-btn {\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n  padding: 0.75rem 1.25rem;\n  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);\n  color: white;\n  border: none;\n  border-radius: 1rem;\n  font-weight: 600;\n  font-size: 0.875rem;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  box-shadow: 0 4px 12px rgba(239, 68, 68, 0.3);\n  backdrop-filter: blur(10px);\n}\n\n.hub-logout-btn:hover {\n  background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);\n  box-shadow: 0 6px 20px rgba(239, 68, 68, 0.4);\n  transform: translateY(-2px);\n}\n\n.hub-logout-btn:active {\n  transform: translateY(0);\n  box-shadow: 0 2px 8px rgba(239, 68, 68, 0.3);\n}\n\n.logout-icon {\n  font-size: 1rem;\n  transition: transform 0.3s ease;\n}\n\n.hub-logout-btn:hover .logout-icon {\n  transform: rotate(-10deg);\n}\n\n.logout-text {\n  font-weight: 600;\n  letter-spacing: 0.025em;\n}\n\n/* Responsive logout button */\n@media (max-width: 768px) {\n  .hub-logout-container {\n    top: 1rem;\n    right: 1rem;\n  }\n\n  .hub-logout-btn {\n    padding: 0.5rem 1rem;\n    font-size: 0.8rem;\n  }\n\n  .logout-text {\n    display: none;\n  }\n\n  .logout-icon {\n    font-size: 1.1rem;\n  }\n}\n\n.hub-brainwave-title {\n  font-size: 4rem;\n  font-weight: 900;\n  background: linear-gradient(135deg, #007BFF 0%, #0056D2 50%, #4338CA 100%);\n  -webkit-background-clip: text;\n  -webkit-text-fill-color: transparent;\n  background-clip: text;\n  margin-bottom: 1rem;\n  line-height: 1.1;\n  letter-spacing: -0.02em;\n  text-shadow: 0 4px 8px rgba(0, 123, 255, 0.15);\n}\n\n.hub-welcome {\n  font-size: 2.25rem;\n  font-weight: 800;\n  background: linear-gradient(135deg, #1e40af 0%, #3b82f6 50%, #60a5fa 100%);\n  -webkit-background-clip: text;\n  -webkit-text-fill-color: transparent;\n  background-clip: text;\n  margin-bottom: 1rem;\n  line-height: 1.3;\n  letter-spacing: -0.025em;\n  text-align: center;\n}\n\n.hub-subtitle {\n  font-size: 1.125rem;\n  color: #6b7280;\n  margin-bottom: 1.5rem;\n  line-height: 1.6;\n  text-align: center;\n  font-weight: 500;\n}\n\n.hub-quote {\n  font-size: 0.875rem;\n  color: #374151;\n  font-style: italic;\n  padding: 0.75rem 1.5rem;\n  background: rgba(0, 123, 255, 0.05);\n  border-left: 3px solid #007BFF;\n  border-radius: 0.5rem;\n  margin: 0 auto;\n  max-width: 500px;\n}\n\n/* ===== ENHANCED RESPONSIVE GRID LAYOUT ===== */\n.hub-grid-container {\n  margin-bottom: 3rem;\n}\n\n.hub-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));\n  gap: 2rem;\n  margin-bottom: 2rem;\n}\n\n/* Mobile: Single column */\n@media (max-width: 640px) {\n  .hub-grid {\n    grid-template-columns: 1fr;\n    gap: 1.5rem;\n  }\n\n  .hub-container {\n    padding: 1.5rem 0.75rem;\n  }\n\n  .hub-header {\n    padding: 1.5rem;\n    margin-bottom: 2rem;\n  }\n\n  .hub-welcome {\n    font-size: 1.5rem;\n  }\n\n  .hub-subtitle {\n    font-size: 1rem;\n  }\n\n  .hub-quote {\n    font-size: 1rem;\n    padding: 0.75rem 1rem;\n  }\n\n  .hub-card {\n    padding: 2rem 1.5rem;\n  }\n\n  .hub-card-icon {\n    width: 70px;\n    height: 70px;\n    font-size: 2rem;\n  }\n\n  .hub-card-title {\n    font-size: 1.25rem;\n  }\n\n  .hub-card-description {\n    font-size: 0.9rem;\n  }\n}\n\n/* Tablet: Two columns */\n@media (min-width: 641px) and (max-width: 1024px) {\n  .hub-grid {\n    grid-template-columns: repeat(2, 1fr);\n    gap: 1.75rem;\n  }\n\n  .hub-container {\n    padding: 1.75rem 1rem;\n  }\n\n  .hub-header {\n    padding: 2rem 1.5rem;\n    margin-bottom: 2.5rem;\n  }\n\n  .hub-welcome {\n    font-size: 1.75rem;\n  }\n\n  .hub-card {\n    padding: 2.25rem 2rem;\n  }\n\n  .hub-card-icon {\n    width: 80px;\n    height: 80px;\n    font-size: 2.125rem;\n  }\n}\n\n/* Desktop: Optimized layout */\n@media (min-width: 1025px) {\n  .hub-grid {\n    grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));\n    gap: 2.5rem;\n  }\n\n  .hub-container {\n    padding: 2rem 1.5rem;\n  }\n\n  .hub-header {\n    padding: 2.5rem 2rem;\n    margin-bottom: 3rem;\n  }\n}\n\n/* ===== CARD STYLES ===== */\n.hub-card {\n  background: linear-gradient(145deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 250, 252, 0.9) 100%);\n  border-radius: 2rem;\n  padding: 2.5rem;\n  text-align: center;\n  box-shadow:\n    0 20px 25px -5px rgba(0, 123, 255, 0.1),\n    0 10px 10px -5px rgba(0, 123, 255, 0.04),\n    0 0 0 1px rgba(255, 255, 255, 0.05);\n  border: 1px solid rgba(0, 123, 255, 0.08);\n  backdrop-filter: blur(20px);\n  cursor: pointer;\n  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);\n  position: relative;\n  overflow: hidden;\n}\n\n.hub-card::before {\n  content: '';\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  height: 5px;\n  background: linear-gradient(135deg, #007BFF 0%, #0056D2 50%, #00D4FF 100%);\n  transform: scaleX(0);\n  transition: transform 0.4s cubic-bezier(0.4, 0, 0.2, 1);\n  border-radius: 2rem 2rem 0 0;\n}\n\n.hub-card::after {\n  content: '';\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: linear-gradient(135deg, rgba(0, 123, 255, 0.02) 0%, rgba(0, 86, 210, 0.05) 100%);\n  opacity: 0;\n  transition: opacity 0.3s ease;\n  border-radius: 2rem;\n  z-index: 0;\n}\n\n.hub-card:hover::before {\n  transform: scaleX(1);\n}\n\n.hub-card:hover::after {\n  opacity: 1;\n}\n\n.hub-card:hover {\n  transform: translateY(-12px) scale(1.03) rotateX(2deg);\n  box-shadow:\n    0 32px 64px -12px rgba(0, 123, 255, 0.25),\n    0 20px 25px -5px rgba(0, 123, 255, 0.1),\n    0 0 0 1px rgba(0, 123, 255, 0.1);\n  border-color: rgba(0, 123, 255, 0.2);\n}\n\n.hub-card > * {\n  position: relative;\n  z-index: 1;\n}\n\n.hub-card-icon {\n  width: 90px;\n  height: 90px;\n  margin: 0 auto 2rem;\n  background: linear-gradient(135deg, #007BFF 0%, #0056D2 50%, #4338CA 100%);\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 2.25rem;\n  color: white;\n  box-shadow:\n    0 15px 25px -5px rgba(0, 123, 255, 0.4),\n    0 8px 10px -6px rgba(0, 123, 255, 0.3);\n  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);\n  position: relative;\n  overflow: hidden;\n}\n\n.hub-card-icon::before {\n  content: '';\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, transparent 50%);\n  border-radius: 50%;\n  opacity: 0;\n  transition: opacity 0.3s ease;\n}\n\n.hub-card:hover .hub-card-icon {\n  transform: scale(1.15) rotate(8deg) translateY(-5px);\n  box-shadow:\n    0 25px 50px -12px rgba(0, 123, 255, 0.5),\n    0 15px 25px -5px rgba(0, 123, 255, 0.4);\n}\n\n.hub-card:hover .hub-card-icon::before {\n  opacity: 1;\n}\n\n.hub-card-title {\n  font-size: 1.5rem;\n  font-weight: 700;\n  color: #1f2937;\n  margin-bottom: 0.75rem;\n  line-height: 1.3;\n}\n\n.hub-card-description {\n  font-size: 1rem;\n  color: #6b7280;\n  line-height: 1.5;\n  margin: 0;\n}\n\n/* ===== ANIMATIONS ===== */\n\n/* Fade in up animation */\n@keyframes fadeInUp {\n  from {\n    opacity: 0;\n    transform: translateY(30px);\n  }\n  to {\n    opacity: 1;\n    transform: translateY(0);\n  }\n}\n\n.animate-fadeInUp {\n  animation: fadeInUp 0.6s ease-out;\n}\n\n/* Bounce animation */\n@keyframes bounce-gentle {\n  0%, 100% {\n    transform: translateY(0);\n  }\n  50% {\n    transform: translateY(-5px);\n  }\n}\n\n.animate-bounce-gentle {\n  animation: bounce-gentle 2s ease-in-out infinite;\n}\n\n/* Stagger animation delays */\n.animate-delay-100 { animation-delay: 0.1s; }\n.animate-delay-200 { animation-delay: 0.2s; }\n.animate-delay-300 { animation-delay: 0.3s; }\n.animate-delay-400 { animation-delay: 0.4s; }\n.animate-delay-500 { animation-delay: 0.5s; }\n.animate-delay-600 { animation-delay: 0.6s; }\n.animate-delay-700 { animation-delay: 0.7s; }\n.animate-delay-800 { animation-delay: 0.8s; }\n.animate-delay-900 { animation-delay: 0.9s; }\n\n/* Float animation for icons */\n@keyframes float {\n  0%, 100% {\n    transform: translateY(0px);\n  }\n  50% {\n    transform: translateY(-10px);\n  }\n}\n\n.animate-float {\n  animation: float 3s ease-in-out infinite;\n}\n\n/* Bounce animation for icons */\n@keyframes bounce {\n  0%, 100% {\n    transform: translateY(0);\n  }\n  50% {\n    transform: translateY(-25%);\n  }\n}\n\n.animate-bounce {\n  animation: bounce 1s infinite;\n}\n\n/* ===== BOTTOM DECORATION ===== */\n.hub-bottom-decoration {\n  text-align: center;\n  margin-top: 4rem;\n  padding: 2rem;\n  background: rgba(255, 255, 255, 0.7);\n  border-radius: 1.5rem;\n  backdrop-filter: blur(10px);\n}\n\n.hub-bottom-decoration .decoration-content {\n  display: inline-flex;\n  align-items: center;\n  gap: 1rem;\n  color: #6b7280;\n  font-size: 1.125rem;\n  font-weight: 600;\n}\n\n.hub-bottom-decoration .decoration-icon {\n  font-size: 1.5rem;\n  color: #007BFF;\n}\n\n/* ===== RESPONSIVE BREAKPOINTS ===== */\n\n/* Mobile Devices (320px - 480px) */\n@media (max-width: 480px) {\n  .hub-container {\n    padding: 0.75rem 0.5rem;\n  }\n\n  .hub-header {\n    padding: 1rem;\n    margin-bottom: 1.5rem;\n    border-radius: 1rem;\n  }\n\n  .hub-welcome {\n    font-size: 1.75rem;\n    margin-bottom: 0.75rem;\n  }\n\n  .hub-subtitle {\n    font-size: 1rem;\n    margin-bottom: 0;\n  }\n\n  .hub-quote {\n    font-size: 0.75rem;\n    padding: 0.5rem 0.75rem;\n    margin: 0;\n    max-width: 100%;\n  }\n\n  .hub-grid {\n    grid-template-columns: 1fr;\n    gap: 1rem;\n    margin-bottom: 1.5rem;\n  }\n\n  .hub-card {\n    padding: 1.5rem;\n    border-radius: 1rem;\n    min-height: 120px; /* Touch-friendly minimum */\n    display: flex;\n    flex-direction: column;\n    justify-content: center;\n    align-items: center;\n  }\n\n  .hub-card-icon {\n    width: 60px;\n    height: 60px;\n    font-size: 1.5rem;\n    margin-bottom: 1rem;\n  }\n\n  .hub-card-title {\n    font-size: 1.25rem;\n    margin-bottom: 0.5rem;\n  }\n\n  .hub-card-description {\n    font-size: 0.875rem;\n  }\n\n  .hub-bottom-decoration {\n    margin-top: 2rem;\n    padding: 1.5rem;\n    border-radius: 1rem;\n  }\n\n  .hub-bottom-decoration .decoration-content {\n    flex-direction: column;\n    gap: 0.5rem;\n    font-size: 1rem;\n  }\n\n  .hub-bottom-decoration .decoration-icon {\n    font-size: 1.25rem;\n  }\n}\n\n/* Tablet Devices (481px - 768px) */\n@media (min-width: 481px) and (max-width: 768px) {\n  .hub-container {\n    padding: 1rem 0.75rem;\n  }\n\n  .hub-header {\n    padding: 1.25rem;\n    margin-bottom: 1.75rem;\n  }\n\n  .hub-welcome {\n    font-size: 2rem;\n  }\n\n  .hub-subtitle {\n    font-size: 1.0625rem;\n  }\n\n  .hub-quote {\n    font-size: 0.8125rem;\n    padding: 0.625rem 1rem;\n  }\n\n  .hub-grid {\n    grid-template-columns: repeat(2, 1fr);\n    gap: 1.5rem;\n  }\n\n  .hub-card {\n    padding: 1.75rem;\n  }\n\n  .hub-card-icon {\n    width: 70px;\n    height: 70px;\n    font-size: 1.75rem;\n    margin-bottom: 1.25rem;\n  }\n\n  .hub-card-title {\n    font-size: 1.375rem;\n  }\n\n  .hub-card-description {\n    font-size: 0.9375rem;\n  }\n\n  .hub-bottom-decoration {\n    margin-top: 3rem;\n    padding: 1.75rem;\n  }\n}\n\n/* Desktop Devices (769px - 1024px) */\n@media (min-width: 769px) and (max-width: 1024px) {\n  .hub-container {\n    padding: 2rem 1.5rem;\n  }\n\n  .hub-header {\n    padding: 2rem;\n    margin-bottom: 3rem;\n  }\n\n  .hub-grid {\n    grid-template-columns: repeat(3, 1fr);\n    gap: 1.75rem;\n  }\n\n  .hub-card {\n    padding: 2rem;\n  }\n\n  .hub-card-icon {\n    width: 75px;\n    height: 75px;\n    font-size: 1.875rem;\n  }\n}\n\n/* Large Desktop (1025px - 1440px) */\n@media (min-width: 1025px) and (max-width: 1440px) {\n  .hub-container {\n    padding: 2.5rem 2rem;\n  }\n\n  .hub-header {\n    padding: 2.5rem;\n    margin-bottom: 3.5rem;\n  }\n\n  .hub-welcome {\n    font-size: 3.5rem;\n  }\n\n  .hub-subtitle {\n    font-size: 1.375rem;\n  }\n\n  .hub-quote {\n    font-size: 1.1875rem;\n    padding: 1.25rem 2.5rem;\n  }\n\n  .hub-grid {\n    grid-template-columns: repeat(3, 1fr);\n    gap: 2rem;\n  }\n\n  .hub-card {\n    padding: 2.5rem;\n  }\n\n  .hub-card-icon {\n    width: 85px;\n    height: 85px;\n    font-size: 2.125rem;\n    margin-bottom: 1.75rem;\n  }\n\n  .hub-card-title {\n    font-size: 1.625rem;\n    margin-bottom: 1rem;\n  }\n\n  .hub-card-description {\n    font-size: 1.0625rem;\n  }\n\n  .hub-bottom-decoration {\n    margin-top: 4.5rem;\n    padding: 2.5rem;\n  }\n\n  .hub-bottom-decoration .decoration-content {\n    font-size: 1.25rem;\n  }\n\n  .hub-bottom-decoration .decoration-icon {\n    font-size: 1.75rem;\n  }\n}\n\n/* Ultra-Wide Screens (1441px+) */\n@media (min-width: 1441px) {\n  .hub-container {\n    padding: 3rem 2.5rem;\n  }\n\n  .hub-content {\n    max-width: 1600px;\n  }\n\n  .hub-header {\n    padding: 3rem;\n    margin-bottom: 4rem;\n  }\n\n  .hub-welcome {\n    font-size: 4rem;\n  }\n\n  .hub-subtitle {\n    font-size: 1.5rem;\n  }\n\n  .hub-quote {\n    font-size: 1.25rem;\n    padding: 1.5rem 3rem;\n    max-width: 800px;\n  }\n\n  .hub-grid {\n    grid-template-columns: repeat(4, 1fr);\n    gap: 2.5rem;\n  }\n\n  .hub-card {\n    padding: 3rem;\n  }\n\n  .hub-card-icon {\n    width: 100px;\n    height: 100px;\n    font-size: 2.5rem;\n    margin-bottom: 2rem;\n  }\n\n  .hub-card-title {\n    font-size: 1.75rem;\n    margin-bottom: 1.25rem;\n  }\n\n  .hub-card-description {\n    font-size: 1.125rem;\n  }\n\n  .hub-bottom-decoration {\n    margin-top: 5rem;\n    padding: 3rem;\n  }\n\n  .hub-bottom-decoration .decoration-content {\n    font-size: 1.375rem;\n    gap: 1.5rem;\n  }\n\n  .hub-bottom-decoration .decoration-icon {\n    font-size: 2rem;\n  }\n}\n\n/* Bounce animation */\n@keyframes bounce-gentle {\n  0%, 100% {\n    transform: translateY(0);\n  }\n  50% {\n    transform: translateY(-5px);\n  }\n}\n\n.animate-bounce-gentle {\n  animation: bounce-gentle 2s ease-in-out infinite;\n}\n\n/* Rotate animation */\n@keyframes rotate-gentle {\n  0% {\n    transform: rotate(0deg);\n  }\n  25% {\n    transform: rotate(1deg);\n  }\n  75% {\n    transform: rotate(-1deg);\n  }\n  100% {\n    transform: rotate(0deg);\n  }\n}\n\n.animate-rotate-gentle {\n  animation: rotate-gentle 4s ease-in-out infinite;\n}\n\n/* Scale pulse */\n@keyframes scale-pulse {\n  0%, 100% {\n    transform: scale(1);\n  }\n  50% {\n    transform: scale(1.05);\n  }\n}\n\n.animate-scale-pulse {\n  animation: scale-pulse 3s ease-in-out infinite;\n}\n\n/* ===== ADDITIONAL RESPONSIVE STYLES ===== */\n\n/* Landscape Mobile (max-height: 500px and orientation: landscape) */\n@media (max-height: 500px) and (orientation: landscape) {\n  .hub-container {\n    padding: 1rem 0.75rem;\n  }\n\n  .hub-header {\n    padding: 1rem;\n    margin-bottom: 1.5rem;\n  }\n\n  .hub-welcome {\n    font-size: 1.75rem;\n    margin-bottom: 0.5rem;\n  }\n\n  .hub-subtitle {\n    font-size: 0.875rem;\n    margin-bottom: 0.75rem;\n  }\n\n  .hub-quote {\n    font-size: 0.8rem;\n    padding: 0.75rem 1rem;\n  }\n\n  .hub-grid {\n    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n    gap: 1rem;\n    margin-bottom: 1rem;\n  }\n\n  .hub-card {\n    padding: 1rem;\n  }\n\n  .hub-card-icon {\n    width: 40px;\n    height: 40px;\n    font-size: 1.25rem;\n    margin-bottom: 0.75rem;\n  }\n\n  .hub-card-title {\n    font-size: 1rem;\n    margin-bottom: 0.375rem;\n  }\n\n  .hub-card-description {\n    font-size: 0.8rem;\n  }\n\n  .hub-bottom-decoration {\n    margin-top: 1.5rem;\n    padding: 1rem;\n  }\n}\n\n/* Very Small Screens (max-width: 320px) */\n@media (max-width: 320px) {\n  .hub-container {\n    padding: 0.75rem 0.5rem;\n  }\n\n  .hub-header {\n    padding: 1rem;\n    border-radius: 0.75rem;\n  }\n\n  .hub-welcome {\n    font-size: 1.75rem;\n  }\n\n  .hub-subtitle {\n    font-size: 0.875rem;\n  }\n\n  .hub-quote {\n    font-size: 0.8125rem;\n    padding: 0.75rem;\n  }\n\n  .hub-card {\n    padding: 1.25rem;\n    border-radius: 0.75rem;\n  }\n\n  .hub-card-icon {\n    width: 50px;\n    height: 50px;\n    font-size: 1.25rem;\n  }\n\n  .hub-card-title {\n    font-size: 1.125rem;\n  }\n\n  .hub-card-description {\n    font-size: 0.8125rem;\n  }\n}\n\n/* ===== ACCESSIBILITY & PERFORMANCE ===== */\n\n/* Focus states for accessibility */\n.hub-card:focus {\n  outline: 3px solid #007BFF;\n  outline-offset: 2px;\n}\n\n.hub-card:focus:not(:focus-visible) {\n  outline: none;\n}\n\n/* Reduced motion support */\n@media (prefers-reduced-motion: reduce) {\n  .hub-card,\n  .hub-card-icon,\n  .animate-fadeInUp,\n  .animate-bounce-gentle {\n    animation: none !important;\n    transition: none !important;\n  }\n\n  .hub-card:hover {\n    transform: none !important;\n  }\n}\n\n/* Gradient background animation */\n@keyframes gradient-bg {\n  0%, 100% {\n    background-position: 0% 50%;\n  }\n  50% {\n    background-position: 100% 50%;\n  }\n}\n\n.animate-gradient-bg {\n  background-size: 400% 400%;\n  animation: gradient-bg 8s ease infinite;\n}\n\n/* Fade in up animation */\n@keyframes fadeInUp {\n  from {\n    opacity: 0;\n    transform: translateY(30px);\n  }\n  to {\n    opacity: 1;\n    transform: translateY(0);\n  }\n}\n\n.animate-fadeInUp {\n  animation: fadeInUp 0.6s ease-out;\n}\n\n/* Stagger animation delays */\n.animate-delay-100 {\n  animation-delay: 0.1s;\n}\n\n.animate-delay-200 {\n  animation-delay: 0.2s;\n}\n\n.animate-delay-300 {\n  animation-delay: 0.3s;\n}\n\n.animate-delay-400 {\n  animation-delay: 0.4s;\n}\n\n.animate-delay-500 {\n  animation-delay: 0.5s;\n}\n\n.animate-delay-600 {\n  animation-delay: 0.6s;\n}\n\n/* Responsive adjustments */\n@media (max-width: 768px) {\n  .animate-blob {\n    animation-duration: 10s;\n  }\n  \n  .hub-card:hover {\n    transform: translateY(-4px) scale(1.01);\n  }\n}\n\n/* Dark mode support */\n@media (prefers-color-scheme: dark) {\n  .animate-pulse-glow {\n    animation: pulse-glow 2s ease-in-out infinite;\n  }\n}\n"], "names": [], "sourceRoot": ""}