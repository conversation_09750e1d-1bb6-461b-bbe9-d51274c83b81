{"version": 3, "file": "static/js/408.44ffe71d.chunk.js", "mappings": "gHAAA,MAAQA,QAASC,GAAkBC,EAAQ,MAE9BC,EAAWC,UACpB,IAEE,aADuBH,EAAcI,IAAI,aAAcC,IACvCC,IAClB,CAAE,MAAOC,GACP,OAAOA,EAAMC,SAASF,IACxB,E,+ECRJ,MAAQP,QAASC,GAAkBC,EAAQ,MAG9BQ,EAAYN,UACrB,IAEI,aADuBH,EAAcU,KAAK,0BAA2BL,IACrDC,IACpB,CAAE,MAAOC,GACL,OAAOA,EAAMC,SAASF,IAC1B,GAISK,EAAgBR,UACzB,IAEI,aADuBH,EAAcU,KAAK,+BAAgCE,IAC1DN,IACpB,CAAE,MAAOC,GACL,OAAOA,EAAMC,SAASF,IAC1B,GAISO,EAAsBV,UAC/B,IAEI,aADuBH,EAAcU,KAAK,uCAAwCL,IAClEC,IACpB,CAAE,MAAOC,GACL,OAAOA,EAAMC,SAASF,IAC1B,GAISQ,EAA0BX,UACnC,IAEI,aADuBH,EAAcI,IAAI,6CACzBE,IACpB,CAAE,MAAOC,GACL,OAAOA,EAAMC,SAASF,IAC1B,GAaSS,EAAmBZ,iBAAyB,IAAlBa,EAAOC,UAAAC,OAAA,QAAAC,IAAAF,UAAA,GAAAA,UAAA,GAAG,CAAC,EAC9C,IACI,MAAMG,EAAS,IAAIC,gBACfL,EAAQM,OAAOF,EAAOG,OAAO,QAASP,EAAQM,OAC9CN,EAAQQ,aAAaJ,EAAOG,OAAO,cAAeP,EAAQQ,aAC1DR,EAAQS,aAAaL,EAAOG,OAAO,cAAeP,EAAQS,aAC1DT,EAAQU,cAAcN,EAAOG,OAAO,eAAgBP,EAAQU,cAC5DV,EAAQW,iBAAiBP,EAAOG,OAAO,kBAAmBP,EAAQW,iBAGtE,aADuB3B,EAAcI,IAAI,4BAADwB,OAA6BR,EAAOS,cAC5DvB,IACpB,CAAE,MAAOC,GACL,OAAOA,EAAMC,SAASF,IAC1B,CACJ,EAEawB,EAAiB3B,eAAO4B,GAAyB,IAAjBC,EAAOf,UAAAC,OAAA,QAAAC,IAAAF,UAAA,GAAAA,UAAA,GAAG,EACnD,IAEI,aADuBjB,EAAcI,IAAI,0BAADwB,OAA2BG,EAAM,aAAAH,OAAYI,KACrE1B,IACpB,CAAE,MAAOC,GACL,OAAOA,EAAMC,SAASF,IAC1B,CACJ,C,wDC3DA,QAdA,SAAkB2B,GAAa,IAAZ,MAAEC,GAAOD,EAC1B,MAAOE,EAAUC,IAAeC,EAAAA,EAAAA,WAAS,GAMzC,OALAC,EAAAA,EAAAA,YAAU,KACJC,OAAOC,WAAa,KACtBJ,GAAY,EACd,GACC,KAEDK,EAAAA,EAAAA,KAAA,OAAKC,UAAU,OAAMC,UACnBF,EAAAA,EAAAA,KAAA,MAAIC,UAAWP,EAAW,UAAY,GAAGQ,SAAET,KAGjD,C,kGCZA,QAD0B,CAAE,KAAQ,CAAE,IAAO,MAAO,MAAS,CAAE,QAAW,gBAAiB,UAAa,SAAW,SAAY,CAAC,CAAE,IAAO,OAAQ,MAAS,CAAE,EAAK,+LAAkM,CAAE,IAAO,OAAQ,MAAS,CAAE,EAAK,oLAAwL,KAAQ,eAAgB,MAAS,Y,cCMzlBU,EAAsB,SAA6BC,EAAOC,GAC5D,OAAoBC,EAAAA,cAAoBC,EAAAA,GAAUC,EAAAA,EAAAA,GAAS,CAAC,EAAGJ,EAAO,CACpEC,IAAKA,EACLI,KAAMC,IAEV,EAIA,QAA4BJ,EAAAA,WAAiBH,G,+BCX7C,MAkLA,EAlLgCX,IAMzB,IAADmB,EAAA,IAN2B,QAC/BC,EAAO,QACPC,EAAO,YACPC,EAAW,aACXC,EAAY,KACZC,GACDxB,EAEC,MAsBMyB,EAtByBC,MAC7B,GAAiB,OAAZH,QAAY,IAAZA,IAAAA,EAAcI,QAAS,OAAO,EACnC,MAEMC,EAFU,IAAIC,KAAKN,EAAaI,SACxB,IAAIE,KAEZC,EAAWC,KAAKC,KAAKJ,EAAQ,OACnC,OAAOG,KAAKE,IAAI,EAAGH,EAAS,EAgBRJ,GAChBQ,EAboBC,MACxB,GAAiB,OAAZZ,QAAY,IAAZA,IAAAA,EAAca,WAA0B,OAAZb,QAAY,IAAZA,IAAAA,EAAcI,QAAS,OAAO,EAC/D,MAAMS,EAAY,IAAIP,KAAKN,EAAaa,WAIlCC,GAHU,IAAIR,KAAKN,EAAaI,SAGTS,GAAS,MAChCE,GAHQ,IAAIT,KAGQO,GAAS,MAEnC,OAAOL,KAAKQ,IAAI,IAAKR,KAAKE,IAAI,EAAIK,EAAWD,EAAa,KAAK,EAIhDF,GACXK,GAAuB,OAAXlB,QAAW,IAAXA,OAAW,EAAXA,EAAarB,SAAqB,OAAZsB,QAAY,IAAZA,OAAY,EAAZA,EAAciB,YAAa,eAC7Db,EAAsB,OAAZJ,QAAY,IAAZA,GAAAA,EAAcI,QAAU,IAAIE,KAAKN,EAAaI,SAASc,qBAAuB,MAE9F,OACEjC,EAAAA,EAAAA,KAACkC,EAAAA,EAAK,CACJC,KAAMvB,EACNwB,SAAUvB,EACVwB,OAAQ,KACRC,MAAO,IACPC,UAAQ,EACRtC,UAAU,4BACVuC,UAAW,CAAEC,gBAAiB,sBAAuBvC,UAErDwC,EAAAA,EAAAA,MAAA,OAAKzC,UAAU,8BAA6BC,SAAA,EAE1CwC,EAAAA,EAAAA,MAAA,OAAKzC,UAAU,eAAcC,SAAA,EAC3BF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,aAAYC,UACzBF,EAAAA,EAAAA,KAAC2C,EAAAA,EAAa,OAEhB3C,EAAAA,EAAAA,KAAA,MAAIC,UAAU,cAAaC,SAAC,6BAC5BF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,iBAAgBC,SAAC,mDAIhCwC,EAAAA,EAAAA,MAAA,OAAKzC,UAAU,oBAAmBC,SAAA,EAChCwC,EAAAA,EAAAA,MAAA,OAAKzC,UAAU,cAAaC,SAAA,EAC1BF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,YAAWC,UACxBF,EAAAA,EAAAA,KAACG,EAAmB,OAEtBuC,EAAAA,EAAAA,MAAA,OAAKzC,UAAU,YAAWC,SAAA,EACxBF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,YAAWC,SAAE8B,KAC3BhC,EAAAA,EAAAA,KAAA,QAAMC,UAAU,cAAaC,SAAC,+BAKlCwC,EAAAA,EAAAA,MAAA,OAAKzC,UAAU,mBAAkBC,SAAA,EAC/BwC,EAAAA,EAAAA,MAAA,OAAKzC,UAAU,kBAAiBC,SAAA,EAC9BF,EAAAA,EAAAA,KAAA,QAAMC,UAAU,iBAAgBC,SAAC,2BACjCwC,EAAAA,EAAAA,MAAA,QAAMzC,UAAU,iBAAgBC,SAAA,CAAEe,EAAc,yBAElDjB,EAAAA,EAAAA,KAAC4C,EAAAA,EAAQ,CACPC,QAASnB,EACToB,YAAa,CACX,KAAM,UACN,MAAO,UACP,OAAQ,WAEVC,WAAW,UACXC,YAAa,EACbC,UAAU,QAKdP,EAAAA,EAAAA,MAAA,OAAKzC,UAAU,uBAAsBC,SAAA,EACnCwC,EAAAA,EAAAA,MAAA,OAAKzC,UAAU,cAAaC,SAAA,EAC1BF,EAAAA,EAAAA,KAACkD,EAAAA,EAAgB,CAACjD,UAAU,iBAC5ByC,EAAAA,EAAAA,MAAA,OAAKzC,UAAU,iBAAgBC,SAAA,EAC7BF,EAAAA,EAAAA,KAAA,QAAMC,UAAU,eAAcC,SAAC,gBAC/BF,EAAAA,EAAAA,KAAA,QAAMC,UAAU,eAAcC,SAAEiB,WAGpCuB,EAAAA,EAAAA,MAAA,OAAKzC,UAAU,cAAaC,SAAA,EAC1BF,EAAAA,EAAAA,KAACmD,EAAAA,EAAmB,CAAClD,UAAU,iBAC/ByC,EAAAA,EAAAA,MAAA,OAAKzC,UAAU,iBAAgBC,SAAA,EAC7BF,EAAAA,EAAAA,KAAA,QAAMC,UAAU,eAAcC,SAAC,oBAC/BF,EAAAA,EAAAA,KAAA,QAAMC,UAAU,eAAcC,SAC3Be,EAAgB,EAAC,GAAA9B,OAAM8B,EAAa,SAAU,yBAQzDyB,EAAAA,EAAAA,MAAA,OAAKzC,UAAU,kBAAiBC,SAAA,EAC9BwC,EAAAA,EAAAA,MAAA,OAAKzC,UAAU,eAAcC,SAAA,EAC3BF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,gBAAeC,SAAC,kCAC9BwC,EAAAA,EAAAA,MAAA,KAAGzC,UAAU,eAAcC,SAAA,CAAC,6DAC+BF,EAAAA,EAAAA,KAAA,UAAAE,SAAS8B,IAAmB,gGAKzFU,EAAAA,EAAAA,MAAA,OAAKzC,UAAU,gBAAeC,SAAA,EAC5BF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,iBAAgBC,SAAC,4BAC/BF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,WAAUC,UACV,OAAXY,QAAW,IAAXA,GAAqB,QAAVH,EAAXG,EAAasC,gBAAQ,IAAAzC,OAAV,EAAXA,EAAuB0C,MAAM,EAAG,GAAGC,KAAI,CAACC,EAASC,KAChDd,EAAAA,EAAAA,MAAA,MAAgBzC,UAAU,eAAcC,SAAA,EACtCF,EAAAA,EAAAA,KAACG,EAAmB,CAACF,UAAU,iBAC9BsD,IAFMC,OAIL,EACJd,EAAAA,EAAAA,MAAA,MAAYzC,UAAU,eAAcC,SAAA,EAClCF,EAAAA,EAAAA,KAACG,EAAmB,CAACF,UAAU,iBAAiB,gCAD1C,MAIRyC,EAAAA,EAAAA,MAAA,MAAYzC,UAAU,eAAcC,SAAA,EAClCF,EAAAA,EAAAA,KAACG,EAAmB,CAACF,UAAU,iBAAiB,mCAD1C,MAIRyC,EAAAA,EAAAA,MAAA,MAAYzC,UAAU,eAAcC,SAAA,EAClCF,EAAAA,EAAAA,KAACG,EAAmB,CAACF,UAAU,iBAAiB,uBAD1C,MAIRyC,EAAAA,EAAAA,MAAA,MAAYzC,UAAU,eAAcC,SAAA,EAClCF,EAAAA,EAAAA,KAACG,EAAmB,CAACF,UAAU,iBAAiB,4BAD1C,eAUhByC,EAAAA,EAAAA,MAAA,OAAKzC,UAAU,iBAAgBC,SAAA,EAC7BF,EAAAA,EAAAA,KAACyD,EAAAA,GAAM,CACLC,KAAK,UACLC,KAAK,QACLC,QAAS/C,EACTZ,UAAU,kBAAiBC,SAC5B,uBAGDF,EAAAA,EAAAA,KAACyD,EAAAA,GAAM,CACLC,KAAK,UACLC,KAAK,QACLC,QAAS/C,EACTZ,UAAU,eAAcC,SACzB,cAMHF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,cAAaC,UAC1BwC,EAAAA,EAAAA,MAAA,KAAAxC,SAAA,CAAG,iBACEF,EAAAA,EAAAA,KAAA,UAAAE,SAAQ,SAAa,oFAAgFF,EAAAA,EAAAA,KAAA,UAAAE,SAASiB,aAIjH,C,0NCtKZ,MA8WA,EA9W0B3B,IAAqC,IAADqE,EAAAC,EAAA,IAAnC,OAAEC,EAAM,QAAElD,EAAO,UAAEmD,GAAWxE,EACvD,MAAOyE,EAAOC,IAAYtE,EAAAA,EAAAA,UAAS,KAC5BuE,EAAcC,IAAmBxE,EAAAA,EAAAA,UAAS,OAC1CyE,EAASC,IAAc1E,EAAAA,EAAAA,WAAS,IAChC2E,EAAgBC,IAAqB5E,EAAAA,EAAAA,WAAS,IAC9C6E,EAAMC,IAAW9E,EAAAA,EAAAA,UAAS,UAC1B+E,EAAwBC,IAA6BhF,EAAAA,EAAAA,WAAS,IAC9DiF,EAAqBC,IAA0BlF,EAAAA,EAAAA,UAAS,OACxDmF,EAAcC,IAAmBpF,EAAAA,EAAAA,WAAS,GAG3CqF,EAAgBA,KACpB,MAAMC,EAAY,OAAJlE,QAAI,IAAJA,OAAI,EAAJA,EAAMmE,YACpB,OAAOD,GAAS,iBAAiBE,KAAKF,EAAM,GAKxC,KAAElE,IAASqE,EAAAA,EAAAA,KAAaC,GAAUA,EAAMtE,QACxC,iBAAEuE,IAAqBF,EAAAA,EAAAA,KAAaC,GAAUA,EAAMvE,eACpDyE,GAAWC,EAAAA,EAAAA,OAEjB5F,EAAAA,EAAAA,YAAU,KACJkE,GACF2B,GACF,GACC,CAAC3B,IAEJ,MAAM2B,EAAahI,UACjB,IACE4G,GAAW,GACX,MAAMvG,QAAiBN,EAAAA,EAAAA,KACvByG,EAASyB,MAAMC,QAAQ7H,GAAYA,EAAW,GAChD,CAAE,MAAOD,GACP+H,QAAQ/H,MAAM,wBAAyBA,GACvCgI,EAAAA,GAAQhI,MAAM,oCAChB,CAAC,QACCwG,GAAW,EACb,GAkCIyB,EAAgBrI,UACpB,GAAKyG,EAKL,GAAKc,IAKL,IAAK,IAADe,EACFxB,GAAkB,GAClBQ,GAAgB,GAChBF,EAAuBzD,KAAK4E,OAC5BT,GAASU,EAAAA,EAAAA,OAGaC,YAAW,KAC/BnB,GAAgB,EAAK,GACpB,KAFH,MAIMoB,EAAc,CAClBC,KAAMlC,EACN7E,OAAQ0B,EAAKsF,IACbC,UAAWvF,EAAKmE,YAChBqB,UAAWxF,EAAKyF,OAAK,GAAAtH,OAAgB,QAAhB6G,EAAOhF,EAAK0F,YAAI,IAAAV,OAAA,EAATA,EAAWW,QAAQ,OAAQ,IAAIC,cAAa,oBAGpE7I,QAAiB8I,EAAAA,EAAAA,GAAWT,GAElC,IAAIrI,EAAS+I,QASX,MAAM,IAAIC,MAAMhJ,EAAS+H,SAAW,kBARpCA,EAAAA,GAAQgB,QAAQ,oEAGhBjG,IAGAmG,EAAyBjJ,EAASkJ,SAItC,CAAE,MAAOnJ,GACP+H,QAAQ/H,MAAM,iBAAkBA,GAChCgI,EAAAA,GAAQhI,MAAMA,EAAMgI,SAAW,oCACjC,CAAC,QACCtB,GAAkB,GAClBgB,GAAS0B,EAAAA,EAAAA,MACX,MAzCEpB,EAAAA,GAAQhI,MAAM,6GALdgI,EAAAA,GAAQhI,MAAM,6BA8ChB,EAGIkJ,EAA2BtJ,UAC/B,IAAIyJ,EAAW,EACf,MAEMC,EAAc1J,UAClB,IACEyJ,IACAtB,QAAQwB,IAAI,mDAADlI,OAA0CgI,EAAQ,KAAAhI,OAL7C,MAOhB,MAAMpB,QAAiBuJ,EAAAA,EAAAA,KAIvB,GAHAzB,QAAQwB,IAAI,wCAA+BtJ,GAGvCA,GAAYA,EAASD,MAAO,CAC9B,GAAuB,uBAAnBC,EAASD,MAIX,OAHA+H,QAAQ/H,MAAM,4CACdgI,EAAAA,GAAQhI,MAAM,yFACd4G,EAAQ,SAIV,GAAuB,kBAAnB3G,EAASD,MAIX,OAHA+H,QAAQ/H,MAAM,qDACdgI,EAAAA,GAAQhI,MAAM,oDACd4G,EAAQ,QAGZ,CAEA,GAAI3G,IAAaA,EAASD,QACI,SAA3BC,EAASwJ,eAAgD,WAApBxJ,EAASyJ,QAC1B,cAApBzJ,EAASyJ,SAA+C,IAArBzJ,EAAS+I,SAwB7C,OAtBAjB,QAAQwB,IAAI,0DAGZ7B,GAASiC,EAAAA,EAAAA,GAAgB1J,IAGzB+H,EAAAA,GAAQgB,QAAQ,CACdY,QAAS,sDACTC,SAAU,EACVC,MAAO,CACLC,UAAW,OACXC,SAAU,OACVC,WAAY,SAKhB/D,GAAaA,IAGbnD,KAEO,EAGT,GAAIsG,GAxDY,IA8Dd,OALAtB,QAAQwB,IAAI,wCACZvB,EAAAA,GAAQkC,QAAQ,CACdN,QAAS,uGACTC,SAAU,KAEL,EAITxB,WAAWiB,EAAa,IAC1B,CAAE,MAAOtJ,GACP+H,QAAQ/H,MAAM,wCAAoCA,GAC9CqJ,GArEY,IAsEdrB,EAAAA,GAAQhI,MAAM,8EAEdqI,WAAWiB,EAAa,IAE5B,GAIFA,GAAa,EAUf,OAAKrD,GAGHrB,EAAAA,EAAAA,MAAA,OAAKzC,UAAU,6BAA4BC,SAAA,EACzCwC,EAAAA,EAAAA,MAAA,OAAKzC,UAAU,qBAAoBC,SAAA,EACjCwC,EAAAA,EAAAA,MAAA,OAAKzC,UAAU,eAAcC,SAAA,EAC3BwC,EAAAA,EAAAA,MAAA,MAAIzC,UAAU,cAAaC,SAAA,CACf,UAATuE,GAAoB,yCACX,YAATA,GAAsB,yCAEzBzE,EAAAA,EAAAA,KAAA,UAAQC,UAAU,eAAe2D,QAjBrBqE,KAClBvD,EAAQ,SACRN,EAAgB,MAChBI,GAAkB,GAClB3D,GAAS,EAamDX,SAAC,aAGzDwC,EAAAA,EAAAA,MAAA,OAAKzC,UAAU,gBAAeC,SAAA,CAClB,UAATuE,IACCzE,EAAAA,EAAAA,KAAA,OAAKC,UAAU,aAAYC,SACxBmE,GACC3B,EAAAA,EAAAA,MAAA,OAAKzC,UAAU,gBAAeC,SAAA,EAC5BF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,aACfD,EAAAA,EAAAA,KAAA,KAAAE,SAAG,wBAGL+D,EAAMX,KAAK+C,IAAI,IAAA6B,EAAAC,EAAAC,EAAAC,EAAA,OACb3F,EAAAA,EAAAA,MAAA,OAAoBzC,UAAU,YAAY2D,QAASA,IAnMzCyC,KAExB,GAAId,GAAgD,WAA5BA,EAAiBiC,QAA0D,SAAnCjC,EAAiBgC,cAG/E,OAFA1B,QAAQwB,IAAI,qDAA4C9B,QACxDX,GAA0B,GAI5BR,EAAgBiC,GAChB3B,EAAQ,UAAU,EA0LqD4D,CAAiBjC,GAAMnG,SAAA,EAC9EwC,EAAAA,EAAAA,MAAA,OAAKzC,UAAU,cAAaC,SAAA,EAC1BF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,aAAYC,SAAEmG,EAAK5G,SACtB,QAAVyI,EAAA7B,EAAK5G,aAAK,IAAAyI,OAAA,EAAVA,EAAYtB,cAAc2B,SAAS,eAClCvI,EAAAA,EAAAA,KAAA,QAAMC,UAAU,aAAYC,SAAC,6BAIjCwC,EAAAA,EAAAA,MAAA,OAAKzC,UAAU,aAAYC,SAAA,EACzBwC,EAAAA,EAAAA,MAAA,QAAMzC,UAAU,eAAcC,SAAA,CAAsB,QAAtBiI,EAAE9B,EAAKmC,uBAAe,IAAAL,OAAA,EAApBA,EAAsBM,iBAAiB,UACtEpC,EAAKqC,aAAerC,EAAKqC,cAAgBrC,EAAKmC,kBAC7C9F,EAAAA,EAAAA,MAAA,QAAMzC,UAAU,iBAAgBC,SAAA,CAAEmG,EAAKqC,YAAYD,iBAAiB,WAEtE/F,EAAAA,EAAAA,MAAA,QAAMzC,UAAU,eAAcC,SAAA,CAAEmG,EAAKsB,SAAS,SAAOtB,EAAKsB,SAAW,EAAI,IAAM,UAGjFjF,EAAAA,EAAAA,MAAA,OAAKzC,UAAU,gBAAeC,SAAA,CACd,QADckI,EAC3B/B,EAAKjD,gBAAQ,IAAAgF,OAAA,EAAbA,EAAe/E,MAAM,EAAG,GAAGC,KAAI,CAACC,EAASC,KACxCd,EAAAA,EAAAA,MAAA,OAAiBzC,UAAU,UAASC,SAAA,EAClCF,EAAAA,EAAAA,KAAA,QAAMC,UAAU,eAAcC,SAAC,YAC/BF,EAAAA,EAAAA,KAAA,QAAMC,UAAU,eAAcC,SAAEqD,MAFxBC,MAKE,QAAb6E,EAAAhC,EAAKjD,gBAAQ,IAAAiF,OAAA,EAAbA,EAAe5J,QAAS,IACvBiE,EAAAA,EAAAA,MAAA,OAAKzC,UAAU,UAASC,SAAA,EACtBF,EAAAA,EAAAA,KAAA,QAAMC,UAAU,eAAcC,SAAC,OAC/BwC,EAAAA,EAAAA,MAAA,QAAMzC,UAAU,eAAcC,SAAA,CAAEmG,EAAKjD,SAAS3E,OAAS,EAAE,2BAK/DiE,EAAAA,EAAAA,MAAA,UAAQzC,UAAU,kBAAiBC,SAAA,CAAC,UAC1BmG,EAAK5G,WAhCP4G,EAAKC,IAkCT,MAMJ,YAAT7B,GAAsBN,IACrBzB,EAAAA,EAAAA,MAAA,OAAKzC,UAAU,eAAcC,SAAA,EAC3BwC,EAAAA,EAAAA,MAAA,OAAKzC,UAAU,wBAAuBC,SAAA,EACpCwC,EAAAA,EAAAA,MAAA,MAAAxC,SAAA,CAAI,kBAAgBiE,EAAa1E,UACjCiD,EAAAA,EAAAA,MAAA,KAAGzC,UAAU,qBAAoBC,SAAA,CACF,QADE2D,EAC9BM,EAAaqE,uBAAe,IAAA3E,OAAA,EAA5BA,EAA8B4E,iBAAiB,YAAUtE,EAAawD,SAAS,SAAOxD,EAAawD,SAAW,EAAI,IAAM,UAI7HjF,EAAAA,EAAAA,MAAA,OAAKzC,UAAU,eAAcC,SAAA,EAC3BwC,EAAAA,EAAAA,MAAA,OAAKzC,UAAU,YAAWC,SAAA,EACxBF,EAAAA,EAAAA,KAAA,QAAMC,UAAU,aAAYC,SAAC,mBAC7BF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,uBAAsBC,SAClC+E,KACCvC,EAAAA,EAAAA,MAAA,QAAMzC,UAAU,yBAAwBC,SAAA,CACrCc,EAAKmE,YAAY,cAGpBzC,EAAAA,EAAAA,MAAA,OAAKzC,UAAU,wBAAuBC,SAAA,EACpCwC,EAAAA,EAAAA,MAAA,QAAMzC,UAAU,2BAA0BC,SAAA,EACnC,OAAJc,QAAI,IAAJA,OAAI,EAAJA,EAAMmE,cAAe,sBAAsB,cAE9CnF,EAAAA,EAAAA,KAAA,UACEC,UAAU,mBACV2D,QAASA,KACPkC,EAAAA,GAAQ6C,KAAK,oDACbxC,YAAW,KACTrG,OAAOqC,KAAK,gBAAiB,SAAS,GACrC,IAAK,EACRjC,SACH,+BAQTwC,EAAAA,EAAAA,MAAA,OAAKzC,UAAU,YAAWC,SAAA,EACxBF,EAAAA,EAAAA,KAAA,QAAMC,UAAU,aAAYC,SAAC,qBAC7BF,EAAAA,EAAAA,KAAA,QAAMC,UAAU,aAAYC,SAAC,sDAG9B+E,MACCjF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,eAAcC,UAC3BF,EAAAA,EAAAA,KAAA,KAAAE,SAAG,4EAKTwC,EAAAA,EAAAA,MAAA,OAAKzC,UAAU,kBAAiBC,SAAA,EAC9BF,EAAAA,EAAAA,KAAA,UAAQC,UAAU,WAAW2D,QAASA,IAAMc,EAAQ,SAASxE,SAAC,0BAG9DF,EAAAA,EAAAA,KAAA,UACEC,UAAU,UACV2D,QAASmC,EACT6C,SAAUrE,IAAmBU,IAAgB/E,SAE5CqE,GACC7B,EAAAA,EAAAA,MAAAmG,EAAAA,SAAA,CAAA3I,SAAA,EACEF,EAAAA,EAAAA,KAAA,QAAMC,UAAU,gBAAqB,mBAGpCgF,IACwB,OAAA9F,OAEQ,QAFR2E,EAEpBK,EAAaqE,uBAAe,IAAA1E,OAAA,EAA5BA,EAA8B2E,iBAAgB,QAFrD,2CAcdzI,EAAAA,EAAAA,KAAC8I,EAAAA,EAAuB,CACtBlI,QAAS+D,EACT9D,QAASA,IAAM+D,GAA0B,GACzC9D,YAAamD,EAAM8E,MAAKC,GAAKA,EAAE1C,OAAwB,OAAhBf,QAAgB,IAAhBA,OAAgB,EAAhBA,EAAkB0D,gBAA+B,OAAhB1D,QAAgB,IAAhBA,OAAgB,EAAhBA,EAAkBc,MAC1FtF,aAAcwE,EACdvE,KAAMA,OAnJQ,IAqJZ,ECwoBV,EAh/BgBkI,KAAO,IAADC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EACpB,MAAOC,EAAaC,IAAkBjK,EAAAA,EAAAA,UAAS,OACxCkK,EAAaC,IAAkBnK,EAAAA,EAAAA,UAAS,OACxCoK,EAAaC,IAAkBrK,EAAAA,EAAAA,UAAS,OACxCsK,EAAkBC,IAAuBvK,EAAAA,EAAAA,UAAS,OAClDwK,EAAMC,IAAWzK,EAAAA,EAAAA,WAAS,IAC1B0K,EAAcC,IAAmB3K,EAAAA,EAAAA,UAAS,OAC1C4K,EAAUC,IAAe7K,EAAAA,EAAAA,UAAS,CACvC8G,KAAM,GACND,MAAO,GACPiE,OAAQ,GACRC,MAAO,GACPC,OAAQ,GACRzF,YAAa,MAER0F,EAAcC,IAAmBlL,EAAAA,EAAAA,UAAS,OAC1CmL,EAAuBC,IAA4BpL,EAAAA,EAAAA,WAAS,IAC5DqL,EAAsBC,IAA2BtL,EAAAA,EAAAA,WAAS,IAC1DuL,EAAoBC,IAAyBxL,EAAAA,EAAAA,UAAS,MACvD4F,GAAWC,EAAAA,EAAAA,OACX,iBAAEF,IAAqBF,EAAAA,EAAAA,KAAaC,GAAUA,EAAMvE,gBAuE1DlB,EAAAA,EAAAA,YAAU,KACJiK,GAAeF,GAvDAyB,MACnB,MAAMC,EAAUxB,EACbxG,KAAI,CAACtC,EAAMwC,KAAK,CACfxC,OACAuK,QAAS/H,EAAQ,MAElBgI,QAAQC,GAASA,EAAKzK,KAAK1B,OAAOiJ,SAASqB,EAAYtD,OAC1D2D,EAAeqB,EAAQ,EAiDrBD,EACF,GACC,CAACvB,EAAaF,IAEjB,MAAM8B,EAAchO,UAClB8H,GAASU,EAAAA,EAAAA,OACT,IACE,MAAMnI,QAAiB4N,EAAAA,EAAAA,MACnB5N,EAAS+I,SACX+C,EAAe9L,EAASF,MACxB4M,EAAY,CACV/D,KAAM3I,EAASF,KAAK6I,MAAQ,GAC5BD,MAAO1I,EAASF,KAAK4I,OAAS,GAC9BiE,OAAQ3M,EAASF,KAAK6M,QAAU,GAChCE,OAAQ7M,EAASF,KAAK+N,OAAS,GAC/BjB,MAAO5M,EAASF,KAAK8M,OAAS,GAC9BxF,YAAapH,EAASF,KAAKsH,aAAe,KAExCpH,EAASF,KAAKgN,cAChBC,EAAgB/M,EAASF,KAAKgN,cA1FjBnN,WACnB,IACE,MAAMK,QAAiBM,EAAAA,EAAAA,MACnBN,EAAS+I,QACXiD,EAAehM,EAASF,MAExBiI,EAAAA,GAAQhI,MAAMC,EAAS+H,SAEzBN,GAAS0B,EAAAA,EAAAA,MACX,CAAE,MAAOpJ,GACPgI,EAAAA,GAAQhI,MAAMA,EAAMgI,SACpBN,GAAS0B,EAAAA,EAAAA,MACX,GAgFI2E,IAEA/F,EAAAA,GAAQhI,MAAMC,EAAS+H,QAE3B,CAAE,MAAOhI,GACPgI,EAAAA,GAAQhI,MAAMA,EAAMgI,QACtB,CAAC,QACCN,GAAS0B,EAAAA,EAAAA,MACX,IAGFrH,EAAAA,EAAAA,YAAU,KACJiM,aAAaC,QAAQ,UACvBL,GACF,GACC,IAEH,MAAMM,EAAgBC,IACpB,MAAM,KAAEvF,EAAI,MAAEwF,GAAUD,EAAEE,OAC1B,KAAa,gBAATzF,GAA0BwF,EAAMzN,OAAS,IAC7C,MAAa,UAATiI,GAAoBwF,KAAqB,OAAXtC,QAAW,IAAXA,OAAW,EAAXA,EAAae,QAAmB,KAAVuB,GACtDd,EAAsBc,QACtBhB,GAAwB,SAG1BT,GAAa2B,IAAIC,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACZD,GAAI,IACP,CAAC1F,GAAOwF,GACK,UAATxF,EAAmB,CAAEkE,OAAQ,IAAO,CAAC,IACxC,EAqGC0B,EAAoB5O,UACxB,MAAM6O,EAAON,EAAEE,OAAOK,MAAM,GAC5B,GAAID,EAAM,CAER,IAAKA,EAAK7I,KAAK+I,WAAW,UAExB,YADA3G,EAAAA,GAAQhI,MAAM,oCAKhB,GAAIyO,EAAK5I,KAAO,QAEd,YADAmC,EAAAA,GAAQhI,MAAM,sCAIhBgN,EAAgByB,GAGhB,MAAMG,EAAS,IAAIC,WACnBD,EAAOE,UAAY,IAAMrC,EAAgBmC,EAAOG,QAChDH,EAAOI,cAAcP,GAGrB,MAAM1O,EAAO,IAAIkP,SACjBlP,EAAKiB,OAAO,eAAgByN,GAC5B/G,GAASU,EAAAA,EAAAA,OAET,IACE,MAAMnI,QAAiBiP,EAAAA,EAAAA,IAAgBnP,GACvC2H,GAAS0B,EAAAA,EAAAA,OACLnJ,EAAS+I,SACXhB,EAAAA,GAAQgB,QAAQ,yCAChB4E,KAEA5F,EAAAA,GAAQhI,MAAMC,EAAS+H,QAE3B,CAAE,MAAOhI,GACP0H,GAAS0B,EAAAA,EAAAA,OACTpB,EAAAA,GAAQhI,MAAMA,EAAMgI,SAAW,mCACjC,CACF,GAkDF,OAzBAjG,EAAAA,EAAAA,YAAU,KACR6L,GAAa,GACZ,KAGH7L,EAAAA,EAAAA,YAAU,KACJ+J,GA3QuBlM,WAC3B,GAAgB,OAAXkM,QAAW,IAAXA,GAAAA,EAAatD,IAElB,IACEd,GAASU,EAAAA,EAAAA,OAGT,MAAM+G,QAAwB5N,EAAAA,EAAAA,IAAeuK,EAAYtD,IAAK,GAE1D2G,EAAgBnG,SAClBqD,EAAoB8C,EAAgBpP,MAItC,MAAMqP,QAA4B5O,EAAAA,EAAAA,IAAiB,CACjDO,MAAO,IACPG,aAAwB,OAAX4K,QAAW,IAAXA,OAAW,EAAXA,EAAae,QAAS,QAGrC,GAAIuC,EAAoBpG,QAAS,CAC/B,MAAMqG,EAAYD,EAAoBrP,KAAKuP,WAAUpM,GAAQA,EAAKsF,MAAQsD,EAAYtD,MACtF,GAAI6G,GAAa,EAAG,CAClB,MAAME,GAAYhB,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACba,EAAoBrP,KAAKsP,IAAU,IACtCG,KAAMH,EAAY,EAClBI,WAAYL,EAAoBrP,KAAKY,SAEvC0L,GAAoBiC,IAAIC,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACnBD,GAAI,IACPoB,SAAUL,EAAY,EACtBI,WAAYL,EAAoBrP,KAAKY,OACrCuC,KAAMqM,KAEV,CACF,CAEA7H,GAAS0B,EAAAA,EAAAA,MACX,CAAE,MAAOpJ,GACP0H,GAAS0B,EAAAA,EAAAA,OACTrB,QAAQ/H,MAAM,+BAAgCA,EAChD,GAoOE2P,EACF,GACC,CAAC7D,KAGJ/J,EAAAA,EAAAA,YAAU,KACJ+J,GACFa,EAAY,CACV/D,KAAMkD,EAAYlD,MAAQ,GAC1BD,MAAOmD,EAAYnD,OAAS,GAC5BiE,OAAQd,EAAYc,QAAU,GAC9BE,OAAQhB,EAAYgC,OAAS,GAC7BjB,MAAOf,EAAYe,OAAS,GAC5BxF,YAAayE,EAAYzE,aAAe,IAE5C,GACC,CAACyE,KAGFlH,EAAAA,EAAAA,MAAA,OAAKzC,UAAU,qEAAoEC,SAAA,EACjFF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,8BAA6BC,UAC1CwC,EAAAA,EAAAA,MAAA,OAAKzC,UAAU,oBAAmBC,SAAA,EAEhCwC,EAAAA,EAAAA,MAAA,OAAKzC,UAAU,mBAAkBC,SAAA,EAC/BF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,wCAAuCC,SAAC,aACtDF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,gBAAeC,SAAC,kDAG7BF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,oCAAmCC,UAChDwC,EAAAA,EAAAA,MAAA,OAAKzC,UAAU,WAAUC,SAAA,EACvBF,EAAAA,EAAAA,KAAC0N,EAAAA,EAAc,CACb1M,KAAM4I,EACNjG,KAAK,MACLgK,kBAAkB,EAClB/J,QAASA,IAAMgK,SAASC,eAAe,qBAAqBC,QAC5D7N,UAAU,oDACV2H,MAAO,CACLtF,MAAO,QACPyL,OAAQ,QACRC,OAAQ,oBACRC,UAAW,kCAKfjO,EAAAA,EAAAA,KAAA,OAAKC,UAAU,mIACV2D,QAASA,IAAMgK,SAASC,eAAe,qBAAqBC,QAAQ5N,UACvEwC,EAAAA,EAAAA,MAAA,OAAKzC,UAAU,qBAAqBiO,KAAK,OAAOC,OAAO,eAAeC,QAAQ,YAAWlO,SAAA,EACvFF,EAAAA,EAAAA,KAAA,QAAMqO,cAAc,QAAQC,eAAe,QAAQtL,YAAa,EAAGuL,EAAE,sKACrEvO,EAAAA,EAAAA,KAAA,QAAMqO,cAAc,QAAQC,eAAe,QAAQtL,YAAa,EAAGuL,EAAE,2CAKzEvO,EAAAA,EAAAA,KAAA,SACEwO,GAAG,oBACH9K,KAAK,OACL+K,OAAO,UACPxO,UAAU,SACVyO,SAAUpC,aAOlBtM,EAAAA,EAAAA,KAAA,OAAKC,UAAU,iDAAgDC,UAC7DwC,EAAAA,EAAAA,MAAA,OAAKzC,UAAU,MAAKC,SAAA,EAClBwC,EAAAA,EAAAA,MAAA,OAAKzC,UAAU,kCAAiCC,SAAA,EAE9CwC,EAAAA,EAAAA,MAAA,OAAKzC,UAAU,uDAAsDC,SAAA,EACnEwC,EAAAA,EAAAA,MAAA,OAAKzC,UAAU,uEAAsEC,SAAA,EACnFF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,oCAAmCC,SAAC,UACjDF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,kCAAiCC,UAAa,OAAX0J,QAAW,IAAXA,OAAW,EAAXA,EAAalD,OAAQ,aAEvEhE,EAAAA,EAAAA,MAAA,OAAKzC,UAAU,yEAAwEC,SAAA,EACrFF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,qCAAoCC,SAAC,cAClDF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,yDAAwDC,UAAa,OAAX0J,QAAW,IAAXA,OAAW,EAAXA,EAAa+E,WAAY,iBAElGjM,EAAAA,EAAAA,MAAA,OAAKzC,UAAU,2EAA0EC,SAAA,EACvFF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,sCAAqCC,SAAC,WACnDF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,kCAAiCC,UAAa,OAAX0J,QAAW,IAAXA,OAAW,EAAXA,EAAagC,QAAS,cAKzE1B,IACCxH,EAAAA,EAAAA,MAAA,OAAKzC,UAAU,kDAAiDC,SAAA,EAC9DwC,EAAAA,EAAAA,MAAA,OAAKzC,UAAU,2EAA0EC,SAAA,EACvFF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,sCAAqCC,SAAC,UACnDwC,EAAAA,EAAAA,MAAA,KAAGzC,UAAU,kCAAiCC,SAAA,CAAC,IAC3CgK,EAAiBsD,UAAY,MAC9BtD,EAAiBqD,aAChB7K,EAAAA,EAAAA,MAAA,QAAMzC,UAAU,wBAAuBC,SAAA,CAAC,IAAEgK,EAAiBqD,qBAIjE7K,EAAAA,EAAAA,MAAA,OAAKzC,UAAU,2EAA0EC,SAAA,EACvFF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,sCAAqCC,SAAC,cACnDF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,kCAAiCC,UACtB,QAArBiJ,EAAAe,EAAiBlJ,YAAI,IAAAmI,GAAS,QAATC,EAArBD,EAAuByF,eAAO,IAAAxF,OAAT,EAArBA,EAAgCX,mBAAoB,UAGzD/F,EAAAA,EAAAA,MAAA,OAAKzC,UAAU,2EAA0EC,SAAA,EACvFF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,sCAAqCC,SAAC,eACnDwC,EAAAA,EAAAA,MAAA,KAAGzC,UAAU,kCAAiCC,SAAA,EACtB,QAArBmJ,EAAAa,EAAiBlJ,YAAI,IAAAqI,OAAA,EAArBA,EAAuBwF,eAAgB,IAAI,WAGhDnM,EAAAA,EAAAA,MAAA,OAAKzC,UAAU,uEAAsEC,SAAA,EACnFF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,oCAAmCC,SAAC,aACjDF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,kCAAiCC,UACtB,QAArBoJ,EAAAY,EAAiBlJ,YAAI,IAAAsI,OAAA,EAArBA,EAAuBwF,oBAAqB,UAGjDpM,EAAAA,EAAAA,MAAA,OAAKzC,UAAU,uEAAsEC,SAAA,EACnFF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,oCAAmCC,SAAC,YACjDF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,kCAAiCC,UACtB,QAArBqJ,EAAAW,EAAiBlJ,YAAI,IAAAuI,OAAA,EAArBA,EAAuBwF,gBAAiB,eAQjD3E,GA4EA1H,EAAAA,EAAAA,MAAA,OAAKzC,UAAU,wCAAuCC,SAAA,EACpDwC,EAAAA,EAAAA,MAAA,OAAKzC,UAAU,YAAWC,SAAA,EACxBwC,EAAAA,EAAAA,MAAA,OAAAxC,SAAA,EACEF,EAAAA,EAAAA,KAAA,SAAOC,UAAU,+CAA8CC,SAAC,YAChEF,EAAAA,EAAAA,KAAA,SACE0D,KAAK,OACLgD,KAAK,OACLwF,MAAO1B,EAAS9D,KAChBgI,SAAU1C,EACV/L,UAAU,wHACV+O,YAAY,kBACZC,UAAQ,QAGZvM,EAAAA,EAAAA,MAAA,OAAAxC,SAAA,EACEF,EAAAA,EAAAA,KAAA,SAAOC,UAAU,+CAA8CC,SAAC,cAChEwC,EAAAA,EAAAA,MAAA,OAAKzC,UAAU,kDAAiDC,SAAA,EAClD,OAAX0J,QAAW,IAAXA,OAAW,EAAXA,EAAa+E,WAAY,iBAC1B3O,EAAAA,EAAAA,KAAA,QAAMC,UAAU,mCAAkCC,SAAC,sCAGvDwC,EAAAA,EAAAA,MAAA,OAAAxC,SAAA,EACEF,EAAAA,EAAAA,KAAA,SAAOC,UAAU,+CAA8CC,SAAC,sBAChEwC,EAAAA,EAAAA,MAAA,OAAKzC,UAAU,WAAUC,SAAA,EACvBF,EAAAA,EAAAA,KAAA,SACE0D,KAAK,QACLgD,KAAK,QACLwF,MAAO1B,EAAS/D,OAAS,GACzBiI,SAAU1C,EACV/L,UAAU,8HACV+O,YAAY,kCAEXxE,EAAS/D,OAA4B,KAAnB+D,EAAS/D,SAC5BzG,EAAAA,EAAAA,KAAA,UACE0D,KAAK,SACLE,QAASA,KACP,MAAMsL,EAAY7N,KAAK4E,MACjBkJ,EAAS,GAAAhQ,OAAMyK,EAAY+E,SAAQ,KAAAxP,OAAI+P,EAAS,mBACtDzE,GAAY2B,IAAIC,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GAAUD,GAAI,IAAE3F,MAAO0I,MACvCrJ,EAAAA,GAAQgB,QAAQ,gCAAgC,EAElD7G,UAAU,8IAA6IC,SACxJ,gBAKJsK,EAAS/D,OAAS+D,EAAS/D,MAAM8B,SAAS,qBACzCvI,EAAAA,EAAAA,KAAA,KAAGC,UAAU,6BAA4BC,SAAC,0GAK9CwC,EAAAA,EAAAA,MAAA,OAAAxC,SAAA,EACEF,EAAAA,EAAAA,KAAA,SAAOC,UAAU,+CAA8CC,SAAC,YAChEF,EAAAA,EAAAA,KAAA,SACE0D,KAAK,OACLgD,KAAK,SACLwF,MAAO1B,EAASE,OAChBgE,SAAU1C,EACV/L,UAAU,wHACV+O,YAAY,6BAIlBtM,EAAAA,EAAAA,MAAA,OAAKzC,UAAU,YAAWC,SAAA,EACxBwC,EAAAA,EAAAA,MAAA,OAAAxC,SAAA,EACEF,EAAAA,EAAAA,KAAA,SAAOC,UAAU,+CAA8CC,SAAC,aAChEwC,EAAAA,EAAAA,MAAA,UACEgE,KAAK,QACLwF,MAAO1B,EAASG,MAChB+D,SAAU1C,EACV/L,UAAU,wHACVgP,UAAQ,EAAA/O,SAAA,EAERF,EAAAA,EAAAA,KAAA,UAAQkM,MAAM,GAAEhM,SAAC,kBACjBF,EAAAA,EAAAA,KAAA,UAAQkM,MAAM,UAAShM,SAAC,aACxBF,EAAAA,EAAAA,KAAA,UAAQkM,MAAM,YAAWhM,SAAC,eAC1BF,EAAAA,EAAAA,KAAA,UAAQkM,MAAM,UAAShM,SAAC,mBAG5BwC,EAAAA,EAAAA,MAAA,OAAAxC,SAAA,EACEF,EAAAA,EAAAA,KAAA,SAAOC,UAAU,+CAA8CC,SAAC,aAChEwC,EAAAA,EAAAA,MAAA,UACEgE,KAAK,SACLwF,MAAO1B,EAASI,OAChB8D,SAAU1C,EACV/L,UAAU,wHACVgP,UAAQ,EAAA/O,SAAA,EAERF,EAAAA,EAAAA,KAAA,UAAQkM,MAAM,GAAEhM,SAAC,iBACG,YAAnBsK,EAASG,QACRjI,EAAAA,EAAAA,MAAAmG,EAAAA,SAAA,CAAA3I,SAAA,EACEF,EAAAA,EAAAA,KAAA,UAAQkM,MAAM,IAAGhM,SAAC,OAClBF,EAAAA,EAAAA,KAAA,UAAQkM,MAAM,IAAGhM,SAAC,OAClBF,EAAAA,EAAAA,KAAA,UAAQkM,MAAM,IAAGhM,SAAC,OAClBF,EAAAA,EAAAA,KAAA,UAAQkM,MAAM,IAAGhM,SAAC,OAClBF,EAAAA,EAAAA,KAAA,UAAQkM,MAAM,IAAGhM,SAAC,OAClBF,EAAAA,EAAAA,KAAA,UAAQkM,MAAM,IAAGhM,SAAC,OAClBF,EAAAA,EAAAA,KAAA,UAAQkM,MAAM,IAAGhM,SAAC,SAGF,cAAnBsK,EAASG,QACRjI,EAAAA,EAAAA,MAAAmG,EAAAA,SAAA,CAAA3I,SAAA,EACEF,EAAAA,EAAAA,KAAA,UAAQkM,MAAM,IAAGhM,SAAC,OAClBF,EAAAA,EAAAA,KAAA,UAAQkM,MAAM,IAAGhM,SAAC,OAClBF,EAAAA,EAAAA,KAAA,UAAQkM,MAAM,IAAGhM,SAAC,OAClBF,EAAAA,EAAAA,KAAA,UAAQkM,MAAM,IAAGhM,SAAC,SAGF,YAAnBsK,EAASG,QACRjI,EAAAA,EAAAA,MAAAmG,EAAAA,SAAA,CAAA3I,SAAA,EACEF,EAAAA,EAAAA,KAAA,UAAQkM,MAAM,IAAGhM,SAAC,OAClBF,EAAAA,EAAAA,KAAA,UAAQkM,MAAM,IAAGhM,SAAC,gBAK1BwC,EAAAA,EAAAA,MAAA,OAAAxC,SAAA,EACEF,EAAAA,EAAAA,KAAA,SAAOC,UAAU,+CAA8CC,SAAC,kBAChEF,EAAAA,EAAAA,KAAA,SACE0D,KAAK,MACLgD,KAAK,cACLwF,MAAO1B,EAASrF,YAChBuJ,SAAU1C,EACV/L,UAAU,wHACV+O,YAAY,qBACZI,UAAU,iBAzMlB1M,EAAAA,EAAAA,MAAA,OAAKzC,UAAU,wCAAuCC,SAAA,EACpDwC,EAAAA,EAAAA,MAAA,OAAKzC,UAAU,YAAWC,SAAA,EACxBwC,EAAAA,EAAAA,MAAA,OAAAxC,SAAA,EACEF,EAAAA,EAAAA,KAAA,SAAOC,UAAU,+CAA8CC,SAAC,UAChEF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,mCAAkCC,UACnC,OAAX0J,QAAW,IAAXA,OAAW,EAAXA,EAAalD,OAAQ,qBAG1BhE,EAAAA,EAAAA,MAAA,OAAAxC,SAAA,EACEF,EAAAA,EAAAA,KAAA,SAAOC,UAAU,+CAA8CC,SAAC,cAChEF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,mCAAkCC,UACnC,OAAX0J,QAAW,IAAXA,OAAW,EAAXA,EAAa+E,WAAY,qBAG9BjM,EAAAA,EAAAA,MAAA,OAAAxC,SAAA,EACEF,EAAAA,EAAAA,KAAA,SAAOC,UAAU,+CAA8CC,SAAC,WAChEF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,mCAAkCC,SACnC,OAAX0J,QAAW,IAAXA,GAAAA,EAAanD,OACZ/D,EAAAA,EAAAA,MAAA,OAAKzC,UAAU,oCAAmCC,SAAA,EAChDF,EAAAA,EAAAA,KAAA,QAAAE,SAAO0J,EAAYnD,QAClBmD,EAAYnD,MAAM8B,SAAS,qBAC1BvI,EAAAA,EAAAA,KAAA,QAAMC,UAAU,2DAA0DC,SAAC,uBAM/EwC,EAAAA,EAAAA,MAAA,OAAKzC,UAAU,oCAAmCC,SAAA,EAChDF,EAAAA,EAAAA,KAAA,QAAMC,UAAU,gBAAeC,SAAC,kBAChCF,EAAAA,EAAAA,KAAA,UACE4D,QAASlG,UACP,MAAMwR,EAAY7N,KAAK4E,MACjBkJ,EAAS,GAAAhQ,OAAMyK,EAAY+E,SAAQ,KAAAxP,OAAI+P,EAAS,mBACtDzE,GAAY2B,IAAIC,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GAAUD,GAAI,IAAE3F,MAAO0I,MACvCrJ,EAAAA,GAAQ6C,KAAK,gEAAgE,EAE/E1I,UAAU,kGAAiGC,SAC5G,4BAOTwC,EAAAA,EAAAA,MAAA,OAAAxC,SAAA,EACEF,EAAAA,EAAAA,KAAA,SAAOC,UAAU,+CAA8CC,SAAC,YAChEF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,mCAAkCC,UACnC,OAAX0J,QAAW,IAAXA,OAAW,EAAXA,EAAac,SAAU,wBAI9BhI,EAAAA,EAAAA,MAAA,OAAKzC,UAAU,YAAWC,SAAA,EACxBwC,EAAAA,EAAAA,MAAA,OAAAxC,SAAA,EACEF,EAAAA,EAAAA,KAAA,SAAOC,UAAU,+CAA8CC,SAAC,WAChEF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,mCAAkCC,UACnC,OAAX0J,QAAW,IAAXA,OAAW,EAAXA,EAAae,QAAS,qBAG3BjI,EAAAA,EAAAA,MAAA,OAAAxC,SAAA,EACEF,EAAAA,EAAAA,KAAA,SAAOC,UAAU,+CAA8CC,SAAC,WAChEF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,mCAAkCC,UACnC,OAAX0J,QAAW,IAAXA,OAAW,EAAXA,EAAagC,QAAS,qBAG3BlJ,EAAAA,EAAAA,MAAA,OAAAxC,SAAA,EACEF,EAAAA,EAAAA,KAAA,SAAOC,UAAU,+CAA8CC,SAAC,kBAChEF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,mCAAkCC,UACnC,OAAX0J,QAAW,IAAXA,OAAW,EAAXA,EAAazE,cAAe,2BA8IvCzC,EAAAA,EAAAA,MAAA,OAAKzC,UAAU,yFAAwFC,SAAA,EACrGwC,EAAAA,EAAAA,MAAA,MAAIzC,UAAU,yDAAwDC,SAAA,EACpEF,EAAAA,EAAAA,KAAA,QAAMC,UAAU,OAAMC,SAAC,iBAAS,uBAIjCqF,GAAgD,WAA5BA,EAAiBiC,QAEpC9E,EAAAA,EAAAA,MAAA,OAAKzC,UAAU,YAAWC,SAAA,EAExBwC,EAAAA,EAAAA,MAAA,OAAKzC,UAAU,oCAAmCC,SAAA,EAChDwC,EAAAA,EAAAA,MAAA,OAAAxC,SAAA,EACEF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,kCAAiCC,SAAEqF,EAAiBvD,YAAkC,QAAzBwH,EAAIjE,EAAiBc,YAAI,IAAAmD,OAAA,EAArBA,EAAuB/J,QAAS,kBAC/GO,EAAAA,EAAAA,KAAA,KAAGC,UAAU,qBAAoBC,UAC/BwC,EAAAA,EAAAA,MAAA,QAAMzC,UAAU,2BAA0BC,SAAA,EACxCF,EAAAA,EAAAA,KAAA,QAAMC,UAAU,yDAA8D,+BAKpFD,EAAAA,EAAAA,KAAA,OAAKC,UAAU,aAAYC,UACzBF,EAAAA,EAAAA,KAAA,QAAMC,UAAU,0HAAyHC,SAAC,wBAO9IwC,EAAAA,EAAAA,MAAA,OAAKzC,UAAU,kFAAiFC,SAAA,EAC9FwC,EAAAA,EAAAA,MAAA,MAAIzC,UAAU,6DAA4DC,SAAA,EACxEF,EAAAA,EAAAA,KAAA,QAAMC,UAAU,OAAMC,SAAC,iBAAS,4BAGlCwC,EAAAA,EAAAA,MAAA,OAAKzC,UAAU,wCAAuCC,SAAA,EACpDwC,EAAAA,EAAAA,MAAA,OAAAxC,SAAA,EACEF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,gDAA+CC,SAAC,gBAC7DF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,sCAAqCC,SAC/CqF,EAAiB3D,UAChB,IAAIP,KAAKkE,EAAiB3D,WAAWK,mBAAmB,QAAS,CAC/DoN,QAAS,QACTC,KAAM,UACNC,MAAO,QACPC,IAAK,YAEP,IAAInO,KAAKkE,EAAiBkK,WAAWxN,mBAAmB,QAAS,CAC/DoN,QAAS,QACTC,KAAM,UACNC,MAAO,QACPC,IAAK,kBAKb9M,EAAAA,EAAAA,MAAA,OAAAxC,SAAA,EACEF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,gDAA+CC,SAAC,gBAC7DF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,qCAAoCC,SAC9C,IAAImB,KAAKkE,EAAiBpE,SAASc,mBAAmB,QAAS,CAC9DoN,QAAS,QACTC,KAAM,UACNC,MAAO,QACPC,IAAK,wBAQf9M,EAAAA,EAAAA,MAAA,OAAKzC,UAAU,wCAAuCC,SAAA,EACpDF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,2DAA0DC,UACvEwC,EAAAA,EAAAA,MAAA,OAAKzC,UAAU,oCAAmCC,SAAA,EAChDwC,EAAAA,EAAAA,MAAA,OAAAxC,SAAA,EACEF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,gDAA+CC,SAAC,oBAC7DwC,EAAAA,EAAAA,MAAA,KAAGzC,UAAU,kCAAiCC,SAAA,CAC3CqF,EAAiBoC,WAAiC,QAAzB8B,EAAIlE,EAAiBc,YAAI,IAAAoD,OAAA,EAArBA,EAAuB9B,WAAY,EAAE,UAAQpC,EAAiBoC,WAAiC,QAAzB+B,EAAInE,EAAiBc,YAAI,IAAAqD,OAAA,EAArBA,EAAuB/B,WAAY,GAAK,EAAI,IAAM,UAG9J3H,EAAAA,EAAAA,KAAA,OAAKC,UAAU,gBAAeC,UAC5BF,EAAAA,EAAAA,KAAA,QAAMC,UAAU,WAAUC,SAAC,yBAKjCF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,2DAA0DC,UACvEwC,EAAAA,EAAAA,MAAA,OAAKzC,UAAU,oCAAmCC,SAAA,EAChDwC,EAAAA,EAAAA,MAAA,OAAAxC,SAAA,EACEF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,gDAA+CC,SAAC,iBAC7DwC,EAAAA,EAAAA,MAAA,KAAGzC,UAAU,mCAAkCC,SAAA,EAC3CqF,EAAiBmK,QAAUnK,EAAiBiD,iBAAmB,GAAGC,iBAAiB,cAGzFzI,EAAAA,EAAAA,KAAA,OAAKC,UAAU,iBAAgBC,UAC7BF,EAAAA,EAAAA,KAAA,QAAMC,UAAU,WAAUC,SAAC,yBAKjCF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,2DAA0DC,UACvEwC,EAAAA,EAAAA,MAAA,OAAKzC,UAAU,oCAAmCC,SAAA,EAChDwC,EAAAA,EAAAA,MAAA,OAAAxC,SAAA,EACEF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,gDAA+CC,SAAC,oBAC7DwC,EAAAA,EAAAA,MAAA,KAAGzC,UAAU,oCAAmCC,SAAA,CAE3BqB,KAAKE,IAAI,EAAGF,KAAKC,MAAM,IAAIH,KAAKkE,EAAiBpE,SAAW,IAAIE,MAAM,QAEpF,eAGTrB,EAAAA,EAAAA,KAAA,OAAKC,UAAU,kBAAiBC,UAC9BF,EAAAA,EAAAA,KAAA,QAAMC,UAAU,WAAUC,SAAC,mBAKjCF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,2DAA0DC,UACvEwC,EAAAA,EAAAA,MAAA,OAAKzC,UAAU,oCAAmCC,SAAA,EAChDwC,EAAAA,EAAAA,MAAA,OAAAxC,SAAA,EACEF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,gDAA+CC,SAAC,gBAC7DwC,EAAAA,EAAAA,MAAA,KAAGzC,UAAU,oCAAmCC,SAAA,CACyB,IAArEqF,EAAiBoC,WAAiC,QAAzBgC,EAAIpE,EAAiBc,YAAI,IAAAsD,OAAA,EAArBA,EAAuBhC,WAAY,GAAQ,eAG9E3H,EAAAA,EAAAA,KAAA,OAAKC,UAAU,kBAAiBC,UAC9BF,EAAAA,EAAAA,KAAA,QAAMC,UAAU,WAAUC,SAAC,4BAOnCwC,EAAAA,EAAAA,MAAA,OAAKzC,UAAU,iDAAgDC,SAAA,EAC7DwC,EAAAA,EAAAA,MAAA,OAAKzC,UAAU,yCAAwCC,SAAA,EACrDF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,sCAAqCC,SAAC,2BACnDF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,wBAAuBC,SACjC,CAACyP,IACA,MACM9N,EAAuB,IADZ0D,EAAiBoC,WAAiC,QAAzBgI,EAAIpK,EAAiBc,YAAI,IAAAsJ,OAAA,EAArBA,EAAuBhI,WAAY,GAG3EiI,EAAW/N,EADAN,KAAKE,IAAI,EAAGF,KAAKC,MAAM,IAAIH,KAAKkE,EAAiBpE,SAAW,IAAIE,MAAM,QAEjFwO,EAAatO,KAAKQ,IAAI,IAAKR,KAAKE,IAAI,EAAImO,EAAW/N,EAAa,MACtE,MAAM,GAAN1C,OAAU0Q,EAAWC,QAAQ,GAAE,cAChC,EAPA,SAUL9P,EAAAA,EAAAA,KAAA,OAAKC,UAAU,sCAAqCC,UAClDF,EAAAA,EAAAA,KAAA,OACEC,UAAU,2FACV2H,MAAO,CACLtF,MAAM,GAADnD,OAAK,CAAC4Q,IACT,MACMlO,EAAuB,IADZ0D,EAAiBoC,WAAiC,QAAzBoI,EAAIxK,EAAiBc,YAAI,IAAA0J,OAAA,EAArBA,EAAuBpI,WAAY,GAG3EiI,EAAW/N,EADAN,KAAKE,IAAI,EAAGF,KAAKC,MAAM,IAAIH,KAAKkE,EAAiBpE,SAAW,IAAIE,MAAM,QAGvF,OADmBE,KAAKQ,IAAI,IAAKR,KAAKE,IAAI,EAAImO,EAAW/N,EAAa,KAEvE,EAPS,GAON,WAIVa,EAAAA,EAAAA,MAAA,OAAKzC,UAAU,kDAAiDC,SAAA,EAC9DF,EAAAA,EAAAA,KAAA,QAAAE,SAAM,aACNF,EAAAA,EAAAA,KAAA,QAAAE,SACG,MACC,MAAM8P,EAAWzO,KAAKE,IAAI,EAAGF,KAAKC,MAAM,IAAIH,KAAKkE,EAAiBpE,SAAW,IAAIE,MAAM,QACvF,OAAI2O,EAAW,EACP,GAAN7Q,OAAU6Q,EAAQ,cACTA,EAAW,EACd,gBAAN7Q,OAAa6Q,EAAQ,cAEd,gBAEV,EATA,YAePtN,EAAAA,EAAAA,MAAA,OAAKzC,UAAU,aAAYC,SAAA,EACzBF,EAAAA,EAAAA,KAAA,UACE4D,QAASA,IAAMoH,GAAyB,GACxC/K,UAAU,wJAAuJC,SAClK,+BAGDF,EAAAA,EAAAA,KAAA,UACE4D,QAASA,KACP,MAAMzC,EAAU,IAAIE,KAAKkE,EAAiBpE,SACpC8O,EAAQ,IAAI5O,KACZ2O,EAAWzO,KAAKC,MAAML,EAAU8O,GAAK,OAEvCD,GAAY,GAAKA,EAAW,EAC9BlK,EAAAA,GAAQkC,QAAQ,gCAAD7I,OAAiC6Q,EAAQ,mCAC/CA,GAAY,EACrBlK,EAAAA,GAAQhI,MAAM,uFAEdgI,EAAAA,GAAQ6C,KAAK,mCAADxJ,OAAoC6Q,EAAQ,eAC1D,EAEF/P,UAAU,iGAAgGC,SAC3G,qCAOLwC,EAAAA,EAAAA,MAAA,OAAKzC,UAAU,mBAAkBC,SAAA,EAC/BwC,EAAAA,EAAAA,MAAA,OAAKzC,UAAU,OAAMC,SAAA,EACnBF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,mFAAkFC,UAC/FF,EAAAA,EAAAA,KAAA,QAAMC,UAAU,WAAUC,SAAC,oBAE7BF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,uCAAsCC,SAAC,4BACrDF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,sCAAqCC,SAAC,8GAMrDwC,EAAAA,EAAAA,MAAA,OAAKzC,UAAU,+DAA8DC,SAAA,EAC3EwC,EAAAA,EAAAA,MAAA,OAAKzC,UAAU,oFAAmFC,SAAA,EAChGF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,8BAA6BC,SAAC,kBAC7CF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,mCAAkCC,SAAC,uBACjDF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,wBAAuBC,SAAC,gDAEvCwC,EAAAA,EAAAA,MAAA,OAAKzC,UAAU,uFAAsFC,SAAA,EACnGF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,+BAA8BC,SAAC,kBAC9CF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,mCAAkCC,SAAC,wBACjDF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,wBAAuBC,SAAC,2CAEvCwC,EAAAA,EAAAA,MAAA,OAAKzC,UAAU,wFAAuFC,SAAA,EACpGF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,gCAA+BC,SAAC,kBAC/CF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,mCAAkCC,SAAC,uBACjDF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,wBAAuBC,SAAC,uCAEvCwC,EAAAA,EAAAA,MAAA,OAAKzC,UAAU,uFAAsFC,SAAA,EACnGF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,gCAA+BC,SAAC,kBAC/CF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,mCAAkCC,SAAC,uBACjDF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,wBAAuBC,SAAC,yCAKzCwC,EAAAA,EAAAA,MAAA,OAAKzC,UAAU,YAAWC,SAAA,EACxBF,EAAAA,EAAAA,KAAA,UACE4D,QAASA,IAAMoH,GAAyB,GACxC/K,UAAU,4MAA2MC,SACtN,2CAGDF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,wBAAuBC,SAAC,8DAS7CF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,iCAAgCC,SAC3CkK,GAmBA1H,EAAAA,EAAAA,MAAAmG,EAAAA,SAAA,CAAA3I,SAAA,EACEF,EAAAA,EAAAA,KAAA,UACE4D,QAnxBGsM,KACrBzF,EAAY,CACV/D,KAAMkD,EAAYlD,KAClBD,MAAOmD,EAAYnD,MACnBiE,OAAQd,EAAYc,OACpBE,OAAQhB,EAAYgC,MACpBjB,MAAOf,EAAYe,MACnBxF,YAAayE,EAAYzE,cAE3BkF,GAAQ,EAAM,EA2wBIpK,UAAU,2GAA0GC,SACrH,YAGDF,EAAAA,EAAAA,KAAA,UACE4D,QA3wBClG,iBAA6B,IAAtB,QAAEyS,GAAS3R,UAAAC,OAAA,QAAAC,IAAAF,UAAA,GAAAA,UAAA,GAAG,CAAC,EAKzC,GAJAqH,QAAQwB,IAAI,iCAAwBmD,GACpC3E,QAAQwB,IAAI,oCAA2BuC,IAGlCY,EAAS9D,MAAiC,KAAzB8D,EAAS9D,KAAK0J,OAElC,OADAvK,QAAQwB,IAAI,2CACLvB,EAAAA,GAAQhI,MAAM,2BAEvB,IAAK0M,EAASI,QAAqC,KAA3BJ,EAASI,OAAOwF,OAEtC,OADAvK,QAAQwB,IAAI,4CACLvB,EAAAA,GAAQhI,MAAM,0BAEvB,IAAK0M,EAASG,OAAmC,KAA1BH,EAASG,MAAMyF,OAEpC,OADAvK,QAAQwB,IAAI,4CACLvB,EAAAA,GAAQhI,MAAM,0BAGvB,GAAI0M,EAAS/D,OAAmC,KAA1B+D,EAAS/D,MAAM2J,OAAe,CAElD,IADmB,6BACHhL,KAAKoF,EAAS/D,OAC5B,OAAOX,EAAAA,GAAQhI,MAAM,sCAEzB,CAKA0H,GAASU,EAAAA,EAAAA,OACT,IAEE,MAAMmK,GAAahE,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACd7B,GAAQ,IACXlL,OAAQsK,EAAYtD,MAIlBkE,EAAS/D,OAAmC,KAA1B+D,EAAS/D,MAAM2J,OACnCC,EAAc5J,MAAQ+D,EAAS/D,MAAM2J,OACjB,OAAXxG,QAAW,IAAXA,GAAAA,EAAanD,QACtB4J,EAAc5J,MAAQmD,EAAYnD,OAGpCZ,QAAQwB,IAAI,oCAA2BgJ,GAEvC,MAAMtS,QAAiBuS,EAAAA,EAAAA,IAAeD,GAEtCxK,QAAQwB,IAAI,gCAAuBtJ,GAE/BA,EAAS+I,SACXhB,EAAAA,GAAQgB,QAAQ/I,EAAS+H,SACzBuE,GAAQ,GACRqB,IACI3N,EAASwS,cACXpK,YAAW,IAAMrG,OAAO0Q,SAASC,UAAU,OAG7C5K,QAAQ/H,MAAM,wBAAoBC,GAClC+H,EAAAA,GAAQhI,MAAMC,EAAS+H,SAAW,+CAEtC,CAAE,MAAOhI,GAAQ,IAAD4S,EAAAC,EACd9K,QAAQ/H,MAAM,uBAAmBA,GACjC,MAAM8S,GAA6B,QAAdF,EAAA5S,EAAMC,gBAAQ,IAAA2S,GAAM,QAANC,EAAdD,EAAgB7S,YAAI,IAAA8S,OAAN,EAAdA,EAAsB7K,UAAWhI,EAAMgI,SAAW,gCACvEA,EAAAA,GAAQhI,MAAM,kBAADqB,OAAmByR,GAClC,CAAC,QACCpL,GAAS0B,EAAAA,EAAAA,MACX,CACF,EAysBoBjH,UAAU,6GAA4GC,SACvH,kBAIDF,EAAAA,EAAAA,KAAA,UACE4D,QAASA,KACPiC,QAAQwB,IAAI,yCAAgCmD,GAC5C3E,QAAQwB,IAAI,4CAAmCuC,GAC/CiH,MAAM,aAAD1R,OAAc2R,KAAKC,UAAUvG,EAAU,KAAM,IAAK,EAEzDvK,UAAU,mHAAkHC,SAC7H,cAvCHF,EAAAA,EAAAA,KAAA,UACE4D,QAASA,KAEP6G,EAAY,CACV/D,MAAiB,OAAXkD,QAAW,IAAXA,OAAW,EAAXA,EAAalD,OAAQ,GAC3BD,OAAkB,OAAXmD,QAAW,IAAXA,OAAW,EAAXA,EAAanD,QAAS,GAC7BiE,QAAmB,OAAXd,QAAW,IAAXA,OAAW,EAAXA,EAAac,SAAU,GAC/BE,QAAmB,OAAXhB,QAAW,IAAXA,OAAW,EAAXA,EAAagC,QAAS,GAC9BjB,OAAkB,OAAXf,QAAW,IAAXA,OAAW,EAAXA,EAAae,QAAS,GAC7BxF,aAAwB,OAAXyE,QAAW,IAAXA,OAAW,EAAXA,EAAazE,cAAe,KAE3CkF,GAAQ,EAAK,EAEfpK,UAAU,2GAA0GC,SACrH,8BAqCbF,EAAAA,EAAAA,KAAA,SACE0D,KAAK,OACL8K,GAAG,oBACHC,OAAO,UACPC,SAAUpC,EACV1E,MAAO,CAAEoJ,QAAS,WAIpBtO,EAAAA,EAAAA,MAACR,EAAAA,EAAK,CACJzC,MAAM,uBACN0C,KAAM8I,EACNgG,KA3uB2BC,KAC/BzG,GAAa2B,IAAIC,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACZD,GAAI,IACPzB,MAAOQ,EACPP,OAAQ,OAEVM,GAAwB,GACxBE,EAAsB,KAAK,EAquBvBhJ,SAAUA,KACR8I,GAAwB,GACxBE,EAAsB,KAAK,EAE7B+F,OAAO,UACPC,WAAW,SAAQlR,SAAA,EAEnBwC,EAAAA,EAAAA,MAAA,KAAAxC,SAAA,CAAG,kDAC6CF,EAAAA,EAAAA,KAAA,UAAAE,SAASiL,IAA4B,QAErFnL,EAAAA,EAAAA,KAAA,KAAGC,UAAU,+BAA8BC,SAAC,4HAM9CF,EAAAA,EAAAA,KAACqR,EAAiB,CAChBtN,OAAQgH,EACRlK,QAASA,IAAMmK,GAAyB,GACxChH,UAAWA,KACTgH,GAAyB,GAEzBU,IACA5F,EAAAA,GAAQgB,QAAQ,qCAAqC,MAIrD,C,0DCz/BV,QADuB,CAAE,KAAQ,CAAE,IAAO,MAAO,MAAS,CAAE,QAAW,gBAAiB,UAAa,SAAW,SAAY,CAAC,CAAE,IAAO,OAAQ,MAAS,CAAE,EAAK,mWAAuW,KAAQ,WAAY,MAAS,Y,cCM9hB5D,EAAmB,SAA0B9C,EAAOC,GACtD,OAAoBC,EAAAA,cAAoBC,EAAAA,GAAUC,EAAAA,EAAAA,GAAS,CAAC,EAAGJ,EAAO,CACpEC,IAAKA,EACLI,KAAM6Q,IAEV,EAIA,QAA4BhR,EAAAA,WAAiB4C,E,wDCd7C,QAD0B,CAAE,KAAQ,CAAE,IAAO,MAAO,MAAS,CAAE,QAAW,gBAAiB,UAAa,SAAW,SAAY,CAAC,CAAE,IAAO,OAAQ,MAAS,CAAE,EAAK,kLAAqL,CAAE,IAAO,OAAQ,MAAS,CAAE,EAAK,yKAA6K,KAAQ,eAAgB,MAAS,Y,cCMjkBC,EAAsB,SAA6B/C,EAAOC,GAC5D,OAAoBC,EAAAA,cAAoBC,EAAAA,GAAUC,EAAAA,EAAAA,GAAS,CAAC,EAAGJ,EAAO,CACpEC,IAAKA,EACLI,KAAM8Q,IAEV,EAIA,QAA4BjR,EAAAA,WAAiB6C,E,0DCd7C,QADoB,CAAE,KAAQ,CAAE,IAAO,MAAO,MAAS,CAAE,QAAW,gBAAiB,UAAa,SAAW,SAAY,CAAC,CAAE,IAAO,OAAQ,MAAS,CAAE,EAAK,wfAA4f,KAAQ,QAAS,MAAS,Y,cCM7qBR,EAAgB,SAAuBvC,EAAOC,GAChD,OAAoBC,EAAAA,cAAoBC,EAAAA,GAAUC,EAAAA,EAAAA,GAAS,CAAC,EAAGJ,EAAO,CACpEC,IAAKA,EACLI,KAAM+Q,IAEV,EAIA,QAA4BlR,EAAAA,WAAiBqC,E", "sources": ["apicalls/plans.js", "apicalls/reports.js", "components/PageTitle.js", "../node_modules/@ant-design/icons-svg/es/asn/CheckCircleOutlined.js", "../node_modules/@ant-design/icons/es/icons/CheckCircleOutlined.js", "components/UpgradeRestrictionModal/UpgradeRestrictionModal.jsx", "components/SubscriptionModal/SubscriptionModal.jsx", "pages/common/Profile/index.js", "../node_modules/@ant-design/icons-svg/es/asn/CalendarOutlined.js", "../node_modules/@ant-design/icons/es/icons/CalendarOutlined.js", "../node_modules/@ant-design/icons-svg/es/asn/ClockCircleOutlined.js", "../node_modules/@ant-design/icons/es/icons/ClockCircleOutlined.js", "../node_modules/@ant-design/icons-svg/es/asn/CrownOutlined.js", "../node_modules/@ant-design/icons/es/icons/CrownOutlined.js"], "sourcesContent": ["const { default: axiosInstance } = require(\".\");\r\n\r\nexport const getPlans = async (payload) => {\r\n    try {\r\n      const response = await axiosInstance.get(\"/api/plans\", payload);\r\n      return response.data;\r\n    } catch (error) {\r\n      return error.response.data;\r\n    }\r\n  };", "const { default: axiosInstance } = require(\".\");\r\n\r\n// add report\r\nexport const addReport = async (payload) => {\r\n    try {\r\n        const response = await axiosInstance.post(\"/api/reports/add-report\", payload);\r\n        return response.data;\r\n    } catch (error) {\r\n        return error.response.data;\r\n    }\r\n}\r\n\r\n// get all reports\r\nexport const getAllReports = async (filters) => {\r\n    try {\r\n        const response = await axiosInstance.post(\"/api/reports/get-all-reports\", filters);\r\n        return response.data;\r\n    } catch (error) {\r\n        return error.response.data;\r\n    }\r\n}\r\n\r\n// get all reports by user\r\nexport const getAllReportsByUser = async (payload) => {\r\n    try {\r\n        const response = await axiosInstance.post(\"/api/reports/get-all-reports-by-user\", payload);\r\n        return response.data;\r\n    } catch (error) {\r\n        return error.response.data;\r\n    }\r\n}\r\n\r\n// get all reports for ranking (legacy)\r\nexport const getAllReportsForRanking = async (filters) => {\r\n    try {\r\n        const response = await axiosInstance.get(\"/api/reports/get-all-reports-for-ranking\");\r\n        return response.data;\r\n    } catch (error) {\r\n        return error.response.data;\r\n    }\r\n}\r\n\r\n// Enhanced XP-based ranking endpoints\r\nexport const getEnhancedLeaderboard = async (level = 'all', limit = 1000) => {\r\n    try {\r\n        const response = await axiosInstance.get(`/api/quiz/enhanced-leaderboard?level=${level}&limit=${limit}`);\r\n        return response.data;\r\n    } catch (error) {\r\n        return error.response.data;\r\n    }\r\n}\r\n\r\nexport const getXPLeaderboard = async (options = {}) => {\r\n    try {\r\n        const params = new URLSearchParams();\r\n        if (options.limit) params.append('limit', options.limit);\r\n        if (options.classFilter) params.append('classFilter', options.classFilter);\r\n        if (options.levelFilter) params.append('levelFilter', options.levelFilter);\r\n        if (options.seasonFilter) params.append('seasonFilter', options.seasonFilter);\r\n        if (options.includeInactive) params.append('includeInactive', options.includeInactive);\r\n\r\n        const response = await axiosInstance.get(`/api/quiz/xp-leaderboard?${params.toString()}`);\r\n        return response.data;\r\n    } catch (error) {\r\n        return error.response.data;\r\n    }\r\n}\r\n\r\nexport const getUserRanking = async (userId, context = 5) => {\r\n    try {\r\n        const response = await axiosInstance.get(`/api/quiz/user-ranking/${userId}?context=${context}`);\r\n        return response.data;\r\n    } catch (error) {\r\n        return error.response.data;\r\n    }\r\n}\r\n\r\nexport const getClassRankings = async (className, limit = 50) => {\r\n    try {\r\n        const response = await axiosInstance.get(`/api/quiz/class-rankings/${className}?limit=${limit}`);\r\n        return response.data;\r\n    } catch (error) {\r\n        return error.response.data;\r\n    }\r\n}\r\n\r\n// XP Dashboard endpoints\r\nexport const getXPDashboard = async () => {\r\n    try {\r\n        const response = await axiosInstance.get(\"/api/xp-dashboard/dashboard\");\r\n        return response.data;\r\n    } catch (error) {\r\n        return error.response.data;\r\n    }\r\n}\r\n\r\nexport const getClassLeaderboard = async () => {\r\n    try {\r\n        const response = await axiosInstance.get(\"/api/xp-dashboard/class-leaderboard\");\r\n        return response.data;\r\n    } catch (error) {\r\n        return error.response.data;\r\n    }\r\n}\r\n", "import React, { useState, useEffect } from 'react'\r\n\r\nfunction PageTitle({ title }) {\r\n  const [isMobile, setIsMobile] = useState(false);\r\n  useEffect(() => {\r\n    if (window.innerWidth < 768) {\r\n      setIsMobile(true);\r\n    }\r\n  }, []);\r\n  return (\r\n    <div className='mt-2'>\r\n      <h1 className={isMobile ? 'text-lg' : ''}>{title}</h1>\r\n    </div>\r\n  )\r\n}\r\n\r\nexport default PageTitle", "// This icon file is generated automatically.\nvar CheckCircleOutlined = { \"icon\": { \"tag\": \"svg\", \"attrs\": { \"viewBox\": \"64 64 896 896\", \"focusable\": \"false\" }, \"children\": [{ \"tag\": \"path\", \"attrs\": { \"d\": \"M699 353h-46.9c-10.2 0-19.9 4.9-25.9 13.3L469 584.3l-71.2-98.8c-6-8.3-15.6-13.3-25.9-13.3H325c-6.5 0-10.3 7.4-6.5 12.7l124.6 172.8a31.8 31.8 0 0051.7 0l210.6-292c3.9-5.3.1-12.7-6.4-12.7z\" } }, { \"tag\": \"path\", \"attrs\": { \"d\": \"M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372z\" } }] }, \"name\": \"check-circle\", \"theme\": \"outlined\" };\nexport default CheckCircleOutlined;\n", "import _extends from \"@babel/runtime/helpers/esm/extends\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react';\nimport CheckCircleOutlinedSvg from \"@ant-design/icons-svg/es/asn/CheckCircleOutlined\";\nimport AntdIcon from \"../components/AntdIcon\";\nvar CheckCircleOutlined = function CheckCircleOutlined(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {\n    ref: ref,\n    icon: CheckCircleOutlinedSvg\n  }));\n};\nif (process.env.NODE_ENV !== 'production') {\n  CheckCircleOutlined.displayName = 'CheckCircleOutlined';\n}\nexport default /*#__PURE__*/React.forwardRef(CheckCircleOutlined);", "import React from 'react';\nimport { Modal, Button, Progress } from 'antd';\nimport { ClockCircleOutlined, CrownOutlined, CalendarOutlined, CheckCircleOutlined } from '@ant-design/icons';\nimport './UpgradeRestrictionModal.css';\n\nconst UpgradeRestrictionModal = ({ \n  visible, \n  onClose, \n  currentPlan, \n  subscription,\n  user \n}) => {\n  // Calculate days remaining\n  const calculateDaysRemaining = () => {\n    if (!subscription?.endDate) return 0;\n    const endDate = new Date(subscription.endDate);\n    const today = new Date();\n    const diffTime = endDate - today;\n    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));\n    return Math.max(0, diffDays);\n  };\n\n  // Calculate progress percentage\n  const calculateProgress = () => {\n    if (!subscription?.startDate || !subscription?.endDate) return 0;\n    const startDate = new Date(subscription.startDate);\n    const endDate = new Date(subscription.endDate);\n    const today = new Date();\n    \n    const totalDays = (endDate - startDate) / (1000 * 60 * 60 * 24);\n    const usedDays = (today - startDate) / (1000 * 60 * 60 * 24);\n    \n    return Math.min(100, Math.max(0, (usedDays / totalDays) * 100));\n  };\n\n  const daysRemaining = calculateDaysRemaining();\n  const progress = calculateProgress();\n  const planTitle = currentPlan?.title || subscription?.planTitle || 'Premium Plan';\n  const endDate = subscription?.endDate ? new Date(subscription.endDate).toLocaleDateString() : 'N/A';\n\n  return (\n    <Modal\n      open={visible}\n      onCancel={onClose}\n      footer={null}\n      width={500}\n      centered\n      className=\"upgrade-restriction-modal\"\n      maskStyle={{ backgroundColor: 'rgba(0, 0, 0, 0.7)' }}\n    >\n      <div className=\"upgrade-restriction-content\">\n        {/* Header with Crown Icon */}\n        <div className=\"modal-header\">\n          <div className=\"crown-icon\">\n            <CrownOutlined />\n          </div>\n          <h2 className=\"modal-title\">Already Premium Member!</h2>\n          <p className=\"modal-subtitle\">You're currently enjoying premium features</p>\n        </div>\n\n        {/* Current Plan Card */}\n        <div className=\"current-plan-card\">\n          <div className=\"plan-header\">\n            <div className=\"plan-icon\">\n              <CheckCircleOutlined />\n            </div>\n            <div className=\"plan-info\">\n              <h3 className=\"plan-name\">{planTitle}</h3>\n              <span className=\"plan-status\">Active Subscription</span>\n            </div>\n          </div>\n\n          {/* Progress Bar */}\n          <div className=\"progress-section\">\n            <div className=\"progress-header\">\n              <span className=\"progress-label\">Subscription Progress</span>\n              <span className=\"days-remaining\">{daysRemaining} days remaining</span>\n            </div>\n            <Progress \n              percent={progress} \n              strokeColor={{\n                '0%': '#52c41a',\n                '50%': '#faad14', \n                '100%': '#ff4d4f',\n              }}\n              trailColor=\"#f0f0f0\"\n              strokeWidth={8}\n              showInfo={false}\n            />\n          </div>\n\n          {/* Subscription Details */}\n          <div className=\"subscription-details\">\n            <div className=\"detail-item\">\n              <CalendarOutlined className=\"detail-icon\" />\n              <div className=\"detail-content\">\n                <span className=\"detail-label\">Expires On</span>\n                <span className=\"detail-value\">{endDate}</span>\n              </div>\n            </div>\n            <div className=\"detail-item\">\n              <ClockCircleOutlined className=\"detail-icon\" />\n              <div className=\"detail-content\">\n                <span className=\"detail-label\">Time Remaining</span>\n                <span className=\"detail-value\">\n                  {daysRemaining > 0 ? `${daysRemaining} days` : 'Expired'}\n                </span>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Message Section */}\n        <div className=\"message-section\">\n          <div className=\"message-card\">\n            <h4 className=\"message-title\">🎉 You're All Set!</h4>\n            <p className=\"message-text\">\n              You're currently enjoying all premium features with your <strong>{planTitle}</strong>. \n              To upgrade to a different plan, please wait until your current subscription expires.\n            </p>\n          </div>\n\n          <div className=\"benefits-list\">\n            <h5 className=\"benefits-title\">Your Current Benefits:</h5>\n            <ul className=\"benefits\">\n              {currentPlan?.features?.slice(0, 4).map((feature, index) => (\n                <li key={index} className=\"benefit-item\">\n                  <CheckCircleOutlined className=\"benefit-icon\" />\n                  {feature}\n                </li>\n              )) || [\n                <li key=\"1\" className=\"benefit-item\">\n                  <CheckCircleOutlined className=\"benefit-icon\" />\n                  Full access to all features\n                </li>,\n                <li key=\"2\" className=\"benefit-item\">\n                  <CheckCircleOutlined className=\"benefit-icon\" />\n                  Unlimited quizzes and practice\n                </li>,\n                <li key=\"3\" className=\"benefit-item\">\n                  <CheckCircleOutlined className=\"benefit-icon\" />\n                  AI chat assistance\n                </li>,\n                <li key=\"4\" className=\"benefit-item\">\n                  <CheckCircleOutlined className=\"benefit-icon\" />\n                  Premium study materials\n                </li>\n              ]}\n            </ul>\n          </div>\n        </div>\n\n        {/* Action Buttons */}\n        <div className=\"action-buttons\">\n          <Button \n            type=\"primary\" \n            size=\"large\" \n            onClick={onClose}\n            className=\"continue-button\"\n          >\n            Continue Learning\n          </Button>\n          <Button \n            type=\"default\" \n            size=\"large\" \n            onClick={onClose}\n            className=\"close-button\"\n          >\n            Close\n          </Button>\n        </div>\n\n        {/* Footer Note */}\n        <div className=\"footer-note\">\n          <p>\n            💡 <strong>Tip:</strong> You can upgrade to a different plan after your current subscription expires on <strong>{endDate}</strong>\n          </p>\n        </div>\n      </div>\n    </Modal>\n  );\n};\n\nexport default UpgradeRestrictionModal;\n", "import React, { useState, useEffect } from 'react';\nimport { useSelector, useDispatch } from 'react-redux';\nimport { message } from 'antd';\nimport { getPlans } from '../../apicalls/plans';\nimport { addPayment, checkPaymentStatus } from '../../apicalls/payment';\nimport { updateUserInfo } from '../../apicalls/users';\nimport axiosInstance from '../../apicalls/index';\nimport { SetSubscription } from '../../redux/subscriptionSlice';\nimport { SetUser } from '../../redux/usersSlice';\nimport { HideLoading, ShowLoading } from '../../redux/loaderSlice';\nimport UpgradeRestrictionModal from '../UpgradeRestrictionModal/UpgradeRestrictionModal';\nimport './SubscriptionModal.css';\n\nconst SubscriptionModal = ({ isOpen, onClose, onSuccess }) => {\n  const [plans, setPlans] = useState([]);\n  const [selectedPlan, setSelectedPlan] = useState(null);\n  const [loading, setLoading] = useState(false);\n  const [paymentLoading, setPaymentLoading] = useState(false);\n  const [step, setStep] = useState('plans'); // 'plans', 'payment'\n  const [showUpgradeRestriction, setShowUpgradeRestriction] = useState(false);\n  const [processingStartTime, setProcessingStartTime] = useState(null);\n  const [showTryAgain, setShowTryAgain] = useState(false);\n\n  // Check if user has valid phone number\n  const hasValidPhone = () => {\n    const phone = user?.phoneNumber;\n    return phone && /^(06|07)\\d{8}$/.test(phone);\n  };\n\n\n  \n  const { user } = useSelector((state) => state.user);\n  const { subscriptionData } = useSelector((state) => state.subscription);\n  const dispatch = useDispatch();\n\n  useEffect(() => {\n    if (isOpen) {\n      fetchPlans();\n    }\n  }, [isOpen]);\n\n  const fetchPlans = async () => {\n    try {\n      setLoading(true);\n      const response = await getPlans();\n      setPlans(Array.isArray(response) ? response : []);\n    } catch (error) {\n      console.error('Error fetching plans:', error);\n      message.error('Failed to load subscription plans');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handlePlanSelect = (plan) => {\n    // Check if user already has an active subscription\n    if (subscriptionData && subscriptionData.status === 'active' && subscriptionData.paymentStatus === 'paid') {\n      console.log('🚫 User already has active subscription:', subscriptionData);\n      setShowUpgradeRestriction(true);\n      return;\n    }\n\n    setSelectedPlan(plan);\n    setStep('payment');\n  };\n\n  // Handle closing payment processing modal\n  const handleCloseProcessingModal = () => {\n    setStep('plans');\n    setPaymentLoading(false);\n    setShowTryAgain(false);\n    setProcessingStartTime(null);\n    dispatch(HideLoading());\n    message.info('Payment process cancelled. You can try again anytime.');\n  };\n\n  // Handle try again functionality\n  const handleTryAgain = () => {\n    if (selectedPlan) {\n      setShowTryAgain(false);\n      setProcessingStartTime(null);\n      handlePayment();\n    }\n  };\n\n  const handlePayment = async () => {\n    if (!selectedPlan) {\n      message.error('Please select a plan first');\n      return;\n    }\n\n    if (!hasValidPhone()) {\n      message.error('Please update your phone number in your profile first. Go to Profile → Edit → Phone Number');\n      return;\n    }\n\n    try {\n      setPaymentLoading(true);\n      setShowTryAgain(false);\n      setProcessingStartTime(Date.now());\n      dispatch(ShowLoading());\n\n      // Set timer for try again button (10 seconds)\n      const tryAgainTimer = setTimeout(() => {\n        setShowTryAgain(true);\n      }, 10000);\n\n      const paymentData = {\n        plan: selectedPlan,\n        userId: user._id,\n        userPhone: user.phoneNumber, // Use phone number from user profile\n        userEmail: user.email || `${user.name?.replace(/\\s+/g, '').toLowerCase()}@brainwave.temp`\n      };\n\n      const response = await addPayment(paymentData);\n\n      if (response.success) {\n        message.success('Payment initiated! Please check your phone for SMS confirmation.');\n\n        // Close modal and start checking payment status\n        onClose();\n\n        // Start checking payment status\n        checkPaymentConfirmation(response.order_id);\n      } else {\n        throw new Error(response.message || 'Payment failed');\n      }\n    } catch (error) {\n      console.error('Payment error:', error);\n      message.error(error.message || 'Payment failed. Please try again.');\n    } finally {\n      setPaymentLoading(false);\n      dispatch(HideLoading());\n    }\n  };\n\n  const checkPaymentConfirmation = async (orderId) => {\n    let attempts = 0;\n    const maxAttempts = 150; // 5 minutes (150 attempts * 2 seconds)\n\n    const checkStatus = async () => {\n      try {\n        attempts++;\n        console.log(`🔍 Checking payment status... Attempt ${attempts}/${maxAttempts}`);\n\n        const response = await checkPaymentStatus();\n        console.log('📥 Payment status response:', response);\n\n        // Handle specific error cases\n        if (response && response.error) {\n          if (response.error === 'ENDPOINT_NOT_FOUND') {\n            console.error('❌ Payment status endpoint not found');\n            message.error('Payment verification service is temporarily unavailable. Please contact support.');\n            setStep('plans'); // Return to plans step\n            return;\n          }\n\n          if (response.error === 'AUTH_REQUIRED') {\n            console.error('❌ Authentication required for payment status');\n            message.error('Please login again to check payment status.');\n            setStep('plans'); // Return to plans step\n            return;\n          }\n        }\n\n        if (response && !response.error && (\n          (response.paymentStatus === 'paid' && response.status === 'active') ||\n          (response.status === 'completed' && response.success === true)\n        )) {\n          console.log('✅ Payment confirmed! Showing success instantly...');\n\n          // Update Redux store\n          dispatch(SetSubscription(response));\n\n          // Show success message with celebration - INSTANTLY\n          message.success({\n            content: '🎉 Payment Confirmed! Welcome to Premium!',\n            duration: 5,\n            style: {\n              marginTop: '20vh',\n              fontSize: '16px',\n              fontWeight: '600'\n            }\n          });\n\n          // Trigger success callback immediately\n          onSuccess && onSuccess();\n\n          // Close modal immediately - no delay\n          onClose();\n\n          return true;\n        }\n\n        if (attempts >= maxAttempts) {\n          console.log('⏰ Payment check timeout reached');\n          message.warning({\n            content: 'Payment is still processing. Your subscription will activate automatically when payment is complete.',\n            duration: 8\n          });\n          return false;\n        }\n\n        // Continue checking - every 2 seconds for better performance\n        setTimeout(checkStatus, 2000);\n      } catch (error) {\n        console.error('❌ Error checking payment status:', error);\n        if (attempts >= maxAttempts) {\n          message.error('Unable to verify payment. Please contact support if payment was completed.');\n        } else {\n          setTimeout(checkStatus, 2000);\n        }\n      }\n    };\n\n    // Start checking immediately\n    checkStatus();\n  };\n\n  const handleClose = () => {\n    setStep('plans');\n    setSelectedPlan(null);\n    setPaymentLoading(false);\n    onClose();\n  };\n\n  if (!isOpen) return null;\n\n  return (\n    <div className=\"subscription-modal-overlay\">\n      <div className=\"subscription-modal\">\n        <div className=\"modal-header\">\n          <h2 className=\"modal-title\">\n            {step === 'plans' && '🚀 Choose Your Learning Plan'}\n            {step === 'payment' && '💳 Complete Your Payment'}\n          </h2>\n          <button className=\"close-button\" onClick={handleClose}>×</button>\n        </div>\n\n        <div className=\"modal-content\">\n          {step === 'plans' && (\n            <div className=\"plans-grid\">\n              {loading ? (\n                <div className=\"loading-state\">\n                  <div className=\"spinner\"></div>\n                  <p>Loading plans...</p>\n                </div>\n              ) : (\n                plans.map((plan) => (\n                  <div key={plan._id} className=\"plan-card\" onClick={() => handlePlanSelect(plan)}>\n                    <div className=\"plan-header\">\n                      <h3 className=\"plan-title\">{plan.title}</h3>\n                      {plan.title?.toLowerCase().includes('standard') && (\n                        <span className=\"plan-badge\">🔥 Popular</span>\n                      )}\n                    </div>\n                    \n                    <div className=\"plan-price\">\n                      <span className=\"price-amount\">{plan.discountedPrice?.toLocaleString()} TZS</span>\n                      {plan.actualPrice && plan.actualPrice !== plan.discountedPrice && (\n                        <span className=\"price-original\">{plan.actualPrice.toLocaleString()} TZS</span>\n                      )}\n                      <span className=\"price-period\">{plan.duration} month{plan.duration > 1 ? 's' : ''}</span>\n                    </div>\n\n                    <div className=\"plan-features\">\n                      {plan.features?.slice(0, 4).map((feature, index) => (\n                        <div key={index} className=\"feature\">\n                          <span className=\"feature-icon\">✓</span>\n                          <span className=\"feature-text\">{feature}</span>\n                        </div>\n                      ))}\n                      {plan.features?.length > 4 && (\n                        <div className=\"feature\">\n                          <span className=\"feature-icon\">+</span>\n                          <span className=\"feature-text\">{plan.features.length - 4} more features</span>\n                        </div>\n                      )}\n                    </div>\n\n                    <button className=\"select-plan-btn\">\n                      Choose {plan.title}\n                    </button>\n                  </div>\n                ))\n              )}\n            </div>\n          )}\n\n          {step === 'payment' && selectedPlan && (\n            <div className=\"payment-step\">\n              <div className=\"selected-plan-summary\">\n                <h3>Selected Plan: {selectedPlan.title}</h3>\n                <p className=\"plan-price-summary\">\n                  {selectedPlan.discountedPrice?.toLocaleString()} TZS for {selectedPlan.duration} month{selectedPlan.duration > 1 ? 's' : ''}\n                </p>\n              </div>\n\n              <div className=\"payment-info\">\n                <div className=\"info-item\">\n                  <span className=\"info-label\">Phone Number:</span>\n                  <div className=\"phone-display-simple\">\n                    {hasValidPhone() ? (\n                      <span className=\"info-value valid-phone\">\n                        {user.phoneNumber} ✅\n                      </span>\n                    ) : (\n                      <div className=\"invalid-phone-warning\">\n                        <span className=\"info-value invalid-phone\">\n                          {user?.phoneNumber || 'No phone number set'} ❌\n                        </span>\n                        <button\n                          className=\"update-phone-btn\"\n                          onClick={() => {\n                            message.info('Redirecting to profile to update phone number...');\n                            setTimeout(() => {\n                              window.open('/user/profile', '_blank');\n                            }, 1000);\n                          }}\n                        >\n                          Update in Profile\n                        </button>\n                      </div>\n                    )}\n                  </div>\n                </div>\n\n                <div className=\"info-item\">\n                  <span className=\"info-label\">Payment Method:</span>\n                  <span className=\"info-value\">Mobile Money (M-Pesa, Tigo Pesa, Airtel Money)</span>\n                </div>\n\n                {hasValidPhone() && (\n                  <div className=\"payment-note\">\n                    <p>💡 Payment SMS will be sent to your phone number above.</p>\n                  </div>\n                )}\n              </div>\n\n              <div className=\"payment-actions\">\n                <button className=\"back-btn\" onClick={() => setStep('plans')}>\n                  ← Back to Plans\n                </button>\n                <button\n                  className=\"pay-btn\"\n                  onClick={handlePayment}\n                  disabled={paymentLoading || !hasValidPhone()}\n                >\n                  {paymentLoading ? (\n                    <>\n                      <span className=\"btn-spinner\"></span>\n                      Processing...\n                    </>\n                  ) : !hasValidPhone() ? (\n                    'Update phone number first'\n                  ) : (\n                    `Pay ${selectedPlan.discountedPrice?.toLocaleString()} TZS`\n                  )}\n                </button>\n              </div>\n            </div>\n          )}\n\n\n        </div>\n      </div>\n\n      {/* Upgrade Restriction Modal */}\n      <UpgradeRestrictionModal\n        visible={showUpgradeRestriction}\n        onClose={() => setShowUpgradeRestriction(false)}\n        currentPlan={plans.find(p => p._id === subscriptionData?.activePlan) || subscriptionData?.plan}\n        subscription={subscriptionData}\n        user={user}\n      />\n    </div>\n  );\n};\n\nexport default SubscriptionModal;\n", "import React, { useEffect, useState } from \"react\";\r\nimport \"./index.css\";\r\nimport PageTitle from \"../../../components/PageTitle\";\r\nimport {\r\n  getUserInfo,\r\n  updateUserInfo,\r\n  updateUserPhoto,\r\n} from \"../../../apicalls/users\";\r\nimport { Form, message, Modal, Input, Button } from \"antd\";\r\nimport { useDispatch, useSelector } from \"react-redux\";\r\nimport { HideLoading, ShowLoading } from \"../../../redux/loaderSlice\";\r\nimport { getAllReportsForRanking, getUserRanking, getXPLeaderboard } from \"../../../apicalls/reports\";\r\nimport ProfilePicture from \"../../../components/common/ProfilePicture\";\r\nimport SubscriptionModal from \"../../../components/SubscriptionModal/SubscriptionModal\";\r\n\r\nconst Profile = () => {\r\n  const [userDetails, setUserDetails] = useState(null);\r\n  const [rankingData, setRankingData] = useState(null);\r\n  const [userRanking, setUserRanking] = useState(null);\r\n  const [userRankingStats, setUserRankingStats] = useState(null);\r\n  const [edit, setEdit] = useState(false);\r\n  const [imagePreview, setImagePreview] = useState(null);\r\n  const [formData, setFormData] = useState({\r\n    name: \"\",\r\n    email: \"\",\r\n    school: \"\",\r\n    level: \"\",\r\n    class_: \"\",\r\n    phoneNumber: \"\",\r\n  });\r\n  const [profileImage, setProfileImage] = useState(null);\r\n  const [showSubscriptionModal, setShowSubscriptionModal] = useState(false);\r\n  const [showLevelChangeModal, setShowLevelChangeModal] = useState(false);\r\n  const [pendingLevelChange, setPendingLevelChange] = useState(null);\r\n  const dispatch = useDispatch();\r\n  const { subscriptionData } = useSelector((state) => state.subscription);\r\n\r\n  const fetchReports = async () => {\r\n    try {\r\n      const response = await getAllReportsForRanking();\r\n      if (response.success) {\r\n        setRankingData(response.data);\r\n      } else {\r\n        message.error(response.message);\r\n      }\r\n      dispatch(HideLoading());\r\n    } catch (error) {\r\n      message.error(error.message);\r\n      dispatch(HideLoading());\r\n    }\r\n  };\r\n\r\n  const getUserStats = () => {\r\n    const Ranking = rankingData\r\n      .map((user, index) => ({\r\n        user,\r\n        ranking: index + 1,\r\n      }))\r\n      .filter((item) => item.user.userId.includes(userDetails._id));\r\n    setUserRanking(Ranking);\r\n  };\r\n\r\n  // Fetch user ranking data from the ranking system\r\n  const fetchUserRankingData = async () => {\r\n    if (!userDetails?._id) return;\r\n\r\n    try {\r\n      dispatch(ShowLoading());\r\n\r\n      // Get user's ranking position and nearby users\r\n      const rankingResponse = await getUserRanking(userDetails._id, 5);\r\n\r\n      if (rankingResponse.success) {\r\n        setUserRankingStats(rankingResponse.data);\r\n      }\r\n\r\n      // Also get the full leaderboard to find user's position\r\n      const leaderboardResponse = await getXPLeaderboard({\r\n        limit: 1000,\r\n        levelFilter: userDetails?.level || 'all'\r\n      });\r\n\r\n      if (leaderboardResponse.success) {\r\n        const userIndex = leaderboardResponse.data.findIndex(user => user._id === userDetails._id);\r\n        if (userIndex >= 0) {\r\n          const userWithRank = {\r\n            ...leaderboardResponse.data[userIndex],\r\n            rank: userIndex + 1,\r\n            totalUsers: leaderboardResponse.data.length\r\n          };\r\n          setUserRankingStats(prev => ({\r\n            ...prev,\r\n            userRank: userIndex + 1,\r\n            totalUsers: leaderboardResponse.data.length,\r\n            user: userWithRank\r\n          }));\r\n        }\r\n      }\r\n\r\n      dispatch(HideLoading());\r\n    } catch (error) {\r\n      dispatch(HideLoading());\r\n      console.error('Error fetching ranking data:', error);\r\n    }\r\n  };\r\n\r\n  useEffect(() => {\r\n    if (rankingData && userDetails) {\r\n      getUserStats();\r\n    }\r\n  }, [rankingData, userDetails]);\r\n\r\n  const getUserData = async () => {\r\n    dispatch(ShowLoading());\r\n    try {\r\n      const response = await getUserInfo();\r\n      if (response.success) {\r\n        setUserDetails(response.data);\r\n        setFormData({\r\n          name: response.data.name || \"\",\r\n          email: response.data.email || \"\",\r\n          school: response.data.school || \"\",\r\n          class_: response.data.class || \"\",\r\n          level: response.data.level || \"\",\r\n          phoneNumber: response.data.phoneNumber || \"\",\r\n        });\r\n        if (response.data.profileImage) {\r\n          setProfileImage(response.data.profileImage);\r\n        }\r\n        fetchReports();\r\n      } else {\r\n        message.error(response.message);\r\n      }\r\n    } catch (error) {\r\n      message.error(error.message);\r\n    } finally {\r\n      dispatch(HideLoading());\r\n    }\r\n  };\r\n\r\n  useEffect(() => {\r\n    if (localStorage.getItem(\"token\")) {\r\n      getUserData();\r\n    }\r\n  }, []);\r\n\r\n  const handleChange = (e) => {\r\n    const { name, value } = e.target;\r\n    if (name === \"phoneNumber\" && value.length > 10) return;\r\n    if (name === \"level\" && value !== userDetails?.level && value !== \"\") {\r\n      setPendingLevelChange(value);\r\n      setShowLevelChangeModal(true);\r\n      return;\r\n    }\r\n    setFormData((prev) => ({\r\n      ...prev,\r\n      [name]: value,\r\n      ...(name === \"level\" ? { class_: \"\" } : {}),\r\n    }));\r\n  };\r\n\r\n  const discardChanges = () => {\r\n    setFormData({\r\n      name: userDetails.name,\r\n      email: userDetails.email,\r\n      school: userDetails.school,\r\n      class_: userDetails.class,\r\n      level: userDetails.level,\r\n      phoneNumber: userDetails.phoneNumber,\r\n    });\r\n    setEdit(false);\r\n  };\r\n\r\n\r\n\r\n  const handleUpdate = async ({ skipOTP } = {}) => {\r\n    console.log('🔍 Current formData:', formData);\r\n    console.log('🔍 Current userDetails:', userDetails);\r\n\r\n    // Validation\r\n    if (!formData.name || formData.name.trim() === \"\") {\r\n      console.log('❌ Validation failed: name is empty');\r\n      return message.error(\"Please enter your name.\");\r\n    }\r\n    if (!formData.class_ || formData.class_.trim() === \"\") {\r\n      console.log('❌ Validation failed: class is empty');\r\n      return message.error(\"Please select a class.\");\r\n    }\r\n    if (!formData.level || formData.level.trim() === \"\") {\r\n      console.log('❌ Validation failed: level is empty');\r\n      return message.error(\"Please select a level.\");\r\n    }\r\n    // Email validation (optional - only validate if provided)\r\n    if (formData.email && formData.email.trim() !== \"\") {\r\n      const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\r\n      if (!emailRegex.test(formData.email)) {\r\n        return message.error(\"Please enter a valid email address.\");\r\n      }\r\n    }\r\n\r\n    // Since email is optional in username-based system, skip OTP verification\r\n    // Users can update their email directly without verification\r\n\r\n    dispatch(ShowLoading());\r\n    try {\r\n      // Prepare update payload - only include email if it has a value\r\n      const updatePayload = {\r\n        ...formData,\r\n        userId: userDetails._id,\r\n      };\r\n\r\n      // Only include email if it's provided and not empty\r\n      if (formData.email && formData.email.trim() !== \"\") {\r\n        updatePayload.email = formData.email.trim();\r\n      } else if (userDetails?.email) {\r\n        updatePayload.email = userDetails.email;\r\n      }\r\n\r\n      console.log('📤 Sending update data:', updatePayload);\r\n\r\n      const response = await updateUserInfo(updatePayload);\r\n\r\n      console.log('📥 Server response:', response);\r\n\r\n      if (response.success) {\r\n        message.success(response.message);\r\n        setEdit(false);\r\n        getUserData();\r\n        if (response.levelChanged) {\r\n          setTimeout(() => window.location.reload(), 2000);\r\n        }\r\n      } else {\r\n        console.error('❌ Update failed:', response);\r\n        message.error(response.message || \"Failed to update profile. Please try again.\");\r\n      }\r\n    } catch (error) {\r\n      console.error('❌ Update error:', error);\r\n      const errorMessage = error.response?.data?.message || error.message || \"An unexpected error occurred.\";\r\n      message.error(`Update failed: ${errorMessage}`);\r\n    } finally {\r\n      dispatch(HideLoading());\r\n    }\r\n  };\r\n\r\n  const handleLevelChangeConfirm = () => {\r\n    setFormData((prev) => ({\r\n      ...prev,\r\n      level: pendingLevelChange,\r\n      class_: \"\",\r\n    }));\r\n    setShowLevelChangeModal(false);\r\n    setPendingLevelChange(null);\r\n  };\r\n\r\n  const handleLevelChangeCancel = () => {\r\n    setShowLevelChangeModal(false);\r\n    setPendingLevelChange(null);\r\n  };\r\n\r\n  const handleImageChange = async (e) => {\r\n    const file = e.target.files[0];\r\n    if (file) {\r\n      // Validate file type\r\n      if (!file.type.startsWith('image/')) {\r\n        message.error('Please select a valid image file');\r\n        return;\r\n      }\r\n\r\n      // Validate file size (max 5MB)\r\n      if (file.size > 5 * 1024 * 1024) {\r\n        message.error('Image size should be less than 5MB');\r\n        return;\r\n      }\r\n\r\n      setProfileImage(file);\r\n\r\n      // Show preview\r\n      const reader = new FileReader();\r\n      reader.onloadend = () => setImagePreview(reader.result);\r\n      reader.readAsDataURL(file);\r\n\r\n      // Auto-upload the image\r\n      const data = new FormData();\r\n      data.append(\"profileImage\", file);\r\n      dispatch(ShowLoading());\r\n\r\n      try {\r\n        const response = await updateUserPhoto(data);\r\n        dispatch(HideLoading());\r\n        if (response.success) {\r\n          message.success(\"Profile picture updated successfully!\");\r\n          getUserData(); // Refresh user data to show new image\r\n        } else {\r\n          message.error(response.message);\r\n        }\r\n      } catch (error) {\r\n        dispatch(HideLoading());\r\n        message.error(error.message || \"Failed to update profile picture\");\r\n      }\r\n    }\r\n  };\r\n\r\n  const handleImageUpload = async () => {\r\n    const data = new FormData();\r\n    data.append(\"profileImage\", profileImage);\r\n    dispatch(ShowLoading());\r\n    try {\r\n      const response = await updateUserPhoto(data);\r\n      if (response.success) {\r\n        message.success(\"Photo updated successfully!\");\r\n        getUserData();\r\n      } else {\r\n        message.error(response.message);\r\n      }\r\n    } catch (error) {\r\n      message.error(error.message);\r\n    } finally {\r\n      dispatch(HideLoading());\r\n    }\r\n  };\r\n\r\n\r\n\r\n  // Load user data on component mount\r\n  useEffect(() => {\r\n    getUserData();\r\n  }, []);\r\n\r\n  // Load ranking data when user details are available\r\n  useEffect(() => {\r\n    if (userDetails) {\r\n      fetchUserRankingData();\r\n    }\r\n  }, [userDetails]);\r\n\r\n  // Ensure formData is synchronized with userDetails\r\n  useEffect(() => {\r\n    if (userDetails) {\r\n      setFormData({\r\n        name: userDetails.name || \"\",\r\n        email: userDetails.email || \"\", // Email is optional\r\n        school: userDetails.school || \"\",\r\n        class_: userDetails.class || \"\",\r\n        level: userDetails.level || \"\",\r\n        phoneNumber: userDetails.phoneNumber || \"\",\r\n      });\r\n    }\r\n  }, [userDetails]);\r\n\r\n  return (\r\n    <div className=\"min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50\">\r\n      <div className=\"container mx-auto px-4 py-8\">\r\n        <div className=\"max-w-4xl mx-auto\">\r\n          {/* Header */}\r\n          <div className=\"text-center mb-8\">\r\n            <h1 className=\"text-4xl font-bold text-gray-900 mb-2\">Profile</h1>\r\n            <p className=\"text-gray-600\">Manage your account settings and preferences</p>\r\n\r\n            {/* Profile Picture with Online Status - Centered Below Header */}\r\n            <div className=\"relative mt-8 flex justify-center\">\r\n              <div className=\"relative\">\r\n                <ProfilePicture\r\n                  user={userDetails}\r\n                  size=\"3xl\"\r\n                  showOnlineStatus={true}\r\n                  onClick={() => document.getElementById('profileImageInput').click()}\r\n                  className=\"hover:scale-105 transition-transform duration-200\"\r\n                  style={{\r\n                    width: '120px',\r\n                    height: '120px',\r\n                    border: '4px solid #BFDBFE',\r\n                    boxShadow: '0 4px 12px rgba(0,0,0,0.15)'\r\n                  }}\r\n                />\r\n\r\n                {/* Camera Icon Overlay */}\r\n                <div className=\"absolute bottom-2 right-2 bg-blue-600 rounded-full p-2 shadow-lg cursor-pointer hover:bg-blue-700 transition-colors duration-200\"\r\n                     onClick={() => document.getElementById('profileImageInput').click()}>\r\n                  <svg className=\"w-5 h-5 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M3 9a2 2 0 012-2h.93a2 2 0 001.664-.89l.812-1.22A2 2 0 0110.07 4h3.86a2 2 0 011.664.89l.812 1.22A2 2 0 0018.07 7H19a2 2 0 012 2v9a2 2 0 01-2 2H5a2 2 0 01-2-2V9z\" />\r\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M15 13a3 3 0 11-6 0 3 3 0 016 0z\" />\r\n                  </svg>\r\n                </div>\r\n\r\n                {/* Hidden File Input */}\r\n                <input\r\n                  id=\"profileImageInput\"\r\n                  type=\"file\"\r\n                  accept=\"image/*\"\r\n                  className=\"hidden\"\r\n                  onChange={handleImageChange}\r\n                />\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          {/* Profile Content */}\r\n          <div className=\"bg-white rounded-2xl shadow-xl overflow-hidden\">\r\n            <div className=\"p-8\">\r\n              <div className=\"flex flex-col items-center mb-8\">\r\n                {/* User Info - Horizontal Layout */}\r\n                <div className=\"flex flex-wrap justify-center gap-4 text-center mb-6\">\r\n                  <div className=\"bg-blue-50 rounded-lg px-4 py-3 border border-blue-200 min-w-[120px]\">\r\n                    <p className=\"text-sm text-blue-600 font-medium\">Name</p>\r\n                    <p className=\"text-lg font-bold text-gray-900\">{userDetails?.name || 'User'}</p>\r\n                  </div>\r\n                  <div className=\"bg-green-50 rounded-lg px-4 py-3 border border-green-200 min-w-[120px]\">\r\n                    <p className=\"text-sm text-green-600 font-medium\">Username</p>\r\n                    <p className=\"text-lg font-bold text-gray-900 truncate max-w-[150px]\">{userDetails?.username || 'username'}</p>\r\n                  </div>\r\n                  <div className=\"bg-purple-50 rounded-lg px-4 py-3 border border-purple-200 min-w-[120px]\">\r\n                    <p className=\"text-sm text-purple-600 font-medium\">Class</p>\r\n                    <p className=\"text-lg font-bold text-gray-900\">{userDetails?.class || 'N/A'}</p>\r\n                  </div>\r\n                </div>\r\n\r\n                {/* Ranking Stats - Horizontal Layout */}\r\n                {userRankingStats && (\r\n                  <div className=\"flex flex-wrap justify-center gap-4 text-center\">\r\n                    <div className=\"bg-yellow-50 rounded-lg px-4 py-3 border border-yellow-200 min-w-[120px]\">\r\n                      <p className=\"text-sm text-yellow-600 font-medium\">Rank</p>\r\n                      <p className=\"text-lg font-bold text-gray-900\">\r\n                        #{userRankingStats.userRank || 'N/A'}\r\n                        {userRankingStats.totalUsers && (\r\n                          <span className=\"text-sm text-gray-500\">/{userRankingStats.totalUsers}</span>\r\n                        )}\r\n                      </p>\r\n                    </div>\r\n                    <div className=\"bg-orange-50 rounded-lg px-4 py-3 border border-orange-200 min-w-[120px]\">\r\n                      <p className=\"text-sm text-orange-600 font-medium\">Total XP</p>\r\n                      <p className=\"text-lg font-bold text-gray-900\">\r\n                        {userRankingStats.user?.totalXP?.toLocaleString() || '0'}\r\n                      </p>\r\n                    </div>\r\n                    <div className=\"bg-indigo-50 rounded-lg px-4 py-3 border border-indigo-200 min-w-[120px]\">\r\n                      <p className=\"text-sm text-indigo-600 font-medium\">Avg Score</p>\r\n                      <p className=\"text-lg font-bold text-gray-900\">\r\n                        {userRankingStats.user?.averageScore || '0'}%\r\n                      </p>\r\n                    </div>\r\n                    <div className=\"bg-pink-50 rounded-lg px-4 py-3 border border-pink-200 min-w-[120px]\">\r\n                      <p className=\"text-sm text-pink-600 font-medium\">Quizzes</p>\r\n                      <p className=\"text-lg font-bold text-gray-900\">\r\n                        {userRankingStats.user?.totalQuizzesTaken || '0'}\r\n                      </p>\r\n                    </div>\r\n                    <div className=\"bg-teal-50 rounded-lg px-4 py-3 border border-teal-200 min-w-[120px]\">\r\n                      <p className=\"text-sm text-teal-600 font-medium\">Streak</p>\r\n                      <p className=\"text-lg font-bold text-gray-900\">\r\n                        {userRankingStats.user?.currentStreak || '0'}\r\n                      </p>\r\n                    </div>\r\n                  </div>\r\n                )}\r\n              </div>\r\n\r\n              {/* Profile Details */}\r\n              {!edit ? (\r\n                // View Mode\r\n                <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\r\n                  <div className=\"space-y-4\">\r\n                    <div>\r\n                      <label className=\"block text-sm font-medium text-gray-700 mb-1\">Name</label>\r\n                      <div className=\"p-3 bg-gray-50 rounded-lg border\">\r\n                        {userDetails?.name || 'Not provided'}\r\n                      </div>\r\n                    </div>\r\n                    <div>\r\n                      <label className=\"block text-sm font-medium text-gray-700 mb-1\">Username</label>\r\n                      <div className=\"p-3 bg-gray-50 rounded-lg border\">\r\n                        {userDetails?.username || 'Not provided'}\r\n                      </div>\r\n                    </div>\r\n                    <div>\r\n                      <label className=\"block text-sm font-medium text-gray-700 mb-1\">Email</label>\r\n                      <div className=\"p-3 bg-gray-50 rounded-lg border\">\r\n                        {userDetails?.email ? (\r\n                          <div className=\"flex items-center justify-between\">\r\n                            <span>{userDetails.email}</span>\r\n                            {userDetails.email.includes('@brainwave.temp') && (\r\n                              <span className=\"text-xs bg-blue-100 text-blue-600 px-2 py-1 rounded-full\">\r\n                                Auto-generated\r\n                              </span>\r\n                            )}\r\n                          </div>\r\n                        ) : (\r\n                          <div className=\"flex items-center justify-between\">\r\n                            <span className=\"text-gray-500\">No email set</span>\r\n                            <button\r\n                              onClick={async () => {\r\n                                const timestamp = Date.now();\r\n                                const autoEmail = `${userDetails.username}.${timestamp}@brainwave.temp`;\r\n                                setFormData(prev => ({ ...prev, email: autoEmail }));\r\n                                message.info('Auto-generated email created. Click \"Save Changes\" to update.');\r\n                              }}\r\n                              className=\"text-xs bg-green-100 text-green-600 px-2 py-1 rounded-full hover:bg-green-200 transition-colors\"\r\n                            >\r\n                              Generate Email\r\n                            </button>\r\n                          </div>\r\n                        )}\r\n                      </div>\r\n                    </div>\r\n                    <div>\r\n                      <label className=\"block text-sm font-medium text-gray-700 mb-1\">School</label>\r\n                      <div className=\"p-3 bg-gray-50 rounded-lg border\">\r\n                        {userDetails?.school || 'Not provided'}\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                  <div className=\"space-y-4\">\r\n                    <div>\r\n                      <label className=\"block text-sm font-medium text-gray-700 mb-1\">Level</label>\r\n                      <div className=\"p-3 bg-gray-50 rounded-lg border\">\r\n                        {userDetails?.level || 'Not provided'}\r\n                      </div>\r\n                    </div>\r\n                    <div>\r\n                      <label className=\"block text-sm font-medium text-gray-700 mb-1\">Class</label>\r\n                      <div className=\"p-3 bg-gray-50 rounded-lg border\">\r\n                        {userDetails?.class || 'Not provided'}\r\n                      </div>\r\n                    </div>\r\n                    <div>\r\n                      <label className=\"block text-sm font-medium text-gray-700 mb-1\">Phone Number</label>\r\n                      <div className=\"p-3 bg-gray-50 rounded-lg border\">\r\n                        {userDetails?.phoneNumber || 'Not provided'}\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              ) : (\r\n                // Edit Mode\r\n                <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\r\n                  <div className=\"space-y-4\">\r\n                    <div>\r\n                      <label className=\"block text-sm font-medium text-gray-700 mb-1\">Name *</label>\r\n                      <input\r\n                        type=\"text\"\r\n                        name=\"name\"\r\n                        value={formData.name}\r\n                        onChange={handleChange}\r\n                        className=\"w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors\"\r\n                        placeholder=\"Enter your name\"\r\n                        required\r\n                      />\r\n                    </div>\r\n                    <div>\r\n                      <label className=\"block text-sm font-medium text-gray-700 mb-1\">Username</label>\r\n                      <div className=\"p-3 bg-gray-100 rounded-lg border text-gray-600\">\r\n                        {userDetails?.username || 'Not available'}\r\n                        <span className=\"text-xs text-gray-500 block mt-1\">Username cannot be changed</span>\r\n                      </div>\r\n                    </div>\r\n                    <div>\r\n                      <label className=\"block text-sm font-medium text-gray-700 mb-1\">Email (Optional)</label>\r\n                      <div className=\"relative\">\r\n                        <input\r\n                          type=\"email\"\r\n                          name=\"email\"\r\n                          value={formData.email || \"\"}\r\n                          onChange={handleChange}\r\n                          className=\"w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors pr-24\"\r\n                          placeholder=\"Enter your email (optional)\"\r\n                        />\r\n                        {(!formData.email || formData.email === '') && (\r\n                          <button\r\n                            type=\"button\"\r\n                            onClick={() => {\r\n                              const timestamp = Date.now();\r\n                              const autoEmail = `${userDetails.username}.${timestamp}@brainwave.temp`;\r\n                              setFormData(prev => ({ ...prev, email: autoEmail }));\r\n                              message.success('Auto-generated email created!');\r\n                            }}\r\n                            className=\"absolute right-2 top-1/2 transform -translate-y-1/2 text-xs bg-blue-100 text-blue-600 px-2 py-1 rounded hover:bg-blue-200 transition-colors\"\r\n                          >\r\n                            Auto-Gen\r\n                          </button>\r\n                        )}\r\n                      </div>\r\n                      {formData.email && formData.email.includes('@brainwave.temp') && (\r\n                        <p className=\"text-xs text-blue-600 mt-1\">\r\n                          📧 This is an auto-generated email. You can change it to your real email if you prefer.\r\n                        </p>\r\n                      )}\r\n                    </div>\r\n                    <div>\r\n                      <label className=\"block text-sm font-medium text-gray-700 mb-1\">School</label>\r\n                      <input\r\n                        type=\"text\"\r\n                        name=\"school\"\r\n                        value={formData.school}\r\n                        onChange={handleChange}\r\n                        className=\"w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors\"\r\n                        placeholder=\"Enter your school\"\r\n                      />\r\n                    </div>\r\n                  </div>\r\n                  <div className=\"space-y-4\">\r\n                    <div>\r\n                      <label className=\"block text-sm font-medium text-gray-700 mb-1\">Level *</label>\r\n                      <select\r\n                        name=\"level\"\r\n                        value={formData.level}\r\n                        onChange={handleChange}\r\n                        className=\"w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors\"\r\n                        required\r\n                      >\r\n                        <option value=\"\">Select Level</option>\r\n                        <option value=\"Primary\">Primary</option>\r\n                        <option value=\"Secondary\">Secondary</option>\r\n                        <option value=\"Advance\">Advance</option>\r\n                      </select>\r\n                    </div>\r\n                    <div>\r\n                      <label className=\"block text-sm font-medium text-gray-700 mb-1\">Class *</label>\r\n                      <select\r\n                        name=\"class_\"\r\n                        value={formData.class_}\r\n                        onChange={handleChange}\r\n                        className=\"w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors\"\r\n                        required\r\n                      >\r\n                        <option value=\"\">Select Class</option>\r\n                        {formData.level === \"Primary\" && (\r\n                          <>\r\n                            <option value=\"1\">1</option>\r\n                            <option value=\"2\">2</option>\r\n                            <option value=\"3\">3</option>\r\n                            <option value=\"4\">4</option>\r\n                            <option value=\"5\">5</option>\r\n                            <option value=\"6\">6</option>\r\n                            <option value=\"7\">7</option>\r\n                          </>\r\n                        )}\r\n                        {formData.level === \"Secondary\" && (\r\n                          <>\r\n                            <option value=\"1\">1</option>\r\n                            <option value=\"2\">2</option>\r\n                            <option value=\"3\">3</option>\r\n                            <option value=\"4\">4</option>\r\n                          </>\r\n                        )}\r\n                        {formData.level === \"Advance\" && (\r\n                          <>\r\n                            <option value=\"5\">5</option>\r\n                            <option value=\"6\">6</option>\r\n                          </>\r\n                        )}\r\n                      </select>\r\n                    </div>\r\n                    <div>\r\n                      <label className=\"block text-sm font-medium text-gray-700 mb-1\">Phone Number</label>\r\n                      <input\r\n                        type=\"tel\"\r\n                        name=\"phoneNumber\"\r\n                        value={formData.phoneNumber}\r\n                        onChange={handleChange}\r\n                        className=\"w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors\"\r\n                        placeholder=\"Enter phone number\"\r\n                        maxLength=\"10\"\r\n                      />\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              )}\r\n\r\n              {/* Subscription Section */}\r\n              <div className=\"mt-8 p-6 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-2xl border border-blue-200\">\r\n                <h3 className=\"text-xl font-bold text-gray-900 mb-4 flex items-center\">\r\n                  <span className=\"mr-2\">💎</span>\r\n                  Subscription Plan\r\n                </h3>\r\n\r\n                {subscriptionData && subscriptionData.status === 'active' ? (\r\n                  // Active Subscription\r\n                  <div className=\"space-y-6\">\r\n                    {/* Plan Header */}\r\n                    <div className=\"flex items-center justify-between\">\r\n                      <div>\r\n                        <h4 className=\"text-xl font-bold text-blue-700\">{subscriptionData.planTitle || subscriptionData.plan?.title || 'Premium Plan'}</h4>\r\n                        <p className=\"text-gray-600 mt-1\">\r\n                          <span className=\"inline-flex items-center\">\r\n                            <span className=\"w-2 h-2 bg-green-500 rounded-full mr-2 animate-pulse\"></span>\r\n                            Active Subscription\r\n                          </span>\r\n                        </p>\r\n                      </div>\r\n                      <div className=\"text-right\">\r\n                        <span className=\"inline-flex items-center px-4 py-2 rounded-full text-sm font-medium bg-green-100 text-green-800 border border-green-200\">\r\n                          ✅ Active\r\n                        </span>\r\n                      </div>\r\n                    </div>\r\n\r\n                    {/* Subscription Timeline */}\r\n                    <div className=\"bg-gradient-to-r from-blue-50 to-green-50 p-4 rounded-xl border border-blue-200\">\r\n                      <h5 className=\"text-sm font-semibold text-gray-700 mb-3 flex items-center\">\r\n                        <span className=\"mr-2\">📅</span>\r\n                        Subscription Timeline\r\n                      </h5>\r\n                      <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\r\n                        <div>\r\n                          <p className=\"text-xs text-gray-500 uppercase tracking-wide\">Started On</p>\r\n                          <p className=\"text-sm font-semibold text-gray-900\">\r\n                            {subscriptionData.startDate ?\r\n                              new Date(subscriptionData.startDate).toLocaleDateString('en-US', {\r\n                                weekday: 'short',\r\n                                year: 'numeric',\r\n                                month: 'short',\r\n                                day: 'numeric'\r\n                              }) :\r\n                              new Date(subscriptionData.createdAt).toLocaleDateString('en-US', {\r\n                                weekday: 'short',\r\n                                year: 'numeric',\r\n                                month: 'short',\r\n                                day: 'numeric'\r\n                              })\r\n                            }\r\n                          </p>\r\n                        </div>\r\n                        <div>\r\n                          <p className=\"text-xs text-gray-500 uppercase tracking-wide\">Expires On</p>\r\n                          <p className=\"text-sm font-semibold text-red-600\">\r\n                            {new Date(subscriptionData.endDate).toLocaleDateString('en-US', {\r\n                              weekday: 'short',\r\n                              year: 'numeric',\r\n                              month: 'short',\r\n                              day: 'numeric'\r\n                            })}\r\n                          </p>\r\n                        </div>\r\n                      </div>\r\n                    </div>\r\n\r\n                    {/* Plan Statistics Grid */}\r\n                    <div className=\"grid grid-cols-1 md:grid-cols-4 gap-4\">\r\n                      <div className=\"bg-white p-4 rounded-xl border border-gray-200 shadow-sm\">\r\n                        <div className=\"flex items-center justify-between\">\r\n                          <div>\r\n                            <p className=\"text-xs text-gray-500 uppercase tracking-wide\">Total Duration</p>\r\n                            <p className=\"text-lg font-bold text-gray-900\">\r\n                              {subscriptionData.duration || subscriptionData.plan?.duration || 1} month{(subscriptionData.duration || subscriptionData.plan?.duration || 1) > 1 ? 's' : ''}\r\n                            </p>\r\n                          </div>\r\n                          <div className=\"text-blue-500\">\r\n                            <span className=\"text-2xl\">📆</span>\r\n                          </div>\r\n                        </div>\r\n                      </div>\r\n\r\n                      <div className=\"bg-white p-4 rounded-xl border border-gray-200 shadow-sm\">\r\n                        <div className=\"flex items-center justify-between\">\r\n                          <div>\r\n                            <p className=\"text-xs text-gray-500 uppercase tracking-wide\">Amount Paid</p>\r\n                            <p className=\"text-lg font-bold text-green-600\">\r\n                              {(subscriptionData.amount || subscriptionData.discountedPrice || 0).toLocaleString()} TZS\r\n                            </p>\r\n                          </div>\r\n                          <div className=\"text-green-500\">\r\n                            <span className=\"text-2xl\">💰</span>\r\n                          </div>\r\n                        </div>\r\n                      </div>\r\n\r\n                      <div className=\"bg-white p-4 rounded-xl border border-gray-200 shadow-sm\">\r\n                        <div className=\"flex items-center justify-between\">\r\n                          <div>\r\n                            <p className=\"text-xs text-gray-500 uppercase tracking-wide\">Days Remaining</p>\r\n                            <p className=\"text-lg font-bold text-orange-600\">\r\n                              {(() => {\r\n                                const daysLeft = Math.max(0, Math.ceil((new Date(subscriptionData.endDate) - new Date()) / (1000 * 60 * 60 * 24)));\r\n                                return daysLeft;\r\n                              })()} days\r\n                            </p>\r\n                          </div>\r\n                          <div className=\"text-orange-500\">\r\n                            <span className=\"text-2xl\">⏰</span>\r\n                          </div>\r\n                        </div>\r\n                      </div>\r\n\r\n                      <div className=\"bg-white p-4 rounded-xl border border-gray-200 shadow-sm\">\r\n                        <div className=\"flex items-center justify-between\">\r\n                          <div>\r\n                            <p className=\"text-xs text-gray-500 uppercase tracking-wide\">Total Days</p>\r\n                            <p className=\"text-lg font-bold text-purple-600\">\r\n                              {(subscriptionData.duration || subscriptionData.plan?.duration || 1) * 30} days\r\n                            </p>\r\n                          </div>\r\n                          <div className=\"text-purple-500\">\r\n                            <span className=\"text-2xl\">📊</span>\r\n                          </div>\r\n                        </div>\r\n                      </div>\r\n                    </div>\r\n\r\n                    {/* Progress Bar */}\r\n                    <div className=\"bg-white p-4 rounded-xl border border-gray-200\">\r\n                      <div className=\"flex items-center justify-between mb-2\">\r\n                        <p className=\"text-sm font-semibold text-gray-700\">Subscription Progress</p>\r\n                        <p className=\"text-xs text-gray-500\">\r\n                          {(() => {\r\n                            const duration = subscriptionData.duration || subscriptionData.plan?.duration || 1;\r\n                            const totalDays = duration * 30;\r\n                            const daysLeft = Math.max(0, Math.ceil((new Date(subscriptionData.endDate) - new Date()) / (1000 * 60 * 60 * 24)));\r\n                            const daysUsed = totalDays - daysLeft;\r\n                            const percentage = Math.min(100, Math.max(0, (daysUsed / totalDays) * 100));\r\n                            return `${percentage.toFixed(1)}% completed`;\r\n                          })()}\r\n                        </p>\r\n                      </div>\r\n                      <div className=\"w-full bg-gray-200 rounded-full h-3\">\r\n                        <div\r\n                          className=\"bg-gradient-to-r from-blue-500 to-green-500 h-3 rounded-full transition-all duration-500\"\r\n                          style={{\r\n                            width: `${(() => {\r\n                              const duration = subscriptionData.duration || subscriptionData.plan?.duration || 1;\r\n                              const totalDays = duration * 30;\r\n                              const daysLeft = Math.max(0, Math.ceil((new Date(subscriptionData.endDate) - new Date()) / (1000 * 60 * 60 * 24)));\r\n                              const daysUsed = totalDays - daysLeft;\r\n                              const percentage = Math.min(100, Math.max(0, (daysUsed / totalDays) * 100));\r\n                              return percentage;\r\n                            })()}%`\r\n                          }}\r\n                        ></div>\r\n                      </div>\r\n                      <div className=\"flex justify-between text-xs text-gray-500 mt-1\">\r\n                        <span>Started</span>\r\n                        <span>\r\n                          {(() => {\r\n                            const daysLeft = Math.max(0, Math.ceil((new Date(subscriptionData.endDate) - new Date()) / (1000 * 60 * 60 * 24)));\r\n                            if (daysLeft > 7) {\r\n                              return `${daysLeft} days left`;\r\n                            } else if (daysLeft > 0) {\r\n                              return `⚠️ ${daysLeft} days left`;\r\n                            } else {\r\n                              return '❌ Expired';\r\n                            }\r\n                          })()}\r\n                        </span>\r\n                      </div>\r\n                    </div>\r\n\r\n                    {/* Action Buttons */}\r\n                    <div className=\"flex gap-3\">\r\n                      <button\r\n                        onClick={() => setShowSubscriptionModal(true)}\r\n                        className=\"px-6 py-3 bg-gradient-to-r from-blue-600 to-blue-700 text-white rounded-lg hover:from-blue-700 hover:to-blue-800 transition-all font-medium shadow-md\"\r\n                      >\r\n                        🚀 Upgrade Plan\r\n                      </button>\r\n                      <button\r\n                        onClick={() => {\r\n                          const endDate = new Date(subscriptionData.endDate);\r\n                          const today = new Date();\r\n                          const daysLeft = Math.ceil((endDate - today) / (1000 * 60 * 60 * 24));\r\n\r\n                          if (daysLeft <= 7 && daysLeft > 0) {\r\n                            message.warning(`Your subscription expires in ${daysLeft} days. Consider renewing soon!`);\r\n                          } else if (daysLeft <= 0) {\r\n                            message.error('Your subscription has expired. Please renew to continue accessing premium features.');\r\n                          } else {\r\n                            message.info(`Your subscription is active for ${daysLeft} more days.`);\r\n                          }\r\n                        }}\r\n                        className=\"px-6 py-3 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors font-medium\"\r\n                      >\r\n                        📊 Check Status\r\n                      </button>\r\n                    </div>\r\n                  </div>\r\n                ) : (\r\n                  // No Active Subscription\r\n                  <div className=\"text-center py-8\">\r\n                    <div className=\"mb-6\">\r\n                      <div className=\"w-20 h-20 mx-auto bg-gray-100 rounded-full flex items-center justify-center mb-4\">\r\n                        <span className=\"text-4xl\">🔒</span>\r\n                      </div>\r\n                      <h4 className=\"text-xl font-bold text-gray-900 mb-2\">No Active Subscription</h4>\r\n                      <p className=\"text-gray-600 mb-6 max-w-md mx-auto\">\r\n                        Unlock premium features and get unlimited access to all educational content with a subscription plan.\r\n                      </p>\r\n                    </div>\r\n\r\n                    {/* Premium Features Preview */}\r\n                    <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4 mb-6 max-w-2xl mx-auto\">\r\n                      <div className=\"bg-gradient-to-br from-blue-50 to-indigo-50 p-4 rounded-lg border border-blue-200\">\r\n                        <div className=\"text-blue-600 text-2xl mb-2\">📚</div>\r\n                        <h5 className=\"font-semibold text-gray-900 mb-1\">Unlimited Quizzes</h5>\r\n                        <p className=\"text-sm text-gray-600\">Access all quizzes without restrictions</p>\r\n                      </div>\r\n                      <div className=\"bg-gradient-to-br from-green-50 to-emerald-50 p-4 rounded-lg border border-green-200\">\r\n                        <div className=\"text-green-600 text-2xl mb-2\">🤖</div>\r\n                        <h5 className=\"font-semibold text-gray-900 mb-1\">AI Study Assistant</h5>\r\n                        <p className=\"text-sm text-gray-600\">Get instant help with your studies</p>\r\n                      </div>\r\n                      <div className=\"bg-gradient-to-br from-purple-50 to-violet-50 p-4 rounded-lg border border-purple-200\">\r\n                        <div className=\"text-purple-600 text-2xl mb-2\">📊</div>\r\n                        <h5 className=\"font-semibold text-gray-900 mb-1\">Progress Tracking</h5>\r\n                        <p className=\"text-sm text-gray-600\">Monitor your learning progress</p>\r\n                      </div>\r\n                      <div className=\"bg-gradient-to-br from-orange-50 to-amber-50 p-4 rounded-lg border border-orange-200\">\r\n                        <div className=\"text-orange-600 text-2xl mb-2\">🏆</div>\r\n                        <h5 className=\"font-semibold text-gray-900 mb-1\">Rankings & Badges</h5>\r\n                        <p className=\"text-sm text-gray-600\">Compete and earn achievements</p>\r\n                      </div>\r\n                    </div>\r\n\r\n                    {/* Call to Action */}\r\n                    <div className=\"space-y-3\">\r\n                      <button\r\n                        onClick={() => setShowSubscriptionModal(true)}\r\n                        className=\"px-8 py-4 bg-gradient-to-r from-blue-600 to-blue-700 text-white rounded-xl hover:from-blue-700 hover:to-blue-800 transition-all font-semibold text-lg shadow-lg hover:shadow-xl transform hover:scale-105\"\r\n                      >\r\n                        🚀 Choose Subscription Plan\r\n                      </button>\r\n                      <p className=\"text-xs text-gray-500\">\r\n                        Plans start from as low as 13,000 TZS per month\r\n                      </p>\r\n                    </div>\r\n                  </div>\r\n                )}\r\n              </div>\r\n\r\n              {/* Action Buttons */}\r\n              <div className=\"mt-8 flex justify-center gap-4\">\r\n                {!edit ? (\r\n                  <button\r\n                    onClick={() => {\r\n                      // Ensure formData is properly initialized with current user data\r\n                      setFormData({\r\n                        name: userDetails?.name || \"\",\r\n                        email: userDetails?.email || \"\",\r\n                        school: userDetails?.school || \"\",\r\n                        class_: userDetails?.class || \"\",\r\n                        level: userDetails?.level || \"\",\r\n                        phoneNumber: userDetails?.phoneNumber || \"\",\r\n                      });\r\n                      setEdit(true);\r\n                    }}\r\n                    className=\"px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors duration-200 font-medium\"\r\n                  >\r\n                    Edit Profile\r\n                  </button>\r\n                ) : (\r\n                  <>\r\n                    <button\r\n                      onClick={discardChanges}\r\n                      className=\"px-6 py-3 bg-gray-500 text-white rounded-lg hover:bg-gray-600 transition-colors duration-200 font-medium\"\r\n                    >\r\n                      Cancel\r\n                    </button>\r\n                    <button\r\n                      onClick={handleUpdate}\r\n                      className=\"px-6 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors duration-200 font-medium\"\r\n                    >\r\n                      Save Changes\r\n                    </button>\r\n                    {/* Debug button - remove in production */}\r\n                    <button\r\n                      onClick={() => {\r\n                        console.log('🔍 Debug - Current formData:', formData);\r\n                        console.log('🔍 Debug - Current userDetails:', userDetails);\r\n                        alert(`FormData: ${JSON.stringify(formData, null, 2)}`);\r\n                      }}\r\n                      className=\"px-4 py-2 bg-gray-400 text-white rounded-lg hover:bg-gray-500 transition-colors duration-200 font-medium text-sm\"\r\n                    >\r\n                      Debug\r\n                    </button>\r\n                  </>\r\n                )}\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Hidden file input for profile image upload */}\r\n      <input\r\n        type=\"file\"\r\n        id=\"profileImageInput\"\r\n        accept=\"image/*\"\r\n        onChange={handleImageChange}\r\n        style={{ display: 'none' }}\r\n      />\r\n\r\n      {/* Level Change Confirmation Modal */}\r\n      <Modal\r\n        title=\"Confirm Level Change\"\r\n        open={showLevelChangeModal}\r\n        onOk={handleLevelChangeConfirm}\r\n        onCancel={() => {\r\n          setShowLevelChangeModal(false);\r\n          setPendingLevelChange(null);\r\n        }}\r\n        okText=\"Confirm\"\r\n        cancelText=\"Cancel\"\r\n      >\r\n        <p>\r\n          Are you sure you want to change your level to <strong>{pendingLevelChange}</strong>?\r\n        </p>\r\n        <p className=\"text-orange-600 text-sm mt-2\">\r\n          Note: Changing your level will reset your class selection and you'll only have access to content for the new level.\r\n        </p>\r\n      </Modal>\r\n\r\n      {/* Subscription Modal */}\r\n      <SubscriptionModal\r\n        isOpen={showSubscriptionModal}\r\n        onClose={() => setShowSubscriptionModal(false)}\r\n        onSuccess={() => {\r\n          setShowSubscriptionModal(false);\r\n          // Refresh user data to show updated subscription\r\n          getUserData();\r\n          message.success('Subscription updated successfully!');\r\n        }}\r\n      />\r\n\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default Profile;\r\n", "// This icon file is generated automatically.\nvar CalendarOutlined = { \"icon\": { \"tag\": \"svg\", \"attrs\": { \"viewBox\": \"64 64 896 896\", \"focusable\": \"false\" }, \"children\": [{ \"tag\": \"path\", \"attrs\": { \"d\": \"M880 184H712v-64c0-4.4-3.6-8-8-8h-56c-4.4 0-8 3.6-8 8v64H384v-64c0-4.4-3.6-8-8-8h-56c-4.4 0-8 3.6-8 8v64H144c-17.7 0-32 14.3-32 32v664c0 17.7 14.3 32 32 32h736c17.7 0 32-14.3 32-32V216c0-17.7-14.3-32-32-32zm-40 656H184V460h656v380zM184 392V256h128v48c0 4.4 3.6 8 8 8h56c4.4 0 8-3.6 8-8v-48h256v48c0 4.4 3.6 8 8 8h56c4.4 0 8-3.6 8-8v-48h128v136H184z\" } }] }, \"name\": \"calendar\", \"theme\": \"outlined\" };\nexport default CalendarOutlined;\n", "import _extends from \"@babel/runtime/helpers/esm/extends\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react';\nimport CalendarOutlinedSvg from \"@ant-design/icons-svg/es/asn/CalendarOutlined\";\nimport AntdIcon from \"../components/AntdIcon\";\nvar CalendarOutlined = function CalendarOutlined(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {\n    ref: ref,\n    icon: CalendarOutlinedSvg\n  }));\n};\nif (process.env.NODE_ENV !== 'production') {\n  CalendarOutlined.displayName = 'CalendarOutlined';\n}\nexport default /*#__PURE__*/React.forwardRef(CalendarOutlined);", "// This icon file is generated automatically.\nvar ClockCircleOutlined = { \"icon\": { \"tag\": \"svg\", \"attrs\": { \"viewBox\": \"64 64 896 896\", \"focusable\": \"false\" }, \"children\": [{ \"tag\": \"path\", \"attrs\": { \"d\": \"M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372z\" } }, { \"tag\": \"path\", \"attrs\": { \"d\": \"M686.7 638.6L544.1 535.5V288c0-4.4-3.6-8-8-8H488c-4.4 0-8 3.6-8 8v275.4c0 2.6 1.2 5 3.3 6.5l165.4 120.6c3.6 2.6 8.6 1.8 11.2-1.7l28.6-39c2.6-3.7 1.8-8.7-1.8-11.2z\" } }] }, \"name\": \"clock-circle\", \"theme\": \"outlined\" };\nexport default ClockCircleOutlined;\n", "import _extends from \"@babel/runtime/helpers/esm/extends\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react';\nimport ClockCircleOutlinedSvg from \"@ant-design/icons-svg/es/asn/ClockCircleOutlined\";\nimport AntdIcon from \"../components/AntdIcon\";\nvar ClockCircleOutlined = function ClockCircleOutlined(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {\n    ref: ref,\n    icon: ClockCircleOutlinedSvg\n  }));\n};\nif (process.env.NODE_ENV !== 'production') {\n  ClockCircleOutlined.displayName = 'ClockCircleOutlined';\n}\nexport default /*#__PURE__*/React.forwardRef(ClockCircleOutlined);", "// This icon file is generated automatically.\nvar CrownOutlined = { \"icon\": { \"tag\": \"svg\", \"attrs\": { \"viewBox\": \"64 64 896 896\", \"focusable\": \"false\" }, \"children\": [{ \"tag\": \"path\", \"attrs\": { \"d\": \"M899.6 276.5L705 396.4 518.4 147.5a8.06 8.06 0 00-12.9 0L319 396.4 124.3 276.5c-5.7-3.5-13.1 1.2-12.2 7.9L188.5 865c1.1 7.9 7.9 14 16 14h615.1c8 0 14.9-6 15.9-14l76.4-580.6c.8-6.7-6.5-11.4-12.3-7.9zm-126 534.1H250.3l-53.8-409.4 139.8 86.1L512 252.9l175.7 234.4 139.8-86.1-53.9 409.4zM512 509c-62.1 0-112.6 50.5-112.6 112.6S449.9 734.2 512 734.2s112.6-50.5 112.6-112.6S574.1 509 512 509zm0 160.9c-26.6 0-48.2-21.6-48.2-48.3 0-26.6 21.6-48.3 48.2-48.3s48.2 21.6 48.2 48.3c0 26.6-21.6 48.3-48.2 48.3z\" } }] }, \"name\": \"crown\", \"theme\": \"outlined\" };\nexport default CrownOutlined;\n", "import _extends from \"@babel/runtime/helpers/esm/extends\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react';\nimport CrownOutlinedSvg from \"@ant-design/icons-svg/es/asn/CrownOutlined\";\nimport AntdIcon from \"../components/AntdIcon\";\nvar CrownOutlined = function CrownOutlined(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {\n    ref: ref,\n    icon: CrownOutlinedSvg\n  }));\n};\nif (process.env.NODE_ENV !== 'production') {\n  CrownOutlined.displayName = 'CrownOutlined';\n}\nexport default /*#__PURE__*/React.forwardRef(CrownOutlined);"], "names": ["default", "axiosInstance", "require", "getPlans", "async", "get", "payload", "data", "error", "response", "addReport", "post", "getAllReports", "filters", "getAllReportsByUser", "getAllReportsForRanking", "getXPLeaderboard", "options", "arguments", "length", "undefined", "params", "URLSearchParams", "limit", "append", "classFilter", "levelFilter", "seasonFilter", "includeInactive", "concat", "toString", "getUserRanking", "userId", "context", "_ref", "title", "isMobile", "setIsMobile", "useState", "useEffect", "window", "innerWidth", "_jsx", "className", "children", "CheckCircleOutlined", "props", "ref", "React", "AntdIcon", "_extends", "icon", "CheckCircleOutlinedSvg", "_currentPlan$features", "visible", "onClose", "currentPlan", "subscription", "user", "daysRemaining", "calculateDaysRemaining", "endDate", "diffTime", "Date", "diffDays", "Math", "ceil", "max", "progress", "calculateProgress", "startDate", "totalDays", "usedDays", "min", "planTitle", "toLocaleDateString", "Modal", "open", "onCancel", "footer", "width", "centered", "maskStyle", "backgroundColor", "_jsxs", "CrownOutlined", "Progress", "percent", "strokeColor", "trailColor", "strokeWidth", "showInfo", "CalendarOutlined", "ClockCircleOutlined", "features", "slice", "map", "feature", "index", "<PERSON><PERSON>", "type", "size", "onClick", "_selectedPlan$discoun", "_selectedPlan$discoun2", "isOpen", "onSuccess", "plans", "setPlans", "<PERSON><PERSON><PERSON>", "setSelectedPlan", "loading", "setLoading", "paymentLoading", "setPaymentLoading", "step", "setStep", "showUpgradeRestriction", "setShowUpgradeRestriction", "processingStartTime", "setProcessingStartTime", "showTryAgain", "setShowTryAgain", "hasValidPhone", "phone", "phoneNumber", "test", "useSelector", "state", "subscriptionData", "dispatch", "useDispatch", "fetchPlans", "Array", "isArray", "console", "message", "handlePayment", "_user$name", "now", "ShowLoading", "setTimeout", "paymentData", "plan", "_id", "userPhone", "userEmail", "email", "name", "replace", "toLowerCase", "addPayment", "success", "Error", "checkPaymentConfirmation", "order_id", "HideLoading", "attempts", "checkStatus", "log", "checkPaymentStatus", "paymentStatus", "status", "SetSubscription", "content", "duration", "style", "marginTop", "fontSize", "fontWeight", "warning", "handleClose", "_plan$title", "_plan$discountedPrice", "_plan$features", "_plan$features2", "handlePlanSelect", "includes", "discountedPrice", "toLocaleString", "actualPrice", "info", "disabled", "_Fragment", "UpgradeRestrictionModal", "find", "p", "activePlan", "Profile", "_userRankingStats$use", "_userRankingStats$use2", "_userRankingStats$use3", "_userRankingStats$use4", "_userRankingStats$use5", "_subscriptionData$pla", "_subscriptionData$pla2", "_subscriptionData$pla3", "_subscriptionData$pla4", "userDetails", "setUserDetails", "rankingData", "setRankingData", "userRanking", "setUserRanking", "userRankingStats", "setUserRankingStats", "edit", "setEdit", "imagePreview", "setImagePreview", "formData", "setFormData", "school", "level", "class_", "profileImage", "setProfileImage", "showSubscriptionModal", "setShowSubscriptionModal", "showLevelChangeModal", "setShowLevelChangeModal", "pendingLevelChange", "setPendingLevelChange", "getUserStats", "Ranking", "ranking", "filter", "item", "getUserData", "getUserInfo", "class", "fetchReports", "localStorage", "getItem", "handleChange", "e", "value", "target", "prev", "_objectSpread", "handleImageChange", "file", "files", "startsWith", "reader", "FileReader", "onloadend", "result", "readAsDataURL", "FormData", "updateUserPhoto", "rankingResponse", "leaderboardResponse", "userIndex", "findIndex", "userWithRank", "rank", "totalUsers", "userRank", "fetchUserRankingData", "ProfilePicture", "showOnlineStatus", "document", "getElementById", "click", "height", "border", "boxShadow", "fill", "stroke", "viewBox", "strokeLinecap", "strokeLinejoin", "d", "id", "accept", "onChange", "username", "totalXP", "averageScore", "totalQuizzesTaken", "currentStreak", "placeholder", "required", "timestamp", "autoEmail", "max<PERSON><PERSON><PERSON>", "weekday", "year", "month", "day", "createdAt", "amount", "_subscriptionData$pla5", "daysUsed", "percentage", "toFixed", "_subscriptionData$pla6", "daysLeft", "today", "discardChanges", "skipOTP", "trim", "updatePayload", "updateUserInfo", "levelChanged", "location", "reload", "_error$response", "_error$response$data", "errorMessage", "alert", "JSON", "stringify", "display", "onOk", "handleLevelChangeConfirm", "okText", "cancelText", "SubscriptionModal", "CalendarOutlinedSvg", "ClockCircleOutlinedSvg", "CrownOutlinedSvg"], "sourceRoot": ""}