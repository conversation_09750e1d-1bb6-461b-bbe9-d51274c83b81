{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport Cell from \"../Cell\";\nimport TableContext from \"../context/TableContext\";\nimport { useContext } from '@rc-component/context';\nimport { getCellFixedInfo } from \"../utils/fixUtil\";\nimport { getColumnsKey } from \"../utils/valueUtil\";\nfunction HeaderRow(_ref) {\n  var cells = _ref.cells,\n    stickyOffsets = _ref.stickyOffsets,\n    flattenColumns = _ref.flattenColumns,\n    RowComponent = _ref.rowComponent,\n    CellComponent = _ref.cellComponent,\n    tdCellComponent = _ref.tdCellComponent,\n    onHeaderRow = _ref.onHeaderRow,\n    index = _ref.index;\n  var _useContext = useContext(TableContext, ['prefixCls', 'direction']),\n    prefixCls = _useContext.prefixCls,\n    direction = _useContext.direction;\n  var rowProps;\n  if (onHeaderRow) {\n    rowProps = onHeaderRow(cells.map(function (cell) {\n      return cell.column;\n    }), index);\n  }\n  var columnsKey = getColumnsKey(cells.map(function (cell) {\n    return cell.column;\n  }));\n  return /*#__PURE__*/React.createElement(RowComponent, rowProps, cells.map(function (cell, cellIndex) {\n    var column = cell.column;\n    var fixedInfo = getCellFixedInfo(cell.colStart, cell.colEnd, flattenColumns, stickyOffsets, direction, column);\n    var additionalProps;\n    if (column && column.onHeaderCell) {\n      additionalProps = cell.column.onHeaderCell(column);\n    }\n    return /*#__PURE__*/React.createElement(Cell, _extends({}, cell, {\n      scope: column.title ? cell.colSpan > 1 ? 'colgroup' : 'col' : null,\n      ellipsis: column.ellipsis,\n      align: column.align,\n      component: column.title ? CellComponent : tdCellComponent,\n      prefixCls: prefixCls,\n      key: columnsKey[cellIndex]\n    }, fixedInfo, {\n      additionalProps: additionalProps,\n      rowType: \"header\"\n    }));\n  }));\n}\nHeaderRow.displayName = 'HeaderRow';\nexport default HeaderRow;", "map": {"version": 3, "names": ["_extends", "React", "Cell", "TableContext", "useContext", "getCellFixedInfo", "getColumnsKey", "HeaderRow", "_ref", "cells", "stickyOffsets", "flattenColumns", "RowComponent", "rowComponent", "CellComponent", "cellComponent", "tdCellComponent", "onHeaderRow", "index", "_useContext", "prefixCls", "direction", "rowProps", "map", "cell", "column", "columnsKey", "createElement", "cellIndex", "fixedInfo", "colStart", "colEnd", "additionalProps", "onHeaderCell", "scope", "title", "colSpan", "ellipsis", "align", "component", "key", "rowType", "displayName"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/node_modules/rc-table/es/Header/HeaderRow.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport Cell from \"../Cell\";\nimport TableContext from \"../context/TableContext\";\nimport { useContext } from '@rc-component/context';\nimport { getCellFixedInfo } from \"../utils/fixUtil\";\nimport { getColumnsKey } from \"../utils/valueUtil\";\nfunction HeaderRow(_ref) {\n  var cells = _ref.cells,\n    stickyOffsets = _ref.stickyOffsets,\n    flattenColumns = _ref.flattenColumns,\n    RowComponent = _ref.rowComponent,\n    CellComponent = _ref.cellComponent,\n    tdCellComponent = _ref.tdCellComponent,\n    onHeaderRow = _ref.onHeaderRow,\n    index = _ref.index;\n  var _useContext = useContext(TableContext, ['prefixCls', 'direction']),\n    prefixCls = _useContext.prefixCls,\n    direction = _useContext.direction;\n  var rowProps;\n  if (onHeaderRow) {\n    rowProps = onHeaderRow(cells.map(function (cell) {\n      return cell.column;\n    }), index);\n  }\n  var columnsKey = getColumnsKey(cells.map(function (cell) {\n    return cell.column;\n  }));\n  return /*#__PURE__*/React.createElement(RowComponent, rowProps, cells.map(function (cell, cellIndex) {\n    var column = cell.column;\n    var fixedInfo = getCellFixedInfo(cell.colStart, cell.colEnd, flattenColumns, stickyOffsets, direction, column);\n    var additionalProps;\n    if (column && column.onHeaderCell) {\n      additionalProps = cell.column.onHeaderCell(column);\n    }\n    return /*#__PURE__*/React.createElement(Cell, _extends({}, cell, {\n      scope: column.title ? cell.colSpan > 1 ? 'colgroup' : 'col' : null,\n      ellipsis: column.ellipsis,\n      align: column.align,\n      component: column.title ? CellComponent : tdCellComponent,\n      prefixCls: prefixCls,\n      key: columnsKey[cellIndex]\n    }, fixedInfo, {\n      additionalProps: additionalProps,\n      rowType: \"header\"\n    }));\n  }));\n}\nHeaderRow.displayName = 'HeaderRow';\nexport default HeaderRow;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,IAAI,MAAM,SAAS;AAC1B,OAAOC,YAAY,MAAM,yBAAyB;AAClD,SAASC,UAAU,QAAQ,uBAAuB;AAClD,SAASC,gBAAgB,QAAQ,kBAAkB;AACnD,SAASC,aAAa,QAAQ,oBAAoB;AAClD,SAASC,SAASA,CAACC,IAAI,EAAE;EACvB,IAAIC,KAAK,GAAGD,IAAI,CAACC,KAAK;IACpBC,aAAa,GAAGF,IAAI,CAACE,aAAa;IAClCC,cAAc,GAAGH,IAAI,CAACG,cAAc;IACpCC,YAAY,GAAGJ,IAAI,CAACK,YAAY;IAChCC,aAAa,GAAGN,IAAI,CAACO,aAAa;IAClCC,eAAe,GAAGR,IAAI,CAACQ,eAAe;IACtCC,WAAW,GAAGT,IAAI,CAACS,WAAW;IAC9BC,KAAK,GAAGV,IAAI,CAACU,KAAK;EACpB,IAAIC,WAAW,GAAGf,UAAU,CAACD,YAAY,EAAE,CAAC,WAAW,EAAE,WAAW,CAAC,CAAC;IACpEiB,SAAS,GAAGD,WAAW,CAACC,SAAS;IACjCC,SAAS,GAAGF,WAAW,CAACE,SAAS;EACnC,IAAIC,QAAQ;EACZ,IAAIL,WAAW,EAAE;IACfK,QAAQ,GAAGL,WAAW,CAACR,KAAK,CAACc,GAAG,CAAC,UAAUC,IAAI,EAAE;MAC/C,OAAOA,IAAI,CAACC,MAAM;IACpB,CAAC,CAAC,EAAEP,KAAK,CAAC;EACZ;EACA,IAAIQ,UAAU,GAAGpB,aAAa,CAACG,KAAK,CAACc,GAAG,CAAC,UAAUC,IAAI,EAAE;IACvD,OAAOA,IAAI,CAACC,MAAM;EACpB,CAAC,CAAC,CAAC;EACH,OAAO,aAAaxB,KAAK,CAAC0B,aAAa,CAACf,YAAY,EAAEU,QAAQ,EAAEb,KAAK,CAACc,GAAG,CAAC,UAAUC,IAAI,EAAEI,SAAS,EAAE;IACnG,IAAIH,MAAM,GAAGD,IAAI,CAACC,MAAM;IACxB,IAAII,SAAS,GAAGxB,gBAAgB,CAACmB,IAAI,CAACM,QAAQ,EAAEN,IAAI,CAACO,MAAM,EAAEpB,cAAc,EAAED,aAAa,EAAEW,SAAS,EAAEI,MAAM,CAAC;IAC9G,IAAIO,eAAe;IACnB,IAAIP,MAAM,IAAIA,MAAM,CAACQ,YAAY,EAAE;MACjCD,eAAe,GAAGR,IAAI,CAACC,MAAM,CAACQ,YAAY,CAACR,MAAM,CAAC;IACpD;IACA,OAAO,aAAaxB,KAAK,CAAC0B,aAAa,CAACzB,IAAI,EAAEF,QAAQ,CAAC,CAAC,CAAC,EAAEwB,IAAI,EAAE;MAC/DU,KAAK,EAAET,MAAM,CAACU,KAAK,GAAGX,IAAI,CAACY,OAAO,GAAG,CAAC,GAAG,UAAU,GAAG,KAAK,GAAG,IAAI;MAClEC,QAAQ,EAAEZ,MAAM,CAACY,QAAQ;MACzBC,KAAK,EAAEb,MAAM,CAACa,KAAK;MACnBC,SAAS,EAAEd,MAAM,CAACU,KAAK,GAAGrB,aAAa,GAAGE,eAAe;MACzDI,SAAS,EAAEA,SAAS;MACpBoB,GAAG,EAAEd,UAAU,CAACE,SAAS;IAC3B,CAAC,EAAEC,SAAS,EAAE;MACZG,eAAe,EAAEA,eAAe;MAChCS,OAAO,EAAE;IACX,CAAC,CAAC,CAAC;EACL,CAAC,CAAC,CAAC;AACL;AACAlC,SAAS,CAACmC,WAAW,GAAG,WAAW;AACnC,eAAenC,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}