"use strict";(self.webpackChunkclient=self.webpackChunkclient||[]).push([[510],{272:(e,t,s)=>{s.d(t,{$s:()=>c,I1:()=>n,Ss:()=>l,cq:()=>r,dM:()=>o,uH:()=>i});const{default:a}=s(3371),r=async e=>{try{return(await a.post("/api/reports/add-report",e)).data}catch(t){return t.response.data}},n=async e=>{try{return(await a.post("/api/reports/get-all-reports",e)).data}catch(t){return t.response.data}},l=async e=>{try{return(await a.post("/api/reports/get-all-reports-by-user",e)).data}catch(t){return t.response.data}},i=async e=>{try{return(await a.get("/api/reports/get-all-reports-for-ranking")).data}catch(t){return t.response.data}},o=async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};try{const t=new URLSearchParams;e.limit&&t.append("limit",e.limit),e.classFilter&&t.append("classFilter",e.classFilter),e.levelFilter&&t.append("levelFilter",e.levelFilter),e.seasonFilter&&t.append("seasonFilter",e.seasonFilter),e.includeInactive&&t.append("includeInactive",e.includeInactive);return(await a.get("/api/quiz/xp-leaderboard?".concat(t.toString()))).data}catch(t){return t.response.data}},c=async function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:5;try{return(await a.get("/api/quiz/user-ranking/".concat(e,"?context=").concat(t))).data}catch(s){return s.response.data}}},5510:(e,t,s)=>{s.r(t),s.d(t,{default:()=>f});var a=s(2791),r=s(7689),n=s(9434),l=s(7027),i=s(5526),o=s(1652),c=s(272),d=s(8247),m=s(184);const x=e=>{var t;let{quiz:s,userResult:a,onStart:r,onView:n,index:l}=e;return s&&"object"===typeof s?(0,m.jsxs)("div",{className:"rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 p-3 transform hover:scale-105 opacity-100 relative flex flex-col",style:{background:"linear-gradient(135deg, #ffffff 0%, #f8fafc 50%, #f1f5f9 100%)",border:a?"Pass"===a.verdict?"2px solid #10b981":"2px solid #ef4444":"2px solid #3b82f6",boxShadow:a?"Pass"===a.verdict?"0 8px 20px rgba(16, 185, 129, 0.3)":"0 8px 20px rgba(239, 68, 68, 0.3)":"0 8px 20px rgba(59, 130, 246, 0.3)",minHeight:window.innerWidth<=768?"240px":"320px",height:"auto"},children:[(0,m.jsx)("div",{className:"mb-2 text-center",children:(0,m.jsx)("h3",{className:"font-bold mb-1 line-clamp-2",style:{color:"#1f2937",textShadow:"0 1px 2px rgba(0,0,0,0.1)",lineHeight:"1.1",fontSize:window.innerWidth<=768?"14px":"16px"},children:"string"===typeof s.name?s.name:"Untitled Quiz"})}),(0,m.jsx)("div",{className:"mb-2 text-center",children:a?(0,m.jsxs)("div",{className:"flex items-center justify-center gap-1",children:[(0,m.jsx)("div",{className:"px-2 py-1 rounded-full text-xs font-bold text-white shadow-md",style:{backgroundColor:"Pass"===a.verdict?"#10b981":"#ef4444",fontSize:window.innerWidth<=768?"9px":"10px"},children:"Pass"===a.verdict?"\u2705 PASSED":"\u274c FAILED"}),(0,m.jsxs)("div",{className:"px-2 py-1 rounded-full text-xs font-bold text-center shadow-md",style:{backgroundColor:"#ffffff",color:"#1f2937",fontSize:window.innerWidth<=768?"9px":"10px"},children:["number"===typeof a.percentage?a.percentage:0,"%"]})]}):(0,m.jsx)("div",{className:"px-2 py-1 rounded-full text-xs font-bold text-white shadow-md",style:{backgroundColor:"#3b82f6",fontSize:window.innerWidth<=768?"9px":"10px"},children:"\ud83c\udd95 NOT ATTEMPTED"})}),(0,m.jsx)("div",{className:"text-center mb-6",children:(0,m.jsxs)("div",{className:"flex-1",children:[(0,m.jsxs)("div",{className:"flex gap-1 mb-2 justify-center",children:[(0,m.jsxs)("div",{className:"flex items-center gap-1 rounded-lg py-1 px-2 border shadow-sm",style:{background:"linear-gradient(to right, #eff6ff, #e0e7ff)",borderColor:"#bfdbfe"},children:[(0,m.jsx)(i.pBF,{className:"w-3 h-3",style:{color:"#2563eb"}}),(0,m.jsx)("span",{className:"font-bold",style:{color:"#1e40af",fontSize:window.innerWidth<=768?"11px":"12px"},children:Array.isArray(s.questions)?s.questions.length:0})]}),(0,m.jsxs)("div",{className:"flex items-center gap-1 rounded-lg py-1 px-2 border shadow-sm",style:{background:"linear-gradient(to right, #fdf4ff, #fce7f3)",borderColor:"#e9d5ff"},children:[(0,m.jsx)(i.rfE,{className:"w-3 h-3",style:{color:"#9333ea"}}),(0,m.jsx)("span",{className:"font-bold",style:{color:"#7c3aed",fontSize:window.innerWidth<=768?"11px":"12px"},children:"3m"})]})]}),(0,m.jsxs)("div",{className:"flex items-center justify-center gap-1 flex-wrap mb-2",children:[(0,m.jsxs)("span",{className:"inline-block px-1 py-1 font-bold rounded-full text-white shadow-sm",style:{background:"linear-gradient(to right, #8b5cf6, #7c3aed)",fontSize:window.innerWidth<=768?"8px":"10px"},children:["\ud83c\udfaf",s.level||"Primary"]}),(0,m.jsxs)("span",{className:"inline-block px-1 py-1 font-bold rounded-full text-white shadow-sm",style:{background:"linear-gradient(to right, #4ade80, #3b82f6)",fontSize:window.innerWidth<=768?"8px":"10px"},children:["\ud83d\udcd6","string"===typeof s.class||"number"===typeof s.class?"primary"===s.level?"Class ".concat(s.class):s.class:"N/A"]}),(0,m.jsxs)("span",{className:"inline-block px-1 py-1 font-bold rounded-full text-white shadow-sm",style:{background:"linear-gradient(to right, #f97316, #ea580c)",fontSize:window.innerWidth<=768?"8px":"10px"},children:["\ud83d\udcc2",s.category||"General"]}),(0,m.jsxs)("span",{className:"inline-block px-1 py-1 font-bold rounded-full text-white shadow-sm",style:{background:s.topic&&"General"!==s.topic&&""!==s.topic?"linear-gradient(to right, #10b981, #059669)":"linear-gradient(to right, #6b7280, #4b5563)",fontSize:window.innerWidth<=768?"8px":"10px"},children:["\ud83d\udcda",s.topic||"General"]})]})]})}),a&&"object"===typeof a&&(0,m.jsxs)("div",{className:"mb-2 p-2 rounded-lg border shadow-md",style:{background:"Pass"===a.verdict?"linear-gradient(to bottom right, #f0fdf4, #ecfdf5)":"linear-gradient(to bottom right, #fef2f2, #fdf2f8)",borderColor:"Pass"===a.verdict?"#86efac":"#fca5a5"},children:[(0,m.jsxs)("div",{className:"flex items-center justify-between mb-3",children:[(0,m.jsxs)("div",{className:"flex items-center gap-3",children:["Pass"===a.verdict?(0,m.jsx)("div",{className:"w-10 h-10 rounded-full flex items-center justify-center shadow-lg border-2 animate-pulse",style:{background:"linear-gradient(to right, #10b981, #059669)",borderColor:"#86efac"},children:(0,m.jsx)(i.e6w,{className:"w-6 h-6 font-bold",style:{color:"#ffffff"}})}):(0,m.jsx)("div",{className:"w-10 h-10 rounded-full flex items-center justify-center shadow-lg border-2 animate-pulse",style:{background:"linear-gradient(to right, #ef4444, #dc2626)",borderColor:"#fca5a5"},children:(0,m.jsx)(i.lhV,{className:"w-6 h-6 font-bold",style:{color:"#ffffff"}})}),(0,m.jsxs)("div",{children:[(0,m.jsx)("span",{className:"text-lg font-bold",style:{color:"#1f2937"},children:"\ud83c\udfc6 Last Result"}),(0,m.jsx)("div",{className:"text-sm",style:{color:"#6b7280"},children:new Date(a.completedAt||a.createdAt||Date.now()).toLocaleDateString()})]})]}),(0,m.jsxs)("span",{className:"text-3xl font-bold shadow-lg",style:{color:"Pass"===a.verdict?"#059669":"#dc2626"},children:["number"===typeof a.percentage?a.percentage:0,"%"]})]}),(0,m.jsxs)("div",{className:"flex gap-1 justify-center flex-wrap",children:[(0,m.jsxs)("div",{className:"flex items-center gap-1 rounded-lg py-1 px-2 border shadow-md",style:{background:"linear-gradient(to right, #dcfce7, #fecaca)",borderColor:"#86efac"},children:[(0,m.jsxs)("div",{className:"flex items-center gap-1",children:[(0,m.jsx)(i.e6w,{className:"w-3 h-3",style:{color:"#16a34a"}}),(0,m.jsx)("span",{className:"text-sm font-bold",style:{color:"#15803d"},children:"number"===typeof a.correctAnswers?a.correctAnswers:0})]}),(0,m.jsxs)("div",{className:"flex items-center gap-1",children:[(0,m.jsx)(i.lhV,{className:"w-3 h-3",style:{color:"#dc2626"}}),(0,m.jsx)("span",{className:"text-sm font-bold",style:{color:"#b91c1c"},children:((null===(t=s.questions)||void 0===t?void 0:t.length)||0)-("number"===typeof a.correctAnswers?a.correctAnswers:0)})]})]}),(0,m.jsxs)("div",{className:"flex items-center gap-1 rounded-lg py-1 px-2 border shadow-md",style:{background:"linear-gradient(to bottom right, #fef3c7, #fed7aa)",borderColor:"#fde047"},children:[(0,m.jsx)("span",{className:"text-sm",children:"\u2b50"}),(0,m.jsx)("span",{className:"text-sm font-bold",style:{color:"#92400e"},children:a.xpEarned||a.points||0})]}),a.timeTaken&&a.timeTaken>0&&(0,m.jsxs)("div",{className:"flex items-center gap-1 rounded-lg py-1 px-2 border shadow-md",style:{background:"linear-gradient(to bottom right, #e9d5ff, #f3e8ff)",borderColor:"#c4b5fd"},children:[(0,m.jsx)(i.rfE,{className:"w-3 h-3",style:{color:"#9333ea"}}),(0,m.jsx)("span",{className:"text-sm font-bold",style:{color:"#7c3aed"},children:(e=>{if(!e&&0!==e)return"0s";let t=e;if("string"===typeof e&&(t=parseInt(e,10)),isNaN(t)||t<0)return"0s";const s=Math.floor(t/60),a=t%60;return s>0?"".concat(s,"m ").concat(a,"s"):"".concat(e,"s")})(a.timeTaken)})]})]})]}),(0,m.jsx)("div",{className:"flex-1"}),(0,m.jsxs)("div",{className:"flex gap-2 mt-3",children:[(0,m.jsxs)("button",{onClick:()=>r(s),className:"flex-1 flex items-center justify-center gap-1 px-3 py-2 rounded-lg font-bold transition-all duration-200 shadow-md hover:shadow-lg transform hover:scale-105 active:scale-95 text-white",style:{background:a?"linear-gradient(to right, #f97316, #ef4444)":"linear-gradient(to right, #3b82f6, #8b5cf6)",fontSize:"13px",minHeight:"36px"},children:[(0,m.jsx)(i.y82,{className:"w-3 h-3"}),a?"\ud83d\udd04 Retake Quiz":"\ud83d\ude80 Start Quiz"]}),a&&(0,m.jsx)("button",{onClick:()=>n(s),className:"px-3 py-2 rounded-lg transition-all duration-200 font-bold transform hover:scale-105 active:scale-95 shadow-md hover:shadow-lg text-white",style:{background:"linear-gradient(to right, #fbbf24, #f97316)",fontSize:"13px",minHeight:"36px"},title:"View Results",children:(0,m.jsx)(i.gBl,{className:"w-3 h-3"})})]})]}):(0,m.jsx)("div",{className:"bg-white rounded-2xl shadow-lg p-6 border border-gray-100",children:(0,m.jsx)("p",{className:"text-gray-500",children:"Invalid quiz data"})})},f=()=>{const[e,t]=(0,a.useState)([]),[s,f]=(0,a.useState)([]),[u,p]=(0,a.useState)((()=>localStorage.getItem("quiz-search-term")||"")),[g,h]=(0,a.useState)((()=>localStorage.getItem("quiz-selected-class")||"")),[b,v]=(0,a.useState)({}),[w,y]=(0,a.useState)(!0),[j,N]=(0,a.useState)(null),z=(0,r.s0)(),S=(0,n.I0)(),{user:k}=(0,n.v9)((e=>e.user));(0,a.useEffect)((()=>(document.body.classList.add("quiz-page-active"),()=>{document.body.classList.remove("quiz-page-active")})),[]),(0,a.useEffect)((()=>{k||q()}),[k]);const q=()=>{p(""),h(""),localStorage.removeItem("quiz-search-term"),localStorage.removeItem("quiz-selected-class")},_=(0,a.useCallback)((async()=>{try{if(null===k||void 0===k||!k._id)return;const e=await(0,c.Ss)({userId:k._id});if(e.success){const t={};e.data.forEach((e=>{var s;const a=null===(s=e.exam)||void 0===s?void 0:s._id;if(!a||!e.result)return;const r=e.result;(!t[a]||new Date(e.createdAt)>new Date(t[a].createdAt))&&(t[a]={verdict:r.verdict,percentage:r.percentage,correctAnswers:r.correctAnswers,wrongAnswers:r.wrongAnswers,totalQuestions:r.totalQuestions,obtainedMarks:r.obtainedMarks,totalMarks:r.totalMarks,score:r.score,points:r.points,xpEarned:r.xpEarned||r.points||r.xpGained||0,timeTaken:e.timeTaken,completedAt:e.createdAt})})),v(t)}}catch(e){console.error("Error fetching user results:",e)}}),[null===k||void 0===k?void 0:k._id]),C=(0,a.useCallback)((async()=>{try{if(!k)return void console.log("User not loaded yet, skipping exam fetch");const e=(null===k||void 0===k?void 0:k.level)||"primary",s="user_exams_cache_".concat(e),a="user_exams_cache_time_".concat(e);["primary","secondary","advance"].forEach((t=>{t!==e&&(localStorage.removeItem("user_exams_cache_".concat(t)),localStorage.removeItem("user_exams_cache_time_".concat(t)))}));const r=localStorage.getItem(s),n=localStorage.getItem(a),i=Date.now();if(r&&n&&i-parseInt(n)<6e5){const s=JSON.parse(r);return t(s),N(new Date(parseInt(n))),y(!1),void console.log("\ud83d\udccb Using cached exams for ".concat(e," level:"),s.length)}S((0,d.YC)());const c=await(0,o.h_)();if(S((0,d.Ir)()),c.success){console.log("Raw exams from API:",c.data.length),console.log("User level:",null===k||void 0===k?void 0:k.level);const e=c.data.filter((e=>!!(e.level&&k&&k.level)&&e.level.toLowerCase()===k.level.toLowerCase()));console.log("User level exams after filtering:",e.length);const r=e.sort(((e,t)=>new Date(t.createdAt)-new Date(e.createdAt)));t(r),N(new Date),localStorage.setItem(s,JSON.stringify(r)),localStorage.setItem(a,Date.now().toString()),null!==k&&void 0!==k&&k.class&&h(String(k.class))}else l.ZP.error(c.message)}catch(e){S((0,d.Ir)()),l.ZP.error(e.message)}finally{y(!1)}}),[S,k]);(0,a.useEffect)((()=>{["primary","secondary","advance"].forEach((e=>{localStorage.removeItem("user_exams_cache_".concat(e)),localStorage.removeItem("user_exams_cache_time_".concat(e))})),localStorage.removeItem("user_exams_cache"),localStorage.removeItem("user_exams_cache_time"),C(),_()}),[C,_]),(0,a.useEffect)((()=>{const e=()=>{console.log("\ud83d\udd04 Quiz listing - refreshing data after quiz completion..."),_()},t=()=>{console.log("\ud83c\udd95 New exam created - refreshing user results only..."),k&&_()},s=()=>{console.log("\ud83c\udfaf Quiz listing - window focused, refreshing user results..."),_()};return window.addEventListener("rankingUpdate",e),window.addEventListener("focus",s),window.addEventListener("newExamCreated",t),()=>{window.removeEventListener("rankingUpdate",e),window.removeEventListener("focus",s),window.removeEventListener("newExamCreated",t)}}),[]),(0,a.useEffect)((()=>{console.log("Filtering exams:",{exams:e.length,searchTerm:u,selectedClass:g});let t=e;if(u){const e=u.toLowerCase();t=t.filter((t=>[t.name,t.subject,t.topic,t.description,t.category,t.level,...(t.questions||[]).map((e=>e.questionText)),...(t.questions||[]).map((e=>e.subject)),...(t.questions||[]).map((e=>e.topic))].some((t=>t&&t.toString().toLowerCase().includes(e)))))}g&&(t=t.filter((e=>String(e.class)===String(g)))),t.sort(((e,t)=>new Date(t.createdAt)-new Date(e.createdAt))),console.log("Filtered exams result:",t.length),f(t)}),[e,u,g]);const I=[...new Set(e.map((e=>e.class)).filter(Boolean))].sort(),A=e=>{if(!e||!e._id)return void l.ZP.error("Invalid quiz selected. Please try again.");/^[0-9a-fA-F]{24}$/.test(e._id)?(0,a.startTransition)((()=>{z("/quiz/".concat(e._id,"/play"))})):l.ZP.error("Invalid quiz ID format. Please try again.")};return w?(0,m.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center",children:(0,m.jsxs)("div",{className:"text-center",children:[(0,m.jsx)("div",{className:"animate-spin rounded-full h-16 w-16 border-b-2 border-blue-600 mx-auto mb-4"}),(0,m.jsx)("p",{className:"text-gray-600",children:"Loading quizzes..."})]})}):(0,m.jsxs)(m.Fragment,{children:[(0,m.jsx)("style",{jsx:!0,global:!0,children:"\n        /* Completely remove all white space from ProtectedRoute main wrapper */\n        body.quiz-page-active main {\n          padding: 0 !important;\n          margin: 0 !important;\n        }\n\n        /* Remove all spacing from layout containers */\n        body.quiz-page-active .safe-content-animation {\n          padding: 0 !important;\n          margin: 0 !important;\n        }\n\n        /* Remove any inherited spacing from all parent containers */\n        body.quiz-page-active main > div,\n        body.quiz-page-active main * {\n          margin-top: 0 !important;\n        }\n\n        /* Ensure Quiz container starts immediately */\n        .quiz-container-immediate {\n          margin-top: 0 !important;\n          padding-top: 0 !important;\n        }\n        .quiz-grid {\n          gap: 1rem !important;\n          margin-top: 1rem !important;\n        }\n        @media (min-width: 640px) {\n          .quiz-grid {\n            gap: 1.25rem !important;\n            margin-top: 1rem !important;\n          }\n        }\n        @media (min-width: 1024px) {\n          .quiz-grid {\n            gap: 1.5rem !important;\n          }\n        }\n      "}),(0,m.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 quiz-container-immediate",children:(0,m.jsxs)("div",{className:"container mx-auto px-3 sm:px-4 lg:px-8 pt-3 pb-3 sm:pb-4 lg:pb-6",children:[(0,m.jsx)("div",{className:"max-w-4xl mx-auto mb-3 sm:mb-4 opacity-100",children:(0,m.jsxs)("div",{className:"bg-white rounded-xl shadow-md p-3 sm:p-4",children:[(0,m.jsxs)("div",{className:"flex flex-col sm:flex-row items-center justify-between mb-3 pb-3 border-b border-gray-100",children:[(0,m.jsxs)("div",{className:"flex items-center gap-4 text-sm text-gray-600",children:[(0,m.jsxs)("div",{className:"flex items-center gap-2",children:[(0,m.jsx)("div",{className:"w-2.5 h-2.5 bg-blue-500 rounded-full"}),(0,m.jsxs)("span",{className:"font-medium",children:["Level: ",(null===k||void 0===k?void 0:k.level)||"All Levels"]})]}),(0,m.jsxs)("div",{className:"flex items-center gap-2",children:[(0,m.jsx)("div",{className:"w-2.5 h-2.5 bg-green-500 rounded-full"}),(0,m.jsxs)("span",{children:[s.length," Available Quizzes"]})]})]}),j&&(0,m.jsxs)("div",{className:"flex items-center gap-2 text-xs text-gray-400 mt-2 sm:mt-0",children:[(0,m.jsx)("div",{className:"w-2 h-2 bg-gray-400 rounded-full"}),(0,m.jsxs)("span",{children:["Updated: ",j.toLocaleTimeString()]})]})]}),(0,m.jsxs)("div",{className:"flex flex-col sm:flex-row gap-2 sm:gap-3",children:[(0,m.jsxs)("div",{className:"flex-1 relative",children:[(0,m.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,m.jsx)(i.adB,{className:"h-4 w-4 text-gray-400"})}),(0,m.jsx)("input",{type:"text",placeholder:"Search quizzes by subject, topic, or name...",value:u,onChange:e=>{return t=e.target.value,p(t),void localStorage.setItem("quiz-search-term",t);var t},className:"block w-full pl-9 pr-3 py-2 border border-gray-300 rounded-lg bg-gray-50 focus:bg-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all text-sm"})]}),(0,m.jsx)("div",{className:"w-36 sm:w-40",children:(0,m.jsxs)("div",{className:"relative",children:[(0,m.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,m.jsx)(i.a9n,{className:"h-4 w-4 text-gray-400"})}),(0,m.jsxs)("select",{value:g,onChange:e=>{return t=e.target.value,h(t),void localStorage.setItem("quiz-selected-class",t);var t},className:"block w-full pl-9 pr-8 py-2 border border-gray-300 rounded-lg bg-gray-50 focus:bg-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all appearance-none text-sm",children:[(0,m.jsx)("option",{value:"",children:"All Classes"}),I.map((e=>(0,m.jsx)("option",{value:e,children:"primary"===(null===k||void 0===k?void 0:k.level)?"Class ".concat(e):e},e)))]})]})}),(u||g)&&(0,m.jsxs)("button",{onClick:q,className:"flex items-center justify-center px-3 py-2 bg-gradient-to-r from-gray-500 to-gray-600 text-white rounded-lg hover:from-gray-600 hover:to-gray-700 focus:outline-none focus:ring-2 focus:ring-gray-500 transition-all shadow-md",title:"Clear all search and filters",children:[(0,m.jsx)(i.lhV,{className:"h-4 w-4"}),(0,m.jsx)("span",{className:"ml-1.5 hidden sm:inline text-sm",children:"Clear"})]})]})]})}),(0,m.jsx)("div",{className:"opacity-100",style:{marginTop:"0.5rem"},children:0===s.length?(0,m.jsx)("div",{className:"text-center py-8 sm:py-12",children:(0,m.jsxs)("div",{className:"bg-white rounded-2xl shadow-lg p-6 sm:p-8 max-w-md mx-auto",children:[(0,m.jsx)(i.Chd,{className:"w-12 h-12 sm:w-16 sm:h-16 text-gray-400 mx-auto mb-4"}),(0,m.jsx)("h3",{className:"text-lg sm:text-xl font-semibold text-gray-900 mb-2",children:"No Quizzes Found"}),(0,m.jsx)("p",{className:"text-gray-600 text-sm sm:text-base",children:u||g?"Try adjusting your search or filter criteria.":"No quizzes are available for your level at the moment."})]})}):(0,m.jsx)("div",{className:"quiz-grid",children:s.map(((e,t)=>(0,m.jsx)(x,{quiz:e,userResult:b[e._id],showResults:!0,onStart:A,onView:()=>(e=>{if(!e||!e._id)return void l.ZP.error("Invalid quiz selected. Please try again.");b[e._id]?(0,a.startTransition)((()=>{z("/quiz/".concat(e._id,"/result"))})):l.ZP.info("You need to attempt this quiz first to view results.")})(e),index:t},e._id)))})})]})})]})}}}]);
//# sourceMappingURL=510.44e94fe3.chunk.js.map