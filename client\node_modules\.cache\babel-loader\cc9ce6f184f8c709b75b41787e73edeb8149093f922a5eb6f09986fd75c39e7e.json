{"ast": null, "code": "import _typeof from \"@babel/runtime/helpers/esm/typeof\";\nimport * as React from 'react';\nvar ExtraContent = /*#__PURE__*/React.forwardRef(function (_ref, ref) {\n  var position = _ref.position,\n    prefixCls = _ref.prefixCls,\n    extra = _ref.extra;\n  if (!extra) return null;\n  var content;\n\n  // Parse extra\n  var assertExtra = {};\n  if (_typeof(extra) === 'object' && ! /*#__PURE__*/React.isValidElement(extra)) {\n    assertExtra = extra;\n  } else {\n    assertExtra.right = extra;\n  }\n  if (position === 'right') {\n    content = assertExtra.right;\n  }\n  if (position === 'left') {\n    content = assertExtra.left;\n  }\n  return content ? /*#__PURE__*/React.createElement(\"div\", {\n    className: \"\".concat(prefixCls, \"-extra-content\"),\n    ref: ref\n  }, content) : null;\n});\nif (process.env.NODE_ENV !== 'production') {\n  ExtraContent.displayName = 'ExtraContent';\n}\nexport default ExtraContent;", "map": {"version": 3, "names": ["_typeof", "React", "ExtraContent", "forwardRef", "_ref", "ref", "position", "prefixCls", "extra", "content", "assertExtra", "isValidElement", "right", "left", "createElement", "className", "concat", "process", "env", "NODE_ENV", "displayName"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/node_modules/rc-tabs/es/TabNavList/ExtraContent.js"], "sourcesContent": ["import _typeof from \"@babel/runtime/helpers/esm/typeof\";\nimport * as React from 'react';\nvar ExtraContent = /*#__PURE__*/React.forwardRef(function (_ref, ref) {\n  var position = _ref.position,\n    prefixCls = _ref.prefixCls,\n    extra = _ref.extra;\n  if (!extra) return null;\n  var content;\n\n  // Parse extra\n  var assertExtra = {};\n  if (_typeof(extra) === 'object' && ! /*#__PURE__*/React.isValidElement(extra)) {\n    assertExtra = extra;\n  } else {\n    assertExtra.right = extra;\n  }\n  if (position === 'right') {\n    content = assertExtra.right;\n  }\n  if (position === 'left') {\n    content = assertExtra.left;\n  }\n  return content ? /*#__PURE__*/React.createElement(\"div\", {\n    className: \"\".concat(prefixCls, \"-extra-content\"),\n    ref: ref\n  }, content) : null;\n});\nif (process.env.NODE_ENV !== 'production') {\n  ExtraContent.displayName = 'ExtraContent';\n}\nexport default ExtraContent;"], "mappings": "AAAA,OAAOA,OAAO,MAAM,mCAAmC;AACvD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,IAAIC,YAAY,GAAG,aAAaD,KAAK,CAACE,UAAU,CAAC,UAAUC,IAAI,EAAEC,GAAG,EAAE;EACpE,IAAIC,QAAQ,GAAGF,IAAI,CAACE,QAAQ;IAC1BC,SAAS,GAAGH,IAAI,CAACG,SAAS;IAC1BC,KAAK,GAAGJ,IAAI,CAACI,KAAK;EACpB,IAAI,CAACA,KAAK,EAAE,OAAO,IAAI;EACvB,IAAIC,OAAO;;EAEX;EACA,IAAIC,WAAW,GAAG,CAAC,CAAC;EACpB,IAAIV,OAAO,CAACQ,KAAK,CAAC,KAAK,QAAQ,IAAI,EAAE,aAAaP,KAAK,CAACU,cAAc,CAACH,KAAK,CAAC,EAAE;IAC7EE,WAAW,GAAGF,KAAK;EACrB,CAAC,MAAM;IACLE,WAAW,CAACE,KAAK,GAAGJ,KAAK;EAC3B;EACA,IAAIF,QAAQ,KAAK,OAAO,EAAE;IACxBG,OAAO,GAAGC,WAAW,CAACE,KAAK;EAC7B;EACA,IAAIN,QAAQ,KAAK,MAAM,EAAE;IACvBG,OAAO,GAAGC,WAAW,CAACG,IAAI;EAC5B;EACA,OAAOJ,OAAO,GAAG,aAAaR,KAAK,CAACa,aAAa,CAAC,KAAK,EAAE;IACvDC,SAAS,EAAE,EAAE,CAACC,MAAM,CAACT,SAAS,EAAE,gBAAgB,CAAC;IACjDF,GAAG,EAAEA;EACP,CAAC,EAAEI,OAAO,CAAC,GAAG,IAAI;AACpB,CAAC,CAAC;AACF,IAAIQ,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzCjB,YAAY,CAACkB,WAAW,GAAG,cAAc;AAC3C;AACA,eAAelB,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}