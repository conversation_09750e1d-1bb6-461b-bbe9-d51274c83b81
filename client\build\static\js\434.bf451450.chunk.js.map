{"version": 3, "file": "static/js/434.bf451450.chunk.js", "mappings": "uLACO,MAAMA,EAA4BC,EAAAA,cAAoB,CAC3DC,YAAa,EACbC,eAAgB,EAChBC,aAAc,EACdC,gBAAgB,IAELC,EAAuBN,EAAaO,SCuCjD,EA5CaC,IACX,IAAI,UACFC,EAAS,UACTC,EAAS,MACTC,EAAK,gBACLC,EAAe,SACfC,EAAQ,MACRC,EAAK,KACLC,EACAC,MAAOC,GACLT,EACJ,MAAM,eACJL,EAAc,aACdC,EAAY,YACZF,EAAW,eACXG,GACEJ,EAAAA,WAAiBD,GACrB,IAAIgB,EAAQ,CAAC,EAgBb,OAfKX,IACe,aAAdK,EACEC,EAAQT,IACVc,EAAQ,CACNE,aAAcf,GAAkBW,EAAQ,EAAI,KAIhDE,EAAQG,OAAOC,OAAOD,OAAOC,OAAO,CAAC,EAAGT,EAAQT,GAAe,CAC7D,CAACU,GAAkBT,GAAkBW,EAAQ,EAAI,KAC/CC,GAAQ,CACVM,cAAejB,KAIJ,OAAbS,QAAkCS,IAAbT,EAChB,KAEWZ,EAAAA,cAAoBA,EAAAA,SAAgB,KAAmBA,EAAAA,cAAoB,MAAO,CACpGQ,UAAWA,EACXO,MAAOG,OAAOC,OAAOD,OAAOC,OAAO,CAAC,EAAGJ,GAAQC,IAC9CJ,GAAWF,EAAQT,GAAeY,GAAsBb,EAAAA,cAAoB,OAAQ,CACrFQ,UAAW,GAAFc,OAAKd,EAAS,UACvBO,MAAOA,GACNF,GAAO,E,cC1CRU,EAAgC,SAAUC,EAAGC,GAC/C,IAAIC,EAAI,CAAC,EACT,IAAK,IAAIC,KAAKH,EAAON,OAAOU,UAAUC,eAAeC,KAAKN,EAAGG,IAAMF,EAAEM,QAAQJ,GAAK,IAAGD,EAAEC,GAAKH,EAAEG,IAC9F,GAAS,MAALH,GAAqD,oBAAjCN,OAAOc,sBAA2C,KAAIC,EAAI,EAAb,IAAgBN,EAAIT,OAAOc,sBAAsBR,GAAIS,EAAIN,EAAEO,OAAQD,IAClIR,EAAEM,QAAQJ,EAAEM,IAAM,GAAKf,OAAOU,UAAUO,qBAAqBL,KAAKN,EAAGG,EAAEM,MAAKP,EAAEC,EAAEM,IAAMT,EAAEG,EAAEM,IADuB,CAGvH,OAAOP,CACT,EAWA,MAAMU,EAAY,CAChBC,MAAO,EACPC,OAAQ,GACRC,MAAO,IAKT,MAAMC,EAAqBxC,EAAAA,YAAiB,CAACyC,EAAOC,KAClD,IAAIC,EAAIC,EACR,MAAM,aACJC,EAAY,MACZC,EACArC,UAAWsC,GACT/C,EAAAA,WAAiBgD,EAAAA,KACf,KACFC,GAAkB,OAAVH,QAA4B,IAAVA,OAAmB,EAASA,EAAMG,OAAS,QAAO,MAC5EC,EAAK,UACL1C,EAAS,cACT2C,EAAa,SACbvC,EAAQ,UACRH,EAAY,aACZ2C,UAAWC,EAAkB,MAC7BxC,EAAK,MACLE,EAAK,KACLD,GAAO,EACPwC,WAAYC,EAAgB,OAC5BC,GACEf,EACJgB,EAAalC,EAAOkB,EAAO,CAAC,OAAQ,QAAS,YAAa,gBAAiB,WAAY,YAAa,YAAa,QAAS,QAAS,OAAQ,aAAc,WACrJrC,GAAiBsD,EAAAA,EAAAA,MAChBxD,EAAgBC,GAAgBH,EAAAA,SAAc,KAAO2D,MAAMC,QAAQX,GAAQA,EAAO,CAACA,EAAMA,IAAOY,KAAIC,GA1B7G,SAAuBb,GACrB,MAAuB,kBAATA,EAAoBb,EAAUa,GAAQA,GAAQ,CAC9D,CAwBqHc,CAAcD,MAAQ,CAACb,IACpIe,GAAaC,EAAAA,EAAAA,GAAQrD,EAAU,CACnCsD,WAAW,IAEPC,OAAwB9C,IAAV6B,GAAqC,eAAdzC,EAA6B,SAAWyC,EAC7EE,EAAYP,EAAa,QAASQ,IACjCe,EAASC,IAAUC,EAAAA,EAAAA,GAASlB,GAC7BmB,EAAKjB,IAAWF,EAAqB,OAAVN,QAA4B,IAAVA,OAAmB,EAASA,EAAMtC,UAAW6D,EAAQ,GAAF/C,OAAK8B,EAAS,KAAA9B,OAAIb,GAAa,CACnI,CAAC,GAADa,OAAI8B,EAAS,SAA6B,QAApBL,EACtB,CAAC,GAADzB,OAAI8B,EAAS,WAAA9B,OAAU6C,IAAgBA,GACtC3D,EAAW2C,GACRqB,EAAgBlB,IAAW,GAADhC,OAAI8B,EAAS,SAA8G,QAApGT,EAA0B,OAArBY,QAAkD,IAArBA,OAA8B,EAASA,EAAiBO,YAAyB,IAAPnB,EAAgBA,EAA+E,QAAzEC,EAAe,OAAVE,QAA4B,IAAVA,OAAmB,EAASA,EAAMQ,kBAA+B,IAAPV,OAAgB,EAASA,EAAGkB,MAChSnD,EAAsC,QAApBoC,EAA4B,aAAe,cAEnE,IAAI9C,EAAc,EAClB,MAAMwE,EAAQT,EAAWH,KAAI,CAACa,EAAOzC,KACnC,IAAIU,EAAIC,EACM,OAAV8B,QAA4BrD,IAAVqD,IACpBzE,EAAcgC,GAEhB,MAAM0C,EAAMD,GAASA,EAAMC,KAAO,GAAJrD,OAAOkD,EAAa,KAAAlD,OAAIW,GACtD,OAAoBjC,EAAAA,cAAoB4E,EAAM,CAC5CpE,UAAWgE,EACXG,IAAKA,EACLlE,UAAWA,EACXC,MAAOuB,EACPtB,gBAAiBA,EACjBE,MAAOA,EACPC,KAAMA,EACNC,MAA8E,QAAtE4B,EAAgB,OAAXa,QAA8B,IAAXA,OAAoB,EAASA,EAAOM,YAAyB,IAAPnB,EAAgBA,EAA2E,QAArEC,EAAe,OAAVE,QAA4B,IAAVA,OAAmB,EAASA,EAAMU,cAA2B,IAAPZ,OAAgB,EAASA,EAAGkB,MACpNY,EAAM,IAELG,EAAe7E,EAAAA,SAAc,KAAM,CACvCE,iBACAC,eACAF,cACAG,oBACE,CAACF,EAAgBC,EAAcF,EAAaG,IAEhD,GAA0B,IAAtB4D,EAAW9B,OACb,OAAO,KAET,MAAM4C,EAAW,CAAC,EAYlB,OAXIhE,IACFgE,EAASC,SAAW,OAEf3E,IACH0E,EAAS7D,cAAgBd,IAGzBC,IACF0E,EAASE,UAAY9E,EACrB4E,EAASG,OAAS9E,GAEbiE,EAAsBpE,EAAAA,cAAoB,MAAOkB,OAAOC,OAAO,CACpEuB,IAAKA,EACLlC,UAAW+D,EACXxD,MAAOG,OAAOC,OAAOD,OAAOC,OAAOD,OAAOC,OAAO,CAAC,EAAG2D,GAAqB,OAAVhC,QAA4B,IAAVA,OAAmB,EAASA,EAAM/B,OAAQA,IAC3H0C,GAA0BzD,EAAAA,cAAoBK,EAAsB,CACrE6E,MAAOL,GACNJ,IAAQ,IAKb,MAAMU,EAAkB3C,EACxB2C,EAAgBC,QAAUA,EAAAA,GAC1B,S,+CCrHO,MAAMC,EAAgBC,IAAS,CAGpCC,MAAOD,EAAME,UACbC,eAAgB,OAChBC,QAAS,OACTC,OAAQ,UACRC,WAAY,SAAFtE,OAAWgE,EAAMO,oBAC3B,mBAAoB,CAClBN,MAAOD,EAAMQ,gBAEf,WAAY,CACVP,MAAOD,EAAMS,kB,wFCXjB,QADmB,CAAE,KAAQ,CAAE,IAAO,MAAO,MAAS,CAAE,QAAW,gBAAiB,UAAa,SAAW,SAAY,CAAC,CAAE,IAAO,OAAQ,MAAS,CAAE,EAAK,8XAAkY,KAAQ,OAAQ,MAAS,Y,cCMjjBC,EAAe,SAAsBvD,EAAOC,GAC9C,OAAoB1C,EAAAA,cAAoBiG,EAAAA,GAAUC,EAAAA,EAAAA,GAAS,CAAC,EAAGzD,EAAO,CACpEC,IAAKA,EACLyD,KAAMC,IAEV,EAIA,QAA4BpG,EAAAA,WAAiBgG,GCd7C,QADmB,CAAE,KAAQ,CAAE,IAAO,MAAO,MAAS,CAAE,QAAW,gBAAiB,UAAa,SAAW,SAAY,CAAC,CAAE,IAAO,OAAQ,MAAS,CAAE,EAAK,uZAA2Z,KAAQ,OAAQ,MAAS,YCM9kB,IAAIK,EAAe,SAAsB5D,EAAOC,GAC9C,OAAoB1C,EAAAA,cAAoBiG,EAAAA,GAAUC,EAAAA,EAAAA,GAAS,CAAC,EAAGzD,EAAO,CACpEC,IAAKA,EACLyD,KAAMG,IAEV,EAIA,QAA4BtG,EAAAA,WAAiBqG,G,0HChBzC9E,EAAgC,SAAUC,EAAGC,GAC/C,IAAIC,EAAI,CAAC,EACT,IAAK,IAAIC,KAAKH,EAAON,OAAOU,UAAUC,eAAeC,KAAKN,EAAGG,IAAMF,EAAEM,QAAQJ,GAAK,IAAGD,EAAEC,GAAKH,EAAEG,IAC9F,GAAS,MAALH,GAAqD,oBAAjCN,OAAOc,sBAA2C,KAAIC,EAAI,EAAb,IAAgBN,EAAIT,OAAOc,sBAAsBR,GAAIS,EAAIN,EAAEO,OAAQD,IAClIR,EAAEM,QAAQJ,EAAEM,IAAM,GAAKf,OAAOU,UAAUO,qBAAqBL,KAAKN,EAAGG,EAAEM,MAAKP,EAAEC,EAAEM,IAAMT,EAAEG,EAAEM,IADuB,CAGvH,OAAOP,CACT,EAQA,MAAM6E,EAAc,CAClBC,OAAQ,EACRC,WAAY,cACZC,QAAS,EACTC,WAAY,UACZC,QAAS,gBAELC,EAA2B7G,EAAAA,YAAiB,CAACyC,EAAOC,KACxD,MAmBM,MACF3B,EAAK,QACL+F,EAAO,SACPC,GACEtE,EACJuE,EAAYzF,EAAOkB,EAAO,CAAC,QAAS,UAAW,aACjD,IAAIwE,EAAc,CAAC,EAQnB,OAPKH,IACHG,EAAc/F,OAAOC,OAAO,CAAC,EAAGoF,IAE9BQ,IACFE,EAAYC,cAAgB,QAE9BD,EAAc/F,OAAOC,OAAOD,OAAOC,OAAO,CAAC,EAAG8F,GAAclG,GACxCf,EAAAA,cAAoB,MAAOkB,OAAOC,OAAO,CAC3DgG,KAAM,SACNC,SAAU,EACV1E,IAAKA,GACJsE,EAAW,CACZK,UAtCgBC,IAChB,MAAM,QACJC,GACED,EACAC,IAAYC,EAAAA,EAAQC,OACtBH,EAAMI,gBACR,EAiCAC,QA/BcL,IACd,MAAM,QACJC,GACED,GACE,QACJM,GACEnF,EACA8E,IAAYC,EAAAA,EAAQC,OAASG,GAC/BA,GACF,EAuBA7G,MAAOkG,IACN,IAEL,I,iCChEA,QADoB,CAAE,KAAQ,CAAE,IAAO,MAAO,MAAS,CAAE,QAAW,gBAAiB,UAAa,SAAW,SAAY,CAAC,CAAE,IAAO,OAAQ,MAAS,CAAE,EAAK,iLAAqL,KAAQ,QAAS,MAAS,YCM1W,IAAIY,EAAgB,SAAuBpF,EAAOC,GAChD,OAAoB1C,EAAAA,cAAoBiG,EAAAA,GAAUC,EAAAA,EAAAA,GAAS,CAAC,EAAGzD,EAAO,CACpEC,IAAKA,EACLyD,KAAM2B,IAEV,EAIA,QAA4B9H,EAAAA,WAAiB6H,G,gECH7C,MAcaE,EAAiBzC,IAC5B,MACM9B,EAAS,CAAC,EAShB,MAViB,CAAC,EAAG,EAAG,EAAG,EAAG,GAErBwE,SAAQC,IACfzE,EAAO,YAADlC,OACD2G,EAAY,oBAAA3G,OACP2G,EAAY,mBAAA3G,OACZ2G,EAAY,yBAAA3G,OACjB2G,EAAY,WAtBCC,EAACC,EAAUxB,EAAYpB,EAAOD,KAClD,MAAM,kBACJ8C,EAAiB,iBACjBC,GACE/C,EACJ,MAAO,CACLrE,aAAcmH,EACd7C,QACA+C,WAAYD,EACZF,WACAxB,aACD,EAYMuB,CAAc5C,EAAM,kBAADhE,OAAmB2G,IAAiB3C,EAAM,oBAADhE,OAAqB2G,IAAiB3C,EAAMiD,iBAAkBjD,EAAM,IAEhI9B,CAAM,EAEFgF,EAAgBlD,IAC3B,MAAM,aACJmD,GACEnD,EACJ,MAAO,CACL,QAASpE,OAAOC,OAAOD,OAAOC,OAAO,CAAC,GAAGkE,EAAAA,EAAAA,GAAcC,IAAS,CAC9DG,eAAgBH,EAAMoD,eACtB,oBAAqB,CACnBjD,eAAgBH,EAAMqD,qBAExB,CAAC,iBAADrH,OAAkBmH,EAAY,cAAc,CAC1ClD,MAAOD,EAAMsD,kBACbjD,OAAQ,cACR,oBAAqB,CACnBJ,MAAOD,EAAMsD,mBAEf,WAAY,CACV1B,cAAe,WAItB,EAEU2B,EAAiBvD,IAAS,CACrCwD,KAAM,CACJC,OAAQ,UACRC,cAAe,QACfC,aAAc,cACdd,SAAU,MACVe,WAAY5D,EAAM6D,eAClB1C,WAAY,2BACZD,OAAQ,qCACR4C,aAAc,GAEhBC,IAAK,CACHN,OAAQ,UACRC,cAAe,QACfC,aAAc,eACdd,SAAU,MACVe,WAAY5D,EAAM6D,eAClB1C,WAAY,4BACZD,OAAQ,qCACR8C,kBAAmB,EACnBF,aAAc,GAEhBG,KAAM,CACJ7C,QAAS,EAET8C,gBAAiBC,EAAAA,GAAK,IAExB,SAAU,CACRhE,eAAgB,YAChBiE,sBAAuB,QAEzB,SAAU,CACRjE,eAAgB,gBAElBkE,OAAQ,CACNrB,WAAY,KAGd,SAAU,CACRsB,aAAc,EACdC,YAAa,QACbnD,QAAS,EACToD,GAAI,CACFF,aAAc,SACdC,YAAa,EACbb,cAAe,QACfC,aAAc,IAGlBc,GAAI,CACFC,cAAe,SACfD,GAAI,CACFC,cAAe,SAGnBC,GAAI,CACFD,cAAe,WAGjB,kBAAmB,CACjBjB,OAAQ,SAEVmB,IAAK,CACHxD,QAAS,cACTyD,WAAY,WACZC,SAAU,aACV3D,WAAY,2BACZD,OAAQ,qCACR4C,aAAc,EACdF,WAAY5D,EAAM6D,eAElBL,KAAM,CACJlC,QAAS,SACTmC,OAAQ,EACRrC,QAAS,EACTyB,SAAU,UACVe,WAAY,UACZzC,WAAY,cACZD,OAAQ,IAGZ6D,WAAY,CACVrB,cAAe,UACfC,aAAc,EACdqB,kBAAmB,qCACnBC,QAAS,OAGAC,EAAoBlF,IAC/B,MAAM,aACJmD,GACEnD,EAEEmF,GADaC,EAAAA,EAAAA,IAAepF,GACJqF,qBAAuB,EACrD,MAAO,CACL,iBAAkB,CAChBC,SAAU,WACV,OAAQ,CACNC,kBAAmBvF,EAAMwF,UACzBC,WAAYN,EACZxJ,aAAc,cAAFK,OAAgBmJ,EAAU,QAExC,CAAC,GAADnJ,OAAImH,EAAY,0BAA0B,CACxCmC,SAAU,WACVI,eAAgB1F,EAAM2F,SAAW,EACjCC,cAAe5F,EAAM2F,SACrB1F,MAAOD,EAAM6F,qBAEb7C,WAAY,SACZH,SAAU7C,EAAM6C,SAChBiD,UAAW,SACXlE,cAAe,QAEjBmE,SAAU,CACRtC,OAAQ,cAERuC,cAAe,OACfC,OAAQ,QAGb,EAEUC,EAAoBlG,IAAS,CACxC,iBAAkB,CAChB,sCAGW,CACTC,MAAOD,EAAMmG,iBC5LbC,EAAqBpG,IACzB,MAAM,aACJmD,EAAY,eACZkD,GACErG,EACJ,MAAO,CACL,CAACmD,GAAevH,OAAOC,OAAOD,OAAOC,OAAOD,OAAOC,OAAOD,OAAOC,OAAOD,OAAOC,OAAOD,OAAOC,OAAOD,OAAOC,OAAOD,OAAOC,OAAOD,OAAOC,OAAO,CAC5IoE,MAAOD,EAAMsG,UACbC,UAAW,aACXlF,WAAYrB,EAAMqB,WAClB,CAAC,IAADrF,OAAKmH,EAAY,eAAe,CAC9BlD,MAAOD,EAAM6F,sBAEf,CAAC,IAAD7J,OAAKmH,EAAY,aAAa,CAC5BlD,MAAOD,EAAMmG,cAEf,CAAC,IAADnK,OAAKmH,EAAY,aAAa,CAC5BlD,MAAOD,EAAMwG,cAEf,CAAC,IAADxK,OAAKmH,EAAY,YAAY,CAC3BlD,MAAOD,EAAMyG,WACb,sBAAuB,CACrBxG,MAAOD,EAAM0G,kBAEf,WAAY,CACVzG,MAAOD,EAAM2G,kBAGjB,CAAC,IAAD3K,OAAKmH,EAAY,cAAc,CAC7BlD,MAAOD,EAAMsD,kBACbjD,OAAQ,cACRuG,WAAY,QAEd,qCAGI,CACFjL,aAAc,QAEf8G,EAAezC,IAAS,CACzB,CAAC,iBAADhE,OACQmH,EAAY,mBAAAnH,OACZmH,EAAY,mBAAAnH,OACZmH,EAAY,mBAAAnH,OACZmH,EAAY,mBAAAnH,OACZmH,EAAY,aAChB,CACFsC,UAAWY,GAEb,qGASM,CACJ,uFAMI,CACFZ,UAAWY,MAGb9C,EAAevD,IAASkD,EAAclD,IAAS,CAEjD,CAAC,aAADhE,OACImH,EAAY,sBAAAnH,OACZmH,EAAY,oBAAAnH,OACZmH,EAAY,kBACZvH,OAAOC,OAAOD,OAAOC,OAAO,CAAC,GAAGkE,EAAAA,EAAAA,GAAcC,IAAS,CACzD6G,kBAAmB7G,EAAM8G,cAEzB5B,EAAkBlF,IAASkG,EAAkBlG,IDkHpB,CAC/B,yCAGI,CACFsB,QAAS,eACTyF,SAAU,QAEZ,gBAAiB,CACflC,WAAY,UAEd,yBAA0B,CACxBmC,SAAU,SACVC,aAAc,WAEd,YAAa,CACXC,cAAe,WAGnB,2BAA4B,CAC1B5F,QAAS,cACT0F,SAAU,SACVG,gBAAiB,EACjBC,gBAAiB,cCzI+D,CAC9E,QAAS,CACPjM,UAAW,SAGhB,EAGH,GAAekM,EAAAA,EAAAA,GAAsB,cAAcrH,GAAS,CAACoG,EAAmBpG,MAAS,KAAM,CAC7FqG,eAAgB,QAChBvD,kBAAmB,YCoBrB,EAxGiB3F,IACf,MAAM,UACJW,EACA,aAAcwJ,EAAS,UACvBpM,EAAS,MACTO,EAAK,UACLN,EAAS,UACToM,EAAS,SACTC,GAAW,EAAI,MACf5H,EAAK,OACL6H,EAAM,SACNC,EAAQ,MACRC,EAAK,UACLC,EAAS,UACTC,EAAyBnN,EAAAA,cAAoB6H,EAAe,OAC1DpF,EACEC,EAAM1C,EAAAA,OAAa,MACnBoN,EAAgBpN,EAAAA,QAAa,GAC7BqN,EAAcrN,EAAAA,UACbsN,EAASC,GAAcvN,EAAAA,SAAekF,GAC7ClF,EAAAA,WAAgB,KACduN,EAAWrI,EAAM,GAChB,CAACA,IACJlF,EAAAA,WAAgB,KACd,GAAI0C,EAAI4K,SAAW5K,EAAI4K,QAAQE,kBAAmB,CAChD,MAAM,SACJC,GACE/K,EAAI4K,QAAQE,kBAChBC,EAASC,QACT,MAAM,OACJxL,GACEuL,EAASvI,MACbuI,EAASE,kBAAkBzL,EAAQA,EACrC,IACC,IACH,MAoBM0L,EAAgBA,KACpBb,EAAOO,EAAQO,OAAO,EAuBlBC,EAAgBZ,EAAY,GAAH5L,OAAM8B,EAAS,KAAA9B,OAAI4L,GAAc,IACzD9I,EAASC,GAAUC,EAASlB,GAC7B2K,EAAoBzK,IAAWF,EAAW,GAAF9B,OAAK8B,EAAS,iBAAiB,CAC3E,CAAC,GAAD9B,OAAI8B,EAAS,SAAuB,QAAd3C,GACrBD,EAAWsN,EAAezJ,GAC7B,OAAOD,EAAsBpE,EAAAA,cAAoB,MAAO,CACtDQ,UAAWuN,EACXhN,MAAOA,GACOf,EAAAA,cAAoBgO,EAAAA,EAAU,CAC5CtL,IAAKA,EACLmK,UAAWA,EACX3H,MAAOoI,EACPW,SAxDe1N,IACf,IAAI,OACF2N,GACE3N,EACJgN,EAAWW,EAAOhJ,MAAMiJ,QAAQ,UAAW,IAAI,EAqD/C9G,UA7CgB+G,IAChB,IAAI,QACF7G,GACE6G,EAEAhB,EAAcE,UAClBD,EAAYC,QAAU/F,EAAO,EAwC7BI,QAnCc0G,IACd,IAAI,QACF9G,EAAO,QACP+G,EAAO,OACPC,EAAM,QACNC,EAAO,SACPC,GACEJ,EAEAhB,EAAYC,UAAY/F,GAAY6F,EAAcE,SAAYgB,GAAYC,GAAWC,GAAYC,IAC/FlH,IAAYC,EAAAA,EAAQC,OACtBmG,IACU,OAAVX,QAA4B,IAAVA,GAA4BA,KACrC1F,IAAYC,EAAAA,EAAQkH,KAC7B1B,IAEJ,EAoBA2B,mBArDyBA,KACzBvB,EAAcE,SAAU,CAAI,EAqD5BsB,iBAnDuBA,KACvBxB,EAAcE,SAAU,CAAK,EAmD7BuB,OApBaA,KACbjB,GAAe,EAoBf,aAAchB,EACdkC,KAAM,EACNhC,SAAUA,IACM,OAAdK,GAAqB4B,EAAAA,EAAAA,IAAa5B,EAAW,CAC/C3M,UAAW,GAAFc,OAAK8B,EAAS,2BACpB,MAAM,EC7Gb,IAAI7B,EAAgC,SAAUC,EAAGC,GAC/C,IAAIC,EAAI,CAAC,EACT,IAAK,IAAIC,KAAKH,EAAON,OAAOU,UAAUC,eAAeC,KAAKN,EAAGG,IAAMF,EAAEM,QAAQJ,GAAK,IAAGD,EAAEC,GAAKH,EAAEG,IAC9F,GAAS,MAALH,GAAqD,oBAAjCN,OAAOc,sBAA2C,KAAIC,EAAI,EAAb,IAAgBN,EAAIT,OAAOc,sBAAsBR,GAAIS,EAAIN,EAAEO,OAAQD,IAClIR,EAAEM,QAAQJ,EAAEM,IAAM,GAAKf,OAAOU,UAAUO,qBAAqBL,KAAKN,EAAGG,EAAEM,MAAKP,EAAEC,EAAEM,IAAMT,EAAEG,EAAEM,IADuB,CAGvH,OAAOP,CACT,EAOA,MAAMsN,EAA0BhP,EAAAA,YAAiB,CAACyC,EAAOC,KACvD,MACIU,UAAWC,EACX6J,UAAW+B,EAAY,UAAS,UAChCzO,EAAS,cACT2C,EAAa,cACb+L,EAAa,SACbtO,EACAH,UAAW0O,EAAmB,MAC9BpO,GACE0B,EACJuE,EAAYzF,EAAOkB,EAAO,CAAC,YAAa,YAAa,YAAa,gBAAiB,gBAAiB,WAAY,YAAa,WACzH,aACJI,EACApC,UAAW2O,EAAgB,WAC3BC,GACErP,EAAAA,WAAiBgD,EAAAA,IACfvC,EAAoC,OAAxB0O,QAAwD,IAAxBA,EAAiCA,EAAsBC,EACzG,IAAIE,EAAY5M,EACZwM,IAEFI,GAAYC,EAAAA,EAAAA,IAAW7M,EAAKwM,IAE9B,MAAM9L,EAAYP,EAAa,aAAcQ,IAEtCe,EAASC,GAAUC,EAASlB,GAC7BoM,EAAqBlM,IAAWF,EAA0B,OAAfiM,QAAsC,IAAfA,OAAwB,EAASA,EAAW7O,UAAW,CAC7H,CAAC,GAADc,OAAI8B,EAAS,SAAuB,QAAd3C,GACrBD,EAAW2C,EAAekB,GACvB4C,EAAc/F,OAAOC,OAAOD,OAAOC,OAAO,CAAC,EAAkB,OAAfkO,QAAsC,IAAfA,OAAwB,EAASA,EAAWtO,OAAQA,GAC/H,OAAOqD,EAGPpE,EAAAA,cAAoBiP,EAAW/N,OAAOC,OAAO,CAC3CX,UAAWgP,EACXzO,MAAOkG,EACPvE,IAAK4M,GACJtI,GAAYpG,GAAU,IAM3B,UCxDe,SAAS6O,EAAgBC,EAAYC,GAClD,OAAO3P,EAAAA,SAAc,KACnB,MAAM4P,IAAYF,EAClB,MAAO,CAACE,EAAS1O,OAAOC,OAAOD,OAAOC,OAAO,CAAC,EAAGwO,GAAiBC,GAAiC,kBAAfF,EAA0BA,EAAa,MAAM,GAChI,CAACA,GACN,CCJA,MAUA,EAVyBG,CAACC,EAAUC,KAClC,MAAMC,EAAWhQ,EAAAA,QAAa,GAC9BA,EAAAA,WAAgB,KACVgQ,EAAS1C,QACXwC,IAEAE,EAAS1C,SAAU,CACrB,GACCyC,EAAW,ECPhB,SAASE,GAASC,GAChB,MAAMC,SAAcD,EACpB,MAAgB,WAATC,GAA8B,WAATA,CAC9B,CAYA,SAASC,GAAWC,EAAUC,GAC5B,IAAIC,EAAU,EACd,MAAMC,EAAkB,GACxB,IAAK,IAAIvO,EAAI,EAAGA,EAAIoO,EAASnO,OAAQD,GAAK,EAAG,CAE3C,GAAIsO,IAAYD,EACd,OAAOE,EAET,MAAMN,EAAOG,EAASpO,GAGhBwO,EAAUF,GAFDN,GAASC,GACCQ,OAAOR,GAAMhO,OAAS,GAI/C,GAAIuO,EAAUH,EAAK,CACjB,MAAMK,EAAUL,EAAMC,EAEtB,OADAC,EAAgBI,KAAKF,OAAOR,GAAMW,MAAM,EAAGF,IACpCH,CACT,CACAA,EAAgBI,KAAKV,GACrBK,EAAUE,CACZ,CACA,OAAOJ,CACT,CA+GA,SAzGiB9P,IACf,IAAI,eACFuQ,EAAc,SACdlQ,EAAQ,KACRmQ,EAAI,MACJC,EAAK,SACL7I,EAAQ,KACR2G,EAAI,WACJmC,GACE1Q,EACJ,OAAQ2Q,EAAUC,EAAQC,GAASC,GAAgBrR,EAAAA,SAAe,CAAC,EAAG,EAAG,KAClEsR,EAAcC,GAAmBvR,EAAAA,SAhB7B,IAiBJwR,EAAiBC,GAAsBzR,EAAAA,SAAe,GACvD0R,EAAe1R,EAAAA,OAAa,MAC5B2R,EAAY3R,EAAAA,OAAa,MACzBqQ,EAAWrQ,EAAAA,SAAc,KAAMiE,EAAAA,EAAAA,GAAQ8M,IAAO,CAACA,IAC/Ca,EAAW5R,EAAAA,SAAc,IAxDjC,SAAqBqQ,GACnB,IAAIuB,EAAW,EAQf,OAPAvB,EAASrI,SAAQkI,IACXD,GAASC,GACX0B,GAAYlB,OAAOR,GAAMhO,OAEzB0P,GAAY,CACd,IAEKA,CACT,CA8CuCC,CAAYxB,IAAW,CAACA,IACvDyB,EAAiB9R,EAAAA,SAAc,IAC9B8Q,GApBkB,IAoBAQ,EAGhB1Q,EAASwP,GAAWC,EAAUc,GAASA,EAASS,GAF9ChR,EAASyP,GAAU,IAG3B,CAACS,EAAgBQ,EAAc1Q,EAAUyP,EAAUc,EAAQS,KAE9DG,EAAAA,EAAAA,IAA0B,KACpBjB,GAAkBE,GAAS7I,GAAYyJ,IACzCL,EA9BU,GA+BVF,EAAa,CAAC,EAAGW,KAAKC,KAAKL,EAAW,GAAIA,IAC5C,GACC,CAACd,EAAgBE,EAAO7I,EAAU4I,EAAMa,EAAU9C,KACrDiD,EAAAA,EAAAA,IAA0B,KACxB,IAAIpP,EAnCQ,IAoCR2O,GACFG,GAAoD,QAA/B9O,EAAK+O,EAAapE,eAA4B,IAAP3K,OAAgB,EAASA,EAAGuP,eAAiB,EAC3G,GACC,CAACZ,KACJS,EAAAA,EAAAA,IAA0B,KACxB,IAAIpP,EAAIC,EACR,GAAI4O,EACF,GA3CU,IA2CNF,EAA0B,GAEoB,QAA5B3O,EAAKgP,EAAUrE,eAA4B,IAAP3K,OAAgB,EAASA,EAAGuP,eAAiB,IACnFpD,EAAO0C,GAEvBD,EA7CoB,GA8CpBN,GAAW,IAEXM,EAlDM,EAoDV,MAAO,GApDG,IAoDCD,EACT,GAAIJ,IAAaE,EAAQ,CACvB,MAAMe,GAA0C,QAA5BvP,EAAK+O,EAAUrE,eAA4B,IAAP1K,OAAgB,EAASA,EAAGsP,eAAiB,EAErG,IAAIE,EAAelB,EACfmB,EAAajB,EAEbF,IAAaE,EAAS,EACxBiB,EAAanB,EACJiB,GANOrD,EAAO0C,EAOvBY,EAAejB,EAEfkB,EAAalB,EAEf,MAAMmB,EAAaN,KAAKC,MAAMG,EAAeC,GAAc,GAC3DhB,EAAa,CAACe,EAAcE,EAAYD,GAC1C,MACEd,EApEiB,GAqEjBN,GAAW,EAGjB,GACC,CAACK,EAAcJ,EAAUE,EAAQtC,EAAM0C,IAE1C,MAAMe,EAAe,CACnBvB,QACA7G,WAAY,SACZpB,OAAQ,EACRrC,QAAS,GAEL8L,EAAgBA,CAACC,EAAS/P,EAAK3B,IAAuBf,EAAAA,cAAoB,OAAQ,CACtF,eAAe,EACf0C,IAAKA,EACL3B,MAAOG,OAAOC,OAAO,CACnByJ,SAAU,QACVhE,QAAS,QACT8L,KAAM,EACNC,IAAK,EACLC,QAAS,KACTC,WAAY,SACZ3L,cAAe,OACfiB,SAAqC,EAA3B6J,KAAKc,MAAM3K,EAAW,IAC/BpH,IACF0R,GAKH,OAAoBzS,EAAAA,cAAoBA,EAAAA,SAAgB,KAAM8R,EAAgBhB,GAnGrD,IAmGuEQ,GAlGpE,IAkG2GA,GAAuDtR,EAAAA,cAAoBA,EAAAA,SAAgB,KAAMwS,EAAc,KAAMd,EAAc,CACxQ7F,UAAW,WACX1B,WAAY,WAvGA,IAwGVmH,EAA2BkB,EAAc5R,EAASyP,GAAU,GAAQsB,EAAWY,GAPxDQ,EAACzC,EAAK5N,KAC/B,MAAMsQ,EAAgB5C,GAAWC,EAAUC,GAC3C,OAAOkC,EAAc5R,EAASoS,GAAe,GAAOtQ,EAAK6P,EAAa,EAK2BQ,CAAmB5B,EAAQQ,IAAY,EChI5I,SAjBwBpR,IACtB,IAAI,gBACF0S,EAAe,WACfC,EAAU,SACVtS,EAAQ,aACRuS,GACE5S,EACJ,OAAuB,OAAjB4S,QAA0C,IAAjBA,OAA0B,EAASA,EAAaC,QAAWH,EAGtEjT,EAAAA,cAAoBqT,EAAAA,EAASnS,OAAOC,OAAO,CAC7DmS,OAAMJ,QAAa7R,GAClB8R,GAAevS,GAJTA,CAIkB,ECd7B,IAAIW,GAAgC,SAAUC,EAAGC,GAC/C,IAAIC,EAAI,CAAC,EACT,IAAK,IAAIC,KAAKH,EAAON,OAAOU,UAAUC,eAAeC,KAAKN,EAAGG,IAAMF,EAAEM,QAAQJ,GAAK,IAAGD,EAAEC,GAAKH,EAAEG,IAC9F,GAAS,MAALH,GAAqD,oBAAjCN,OAAOc,sBAA2C,KAAIC,EAAI,EAAb,IAAgBN,EAAIT,OAAOc,sBAAsBR,GAAIS,EAAIN,EAAEO,OAAQD,IAClIR,EAAEM,QAAQJ,EAAEM,IAAM,GAAKf,OAAOU,UAAUO,qBAAqBL,KAAKN,EAAGG,EAAEM,MAAKP,EAAEC,EAAEM,IAAMT,EAAEG,EAAEM,IADuB,CAGvH,OAAOP,CACT,EAkDA,SAAS6R,GAAQC,EAAKC,EAAaC,GACjC,OAAY,IAARF,QAAwBnS,IAARmS,EACXC,EAEFD,GAAOE,GAAWD,CAC3B,CACA,SAASE,GAAOC,GACd,OAAY,IAARA,EACK,EAAC,GAAO,GAEVjQ,MAAMC,QAAQgQ,GAAOA,EAAM,CAACA,EACrC,CACA,MACMC,GAAoB7T,EAAAA,YAAiB,CAACyC,EAAOC,KACjD,IAAIC,EAAIC,EAAIkR,EACZ,MACI1Q,UAAWC,EAAkB,UAC7B7C,EAAS,MACTO,EAAK,KACLoP,EAAI,SACJpJ,EAAQ,SACRnG,EAAQ,SACRmT,EAAQ,SACRC,EAAQ,SACRC,EAAQ,UACR/G,EAAS,MACTkG,GACE3Q,EACJuE,EAAYzF,GAAOkB,EAAO,CAAC,YAAa,YAAa,QAAS,OAAQ,WAAY,WAAY,WAAY,WAAY,WAAY,YAAa,WAC3I,aACJI,EAAY,UACZpC,GACET,EAAAA,WAAiBgD,EAAAA,KACdkR,IAAcC,EAAAA,EAAAA,GAAU,QACzBC,EAAgBpU,EAAAA,OAAa,MAC7BqU,EAAcrU,EAAAA,OAAa,MAE3BoD,EAAYP,EAAa,aAAcQ,GACvCiR,GAAYC,EAAAA,EAAAA,GAAKvN,EAAW,CAAC,OAAQ,OAAQ,SAAU,YAAa,SAAU,WAAY,YAEzFwN,EAAYC,GAAchF,EAAgBuE,IAC1CU,EAASC,IAAcC,EAAAA,EAAAA,IAAe,EAAO,CAClD1P,MAAOuP,EAAWC,WAEd,YACJG,EAAc,CAAC,SACbJ,EACEK,EAAcC,IAClB,IAAIpS,EACAoS,IAC4B,QAA7BpS,EAAK8R,EAAWO,eAA4B,IAAPrS,GAAyBA,EAAGb,KAAK2S,IAEzEE,EAAWI,EAAK,EAGlBlF,GAAiB,KACf,IAAIlN,EACC+R,GAC4B,QAA9B/R,EAAK0R,EAAY/G,eAA4B,IAAP3K,GAAyBA,EAAG+K,OACrE,GACC,CAACgH,IACJ,MAAMO,EAAcxT,IACZ,OAANA,QAAoB,IAANA,GAAwBA,EAAEiG,iBACxCoN,GAAY,EAAK,EAEbI,EAAehQ,IACnB,IAAIvC,EAC2B,QAA9BA,EAAK8R,EAAWxG,gBAA6B,IAAPtL,GAAyBA,EAAGb,KAAK2S,EAAYvP,GACpF4P,GAAY,EAAM,EAEdK,EAAeA,KACnB,IAAIxS,EAC2B,QAA9BA,EAAK8R,EAAWzH,gBAA6B,IAAPrK,GAAyBA,EAAGb,KAAK2S,GACxEK,GAAY,EAAM,GAGbM,GAAYC,IAAc5F,EAAgBwE,IAC1CqB,GAAQC,IAAavV,EAAAA,UAAe,GACrCwV,GAAYxV,EAAAA,OAAa,MACzByV,GAAc,CAAC,EACjBJ,GAAWK,SACbD,GAAYC,OAASL,GAAWK,QAElC,MAAMC,GAAcA,KACdH,GAAUlI,SACZsI,aAAaJ,GAAUlI,QACzB,EAEIuI,GAAcpU,IAClB,IAAIkB,EACE,OAANlB,QAAoB,IAANA,GAAwBA,EAAEiG,iBAClC,OAANjG,QAAoB,IAANA,GAAwBA,EAAEqU,kBACxCC,IAAKV,GAAWtE,MAAQL,OAAO9P,IAAa,GAAI6U,IAChDF,IAAU,GAEVI,KACAH,GAAUlI,QAAU0I,YAAW,KAC7BT,IAAU,EAAM,GACf,KAC0B,QAA5B5S,EAAK0S,GAAWY,cAA2B,IAAPtT,GAAyBA,EAAGb,KAAKuT,GAAY5T,EAAE,EAEtFzB,EAAAA,WAAgB,IAAM2V,IAAa,IAEnC,MAAOO,GAAoBC,IAAyBnW,EAAAA,UAAe,IAC5DoW,GAAuBC,IAA4BrW,EAAAA,UAAe,IAClEsW,GAAUC,IAAevW,EAAAA,UAAe,IACxCwW,GAAcC,IAAmBzW,EAAAA,UAAe,IAChD0W,GAAkBC,IAAuB3W,EAAAA,UAAe,IACxD4W,GAAiBC,IAAsB7W,EAAAA,UAAe,IACtD8W,GAAgBC,IAAkBtH,EAAgBsE,EAAU,CACjEiD,YAAY,IAERC,GAAuBH,KAAmBR,IAE1C,KACJxH,GAAO,GACLiI,GACEG,GAAsBlX,EAAAA,SAAc,KAEzCiX,SAEyB5V,IAA1B0V,GAAeI,QAAwBJ,GAAe9F,YAEtD8F,GAAeC,YAAcxC,GAAcY,IAAY,CAAC6B,GAAsBF,GAAgBvC,EAAYY,MAC1GrD,EAAAA,EAAAA,IAA0B,KACpB+E,KAAmBI,KACrBf,IAAsBiB,EAAAA,EAAAA,GAAe,oBACrCf,IAAyBe,EAAAA,EAAAA,GAAe,iBAC1C,GACC,CAACF,GAAqBJ,KACzB,MAAMO,GAAcrX,EAAAA,SAAc,KAC5BkX,KAGS,IAATpI,GACKsH,GAEFF,KACN,CAACgB,GAAqBd,GAAuBF,KAC1CoB,GAAmBL,KAAyBI,GAAcX,GAAmBF,IAC7Ee,GAAkBN,IAAiC,IAATnI,IAAcuI,GACxDG,GAAeP,IAAwBnI,GAAO,GAAKuI,GAEnDI,GAAgBhW,IACpB,IAAIkB,EACJ4T,IAAY,GACuB,QAAlC5T,EAAKoU,GAAeW,gBAA6B,IAAP/U,GAAyBA,EAAGb,KAAKiV,GAAgBtV,EAAE,GAEzFkW,GAAeC,IAAoB5X,EAAAA,SAAe,IAClD6X,GAAkBC,IAAuB9X,EAAAA,SAAe,GAUzD+X,GAAeC,IACnB,IAAIrV,EACJ8T,GAAgBuB,GAEZxB,KAAiBwB,IACkB,QAApCrV,EAAKoU,GAAe9F,kBAA+B,IAAPtO,GAAyBA,EAAGb,KAAKiV,GAAgBiB,GAChG,EAGFhY,EAAAA,WAAgB,KACd,MAAMiY,EAAU7D,EAAc9G,QAC9B,GAAIwJ,IAAkBO,IAAeY,EAAS,CAC5C,MAAMC,EAAkBV,GAAeS,EAAQ/F,aAAe+F,EAAQE,aAAeF,EAAQG,YAAcH,EAAQI,YAC/G3B,KAAqBwB,GACvBvB,GAAoBuB,EAExB,IACC,CAACpB,GAAgBO,GAAazW,EAAU4W,GAAcZ,KAGzD5W,EAAAA,WAAgB,KACd,MAAMiY,EAAU7D,EAAc9G,QAC9B,GAAoC,qBAAzBgL,uBAAyCL,IAAYZ,KAAgBJ,GAC9E,OAGF,MAAMsB,EAAW,IAAID,sBAAqB,KACxCzB,KAAqBoB,EAAQO,aAAa,IAG5C,OADAD,EAASE,QAAQR,GACV,KACLM,EAASG,YAAY,CACtB,GACA,CAACrB,GAAaJ,KAEjB,IAAI9D,GAAe,CAAC,EAElBA,IAD6B,IAA3B4D,GAAe4B,QACF,CACbvF,MAAkC,QAA1BzQ,EAAK8R,EAAW1D,YAAyB,IAAPpO,EAAgBA,EAAK/B,GAE1CZ,EAAAA,eAAqB+W,GAAe4B,SAC5C,CACbvF,MAAO2D,GAAe4B,SAEmB,kBAA3B5B,GAAe4B,QAChBzX,OAAOC,OAAO,CAC3BiS,MAAkC,QAA1BxQ,EAAK6R,EAAW1D,YAAyB,IAAPnO,EAAgBA,EAAKhC,GAC9DmW,GAAe4B,SAEH,CACbvF,MAAO2D,GAAe4B,SAG1B,MAAMC,GAAe5Y,EAAAA,SAAc,KACjC,MAAM6Y,EAAUjF,GAAO,CAAC,SAAU,UAAUkF,gBAAgBlF,GAC5D,GAAKkD,KAAkBO,GAGvB,OAAIwB,EAAQpE,EAAW1D,MACd0D,EAAW1D,KAEhB8H,EAAQjY,GACHA,EAELiY,EAAQzF,GACHA,EAELyF,EAAQ1F,GAAaC,OAChBD,GAAaC,WADtB,CAGgB,GACf,CAAC0D,GAAgBO,GAAajE,EAAOD,GAAaC,MAAOkE,KAG5D,GAAI5C,EACF,OAAoB1U,EAAAA,cAAoB+Y,EAAU,CAChD7T,MAAkC,QAA1B4O,EAAKW,EAAW1D,YAAyB,IAAP+C,EAAgBA,EAAyB,kBAAblT,EAAwBA,EAAW,GACzGmM,OAAQmI,EACRlI,SAAUmI,EACVlI,MAAOwH,EAAWxH,MAClB7J,UAAWA,EACX5C,UAAWA,EACXO,MAAOA,EACPN,UAAWA,EACXyM,UAAWA,EACXL,UAAW4H,EAAW5H,UACtBC,SAAU2H,EAAW3H,SACrBK,UAAWsH,EAAWtH,YAK1B,MAAM6L,GAAeA,KACnB,MAAM,WACJhC,EAAU,OACViC,GACElC,GACJ,IAAKC,EAAY,OAAO,KACxB,IAAIkC,EAMJ,OAJEA,EADED,IAG6B,OAAf/E,QAAsC,IAAfA,OAAwB,EAASA,EAAWiF,QAEjEnZ,EAAAA,cAAoB,IAAK,CAC3C2E,IAAK,SACLnE,UAAW,GAAFc,OAAK8B,EAAS,WACvBwE,QAAS6P,GACT,aAA6B,OAAfvD,QAAsC,IAAfA,OAAwB,EAASA,EAAWiF,QAChFD,EAAc,EAGbE,GAAaA,KACjB,IAAK5E,EAAY,OACjB,MAAM,KACJrO,EAAI,QACJwS,GACElE,EACE4E,GAAYpV,EAAAA,EAAAA,GAAQ0U,GAAS,KAAsB,OAAfzE,QAAsC,IAAfA,OAAwB,EAASA,EAAWa,MACvGnI,EAAiC,kBAAdyM,EAAyBA,EAAY,GAC9D,OAAOxE,EAAYiE,SAAS,QAAuB9Y,EAAAA,cAAoBqT,EAAAA,EAAS,CAC9E1O,IAAK,OACLyO,OAAmB,IAAZuF,EAAoB,GAAKU,GAClBrZ,EAAAA,cAAoB6G,EAAa,CAC/CnE,IAAK2R,EACL7T,UAAW,GAAFc,OAAK8B,EAAS,SACvBwE,QAASqN,EACT,aAAcrI,GACbzG,GAAqBnG,EAAAA,cAAoBqG,EAAc,CACxDc,KAAM,aACD,IAAI,EAGPmS,GAAaA,KACjB,IAAKlE,GAAY,OACjB,MAAM,SACJmE,EAAQ,KACRpT,GACEkP,GACEmE,EAAe7F,GAAO4F,GACtBE,EAAY9F,GAAOxN,GACnBuT,EAAYpE,GAAS/B,GAAQiG,EAAa,GAAmB,OAAftF,QAAsC,IAAfA,OAAwB,EAASA,EAAWoB,QAAU/B,GAAQiG,EAAa,GAAmB,OAAftF,QAAsC,IAAfA,OAAwB,EAASA,EAAW6B,MACvN4D,EAAYrE,GAAwB,OAAfpB,QAAsC,IAAfA,OAAwB,EAASA,EAAWoB,OAAwB,OAAfpB,QAAsC,IAAfA,OAAwB,EAASA,EAAW6B,KACpKnJ,EAAiC,kBAAd8M,EAAyBA,EAAYC,EAC9D,OAAoB3Z,EAAAA,cAAoBqT,EAAAA,EAAS,CAC/C1O,IAAK,OACLyO,MAAOsG,GACO1Z,EAAAA,cAAoB6G,EAAa,CAC/CrG,UAAW8C,IAAW,GAADhC,OAAI8B,EAAS,SAASkS,IAAU,GAAJhU,OAAO8B,EAAS,kBACjEwE,QAASiO,GACT,aAAcjJ,GACb0I,GAAS/B,GAAQkG,EAAU,GAAiBzZ,EAAAA,cAAoB4Z,EAAAA,EAAe,OAAO,GAAQrG,GAAQkG,EAAU,GAAiBzZ,EAAAA,cAAoBgG,EAAc,OAAO,IAAO,EAOtL,OAAoBhG,EAAAA,cAAoB6Z,EAAAA,EAAgB,CACtDC,SAxKeA,CAAC1L,EAAO2L,KACvB,IAAI,YACF3B,GACEhK,EACJ,IAAIzL,EACJiV,GAAiBQ,GACjBN,GAAoBkC,SAA4C,QAAlCrX,EAAKsX,OAAOC,wBAAqC,IAAPvX,OAAgB,EAASA,EAAGb,KAAKmY,OAAQF,GAAS5R,SAAU,KAAO,EAAE,EAmK7IpB,UAAWkQ,IAAwBI,KAClC8C,GAA0Bna,EAAAA,cAAoBoa,GAAiB,CAChEjH,aAAcA,GACdF,gBAAiBgE,GACjB/D,WAAYoE,IACEtX,EAAAA,cAAoBgP,EAAY9N,OAAOC,OAAO,CAC5DX,UAAW8C,IAAW,CACpB,CAAC,GAADhC,OAAI8B,EAAS,KAAA9B,OAAI6O,IAASA,EAC1B,CAAC,GAAD7O,OAAI8B,EAAS,cAAc2D,EAC3B,CAAC,GAADzF,OAAI8B,EAAS,cAAc0T,GAC3B,CAAC,GAADxV,OAAI8B,EAAS,iBAAiB6T,IAAiC,IAATnI,GACtD,CAAC,GAADxN,OAAI8B,EAAS,0BAA0BmU,GACvC,CAAC,GAADjW,OAAI8B,EAAS,4BAA4BoU,IACxChX,GACH4C,UAAWC,EACXtC,MAAOG,OAAOC,OAAOD,OAAOC,OAAO,CAAC,EAAGJ,GAAQ,CAC7C0L,gBAAiB+K,GAAe1I,QAAOzN,IAEzC6L,UAAWA,EACXxK,KAAK6M,EAAAA,EAAAA,IAAW4K,EAAW/F,EAAe1R,GAC1CjC,UAAWA,EACXmH,QAASiN,EAAYiE,SAAS,QAAU7D,OAAc5T,EACtD,aAA+B,OAAjBuX,SAA0C,IAAjBA,QAA0B,EAASA,GAAayB,WACvFjH,MAAOA,GACNkB,GAAyBtU,EAAAA,cAAoBsa,GAAU,CACxDxJ,eAAgBmG,KAAyBI,GACzCtG,KAAMnQ,EACNkO,KAAMA,GACNkC,MAAO2G,GACPxP,SAAU0P,GACV5G,WAAY8G,KACX,CAAC7H,EAAMqK,KACR,IAAIC,EAAatK,EACbA,EAAKhO,QAAUqY,GAAgB3B,KACjC4B,EAA0Bxa,EAAAA,cAAoB,OAAQ,CACpD2E,IAAK,eACL,eAAe,GACd6V,IAEL,MAAMC,EAhYV,SAA4Bla,EAAMkS,GAChC,IAAI,KACFlJ,EAAI,KACJT,EAAI,UACJ4R,EACAC,OAAQC,EAAG,OACXjR,EAAM,SACNkR,EAAQ,OACRC,GACEva,EACAwa,EAAiBtI,EACrB,SAAS3R,EAAKka,EAAKC,GACZA,IAGLF,EAA8B/a,EAAAA,cAAoBgb,EAAK,CAAC,EAAGD,GAC7D,CAQA,OAPAja,EAAK,SAAU6I,GACf7I,EAAK,IAAK4Z,GACV5Z,EAAK,MAAO8Z,GACZ9Z,EAAK,OAAQgI,GACbhI,EAAK,OAAQyI,GACbzI,EAAK,MAAO+Z,GACZ/Z,EAAK,IAAKga,GACHC,CACT,CAuW2BG,CAAmBzY,EAAoBzC,EAAAA,cAAoBA,EAAAA,SAAgB,KAAMwa,EA7CnFD,KAAgB,OAACA,GAA6Bva,EAAAA,cAAoB,OAAQ,CAC/F,eAAe,EACf2E,IAAK,YA/SY,OAgTDoS,GAAeI,QAJRgE,EAIiCZ,EAJf,CAACY,GAAkBnC,KAAgBI,KAAcE,QAAnE6B,KAI+C,EA0C8CC,CAAeb,KACnI,OAAOE,CAAc,OAClB,IAEP,MCnaA,IAAIlZ,GAAgC,SAAUC,EAAGC,GAC/C,IAAIC,EAAI,CAAC,EACT,IAAK,IAAIC,KAAKH,EAAON,OAAOU,UAAUC,eAAeC,KAAKN,EAAGG,IAAMF,EAAEM,QAAQJ,GAAK,IAAGD,EAAEC,GAAKH,EAAEG,IAC9F,GAAS,MAALH,GAAqD,oBAAjCN,OAAOc,sBAA2C,KAAIC,EAAI,EAAb,IAAgBN,EAAIT,OAAOc,sBAAsBR,GAAIS,EAAIN,EAAEO,OAAQD,IAClIR,EAAEM,QAAQJ,EAAEM,IAAM,GAAKf,OAAOU,UAAUO,qBAAqBL,KAAKN,EAAGG,EAAEM,MAAKP,EAAEC,EAAEM,IAAMT,EAAEG,EAAEM,IADuB,CAGvH,OAAOP,CACT,EAIA,MAkBA,GAlB0B1B,EAAAA,YAAiB,CAAC2C,EAAID,KAC9C,IAAI,SACAqR,EAAQ,IACRsH,GACE1Y,EACJqE,EAAYzF,GAAOoB,EAAI,CAAC,WAAY,QAEtC,MAAM2Y,EAAcpa,OAAOC,OAAOD,OAAOC,OAAO,CAAC,EAAG6F,GAAY,CAC9DqU,SAAaha,IAARga,GAA0C,WAArBrU,EAAUkH,OAAsB,sBAAwBmN,IAIpF,cADOC,EAAYC,SACCvb,EAAAA,cAAoB6T,GAAM3S,OAAOC,OAAO,CAAC,EAAGma,EAAa,CAC3E5Y,IAAKA,EACLqR,WAAYA,EACZ7G,UAAW,MACV,ICpBL,GAL+BlN,EAAAA,YAAiB,CAACyC,EAAOC,IAAqB1C,EAAAA,cAAoB6T,GAAM3S,OAAOC,OAAO,CACnHuB,IAAKA,GACJD,EAAO,CACRyK,UAAW,WCLb,IAAI3L,GAAgC,SAAUC,EAAGC,GAC/C,IAAIC,EAAI,CAAC,EACT,IAAK,IAAIC,KAAKH,EAAON,OAAOU,UAAUC,eAAeC,KAAKN,EAAGG,IAAMF,EAAEM,QAAQJ,GAAK,IAAGD,EAAEC,GAAKH,EAAEG,IAC9F,GAAS,MAALH,GAAqD,oBAAjCN,OAAOc,sBAA2C,KAAIC,EAAI,EAAb,IAAgBN,EAAIT,OAAOc,sBAAsBR,GAAIS,EAAIN,EAAEO,OAAQD,IAClIR,EAAEM,QAAQJ,EAAEM,IAAM,GAAKf,OAAOU,UAAUO,qBAAqBL,KAAKN,EAAGG,EAAEM,MAAKP,EAAEC,EAAEM,IAAMT,EAAEG,EAAEM,IADuB,CAGvH,OAAOP,CACT,EAKA,MAAM8Z,GAAOA,CAAC7Y,EAAID,KAChB,IAAI,SACAqR,GACEpR,EACJqE,EAAYzF,GAAOoB,EAAI,CAAC,aAC1B,MAAM8Y,EAAiBzb,EAAAA,SAAc,IAC/B+T,GAAgC,kBAAbA,GACdQ,EAAAA,EAAAA,GAAKR,EAAU,CAAC,aAAc,SAEhCA,GACN,CAACA,IAEJ,OAAoB/T,EAAAA,cAAoB6T,GAAM3S,OAAOC,OAAO,CAC1DuB,IAAKA,GACJsE,EAAW,CACZ+M,SAAU0H,EACVvO,UAAW,SACV,EAEL,GAA4BlN,EAAAA,WAAiBwb,IC/B7C,IAAIja,GAAgC,SAAUC,EAAGC,GAC/C,IAAIC,EAAI,CAAC,EACT,IAAK,IAAIC,KAAKH,EAAON,OAAOU,UAAUC,eAAeC,KAAKN,EAAGG,IAAMF,EAAEM,QAAQJ,GAAK,IAAGD,EAAEC,GAAKH,EAAEG,IAC9F,GAAS,MAALH,GAAqD,oBAAjCN,OAAOc,sBAA2C,KAAIC,EAAI,EAAb,IAAgBN,EAAIT,OAAOc,sBAAsBR,GAAIS,EAAIN,EAAEO,OAAQD,IAClIR,EAAEM,QAAQJ,EAAEM,IAAM,GAAKf,OAAOU,UAAUO,qBAAqBL,KAAKN,EAAGG,EAAEM,MAAKP,EAAEC,EAAEM,IAAMT,EAAEG,EAAEM,IADuB,CAGvH,OAAOP,CACT,EAIA,MAAMga,GAAiB,CAAC,EAAG,EAAG,EAAG,EAAG,GAmBpC,GAlB2B1b,EAAAA,YAAiB,CAACyC,EAAOC,KAClD,MAAM,MACFiZ,EAAQ,GACNlZ,EACJuE,EAAYzF,GAAOkB,EAAO,CAAC,UAC7B,IAAIyK,EAOJ,OALEA,EADEwO,GAAe5C,SAAS6C,GACd,IAAHra,OAAOqa,GAGJ,KAEM3b,EAAAA,cAAoB6T,GAAM3S,OAAOC,OAAO,CAC1DuB,IAAKA,GACJsE,EAAW,CACZkG,UAAWA,IACV,ICrBC8B,GAAa4M,EACnB5M,GAAWwM,KAAOA,GAClBxM,GAAW6M,KAAOA,GAClB7M,GAAW8M,MAAQA,GACnB9M,GAAW+M,UAAYA,GACvB,W,8BCVA,IAAIC,EAAkBC,EAAQ,MAE1BC,EAA4B,CAC9B,aAAc,OACd,YAAa,MACb,QAAW,QA2GbC,EAAOC,QAjGP,SAAcrL,EAAMsL,GAClB,IAAIC,EACFC,EACAC,EACAC,EACAC,EACAnT,EACAoT,GAAU,EACPN,IACHA,EAAU,CAAC,GAEbC,EAAQD,EAAQC,QAAS,EACzB,IAkDE,GAjDAE,EAAmBR,IAEnBS,EAAQG,SAASC,cACjBH,EAAYE,SAASE,gBAErBvT,EAAOqT,SAASG,cAAc,SACzBC,YAAcjM,EAEnBxH,EAAK0T,WAAa,OAElB1T,EAAKxI,MAAMmc,IAAM,QAEjB3T,EAAKxI,MAAM6J,SAAW,QACtBrB,EAAKxI,MAAM4R,IAAM,EACjBpJ,EAAKxI,MAAMoc,KAAO,mBAElB5T,EAAKxI,MAAMoJ,WAAa,MAExBZ,EAAKxI,MAAMqc,iBAAmB,OAC9B7T,EAAKxI,MAAMsc,cAAgB,OAC3B9T,EAAKxI,MAAMuc,aAAe,OAC1B/T,EAAKxI,MAAMmL,WAAa,OACxB3C,EAAKgU,iBAAiB,QAAQ,SAAS9b,GAErC,GADAA,EAAEqU,kBACEuG,EAAQ3G,OAEV,GADAjU,EAAEiG,iBAC6B,qBAApBjG,EAAE+b,cAA+B,CAC1ClB,GAASmB,QAAQC,KAAK,iCACtBpB,GAASmB,QAAQC,KAAK,4BACtBzD,OAAOuD,cAAcG,YACrB,IAAIjI,EAASwG,EAA0BG,EAAQ3G,SAAWwG,EAAmC,QAC7FjC,OAAOuD,cAAcI,QAAQlI,EAAQ3E,EACvC,MACEtP,EAAE+b,cAAcG,YAChBlc,EAAE+b,cAAcI,QAAQvB,EAAQ3G,OAAQ3E,GAGxCsL,EAAQpG,SACVxU,EAAEiG,iBACF2U,EAAQpG,OAAOxU,EAAE+b,eAErB,IAEAZ,SAASiB,KAAKC,YAAYvU,GAE1BkT,EAAMsB,mBAAmBxU,GACzBmT,EAAUsB,SAASvB,IAEFG,SAASqB,YAAY,QAEpC,MAAM,IAAIC,MAAM,iCAElBvB,GAAU,CACZ,CAAE,MAAOwB,GACP7B,GAASmB,QAAQW,MAAM,qCAAsCD,GAC7D7B,GAASmB,QAAQC,KAAK,4BACtB,IACEzD,OAAOuD,cAAcI,QAAQvB,EAAQ3G,QAAU,OAAQ3E,GACvDsL,EAAQpG,QAAUoG,EAAQpG,OAAOgE,OAAOuD,eACxCb,GAAU,CACZ,CAAE,MAAOwB,GACP7B,GAASmB,QAAQW,MAAM,uCAAwCD,GAC/D7B,GAASmB,QAAQW,MAAM,0BACvB7B,EAjFN,SAAgBA,GACd,IAAI8B,GAAW,YAAYC,KAAKC,UAAUC,WAAa,SAAM,QAAU,KACvE,OAAOjC,EAAQpO,QAAQ,gBAAiBkQ,EAC1C,CA8EgB3I,CAAO,YAAa2G,EAAUA,EAAQE,QAnFjC,oCAoFftC,OAAOwE,OAAOlC,EAASxL,EACzB,CACF,CAAE,QACI2L,IACkC,mBAAzBA,EAAUgC,YACnBhC,EAAUgC,YAAYjC,GAEtBC,EAAUiC,mBAIVpV,GACFqT,SAASiB,KAAKe,YAAYrV,GAE5BiT,GACF,CAEA,OAAOG,CACT,C,6DC/GIkC,EAAqB,SAA4BC,GACnD,IAAIC,EAAAA,EAAAA,MAAe9E,OAAO2C,SAASoC,gBAAiB,CAClD,IAAIC,EAAgBtb,MAAMC,QAAQkb,GAAaA,EAAY,CAACA,GACxDE,EAAkB/E,OAAO2C,SAASoC,gBACtC,OAAOC,EAAcC,MAAK,SAAUC,GAClC,OAAOA,KAAQH,EAAgBje,KACjC,GACF,CACA,OAAO,CACT,EACIqe,EAAsB,SAA6BN,EAAW5Z,GAChE,IAAK2Z,EAAmBC,GACtB,OAAO,EAET,IAAIO,EAAMzC,SAASG,cAAc,OAC7BuC,EAASD,EAAIte,MAAM+d,GAEvB,OADAO,EAAIte,MAAM+d,GAAa5Z,EAChBma,EAAIte,MAAM+d,KAAeQ,CAClC,EACO,SAASlI,EAAe0H,EAAWS,GACxC,OAAK5b,MAAMC,QAAQkb,SAA6Bzd,IAAfke,EAG1BV,EAAmBC,GAFjBM,EAAoBN,EAAWS,EAG1C,C,WCxBApD,EAAOC,QAAU,WACf,IAAIM,EAAYE,SAASE,eACzB,IAAKJ,EAAU8C,WACb,OAAO,WAAa,EAKtB,IAHA,IAAIC,EAAS7C,SAAS8C,cAElBC,EAAS,GACJ1d,EAAI,EAAGA,EAAIya,EAAU8C,WAAYvd,IACxC0d,EAAO/O,KAAK8L,EAAUkD,WAAW3d,IAGnC,OAAQwd,EAAOI,QAAQC,eACrB,IAAK,QACL,IAAK,WACHL,EAAOM,OACP,MAEF,QACEN,EAAS,KAKb,OADA/C,EAAUiC,kBACH,WACc,UAAnBjC,EAAUvM,MACVuM,EAAUiC,kBAELjC,EAAU8C,YACbG,EAAO3X,SAAQ,SAASyU,GACtBC,EAAUsB,SAASvB,EACrB,IAGFgD,GACAA,EAAO/R,OACT,CACF,C", "sources": ["../node_modules/antd/es/space/context.js", "../node_modules/antd/es/space/Item.js", "../node_modules/antd/es/space/index.js", "../node_modules/antd/es/style/operationUnit.js", "../node_modules/@ant-design/icons-svg/es/asn/CopyOutlined.js", "../node_modules/@ant-design/icons/es/icons/CopyOutlined.js", "../node_modules/@ant-design/icons-svg/es/asn/EditOutlined.js", "../node_modules/@ant-design/icons/es/icons/EditOutlined.js", "../node_modules/antd/es/_util/transButton.js", "../node_modules/@ant-design/icons-svg/es/asn/EnterOutlined.js", "../node_modules/@ant-design/icons/es/icons/EnterOutlined.js", "../node_modules/antd/es/typography/style/mixins.js", "../node_modules/antd/es/typography/style/index.js", "../node_modules/antd/es/typography/Editable.js", "../node_modules/antd/es/typography/Typography.js", "../node_modules/antd/es/typography/hooks/useMergedConfig.js", "../node_modules/antd/es/typography/hooks/useUpdatedEffect.js", "../node_modules/antd/es/typography/Base/Ellipsis.js", "../node_modules/antd/es/typography/Base/EllipsisTooltip.js", "../node_modules/antd/es/typography/Base/index.js", "../node_modules/antd/es/typography/Link.js", "../node_modules/antd/es/typography/Paragraph.js", "../node_modules/antd/es/typography/Text.js", "../node_modules/antd/es/typography/Title.js", "../node_modules/antd/es/typography/index.js", "../node_modules/copy-to-clipboard/index.js", "../node_modules/rc-util/es/Dom/styleChecker.js", "../node_modules/toggle-selection/index.js"], "sourcesContent": ["import React from 'react';\nexport const SpaceContext = /*#__PURE__*/React.createContext({\n  latestIndex: 0,\n  horizontalSize: 0,\n  verticalSize: 0,\n  supportFlexGap: false\n});\nexport const SpaceContextProvider = SpaceContext.Provider;", "import * as React from 'react';\nimport { SpaceContext } from './context';\nconst Item = _ref => {\n  let {\n    className,\n    direction,\n    index,\n    marginDirection,\n    children,\n    split,\n    wrap,\n    style: customStyle\n  } = _ref;\n  const {\n    horizontalSize,\n    verticalSize,\n    latestIndex,\n    supportFlexGap\n  } = React.useContext(SpaceContext);\n  let style = {};\n  if (!supportFlexGap) {\n    if (direction === 'vertical') {\n      if (index < latestIndex) {\n        style = {\n          marginBottom: horizontalSize / (split ? 2 : 1)\n        };\n      }\n    } else {\n      style = Object.assign(Object.assign({}, index < latestIndex && {\n        [marginDirection]: horizontalSize / (split ? 2 : 1)\n      }), wrap && {\n        paddingBottom: verticalSize\n      });\n    }\n  }\n  if (children === null || children === undefined) {\n    return null;\n  }\n  return /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(\"div\", {\n    className: className,\n    style: Object.assign(Object.assign({}, style), customStyle)\n  }, children), index < latestIndex && split && /*#__PURE__*/React.createElement(\"span\", {\n    className: `${className}-split`,\n    style: style\n  }, split));\n};\nexport default Item;", "'use client';\n\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport classNames from 'classnames';\nimport toArray from \"rc-util/es/Children/toArray\";\nimport * as React from 'react';\nimport useFlexGapSupport from '../_util/hooks/useFlexGapSupport';\nimport { ConfigContext } from '../config-provider';\nimport Compact from './Compact';\nimport Item from './Item';\nimport { SpaceContextProvider } from './context';\nimport useStyle from './style';\nexport { SpaceContext } from './context';\nconst spaceSize = {\n  small: 8,\n  middle: 16,\n  large: 24\n};\nfunction getNumberSize(size) {\n  return typeof size === 'string' ? spaceSize[size] : size || 0;\n}\nconst Space = /*#__PURE__*/React.forwardRef((props, ref) => {\n  var _a, _b;\n  const {\n    getPrefixCls,\n    space,\n    direction: directionConfig\n  } = React.useContext(ConfigContext);\n  const {\n      size = (space === null || space === void 0 ? void 0 : space.size) || 'small',\n      align,\n      className,\n      rootClassName,\n      children,\n      direction = 'horizontal',\n      prefixCls: customizePrefixCls,\n      split,\n      style,\n      wrap = false,\n      classNames: customClassNames,\n      styles\n    } = props,\n    otherProps = __rest(props, [\"size\", \"align\", \"className\", \"rootClassName\", \"children\", \"direction\", \"prefixCls\", \"split\", \"style\", \"wrap\", \"classNames\", \"styles\"]);\n  const supportFlexGap = useFlexGapSupport();\n  const [horizontalSize, verticalSize] = React.useMemo(() => (Array.isArray(size) ? size : [size, size]).map(item => getNumberSize(item)), [size]);\n  const childNodes = toArray(children, {\n    keepEmpty: true\n  });\n  const mergedAlign = align === undefined && direction === 'horizontal' ? 'center' : align;\n  const prefixCls = getPrefixCls('space', customizePrefixCls);\n  const [wrapSSR, hashId] = useStyle(prefixCls);\n  const cn = classNames(prefixCls, space === null || space === void 0 ? void 0 : space.className, hashId, `${prefixCls}-${direction}`, {\n    [`${prefixCls}-rtl`]: directionConfig === 'rtl',\n    [`${prefixCls}-align-${mergedAlign}`]: mergedAlign\n  }, className, rootClassName);\n  const itemClassName = classNames(`${prefixCls}-item`, (_a = customClassNames === null || customClassNames === void 0 ? void 0 : customClassNames.item) !== null && _a !== void 0 ? _a : (_b = space === null || space === void 0 ? void 0 : space.classNames) === null || _b === void 0 ? void 0 : _b.item);\n  const marginDirection = directionConfig === 'rtl' ? 'marginLeft' : 'marginRight';\n  // Calculate latest one\n  let latestIndex = 0;\n  const nodes = childNodes.map((child, i) => {\n    var _a, _b;\n    if (child !== null && child !== undefined) {\n      latestIndex = i;\n    }\n    const key = child && child.key || `${itemClassName}-${i}`;\n    return /*#__PURE__*/React.createElement(Item, {\n      className: itemClassName,\n      key: key,\n      direction: direction,\n      index: i,\n      marginDirection: marginDirection,\n      split: split,\n      wrap: wrap,\n      style: (_a = styles === null || styles === void 0 ? void 0 : styles.item) !== null && _a !== void 0 ? _a : (_b = space === null || space === void 0 ? void 0 : space.styles) === null || _b === void 0 ? void 0 : _b.item\n    }, child);\n  });\n  const spaceContext = React.useMemo(() => ({\n    horizontalSize,\n    verticalSize,\n    latestIndex,\n    supportFlexGap\n  }), [horizontalSize, verticalSize, latestIndex, supportFlexGap]);\n  // =========================== Render ===========================\n  if (childNodes.length === 0) {\n    return null;\n  }\n  const gapStyle = {};\n  if (wrap) {\n    gapStyle.flexWrap = 'wrap';\n    // Patch for gap not support\n    if (!supportFlexGap) {\n      gapStyle.marginBottom = -verticalSize;\n    }\n  }\n  if (supportFlexGap) {\n    gapStyle.columnGap = horizontalSize;\n    gapStyle.rowGap = verticalSize;\n  }\n  return wrapSSR( /*#__PURE__*/React.createElement(\"div\", Object.assign({\n    ref: ref,\n    className: cn,\n    style: Object.assign(Object.assign(Object.assign({}, gapStyle), space === null || space === void 0 ? void 0 : space.style), style)\n  }, otherProps), /*#__PURE__*/React.createElement(SpaceContextProvider, {\n    value: spaceContext\n  }, nodes)));\n});\nif (process.env.NODE_ENV !== 'production') {\n  Space.displayName = 'Space';\n}\nconst CompoundedSpace = Space;\nCompoundedSpace.Compact = Compact;\nexport default CompoundedSpace;", "// eslint-disable-next-line import/prefer-default-export\nexport const operationUnit = token => ({\n  // FIXME: This use link but is a operation unit. Seems should be a colorPrimary.\n  // And Typography use this to generate link style which should not do this.\n  color: token.colorLink,\n  textDecoration: 'none',\n  outline: 'none',\n  cursor: 'pointer',\n  transition: `color ${token.motionDurationSlow}`,\n  '&:focus, &:hover': {\n    color: token.colorLinkHover\n  },\n  '&:active': {\n    color: token.colorLinkActive\n  }\n});", "// This icon file is generated automatically.\nvar CopyOutlined = { \"icon\": { \"tag\": \"svg\", \"attrs\": { \"viewBox\": \"64 64 896 896\", \"focusable\": \"false\" }, \"children\": [{ \"tag\": \"path\", \"attrs\": { \"d\": \"M832 64H296c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h496v688c0 4.4 3.6 8 8 8h56c4.4 0 8-3.6 8-8V96c0-17.7-14.3-32-32-32zM704 192H192c-17.7 0-32 14.3-32 32v530.7c0 8.5 3.4 16.6 9.4 22.6l173.3 173.3c2.2 2.2 4.7 4 7.4 5.5v1.9h4.2c3.5 1.3 7.2 2 11 2H704c17.7 0 32-14.3 32-32V224c0-17.7-14.3-32-32-32zM350 856.2L263.9 770H350v86.2zM664 888H414V746c0-22.1-17.9-40-40-40H232V264h432v624z\" } }] }, \"name\": \"copy\", \"theme\": \"outlined\" };\nexport default CopyOutlined;\n", "import _extends from \"@babel/runtime/helpers/esm/extends\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react';\nimport CopyOutlinedSvg from \"@ant-design/icons-svg/es/asn/CopyOutlined\";\nimport AntdIcon from \"../components/AntdIcon\";\nvar CopyOutlined = function CopyOutlined(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {\n    ref: ref,\n    icon: CopyOutlinedSvg\n  }));\n};\nif (process.env.NODE_ENV !== 'production') {\n  CopyOutlined.displayName = 'CopyOutlined';\n}\nexport default /*#__PURE__*/React.forwardRef(CopyOutlined);", "// This icon file is generated automatically.\nvar EditOutlined = { \"icon\": { \"tag\": \"svg\", \"attrs\": { \"viewBox\": \"64 64 896 896\", \"focusable\": \"false\" }, \"children\": [{ \"tag\": \"path\", \"attrs\": { \"d\": \"M257.7 752c2 0 4-.2 6-.5L431.9 722c2-.4 3.9-1.3 5.3-2.8l423.9-423.9a9.96 9.96 0 000-14.1L694.9 114.9c-1.9-1.9-4.4-2.9-7.1-2.9s-5.2 1-7.1 2.9L256.8 538.8c-1.5 1.5-2.4 3.3-2.8 5.3l-29.5 168.2a33.5 33.5 0 009.4 29.8c6.6 6.4 14.9 9.9 23.8 9.9zm67.4-174.4L687.8 215l73.3 73.3-362.7 362.6-88.9 15.7 15.6-89zM880 836H144c-17.7 0-32 14.3-32 32v36c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-36c0-17.7-14.3-32-32-32z\" } }] }, \"name\": \"edit\", \"theme\": \"outlined\" };\nexport default EditOutlined;\n", "import _extends from \"@babel/runtime/helpers/esm/extends\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react';\nimport EditOutlinedSvg from \"@ant-design/icons-svg/es/asn/EditOutlined\";\nimport AntdIcon from \"../components/AntdIcon\";\nvar EditOutlined = function EditOutlined(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {\n    ref: ref,\n    icon: EditOutlinedSvg\n  }));\n};\nif (process.env.NODE_ENV !== 'production') {\n  EditOutlined.displayName = 'EditOutlined';\n}\nexport default /*#__PURE__*/React.forwardRef(EditOutlined);", "var __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\n/**\n * Wrap of sub component which need use as Button capacity (like Icon component).\n *\n * This helps accessibility reader to tread as a interactive button to operation.\n */\nimport KeyCode from \"rc-util/es/KeyCode\";\nimport * as React from 'react';\nconst inlineStyle = {\n  border: 0,\n  background: 'transparent',\n  padding: 0,\n  lineHeight: 'inherit',\n  display: 'inline-block'\n};\nconst TransButton = /*#__PURE__*/React.forwardRef((props, ref) => {\n  const onKeyDown = event => {\n    const {\n      keyCode\n    } = event;\n    if (keyCode === KeyCode.ENTER) {\n      event.preventDefault();\n    }\n  };\n  const onKeyUp = event => {\n    const {\n      keyCode\n    } = event;\n    const {\n      onClick\n    } = props;\n    if (keyCode === KeyCode.ENTER && onClick) {\n      onClick();\n    }\n  };\n  const {\n      style,\n      noStyle,\n      disabled\n    } = props,\n    restProps = __rest(props, [\"style\", \"noStyle\", \"disabled\"]);\n  let mergedStyle = {};\n  if (!noStyle) {\n    mergedStyle = Object.assign({}, inlineStyle);\n  }\n  if (disabled) {\n    mergedStyle.pointerEvents = 'none';\n  }\n  mergedStyle = Object.assign(Object.assign({}, mergedStyle), style);\n  return /*#__PURE__*/React.createElement(\"div\", Object.assign({\n    role: \"button\",\n    tabIndex: 0,\n    ref: ref\n  }, restProps, {\n    onKeyDown: onKeyDown,\n    onKeyUp: onKeyUp,\n    style: mergedStyle\n  }));\n});\nexport default TransButton;", "// This icon file is generated automatically.\nvar EnterOutlined = { \"icon\": { \"tag\": \"svg\", \"attrs\": { \"viewBox\": \"64 64 896 896\", \"focusable\": \"false\" }, \"children\": [{ \"tag\": \"path\", \"attrs\": { \"d\": \"M864 170h-60c-4.4 0-8 3.6-8 8v518H310v-73c0-6.7-7.8-10.5-13-6.3l-141.9 112a8 8 0 000 12.6l141.9 112c5.3 4.2 13 .4 13-6.3v-75h498c35.3 0 64-28.7 64-64V178c0-4.4-3.6-8-8-8z\" } }] }, \"name\": \"enter\", \"theme\": \"outlined\" };\nexport default EnterOutlined;\n", "import _extends from \"@babel/runtime/helpers/esm/extends\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react';\nimport EnterOutlinedSvg from \"@ant-design/icons-svg/es/asn/EnterOutlined\";\nimport AntdIcon from \"../components/AntdIcon\";\nvar EnterOutlined = function EnterOutlined(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {\n    ref: ref,\n    icon: EnterOutlinedSvg\n  }));\n};\nif (process.env.NODE_ENV !== 'production') {\n  EnterOutlined.displayName = 'EnterOutlined';\n}\nexport default /*#__PURE__*/React.forwardRef(EnterOutlined);", "/*\n.typography-title(@fontSize; @fontWeight; @lineHeight; @headingColor; @headingMarginBottom;) {\n  margin-bottom: @headingMarginBottom;\n  color: @headingColor;\n  font-weight: @fontWeight;\n  fontSize: @fontSize;\n  line-height: @lineHeight;\n}\n*/\nimport { gold } from '@ant-design/colors';\nimport { initInputToken } from '../../input/style';\nimport { operationUnit } from '../../style';\n// eslint-disable-next-line import/prefer-default-export\nconst getTitleStyle = (fontSize, lineHeight, color, token) => {\n  const {\n    titleMarginBottom,\n    fontWeightStrong\n  } = token;\n  return {\n    marginBottom: titleMarginBottom,\n    color,\n    fontWeight: fontWeightStrong,\n    fontSize,\n    lineHeight\n  };\n};\n// eslint-disable-next-line import/prefer-default-export\nexport const getTitleStyles = token => {\n  const headings = [1, 2, 3, 4, 5];\n  const styles = {};\n  headings.forEach(headingLevel => {\n    styles[`\n      h${headingLevel}&,\n      div&-h${headingLevel},\n      div&-h${headingLevel} > textarea,\n      h${headingLevel}\n    `] = getTitleStyle(token[`fontSizeHeading${headingLevel}`], token[`lineHeightHeading${headingLevel}`], token.colorTextHeading, token);\n  });\n  return styles;\n};\nexport const getLinkStyles = token => {\n  const {\n    componentCls\n  } = token;\n  return {\n    'a&, a': Object.assign(Object.assign({}, operationUnit(token)), {\n      textDecoration: token.linkDecoration,\n      '&:active, &:hover': {\n        textDecoration: token.linkHoverDecoration\n      },\n      [`&[disabled], &${componentCls}-disabled`]: {\n        color: token.colorTextDisabled,\n        cursor: 'not-allowed',\n        '&:active, &:hover': {\n          color: token.colorTextDisabled\n        },\n        '&:active': {\n          pointerEvents: 'none'\n        }\n      }\n    })\n  };\n};\nexport const getResetStyles = token => ({\n  code: {\n    margin: '0 0.2em',\n    paddingInline: '0.4em',\n    paddingBlock: '0.2em 0.1em',\n    fontSize: '85%',\n    fontFamily: token.fontFamilyCode,\n    background: 'rgba(150, 150, 150, 0.1)',\n    border: '1px solid rgba(100, 100, 100, 0.2)',\n    borderRadius: 3\n  },\n  kbd: {\n    margin: '0 0.2em',\n    paddingInline: '0.4em',\n    paddingBlock: '0.15em 0.1em',\n    fontSize: '90%',\n    fontFamily: token.fontFamilyCode,\n    background: 'rgba(150, 150, 150, 0.06)',\n    border: '1px solid rgba(100, 100, 100, 0.2)',\n    borderBottomWidth: 2,\n    borderRadius: 3\n  },\n  mark: {\n    padding: 0,\n    // FIXME hardcode in v4\n    backgroundColor: gold[2]\n  },\n  'u, ins': {\n    textDecoration: 'underline',\n    textDecorationSkipInk: 'auto'\n  },\n  's, del': {\n    textDecoration: 'line-through'\n  },\n  strong: {\n    fontWeight: 600\n  },\n  // list\n  'ul, ol': {\n    marginInline: 0,\n    marginBlock: '0 1em',\n    padding: 0,\n    li: {\n      marginInline: '20px 0',\n      marginBlock: 0,\n      paddingInline: '4px 0',\n      paddingBlock: 0\n    }\n  },\n  ul: {\n    listStyleType: 'circle',\n    ul: {\n      listStyleType: 'disc'\n    }\n  },\n  ol: {\n    listStyleType: 'decimal'\n  },\n  // pre & block\n  'pre, blockquote': {\n    margin: '1em 0'\n  },\n  pre: {\n    padding: '0.4em 0.6em',\n    whiteSpace: 'pre-wrap',\n    wordWrap: 'break-word',\n    background: 'rgba(150, 150, 150, 0.1)',\n    border: '1px solid rgba(100, 100, 100, 0.2)',\n    borderRadius: 3,\n    fontFamily: token.fontFamilyCode,\n    // Compatible for marked\n    code: {\n      display: 'inline',\n      margin: 0,\n      padding: 0,\n      fontSize: 'inherit',\n      fontFamily: 'inherit',\n      background: 'transparent',\n      border: 0\n    }\n  },\n  blockquote: {\n    paddingInline: '0.6em 0',\n    paddingBlock: 0,\n    borderInlineStart: '4px solid rgba(100, 100, 100, 0.2)',\n    opacity: 0.85\n  }\n});\nexport const getEditableStyles = token => {\n  const {\n    componentCls\n  } = token;\n  const inputToken = initInputToken(token);\n  const inputShift = inputToken.inputPaddingVertical + 1;\n  return {\n    '&-edit-content': {\n      position: 'relative',\n      'div&': {\n        insetInlineStart: -token.paddingSM,\n        marginTop: -inputShift,\n        marginBottom: `calc(1em - ${inputShift}px)`\n      },\n      [`${componentCls}-edit-content-confirm`]: {\n        position: 'absolute',\n        insetInlineEnd: token.marginXS + 2,\n        insetBlockEnd: token.marginXS,\n        color: token.colorTextDescription,\n        // default style\n        fontWeight: 'normal',\n        fontSize: token.fontSize,\n        fontStyle: 'normal',\n        pointerEvents: 'none'\n      },\n      textarea: {\n        margin: '0!important',\n        // Fix Editable Textarea flash in Firefox\n        MozTransition: 'none',\n        height: '1em'\n      }\n    }\n  };\n};\nexport const getCopyableStyles = token => ({\n  '&-copy-success': {\n    [`\n    &,\n    &:hover,\n    &:focus`]: {\n      color: token.colorSuccess\n    }\n  }\n});\nexport const getEllipsisStyles = () => ({\n  [`\n  a&-ellipsis,\n  span&-ellipsis\n  `]: {\n    display: 'inline-block',\n    maxWidth: '100%'\n  },\n  '&-single-line': {\n    whiteSpace: 'nowrap'\n  },\n  '&-ellipsis-single-line': {\n    overflow: 'hidden',\n    textOverflow: 'ellipsis',\n    // https://blog.csdn.net/iefreer/article/details/50421025\n    'a&, span&': {\n      verticalAlign: 'bottom'\n    }\n  },\n  '&-ellipsis-multiple-line': {\n    display: '-webkit-box',\n    overflow: 'hidden',\n    WebkitLineClamp: 3,\n    WebkitBoxOrient: 'vertical'\n  }\n});", "import { operationUnit } from '../../style';\nimport { genComponentStyleHook } from '../../theme/internal';\nimport { getCopyableStyles, getEditableStyles, getEllipsisStyles, getLinkStyles, getResetStyles, getTitleStyles } from './mixins';\nconst genTypographyStyle = token => {\n  const {\n    componentCls,\n    titleMarginTop\n  } = token;\n  return {\n    [componentCls]: Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({\n      color: token.colorText,\n      wordBreak: 'break-word',\n      lineHeight: token.lineHeight,\n      [`&${componentCls}-secondary`]: {\n        color: token.colorTextDescription\n      },\n      [`&${componentCls}-success`]: {\n        color: token.colorSuccess\n      },\n      [`&${componentCls}-warning`]: {\n        color: token.colorWarning\n      },\n      [`&${componentCls}-danger`]: {\n        color: token.colorError,\n        'a&:active, a&:focus': {\n          color: token.colorErrorActive\n        },\n        'a&:hover': {\n          color: token.colorErrorHover\n        }\n      },\n      [`&${componentCls}-disabled`]: {\n        color: token.colorTextDisabled,\n        cursor: 'not-allowed',\n        userSelect: 'none'\n      },\n      [`\n        div&,\n        p\n      `]: {\n        marginBottom: '1em'\n      }\n    }, getTitleStyles(token)), {\n      [`\n      & + h1${componentCls},\n      & + h2${componentCls},\n      & + h3${componentCls},\n      & + h4${componentCls},\n      & + h5${componentCls}\n      `]: {\n        marginTop: titleMarginTop\n      },\n      [`\n      div,\n      ul,\n      li,\n      p,\n      h1,\n      h2,\n      h3,\n      h4,\n      h5`]: {\n        [`\n        + h1,\n        + h2,\n        + h3,\n        + h4,\n        + h5\n        `]: {\n          marginTop: titleMarginTop\n        }\n      }\n    }), getResetStyles(token)), getLinkStyles(token)), {\n      // Operation\n      [`\n        ${componentCls}-expand,\n        ${componentCls}-edit,\n        ${componentCls}-copy\n      `]: Object.assign(Object.assign({}, operationUnit(token)), {\n        marginInlineStart: token.marginXXS\n      })\n    }), getEditableStyles(token)), getCopyableStyles(token)), getEllipsisStyles()), {\n      '&-rtl': {\n        direction: 'rtl'\n      }\n    })\n  };\n};\n// ============================== Export ==============================\nexport default genComponentStyleHook('Typography', token => [genTypographyStyle(token)], () => ({\n  titleMarginTop: '1.2em',\n  titleMarginBottom: '0.5em'\n}));", "import EnterOutlined from \"@ant-design/icons/es/icons/EnterOutlined\";\nimport classNames from 'classnames';\nimport KeyCode from \"rc-util/es/KeyCode\";\nimport * as React from 'react';\nimport { cloneElement } from '../_util/reactNode';\nimport TextArea from '../input/TextArea';\nimport useStyle from './style';\nconst Editable = props => {\n  const {\n    prefixCls,\n    'aria-label': ariaLabel,\n    className,\n    style,\n    direction,\n    maxLength,\n    autoSize = true,\n    value,\n    onSave,\n    onCancel,\n    onEnd,\n    component,\n    enterIcon = /*#__PURE__*/React.createElement(EnterOutlined, null)\n  } = props;\n  const ref = React.useRef(null);\n  const inComposition = React.useRef(false);\n  const lastKeyCode = React.useRef();\n  const [current, setCurrent] = React.useState(value);\n  React.useEffect(() => {\n    setCurrent(value);\n  }, [value]);\n  React.useEffect(() => {\n    if (ref.current && ref.current.resizableTextArea) {\n      const {\n        textArea\n      } = ref.current.resizableTextArea;\n      textArea.focus();\n      const {\n        length\n      } = textArea.value;\n      textArea.setSelectionRange(length, length);\n    }\n  }, []);\n  const onChange = _ref => {\n    let {\n      target\n    } = _ref;\n    setCurrent(target.value.replace(/[\\n\\r]/g, ''));\n  };\n  const onCompositionStart = () => {\n    inComposition.current = true;\n  };\n  const onCompositionEnd = () => {\n    inComposition.current = false;\n  };\n  const onKeyDown = _ref2 => {\n    let {\n      keyCode\n    } = _ref2;\n    // We don't record keyCode when IME is using\n    if (inComposition.current) return;\n    lastKeyCode.current = keyCode;\n  };\n  const confirmChange = () => {\n    onSave(current.trim());\n  };\n  const onKeyUp = _ref3 => {\n    let {\n      keyCode,\n      ctrlKey,\n      altKey,\n      metaKey,\n      shiftKey\n    } = _ref3;\n    // Check if it's a real key\n    if (lastKeyCode.current === keyCode && !inComposition.current && !ctrlKey && !altKey && !metaKey && !shiftKey) {\n      if (keyCode === KeyCode.ENTER) {\n        confirmChange();\n        onEnd === null || onEnd === void 0 ? void 0 : onEnd();\n      } else if (keyCode === KeyCode.ESC) {\n        onCancel();\n      }\n    }\n  };\n  const onBlur = () => {\n    confirmChange();\n  };\n  const textClassName = component ? `${prefixCls}-${component}` : '';\n  const [wrapSSR, hashId] = useStyle(prefixCls);\n  const textAreaClassName = classNames(prefixCls, `${prefixCls}-edit-content`, {\n    [`${prefixCls}-rtl`]: direction === 'rtl'\n  }, className, textClassName, hashId);\n  return wrapSSR( /*#__PURE__*/React.createElement(\"div\", {\n    className: textAreaClassName,\n    style: style\n  }, /*#__PURE__*/React.createElement(TextArea, {\n    ref: ref,\n    maxLength: maxLength,\n    value: current,\n    onChange: onChange,\n    onKeyDown: onKeyDown,\n    onKeyUp: onKeyUp,\n    onCompositionStart: onCompositionStart,\n    onCompositionEnd: onCompositionEnd,\n    onBlur: onBlur,\n    \"aria-label\": ariaLabel,\n    rows: 1,\n    autoSize: autoSize\n  }), enterIcon !== null ? cloneElement(enterIcon, {\n    className: `${prefixCls}-edit-content-confirm`\n  }) : null));\n};\nexport default Editable;", "var __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport classNames from 'classnames';\nimport { composeRef } from \"rc-util/es/ref\";\nimport * as React from 'react';\nimport warning from '../_util/warning';\nimport { ConfigContext } from '../config-provider';\nimport useStyle from './style';\nconst Typography = /*#__PURE__*/React.forwardRef((props, ref) => {\n  const {\n      prefixCls: customizePrefixCls,\n      component: Component = 'article',\n      className,\n      rootClassName,\n      setContentRef,\n      children,\n      direction: typographyDirection,\n      style\n    } = props,\n    restProps = __rest(props, [\"prefixCls\", \"component\", \"className\", \"rootClassName\", \"setContentRef\", \"children\", \"direction\", \"style\"]);\n  const {\n    getPrefixCls,\n    direction: contextDirection,\n    typography\n  } = React.useContext(ConfigContext);\n  const direction = typographyDirection !== null && typographyDirection !== void 0 ? typographyDirection : contextDirection;\n  let mergedRef = ref;\n  if (setContentRef) {\n    process.env.NODE_ENV !== \"production\" ? warning(false, 'Typography', '`setContentRef` is deprecated. Please use `ref` instead.') : void 0;\n    mergedRef = composeRef(ref, setContentRef);\n  }\n  const prefixCls = getPrefixCls('typography', customizePrefixCls);\n  // Style\n  const [wrapSSR, hashId] = useStyle(prefixCls);\n  const componentClassName = classNames(prefixCls, typography === null || typography === void 0 ? void 0 : typography.className, {\n    [`${prefixCls}-rtl`]: direction === 'rtl'\n  }, className, rootClassName, hashId);\n  const mergedStyle = Object.assign(Object.assign({}, typography === null || typography === void 0 ? void 0 : typography.style), style);\n  return wrapSSR(\n  /*#__PURE__*/\n  // @ts-expect-error: Expression produces a union type that is too complex to represent.\n  React.createElement(Component, Object.assign({\n    className: componentClassName,\n    style: mergedStyle,\n    ref: mergedRef\n  }, restProps), children));\n});\nif (process.env.NODE_ENV !== 'production') {\n  Typography.displayName = 'Typography';\n}\n// es default export should use const instead of let\nexport default Typography;", "import * as React from 'react';\nexport default function useMergedConfig(propConfig, templateConfig) {\n  return React.useMemo(() => {\n    const support = !!propConfig;\n    return [support, Object.assign(Object.assign({}, templateConfig), support && typeof propConfig === 'object' ? propConfig : null)];\n  }, [propConfig]);\n}", "import * as React from 'react';\n/** Similar with `useEffect` but only trigger after mounted */\nconst useUpdatedEffect = (callback, conditions) => {\n  const mountRef = React.useRef(false);\n  React.useEffect(() => {\n    if (mountRef.current) {\n      callback();\n    } else {\n      mountRef.current = true;\n    }\n  }, conditions);\n};\nexport default useUpdatedEffect;", "import toArray from \"rc-util/es/Children/toArray\";\nimport useIsomorphicLayoutEffect from \"rc-util/es/hooks/useLayoutEffect\";\nimport * as React from 'react';\nfunction cuttable(node) {\n  const type = typeof node;\n  return type === 'string' || type === 'number';\n}\nfunction getNodesLen(nodeList) {\n  let totalLen = 0;\n  nodeList.forEach(node => {\n    if (cuttable(node)) {\n      totalLen += String(node).length;\n    } else {\n      totalLen += 1;\n    }\n  });\n  return totalLen;\n}\nfunction sliceNodes(nodeList, len) {\n  let currLen = 0;\n  const currentNodeList = [];\n  for (let i = 0; i < nodeList.length; i += 1) {\n    // Match to return\n    if (currLen === len) {\n      return currentNodeList;\n    }\n    const node = nodeList[i];\n    const canCut = cuttable(node);\n    const nodeLen = canCut ? String(node).length : 1;\n    const nextLen = currLen + nodeLen;\n    // Exceed but current not which means we need cut this\n    // This will not happen on validate ReactElement\n    if (nextLen > len) {\n      const restLen = len - currLen;\n      currentNodeList.push(String(node).slice(0, restLen));\n      return currentNodeList;\n    }\n    currentNodeList.push(node);\n    currLen = nextLen;\n  }\n  return nodeList;\n}\nconst NONE = 0;\nconst PREPARE = 1;\nconst WALKING = 2;\nconst DONE_WITH_ELLIPSIS = 3;\nconst DONE_WITHOUT_ELLIPSIS = 4;\nconst Ellipsis = _ref => {\n  let {\n    enabledMeasure,\n    children,\n    text,\n    width,\n    fontSize,\n    rows,\n    onEllipsis\n  } = _ref;\n  const [[startLen, midLen, endLen], setCutLength] = React.useState([0, 0, 0]);\n  const [walkingState, setWalkingState] = React.useState(NONE);\n  const [singleRowHeight, setSingleRowHeight] = React.useState(0);\n  const singleRowRef = React.useRef(null);\n  const midRowRef = React.useRef(null);\n  const nodeList = React.useMemo(() => toArray(text), [text]);\n  const totalLen = React.useMemo(() => getNodesLen(nodeList), [nodeList]);\n  const mergedChildren = React.useMemo(() => {\n    if (!enabledMeasure || walkingState !== DONE_WITH_ELLIPSIS) {\n      return children(nodeList, false);\n    }\n    return children(sliceNodes(nodeList, midLen), midLen < totalLen);\n  }, [enabledMeasure, walkingState, children, nodeList, midLen, totalLen]);\n  // ======================== Walk ========================\n  useIsomorphicLayoutEffect(() => {\n    if (enabledMeasure && width && fontSize && totalLen) {\n      setWalkingState(PREPARE);\n      setCutLength([0, Math.ceil(totalLen / 2), totalLen]);\n    }\n  }, [enabledMeasure, width, fontSize, text, totalLen, rows]);\n  useIsomorphicLayoutEffect(() => {\n    var _a;\n    if (walkingState === PREPARE) {\n      setSingleRowHeight(((_a = singleRowRef.current) === null || _a === void 0 ? void 0 : _a.offsetHeight) || 0);\n    }\n  }, [walkingState]);\n  useIsomorphicLayoutEffect(() => {\n    var _a, _b;\n    if (singleRowHeight) {\n      if (walkingState === PREPARE) {\n        // Ignore if position is enough\n        const midHeight = ((_a = midRowRef.current) === null || _a === void 0 ? void 0 : _a.offsetHeight) || 0;\n        const maxHeight = rows * singleRowHeight;\n        if (midHeight <= maxHeight) {\n          setWalkingState(DONE_WITHOUT_ELLIPSIS);\n          onEllipsis(false);\n        } else {\n          setWalkingState(WALKING);\n        }\n      } else if (walkingState === WALKING) {\n        if (startLen !== endLen) {\n          const midHeight = ((_b = midRowRef.current) === null || _b === void 0 ? void 0 : _b.offsetHeight) || 0;\n          const maxHeight = rows * singleRowHeight;\n          let nextStartLen = startLen;\n          let nextEndLen = endLen;\n          // We reach the last round\n          if (startLen === endLen - 1) {\n            nextEndLen = startLen;\n          } else if (midHeight <= maxHeight) {\n            nextStartLen = midLen;\n          } else {\n            nextEndLen = midLen;\n          }\n          const nextMidLen = Math.ceil((nextStartLen + nextEndLen) / 2);\n          setCutLength([nextStartLen, nextMidLen, nextEndLen]);\n        } else {\n          setWalkingState(DONE_WITH_ELLIPSIS);\n          onEllipsis(true);\n        }\n      }\n    }\n  }, [walkingState, startLen, endLen, rows, singleRowHeight]);\n  // ======================= Render =======================\n  const measureStyle = {\n    width,\n    whiteSpace: 'normal',\n    margin: 0,\n    padding: 0\n  };\n  const renderMeasure = (content, ref, style) => /*#__PURE__*/React.createElement(\"span\", {\n    \"aria-hidden\": true,\n    ref: ref,\n    style: Object.assign({\n      position: 'fixed',\n      display: 'block',\n      left: 0,\n      top: 0,\n      zIndex: -9999,\n      visibility: 'hidden',\n      pointerEvents: 'none',\n      fontSize: Math.floor(fontSize / 2) * 2\n    }, style)\n  }, content);\n  const renderMeasureSlice = (len, ref) => {\n    const sliceNodeList = sliceNodes(nodeList, len);\n    return renderMeasure(children(sliceNodeList, true), ref, measureStyle);\n  };\n  return /*#__PURE__*/React.createElement(React.Fragment, null, mergedChildren, enabledMeasure && walkingState !== DONE_WITH_ELLIPSIS && walkingState !== DONE_WITHOUT_ELLIPSIS && /*#__PURE__*/React.createElement(React.Fragment, null, renderMeasure('lg', singleRowRef, {\n    wordBreak: 'keep-all',\n    whiteSpace: 'nowrap'\n  }), walkingState === PREPARE ? renderMeasure(children(nodeList, false), midRowRef, measureStyle) : renderMeasureSlice(midLen, midRowRef)));\n};\nif (process.env.NODE_ENV !== 'production') {\n  Ellipsis.displayName = 'Ellipsis';\n}\nexport default Ellipsis;", "import * as React from 'react';\nimport Tooltip from '../../tooltip';\nconst EllipsisTooltip = _ref => {\n  let {\n    enabledEllipsis,\n    isEllipsis,\n    children,\n    tooltipProps\n  } = _ref;\n  if (!(tooltipProps === null || tooltipProps === void 0 ? void 0 : tooltipProps.title) || !enabledEllipsis) {\n    return children;\n  }\n  return /*#__PURE__*/React.createElement(Tooltip, Object.assign({\n    open: isEllipsis ? undefined : false\n  }, tooltipProps), children);\n};\nif (process.env.NODE_ENV !== 'production') {\n  EllipsisTooltip.displayName = 'EllipsisTooltip';\n}\nexport default EllipsisTooltip;", "var __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport CheckOutlined from \"@ant-design/icons/es/icons/CheckOutlined\";\nimport CopyOutlined from \"@ant-design/icons/es/icons/CopyOutlined\";\nimport EditOutlined from \"@ant-design/icons/es/icons/EditOutlined\";\nimport classNames from 'classnames';\nimport copy from 'copy-to-clipboard';\nimport ResizeObserver from 'rc-resize-observer';\nimport toArray from \"rc-util/es/Children/toArray\";\nimport useIsomorphicLayoutEffect from \"rc-util/es/hooks/useLayoutEffect\";\nimport useMergedState from \"rc-util/es/hooks/useMergedState\";\nimport omit from \"rc-util/es/omit\";\nimport { composeRef } from \"rc-util/es/ref\";\nimport * as React from 'react';\nimport { isStyleSupport } from '../../_util/styleChecker';\nimport TransButton from '../../_util/transButton';\nimport { ConfigContext } from '../../config-provider';\nimport useLocale from '../../locale/useLocale';\nimport Tooltip from '../../tooltip';\nimport Editable from '../Editable';\nimport Typography from '../Typography';\nimport useMergedConfig from '../hooks/useMergedConfig';\nimport useUpdatedEffect from '../hooks/useUpdatedEffect';\nimport Ellipsis from './Ellipsis';\nimport EllipsisTooltip from './EllipsisTooltip';\nfunction wrapperDecorations(_ref, content) {\n  let {\n    mark,\n    code,\n    underline,\n    delete: del,\n    strong,\n    keyboard,\n    italic\n  } = _ref;\n  let currentContent = content;\n  function wrap(tag, needed) {\n    if (!needed) {\n      return;\n    }\n    currentContent = /*#__PURE__*/React.createElement(tag, {}, currentContent);\n  }\n  wrap('strong', strong);\n  wrap('u', underline);\n  wrap('del', del);\n  wrap('code', code);\n  wrap('mark', mark);\n  wrap('kbd', keyboard);\n  wrap('i', italic);\n  return currentContent;\n}\nfunction getNode(dom, defaultNode, needDom) {\n  if (dom === true || dom === undefined) {\n    return defaultNode;\n  }\n  return dom || needDom && defaultNode;\n}\nfunction toList(val) {\n  if (val === false) {\n    return [false, false];\n  }\n  return Array.isArray(val) ? val : [val];\n}\nconst ELLIPSIS_STR = '...';\nconst Base = /*#__PURE__*/React.forwardRef((props, ref) => {\n  var _a, _b, _c;\n  const {\n      prefixCls: customizePrefixCls,\n      className,\n      style,\n      type,\n      disabled,\n      children,\n      ellipsis,\n      editable,\n      copyable,\n      component,\n      title\n    } = props,\n    restProps = __rest(props, [\"prefixCls\", \"className\", \"style\", \"type\", \"disabled\", \"children\", \"ellipsis\", \"editable\", \"copyable\", \"component\", \"title\"]);\n  const {\n    getPrefixCls,\n    direction\n  } = React.useContext(ConfigContext);\n  const [textLocale] = useLocale('Text');\n  const typographyRef = React.useRef(null);\n  const editIconRef = React.useRef(null);\n  // ============================ MISC ============================\n  const prefixCls = getPrefixCls('typography', customizePrefixCls);\n  const textProps = omit(restProps, ['mark', 'code', 'delete', 'underline', 'strong', 'keyboard', 'italic']);\n  // ========================== Editable ==========================\n  const [enableEdit, editConfig] = useMergedConfig(editable);\n  const [editing, setEditing] = useMergedState(false, {\n    value: editConfig.editing\n  });\n  const {\n    triggerType = ['icon']\n  } = editConfig;\n  const triggerEdit = edit => {\n    var _a;\n    if (edit) {\n      (_a = editConfig.onStart) === null || _a === void 0 ? void 0 : _a.call(editConfig);\n    }\n    setEditing(edit);\n  };\n  // Focus edit icon when back\n  useUpdatedEffect(() => {\n    var _a;\n    if (!editing) {\n      (_a = editIconRef.current) === null || _a === void 0 ? void 0 : _a.focus();\n    }\n  }, [editing]);\n  const onEditClick = e => {\n    e === null || e === void 0 ? void 0 : e.preventDefault();\n    triggerEdit(true);\n  };\n  const onEditChange = value => {\n    var _a;\n    (_a = editConfig.onChange) === null || _a === void 0 ? void 0 : _a.call(editConfig, value);\n    triggerEdit(false);\n  };\n  const onEditCancel = () => {\n    var _a;\n    (_a = editConfig.onCancel) === null || _a === void 0 ? void 0 : _a.call(editConfig);\n    triggerEdit(false);\n  };\n  // ========================== Copyable ==========================\n  const [enableCopy, copyConfig] = useMergedConfig(copyable);\n  const [copied, setCopied] = React.useState(false);\n  const copyIdRef = React.useRef(null);\n  const copyOptions = {};\n  if (copyConfig.format) {\n    copyOptions.format = copyConfig.format;\n  }\n  const cleanCopyId = () => {\n    if (copyIdRef.current) {\n      clearTimeout(copyIdRef.current);\n    }\n  };\n  const onCopyClick = e => {\n    var _a;\n    e === null || e === void 0 ? void 0 : e.preventDefault();\n    e === null || e === void 0 ? void 0 : e.stopPropagation();\n    copy(copyConfig.text || String(children) || '', copyOptions);\n    setCopied(true);\n    // Trigger tips update\n    cleanCopyId();\n    copyIdRef.current = setTimeout(() => {\n      setCopied(false);\n    }, 3000);\n    (_a = copyConfig.onCopy) === null || _a === void 0 ? void 0 : _a.call(copyConfig, e);\n  };\n  React.useEffect(() => cleanCopyId, []);\n  // ========================== Ellipsis ==========================\n  const [isLineClampSupport, setIsLineClampSupport] = React.useState(false);\n  const [isTextOverflowSupport, setIsTextOverflowSupport] = React.useState(false);\n  const [expanded, setExpanded] = React.useState(false);\n  const [isJsEllipsis, setIsJsEllipsis] = React.useState(false);\n  const [isNativeEllipsis, setIsNativeEllipsis] = React.useState(false);\n  const [isNativeVisible, setIsNativeVisible] = React.useState(true);\n  const [enableEllipsis, ellipsisConfig] = useMergedConfig(ellipsis, {\n    expandable: false\n  });\n  const mergedEnableEllipsis = enableEllipsis && !expanded;\n  // Shared prop to reduce bundle size\n  const {\n    rows = 1\n  } = ellipsisConfig;\n  const needMeasureEllipsis = React.useMemo(() =>\n  // Disable ellipsis\n  !mergedEnableEllipsis ||\n  // Provide suffix\n  ellipsisConfig.suffix !== undefined || ellipsisConfig.onEllipsis ||\n  // Can't use css ellipsis since we need to provide the place for button\n  ellipsisConfig.expandable || enableEdit || enableCopy, [mergedEnableEllipsis, ellipsisConfig, enableEdit, enableCopy]);\n  useIsomorphicLayoutEffect(() => {\n    if (enableEllipsis && !needMeasureEllipsis) {\n      setIsLineClampSupport(isStyleSupport('webkitLineClamp'));\n      setIsTextOverflowSupport(isStyleSupport('textOverflow'));\n    }\n  }, [needMeasureEllipsis, enableEllipsis]);\n  const cssEllipsis = React.useMemo(() => {\n    if (needMeasureEllipsis) {\n      return false;\n    }\n    if (rows === 1) {\n      return isTextOverflowSupport;\n    }\n    return isLineClampSupport;\n  }, [needMeasureEllipsis, isTextOverflowSupport, isLineClampSupport]);\n  const isMergedEllipsis = mergedEnableEllipsis && (cssEllipsis ? isNativeEllipsis : isJsEllipsis);\n  const cssTextOverflow = mergedEnableEllipsis && rows === 1 && cssEllipsis;\n  const cssLineClamp = mergedEnableEllipsis && rows > 1 && cssEllipsis;\n  // >>>>> Expand\n  const onExpandClick = e => {\n    var _a;\n    setExpanded(true);\n    (_a = ellipsisConfig.onExpand) === null || _a === void 0 ? void 0 : _a.call(ellipsisConfig, e);\n  };\n  const [ellipsisWidth, setEllipsisWidth] = React.useState(0);\n  const [ellipsisFontSize, setEllipsisFontSize] = React.useState(0);\n  const onResize = (_ref2, element) => {\n    let {\n      offsetWidth\n    } = _ref2;\n    var _a;\n    setEllipsisWidth(offsetWidth);\n    setEllipsisFontSize(parseInt((_a = window.getComputedStyle) === null || _a === void 0 ? void 0 : _a.call(window, element).fontSize, 10) || 0);\n  };\n  // >>>>> JS Ellipsis\n  const onJsEllipsis = jsEllipsis => {\n    var _a;\n    setIsJsEllipsis(jsEllipsis);\n    // Trigger if changed\n    if (isJsEllipsis !== jsEllipsis) {\n      (_a = ellipsisConfig.onEllipsis) === null || _a === void 0 ? void 0 : _a.call(ellipsisConfig, jsEllipsis);\n    }\n  };\n  // >>>>> Native ellipsis\n  React.useEffect(() => {\n    const textEle = typographyRef.current;\n    if (enableEllipsis && cssEllipsis && textEle) {\n      const currentEllipsis = cssLineClamp ? textEle.offsetHeight < textEle.scrollHeight : textEle.offsetWidth < textEle.scrollWidth;\n      if (isNativeEllipsis !== currentEllipsis) {\n        setIsNativeEllipsis(currentEllipsis);\n      }\n    }\n  }, [enableEllipsis, cssEllipsis, children, cssLineClamp, isNativeVisible]);\n  // https://github.com/ant-design/ant-design/issues/36786\n  // Use IntersectionObserver to check if element is invisible\n  React.useEffect(() => {\n    const textEle = typographyRef.current;\n    if (typeof IntersectionObserver === 'undefined' || !textEle || !cssEllipsis || !mergedEnableEllipsis) {\n      return;\n    }\n    /* eslint-disable-next-line compat/compat */\n    const observer = new IntersectionObserver(() => {\n      setIsNativeVisible(!!textEle.offsetParent);\n    });\n    observer.observe(textEle);\n    return () => {\n      observer.disconnect();\n    };\n  }, [cssEllipsis, mergedEnableEllipsis]);\n  // ========================== Tooltip ===========================\n  let tooltipProps = {};\n  if (ellipsisConfig.tooltip === true) {\n    tooltipProps = {\n      title: (_a = editConfig.text) !== null && _a !== void 0 ? _a : children\n    };\n  } else if ( /*#__PURE__*/React.isValidElement(ellipsisConfig.tooltip)) {\n    tooltipProps = {\n      title: ellipsisConfig.tooltip\n    };\n  } else if (typeof ellipsisConfig.tooltip === 'object') {\n    tooltipProps = Object.assign({\n      title: (_b = editConfig.text) !== null && _b !== void 0 ? _b : children\n    }, ellipsisConfig.tooltip);\n  } else {\n    tooltipProps = {\n      title: ellipsisConfig.tooltip\n    };\n  }\n  const topAriaLabel = React.useMemo(() => {\n    const isValid = val => ['string', 'number'].includes(typeof val);\n    if (!enableEllipsis || cssEllipsis) {\n      return undefined;\n    }\n    if (isValid(editConfig.text)) {\n      return editConfig.text;\n    }\n    if (isValid(children)) {\n      return children;\n    }\n    if (isValid(title)) {\n      return title;\n    }\n    if (isValid(tooltipProps.title)) {\n      return tooltipProps.title;\n    }\n    return undefined;\n  }, [enableEllipsis, cssEllipsis, title, tooltipProps.title, isMergedEllipsis]);\n  // =========================== Render ===========================\n  // >>>>>>>>>>> Editing input\n  if (editing) {\n    return /*#__PURE__*/React.createElement(Editable, {\n      value: (_c = editConfig.text) !== null && _c !== void 0 ? _c : typeof children === 'string' ? children : '',\n      onSave: onEditChange,\n      onCancel: onEditCancel,\n      onEnd: editConfig.onEnd,\n      prefixCls: prefixCls,\n      className: className,\n      style: style,\n      direction: direction,\n      component: component,\n      maxLength: editConfig.maxLength,\n      autoSize: editConfig.autoSize,\n      enterIcon: editConfig.enterIcon\n    });\n  }\n  // >>>>>>>>>>> Typography\n  // Expand\n  const renderExpand = () => {\n    const {\n      expandable,\n      symbol\n    } = ellipsisConfig;\n    if (!expandable) return null;\n    let expandContent;\n    if (symbol) {\n      expandContent = symbol;\n    } else {\n      expandContent = textLocale === null || textLocale === void 0 ? void 0 : textLocale.expand;\n    }\n    return /*#__PURE__*/React.createElement(\"a\", {\n      key: \"expand\",\n      className: `${prefixCls}-expand`,\n      onClick: onExpandClick,\n      \"aria-label\": textLocale === null || textLocale === void 0 ? void 0 : textLocale.expand\n    }, expandContent);\n  };\n  // Edit\n  const renderEdit = () => {\n    if (!enableEdit) return;\n    const {\n      icon,\n      tooltip\n    } = editConfig;\n    const editTitle = toArray(tooltip)[0] || (textLocale === null || textLocale === void 0 ? void 0 : textLocale.edit);\n    const ariaLabel = typeof editTitle === 'string' ? editTitle : '';\n    return triggerType.includes('icon') ? /*#__PURE__*/React.createElement(Tooltip, {\n      key: \"edit\",\n      title: tooltip === false ? '' : editTitle\n    }, /*#__PURE__*/React.createElement(TransButton, {\n      ref: editIconRef,\n      className: `${prefixCls}-edit`,\n      onClick: onEditClick,\n      \"aria-label\": ariaLabel\n    }, icon || /*#__PURE__*/React.createElement(EditOutlined, {\n      role: \"button\"\n    }))) : null;\n  };\n  // Copy\n  const renderCopy = () => {\n    if (!enableCopy) return;\n    const {\n      tooltips,\n      icon\n    } = copyConfig;\n    const tooltipNodes = toList(tooltips);\n    const iconNodes = toList(icon);\n    const copyTitle = copied ? getNode(tooltipNodes[1], textLocale === null || textLocale === void 0 ? void 0 : textLocale.copied) : getNode(tooltipNodes[0], textLocale === null || textLocale === void 0 ? void 0 : textLocale.copy);\n    const systemStr = copied ? textLocale === null || textLocale === void 0 ? void 0 : textLocale.copied : textLocale === null || textLocale === void 0 ? void 0 : textLocale.copy;\n    const ariaLabel = typeof copyTitle === 'string' ? copyTitle : systemStr;\n    return /*#__PURE__*/React.createElement(Tooltip, {\n      key: \"copy\",\n      title: copyTitle\n    }, /*#__PURE__*/React.createElement(TransButton, {\n      className: classNames(`${prefixCls}-copy`, copied && `${prefixCls}-copy-success`),\n      onClick: onCopyClick,\n      \"aria-label\": ariaLabel\n    }, copied ? getNode(iconNodes[1], /*#__PURE__*/React.createElement(CheckOutlined, null), true) : getNode(iconNodes[0], /*#__PURE__*/React.createElement(CopyOutlined, null), true)));\n  };\n  const renderOperations = renderExpanded => [renderExpanded && renderExpand(), renderEdit(), renderCopy()];\n  const renderEllipsis = needEllipsis => [needEllipsis && /*#__PURE__*/React.createElement(\"span\", {\n    \"aria-hidden\": true,\n    key: \"ellipsis\"\n  }, ELLIPSIS_STR), ellipsisConfig.suffix, renderOperations(needEllipsis)];\n  return /*#__PURE__*/React.createElement(ResizeObserver, {\n    onResize: onResize,\n    disabled: !mergedEnableEllipsis || cssEllipsis\n  }, resizeRef => /*#__PURE__*/React.createElement(EllipsisTooltip, {\n    tooltipProps: tooltipProps,\n    enabledEllipsis: mergedEnableEllipsis,\n    isEllipsis: isMergedEllipsis\n  }, /*#__PURE__*/React.createElement(Typography, Object.assign({\n    className: classNames({\n      [`${prefixCls}-${type}`]: type,\n      [`${prefixCls}-disabled`]: disabled,\n      [`${prefixCls}-ellipsis`]: enableEllipsis,\n      [`${prefixCls}-single-line`]: mergedEnableEllipsis && rows === 1,\n      [`${prefixCls}-ellipsis-single-line`]: cssTextOverflow,\n      [`${prefixCls}-ellipsis-multiple-line`]: cssLineClamp\n    }, className),\n    prefixCls: customizePrefixCls,\n    style: Object.assign(Object.assign({}, style), {\n      WebkitLineClamp: cssLineClamp ? rows : undefined\n    }),\n    component: component,\n    ref: composeRef(resizeRef, typographyRef, ref),\n    direction: direction,\n    onClick: triggerType.includes('text') ? onEditClick : undefined,\n    \"aria-label\": topAriaLabel === null || topAriaLabel === void 0 ? void 0 : topAriaLabel.toString(),\n    title: title\n  }, textProps), /*#__PURE__*/React.createElement(Ellipsis, {\n    enabledMeasure: mergedEnableEllipsis && !cssEllipsis,\n    text: children,\n    rows: rows,\n    width: ellipsisWidth,\n    fontSize: ellipsisFontSize,\n    onEllipsis: onJsEllipsis\n  }, (node, needEllipsis) => {\n    let renderNode = node;\n    if (node.length && needEllipsis && topAriaLabel) {\n      renderNode = /*#__PURE__*/React.createElement(\"span\", {\n        key: \"show-content\",\n        \"aria-hidden\": true\n      }, renderNode);\n    }\n    const wrappedContext = wrapperDecorations(props, /*#__PURE__*/React.createElement(React.Fragment, null, renderNode, renderEllipsis(needEllipsis)));\n    return wrappedContext;\n  }))));\n});\nexport default Base;", "var __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport * as React from 'react';\nimport warning from '../_util/warning';\nimport Base from './Base';\nconst Link = /*#__PURE__*/React.forwardRef((_a, ref) => {\n  var {\n      ellipsis,\n      rel\n    } = _a,\n    restProps = __rest(_a, [\"ellipsis\", \"rel\"]);\n  process.env.NODE_ENV !== \"production\" ? warning(typeof ellipsis !== 'object', 'Typography.Link', '`ellipsis` only supports boolean value.') : void 0;\n  const mergedProps = Object.assign(Object.assign({}, restProps), {\n    rel: rel === undefined && restProps.target === '_blank' ? 'noopener noreferrer' : rel\n  });\n  // @ts-expect-error: https://github.com/ant-design/ant-design/issues/26622\n  delete mergedProps.navigate;\n  return /*#__PURE__*/React.createElement(Base, Object.assign({}, mergedProps, {\n    ref: ref,\n    ellipsis: !!ellipsis,\n    component: \"a\"\n  }));\n});\nexport default Link;", "import * as React from 'react';\nimport Base from './Base';\nconst Paragraph = /*#__PURE__*/React.forwardRef((props, ref) => /*#__PURE__*/React.createElement(Base, Object.assign({\n  ref: ref\n}, props, {\n  component: \"div\"\n})));\nexport default Paragraph;", "var __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport omit from \"rc-util/es/omit\";\nimport * as React from 'react';\nimport warning from '../_util/warning';\nimport Base from './Base';\nconst Text = (_a, ref) => {\n  var {\n      ellipsis\n    } = _a,\n    restProps = __rest(_a, [\"ellipsis\"]);\n  const mergedEllipsis = React.useMemo(() => {\n    if (ellipsis && typeof ellipsis === 'object') {\n      return omit(ellipsis, ['expandable', 'rows']);\n    }\n    return ellipsis;\n  }, [ellipsis]);\n  process.env.NODE_ENV !== \"production\" ? warning(typeof ellipsis !== 'object' || !ellipsis || !('expandable' in ellipsis) && !('rows' in ellipsis), 'Typography.Text', '`ellipsis` do not support `expandable` or `rows` props.') : void 0;\n  return /*#__PURE__*/React.createElement(Base, Object.assign({\n    ref: ref\n  }, restProps, {\n    ellipsis: mergedEllipsis,\n    component: \"span\"\n  }));\n};\nexport default /*#__PURE__*/React.forwardRef(Text);", "var __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport * as React from 'react';\nimport warning from '../_util/warning';\nimport Base from './Base';\nconst TITLE_ELE_LIST = [1, 2, 3, 4, 5];\nconst Title = /*#__PURE__*/React.forwardRef((props, ref) => {\n  const {\n      level = 1\n    } = props,\n    restProps = __rest(props, [\"level\"]);\n  let component;\n  if (TITLE_ELE_LIST.includes(level)) {\n    component = `h${level}`;\n  } else {\n    process.env.NODE_ENV !== \"production\" ? warning(false, 'Typography.Title', 'Title only accept `1 | 2 | 3 | 4 | 5` as `level` value. And `5` need 4.6.0+ version.') : void 0;\n    component = 'h1';\n  }\n  return /*#__PURE__*/React.createElement(Base, Object.assign({\n    ref: ref\n  }, restProps, {\n    component: component\n  }));\n});\nexport default Title;", "'use client';\n\nimport Link from './<PERSON>';\nimport Paragraph from './Paragraph';\nimport Text from './Text';\nimport Title from './Title';\nimport OriginTypography from './Typography';\nconst Typography = OriginTypography;\nTypography.Text = Text;\nTypography.Link = Link;\nTypography.Title = Title;\nTypography.Paragraph = Paragraph;\nexport default Typography;", "\"use strict\";\n\nvar deselectCurrent = require(\"toggle-selection\");\n\nvar clipboardToIE11Formatting = {\n  \"text/plain\": \"Text\",\n  \"text/html\": \"Url\",\n  \"default\": \"Text\"\n}\n\nvar defaultMessage = \"Copy to clipboard: #{key}, Enter\";\n\nfunction format(message) {\n  var copyKey = (/mac os x/i.test(navigator.userAgent) ? \"⌘\" : \"Ctrl\") + \"+C\";\n  return message.replace(/#{\\s*key\\s*}/g, copyKey);\n}\n\nfunction copy(text, options) {\n  var debug,\n    message,\n    reselectPrevious,\n    range,\n    selection,\n    mark,\n    success = false;\n  if (!options) {\n    options = {};\n  }\n  debug = options.debug || false;\n  try {\n    reselectPrevious = deselectCurrent();\n\n    range = document.createRange();\n    selection = document.getSelection();\n\n    mark = document.createElement(\"span\");\n    mark.textContent = text;\n    // avoid screen readers from reading out loud the text\n    mark.ariaHidden = \"true\"\n    // reset user styles for span element\n    mark.style.all = \"unset\";\n    // prevents scrolling to the end of the page\n    mark.style.position = \"fixed\";\n    mark.style.top = 0;\n    mark.style.clip = \"rect(0, 0, 0, 0)\";\n    // used to preserve spaces and line breaks\n    mark.style.whiteSpace = \"pre\";\n    // do not inherit user-select (it may be `none`)\n    mark.style.webkitUserSelect = \"text\";\n    mark.style.MozUserSelect = \"text\";\n    mark.style.msUserSelect = \"text\";\n    mark.style.userSelect = \"text\";\n    mark.addEventListener(\"copy\", function(e) {\n      e.stopPropagation();\n      if (options.format) {\n        e.preventDefault();\n        if (typeof e.clipboardData === \"undefined\") { // IE 11\n          debug && console.warn(\"unable to use e.clipboardData\");\n          debug && console.warn(\"trying IE specific stuff\");\n          window.clipboardData.clearData();\n          var format = clipboardToIE11Formatting[options.format] || clipboardToIE11Formatting[\"default\"]\n          window.clipboardData.setData(format, text);\n        } else { // all other browsers\n          e.clipboardData.clearData();\n          e.clipboardData.setData(options.format, text);\n        }\n      }\n      if (options.onCopy) {\n        e.preventDefault();\n        options.onCopy(e.clipboardData);\n      }\n    });\n\n    document.body.appendChild(mark);\n\n    range.selectNodeContents(mark);\n    selection.addRange(range);\n\n    var successful = document.execCommand(\"copy\");\n    if (!successful) {\n      throw new Error(\"copy command was unsuccessful\");\n    }\n    success = true;\n  } catch (err) {\n    debug && console.error(\"unable to copy using execCommand: \", err);\n    debug && console.warn(\"trying IE specific stuff\");\n    try {\n      window.clipboardData.setData(options.format || \"text\", text);\n      options.onCopy && options.onCopy(window.clipboardData);\n      success = true;\n    } catch (err) {\n      debug && console.error(\"unable to copy using clipboardData: \", err);\n      debug && console.error(\"falling back to prompt\");\n      message = format(\"message\" in options ? options.message : defaultMessage);\n      window.prompt(message, text);\n    }\n  } finally {\n    if (selection) {\n      if (typeof selection.removeRange == \"function\") {\n        selection.removeRange(range);\n      } else {\n        selection.removeAllRanges();\n      }\n    }\n\n    if (mark) {\n      document.body.removeChild(mark);\n    }\n    reselectPrevious();\n  }\n\n  return success;\n}\n\nmodule.exports = copy;\n", "import canUseDom from \"./canUseDom\";\nvar isStyleNameSupport = function isStyleNameSupport(styleName) {\n  if (canUseDom() && window.document.documentElement) {\n    var styleNameList = Array.isArray(styleName) ? styleName : [styleName];\n    var documentElement = window.document.documentElement;\n    return styleNameList.some(function (name) {\n      return name in documentElement.style;\n    });\n  }\n  return false;\n};\nvar isStyleValueSupport = function isStyleValueSupport(styleName, value) {\n  if (!isStyleNameSupport(styleName)) {\n    return false;\n  }\n  var ele = document.createElement('div');\n  var origin = ele.style[styleName];\n  ele.style[styleName] = value;\n  return ele.style[styleName] !== origin;\n};\nexport function isStyleSupport(styleName, styleValue) {\n  if (!Array.isArray(styleName) && styleValue !== undefined) {\n    return isStyleValueSupport(styleName, styleValue);\n  }\n  return isStyleNameSupport(styleName);\n}", "\nmodule.exports = function () {\n  var selection = document.getSelection();\n  if (!selection.rangeCount) {\n    return function () {};\n  }\n  var active = document.activeElement;\n\n  var ranges = [];\n  for (var i = 0; i < selection.rangeCount; i++) {\n    ranges.push(selection.getRangeAt(i));\n  }\n\n  switch (active.tagName.toUpperCase()) { // .toUpperCase handles XHTML\n    case 'INPUT':\n    case 'TEXTAREA':\n      active.blur();\n      break;\n\n    default:\n      active = null;\n      break;\n  }\n\n  selection.removeAllRanges();\n  return function () {\n    selection.type === 'Caret' &&\n    selection.removeAllRanges();\n\n    if (!selection.rangeCount) {\n      ranges.forEach(function(range) {\n        selection.addRange(range);\n      });\n    }\n\n    active &&\n    active.focus();\n  };\n};\n"], "names": ["SpaceContext", "React", "latestIndex", "horizontalSize", "verticalSize", "supportFlexGap", "SpaceContextProvider", "Provider", "_ref", "className", "direction", "index", "marginDirection", "children", "split", "wrap", "style", "customStyle", "marginBottom", "Object", "assign", "paddingBottom", "undefined", "concat", "__rest", "s", "e", "t", "p", "prototype", "hasOwnProperty", "call", "indexOf", "getOwnPropertySymbols", "i", "length", "propertyIsEnumerable", "spaceSize", "small", "middle", "large", "Space", "props", "ref", "_a", "_b", "getPrefixCls", "space", "directionConfig", "ConfigContext", "size", "align", "rootClassName", "prefixCls", "customizePrefixCls", "classNames", "customClassNames", "styles", "otherProps", "useFlexGapSupport", "Array", "isArray", "map", "item", "getNumberSize", "childNodes", "toArray", "keepEmpty", "mergedAlign", "wrapSSR", "hashId", "useStyle", "cn", "itemClassName", "nodes", "child", "key", "<PERSON><PERSON>", "spaceContext", "gapStyle", "flexWrap", "columnGap", "rowGap", "value", "CompoundedSpace", "Compact", "operationUnit", "token", "color", "colorLink", "textDecoration", "outline", "cursor", "transition", "motionDurationSlow", "colorLinkHover", "colorLinkActive", "CopyOutlined", "AntdIcon", "_extends", "icon", "CopyOutlinedSvg", "EditOutlined", "EditOutlinedSvg", "inlineStyle", "border", "background", "padding", "lineHeight", "display", "TransButton", "noStyle", "disabled", "restProps", "mergedStyle", "pointerEvents", "role", "tabIndex", "onKeyDown", "event", "keyCode", "KeyCode", "ENTER", "preventDefault", "onKeyUp", "onClick", "EnterOutlined", "EnterOutlinedSvg", "getTitleStyles", "for<PERSON>ach", "headingLevel", "getTitleStyle", "fontSize", "titleMarginBottom", "fontWeightStrong", "fontWeight", "colorTextHeading", "getLinkStyles", "componentCls", "linkDecoration", "linkHoverDecoration", "colorTextDisabled", "getResetStyles", "code", "margin", "paddingInline", "paddingBlock", "fontFamily", "fontFamilyCode", "borderRadius", "kbd", "borderBottomWidth", "mark", "backgroundColor", "gold", "textDecorationSkipInk", "strong", "marginInline", "marginBlock", "li", "ul", "listStyleType", "ol", "pre", "whiteSpace", "wordWrap", "blockquote", "borderInlineStart", "opacity", "getEditableStyles", "inputShift", "initInputToken", "inputPaddingVertical", "position", "insetInlineStart", "paddingSM", "marginTop", "insetInlineEnd", "marginXS", "insetBlockEnd", "colorTextDescription", "fontStyle", "textarea", "MozTransition", "height", "getCopyableStyles", "colorSuccess", "genTypographyStyle", "titleMarginTop", "colorText", "wordBreak", "colorWarning", "colorError", "colorErrorActive", "colorErrorHover", "userSelect", "marginInlineStart", "marginXXS", "max<PERSON><PERSON><PERSON>", "overflow", "textOverflow", "verticalAlign", "WebkitLineClamp", "WebkitBoxOrient", "genComponentStyleHook", "aria<PERSON><PERSON><PERSON>", "max<PERSON><PERSON><PERSON>", "autoSize", "onSave", "onCancel", "onEnd", "component", "enterIcon", "inComposition", "lastKeyCode", "current", "setCurrent", "resizableTextArea", "textArea", "focus", "setSelectionRange", "confirmChange", "trim", "textClassName", "textAreaClassName", "TextArea", "onChange", "target", "replace", "_ref2", "_ref3", "ctrl<PERSON>ey", "altKey", "metaKey", "shift<PERSON>ey", "ESC", "onCompositionStart", "onCompositionEnd", "onBlur", "rows", "cloneElement", "Typography", "Component", "setContentRef", "typographyDirection", "contextDirection", "typography", "mergedRef", "composeRef", "componentClassName", "useMergedConfig", "propConfig", "templateConfig", "support", "useUpdatedEffect", "callback", "conditions", "mountRef", "cuttable", "node", "type", "sliceNodes", "nodeList", "len", "currLen", "currentNodeList", "nextLen", "String", "restLen", "push", "slice", "enabledMeasure", "text", "width", "onEllipsis", "startLen", "midLen", "endLen", "set<PERSON><PERSON><PERSON><PERSON><PERSON>", "walkingState", "setWalkingState", "singleRowHeight", "setSingleRowHeight", "singleRowRef", "midRowRef", "totalLen", "getNodesLen", "mergedChildren", "useIsomorphicLayoutEffect", "Math", "ceil", "offsetHeight", "midHeight", "nextStartLen", "nextEndLen", "nextMidLen", "measureStyle", "renderMeasure", "content", "left", "top", "zIndex", "visibility", "floor", "renderMeasureSlice", "sliceNodeList", "enabledEllipsis", "isEllipsis", "tooltipProps", "title", "<PERSON><PERSON><PERSON>", "open", "getNode", "dom", "defaultNode", "needDom", "toList", "val", "Base", "_c", "ellipsis", "editable", "copyable", "textLocale", "useLocale", "typographyRef", "editIconRef", "textProps", "omit", "enableEdit", "editConfig", "editing", "setEditing", "useMergedState", "triggerType", "triggerEdit", "edit", "onStart", "onEditClick", "onEditChange", "onEditCancel", "enableCopy", "copyConfig", "copied", "setCopied", "copyIdRef", "copyOptions", "format", "cleanCopyId", "clearTimeout", "onCopyClick", "stopPropagation", "copy", "setTimeout", "onCopy", "isLineClampSupport", "setIsLineClampSupport", "isTextOverflowSupport", "setIsTextOverflowSupport", "expanded", "setExpanded", "isJsEllipsis", "setIsJsEllipsis", "isNativeEllipsis", "setIsNativeEllipsis", "isNativeVisible", "setIsNativeVisible", "enableEllipsis", "ellipsisConfig", "expandable", "mergedEnableEllipsis", "needMeasureEllipsis", "suffix", "isStyleSupport", "cssEllipsis", "isMergedEllipsis", "cssTextOverflow", "cssLineClamp", "onExpandClick", "onExpand", "ellip<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "ellipsisFontSize", "setEllipsisFontSize", "onJsEllipsis", "js<PERSON><PERSON><PERSON>", "textEle", "currentEllipsis", "scrollHeight", "offsetWidth", "scrollWidth", "IntersectionObserver", "observer", "offsetParent", "observe", "disconnect", "tooltip", "topAriaLabel", "<PERSON><PERSON><PERSON><PERSON>", "includes", "Editable", "renderExpand", "symbol", "expandContent", "expand", "renderEdit", "editTitle", "renderCopy", "tooltips", "tooltipNodes", "iconNodes", "copyTitle", "systemStr", "CheckOutlined", "ResizeObserver", "onResize", "element", "parseInt", "window", "getComputedStyle", "resizeRef", "EllipsisTooltip", "toString", "El<PERSON><PERSON>", "needEllipsis", "renderNode", "wrappedContext", "underline", "delete", "del", "keyboard", "italic", "currentC<PERSON>nt", "tag", "needed", "wrapperDecorations", "renderExpanded", "renderEllipsis", "rel", "mergedProps", "navigate", "Text", "mergedEllipsis", "TITLE_ELE_LIST", "level", "OriginTypography", "Link", "Title", "Paragraph", "deselectCurrent", "require", "clipboardToIE11Formatting", "module", "exports", "options", "debug", "message", "reselectPrevious", "range", "selection", "success", "document", "createRange", "getSelection", "createElement", "textContent", "ariaHidden", "all", "clip", "webkitUserSelect", "MozUserSelect", "msUserSelect", "addEventListener", "clipboardData", "console", "warn", "clearData", "setData", "body", "append<PERSON><PERSON><PERSON>", "selectNodeContents", "addRange", "execCommand", "Error", "err", "error", "copyKey", "test", "navigator", "userAgent", "prompt", "<PERSON><PERSON><PERSON><PERSON>", "removeAllRanges", "<PERSON><PERSON><PERSON><PERSON>", "isStyleNameSupport", "styleName", "canUseDom", "documentElement", "styleNameList", "some", "name", "isStyleValueSupport", "ele", "origin", "styleValue", "rangeCount", "active", "activeElement", "ranges", "getRangeAt", "tagName", "toUpperCase", "blur"], "sourceRoot": ""}