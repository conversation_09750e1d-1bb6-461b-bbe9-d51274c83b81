"use strict";(self.webpackChunkclient=self.webpackChunkclient||[]).push([[332],{5332:(e,t,a)=>{a.r(t),a.d(t,{default:()=>b});var s=a(2791),r=a(6042),n=a(1413),i=a(5526),o=a(184);const l=e=>{let{achievement:t,size:a="medium",showDetails:s=!0,className:n="",onClick:l=null}=e;const c={first_quiz:{icon:i.jsT,title:"First Steps",description:"Completed your first quiz",color:"from-blue-400 to-blue-600",bgColor:"bg-blue-50",textColor:"text-blue-700"},perfect_score:{icon:i.gBl,title:"Perfect Score",description:"Achieved 100% on a quiz",color:"from-yellow-400 to-yellow-600",bgColor:"bg-yellow-50",textColor:"text-yellow-700"},streak_5:{icon:i.p8m,title:"Hot Streak",description:"5 correct answers in a row",color:"from-orange-400 to-red-500",bgColor:"bg-orange-50",textColor:"text-orange-700"},streak_10:{icon:i.p8m,title:"Fire Streak",description:"10 correct answers in a row",color:"from-red-400 to-red-600",bgColor:"bg-red-50",textColor:"text-red-700"},streak_20:{icon:i.p8m,title:"Blazing Streak",description:"20 correct answers in a row",color:"from-red-500 to-purple-600",bgColor:"bg-red-50",textColor:"text-red-700"},subject_master:{icon:i.PWB,title:"Subject Master",description:"Mastered a subject",color:"from-purple-400 to-purple-600",bgColor:"bg-purple-50",textColor:"text-purple-700"},speed_demon:{icon:i.lbY,title:"Speed Demon",description:"Completed quiz in record time",color:"from-cyan-400 to-blue-500",bgColor:"bg-cyan-50",textColor:"text-cyan-700"},consistent_learner:{icon:i.Chd,title:"Consistent Learner",description:"Maintained consistent performance",color:"from-green-400 to-green-600",bgColor:"bg-green-50",textColor:"text-green-700"},improvement_star:{icon:i.ehl,title:"Improvement Star",description:"Showed remarkable improvement",color:"from-indigo-400 to-indigo-600",bgColor:"bg-indigo-50",textColor:"text-indigo-700"}},d=c[t.type]||c.first_quiz,m={small:{container:"w-12 h-12",icon:"w-6 h-6",text:"text-xs",padding:"p-2"},medium:{container:"w-16 h-16",icon:"w-8 h-8",text:"text-sm",padding:"p-3"},large:{container:"w-20 h-20",icon:"w-10 h-10",text:"text-base",padding:"p-4"}}[a],x=d.icon,u={hidden:{opacity:0},visible:{opacity:[.5,1,.5],transition:{duration:2,repeat:1/0,ease:"easeInOut"}}};return(0,o.jsxs)(r.E.div,{variants:{hidden:{opacity:0,scale:.8,rotate:-10},visible:{opacity:1,scale:1,rotate:0,transition:{type:"spring",stiffness:300,damping:20}},hover:{scale:1.1,rotate:5,transition:{type:"spring",stiffness:400,damping:10}}},initial:"hidden",animate:"visible",whileHover:"hover",onClick:l,className:"\n        relative cursor-pointer group\n        ".concat(n,"\n      "),children:[(0,o.jsx)(r.E.div,{variants:u,className:"\n          absolute inset-0 rounded-full blur-md opacity-75\n          bg-gradient-to-r ".concat(d.color,"\n          ").concat(m.container,"\n        ")}),(0,o.jsx)("div",{className:"\n        relative flex items-center justify-center rounded-full\n        bg-gradient-to-r ".concat(d.color,"\n        ").concat(m.container," ").concat(m.padding,"\n        shadow-lg border-2 border-white\n        group-hover:shadow-xl transition-shadow duration-200\n      "),children:(0,o.jsx)(x,{className:"".concat(m.icon," text-white drop-shadow-sm")})}),s&&(0,o.jsxs)(r.E.div,{initial:{opacity:0,y:10,scale:.9},whileHover:{opacity:1,y:0,scale:1},className:"\n            absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2\n            ".concat(d.bgColor," ").concat(d.textColor,"\n            px-3 py-2 rounded-lg shadow-lg border\n            whitespace-nowrap z-10\n            pointer-events-none\n            ").concat(m.text,"\n          "),children:[(0,o.jsx)("div",{className:"font-semibold",children:d.title}),(0,o.jsx)("div",{className:"text-xs opacity-75",children:d.description}),t.subject&&(0,o.jsxs)("div",{className:"text-xs font-medium mt-1",children:["Subject: ",t.subject]}),t.earnedAt&&(0,o.jsx)("div",{className:"text-xs opacity-60 mt-1",children:new Date(t.earnedAt).toLocaleDateString()}),(0,o.jsx)("div",{className:"\n            absolute top-full left-1/2 transform -translate-x-1/2\n            w-0 h-0 border-l-4 border-r-4 border-t-4\n            border-l-transparent border-r-transparent\n            ".concat(d.bgColor.replace("bg-","border-t-"),"\n          ")})]})]})},c=e=>{var t,a,s;let{achievements:n=[],maxDisplay:i=5,size:c="medium",layout:d="horizontal",className:m=""}=e;const x=n.slice(0,i),u=Math.max(0,n.length-i),p={small:{container:"w-12 h-12",text:"text-xs",padding:"p-2"},medium:{container:"w-16 h-16",text:"text-sm",padding:"p-3"},large:{container:"w-20 h-20",text:"text-base",padding:"p-4"}},h="grid"===d?"grid grid-cols-3 sm:grid-cols-4 md:grid-cols-5 gap-4":"flex flex-wrap gap-2";return(0,o.jsxs)(r.E.div,{variants:{hidden:{opacity:0},visible:{opacity:1,transition:{staggerChildren:.1}}},initial:"hidden",animate:"visible",className:"".concat(h," ").concat(m),children:[x.map(((e,t)=>(0,o.jsx)(l,{achievement:e,size:c,showDetails:!0},"".concat(e.type,"-").concat(t)))),u>0&&(0,o.jsxs)(r.E.div,{variants:{hidden:{opacity:0,scale:.8},visible:{opacity:1,scale:1}},className:"\n            flex items-center justify-center rounded-full\n            bg-gray-100 border-2 border-gray-200\n            ".concat(null===(t=p[c])||void 0===t?void 0:t.container," ").concat(null===(a=p[c])||void 0===a?void 0:a.padding,"\n            text-gray-600 font-semibold\n            ").concat(null===(s=p[c])||void 0===s?void 0:s.text,"\n          "),children:["+",u]})]})};var d=a(3791);const m=e=>{let{currentXP:t=0,totalXP:a=0,currentLevel:n=1,xpToNextLevel:l=100,showAnimation:c=!0,size:m="medium",showLevel:x=!0,showXPNumbers:u=!0,className:p=""}=e;const[h,g]=(0,s.useState)(0),[b,f]=(0,s.useState)(!1),v=a-l,y=t-v,w=a-v,j=Math.min(100,Math.max(0,y/w*100)),N={small:{height:"h-2",levelSize:"w-6 h-6 text-xs",textSize:"text-xs",padding:"px-2 py-1"},medium:{height:"h-3",levelSize:"w-8 h-8 text-sm",textSize:"text-sm",padding:"px-3 py-2"},large:{height:"h-4",levelSize:"w-10 h-10 text-base",textSize:"text-base",padding:"px-4 py-3"}}[m];(0,s.useEffect)((()=>{if(c){const e=setTimeout((()=>{g(t)}),100);return()=>clearTimeout(e)}g(t)}),[t,c]);return(0,o.jsxs)("div",{className:"xp-progress-container ".concat(p),children:[(0,o.jsx)(d.M,{children:b&&(0,o.jsx)(r.E.div,{initial:{opacity:0,scale:.5},animate:{opacity:1,scale:1},exit:{opacity:0,scale:.5},className:"fixed inset-0 z-50 flex items-center justify-center bg-black/50 backdrop-blur-sm",children:(0,o.jsxs)(r.E.div,{initial:{y:-50,opacity:0},animate:{y:0,opacity:1},exit:{y:50,opacity:0},className:"bg-gradient-to-r from-yellow-400 to-orange-500 text-white px-8 py-6 rounded-2xl shadow-2xl text-center",children:[(0,o.jsx)(r.E.div,{animate:{rotate:360},transition:{duration:1,repeat:1/0,ease:"linear"},className:"text-4xl mb-2",children:"\ud83c\udf89"}),(0,o.jsx)("h2",{className:"text-2xl font-bold mb-1",children:"LEVEL UP!"}),(0,o.jsxs)("p",{className:"text-lg",children:["You reached Level ",n,"!"]})]})})}),(0,o.jsxs)("div",{className:"flex items-center space-x-3",children:[x&&(0,o.jsxs)(r.E.div,{whileHover:{scale:1.1},whileTap:{scale:.95},className:"\n              ".concat(N.levelSize," rounded-full flex items-center justify-center\n              bg-gradient-to-r ").concat((k=n,k>=10?"from-purple-600 to-pink-600":k>=8?"from-yellow-500 to-orange-600":k>=6?"from-green-500 to-blue-600":k>=4?"from-blue-500 to-purple-600":k>=2?"from-indigo-500 to-blue-600":"from-gray-500 to-gray-600"),"\n              text-white font-bold shadow-lg border-2 border-white\n              relative overflow-hidden\n            "),children:[(0,o.jsx)("div",{className:"absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent animate-pulse"}),(0,o.jsx)("span",{className:"relative z-10 ".concat(N.textSize),children:n}),n>=10&&(0,o.jsx)(i.gBl,{className:"absolute top-0 right-0 w-3 h-3 text-yellow-300"})]}),(0,o.jsxs)("div",{className:"flex-1",children:[u&&(0,o.jsxs)("div",{className:"flex justify-between items-center mb-1 ".concat(N.textSize," text-gray-600"),children:[(0,o.jsxs)("span",{className:"font-medium",children:[h.toLocaleString()," XP"]}),(0,o.jsx)("span",{className:"text-gray-500",children:l>0?"".concat(l," to next level"):"Max Level"})]}),(0,o.jsxs)("div",{className:"\n            relative ".concat(N.height," bg-gray-200 rounded-full overflow-hidden\n            shadow-inner border border-gray-300\n          "),children:[(0,o.jsx)("div",{className:"absolute inset-0 bg-gradient-to-r from-gray-100 to-gray-200"}),(0,o.jsxs)(r.E.div,{initial:{width:0},animate:{width:"".concat(j,"%")},transition:{duration:1,ease:"easeOut"},className:"\n                absolute inset-y-0 left-0 rounded-full\n                bg-gradient-to-r ".concat(j>=90?"from-yellow-400 via-orange-500 to-red-500":j>=70?"from-green-400 via-blue-500 to-purple-500":j>=50?"from-blue-400 via-purple-500 to-pink-500":j>=25?"from-indigo-400 via-blue-500 to-cyan-500":"from-gray-400 via-gray-500 to-gray-600","\n                shadow-lg relative overflow-hidden\n              "),children:[(0,o.jsx)(r.E.div,{animate:{x:["0%","100%"]},transition:{duration:2,repeat:1/0,ease:"linear"},className:"absolute inset-0 bg-gradient-to-r from-transparent via-white/30 to-transparent transform skew-x-12"}),(0,o.jsx)("div",{className:"absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent animate-pulse"})]}),(0,o.jsx)(d.M,{children:c&&(0,o.jsx)(r.E.div,{initial:{opacity:0,y:0},animate:{opacity:[0,1,0],y:-20},exit:{opacity:0},transition:{duration:1},className:"absolute right-2 top-1/2 transform -translate-y-1/2",children:(0,o.jsx)(i.lbY,{className:"w-4 h-4 text-yellow-400"})})})]}),l>0&&(0,o.jsxs)("div",{className:"flex justify-between mt-1",children:[(0,o.jsxs)("span",{className:"".concat(N.textSize," text-gray-500 font-medium"),children:["Level ",n]}),(0,o.jsxs)("span",{className:"".concat(N.textSize," text-gray-500 font-medium"),children:["Level ",n+1]})]})]}),n>1&&(0,o.jsxs)(r.E.div,{whileHover:{scale:1.1},className:"flex items-center space-x-1 bg-gradient-to-r from-yellow-100 to-orange-100 px-2 py-1 rounded-full border border-yellow-300",children:[(0,o.jsx)(i.p8m,{className:"w-3 h-3 text-orange-500"}),(0,o.jsxs)("span",{className:"text-xs font-medium text-orange-700",children:["+",10*(n-1),"% XP"]})]})]})]});var k},x=e=>{let{level:t=1,size:a="medium",showTitle:s=!1,showGlow:n=!0,animated:l=!0,className:c=""}=e;const d={small:{container:"w-8 h-8",text:"text-xs",icon:"w-3 h-3",titleText:"text-xs"},medium:{container:"w-12 h-12",text:"text-sm",icon:"w-4 h-4",titleText:"text-sm"},large:{container:"w-16 h-16",text:"text-lg",icon:"w-5 h-5",titleText:"text-base"},xl:{container:"w-20 h-20",text:"text-xl",icon:"w-6 h-6",titleText:"text-lg"}}[a],m=(e=>e>=10?{title:"Elite",icon:i.PWB,gradient:"from-purple-600 via-pink-600 to-red-600",glow:"shadow-purple-500/50",shape:"diamond",rarity:"mythic",animation:"rotate"}:e>=8?{title:"Legend",icon:i.gBl,gradient:"from-yellow-500 via-orange-500 to-red-500",glow:"shadow-yellow-500/50",shape:"star",rarity:"legendary",animation:"bounce"}:e>=6?{title:"Master",icon:i.ab0,gradient:"from-emerald-500 via-blue-500 to-purple-500",glow:"shadow-blue-500/50",shape:"crown",rarity:"epic",animation:"pulse"}:e>=4?{title:"Expert",icon:i.p8m,gradient:"from-blue-500 via-purple-500 to-pink-500",glow:"shadow-blue-500/50",shape:"hexagon",rarity:"rare",animation:"glow"}:e>=2?{title:"Student",icon:i.lbY,gradient:"from-indigo-500 via-blue-500 to-cyan-500",glow:"shadow-indigo-500/50",shape:"circle",rarity:"uncommon",animation:"pulse"}:{title:"Beginner",icon:i.jsT,gradient:"from-gray-500 to-gray-600",glow:"shadow-gray-500/50",shape:"circle",rarity:"common",animation:"none"})(t),x=m.icon,u={none:{},pulse:{scale:[1,1.05,1],transition:{duration:2,repeat:1/0}},bounce:{y:[0,-2,0],transition:{duration:1.5,repeat:1/0}},rotate:{rotate:[0,360],transition:{duration:3,repeat:1/0,ease:"linear"}},glow:{boxShadow:["0 0 10px rgba(59, 130, 246, 0.5)","0 0 20px rgba(59, 130, 246, 0.8)","0 0 10px rgba(59, 130, 246, 0.5)"],transition:{duration:2,repeat:1/0}}};return(0,o.jsxs)("div",{className:"level-badge-container ".concat(c),children:[(0,o.jsxs)(r.E.div,{variants:u[m.animation],animate:l?m.animation:"none",whileHover:{scale:1.1},whileTap:{scale:.95},className:"\n          ".concat(d.container," relative flex items-center justify-center\n          bg-gradient-to-br ").concat(m.gradient,"\n          ").concat((e=>{switch(e){case"diamond":return"transform rotate-45";case"star":return"clip-path-star";case"crown":return"clip-path-crown";case"hexagon":return"clip-path-hexagon";default:return"rounded-full"}})(m.shape),"\n          ").concat((e=>{switch(e){case"mythic":return"border-4 border-purple-400 shadow-2xl";case"legendary":return"border-3 border-yellow-400 shadow-xl";case"epic":return"border-2 border-blue-400 shadow-lg";case"rare":return"border-2 border-purple-300 shadow-md";case"uncommon":return"border border-blue-300 shadow-sm";default:return"border border-gray-300"}})(m.rarity),"\n          ").concat(n?"shadow-lg ".concat(m.glow):"","\n          cursor-pointer overflow-hidden\n        "),children:[t>=6&&(0,o.jsx)("div",{className:"absolute inset-0 opacity-20",children:(0,o.jsx)("div",{className:"absolute inset-0 bg-gradient-to-r from-transparent via-white to-transparent transform -skew-x-12 animate-pulse"})}),(0,o.jsx)("div",{className:"relative z-10 flex items-center justify-center text-white font-bold",children:t>=8?(0,o.jsx)(x,{className:"".concat(d.icon," drop-shadow-lg")}):(0,o.jsx)("span",{className:"".concat(d.text," drop-shadow-lg"),children:t})}),t>=8&&(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)(r.E.div,{animate:{scale:[0,1,0],rotate:[0,180,360]},transition:{duration:2,repeat:1/0,delay:0},className:"absolute top-1 right-1 w-1 h-1 bg-yellow-300 rounded-full"}),(0,o.jsx)(r.E.div,{animate:{scale:[0,1,0],rotate:[0,-180,-360]},transition:{duration:2,repeat:1/0,delay:.5},className:"absolute bottom-1 left-1 w-1 h-1 bg-white rounded-full"})]}),t>=10&&(0,o.jsx)("div",{className:"absolute -inset-1 bg-gradient-to-r from-purple-600 via-pink-600 to-purple-600 rounded-full opacity-75 blur-sm animate-pulse"})]}),s&&(0,o.jsxs)(r.E.div,{initial:{opacity:0,y:5},animate:{opacity:1,y:0},className:"mt-2 text-center",children:[(0,o.jsx)("p",{className:"".concat(d.titleText," font-semibold text-gray-700"),children:m.title}),(0,o.jsxs)("p",{className:"text-xs text-gray-500",children:["Level ",t]})]}),(0,o.jsxs)("div",{className:"absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-2 py-1 bg-gray-900 text-white text-xs rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap z-20",children:["Level ",t," - ",m.title,(0,o.jsx)("div",{className:"absolute top-full left-1/2 transform -translate-x-1/2 border-4 border-transparent border-t-gray-900"})]})]})},u=e=>{var t;let{achievement:a,size:s="medium",showTooltip:n=!0,animated:l=!0,showXP:c=!1,className:d=""}=e;if(!a)return null;const m={small:{container:"w-6 h-6",icon:"w-3 h-3",text:"text-xs"},medium:{container:"w-8 h-8",icon:"w-4 h-4",text:"text-sm"},large:{container:"w-12 h-12",icon:"w-6 h-6",text:"text-base"}}[s],x=(e=>{const t={first_quiz:i.Chd,perfect_score:i.ab0,streak_5:i.p8m,streak_10:i.p8m,streak_20:i.p8m,subject_master:i.PWB,speed_demon:i.lbY,consistent_learner:i.jsT,improvement_star:i.gBl,first_steps:i.Chd,quick_learner:i.lbY,perfectionist:i.ab0,on_fire:i.p8m,unstoppable:i.Wzz,math_master:i.Kkf,science_genius:i.Kkf,top_performer:i.PWB,helping_hand:i.HLl};return t[e]||t[a.type]||i.Vh6})(a.id),u=a.rarity?(e=>{const t={common:"from-gray-400 to-gray-600",uncommon:"from-green-400 to-green-600",rare:"from-blue-400 to-blue-600",epic:"from-purple-400 to-purple-600",legendary:"from-yellow-400 to-orange-500",mythic:"from-pink-500 to-purple-600"};return t[e]||t.common})(a.rarity):{first_quiz:"from-green-400 to-green-600",perfect_score:"from-purple-400 to-purple-600",streak_5:"from-orange-400 to-red-500",streak_10:"from-red-400 to-red-600",streak_20:"from-red-500 to-pink-600",subject_master:"from-yellow-400 to-yellow-600",speed_demon:"from-blue-400 to-blue-600",consistent_learner:"from-indigo-400 to-indigo-600",improvement_star:"from-pink-400 to-pink-600"}[a.type]||"from-gray-400 to-gray-600";return(0,o.jsxs)("div",{className:"achievement-badge relative group ".concat(d),children:[(0,o.jsxs)(r.E.div,{whileHover:{scale:1.1},whileTap:{scale:.95},animate:l?(e=>{switch(e){case"legendary":case"mythic":return{rotate:[0,5,-5,0],scale:[1,1.05,1],transition:{duration:2,repeat:1/0,repeatDelay:2}};case"epic":return{y:[0,-2,0],transition:{duration:2,repeat:1/0,repeatDelay:3}};case"rare":return{scale:[1,1.02,1],transition:{duration:3,repeat:1/0}};default:return{rotate:[0,2,-2,0],transition:{duration:4,repeat:1/0,repeatDelay:5}}}})(a.rarity):{},className:"\n                    ".concat(m.container," rounded-full flex items-center justify-center\n                    bg-gradient-to-br ").concat(u,"\n                    shadow-lg ").concat((e=>{const t={common:"shadow-gray-500/50",uncommon:"shadow-green-500/50",rare:"shadow-blue-500/50",epic:"shadow-purple-500/50",legendary:"shadow-yellow-500/50",mythic:"shadow-pink-500/50"};return t[e]||t.common})(a.rarity),"\n                    border-2 border-white\n                    cursor-pointer relative overflow-hidden\n                "),children:[a.rarity&&["rare","epic","legendary","mythic"].includes(a.rarity)&&(0,o.jsx)(r.E.div,{animate:{x:["-100%","100%"],transition:{duration:2,repeat:1/0,repeatDelay:4}},className:"absolute inset-0 bg-gradient-to-r from-transparent via-white/40 to-transparent transform skew-x-12"}),a.rarity&&["legendary","mythic"].includes(a.rarity)&&(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)(r.E.div,{animate:{scale:[0,1,0],rotate:[0,180,360]},transition:{duration:2,repeat:1/0,delay:0},className:"absolute top-0 right-0 w-1 h-1 bg-yellow-300 rounded-full"}),(0,o.jsx)(r.E.div,{animate:{scale:[0,1,0],rotate:[0,-180,-360]},transition:{duration:2,repeat:1/0,delay:1},className:"absolute bottom-0 left-0 w-1 h-1 bg-white rounded-full"})]}),(0,o.jsx)(x,{className:"".concat(m.icon," text-white drop-shadow-lg relative z-10")}),c&&a.xpReward&&(0,o.jsx)("div",{className:"absolute -top-1 -right-1 bg-yellow-500 text-white text-xs rounded-full w-4 h-4 flex items-center justify-center font-bold",children:a.xpReward})]}),n&&(0,o.jsxs)("div",{className:"absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-3 py-2 bg-gray-900 text-white text-xs rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap z-20 shadow-xl max-w-xs",children:[(0,o.jsx)("div",{className:"font-semibold",children:a.name||(null===(t=a.type)||void 0===t?void 0:t.replace(/_/g," ").replace(/\b\w/g,(e=>e.toUpperCase())))}),a.description&&(0,o.jsx)("div",{className:"text-gray-300 text-xs mt-1",children:a.description}),a.rarity&&(0,o.jsx)("div",{className:"text-xs mt-1 font-medium ".concat("mythic"===a.rarity?"text-pink-300":"legendary"===a.rarity?"text-yellow-300":"epic"===a.rarity?"text-purple-300":"rare"===a.rarity?"text-blue-300":"uncommon"===a.rarity?"text-green-300":"text-gray-300"),children:a.rarity.charAt(0).toUpperCase()+a.rarity.slice(1)}),a.xpReward&&(0,o.jsxs)("div",{className:"text-yellow-300 text-xs mt-1",children:["+",a.xpReward," XP"]}),a.earnedAt&&(0,o.jsxs)("div",{className:"text-gray-400 text-xs mt-1",children:["Earned: ",new Date(a.earnedAt).toLocaleDateString()]}),(0,o.jsx)("div",{className:"absolute top-full left-1/2 transform -translate-x-1/2 border-4 border-transparent border-t-gray-900"})]})]})};var p=a(2048);const h=e=>{let{user:t,rank:a,classRank:s,isCurrentUser:r=!1,layout:n="horizontal",size:l="medium",showStats:d=!0,className:h=""}=e;const g={small:{avatar:"w-12 h-12",text:"text-sm",subtext:"text-xs",padding:"p-3",spacing:"space-x-3"},medium:{avatar:"w-14 h-14",text:"text-base",subtext:"text-sm",padding:"p-4",spacing:"space-x-4"},large:{avatar:"w-16 h-16",text:"text-lg",subtext:"text-base",padding:"p-5",spacing:"space-x-5"}}[l],b=(()=>{const e=((null===t||void 0===t?void 0:t.subscriptionStatus)||(null===t||void 0===t?void 0:t.normalizedSubscriptionStatus)||"free").toLowerCase();return"active"===e||"premium"===e?{avatarClass:"ring-4 ring-yellow-400 ring-offset-2 ring-offset-white",badge:"status-premium",glow:"shadow-xl shadow-yellow-500/30 hover:shadow-yellow-500/40",statusText:"Premium",badgeIcon:"\ud83d\udc51",borderClass:"ring-2 ring-yellow-400",bgClass:"bg-gradient-to-r from-yellow-500 via-amber-500 to-orange-500 text-white shadow-lg border border-yellow-400/50",cardBg:"bg-gradient-to-br from-yellow-50 via-amber-50 to-orange-50",textColor:"text-yellow-700",borderColor:"border-yellow-200"}:"free"===e?{avatarClass:"ring-2 ring-blue-300 ring-offset-2 ring-offset-white",badge:"status-free",glow:"shadow-md shadow-blue-500/20 hover:shadow-blue-500/30",statusText:"Free",badgeIcon:"\ud83c\udd93",borderClass:"ring-2 ring-blue-400",bgClass:"bg-gradient-to-r from-blue-500 via-blue-600 to-indigo-600 text-white shadow-md border border-blue-400/50",cardBg:"bg-gradient-to-br from-blue-50 via-indigo-50 to-blue-50",textColor:"text-blue-700",borderColor:"border-blue-200"}:{avatarClass:"ring-2 ring-red-400 ring-offset-2 ring-offset-white opacity-75",badge:"status-expired",glow:"shadow-lg shadow-red-500/25 hover:shadow-red-500/35",statusText:"Expired",badgeIcon:"\u23f0",borderClass:"ring-2 ring-red-400",bgClass:"bg-gradient-to-r from-red-500 via-red-600 to-red-700 text-white shadow-lg border border-red-400/50",cardBg:"bg-gradient-to-br from-red-50 via-pink-50 to-red-50",textColor:"text-red-700",borderColor:"border-red-200"}})(),f=(()=>{const e=a||0;return 1===e?{icon:i.PWB,color:"text-yellow-500",bg:"bg-yellow-50"}:2===e?{icon:i.Vh6,color:"text-gray-400",bg:"bg-gray-50"}:3===e?{icon:i.gBl,color:"text-amber-600",bg:"bg-amber-50"}:e<=10&&e>0?{icon:i.jsT,color:"text-blue-500",bg:"bg-blue-50"}:{icon:null,color:"text-gray-500",bg:"bg-gray-50"}})(),v=f.icon;return"vertical"===n?(0,o.jsxs)("div",{className:"\n                    ranking-card flex flex-col items-center text-center ".concat(g.padding,"\n                    ").concat(b.cardBg||"bg-white"," rounded-xl border ").concat(b.borderColor||"border-gray-200","\n                    ").concat(b.glow," transition-all duration-300 hover:scale-105 hover:shadow-xl\n                    transform hover:-translate-y-1 animate-fadeInUp\n                    ").concat(r?"current-user-card ring-2 ring-blue-500 ring-offset-2":"","\n                    ").concat(h,"\n                "),children:[(0,o.jsxs)("div",{className:"flex items-center space-x-2 mb-3",children:[(0,o.jsx)("div",{className:"\n                        rank-badge flex items-center justify-center w-8 h-8 rounded-full\n                        ".concat(f.bg," ").concat(f.color,"\n                    "),children:v?(0,o.jsx)(v,{className:"w-4 h-4"}):(0,o.jsxs)("span",{className:"text-xs font-bold",children:["#",a||"?"]})}),s&&(0,o.jsx)("div",{className:"flex items-center justify-center w-8 h-8 rounded-full bg-blue-100 text-blue-700",children:(0,o.jsxs)("span",{className:"text-xs font-bold",children:["C",s]})})]}),(0,o.jsx)("div",{className:"mb-3",children:(0,o.jsx)(p.Z,{user:t,size:"md",showOnlineStatus:!0,className:"hover:scale-105 transition-transform duration-200",style:{width:g.avatar.includes("w-12")?"48px":g.avatar.includes("w-10")?"40px":"32px",height:g.avatar.includes("w-12")?"48px":g.avatar.includes("w-10")?"40px":"32px"}})}),(0,o.jsxs)("div",{className:"space-y-1",children:[(0,o.jsx)("h3",{className:"font-semibold ".concat(g.text," text-gray-900 truncate max-w-24"),children:(null===t||void 0===t?void 0:t.name)||"Unknown User"}),(0,o.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,o.jsx)(x,{level:(null===t||void 0===t?void 0:t.currentLevel)||1,size:"small",showTitle:!1,animated:!0}),(0,o.jsxs)("div",{className:"flex flex-col",children:[(0,o.jsxs)("p",{className:"".concat(g.subtext," text-blue-600 font-medium"),children:[(null===t||void 0===t?void 0:t.totalXP)||0," XP"]}),(null===t||void 0===t?void 0:t.xpToNextLevel)>0&&(0,o.jsxs)("p",{className:"text-xs text-gray-400",children:[t.xpToNextLevel," to next"]})]})]}),(0,o.jsxs)("p",{className:"text-xs text-gray-400",children:[(null===t||void 0===t?void 0:t.totalPoints)||0," pts (legacy)"]}),(null===t||void 0===t?void 0:t.averageScore)&&(0,o.jsxs)("p",{className:"".concat(g.subtext," text-gray-500"),children:["Avg: ",t.averageScore,"%"]}),(null===t||void 0===t?void 0:t.currentStreak)>0&&(0,o.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,o.jsx)(i.p8m,{className:"w-3 h-3 text-orange-500"}),(0,o.jsx)("span",{className:"".concat(g.subtext," text-orange-600 font-medium"),children:t.currentStreak})]}),(null===t||void 0===t?void 0:t.achievements)&&t.achievements.length>0&&(0,o.jsxs)("div",{className:"flex items-center space-x-1",children:[t.achievements.slice(0,3).map(((e,t)=>(0,o.jsx)(u,{achievement:e,size:"small",showTooltip:!0,animated:!0,showXP:!1},e.id||e.type||t))),t.achievements.length>3&&(0,o.jsxs)("span",{className:"text-xs text-gray-500 ml-1",children:["+",t.achievements.length-3]})]}),(0,o.jsxs)("div",{className:"\n                        inline-flex items-center space-x-1 px-3 py-1.5 rounded-full text-xs font-semibold\n                        ".concat(b.bgClass," transform hover:scale-105 transition-all duration-200\n                        backdrop-blur-sm\n                    "),children:[(0,o.jsx)("span",{className:"text-sm",children:b.badgeIcon}),(0,o.jsx)("span",{children:b.statusText})]})]})]}):(0,o.jsxs)("div",{className:"\n                ranking-card flex items-center ".concat(g.spacing," ").concat(g.padding,"\n                bg-white rounded-xl border border-gray-200 hover:scale-105 transition-all duration-300\n                hover:shadow-xl transform hover:-translate-y-1 animate-fadeInUp\n                ").concat(r?"current-user-card ring-2 ring-blue-500 ring-offset-2":"","\n                ").concat(h,"\n            "),children:[(0,o.jsxs)("div",{className:"flex items-center space-x-2 flex-shrink-0",children:[(0,o.jsx)("div",{className:"\n                    rank-badge flex items-center justify-center w-10 h-10 rounded-full\n                    ".concat(f.bg," ").concat(f.color,"\n                "),children:v?(0,o.jsx)(v,{className:"w-5 h-5"}):(0,o.jsxs)("span",{className:"text-sm font-bold",children:["#",a||"?"]})}),s&&(0,o.jsx)("div",{className:"flex items-center justify-center w-10 h-10 rounded-full bg-blue-100 text-blue-700",children:(0,o.jsxs)("span",{className:"text-sm font-bold",children:["C",s]})})]}),(0,o.jsx)("div",{className:"flex-shrink-0",children:(0,o.jsx)(p.Z,{user:t,size:"small"===l?"sm":"large"===l?"lg":"md",showOnlineStatus:!0,className:"hover:scale-105 transition-transform duration-200"})}),(0,o.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,o.jsxs)("div",{className:"flex items-center space-x-2 mb-1",children:[(0,o.jsx)("h3",{className:"font-semibold ".concat(g.text," text-gray-900 truncate"),children:(null===t||void 0===t?void 0:t.name)||"Unknown User"}),(0,o.jsx)(x,{level:(null===t||void 0===t?void 0:t.currentLevel)||1,size:"small",showTitle:!1,animated:!0}),(0,o.jsx)("span",{className:"\n                        px-2 py-1 rounded-full text-xs font-medium flex-shrink-0\n                        ".concat(b.bgClass,"\n                    "),children:b.statusText})]}),d&&(0,o.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,o.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,o.jsx)(i.lbY,{className:"w-3 h-3 text-blue-500"}),(0,o.jsxs)("span",{className:"".concat(g.subtext," text-blue-600 font-medium"),children:[(null===t||void 0===t?void 0:t.totalXP)||0," XP"]})]}),(0,o.jsxs)("span",{className:"text-xs text-gray-400",children:[(null===t||void 0===t?void 0:t.totalPoints)||0," pts"]}),void 0!==(null===t||void 0===t?void 0:t.passedExamsCount)&&(0,o.jsxs)("span",{className:"".concat(g.subtext," text-green-600"),children:[t.passedExamsCount," passed"]}),void 0!==(null===t||void 0===t?void 0:t.quizzesTaken)&&(0,o.jsxs)("span",{className:"".concat(g.subtext," text-blue-600"),children:[t.quizzesTaken," quizzes"]}),(null===t||void 0===t?void 0:t.averageScore)&&(0,o.jsxs)("span",{className:"".concat(g.subtext," text-gray-600"),children:[t.averageScore,"% avg"]}),(null===t||void 0===t?void 0:t.currentStreak)>0&&(0,o.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,o.jsx)(i.p8m,{className:"w-3 h-3 text-orange-500"}),(0,o.jsx)("span",{className:"".concat(g.subtext," text-orange-600 font-medium"),children:t.currentStreak})]})]}),(null===t||void 0===t?void 0:t.xpToNextLevel)>0&&(0,o.jsx)("div",{className:"mt-2",children:(0,o.jsx)(m,{currentXP:t.totalXP||0,totalXP:(t.totalXP||0)+(t.xpToNextLevel||0),currentLevel:t.currentLevel||1,xpToNextLevel:t.xpToNextLevel||0,size:"small",showLevel:!1,showXPNumbers:!1,showAnimation:!1})}),(null===t||void 0===t?void 0:t.achievements)&&t.achievements.length>0&&(0,o.jsx)("div",{className:"mt-2",children:(0,o.jsx)(c,{achievements:t.achievements,maxDisplay:5,size:"small",layout:"horizontal"})})]}),(0,o.jsxs)("div",{className:"text-right flex-shrink-0 space-y-2",children:[(0,o.jsxs)("div",{children:[(0,o.jsx)("div",{className:"font-bold ".concat(g.text," ").concat(r?"text-blue-600":"text-gray-900"),children:(t.rankingScore||t.score||t.totalXP||t.totalPoints||0).toLocaleString()}),(0,o.jsx)("div",{className:"".concat(g.subtext," text-gray-500"),children:t.rankingScore?"ranking pts":t.totalXP?"XP":"points"}),t.breakdown&&(0,o.jsxs)("div",{className:"text-xs text-gray-400 mt-1",children:["XP: ",(t.totalXP||0).toLocaleString()]})]}),(0,o.jsxs)("div",{className:"\n                    inline-flex items-center space-x-1 px-2 py-1 rounded-full text-xs font-semibold flex-shrink-0\n                    ".concat(b.bgClass," transform hover:scale-105 transition-all duration-200\n                    backdrop-blur-sm\n                "),children:[(0,o.jsx)("span",{className:"text-xs",children:b.badgeIcon}),(0,o.jsx)("span",{children:b.statusText})]})]})]})},g=e=>{let{users:t=[],currentUserId:a=null,layout:r="horizontal",size:l="medium",showStats:c=!0,className:d="",currentUserRef:m=null,showFindMe:x=!1,lastUpdated:u=null,autoRefresh:p=!1,onAutoRefreshToggle:g=null}=e;const[b,f]=(0,s.useState)(""),[v,y]=(0,s.useState)("all"),[w,j]=(0,s.useState)("rank"),[N,k]=(0,s.useState)("card"),[C,S]=(0,s.useState)(!1),[z,P]=(0,s.useState)(!1),E=(0,s.useRef)(null),L=m||E,T=x||z,_=t.find((e=>e.userId===a||e._id===a)),I=null===_||void 0===_?void 0:_.class,R=null===_||void 0===_?void 0:_.level,U=t.filter((e=>{var t,a,s,r,n;const i=(null===(t=e.name)||void 0===t?void 0:t.toLowerCase().includes(b.toLowerCase()))||(null===(a=e.email)||void 0===a?void 0:a.toLowerCase().includes(b.toLowerCase()))||(null===(s=e.class)||void 0===s?void 0:s.toLowerCase().includes(b.toLowerCase())),o=!C||e.class===I,l=(null===(r=e.normalizedSubscriptionStatus)||void 0===r?void 0:r.toLowerCase())||(null===(n=e.subscriptionStatus)||void 0===n?void 0:n.toLowerCase())||"free";let c=!0;switch(v){case"premium":c="premium"===l||"active"===l;break;case"expired":c="expired"===l;break;case"free":c="free"===l;break;default:c=!0}return i&&c&&o})).sort(((e,t)=>{switch(w){case"xp":return(t.totalXP||0)-(e.totalXP||0);case"name":return(e.name||"").localeCompare(t.name||"");case"score":return(t.rankingScore||t.score||0)-(e.rankingScore||e.score||0);case"class":return(e.class||"").localeCompare(t.class||"");default:return(e.rank||0)-(t.rank||0)}})),X=U.map((e=>{const t=U.filter((t=>t.class===e.class)).findIndex((t=>t._id===e._id||t.userId===e.userId))+1;return(0,n.Z)((0,n.Z)({},e),{},{classRank:t})})),M=()=>{L.current&&(L.current.scrollIntoView({behavior:"smooth",block:"center"}),P(!0),setTimeout((()=>P(!1)),3e3))},B=t.length,A=t.filter((e=>"active"===e.subscriptionStatus||"premium"===e.subscriptionStatus||"premium"===e.normalizedSubscriptionStatus)).length,q=t.length>0?Math.max(...t.map((e=>e.rankingScore||e.totalXP||e.totalPoints||0))):0,D=t.filter((e=>(e.totalQuizzesTaken||0)>0)).length,F=t.length>0?Math.round(t.reduce(((e,t)=>e+(t.totalXP||0)),0)/t.length):0;return(0,o.jsxs)("div",{className:"space-y-6 ".concat(d),children:[c&&(0,o.jsxs)("div",{className:"bg-gradient-to-r from-blue-50 to-purple-50 rounded-xl p-6 border border-blue-200 animate-fadeInUp",children:[(0,o.jsxs)("div",{className:"mb-6 bg-white/90 backdrop-blur-sm rounded-xl p-6 border border-white/50 shadow-lg",children:[(0,o.jsxs)("div",{className:"flex flex-col gap-4",children:[(0,o.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4",children:[(0,o.jsxs)("div",{className:"flex-1 relative",children:[(0,o.jsx)(i.adB,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5"}),(0,o.jsx)("input",{id:"ranking-search",name:"ranking-search",type:"text",placeholder:"Search by name, email, or class...",value:b,onChange:e=>f(e.target.value),autoComplete:"off",className:"w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 bg-white shadow-sm"}),b&&(0,o.jsx)("button",{onClick:()=>f(""),className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600",children:"\u2715"})]}),(0,o.jsx)("div",{className:"flex gap-2",children:(0,o.jsx)("button",{onClick:()=>S(!C),className:"px-4 py-2 rounded-lg font-medium transition-all duration-200 ".concat(C?"bg-blue-500 text-white shadow-md":"bg-gray-100 text-gray-700 hover:bg-gray-200"),children:"My Class Only"})})]}),(0,o.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4",children:[(0,o.jsxs)("div",{className:"relative",children:[(0,o.jsx)(i.a9n,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5"}),(0,o.jsxs)("select",{id:"ranking-filter",name:"ranking-filter",value:v,onChange:e=>y(e.target.value),autoComplete:"off",className:"pl-10 pr-8 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 bg-white shadow-sm",children:[(0,o.jsx)("option",{value:"all",children:"All Subscriptions"}),(0,o.jsx)("option",{value:"premium",children:"\ud83d\udc51 Premium Users"}),(0,o.jsx)("option",{value:"free",children:"\ud83c\udd93 Free Users"}),(0,o.jsx)("option",{value:"expired",children:"\u23f0 Expired Users"})]})]}),(0,o.jsxs)("select",{id:"ranking-sort",name:"ranking-sort",value:w,onChange:e=>j(e.target.value),autoComplete:"off",className:"px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 bg-white shadow-sm",children:[(0,o.jsx)("option",{value:"rank",children:"\ud83c\udfc6 Sort by Rank"}),(0,o.jsx)("option",{value:"xp",children:"\u26a1 Sort by XP"}),(0,o.jsx)("option",{value:"score",children:"\ud83d\udcca Sort by Score"}),(0,o.jsx)("option",{value:"name",children:"\ud83d\udcdd Sort by Name"}),(0,o.jsx)("option",{value:"class",children:"\ud83c\udf93 Sort by Class"})]}),(0,o.jsxs)("div",{className:"flex bg-gray-100 rounded-lg p-1",children:[(0,o.jsx)("button",{onClick:()=>k("card"),className:"px-3 py-2 rounded-md text-sm font-medium transition-all duration-200 ".concat("card"===N?"bg-white text-blue-600 shadow-sm":"text-gray-600 hover:text-gray-800"),children:"Cards"}),(0,o.jsx)("button",{onClick:()=>k("compact"),className:"px-3 py-2 rounded-md text-sm font-medium transition-all duration-200 ".concat("compact"===N?"bg-white text-blue-600 shadow-sm":"text-gray-600 hover:text-gray-800"),children:"Compact"})]})]})]}),(0,o.jsxs)("div",{className:"mt-4 flex flex-col sm:flex-row justify-between items-start sm:items-center gap-2",children:[(0,o.jsxs)("div",{className:"text-sm text-gray-600",children:[(0,o.jsx)("span",{className:"font-semibold text-gray-800",children:X.length})," of ",(0,o.jsx)("span",{className:"font-semibold text-gray-800",children:t.length})," users",b&&(0,o.jsxs)("span",{className:"ml-1",children:["matching ",(0,o.jsxs)("span",{className:"font-medium text-blue-600",children:['"',b,'"']})]}),"all"!==v&&(0,o.jsx)("span",{className:"ml-1 px-2 py-1 bg-blue-100 text-blue-700 rounded-full text-xs font-medium",children:v}),C&&I&&(0,o.jsx)("span",{className:"ml-1 px-2 py-1 bg-green-100 text-green-700 rounded-full text-xs font-medium",children:"primary"===R?"Class ".concat(I):I})]}),(0,o.jsxs)("div",{className:"flex gap-4 text-xs text-gray-500",children:[(0,o.jsxs)("span",{children:["\ud83c\udfc6 Top: ",Math.max(...X.map((e=>e.rankingScore||e.totalXP||0))).toLocaleString()]}),(0,o.jsxs)("span",{children:["\ud83d\udcca Avg: ",Math.round(X.reduce(((e,t)=>e+(t.rankingScore||t.totalXP||0)),0)/X.length||0).toLocaleString()]})]})]})]}),(0,o.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,o.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,o.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,o.jsx)("div",{className:"p-3 bg-gradient-to-br from-yellow-400 via-yellow-500 to-orange-500 rounded-xl shadow-lg",children:(0,o.jsx)(i.gBl,{className:"w-7 h-7 text-white"})}),(0,o.jsxs)("div",{children:[(0,o.jsx)("h2",{className:"text-3xl font-black text-gray-900 bg-gradient-to-r from-gray-900 via-blue-900 to-purple-900 bg-clip-text text-transparent",children:"Leaderboard"}),(0,o.jsx)("p",{className:"text-sm text-gray-600 font-medium",children:"Top performers across all levels"})]})]}),u&&(0,o.jsxs)("div",{className:"flex items-center space-x-2 px-3 py-2 bg-blue-50 rounded-lg border border-blue-200",children:[(0,o.jsx)(i.rfE,{className:"w-4 h-4 text-blue-600"}),(0,o.jsxs)("span",{className:"text-sm text-blue-700 font-medium",children:["Updated ",new Date(u).toLocaleTimeString()]})]})]}),(0,o.jsxs)("div",{className:"flex items-center space-x-2",children:[g&&(0,o.jsxs)("button",{onClick:g,className:"px-3 py-2 rounded-lg font-medium transition-all duration-200 flex items-center space-x-2 hover:scale-105 active:scale-95 ".concat(p?"bg-green-500 hover:bg-green-600 text-white":"bg-gray-200 hover:bg-gray-300 text-gray-700"),children:[p?(0,o.jsx)(i.K6B,{className:"w-4 h-4"}):(0,o.jsx)(i.y82,{className:"w-4 h-4"}),(0,o.jsx)("span",{className:"hidden sm:inline",children:p?"Auto":"Manual"})]}),a&&(0,o.jsxs)("button",{onClick:M,className:"bg-purple-500 hover:bg-purple-600 text-white px-3 py-2 rounded-lg font-medium transition-all duration-200 flex items-center space-x-2 hover:scale-105 active:scale-95",children:[(0,o.jsx)(i.S8z,{className:"w-4 h-4"}),(0,o.jsx)("span",{className:"hidden sm:inline",children:"Find Me"})]})]})]}),(0,o.jsxs)("div",{className:"grid grid-cols-2 sm:grid-cols-4 gap-4",children:[(0,o.jsxs)("div",{className:"bg-gradient-to-br from-blue-50 to-indigo-50 rounded-xl p-5 border border-blue-200 shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105",children:[(0,o.jsxs)("div",{className:"flex items-center space-x-3 mb-3",children:[(0,o.jsx)("div",{className:"p-2 bg-blue-500 rounded-lg",children:(0,o.jsx)(i.HLl,{className:"w-5 h-5 text-white"})}),(0,o.jsx)("span",{className:"text-sm font-semibold text-blue-700",children:"Total Users"})]}),(0,o.jsx)("div",{className:"text-3xl font-black text-blue-900 mb-1",children:B}),(0,o.jsxs)("div",{className:"text-xs text-blue-600 font-medium",children:[D," active"]})]}),(0,o.jsxs)("div",{className:"bg-gradient-to-br from-yellow-50 to-orange-50 rounded-xl p-5 border border-yellow-200 shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105",children:[(0,o.jsxs)("div",{className:"flex items-center space-x-3 mb-3",children:[(0,o.jsx)("div",{className:"p-2 bg-gradient-to-r from-yellow-500 to-orange-500 rounded-lg",children:(0,o.jsx)(i.gBl,{className:"w-5 h-5 text-white"})}),(0,o.jsx)("span",{className:"text-sm font-semibold text-yellow-700",children:"Premium Users"})]}),(0,o.jsx)("div",{className:"text-3xl font-black text-yellow-900 mb-1",children:A}),(0,o.jsxs)("div",{className:"text-xs text-yellow-600 font-medium",children:[B>0?Math.round(A/B*100):0,"% premium"]})]}),(0,o.jsxs)("div",{className:"bg-gradient-to-br from-green-50 to-emerald-50 rounded-xl p-5 border border-green-200 shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105",children:[(0,o.jsxs)("div",{className:"flex items-center space-x-3 mb-3",children:[(0,o.jsx)("div",{className:"p-2 bg-green-500 rounded-lg",children:(0,o.jsx)(i.S8z,{className:"w-5 h-5 text-white"})}),(0,o.jsx)("span",{className:"text-sm font-semibold text-green-700",children:"Top Score"})]}),(0,o.jsx)("div",{className:"text-3xl font-black text-green-900 mb-1",children:q.toLocaleString()}),(0,o.jsx)("div",{className:"text-xs text-green-600 font-medium",children:"ranking points"})]}),(0,o.jsxs)("div",{className:"bg-gradient-to-br from-purple-50 to-pink-50 rounded-xl p-5 border border-purple-200 shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105",children:[(0,o.jsxs)("div",{className:"flex items-center space-x-3 mb-3",children:[(0,o.jsx)("div",{className:"p-2 bg-purple-500 rounded-lg",children:(0,o.jsx)(i.gBl,{className:"w-5 h-5 text-white"})}),(0,o.jsx)("span",{className:"text-sm font-semibold text-purple-700",children:"Avg XP"})]}),(0,o.jsx)("div",{className:"text-3xl font-black text-purple-900 mb-1",children:F.toLocaleString()}),(0,o.jsx)("div",{className:"text-xs text-gray-500 mt-1",children:"experience points"})]})]})]}),(0,o.jsx)("div",{className:"animate-fadeInUp ".concat((()=>{if("compact"===N)return"space-y-2";switch(r){case"vertical":return"grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-4";case"grid":return"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6";default:return"space-y-4"}})()),children:X.map(((e,t)=>{const s=e.userId===a||e._id===a,n=e.rank||t+1;var i,d,m,x,u,p;return"compact"===N?(0,o.jsx)("div",{ref:s?L:null,className:"animate-slideInLeft transition-all duration-200 p-3 rounded-lg border ".concat(s&&T?"find-me-highlight ring-2 ring-yellow-400 bg-gradient-to-r from-yellow-50 to-orange-50 border-yellow-200":s?"ring-2 ring-blue-400 bg-blue-50 border-blue-200":"bg-white border-gray-200 hover:border-gray-300 hover:shadow-md"),children:(0,o.jsxs)("div",{className:"flex items-center justify-between",children:[(0,o.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,o.jsx)("div",{className:"w-8 h-8 rounded-full flex items-center justify-center text-sm font-bold ".concat(n<=3?"bg-gradient-to-r from-yellow-400 to-orange-500 text-white":n<=10?"bg-gradient-to-r from-blue-400 to-blue-500 text-white":"bg-gray-100 text-gray-600"),children:n}),(0,o.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,o.jsx)("img",{src:e.profilePicture||"/default-avatar.png",alt:e.name,className:"w-8 h-8 rounded-full object-cover border-2 border-gray-200"}),(0,o.jsxs)("div",{children:[(0,o.jsx)("div",{className:"font-semibold text-gray-900 text-sm",children:e.name}),(0,o.jsx)("div",{className:"text-xs text-gray-500",children:"primary"===e.level?"Class ".concat(e.class):e.class})]})]})]}),(0,o.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,o.jsxs)("div",{className:"text-right",children:[(0,o.jsx)("div",{className:"font-bold text-gray-900 text-sm",children:(e.rankingScore||e.totalXP||0).toLocaleString()}),(0,o.jsx)("div",{className:"text-xs text-gray-500",children:e.rankingScore?"pts":"XP"})]}),(0,o.jsx)("div",{className:"px-2 py-1 rounded-full text-xs font-medium ".concat("premium"===(null===(i=e.normalizedSubscriptionStatus||e.subscriptionStatus)||void 0===i?void 0:i.toLowerCase())||"active"===(null===(d=e.normalizedSubscriptionStatus||e.subscriptionStatus)||void 0===d?void 0:d.toLowerCase())?"bg-yellow-100 text-yellow-800":"expired"===(null===(m=e.normalizedSubscriptionStatus||e.subscriptionStatus)||void 0===m?void 0:m.toLowerCase())?"bg-red-100 text-red-800":"bg-blue-100 text-blue-800"),children:"premium"===(null===(x=e.normalizedSubscriptionStatus||e.subscriptionStatus)||void 0===x?void 0:x.toLowerCase())||"active"===(null===(u=e.normalizedSubscriptionStatus||e.subscriptionStatus)||void 0===u?void 0:u.toLowerCase())?"\ud83d\udc51":"expired"===(null===(p=e.normalizedSubscriptionStatus||e.subscriptionStatus)||void 0===p?void 0:p.toLowerCase())?"\u23f0":"\ud83c\udd93"})]})]})},e.userId||e._id):(0,o.jsx)("div",{ref:s?L:null,className:"animate-slideInLeft transition-all duration-300 ".concat(s&&T?"find-me-highlight ring-4 ring-yellow-400 bg-gradient-to-r from-yellow-50 to-orange-50 rounded-xl shadow-2xl":s?"ring-2 ring-blue-400 bg-blue-50/50 rounded-lg":""),children:(0,o.jsx)(h,{user:e,rank:n,classRank:e.classRank,isCurrentUser:s,layout:r,size:l,showStats:c})},e.userId||e._id)}))}),0===X.length&&(0,o.jsxs)("div",{className:"text-center py-12 bg-white rounded-xl border border-gray-200 animate-fadeInUp",children:[(0,o.jsx)(i.HLl,{className:"w-16 h-16 text-gray-400 mx-auto mb-4"}),(0,o.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:"No users found"}),(0,o.jsx)("p",{className:"text-gray-500",children:0===t.length?"No ranking data available.":"Try adjusting your search or filter criteria."})]}),a&&X.length>10&&(0,o.jsx)("button",{onClick:M,className:"fixed bottom-6 right-6 bg-blue-500 hover:bg-blue-600 text-white p-3 rounded-full shadow-lg hover:shadow-xl transition-all duration-200 z-50 hover:scale-110 active:scale-95 animate-bounce",title:"Find me in ranking",children:(0,o.jsx)(i.S8z,{className:"w-6 h-6"})})]})},b=()=>{const[e,t]=(0,s.useState)("horizontal"),[a,n]=(0,s.useState)("medium"),i=[{userId:"1",name:"Alice Johnson",profilePicture:"https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face",subscriptionStatus:"active",totalPoints:2850,passedExamsCount:15,quizzesTaken:23,score:2850,rank:1},{userId:"2",name:"Bob Smith",profilePicture:"https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face",subscriptionStatus:"free",totalPoints:2720,passedExamsCount:12,quizzesTaken:20,score:2720,rank:2},{userId:"3",name:"Carol Davis",profilePicture:"https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150&h=150&fit=crop&crop=face",subscriptionStatus:"premium",totalPoints:2650,passedExamsCount:14,quizzesTaken:19,score:2650,rank:3},{userId:"4",name:"David Wilson",profilePicture:"https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face",subscriptionStatus:"expired",totalPoints:2400,passedExamsCount:10,quizzesTaken:18,score:2400,rank:4},{userId:"5",name:"Emma Brown",profilePicture:"https://images.unsplash.com/photo-1544005313-94ddf0286df2?w=150&h=150&fit=crop&crop=face",subscriptionStatus:"active",totalPoints:2350,passedExamsCount:11,quizzesTaken:16,score:2350,rank:5},{userId:"6",name:"Frank Miller",profilePicture:"https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=150&h=150&fit=crop&crop=face",subscriptionStatus:"free",totalPoints:2200,passedExamsCount:9,quizzesTaken:15,score:2200,rank:6},{userId:"7",name:"Grace Lee",profilePicture:"https://images.unsplash.com/photo-1534528741775-53994a69daeb?w=150&h=150&fit=crop&crop=face",subscriptionStatus:"premium",totalPoints:2100,passedExamsCount:8,quizzesTaken:14,score:2100,rank:7},{userId:"8",name:"Henry Taylor",profilePicture:"https://images.unsplash.com/photo-1506794778202-cad84cf45f1d?w=150&h=150&fit=crop&crop=face",subscriptionStatus:"free",totalPoints:1950,passedExamsCount:7,quizzesTaken:13,score:1950,rank:8}];return(0,o.jsx)("div",{className:"min-h-screen bg-gray-50 py-8",children:(0,o.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,o.jsxs)(r.E.div,{initial:{opacity:0,y:-20},animate:{opacity:1,y:0},className:"text-center mb-8",children:[(0,o.jsx)("h1",{className:"text-4xl font-bold text-gray-900 mb-4",children:"Modern User Ranking Component"}),(0,o.jsx)("p",{className:"text-lg text-gray-600 max-w-3xl mx-auto",children:"A beautiful, responsive ranking component with Instagram-style profile circles, premium user highlighting, and multiple layout options."})]}),(0,o.jsxs)(r.E.div,{initial:{opacity:0,y:-10},animate:{opacity:1,y:0},transition:{delay:.2},className:"bg-white rounded-xl p-6 mb-8 shadow-sm border border-gray-200",children:[(0,o.jsx)("h2",{className:"text-xl font-semibold text-gray-900 mb-4",children:"Customization Options"}),(0,o.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,o.jsxs)("div",{children:[(0,o.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Layout Style"}),(0,o.jsx)("div",{className:"space-y-2",children:["horizontal","vertical","grid"].map((a=>(0,o.jsxs)("label",{className:"flex items-center",children:[(0,o.jsx)("input",{id:"layout-".concat(a),type:"radio",name:"layout",value:a,checked:e===a,onChange:e=>t(e.target.value),className:"mr-2 text-blue-600"}),(0,o.jsx)("span",{className:"capitalize",children:a})]},a)))})]}),(0,o.jsxs)("div",{children:[(0,o.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Component Size"}),(0,o.jsx)("div",{className:"space-y-2",children:["small","medium","large"].map((e=>(0,o.jsxs)("label",{className:"flex items-center",children:[(0,o.jsx)("input",{id:"size-".concat(e),type:"radio",name:"size",value:e,checked:a===e,onChange:e=>n(e.target.value),className:"mr-2 text-blue-600"}),(0,o.jsx)("span",{className:"capitalize",children:e})]},e)))})]})]})]}),(0,o.jsxs)(r.E.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.3},className:"mb-8",children:[(0,o.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-6",children:"Individual Card Examples"}),(0,o.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,o.jsxs)("div",{className:"bg-white rounded-xl p-6 border border-gray-200",children:[(0,o.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Premium User (Gold Glow)"}),(0,o.jsx)(h,{user:i[0],rank:1,isCurrentUser:!1,layout:"horizontal",size:a,showStats:!0})]}),(0,o.jsxs)("div",{className:"bg-white rounded-xl p-6 border border-gray-200",children:[(0,o.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Current User (Highlighted)"}),(0,o.jsx)(h,{user:i[1],rank:2,isCurrentUser:!0,layout:"horizontal",size:a,showStats:!0})]})]})]}),(0,o.jsxs)(r.E.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.4},children:[(0,o.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-6",children:"Complete Ranking List"}),(0,o.jsx)(g,{users:i,currentUserId:"3",layout:e,size:a,showSearch:!0,showFilters:!0,showStats:!0})]}),(0,o.jsxs)(r.E.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.5},className:"mt-12 bg-white rounded-xl p-8 border border-gray-200",children:[(0,o.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-6",children:"Key Features"}),(0,o.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:[(0,o.jsxs)("div",{className:"space-y-2",children:[(0,o.jsx)("h3",{className:"font-semibold text-gray-900",children:"\ud83c\udfa8 Premium Highlighting"}),(0,o.jsx)("p",{className:"text-gray-600 text-sm",children:"Gold gradient glow for premium users with vibrant visual distinction"})]}),(0,o.jsxs)("div",{className:"space-y-2",children:[(0,o.jsx)("h3",{className:"font-semibold text-gray-900",children:"\ud83d\udcf1 Responsive Design"}),(0,o.jsx)("p",{className:"text-gray-600 text-sm",children:"Adapts perfectly to mobile, tablet, and desktop screens"})]}),(0,o.jsxs)("div",{className:"space-y-2",children:[(0,o.jsx)("h3",{className:"font-semibold text-gray-900",children:"\ud83d\udd0d Search & Filter"}),(0,o.jsx)("p",{className:"text-gray-600 text-sm",children:"Real-time search and filtering by subscription status"})]}),(0,o.jsxs)("div",{className:"space-y-2",children:[(0,o.jsx)("h3",{className:"font-semibold text-gray-900",children:"\u2728 Smooth Animations"}),(0,o.jsx)("p",{className:"text-gray-600 text-sm",children:"Framer Motion powered animations for engaging interactions"})]}),(0,o.jsxs)("div",{className:"space-y-2",children:[(0,o.jsx)("h3",{className:"font-semibold text-gray-900",children:"\ud83c\udfc6 Rank Indicators"}),(0,o.jsx)("p",{className:"text-gray-600 text-sm",children:"Special icons and colors for top performers"})]}),(0,o.jsxs)("div",{className:"space-y-2",children:[(0,o.jsx)("h3",{className:"font-semibold text-gray-900",children:"\ud83c\udfaf Current User Focus"}),(0,o.jsx)("p",{className:"text-gray-600 text-sm",children:"Automatic highlighting and scroll-to functionality"})]})]})]})]})})}},3791:(e,t,a)=>{a.d(t,{M:()=>g});var s=a(2791),r=a(2199);function n(){const e=(0,s.useRef)(!1);return(0,r.L)((()=>(e.current=!0,()=>{e.current=!1})),[]),e}var i=a(8771);var o=a(131),l=a(1421);class c extends s.Component{getSnapshotBeforeUpdate(e){const t=this.props.childRef.current;if(t&&e.isPresent&&!this.props.isPresent){const e=this.props.sizeRef.current;e.height=t.offsetHeight||0,e.width=t.offsetWidth||0,e.top=t.offsetTop,e.left=t.offsetLeft}return null}componentDidUpdate(){}render(){return this.props.children}}function d(e){let{children:t,isPresent:a}=e;const r=(0,s.useId)(),n=(0,s.useRef)(null),i=(0,s.useRef)({width:0,height:0,top:0,left:0});return(0,s.useInsertionEffect)((()=>{const{width:e,height:t,top:s,left:o}=i.current;if(a||!n.current||!e||!t)return;n.current.dataset.motionPopId=r;const l=document.createElement("style");return document.head.appendChild(l),l.sheet&&l.sheet.insertRule('\n          [data-motion-pop-id="'.concat(r,'"] {\n            position: absolute !important;\n            width: ').concat(e,"px !important;\n            height: ").concat(t,"px !important;\n            top: ").concat(s,"px !important;\n            left: ").concat(o,"px !important;\n          }\n        ")),()=>{document.head.removeChild(l)}}),[a]),s.createElement(c,{isPresent:a,childRef:n,sizeRef:i},s.cloneElement(t,{ref:n}))}const m=e=>{let{children:t,initial:a,isPresent:r,onExitComplete:n,custom:i,presenceAffectsLayout:c,mode:m}=e;const u=(0,l.h)(x),p=(0,s.useId)(),h=(0,s.useMemo)((()=>({id:p,initial:a,isPresent:r,custom:i,onExitComplete:e=>{u.set(e,!0);for(const t of u.values())if(!t)return;n&&n()},register:e=>(u.set(e,!1),()=>u.delete(e))})),c?void 0:[r]);return(0,s.useMemo)((()=>{u.forEach(((e,t)=>u.set(t,!1)))}),[r]),s.useEffect((()=>{!r&&!u.size&&n&&n()}),[r]),"popLayout"===m&&(t=s.createElement(d,{isPresent:r},t)),s.createElement(o.O.Provider,{value:h},t)};function x(){return new Map}var u=a(7497);var p=a(5956);const h=e=>e.key||"";const g=e=>{let{children:t,custom:a,initial:o=!0,onExitComplete:l,exitBeforeEnter:c,presenceAffectsLayout:d=!0,mode:x="sync"}=e;(0,p.k)(!c,"Replace exitBeforeEnter with mode='wait'");const g=(0,s.useContext)(u.p).forceRender||function(){const e=n(),[t,a]=(0,s.useState)(0),r=(0,s.useCallback)((()=>{e.current&&a(t+1)}),[t]);return[(0,s.useCallback)((()=>i.Wi.postRender(r)),[r]),t]}()[0],b=n(),f=function(e){const t=[];return s.Children.forEach(e,(e=>{(0,s.isValidElement)(e)&&t.push(e)})),t}(t);let v=f;const y=(0,s.useRef)(new Map).current,w=(0,s.useRef)(v),j=(0,s.useRef)(new Map).current,N=(0,s.useRef)(!0);var k;if((0,r.L)((()=>{N.current=!1,function(e,t){e.forEach((e=>{const a=h(e);t.set(a,e)}))}(f,j),w.current=v})),k=()=>{N.current=!0,j.clear(),y.clear()},(0,s.useEffect)((()=>()=>k()),[]),N.current)return s.createElement(s.Fragment,null,v.map((e=>s.createElement(m,{key:h(e),isPresent:!0,initial:!!o&&void 0,presenceAffectsLayout:d,mode:x},e))));v=[...v];const C=w.current.map(h),S=f.map(h),z=C.length;for(let s=0;s<z;s++){const e=C[s];-1!==S.indexOf(e)||y.has(e)||y.set(e,void 0)}return"wait"===x&&y.size&&(v=[]),y.forEach(((e,t)=>{if(-1!==S.indexOf(t))return;const r=j.get(t);if(!r)return;const n=C.indexOf(t);let i=e;if(!i){const e=()=>{y.delete(t);const e=Array.from(j.keys()).filter((e=>!S.includes(e)));if(e.forEach((e=>j.delete(e))),w.current=f.filter((a=>{const s=h(a);return s===t||e.includes(s)})),!y.size){if(!1===b.current)return;g(),l&&l()}};i=s.createElement(m,{key:h(r),isPresent:!1,onExitComplete:e,custom:a,presenceAffectsLayout:d,mode:x},r),y.set(t,i)}v.splice(n,0,i)})),v=v.map((e=>{const t=e.key;return y.has(t)?e:s.createElement(m,{key:h(e),isPresent:!0,presenceAffectsLayout:d,mode:x},e)})),s.createElement(s.Fragment,null,y.size?v:v.map((e=>(0,s.cloneElement)(e))))}}}]);
//# sourceMappingURL=332.03ed5eae.chunk.js.map