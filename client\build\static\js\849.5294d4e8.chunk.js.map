{"version": 3, "file": "static/js/849.5294d4e8.chunk.js", "mappings": "sIAgBA,QAdA,SAAkBA,GAAa,IAAZ,MAAEC,GAAOD,EAC1B,MAAOE,EAAUC,IAAeC,EAAAA,EAAAA,WAAS,GAMzC,OALAC,EAAAA,EAAAA,YAAU,KACJC,OAAOC,WAAa,KACtBJ,GAAY,EACd,GACC,KAEDK,EAAAA,EAAAA,KAAA,OAAKC,UAAU,OAAMC,UACnBF,EAAAA,EAAAA,KAAA,MAAIC,UAAWP,EAAW,UAAY,GAAGQ,SAAET,KAGjD,C,4LC+TA,QAnUA,WACE,MAAMU,GAAWC,EAAAA,EAAAA,OACVC,EAAOC,IAAYV,EAAAA,EAAAA,UAAS,KAC5BW,EAAeC,IAAoBZ,EAAAA,EAAAA,UAAS,IAC7Ca,GAAWC,EAAAA,EAAAA,OAGVC,EAASC,IAAchB,EAAAA,EAAAA,UAAS,CACrCiB,MAAO,GACPC,MAAO,GACPC,MAAO,GACPC,OAAQ,KAGJC,EAAeC,UACnB,IACET,GAASU,EAAAA,EAAAA,OACT,MAAMC,QAAiBC,EAAAA,EAAAA,MAEvB,GADAZ,GAASa,EAAAA,EAAAA,OACLF,EAASG,QAAS,CACpB,MAAMC,EAAWJ,EAASK,KAAKC,UAC/BpB,EAASkB,GACThB,EAAiBgB,GACjBG,QAAQC,IAAIR,EAAU,OACxB,MACES,EAAAA,GAAQC,MAAMV,EAASS,QAE3B,CAAE,MAAOC,GACPrB,GAASa,EAAAA,EAAAA,OACTO,EAAAA,GAAQC,MAAMA,EAAMD,QACtB,GAuBIE,GAAgBC,EAAAA,EAAAA,UAAQ,KAKrB,CAAEC,OAJM,IAAI,IAAIC,IAAI7B,EAAM8B,KAAIC,GAAQA,EAAKvB,QAAOwB,OAAOC,WAI/CC,QAHD,IAAI,IAAIL,IAAI7B,EAAM8B,KAAIC,GAAQA,EAAKtB,QAAOuB,OAAOC,WAAWE,OAGlDC,OAFX,IAAI,IAAIP,IAAI7B,EAAM8B,KAAIC,GAAQA,EAAKrB,QAAOsB,OAAOC,WAAWE,UAG1E,CAACnC,KAGJR,EAAAA,EAAAA,YAAU,KACR,IAAI6C,EAAW,IAAIrC,GAoBnB,GAjBIM,EAAQE,QACV6B,EAAWA,EAASL,QAAOD,GACzBA,EAAKvB,OAASuB,EAAKvB,MAAM8B,gBAAkBhC,EAAQE,MAAM8B,iBAKzDhC,EAAQG,QACV4B,EAAWA,EAASL,QAAOD,GAAQA,EAAKtB,QAAUH,EAAQG,SAIxDH,EAAQI,QACV2B,EAAWA,EAASL,QAAOD,GAAQA,EAAKrB,QAAUJ,EAAQI,SAIxDJ,EAAQK,OAAQ,CAClB,MAAM4B,EAAajC,EAAQK,OAAO2B,cAClCD,EAAWA,EAASL,QAAOD,IAAI,IAAAS,EAAAC,EAAAC,EAAA,OACpB,QAATF,EAAAT,EAAKY,YAAI,IAAAH,OAAA,EAATA,EAAWF,cAAcM,SAASL,MACtB,QADiCE,EAC7CV,EAAKc,eAAO,IAAAJ,OAAA,EAAZA,EAAcH,cAAcM,SAASL,MACxB,QADmCG,EAChDX,EAAKe,gBAAQ,IAAAJ,OAAA,EAAbA,EAAeJ,cAAcM,SAASL,GAAW,GAErD,CAEApC,EAAiBkC,EAAS,GACzB,CAACrC,EAAOM,IAGX,MAAMyC,EAAqBA,CAACC,EAAKC,KAC/B1C,GAAW2C,IAAIC,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GAAUD,GAAI,IAAE,CAACF,GAAMC,KAAS,EAa3CG,EAAU,CACd,CACEhE,MAAO,YACPiE,UAAW,OACXC,MAAO,IACPC,UAAU,GAEZ,CACEnE,MAAO,QACPiE,UAAW,QACXC,MAAO,IACPE,OAAShD,IACPb,EAAAA,EAAAA,KAAA,QAAMC,UAAS,8CAAA6D,OACH,YAAVjD,EAAsB,8BACZ,cAAVA,EAAwB,4BACd,YAAVA,EAAsB,gCACtB,6BACCX,UACK,OAALW,QAAK,IAALA,OAAK,EAALA,EAAOkD,OAAO,GAAGC,gBAAqB,OAALnD,QAAK,IAALA,OAAK,EAALA,EAAOoD,MAAM,OAIrD,CACExE,MAAO,QACPiE,UAAW,QACXC,MAAO,KAET,CACElE,MAAO,UACPiE,UAAW,UACXC,MAAO,KAET,CACElE,MAAO,QACPiE,UAAW,QACXC,MAAO,IACPC,UAAU,EACVC,OAAS9C,GAAUA,IAASf,EAAAA,EAAAA,KAAA,QAAMC,UAAU,uBAAsBC,SAAC,aAErE,CACET,MAAO,WACPiE,UAAW,WACXC,MAAO,IACPE,OAASK,GAAQ,GAAAJ,OAAQK,KAAKC,MAAMF,EAAW,IAAG,SAEpD,CACEzE,MAAO,YACPiE,UAAW,YACXC,MAAO,IACPE,OAASQ,IAAuB,OAATA,QAAS,IAATA,OAAS,EAATA,EAAWC,SAAU,GAE9C,CACE7E,MAAO,SACPiE,UAAW,SACXG,OAAQA,CAACU,EAAMC,KACbC,EAAAA,EAAAA,MAAA,OAAKxE,UAAU,aAAYC,SAAA,EACzBF,EAAAA,EAAAA,KAAA,KACEC,UAAU,iBACVyE,QAASA,IAAMvE,EAAS,qBAAD2D,OAAsBU,EAAOG,SAEtD3E,EAAAA,EAAAA,KAAA,KACEC,UAAU,qBACVyE,QAASA,IA3IAxD,WACjB,IACET,GAASU,EAAAA,EAAAA,OACT,MAAMC,QAAiBwD,EAAAA,EAAAA,IAAe,CACpCC,WAEFpE,GAASa,EAAAA,EAAAA,OACLF,EAASG,SACXM,EAAAA,GAAQN,QAAQH,EAASS,SACzBZ,KAEAY,EAAAA,GAAQC,MAAMV,EAASS,QAE3B,CAAE,MAAOC,GACPrB,GAASa,EAAAA,EAAAA,OACTO,EAAAA,GAAQC,MAAMA,EAAMD,QACtB,GA2HuBiD,CAAWN,EAAOG,YAS3C,OAHA9E,EAAAA,EAAAA,YAAU,KACRoB,GAAc,GACb,KAEDwD,EAAAA,EAAAA,MAAA,OAAAvE,SAAA,EACEuE,EAAAA,EAAAA,MAAA,OAAKxE,UAAU,sCAAqCC,SAAA,EAClDuE,EAAAA,EAAAA,MAAA,OAAKxE,UAAU,0BAAyBC,SAAA,EAEtCuE,EAAAA,EAAAA,MAACM,EAAAA,EAAOC,OAAM,CACZC,WAAY,CAAEC,MAAO,MACrBC,SAAU,CAAED,MAAO,KACnBR,QAASA,IAAMvE,EAAS,oBACxBF,UAAU,iIAAgIC,SAAA,EAE1IF,EAAAA,EAAAA,KAACoF,EAAAA,IAAW,CAACnF,UAAU,aACvBD,EAAAA,EAAAA,KAAA,QAAMC,UAAU,uCAAsCC,SAAC,kBAGzDF,EAAAA,EAAAA,KAACqF,EAAAA,EAAS,CAAC5F,MAAM,cAGnBgF,EAAAA,EAAAA,MAACM,EAAAA,EAAOC,OAAM,CACZC,WAAY,CAAEC,MAAO,MACrBC,SAAU,CAAED,MAAO,KACnBjF,UAAU,+CACVyE,QAASA,IAAMvE,EAAS,oBAAoBD,SAAA,EAE5CF,EAAAA,EAAAA,KAACsF,EAAAA,IAAM,CAACrF,UAAU,YAAY,kBAIlCD,EAAAA,EAAAA,KAAA,OAAKC,UAAU,aAGfwE,EAAAA,EAAAA,MAAA,OAAKxE,UAAU,gDAA+CC,SAAA,EAC5DuE,EAAAA,EAAAA,MAAA,OAAKxE,UAAU,+BAA8BC,SAAA,EAC3CF,EAAAA,EAAAA,KAACuF,EAAAA,IAAQ,CAACtF,UAAU,2BACpBD,EAAAA,EAAAA,KAAA,MAAIC,UAAU,sCAAqCC,SAAC,aAClDS,EAAQE,OAASF,EAAQG,OAASH,EAAQI,OAASJ,EAAQK,UAC3DyD,EAAAA,EAAAA,MAACM,EAAAA,EAAOC,OAAM,CACZC,WAAY,CAAEC,MAAO,MACrBC,SAAU,CAAED,MAAO,KACnBR,QAvHSc,KACnB5E,EAAW,CACTC,MAAO,GACPC,MAAO,GACPC,MAAO,GACPC,OAAQ,IACR,EAkHQf,UAAU,6HAA4HC,SAAA,EAEtIF,EAAAA,EAAAA,KAACyF,EAAAA,IAAG,CAACxF,UAAU,YAAY,mBAMjCwE,EAAAA,EAAAA,MAAA,OAAKxE,UAAU,uDAAsDC,SAAA,EAEnEuE,EAAAA,EAAAA,MAAA,OAAAvE,SAAA,EACEF,EAAAA,EAAAA,KAAA,SAAOC,UAAU,+CAA8CC,SAAC,kBAGhEF,EAAAA,EAAAA,KAAC0F,EAAAA,QAAK,CACJC,YAAY,uCACZC,QAAQ5F,EAAAA,EAAAA,KAAC6F,EAAAA,IAAQ,CAAC5F,UAAU,0BAC5BqD,MAAO3C,EAAQK,OACf8E,SAAWC,GAAM3C,EAAmB,SAAU2C,EAAEC,OAAO1C,OACvD2C,YAAU,QAKdxB,EAAAA,EAAAA,MAAA,OAAAvE,SAAA,EACEF,EAAAA,EAAAA,KAAA,SAAOC,UAAU,+CAA8CC,SAAC,WAGhEF,EAAAA,EAAAA,KAACkG,EAAAA,QAAM,CACLP,YAAY,eACZrC,MAAO3C,EAAQE,YAASsF,EACxBL,SAAWxC,GAAUF,EAAmB,QAASE,GACjD2C,YAAU,EACVhG,UAAU,SAAQC,SAEjB6B,EAAcE,OAAOE,KAAItB,IACxBb,EAAAA,EAAAA,KAACkG,EAAAA,QAAOE,OAAM,CAAa9C,MAAOzC,EAAMX,SACrCW,EAAMkD,OAAO,GAAGC,cAAgBnD,EAAMoD,MAAM,IAD3BpD,WAQ1B4D,EAAAA,EAAAA,MAAA,OAAAvE,SAAA,EACEF,EAAAA,EAAAA,KAAA,SAAOC,UAAU,+CAA8CC,SAAC,WAGhEF,EAAAA,EAAAA,KAACkG,EAAAA,QAAM,CACLP,YAAY,eACZrC,MAAO3C,EAAQG,YAASqF,EACxBL,SAAWxC,GAAUF,EAAmB,QAASE,GACjD2C,YAAU,EACVhG,UAAU,SAAQC,SAEjB6B,EAAcQ,QAAQJ,KAAIlC,IACzBD,EAAAA,EAAAA,KAACkG,EAAAA,QAAOE,OAAM,CAAiB9C,MAAOrD,EAAUC,SAC7CD,GADiBA,WAQ1BwE,EAAAA,EAAAA,MAAA,OAAAvE,SAAA,EACEF,EAAAA,EAAAA,KAAA,SAAOC,UAAU,+CAA8CC,SAAC,WAGhEF,EAAAA,EAAAA,KAACkG,EAAAA,QAAM,CACLP,YAAY,eACZrC,MAAO3C,EAAQI,YAASoF,EACxBL,SAAWxC,GAAUF,EAAmB,QAASE,GACjD2C,YAAU,EACVhG,UAAU,SAAQC,SAEjB6B,EAAcU,OAAON,KAAIpB,IACxBf,EAAAA,EAAAA,KAACkG,EAAAA,QAAOE,OAAM,CAAa9C,MAAOvC,EAAMb,SACrCa,GADiBA,cAS5B0D,EAAAA,EAAAA,MAAA,OAAKxE,UAAU,+DAA8DC,SAAA,EAC3EuE,EAAAA,EAAAA,MAAA,QAAAvE,SAAA,CAAM,WACKK,EAAc+D,OAAO,OAAKjE,EAAMiE,OAAO,YAEjD/D,EAAc+D,SAAWjE,EAAMiE,SAC9BtE,EAAAA,EAAAA,KAAA,QAAMC,UAAU,4BAA2BC,SAAC,2BAOlDF,EAAAA,EAAAA,KAACqG,EAAAA,EAAK,CAAC5C,QAASA,EAAS6C,WAAY/F,MAG3C,C", "sources": ["components/PageTitle.js", "pages/admin/Exams/index.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react'\r\n\r\nfunction PageTitle({ title }) {\r\n  const [isMobile, setIsMobile] = useState(false);\r\n  useEffect(() => {\r\n    if (window.innerWidth < 768) {\r\n      setIsMobile(true);\r\n    }\r\n  }, []);\r\n  return (\r\n    <div className='mt-2'>\r\n      <h1 className={isMobile ? 'text-lg' : ''}>{title}</h1>\r\n    </div>\r\n  )\r\n}\r\n\r\nexport default PageTitle", "import { message, Table, Select, Input } from \"antd\";\r\nimport React, { useEffect, useState, useMemo } from \"react\";\r\nimport { useDispatch } from \"react-redux\";\r\nimport { useNavigate } from \"react-router-dom\";\r\nimport { motion } from \"framer-motion\";\r\nimport { TbDashboard, TbPlus, TbFilter, TbSearch, TbX } from \"react-icons/tb\";\r\nimport { deleteExamById, getAllExams } from \"../../../apicalls/exams\";\r\nimport PageTitle from \"../../../components/PageTitle\";\r\nimport { HideLoading, ShowLoading } from \"../../../redux/loaderSlice\";\r\n\r\nfunction Exams() {\r\n  const navigate = useNavigate();\r\n  const [exams, setExams] = useState([]);\r\n  const [filteredExams, setFilteredExams] = useState([]);\r\n  const dispatch = useDispatch();\r\n\r\n  // Filter states\r\n  const [filters, setFilters] = useState({\r\n    level: '',\r\n    class: '',\r\n    topic: '',\r\n    search: ''\r\n  });\r\n\r\n  const getExamsData = async () => {\r\n    try {\r\n      dispatch(ShowLoading());\r\n      const response = await getAllExams();\r\n      dispatch(HideLoading());\r\n      if (response.success) {\r\n        const examData = response.data.reverse();\r\n        setExams(examData);\r\n        setFilteredExams(examData);\r\n        console.log(response, \"exam\");\r\n      } else {\r\n        message.error(response.message);\r\n      }\r\n    } catch (error) {\r\n      dispatch(HideLoading());\r\n      message.error(error.message);\r\n    }\r\n  };\r\n\r\n  const deleteExam = async (examId) => {\r\n    try {\r\n      dispatch(ShowLoading());\r\n      const response = await deleteExamById({\r\n        examId,\r\n      });\r\n      dispatch(HideLoading());\r\n      if (response.success) {\r\n        message.success(response.message);\r\n        getExamsData();\r\n      } else {\r\n        message.error(response.message);\r\n      }\r\n    } catch (error) {\r\n      dispatch(HideLoading());\r\n      message.error(error.message);\r\n    }\r\n  };\r\n\r\n  // Get unique filter options from exams\r\n  const filterOptions = useMemo(() => {\r\n    const levels = [...new Set(exams.map(exam => exam.level).filter(Boolean))];\r\n    const classes = [...new Set(exams.map(exam => exam.class).filter(Boolean))].sort();\r\n    const topics = [...new Set(exams.map(exam => exam.topic).filter(Boolean))].sort();\r\n\r\n    return { levels, classes, topics };\r\n  }, [exams]);\r\n\r\n  // Apply filters\r\n  useEffect(() => {\r\n    let filtered = [...exams];\r\n\r\n    // Apply level filter\r\n    if (filters.level) {\r\n      filtered = filtered.filter(exam =>\r\n        exam.level && exam.level.toLowerCase() === filters.level.toLowerCase()\r\n      );\r\n    }\r\n\r\n    // Apply class filter\r\n    if (filters.class) {\r\n      filtered = filtered.filter(exam => exam.class === filters.class);\r\n    }\r\n\r\n    // Apply topic filter\r\n    if (filters.topic) {\r\n      filtered = filtered.filter(exam => exam.topic === filters.topic);\r\n    }\r\n\r\n    // Apply search filter\r\n    if (filters.search) {\r\n      const searchTerm = filters.search.toLowerCase();\r\n      filtered = filtered.filter(exam =>\r\n        exam.name?.toLowerCase().includes(searchTerm) ||\r\n        exam.subject?.toLowerCase().includes(searchTerm) ||\r\n        exam.category?.toLowerCase().includes(searchTerm)\r\n      );\r\n    }\r\n\r\n    setFilteredExams(filtered);\r\n  }, [exams, filters]);\r\n\r\n  // Handle filter changes\r\n  const handleFilterChange = (key, value) => {\r\n    setFilters(prev => ({ ...prev, [key]: value }));\r\n  };\r\n\r\n  // Clear all filters\r\n  const clearFilters = () => {\r\n    setFilters({\r\n      level: '',\r\n      class: '',\r\n      topic: '',\r\n      search: ''\r\n    });\r\n  };\r\n\r\n  const columns = [\r\n    {\r\n      title: \"Exam Name\",\r\n      dataIndex: \"name\",\r\n      width: 200,\r\n      ellipsis: true,\r\n    },\r\n    {\r\n      title: \"Level\",\r\n      dataIndex: \"level\",\r\n      width: 100,\r\n      render: (level) => (\r\n        <span className={`px-2 py-1 rounded-full text-xs font-medium ${\r\n          level === 'primary' ? 'bg-green-100 text-green-800' :\r\n          level === 'secondary' ? 'bg-blue-100 text-blue-800' :\r\n          level === 'advance' ? 'bg-purple-100 text-purple-800' :\r\n          'bg-gray-100 text-gray-800'\r\n        }`}>\r\n          {level?.charAt(0).toUpperCase() + level?.slice(1)}\r\n        </span>\r\n      ),\r\n    },\r\n    {\r\n      title: \"Class\",\r\n      dataIndex: \"class\",\r\n      width: 100,\r\n    },\r\n    {\r\n      title: \"Subject\",\r\n      dataIndex: \"subject\",\r\n      width: 120,\r\n    },\r\n    {\r\n      title: \"Topic\",\r\n      dataIndex: \"topic\",\r\n      width: 150,\r\n      ellipsis: true,\r\n      render: (topic) => topic || <span className=\"text-gray-400 italic\">General</span>,\r\n    },\r\n    {\r\n      title: \"Duration\",\r\n      dataIndex: \"duration\",\r\n      width: 100,\r\n      render: (duration) => `${Math.round(duration / 60)} min`,\r\n    },\r\n    {\r\n      title: \"Questions\",\r\n      dataIndex: \"questions\",\r\n      width: 100,\r\n      render: (questions) => questions?.length || 0,\r\n    },\r\n    {\r\n      title: \"Action\",\r\n      dataIndex: \"action\",\r\n      render: (text, record) => (\r\n        <div className=\"flex gap-2\">\r\n          <i\r\n            className=\"ri-pencil-line\"\r\n            onClick={() => navigate(`/admin/exams/edit/${record._id}`)}\r\n          ></i>\r\n          <i\r\n            className=\"ri-delete-bin-line\"\r\n            onClick={() => deleteExam(record._id)}\r\n          ></i>\r\n        </div>\r\n      ),\r\n    },\r\n  ];\r\n  useEffect(() => {\r\n    getExamsData();\r\n  }, []);\r\n  return (\r\n    <div>\r\n      <div className=\"flex justify-between mt-2 items-end\">\r\n        <div className=\"flex items-center gap-4\">\r\n          {/* Dashboard Shortcut */}\r\n          <motion.button\r\n            whileHover={{ scale: 1.05 }}\r\n            whileTap={{ scale: 0.95 }}\r\n            onClick={() => navigate('/admin/dashboard')}\r\n            className=\"flex items-center gap-2 px-3 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors duration-200 shadow-md\"\r\n          >\r\n            <TbDashboard className=\"w-4 h-4\" />\r\n            <span className=\"hidden sm:inline text-sm font-medium\">Dashboard</span>\r\n          </motion.button>\r\n\r\n          <PageTitle title=\"Exams\" />\r\n        </div>\r\n\r\n        <motion.button\r\n          whileHover={{ scale: 1.05 }}\r\n          whileTap={{ scale: 0.95 }}\r\n          className=\"primary-outlined-btn flex items-center gap-2\"\r\n          onClick={() => navigate(\"/admin/exams/add\")}\r\n        >\r\n          <TbPlus className=\"w-4 h-4\" />\r\n          Add Exam\r\n        </motion.button>\r\n      </div>\r\n      <div className=\"divider\"></div>\r\n\r\n      {/* Filters Section */}\r\n      <div className=\"bg-white rounded-lg shadow-sm border p-4 mb-6\">\r\n        <div className=\"flex items-center gap-2 mb-4\">\r\n          <TbFilter className=\"w-5 h-5 text-blue-600\" />\r\n          <h3 className=\"text-lg font-semibold text-gray-800\">Filters</h3>\r\n          {(filters.level || filters.class || filters.topic || filters.search) && (\r\n            <motion.button\r\n              whileHover={{ scale: 1.05 }}\r\n              whileTap={{ scale: 0.95 }}\r\n              onClick={clearFilters}\r\n              className=\"ml-auto flex items-center gap-1 px-3 py-1 text-sm bg-gray-100 hover:bg-gray-200 text-gray-700 rounded-lg transition-colors\"\r\n            >\r\n              <TbX className=\"w-4 h-4\" />\r\n              Clear All\r\n            </motion.button>\r\n          )}\r\n        </div>\r\n\r\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\">\r\n          {/* Search Filter */}\r\n          <div>\r\n            <label className=\"block text-sm font-medium text-gray-700 mb-2\">\r\n              Search Exams\r\n            </label>\r\n            <Input\r\n              placeholder=\"Search by name, subject, category...\"\r\n              prefix={<TbSearch className=\"w-4 h-4 text-gray-400\" />}\r\n              value={filters.search}\r\n              onChange={(e) => handleFilterChange('search', e.target.value)}\r\n              allowClear\r\n            />\r\n          </div>\r\n\r\n          {/* Level Filter */}\r\n          <div>\r\n            <label className=\"block text-sm font-medium text-gray-700 mb-2\">\r\n              Level\r\n            </label>\r\n            <Select\r\n              placeholder=\"Select level\"\r\n              value={filters.level || undefined}\r\n              onChange={(value) => handleFilterChange('level', value)}\r\n              allowClear\r\n              className=\"w-full\"\r\n            >\r\n              {filterOptions.levels.map(level => (\r\n                <Select.Option key={level} value={level}>\r\n                  {level.charAt(0).toUpperCase() + level.slice(1)}\r\n                </Select.Option>\r\n              ))}\r\n            </Select>\r\n          </div>\r\n\r\n          {/* Class Filter */}\r\n          <div>\r\n            <label className=\"block text-sm font-medium text-gray-700 mb-2\">\r\n              Class\r\n            </label>\r\n            <Select\r\n              placeholder=\"Select class\"\r\n              value={filters.class || undefined}\r\n              onChange={(value) => handleFilterChange('class', value)}\r\n              allowClear\r\n              className=\"w-full\"\r\n            >\r\n              {filterOptions.classes.map(className => (\r\n                <Select.Option key={className} value={className}>\r\n                  {className}\r\n                </Select.Option>\r\n              ))}\r\n            </Select>\r\n          </div>\r\n\r\n          {/* Topic Filter */}\r\n          <div>\r\n            <label className=\"block text-sm font-medium text-gray-700 mb-2\">\r\n              Topic\r\n            </label>\r\n            <Select\r\n              placeholder=\"Select topic\"\r\n              value={filters.topic || undefined}\r\n              onChange={(value) => handleFilterChange('topic', value)}\r\n              allowClear\r\n              className=\"w-full\"\r\n            >\r\n              {filterOptions.topics.map(topic => (\r\n                <Select.Option key={topic} value={topic}>\r\n                  {topic}\r\n                </Select.Option>\r\n              ))}\r\n            </Select>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Filter Results Summary */}\r\n        <div className=\"mt-4 flex items-center justify-between text-sm text-gray-600\">\r\n          <span>\r\n            Showing {filteredExams.length} of {exams.length} exams\r\n          </span>\r\n          {filteredExams.length !== exams.length && (\r\n            <span className=\"text-blue-600 font-medium\">\r\n              Filters applied\r\n            </span>\r\n          )}\r\n        </div>\r\n      </div>\r\n\r\n      <Table columns={columns} dataSource={filteredExams} />\r\n    </div>\r\n  );\r\n}\r\n\r\nexport default Exams;\r\n"], "names": ["_ref", "title", "isMobile", "setIsMobile", "useState", "useEffect", "window", "innerWidth", "_jsx", "className", "children", "navigate", "useNavigate", "exams", "setExams", "filteredExams", "setFilteredExams", "dispatch", "useDispatch", "filters", "setFilters", "level", "class", "topic", "search", "getExamsData", "async", "ShowLoading", "response", "getAllExams", "HideLoading", "success", "examData", "data", "reverse", "console", "log", "message", "error", "filterOptions", "useMemo", "levels", "Set", "map", "exam", "filter", "Boolean", "classes", "sort", "topics", "filtered", "toLowerCase", "searchTerm", "_exam$name", "_exam$subject", "_exam$category", "name", "includes", "subject", "category", "handleFilterChange", "key", "value", "prev", "_objectSpread", "columns", "dataIndex", "width", "ellipsis", "render", "concat", "char<PERSON>t", "toUpperCase", "slice", "duration", "Math", "round", "questions", "length", "text", "record", "_jsxs", "onClick", "_id", "deleteExamById", "examId", "deleteExam", "motion", "button", "whileHover", "scale", "whileTap", "TbDashboard", "Page<PERSON><PERSON>le", "TbPlus", "Tb<PERSON><PERSON>er", "clearFilters", "TbX", "Input", "placeholder", "prefix", "TbSearch", "onChange", "e", "target", "allowClear", "Select", "undefined", "Option", "Table", "dataSource"], "sourceRoot": ""}