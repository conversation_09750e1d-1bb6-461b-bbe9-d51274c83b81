{"ast": null, "code": "import { useRef } from 'react';\nimport useLayoutEffect from \"rc-util/es/hooks/useLayoutEffect\";\nvar SMOOTH_PTG = 14 / 15;\nexport default function useMobileTouchMove(inVirtual, listRef, callback) {\n  var touchedRef = useRef(false);\n  var touchYRef = useRef(0);\n  var elementRef = useRef(null);\n  // Smooth scroll\n  var intervalRef = useRef(null);\n  /* eslint-disable prefer-const */\n  var cleanUpEvents;\n  var onTouchMove = function onTouchMove(e) {\n    if (touchedRef.current) {\n      var currentY = Math.ceil(e.touches[0].pageY);\n      var offsetY = touchYRef.current - currentY;\n      touchYRef.current = currentY;\n      if (callback(offsetY)) {\n        e.preventDefault();\n      }\n      // Smooth interval\n      clearInterval(intervalRef.current);\n      intervalRef.current = setInterval(function () {\n        offsetY *= SMOOTH_PTG;\n        if (!callback(offsetY, true) || Math.abs(offsetY) <= 0.1) {\n          clearInterval(intervalRef.current);\n        }\n      }, 16);\n    }\n  };\n  var onTouchEnd = function onTouchEnd() {\n    touchedRef.current = false;\n    cleanUpEvents();\n  };\n  var onTouchStart = function onTouchStart(e) {\n    cleanUpEvents();\n    if (e.touches.length === 1 && !touchedRef.current) {\n      touchedRef.current = true;\n      touchYRef.current = Math.ceil(e.touches[0].pageY);\n      elementRef.current = e.target;\n      elementRef.current.addEventListener('touchmove', onTouchMove);\n      elementRef.current.addEventListener('touchend', onTouchEnd);\n    }\n  };\n  cleanUpEvents = function cleanUpEvents() {\n    if (elementRef.current) {\n      elementRef.current.removeEventListener('touchmove', onTouchMove);\n      elementRef.current.removeEventListener('touchend', onTouchEnd);\n    }\n  };\n  useLayoutEffect(function () {\n    if (inVirtual) {\n      listRef.current.addEventListener('touchstart', onTouchStart);\n    }\n    return function () {\n      var _listRef$current;\n      (_listRef$current = listRef.current) === null || _listRef$current === void 0 ? void 0 : _listRef$current.removeEventListener('touchstart', onTouchStart);\n      cleanUpEvents();\n      clearInterval(intervalRef.current);\n    };\n  }, [inVirtual]);\n}", "map": {"version": 3, "names": ["useRef", "useLayoutEffect", "SMOOTH_PTG", "useMobileTouchMove", "inVirtual", "listRef", "callback", "touchedRef", "touchYRef", "elementRef", "intervalRef", "cleanUpEvents", "onTouchMove", "e", "current", "currentY", "Math", "ceil", "touches", "pageY", "offsetY", "preventDefault", "clearInterval", "setInterval", "abs", "onTouchEnd", "onTouchStart", "length", "target", "addEventListener", "removeEventListener", "_listRef$current"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/node_modules/rc-virtual-list/es/hooks/useMobileTouchMove.js"], "sourcesContent": ["import { useRef } from 'react';\nimport useLayoutEffect from \"rc-util/es/hooks/useLayoutEffect\";\nvar SMOOTH_PTG = 14 / 15;\nexport default function useMobileTouchMove(inVirtual, listRef, callback) {\n  var touchedRef = useRef(false);\n  var touchYRef = useRef(0);\n  var elementRef = useRef(null);\n  // Smooth scroll\n  var intervalRef = useRef(null);\n  /* eslint-disable prefer-const */\n  var cleanUpEvents;\n  var onTouchMove = function onTouchMove(e) {\n    if (touchedRef.current) {\n      var currentY = Math.ceil(e.touches[0].pageY);\n      var offsetY = touchYRef.current - currentY;\n      touchYRef.current = currentY;\n      if (callback(offsetY)) {\n        e.preventDefault();\n      }\n      // Smooth interval\n      clearInterval(intervalRef.current);\n      intervalRef.current = setInterval(function () {\n        offsetY *= SMOOTH_PTG;\n        if (!callback(offsetY, true) || Math.abs(offsetY) <= 0.1) {\n          clearInterval(intervalRef.current);\n        }\n      }, 16);\n    }\n  };\n  var onTouchEnd = function onTouchEnd() {\n    touchedRef.current = false;\n    cleanUpEvents();\n  };\n  var onTouchStart = function onTouchStart(e) {\n    cleanUpEvents();\n    if (e.touches.length === 1 && !touchedRef.current) {\n      touchedRef.current = true;\n      touchYRef.current = Math.ceil(e.touches[0].pageY);\n      elementRef.current = e.target;\n      elementRef.current.addEventListener('touchmove', onTouchMove);\n      elementRef.current.addEventListener('touchend', onTouchEnd);\n    }\n  };\n  cleanUpEvents = function cleanUpEvents() {\n    if (elementRef.current) {\n      elementRef.current.removeEventListener('touchmove', onTouchMove);\n      elementRef.current.removeEventListener('touchend', onTouchEnd);\n    }\n  };\n  useLayoutEffect(function () {\n    if (inVirtual) {\n      listRef.current.addEventListener('touchstart', onTouchStart);\n    }\n    return function () {\n      var _listRef$current;\n      (_listRef$current = listRef.current) === null || _listRef$current === void 0 ? void 0 : _listRef$current.removeEventListener('touchstart', onTouchStart);\n      cleanUpEvents();\n      clearInterval(intervalRef.current);\n    };\n  }, [inVirtual]);\n}"], "mappings": "AAAA,SAASA,MAAM,QAAQ,OAAO;AAC9B,OAAOC,eAAe,MAAM,kCAAkC;AAC9D,IAAIC,UAAU,GAAG,EAAE,GAAG,EAAE;AACxB,eAAe,SAASC,kBAAkBA,CAACC,SAAS,EAAEC,OAAO,EAAEC,QAAQ,EAAE;EACvE,IAAIC,UAAU,GAAGP,MAAM,CAAC,KAAK,CAAC;EAC9B,IAAIQ,SAAS,GAAGR,MAAM,CAAC,CAAC,CAAC;EACzB,IAAIS,UAAU,GAAGT,MAAM,CAAC,IAAI,CAAC;EAC7B;EACA,IAAIU,WAAW,GAAGV,MAAM,CAAC,IAAI,CAAC;EAC9B;EACA,IAAIW,aAAa;EACjB,IAAIC,WAAW,GAAG,SAASA,WAAWA,CAACC,CAAC,EAAE;IACxC,IAAIN,UAAU,CAACO,OAAO,EAAE;MACtB,IAAIC,QAAQ,GAAGC,IAAI,CAACC,IAAI,CAACJ,CAAC,CAACK,OAAO,CAAC,CAAC,CAAC,CAACC,KAAK,CAAC;MAC5C,IAAIC,OAAO,GAAGZ,SAAS,CAACM,OAAO,GAAGC,QAAQ;MAC1CP,SAAS,CAACM,OAAO,GAAGC,QAAQ;MAC5B,IAAIT,QAAQ,CAACc,OAAO,CAAC,EAAE;QACrBP,CAAC,CAACQ,cAAc,CAAC,CAAC;MACpB;MACA;MACAC,aAAa,CAACZ,WAAW,CAACI,OAAO,CAAC;MAClCJ,WAAW,CAACI,OAAO,GAAGS,WAAW,CAAC,YAAY;QAC5CH,OAAO,IAAIlB,UAAU;QACrB,IAAI,CAACI,QAAQ,CAACc,OAAO,EAAE,IAAI,CAAC,IAAIJ,IAAI,CAACQ,GAAG,CAACJ,OAAO,CAAC,IAAI,GAAG,EAAE;UACxDE,aAAa,CAACZ,WAAW,CAACI,OAAO,CAAC;QACpC;MACF,CAAC,EAAE,EAAE,CAAC;IACR;EACF,CAAC;EACD,IAAIW,UAAU,GAAG,SAASA,UAAUA,CAAA,EAAG;IACrClB,UAAU,CAACO,OAAO,GAAG,KAAK;IAC1BH,aAAa,CAAC,CAAC;EACjB,CAAC;EACD,IAAIe,YAAY,GAAG,SAASA,YAAYA,CAACb,CAAC,EAAE;IAC1CF,aAAa,CAAC,CAAC;IACf,IAAIE,CAAC,CAACK,OAAO,CAACS,MAAM,KAAK,CAAC,IAAI,CAACpB,UAAU,CAACO,OAAO,EAAE;MACjDP,UAAU,CAACO,OAAO,GAAG,IAAI;MACzBN,SAAS,CAACM,OAAO,GAAGE,IAAI,CAACC,IAAI,CAACJ,CAAC,CAACK,OAAO,CAAC,CAAC,CAAC,CAACC,KAAK,CAAC;MACjDV,UAAU,CAACK,OAAO,GAAGD,CAAC,CAACe,MAAM;MAC7BnB,UAAU,CAACK,OAAO,CAACe,gBAAgB,CAAC,WAAW,EAAEjB,WAAW,CAAC;MAC7DH,UAAU,CAACK,OAAO,CAACe,gBAAgB,CAAC,UAAU,EAAEJ,UAAU,CAAC;IAC7D;EACF,CAAC;EACDd,aAAa,GAAG,SAASA,aAAaA,CAAA,EAAG;IACvC,IAAIF,UAAU,CAACK,OAAO,EAAE;MACtBL,UAAU,CAACK,OAAO,CAACgB,mBAAmB,CAAC,WAAW,EAAElB,WAAW,CAAC;MAChEH,UAAU,CAACK,OAAO,CAACgB,mBAAmB,CAAC,UAAU,EAAEL,UAAU,CAAC;IAChE;EACF,CAAC;EACDxB,eAAe,CAAC,YAAY;IAC1B,IAAIG,SAAS,EAAE;MACbC,OAAO,CAACS,OAAO,CAACe,gBAAgB,CAAC,YAAY,EAAEH,YAAY,CAAC;IAC9D;IACA,OAAO,YAAY;MACjB,IAAIK,gBAAgB;MACpB,CAACA,gBAAgB,GAAG1B,OAAO,CAACS,OAAO,MAAM,IAAI,IAAIiB,gBAAgB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,gBAAgB,CAACD,mBAAmB,CAAC,YAAY,EAAEJ,YAAY,CAAC;MACxJf,aAAa,CAAC,CAAC;MACfW,aAAa,CAACZ,WAAW,CAACI,OAAO,CAAC;IACpC,CAAC;EACH,CAAC,EAAE,CAACV,SAAS,CAAC,CAAC;AACjB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}