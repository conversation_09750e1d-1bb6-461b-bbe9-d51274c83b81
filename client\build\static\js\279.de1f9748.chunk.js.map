{"version": 3, "file": "static/js/279.de1f9748.chunk.js", "mappings": "oNAMA,MA8PA,EA9P2BA,IAAgD,IAADC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAA,IAA9C,cAAEC,EAAa,eAAEC,EAAc,OAAEC,GAAQnB,EACnE,MAAOoB,EAASC,IAAcC,EAAAA,EAAAA,WAAS,IAChCC,EAAMC,IAAWF,EAAAA,EAAAA,UAAS,OAC1BG,EAAOC,IAAYJ,EAAAA,EAAAA,UAAS,OAEnCK,EAAAA,EAAAA,YAAU,KACRC,GAAgB,GACf,CAACX,IAEJ,MAAMW,EAAiBC,UACrB,IACER,GAAW,GACXK,EAAS,MAET,MAAMI,OCjBgBD,WAC1B,IAEE,aADuBE,EAAAA,QAAcC,KAAK,4BAA6BC,IACvDC,IAClB,CAAE,MAAOT,GAAQ,IAADU,EACd,OAAqB,QAAdA,EAAAV,EAAMK,gBAAQ,IAAAK,OAAA,EAAdA,EAAgBD,OAAQ,CAAEE,SAAS,EAAOC,QAAS,gBAC5D,GDW2BC,CAAa,CAClCC,MAAOtB,EAAcsB,MACrBC,MAAOvB,EAAcuB,QAGnBV,EAASM,SACXZ,EAAQM,EAASI,MACjBO,QAAQC,IAAI,4BAAwBZ,EAASI,QAE7CR,EAASI,EAASO,SAAW,6BAC7BA,EAAAA,GAAQZ,MAAMK,EAASO,SAAW,6BAEtC,CAAE,MAAOZ,GACPgB,QAAQhB,MAAM,oCAAgCA,GAC9CC,EAAS,2CACTW,EAAAA,GAAQZ,MAAM,0CAChB,CAAC,QACCJ,GAAW,EACb,GAYIsB,EAAoB,CACxBC,OAAQ,CAAEC,QAAS,EAAGC,EAAG,IACzBC,QAAS,CACPF,QAAS,EACTC,EAAG,EACHE,WAAY,CAAEC,SAAU,GAAKC,KAAM,aAavC,OAAI9B,GAEA+B,EAAAA,EAAAA,KAACC,EAAAA,EAAOC,IAAG,CACTC,UAAU,wGACVC,SAAUZ,EACVa,QAAQ,SACRC,QAAQ,UAASC,UAEjBC,EAAAA,EAAAA,MAAA,OAAKL,UAAU,cAAaI,SAAA,EAC1BP,EAAAA,EAAAA,KAAA,OAAKG,UAAU,mFAAkFI,UAC/FP,EAAAA,EAAAA,KAACS,EAAAA,IAAQ,CAACN,UAAU,0CAEtBH,EAAAA,EAAAA,KAAA,MAAIG,UAAU,2CAA0CI,SAAC,qCAGzDP,EAAAA,EAAAA,KAAA,KAAGG,UAAU,gBAAeI,SAAC,qEAQjCjC,GAEA0B,EAAAA,EAAAA,KAACC,EAAAA,EAAOC,IAAG,CACTC,UAAU,wGACVC,SAAUZ,EACVa,QAAQ,SACRC,QAAQ,UAASC,UAEjBC,EAAAA,EAAAA,MAAA,OAAKL,UAAU,iEAAgEI,SAAA,EAC7EP,EAAAA,EAAAA,KAAA,OAAKG,UAAU,kFAAiFI,UAC9FP,EAAAA,EAAAA,KAACU,EAAAA,IAAc,CAACP,UAAU,4BAE5BH,EAAAA,EAAAA,KAAA,MAAIG,UAAU,2CAA0CI,SAAC,wBAGzDP,EAAAA,EAAAA,KAAA,KAAGG,UAAU,qBAAoBI,SAC9BjC,KAEHkC,EAAAA,EAAAA,MAAA,OAAKL,UAAU,YAAWI,SAAA,EACxBP,EAAAA,EAAAA,KAAA,UACEW,QAASlC,EACT0B,UAAU,qGAAoGI,SAC/G,eAGDP,EAAAA,EAAAA,KAAA,UACEW,QAAS3C,EACTmC,UAAU,kHAAiHI,SAC5H,qBAUTP,EAAAA,EAAAA,KAACC,EAAAA,EAAOC,IAAG,CACTC,UAAU,wGACVC,SAAUZ,EACVa,QAAQ,SACRC,QAAQ,UAASC,UAEjBC,EAAAA,EAAAA,MAAA,OAAKL,UAAU,2BAA0BI,SAAA,EAEvCC,EAAAA,EAAAA,MAACP,EAAAA,EAAOC,IAAG,CACTC,UAAU,2BACVE,QAAS,CAAEX,QAAS,EAAGC,GAAI,IAC3BW,QAAS,CAAEZ,QAAS,EAAGC,EAAG,GAC1BE,WAAY,CAAEC,SAAU,IAAMS,SAAA,EAE9BC,EAAAA,EAAAA,MAAA,MAAIL,UAAU,gEAA+DI,SAAA,CAAC,YAClEzC,EAAc8C,KAAK,qBAE/BJ,EAAAA,EAAAA,MAAA,KAAGL,UAAU,0CAAyCI,SAAA,CAAC,0CACfC,EAAAA,EAAAA,MAAA,QAAML,UAAU,8BAA6BI,SAAA,CAAEzC,EAAcsB,MAAM,IAAEtB,EAAcuB,SAAa,gBAK1ImB,EAAAA,EAAAA,MAACP,EAAAA,EAAOC,IAAG,CACTC,UAAU,iDACVC,SA/Fa,CACnBX,OAAQ,CAAEC,QAAS,EAAGmB,MAAO,IAC7BjB,QAAS,CACPF,QAAS,EACTmB,MAAO,EACPhB,WAAY,CAAEC,SAAU,GAAKgB,MAAO,MA2FhCT,QAAQ,SACRC,QAAQ,UAASC,SAAA,EAGjBP,EAAAA,EAAAA,KAAA,OAAKG,UAAU,kFAAiFI,UAC9FC,EAAAA,EAAAA,MAAA,OAAKL,UAAU,2CAA0CI,SAAA,EACvDP,EAAAA,EAAAA,KAAA,OAAKG,UAAU,oCAAmCI,UAChDP,EAAAA,EAAAA,KAACe,EAAAA,IAAO,CAACZ,UAAU,6BAErBK,EAAAA,EAAAA,MAAA,OAAKL,UAAU,iBAAgBI,SAAA,EAC7BP,EAAAA,EAAAA,KAAA,MAAIG,UAAU,yCAAwCI,SAAM,OAAJnC,QAAI,IAAJA,GAAU,QAANtB,EAAJsB,EAAM4C,YAAI,IAAAlE,OAAN,EAAJA,EAAY8D,QACpEJ,EAAAA,EAAAA,MAAA,KAAGL,UAAU,qCAAoCI,SAAA,CAC1C,OAAJnC,QAAI,IAAJA,GAAU,QAANrB,EAAJqB,EAAM4C,YAAI,IAAAjE,OAAN,EAAJA,EAAYkE,QAAQ,WAAInD,EAAcsB,MAAM8B,OAAO,GAAGC,cAAgBrD,EAAcsB,MAAMgC,MAAM,GAAG,qBAO5GZ,EAAAA,EAAAA,MAAA,OAAKL,UAAU,aAAYI,SAAA,EACzBC,EAAAA,EAAAA,MAAA,OAAKL,UAAU,8DAA6DI,SAAA,EAC1EC,EAAAA,EAAAA,MAAA,OAAKL,UAAU,wCAAuCI,SAAA,EACpDP,EAAAA,EAAAA,KAACU,EAAAA,IAAc,CAACP,UAAU,wCAC1BH,EAAAA,EAAAA,KAAA,OAAKG,UAAU,mCAAkCI,UAC1C,OAAJnC,QAAI,IAAJA,GAAe,QAAXpB,EAAJoB,EAAMiD,iBAAS,IAAArE,OAAX,EAAJA,EAAiBsE,sBAA0B,OAAJlD,QAAI,IAAJA,GAAU,QAANnB,EAAJmB,EAAM4C,YAAI,IAAA/D,GAAW,QAAXC,EAAVD,EAAYsE,iBAAS,IAAArE,OAAjB,EAAJA,EAAuBsE,SAAU,KAE3ExB,EAAAA,EAAAA,KAAA,OAAKG,UAAU,wBAAuBI,SAAC,kBAGzCC,EAAAA,EAAAA,MAAA,OAAKL,UAAU,yCAAwCI,SAAA,EACrDP,EAAAA,EAAAA,KAACyB,EAAAA,IAAO,CAACtB,UAAU,yCACnBH,EAAAA,EAAAA,KAAA,OAAKG,UAAU,mCAAkCI,UAC1C,OAAJnC,QAAI,IAAJA,GAAe,QAAXjB,EAAJiB,EAAMiD,iBAAS,IAAAlE,OAAX,EAAJA,EAAiBuE,iBAAqB,OAAJtD,QAAI,IAAJA,GAAU,QAANhB,EAAJgB,EAAM4C,YAAI,IAAA5D,OAAN,EAAJA,EAAY0C,WAAY,KAE7DE,EAAAA,EAAAA,KAAA,OAAKG,UAAU,wBAAuBI,SAAC,gBAGzCC,EAAAA,EAAAA,MAAA,OAAKL,UAAU,0CAAyCI,SAAA,EACtDP,EAAAA,EAAAA,KAACe,EAAAA,IAAO,CAACZ,UAAU,0CACnBH,EAAAA,EAAAA,KAAA,OAAKG,UAAU,mCAAkCI,UAC1C,OAAJnC,QAAI,IAAJA,GAAU,QAANf,EAAJe,EAAM4C,YAAI,IAAA3D,OAAN,EAAJA,EAAYsE,eAAgBC,KAAKC,KAA4C,KAAlC,OAAJzD,QAAI,IAAJA,GAAU,QAANd,EAAJc,EAAM4C,YAAI,IAAA1D,GAAW,QAAXC,EAAVD,EAAYiE,iBAAS,IAAAhE,OAAjB,EAAJA,EAAuBiE,SAAU,OAE3ExB,EAAAA,EAAAA,KAAA,OAAKG,UAAU,wBAAuBI,SAAC,qBAK3CP,EAAAA,EAAAA,KAAA,OAAKG,UAAU,wDAAuDI,UACpEC,EAAAA,EAAAA,MAAA,OAAKL,UAAU,yEAAwEI,SAAA,EACrFC,EAAAA,EAAAA,MAAA,OAAKL,UAAU,oBAAmBI,SAAA,EAChCP,EAAAA,EAAAA,KAACyB,EAAAA,IAAO,CAACtB,UAAU,gCACnBK,EAAAA,EAAAA,MAAA,QAAAD,SAAA,EAAMP,EAAAA,EAAAA,KAAA,UAAAO,UAAa,OAAJnC,QAAI,IAAJA,GAAe,QAAXZ,EAAJY,EAAMiD,iBAAS,IAAA7D,OAAX,EAAJA,EAAiBkE,iBAAqB,OAAJtD,QAAI,IAAJA,GAAU,QAANX,EAAJW,EAAM4C,YAAI,IAAAvD,OAAN,EAAJA,EAAYqC,WAAY,IAAW,kBAEtFU,EAAAA,EAAAA,MAAA,OAAKL,UAAU,oBAAmBI,SAAA,EAChCP,EAAAA,EAAAA,KAACU,EAAAA,IAAc,CAACP,UAAU,iCAC1BK,EAAAA,EAAAA,MAAA,QAAAD,SAAA,EAAMP,EAAAA,EAAAA,KAAA,UAAAO,UAAa,OAAJnC,QAAI,IAAJA,GAAe,QAAXV,EAAJU,EAAMiD,iBAAS,IAAA3D,OAAX,EAAJA,EAAiB4D,qBAAsB,IAAW,oBAEnEd,EAAAA,EAAAA,MAAA,OAAKL,UAAU,oBAAmBI,SAAA,EAChCP,EAAAA,EAAAA,KAACe,EAAAA,IAAO,CAACZ,UAAU,kCACnBK,EAAAA,EAAAA,MAAA,QAAAD,SAAA,CAAM,eAAWP,EAAAA,EAAAA,KAAA,UAAAO,UAAa,OAAJnC,QAAI,IAAJA,GAAU,QAANT,EAAJS,EAAM4C,YAAI,IAAArD,OAAN,EAAJA,EAAYgE,eAAgBC,KAAKC,KAA4C,KAAlC,OAAJzD,QAAI,IAAJA,GAAU,QAANR,EAAJQ,EAAM4C,YAAI,IAAApD,GAAW,QAAXC,EAAVD,EAAY2D,iBAAS,IAAA1D,OAAjB,EAAJA,EAAuB2D,SAAU,aAEpGhB,EAAAA,EAAAA,MAAA,OAAKL,UAAU,oBAAmBI,SAAA,EAChCP,EAAAA,EAAAA,KAAC8B,EAAAA,IAAY,CAAC3B,UAAU,kCACxBH,EAAAA,EAAAA,KAAA,QAAAO,SAAM,qCAQZC,EAAAA,EAAAA,MAAA,OAAKL,UAAU,kCAAiCI,SAAA,EAC9CP,EAAAA,EAAAA,KAAA,UACEW,QAAS3C,EACTmC,UAAU,kHAAiHI,SAC5H,aAGDC,EAAAA,EAAAA,MAACP,EAAAA,EAAO8B,OAAM,CACZpB,QAjMUqB,KAClB5D,GAAQA,EAAK4C,MACfjD,GAAckE,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAC,CAAC,EACX7D,GAAI,IACPN,kBAEJ,EA4LYqC,UAAU,0NACV+B,WAAY,CAAErB,MAAO,MACrBsB,SAAU,CAAEtB,MAAO,KAAON,SAAA,EAE1BP,EAAAA,EAAAA,KAAA,QAAAO,SAAM,sBACNP,EAAAA,EAAAA,KAAC8B,EAAAA,IAAY,CAAC3B,UAAU,yBAOhCH,EAAAA,EAAAA,KAACC,EAAAA,EAAOC,IAAG,CACTC,UAAU,yCACVE,QAAS,CAAEX,QAAS,GACpBY,QAAS,CAAEZ,QAAS,GACpBG,WAAY,CAAEiB,MAAO,IAAMP,SAC5B,mGAIQ,EE+WjB,EAxmBsB1D,IAAuC,IAAtC,SAAEuF,EAAQ,WAAEC,EAAU,OAAErE,GAAQnB,EACrD,MAAM,KAAEmE,EAAI,cAAElD,GAAkBsE,EAC1Bb,EAAYP,EAAKO,WAAa,IAE7Be,EAAsBC,IAA2BpE,EAAAA,EAAAA,UAAS,IAC1DqE,EAAiBC,IAAsBtE,EAAAA,EAAAA,UAAS,CAAC,IACjDuE,EAAUC,IAAexE,EAAAA,EAAAA,UAAS6C,EAAKlB,UAAY,MACnD8C,EAAcC,IAAmB1E,EAAAA,EAAAA,WAAS,IAC1C2E,IAAa3E,EAAAA,EAAAA,UAAS4E,KAAKC,QAGlCxE,EAAAA,EAAAA,YAAU,KACR,GAAIkE,GAAY,EAEd,YADAO,IAIF,MAAMC,EAAQC,aAAY,KACxBR,GAAYS,GAAQA,EAAO,GAAE,GAC5B,KAEH,MAAO,IAAMC,cAAcH,EAAM,GAChC,CAACR,IAGJ,MAQMY,EAAqBA,CAACC,EAAYC,KACtCC,IACAhB,GAAmBW,IAAInB,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GAClBmB,GAAI,IACP,CAACG,GAAaC,KACb,EAICP,GAAmBS,EAAAA,EAAAA,cAAYhF,UACnC,IAAIkE,EAAJ,CAEAe,IACAd,GAAgB,GAChB,IACE,MAAMe,EAAYhC,KAAKiC,OAAOd,KAAKC,MAAQF,GAAa,KAElDnE,OD5CqBD,WAC/B,IAEE,aADuBE,EAAAA,QAAcC,KAAK,iCAAkCC,IAC5DC,IAClB,CAAE,MAAOT,GAAQ,IAADwF,EACd,OAAqB,QAAdA,EAAAxF,EAAMK,gBAAQ,IAAAmF,OAAA,EAAdA,EAAgB/E,OAAQ,CAAEE,SAAS,EAAOC,QAAS,gBAC5D,GCsC2B6E,CAAkB,CACvCC,OAAQhD,EAAKiD,IACbC,QAAS1B,EACToB,YACA9F,kBAGEa,EAASM,QACXoD,EAAW1D,EAASI,OAEpBG,EAAAA,GAAQZ,MAAMK,EAASO,SAAW,yBAClC2D,GAAgB,GAEpB,CAAE,MAAOvE,GACPgB,QAAQhB,MAAM,sCAAkCA,GAChDY,EAAAA,GAAQZ,MAAM,2CACduE,GAAgB,EAClB,CAxBwB,CAwBxB,GACC,CAAC7B,EAAKiD,IAAKzB,EAAiB1E,EAAegF,EAAWT,EAAYO,IAG/DuB,EAAsBA,KAC1B,IAEE,MAAMC,EAAe,IAAKC,OAAOC,cAAgBD,OAAOE,oBAClDC,EAAaJ,EAAaK,mBAC1BC,EAAWN,EAAaO,aAE9BH,EAAWI,QAAQF,GACnBA,EAASE,QAAQR,EAAaS,aAE9BL,EAAWM,UAAUC,eAAe,IAAKX,EAAaY,aACtDR,EAAWM,UAAUG,6BAA6B,IAAKb,EAAaY,YAAc,IAElFN,EAASQ,KAAKH,eAAe,GAAKX,EAAaY,aAC/CN,EAASQ,KAAKD,6BAA6B,IAAMb,EAAaY,YAAc,IAE5ER,EAAWW,MAAMf,EAAaY,aAC9BR,EAAWY,KAAKhB,EAAaY,YAAc,GAC7C,CAAE,MAAO1G,GACPgB,QAAQC,IAAI,iCACd,GAGIkE,EAAmBA,KACvB,IAEE,MAAMW,EAAe,IAAKC,OAAOC,cAAgBD,OAAOE,oBAClDC,EAAaJ,EAAaK,mBAC1BC,EAAWN,EAAaO,aAE9BH,EAAWI,QAAQF,GACnBA,EAASE,QAAQR,EAAaS,aAE9BL,EAAWM,UAAUC,eAAe,IAAKX,EAAaY,aACtDR,EAAWM,UAAUC,eAAe,IAAKX,EAAaY,YAAc,IACpER,EAAWM,UAAUC,eAAe,IAAKX,EAAaY,YAAc,IAEpEN,EAASQ,KAAKH,eAAe,IAAMX,EAAaY,aAChDN,EAASQ,KAAKD,6BAA6B,IAAMb,EAAaY,YAAc,IAE5ER,EAAWW,MAAMf,EAAaY,aAC9BR,EAAWY,KAAKhB,EAAaY,YAAc,GAC7C,CAAE,MAAO1G,GACPgB,QAAQC,IAAI,8BACd,GAGIoE,EAAkBA,KACtB,IAEE,MAAMS,EAAe,IAAKC,OAAOC,cAAgBD,OAAOE,oBAClDC,EAAaJ,EAAaK,mBAC1BC,EAAWN,EAAaO,aAE9BH,EAAWI,QAAQF,GACnBA,EAASE,QAAQR,EAAaS,aAE9BL,EAAWM,UAAUC,eAAe,IAAKX,EAAaY,aACtDR,EAAWM,UAAUC,eAAe,IAAKX,EAAaY,YAAc,KACpER,EAAWM,UAAUC,eAAe,IAAKX,EAAaY,YAAc,IACpER,EAAWM,UAAUC,eAAe,IAAKX,EAAaY,YAAc,KAEpEN,EAASQ,KAAKH,eAAe,GAAKX,EAAaY,aAC/CN,EAASQ,KAAKD,6BAA6B,IAAMb,EAAaY,YAAc,IAE5ER,EAAWW,MAAMf,EAAaY,aAC9BR,EAAWY,KAAKhB,EAAaY,YAAc,GAC7C,CAAE,MAAO1G,GACPgB,QAAQC,IAAI,6BACd,GAuBF,GAAyB,IAArBgC,EAAUC,OACZ,OACExB,EAAAA,EAAAA,KAAA,OAAKG,UAAU,wGAAuGI,UACpHC,EAAAA,EAAAA,MAAA,OAAKL,UAAU,cAAaI,SAAA,EAC1BP,EAAAA,EAAAA,KAAA,MAAIG,UAAU,2CAA0CI,SAAC,4BACzDP,EAAAA,EAAAA,KAAA,KAAGG,UAAU,qBAAoBI,SAAC,2CAClCP,EAAAA,EAAAA,KAAA,UACEW,QAAS3C,EACTmC,UAAU,kFAAiFI,SAC5F,iBAQT,MAAM8E,EAAkB9D,EAAUe,GAC5BgD,EAAiBhD,IAAyBf,EAAUC,OAAS,EAC7D+D,EAAoBC,OAAOC,KAAKjD,GAAiBhB,OAGjDkE,GAA8B,OAAfL,QAAe,IAAfA,OAAe,EAAfA,EAAiBM,QAAuB,OAAfN,QAAe,IAAfA,OAAe,EAAfA,EAAiBO,aAAc,MACvEC,EAAyB,QAAjBH,GAA2C,YAAjBA,GAA+C,oBAAjBA,EAChEI,EAA+B,SAAjBJ,GAA4C,sBAAjBA,GAAyD,cAAjBA,EACjFK,EAA6B,YAAjBL,GAA+C,YAAjBA,IAA6C,OAAfL,QAAe,IAAfA,OAAe,EAAfA,EAAiBW,OAG/F,IAAIC,EAAkB,GAWtB,OAVIJ,IAEAI,EADEC,MAAMC,QAAuB,OAAfd,QAAe,IAAfA,OAAe,EAAfA,EAAiBe,SACff,EAAgBe,QACW,kBAAd,OAAff,QAAe,IAAfA,OAAe,EAAfA,EAAiBe,UAAqD,QAAd,OAAff,QAAe,IAAfA,OAAe,EAAfA,EAAiBe,SACxDZ,OAAOa,OAAOhB,EAAgBe,SAE9B,CAAgB,OAAff,QAAe,IAAfA,OAAe,EAAfA,EAAiBiB,QAAwB,OAAfjB,QAAe,IAAfA,OAAe,EAAfA,EAAiBkB,QAAwB,OAAflB,QAAe,IAAfA,OAAe,EAAfA,EAAiBmB,QAAwB,OAAfnB,QAAe,IAAfA,OAAe,EAAfA,EAAiBoB,SAASC,OAAOC,WAKpInG,EAAAA,EAAAA,MAAA,OAAKL,UAAU,mBAAmByG,MAAO,CACvCC,UAAW,QACXC,WAAY,oDACZC,MAAO,OACPC,SAAU,UACVzG,SAAA,EAEAP,EAAAA,EAAAA,KAAA,OAAKG,UAAU,eAAeyG,MAAO,CACnCE,WAAY,QACZG,UAAW,iCACXC,aAAc,oBACdH,MAAO,OACPI,SAAU,SACVC,IAAK,EACLC,OAAQ,KACR9G,UACAC,EAAAA,EAAAA,MAAA,OAAKL,UAAU,kBAAiBI,SAAA,EAC9BC,EAAAA,EAAAA,MAAA,OAAKoG,MAAO,CACVU,QAAS,OACTC,WAAY,SACZC,eAAgB,gBAChBC,aAAc,yBACdC,SAAU,OACVC,IAAK,wBACLZ,MAAO,QACPxG,SAAA,EACAC,EAAAA,EAAAA,MAAA,OAAKoG,MAAO,CACVU,QAAS,OACTC,WAAY,SACZI,IAAK,wBACLC,SAAU,EACVC,KAAM,EACNC,SAAU,QACVvH,SAAA,EACAP,EAAAA,EAAAA,KAAA,UACEW,QAAS3C,EACT4I,MAAO,CACLmB,QAAS,wBACTjB,WAAY,0BACZkB,OAAQ,OACRC,aAAc,wBACdC,OAAQ,UACRrI,WAAY,gBACZyH,QAAS,OACTC,WAAY,SACZC,eAAgB,SAChBI,SAAU,OACVf,UAAW,QAEbsB,aAAeC,GAAMA,EAAEC,OAAOzB,MAAME,WAAa,0BACjDwB,aAAeF,GAAMA,EAAEC,OAAOzB,MAAME,WAAa,0BAA0BvG,UAE3EP,EAAAA,EAAAA,KAACuI,EAAAA,IAAW,CAAC3B,MAAO,CAClBG,MAAO,yBACPyB,OAAQ,yBACRC,MAAO,gBAGXjI,EAAAA,EAAAA,MAAA,OAAKoG,MAAO,CAAEgB,SAAU,EAAGC,KAAM,EAAGb,SAAU,UAAWzG,SAAA,EACvDP,EAAAA,EAAAA,KAAA,MAAI4G,MAAO,CACT8B,SAAU,yBACVC,WAAY,OACZ7B,WAAY,oDACZ8B,qBAAsB,OACtBC,oBAAqB,cACrBC,OAAQ,EACR9B,SAAU,SACV+B,aAAc,WACdC,WAAY,SACZC,WAAY,KACZ1I,SAAES,EAAKJ,QACTJ,EAAAA,EAAAA,MAAA,OAAKL,UAAU,mCAAkCI,SAAA,EAC/CP,EAAAA,EAAAA,KAAA,QAAMG,UAAU,qCAAoCI,SAAES,EAAKC,WAC3DjB,EAAAA,EAAAA,KAAA,QAAMG,UAAU,gBAAeI,SAAC,YAChCC,EAAAA,EAAAA,MAAA,QAAML,UAAU,iDAAgDI,SAAA,CAAC,YACrD+B,EAAuB,EAAE,OAAKf,EAAUC,oBAM1DhB,EAAAA,EAAAA,MAAA,OAAKoG,MAAO,CACVU,QAAS,OACTC,WAAY,SACZI,IAAK,wBACLuB,WAAY,EACZxB,SAAU,OACVF,eAAgB,YAChBjH,SAAA,EAEAC,EAAAA,EAAAA,MAAA,OAAKoG,MAAO,CAAEO,SAAU,YAAa5G,SAAA,EACnCC,EAAAA,EAAAA,MAAA,OAAKoG,MAAO,CACVU,QAAS,OACTC,WAAY,SACZI,IAAK,MACLwB,WAAY,8BACZlC,UAAWvE,GAAY,GACnB,8DACA,8DACJoE,WAAYpE,GAAY,GACpB,8CACA,8CACJ+F,MAAO,QACPV,QAAS,YACTE,aAAc,OACdD,OAAQtF,GAAY,GAChB,oBACA,oBACJ0G,UAAW1G,GAAY,GAAK,oBAAsB,QAClDnC,SAAA,EACAP,EAAAA,EAAAA,KAACyB,EAAAA,IAAO,CAACmF,MAAO,CACdG,MAAO,OACPyB,OAAQ,OACR9B,OAAQ,yCACR0C,UAAW1G,GAAY,GAAK,qBAAuB,WAErD1C,EAAAA,EAAAA,KAAA,QAAM4G,MAAO,CACXyC,WAAY,2BACZV,WAAY,MACZD,SAAU,OACVU,UAAW1G,GAAY,GAAK,oBAAsB,QAClDnC,SA3SE+I,KAClB,MAAMC,EAAU3H,KAAK4H,MAAMF,EAAU,IAC/BG,EAAOH,EAAU,GAEvB,MAAM,GAANI,OAAUH,EAAO,KAAAG,OAAID,EAAKE,WAAWC,SAAS,EAAG,KAAI,EAwSpCC,CAAWnH,QAIfA,GAAY,KACX1C,EAAAA,EAAAA,KAAA,OAAKG,UAAU,2BAInBH,EAAAA,EAAAA,KAAA,OAAKG,UAAU,yEAAwEI,UACrFC,EAAAA,EAAAA,MAAA,QAAML,UAAU,oCAAmCI,SAAA,CAChDgF,EAAkB,IAAEhE,EAAUC,OAAO,wBAO9ChB,EAAAA,EAAAA,MAAA,OAAKL,UAAU,2BAA0BI,SAAA,EACvCC,EAAAA,EAAAA,MAAA,OAAKL,UAAU,wBAAuBI,SAAA,EACpCC,EAAAA,EAAAA,MAAA,QAAML,UAAU,uBAAsBI,SAAA,CAAC,aAC1B+B,EAAuB,EAAE,OAAKf,EAAUC,WAErDhB,EAAAA,EAAAA,MAAA,QAAML,UAAU,4BAA2BI,SAAA,CACxCqB,KAAKiC,OAAQvB,EAAuB,GAAKf,EAAUC,OAAU,KAAK,WAGvExB,EAAAA,EAAAA,KAAA,OAAKG,UAAU,qBAAoBI,UACjCP,EAAAA,EAAAA,KAAA,OACEG,UAAU,sBACVyG,MAAO,CAAEG,MAAM,GAAD2C,QAAOpH,EAAuB,GAAKf,EAAUC,OAAU,IAAG,cAM9ExB,EAAAA,EAAAA,KAAA,OAAKG,UAAU,kDAAiDI,UAC9DP,EAAAA,EAAAA,KAAA,OAAKG,UAAU,+DAA8DI,UAC3EC,EAAAA,EAAAA,MAAA,QAAML,UAAU,oCAAmCI,SAAA,CAChDgF,EAAkB,IAAEhE,EAAUC,OAAO,yBAUhDxB,EAAAA,EAAAA,KAAA,OAAKG,UAAU,kBAAkByG,MAAO,CACtCkD,WAAY,yBACZC,cAAe,0BACfxJ,UACAC,EAAAA,EAAAA,MAACP,EAAAA,EAAOC,IAAG,CAETG,QAAS,CAAEX,QAAS,EAAGC,EAAG,IAC1BW,QAAS,CAAEZ,QAAS,EAAGC,EAAG,GAC1BqK,KAAM,CAAEtK,QAAS,EAAGC,GAAI,IACxBE,WAAY,CAAEC,SAAU,GAAKC,KAAM,WACnCI,UAAU,sBAAqBI,SAAA,EAG/BC,EAAAA,EAAAA,MAAA,OAAKL,UAAU,wBAAuBI,SAAA,EACpCC,EAAAA,EAAAA,MAAA,OAAKL,UAAU,wBAAuBI,SAAA,EACpCP,EAAAA,EAAAA,KAAA,OAAKG,UAAU,8BAA6BI,UAC1CP,EAAAA,EAAAA,KAAA,QAAAO,SAAO+B,EAAuB,OAEhC9B,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEP,EAAAA,EAAAA,KAAA,OAAK4G,MAAO,CAAE6B,MAAO,2BAA4BC,SAAU,QAASnI,SAAC,cACrEC,EAAAA,EAAAA,MAAA,OAAKoG,MAAO,CAAE6B,MAAO,QAASE,WAAY,MAAOD,SAAU,QAASnI,SAAA,CACjE+B,EAAuB,EAAE,OAAKf,EAAUC,iBAK/CxB,EAAAA,EAAAA,KAAA,MAAIG,UAAU,uBAAsBI,SACjC8E,EAAgBzE,WAKrBJ,EAAAA,EAAAA,MAAA,OAAKL,UAAU,gBAAgByG,MAAO,CACpCmB,QAAS,yBACThB,MAAO,OACPkD,UAAW,cACX1J,SAAA,CAEC8E,EAAgBW,QACfhG,EAAAA,EAAAA,KAAA,OAAK4G,MAAO,CAAEa,aAAc,OAAQyC,UAAW,UAAW3J,UACxDP,EAAAA,EAAAA,KAAA,OACEmK,IAAK9E,EAAgBW,MACrBoE,IAAI,WACJxD,MAAO,CACLkB,SAAU,OACVuC,UAAW,QACXpC,aAAc,OACdhB,UAAW,sCAOnBjH,EAAAA,EAAAA,KAAA,OAAKG,UAAU,iBAAgBI,SAC5BsF,GAASI,EAAgBzE,OAAS,GAEjChB,EAAAA,EAAAA,MAAA,OAAKL,UAAU,yBAAwBI,SAAA,EACrCP,EAAAA,EAAAA,KAAA,MAAIG,UAAU,sDAAqDI,SAAC,wBAGnE0F,EAAgBqE,KAAI,CAACC,EAAQC,KAC5B,MAAMC,EAAeC,OAAOC,aAAa,GAAKH,GACxCI,EAAapI,EAAgB6C,EAAgBpB,OAASwG,EAE5D,OACEzK,EAAAA,EAAAA,KAACC,EAAAA,EAAO8B,OAAM,CAEZpB,QAASA,IAAM2C,EAAmB+B,EAAgBpB,IAAKwG,GACvDtK,UAAS,gBAAAuJ,OAAkBkB,EAAa,WAAa,IACrD1I,WAAY,CAAErB,MAAO+J,EAAa,KAAO,OACzCzI,SAAU,CAAEtB,MAAO,MAAQN,UAE3BC,EAAAA,EAAAA,MAAA,OAAKL,UAAU,uBAAsBI,SAAA,EACnCP,EAAAA,EAAAA,KAAA,OAAKG,UAAU,sBAAqBI,SACjCqK,GAAa5K,EAAAA,EAAAA,KAAC6K,EAAAA,IAAO,CAACjE,MAAO,CAAEG,MAAO,OAAQyB,OAAQ,UAAeiC,KAExEzK,EAAAA,EAAAA,KAAA,QAAMG,UAAU,oBAAmBI,SAChCgK,QAXAC,EAcS,OAIpB1E,GAEFtF,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEP,EAAAA,EAAAA,KAAA,MAAI4G,MAAO,CAAE8B,SAAU,OAAQC,WAAY,MAAOF,MAAO,UAAWhB,aAAc,QAASlH,SAAC,oCAG5FC,EAAAA,EAAAA,MAAA,OAAKoG,MAAO,CAAEO,SAAU,YAAa5G,SAAA,EACnCP,EAAAA,EAAAA,KAAA,SACE2F,KAAK,OACLmF,MAAOtI,EAAgB6C,EAAgBpB,MAAQ,GAC/C8G,SAAW3C,GAAM9E,EAAmB+B,EAAgBpB,IAAKmE,EAAEC,OAAOyC,OAClEE,YAAY,2BACZ7K,UAAU,cACVyG,MAAO,CAAEqE,aAAc,WAEzBjL,EAAAA,EAAAA,KAAA,OAAK4G,MAAO,CACVO,SAAU,WACV+D,MAAO,OACP9D,IAAK,MACL+D,UAAW,mBACXzC,SAAU,OACVD,MAAO,WACPlI,SAAC,qBAILP,EAAAA,EAAAA,KAAA,OAAK4G,MAAO,CACVwE,UAAW,OACX1C,SAAU,OACVD,MAAO,UACP4C,UAAW,UACX9K,SAAC,8DAIHwF,GAEFvF,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEP,EAAAA,EAAAA,KAAA,MAAI4G,MAAO,CAAE8B,SAAU,OAAQC,WAAY,MAAOF,MAAO,UAAWhB,aAAc,QAASlH,SAAC,gDAG5FC,EAAAA,EAAAA,MAAA,OAAKoG,MAAO,CAAEO,SAAU,YAAa5G,SAAA,EACnCP,EAAAA,EAAAA,KAAA,SACE2F,KAAK,OACLmF,MAAOtI,EAAgB6C,EAAgBpB,MAAQ,GAC/C8G,SAAW3C,GAAM9E,EAAmB+B,EAAgBpB,IAAKmE,EAAEC,OAAOyC,OAClEE,YAAY,8CACZ7K,UAAU,cACVyG,MAAO,CAAEqE,aAAc,WAEzBjL,EAAAA,EAAAA,KAAA,OAAK4G,MAAO,CACVO,SAAU,WACV+D,MAAO,OACP9D,IAAK,MACL+D,UAAW,mBACXzC,SAAU,OACVD,MAAO,WACPlI,SAAC,qBAILP,EAAAA,EAAAA,KAAA,OAAK4G,MAAO,CACVwE,UAAW,OACX1C,SAAU,OACVD,MAAO,UACP4C,UAAW,UACX9K,SAAC,qEAMLC,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEP,EAAAA,EAAAA,KAAA,MAAI4G,MAAO,CAAE8B,SAAU,OAAQC,WAAY,MAAOF,MAAO,UAAWhB,aAAc,QAASlH,SAAC,uCAG5FP,EAAAA,EAAAA,KAAA,OAAK4G,MAAO,CAAEO,SAAU,YAAa5G,UACnCP,EAAAA,EAAAA,KAAA,SACE2F,KAAK,OACLmF,MAAOtI,EAAgB6C,EAAgBpB,MAAQ,GAC/C8G,SAAW3C,GAAM9E,EAAmB+B,EAAgBpB,IAAKmE,EAAEC,OAAOyC,OAClEE,YAAY,2BACZ7K,UAAU,wBAQpBK,EAAAA,EAAAA,MAAA,OAAKL,UAAU,oBAAmBI,SAAA,EAChCC,EAAAA,EAAAA,MAACP,EAAAA,EAAO8B,OAAM,CACZpB,QAhZO2K,KACfhJ,EAAuB,IACzB6B,IACA5B,EAAwBD,EAAuB,GACjD,EA6YYiJ,SAAmC,IAAzBjJ,EACVnC,UAAS,iCAAAuJ,OAAgE,IACzExH,WAAYI,EAAuB,EAAI,CAAEzB,MAAO,MAAS,CAAC,EAC1DsB,SAAUG,EAAuB,EAAI,CAAEzB,MAAO,KAAS,CAAC,EAAEN,SAAA,EAE1DP,EAAAA,EAAAA,KAACuI,EAAAA,IAAW,CAAC3B,MAAO,CAAEG,MAAO,OAAQyB,OAAQ,WAC7CxI,EAAAA,EAAAA,KAAA,QAAAO,SAAM,gBAGP+E,GACC9E,EAAAA,EAAAA,MAACP,EAAAA,EAAO8B,OAAM,CACZpB,QAASsC,EACTsI,SAAU3I,EACVzC,UAAS,aAAAuJ,OAAe9G,EAAe,sBAAwB,qBAC/DV,WAAaU,EAAiC,CAAC,EAAnB,CAAE/B,MAAO,MACrCsB,SAAWS,EAAiC,CAAC,EAAnB,CAAE/B,MAAO,KAAYN,SAAA,CAE9CqC,GACC5C,EAAAA,EAAAA,KAAA,OAAKG,UAAU,mBAEfH,EAAAA,EAAAA,KAAC6K,EAAAA,IAAO,CAACjE,MAAO,CAAEG,MAAO,OAAQyB,OAAQ,WAE3CxI,EAAAA,EAAAA,KAAA,QAAAO,SACGqC,EAAe,gBAAkB,oBAItCpC,EAAAA,EAAAA,MAACP,EAAAA,EAAO8B,OAAM,CACZpB,QApbC6K,KACXlJ,EAAuBf,EAAUC,OAAS,IAC5C2C,IACA5B,EAAwBD,EAAuB,GACjD,EAibcnC,UAAU,8BACV+B,WAAY,CAAErB,MAAO,MACrBsB,SAAU,CAAEtB,MAAO,KAAON,SAAA,EAE1BP,EAAAA,EAAAA,KAAA,QAAAO,SAAM,mBACNP,EAAAA,EAAAA,KAAC8B,EAAAA,IAAY,CAAC8E,MAAO,CAAEG,MAAO,OAAQyB,OAAQ,qBA/MjDlG,MAwNTtC,EAAAA,EAAAA,KAAA,OAAK4G,MAAO,CACVO,SAAU,QACVsE,OAAQ,yBACRP,MAAO,yBACPpE,WAAY,UACZ2B,MAAO,QACPV,QAAS,gDACTE,aAAc,OACdS,SAAU,2BACVC,WAAY,MACZ1B,UAAW,oCACXI,OAAQ,IACRqE,WAAY,OACZC,cAAe,QACfpL,SAAC,iBAGC,E,wBCvlBV,MA6oBA,EA7oBwB1D,IAA2C,IAAD+O,EAAA,IAAzC,OAAEC,EAAM,aAAEC,EAAY,WAAEC,GAAYlP,EAC3D,MAAOmP,EAAaC,IAAkB9N,EAAAA,EAAAA,WAAS,IACxC+N,EAAmBC,IAAwBhO,EAAAA,EAAAA,WAAS,IACpDiO,EAAWC,IAAgBlO,EAAAA,EAAAA,WAAS,IACpCmO,EAAmBC,IAAwBpO,EAAAA,EAAAA,WAAS,IAG3DK,EAAAA,EAAAA,YAAU,KACSqN,EAAOW,YAAc,GAIpCC,YAAW,MACTC,EAAAA,EAAAA,GAAS,CACPC,cAAe,IACfC,OAAQ,GACRC,OAAQ,CAAElN,EAAG,MAIf,IACE,MAAMmN,EAAQ,IAAIC,MAAM,oBACxBD,EAAME,OAAS,GACfF,EAAMG,OAAOC,OAAM,QACrB,CAAE,MAAO5O,GACPgB,QAAQC,IAAI,sBACd,CAGA8M,GAAa,GACbI,YAAW,IAAMJ,GAAa,IAAQ,IAAI,GACzC,KAGHI,YAAW,KAETJ,GAAa,GACbE,GAAqB,GAGrB,IACE,MAAMnI,EAAe,IAAKC,OAAOC,cAAgBD,OAAOE,oBAClDC,EAAaJ,EAAaK,mBAC1BC,EAAWN,EAAaO,aAE9BH,EAAWI,QAAQF,GACnBA,EAASE,QAAQR,EAAaS,aAG9BL,EAAWM,UAAUC,eAAe,IAAKX,EAAaY,aACtDR,EAAWM,UAAUG,6BAA6B,IAAKb,EAAaY,YAAc,IAClFR,EAAWM,UAAUG,6BAA6B,IAAKb,EAAaY,YAAc,GAElFN,EAASQ,KAAKH,eAAe,IAAMX,EAAaY,aAChDN,EAASQ,KAAKD,6BAA6B,IAAMb,EAAaY,YAAc,GAE5ER,EAAWW,MAAMf,EAAaY,aAC9BR,EAAWY,KAAKhB,EAAaY,YAAc,EAC7C,CAAE,MAAO1G,GACPgB,QAAQC,IAAI,8BACd,CAEAkN,YAAW,KACTJ,GAAa,GACbE,GAAqB,EAAM,GAC1B,IAAI,GACN,IACL,GACC,CAACV,EAAOW,aAEX,MA6CMW,GAvCyBX,EAuCWX,EAAOW,aAtC7B,GAAW,CAC3BtN,QAAS,wCACTuJ,MAAO,kBACP2E,GAAI,eACJC,SAAU,iCAERb,GAAc,GAAW,CAC3BtN,QAAS,+BACTuJ,MAAO,iBACP2E,GAAI,cACJC,SAAU,+BAERb,GAAc,GAAW,CAC3BtN,QAAS,0BACTuJ,MAAO,gBACP2E,GAAI,aACJC,SAAU,6BAERb,GAAc,GAAW,CAC3BtN,QAAS,oBACTuJ,MAAO,mBACP2E,GAAI,gBACJC,SAAU,mCAERb,GAAc,GAAW,CAC3BtN,QAAS,4BACTuJ,MAAO,kBACP2E,GAAI,eACJC,SAAU,iCAEL,CACLnO,QAAS,gCACTuJ,MAAO,kBACP2E,GAAI,eACJC,SAAU,iCAnCiBb,MAwC/B,MAAMc,EAAWzB,EAAOW,YAAc,GAEhCe,EAAkB,CACtB,CACEC,KAAMC,EAAAA,IACNC,MAAO,kBACPC,YAAa,8DAEf,CACEH,KAAMzM,EAAAA,IACN2M,MAAO,eACPC,YAAa,2DAEf,CACEH,KAAMI,EAAAA,IACNF,MAAO,iBACPC,YAAa,uDAEf,CACEH,KAAMK,EAAAA,IACNH,MAAO,eACPC,YAAa,0DAEf,CACEH,KAAMM,EAAAA,IACNJ,MAAO,oBACPC,YAAa,wDAEf,CACEH,KAAMO,EAAAA,IACNL,MAAO,oBACPC,YAAa,gDAIjB,OACEnN,EAAAA,EAAAA,MAAA,OAAKL,UAAU,mBAAmByG,MAAO,CACvCC,UAAW,QACXC,WAAY,iEACZK,SAAU,YACV5G,SAAA,CAEC6L,IACCpM,EAAAA,EAAAA,KAAA,OACEG,UAAS,mBAAAuJ,OACPmC,EAAOW,YAAc,GAAK,gBAAkB,oBAKlDhM,EAAAA,EAAAA,MAAA,OAAKL,UAAU,kBAAkByG,MAAO,CACtCO,SAAU,WACVE,OAAQ,GACRU,QAAS,yBACThB,MAAO,OACPe,SAAU,OACVmC,UAAW,cACX1J,SAAA,EAEAC,EAAAA,EAAAA,MAACP,EAAAA,EAAOC,IAAG,CACTG,QAAS,CAAEX,QAAS,EAAGC,GAAI,IAC3BW,QAAS,CAAEZ,QAAS,EAAGC,EAAG,GAC1BE,WAAY,CAAEC,SAAU,GAAKC,KAAM,WACnCI,UAAU,4BAA2BI,SAAA,EAErCP,EAAAA,EAAAA,KAACC,EAAAA,EAAOC,IAAG,CACTG,QAAS,CAAEQ,MAAO,GAClBP,QAAS,CAAEO,MAAO,GAClBhB,WAAY,CAAEC,SAAU,GAAKgB,MAAO,GAAK6E,KAAM,SAAUqI,UAAW,KACpE7N,UAAS,yEAAAuJ,OAA2EyD,EAAYE,SAAQ,oBAAA3D,OAAmB4C,EAAoB,mBAAqB,IACpK1F,MAAO,CACLG,MAAO,2BACPyB,OAAQ,4BAEVyF,oBAAqBA,IAAM9B,GAAqB,GAAM5L,UAEtDP,EAAAA,EAAAA,KAACkO,EAAAA,IAAQ,CACP/N,UAAU,aACVyG,MAAO,CACLG,MAAO,yBACPyB,OAAQ,+BAKdxI,EAAAA,EAAAA,KAACC,EAAAA,EAAOkO,GAAE,CACR9N,QAAS,CAAEX,QAAS,GACpBY,QAAS,CAAEZ,QAAS,GACpBG,WAAY,CAAEC,SAAU,GAAKgB,MAAO,IACpCX,UAAS,gCAAAuJ,OAAkC4C,EAAoB,kBAAoB,IACnF1F,MAAO,CAAE8B,SAAU,0BAA2BnI,SAC/C,iCAIDP,EAAAA,EAAAA,KAACC,EAAAA,EAAOC,IAAG,CACTG,QAAS,CAAEX,QAAS,EAAGC,EAAG,IAC1BW,QAAS,CAAEZ,QAAS,EAAGC,EAAG,GAC1BE,WAAY,CAAEC,SAAU,GAAKgB,MAAO,IACpCX,UAAS,uCAAAuJ,OAAyCyD,EAAYC,GAAE,qBAAA1D,OAAoByD,EAAY1E,MAAM2F,MAAM,KAAK,GAAE,QAAO7N,UAE1HP,EAAAA,EAAAA,KAAA,KAAGG,UAAS,iCAAAuJ,OAAmCyD,EAAY1E,OAAQlI,SAChE4M,EAAYjO,gBAMnBsB,EAAAA,EAAAA,MAACP,EAAAA,EAAOC,IAAG,CACTG,QAAS,CAAEX,QAAS,EAAGmB,MAAO,IAC9BP,QAAS,CAAEZ,QAAS,EAAGmB,MAAO,GAC9BhB,WAAY,CAAEC,SAAU,GAAKgB,MAAO,GACpCX,UAAU,uFAAsFI,SAAA,EAGhGP,EAAAA,EAAAA,KAAA,OAAKG,UAAS,oBAAAuJ,OAAsByD,EAAYE,SAAQ,uCAAsC9M,UAC5FC,EAAAA,EAAAA,MAAA,OAAKL,UAAU,cAAaI,SAAA,EAC1BC,EAAAA,EAAAA,MAACP,EAAAA,EAAOC,IAAG,CACTG,QAAS,CAAEQ,MAAO,GAClBP,QAAS,CAAEO,MAAO,GAClBhB,WAAY,CAAEC,SAAU,GAAKgB,MAAO,IAAK6E,KAAM,UAC/CxF,UAAS,6BAAAuJ,OAA+B4C,EAAoB,oBAAsB,IAClF1F,MAAO,CAAE8B,SAAU,2BAA4BnI,SAAA,CAE9CsL,EAAOW,WAAW,QAErBxM,EAAAA,EAAAA,KAAA,OAAKG,UAAU,mCAAkCI,SAAC,qBAOtDC,EAAAA,EAAAA,MAAA,OAAKL,UAAU,sCAAqCI,SAAA,EAElDP,EAAAA,EAAAA,KAAA,OAAKG,UAAU,sDAAqDI,SACjE,CACC,CACE8N,MAAO,kBACPvD,MAAOe,EAAOyC,eACdd,KAAMC,EAAAA,IACNhF,MAAO,OACP3H,MAAO,KAET,CACEuN,MAAO,kBACPvD,MAAOe,EAAO0C,eACdf,KAAM3C,EAAAA,IACNpC,MAAO,QACP3H,MAAO,KAET,CACEuN,MAAO,gBACPvD,MAAOe,EAAO2C,aACdhB,KAAMiB,EAAAA,IACNhG,MAAO,MACP3H,MAAO,KAET,CACEuN,MAAO,aACPvD,MA9MIxB,KAClB,MAAMC,EAAU3H,KAAK4H,MAAMF,EAAU,IAC/BoF,EAAmBpF,EAAU,GACnC,MAAM,GAANI,OAAUH,EAAO,MAAAG,OAAKgF,EAAgB,MA2MjB7E,CAAWgC,EAAOjI,WACzB4J,KAAM/L,EAAAA,IACNgH,MAAO,SACP3H,MAAO,IAETwJ,KAAI,CAACqE,EAAMnE,KACXhK,EAAAA,EAAAA,MAACP,EAAAA,EAAOC,IAAG,CAETG,QAAS,CAAEX,QAAS,EAAGC,EAAG,IAC1BW,QAAS,CAAEZ,QAAS,EAAGC,EAAG,GAC1BE,WAAY,CAAEC,SAAU,GAAKgB,MAAO6N,EAAK7N,OACzCX,UAAS,iBAAAuJ,OAAmBiF,EAAKlG,MAAK,kCAAAiB,OAAiCiF,EAAKlG,MAAK,oBAAmBlI,SAAA,EAEpGP,EAAAA,EAAAA,KAAA,OAAKG,UAAS,6BAAAuJ,OAA+BiF,EAAKlG,MAAK,oDAAmDlI,UACxGP,EAAAA,EAAAA,KAAC2O,EAAKnB,KAAI,CAACrN,UAAS,gBAAAuJ,OAAkBiF,EAAKlG,MAAK,aAElDzI,EAAAA,EAAAA,KAAA,OAAKG,UAAS,uCAAAuJ,OAAyCiF,EAAKlG,MAAK,aAAYlI,SAC1EoO,EAAK7D,SAER9K,EAAAA,EAAAA,KAAA,OAAKG,UAAU,oCAAmCI,SAC/CoO,EAAKN,UAbH7D,QAoBXxK,EAAAA,EAAAA,KAACC,EAAAA,EAAOC,IAAG,CACTG,QAAS,CAAEX,QAAS,EAAGmB,MAAO,IAC9BP,QAAS,CAAEZ,QAAS,EAAGmB,MAAO,GAC9BhB,WAAY,CAAEC,SAAU,GAAKgB,MAAO,KACpCX,UAAU,mBAAkBI,UAE5BC,EAAAA,EAAAA,MAAA,OAAKL,UAAS,kFAAAuJ,OACZ4D,EACI,wDACA,mDACH/M,SAAA,CACA+M,GACCtN,EAAAA,EAAAA,KAAC6K,EAAAA,IAAO,CAAC1K,UAAU,aAEnBH,EAAAA,EAAAA,KAACyO,EAAAA,IAAG,CAACtO,UAAU,aAEjBH,EAAAA,EAAAA,KAAA,QAAAO,SACG+M,EAAW,4CAAoC,yDAMtDtN,EAAAA,EAAAA,KAACC,EAAAA,EAAOC,IAAG,CACTG,QAAS,CAAEX,QAAS,GACpBY,QAAS,CAAEZ,QAAS,GACpBG,WAAY,CAAEC,SAAU,GAAKgB,MAAO,KACpCX,UAAU,cAAaI,UAEvBC,EAAAA,EAAAA,MAAA,UACEG,QAASA,IAAMsL,GAAgBD,GAC/B7L,UAAU,8IAA6II,SAAA,EAEvJP,EAAAA,EAAAA,KAAC4N,EAAAA,IAAU,CAACzN,UAAU,aACtBH,EAAAA,EAAAA,KAAA,QAAAO,SAAOyL,EAAc,wBAA0B,sCAOvDhM,EAAAA,EAAAA,KAACC,EAAAA,EAAOC,IAAG,CACTG,QAAS,CAAEX,QAAS,EAAGC,EAAG,IAC1BW,QAAS,CAAEZ,QAAS,EAAGC,EAAG,GAC1BE,WAAY,CAAEC,SAAU,GAAKgB,MAAO,KACpCX,UAAU,mBAAkBI,UAE5BP,EAAAA,EAAAA,KAAC4O,EAAAA,GAAI,CAACC,GAAG,YAAWtO,UAClBC,EAAAA,EAAAA,MAACP,EAAAA,EAAO8B,OAAM,CACZ5B,UAAU,oOACV+B,WAAY,CAAErB,MAAO,KAAMlB,GAAI,GAC/BwC,SAAU,CAAEtB,MAAO,KAAON,SAAA,EAE1BP,EAAAA,EAAAA,KAAA,QAAAO,SAAM,kBACNP,EAAAA,EAAAA,KAAC8B,EAAAA,IAAY,CAAC3B,UAAU,oBAM9BK,EAAAA,EAAAA,MAACP,EAAAA,EAAOC,IAAG,CACTG,QAAS,CAAEX,QAAS,EAAGC,EAAG,IAC1BW,QAAS,CAAEZ,QAAS,EAAGC,EAAG,GAC1BE,WAAY,CAAEC,SAAU,GAAKgB,MAAO,KACpCX,UAAU,6EAA4EI,SAAA,EAGtFC,EAAAA,EAAAA,MAAA,OAAKL,UAAU,wFAAuFI,SAAA,EACpGP,EAAAA,EAAAA,KAAA,MAAIG,UAAU,sCAAqCI,SAAC,gDAGpDP,EAAAA,EAAAA,KAAA,KAAGG,UAAU,wBAAuBI,SAAC,oEAMvCC,EAAAA,EAAAA,MAAA,OAAKL,UAAU,aAAYI,SAAA,EACzBP,EAAAA,EAAAA,KAAA,OAAKG,UAAU,uDAAsDI,SAClEgN,EAAgBjD,KAAI,CAACwE,EAAStE,KAC7BhK,EAAAA,EAAAA,MAACP,EAAAA,EAAOC,IAAG,CAETG,QAAS,CAAEX,QAAS,EAAGC,EAAG,IAC1BW,QAAS,CAAEZ,QAAS,EAAGC,EAAG,GAC1BE,WAAY,CAAEC,SAAU,GAAKgB,MAAO,EAAO,GAAM0J,GACjDrK,UAAU,uIAAsII,SAAA,EAEhJC,EAAAA,EAAAA,MAAA,OAAKL,UAAU,mCAAkCI,SAAA,EAC/CP,EAAAA,EAAAA,KAAA,OAAKG,UAAU,6JAA4JI,UACzKP,EAAAA,EAAAA,KAAC8O,EAAQtB,KAAI,CAACrN,UAAU,0BAE1BH,EAAAA,EAAAA,KAAA,MAAIG,UAAU,kCAAiCI,SAAEuO,EAAQpB,YAE3D1N,EAAAA,EAAAA,KAAA,KAAGG,UAAU,gCAA+BI,SAAEuO,EAAQnB,eACtDnN,EAAAA,EAAAA,MAAA,OAAKL,UAAU,mDAAkDI,SAAA,EAC/DP,EAAAA,EAAAA,KAAC+N,EAAAA,IAAM,CAAC5N,UAAU,kBAClBH,EAAAA,EAAAA,KAAA,QAAMG,UAAU,UAASI,SAAC,yBAfvBiK,QAsBXhK,EAAAA,EAAAA,MAACP,EAAAA,EAAOC,IAAG,CACTG,QAAS,CAAEX,QAAS,EAAGC,EAAG,IAC1BW,QAAS,CAAEZ,QAAS,EAAGC,EAAG,GAC1BE,WAAY,CAAEC,SAAU,GAAKgB,MAAO,KACpCX,UAAU,2FAA0FI,SAAA,EAEpGC,EAAAA,EAAAA,MAAA,MAAIL,UAAU,oFAAmFI,SAAA,EAC/FP,EAAAA,EAAAA,KAACe,EAAAA,IAAO,CAACZ,UAAU,iCAAiC,+CAGtDK,EAAAA,EAAAA,MAAA,OAAKL,UAAU,wCAAuCI,SAAA,EAEpDC,EAAAA,EAAAA,MAACP,EAAAA,EAAOC,IAAG,CACTG,QAAS,CAAEX,QAAS,EAAGqP,GAAI,IAC3BzO,QAAS,CAAEZ,QAAS,EAAGqP,EAAG,GAC1BlP,WAAY,CAAEC,SAAU,GAAKgB,MAAO,KACpCX,UAAU,6DAA4DI,SAAA,EAEtEC,EAAAA,EAAAA,MAAA,OAAKL,UAAU,yBAAwBI,SAAA,EACrCP,EAAAA,EAAAA,KAAA,OAAKG,UAAU,0GAAyGI,UACtHP,EAAAA,EAAAA,KAACyN,EAAAA,IAAM,CAACtN,UAAU,0BAEpBH,EAAAA,EAAAA,KAAA,MAAIG,UAAU,0BAAyBI,SAAC,mCAE1CC,EAAAA,EAAAA,MAAA,MAAIL,UAAU,kCAAiCI,SAAA,EAC7CC,EAAAA,EAAAA,MAAA,MAAIL,UAAU,mBAAkBI,SAAA,EAC9BP,EAAAA,EAAAA,KAAA,QAAMG,UAAU,uBAAsBI,SAAC,YACvCC,EAAAA,EAAAA,MAAA,QAAAD,SAAA,CAAM,gBAAYP,EAAAA,EAAAA,KAAA,UAAAO,SAAQ,iBAAqB,4BAEjDC,EAAAA,EAAAA,MAAA,MAAIL,UAAU,mBAAkBI,SAAA,EAC9BP,EAAAA,EAAAA,KAAA,QAAMG,UAAU,uBAAsBI,SAAC,YACvCP,EAAAA,EAAAA,KAAA,QAAAO,SAAM,iDAERC,EAAAA,EAAAA,MAAA,MAAIL,UAAU,mBAAkBI,SAAA,EAC9BP,EAAAA,EAAAA,KAAA,QAAMG,UAAU,uBAAsBI,SAAC,YACvCP,EAAAA,EAAAA,KAAA,QAAAO,SAAM,8CAERC,EAAAA,EAAAA,MAAA,MAAIL,UAAU,mBAAkBI,SAAA,EAC9BP,EAAAA,EAAAA,KAAA,QAAMG,UAAU,uBAAsBI,SAAC,YACvCP,EAAAA,EAAAA,KAAA,QAAAO,SAAM,iDAMZC,EAAAA,EAAAA,MAACP,EAAAA,EAAOC,IAAG,CACTG,QAAS,CAAEX,QAAS,EAAGqP,EAAG,IAC1BzO,QAAS,CAAEZ,QAAS,EAAGqP,EAAG,GAC1BlP,WAAY,CAAEC,SAAU,GAAKgB,MAAO,KACpCX,UAAU,2DAA0DI,SAAA,EAEpEC,EAAAA,EAAAA,MAAA,OAAKL,UAAU,yBAAwBI,SAAA,EACrCP,EAAAA,EAAAA,KAAA,OAAKG,UAAU,yGAAwGI,UACrHP,EAAAA,EAAAA,KAACgP,EAAAA,IAAU,CAAC7O,UAAU,0BAExBH,EAAAA,EAAAA,KAAA,MAAIG,UAAU,0BAAyBI,SAAC,6BAE1CC,EAAAA,EAAAA,MAAA,MAAIL,UAAU,kCAAiCI,SAAA,EAC7CC,EAAAA,EAAAA,MAAA,MAAIL,UAAU,mBAAkBI,SAAA,EAC9BP,EAAAA,EAAAA,KAAA,QAAMG,UAAU,qBAAoBI,SAAC,YACrCC,EAAAA,EAAAA,MAAA,QAAAD,SAAA,CAAM,eAAWP,EAAAA,EAAAA,KAAA,UAAAO,SAAQ,gBAAoB,0BAE/CC,EAAAA,EAAAA,MAAA,MAAIL,UAAU,mBAAkBI,SAAA,EAC9BP,EAAAA,EAAAA,KAAA,QAAMG,UAAU,qBAAoBI,SAAC,YACrCP,EAAAA,EAAAA,KAAA,QAAAO,SAAM,gDAERC,EAAAA,EAAAA,MAAA,MAAIL,UAAU,mBAAkBI,SAAA,EAC9BP,EAAAA,EAAAA,KAAA,QAAMG,UAAU,qBAAoBI,SAAC,YACrCP,EAAAA,EAAAA,KAAA,QAAAO,SAAM,sDAERC,EAAAA,EAAAA,MAAA,MAAIL,UAAU,mBAAkBI,SAAA,EAC9BP,EAAAA,EAAAA,KAAA,QAAMG,UAAU,qBAAoBI,SAAC,YACrCP,EAAAA,EAAAA,KAAA,QAAAO,SAAM,gDAOdC,EAAAA,EAAAA,MAACP,EAAAA,EAAOC,IAAG,CACTG,QAAS,CAAEX,QAAS,EAAGC,EAAG,IAC1BW,QAAS,CAAEZ,QAAS,EAAGC,EAAG,GAC1BE,WAAY,CAAEC,SAAU,GAAKgB,MAAO,GACpCX,UAAU,kEAAiEI,SAAA,EAE3EP,EAAAA,EAAAA,KAAA,MAAIG,UAAU,2CAA0CI,SAAC,yCACzDP,EAAAA,EAAAA,KAAA,OAAKG,UAAU,wCAAuCI,SACnD,CACC,CAAEiN,KAAM,eAAME,MAAO,cAAeuB,KAAM,mBAC1C,CAAEzB,KAAM,eAAME,MAAO,oBAAqBuB,KAAM,mBAChD,CAAEzB,KAAM,eAAME,MAAO,iBAAkBuB,KAAM,sBAC7C,CAAEzB,KAAM,eAAME,MAAO,qBAAsBuB,KAAM,mBACjD3E,KAAI,CAACwE,EAAStE,KACdhK,EAAAA,EAAAA,MAACP,EAAAA,EAAOC,IAAG,CAETG,QAAS,CAAEX,QAAS,EAAGmB,MAAO,IAC9BP,QAAS,CAAEZ,QAAS,EAAGmB,MAAO,GAC9BhB,WAAY,CAAEC,SAAU,GAAKgB,MAAO,IAAO,GAAM0J,GACjDrK,UAAU,gDAA+CI,SAAA,EAEzDP,EAAAA,EAAAA,KAAA,OAAKG,UAAU,eAAcI,SAAEuO,EAAQtB,QACvCxN,EAAAA,EAAAA,KAAA,OAAKG,UAAU,sCAAqCI,SAAEuO,EAAQpB,SAC9D1N,EAAAA,EAAAA,KAAA,OAAKG,UAAU,wBAAuBI,SAAEuO,EAAQG,SAR3CzE,cAgBfhK,EAAAA,EAAAA,MAACP,EAAAA,EAAOC,IAAG,CACTG,QAAS,CAAEX,QAAS,EAAGC,EAAG,IAC1BW,QAAS,CAAEZ,QAAS,EAAGC,EAAG,GAC1BE,WAAY,CAAEC,SAAU,GAAKgB,MAAO,KACpCX,UAAU,yFAAwFI,SAAA,EAElGP,EAAAA,EAAAA,KAAA,MAAIG,UAAU,mDAAkDI,SAAC,gDAGjEP,EAAAA,EAAAA,KAAA,OAAKG,UAAU,uDAAsDI,SAClE,CACC,CAAEiN,KAAM,eAAME,MAAO,iBAAkBuB,KAAM,8BAC7C,CAAEzB,KAAM,eAAME,MAAO,kBAAmBuB,KAAM,2BAC9C,CAAEzB,KAAM,eAAME,MAAO,iBAAkBuB,KAAM,4BAC7C,CAAEzB,KAAM,eAAME,MAAO,iBAAkBuB,KAAM,qBAC7C3E,KAAI,CAAC4E,EAAS1E,KACdhK,EAAAA,EAAAA,MAACP,EAAAA,EAAOC,IAAG,CAETG,QAAS,CAAEX,QAAS,EAAGmB,MAAO,IAC9BP,QAAS,CAAEZ,QAAS,EAAGmB,MAAO,GAC9BhB,WAAY,CAAEC,SAAU,GAAKgB,MAAO,IAAO,GAAM0J,GACjDrK,UAAU,gDAA+CI,SAAA,EAEzDP,EAAAA,EAAAA,KAAA,OAAKG,UAAU,gBAAeI,SAAE2O,EAAQ1B,QACxCxN,EAAAA,EAAAA,KAAA,OAAKG,UAAU,mCAAkCI,SAAE2O,EAAQxB,SAC3D1N,EAAAA,EAAAA,KAAA,OAAKG,UAAU,wBAAuBI,SAAE2O,EAAQD,SAR3CzE,WAebhK,EAAAA,EAAAA,MAACP,EAAAA,EAAOC,IAAG,CACTG,QAAS,CAAEX,QAAS,EAAGC,EAAG,IAC1BW,QAAS,CAAEZ,QAAS,EAAGC,EAAG,GAC1BE,WAAY,CAAEC,SAAU,GAAKgB,MAAO,KACpCX,UAAU,2FAA0FI,SAAA,EAEpGP,EAAAA,EAAAA,KAAA,MAAIG,UAAU,yBAAwBI,SAAC,kCACvCP,EAAAA,EAAAA,KAAA,KAAGG,UAAU,qBAAoBI,SAAC,0DAClCC,EAAAA,EAAAA,MAAA,OAAKL,UAAU,8DAA6DI,SAAA,EAC1EP,EAAAA,EAAAA,KAAC4O,EAAAA,GAAI,CAACC,GAAG,YAAWtO,UAClBC,EAAAA,EAAAA,MAACP,EAAAA,EAAO8B,OAAM,CACZ5B,UAAU,8HACV+B,WAAY,CAAErB,MAAO,MACrBsB,SAAU,CAAEtB,MAAO,KAAON,SAAA,EAE1BP,EAAAA,EAAAA,KAAA,QAAAO,SAAM,yBACNP,EAAAA,EAAAA,KAAC8B,EAAAA,IAAY,CAAC3B,UAAU,kBAG5BH,EAAAA,EAAAA,KAAA,OAAKG,UAAU,wBAAuBI,SAAC,wEAS9CyL,IACCxL,EAAAA,EAAAA,MAACP,EAAAA,EAAOC,IAAG,CACTG,QAAS,CAAEX,QAAS,EAAG8I,OAAQ,GAC/BlI,QAAS,CAAEZ,QAAS,EAAG8I,OAAQ,QAC/B3I,WAAY,CAAEC,SAAU,IACxBK,UAAU,0CAAyCI,SAAA,EAEnDP,EAAAA,EAAAA,KAAA,MAAIG,UAAU,uCAAsCI,SAAC,qBACrDP,EAAAA,EAAAA,KAAA,OAAKG,UAAU,YAAWI,SACD,QADCqL,EACvBC,EAAOsD,uBAAe,IAAAvD,OAAA,EAAtBA,EAAwBtB,KAAI,CAAC8E,EAAG5E,KAC/BxK,EAAAA,EAAAA,KAAA,OAAiBG,UAAS,2BAAAuJ,OACxB0F,EAAEC,UAAY,+BAAiC,4BAC9C9O,UACDC,EAAAA,EAAAA,MAAA,OAAKL,UAAU,6BAA4BI,SAAA,EACzCP,EAAAA,EAAAA,KAAA,OAAKG,UAAS,yDAAAuJ,OACZ0F,EAAEC,UAAY,0BAA4B,yBACzC9O,SACA6O,EAAEC,WAAYrP,EAAAA,EAAAA,KAAC6K,EAAAA,IAAO,CAAC1K,UAAU,aAAeH,EAAAA,EAAAA,KAACyO,EAAAA,IAAG,CAACtO,UAAU,eAElEK,EAAAA,EAAAA,MAAA,OAAKL,UAAU,SAAQI,SAAA,EACrBP,EAAAA,EAAAA,KAAA,KAAGG,UAAU,iCAAgCI,SAAE6O,EAAEE,YACjD9O,EAAAA,EAAAA,MAAA,OAAKL,UAAU,oBAAmBI,SAAA,EAChCC,EAAAA,EAAAA,MAAA,KAAAD,SAAA,EACEP,EAAAA,EAAAA,KAAA,QAAMG,UAAU,gBAAeI,SAAC,kBAChCP,EAAAA,EAAAA,KAAA,QAAMG,UAAS,oBAAAuJ,OAAsB0F,EAAEC,UAAY,iBAAmB,gBAAiB9O,SACpF6O,EAAEG,YAAc,qBAGnBH,EAAEC,YACF7O,EAAAA,EAAAA,MAAA,KAAAD,SAAA,EACEP,EAAAA,EAAAA,KAAA,QAAMG,UAAU,gBAAeI,SAAC,qBAChCP,EAAAA,EAAAA,KAAA,QAAMG,UAAU,kCAAiCI,SAAE6O,EAAEI,6BArBvDhF,WAoClBhK,EAAAA,EAAAA,MAACP,EAAAA,EAAOC,IAAG,CACTG,QAAS,CAAEX,QAAS,EAAGC,EAAG,IAC1BW,QAAS,CAAEZ,QAAS,EAAGC,EAAG,GAC1BE,WAAY,CAAEC,SAAU,GAAKgB,MAAO,IACpCX,UAAU,iDAAgDI,SAAA,EAE1DP,EAAAA,EAAAA,KAAA,UACEW,QAASmL,EACT3L,UAAU,2GAA0GI,SACrH,sBAIDP,EAAAA,EAAAA,KAAC4O,EAAAA,GAAI,CAACC,GAAG,IAAGtO,UACVP,EAAAA,EAAAA,KAAA,UAAQG,UAAU,8FAA6FI,SAAC,uBAOpHP,EAAAA,EAAAA,KAAA,OAAKG,UAAU,mBAAkBI,UAC/BP,EAAAA,EAAAA,KAAA,QAAMG,UAAU,wFAAuFI,SAAC,mEAKxG,E,cC3oBV,MAgQA,EAhQgC1D,IAAuC,IAAtC,OAAE4S,EAAM,QAAEC,EAAO,YAAEC,GAAa9S,EAC/D,MAAM0Q,EAAkB,CACtB,CACEC,KAAMoC,EAAAA,IACNlC,MAAO,oBACPC,YAAa,6DACblF,MAAO,gBACP2E,GAAI,cAEN,CACEI,KAAMC,EAAAA,IACNC,MAAO,kBACPC,YAAa,8DACblF,MAAO,iBACP2E,GAAI,eAEN,CACEI,KAAMzM,EAAAA,IACN2M,MAAO,eACPC,YAAa,0DACblF,MAAO,kBACP2E,GAAI,gBAEN,CACEI,KAAMI,EAAAA,IACNF,MAAO,iBACPC,YAAa,2DACblF,MAAO,kBACP2E,GAAI,gBAEN,CACEI,KAAMK,EAAAA,IACNH,MAAO,eACPC,YAAa,yDACblF,MAAO,kBACP2E,GAAI,gBAEN,CACEI,KAAMU,EAAAA,IACNR,MAAO,eACPC,YAAa,uDACblF,MAAO,kBACP2E,GAAI,iBAuCR,OAAKqC,GAGHzP,EAAAA,EAAAA,KAAC6P,EAAAA,EAAe,CAAAtP,UACdC,EAAAA,EAAAA,MAACP,EAAAA,EAAOC,IAAG,CACTC,UAAU,0DACVC,SAZkB,CACtBX,OAAQ,CAAEC,QAAS,GACnBE,QAAS,CAAEF,QAAS,GACpBsK,KAAM,CAAEtK,QAAS,IAUbW,QAAQ,SACRC,QAAQ,UACR0J,KAAK,OAAMzJ,SAAA,EAGXP,EAAAA,EAAAA,KAACC,EAAAA,EAAOC,IAAG,CACTC,UAAU,gDACVQ,QAAS+O,KAIXlP,EAAAA,EAAAA,MAACP,EAAAA,EAAOC,IAAG,CACTC,UAAU,8FACVC,SA9Cc,CACpBX,OAAQ,CAAEC,QAAS,EAAGmB,MAAO,GAAKlB,EAAG,IACrCC,QAAS,CACPF,QAAS,EACTmB,MAAO,EACPlB,EAAG,EACHE,WAAY,CACV8F,KAAM,SACNmK,QAAS,GACT9B,UAAW,MAGfhE,KAAM,CACJtK,QAAS,EACTmB,MAAO,GACPlB,EAAG,GACHE,WAAY,CAAEC,SAAU,MA+BpBO,QAAQ,SACRC,QAAQ,UACR0J,KAAK,OAAMzJ,SAAA,EAGXC,EAAAA,EAAAA,MAAA,OAAKL,UAAU,6GAA4GI,SAAA,EACzHP,EAAAA,EAAAA,KAAA,OAAKG,UAAU,yEACfK,EAAAA,EAAAA,MAAA,OAAKL,UAAU,gBAAeI,SAAA,EAC5BC,EAAAA,EAAAA,MAAA,OAAKL,UAAU,yCAAwCI,SAAA,EACrDC,EAAAA,EAAAA,MAAA,OAAKL,UAAU,8BAA6BI,SAAA,EAC1CP,EAAAA,EAAAA,KAAA,OAAKG,UAAU,6BAA4BI,UACzCP,EAAAA,EAAAA,KAAC+N,EAAAA,IAAM,CAAC5N,UAAU,eAEpBK,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEP,EAAAA,EAAAA,KAAA,MAAIG,UAAU,gCAA+BI,SAAC,mCAC9CP,EAAAA,EAAAA,KAAA,KAAGG,UAAU,qCAAoCI,SAAC,4CAGtDP,EAAAA,EAAAA,KAAA,UACEW,QAAS+O,EACTvP,UAAU,qDAAoDI,UAE9DP,EAAAA,EAAAA,KAACyO,EAAAA,IAAG,CAACtO,UAAU,iBAKlBwP,IACC3P,EAAAA,EAAAA,KAAA,OAAKG,UAAU,8CAA6CI,UAC1DC,EAAAA,EAAAA,MAAA,OAAKL,UAAU,qCAAoCI,SAAA,EACjDC,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEC,EAAAA,EAAAA,MAAA,OAAKL,UAAU,qBAAoBI,SAAA,CAAEoP,EAAYnD,WAAW,QAC5DxM,EAAAA,EAAAA,KAAA,OAAKG,UAAU,wBAAuBI,SAAC,cAEzCC,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEC,EAAAA,EAAAA,MAAA,OAAKL,UAAU,qBAAoBI,SAAA,CAAEoP,EAAYpB,eAAe,IAAEoB,EAAYrB,mBAC9EtO,EAAAA,EAAAA,KAAA,OAAKG,UAAU,wBAAuBI,SAAC,gBAEzCC,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEP,EAAAA,EAAAA,KAAA,OAAKG,UAAU,qBAAoBI,SAAEoP,EAAYI,OAAS,SAAW,YACrE/P,EAAAA,EAAAA,KAAA,OAAKG,UAAU,wBAAuBI,SAAC,0BASnDC,EAAAA,EAAAA,MAAA,OAAKL,UAAU,0CAAyCI,SAAA,EAEtDC,EAAAA,EAAAA,MAAA,OAAKL,UAAU,mBAAkBI,SAAA,EAC/BP,EAAAA,EAAAA,KAAA,OAAKG,UAAU,oHAAmHI,UAChIP,EAAAA,EAAAA,KAACgQ,EAAAA,IAAM,CAAC7P,UAAU,0BAEpBH,EAAAA,EAAAA,KAAA,MAAIG,UAAU,wCAAuCI,SAAC,0CAGtDP,EAAAA,EAAAA,KAAA,KAAGG,UAAU,wBAAuBI,SAAC,4EAMvCP,EAAAA,EAAAA,KAAA,OAAKG,UAAU,4DAA2DI,SACvEgN,EAAgBjD,KAAI,CAACwE,EAAStE,KAC7BhK,EAAAA,EAAAA,MAACP,EAAAA,EAAOC,IAAG,CAETG,QAAS,CAAEX,QAAS,EAAGC,EAAG,IAC1BW,QAAS,CAAEZ,QAAS,EAAGC,EAAG,GAC1BE,WAAY,CAAEC,SAAU,GAAKgB,MAAO,GAAM0J,GAC1CrK,UAAS,gFAAAuJ,OAAkFoF,EAAQ1B,IAAK7M,SAAA,EAExGP,EAAAA,EAAAA,KAAC8O,EAAQtB,KAAI,CAACrN,UAAS,WAAAuJ,OAAaoF,EAAQrG,MAAK,YACjDzI,EAAAA,EAAAA,KAAA,MAAIG,UAAU,mCAAkCI,SAAEuO,EAAQpB,SAC1D1N,EAAAA,EAAAA,KAAA,KAAGG,UAAU,wBAAuBI,SAAEuO,EAAQnB,gBARzCnD,QAcXhK,EAAAA,EAAAA,MAAA,OAAKL,UAAU,iCAAgCI,SAAA,EAC7CC,EAAAA,EAAAA,MAAA,MAAIL,UAAU,iDAAgDI,SAAA,EAC5DP,EAAAA,EAAAA,KAAC6K,EAAAA,IAAO,CAAC1K,UAAU,gCAAgC,wCAGrDH,EAAAA,EAAAA,KAAA,OAAKG,UAAU,wCAAuCI,SA9IjD,CACf,0CACA,qCACA,0CACA,oCACA,mCACA,yCAyIsB+J,KAAI,CAAC4E,EAAS1E,KACtBhK,EAAAA,EAAAA,MAAA,OAAiBL,UAAU,8BAA6BI,SAAA,EACtDP,EAAAA,EAAAA,KAAC6K,EAAAA,IAAO,CAAC1K,UAAU,0CACnBH,EAAAA,EAAAA,KAAA,QAAMG,UAAU,wBAAuBI,SAAE2O,MAFjC1E,WAShBxK,EAAAA,EAAAA,KAAA,OAAKG,UAAU,iCAAgCI,UAC7CC,EAAAA,EAAAA,MAAA,OAAKL,UAAU,yDAAwDI,SAAA,EACrEC,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEP,EAAAA,EAAAA,KAAA,OAAKG,UAAU,mCAAkCI,SAAC,aAClDP,EAAAA,EAAAA,KAAA,OAAKG,UAAU,wBAAuBI,SAAC,wBAEzCC,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEP,EAAAA,EAAAA,KAAA,OAAKG,UAAU,mCAAkCI,SAAC,aAClDP,EAAAA,EAAAA,KAAA,OAAKG,UAAU,wBAAuBI,SAAC,0BAEzCC,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEP,EAAAA,EAAAA,KAAA,OAAKG,UAAU,mCAAkCI,SAAC,SAClDP,EAAAA,EAAAA,KAAA,OAAKG,UAAU,wBAAuBI,SAAC,0BAM7CC,EAAAA,EAAAA,MAAA,OAAKL,UAAU,YAAWI,SAAA,EACxBP,EAAAA,EAAAA,KAAC4O,EAAAA,GAAI,CAACC,GAAG,YAAY1O,UAAU,QAAOI,UACpCC,EAAAA,EAAAA,MAACP,EAAAA,EAAO8B,OAAM,CACZ5B,UAAU,oOACV+B,WAAY,CAAErB,MAAO,MACrBsB,SAAU,CAAEtB,MAAO,KAAON,SAAA,EAE1BP,EAAAA,EAAAA,KAAA,QAAAO,SAAM,yBACNP,EAAAA,EAAAA,KAAC8B,EAAAA,IAAY,CAAC3B,UAAU,kBAI5BH,EAAAA,EAAAA,KAAC4O,EAAAA,GAAI,CAACC,GAAG,SAAS1O,UAAU,QAAOI,UACjCP,EAAAA,EAAAA,KAAA,UAAQG,UAAU,iHAAgHI,SAAC,wCAKrIP,EAAAA,EAAAA,KAAA,OAAKG,UAAU,cAAaI,UAC1BP,EAAAA,EAAAA,KAAA,UACEW,QAAS+O,EACTvP,UAAU,wDAAuDI,SAClE,sBAOLP,EAAAA,EAAAA,KAAA,OAAKG,UAAU,yCAAwCI,UACrDP,EAAAA,EAAAA,KAAA,KAAAO,SAAG,wGAtKK,IA2KA,EClGtB,EApKkB0P,KAChB,MAAMC,GAAWC,EAAAA,EAAAA,MACXC,GAAWC,EAAAA,EAAAA,OAEVC,EAAaC,IAAkBpS,EAAAA,EAAAA,UAAS,cACxCL,EAAe0S,IAAoBrS,EAAAA,EAAAA,UAAS,OAC5CsS,EAAcC,IAAmBvS,EAAAA,EAAAA,UAAS,OAC1CwS,EAAYC,IAAiBzS,EAAAA,EAAAA,UAAS,OACtC0S,EAAwBC,IAA6B3S,EAAAA,EAAAA,WAAS,IAGrEK,EAAAA,EAAAA,YAAU,KAAO,IAADuS,EACI,QAAlBA,EAAIb,EAASc,aAAK,IAAAD,GAAdA,EAAgBjT,cAClB0S,EAAiBN,EAASc,MAAMlT,eAGhCsS,EAAS,IACX,GACC,CAACF,EAASc,MAAOZ,IAGpB,MAiBMa,EAAaA,KACG,YAAhBX,GACFC,EAAe,aACfG,EAAgB,OACS,cAAhBJ,GACTF,EAAS,IACX,EAsBF,OAAKtS,GAcH0C,EAAAA,EAAAA,MAAA,OAAKL,UAAU,aAAYI,SAAA,EAEzBP,EAAAA,EAAAA,KAAA,OAAKG,UAAU,qEAAoEI,UACjFP,EAAAA,EAAAA,KAAA,OAAKG,UAAU,sHAAqHI,UAClIC,EAAAA,EAAAA,MAAA,OAAKL,UAAU,2CAA0CI,SAAA,EACvDC,EAAAA,EAAAA,MAAA,OAAKL,UAAS,4CAAAuJ,OACI,cAAhB4G,EAA8B,gBAAkB,iBAC/C/P,SAAA,EACDP,EAAAA,EAAAA,KAAA,OAAKG,UAAS,sCAAAuJ,OACI,cAAhB4G,EAA8B,cAAgB,kBAEhDtQ,EAAAA,EAAAA,KAAA,QAAMG,UAAU,kDAAiDI,SAAC,iBAClEP,EAAAA,EAAAA,KAAA,QAAMG,UAAU,2CAA0CI,SAAC,eAG7DP,EAAAA,EAAAA,KAAA,OAAKG,UAAU,iCAEfK,EAAAA,EAAAA,MAAA,OAAKL,UAAS,4CAAAuJ,OACI,YAAhB4G,EAA4B,gBAAkB,iBAC7C/P,SAAA,EACDP,EAAAA,EAAAA,KAAA,OAAKG,UAAS,sCAAAuJ,OACI,YAAhB4G,EAA4B,cAAgB,kBAE9CtQ,EAAAA,EAAAA,KAAA,QAAMG,UAAU,kDAAiDI,SAAC,eAClEP,EAAAA,EAAAA,KAAA,QAAMG,UAAU,2CAA0CI,SAAC,aAG7DP,EAAAA,EAAAA,KAAA,OAAKG,UAAU,iCAEfK,EAAAA,EAAAA,MAAA,OAAKL,UAAS,4CAAAuJ,OACI,WAAhB4G,EAA2B,gBAAkB,iBAC5C/P,SAAA,EACDP,EAAAA,EAAAA,KAAA,OAAKG,UAAS,sCAAAuJ,OACI,WAAhB4G,EAA2B,cAAgB,kBAE7CtQ,EAAAA,EAAAA,KAAA,QAAMG,UAAU,iCAAgCI,SAAC,uBAOzDC,EAAAA,EAAAA,MAACP,EAAAA,EAAOC,IAAG,CAETG,QAAS,CAAEX,QAAS,EAAGqP,EAAG,IAC1BzO,QAAS,CAAEZ,QAAS,EAAGqP,EAAG,GAC1B/E,KAAM,CAAEtK,QAAS,EAAGqP,GAAI,IACxBlP,WAAY,CAAEC,SAAU,IAAMS,SAAA,CAEb,cAAhB+P,IACCtQ,EAAAA,EAAAA,KAACkR,EAAkB,CACjBpT,cAAeA,EACfC,eA/GkBqE,IAC1BsO,EAAgBtO,GAChBmO,EAAe,UAAU,EA8GjBvS,OAAQiT,IAIK,YAAhBX,GAA6BG,IAC5BzQ,EAAAA,EAAAA,KAACmR,EAAa,CACZ/O,SAAUqO,EACVpO,WAjHkBwJ,IAC1B+E,EAAc/E,GACd0E,EAAe,UAGf9D,YAAW,KACTqE,GAA0B,EAAK,GAC9B,IAAK,EA2GA9S,OAAQiT,IAIK,WAAhBX,GAA4BK,IAC3B3Q,EAAAA,EAAAA,KAACoR,EAAe,CACdvF,OAAQ8E,EACR7E,aApGeuF,KACvBd,EAAe,aACfG,EAAgB,MAChBE,EAAc,MACdE,GAA0B,EAAM,EAiGxB/E,WA7FauF,KACrBlB,EAAS,YAAa,CACpBY,MAAO,CACLO,gBAAgB,EAChB5B,YAAagB,EACb7S,cAAeA,IAEjB,MA4DOwS,IAgCPtQ,EAAAA,EAAAA,KAACwR,EAAuB,CACtB/B,OAAQoB,EACRnB,QAASA,IAAMoB,GAA0B,GACzCnB,YAAagB,QA1Ff3Q,EAAAA,EAAAA,KAAA,OAAKG,UAAU,oGAAmGI,UAChHC,EAAAA,EAAAA,MAAA,OAAKL,UAAU,cAAaI,SAAA,EAC1BP,EAAAA,EAAAA,KAAA,OAAKG,UAAU,mFAAkFI,UAC/FP,EAAAA,EAAAA,KAAA,OAAKG,UAAU,uFAEjBH,EAAAA,EAAAA,KAAA,KAAGG,UAAU,gBAAeI,SAAC,oCAuF7B,C", "sources": ["components/trial/TrialQuizSelection.js", "apicalls/trial.js", "components/trial/TrialQuizPlay.js", "components/trial/TrialQuizResult.js", "components/trial/TrialRegistrationPrompt.js", "pages/trial/TrialPage.js"], "sourcesContent": ["import React, { useState, useEffect } from \"react\";\nimport { motion } from \"framer-motion\";\nimport { TbBrain, TbClock, TbQuestionMark, TbArrowRight, TbLoader } from \"react-icons/tb\";\nimport { message } from \"antd\";\nimport { getTrialQuiz } from \"../../apicalls/trial\";\n\nconst TrialQuizSelection = ({ trialUserInfo, onQuizSelected, onBack }) => {\n  const [loading, setLoading] = useState(true);\n  const [quiz, setQuiz] = useState(null);\n  const [error, setError] = useState(null);\n\n  useEffect(() => {\n    fetchTrialQuiz();\n  }, [trialUserInfo]);\n\n  const fetchTrialQuiz = async () => {\n    try {\n      setLoading(true);\n      setError(null);\n\n      const response = await getTrialQuiz({\n        level: trialUserInfo.level,\n        class: trialUserInfo.class\n      });\n\n      if (response.success) {\n        setQuiz(response.data);\n        console.log(\"✅ Trial quiz loaded:\", response.data);\n      } else {\n        setError(response.message || \"Failed to load trial quiz\");\n        message.error(response.message || \"Failed to load trial quiz\");\n      }\n    } catch (error) {\n      console.error(\"❌ Error fetching trial quiz:\", error);\n      setError(\"Something went wrong. Please try again.\");\n      message.error(\"Something went wrong. Please try again.\");\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleStartQuiz = () => {\n    if (quiz && quiz.exam) {\n      onQuizSelected({\n        ...quiz,\n        trialUserInfo\n      });\n    }\n  };\n\n  const containerVariants = {\n    hidden: { opacity: 0, y: 20 },\n    visible: { \n      opacity: 1, \n      y: 0,\n      transition: { duration: 0.6, ease: \"easeOut\" }\n    }\n  };\n\n  const cardVariants = {\n    hidden: { opacity: 0, scale: 0.9 },\n    visible: { \n      opacity: 1, \n      scale: 1,\n      transition: { duration: 0.5, delay: 0.2 }\n    }\n  };\n\n  if (loading) {\n    return (\n      <motion.div\n        className=\"min-h-screen bg-gradient-to-br from-blue-50 via-white to-blue-50 flex items-center justify-center p-4\"\n        variants={containerVariants}\n        initial=\"hidden\"\n        animate=\"visible\"\n      >\n        <div className=\"text-center\">\n          <div className=\"w-16 h-16 mx-auto mb-4 bg-blue-100 rounded-full flex items-center justify-center\">\n            <TbLoader className=\"w-8 h-8 text-blue-600 animate-spin\" />\n          </div>\n          <h2 className=\"text-xl font-semibold text-gray-800 mb-2\">\n            Finding Your Perfect Trial Quiz\n          </h2>\n          <p className=\"text-gray-600\">\n            We're selecting a quiz that matches your level and class...\n          </p>\n        </div>\n      </motion.div>\n    );\n  }\n\n  if (error) {\n    return (\n      <motion.div\n        className=\"min-h-screen bg-gradient-to-br from-blue-50 via-white to-blue-50 flex items-center justify-center p-4\"\n        variants={containerVariants}\n        initial=\"hidden\"\n        animate=\"visible\"\n      >\n        <div className=\"max-w-md w-full bg-white rounded-2xl shadow-xl p-8 text-center\">\n          <div className=\"w-16 h-16 mx-auto mb-4 bg-red-100 rounded-full flex items-center justify-center\">\n            <TbQuestionMark className=\"w-8 h-8 text-red-600\" />\n          </div>\n          <h2 className=\"text-xl font-semibold text-gray-800 mb-2\">\n            Quiz Not Available\n          </h2>\n          <p className=\"text-gray-600 mb-6\">\n            {error}\n          </p>\n          <div className=\"space-y-3\">\n            <button\n              onClick={fetchTrialQuiz}\n              className=\"w-full py-3 px-6 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors font-medium\"\n            >\n              Try Again\n            </button>\n            <button\n              onClick={onBack}\n              className=\"w-full py-3 px-6 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors font-medium\"\n            >\n              Go Back\n            </button>\n          </div>\n        </div>\n      </motion.div>\n    );\n  }\n\n  return (\n    <motion.div\n      className=\"min-h-screen bg-gradient-to-br from-blue-50 via-white to-blue-50 flex items-center justify-center p-4\"\n      variants={containerVariants}\n      initial=\"hidden\"\n      animate=\"visible\"\n    >\n      <div className=\"max-w-2xl w-full mx-auto\">\n        {/* Welcome Header */}\n        <motion.div\n          className=\"text-center mb-6 sm:mb-8\"\n          initial={{ opacity: 0, y: -20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.6 }}\n        >\n          <h1 className=\"text-2xl sm:text-3xl md:text-4xl font-bold text-gray-800 mb-2\">\n            Welcome, {trialUserInfo.name}! 👋\n          </h1>\n          <p className=\"text-base sm:text-lg text-gray-600 px-4\">\n            We've found the perfect quiz for your <span className=\"font-semibold text-blue-600\">{trialUserInfo.level} {trialUserInfo.class}</span> level\n          </p>\n        </motion.div>\n\n        {/* Quiz Card */}\n        <motion.div\n          className=\"bg-white rounded-2xl shadow-xl overflow-hidden\"\n          variants={cardVariants}\n          initial=\"hidden\"\n          animate=\"visible\"\n        >\n          {/* Quiz Header */}\n          <div className=\"bg-gradient-to-r from-blue-600 to-blue-700 px-4 sm:px-6 py-6 sm:py-8 text-white\">\n            <div className=\"flex items-center space-x-3 sm:space-x-4\">\n              <div className=\"p-2 sm:p-3 bg-white/20 rounded-xl\">\n                <TbBrain className=\"w-6 h-6 sm:w-8 sm:h-8\" />\n              </div>\n              <div className=\"min-w-0 flex-1\">\n                <h2 className=\"text-lg sm:text-2xl font-bold truncate\">{quiz?.exam?.name}</h2>\n                <p className=\"text-blue-100 text-sm sm:text-base\">\n                  {quiz?.exam?.subject} • {trialUserInfo.level.charAt(0).toUpperCase() + trialUserInfo.level.slice(1)} Level\n                </p>\n              </div>\n            </div>\n          </div>\n\n          {/* Quiz Details */}\n          <div className=\"p-4 sm:p-6\">\n            <div className=\"grid grid-cols-1 sm:grid-cols-3 gap-4 sm:gap-6 mb-6 sm:mb-8\">\n              <div className=\"text-center p-4 bg-blue-50 rounded-xl\">\n                <TbQuestionMark className=\"w-8 h-8 text-blue-600 mx-auto mb-2\" />\n                <div className=\"text-2xl font-bold text-gray-800\">\n                  {quiz?.trialInfo?.trialQuestionCount || quiz?.exam?.questions?.length || 0}\n                </div>\n                <div className=\"text-sm text-gray-600\">Questions</div>\n              </div>\n              \n              <div className=\"text-center p-4 bg-green-50 rounded-xl\">\n                <TbClock className=\"w-8 h-8 text-green-600 mx-auto mb-2\" />\n                <div className=\"text-2xl font-bold text-gray-800\">\n                  {quiz?.trialInfo?.trialDuration || quiz?.exam?.duration || 0}\n                </div>\n                <div className=\"text-sm text-gray-600\">Minutes</div>\n              </div>\n              \n              <div className=\"text-center p-4 bg-purple-50 rounded-xl\">\n                <TbBrain className=\"w-8 h-8 text-purple-600 mx-auto mb-2\" />\n                <div className=\"text-2xl font-bold text-gray-800\">\n                  {quiz?.exam?.passingMarks || Math.ceil((quiz?.exam?.questions?.length || 0) * 0.6)}\n                </div>\n                <div className=\"text-sm text-gray-600\">Pass Mark</div>\n              </div>\n            </div>\n\n            {/* Simple Instructions */}\n            <div className=\"bg-gray-50 border border-gray-200 rounded-xl p-4 mb-6\">\n              <div className=\"flex flex-wrap items-center justify-center gap-4 text-sm text-gray-700\">\n                <div className=\"flex items-center\">\n                  <TbClock className=\"w-4 h-4 mr-2 text-blue-600\" />\n                  <span><strong>{quiz?.trialInfo?.trialDuration || quiz?.exam?.duration || 0}</strong> minutes</span>\n                </div>\n                <div className=\"flex items-center\">\n                  <TbQuestionMark className=\"w-4 h-4 mr-2 text-green-600\" />\n                  <span><strong>{quiz?.trialInfo?.trialQuestionCount || 5}</strong> questions</span>\n                </div>\n                <div className=\"flex items-center\">\n                  <TbBrain className=\"w-4 h-4 mr-2 text-purple-600\" />\n                  <span>Pass mark: <strong>{quiz?.exam?.passingMarks || Math.ceil((quiz?.exam?.questions?.length || 0) * 0.6)}</strong></span>\n                </div>\n                <div className=\"flex items-center\">\n                  <TbArrowRight className=\"w-4 h-4 mr-2 text-orange-600\" />\n                  <span>Click answers to select</span>\n                </div>\n              </div>\n            </div>\n\n\n\n            {/* Action Buttons */}\n            <div className=\"flex flex-col sm:flex-row gap-3\">\n              <button\n                onClick={onBack}\n                className=\"flex-1 py-3 px-6 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors font-medium\"\n              >\n                Go Back\n              </button>\n              <motion.button\n                onClick={handleStartQuiz}\n                className=\"flex-1 py-3 px-6 bg-gradient-to-r from-blue-600 to-blue-700 text-white rounded-lg hover:from-blue-700 hover:to-blue-800 transition-all font-medium flex items-center justify-center space-x-2 shadow-lg hover:shadow-xl\"\n                whileHover={{ scale: 1.02 }}\n                whileTap={{ scale: 0.98 }}\n              >\n                <span>Start Trial Quiz</span>\n                <TbArrowRight className=\"w-5 h-5\" />\n              </motion.button>\n            </div>\n          </div>\n        </motion.div>\n\n        {/* Footer Note */}\n        <motion.div\n          className=\"text-center mt-6 text-sm text-gray-500\"\n          initial={{ opacity: 0 }}\n          animate={{ opacity: 1 }}\n          transition={{ delay: 0.8 }}\n        >\n          After completing this trial, you'll be invited to register for full access to our platform\n        </motion.div>\n      </div>\n    </motion.div>\n  );\n};\n\nexport default TrialQuizSelection;\n", "import axiosInstance from \"./index\";\n\n// Get trial quiz based on level and class (no authentication required)\nexport const getTrialQuiz = async (payload) => {\n  try {\n    const response = await axiosInstance.post(\"/api/trial/get-trial-quiz\", payload);\n    return response.data;\n  } catch (error) {\n    return error.response?.data || { success: false, message: \"Network error\" };\n  }\n};\n\n// Submit trial quiz results (no authentication required)\nexport const submitTrialResult = async (payload) => {\n  try {\n    const response = await axiosInstance.post(\"/api/trial/submit-trial-result\", payload);\n    return response.data;\n  } catch (error) {\n    return error.response?.data || { success: false, message: \"Network error\" };\n  }\n};\n\n// Get trial statistics (optional)\nexport const getTrialStats = async () => {\n  try {\n    const response = await axiosInstance.get(\"/api/trial/trial-stats\");\n    return response.data;\n  } catch (error) {\n    return error.response?.data || { success: false, message: \"Network error\" };\n  }\n};\n", "import React, { useState, useEffect, useCallback } from \"react\";\nimport { motion } from \"framer-motion\";\nimport { TbArrowLeft, TbArrowRight, Tb<PERSON>lock, TbCheck } from \"react-icons/tb\";\nimport { message } from \"antd\";\nimport { submitTrialResult } from \"../../apicalls/trial\";\nimport \"./TrialQuiz.css\";\n\nconst TrialQuizPlay = ({ quizData, onComplete, onBack }) => {\n  const { exam, trialUserInfo } = quizData;\n  const questions = exam.questions || [];\n  \n  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);\n  const [selectedAnswers, setSelectedAnswers] = useState({});\n  const [timeLeft, setTimeLeft] = useState(exam.duration || 180); // Duration is already in seconds\n  const [isSubmitting, setIsSubmitting] = useState(false);\n  const [startTime] = useState(Date.now());\n\n  // Timer effect\n  useEffect(() => {\n    if (timeLeft <= 0) {\n      handleSubmitQuiz();\n      return;\n    }\n\n    const timer = setInterval(() => {\n      setTimeLeft(prev => prev - 1);\n    }, 1000);\n\n    return () => clearInterval(timer);\n  }, [timeLeft]);\n\n  // Duolingo-style time formatting (min:sec format)\n  const formatTime = (seconds) => {\n    const minutes = Math.floor(seconds / 60);\n    const secs = seconds % 60;\n\n    return `${minutes}:${secs.toString().padStart(2, '0')}`;\n  };\n\n  // Handle answer selection\n  const handleAnswerSelect = (questionId, answer) => {\n    playSuccessSound();\n    setSelectedAnswers(prev => ({\n      ...prev,\n      [questionId]: answer\n    }));\n  };\n\n  // Submit quiz\n  const handleSubmitQuiz = useCallback(async () => {\n    if (isSubmitting) return;\n\n    playSubmitSound(); // Play submit sound\n    setIsSubmitting(true);\n    try {\n      const timeSpent = Math.round((Date.now() - startTime) / 1000); // Time in seconds\n\n      const response = await submitTrialResult({\n        examId: exam._id,\n        answers: selectedAnswers,\n        timeSpent,\n        trialUserInfo\n      });\n\n      if (response.success) {\n        onComplete(response.data);\n      } else {\n        message.error(response.message || \"Failed to submit quiz\");\n        setIsSubmitting(false);\n      }\n    } catch (error) {\n      console.error(\"❌ Error submitting trial quiz:\", error);\n      message.error(\"Something went wrong. Please try again.\");\n      setIsSubmitting(false);\n    }\n  }, [exam._id, selectedAnswers, trialUserInfo, startTime, onComplete, isSubmitting]);\n\n  // Enhanced Sound effects for navigation\n  const playNavigationSound = () => {\n    try {\n      // Create a simple click sound using Web Audio API\n      const audioContext = new (window.AudioContext || window.webkitAudioContext)();\n      const oscillator = audioContext.createOscillator();\n      const gainNode = audioContext.createGain();\n\n      oscillator.connect(gainNode);\n      gainNode.connect(audioContext.destination);\n\n      oscillator.frequency.setValueAtTime(800, audioContext.currentTime);\n      oscillator.frequency.exponentialRampToValueAtTime(400, audioContext.currentTime + 0.1);\n\n      gainNode.gain.setValueAtTime(0.1, audioContext.currentTime);\n      gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.1);\n\n      oscillator.start(audioContext.currentTime);\n      oscillator.stop(audioContext.currentTime + 0.1);\n    } catch (error) {\n      console.log('Navigation sound not available');\n    }\n  };\n\n  const playSuccessSound = () => {\n    try {\n      // Create a pleasant success sound\n      const audioContext = new (window.AudioContext || window.webkitAudioContext)();\n      const oscillator = audioContext.createOscillator();\n      const gainNode = audioContext.createGain();\n\n      oscillator.connect(gainNode);\n      gainNode.connect(audioContext.destination);\n\n      oscillator.frequency.setValueAtTime(523, audioContext.currentTime); // C5\n      oscillator.frequency.setValueAtTime(659, audioContext.currentTime + 0.1); // E5\n      oscillator.frequency.setValueAtTime(784, audioContext.currentTime + 0.2); // G5\n\n      gainNode.gain.setValueAtTime(0.15, audioContext.currentTime);\n      gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.3);\n\n      oscillator.start(audioContext.currentTime);\n      oscillator.stop(audioContext.currentTime + 0.3);\n    } catch (error) {\n      console.log('Success sound not available');\n    }\n  };\n\n  const playSubmitSound = () => {\n    try {\n      // Create a completion sound\n      const audioContext = new (window.AudioContext || window.webkitAudioContext)();\n      const oscillator = audioContext.createOscillator();\n      const gainNode = audioContext.createGain();\n\n      oscillator.connect(gainNode);\n      gainNode.connect(audioContext.destination);\n\n      oscillator.frequency.setValueAtTime(440, audioContext.currentTime); // A4\n      oscillator.frequency.setValueAtTime(554, audioContext.currentTime + 0.15); // C#5\n      oscillator.frequency.setValueAtTime(659, audioContext.currentTime + 0.3); // E5\n      oscillator.frequency.setValueAtTime(880, audioContext.currentTime + 0.45); // A5\n\n      gainNode.gain.setValueAtTime(0.2, audioContext.currentTime);\n      gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.6);\n\n      oscillator.start(audioContext.currentTime);\n      oscillator.stop(audioContext.currentTime + 0.6);\n    } catch (error) {\n      console.log('Submit sound not available');\n    }\n  };\n\n  // Navigation functions\n  const goToNext = () => {\n    if (currentQuestionIndex < questions.length - 1) {\n      playNavigationSound();\n      setCurrentQuestionIndex(currentQuestionIndex + 1);\n    }\n  };\n\n  const goToPrevious = () => {\n    if (currentQuestionIndex > 0) {\n      playNavigationSound();\n      setCurrentQuestionIndex(currentQuestionIndex - 1);\n    }\n  };\n\n  const goToQuestion = (index) => {\n    playNavigationSound();\n    setCurrentQuestionIndex(index);\n  };\n\n  if (questions.length === 0) {\n    return (\n      <div className=\"min-h-screen bg-gradient-to-br from-blue-50 via-white to-blue-50 flex items-center justify-center p-4\">\n        <div className=\"text-center\">\n          <h2 className=\"text-xl font-semibold text-gray-800 mb-2\">No Questions Available</h2>\n          <p className=\"text-gray-600 mb-4\">This quiz doesn't have any questions.</p>\n          <button\n            onClick={onBack}\n            className=\"py-2 px-4 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\"\n          >\n            Go Back\n          </button>\n        </div>\n      </div>\n    );\n  }\n\n  const currentQuestion = questions[currentQuestionIndex];\n  const isLastQuestion = currentQuestionIndex === questions.length - 1;\n  const answeredQuestions = Object.keys(selectedAnswers).length;\n\n  // Determine question type and prepare options\n  const questionType = currentQuestion?.type || currentQuestion?.answerType || 'mcq';\n  const isMCQ = questionType === 'mcq' || questionType === 'Options' || questionType === 'multiple-choice';\n  const isFillBlank = questionType === 'fill' || questionType === 'Fill in the Blank' || questionType === 'Free Text';\n  const isDiagram = questionType === 'diagram' || questionType === 'Diagram' || currentQuestion?.image;\n\n  // Prepare options for MCQ questions\n  let questionOptions = [];\n  if (isMCQ) {\n    if (Array.isArray(currentQuestion?.options)) {\n      questionOptions = currentQuestion.options;\n    } else if (typeof currentQuestion?.options === 'object' && currentQuestion?.options !== null) {\n      questionOptions = Object.values(currentQuestion.options);\n    } else {\n      questionOptions = [currentQuestion?.optionA, currentQuestion?.optionB, currentQuestion?.optionC, currentQuestion?.optionD].filter(Boolean);\n    }\n  }\n\n  return (\n    <div className=\"trial-background\" style={{\n      minHeight: '100vh',\n      background: 'linear-gradient(135deg, #eff6ff 0%, #e0e7ff 100%)',\n      width: '100%',\n      overflow: 'hidden'\n    }}>\n      {/* Enhanced Header with Progress */}\n      <div className=\"trial-header\" style={{\n        background: 'white',\n        boxShadow: '0 10px 25px rgba(0, 0, 0, 0.1)',\n        borderBottom: '2px solid #e0e7ff',\n        width: '100%',\n        position: 'sticky',\n        top: 0,\n        zIndex: 100\n      }}>\n        <div className=\"trial-container\">\n          <div style={{\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'space-between',\n            marginBottom: 'clamp(12px, 3vw, 20px)',\n            flexWrap: 'wrap',\n            gap: 'clamp(8px, 2vw, 16px)',\n            width: '100%'\n          }}>\n            <div style={{\n              display: 'flex',\n              alignItems: 'center',\n              gap: 'clamp(8px, 2vw, 16px)',\n              minWidth: 0,\n              flex: 1,\n              maxWidth: '100%'\n            }}>\n              <button\n                onClick={onBack}\n                style={{\n                  padding: 'clamp(8px, 2vw, 12px)',\n                  background: 'rgba(59, 130, 246, 0.1)',\n                  border: 'none',\n                  borderRadius: 'clamp(8px, 2vw, 12px)',\n                  cursor: 'pointer',\n                  transition: 'all 0.3s ease',\n                  display: 'flex',\n                  alignItems: 'center',\n                  justifyContent: 'center',\n                  minWidth: '40px',\n                  minHeight: '40px'\n                }}\n                onMouseEnter={(e) => e.target.style.background = 'rgba(59, 130, 246, 0.2)'}\n                onMouseLeave={(e) => e.target.style.background = 'rgba(59, 130, 246, 0.1)'}\n              >\n                <TbArrowLeft style={{\n                  width: 'clamp(18px, 4vw, 24px)',\n                  height: 'clamp(18px, 4vw, 24px)',\n                  color: '#2563eb'\n                }} />\n              </button>\n              <div style={{ minWidth: 0, flex: 1, overflow: 'hidden' }}>\n                <h1 style={{\n                  fontSize: 'clamp(16px, 4vw, 24px)',\n                  fontWeight: 'bold',\n                  background: 'linear-gradient(135deg, #2563eb 0%, #4f46e5 100%)',\n                  WebkitBackgroundClip: 'text',\n                  WebkitTextFillColor: 'transparent',\n                  margin: 0,\n                  overflow: 'hidden',\n                  textOverflow: 'ellipsis',\n                  whiteSpace: 'nowrap',\n                  lineHeight: 1.2\n                }}>{exam.name}</h1>\n                <div className=\"flex items-center space-x-2 mt-1\">\n                  <span className=\"text-sm sm:text-base text-gray-600\">{exam.subject}</span>\n                  <span className=\"text-gray-400\">•</span>\n                  <span className=\"text-sm sm:text-base font-medium text-blue-600\">\n                    Question {currentQuestionIndex + 1} of {questions.length}\n                  </span>\n                </div>\n              </div>\n            </div>\n\n            <div style={{\n              display: 'flex',\n              alignItems: 'center',\n              gap: 'clamp(8px, 2vw, 16px)',\n              flexShrink: 0,\n              flexWrap: 'wrap',\n              justifyContent: 'flex-end'\n            }}>\n              {/* Duolingo-style Enhanced Timer */}\n              <div style={{ position: 'relative' }}>\n                <div style={{\n                  display: 'flex',\n                  alignItems: 'center',\n                  gap: '8px',\n                  textShadow: '1px 1px 2px rgba(0,0,0,0.5)',\n                  boxShadow: timeLeft <= 60\n                    ? '0 0 20px rgba(239, 68, 68, 0.6), 0 4px 15px rgba(0,0,0,0.3)'\n                    : '0 0 15px rgba(34, 197, 94, 0.4), 0 4px 15px rgba(0,0,0,0.3)',\n                  background: timeLeft <= 60\n                    ? 'linear-gradient(to right, #ef4444, #dc2626)'\n                    : 'linear-gradient(to right, #22c55e, #16a34a)',\n                  color: 'white',\n                  padding: '10px 16px',\n                  borderRadius: '12px',\n                  border: timeLeft <= 60\n                    ? '2px solid #fca5a5'\n                    : '2px solid #86efac',\n                  animation: timeLeft <= 60 ? 'pulse 1s infinite' : 'none'\n                }}>\n                  <TbClock style={{\n                    width: '20px',\n                    height: '20px',\n                    filter: 'drop-shadow(0 2px 4px rgba(0,0,0,0.3))',\n                    animation: timeLeft <= 60 ? 'bounce 1s infinite' : 'none'\n                  }} />\n                  <span style={{\n                    fontFamily: 'Monaco, Menlo, monospace',\n                    fontWeight: '900',\n                    fontSize: '18px',\n                    animation: timeLeft <= 60 ? 'pulse 1s infinite' : 'none'\n                  }}>\n                    {formatTime(timeLeft)}\n                  </span>\n                </div>\n                {/* Warning animation for low time */}\n                {timeLeft <= 60 && (\n                  <div className=\"timer-warning-ring\"></div>\n                )}\n              </div>\n\n              <div className=\"hidden sm:flex items-center space-x-2 px-3 py-2 bg-gray-100 rounded-xl\">\n                <span className=\"text-sm font-medium text-gray-700\">\n                  {answeredQuestions}/{questions.length} answered\n                </span>\n              </div>\n            </div>\n          </div>\n\n          {/* Enhanced Progress Bar */}\n          <div className=\"trial-progress-container\">\n            <div className=\"trial-progress-header\">\n              <span className=\"trial-progress-label\">\n                Progress: {currentQuestionIndex + 1} of {questions.length}\n              </span>\n              <span className=\"trial-progress-percentage\">\n                {Math.round(((currentQuestionIndex + 1) / questions.length) * 100)}%\n              </span>\n            </div>\n            <div className=\"trial-progress-bar\">\n              <div\n                className=\"trial-progress-fill\"\n                style={{ width: `${((currentQuestionIndex + 1) / questions.length) * 100}%` }}\n              ></div>\n            </div>\n          </div>\n\n          {/* Mobile progress indicator */}\n          <div className=\"sm:hidden mt-3 flex items-center justify-center\">\n            <div className=\"flex items-center space-x-2 px-3 py-2 bg-gray-100 rounded-lg\">\n              <span className=\"text-sm font-medium text-gray-700\">\n                {answeredQuestions}/{questions.length} answered\n              </span>\n            </div>\n          </div>\n        </div>\n      </div>\n\n\n\n      {/* Question Content */}\n      <div className=\"trial-container\" style={{\n        paddingTop: 'clamp(16px, 4vw, 32px)',\n        paddingBottom: 'clamp(16px, 4vw, 32px)'\n      }}>\n        <motion.div\n          key={currentQuestionIndex}\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          exit={{ opacity: 0, y: -20 }}\n          transition={{ duration: 0.4, ease: \"easeOut\" }}\n          className=\"trial-question-card\"\n        >\n          {/* Question Header */}\n          <div className=\"trial-question-header\">\n            <div className=\"trial-question-number\">\n              <div className=\"trial-question-number-badge\">\n                <span>{currentQuestionIndex + 1}</span>\n              </div>\n              <div>\n                <div style={{ color: 'rgba(255, 255, 255, 0.9)', fontSize: '14px' }}>Question</div>\n                <div style={{ color: 'white', fontWeight: '500', fontSize: '18px' }}>\n                  {currentQuestionIndex + 1} of {questions.length}\n                </div>\n              </div>\n            </div>\n\n            <h2 className=\"trial-question-title\">\n              {currentQuestion.name}\n            </h2>\n          </div>\n\n          {/* Question Content */}\n          <div className=\"trial-content\" style={{\n            padding: 'clamp(16px, 4vw, 40px)',\n            width: '100%',\n            boxSizing: 'border-box'\n          }}>\n            {/* Question Image (if exists) */}\n            {currentQuestion.image && (\n              <div style={{ marginBottom: '32px', textAlign: 'center' }}>\n                <img\n                  src={currentQuestion.image}\n                  alt=\"Question\"\n                  style={{\n                    maxWidth: '100%',\n                    maxHeight: '400px',\n                    borderRadius: '16px',\n                    boxShadow: '0 8px 25px rgba(0, 0, 0, 0.1)'\n                  }}\n                />\n              </div>\n            )}\n\n            {/* Answer Section */}\n            <div className=\"mb-10 sm:mb-12\">\n              {isMCQ && questionOptions.length > 0 ? (\n                // Multiple Choice Questions\n                <div className=\"space-y-4 sm:space-y-5\">\n                  <h3 className=\"text-lg sm:text-xl font-semibold text-gray-800 mb-6\">\n                    Choose your answer:\n                  </h3>\n                  {questionOptions.map((option, index) => {\n                    const optionLetter = String.fromCharCode(65 + index); // A, B, C, D\n                    const isSelected = selectedAnswers[currentQuestion._id] === optionLetter;\n\n                    return (\n                      <motion.button\n                        key={index}\n                        onClick={() => handleAnswerSelect(currentQuestion._id, optionLetter)}\n                        className={`trial-option ${isSelected ? 'selected' : ''}`}\n                        whileHover={{ scale: isSelected ? 1.01 : 1.005 }}\n                        whileTap={{ scale: 0.995 }}\n                      >\n                        <div className=\"trial-option-content\">\n                          <div className=\"trial-option-letter\">\n                            {isSelected ? <TbCheck style={{ width: '24px', height: '24px' }} /> : optionLetter}\n                          </div>\n                          <span className=\"trial-option-text\">\n                            {option}\n                          </span>\n                        </div>\n                      </motion.button>\n                    );\n                  })}\n                </div>\n              ) : isFillBlank ? (\n                // Fill-in-the-blank / Free Text Questions\n                <div>\n                  <h3 style={{ fontSize: '20px', fontWeight: '600', color: '#374151', marginBottom: '24px' }}>\n                    💭 Type your answer:\n                  </h3>\n                  <div style={{ position: 'relative' }}>\n                    <input\n                      type=\"text\"\n                      value={selectedAnswers[currentQuestion._id] || ''}\n                      onChange={(e) => handleAnswerSelect(currentQuestion._id, e.target.value)}\n                      placeholder=\"Type your answer here...\"\n                      className=\"trial-input\"\n                      style={{ paddingRight: '60px' }}\n                    />\n                    <div style={{\n                      position: 'absolute',\n                      right: '20px',\n                      top: '50%',\n                      transform: 'translateY(-50%)',\n                      fontSize: '24px',\n                      color: '#9ca3af'\n                    }}>\n                      ✏️\n                    </div>\n                  </div>\n                  <div style={{\n                    marginTop: '12px',\n                    fontSize: '14px',\n                    color: '#6b7280',\n                    fontStyle: 'italic'\n                  }}>\n                    💡 Tip: Be specific and clear in your answer\n                  </div>\n                </div>\n              ) : isDiagram ? (\n                // Diagram Questions\n                <div>\n                  <h3 style={{ fontSize: '20px', fontWeight: '600', color: '#374151', marginBottom: '24px' }}>\n                    🔍 Study the diagram and answer:\n                  </h3>\n                  <div style={{ position: 'relative' }}>\n                    <input\n                      type=\"text\"\n                      value={selectedAnswers[currentQuestion._id] || ''}\n                      onChange={(e) => handleAnswerSelect(currentQuestion._id, e.target.value)}\n                      placeholder=\"Analyze the diagram and type your answer...\"\n                      className=\"trial-input\"\n                      style={{ paddingRight: '60px' }}\n                    />\n                    <div style={{\n                      position: 'absolute',\n                      right: '20px',\n                      top: '50%',\n                      transform: 'translateY(-50%)',\n                      fontSize: '24px',\n                      color: '#9ca3af'\n                    }}>\n                      📊\n                    </div>\n                  </div>\n                  <div style={{\n                    marginTop: '12px',\n                    fontSize: '14px',\n                    color: '#6b7280',\n                    fontStyle: 'italic'\n                  }}>\n                    🎯 Tip: Look carefully at all parts of the diagram\n                  </div>\n                </div>\n              ) : (\n                // Default fallback\n                <div>\n                  <h3 style={{ fontSize: '20px', fontWeight: '600', color: '#374151', marginBottom: '24px' }}>\n                    📝 Provide your answer:\n                  </h3>\n                  <div style={{ position: 'relative' }}>\n                    <input\n                      type=\"text\"\n                      value={selectedAnswers[currentQuestion._id] || ''}\n                      onChange={(e) => handleAnswerSelect(currentQuestion._id, e.target.value)}\n                      placeholder=\"Type your answer here...\"\n                      className=\"trial-input\"\n                    />\n                  </div>\n                </div>\n              )}\n            </div>\n\n            {/* Navigation Buttons */}\n            <div className=\"trial-nav-buttons\">\n              <motion.button\n                onClick={goToPrevious}\n                disabled={currentQuestionIndex === 0}\n                className={`trial-btn trial-btn-secondary ${currentQuestionIndex === 0 ? '' : ''}`}\n                whileHover={currentQuestionIndex > 0 ? { scale: 1.02 } : {}}\n                whileTap={currentQuestionIndex > 0 ? { scale: 0.98 } : {}}\n              >\n                <TbArrowLeft style={{ width: '20px', height: '20px' }} />\n                <span>Previous</span>\n              </motion.button>\n\n              {isLastQuestion ? (\n                <motion.button\n                  onClick={handleSubmitQuiz}\n                  disabled={isSubmitting}\n                  className={`trial-btn ${isSubmitting ? 'trial-btn-secondary' : 'trial-btn-success'}`}\n                  whileHover={!isSubmitting ? { scale: 1.02 } : {}}\n                  whileTap={!isSubmitting ? { scale: 0.98 } : {}}\n                >\n                  {isSubmitting ? (\n                    <div className=\"trial-spinner\" />\n                  ) : (\n                    <TbCheck style={{ width: '20px', height: '20px' }} />\n                  )}\n                  <span>\n                    {isSubmitting ? 'Submitting...' : 'Submit Quiz'}\n                  </span>\n                </motion.button>\n              ) : (\n                <motion.button\n                  onClick={goToNext}\n                  className=\"trial-btn trial-btn-primary\"\n                  whileHover={{ scale: 1.02 }}\n                  whileTap={{ scale: 0.98 }}\n                >\n                  <span>Next Question</span>\n                  <TbArrowRight style={{ width: '20px', height: '20px' }} />\n                </motion.button>\n              )}\n            </div>\n          </div>\n        </motion.div>\n      </div>\n\n      {/* Trial Watermark */}\n      <div style={{\n        position: 'fixed',\n        bottom: 'clamp(12px, 3vw, 20px)',\n        right: 'clamp(12px, 3vw, 20px)',\n        background: '#2563eb',\n        color: 'white',\n        padding: 'clamp(6px, 1.5vw, 8px) clamp(12px, 3vw, 16px)',\n        borderRadius: '20px',\n        fontSize: 'clamp(10px, 2.5vw, 12px)',\n        fontWeight: '500',\n        boxShadow: '0 4px 12px rgba(37, 99, 235, 0.3)',\n        zIndex: 1000,\n        userSelect: 'none',\n        pointerEvents: 'none'\n      }}>\n        Trial Mode\n      </div>\n    </div>\n  );\n};\n\nexport default TrialQuizPlay;\n", "import React, { useState, useEffect } from \"react\";\nimport { motion } from \"framer-motion\";\nimport { Link } from \"react-router-dom\";\nimport {\n  TbTrophy,\n  TbCheck,\n  TbX,\n  TbClock,\n  TbBrain,\n  TbArrowRight,\n  TbStar,\n  TbUsers,\n  TbBook,\n  TbMessageCircle,\n  TbChartBar,\n  TbSettings\n} from \"react-icons/tb\";\nimport confetti from 'canvas-confetti';\nimport \"./TrialQuiz.css\";\n\nconst TrialQuizResult = ({ result, onTryAnother, onRegister }) => {\n  const [showDetails, setShowDetails] = useState(false);\n  const [animationComplete, setAnimationComplete] = useState(false);\n  const [showFlash, setShowFlash] = useState(false);\n  const [showFailAnimation, setShowFailAnimation] = useState(false);\n\n  // Add confetti and sound effects like normal quiz\n  useEffect(() => {\n    const isPassed = result.percentage >= 60;\n\n    if (isPassed) {\n      // Success confetti and sound\n      setTimeout(() => {\n        confetti({\n          particleCount: 100,\n          spread: 70,\n          origin: { y: 0.6 }\n        });\n\n        // Play clapping sound if available\n        try {\n          const audio = new Audio('/sounds/clap.mp3');\n          audio.volume = 0.3;\n          audio.play().catch(() => {});\n        } catch (error) {\n          console.log('Sound not available');\n        }\n\n        // Green flash animation\n        setShowFlash(true);\n        setTimeout(() => setShowFlash(false), 500);\n      }, 1000);\n    } else {\n      // Enhanced failure effects\n      setTimeout(() => {\n        // Red flash animation for failure\n        setShowFlash(true);\n        setShowFailAnimation(true);\n\n        // Play failure sound\n        try {\n          const audioContext = new (window.AudioContext || window.webkitAudioContext)();\n          const oscillator = audioContext.createOscillator();\n          const gainNode = audioContext.createGain();\n\n          oscillator.connect(gainNode);\n          gainNode.connect(audioContext.destination);\n\n          // Create a descending failure sound\n          oscillator.frequency.setValueAtTime(400, audioContext.currentTime);\n          oscillator.frequency.exponentialRampToValueAtTime(200, audioContext.currentTime + 0.5);\n          oscillator.frequency.exponentialRampToValueAtTime(100, audioContext.currentTime + 1);\n\n          gainNode.gain.setValueAtTime(0.15, audioContext.currentTime);\n          gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 1);\n\n          oscillator.start(audioContext.currentTime);\n          oscillator.stop(audioContext.currentTime + 1);\n        } catch (error) {\n          console.log('Failure sound not available');\n        }\n\n        setTimeout(() => {\n          setShowFlash(false);\n          setShowFailAnimation(false);\n        }, 800);\n      }, 1000);\n    }\n  }, [result.percentage]);\n\n  const formatTime = (seconds) => {\n    const minutes = Math.floor(seconds / 60);\n    const remainingSeconds = seconds % 60;\n    return `${minutes}m ${remainingSeconds}s`;\n  };\n\n  const getPerformanceMessage = (percentage) => {\n    if (percentage >= 90) return {\n      message: \"Outstanding Performance! 🌟\",\n      color: \"text-purple-600\",\n      bg: \"bg-purple-50\",\n      gradient: \"from-purple-500 to-purple-600\"\n    };\n    if (percentage >= 80) return {\n      message: \"Excellent Work! 🎉\",\n      color: \"text-green-600\",\n      bg: \"bg-green-50\",\n      gradient: \"from-green-500 to-green-600\"\n    };\n    if (percentage >= 70) return {\n      message: \"Great Job! 👏\",\n      color: \"text-blue-600\",\n      bg: \"bg-blue-50\",\n      gradient: \"from-blue-500 to-blue-600\"\n    };\n    if (percentage >= 60) return {\n      message: \"Well Done! ✨\",\n      color: \"text-emerald-600\",\n      bg: \"bg-emerald-50\",\n      gradient: \"from-emerald-500 to-emerald-600\"\n    };\n    if (percentage >= 40) return {\n      message: \"Good Effort! 💪\",\n      color: \"text-yellow-600\",\n      bg: \"bg-yellow-50\",\n      gradient: \"from-yellow-500 to-yellow-600\"\n    };\n    return {\n      message: \"Keep Practicing! 📚\",\n      color: \"text-orange-600\",\n      bg: \"bg-orange-50\",\n      gradient: \"from-orange-500 to-orange-600\"\n    };\n  };\n\n  const performance = getPerformanceMessage(result.percentage);\n  const isPassed = result.percentage >= 60;\n\n  const premiumFeatures = [\n    {\n      icon: TbBook,\n      title: \"Study Materials\",\n      description: \"Access comprehensive study materials, notes, and resources\"\n    },\n    {\n      icon: TbBrain,\n      title: \"AI Assistant\",\n      description: \"Get personalized explanations and study recommendations\"\n    },\n    {\n      icon: TbChartBar,\n      title: \"Ranking System\",\n      description: \"Compete with other students and track your progress\"\n    },\n    {\n      icon: TbMessageCircle,\n      title: \"Forum Access\",\n      description: \"Ask questions and help other students in our community\"\n    },\n    {\n      icon: TbUsers,\n      title: \"Unlimited Quizzes\",\n      description: \"Take as many quizzes as you want across all subjects\"\n    },\n    {\n      icon: TbStar,\n      title: \"Progress Tracking\",\n      description: \"Detailed analytics and performance insights\"\n    }\n  ];\n\n  return (\n    <div className=\"trial-background\" style={{\n      minHeight: '100vh',\n      background: 'linear-gradient(135deg, #eff6ff 0%, #ffffff 50%, #eff6ff 100%)',\n      position: 'relative'\n    }}>\n      {/* Flash Animation Overlay */}\n      {showFlash && (\n        <div\n          className={`flash-animation ${\n            result.percentage >= 60 ? 'flash-success' : 'flash-failure'\n          }`}\n        />\n      )}\n\n      <div className=\"trial-container\" style={{\n        position: 'relative',\n        zIndex: 10,\n        padding: 'clamp(12px, 3vw, 32px)',\n        width: '100%',\n        maxWidth: '100%',\n        boxSizing: 'border-box'\n      }}>\n        {/* Animated Result Header */}\n        <motion.div\n          initial={{ opacity: 0, y: -30 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8, ease: \"easeOut\" }}\n          className=\"text-center mb-8 sm:mb-12\"\n        >\n          <motion.div\n            initial={{ scale: 0 }}\n            animate={{ scale: 1 }}\n            transition={{ duration: 0.6, delay: 0.3, type: \"spring\", stiffness: 200 }}\n            className={`inline-flex items-center justify-center rounded-full bg-gradient-to-r ${performance.gradient} mb-6 shadow-lg ${showFailAnimation ? 'trial-fail-shake' : ''}`}\n            style={{\n              width: 'clamp(64px, 15vw, 112px)',\n              height: 'clamp(64px, 15vw, 112px)'\n            }}\n            onAnimationComplete={() => setAnimationComplete(true)}\n          >\n            <TbTrophy\n              className=\"text-white\"\n              style={{\n                width: 'clamp(32px, 8vw, 56px)',\n                height: 'clamp(32px, 8vw, 56px)'\n              }}\n            />\n          </motion.div>\n\n          <motion.h1\n            initial={{ opacity: 0 }}\n            animate={{ opacity: 1 }}\n            transition={{ duration: 0.6, delay: 0.6 }}\n            className={`font-bold text-gray-900 mb-4 ${showFailAnimation ? 'trial-fail-glow' : ''}`}\n            style={{ fontSize: 'clamp(24px, 6vw, 48px)' }}\n          >\n            Quiz Complete! 🎉\n          </motion.h1>\n\n          <motion.div\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.6, delay: 0.8 }}\n            className={`inline-block px-6 py-3 rounded-full ${performance.bg} border-2 border-${performance.color.split('-')[1]}-200`}\n          >\n            <p className={`text-xl sm:text-2xl font-bold ${performance.color}`}>\n              {performance.message}\n            </p>\n          </motion.div>\n        </motion.div>\n\n        {/* Animated Score Card */}\n        <motion.div\n          initial={{ opacity: 0, scale: 0.8 }}\n          animate={{ opacity: 1, scale: 1 }}\n          transition={{ duration: 0.6, delay: 1.0 }}\n          className=\"bg-white rounded-3xl shadow-2xl border border-gray-100 overflow-hidden mb-8 sm:mb-12\"\n        >\n          {/* Score Header */}\n          <div className={`bg-gradient-to-r ${performance.gradient} px-6 sm:px-8 lg:px-10 py-6 sm:py-8`}>\n            <div className=\"text-center\">\n              <motion.div\n                initial={{ scale: 0 }}\n                animate={{ scale: 1 }}\n                transition={{ duration: 0.8, delay: 1.2, type: \"spring\" }}\n                className={`font-bold text-white mb-2 ${showFailAnimation ? 'trial-fail-bounce' : ''}`}\n                style={{ fontSize: 'clamp(48px, 12vw, 96px)' }}\n              >\n                {result.percentage}%\n              </motion.div>\n              <div className=\"text-white/90 text-lg sm:text-xl\">\n                Your Score\n              </div>\n            </div>\n          </div>\n\n          {/* Score Details */}\n          <div className=\"px-6 sm:px-8 lg:px-10 py-8 sm:py-10\"\n        >\n            <div className=\"grid grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6 mb-8\">\n              {[\n                {\n                  label: \"Total Questions\",\n                  value: result.totalQuestions,\n                  icon: TbBook,\n                  color: \"blue\",\n                  delay: 1.4\n                },\n                {\n                  label: \"Correct Answers\",\n                  value: result.correctAnswers,\n                  icon: TbCheck,\n                  color: \"green\",\n                  delay: 1.6\n                },\n                {\n                  label: \"Wrong Answers\",\n                  value: result.wrongAnswers,\n                  icon: TbX,\n                  color: \"red\",\n                  delay: 1.8\n                },\n                {\n                  label: \"Time Taken\",\n                  value: formatTime(result.timeSpent),\n                  icon: TbClock,\n                  color: \"purple\",\n                  delay: 2.0\n                }\n              ].map((stat, index) => (\n                <motion.div\n                  key={index}\n                  initial={{ opacity: 0, y: 20 }}\n                  animate={{ opacity: 1, y: 0 }}\n                  transition={{ duration: 0.5, delay: stat.delay }}\n                  className={`p-4 sm:p-6 bg-${stat.color}-50 rounded-2xl border border-${stat.color}-100 text-center`}\n                >\n                  <div className={`w-12 h-12 mx-auto mb-3 bg-${stat.color}-100 rounded-xl flex items-center justify-center`}>\n                    <stat.icon className={`w-6 h-6 text-${stat.color}-600`} />\n                  </div>\n                  <div className={`text-2xl sm:text-3xl font-bold text-${stat.color}-600 mb-1`}>\n                    {stat.value}\n                  </div>\n                  <div className=\"text-sm text-gray-600 font-medium\">\n                    {stat.label}\n                  </div>\n                </motion.div>\n              ))}\n            </div>\n\n            {/* Pass/Fail Status */}\n            <motion.div\n              initial={{ opacity: 0, scale: 0.9 }}\n              animate={{ opacity: 1, scale: 1 }}\n              transition={{ duration: 0.5, delay: 2.2 }}\n              className=\"text-center mb-8\"\n            >\n              <div className={`inline-flex items-center space-x-3 px-6 py-4 rounded-2xl text-lg font-semibold ${\n                isPassed\n                  ? 'bg-green-100 text-green-700 border-2 border-green-200'\n                  : 'bg-red-100 text-red-700 border-2 border-red-200'\n              }`}>\n                {isPassed ? (\n                  <TbCheck className=\"w-6 h-6\" />\n                ) : (\n                  <TbX className=\"w-6 h-6\" />\n                )}\n                <span>\n                  {isPassed ? '🎉 Congratulations! You Passed!' : '📚 Keep Studying! You Can Do Better!'}\n                </span>\n              </div>\n            </motion.div>\n\n            {/* Show Details Button */}\n            <motion.div\n              initial={{ opacity: 0 }}\n              animate={{ opacity: 1 }}\n              transition={{ duration: 0.5, delay: 2.4 }}\n              className=\"text-center\"\n            >\n              <button\n                onClick={() => setShowDetails(!showDetails)}\n                className=\"inline-flex items-center space-x-2 px-6 py-3 bg-gray-100 hover:bg-gray-200 text-gray-700 rounded-xl font-medium transition-all duration-200\"\n              >\n                <TbChartBar className=\"w-5 h-5\" />\n                <span>{showDetails ? 'Hide Question Summary' : 'View Question Summary'}</span>\n              </button>\n            </motion.div>\n          </div>\n        </motion.div>\n\n        {/* Register Now Button */}\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.6, delay: 2.6 }}\n          className=\"text-center mb-8\"\n        >\n          <Link to=\"/register\">\n            <motion.button\n              className=\"px-12 py-4 bg-gradient-to-r from-blue-600 to-blue-700 text-white rounded-2xl font-bold text-xl hover:from-blue-700 hover:to-blue-800 transition-all shadow-xl hover:shadow-2xl flex items-center justify-center space-x-3 mx-auto\"\n              whileHover={{ scale: 1.05, y: -2 }}\n              whileTap={{ scale: 0.98 }}\n            >\n              <span>Register Now</span>\n              <TbArrowRight className=\"w-6 h-6\" />\n            </motion.button>\n          </Link>\n        </motion.div>\n\n        {/* Unlock Features Section */}\n        <motion.div\n          initial={{ opacity: 0, y: 30 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8, delay: 2.8 }}\n          className=\"bg-white rounded-3xl shadow-xl border border-gray-100 overflow-hidden mb-8\"\n        >\n          {/* Header */}\n          <div className=\"bg-gradient-to-r from-blue-600 to-purple-600 px-6 sm:px-8 py-6 text-white text-center\">\n            <h3 className=\"text-2xl sm:text-3xl font-bold mb-2\">\n              🔓 Unlock These Amazing Features\n            </h3>\n            <p className=\"text-blue-100 text-lg\">\n              Join thousands of students already excelling with BrainWave\n            </p>\n          </div>\n\n          {/* Features Grid */}\n          <div className=\"p-6 sm:p-8\">\n            <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\n              {premiumFeatures.map((feature, index) => (\n                <motion.div\n                  key={index}\n                  initial={{ opacity: 0, y: 20 }}\n                  animate={{ opacity: 1, y: 0 }}\n                  transition={{ duration: 0.5, delay: 3.0 + (0.1 * index) }}\n                  className=\"group bg-gradient-to-br from-blue-50 to-purple-50 rounded-2xl p-6 border border-blue-100 hover:shadow-lg transition-all duration-300\"\n                >\n                  <div className=\"flex items-center space-x-4 mb-4\">\n                    <div className=\"w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-500 rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300\">\n                      <feature.icon className=\"w-6 h-6 text-white\" />\n                    </div>\n                    <h4 className=\"text-lg font-bold text-gray-800\">{feature.title}</h4>\n                  </div>\n                  <p className=\"text-gray-600 leading-relaxed\">{feature.description}</p>\n                  <div className=\"mt-4 flex items-center text-blue-600 font-medium\">\n                    <TbStar className=\"w-5 h-5 mr-2\" />\n                    <span className=\"text-sm\">Premium Feature</span>\n                  </div>\n                </motion.div>\n              ))}\n            </div>\n\n            {/* Better Quiz Features */}\n            <motion.div\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.6, delay: 3.4 }}\n              className=\"mt-8 bg-gradient-to-r from-purple-50 to-blue-50 rounded-2xl p-6 border border-purple-200\"\n            >\n              <h4 className=\"text-xl font-bold text-gray-800 mb-6 text-center flex items-center justify-center\">\n                <TbBrain className=\"w-6 h-6 mr-2 text-purple-600\" />\n                Advanced Quiz Features & Maximum Control\n              </h4>\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n                {/* Multiple Subject Selection */}\n                <motion.div\n                  initial={{ opacity: 0, x: -20 }}\n                  animate={{ opacity: 1, x: 0 }}\n                  transition={{ duration: 0.5, delay: 3.6 }}\n                  className=\"bg-white rounded-xl p-5 shadow-sm border border-purple-100\"\n                >\n                  <div className=\"flex items-center mb-3\">\n                    <div className=\"w-10 h-10 bg-gradient-to-r from-purple-500 to-blue-500 rounded-lg flex items-center justify-center mr-3\">\n                      <TbBook className=\"w-5 h-5 text-white\" />\n                    </div>\n                    <h5 className=\"font-bold text-gray-800\">Multiple Subject Selection</h5>\n                  </div>\n                  <ul className=\"text-sm text-gray-600 space-y-2\">\n                    <li className=\"flex items-start\">\n                      <span className=\"text-purple-500 mr-2\">•</span>\n                      <span>Choose from <strong>15+ subjects</strong> across all levels</span>\n                    </li>\n                    <li className=\"flex items-start\">\n                      <span className=\"text-purple-500 mr-2\">•</span>\n                      <span>Mix and match subjects in custom quizzes</span>\n                    </li>\n                    <li className=\"flex items-start\">\n                      <span className=\"text-purple-500 mr-2\">•</span>\n                      <span>Subject-specific performance tracking</span>\n                    </li>\n                    <li className=\"flex items-start\">\n                      <span className=\"text-purple-500 mr-2\">•</span>\n                      <span>Cross-subject comparison analytics</span>\n                    </li>\n                  </ul>\n                </motion.div>\n\n                {/* Maximum Control */}\n                <motion.div\n                  initial={{ opacity: 0, x: 20 }}\n                  animate={{ opacity: 1, x: 0 }}\n                  transition={{ duration: 0.5, delay: 3.8 }}\n                  className=\"bg-white rounded-xl p-5 shadow-sm border border-blue-100\"\n                >\n                  <div className=\"flex items-center mb-3\">\n                    <div className=\"w-10 h-10 bg-gradient-to-r from-blue-500 to-green-500 rounded-lg flex items-center justify-center mr-3\">\n                      <TbSettings className=\"w-5 h-5 text-white\" />\n                    </div>\n                    <h5 className=\"font-bold text-gray-800\">Maximum Quiz Control</h5>\n                  </div>\n                  <ul className=\"text-sm text-gray-600 space-y-2\">\n                    <li className=\"flex items-start\">\n                      <span className=\"text-blue-500 mr-2\">•</span>\n                      <span>Set custom <strong>time limits</strong> (5-180 minutes)</span>\n                    </li>\n                    <li className=\"flex items-start\">\n                      <span className=\"text-blue-500 mr-2\">•</span>\n                      <span>Choose question count (5-100 questions)</span>\n                    </li>\n                    <li className=\"flex items-start\">\n                      <span className=\"text-blue-500 mr-2\">•</span>\n                      <span>Select difficulty levels (Easy, Medium, Hard)</span>\n                    </li>\n                    <li className=\"flex items-start\">\n                      <span className=\"text-blue-500 mr-2\">•</span>\n                      <span>Pause and resume quiz sessions</span>\n                    </li>\n                  </ul>\n                </motion.div>\n              </div>\n\n              {/* Advanced Features */}\n              <motion.div\n                initial={{ opacity: 0, y: 20 }}\n                animate={{ opacity: 1, y: 0 }}\n                transition={{ duration: 0.5, delay: 4.0 }}\n                className=\"mt-6 bg-gradient-to-r from-green-100 to-blue-100 rounded-xl p-5\"\n              >\n                <h5 className=\"font-bold text-gray-800 mb-4 text-center\">🚀 Advanced Quiz Features</h5>\n                <div className=\"grid grid-cols-2 md:grid-cols-4 gap-4\">\n                  {[\n                    { icon: \"⏱️\", title: \"Smart Timer\", desc: \"Adaptive timing\" },\n                    { icon: \"🎯\", title: \"Targeted Practice\", desc: \"Weak area focus\" },\n                    { icon: \"📊\", title: \"Live Analytics\", desc: \"Real-time insights\" },\n                    { icon: \"🏆\", title: \"Achievement System\", desc: \"Unlock rewards\" }\n                  ].map((feature, index) => (\n                    <motion.div\n                      key={index}\n                      initial={{ opacity: 0, scale: 0.9 }}\n                      animate={{ opacity: 1, scale: 1 }}\n                      transition={{ duration: 0.3, delay: 4.2 + (0.1 * index) }}\n                      className=\"text-center p-3 bg-white rounded-lg shadow-sm\"\n                    >\n                      <div className=\"text-xl mb-1\">{feature.icon}</div>\n                      <div className=\"font-semibold text-xs text-gray-800\">{feature.title}</div>\n                      <div className=\"text-xs text-gray-600\">{feature.desc}</div>\n                    </motion.div>\n                  ))}\n                </div>\n              </motion.div>\n            </motion.div>\n\n            {/* Additional Benefits */}\n            <motion.div\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.6, delay: 3.6 }}\n              className=\"mt-8 bg-gradient-to-r from-green-50 to-blue-50 rounded-2xl p-6 border border-green-200\"\n            >\n              <h4 className=\"text-xl font-bold text-gray-800 mb-4 text-center\">\n                🎯 Why Students Choose BrainWave\n              </h4>\n              <div className=\"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4\">\n                {[\n                  { icon: \"🚀\", title: \"Instant Access\", desc: \"Start learning immediately\" },\n                  { icon: \"📱\", title: \"Mobile Friendly\", desc: \"Study anywhere, anytime\" },\n                  { icon: \"🎓\", title: \"Expert Content\", desc: \"Created by top educators\" },\n                  { icon: \"🏆\", title: \"Proven Results\", desc: \"98% success rate\" }\n                ].map((benefit, index) => (\n                  <motion.div\n                    key={index}\n                    initial={{ opacity: 0, scale: 0.9 }}\n                    animate={{ opacity: 1, scale: 1 }}\n                    transition={{ duration: 0.4, delay: 3.8 + (0.1 * index) }}\n                    className=\"text-center p-4 bg-white rounded-xl shadow-sm\"\n                  >\n                    <div className=\"text-2xl mb-2\">{benefit.icon}</div>\n                    <div className=\"font-semibold text-gray-800 mb-1\">{benefit.title}</div>\n                    <div className=\"text-sm text-gray-600\">{benefit.desc}</div>\n                  </motion.div>\n                ))}\n              </div>\n            </motion.div>\n\n            {/* Call to Action */}\n            <motion.div\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.6, delay: 4.2 }}\n              className=\"text-center mt-8 p-6 bg-gradient-to-r from-blue-600 to-purple-600 rounded-2xl text-white\"\n            >\n              <h4 className=\"text-xl font-bold mb-2\">Ready to Excel? 🌟</h4>\n              <p className=\"text-blue-100 mb-4\">Join BrainWave today and unlock your full potential!</p>\n              <div className=\"flex flex-col sm:flex-row gap-3 justify-center items-center\">\n                <Link to=\"/register\">\n                  <motion.button\n                    className=\"px-8 py-3 bg-white text-blue-600 rounded-xl font-bold hover:bg-blue-50 transition-all shadow-lg flex items-center space-x-2\"\n                    whileHover={{ scale: 1.05 }}\n                    whileTap={{ scale: 0.98 }}\n                  >\n                    <span>Create Free Account</span>\n                    <TbArrowRight className=\"w-5 h-5\" />\n                  </motion.button>\n                </Link>\n                <div className=\"text-blue-200 text-sm\">\n                  ✨ No credit card required • Start immediately\n                </div>\n              </div>\n            </motion.div>\n          </div>\n        </motion.div>\n\n        {/* Question Details */}\n        {showDetails && (\n          <motion.div\n            initial={{ opacity: 0, height: 0 }}\n            animate={{ opacity: 1, height: \"auto\" }}\n            transition={{ duration: 0.3 }}\n            className=\"bg-white rounded-2xl shadow-xl p-6 mb-8\"\n          >\n            <h3 className=\"text-xl font-bold text-gray-800 mb-4\">Question Review</h3>\n            <div className=\"space-y-4\">\n              {result.questionResults?.map((q, index) => (\n                <div key={index} className={`p-4 rounded-lg border-2 ${\n                  q.isCorrect ? 'border-green-200 bg-green-50' : 'border-red-200 bg-red-50'\n                }`}>\n                  <div className=\"flex items-start space-x-3\">\n                    <div className={`w-8 h-8 rounded-full flex items-center justify-center ${\n                      q.isCorrect ? 'bg-green-500 text-white' : 'bg-red-500 text-white'\n                    }`}>\n                      {q.isCorrect ? <TbCheck className=\"w-4 h-4\" /> : <TbX className=\"w-4 h-4\" />}\n                    </div>\n                    <div className=\"flex-1\">\n                      <p className=\"font-medium text-gray-800 mb-2\">{q.question}</p>\n                      <div className=\"text-sm space-y-1\">\n                        <p>\n                          <span className=\"text-gray-600\">Your answer:</span>\n                          <span className={`ml-2 font-medium ${q.isCorrect ? 'text-green-600' : 'text-red-600'}`}>\n                            {q.userAnswer || 'Not answered'}\n                          </span>\n                        </p>\n                        {!q.isCorrect && (\n                          <p>\n                            <span className=\"text-gray-600\">Correct answer:</span>\n                            <span className=\"ml-2 font-medium text-green-600\">{q.correctAnswer}</span>\n                          </p>\n                        )}\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              ))}\n            </div>\n          </motion.div>\n        )}\n\n\n\n        {/* Action Buttons */}\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.5, delay: 0.6 }}\n          className=\"flex flex-col sm:flex-row gap-4 justify-center\"\n        >\n          <button\n            onClick={onTryAnother}\n            className=\"px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors font-medium\"\n          >\n            Try Another Quiz\n          </button>\n          \n          <Link to=\"/\">\n            <button className=\"px-6 py-3 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors font-medium\">\n              Back to Home\n            </button>\n          </Link>\n        </motion.div>\n\n        {/* Trial Badge */}\n        <div className=\"text-center mt-8\">\n          <span className=\"inline-block bg-yellow-100 text-yellow-800 px-4 py-2 rounded-full text-sm font-medium\">\n            🎯 Trial Mode - Register for unlimited access\n          </span>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default TrialQuizResult;\n", "import React from \"react\";\nimport { motion, AnimatePresence } from \"framer-motion\";\nimport { Link } from \"react-router-dom\";\nimport { \n  TbX, \n  TbBrain, \n  TbBook, \n  TbChartBar, \n  TbMessageCircle, \n  TbUsers,\n  TbStar,\n  TbArrowRight,\n  TbCheck,\n  TbInfinity,\n  TbTrophy,\n  TbBulb\n} from \"react-icons/tb\";\n\nconst TrialRegistrationPrompt = ({ isOpen, onClose, trialResult }) => {\n  const premiumFeatures = [\n    {\n      icon: TbInfinity,\n      title: \"Unlimited Quizzes\",\n      description: \"Access thousands of quizzes across all subjects and levels\",\n      color: \"text-blue-600\",\n      bg: \"bg-blue-50\"\n    },\n    {\n      icon: TbBook,\n      title: \"Study Materials\",\n      description: \"Comprehensive notes, videos, and resources for all subjects\",\n      color: \"text-green-600\",\n      bg: \"bg-green-50\"\n    },\n    {\n      icon: TbBrain,\n      title: \"AI Assistant\",\n      description: \"Get personalized explanations and study recommendations\",\n      color: \"text-purple-600\",\n      bg: \"bg-purple-50\"\n    },\n    {\n      icon: Tb<PERSON>hartBar,\n      title: \"Ranking System\",\n      description: \"Compete with students nationwide and track your progress\",\n      color: \"text-orange-600\",\n      bg: \"bg-orange-50\"\n    },\n    {\n      icon: TbMessageCircle,\n      title: \"Forum Access\",\n      description: \"Ask questions and help other students in our community\",\n      color: \"text-indigo-600\",\n      bg: \"bg-indigo-50\"\n    },\n    {\n      icon: TbTrophy,\n      title: \"Achievements\",\n      description: \"Earn badges and rewards for your learning milestones\",\n      color: \"text-yellow-600\",\n      bg: \"bg-yellow-50\"\n    }\n  ];\n\n  const benefits = [\n    \"Track your progress across all subjects\",\n    \"Get detailed performance analytics\",\n    \"Access past papers and exam preparation\",\n    \"Join study groups and discussions\",\n    \"Receive personalized study plans\",\n    \"Get instant feedback and explanations\"\n  ];\n\n  const modalVariants = {\n    hidden: { opacity: 0, scale: 0.8, y: 50 },\n    visible: { \n      opacity: 1, \n      scale: 1, \n      y: 0,\n      transition: { \n        type: \"spring\", \n        damping: 25, \n        stiffness: 300 \n      }\n    },\n    exit: { \n      opacity: 0, \n      scale: 0.8, \n      y: 50,\n      transition: { duration: 0.2 }\n    }\n  };\n\n  const overlayVariants = {\n    hidden: { opacity: 0 },\n    visible: { opacity: 1 },\n    exit: { opacity: 0 }\n  };\n\n  if (!isOpen) return null;\n\n  return (\n    <AnimatePresence>\n      <motion.div\n        className=\"fixed inset-0 z-50 flex items-center justify-center p-4\"\n        variants={overlayVariants}\n        initial=\"hidden\"\n        animate=\"visible\"\n        exit=\"exit\"\n      >\n        {/* Backdrop */}\n        <motion.div\n          className=\"absolute inset-0 bg-black/60 backdrop-blur-sm\"\n          onClick={onClose}\n        />\n        \n        {/* Modal */}\n        <motion.div\n          className=\"relative w-full max-w-4xl max-h-[90vh] mx-4 bg-white rounded-2xl shadow-2xl overflow-hidden\"\n          variants={modalVariants}\n          initial=\"hidden\"\n          animate=\"visible\"\n          exit=\"exit\"\n        >\n          {/* Header */}\n          <div className=\"bg-gradient-to-r from-blue-600 to-purple-600 px-4 sm:px-6 py-4 sm:py-6 text-white relative overflow-hidden\">\n            <div className=\"absolute inset-0 bg-gradient-to-r from-blue-600/90 to-purple-600/90\"></div>\n            <div className=\"relative z-10\">\n              <div className=\"flex items-center justify-between mb-4\">\n                <div className=\"flex items-center space-x-3\">\n                  <div className=\"p-2 bg-white/20 rounded-lg\">\n                    <TbStar className=\"w-6 h-6\" />\n                  </div>\n                  <div>\n                    <h2 className=\"text-xl sm:text-2xl font-bold\">Congratulations! 🎉</h2>\n                    <p className=\"text-blue-100 text-sm sm:text-base\">You've completed your trial quiz</p>\n                  </div>\n                </div>\n                <button\n                  onClick={onClose}\n                  className=\"p-2 hover:bg-white/20 rounded-lg transition-colors\"\n                >\n                  <TbX className=\"w-5 h-5\" />\n                </button>\n              </div>\n\n              {/* Trial Result Summary */}\n              {trialResult && (\n                <div className=\"bg-white/10 backdrop-blur-sm rounded-xl p-4\">\n                  <div className=\"grid grid-cols-3 gap-4 text-center\">\n                    <div>\n                      <div className=\"text-2xl font-bold\">{trialResult.percentage}%</div>\n                      <div className=\"text-sm text-blue-100\">Score</div>\n                    </div>\n                    <div>\n                      <div className=\"text-2xl font-bold\">{trialResult.correctAnswers}/{trialResult.totalQuestions}</div>\n                      <div className=\"text-sm text-blue-100\">Correct</div>\n                    </div>\n                    <div>\n                      <div className=\"text-2xl font-bold\">{trialResult.passed ? 'PASSED' : 'FAILED'}</div>\n                      <div className=\"text-sm text-blue-100\">Result</div>\n                    </div>\n                  </div>\n                </div>\n              )}\n            </div>\n          </div>\n\n          {/* Content */}\n          <div className=\"p-4 sm:p-6 overflow-y-auto max-h-[60vh]\">\n            {/* Unlock Message */}\n            <div className=\"text-center mb-8\">\n              <div className=\"w-16 h-16 mx-auto mb-4 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full flex items-center justify-center\">\n                <TbBulb className=\"w-8 h-8 text-white\" />\n              </div>\n              <h3 className=\"text-2xl font-bold text-gray-800 mb-2\">\n                Ready to Unlock Your Full Potential?\n              </h3>\n              <p className=\"text-gray-600 text-lg\">\n                Join thousands of students who are already excelling with BrainWave\n              </p>\n            </div>\n\n            {/* Premium Features Grid */}\n            <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-8\">\n              {premiumFeatures.map((feature, index) => (\n                <motion.div\n                  key={index}\n                  initial={{ opacity: 0, y: 20 }}\n                  animate={{ opacity: 1, y: 0 }}\n                  transition={{ duration: 0.3, delay: 0.1 * index }}\n                  className={`p-4 rounded-xl border-2 border-gray-100 hover:border-blue-200 transition-all ${feature.bg}`}\n                >\n                  <feature.icon className={`w-8 h-8 ${feature.color} mb-3`} />\n                  <h4 className=\"font-semibold text-gray-800 mb-2\">{feature.title}</h4>\n                  <p className=\"text-sm text-gray-600\">{feature.description}</p>\n                </motion.div>\n              ))}\n            </div>\n\n            {/* Benefits List */}\n            <div className=\"bg-gray-50 rounded-xl p-6 mb-8\">\n              <h4 className=\"font-bold text-gray-800 mb-4 flex items-center\">\n                <TbCheck className=\"w-5 h-5 text-green-600 mr-2\" />\n                What You'll Get With Full Access:\n              </h4>\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-3\">\n                {benefits.map((benefit, index) => (\n                  <div key={index} className=\"flex items-center space-x-2\">\n                    <TbCheck className=\"w-4 h-4 text-green-600 flex-shrink-0\" />\n                    <span className=\"text-sm text-gray-700\">{benefit}</span>\n                  </div>\n                ))}\n              </div>\n            </div>\n\n            {/* Social Proof */}\n            <div className=\"bg-blue-50 rounded-xl p-6 mb-8\">\n              <div className=\"flex items-center justify-center space-x-8 text-center\">\n                <div>\n                  <div className=\"text-2xl font-bold text-blue-600\">10,000+</div>\n                  <div className=\"text-sm text-gray-600\">Active Students</div>\n                </div>\n                <div>\n                  <div className=\"text-2xl font-bold text-blue-600\">50,000+</div>\n                  <div className=\"text-sm text-gray-600\">Quizzes Completed</div>\n                </div>\n                <div>\n                  <div className=\"text-2xl font-bold text-blue-600\">95%</div>\n                  <div className=\"text-sm text-gray-600\">Success Rate</div>\n                </div>\n              </div>\n            </div>\n\n            {/* Action Buttons */}\n            <div className=\"space-y-4\">\n              <Link to=\"/register\" className=\"block\">\n                <motion.button\n                  className=\"w-full py-4 px-6 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-xl font-bold text-lg hover:from-blue-700 hover:to-purple-700 transition-all shadow-lg hover:shadow-xl flex items-center justify-center space-x-2\"\n                  whileHover={{ scale: 1.02 }}\n                  whileTap={{ scale: 0.98 }}\n                >\n                  <span>Create Free Account</span>\n                  <TbArrowRight className=\"w-5 h-5\" />\n                </motion.button>\n              </Link>\n\n              <Link to=\"/login\" className=\"block\">\n                <button className=\"w-full py-3 px-6 border-2 border-gray-300 text-gray-700 rounded-xl font-medium hover:bg-gray-50 transition-all\">\n                  Already have an account? Sign In\n                </button>\n              </Link>\n\n              <div className=\"text-center\">\n                <button\n                  onClick={onClose}\n                  className=\"text-gray-500 hover:text-gray-700 text-sm font-medium\"\n                >\n                  Maybe later\n                </button>\n              </div>\n            </div>\n\n            {/* Trust Indicators */}\n            <div className=\"mt-6 text-center text-xs text-gray-500\">\n              <p>✅ Free to join • ✅ No credit card required • ✅ Cancel anytime</p>\n            </div>\n          </div>\n        </motion.div>\n      </motion.div>\n    </AnimatePresence>\n  );\n};\n\nexport default TrialRegistrationPrompt;\n", "import React, { useState, useEffect } from \"react\";\nimport { useLocation, useNavigate } from \"react-router-dom\";\nimport { motion } from \"framer-motion\";\nimport TrialQuizSelection from \"../../components/trial/TrialQuizSelection\";\nimport TrialQuizPlay from \"../../components/trial/TrialQuizPlay\";\nimport TrialQuizResult from \"../../components/trial/TrialQuizResult\";\nimport TrialRegistrationPrompt from \"../../components/trial/TrialRegistrationPrompt\";\n\nconst TrialPage = () => {\n  const location = useLocation();\n  const navigate = useNavigate();\n  \n  const [currentStep, setCurrentStep] = useState(\"selection\"); // selection, playing, result\n  const [trialUserInfo, setTrialUserInfo] = useState(null);\n  const [selectedQuiz, setSelectedQuiz] = useState(null);\n  const [quizResult, setQuizResult] = useState(null);\n  const [showRegistrationPrompt, setShowRegistrationPrompt] = useState(false);\n\n  // Get trial user info from navigation state\n  useEffect(() => {\n    if (location.state?.trialUserInfo) {\n      setTrialUserInfo(location.state.trialUserInfo);\n    } else {\n      // If no trial user info, redirect to home\n      navigate('/');\n    }\n  }, [location.state, navigate]);\n\n  // Handle quiz selection\n  const handleQuizSelected = (quizData) => {\n    setSelectedQuiz(quizData);\n    setCurrentStep(\"playing\");\n  };\n\n  // Handle quiz completion\n  const handleQuizComplete = (result) => {\n    setQuizResult(result);\n    setCurrentStep(\"result\");\n    \n    // Show registration prompt after a short delay\n    setTimeout(() => {\n      setShowRegistrationPrompt(true);\n    }, 3000);\n  };\n\n  // Handle back navigation\n  const handleBack = () => {\n    if (currentStep === \"playing\") {\n      setCurrentStep(\"selection\");\n      setSelectedQuiz(null);\n    } else if (currentStep === \"selection\") {\n      navigate('/');\n    }\n  };\n\n  // Handle try another quiz\n  const handleTryAnother = () => {\n    setCurrentStep(\"selection\");\n    setSelectedQuiz(null);\n    setQuizResult(null);\n    setShowRegistrationPrompt(false);\n  };\n\n  // Handle registration\n  const handleRegister = () => {\n    navigate('/register', { \n      state: { \n        trialCompleted: true,\n        trialResult: quizResult,\n        trialUserInfo: trialUserInfo\n      }\n    });\n  };\n\n  if (!trialUserInfo) {\n    return (\n      <div className=\"min-h-screen bg-gradient-to-br from-blue-50 via-white to-blue-50 flex items-center justify-center\">\n        <div className=\"text-center\">\n          <div className=\"w-16 h-16 mx-auto mb-4 bg-blue-100 rounded-full flex items-center justify-center\">\n            <div className=\"w-6 h-6 border-2 border-blue-600 border-t-transparent rounded-full animate-spin\"></div>\n          </div>\n          <p className=\"text-gray-600\">Loading trial experience...</p>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"trial-page\">\n      {/* Step Indicator */}\n      <div className=\"fixed top-2 sm:top-4 left-1/2 transform -translate-x-1/2 z-40 px-4\">\n        <div className=\"bg-white/90 backdrop-blur-sm rounded-full px-3 sm:px-6 py-2 shadow-lg border max-w-sm sm:max-w-none overflow-hidden\">\n          <div className=\"flex items-center space-x-2 sm:space-x-4\">\n            <div className={`flex items-center space-x-1 sm:space-x-2 ${\n              currentStep === \"selection\" ? \"text-blue-600\" : \"text-gray-400\"\n            }`}>\n              <div className={`w-2 h-2 sm:w-3 sm:h-3 rounded-full ${\n                currentStep === \"selection\" ? \"bg-blue-600\" : \"bg-gray-300\"\n              }`}></div>\n              <span className=\"text-xs sm:text-sm font-medium hidden sm:inline\">Select Quiz</span>\n              <span className=\"text-xs sm:text-sm font-medium sm:hidden\">Select</span>\n            </div>\n\n            <div className=\"w-4 sm:w-8 h-px bg-gray-300\"></div>\n\n            <div className={`flex items-center space-x-1 sm:space-x-2 ${\n              currentStep === \"playing\" ? \"text-blue-600\" : \"text-gray-400\"\n            }`}>\n              <div className={`w-2 h-2 sm:w-3 sm:h-3 rounded-full ${\n                currentStep === \"playing\" ? \"bg-blue-600\" : \"bg-gray-300\"\n              }`}></div>\n              <span className=\"text-xs sm:text-sm font-medium hidden sm:inline\">Take Quiz</span>\n              <span className=\"text-xs sm:text-sm font-medium sm:hidden\">Quiz</span>\n            </div>\n\n            <div className=\"w-4 sm:w-8 h-px bg-gray-300\"></div>\n\n            <div className={`flex items-center space-x-1 sm:space-x-2 ${\n              currentStep === \"result\" ? \"text-blue-600\" : \"text-gray-400\"\n            }`}>\n              <div className={`w-2 h-2 sm:w-3 sm:h-3 rounded-full ${\n                currentStep === \"result\" ? \"bg-blue-600\" : \"bg-gray-300\"\n              }`}></div>\n              <span className=\"text-xs sm:text-sm font-medium\">Results</span>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Content */}\n      <motion.div\n        key={currentStep}\n        initial={{ opacity: 0, x: 20 }}\n        animate={{ opacity: 1, x: 0 }}\n        exit={{ opacity: 0, x: -20 }}\n        transition={{ duration: 0.3 }}\n      >\n        {currentStep === \"selection\" && (\n          <TrialQuizSelection\n            trialUserInfo={trialUserInfo}\n            onQuizSelected={handleQuizSelected}\n            onBack={handleBack}\n          />\n        )}\n\n        {currentStep === \"playing\" && selectedQuiz && (\n          <TrialQuizPlay\n            quizData={selectedQuiz}\n            onComplete={handleQuizComplete}\n            onBack={handleBack}\n          />\n        )}\n\n        {currentStep === \"result\" && quizResult && (\n          <TrialQuizResult\n            result={quizResult}\n            onTryAnother={handleTryAnother}\n            onRegister={handleRegister}\n          />\n        )}\n      </motion.div>\n\n      {/* Registration Prompt Modal */}\n      <TrialRegistrationPrompt\n        isOpen={showRegistrationPrompt}\n        onClose={() => setShowRegistrationPrompt(false)}\n        trialResult={quizResult}\n      />\n    </div>\n  );\n};\n\nexport default TrialPage;\n"], "names": ["_ref", "_quiz$exam", "_quiz$exam2", "_quiz$trialInfo", "_quiz$exam3", "_quiz$exam3$questions", "_quiz$trialInfo2", "_quiz$exam4", "_quiz$exam5", "_quiz$exam6", "_quiz$exam6$questions", "_quiz$trialInfo3", "_quiz$exam7", "_quiz$trialInfo4", "_quiz$exam8", "_quiz$exam9", "_quiz$exam9$questions", "trialUserInfo", "onQuizSelected", "onBack", "loading", "setLoading", "useState", "quiz", "setQuiz", "error", "setError", "useEffect", "fetchTrialQuiz", "async", "response", "axiosInstance", "post", "payload", "data", "_error$response", "success", "message", "getTrialQuiz", "level", "class", "console", "log", "containerVariants", "hidden", "opacity", "y", "visible", "transition", "duration", "ease", "_jsx", "motion", "div", "className", "variants", "initial", "animate", "children", "_jsxs", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "TbQuestionMark", "onClick", "name", "scale", "delay", "TbBrain", "exam", "subject", "char<PERSON>t", "toUpperCase", "slice", "trialInfo", "trialQuestionCount", "questions", "length", "TbClock", "trialDuration", "passingMarks", "Math", "ceil", "TbArrowRight", "button", "handleStartQuiz", "_objectSpread", "whileHover", "whileTap", "quizData", "onComplete", "currentQuestionIndex", "setCurrentQuestionIndex", "selectedAnswer<PERSON>", "setSelectedAnswers", "timeLeft", "setTimeLeft", "isSubmitting", "setIsSubmitting", "startTime", "Date", "now", "handleSubmitQuiz", "timer", "setInterval", "prev", "clearInterval", "handleAnswerSelect", "questionId", "answer", "playSuccessSound", "useCallback", "playSubmitSound", "timeSpent", "round", "_error$response2", "submitTrialResult", "examId", "_id", "answers", "playNavigationSound", "audioContext", "window", "AudioContext", "webkitAudioContext", "oscillator", "createOscillator", "gainNode", "createGain", "connect", "destination", "frequency", "setValueAtTime", "currentTime", "exponentialRampToValueAtTime", "gain", "start", "stop", "currentQuestion", "isLastQuestion", "answeredQuestions", "Object", "keys", "questionType", "type", "answerType", "isMCQ", "isFillBlank", "isDiagram", "image", "questionOptions", "Array", "isArray", "options", "values", "optionA", "optionB", "optionC", "optionD", "filter", "Boolean", "style", "minHeight", "background", "width", "overflow", "boxShadow", "borderBottom", "position", "top", "zIndex", "display", "alignItems", "justifyContent", "marginBottom", "flexWrap", "gap", "min<PERSON><PERSON><PERSON>", "flex", "max<PERSON><PERSON><PERSON>", "padding", "border", "borderRadius", "cursor", "onMouseEnter", "e", "target", "onMouseLeave", "TbArrowLeft", "height", "color", "fontSize", "fontWeight", "WebkitBackgroundClip", "WebkitTextFillColor", "margin", "textOverflow", "whiteSpace", "lineHeight", "flexShrink", "textShadow", "animation", "fontFamily", "seconds", "minutes", "floor", "secs", "concat", "toString", "padStart", "formatTime", "paddingTop", "paddingBottom", "exit", "boxSizing", "textAlign", "src", "alt", "maxHeight", "map", "option", "index", "optionLetter", "String", "fromCharCode", "isSelected", "TbCheck", "value", "onChange", "placeholder", "paddingRight", "right", "transform", "marginTop", "fontStyle", "goToPrevious", "disabled", "goToNext", "bottom", "userSelect", "pointerEvents", "_result$questionResul", "result", "onTryAnother", "onRegister", "showDetails", "setShowDetails", "animationComplete", "setAnimationComplete", "showFlash", "setShowFlash", "showFailAnimation", "setShowFailAnimation", "percentage", "setTimeout", "confetti", "particleCount", "spread", "origin", "audio", "Audio", "volume", "play", "catch", "performance", "bg", "gradient", "isPassed", "premiumFeatures", "icon", "TbBook", "title", "description", "TbChartBar", "TbMessageCircle", "TbUsers", "TbStar", "stiffness", "onAnimationComplete", "TbTrophy", "h1", "split", "label", "totalQuestions", "correctAnswers", "wrongAnswers", "TbX", "remainingSeconds", "stat", "Link", "to", "feature", "x", "TbSettings", "desc", "benefit", "questionResults", "q", "isCorrect", "question", "userAnswer", "<PERSON><PERSON><PERSON><PERSON>", "isOpen", "onClose", "trialResult", "TbInfinity", "AnimatePresence", "damping", "passed", "TbBulb", "TrialPage", "location", "useLocation", "navigate", "useNavigate", "currentStep", "setCurrentStep", "setTrialUserInfo", "selectedQuiz", "setSelectedQuiz", "quizResult", "setQuizResult", "showRegistrationPrompt", "setShowRegistrationPrompt", "_location$state", "state", "handleBack", "TrialQuizSelection", "TrialQuizPlay", "TrialQuizResult", "handleTryAnother", "handleRegister", "trialCompleted", "TrialRegistrationPrompt"], "sourceRoot": ""}