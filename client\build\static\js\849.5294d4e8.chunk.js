"use strict";(self.webpackChunkclient=self.webpackChunkclient||[]).push([[849],{640:(e,s,l)=>{l.d(s,{Z:()=>i});var a=l(2791),t=l(184);const i=function(e){let{title:s}=e;const[l,i]=(0,a.useState)(!1);return(0,a.useEffect)((()=>{window.innerWidth<768&&i(!0)}),[]),(0,t.jsx)("div",{className:"mt-2",children:(0,t.jsx)("h1",{className:l?"text-lg":"",children:s})})}},4849:(e,s,l)=>{l.r(s),l.d(s,{default:()=>g});var a=l(1413),t=l(7027),i=l(9389),c=l(5725),r=l(222),n=l(2791),d=l(9434),o=l(7689),m=l(6042),x=l(5526),h=l(1652),u=l(640),p=l(8247),v=l(184);const g=function(){const e=(0,o.s0)(),[s,l]=(0,n.useState)([]),[g,f]=(0,n.useState)([]),j=(0,d.I0)(),[b,w]=(0,n.useState)({level:"",class:"",topic:"",search:""}),N=async()=>{try{j((0,p.YC)());const e=await(0,h.h_)();if(j((0,p.Ir)()),e.success){const s=e.data.reverse();l(s),f(s),console.log(e,"exam")}else t.ZP.error(e.message)}catch(e){j((0,p.Ir)()),t.ZP.error(e.message)}},y=(0,n.useMemo)((()=>({levels:[...new Set(s.map((e=>e.level)).filter(Boolean))],classes:[...new Set(s.map((e=>e.class)).filter(Boolean))].sort(),topics:[...new Set(s.map((e=>e.topic)).filter(Boolean))].sort()})),[s]);(0,n.useEffect)((()=>{let e=[...s];if(b.level&&(e=e.filter((e=>e.level&&e.level.toLowerCase()===b.level.toLowerCase()))),b.class&&(e=e.filter((e=>e.class===b.class))),b.topic&&(e=e.filter((e=>e.topic===b.topic))),b.search){const s=b.search.toLowerCase();e=e.filter((e=>{var l,a,t;return(null===(l=e.name)||void 0===l?void 0:l.toLowerCase().includes(s))||(null===(a=e.subject)||void 0===a?void 0:a.toLowerCase().includes(s))||(null===(t=e.category)||void 0===t?void 0:t.toLowerCase().includes(s))}))}f(e)}),[s,b]);const C=(e,s)=>{w((l=>(0,a.Z)((0,a.Z)({},l),{},{[e]:s})))},I=[{title:"Exam Name",dataIndex:"name",width:200,ellipsis:!0},{title:"Level",dataIndex:"level",width:100,render:e=>(0,v.jsx)("span",{className:"px-2 py-1 rounded-full text-xs font-medium ".concat("primary"===e?"bg-green-100 text-green-800":"secondary"===e?"bg-blue-100 text-blue-800":"advance"===e?"bg-purple-100 text-purple-800":"bg-gray-100 text-gray-800"),children:(null===e||void 0===e?void 0:e.charAt(0).toUpperCase())+(null===e||void 0===e?void 0:e.slice(1))})},{title:"Class",dataIndex:"class",width:100},{title:"Subject",dataIndex:"subject",width:120},{title:"Topic",dataIndex:"topic",width:150,ellipsis:!0,render:e=>e||(0,v.jsx)("span",{className:"text-gray-400 italic",children:"General"})},{title:"Duration",dataIndex:"duration",width:100,render:e=>"".concat(Math.round(e/60)," min")},{title:"Questions",dataIndex:"questions",width:100,render:e=>(null===e||void 0===e?void 0:e.length)||0},{title:"Action",dataIndex:"action",render:(s,l)=>(0,v.jsxs)("div",{className:"flex gap-2",children:[(0,v.jsx)("i",{className:"ri-pencil-line",onClick:()=>e("/admin/exams/edit/".concat(l._id))}),(0,v.jsx)("i",{className:"ri-delete-bin-line",onClick:()=>(async e=>{try{j((0,p.YC)());const s=await(0,h.AN)({examId:e});j((0,p.Ir)()),s.success?(t.ZP.success(s.message),N()):t.ZP.error(s.message)}catch(s){j((0,p.Ir)()),t.ZP.error(s.message)}})(l._id)})]})}];return(0,n.useEffect)((()=>{N()}),[]),(0,v.jsxs)("div",{children:[(0,v.jsxs)("div",{className:"flex justify-between mt-2 items-end",children:[(0,v.jsxs)("div",{className:"flex items-center gap-4",children:[(0,v.jsxs)(m.E.button,{whileHover:{scale:1.05},whileTap:{scale:.95},onClick:()=>e("/admin/dashboard"),className:"flex items-center gap-2 px-3 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors duration-200 shadow-md",children:[(0,v.jsx)(x.Qlt,{className:"w-4 h-4"}),(0,v.jsx)("span",{className:"hidden sm:inline text-sm font-medium",children:"Dashboard"})]}),(0,v.jsx)(u.Z,{title:"Exams"})]}),(0,v.jsxs)(m.E.button,{whileHover:{scale:1.05},whileTap:{scale:.95},className:"primary-outlined-btn flex items-center gap-2",onClick:()=>e("/admin/exams/add"),children:[(0,v.jsx)(x.rIf,{className:"w-4 h-4"}),"Add Exam"]})]}),(0,v.jsx)("div",{className:"divider"}),(0,v.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border p-4 mb-6",children:[(0,v.jsxs)("div",{className:"flex items-center gap-2 mb-4",children:[(0,v.jsx)(x.a9n,{className:"w-5 h-5 text-blue-600"}),(0,v.jsx)("h3",{className:"text-lg font-semibold text-gray-800",children:"Filters"}),(b.level||b.class||b.topic||b.search)&&(0,v.jsxs)(m.E.button,{whileHover:{scale:1.05},whileTap:{scale:.95},onClick:()=>{w({level:"",class:"",topic:"",search:""})},className:"ml-auto flex items-center gap-1 px-3 py-1 text-sm bg-gray-100 hover:bg-gray-200 text-gray-700 rounded-lg transition-colors",children:[(0,v.jsx)(x.lhV,{className:"w-4 h-4"}),"Clear All"]})]}),(0,v.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4",children:[(0,v.jsxs)("div",{children:[(0,v.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Search Exams"}),(0,v.jsx)(i.default,{placeholder:"Search by name, subject, category...",prefix:(0,v.jsx)(x.adB,{className:"w-4 h-4 text-gray-400"}),value:b.search,onChange:e=>C("search",e.target.value),allowClear:!0})]}),(0,v.jsxs)("div",{children:[(0,v.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Level"}),(0,v.jsx)(c.default,{placeholder:"Select level",value:b.level||void 0,onChange:e=>C("level",e),allowClear:!0,className:"w-full",children:y.levels.map((e=>(0,v.jsx)(c.default.Option,{value:e,children:e.charAt(0).toUpperCase()+e.slice(1)},e)))})]}),(0,v.jsxs)("div",{children:[(0,v.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Class"}),(0,v.jsx)(c.default,{placeholder:"Select class",value:b.class||void 0,onChange:e=>C("class",e),allowClear:!0,className:"w-full",children:y.classes.map((e=>(0,v.jsx)(c.default.Option,{value:e,children:e},e)))})]}),(0,v.jsxs)("div",{children:[(0,v.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Topic"}),(0,v.jsx)(c.default,{placeholder:"Select topic",value:b.topic||void 0,onChange:e=>C("topic",e),allowClear:!0,className:"w-full",children:y.topics.map((e=>(0,v.jsx)(c.default.Option,{value:e,children:e},e)))})]})]}),(0,v.jsxs)("div",{className:"mt-4 flex items-center justify-between text-sm text-gray-600",children:[(0,v.jsxs)("span",{children:["Showing ",g.length," of ",s.length," exams"]}),g.length!==s.length&&(0,v.jsx)("span",{className:"text-blue-600 font-medium",children:"Filters applied"})]})]}),(0,v.jsx)(r.Z,{columns:I,dataSource:g})]})}}}]);
//# sourceMappingURL=849.5294d4e8.chunk.js.map