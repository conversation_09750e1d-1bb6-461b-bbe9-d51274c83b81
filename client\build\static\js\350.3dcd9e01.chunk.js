"use strict";(self.webpackChunkclient=self.webpackChunkclient||[]).push([[350],{350:(e,s,t)=>{t.r(s),t.d(s,{default:()=>i});var a=t(2791),c=t(8262),r=t(7027),n=t(184);const i=()=>{const[e,s]=(0,a.useState)(""),[t,i]=(0,a.useState)(!1);return(0,a.useEffect)((()=>{localStorage.getItem("token")&&(async()=>{try{const e=await(0,c.bG)();e.success?e.data.isAdmin?i(!0):(i(!1),s(e.data)):r.ZP.error(e.message)}catch(e){r.ZP.error(e.message)}})()}),[]),(0,n.jsxs)("div",{className:"",children:[(0,n.jsx)("div",{children:e.name}),(0,n.jsx)("div",{children:e.school}),(0,n.jsx)("div",{children:e.class})]})}}}]);
//# sourceMappingURL=350.3dcd9e01.chunk.js.map