{"version": 3, "file": "static/css/79.e2679f18.chunk.css", "mappings": "AACA,OAII,0BACI,0CACJ,CAEA,gCACI,0BACJ,CAoBA,wFACI,+BACJ,CAEA,qBACI,UAAW,CACX,yEACJ,CAsCA,6GACI,wFACJ,CAEA,oBACI,kBAAmB,CACnB,kGACJ,CAOA,yCACI,qFACJ,CAEA,uBACI,oBAAqB,CACrB,iGACJ,CA8BA,qMACI,wFACJ,CAEJ,CAEA,yCACI,OACI,SACI,WACJ,CAEA,gBACI,6BACJ,CACJ,CACJ", "sources": ["pages/common/Forum/index.css"], "sourcesContent": ["/* Modern Forum Styles */\r\n.Forum {\r\n    /* Remove old styles and use modern Tailwind classes in JSX */\r\n\r\n    /* Custom animations and effects */\r\n    .forum-question-container {\r\n        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\r\n    }\r\n\r\n    .forum-question-container:hover {\r\n        transform: translateY(-2px);\r\n    }\r\n\r\n    /* Modern button styles */\r\n    .modern-btn {\r\n        @apply inline-flex items-center px-4 py-2 rounded-lg font-medium transition-all duration-200;\r\n    }\r\n\r\n    .modern-btn-primary {\r\n        @apply bg-gradient-to-r from-blue-600 to-indigo-600 text-white hover:from-blue-700 hover:to-indigo-700 shadow-lg hover:shadow-xl;\r\n    }\r\n\r\n    .modern-btn-secondary {\r\n        @apply bg-gray-100 text-gray-700 hover:bg-gray-200;\r\n    }\r\n\r\n    .modern-btn-danger {\r\n        @apply bg-red-50 text-red-600 hover:bg-red-100;\r\n    }\r\n\r\n    /* Profile image enhancements */\r\n    .profile-image {\r\n        @apply relative overflow-hidden;\r\n    }\r\n\r\n    .profile-image::after {\r\n        content: '';\r\n        @apply absolute inset-0 rounded-full ring-2 ring-blue-100 ring-opacity-50;\r\n    }\r\n\r\n    /* Card hover effects */\r\n    .question-card {\r\n        @apply transform transition-all duration-300 hover:scale-[1.02] hover:shadow-xl;\r\n    }\r\n\r\n    /* Typography improvements */\r\n    .question-title {\r\n        @apply text-xl font-bold text-gray-900 leading-tight;\r\n    }\r\n\r\n    .question-body {\r\n        @apply text-gray-700 leading-relaxed;\r\n    }\r\n\r\n    .user-name {\r\n        @apply font-semibold text-gray-900;\r\n    }\r\n\r\n    .timestamp {\r\n        @apply text-sm text-gray-500;\r\n    }\r\n\r\n    /* Modern Replies Section */\r\n    .replies {\r\n        @apply bg-gradient-to-r from-gray-50 to-blue-50 rounded-xl p-6 mt-4 border border-gray-100;\r\n    }\r\n\r\n    .reply {\r\n        @apply mb-4 p-4 bg-white rounded-lg shadow-sm border border-gray-100 transition-all duration-200 hover:shadow-md;\r\n    }\r\n\r\n    .reply-text {\r\n        @apply text-gray-700 leading-relaxed;\r\n    }\r\n\r\n    /* Admin Reply Styles */\r\n    .admin-reply {\r\n        @apply bg-gradient-to-r from-green-50 to-emerald-50 border-l-4 border-green-500 relative;\r\n    }\r\n\r\n    .admin-reply::before {\r\n        content: '👑 Admin';\r\n        @apply absolute -top-2 left-4 bg-green-500 text-white text-xs px-2 py-1 rounded-full font-semibold;\r\n    }\r\n\r\n    .admin-reply .reply-text {\r\n        @apply font-semibold text-green-800;\r\n    }\r\n\r\n    /* Verified Reply Styles */\r\n    .verified-reply {\r\n        @apply bg-gradient-to-r from-blue-50 to-indigo-50 border-l-4 border-blue-500 relative;\r\n    }\r\n\r\n    .verified-reply::before {\r\n        content: '✓ Verified';\r\n        @apply absolute -top-2 left-4 bg-blue-500 text-white text-xs px-2 py-1 rounded-full font-semibold;\r\n    }\r\n\r\n    .verified-reply .reply-text {\r\n        @apply font-semibold text-blue-800;\r\n    }\r\n\r\n    /* Verification Button */\r\n    .verification-btn {\r\n        @apply px-4 py-2 bg-blue-100 text-blue-700 rounded-lg hover:bg-blue-200 transition-colors duration-200 font-medium;\r\n    }\r\n\r\n    /* Date styling */\r\n    .date {\r\n        @apply text-xs text-gray-500 font-medium;\r\n    }\r\n\r\n    /* Form styling */\r\n    .modern-form {\r\n        @apply bg-white rounded-xl p-6 shadow-lg border border-gray-100 mt-6;\r\n    }\r\n\r\n    .modern-form .ant-form-item-label > label {\r\n        @apply font-semibold text-gray-700;\r\n    }\r\n\r\n    .modern-form .ant-input,\r\n    .modern-form .ant-input-affix-wrapper {\r\n        @apply border-gray-300 rounded-lg hover:border-blue-400 focus:border-blue-500;\r\n    }\r\n\r\n    .modern-form .ant-btn-primary {\r\n        @apply bg-gradient-to-r from-blue-600 to-indigo-600 border-none rounded-lg font-semibold;\r\n    }\r\n\r\n}\r\n\r\n@media only screen and (max-width: 768px) {\r\n    .Forum {\r\n        .replies {\r\n            padding: 3px;\r\n        }\r\n\r\n        .verified-reply {\r\n            flex-direction: column-reverse;\r\n        }\r\n    }\r\n}"], "names": [], "sourceRoot": ""}