{"version": 3, "file": "static/js/419.495c73fd.chunk.js", "mappings": "6JAAA,MAAQA,QAASC,GAAkBC,EAAQ,MAG9BC,EAAYC,UACrB,IAEI,aADuBH,EAAcI,KAAK,0BAA2BC,IACrDC,IACpB,CAAE,MAAOC,GACL,OAAOA,EAAMC,SAASF,IAC1B,GAISG,EAAgBN,UACzB,IAEI,aADuBH,EAAcI,KAAK,+BAAgCM,IAC1DJ,IACpB,CAAE,MAAOC,GACL,OAAOA,EAAMC,SAASF,IAC1B,GAISK,EAAsBR,UAC/B,IAEI,aADuBH,EAAcI,KAAK,uCAAwCC,IAClEC,IACpB,CAAE,MAAOC,GACL,OAAOA,EAAMC,SAASF,IAC1B,GAISM,EAA0BT,UACnC,IAEI,aADuBH,EAAca,IAAI,6CACzBP,IACpB,CAAE,MAAOC,GACL,OAAOA,EAAMC,SAASF,IAC1B,GAaSQ,EAAmBX,iBAAyB,IAAlBY,EAAOC,UAAAC,OAAA,QAAAC,IAAAF,UAAA,GAAAA,UAAA,GAAG,CAAC,EAC9C,IACI,MAAMG,EAAS,IAAIC,gBACfL,EAAQM,OAAOF,EAAOG,OAAO,QAASP,EAAQM,OAC9CN,EAAQQ,aAAaJ,EAAOG,OAAO,cAAeP,EAAQQ,aAC1DR,EAAQS,aAAaL,EAAOG,OAAO,cAAeP,EAAQS,aAC1DT,EAAQU,cAAcN,EAAOG,OAAO,eAAgBP,EAAQU,cAC5DV,EAAQW,iBAAiBP,EAAOG,OAAO,kBAAmBP,EAAQW,iBAGtE,aADuB1B,EAAca,IAAI,4BAADc,OAA6BR,EAAOS,cAC5DtB,IACpB,CAAE,MAAOC,GACL,OAAOA,EAAMC,SAASF,IAC1B,CACJ,EAEauB,EAAiB1B,eAAO2B,GAAyB,IAAjBC,EAAOf,UAAAC,OAAA,QAAAC,IAAAF,UAAA,GAAAA,UAAA,GAAG,EACnD,IAEI,aADuBhB,EAAca,IAAI,0BAADc,OAA2BG,EAAM,aAAAH,OAAYI,KACrEzB,IACpB,CAAE,MAAOC,GACL,OAAOA,EAAMC,SAASF,IAC1B,CACJ,C,2ICUA,QAlFA,SAAqB0B,GAAqD,IAApD,SAAEC,EAAQ,QAAEC,EAAO,WAAEC,EAAU,UAAEC,EAAY,IAAIJ,EACrE,MAAMK,GAAWC,EAAAA,EAAAA,MAQjB,OACEC,EAAAA,EAAAA,MAAA,OAAKC,UAAU,4DAA2DC,SAAA,EAExEC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,uDAAsDC,UACnEC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,8BAA6BC,UAC1CC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,cAAaC,UAC1BC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,gCAA+BC,SAAC,wBAKpDC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,+BAA8BC,UAE3CF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,mDAAkDC,SAAA,EAC/DF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,mBAAkBC,SAAA,EAC/BC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,qGAAoGC,SAC/GR,EAASU,QAEZD,EAAAA,EAAAA,KAAA,KAAGF,UAAU,oCAAmCC,SAAC,yCAGjDF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,wDAAuDC,SAAA,EACpEF,EAAAA,EAAAA,MAAA,KAAAE,SAAA,EAAGC,EAAAA,EAAAA,KAAA,UAAAD,SAAQ,yBAA6B,IAAEL,EAAUnB,WACpDsB,EAAAA,EAAAA,MAAA,KAAAE,SAAA,EAAGC,EAAAA,EAAAA,KAAA,UAAAD,SAAQ,aAAiB,IAAER,EAASW,cAK3CL,EAAAA,EAAAA,MAAA,OAAKC,UAAU,8BAA6BC,SAAA,EAC1CF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,mFAAkFC,SAAA,EAC/FF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,qCAAoCC,SAAA,CAAEI,KAAKC,MAAMb,EAASc,SAAW,IAAI,WACxFL,EAAAA,EAAAA,KAAA,OAAKF,UAAU,sCAAqCC,SAAC,iBAEvDF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,qFAAoFC,SAAA,EACjGC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,qCAAoCC,SAAER,EAASe,cAC9DN,EAAAA,EAAAA,KAAA,OAAKF,UAAU,uCAAsCC,SAAC,uBAK1DF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,4BAA2BC,SAAA,EACxCC,EAAAA,EAAAA,KAAA,UACEF,UAAU,6LACVS,QAASA,IAAMZ,EAAS,cAAcI,SACvC,YAGDC,EAAAA,EAAAA,KAAA,UACEF,UAAS,kFAAAb,OACPS,EAAUnB,OAAS,EACf,kHACA,gDAENgC,QAASA,KACHb,EAAUnB,OAAS,GACrBiC,QAAQC,IAAI,qBAAsBf,EAAUnB,OAAQ,aACpDkB,IACAD,EAAQ,cAERkB,MAAM,6CACR,EAEFC,SAA+B,IAArBjB,EAAUnB,OAAawB,SAEhCL,EAAUnB,OAAS,EAAI,aAAe,qCAOrD,E,4OCpEA,MAwaA,EAxawBe,IAAiC,IAAhC,OAAEsB,EAAM,UAAEd,EAAY,IAAIR,EACjD,MAAOuB,EAAeC,IAAoBC,EAAAA,EAAAA,WAAS,IAC5CC,EAAYC,IAAiBF,EAAAA,EAAAA,UAAS,GAyG7C,IAlGAG,EAAAA,EAAAA,YAAU,KACR,GAAU,OAANN,QAAM,IAANA,GAAAA,EAAQO,UAAW,CAErB,MAAMd,EAAW,IACXe,EAAQ,GACRC,EAAYT,EAAOO,UAAYC,EACrC,IAAIE,EAAU,EAEd,MAAMC,EAAQC,aAAY,KACxBF,GAAWD,EACPC,GAAWV,EAAOO,WACpBF,EAAcL,EAAOO,WACrBM,cAAcF,IAEdN,EAAcd,KAAKuB,MAAMJ,GAC3B,GACCjB,EAAWe,GAEd,MAAO,IAAMK,cAAcF,EAC7B,IACC,CAACX,KAGJM,EAAAA,EAAAA,YAAU,KACR,GAAU,OAANN,QAAM,IAANA,GAAAA,EAAQe,QAAS,CACnB,MAAMC,EAAmBA,KACvB,IACE,MAAMC,EAAe,IAAIC,OAAOC,aAGLC,MAEV,CACb,CAAC,IAAK,IAAK,KACX,CAAC,IAAK,IAAK,KACX,CAAC,IAAK,IAAK,KACX,CAAC,IAAK,IAAK,MAGNC,SAAQ,CAACC,EAAOC,KACrBC,YAAW,KACTF,EAAMD,SAASI,IACb,MAAMC,EAAaT,EAAaU,mBAC1BC,EAAWX,EAAaY,aAE9BH,EAAWI,QAAQF,GACnBA,EAASE,QAAQb,EAAac,aAE9BL,EAAWD,UAAUO,eAAeP,EAAWR,EAAagB,aAC5DP,EAAWQ,KAAO,WAElBN,EAASO,KAAKH,eAAe,EAAGf,EAAagB,aAC7CL,EAASO,KAAKC,wBAAwB,IAAMnB,EAAagB,YAAc,KACvEL,EAASO,KAAKE,6BAA6B,KAAOpB,EAAagB,YAAc,IAE7EP,EAAWY,MAAMrB,EAAagB,aAC9BP,EAAWa,KAAKtB,EAAagB,YAAc,GAAI,GAC/C,GACO,IAARV,EAAY,IAIjBC,YAAW,KACT,CAAC,IAAK,IAAK,IAAK,MAAMH,SAAQ,CAACmB,EAAMC,KACnCjB,YAAW,KACT,MAAMkB,EAAMzB,EAAaU,mBACnBQ,EAAOlB,EAAaY,aAE1Ba,EAAIZ,QAAQK,GACZA,EAAKL,QAAQb,EAAac,aAE1BW,EAAIjB,UAAUO,eAAeQ,EAAMvB,EAAagB,aAChDS,EAAIR,KAAO,OAEXC,EAAKA,KAAKH,eAAe,EAAGf,EAAagB,aACzCE,EAAKA,KAAKC,wBAAwB,GAAKnB,EAAagB,YAAc,KAClEE,EAAKA,KAAKE,6BAA6B,KAAOpB,EAAagB,YAAc,IAEzES,EAAIJ,MAAMrB,EAAagB,aACvBS,EAAIH,KAAKtB,EAAagB,YAAc,GAAI,GACnC,IAAJQ,EAAQ,GACX,GACD,IAAI,EAGTrB,GACAxB,QAAQC,IAAI,sCAEd,CAAE,MAAO5C,GACP2C,QAAQC,IAAI,gCAAiC5C,EAC/C,GAIFuE,WAAWR,EAAkB,IAC/B,IACC,CAAO,OAANhB,QAAM,IAANA,OAAM,EAANA,EAAQe,WAEPf,EACH,OAAO,KAGT,MAAM,UACJO,EAAY,EAAC,YACboC,EAAc,CAAC,EAAC,QAChB5B,GAAU,EAAK,SACf6B,EAAW,EAAC,WACZC,EAAa,EAAC,cACdC,EAAgB,EAAC,aACjBC,EAAe,IACb/C,EAEJ,OACEf,EAAAA,EAAAA,MAAA,OAAKC,UAAS,sGAAAb,OAAwGa,GAAYC,SAAA,EAEhIC,EAAAA,EAAAA,KAAA,SAAO4D,KAAG,EAAA7D,SAAA,mRAOVC,EAAAA,EAAAA,KAAC6D,EAAAA,EAAe,CAAA9D,SACb4B,IACC9B,EAAAA,EAAAA,MAACiE,EAAAA,EAAOC,IAAG,CACTC,QAAS,CAAEC,QAAS,EAAGC,MAAO,GAAKC,GAAI,GAAIC,SAAU,KACrDC,QAAS,CACPJ,QAAS,EACTC,MAAO,CAAC,GAAK,IAAK,GAClBC,EAAG,EACHC,QAAS,EACTE,WAAY,CACVjE,SAAU,IACVkE,KAAM,UACNL,MAAO,CACLM,MAAO,CAAC,EAAG,GAAK,GAChBnE,SAAU,OAIhBoE,KAAM,CAAER,QAAS,EAAGC,MAAO,GAAKC,GAAI,IACpCrE,UAAU,6IACV4E,MAAO,CACLC,WAAY,6DACZC,UAAW,gFACXC,UAAW,qCACX9E,SAAA,EAGFC,EAAAA,EAAAA,KAAC8D,EAAAA,EAAOC,IAAG,CACTjE,UAAU,yFACVuE,QAAS,CACPS,EAAG,CAAC,QAAS,QACbR,WAAY,CACVjE,SAAU,EACV0E,OAAQC,IACRT,KAAM,aAMX,IAAIU,MAAM,IAAIC,KAAI,CAACC,EAAG9B,KACrBrD,EAAAA,EAAAA,KAAC8D,EAAAA,EAAOC,IAAG,CAETjE,UAAU,oDACV4E,MAAO,CACLU,KAAK,GAADnG,OAAK,GAAS,GAAJoE,EAAM,KACpBgC,IAAI,GAADpG,OAAK,GAAMoE,EAAI,EAAK,GAAE,MAE3BgB,QAAS,CACPF,EAAG,EAAE,IAAK,IAAK,IACfF,QAAS,CAAC,GAAK,GAAK,IACpBC,MAAO,CAAC,EAAG,IAAK,GAChBI,WAAY,CACVjE,SAAU,EAAQ,GAAJgD,EACd0B,OAAQC,IACRT,KAAM,eAbLlB,MAmBTxD,EAAAA,EAAAA,MAACiE,EAAAA,EAAOC,IAAG,CACTjE,UAAU,gBACVuE,QAAS,CACPH,MAAO,CAAC,EAAG,KAAM,GACjBI,WAAY,CACVjE,SAAU,IACV0E,OAAQC,IACRT,KAAM,cAERxE,SAAA,EAEFF,EAAAA,EAAAA,MAACiE,EAAAA,EAAOC,IAAG,CACTjE,UAAU,wCACVuE,QAAS,CACPD,QAAS,CAAC,EAAG,KACbE,WAAY,CACVjE,SAAU,EACV0E,OAAQC,IACRT,KAAM,WAERxE,SAAA,EAEFC,EAAAA,EAAAA,KAACsF,EAAAA,IAAQ,CAACxF,UAAU,mCACpBE,EAAAA,EAAAA,KAAC8D,EAAAA,EAAOyB,KAAI,CACVzF,UAAU,qCACV4E,MAAO,CACLc,WAAY,8BACZb,WAAY,wCACZc,qBAAsB,OACtBC,oBAAqB,eAEvBrB,QAAS,CACPH,MAAO,CAAC,EAAG,IAAK,GAChBI,WAAY,CACVjE,SAAU,EACV0E,OAAQC,IACRT,KAAM,cAERxE,SACH,kBAKHF,EAAAA,EAAAA,MAACiE,EAAAA,EAAO6B,EAAC,CACP7F,UAAU,oBACV4E,MAAO,CAAEc,WAAY,+BACrBxB,QAAS,CAAEC,QAAS,EAAGE,EAAG,IAC1BE,QAAS,CACPJ,QAAS,EACTE,EAAG,EACHG,WAAY,CAAEsB,MAAO,GAAKvF,SAAU,KACpCN,SAAA,CACH,kCACuByD,EAAS,qBAGjCxD,EAAAA,EAAAA,KAAC8D,EAAAA,EAAOC,IAAG,CACTjE,UAAU,wCACVkE,QAAS,CAAEC,QAAS,GACpBI,QAAS,CACPJ,QAAS,EACTK,WAAY,CAAEsB,MAAO,EAAGvF,SAAU,KAClCN,SACH,oDASTF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,mBAAkBC,SAAA,EAC/BF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,wCAAuCC,SAAA,EACpDC,EAAAA,EAAAA,KAAC6F,EAAAA,IAAM,CAAC/F,UAAU,kCAClBE,EAAAA,EAAAA,KAAA,MAAIF,UAAU,mCAAkCC,SAAC,kBAGnDF,EAAAA,EAAAA,MAACiE,EAAAA,EAAOC,IAAG,CACTC,QAAS,CAAEE,MAAO,GAClBG,QAAS,CAAEH,MAAO,GAClBI,WAAY,CAAExB,KAAM,SAAUgD,UAAW,IAAKC,QAAS,IACvDjG,UAAU,0CAAyCC,SAAA,CACpD,IACGiB,MAGJnB,EAAAA,EAAAA,MAAA,KAAGC,UAAU,gBAAeC,SAAA,CAAC,aAChB0D,EAAWuC,iBAAiB,YAAUxC,SAKrD3D,EAAAA,EAAAA,MAAA,OAAKC,UAAU,8BAA6BC,SAAA,CACzC2D,EAAgB,IACf7D,EAAAA,EAAAA,MAAA,OAAKC,UAAU,gDAA+CC,SAAA,EAC5DC,EAAAA,EAAAA,KAACiG,EAAAA,IAAO,CAACnG,UAAU,0CACnBE,EAAAA,EAAAA,KAAA,OAAKF,UAAU,kCAAiCC,SAAE2D,KAClD1D,EAAAA,EAAAA,KAAA,OAAKF,UAAU,wBAAuBC,SAAC,cAI1C4D,EAAapF,OAAS,IACrBsB,EAAAA,EAAAA,MAAA,OAAKC,UAAU,gDAA+CC,SAAA,EAC5DC,EAAAA,EAAAA,KAACkG,EAAAA,IAAO,CAACpG,UAAU,0CACnBE,EAAAA,EAAAA,KAAA,OAAKF,UAAU,kCAAiCC,SAAE4D,EAAapF,UAC/DyB,EAAAA,EAAAA,KAAA,OAAKF,UAAU,wBAAuBC,SAAC,qBAM5CoG,OAAOC,KAAK7C,GAAahF,OAAS,IACjCsB,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEF,EAAAA,EAAAA,MAAA,UACEU,QAASA,IAAMO,GAAkBD,GACjCf,UAAU,4HAA2HC,SAAA,EAErIC,EAAAA,EAAAA,KAAA,QAAMF,UAAU,4BAA2BC,SAAC,iBAC3Cc,GACCb,EAAAA,EAAAA,KAACqG,EAAAA,IAAW,CAACvG,UAAU,2BAEvBE,EAAAA,EAAAA,KAACsG,EAAAA,IAAa,CAACxG,UAAU,8BAI7BE,EAAAA,EAAAA,KAAC6D,EAAAA,EAAe,CAAA9D,SACbc,IACCb,EAAAA,EAAAA,KAAC8D,EAAAA,EAAOC,IAAG,CACTC,QAAS,CAAEC,QAAS,EAAGsC,OAAQ,GAC/BlC,QAAS,CAAEJ,QAAS,EAAGsC,OAAQ,QAC/B9B,KAAM,CAAER,QAAS,EAAGsC,OAAQ,GAC5BzG,UAAU,yCAAwCC,UAElDF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,YAAWC,SAAA,CACvBwD,EAAYiD,SACX3G,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oCAAmCC,SAAA,EAChDF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oBAAmBC,SAAA,EAChCC,EAAAA,EAAAA,KAACyG,EAAAA,IAAQ,CAAC3G,UAAU,gCACpBE,EAAAA,EAAAA,KAAA,QAAMF,UAAU,wBAAuBC,SAAC,gBAE1CF,EAAAA,EAAAA,MAAA,QAAMC,UAAU,4BAA2BC,SAAA,CAAC,IAAEwD,EAAYiD,aAI7DjD,EAAYmD,gBAAkB,IAC7B7G,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oCAAmCC,SAAA,EAChDF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oBAAmBC,SAAA,EAChCC,EAAAA,EAAAA,KAAC2G,EAAAA,IAAY,CAAC7G,UAAU,iCACxBE,EAAAA,EAAAA,KAAA,QAAMF,UAAU,wBAAuBC,SAAC,yBAE1CF,EAAAA,EAAAA,MAAA,QAAMC,UAAU,6BAA4BC,SAAA,CAAC,IAAEwD,EAAYmD,sBAI9DnD,EAAYqD,WAAa,IACxB/G,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oCAAmCC,SAAA,EAChDF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oBAAmBC,SAAA,EAChCC,EAAAA,EAAAA,KAAC6G,EAAAA,IAAO,CAAC/G,UAAU,kCACnBE,EAAAA,EAAAA,KAAA,QAAMF,UAAU,wBAAuBC,SAAC,oBAE1CF,EAAAA,EAAAA,MAAA,QAAMC,UAAU,8BAA6BC,SAAA,CAAC,IAAEwD,EAAYqD,iBAI/DrD,EAAYuD,kBAAoB,IAC/BjH,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oCAAmCC,SAAA,EAChDF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oBAAmBC,SAAA,EAChCC,EAAAA,EAAAA,KAAC+G,EAAAA,IAAM,CAACjH,UAAU,kCAClBE,EAAAA,EAAAA,KAAA,QAAMF,UAAU,wBAAuBC,SAAC,sBAE1CF,EAAAA,EAAAA,MAAA,QAAMC,UAAU,8BAA6BC,SAAA,CAAC,IAAEwD,EAAYuD,wBAI/DvD,EAAYyD,YAAc,IACzBnH,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oCAAmCC,SAAA,EAChDF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oBAAmBC,SAAA,EAChCC,EAAAA,EAAAA,KAACiG,EAAAA,IAAO,CAACnG,UAAU,kCACnBE,EAAAA,EAAAA,KAAA,QAAMF,UAAU,wBAAuBC,SAAC,qBAE1CF,EAAAA,EAAAA,MAAA,QAAMC,UAAU,8BAA6BC,SAAA,CAAC,IAAEwD,EAAYyD,kBAI/DzD,EAAY0D,kBAAoB,IAC/BpH,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oCAAmCC,SAAA,EAChDF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oBAAmBC,SAAA,EAChCC,EAAAA,EAAAA,KAACkG,EAAAA,IAAO,CAACpG,UAAU,kCACnBE,EAAAA,EAAAA,KAAA,QAAMF,UAAU,wBAAuBC,SAAC,sBAE1CF,EAAAA,EAAAA,MAAA,QAAMC,UAAU,8BAA6BC,SAAA,CAAC,IAAEwD,EAAY0D,kCAW3EtD,EAAapF,OAAS,IACrBsB,EAAAA,EAAAA,MAAA,OAAKC,UAAU,OAAMC,SAAA,EACnBF,EAAAA,EAAAA,MAAA,MAAIC,UAAU,yDAAwDC,SAAA,EACpEC,EAAAA,EAAAA,KAACkG,EAAAA,IAAO,CAACpG,UAAU,iCAAiC,uBAGtDE,EAAAA,EAAAA,KAAA,OAAKF,UAAU,YAAWC,SACvB4D,EAAauB,KAAI,CAACgC,EAAa/E,KAC9BtC,EAAAA,EAAAA,MAACiE,EAAAA,EAAOC,IAAG,CAETC,QAAS,CAAEC,QAAS,EAAGa,GAAI,IAC3BT,QAAS,CAAEJ,QAAS,EAAGa,EAAG,GAC1BR,WAAY,CAAEsB,MAAe,GAARzD,GACrBrC,UAAU,yGAAwGC,SAAA,EAElHC,EAAAA,EAAAA,KAACsF,EAAAA,IAAQ,CAACxF,UAAU,kCACpBD,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,4BAA2BC,SAAEoH,OAAOD,EAAYjH,MAAQ,kBACvED,EAAAA,EAAAA,KAAA,OAAKF,UAAU,wBAAuBC,SAAEoH,OAAOD,EAAYE,aAAe,gCATvEjF,YAgBX,EChaJkF,EAAsB/H,IAA2H,IAA1H,SAAEgI,EAAQ,cAAEC,EAAa,eAAEC,EAAc,eAAEC,EAAc,eAAEC,EAAc,OAAEC,EAAM,WAAEC,EAAU,SAAEC,EAAQ,UAAEC,GAAWxI,EAE/I,IAAKgI,EACH,OAAOtH,EAAAA,EAAAA,KAAA,OAAAD,SAAK,wBAId,MAAMgI,EAAeT,EAASrH,KAAOkH,OAAOG,EAASrH,MAAQ,8BACvD+H,EAAaV,EAASU,WAAab,OAAOG,EAASU,YAAc,UAGvE,IAAI3J,EAAU,GACViJ,EAASjJ,UACP4G,MAAMgD,QAAQX,EAASjJ,SACzBA,EAAUiJ,EAASjJ,QAAQ6G,KAAIgD,GAAOf,OAAOe,GAAO,MACf,kBAArBZ,EAASjJ,UACzBA,EAAU8H,OAAOgC,OAAOb,EAASjJ,SAAS6G,KAAIgD,GAAOf,OAAOe,GAAO,QAUvE,OACErI,EAAAA,EAAAA,MAAA,OAAK6E,MAAO,CAAE0D,UAAW,QAASzD,WAAY,qDAAsD5E,SAAA,EAElGC,EAAAA,EAAAA,KAAA,OAAK0E,MAAO,CACVC,WAAY,QACZ0D,aAAc,oBACdC,QAAS,OACTC,SAAU,SACVlD,IAAK,EACLmD,OAAQ,IACRzI,UACAF,EAAAA,EAAAA,MAAA,OAAK6E,MAAO,CAAE+D,SAAU,SAAUC,OAAQ,SAAUC,QAAS,OAAQC,eAAgB,gBAAiBC,WAAY,UAAW9I,SAAA,EAC3HF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,MAAI0E,MAAO,CAAEoE,SAAU,OAAQC,WAAY,MAAOC,MAAO,UAAWN,OAAQ,GAAI3I,SAC7E+H,EAAYX,OAAOW,GAAa,UAEnCjI,EAAAA,EAAAA,MAAA,KAAG6E,MAAO,CAAEoE,SAAU,OAAQE,MAAO,UAAWN,OAAQ,GAAI3I,SAAA,CAAC,YACjDwH,EAAgB,EAAE,OAAKC,SAIrC3H,EAAAA,EAAAA,MAAA,OAAK6E,MAAO,CACVC,WAAYkD,GAAY,GAAK,UAAY,UACzCmB,MAAO,QACPV,QAAS,YACTW,aAAc,OACdC,WAAY,YACZH,WAAY,QACZhJ,SAAA,CAAC,SAlCSoJ,KAClB,MACMC,EAAOD,EAAU,GACvB,OAFahJ,KAAKuB,MAAMyH,EAAU,IAEpB,KAAOC,EAAO,GAAK,IAAM,IAAMA,CAAI,EAgClCC,CAAWxB,YAMxBhI,EAAAA,EAAAA,MAAA,OAAK6E,MAAO,CAAE+D,SAAU,QAASC,OAAQ,SAAUJ,QAAS,aAAcvI,SAAA,EACxEF,EAAAA,EAAAA,MAAA,OAAK6E,MAAO,CACVC,WAAY,QACZsE,aAAc,OACdrE,UAAW,wCACX0D,QAAS,OACTgB,aAAc,QACdvJ,SAAA,EAEAC,EAAAA,EAAAA,KAAA,OAAK0E,MAAO,CAAE4E,aAAc,QAASvJ,UACnCF,EAAAA,EAAAA,MAAA,QAAM6E,MAAO,CACXC,WAAY,UACZqE,MAAO,UACPV,QAAS,WACTW,aAAc,OACdH,SAAU,OACVC,WAAY,OACZhJ,SAAA,CAAC,YACSwH,EAAgB,EAAE,OAAKC,QAKrCxH,EAAAA,EAAAA,KAAA,OAAK0E,MAAO,CAAE4E,aAAc,QAASvJ,UACnCC,EAAAA,EAAAA,KAAA,MAAI0E,MAAO,CACToE,SAAU,OACVC,WAAY,MACZC,MAAO,UACPO,WAAY,MACZb,OAAQ,GACR3I,SACCgI,OAKHT,EAASkC,OAASlC,EAASmC,YAC3BzJ,EAAAA,EAAAA,KAAA,OAAK0E,MAAO,CAAE4E,aAAc,OAAQI,UAAW,UAAW3J,UACxDC,EAAAA,EAAAA,KAAA,OACE2J,IAAKrC,EAASkC,OAASlC,EAASmC,SAChCG,IAAI,WACJlF,MAAO,CACL+D,SAAU,OACVoB,UAAW,QACXC,UAAW,UACXb,aAAc,OACdc,OAAQ,0BAOhB/J,EAAAA,EAAAA,KAAA,OAAK0E,MAAO,CAAEiE,QAAS,OAAQqB,cAAe,SAAUC,IAAK,QAASlK,SACpD,YAAfiI,GAA4B3J,EAAQE,OAAS,EAC5CF,EAAQ6G,KAAI,CAACgF,EAAQ/H,KACnB,MAAMgI,EAAShD,OAAOiD,aAAa,GAAKjI,GAClCkI,EAAa5C,IAAmBtF,EAEtC,OACEtC,EAAAA,EAAAA,MAAA,UAEEU,QAASA,IAAMmH,EAAevF,GAC9BuC,MAAO,CACL4F,MAAO,OACPZ,UAAW,OACXpB,QAAS,OACTW,aAAc,OACdc,OAAQM,EAAa,oBAAsB,oBAC3C1F,WAAY0F,EAAa,UAAY,QACrCrB,MAAOqB,EAAa,UAAY,UAChCE,OAAQ,UACRjG,WAAY,WACZqE,QAAS,OACTE,WAAY,SACZoB,IAAK,QACLlK,SAAA,EAEFC,EAAAA,EAAAA,KAAA,OAAK0E,MAAO,CACV4F,MAAO,OACP/D,OAAQ,OACR0C,aAAc,MACdtE,WAAY0F,EAAa,UAAY,UACrCrB,MAAOqB,EAAa,QAAU,UAC9B1B,QAAS,OACTE,WAAY,SACZD,eAAgB,SAChBG,WAAY,OACZD,SAAU,QACV/I,SACCoK,KAEHnK,EAAAA,EAAAA,KAAA,QAAM0E,MAAO,CAAE8F,KAAM,EAAGzB,WAAY,OAAQhJ,SAAEmK,IAC7CG,IACCrK,EAAAA,EAAAA,KAAA,OAAK0E,MAAO,CACV4F,MAAO,OACP/D,OAAQ,OACR0C,aAAc,MACdtE,WAAY,UACZqE,MAAO,QACPL,QAAS,OACTE,WAAY,SACZD,eAAgB,UAChB7I,SAAC,aA1CAoC,EA8CE,KAIbtC,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAO0E,MAAO,CACZiE,QAAS,QACTG,SAAU,OACVC,WAAY,MACZC,MAAO,UACPM,aAAc,OACdvJ,SAAC,kBAGHC,EAAAA,EAAAA,KAAA,SACE8C,KAAK,OACL2H,MAAOhD,GAAkB,GACzBiD,SAAWC,GAAMjD,EAAeiD,EAAEC,OAAOH,OACzCI,YAAY,2BACZnG,MAAO,CACL4F,MAAO,OACPhC,QAAS,OACTyB,OAAQ,oBACRd,aAAc,OACdH,SAAU,OACVgC,QAAS,mBASrBjL,EAAAA,EAAAA,MAAA,OAAK6E,MAAO,CAAEiE,QAAS,OAAQC,eAAgB,gBAAiBC,WAAY,UAAW9I,SAAA,EACrFC,EAAAA,EAAAA,KAAA,UACEO,QAASqH,EACTjH,SAA4B,IAAlB4G,EACV7C,MAAO,CACL4D,QAAS,YACTW,aAAc,OACdF,WAAY,MACZgB,OAAQ,OACRQ,OAA0B,IAAlBhD,EAAsB,cAAgB,UAC9C5C,WAA8B,IAAlB4C,EAAsB,UAAY,UAC9CyB,MAAyB,IAAlBzB,EAAsB,UAAY,SACzCxH,SACH,qBAIDF,EAAAA,EAAAA,MAAA,OAAK6E,MAAO,CAAEgF,UAAW,UAAW3J,SAAA,EAClCC,EAAAA,EAAAA,KAAA,OAAK0E,MAAO,CAAEoE,SAAU,OAAQE,MAAO,UAAWM,aAAc,OAAQvJ,SAAC,cACzEC,EAAAA,EAAAA,KAAA,OAAK0E,MAAO,CACV4F,MAAO,QACP/D,OAAQ,MACR5B,WAAY,UACZsE,aAAc,MACd8B,SAAU,UACVhL,UACAC,EAAAA,EAAAA,KAAA,OACE0E,MAAO,CACL6B,OAAQ,OACR5B,WAAY,UACZsE,aAAc,MACdqB,OAAS/C,EAAgB,GAAKC,EAAkB,IAAM,IACtDlD,WAAY,sBAMpBtE,EAAAA,EAAAA,KAAA,UACEO,QAASoH,EACTjD,MAAO,CACL4D,QAAS,YACTW,aAAc,OACdF,WAAY,MACZgB,OAAQ,OACRQ,OAAQ,UACR5F,WAAY,UACZqE,MAAO,SACPjJ,SAEDwH,IAAkBC,EAAiB,EAAI,cAAgB,wBAI1D,EAKJwD,EAAwBC,IAavB,IAbwB,UAC7BvL,EAAS,gBACTwL,EAAe,aACfC,EAAY,iBACZC,EAAgB,QAChB5L,EAAO,SACPD,EAAQ,yBACR8L,EAAwB,mBACxBC,EAAkB,UAClBC,EAAS,UACTC,EAAS,eACTC,EAAc,gBACdC,GACDT,EACC,OAAKvL,GAAcuF,MAAMgD,QAAQvI,IAK/BM,EAAAA,EAAAA,KAAA,OAAK0E,MAAO,CAAE0D,UAAW,QAASzD,WAAY,oDAAqD2D,QAAS,QAASvI,UACnHF,EAAAA,EAAAA,MAAA,OAAK6E,MAAO,CAAE+D,SAAU,QAASC,OAAQ,UAAW3I,SAAA,EAElDC,EAAAA,EAAAA,KAAA,OAAK0E,MAAO,CAAEgF,UAAW,SAAUJ,aAAc,QAASvJ,UACxDF,EAAAA,EAAAA,MAAA,OAAK6E,MAAO,CACVC,WAAY,QACZsE,aAAc,OACdX,QAAS,OACT1D,UAAW,sCACXmF,OAAQ,qBACRhK,SAAA,EACAC,EAAAA,EAAAA,KAAA,MAAI0E,MAAO,CAAEoE,SAAU,OAAQC,WAAY,OAAQC,MAAO,UAAWN,OAAQ,aAAc3I,SAAC,mBAG5FC,EAAAA,EAAAA,KAAA,KAAG0E,MAAO,CAAEsE,MAAO,UAAWN,OAAQ,GAAI3I,SAAC,mDAK/CC,EAAAA,EAAAA,KAAA,OAAK0E,MAAO,CAAEiE,QAAS,OAAQqB,cAAe,SAAUC,IAAK,OAAQX,aAAc,QAASvJ,SACzFL,EAAUwF,KAAI,CAACoC,EAAUnF,KACxB,IAAKmF,EAAU,OAAO,KAEtB,MAAMS,EAAeT,EAASrH,KAAOkH,OAAOG,EAASrH,MAAQ,yBACvD+H,EAAaV,EAASU,WAAab,OAAOG,EAASU,YAAc,UACjE2D,EAAaT,EAAgB/I,GAEnC,IAAIyJ,GAAY,EACZC,EAAoB,UACpBC,EAAiB,eAErB,GAAmB,YAAf9D,EAA0B,CAG5B,GAFA4D,EAAYtE,EAASyE,gBAAkBJ,EAEnCrE,EAASjJ,cAAsCG,IAA3B8I,EAASyE,cAA6B,CAC5D,MAAMC,EAAa1E,EAASjJ,QAAQiJ,EAASyE,eAC7CF,EAAoBG,EAAa7E,OAAO6E,GAAc,SACxD,CAEA,GAAI1E,EAASjJ,cAA0BG,IAAfmN,EAA0B,CAChD,MAAMM,EAAU3E,EAASjJ,QAAQsN,GACjCG,EAAiBG,EAAU9E,OAAO8E,GAAW,cAC/C,CACF,MACEL,EAAYtE,EAAS4E,gBAAkBP,EACvCE,EAAoBvE,EAAS4E,cAAgB/E,OAAOG,EAAS4E,eAAiB,UAC9EJ,EAAiBH,EAAaxE,OAAOwE,GAAc,eAGrD,OACE9L,EAAAA,EAAAA,MAAA,OAEE6E,MAAO,CACLuE,aAAc,OACdrE,UAAW,oCACXmF,OAAQ,cAAgB6B,EAAY,UAAY,WAChDtD,QAAS,OACT3D,WAAYiH,EAAY,UAAY,WACpC7L,SAAA,EAGFC,EAAAA,EAAAA,KAAA,OAAK0E,MAAO,CAAE4E,aAAc,QAASvJ,UACnCF,EAAAA,EAAAA,MAAA,OAAK6E,MAAO,CAAEiE,QAAS,OAAQE,WAAY,aAAcoB,IAAK,QAASlK,SAAA,EACrEC,EAAAA,EAAAA,KAAA,OAAK0E,MAAO,CACV4F,MAAO,OACP/D,OAAQ,OACR0C,aAAc,MACdtE,WAAY,UACZqE,MAAO,QACPL,QAAS,OACTE,WAAY,SACZD,eAAgB,SAChBG,WAAY,OACZD,SAAU,OACVqD,WAAY,EACZC,UAAW,OACXrM,SACCoC,EAAQ,KAEXnC,EAAAA,EAAAA,KAAA,OAAK0E,MAAO,CAAE8F,KAAM,GAAIzK,UACtBC,EAAAA,EAAAA,KAAA,KAAG0E,MAAO,CACRsE,MAAO,UACPD,WAAY,MACZQ,WAAY,MACZb,OAAQ,GACR3I,SACCgI,YAOTlI,EAAAA,EAAAA,MAAA,OAAK6E,MAAO,CAAE4E,aAAc,OAAQvJ,SAAA,EAClCC,EAAAA,EAAAA,KAAA,QAAM0E,MAAO,CAAEoE,SAAU,OAAQC,WAAY,MAAOC,MAAO,WAAYjJ,SAAC,mBACxEC,EAAAA,EAAAA,KAAA,QAAM0E,MAAO,CACXqE,WAAY,MACZC,MAAO4C,EAAY,UAAY,WAC/B7L,SACC+L,KAEH9L,EAAAA,EAAAA,KAAA,QAAM0E,MAAO,CACX2H,WAAY,OACZvD,SAAU,OACVE,MAAO4C,EAAY,UAAY,WAC/B7L,SACC6L,EAAY,SAAM,eAKrBA,IACA/L,EAAAA,EAAAA,MAAA,OAAK6E,MAAO,CAAE4E,aAAc,OAAQvJ,SAAA,EAClCC,EAAAA,EAAAA,KAAA,QAAM0E,MAAO,CAAEoE,SAAU,OAAQC,WAAY,MAAOC,MAAO,WAAYjJ,SAAC,sBACxEC,EAAAA,EAAAA,KAAA,QAAM0E,MAAO,CAAEqE,WAAY,MAAOC,MAAO,WAAYjJ,SAAE8L,KACvD7L,EAAAA,EAAAA,KAAA,QAAM0E,MAAO,CAAE2H,WAAY,OAAQvD,SAAU,OAAQE,MAAO,WAAYjJ,SAAC,eAK3E6L,IACA5L,EAAAA,EAAAA,KAAA,OAAK0E,MAAO,CAAE0H,UAAW,OAAQrM,UAC/BF,EAAAA,EAAAA,MAAA,UACEU,QAASA,KACP6K,EACErD,EACA8D,EACAC,EACAxE,EAASkC,OAASlC,EAASmC,UAAY,GACxC,EAEH/E,MAAO,CACL4D,QAAS,WACT3D,WAAY,UACZqE,MAAO,QACPe,OAAQ,OACRd,aAAc,MACdH,SAAU,OACVC,WAAY,MACZwB,OAAQ,UACR5B,QAAS,OACTE,WAAY,SACZoB,IAAK,OACLlK,SAAA,EAEFC,EAAAA,EAAAA,KAAA,QAAAD,SAAM,kBACNC,EAAAA,EAAAA,KAAA,QAAAD,SAAM,yBAMXoL,EAAapD,KACZlI,EAAAA,EAAAA,MAAA,OAAK6E,MAAO,CACV0H,UAAW,OACX9D,QAAS,OACT3D,WAAY,QACZsE,aAAc,MACdqD,WAAY,oBACZ1H,UAAW,iCACXmF,OAAQ,qBACRhK,SAAA,EACAF,EAAAA,EAAAA,MAAA,OAAK6E,MAAO,CAAEiE,QAAS,OAAQE,WAAY,SAAUS,aAAc,OAAQvJ,SAAA,EACzEC,EAAAA,EAAAA,KAAA,QAAM0E,MAAO,CAAEoE,SAAU,OAAQyD,YAAa,OAAQxM,SAAC,kBACvDC,EAAAA,EAAAA,KAAA,MAAI0E,MAAO,CAAEqE,WAAY,OAAQC,MAAO,UAAWF,SAAU,OAAQJ,OAAQ,GAAI3I,SAAC,oBAMlFuH,EAASkC,OAASlC,EAASmC,YAC3B5J,EAAAA,EAAAA,MAAA,OAAK6E,MAAO,CACV4E,aAAc,OACdhB,QAAS,MACT3D,WAAY,UACZsE,aAAc,MACdc,OAAQ,qBACRhK,SAAA,EACAC,EAAAA,EAAAA,KAAA,OAAK0E,MAAO,CAAE4E,aAAc,OAAQvJ,UAClCC,EAAAA,EAAAA,KAAA,QAAM0E,MAAO,CAAEsE,MAAO,UAAWF,SAAU,OAAQC,WAAY,OAAQhJ,SAAC,uCAI1EC,EAAAA,EAAAA,KAAA,OAAK0E,MAAO,CAAEgF,UAAW,UAAW3J,UAClCC,EAAAA,EAAAA,KAAA,OACE2J,IAAKrC,EAASkC,OAASlC,EAASmC,SAChCG,IAAI,mBACJlF,MAAO,CACL+D,SAAU,OACVoB,UAAW,QACXC,UAAW,UACXb,aAAc,MACdc,OAAQ,6BAOlB/J,EAAAA,EAAAA,KAAA,OAAK0E,MAAO,CACVoE,SAAU,OACVE,MAAO,UACPO,WAAY,MACZ5E,WAAY,UACZ2D,QAAS,MACTW,aAAc,OACdlJ,SACCoL,EAAapD,GAAgBZ,OAAOgE,EAAapD,IAAiB,UA5JpET,EAASpH,KAAOiC,EAgKjB,OAMZtC,EAAAA,EAAAA,MAAA,OAAK6E,MAAO,CACViE,QAAS,OACTqB,cAAelI,OAAO0K,WAAa,IAAM,SAAW,MACpDvC,IAAK,OACLrB,eAAgB,SAChBC,WAAY,UACZ9I,SAAA,EACAC,EAAAA,EAAAA,KAAA,UACEO,QAASA,IAAMf,EAAQ,UACvBkF,MAAO,CACL4D,QAAS,YACT3D,WAAY,UACZqE,MAAO,QACPe,OAAQ,OACRd,aAAc,OACdF,WAAY,OACZwB,OAAQ,UACR3F,UAAW,qCACX7E,SACH,4BAIDC,EAAAA,EAAAA,KAAA,UACEO,QAASA,KACPf,EAAQ,gBACR6L,EAAyB,GACzBC,EAAmB,CAAC,GACpBC,EAAU,CAAC,GACXC,GAAU,GACVC,GAAuB,OAARlM,QAAQ,IAARA,OAAQ,EAARA,EAAUc,WAAY,GACrCqL,EAAgB,CAAC,EAAE,EAErBhH,MAAO,CACL4D,QAAS,YACT3D,WAAY,UACZqE,MAAO,QACPe,OAAQ,OACRd,aAAc,OACdF,WAAY,OACZwB,OAAQ,UACR3F,UAAW,qCACX7E,SACH,sCAxQAC,EAAAA,EAAAA,KAAA,OAAAD,SAAK,0BA6QN,EAulBV,QAnlBA,WAAsB,IAAD0M,EAAAC,EACnB,MAAOnN,EAAUoN,IAAe5L,EAAAA,EAAAA,UAAS,OAClCrB,EAAWkN,IAAgB7L,EAAAA,EAAAA,UAAS,KACpC8L,EAAuBxB,IAA4BtK,EAAAA,EAAAA,UAAS,IAC5DmK,EAAiBI,IAAsBvK,EAAAA,EAAAA,UAAS,CAAC,IACjD+L,EAAQvB,IAAaxK,EAAAA,EAAAA,UAAS,CAAC,GAChCtC,GAASsO,EAAAA,EAAAA,MACTC,GAAWC,EAAAA,EAAAA,MACXtN,GAAWC,EAAAA,EAAAA,OACVsN,EAAM1N,IAAWuB,EAAAA,EAAAA,UAAS,iBAC1BoM,EAAa1B,IAAkB1K,EAAAA,EAAAA,UAAS,IACxCqM,EAAQ5B,IAAazK,EAAAA,EAAAA,WAAS,IAC9BsM,EAAYC,IAAiBvM,EAAAA,EAAAA,UAAS,OACtCwM,EAAWC,IAAgBzM,EAAAA,EAAAA,WAAS,IACpC0M,EAAWC,IAAgB3M,EAAAA,EAAAA,UAAS,OACrC,KAAE4M,IAASC,EAAAA,EAAAA,KAAaC,GAAUA,EAAMF,QAExC,MAAErD,EAAK,OAAE/D,IAAWuH,EAAAA,EAAAA,MACnB3C,EAAcO,IAAmB3K,EAAAA,EAAAA,UAAS,CAAC,GAE5CgN,GAAcC,EAAAA,EAAAA,cAAYvQ,UAC9B,IACE+P,GAAa,GACbR,GAASiB,EAAAA,EAAAA,OACTzN,QAAQC,IAAI,6BAA8BhC,EAAOyP,IAEjD,MAAMpQ,QAAiBqQ,EAAAA,EAAAA,IAAY,CAAEC,OAAQ3P,EAAOyP,KAMpD,GALA1N,QAAQC,IAAI,qBAAsB3C,GAElCkP,GAASqB,EAAAA,EAAAA,OACTb,GAAa,GAET1P,EAASwQ,QAAS,CACpB,MAAM/O,EAAWzB,EAASF,KAG1B,IAAI8B,EAAY,GACJ,OAARH,QAAQ,IAARA,GAAAA,EAAUG,WAAauF,MAAMgD,QAAQ1I,EAASG,WAChDA,EAAYH,EAASG,UACJ,OAARH,QAAQ,IAARA,GAAAA,EAAU+H,UAAYrC,MAAMgD,QAAQ1I,EAAS+H,UACtD5H,EAAYH,EAAS+H,SACZ/H,GAAY0F,MAAMgD,QAAQ1I,KACnCG,EAAYH,GAGdiB,QAAQC,IAAI,aAAclB,GAC1BiB,QAAQC,IAAI,mBAAoBf,EAAUnB,QAC1CiC,QAAQC,IAAI,uBAAwB0F,OAAOC,KAAK7G,GAAY,CAAC,IAE7DqN,EAAalN,GACbiN,EAAYpN,GACZkM,GAAuB,OAARlM,QAAQ,IAARA,OAAQ,EAARA,EAAUc,WAAY,GAEZ,IAArBX,EAAUnB,SACZiC,QAAQ+N,KAAK,mCACb/N,QAAQC,IAAI,+BAAgC3C,GAC5C0Q,EAAAA,GAAQC,QAAQ,+DAEpB,MACEjO,QAAQ3C,MAAM,aAAcC,EAAS0Q,SACrChO,QAAQC,IAAI,uBAAwB3C,GACpC0Q,EAAAA,GAAQ3Q,MAAMC,EAAS0Q,SAAW,2BAEtC,CAAE,MAAO3Q,GACPmP,GAASqB,EAAAA,EAAAA,OACTb,GAAa,GACbhN,QAAQ3C,MAAM,4BAA6BA,GAC3C2Q,EAAAA,GAAQ3Q,MAAMA,EAAM2Q,SAAW,yCACjC,IACC,CAAC/P,EAAOyP,GAAIlB,IAQT0B,GAAkBV,EAAAA,EAAAA,cAAYvQ,UAClC,IAEE,IAAKkQ,IAASA,EAAKzN,IAGjB,OAFAsO,EAAAA,GAAQ3Q,MAAM,6CACd8B,EAAS,UAIXqN,GAASiB,EAAAA,EAAAA,OAET,MAAMU,EAAkB,GAClBC,EAAW,GAEjBlP,EAAUuC,SAAQ,CAAC4M,EAAGC,KACC,cAAjBD,EAAE7G,YAA+C,sBAAjB6G,EAAE7G,aACpC4G,EAASG,KAAKD,GACdH,EAAgBI,KAAK,CACnBzH,SAAUuH,EAAE5O,KACZ+O,eAAgBH,EAAE3C,eAAiB2C,EAAE9C,cACrCJ,WAAYT,EAAgB4D,IAAQ,KAExC,IAGF,MAAMG,OA/BmBxR,WAC3B,IAAKE,EAAQY,OAAQ,MAAO,GAC5B,MAAM,KAAEX,SAAesR,EAAAA,EAAAA,IAAwBvR,GAC/C,OAAOC,CAAI,EA4BgBuR,CAAqBR,GACxCS,EAAS,CAAC,EAEhBH,EAAWhN,SAASoN,IACdA,EAAEvC,QAAwC,mBAAvBuC,EAAEvC,OAAOlB,UAC9BwD,EAAOC,EAAE/H,UAAY+H,EAAEvC,OACS,mBAAhBuC,EAAEzD,YAClBwD,EAAOC,EAAE/H,UAAY,CAAEsE,UAAWyD,EAAEzD,UAAW0D,OAAQD,EAAEC,QAAU,IACrE,IAGF,MAAMC,EAAiB,GACjBC,EAAe,GACfC,EAAe,GAErB/P,EAAUuC,SAAQ,CAAC4M,EAAGC,KACpB,MAAMY,EAAgBxE,EAAgB4D,IAAQ,GAE9C,GAAqB,cAAjBD,EAAE7G,YAA+C,sBAAjB6G,EAAE7G,WAAoC,CACxE,MAAM,UAAE4D,GAAY,EAAK,OAAE0D,EAAS,IAAOF,EAAOP,EAAE5O,OAAS,CAAC,EACxD0P,GAAQC,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GAAQf,GAAC,IAAElD,WAAY+D,EAAeJ,WAEhD1D,EACF2D,EAAeR,KAAKY,IAEpBH,EAAaT,KAAKY,GAClBF,EAAaV,KAAK,CAChBzH,SAAUuH,EAAE5O,KACZ+O,eAAgBH,EAAE3C,eAAiB2C,EAAE9C,cACrCJ,WAAY+D,IAGlB,MAAO,GAAqB,YAAjBb,EAAE7G,WAA0B,CACrC,MAAM6H,EAAahB,EAAE9C,cACf+D,EAAgBjB,EAAExQ,SAAWwQ,EAAExQ,QAAQwR,IAAgBA,EACvDE,EAAalB,EAAExQ,SAAWwQ,EAAExQ,QAAQqR,IAAmBA,GAAiB,GAExE9D,EAAYiE,IAAeH,EAC3BC,GAAQC,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GAAQf,GAAC,IAAElD,WAAY+D,IAEjC9D,EACF2D,EAAeR,KAAKY,IAEpBH,EAAaT,KAAKY,GAClBF,EAAaV,KAAK,CAChBzH,SAAUuH,EAAE5O,KACZ+O,eAAgBc,EAChBnE,WAAYoE,IAGlB,KAIF,MAAMC,EAAYvC,EAAYtN,KAAKuB,OAAOuO,KAAKC,MAAQzC,GAAa,KAAQ,EACtE0C,EAA+C,KAAnB,OAAR5Q,QAAQ,IAARA,OAAQ,EAARA,EAAUc,WAAY,GAG1CmH,EAAiB9H,EAAUnB,OAC3B6R,EAAeb,EAAehR,OAC9B8R,EAAkBlQ,KAAKC,MAAOgQ,EAAe5I,EAAkB,KAC/D8I,EAAwB,GAAfF,EAITG,EAAUF,IADU9Q,EAASiR,cAAgB,IACI,OAAS,OAE1DC,EAAa,CACjBlB,eAAgBA,GAAkB,GAClCC,aAAcA,GAAgB,GAC9Be,QAASA,GAAW,OACpBG,MAAOL,EACPC,OAAQA,EACR9I,eAAgBA,EAChBwI,UAAWA,EACXG,iBAAkBA,GAGpB5E,EAAUkF,GAEV,MAAM3S,QAAiBN,EAAAA,EAAAA,IAAU,CAC/BmT,KAAMlS,EAAOyP,GACbpB,OAAQ2D,EACR9C,KAAMA,EAAKzN,MAGb,GAAIpC,EAASwQ,QAAS,CAEpB,MAAMsC,GAAYhB,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACba,GAAU,IACb7P,OAAQ9C,EAAS8C,SAEnB2K,EAAUqF,GAEVpR,EAAQ,UACRsC,OAAO+O,SAAS,EAAG,GACnB,IAAIC,MAAkB,SAAZP,EAAqBQ,EAAYC,GAAWC,MACxD,MACEzC,EAAAA,GAAQ3Q,MAAMC,EAAS0Q,SAEzBxB,GAASqB,EAAAA,EAAAA,MAEX,CAAE,MAAOxQ,GACPmP,GAASqB,EAAAA,EAAAA,OACTG,EAAAA,GAAQ3Q,MAAMA,EAAM2Q,QACtB,IACC,CAAC9O,EAAWwL,EAAiB3L,EAAUd,EAAOyP,GAAIP,EAAMhO,EAAUqN,IA0GrE,OArEA9L,EAAAA,EAAAA,YAAU,KACJkM,GAAmB,cAATF,IACZzL,cAAc4L,GACdqB,IACF,GACC,CAACtB,EAAQF,EAAMG,EAAYqB,KAE9BxN,EAAAA,EAAAA,YAAU,KACRV,QAAQC,IAAI,mCAAoChC,EAAOyP,IACnDzP,EAAOyP,GACTH,KAEAvN,QAAQ3C,MAAM,yCACd2Q,EAAAA,GAAQ3Q,MAAM,wDACd8B,EAAS,cACX,GACC,CAAClB,EAAOyP,GAAIH,EAAapO,KAE5BuB,EAAAA,EAAAA,YAAU,IACD,KACDmM,GACF5L,cAAc4L,EAChB,GAED,CAACA,KAGJnM,EAAAA,EAAAA,YAAU,KACK,iBAATgM,GAAoC,cAATA,GAAiC,WAATA,EACrDgE,SAASC,KAAKC,UAAUC,IAAI,mBAE5BH,SAASC,KAAKC,UAAUE,OAAO,mBAI1B,KACLJ,SAASC,KAAKC,UAAUE,OAAO,kBAAkB,IAElD,CAACpE,IA+BCS,EAsBEpO,GACLM,EAAAA,EAAAA,MAAA,OAAKC,UAAU,qEAAoEC,SAAA,CAEvE,iBAATmN,IACClN,EAAAA,EAAAA,KAACuR,EAAY,CACXhS,SAAUA,EACVC,QAASA,EACTC,WApHWA,KACjB,MAAM+R,GAAuB,OAARjS,QAAQ,IAARA,OAAQ,EAARA,EAAUc,WAAY,EAC3CoL,EAAe+F,GACf9D,EAAauC,KAAKC,OAElB,MAAMuB,EAAgBjQ,aAAY,KAChCiK,GAAgBiG,GACVA,EAAc,EACTA,EAAc,GAErBlG,GAAU,GACH,IAET,GACD,KACH8B,EAAcmE,EAAc,EAsGtB/R,UAAWA,IAIL,cAATwN,IACCK,GACEvN,EAAAA,EAAAA,KAAA,OAAKF,UAAU,sGAAqGC,UAClHF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,4GAA2GC,SAAA,EACxHC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,4IAA2IC,UACxJF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oCAAoC6R,KAAK,OAAOC,QAAQ,YAAW7R,SAAA,EAChFC,EAAAA,EAAAA,KAAA,UAAQF,UAAU,aAAa+R,GAAG,KAAKC,GAAG,KAAKzC,EAAE,KAAK0C,OAAO,eAAeC,YAAY,OACxFhS,EAAAA,EAAAA,KAAA,QAAMF,UAAU,aAAa6R,KAAK,eAAeM,EAAE,0HAGvDjS,EAAAA,EAAAA,KAAA,MAAIF,UAAU,wCAAuCC,SAAC,qBACtDC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,wBAAuBC,SAAC,sDAKlB,IAArBL,EAAUnB,QACZyB,EAAAA,EAAAA,KAAA,OAAKF,UAAU,uGAAsGC,UACnHF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,6GAA4GC,SAAA,EACzHC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,+HAA8HC,UAC3IC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,uBAAuB6R,KAAK,eAAeC,QAAQ,YAAW7R,UAC3EC,EAAAA,EAAAA,KAAA,QAAMkS,SAAS,UAAUD,EAAE,oNAAoNE,SAAS,iBAG5PnS,EAAAA,EAAAA,KAAA,MAAIF,UAAU,yCAAwCC,SAAC,wBACvDC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,8CAA6CC,SAAC,mEAG3DF,EAAAA,EAAAA,MAAA,MAAIC,UAAU,0CAAyCC,SAAA,EACrDC,EAAAA,EAAAA,KAAA,MAAAD,SAAI,uDACJC,EAAAA,EAAAA,KAAA,MAAAD,SAAI,uCACJC,EAAAA,EAAAA,KAAA,MAAAD,SAAI,2CAENF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,YAAWC,SAAA,EACxBC,EAAAA,EAAAA,KAAA,UACEO,QAjGY9C,UAC1B,IACEuP,GAASiB,EAAAA,EAAAA,OACT,MAAMnQ,QAAiBsU,MAAM,mCAAoC,CAC/DC,OAAQ,OACRC,QAAS,CACP,eAAgB,mBAChB,cAAgB,UAADrT,OAAYsT,aAAaC,QAAQ,WAElDrB,KAAMsB,KAAKC,UAAU,CAAEtE,OAAQ3P,EAAOyP,OAGlCtQ,QAAaE,EAAS6U,OACxB/U,EAAK0Q,SACPE,EAAAA,GAAQF,QAAQ1Q,EAAK4Q,SAErBT,KAEAS,EAAAA,GAAQ3Q,MAAMD,EAAK4Q,QAEvB,CAAE,MAAO3Q,GACP2Q,EAAAA,GAAQ3Q,MAAM,kCAChB,CAAC,QACCmP,GAASqB,EAAAA,EAAAA,MACX,GA0EcvO,UAAU,kNAAiNC,SAC5N,mCAGDC,EAAAA,EAAAA,KAAA,UACEO,QAASA,KACPC,QAAQC,IAAI,+BACZsN,GAAa,EAEfjO,UAAU,4MAA2MC,SACtN,gCAGDC,EAAAA,EAAAA,KAAA,UACEO,QAASA,IAAMZ,EAAS,cACxBG,UAAU,4MAA2MC,SACtN,sCAOPC,EAAAA,EAAAA,KAACqH,EAAmB,CAClBC,SAAU5H,EAAUmN,GACpBtF,cAAesF,EACfrF,eAAgB9H,EAAUnB,OAC1BkJ,eAAgByD,EAAgB2B,GAChCnF,eAAiBkL,GAAWtH,GAAkBsE,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAC,CAAC,EAAG1E,GAAe,IAAE,CAAC2B,GAAwB+F,KAC7FjL,OAAQA,KACFkF,IAA0BnN,EAAUnB,OAAS,EAC/CmQ,IAEArD,EAAyBwB,EAAwB,EACnD,EAEFjF,WAAYA,KACNiF,EAAwB,GAC1BxB,EAAyBwB,EAAwB,EACnD,EAEFhF,SAAUsF,EACVrF,WAAmB,OAARvI,QAAQ,IAARA,OAAQ,EAARA,EAAUU,OAAQ,UAKzB,WAATiN,IACCrN,EAAAA,EAAAA,MAAA,OAAKC,UAAU,8EAA6EC,SAAA,CACtE,SAAnB+M,EAAOyD,UAAsBvQ,EAAAA,EAAAA,KAAC6S,IAAQ,CAACvI,MAAOA,EAAO/D,OAAQA,KAE9DvG,EAAAA,EAAAA,KAAA,OAAKF,UAAU,yBAAwBC,UACrCF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,gGAA+FC,SAAA,EAE5GC,EAAAA,EAAAA,KAAA,OAAKF,UAAS,mCAAAb,OACO,SAAnB6N,EAAOyD,QACH,uEACA,sEACHxQ,UACDF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,WAAUC,SAAA,EACvBC,EAAAA,EAAAA,KAAA,OAAKF,UAAS,iFAAAb,OACO,SAAnB6N,EAAOyD,QACH,kDACA,kDACHxQ,UACDC,EAAAA,EAAAA,KAAA,OACE2J,IAAwB,SAAnBmD,EAAOyD,QAAqBuC,E,qxDACjClJ,IAAKkD,EAAOyD,QACZzQ,UAAU,gCAGdE,EAAAA,EAAAA,KAAA,MAAIF,UAAS,2CAAAb,OACQ,SAAnB6N,EAAOyD,QAAqB,mBAAqB,kBAChDxQ,SACmB,SAAnB+M,EAAOyD,QAAqB,kBAAoB,mBAEnDvQ,EAAAA,EAAAA,KAAA,KAAGF,UAAU,sEAAqEC,SAC5D,SAAnB+M,EAAOyD,QACJ,gDACA,yDAMV1Q,EAAAA,EAAAA,MAAA,OAAKC,UAAU,MAAKC,SAAA,EAClBF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,6CAA4CC,SAAA,EAEzDF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,0KAAyKC,SAAA,EACtLC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,yIACfD,EAAAA,EAAAA,MAAA,OAAKC,UAAU,uBAAsBC,SAAA,EACnCF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,wDAAuDC,SAAA,CACnEI,KAAKC,QAA6B,QAArBqM,EAAAK,EAAOyC,sBAAc,IAAA9C,OAAA,EAArBA,EAAuBlO,SAAU,GAAKmB,EAAUnB,OAAU,KAAK,QAE/EyB,EAAAA,EAAAA,KAAA,OAAKF,UAAU,8DAA6DC,SAAC,sBAKjFF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,+KAA8KC,SAAA,EAC3LC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,4IACfD,EAAAA,EAAAA,MAAA,OAAKC,UAAU,uBAAsBC,SAAA,EACnCF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,2DAA0DC,SAAA,CACtEkF,MAAMgD,QAAQ6E,EAAOyC,gBAAkBzC,EAAOyC,eAAehR,OAAS,EAAE,IAAEmB,EAAUnB,WAEvFyB,EAAAA,EAAAA,KAAA,OAAKF,UAAU,iEAAgEC,SAAC,mBAKpFF,EAAAA,EAAAA,MAAA,OAAKC,UAAS,qGAAAb,OACO,SAAnB6N,EAAOyD,QACH,6EACA,2EACHxQ,SAAA,EACDC,EAAAA,EAAAA,KAAA,OAAKF,UAAS,wGAAAb,OACO,SAAnB6N,EAAOyD,QAAqB,qBAAuB,mBAAkB,sBAEvE1Q,EAAAA,EAAAA,MAAA,OAAKC,UAAU,uBAAsBC,SAAA,EACnCC,EAAAA,EAAAA,KAAA,OAAKF,UAAS,2CAAAb,OACO,SAAnB6N,EAAOyD,QAAqB,mBAAqB,kBAChDxQ,SACmB,SAAnB+M,EAAOyD,QAAqB,OAAS,WAExCvQ,EAAAA,EAAAA,KAAA,OAAKF,UAAS,8CAAAb,OACO,SAAnB6N,EAAOyD,QAAqB,sBAAwB,qBACnDxQ,SACmB,SAAnB+M,EAAOyD,QAAqB,WAAU,QAAAtR,OAAWM,EAASiR,0BAOnExQ,EAAAA,EAAAA,KAAA,OAAKF,UAAU,OAAMC,UACnBF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,wCAAuCC,SAAA,EACpDF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,mBAAkBC,SAAA,EAC/BC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,wCAAuCC,SAAC,0BACtDC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,yBAAwBC,SAAC,+BAExCF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,WAAUC,SAAA,EACvBC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,oEAAmEC,UAChFC,EAAAA,EAAAA,KAAA,OACEF,UAAS,uFAAAb,OACY,SAAnB6N,EAAOyD,QACH,8DACA,6DAEN7L,MAAO,CAAE4F,MAAM,GAADrL,SAA4B,QAArByN,EAAAI,EAAOyC,sBAAc,IAAA7C,OAAA,EAArBA,EAAuBnO,SAAU,GAAKmB,EAAUnB,OAAU,IAAG,MAAMwB,UAExFC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,wEAGnBD,EAAAA,EAAAA,MAAA,OAAKC,UAAU,yCAAwCC,SAAA,EACrDC,EAAAA,EAAAA,KAAA,QAAMF,UAAU,qCAAoCC,SAAC,QACrDF,EAAAA,EAAAA,MAAA,QAAMC,UAAS,qCAAAb,OACM,SAAnB6N,EAAOyD,QAAqB,mBAAqB,kBAChDxQ,SAAA,CACAI,KAAKC,OAAQ6E,MAAMgD,QAAQ6E,EAAOyC,gBAAkBzC,EAAOyC,eAAehR,OAAS,GAAKmB,EAAUnB,OAAU,KAAK,QAEpHyB,EAAAA,EAAAA,KAAA,QAAMF,UAAU,qCAAoCC,SAAC,oBAO5D+M,EAAOlM,SACNZ,EAAAA,EAAAA,KAAA,OAAKF,UAAU,OAAMC,UACnBC,EAAAA,EAAAA,KAAC+S,EAAe,CAACnS,OAAQkM,EAAOlM,YAKpCZ,EAAAA,EAAAA,KAAA,OAAKF,UAAU,iDAAgDC,UAC7DF,EAAAA,EAAAA,MAAA,UACEC,UAAU,uPACVS,QAASA,IAAMf,EAAQ,UAAUO,SAAA,EAEjCC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,sIACfE,EAAAA,EAAAA,KAAA,QAAMF,UAAU,WAAUC,SAAC,mCAW/B,WAATmN,IACClN,EAAAA,EAAAA,KAACgL,EAAqB,CACpBtL,UAAWA,EACXwL,gBAAiBA,EACjBC,aAAcA,EACdC,iBAlXiB3N,MAAO6J,EAAU0H,EAAgBrD,EAAYlC,KACpE,IACEuD,GAASiB,EAAAA,EAAAA,OACT,MAAMnQ,QAAiBkV,EAAAA,EAAAA,IAA4B,CAAE1L,WAAU0H,iBAAgBrD,aAAYlC,aAC3FuD,GAASqB,EAAAA,EAAAA,OAELvQ,EAASwQ,QACX5C,GAAiBuH,IAAIrD,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GAAWqD,GAAI,IAAE,CAAC3L,GAAWxJ,EAASoV,gBAE3D1E,EAAAA,GAAQ3Q,MAAMC,EAASD,OAAS,+BAEpC,CAAE,MAAOA,GACPmP,GAASqB,EAAAA,EAAAA,OACTG,EAAAA,GAAQ3Q,MAAMA,EAAM2Q,QACtB,GAqWMhP,QAASA,EACTD,SAAUA,EACV8L,yBAA0BA,EAC1BC,mBAAoBA,EACpBC,UAAWA,EACXC,UAAWA,EACXC,eAAgBA,EAChBC,gBAAiBA,OAIrB,MApRA1L,EAAAA,EAAAA,KAAA,OAAKF,UAAU,sGAAqGC,UAClHF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,4GAA2GC,SAAA,EACxHC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,mFAAkFC,UAC/FC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,uBAAuB6R,KAAK,eAAeC,QAAQ,YAAW7R,UAC3EC,EAAAA,EAAAA,KAAA,QAAMkS,SAAS,UAAUD,EAAE,+JAA+JE,SAAS,iBAGvMnS,EAAAA,EAAAA,KAAA,MAAIF,UAAU,wCAAuCC,SAAC,6BACtDC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,qBAAoBC,SAAC,uEAClCC,EAAAA,EAAAA,KAAA,UACEF,UAAU,oNACVS,QAASA,IAAMZ,EAAS,UAAUI,SACnC,oBAyQX,C", "sources": ["apicalls/reports.js", "pages/user/WriteExam/Instructions.js", "components/modern/XPResultDisplay.js", "pages/user/WriteExam/index.js"], "sourcesContent": ["const { default: axiosInstance } = require(\".\");\r\n\r\n// add report\r\nexport const addReport = async (payload) => {\r\n    try {\r\n        const response = await axiosInstance.post(\"/api/reports/add-report\", payload);\r\n        return response.data;\r\n    } catch (error) {\r\n        return error.response.data;\r\n    }\r\n}\r\n\r\n// get all reports\r\nexport const getAllReports = async (filters) => {\r\n    try {\r\n        const response = await axiosInstance.post(\"/api/reports/get-all-reports\", filters);\r\n        return response.data;\r\n    } catch (error) {\r\n        return error.response.data;\r\n    }\r\n}\r\n\r\n// get all reports by user\r\nexport const getAllReportsByUser = async (payload) => {\r\n    try {\r\n        const response = await axiosInstance.post(\"/api/reports/get-all-reports-by-user\", payload);\r\n        return response.data;\r\n    } catch (error) {\r\n        return error.response.data;\r\n    }\r\n}\r\n\r\n// get all reports for ranking (legacy)\r\nexport const getAllReportsForRanking = async (filters) => {\r\n    try {\r\n        const response = await axiosInstance.get(\"/api/reports/get-all-reports-for-ranking\");\r\n        return response.data;\r\n    } catch (error) {\r\n        return error.response.data;\r\n    }\r\n}\r\n\r\n// Enhanced XP-based ranking endpoints\r\nexport const getEnhancedLeaderboard = async (level = 'all', limit = 1000) => {\r\n    try {\r\n        const response = await axiosInstance.get(`/api/quiz/enhanced-leaderboard?level=${level}&limit=${limit}`);\r\n        return response.data;\r\n    } catch (error) {\r\n        return error.response.data;\r\n    }\r\n}\r\n\r\nexport const getXPLeaderboard = async (options = {}) => {\r\n    try {\r\n        const params = new URLSearchParams();\r\n        if (options.limit) params.append('limit', options.limit);\r\n        if (options.classFilter) params.append('classFilter', options.classFilter);\r\n        if (options.levelFilter) params.append('levelFilter', options.levelFilter);\r\n        if (options.seasonFilter) params.append('seasonFilter', options.seasonFilter);\r\n        if (options.includeInactive) params.append('includeInactive', options.includeInactive);\r\n\r\n        const response = await axiosInstance.get(`/api/quiz/xp-leaderboard?${params.toString()}`);\r\n        return response.data;\r\n    } catch (error) {\r\n        return error.response.data;\r\n    }\r\n}\r\n\r\nexport const getUserRanking = async (userId, context = 5) => {\r\n    try {\r\n        const response = await axiosInstance.get(`/api/quiz/user-ranking/${userId}?context=${context}`);\r\n        return response.data;\r\n    } catch (error) {\r\n        return error.response.data;\r\n    }\r\n}\r\n\r\nexport const getClassRankings = async (className, limit = 50) => {\r\n    try {\r\n        const response = await axiosInstance.get(`/api/quiz/class-rankings/${className}?limit=${limit}`);\r\n        return response.data;\r\n    } catch (error) {\r\n        return error.response.data;\r\n    }\r\n}\r\n\r\n// XP Dashboard endpoints\r\nexport const getXPDashboard = async () => {\r\n    try {\r\n        const response = await axiosInstance.get(\"/api/xp-dashboard/dashboard\");\r\n        return response.data;\r\n    } catch (error) {\r\n        return error.response.data;\r\n    }\r\n}\r\n\r\nexport const getClassLeaderboard = async () => {\r\n    try {\r\n        const response = await axiosInstance.get(\"/api/xp-dashboard/class-leaderboard\");\r\n        return response.data;\r\n    } catch (error) {\r\n        return error.response.data;\r\n    }\r\n}\r\n", "import React from \"react\";\r\nimport { useNavigate } from \"react-router-dom\";\r\n\r\nfunction Instructions({ examData, setView, startTimer, questions = [] }) {\r\n  const navigate = useNavigate();\r\n\r\n  const formatTime = (seconds) => {\r\n    const minutes = Math.floor(seconds / 60);\r\n    const remainingSeconds = seconds % 60;\r\n    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;\r\n  };\r\n\r\n  return (\r\n    <div className=\"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100\">\r\n      {/* Header */}\r\n      <div className=\"bg-gradient-to-r from-blue-600 to-blue-700 shadow-lg\">\r\n        <div className=\"max-w-4xl mx-auto px-4 py-8\">\r\n          <div className=\"text-center\">\r\n            <h1 className=\"text-3xl font-bold text-white\">Instructions</h1>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <div className=\"max-w-2xl mx-auto px-4 py-12\">\r\n        {/* Simplified Content Card */}\r\n        <div className=\"bg-white rounded-2xl shadow-xl border-0 p-8 mb-8\">\r\n          <div className=\"text-center mb-8\">\r\n            <h1 className=\"text-4xl font-bold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent mb-4\">\r\n              {examData.name}\r\n            </h1>\r\n            <p className=\"text-lg text-gray-600 font-medium\">Challenge your brain, Beat the rest</p>\r\n\r\n            {/* Debug Info */}\r\n            <div className=\"mt-4 p-3 bg-gray-100 rounded-lg text-sm text-gray-600\">\r\n              <p><strong>Questions Available:</strong> {questions.length}</p>\r\n              <p><strong>Exam ID:</strong> {examData._id}</p>\r\n            </div>\r\n          </div>\r\n\r\n          {/* Quick Stats */}\r\n          <div className=\"grid grid-cols-2 gap-6 mb-8\">\r\n            <div className=\"text-center p-6 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl shadow-lg\">\r\n              <div className=\"text-3xl font-bold text-white mb-2\">{Math.round(examData.duration / 60)} min</div>\r\n              <div className=\"text-sm font-semibold text-blue-100\">Duration</div>\r\n            </div>\r\n            <div className=\"text-center p-6 bg-gradient-to-br from-green-500 to-green-600 rounded-xl shadow-lg\">\r\n              <div className=\"text-3xl font-bold text-white mb-2\">{examData.totalMarks}</div>\r\n              <div className=\"text-sm font-semibold text-green-100\">Total Marks</div>\r\n            </div>\r\n          </div>\r\n\r\n          {/* Action Buttons */}\r\n          <div className=\"flex justify-center gap-4\">\r\n            <button\r\n              className=\"px-6 py-3 bg-gradient-to-r from-gray-500 to-gray-600 text-white rounded-xl font-bold hover:from-gray-600 hover:to-gray-700 transform hover:scale-105 transition-all duration-300 shadow-lg\"\r\n              onClick={() => navigate('/user/quiz')}\r\n            >\r\n              Cancel\r\n            </button>\r\n            <button\r\n              className={`px-8 py-3 rounded-xl font-bold transform transition-all duration-300 shadow-lg ${\r\n                questions.length > 0\r\n                  ? 'bg-gradient-to-r from-blue-600 to-indigo-600 text-white hover:from-blue-700 hover:to-indigo-700 hover:scale-105'\r\n                  : 'bg-gray-400 text-gray-600 cursor-not-allowed'\r\n              }`}\r\n              onClick={() => {\r\n                if (questions.length > 0) {\r\n                  console.log(\"Starting quiz with\", questions.length, \"questions\");\r\n                  startTimer();\r\n                  setView(\"questions\");\r\n                } else {\r\n                  alert(\"Cannot start quiz: No questions available!\");\r\n                }\r\n              }}\r\n              disabled={questions.length === 0}\r\n            >\r\n              {questions.length > 0 ? 'Start Quiz' : 'No Questions Available'}\r\n            </button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n\r\nexport default Instructions;", "import React, { useState, useEffect } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { \n  TbB<PERSON>, \n  TbTrophy, \n  Tb<PERSON><PERSON>e, \n  TbTarget, \n  TbClock, \n  TbTrendingUp,\n  TbStar,\n  TbMedal,\n  TbChevronDown,\n  TbChevronUp\n} from 'react-icons/tb';\n\nconst XPResultDisplay = ({ xpData, className = '' }) => {\n  const [showBreakdown, setShowBreakdown] = useState(false);\n  const [animatedXP, setAnimatedXP] = useState(0);\n\n  // Debug XP data (safely)\n  if (process.env.NODE_ENV === 'development') {\n    console.log('🎨 XPResultDisplay - XP Data received:', xpData);\n  }\n\n  useEffect(() => {\n    if (xpData?.xpAwarded) {\n      // Animate XP counter\n      const duration = 2000;\n      const steps = 60;\n      const increment = xpData.xpAwarded / steps;\n      let current = 0;\n\n      const timer = setInterval(() => {\n        current += increment;\n        if (current >= xpData.xpAwarded) {\n          setAnimatedXP(xpData.xpAwarded);\n          clearInterval(timer);\n        } else {\n          setAnimatedXP(Math.floor(current));\n        }\n      }, duration / steps);\n\n      return () => clearInterval(timer);\n    }\n  }, [xpData]);\n\n  // Play level-up sound effect\n  useEffect(() => {\n    if (xpData?.levelUp) {\n      const playLevelUpSound = () => {\n        try {\n          const audioContext = new window.AudioContext();\n\n          // Create epic level-up sound sequence\n          const createLevelUpSound = () => {\n            // Triumphant ascending chord progression\n            const chords = [\n              [262, 330, 392], // C4, E4, G4\n              [294, 370, 440], // D4, F#4, A4\n              [330, 415, 494], // E4, G#4, B4\n              [349, 440, 523]  // F4, A4, C5\n            ];\n\n            chords.forEach((chord, index) => {\n              setTimeout(() => {\n                chord.forEach((frequency) => {\n                  const oscillator = audioContext.createOscillator();\n                  const gainNode = audioContext.createGain();\n\n                  oscillator.connect(gainNode);\n                  gainNode.connect(audioContext.destination);\n\n                  oscillator.frequency.setValueAtTime(frequency, audioContext.currentTime);\n                  oscillator.type = 'triangle';\n\n                  gainNode.gain.setValueAtTime(0, audioContext.currentTime);\n                  gainNode.gain.linearRampToValueAtTime(0.15, audioContext.currentTime + 0.02);\n                  gainNode.gain.exponentialRampToValueAtTime(0.001, audioContext.currentTime + 0.6);\n\n                  oscillator.start(audioContext.currentTime);\n                  oscillator.stop(audioContext.currentTime + 0.6);\n                });\n              }, index * 200);\n            });\n\n            // Add sparkle effect at the end\n            setTimeout(() => {\n              [659, 784, 988, 1175].forEach((freq, i) => {\n                setTimeout(() => {\n                  const osc = audioContext.createOscillator();\n                  const gain = audioContext.createGain();\n\n                  osc.connect(gain);\n                  gain.connect(audioContext.destination);\n\n                  osc.frequency.setValueAtTime(freq, audioContext.currentTime);\n                  osc.type = 'sine';\n\n                  gain.gain.setValueAtTime(0, audioContext.currentTime);\n                  gain.gain.linearRampToValueAtTime(0.2, audioContext.currentTime + 0.01);\n                  gain.gain.exponentialRampToValueAtTime(0.001, audioContext.currentTime + 0.3);\n\n                  osc.start(audioContext.currentTime);\n                  osc.stop(audioContext.currentTime + 0.3);\n                }, i * 100);\n              });\n            }, 800);\n          };\n\n          createLevelUpSound();\n          console.log('🎵 Level Up sound played!');\n\n        } catch (error) {\n          console.log('Level-up audio not supported:', error);\n        }\n      };\n\n      // Delay sound to sync with animation\n      setTimeout(playLevelUpSound, 600);\n    }\n  }, [xpData?.levelUp]);\n\n  if (!xpData) {\n    return null;\n  }\n\n  const {\n    xpAwarded = 0,\n    xpBreakdown = {},\n    levelUp = false,\n    newLevel = 1,\n    newTotalXP = 0,\n    currentStreak = 0,\n    achievements = []\n  } = xpData;\n\n  return (\n    <div className={`bg-gradient-to-br from-purple-50 to-indigo-50 rounded-2xl p-6 border-2 border-purple-200 shadow-lg ${className}`}>\n      {/* CSS Animations */}\n      <style jsx>{`\n        @keyframes levelUpGlow {\n          0% { box-shadow: 0 25px 50px -12px rgba(251, 191, 36, 0.5), 0 0 20px rgba(251, 191, 36, 0.3); }\n          100% { box-shadow: 0 25px 50px -12px rgba(251, 191, 36, 0.8), 0 0 40px rgba(251, 191, 36, 0.6); }\n        }\n      `}</style>\n      {/* Enhanced Level Up Notification */}\n      <AnimatePresence>\n        {levelUp && (\n          <motion.div\n            initial={{ opacity: 0, scale: 0.3, y: -50, rotateY: -180 }}\n            animate={{\n              opacity: 1,\n              scale: [0.3, 1.2, 1],\n              y: 0,\n              rotateY: 0,\n              transition: {\n                duration: 1.2,\n                ease: \"easeOut\",\n                scale: {\n                  times: [0, 0.6, 1],\n                  duration: 1.2\n                }\n              }\n            }}\n            exit={{ opacity: 0, scale: 0.8, y: -20 }}\n            className=\"mb-6 p-6 bg-gradient-to-r from-yellow-400 via-orange-500 to-red-500 rounded-2xl text-white text-center shadow-2xl relative overflow-hidden\"\n            style={{\n              background: 'linear-gradient(45deg, #fbbf24, #f59e0b, #ea580c, #dc2626)',\n              boxShadow: '0 25px 50px -12px rgba(251, 191, 36, 0.5), 0 0 0 1px rgba(255, 255, 255, 0.1)',\n              animation: 'levelUpGlow 2s infinite alternate'\n            }}\n          >\n            {/* Animated Background Effects */}\n            <motion.div\n              className=\"absolute inset-0 bg-gradient-to-r from-transparent via-white to-transparent opacity-20\"\n              animate={{\n                x: ['-100%', '100%'],\n                transition: {\n                  duration: 2,\n                  repeat: Infinity,\n                  ease: \"linear\"\n                }\n              }}\n            />\n\n            {/* Floating Particles */}\n            {[...Array(8)].map((_, i) => (\n              <motion.div\n                key={i}\n                className=\"absolute w-2 h-2 bg-white rounded-full opacity-80\"\n                style={{\n                  left: `${20 + i * 10}%`,\n                  top: `${20 + (i % 3) * 20}%`\n                }}\n                animate={{\n                  y: [-10, -30, -10],\n                  opacity: [0.8, 0.3, 0.8],\n                  scale: [1, 1.5, 1],\n                  transition: {\n                    duration: 2 + i * 0.2,\n                    repeat: Infinity,\n                    ease: \"easeInOut\"\n                  }\n                }}\n              />\n            ))}\n\n            <motion.div\n              className=\"relative z-10\"\n              animate={{\n                scale: [1, 1.05, 1],\n                transition: {\n                  duration: 1.5,\n                  repeat: Infinity,\n                  ease: \"easeInOut\"\n                }\n              }}\n            >\n              <motion.div\n                className=\"flex items-center justify-center mb-3\"\n                animate={{\n                  rotateY: [0, 360],\n                  transition: {\n                    duration: 3,\n                    repeat: Infinity,\n                    ease: \"linear\"\n                  }\n                }}\n              >\n                <TbTrophy className=\"w-12 h-12 mr-3 drop-shadow-lg\" />\n                <motion.span\n                  className=\"text-3xl font-black tracking-wider\"\n                  style={{\n                    textShadow: '2px 2px 4px rgba(0,0,0,0.5)',\n                    background: 'linear-gradient(45deg, #fff, #fbbf24)',\n                    WebkitBackgroundClip: 'text',\n                    WebkitTextFillColor: 'transparent'\n                  }}\n                  animate={{\n                    scale: [1, 1.1, 1],\n                    transition: {\n                      duration: 1,\n                      repeat: Infinity,\n                      ease: \"easeInOut\"\n                    }\n                  }}\n                >\n                  LEVEL UP!\n                </motion.span>\n              </motion.div>\n\n              <motion.p\n                className=\"text-xl font-bold\"\n                style={{ textShadow: '1px 1px 2px rgba(0,0,0,0.5)' }}\n                initial={{ opacity: 0, y: 20 }}\n                animate={{\n                  opacity: 1,\n                  y: 0,\n                  transition: { delay: 0.5, duration: 0.8 }\n                }}\n              >\n                🎉 You reached Level {newLevel}! 🎉\n              </motion.p>\n\n              <motion.div\n                className=\"mt-3 text-sm font-semibold opacity-90\"\n                initial={{ opacity: 0 }}\n                animate={{\n                  opacity: 1,\n                  transition: { delay: 1, duration: 0.8 }\n                }}\n              >\n                Keep up the amazing work! 🚀\n              </motion.div>\n            </motion.div>\n          </motion.div>\n        )}\n      </AnimatePresence>\n\n      {/* Main XP Display */}\n      <div className=\"text-center mb-6\">\n        <div className=\"flex items-center justify-center mb-3\">\n          <TbBolt className=\"w-8 h-8 text-purple-600 mr-2\" />\n          <h3 className=\"text-2xl font-bold text-gray-800\">XP Earned</h3>\n        </div>\n        \n        <motion.div\n          initial={{ scale: 0 }}\n          animate={{ scale: 1 }}\n          transition={{ type: \"spring\", stiffness: 200, damping: 10 }}\n          className=\"text-6xl font-bold text-purple-600 mb-2\"\n        >\n          +{animatedXP}\n        </motion.div>\n        \n        <p className=\"text-gray-600\">\n          Total XP: {newTotalXP.toLocaleString()} | Level {newLevel}\n        </p>\n      </div>\n\n      {/* Quick Stats */}\n      <div className=\"grid grid-cols-2 gap-4 mb-6\">\n        {currentStreak > 0 && (\n          <div className=\"bg-white rounded-lg p-3 text-center shadow-sm\">\n            <TbFlame className=\"w-6 h-6 text-orange-500 mx-auto mb-1\" />\n            <div className=\"text-lg font-bold text-gray-800\">{currentStreak}</div>\n            <div className=\"text-sm text-gray-600\">Streak</div>\n          </div>\n        )}\n        \n        {achievements.length > 0 && (\n          <div className=\"bg-white rounded-lg p-3 text-center shadow-sm\">\n            <TbMedal className=\"w-6 h-6 text-yellow-500 mx-auto mb-1\" />\n            <div className=\"text-lg font-bold text-gray-800\">{achievements.length}</div>\n            <div className=\"text-sm text-gray-600\">New Badges</div>\n          </div>\n        )}\n      </div>\n\n      {/* XP Breakdown Toggle */}\n      {Object.keys(xpBreakdown).length > 0 && (\n        <div>\n          <button\n            onClick={() => setShowBreakdown(!showBreakdown)}\n            className=\"w-full flex items-center justify-between p-3 bg-white rounded-lg shadow-sm hover:shadow-md transition-shadow duration-200\"\n          >\n            <span className=\"font-medium text-gray-800\">XP Breakdown</span>\n            {showBreakdown ? (\n              <TbChevronUp className=\"w-5 h-5 text-gray-600\" />\n            ) : (\n              <TbChevronDown className=\"w-5 h-5 text-gray-600\" />\n            )}\n          </button>\n\n          <AnimatePresence>\n            {showBreakdown && (\n              <motion.div\n                initial={{ opacity: 0, height: 0 }}\n                animate={{ opacity: 1, height: \"auto\" }}\n                exit={{ opacity: 0, height: 0 }}\n                className=\"mt-3 bg-white rounded-lg p-4 shadow-sm\"\n              >\n                <div className=\"space-y-3\">\n                  {xpBreakdown.baseXP && (\n                    <div className=\"flex justify-between items-center\">\n                      <div className=\"flex items-center\">\n                        <TbTarget className=\"w-4 h-4 text-blue-500 mr-2\" />\n                        <span className=\"text-sm text-gray-700\">Base XP</span>\n                      </div>\n                      <span className=\"font-medium text-gray-800\">+{xpBreakdown.baseXP}</span>\n                    </div>\n                  )}\n                  \n                  {xpBreakdown.difficultyBonus > 0 && (\n                    <div className=\"flex justify-between items-center\">\n                      <div className=\"flex items-center\">\n                        <TbTrendingUp className=\"w-4 h-4 text-green-500 mr-2\" />\n                        <span className=\"text-sm text-gray-700\">Difficulty Bonus</span>\n                      </div>\n                      <span className=\"font-medium text-green-600\">+{xpBreakdown.difficultyBonus}</span>\n                    </div>\n                  )}\n                  \n                  {xpBreakdown.speedBonus > 0 && (\n                    <div className=\"flex justify-between items-center\">\n                      <div className=\"flex items-center\">\n                        <TbClock className=\"w-4 h-4 text-yellow-500 mr-2\" />\n                        <span className=\"text-sm text-gray-700\">Speed Bonus</span>\n                      </div>\n                      <span className=\"font-medium text-yellow-600\">+{xpBreakdown.speedBonus}</span>\n                    </div>\n                  )}\n                  \n                  {xpBreakdown.perfectScoreBonus > 0 && (\n                    <div className=\"flex justify-between items-center\">\n                      <div className=\"flex items-center\">\n                        <TbStar className=\"w-4 h-4 text-purple-500 mr-2\" />\n                        <span className=\"text-sm text-gray-700\">Perfect Score</span>\n                      </div>\n                      <span className=\"font-medium text-purple-600\">+{xpBreakdown.perfectScoreBonus}</span>\n                    </div>\n                  )}\n                  \n                  {xpBreakdown.streakBonus > 0 && (\n                    <div className=\"flex justify-between items-center\">\n                      <div className=\"flex items-center\">\n                        <TbFlame className=\"w-4 h-4 text-orange-500 mr-2\" />\n                        <span className=\"text-sm text-gray-700\">Streak Bonus</span>\n                      </div>\n                      <span className=\"font-medium text-orange-600\">+{xpBreakdown.streakBonus}</span>\n                    </div>\n                  )}\n                  \n                  {xpBreakdown.firstAttemptBonus > 0 && (\n                    <div className=\"flex justify-between items-center\">\n                      <div className=\"flex items-center\">\n                        <TbMedal className=\"w-4 h-4 text-indigo-500 mr-2\" />\n                        <span className=\"text-sm text-gray-700\">First Attempt</span>\n                      </div>\n                      <span className=\"font-medium text-indigo-600\">+{xpBreakdown.firstAttemptBonus}</span>\n                    </div>\n                  )}\n                </div>\n              </motion.div>\n            )}\n          </AnimatePresence>\n        </div>\n      )}\n\n      {/* Achievement Notifications */}\n      {achievements.length > 0 && (\n        <div className=\"mt-6\">\n          <h4 className=\"text-lg font-bold text-gray-800 mb-3 flex items-center\">\n            <TbMedal className=\"w-5 h-5 mr-2 text-yellow-500\" />\n            New Achievements\n          </h4>\n          <div className=\"space-y-2\">\n            {achievements.map((achievement, index) => (\n              <motion.div\n                key={index}\n                initial={{ opacity: 0, x: -20 }}\n                animate={{ opacity: 1, x: 0 }}\n                transition={{ delay: index * 0.1 }}\n                className=\"flex items-center p-3 bg-gradient-to-r from-yellow-50 to-orange-50 rounded-lg border border-yellow-200\"\n              >\n                <TbTrophy className=\"w-6 h-6 text-yellow-500 mr-3\" />\n                <div>\n                  <div className=\"font-medium text-gray-800\">{String(achievement.name || 'Achievement')}</div>\n                  <div className=\"text-sm text-gray-600\">{String(achievement.description || 'Achievement unlocked!')}</div>\n                </div>\n              </motion.div>\n            ))}\n          </div>\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default XPResultDisplay;\n", "import { message } from \"antd\";\r\nimport React, { useCallback, useEffect, useState } from \"react\";\r\nimport { useDispatch, useSelector } from \"react-redux\";\r\nimport { useNavigate, useParams } from \"react-router-dom\";\r\nimport { getExamById } from \"../../../apicalls/exams\";\r\nimport { addReport } from \"../../../apicalls/reports\";\r\nimport { HideLoading, ShowLoading } from \"../../../redux/loaderSlice\";\r\nimport Instructions from \"./Instructions\";\r\nimport Pass from \"../../../assets/pass.gif\";\r\nimport Fail from \"../../../assets/fail.gif\";\r\nimport Confetti from \"react-confetti\";\r\nimport useWindowSize from \"react-use/lib/useWindowSize\";\r\nimport PassSound from \"../../../assets/pass.mp3\";\r\nimport FailSound from \"../../../assets/fail.mp3\";\r\nimport { chatWithChatGPTToGetAns, chatWithChatGPTToExplainAns } from \"../../../apicalls/chat\";\r\nimport XPResultDisplay from \"../../../components/modern/XPResultDisplay\";\r\nimport { extractUserResultData, safeNumber } from \"../../../utils/quizDataUtils\";\r\n\r\n// Minimal Safe Quiz Component\r\nconst MinimalQuizRenderer = ({ question, questionIndex, totalQuestions, selectedAnswer, onAnswerSelect, onNext, onPrevious, timeLeft, examTitle }) => {\r\n  // Safety checks\r\n  if (!question) {\r\n    return <div>Loading question...</div>;\r\n  }\r\n\r\n  // Convert everything to safe strings\r\n  const questionText = question.name ? String(question.name) : 'Question text not available';\r\n  const answerType = question.answerType ? String(question.answerType) : 'Options';\r\n\r\n  // Process options safely\r\n  let options = [];\r\n  if (question.options) {\r\n    if (Array.isArray(question.options)) {\r\n      options = question.options.map(opt => String(opt || ''));\r\n    } else if (typeof question.options === 'object') {\r\n      options = Object.values(question.options).map(opt => String(opt || ''));\r\n    }\r\n  }\r\n\r\n  const formatTime = (seconds) => {\r\n    const mins = Math.floor(seconds / 60);\r\n    const secs = seconds % 60;\r\n    return mins + ':' + (secs < 10 ? '0' : '') + secs;\r\n  };\r\n\r\n  return (\r\n    <div style={{ minHeight: '100vh', background: 'linear-gradient(135deg, #f0f9ff 0%, #e0e7ff 100%)' }}>\r\n      {/* Simple Header */}\r\n      <div style={{\r\n        background: 'white',\r\n        borderBottom: '1px solid #e5e7eb',\r\n        padding: '16px',\r\n        position: 'sticky',\r\n        top: 0,\r\n        zIndex: 50\r\n      }}>\r\n        <div style={{ maxWidth: '1200px', margin: '0 auto', display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>\r\n          <div>\r\n            <h1 style={{ fontSize: '18px', fontWeight: '600', color: '#111827', margin: 0 }}>\r\n              {examTitle ? String(examTitle) : 'Quiz'}\r\n            </h1>\r\n            <p style={{ fontSize: '14px', color: '#6b7280', margin: 0 }}>\r\n              Question {questionIndex + 1} of {totalQuestions}\r\n            </p>\r\n          </div>\r\n\r\n          <div style={{\r\n            background: timeLeft <= 60 ? '#dc2626' : '#2563eb',\r\n            color: 'white',\r\n            padding: '12px 24px',\r\n            borderRadius: '12px',\r\n            fontFamily: 'monospace',\r\n            fontWeight: 'bold'\r\n          }}>\r\n            TIME: {formatTime(timeLeft)}\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Question Content */}\r\n      <div style={{ maxWidth: '800px', margin: '0 auto', padding: '32px 16px' }}>\r\n        <div style={{\r\n          background: 'white',\r\n          borderRadius: '16px',\r\n          boxShadow: '0 25px 50px -12px rgba(0, 0, 0, 0.25)',\r\n          padding: '32px',\r\n          marginBottom: '24px'\r\n        }}>\r\n          {/* Question Number Badge */}\r\n          <div style={{ marginBottom: '24px' }}>\r\n            <span style={{\r\n              background: '#dbeafe',\r\n              color: '#1e40af',\r\n              padding: '8px 16px',\r\n              borderRadius: '20px',\r\n              fontSize: '14px',\r\n              fontWeight: '600'\r\n            }}>\r\n              Question {questionIndex + 1} of {totalQuestions}\r\n            </span>\r\n          </div>\r\n\r\n          {/* Question Text */}\r\n          <div style={{ marginBottom: '32px' }}>\r\n            <h2 style={{\r\n              fontSize: '20px',\r\n              fontWeight: '500',\r\n              color: '#111827',\r\n              lineHeight: '1.6',\r\n              margin: 0\r\n            }}>\r\n              {questionText}\r\n            </h2>\r\n          </div>\r\n\r\n          {/* Image */}\r\n          {(question.image || question.imageUrl) && (\r\n            <div style={{ marginBottom: '32px', textAlign: 'center' }}>\r\n              <img\r\n                src={question.image || question.imageUrl}\r\n                alt=\"Question\"\r\n                style={{\r\n                  maxWidth: '100%',\r\n                  maxHeight: '400px',\r\n                  objectFit: 'contain',\r\n                  borderRadius: '12px',\r\n                  border: '1px solid #e5e7eb'\r\n                }}\r\n              />\r\n            </div>\r\n          )}\r\n\r\n          {/* Answer Options */}\r\n          <div style={{ display: 'flex', flexDirection: 'column', gap: '16px' }}>\r\n            {answerType === \"Options\" && options.length > 0 ? (\r\n              options.map((option, index) => {\r\n                const letter = String.fromCharCode(65 + index);\r\n                const isSelected = selectedAnswer === index;\r\n\r\n                return (\r\n                  <button\r\n                    key={index}\r\n                    onClick={() => onAnswerSelect(index)}\r\n                    style={{\r\n                      width: '100%',\r\n                      textAlign: 'left',\r\n                      padding: '16px',\r\n                      borderRadius: '12px',\r\n                      border: isSelected ? '2px solid #2563eb' : '2px solid #e5e7eb',\r\n                      background: isSelected ? '#eff6ff' : 'white',\r\n                      color: isSelected ? '#1e40af' : '#111827',\r\n                      cursor: 'pointer',\r\n                      transition: 'all 0.3s',\r\n                      display: 'flex',\r\n                      alignItems: 'center',\r\n                      gap: '16px'\r\n                    }}\r\n                  >\r\n                    <div style={{\r\n                      width: '32px',\r\n                      height: '32px',\r\n                      borderRadius: '50%',\r\n                      background: isSelected ? '#2563eb' : '#f3f4f6',\r\n                      color: isSelected ? 'white' : '#6b7280',\r\n                      display: 'flex',\r\n                      alignItems: 'center',\r\n                      justifyContent: 'center',\r\n                      fontWeight: 'bold',\r\n                      fontSize: '14px'\r\n                    }}>\r\n                      {letter}\r\n                    </div>\r\n                    <span style={{ flex: 1, fontWeight: '500' }}>{option}</span>\r\n                    {isSelected && (\r\n                      <div style={{\r\n                        width: '24px',\r\n                        height: '24px',\r\n                        borderRadius: '50%',\r\n                        background: '#2563eb',\r\n                        color: 'white',\r\n                        display: 'flex',\r\n                        alignItems: 'center',\r\n                        justifyContent: 'center'\r\n                      }}>\r\n                        ✓\r\n                      </div>\r\n                    )}\r\n                  </button>\r\n                );\r\n              })\r\n            ) : (\r\n              <div>\r\n                <label style={{\r\n                  display: 'block',\r\n                  fontSize: '14px',\r\n                  fontWeight: '500',\r\n                  color: '#374151',\r\n                  marginBottom: '8px'\r\n                }}>\r\n                  Your Answer:\r\n                </label>\r\n                <input\r\n                  type=\"text\"\r\n                  value={selectedAnswer || ''}\r\n                  onChange={(e) => onAnswerSelect(e.target.value)}\r\n                  placeholder=\"Type your answer here...\"\r\n                  style={{\r\n                    width: '100%',\r\n                    padding: '16px',\r\n                    border: '2px solid #e5e7eb',\r\n                    borderRadius: '12px',\r\n                    fontSize: '16px',\r\n                    outline: 'none'\r\n                  }}\r\n                />\r\n              </div>\r\n            )}\r\n          </div>\r\n        </div>\r\n\r\n        {/* Navigation */}\r\n        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>\r\n          <button\r\n            onClick={onPrevious}\r\n            disabled={questionIndex === 0}\r\n            style={{\r\n              padding: '12px 24px',\r\n              borderRadius: '12px',\r\n              fontWeight: '600',\r\n              border: 'none',\r\n              cursor: questionIndex === 0 ? 'not-allowed' : 'pointer',\r\n              background: questionIndex === 0 ? '#e5e7eb' : '#4b5563',\r\n              color: questionIndex === 0 ? '#9ca3af' : 'white'\r\n            }}\r\n          >\r\n            ← Previous\r\n          </button>\r\n\r\n          <div style={{ textAlign: 'center' }}>\r\n            <div style={{ fontSize: '14px', color: '#6b7280', marginBottom: '8px' }}>Progress</div>\r\n            <div style={{\r\n              width: '200px',\r\n              height: '8px',\r\n              background: '#e5e7eb',\r\n              borderRadius: '4px',\r\n              overflow: 'hidden'\r\n            }}>\r\n              <div\r\n                style={{\r\n                  height: '100%',\r\n                  background: '#2563eb',\r\n                  borderRadius: '4px',\r\n                  width: ((questionIndex + 1) / totalQuestions) * 100 + '%',\r\n                  transition: 'width 0.3s'\r\n                }}\r\n              />\r\n            </div>\r\n          </div>\r\n\r\n          <button\r\n            onClick={onNext}\r\n            style={{\r\n              padding: '12px 24px',\r\n              borderRadius: '12px',\r\n              fontWeight: '600',\r\n              border: 'none',\r\n              cursor: 'pointer',\r\n              background: '#2563eb',\r\n              color: 'white'\r\n            }}\r\n          >\r\n            {questionIndex === totalQuestions - 1 ? 'Submit Quiz' : 'Next →'}\r\n          </button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\n// Minimal Safe Review Component\r\nconst MinimalReviewRenderer = ({\r\n  questions,\r\n  selectedOptions,\r\n  explanations,\r\n  fetchExplanation,\r\n  setView,\r\n  examData,\r\n  setSelectedQuestionIndex,\r\n  setSelectedOptions,\r\n  setResult,\r\n  setTimeUp,\r\n  setSecondsLeft,\r\n  setExplanations\r\n}) => {\r\n  if (!questions || !Array.isArray(questions)) {\r\n    return <div>No questions to review</div>;\r\n  }\r\n\r\n  return (\r\n    <div style={{ minHeight: '100vh', background: 'linear-gradient(135deg, #f8fafc 0%, #e0e7ff 100%)', padding: '24px' }}>\r\n      <div style={{ maxWidth: '800px', margin: '0 auto' }}>\r\n        {/* Header */}\r\n        <div style={{ textAlign: 'center', marginBottom: '24px' }}>\r\n          <div style={{\r\n            background: 'white',\r\n            borderRadius: '16px',\r\n            padding: '24px',\r\n            boxShadow: '0 10px 25px -5px rgba(0, 0, 0, 0.1)',\r\n            border: '1px solid #e2e8f0'\r\n          }}>\r\n            <h2 style={{ fontSize: '24px', fontWeight: 'bold', color: '#2563eb', margin: '0 0 8px 0' }}>\r\n              Answer Review\r\n            </h2>\r\n            <p style={{ color: '#64748b', margin: 0 }}>Review your answers and get explanations</p>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Questions */}\r\n        <div style={{ display: 'flex', flexDirection: 'column', gap: '16px', marginBottom: '24px' }}>\r\n          {questions.map((question, index) => {\r\n            if (!question) return null;\r\n\r\n            const questionText = question.name ? String(question.name) : 'Question not available';\r\n            const answerType = question.answerType ? String(question.answerType) : 'Options';\r\n            const userAnswer = selectedOptions[index];\r\n\r\n            let isCorrect = false;\r\n            let correctAnswerText = 'Unknown';\r\n            let userAnswerText = 'Not answered';\r\n\r\n            if (answerType === \"Options\") {\r\n              isCorrect = question.correctOption === userAnswer;\r\n\r\n              if (question.options && question.correctOption !== undefined) {\r\n                const correctOpt = question.options[question.correctOption];\r\n                correctAnswerText = correctOpt ? String(correctOpt) : 'Unknown';\r\n              }\r\n\r\n              if (question.options && userAnswer !== undefined) {\r\n                const userOpt = question.options[userAnswer];\r\n                userAnswerText = userOpt ? String(userOpt) : 'Not answered';\r\n              }\r\n            } else {\r\n              isCorrect = question.correctAnswer === userAnswer;\r\n              correctAnswerText = question.correctAnswer ? String(question.correctAnswer) : 'Unknown';\r\n              userAnswerText = userAnswer ? String(userAnswer) : 'Not answered';\r\n            }\r\n\r\n            return (\r\n              <div\r\n                key={question._id || index}\r\n                style={{\r\n                  borderRadius: '12px',\r\n                  boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',\r\n                  border: '2px solid ' + (isCorrect ? '#10b981' : '#ef4444'),\r\n                  padding: '16px',\r\n                  background: isCorrect ? '#f0fdf4' : '#fef2f2'\r\n                }}\r\n              >\r\n                {/* Question */}\r\n                <div style={{ marginBottom: '12px' }}>\r\n                  <div style={{ display: 'flex', alignItems: 'flex-start', gap: '12px' }}>\r\n                    <div style={{\r\n                      width: '32px',\r\n                      height: '32px',\r\n                      borderRadius: '8px',\r\n                      background: '#2563eb',\r\n                      color: 'white',\r\n                      display: 'flex',\r\n                      alignItems: 'center',\r\n                      justifyContent: 'center',\r\n                      fontWeight: 'bold',\r\n                      fontSize: '14px',\r\n                      flexShrink: 0,\r\n                      marginTop: '4px'\r\n                    }}>\r\n                      {index + 1}\r\n                    </div>\r\n                    <div style={{ flex: 1 }}>\r\n                      <p style={{\r\n                        color: '#1e293b',\r\n                        fontWeight: '500',\r\n                        lineHeight: '1.6',\r\n                        margin: 0\r\n                      }}>\r\n                        {questionText}\r\n                      </p>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n\r\n                {/* Your Answer */}\r\n                <div style={{ marginBottom: '8px' }}>\r\n                  <span style={{ fontSize: '14px', fontWeight: '600', color: '#64748b' }}>Your Answer: </span>\r\n                  <span style={{\r\n                    fontWeight: '500',\r\n                    color: isCorrect ? '#059669' : '#dc2626'\r\n                  }}>\r\n                    {userAnswerText}\r\n                  </span>\r\n                  <span style={{\r\n                    marginLeft: '12px',\r\n                    fontSize: '18px',\r\n                    color: isCorrect ? '#10b981' : '#ef4444'\r\n                  }}>\r\n                    {isCorrect ? '✓' : '✗'}\r\n                  </span>\r\n                </div>\r\n\r\n                {/* Correct Answer */}\r\n                {!isCorrect && (\r\n                  <div style={{ marginBottom: '8px' }}>\r\n                    <span style={{ fontSize: '14px', fontWeight: '600', color: '#64748b' }}>Correct Answer: </span>\r\n                    <span style={{ fontWeight: '500', color: '#059669' }}>{correctAnswerText}</span>\r\n                    <span style={{ marginLeft: '12px', fontSize: '18px', color: '#10b981' }}>✓</span>\r\n                  </div>\r\n                )}\r\n\r\n                {/* Explanation Button */}\r\n                {!isCorrect && (\r\n                  <div style={{ marginTop: '8px' }}>\r\n                    <button\r\n                      onClick={() => {\r\n                        fetchExplanation(\r\n                          questionText,\r\n                          correctAnswerText,\r\n                          userAnswerText,\r\n                          question.image || question.imageUrl || ''\r\n                        );\r\n                      }}\r\n                      style={{\r\n                        padding: '8px 16px',\r\n                        background: '#2563eb',\r\n                        color: 'white',\r\n                        border: 'none',\r\n                        borderRadius: '8px',\r\n                        fontSize: '14px',\r\n                        fontWeight: '500',\r\n                        cursor: 'pointer',\r\n                        display: 'flex',\r\n                        alignItems: 'center',\r\n                        gap: '8px'\r\n                      }}\r\n                    >\r\n                      <span>💡</span>\r\n                      <span>Get Explanation</span>\r\n                    </button>\r\n                  </div>\r\n                )}\r\n\r\n                {/* Explanation */}\r\n                {explanations[questionText] && (\r\n                  <div style={{\r\n                    marginTop: '12px',\r\n                    padding: '12px',\r\n                    background: 'white',\r\n                    borderRadius: '8px',\r\n                    borderLeft: '4px solid #2563eb',\r\n                    boxShadow: '0 1px 3px 0 rgba(0, 0, 0, 0.1)',\r\n                    border: '1px solid #e5e7eb'\r\n                  }}>\r\n                    <div style={{ display: 'flex', alignItems: 'center', marginBottom: '8px' }}>\r\n                      <span style={{ fontSize: '18px', marginRight: '8px' }}>💡</span>\r\n                      <h6 style={{ fontWeight: 'bold', color: '#1f2937', fontSize: '16px', margin: 0 }}>\r\n                        Explanation\r\n                      </h6>\r\n                    </div>\r\n\r\n                    {/* Image */}\r\n                    {(question.image || question.imageUrl) && (\r\n                      <div style={{\r\n                        marginBottom: '12px',\r\n                        padding: '8px',\r\n                        background: '#f9fafb',\r\n                        borderRadius: '6px',\r\n                        border: '1px solid #e5e7eb'\r\n                      }}>\r\n                        <div style={{ marginBottom: '4px' }}>\r\n                          <span style={{ color: '#374151', fontSize: '14px', fontWeight: '500' }}>\r\n                            📊 Reference Diagram:\r\n                          </span>\r\n                        </div>\r\n                        <div style={{ textAlign: 'center' }}>\r\n                          <img\r\n                            src={question.image || question.imageUrl}\r\n                            alt=\"Question diagram\"\r\n                            style={{\r\n                              maxWidth: '100%',\r\n                              maxHeight: '200px',\r\n                              objectFit: 'contain',\r\n                              borderRadius: '6px',\r\n                              border: '1px solid #d1d5db'\r\n                            }}\r\n                          />\r\n                        </div>\r\n                      </div>\r\n                    )}\r\n\r\n                    <div style={{\r\n                      fontSize: '14px',\r\n                      color: '#1f2937',\r\n                      lineHeight: '1.6',\r\n                      background: '#f9fafb',\r\n                      padding: '8px',\r\n                      borderRadius: '6px'\r\n                    }}>\r\n                      {explanations[questionText] ? String(explanations[questionText]) : ''}\r\n                    </div>\r\n                  </div>\r\n                )}\r\n              </div>\r\n            );\r\n          })}\r\n        </div>\r\n\r\n        {/* Navigation */}\r\n        <div style={{\r\n          display: 'flex',\r\n          flexDirection: window.innerWidth < 640 ? 'column' : 'row',\r\n          gap: '16px',\r\n          justifyContent: 'center',\r\n          alignItems: 'center'\r\n        }}>\r\n          <button\r\n            onClick={() => setView(\"result\")}\r\n            style={{\r\n              padding: '16px 32px',\r\n              background: '#4b5563',\r\n              color: 'white',\r\n              border: 'none',\r\n              borderRadius: '12px',\r\n              fontWeight: 'bold',\r\n              cursor: 'pointer',\r\n              boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'\r\n            }}\r\n          >\r\n            ← Back to Results\r\n          </button>\r\n\r\n          <button\r\n            onClick={() => {\r\n              setView(\"instructions\");\r\n              setSelectedQuestionIndex(0);\r\n              setSelectedOptions({});\r\n              setResult({});\r\n              setTimeUp(false);\r\n              setSecondsLeft(examData?.duration || 0);\r\n              setExplanations({});\r\n            }}\r\n            style={{\r\n              padding: '16px 32px',\r\n              background: '#059669',\r\n              color: 'white',\r\n              border: 'none',\r\n              borderRadius: '12px',\r\n              fontWeight: 'bold',\r\n              cursor: 'pointer',\r\n              boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'\r\n            }}\r\n          >\r\n            🔄 Retake Quiz\r\n          </button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nfunction WriteExam() {\r\n  const [examData, setExamData] = useState(null);\r\n  const [questions, setQuestions] = useState([]);\r\n  const [selectedQuestionIndex, setSelectedQuestionIndex] = useState(0);\r\n  const [selectedOptions, setSelectedOptions] = useState({});\r\n  const [result, setResult] = useState({});\r\n  const params = useParams();\r\n  const dispatch = useDispatch();\r\n  const navigate = useNavigate();\r\n  const [view, setView] = useState(\"instructions\");\r\n  const [secondsLeft, setSecondsLeft] = useState(0);\r\n  const [timeUp, setTimeUp] = useState(false);\r\n  const [intervalId, setIntervalId] = useState(null);\r\n  const [isLoading, setIsLoading] = useState(true);\r\n  const [startTime, setStartTime] = useState(null);\r\n  const { user } = useSelector((state) => state.user);\r\n\r\n  const { width, height } = useWindowSize();\r\n  const [explanations, setExplanations] = useState({});\r\n\r\n  const getExamData = useCallback(async () => {\r\n    try {\r\n      setIsLoading(true);\r\n      dispatch(ShowLoading());\r\n      console.log(\"Fetching exam data for ID:\", params.id);\r\n\r\n      const response = await getExamById({ examId: params.id });\r\n      console.log(\"Exam API Response:\", response);\r\n\r\n      dispatch(HideLoading());\r\n      setIsLoading(false);\r\n\r\n      if (response.success) {\r\n        const examData = response.data;\r\n\r\n        // Check different possible question locations\r\n        let questions = [];\r\n        if (examData?.questions && Array.isArray(examData.questions)) {\r\n          questions = examData.questions;\r\n        } else if (examData?.question && Array.isArray(examData.question)) {\r\n          questions = examData.question;\r\n        } else if (examData && Array.isArray(examData)) {\r\n          questions = examData;\r\n        }\r\n\r\n        console.log(\"Exam Data:\", examData);\r\n        console.log(\"Questions found:\", questions.length);\r\n        console.log(\"Exam Data structure:\", Object.keys(examData || {}));\r\n\r\n        setQuestions(questions);\r\n        setExamData(examData);\r\n        setSecondsLeft(examData?.duration || 0);\r\n\r\n        if (questions.length === 0) {\r\n          console.warn(\"No questions found in exam data\");\r\n          console.log(\"Full response for debugging:\", response);\r\n          message.warning(\"This exam has no questions. Please contact your instructor.\");\r\n        }\r\n      } else {\r\n        console.error(\"API Error:\", response.message);\r\n        console.log(\"Full error response:\", response);\r\n        message.error(response.message || \"Failed to load exam data\");\r\n      }\r\n    } catch (error) {\r\n      dispatch(HideLoading());\r\n      setIsLoading(false);\r\n      console.error(\"Exception in getExamData:\", error);\r\n      message.error(error.message || \"Failed to load exam. Please try again.\");\r\n    }\r\n  }, [params.id, dispatch]);\r\n\r\n  const checkFreeTextAnswers = async (payload) => {\r\n    if (!payload.length) return [];\r\n    const { data } = await chatWithChatGPTToGetAns(payload);\r\n    return data;\r\n  };\r\n\r\n  const calculateResult = useCallback(async () => {\r\n    try {\r\n      // Check if user is available\r\n      if (!user || !user._id) {\r\n        message.error(\"User not found. Please log in again.\");\r\n        navigate(\"/login\");\r\n        return;\r\n      }\r\n\r\n      dispatch(ShowLoading());\r\n\r\n      const freeTextPayload = [];\r\n      const indexMap = [];\r\n\r\n      questions.forEach((q, idx) => {\r\n        if (q.answerType === \"Free Text\" || q.answerType === \"Fill in the Blank\") {\r\n          indexMap.push(idx);\r\n          freeTextPayload.push({\r\n            question: q.name,\r\n            expectedAnswer: q.correctAnswer || q.correctOption,\r\n            userAnswer: selectedOptions[idx] || \"\",\r\n          });\r\n        }\r\n      });\r\n\r\n      const gptResults = await checkFreeTextAnswers(freeTextPayload);\r\n      const gptMap = {};\r\n\r\n      gptResults.forEach((r) => {\r\n        if (r.result && typeof r.result.isCorrect === \"boolean\") {\r\n          gptMap[r.question] = r.result;\r\n        } else if (typeof r.isCorrect === \"boolean\") {\r\n          gptMap[r.question] = { isCorrect: r.isCorrect, reason: r.reason || \"\" };\r\n        }\r\n      });\r\n\r\n      const correctAnswers = [];\r\n      const wrongAnswers = [];\r\n      const wrongPayload = [];\r\n\r\n      questions.forEach((q, idx) => {\r\n        const userAnswerKey = selectedOptions[idx] || \"\";\r\n\r\n        if (q.answerType === \"Free Text\" || q.answerType === \"Fill in the Blank\") {\r\n          const { isCorrect = false, reason = \"\" } = gptMap[q.name] || {};\r\n          const enriched = { ...q, userAnswer: userAnswerKey, reason };\r\n\r\n          if (isCorrect) {\r\n            correctAnswers.push(enriched);\r\n          } else {\r\n            wrongAnswers.push(enriched);\r\n            wrongPayload.push({\r\n              question: q.name,\r\n              expectedAnswer: q.correctAnswer || q.correctOption,\r\n              userAnswer: userAnswerKey,\r\n            });\r\n          }\r\n        } else if (q.answerType === \"Options\") {\r\n          const correctKey = q.correctOption;\r\n          const correctValue = (q.options && q.options[correctKey]) || correctKey;\r\n          const userValue = (q.options && q.options[userAnswerKey]) || userAnswerKey || \"\";\r\n\r\n          const isCorrect = correctKey === userAnswerKey;\r\n          const enriched = { ...q, userAnswer: userAnswerKey };\r\n\r\n          if (isCorrect) {\r\n            correctAnswers.push(enriched);\r\n          } else {\r\n            wrongAnswers.push(enriched);\r\n            wrongPayload.push({\r\n              question: q.name,\r\n              expectedAnswer: correctValue,\r\n              userAnswer: userValue,\r\n            });\r\n          }\r\n        }\r\n      });\r\n\r\n      // Calculate time spent\r\n      const timeSpent = startTime ? Math.floor((Date.now() - startTime) / 1000) : 0;\r\n      const totalTimeAllowed = (examData?.duration || 0) * 60; // Convert minutes to seconds\r\n\r\n      // Calculate score and points\r\n      const totalQuestions = questions.length;\r\n      const correctCount = correctAnswers.length;\r\n      const scorePercentage = Math.round((correctCount / totalQuestions) * 100);\r\n      const points = correctCount * 10; // 10 points per correct answer\r\n\r\n      // Determine pass/fail based on percentage\r\n      const passingPercentage = examData.passingMarks || 70; // Default 70%\r\n      const verdict = scorePercentage >= passingPercentage ? \"Pass\" : \"Fail\";\r\n\r\n      const tempResult = {\r\n        correctAnswers: correctAnswers || [],\r\n        wrongAnswers: wrongAnswers || [],\r\n        verdict: verdict || \"Fail\",\r\n        score: scorePercentage,\r\n        points: points,\r\n        totalQuestions: totalQuestions,\r\n        timeSpent: timeSpent,\r\n        totalTimeAllowed: totalTimeAllowed\r\n      };\r\n\r\n      setResult(tempResult);\r\n\r\n      const response = await addReport({\r\n        exam: params.id,\r\n        result: tempResult,\r\n        user: user._id,\r\n      });\r\n\r\n      if (response.success) {\r\n        // Include XP data in the result\r\n        const resultWithXP = {\r\n          ...tempResult,\r\n          xpData: response.xpData\r\n        };\r\n        setResult(resultWithXP);\r\n\r\n        setView(\"result\");\r\n        window.scrollTo(0, 0);\r\n        new Audio(verdict === \"Pass\" ? PassSound : FailSound).play();\r\n      } else {\r\n        message.error(response.message);\r\n      }\r\n      dispatch(HideLoading());\r\n\r\n    } catch (error) {\r\n      dispatch(HideLoading());\r\n      message.error(error.message);\r\n    }\r\n  }, [questions, selectedOptions, examData, params.id, user, navigate, dispatch]);\r\n\r\n  const fetchExplanation = async (question, expectedAnswer, userAnswer, imageUrl) => {\r\n    try {\r\n      dispatch(ShowLoading());\r\n      const response = await chatWithChatGPTToExplainAns({ question, expectedAnswer, userAnswer, imageUrl });\r\n      dispatch(HideLoading());\r\n\r\n      if (response.success) {\r\n        setExplanations((prev) => ({ ...prev, [question]: response.explanation }));\r\n      } else {\r\n        message.error(response.error || \"Failed to fetch explanation.\");\r\n      }\r\n    } catch (error) {\r\n      dispatch(HideLoading());\r\n      message.error(error.message);\r\n    }\r\n  };\r\n\r\n  const startTimer = () => {\r\n    const totalSeconds = examData?.duration || 0; // Duration is already in seconds\r\n    setSecondsLeft(totalSeconds);\r\n    setStartTime(Date.now()); // Record start time for XP calculation\r\n\r\n    const newIntervalId = setInterval(() => {\r\n      setSecondsLeft((prevSeconds) => {\r\n        if (prevSeconds > 0) {\r\n          return prevSeconds - 1;\r\n        } else {\r\n          setTimeUp(true);\r\n          return 0;\r\n        }\r\n      });\r\n    }, 1000);\r\n    setIntervalId(newIntervalId);\r\n  };\r\n\r\n  useEffect(() => {\r\n    if (timeUp && view === \"questions\") {\r\n      clearInterval(intervalId);\r\n      calculateResult();\r\n    }\r\n  }, [timeUp, view, intervalId, calculateResult]);\r\n\r\n  useEffect(() => {\r\n    console.log(\"WriteExam useEffect - params.id:\", params.id);\r\n    if (params.id) {\r\n      getExamData();\r\n    } else {\r\n      console.error(\"No exam ID provided in URL parameters\");\r\n      message.error(\"Invalid exam ID. Please select a quiz from the list.\");\r\n      navigate('/user/quiz');\r\n    }\r\n  }, [params.id, getExamData, navigate]);\r\n\r\n  useEffect(() => {\r\n    return () => {\r\n      if (intervalId) {\r\n        clearInterval(intervalId);\r\n      }\r\n    };\r\n  }, [intervalId]);\r\n\r\n  // Add fullscreen class for all quiz views (instructions, questions, results)\r\n  useEffect(() => {\r\n    if (view === \"instructions\" || view === \"questions\" || view === \"result\") {\r\n      document.body.classList.add(\"quiz-fullscreen\");\r\n    } else {\r\n      document.body.classList.remove(\"quiz-fullscreen\");\r\n    }\r\n\r\n    // Cleanup on unmount\r\n    return () => {\r\n      document.body.classList.remove(\"quiz-fullscreen\");\r\n    };\r\n  }, [view]);\r\n\r\n  // Repair function for fixing orphaned questions\r\n  const repairExamQuestions = async () => {\r\n    try {\r\n      dispatch(ShowLoading());\r\n      const response = await fetch('/api/exams/repair-exam-questions', {\r\n        method: 'POST',\r\n        headers: {\r\n          'Content-Type': 'application/json',\r\n          'Authorization': `Bearer ${localStorage.getItem('token')}`\r\n        },\r\n        body: JSON.stringify({ examId: params.id })\r\n      });\r\n\r\n      const data = await response.json();\r\n      if (data.success) {\r\n        message.success(data.message);\r\n        // Reload the exam data\r\n        getExamData();\r\n      } else {\r\n        message.error(data.message);\r\n      }\r\n    } catch (error) {\r\n      message.error(\"Failed to repair exam questions\");\r\n    } finally {\r\n      dispatch(HideLoading());\r\n    }\r\n  };\r\n\r\n  // Check if user is authenticated\r\n  if (!user) {\r\n    return (\r\n      <div className=\"min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50 flex justify-center items-center\">\r\n        <div className=\"bg-white/90 backdrop-blur-sm rounded-2xl shadow-2xl border border-blue-100 p-12 text-center max-w-md mx-4\">\r\n          <div className=\"w-20 h-20 bg-blue-600 rounded-full flex items-center justify-center mx-auto mb-6\">\r\n            <svg className=\"w-10 h-10 text-white\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\r\n              <path fillRule=\"evenodd\" d=\"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-6-3a2 2 0 11-4 0 2 2 0 014 0zm-2 4a5 5 0 00-4.546 2.916A5.986 5.986 0 0010 16a5.986 5.986 0 004.546-2.084A5 5 0 0010 11z\" clipRule=\"evenodd\" />\r\n            </svg>\r\n          </div>\r\n          <h2 className=\"text-2xl font-bold text-gray-900 mb-4\">Authentication Required</h2>\r\n          <p className=\"text-gray-600 mb-8\">Please log in to access the exam and start your learning journey.</p>\r\n          <button\r\n            className=\"w-full px-6 py-4 bg-gradient-to-r from-blue-600 to-indigo-600 text-white rounded-xl font-semibold text-lg hover:from-blue-700 hover:to-indigo-700 transform hover:scale-105 transition-all duration-300 shadow-lg\"\r\n            onClick={() => navigate(\"/login\")}\r\n          >\r\n            Go to Login\r\n          </button>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return examData ? (\r\n    <div className=\"min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50\">\r\n\r\n      {view === \"instructions\" && (\r\n        <Instructions\r\n          examData={examData}\r\n          setView={setView}\r\n          startTimer={startTimer}\r\n          questions={questions}\r\n        />\r\n      )}\r\n\r\n      {view === \"questions\" && (\r\n        isLoading ? (\r\n          <div className=\"min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50 flex items-center justify-center\">\r\n            <div className=\"bg-white/90 backdrop-blur-sm rounded-3xl p-12 shadow-2xl border border-blue-200 max-w-lg mx-4 text-center\">\r\n              <div className=\"w-24 h-24 bg-gradient-to-r from-blue-500 to-indigo-500 rounded-full flex items-center justify-center mx-auto mb-6 shadow-lg animate-pulse\">\r\n                <svg className=\"w-12 h-12 text-white animate-spin\" fill=\"none\" viewBox=\"0 0 24 24\">\r\n                  <circle className=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" strokeWidth=\"4\"></circle>\r\n                  <path className=\"opacity-75\" fill=\"currentColor\" d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"></path>\r\n                </svg>\r\n              </div>\r\n              <h3 className=\"text-2xl font-bold text-blue-800 mb-4\">Loading Quiz...</h3>\r\n              <p className=\"text-blue-600 text-lg\">\r\n                Please wait while we prepare your questions.\r\n              </p>\r\n            </div>\r\n          </div>\r\n        ) : questions.length === 0 ? (\r\n          <div className=\"min-h-screen bg-gradient-to-br from-amber-50 via-white to-orange-50 flex items-center justify-center\">\r\n            <div className=\"bg-white/90 backdrop-blur-sm rounded-3xl p-12 shadow-2xl border border-amber-200 max-w-lg mx-4 text-center\">\r\n              <div className=\"w-24 h-24 bg-gradient-to-r from-amber-500 to-orange-500 rounded-full flex items-center justify-center mx-auto mb-6 shadow-lg\">\r\n                <svg className=\"w-12 h-12 text-white\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\r\n                  <path fillRule=\"evenodd\" d=\"M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z\" clipRule=\"evenodd\" />\r\n                </svg>\r\n              </div>\r\n              <h3 className=\"text-2xl font-bold text-amber-800 mb-4\">No Questions Found</h3>\r\n              <p className=\"text-amber-700 mb-6 text-lg leading-relaxed\">\r\n                This exam appears to have no questions. This could be due to:\r\n              </p>\r\n              <ul className=\"text-left text-amber-700 mb-8 space-y-2\">\r\n                <li>• Questions not properly linked to this exam</li>\r\n                <li>• Database connection issues</li>\r\n                <li>• Exam configuration problems</li>\r\n              </ul>\r\n              <div className=\"space-y-3\">\r\n                <button\r\n                  onClick={repairExamQuestions}\r\n                  className=\"w-full px-8 py-4 bg-gradient-to-r from-amber-500 to-orange-500 text-white rounded-xl font-bold text-lg hover:from-amber-600 hover:to-orange-600 transform hover:scale-105 transition-all duration-300 shadow-lg\"\r\n                >\r\n                  🔧 Repair Questions\r\n                </button>\r\n                <button\r\n                  onClick={() => {\r\n                    console.log(\"Retrying exam data fetch...\");\r\n                    getExamData();\r\n                  }}\r\n                  className=\"w-full px-8 py-4 bg-gradient-to-r from-blue-500 to-blue-600 text-white rounded-xl font-bold text-lg hover:from-blue-600 hover:to-blue-700 transform hover:scale-105 transition-all duration-300 shadow-lg\"\r\n                >\r\n                  🔄 Retry Loading\r\n                </button>\r\n                <button\r\n                  onClick={() => navigate('/user/quiz')}\r\n                  className=\"w-full px-8 py-4 bg-gradient-to-r from-gray-500 to-gray-600 text-white rounded-xl font-bold text-lg hover:from-gray-600 hover:to-gray-700 transform hover:scale-105 transition-all duration-300 shadow-lg\"\r\n                >\r\n                  ← Back to Quiz List\r\n                </button>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        ) : (\r\n          <MinimalQuizRenderer\r\n            question={questions[selectedQuestionIndex]}\r\n            questionIndex={selectedQuestionIndex}\r\n            totalQuestions={questions.length}\r\n            selectedAnswer={selectedOptions[selectedQuestionIndex]}\r\n            onAnswerSelect={(answer) => setSelectedOptions({...selectedOptions, [selectedQuestionIndex]: answer})}\r\n            onNext={() => {\r\n              if (selectedQuestionIndex === questions.length - 1) {\r\n                calculateResult();\r\n              } else {\r\n                setSelectedQuestionIndex(selectedQuestionIndex + 1);\r\n              }\r\n            }}\r\n            onPrevious={() => {\r\n              if (selectedQuestionIndex > 0) {\r\n                setSelectedQuestionIndex(selectedQuestionIndex - 1);\r\n              }\r\n            }}\r\n            timeLeft={secondsLeft}\r\n            examTitle={examData?.name || \"Quiz\"}\r\n          />\r\n        )\r\n      )}\r\n\r\n      {view === \"result\" && (\r\n        <div className=\"min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 py-8\">\r\n          {result.verdict === \"Pass\" && <Confetti width={width} height={height} />}\r\n\r\n          <div className=\"max-w-4xl mx-auto px-4\">\r\n            <div className=\"bg-white/95 backdrop-blur-md rounded-2xl shadow-xl border border-slate-200/50 overflow-hidden\">\r\n              {/* Modern Header */}\r\n              <div className={`px-8 py-10 text-center relative ${\r\n                result.verdict === \"Pass\"\r\n                  ? \"bg-gradient-to-br from-emerald-500/10 via-green-500/5 to-teal-500/10\"\r\n                  : \"bg-gradient-to-br from-amber-500/10 via-orange-500/5 to-red-500/10\"\r\n              }`}>\r\n                <div className=\"relative\">\r\n                  <div className={`w-20 h-20 mx-auto mb-6 rounded-2xl flex items-center justify-center shadow-lg ${\r\n                    result.verdict === \"Pass\"\r\n                      ? \"bg-gradient-to-br from-emerald-500 to-green-600\"\r\n                      : \"bg-gradient-to-br from-amber-500 to-orange-600\"\r\n                  }`}>\r\n                    <img\r\n                      src={result.verdict === \"Pass\" ? Pass : Fail}\r\n                      alt={result.verdict}\r\n                      className=\"w-12 h-12 object-contain\"\r\n                    />\r\n                  </div>\r\n                  <h1 className={`text-4xl font-black mb-4 tracking-tight ${\r\n                    result.verdict === \"Pass\" ? \"text-emerald-700\" : \"text-amber-700\"\r\n                  }`}>\r\n                    {result.verdict === \"Pass\" ? \"Excellent Work!\" : \"Keep Pushing!\"}\r\n                  </h1>\r\n                  <p className=\"text-xl text-slate-600 font-medium max-w-md mx-auto leading-relaxed\">\r\n                    {result.verdict === \"Pass\"\r\n                      ? \"You've mastered this exam with flying colors!\"\r\n                      : \"Every challenge makes you stronger. Try again!\"}\r\n                  </p>\r\n                </div>\r\n              </div>\r\n\r\n              {/* Modern Statistics Cards */}\r\n              <div className=\"p-8\">\r\n                <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6 mb-8\">\r\n                  {/* Score Card */}\r\n                  <div className=\"group relative overflow-hidden bg-gradient-to-br from-blue-500/5 to-indigo-500/10 rounded-2xl border border-blue-200/50 p-6 hover:shadow-lg transition-all duration-300\">\r\n                    <div className=\"absolute inset-0 bg-gradient-to-br from-blue-500/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300\"></div>\r\n                    <div className=\"relative text-center\">\r\n                      <div className=\"text-4xl font-black text-blue-600 mb-2 tracking-tight\">\r\n                        {Math.round(((result.correctAnswers?.length || 0) / questions.length) * 100)}%\r\n                      </div>\r\n                      <div className=\"text-sm font-bold text-blue-700/80 uppercase tracking-wider\">Your Score</div>\r\n                    </div>\r\n                  </div>\r\n\r\n                  {/* Correct vs Total */}\r\n                  <div className=\"group relative overflow-hidden bg-gradient-to-br from-emerald-500/5 to-green-500/10 rounded-2xl border border-emerald-200/50 p-6 hover:shadow-lg transition-all duration-300\">\r\n                    <div className=\"absolute inset-0 bg-gradient-to-br from-emerald-500/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300\"></div>\r\n                    <div className=\"relative text-center\">\r\n                      <div className=\"text-4xl font-black text-emerald-600 mb-2 tracking-tight\">\r\n                        {Array.isArray(result.correctAnswers) ? result.correctAnswers.length : 0}/{questions.length}\r\n                      </div>\r\n                      <div className=\"text-sm font-bold text-emerald-700/80 uppercase tracking-wider\">Correct</div>\r\n                    </div>\r\n                  </div>\r\n\r\n                  {/* Pass Status */}\r\n                  <div className={`group relative overflow-hidden rounded-2xl border p-6 hover:shadow-lg transition-all duration-300 ${\r\n                    result.verdict === \"Pass\"\r\n                      ? \"bg-gradient-to-br from-emerald-500/5 to-green-500/10 border-emerald-200/50\"\r\n                      : \"bg-gradient-to-br from-amber-500/5 to-orange-500/10 border-amber-200/50\"\r\n                  }`}>\r\n                    <div className={`absolute inset-0 bg-gradient-to-br opacity-0 group-hover:opacity-100 transition-opacity duration-300 ${\r\n                      result.verdict === \"Pass\" ? \"from-emerald-500/5\" : \"from-amber-500/5\"\r\n                    } to-transparent`}></div>\r\n                    <div className=\"relative text-center\">\r\n                      <div className={`text-4xl font-black mb-2 tracking-tight ${\r\n                        result.verdict === \"Pass\" ? \"text-emerald-600\" : \"text-amber-600\"\r\n                      }`}>\r\n                        {result.verdict === \"Pass\" ? \"PASS\" : \"RETRY\"}\r\n                      </div>\r\n                      <div className={`text-sm font-bold uppercase tracking-wider ${\r\n                        result.verdict === \"Pass\" ? \"text-emerald-700/80\" : \"text-amber-700/80\"\r\n                      }`}>\r\n                        {result.verdict === \"Pass\" ? \"Success!\" : `Need ${examData.passingMarks}`}\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n\r\n                {/* Modern Progress Visualization */}\r\n                <div className=\"mb-8\">\r\n                  <div className=\"relative bg-slate-100 rounded-2xl p-6\">\r\n                    <div className=\"text-center mb-4\">\r\n                      <h3 className=\"text-lg font-bold text-slate-700 mb-1\">Performance Overview</h3>\r\n                      <p className=\"text-sm text-slate-500\">Your achievement level</p>\r\n                    </div>\r\n                    <div className=\"relative\">\r\n                      <div className=\"w-full bg-slate-200 rounded-full h-4 shadow-inner overflow-hidden\">\r\n                        <div\r\n                          className={`h-full rounded-full transition-all duration-1000 shadow-sm relative overflow-hidden ${\r\n                            result.verdict === \"Pass\"\r\n                              ? \"bg-gradient-to-r from-emerald-500 via-green-500 to-teal-500\"\r\n                              : \"bg-gradient-to-r from-amber-500 via-orange-500 to-red-500\"\r\n                          }`}\r\n                          style={{ width: `${((result.correctAnswers?.length || 0) / questions.length) * 100}%` }}\r\n                        >\r\n                          <div className=\"absolute inset-0 bg-gradient-to-r from-white/20 to-transparent\"></div>\r\n                        </div>\r\n                      </div>\r\n                      <div className=\"flex justify-between items-center mt-3\">\r\n                        <span className=\"text-xs font-medium text-slate-500\">0%</span>\r\n                        <span className={`text-lg font-black tracking-tight ${\r\n                          result.verdict === \"Pass\" ? \"text-emerald-600\" : \"text-amber-600\"\r\n                        }`}>\r\n                          {Math.round(((Array.isArray(result.correctAnswers) ? result.correctAnswers.length : 0) / questions.length) * 100)}%\r\n                        </span>\r\n                        <span className=\"text-xs font-medium text-slate-500\">100%</span>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n\r\n                {/* XP Display */}\r\n                {result.xpData && (\r\n                  <div className=\"mb-8\">\r\n                    <XPResultDisplay xpData={result.xpData} />\r\n                  </div>\r\n                )}\r\n\r\n                {/* Modern Action Buttons */}\r\n                <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\r\n                  <button\r\n                    className=\"group relative overflow-hidden px-8 py-4 bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white rounded-xl font-bold transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5\"\r\n                    onClick={() => setView(\"review\")}\r\n                  >\r\n                    <div className=\"absolute inset-0 bg-gradient-to-r from-white/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300\"></div>\r\n                    <span className=\"relative\">Review Answers</span>\r\n                  </button>\r\n\r\n\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      )}\r\n\r\n      {view === \"review\" && (\r\n        <MinimalReviewRenderer\r\n          questions={questions}\r\n          selectedOptions={selectedOptions}\r\n          explanations={explanations}\r\n          fetchExplanation={fetchExplanation}\r\n          setView={setView}\r\n          examData={examData}\r\n          setSelectedQuestionIndex={setSelectedQuestionIndex}\r\n          setSelectedOptions={setSelectedOptions}\r\n          setResult={setResult}\r\n          setTimeUp={setTimeUp}\r\n          setSecondsLeft={setSecondsLeft}\r\n          setExplanations={setExplanations}\r\n        />\r\n      )}\r\n    </div>\r\n  ) : null;\r\n}\r\n\r\nexport default WriteExam;"], "names": ["default", "axiosInstance", "require", "addReport", "async", "post", "payload", "data", "error", "response", "getAllReports", "filters", "getAllReportsByUser", "getAllReportsForRanking", "get", "getXPLeaderboard", "options", "arguments", "length", "undefined", "params", "URLSearchParams", "limit", "append", "classFilter", "levelFilter", "seasonFilter", "includeInactive", "concat", "toString", "getUserRanking", "userId", "context", "_ref", "examData", "<PERSON><PERSON><PERSON><PERSON>", "startTimer", "questions", "navigate", "useNavigate", "_jsxs", "className", "children", "_jsx", "name", "_id", "Math", "round", "duration", "totalMarks", "onClick", "console", "log", "alert", "disabled", "xpData", "showBreakdown", "setShowBreakdown", "useState", "animatedXP", "setAnimatedXP", "useEffect", "xpAwarded", "steps", "increment", "current", "timer", "setInterval", "clearInterval", "floor", "levelUp", "playLevelUpSound", "audioContext", "window", "AudioContext", "createLevelUpSound", "for<PERSON>ach", "chord", "index", "setTimeout", "frequency", "oscillator", "createOscillator", "gainNode", "createGain", "connect", "destination", "setValueAtTime", "currentTime", "type", "gain", "linearRampToValueAtTime", "exponentialRampToValueAtTime", "start", "stop", "freq", "i", "osc", "xpBreakdown", "newLevel", "newTotalXP", "currentStreak", "achievements", "jsx", "AnimatePresence", "motion", "div", "initial", "opacity", "scale", "y", "rotateY", "animate", "transition", "ease", "times", "exit", "style", "background", "boxShadow", "animation", "x", "repeat", "Infinity", "Array", "map", "_", "left", "top", "TbTrophy", "span", "textShadow", "WebkitBackgroundClip", "WebkitTextFillColor", "p", "delay", "TbBolt", "stiffness", "damping", "toLocaleString", "TbFlame", "TbMedal", "Object", "keys", "TbChevronUp", "TbChevronDown", "height", "baseXP", "TbTarget", "difficultyBonus", "TbTrendingUp", "speedBonus", "TbClock", "perfectScoreBonus", "TbStar", "streakBonus", "firstAttemptBonus", "achievement", "String", "description", "MinimalQuiz<PERSON><PERSON><PERSON>", "question", "questionIndex", "totalQuestions", "<PERSON><PERSON><PERSON><PERSON>", "onAnswerSelect", "onNext", "onPrevious", "timeLeft", "examTitle", "questionText", "answerType", "isArray", "opt", "values", "minHeight", "borderBottom", "padding", "position", "zIndex", "max<PERSON><PERSON><PERSON>", "margin", "display", "justifyContent", "alignItems", "fontSize", "fontWeight", "color", "borderRadius", "fontFamily", "seconds", "secs", "formatTime", "marginBottom", "lineHeight", "image", "imageUrl", "textAlign", "src", "alt", "maxHeight", "objectFit", "border", "flexDirection", "gap", "option", "letter", "fromCharCode", "isSelected", "width", "cursor", "flex", "value", "onChange", "e", "target", "placeholder", "outline", "overflow", "MinimalReview<PERSON><PERSON><PERSON>", "_ref2", "selectedOptions", "explanations", "fetchExplanation", "setSelectedQuestionIndex", "setSelectedOptions", "setResult", "setTimeUp", "setSecondsLeft", "setExplanations", "userAnswer", "isCorrect", "correctAnswerText", "userAnswerText", "correctOption", "correctOpt", "userOpt", "<PERSON><PERSON><PERSON><PERSON>", "flexShrink", "marginTop", "marginLeft", "borderLeft", "marginRight", "innerWidth", "_result$correctAnswer", "_result$correctAnswer2", "setExamData", "setQuestions", "selectedQuestionIndex", "result", "useParams", "dispatch", "useDispatch", "view", "secondsLeft", "timeUp", "intervalId", "setIntervalId", "isLoading", "setIsLoading", "startTime", "setStartTime", "user", "useSelector", "state", "useWindowSize", "getExamData", "useCallback", "ShowLoading", "id", "getExamById", "examId", "HideLoading", "success", "warn", "message", "warning", "calculateResult", "freeTextPayload", "indexMap", "q", "idx", "push", "expectedAnswer", "gptResults", "chatWithChatGPTToGetAns", "checkFreeTextAnswers", "gptMap", "r", "reason", "correctAnswers", "wrongAnswers", "wrongPayload", "userAnswerKey", "enriched", "_objectSpread", "<PERSON><PERSON><PERSON>", "correctValue", "userValue", "timeSpent", "Date", "now", "totalTimeAllowed", "correctCount", "scorePercentage", "points", "verdict", "passingMarks", "tempResult", "score", "exam", "resultWithXP", "scrollTo", "Audio", "PassSound", "FailSound", "play", "document", "body", "classList", "add", "remove", "Instructions", "totalSeconds", "newIntervalId", "prevSeconds", "fill", "viewBox", "cx", "cy", "stroke", "strokeWidth", "d", "fillRule", "clipRule", "fetch", "method", "headers", "localStorage", "getItem", "JSON", "stringify", "json", "answer", "Confetti", "Pass", "XPResultDisplay", "chatWithChatGPTToExplainAns", "prev", "explanation"], "sourceRoot": ""}