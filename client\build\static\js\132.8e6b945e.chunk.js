"use strict";(self.webpackChunkclient=self.webpackChunkclient||[]).push([[132],{9941:(e,s,a)=>{a.d(s,{q:()=>n});const{default:i}=a(3371),n=async e=>{try{return(await i.get("/api/plans",e)).data}catch(s){return s.response.data}}},8155:(e,s,a)=>{a.d(s,{Z:()=>v});var i=a(2791),n=a(6275),t=a(5273),r=a(7309),l=a(7455),c=a(7462);const o={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M699 353h-46.9c-10.2 0-19.9 4.9-25.9 13.3L469 584.3l-71.2-98.8c-6-8.3-15.6-13.3-25.9-13.3H325c-6.5 0-10.3 7.4-6.5 12.7l124.6 172.8a31.8 31.8 0 0051.7 0l210.6-292c3.9-5.3.1-12.7-6.4-12.7z"}},{tag:"path",attrs:{d:"M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372z"}}]},name:"check-circle",theme:"outlined"};var d=a(4291),u=function(e,s){return i.createElement(d.Z,(0,c.Z)({},e,{ref:s,icon:o}))};const h=i.forwardRef(u);var m=a(9168),p=a(50),x=a(184);const v=e=>{var s;let{visible:a,onClose:i,currentPlan:c,subscription:o,user:d}=e;const u=(()=>{if(null===o||void 0===o||!o.endDate)return 0;const e=new Date(o.endDate)-new Date,s=Math.ceil(e/864e5);return Math.max(0,s)})(),v=(()=>{if(null===o||void 0===o||!o.startDate||null===o||void 0===o||!o.endDate)return 0;const e=new Date(o.startDate),s=(new Date(o.endDate)-e)/864e5,a=(new Date-e)/864e5;return Math.min(100,Math.max(0,a/s*100))})(),j=(null===c||void 0===c?void 0:c.title)||(null===o||void 0===o?void 0:o.planTitle)||"Premium Plan",g=null!==o&&void 0!==o&&o.endDate?new Date(o.endDate).toLocaleDateString():"N/A";return(0,x.jsx)(n.Z,{open:a,onCancel:i,footer:null,width:500,centered:!0,className:"upgrade-restriction-modal",maskStyle:{backgroundColor:"rgba(0, 0, 0, 0.7)"},children:(0,x.jsxs)("div",{className:"upgrade-restriction-content",children:[(0,x.jsxs)("div",{className:"modal-header",children:[(0,x.jsx)("div",{className:"crown-icon",children:(0,x.jsx)(l.Z,{})}),(0,x.jsx)("h2",{className:"modal-title",children:"Already Premium Member!"}),(0,x.jsx)("p",{className:"modal-subtitle",children:"You're currently enjoying premium features"})]}),(0,x.jsxs)("div",{className:"current-plan-card",children:[(0,x.jsxs)("div",{className:"plan-header",children:[(0,x.jsx)("div",{className:"plan-icon",children:(0,x.jsx)(h,{})}),(0,x.jsxs)("div",{className:"plan-info",children:[(0,x.jsx)("h3",{className:"plan-name",children:j}),(0,x.jsx)("span",{className:"plan-status",children:"Active Subscription"})]})]}),(0,x.jsxs)("div",{className:"progress-section",children:[(0,x.jsxs)("div",{className:"progress-header",children:[(0,x.jsx)("span",{className:"progress-label",children:"Subscription Progress"}),(0,x.jsxs)("span",{className:"days-remaining",children:[u," days remaining"]})]}),(0,x.jsx)(t.Z,{percent:v,strokeColor:{"0%":"#52c41a","50%":"#faad14","100%":"#ff4d4f"},trailColor:"#f0f0f0",strokeWidth:8,showInfo:!1})]}),(0,x.jsxs)("div",{className:"subscription-details",children:[(0,x.jsxs)("div",{className:"detail-item",children:[(0,x.jsx)(m.Z,{className:"detail-icon"}),(0,x.jsxs)("div",{className:"detail-content",children:[(0,x.jsx)("span",{className:"detail-label",children:"Expires On"}),(0,x.jsx)("span",{className:"detail-value",children:g})]})]}),(0,x.jsxs)("div",{className:"detail-item",children:[(0,x.jsx)(p.Z,{className:"detail-icon"}),(0,x.jsxs)("div",{className:"detail-content",children:[(0,x.jsx)("span",{className:"detail-label",children:"Time Remaining"}),(0,x.jsx)("span",{className:"detail-value",children:u>0?"".concat(u," days"):"Expired"})]})]})]})]}),(0,x.jsxs)("div",{className:"message-section",children:[(0,x.jsxs)("div",{className:"message-card",children:[(0,x.jsx)("h4",{className:"message-title",children:"\ud83c\udf89 You're All Set!"}),(0,x.jsxs)("p",{className:"message-text",children:["You're currently enjoying all premium features with your ",(0,x.jsx)("strong",{children:j}),". To upgrade to a different plan, please wait until your current subscription expires."]})]}),(0,x.jsxs)("div",{className:"benefits-list",children:[(0,x.jsx)("h5",{className:"benefits-title",children:"Your Current Benefits:"}),(0,x.jsx)("ul",{className:"benefits",children:(null===c||void 0===c||null===(s=c.features)||void 0===s?void 0:s.slice(0,4).map(((e,s)=>(0,x.jsxs)("li",{className:"benefit-item",children:[(0,x.jsx)(h,{className:"benefit-icon"}),e]},s))))||[(0,x.jsxs)("li",{className:"benefit-item",children:[(0,x.jsx)(h,{className:"benefit-icon"}),"Full access to all features"]},"1"),(0,x.jsxs)("li",{className:"benefit-item",children:[(0,x.jsx)(h,{className:"benefit-icon"}),"Unlimited quizzes and practice"]},"2"),(0,x.jsxs)("li",{className:"benefit-item",children:[(0,x.jsx)(h,{className:"benefit-icon"}),"AI chat assistance"]},"3"),(0,x.jsxs)("li",{className:"benefit-item",children:[(0,x.jsx)(h,{className:"benefit-icon"}),"Premium study materials"]},"4")]})]})]}),(0,x.jsxs)("div",{className:"action-buttons",children:[(0,x.jsx)(r.ZP,{type:"primary",size:"large",onClick:i,className:"continue-button",children:"Continue Learning"}),(0,x.jsx)(r.ZP,{type:"default",size:"large",onClick:i,className:"close-button",children:"Close"})]}),(0,x.jsx)("div",{className:"footer-note",children:(0,x.jsxs)("p",{children:["\ud83d\udca1 ",(0,x.jsx)("strong",{children:"Tip:"})," You can upgrade to a different plan after your current subscription expires on ",(0,x.jsx)("strong",{children:g})]})})]})})}},7132:(e,s,a)=>{a.r(s),a.d(s,{default:()=>Z});var i=a(2791),n=a(9434),t=a(6042),r=a(7027),l=a(2202),c=a(9941),o=a(1169),d=(a(8247),a(8155)),u=a(6275),h=a(5273),m=a(7309),p=a(7462);const x={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372z"}},{tag:"path",attrs:{d:"M464 688a48 48 0 1096 0 48 48 0 10-96 0zm24-112h48c4.4 0 8-3.6 8-8V296c0-4.4-3.6-8-8-8h-48c-4.4 0-8 3.6-8 8v272c0 4.4 3.6 8 8 8z"}}]},name:"exclamation-circle",theme:"outlined"};var v=a(4291),j=function(e,s){return i.createElement(v.Z,(0,p.Z)({},e,{ref:s,icon:x}))};const g=i.forwardRef(j);var f=a(50),N=a(9168);const y={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M864 736c0-111.6-65.4-208-160-252.9V317.3c0-15.1-5.3-29.7-15.1-41.2L536.5 95.4C530.1 87.8 521 84 512 84s-18.1 3.8-24.5 11.4L335.1 276.1a63.97 63.97 0 00-15.1 41.2v165.8C225.4 528 160 624.4 160 736h156.5c-2.3 7.2-3.5 15-3.5 23.8 0 22.1 7.6 43.7 21.4 60.8a97.2 97.2 0 0043.1 30.6c23.1 54 75.6 88.8 134.5 88.8 29.1 0 57.3-8.6 81.4-24.8 23.6-15.8 41.9-37.9 53-64a97 97 0 0043.1-30.5 97.52 97.52 0 0021.4-60.8c0-8.4-1.1-16.4-3.1-23.8H864zM762.3 621.4c9.4 14.6 17 30.3 22.5 46.6H700V558.7a211.6 211.6 0 0162.3 62.7zM388 483.1V318.8l124-147 124 147V668H388V483.1zM239.2 668c5.5-16.3 13.1-32 22.5-46.6 16.3-25.2 37.5-46.5 62.3-62.7V668h-84.8zm388.9 116.2c-5.2 3-11.2 4.2-17.1 3.4l-19.5-2.4-2.8 19.4c-5.4 37.9-38.4 66.5-76.7 66.5-38.3 0-71.3-28.6-76.7-66.5l-2.8-19.5-19.5 2.5a27.7 27.7 0 01-17.1-3.5c-8.7-5-14.1-14.3-14.1-24.4 0-10.6 5.9-19.4 14.6-23.8h231.3c8.8 4.5 14.6 13.3 14.6 23.8-.1 10.2-5.5 19.6-14.2 24.5zM464 400a48 48 0 1096 0 48 48 0 10-96 0z"}}]},name:"rocket",theme:"outlined"};var b=function(e,s){return i.createElement(v.Z,(0,p.Z)({},e,{ref:s,icon:y}))};const k=i.forwardRef(b);var w=a(7455);const P={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M908.1 353.1l-253.9-36.9L540.7 86.1c-3.1-6.3-8.2-11.4-14.5-14.5-15.8-7.8-35-1.3-42.9 14.5L369.8 316.2l-253.9 36.9c-7 1-13.4 4.3-18.3 9.3a32.05 32.05 0 00.6 45.3l183.7 179.1-43.4 252.9a31.95 31.95 0 0046.4 33.7L512 754l227.1 119.4c6.2 3.3 13.4 4.4 20.3 3.2 17.4-3 29.1-19.5 26.1-36.9l-43.4-252.9 183.7-179.1c5-4.9 8.3-11.3 9.3-18.3 2.7-17.5-9.5-33.7-27-36.3zM664.8 561.6l36.1 210.3L512 672.7 323.1 772l36.1-210.3-152.8-149L417.6 382 512 190.7 606.4 382l211.2 30.7-152.8 148.9z"}}]},name:"star",theme:"outlined"};var C=function(e,s){return i.createElement(v.Z,(0,p.Z)({},e,{ref:s,icon:P}))};const L=i.forwardRef(C);var S=a(184);const M=e=>{let{visible:s,onClose:a,onRenew:n,subscription:t,user:r,plans:l=[]}=e;const[c,o]=(0,i.useState)(null),[d,p]=(0,i.useState)(!1),x=(()=>{if(null===t||void 0===t||!t.endDate)return 0;const e=new Date(t.endDate),s=new Date-e,a=Math.ceil(s/864e5);return Math.max(0,a)})(),v=(null===t||void 0===t?void 0:t.planTitle)||"Premium Plan",j=null!==t&&void 0!==t&&t.endDate?new Date(t.endDate).toLocaleDateString():"N/A",y=l.length>0?l:[{_id:"premium-plan",title:"Premium Plan",discountedPrice:13e3,actualPrice:3e4,duration:3,features:["3-months full access","Unlimited quizzes","AI chat assistance","Premium study materials","Priority support"]}];return(0,S.jsx)(u.Z,{open:s,onCancel:a,footer:null,width:600,centered:!0,className:"subscription-expired-modal",maskStyle:{backgroundColor:"rgba(0, 0, 0, 0.8)"},closable:!1,children:(0,S.jsx)("div",{className:"expired-modal-content",children:d?(0,S.jsxs)("div",{className:"plan-selection",children:[(0,S.jsxs)("div",{className:"modal-header",children:[(0,S.jsx)("div",{className:"crown-icon",children:(0,S.jsx)(w.Z,{})}),(0,S.jsx)("h2",{className:"modal-title",children:"Choose Your Plan"}),(0,S.jsx)("p",{className:"modal-subtitle",children:"Select a plan to continue your learning journey"})]}),(0,S.jsx)("div",{className:"plans-grid",children:y.map((e=>{var s,a,i,n;return(0,S.jsxs)("div",{className:"plan-card ".concat((null===c||void 0===c?void 0:c._id)===e._id?"selected":""),onClick:()=>(e=>{o(e)})(e),children:[(0,S.jsxs)("div",{className:"plan-header",children:[(0,S.jsx)("h3",{className:"plan-title",children:e.title}),(null===(s=e.title)||void 0===s?void 0:s.toLowerCase().includes("standard"))&&(0,S.jsx)("span",{className:"plan-badge",children:"\ud83d\udd25 Popular"})]}),(0,S.jsxs)("div",{className:"plan-pricing",children:[(0,S.jsxs)("div",{className:"price-main",children:[(0,S.jsx)("span",{className:"currency",children:"TZS"}),(0,S.jsx)("span",{className:"amount",children:null===(a=e.discountedPrice)||void 0===a?void 0:a.toLocaleString()})]}),e.actualPrice>e.discountedPrice&&(0,S.jsxs)("div",{className:"price-original",children:[(0,S.jsxs)("span",{className:"original-price",children:["TZS ",null===(i=e.actualPrice)||void 0===i?void 0:i.toLocaleString()]}),(0,S.jsxs)("span",{className:"discount",children:[Math.round((e.actualPrice-e.discountedPrice)/e.actualPrice*100),"% OFF"]})]}),(0,S.jsxs)("div",{className:"duration",children:[e.duration," month",e.duration>1?"s":""]})]}),(0,S.jsx)("div",{className:"plan-features",children:null===(n=e.features)||void 0===n?void 0:n.slice(0,4).map(((e,s)=>(0,S.jsxs)("div",{className:"feature-item",children:[(0,S.jsx)(L,{className:"feature-icon"}),e]},s)))}),(null===c||void 0===c?void 0:c._id)===e._id&&(0,S.jsx)("div",{className:"selected-indicator",children:"\u2713 Selected"})]},e._id)}))}),(0,S.jsxs)("div",{className:"action-buttons",children:[(0,S.jsxs)(m.ZP,{type:"primary",size:"large",onClick:()=>{c&&n&&n(c)},disabled:!c,className:"continue-button",icon:(0,S.jsx)(k,{}),children:["Continue with ",(null===c||void 0===c?void 0:c.title)||"Selected Plan"]}),(0,S.jsx)(m.ZP,{type:"default",size:"large",onClick:()=>p(!1),className:"back-button",children:"\u2190 Back"})]})]}):(0,S.jsxs)("div",{className:"expiration-notice",children:[(0,S.jsxs)("div",{className:"modal-header expired-header",children:[(0,S.jsx)("div",{className:"warning-icon",children:(0,S.jsx)(g,{})}),(0,S.jsx)("h2",{className:"modal-title",children:"Subscription Expired!"}),(0,S.jsx)("p",{className:"modal-subtitle",children:"Your premium access has ended"})]}),(0,S.jsxs)("div",{className:"expired-plan-card",children:[(0,S.jsxs)("div",{className:"plan-header",children:[(0,S.jsx)("div",{className:"plan-icon expired-icon",children:(0,S.jsx)(f.Z,{})}),(0,S.jsxs)("div",{className:"plan-info",children:[(0,S.jsx)("h3",{className:"plan-name",children:v}),(0,S.jsx)("span",{className:"plan-status expired-status",children:"Expired"})]})]}),(0,S.jsxs)("div",{className:"expiration-details",children:[(0,S.jsxs)("div",{className:"detail-item",children:[(0,S.jsx)(N.Z,{className:"detail-icon"}),(0,S.jsxs)("div",{className:"detail-content",children:[(0,S.jsx)("span",{className:"detail-label",children:"Expired On"}),(0,S.jsx)("span",{className:"detail-value",children:j})]})]}),(0,S.jsxs)("div",{className:"detail-item",children:[(0,S.jsx)(f.Z,{className:"detail-icon"}),(0,S.jsxs)("div",{className:"detail-content",children:[(0,S.jsx)("span",{className:"detail-label",children:"Days Since Expiration"}),(0,S.jsx)("span",{className:"detail-value",children:0===x?"Today":"".concat(x," days ago")})]})]})]}),(0,S.jsxs)("div",{className:"progress-section",children:[(0,S.jsxs)("div",{className:"progress-header",children:[(0,S.jsx)("span",{className:"progress-label",children:"Subscription Status"}),(0,S.jsx)("span",{className:"expired-badge",children:"Expired"})]}),(0,S.jsx)(h.Z,{percent:100,strokeColor:"#ff4d4f",trailColor:"#f0f0f0",strokeWidth:8,showInfo:!1})]})]}),(0,S.jsxs)("div",{className:"message-section",children:[(0,S.jsxs)("div",{className:"message-card expired-message",children:[(0,S.jsx)("h4",{className:"message-title",children:"\u23f0 Time to Renew!"}),(0,S.jsxs)("p",{className:"message-text",children:["Your ",(0,S.jsx)("strong",{children:v})," subscription expired on ",(0,S.jsx)("strong",{children:j}),". To continue enjoying all premium features, please choose a new subscription plan."]})]}),(0,S.jsxs)("div",{className:"restricted-access",children:[(0,S.jsx)("h5",{className:"restricted-title",children:"\ud83d\udeab Currently Restricted:"}),(0,S.jsxs)("ul",{className:"restricted-list",children:[(0,S.jsx)("li",{children:"\u274c Quiz access"}),(0,S.jsx)("li",{children:"\u274c AI chat assistance"}),(0,S.jsx)("li",{children:"\u274c Premium study materials"}),(0,S.jsx)("li",{children:"\u274c Progress tracking"})]})]})]}),(0,S.jsxs)("div",{className:"action-buttons",children:[(0,S.jsx)(m.ZP,{type:"primary",size:"large",onClick:()=>{p(!0)},className:"renew-button",icon:(0,S.jsx)(k,{}),children:"Choose New Plan"}),(0,S.jsx)(m.ZP,{type:"default",size:"large",onClick:a,className:"later-button",children:"Maybe Later"})]}),(0,S.jsx)("div",{className:"footer-note",children:(0,S.jsxs)("p",{children:["\ud83d\udca1 ",(0,S.jsx)("strong",{children:"Good News:"})," You can access your profile and subscription settings anytime to renew!"]})})]})})})},Z=()=>{var e,s,a;const[u,h]=(0,i.useState)([]),[m,p]=(0,i.useState)(!1),[x,v]=(0,i.useState)(null),[j,g]=(0,i.useState)(!1);(0,i.useEffect)((()=>{console.log("\ud83d\udd0d showProcessingModal state changed to:",j)}),[j]);const[f,N]=(0,i.useState)(!1),[y,b]=(0,i.useState)(null),[k,w]=(0,i.useState)(""),[P,C]=(0,i.useState)(!1),[L,Z]=(0,i.useState)(!1),[z,D]=(0,i.useState)(null),[E,T]=(0,i.useState)(!1),[A,W]=(0,i.useState)(null),{user:B}=(0,n.v9)((e=>e.user)),{subscriptionData:I}=(0,n.v9)((e=>e.subscription)),H=((0,n.I0)(),[{_id:"basic-plan-sample",title:"Basic Membership",features:["2-month full access","Unlimited quizzes","Personalized profile","AI chat for instant help","Forum for student discussions","Study notes","Past papers","Books","Learning videos","Track progress with rankings"],actualPrice:28570,discountedPrice:2e4,discountPercentage:30,duration:2,status:!0},{_id:"premium-plan-sample",title:"Premium Plan",features:["3-month full access","Unlimited quizzes","Personalized profile","AI chat for instant help","Forum for student discussions","Study notes","Past papers","Books","Learning videos","Track progress with rankings","Priority support"],actualPrice:45e3,discountedPrice:35e3,discountPercentage:22,duration:3,status:!0}]);(0,i.useEffect)((()=>{Y(),R()}),[]),(0,i.useEffect)((()=>(document.body.style.overflow="",document.body.style.position="",document.body.style.width="",document.body.style.height="",()=>{document.body.style.overflow="",document.body.style.position="",document.body.style.width="",document.body.style.height=""})),[j,f]),(0,i.useEffect)((()=>{const e=()=>{document.querySelectorAll(".modal-content").forEach((e=>{e.scrollHeight>e.clientHeight?e.classList.add("has-scroll"):e.classList.remove("has-scroll")}))};if(j||f)return setTimeout(e,100),window.addEventListener("resize",e),()=>{window.removeEventListener("resize",e)}}),[j,f]),(0,i.useEffect)((()=>{I&&_()?(console.log("\ud83d\udeab Subscription expired, showing modal"),Z(!0)):Z(!1)}),[I]);const Y=async()=>{try{p(!0),console.log("Fetching plans...");const e=await(0,c.q)();console.log("Plans response:",e),e.success&&e.data&&e.data.length>0?(h(e.data),console.log("Plans loaded successfully from API:",e.data)):Array.isArray(e)&&e.length>0?(h(e),console.log("Plans loaded as array from API:",e)):(console.warn("No plans from API, using sample plans"),h(H),r.ZP.info("Showing sample plans. Please check your connection."))}catch(e){console.error("Error loading plans from API:",e),console.log("Using fallback sample plans"),h(H),r.ZP.warning("Using sample plans. Please check your connection and try again.")}finally{p(!1)}},R=async()=>{try{const e=await(0,o.W)();console.log("Current subscription:",e)}catch(e){console.log("No active subscription found")}},_=()=>{if(!I)return!0;if(!I.endDate)return!0;if("paid"!==I.paymentStatus)return!0;if("active"!==I.status)return!0;const e=new Date(I.endDate),s=new Date;return s.setHours(0,0,0,0),e.setHours(0,0,0,0),e<s},U=async e=>{if(I&&"active"===I.status&&"paid"===I.paymentStatus)return console.log("\ud83d\udeab User already has active subscription:",I),void C(!0);if(B.phoneNumber&&/^(06|07)\d{8}$/.test(B.phoneNumber)){console.log("\ud83c\udfaf Modal positioned optimally for best UX");try{var s;console.log("\ud83d\ude80 Starting payment for plan:",e.title),console.log("\ud83d\udd27 IMMEDIATELY showing processing modal..."),b(e),v(e._id),g(!0),T(!1),D(Date.now()),w("\ud83d\ude80 Preparing your payment request..."),console.log("\u2705 Processing modal IMMEDIATELY displayed"),await new Promise((e=>setTimeout(e,200))),setTimeout((()=>{T(!0)}),1e4);const i={plan:e,userId:B._id,userPhone:B.phoneNumber,userEmail:B.email||"".concat(null===(s=B.name)||void 0===s?void 0:s.replace(/\s+/g,"").toLowerCase(),"@brainwave.temp")};w("\ud83d\udce4 Sending payment request to ZenoPay...");const n=await(0,o._)(i);if(!n.success)throw new Error(n.message||"Payment failed");{var a;w("Payment sent! Check your phone for SMS confirmation..."),console.log("\ud83d\udcb3 Payment response:",n),console.log("\ud83c\udd94 Order ID:",n.order_id),r.ZP.success({content:"\ud83d\udcb3 Payment initiated! \ud83d\udcf1 Check your phone (".concat(B.phoneNumber,") for SMS confirmation from ZenoPay."),duration:8,style:{marginTop:"20vh"}});const e=n.order_id||(null===(a=n.data)||void 0===a?void 0:a.order_id)||"demo_order";console.log("\ud83d\udd0d Starting payment confirmation check for order:",e),V(e)}}catch(i){console.error("\u274c Payment failed:",i),g(!1),r.ZP.error("Payment failed: "+i.message),v(null)}}else r.ZP.error("Please update your phone number in your profile before subscribing")},V=async e=>{console.log("\ud83d\ude80 Starting payment confirmation check for order:",e);let s,a=!0;try{w("\ud83d\udcf1 Complete the payment on your phone, we'll detect it automatically...");let i=0;const n=150,t=async()=>{i++,console.log("\ud83d\udd0d Payment status check attempt ".concat(i,"/").concat(n," for order:"),e);try{const l=await(0,o.W)({orderId:e});if(console.log("\ud83d\udcca Payment status response:",l),console.log("\ud83d\udd0d Checking payment conditions:"),console.log("  - Live payment:","paid"===(null===l||void 0===l?void 0:l.paymentStatus)&&"active"===(null===l||void 0===l?void 0:l.status)),console.log("  - Demo payment:","completed"===(null===l||void 0===l?void 0:l.status)&&!0===(null===l||void 0===l?void 0:l.success)),l&&("paid"===l.paymentStatus&&"active"===l.status||"completed"===l.status&&!0===l.success)){a=!1,s&&document.removeEventListener("visibilitychange",s),w("\ud83c\udf89 Payment confirmed! Activating your subscription..."),console.log("\u2705 Payment confirmed, preparing to show success modal..."),console.log("\ud83d\udd04 Setting modal states - Processing: false, Success: true"),g(!1),N(!0),v(null),console.log("\u2705 Success modal state set to true"),R(),r.ZP.success({content:"\ud83c\udf89 Payment confirmed! All features are now unlocked!",duration:5,style:{marginTop:"20vh",fontSize:"16px"}}),W(5);const e=setInterval((()=>{W((s=>s<=1?(clearInterval(e),console.log("\ud83c\udfe0 Auto-navigating to hub after successful payment..."),N(!1),window.location.href="/user/hub",null):s-1))}),1e3)}else i>=n?(a=!1,s&&document.removeEventListener("visibilitychange",s),w("\u23f0 Still waiting for confirmation. Please complete the payment on your phone."),setTimeout((()=>{g(!1),v(null),r.ZP.warning("Payment confirmation is taking longer than expected. Please check your subscription status or try again.")}),2e3)):(w("\ud83d\udcf1 Complete the payment on your phone, we'll detect it automatically..."),setTimeout(t,2e3))}catch(l){if(console.error("Payment status check error:",l),l.message&&l.message.includes("404"))return console.error("\u274c Payment status endpoint not found (404)"),a=!1,s&&document.removeEventListener("visibilitychange",s),g(!1),v(null),void r.ZP.error("Payment verification service is temporarily unavailable. Please contact support or check your subscription status manually.");if(l.message&&l.message.includes("401"))return console.error("\u274c Authentication required for payment status check"),a=!1,s&&document.removeEventListener("visibilitychange",s),g(!1),v(null),void r.ZP.error("Please login again to check payment status.");i>=n?(a=!1,s&&document.removeEventListener("visibilitychange",s),g(!1),v(null),r.ZP.error("Unable to confirm payment status. Please check your subscription status manually.")):setTimeout(t,1e3)}};s=()=>{!document.hidden&&a&&(console.log("User returned to tab, checking payment status immediately..."),w("\ud83d\udd0d Checking payment status..."),setTimeout((()=>t()),100))},document.addEventListener("visibilitychange",s),setTimeout(t,500)}catch(i){a=!1,s&&document.removeEventListener("visibilitychange",s),g(!1),r.ZP.error("Payment confirmation failed: "+i.message),v(null)}},F=e=>e?new Date(e).toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric"}):"N/A",q=(()=>{if(I&&"paid"===I.paymentStatus&&"active"===I.status){if(new Date(I.endDate)>new Date)return"active"}return"expired"===(null===B||void 0===B?void 0:B.subscriptionStatus)||I&&"expired"===I.status?"expired":"none"})();return(0,S.jsx)("div",{className:"subscription-page",children:(0,S.jsxs)("div",{className:"subscription-container",children:[(0,S.jsxs)(t.E.div,{initial:{opacity:0,y:-20},animate:{opacity:1,y:0},transition:{duration:.6},className:"subscription-header",children:[(0,S.jsx)("button",{onClick:()=>{console.log("\ud83e\uddea Testing success modal..."),b(u[0]||{title:"Test Plan",duration:1,discountedPrice:13e3}),N(!0)},style:{position:"fixed",top:"10px",right:"10px",background:"#52c41a",color:"white",border:"none",padding:"8px 16px",borderRadius:"4px",cursor:"pointer",zIndex:9999},children:"\ud83e\uddea Test Success Modal"}),(0,S.jsxs)("h1",{className:"page-title",children:[(0,S.jsx)(l.CvY,{className:"title-icon"}),"Subscription Management"]}),(0,S.jsx)("p",{className:"page-subtitle",children:"Manage your subscription and access premium features"})]}),(0,S.jsxs)(t.E.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6,delay:.2},className:"current-subscription",children:[(0,S.jsx)("h2",{className:"section-title",children:"Current Subscription"}),"active"===q&&(0,S.jsxs)("div",{className:"subscription-card active",children:[(0,S.jsxs)("div",{className:"subscription-status",children:[(0,S.jsx)(l.FJM,{className:"status-icon active"}),(0,S.jsx)("span",{className:"status-text",children:"Active Subscription"})]}),(0,S.jsxs)("div",{className:"subscription-details",children:[(0,S.jsxs)("div",{className:"detail-item",children:[(0,S.jsx)(l.CvY,{className:"detail-icon"}),(0,S.jsxs)("span",{children:["Plan: ",(null===I||void 0===I||null===(e=I.activePlan)||void 0===e?void 0:e.title)||"Premium Plan"]})]}),(0,S.jsxs)("div",{className:"detail-item",children:[(0,S.jsx)(l.IiJ,{className:"detail-icon"}),(0,S.jsxs)("span",{children:["Expires: ",F(null===I||void 0===I?void 0:I.endDate)]})]}),(0,S.jsxs)("div",{className:"detail-item",children:[(0,S.jsx)(l.FJM,{className:"detail-icon"}),(0,S.jsxs)("span",{children:["Days Remaining: ",(()=>{if(null===I||void 0===I||!I.endDate)return 0;const e=new Date(I.endDate)-new Date,s=Math.ceil(e/864e5);return Math.max(0,s)})()]})]})]})]}),"expired"===q&&(0,S.jsxs)("div",{className:"subscription-card expired",children:[(0,S.jsxs)("div",{className:"subscription-status",children:[(0,S.jsx)(l.G5m,{className:"status-icon expired"}),(0,S.jsx)("span",{className:"status-text",children:"Subscription Expired"})]}),(0,S.jsxs)("div",{className:"subscription-details",children:[(0,S.jsxs)("div",{className:"detail-item",children:[(0,S.jsx)(l.IiJ,{className:"detail-icon"}),(0,S.jsxs)("span",{children:["Expired: ",F(null===I||void 0===I?void 0:I.endDate)]})]}),(0,S.jsx)("p",{className:"renewal-message",children:"Your subscription has expired. Choose a new plan below to continue accessing premium features."})]})]}),"none"===q&&(0,S.jsxs)("div",{className:"subscription-card none",children:[(0,S.jsxs)("div",{className:"subscription-status",children:[(0,S.jsx)(l.Xws,{className:"status-icon none"}),(0,S.jsx)("span",{className:"status-text",children:"Free Account"})]}),(0,S.jsx)("div",{className:"subscription-details",children:(0,S.jsx)("p",{className:"upgrade-message",children:"You're currently using a free account. Upgrade to a premium plan to unlock all features."})})]})]}),(0,S.jsxs)(t.E.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6,delay:.4},className:"available-plans",children:[(0,S.jsx)("h2",{className:"section-title",children:"active"===q?"\ud83d\ude80 Upgrade Your Plan":"expired"===q?"\ud83d\udd04 Renew Your Subscription":"\ud83c\udfaf Choose Your Plan"}),(0,S.jsxs)("div",{style:{display:"flex",gap:"10px",justifyContent:"center",marginBottom:"20px"},children:[(0,S.jsx)("button",{onClick:()=>{console.log("\ud83e\uddea Testing processing modal..."),g(!0),w("Testing processing modal..."),b(u[0]||{title:"Test Plan",discountedPrice:5e3,duration:1})},style:{background:"#ff6b6b",color:"white",border:"none",padding:"8px 16px",borderRadius:"6px",cursor:"pointer",fontSize:"12px"},children:"\ud83e\uddea Test Processing Modal"}),(0,S.jsx)("button",{onClick:()=>{console.log("\ud83e\uddea Testing success modal..."),g(!1),N(!0),v(null)},style:{background:"#51cf66",color:"white",border:"none",padding:"8px 16px",borderRadius:"6px",cursor:"pointer",fontSize:"12px"},children:"\ud83e\uddea Test Success Modal"})]}),(0,S.jsx)("p",{className:"section-subtitle",children:"active"===q?"Upgrade to a longer plan for better value and extended access":"expired"===q?"Your subscription has expired. Renew now to continue accessing premium features":"Select a subscription plan to unlock all premium features and start your learning journey"}),m?(0,S.jsxs)("div",{className:"loading-state",children:[(0,S.jsx)("div",{className:"spinner"}),(0,S.jsx)("p",{children:"Loading plans..."})]}):0===u.length?(0,S.jsxs)("div",{className:"no-plans-state",children:[(0,S.jsx)("div",{className:"no-plans-icon",children:"\ud83d\udccb"}),(0,S.jsx)("h3",{children:"No Plans Available"}),(0,S.jsx)("p",{children:"Plans are currently being loaded. Please refresh the page or try again later."}),(0,S.jsx)("button",{className:"refresh-btn",onClick:Y,children:"\ud83d\udd04 Refresh Plans"})]}):(0,S.jsx)("div",{className:"plans-grid",children:u.map((e=>{var s,a,i,n;return(0,S.jsxs)(t.E.div,{whileHover:{scale:1.02},whileTap:{scale:.98},className:"plan-card",children:[(0,S.jsxs)("div",{className:"plan-header",children:[(0,S.jsx)("h3",{className:"plan-title",children:e.title}),(null===(s=e.title)||void 0===s?void 0:s.toLowerCase().includes("standard"))&&(0,S.jsx)("span",{className:"plan-badge",children:"\ud83d\udd25 Popular"})]}),(0,S.jsxs)("div",{className:"plan-pricing",children:[(0,S.jsxs)("div",{className:"price-display",children:[(0,S.jsxs)("div",{className:"current-price",children:[(0,S.jsx)("span",{className:"currency",children:"TZS"}),null===(a=e.discountedPrice)||void 0===a?void 0:a.toLocaleString()]}),e.actualPrice>e.discountedPrice&&(0,S.jsxs)(S.Fragment,{children:[(0,S.jsxs)("span",{className:"original-price",children:[null===(i=e.actualPrice)||void 0===i?void 0:i.toLocaleString()," TZS"]}),(0,S.jsxs)("span",{className:"discount-badge",children:[Math.round((e.actualPrice-e.discountedPrice)/e.actualPrice*100),"% OFF"]})]})]}),(0,S.jsxs)("div",{className:"plan-duration",children:[(0,S.jsx)("span",{className:"duration-highlight",children:e.duration})," month",e.duration>1?"s":""," access"]})]}),(0,S.jsx)("div",{className:"plan-features",children:null===(n=e.features)||void 0===n?void 0:n.slice(0,5).map(((e,s)=>(0,S.jsxs)("div",{className:"feature-item",children:[(0,S.jsx)(l.FJM,{className:"feature-icon"}),(0,S.jsx)("span",{children:e})]},s)))}),(0,S.jsxs)("button",{className:"select-plan-btn",onClick:()=>U(e),disabled:x===e._id,style:{background:"linear-gradient(135deg, #3b82f6, #1d4ed8)",color:"white",border:"none",borderRadius:"12px",padding:"1rem 1.5rem",fontSize:"1rem",fontWeight:"600",cursor:x===e._id?"not-allowed":"pointer",transition:"all 0.3s ease",display:"flex",alignItems:"center",justifyContent:"center",gap:"0.5rem",width:"100%",opacity:x===e._id?.6:1},onMouseEnter:s=>{x!==e._id&&(s.target.style.background="linear-gradient(135deg, #1d4ed8, #1e40af)",s.target.style.transform="translateY(-2px)",s.target.style.boxShadow="0 8px 25px rgba(59, 130, 246, 0.4)")},onMouseLeave:s=>{x!==e._id&&(s.target.style.background="linear-gradient(135deg, #3b82f6, #1d4ed8)",s.target.style.transform="translateY(0)",s.target.style.boxShadow="0 4px 15px rgba(59, 130, 246, 0.3)")},children:[(0,S.jsx)(l.ypE,{className:"btn-icon"}),x===e._id?"Processing...":"active"===q?"Click to Upgrade":"expired"===q?"Click to Renew":"Click to Pay"]})]},e._id)}))})]}),(!B.phoneNumber||!/^(06|07)\d{8}$/.test(B.phoneNumber))&&(0,S.jsx)(t.E.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6,delay:.6},className:"phone-warning",children:(0,S.jsxs)("div",{className:"warning-content",children:[(0,S.jsx)(l.G5m,{className:"warning-icon"}),(0,S.jsxs)("div",{children:[(0,S.jsx)("h4",{children:"Phone Number Required"}),(0,S.jsx)("p",{children:"Please update your phone number in your profile to subscribe to a plan."}),(0,S.jsx)("button",{className:"update-phone-btn",onClick:()=>window.location.href="/profile",children:"Update Phone Number"})]})]})}),j&&(0,S.jsx)("div",{className:"payment-modal-overlay",onClick:e=>{e.target===e.currentTarget&&e.stopPropagation()},children:(0,S.jsxs)("div",{className:"payment-modal-container processing",children:[(0,S.jsx)("button",{className:"modal-close-btn",onClick:()=>{g(!1),v(null),T(!1),D(null),w(""),r.ZP.info("Payment process cancelled. You can try again anytime.")},"aria-label":"Close modal",children:(0,S.jsx)("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",children:(0,S.jsx)("path",{d:"M18 6L6 18M6 6L18 18",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round"})})}),(0,S.jsxs)("div",{className:"modal-header processing",children:[(0,S.jsxs)("div",{className:"processing-icon",children:[(0,S.jsx)("div",{className:"spinner"}),(0,S.jsxs)("svg",{className:"payment-icon",width:"32",height:"32",viewBox:"0 0 24 24",fill:"none",children:[(0,S.jsx)("path",{d:"M2 12C2 8.229 2 6.343 3.172 5.172C4.343 4 6.229 4 10 4H14C17.771 4 19.657 4 20.828 5.172C22 6.343 22 8.229 22 12C22 15.771 22 17.657 20.828 18.828C19.657 20 17.771 20 14 20H10C6.229 20 4.343 20 3.172 18.828C2 17.657 2 15.771 2 12Z",stroke:"currentColor",strokeWidth:"1.5"}),(0,S.jsx)("path",{d:"M10 16H6",stroke:"currentColor",strokeWidth:"1.5",strokeLinecap:"round"}),(0,S.jsx)("path",{d:"M14 16H12.5",stroke:"currentColor",strokeWidth:"1.5",strokeLinecap:"round"}),(0,S.jsx)("path",{d:"M2 10L22 10",stroke:"currentColor",strokeWidth:"1.5",strokeLinecap:"round"})]})]}),(0,S.jsx)("h2",{children:"Processing Payment"}),(0,S.jsx)("p",{children:"Secure transaction in progress"})]}),(0,S.jsxs)("div",{className:"modal-content",children:[(0,S.jsxs)("div",{className:"status-card",children:[(0,S.jsx)("div",{className:"status-indicator processing"}),(0,S.jsx)("p",{className:"status-text",children:k})]}),(0,S.jsxs)("div",{className:"plan-info-card",children:[(0,S.jsx)("h3",{children:null===y||void 0===y?void 0:y.title}),(0,S.jsxs)("div",{className:"plan-details",children:[(0,S.jsxs)("div",{className:"detail-row",children:[(0,S.jsx)("span",{children:"Amount"}),(0,S.jsxs)("strong",{children:[null===y||void 0===y||null===(s=y.discountedPrice)||void 0===s?void 0:s.toLocaleString()," TZS"]})]}),(0,S.jsxs)("div",{className:"detail-row",children:[(0,S.jsx)("span",{children:"Duration"}),(0,S.jsxs)("strong",{children:[null===y||void 0===y?void 0:y.duration," month",(null===y||void 0===y?void 0:y.duration)>1?"s":""]})]})]})]}),(0,S.jsxs)("div",{className:"instruction-card",children:[(0,S.jsxs)("div",{className:"instruction-header",children:[(0,S.jsx)("svg",{width:"20",height:"20",viewBox:"0 0 24 24",fill:"none",children:(0,S.jsx)("path",{d:"M16.5562 12.9062L16.1007 13.359C16.1007 13.359 15.0181 14.4355 12.0631 11.4972C9.10812 8.55901 10.1907 7.48257 10.1907 7.48257L10.4775 7.19738C11.1841 6.49484 11.2507 5.36691 10.6342 4.54348L9.37326 2.85908C8.61028 1.83992 7.13596 1.70529 6.26145 2.57483L4.69185 4.13552C4.25823 4.56668 3.96765 5.12559 4.00289 5.74561C4.09304 7.33182 4.81071 10.7447 8.81536 14.7266C13.0621 18.9492 17.0468 19.117 18.6763 18.9651C19.1917 18.9171 19.6399 18.6546 20.0011 18.2954L21.4217 16.883C22.3806 15.9295 22.1102 14.2949 20.8833 13.628L18.9728 12.5894C18.1672 12.1515 17.1858 12.2801 16.5562 12.9062Z",fill:"currentColor"})}),(0,S.jsx)("span",{children:"Check Your Phone"})]}),(0,S.jsx)("div",{className:"phone-number",children:null===B||void 0===B?void 0:B.phoneNumber}),(0,S.jsxs)("div",{className:"instruction-steps",children:[(0,S.jsx)("div",{className:"step",children:"1. You'll receive an SMS with payment instructions"}),(0,S.jsx)("div",{className:"step",children:"2. Follow the SMS steps to confirm payment"}),(0,S.jsx)("div",{className:"step",children:"3. Complete the mobile money transaction"})]})]}),E&&(0,S.jsxs)("div",{className:"try-again-card",children:[(0,S.jsx)("p",{children:"Taking longer than expected?"}),(0,S.jsxs)("button",{className:"try-again-btn",onClick:()=>{y&&(T(!1),D(null),U(y))},children:[(0,S.jsxs)("svg",{width:"16",height:"16",viewBox:"0 0 24 24",fill:"none",children:[(0,S.jsx)("path",{d:"M4 12a8 8 0 018-8V2.5",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round"}),(0,S.jsx)("path",{d:"M12 4L9 7L12 10",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"})]}),"Try Again"]})]})]})]})}),f&&(0,S.jsx)("div",{className:"payment-modal-overlay",onClick:e=>{e.target===e.currentTarget&&e.stopPropagation()},children:(0,S.jsxs)("div",{className:"payment-modal-container success",children:[(0,S.jsx)("button",{className:"modal-close-btn",onClick:()=>{W(null),N(!1)},"aria-label":"Close modal",children:(0,S.jsx)("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",children:(0,S.jsx)("path",{d:"M18 6L6 18M6 6L18 18",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round"})})}),(0,S.jsxs)("div",{className:"modal-header success",children:[(0,S.jsx)("div",{className:"success-icon",children:(0,S.jsxs)("svg",{width:"48",height:"48",viewBox:"0 0 24 24",fill:"none",children:[(0,S.jsx)("path",{d:"M22 12C22 17.5228 17.5228 22 12 22C6.47715 22 2 17.5228 2 12C2 6.47715 6.47715 2 12 2C17.5228 2 22 6.47715 22 12Z",fill:"#22c55e",fillOpacity:"0.2"}),(0,S.jsx)("path",{d:"M16 9L10.5 14.5L8 12",stroke:"#22c55e",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"}),(0,S.jsx)("path",{d:"M22 12C22 17.5228 17.5228 22 12 22C6.47715 22 2 17.5228 2 12C2 6.47715 6.47715 2 12 2C17.5228 2 22 6.47715 22 12Z",stroke:"#22c55e",strokeWidth:"2"})]})}),(0,S.jsx)("h2",{children:"Payment Successful!"}),(0,S.jsxs)("p",{children:["Welcome to ",null===y||void 0===y?void 0:y.title,"!"]})]}),(0,S.jsxs)("div",{className:"modal-content",children:[A&&(0,S.jsxs)("div",{className:"countdown-card",children:[(0,S.jsx)("div",{className:"countdown-icon",children:(0,S.jsxs)("svg",{width:"20",height:"20",viewBox:"0 0 24 24",fill:"none",children:[(0,S.jsx)("path",{d:"M12 2V6M12 18V22M4.93 4.93L7.76 7.76M16.24 16.24L19.07 19.07M2 12H6M18 12H22M4.93 19.07L7.76 16.24M16.24 7.76L19.07 4.93",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round"}),(0,S.jsx)("circle",{cx:"12",cy:"12",r:"3",stroke:"currentColor",strokeWidth:"2"})]})}),(0,S.jsxs)("p",{children:["Redirecting to Hub in ",A," seconds..."]})]}),(0,S.jsxs)("div",{className:"plan-summary-card",children:[(0,S.jsx)("h3",{children:"Subscription Activated"}),(0,S.jsxs)("div",{className:"plan-details",children:[(0,S.jsxs)("div",{className:"detail-row",children:[(0,S.jsx)("span",{children:"Plan"}),(0,S.jsx)("strong",{children:null===y||void 0===y?void 0:y.title})]}),(0,S.jsxs)("div",{className:"detail-row",children:[(0,S.jsx)("span",{children:"Duration"}),(0,S.jsxs)("strong",{children:[null===y||void 0===y?void 0:y.duration," month",(null===y||void 0===y?void 0:y.duration)>1?"s":""]})]}),(0,S.jsxs)("div",{className:"detail-row",children:[(0,S.jsx)("span",{children:"Amount Paid"}),(0,S.jsxs)("strong",{children:[null===y||void 0===y||null===(a=y.discountedPrice)||void 0===a?void 0:a.toLocaleString()," TZS"]})]}),(0,S.jsxs)("div",{className:"detail-row status",children:[(0,S.jsx)("span",{children:"Status"}),(0,S.jsxs)("div",{className:"status-badge",children:[(0,S.jsxs)("svg",{width:"16",height:"16",viewBox:"0 0 24 24",fill:"none",children:[(0,S.jsx)("path",{d:"M9 12L11 14L15 10",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"}),(0,S.jsx)("circle",{cx:"12",cy:"12",r:"9",stroke:"currentColor",strokeWidth:"2"})]}),"Active"]})]})]})]}),(0,S.jsxs)("div",{className:"features-card",children:[(0,S.jsx)("h3",{children:"\ud83d\ude80 Premium Features Unlocked"}),(0,S.jsxs)("div",{className:"features-grid",children:[(0,S.jsxs)("div",{className:"feature-item",children:[(0,S.jsxs)("svg",{width:"20",height:"20",viewBox:"0 0 24 24",fill:"none",children:[(0,S.jsx)("path",{d:"M9 12L11 14L15 10",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"}),(0,S.jsx)("circle",{cx:"12",cy:"12",r:"9",stroke:"currentColor",strokeWidth:"2"})]}),(0,S.jsx)("span",{children:"Unlimited Quizzes"})]}),(0,S.jsxs)("div",{className:"feature-item",children:[(0,S.jsxs)("svg",{width:"20",height:"20",viewBox:"0 0 24 24",fill:"none",children:[(0,S.jsx)("path",{d:"M9 12L11 14L15 10",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"}),(0,S.jsx)("circle",{cx:"12",cy:"12",r:"9",stroke:"currentColor",strokeWidth:"2"})]}),(0,S.jsx)("span",{children:"AI Assistant"})]}),(0,S.jsxs)("div",{className:"feature-item",children:[(0,S.jsxs)("svg",{width:"20",height:"20",viewBox:"0 0 24 24",fill:"none",children:[(0,S.jsx)("path",{d:"M9 12L11 14L15 10",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"}),(0,S.jsx)("circle",{cx:"12",cy:"12",r:"9",stroke:"currentColor",strokeWidth:"2"})]}),(0,S.jsx)("span",{children:"Study Materials"})]}),(0,S.jsxs)("div",{className:"feature-item",children:[(0,S.jsxs)("svg",{width:"20",height:"20",viewBox:"0 0 24 24",fill:"none",children:[(0,S.jsx)("path",{d:"M9 12L11 14L15 10",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"}),(0,S.jsx)("circle",{cx:"12",cy:"12",r:"9",stroke:"currentColor",strokeWidth:"2"})]}),(0,S.jsx)("span",{children:"Progress Tracking"})]}),(0,S.jsxs)("div",{className:"feature-item",children:[(0,S.jsxs)("svg",{width:"20",height:"20",viewBox:"0 0 24 24",fill:"none",children:[(0,S.jsx)("path",{d:"M9 12L11 14L15 10",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"}),(0,S.jsx)("circle",{cx:"12",cy:"12",r:"9",stroke:"currentColor",strokeWidth:"2"})]}),(0,S.jsx)("span",{children:"Learning Videos"})]}),(0,S.jsxs)("div",{className:"feature-item",children:[(0,S.jsxs)("svg",{width:"20",height:"20",viewBox:"0 0 24 24",fill:"none",children:[(0,S.jsx)("path",{d:"M9 12L11 14L15 10",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"}),(0,S.jsx)("circle",{cx:"12",cy:"12",r:"9",stroke:"currentColor",strokeWidth:"2"})]}),(0,S.jsx)("span",{children:"Forum Access"})]})]})]}),(0,S.jsxs)("div",{className:"modal-actions",children:[(0,S.jsxs)("button",{className:"primary-btn",onClick:()=>{W(null),N(!1),window.location.href="/user/hub"},children:[(0,S.jsxs)("svg",{width:"20",height:"20",viewBox:"0 0 24 24",fill:"none",children:[(0,S.jsx)("path",{d:"M3 9L12 2L21 9V20C21 20.5304 20.7893 21.0391 20.4142 21.4142C20.0391 21.7893 19.5304 22 19 22H5C4.46957 22 3.96086 21.7893 3.58579 21.4142C3.21071 21.0391 3 20.5304 3 20V9Z",stroke:"currentColor",strokeWidth:"2"}),(0,S.jsx)("polyline",{points:"9,22 9,12 15,12 15,22",stroke:"currentColor",strokeWidth:"2"})]}),"Continue to Hub ",A?"(".concat(A,"s)"):""]}),(0,S.jsx)("button",{className:"secondary-btn",onClick:()=>{W(null),N(!1)},children:"Close"})]})]})]})}),(0,S.jsx)(d.Z,{visible:P,onClose:()=>C(!1),currentPlan:u.find((e=>e._id===(null===I||void 0===I?void 0:I.activePlan)))||(null===I||void 0===I?void 0:I.plan),subscription:I,user:B}),(0,S.jsx)(M,{visible:L,onClose:()=>Z(!1),onRenew:async e=>{Z(!1),await U(e)},subscription:I,user:B,plans:u})]})})}},9168:(e,s,a)=>{a.d(s,{Z:()=>c});var i=a(7462),n=a(2791);const t={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M880 184H712v-64c0-4.4-3.6-8-8-8h-56c-4.4 0-8 3.6-8 8v64H384v-64c0-4.4-3.6-8-8-8h-56c-4.4 0-8 3.6-8 8v64H144c-17.7 0-32 14.3-32 32v664c0 17.7 14.3 32 32 32h736c17.7 0 32-14.3 32-32V216c0-17.7-14.3-32-32-32zm-40 656H184V460h656v380zM184 392V256h128v48c0 4.4 3.6 8 8 8h56c4.4 0 8-3.6 8-8v-48h256v48c0 4.4 3.6 8 8 8h56c4.4 0 8-3.6 8-8v-48h128v136H184z"}}]},name:"calendar",theme:"outlined"};var r=a(4291),l=function(e,s){return n.createElement(r.Z,(0,i.Z)({},e,{ref:s,icon:t}))};const c=n.forwardRef(l)},50:(e,s,a)=>{a.d(s,{Z:()=>c});var i=a(7462),n=a(2791);const t={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372z"}},{tag:"path",attrs:{d:"M686.7 638.6L544.1 535.5V288c0-4.4-3.6-8-8-8H488c-4.4 0-8 3.6-8 8v275.4c0 2.6 1.2 5 3.3 6.5l165.4 120.6c3.6 2.6 8.6 1.8 11.2-1.7l28.6-39c2.6-3.7 1.8-8.7-1.8-11.2z"}}]},name:"clock-circle",theme:"outlined"};var r=a(4291),l=function(e,s){return n.createElement(r.Z,(0,i.Z)({},e,{ref:s,icon:t}))};const c=n.forwardRef(l)},7455:(e,s,a)=>{a.d(s,{Z:()=>c});var i=a(7462),n=a(2791);const t={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M899.6 276.5L705 396.4 518.4 147.5a8.06 8.06 0 00-12.9 0L319 396.4 124.3 276.5c-5.7-3.5-13.1 1.2-12.2 7.9L188.5 865c1.1 7.9 7.9 14 16 14h615.1c8 0 14.9-6 15.9-14l76.4-580.6c.8-6.7-6.5-11.4-12.3-7.9zm-126 534.1H250.3l-53.8-409.4 139.8 86.1L512 252.9l175.7 234.4 139.8-86.1-53.9 409.4zM512 509c-62.1 0-112.6 50.5-112.6 112.6S449.9 734.2 512 734.2s112.6-50.5 112.6-112.6S574.1 509 512 509zm0 160.9c-26.6 0-48.2-21.6-48.2-48.3 0-26.6 21.6-48.3 48.2-48.3s48.2 21.6 48.2 48.3c0 26.6-21.6 48.3-48.2 48.3z"}}]},name:"crown",theme:"outlined"};var r=a(4291),l=function(e,s){return n.createElement(r.Z,(0,i.Z)({},e,{ref:s,icon:t}))};const c=n.forwardRef(l)}}]);
//# sourceMappingURL=132.8e6b945e.chunk.js.map