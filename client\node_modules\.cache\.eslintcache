[{"C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\index.js": "1", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\App.js": "2", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\reportWebVitals.js": "3", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\redux\\store.js": "4", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\ProtectedRoute.js": "5", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\Loader.js": "6", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\AdminProtectedRoute.js": "7", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\contexts\\ThemeContext.js": "8", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\RankingErrorBoundary.js": "9", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\DebugAuth.jsx": "10", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\RankingDemo.js": "11", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\redux\\loaderSlice.js": "12", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\redux\\usersSlice.js": "13", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\redux\\subscriptionSlice.js": "14", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\trial\\TrialPage.js": "15", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\redux\\paymentSlice.js": "16", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\admin\\Exams\\AddEditExam.js": "17", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\admin\\Notifications\\AdminNotifications.jsx": "18", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\Quiz\\QuizPlay.js": "19", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\Quiz\\QuizResult.js": "20", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\index.js": "21", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\Quiz\\index.js": "22", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\admin\\Exams\\index.js": "23", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\admin\\Forum\\index.js": "24", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\admin\\AdminReports\\index.js": "25", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\admin\\Dashboard\\index.js": "26", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\admin\\StudyMaterials\\index.js": "27", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\admin\\Users\\index.js": "28", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\Subscription\\index.js": "29", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\Test\\index.js": "30", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\AboutUs\\index.js": "31", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\UserReports\\index.js": "32", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\Ranking\\index.js": "33", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\StudyMaterial\\index.js": "34", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\WriteExam\\index.js": "35", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\VideoLessons\\index.js": "36", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\Hub\\index.js": "37", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\common\\Home\\index.js": "38", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\common\\Register\\index.js": "39", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\common\\Login\\index.js": "40", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\common\\Forum\\index.js": "41", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\common\\Profile\\index.js": "42", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\apicalls\\payment.js": "43", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\ModernSidebar.js": "44", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\AdminNavigation.js": "45", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\FloatingBrainwaveAI.js": "46", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\apicalls\\users.js": "47", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\apicalls\\syllabus.js": "48", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\apicalls\\aiQuestions.js": "49", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\apicalls\\notifications.js": "50", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\PageTitle.js": "51", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\apicalls\\exams.js": "52", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\apicalls\\reports.js": "53", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\apicalls\\chat.js": "54", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\UserRankingList.js": "55", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\ContentRenderer.js": "56", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\common\\NotificationBell.js": "57", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\UserRankingCard.js": "58", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\common\\ProfilePicture.js": "59", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\common\\OnlineStatusIndicator.js": "60", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\trial\\TrialQuizPlay.js": "61", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\trial\\TrialQuizSelection.js": "62", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\trial\\TrialQuizResult.js": "63", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\Button.js": "64", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\Loading.js": "65", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\Card.js": "66", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\trial\\TrialRegistrationPrompt.js": "67", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\Input.js": "68", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\QuizTimer.js": "69", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\LazyImage.js": "70", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\QuizCard.js": "71", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\QuizQuestion.js": "72", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\apicalls\\forum.js": "73", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\data\\Subjects.jsx": "74", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\ThemeToggle.js": "75", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\AdminLayout.js": "76", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\AdminCard.js": "77", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\admin\\Exams\\AddEditQuestion.js": "78", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\ErrorBoundary.js": "79", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\ResponsiveContainer.js": "80", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\PerformanceMonitor.js": "81", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\admin\\StudyMaterials\\SubtitleManager.js": "82", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\admin\\StudyMaterials\\AddStudyMaterialForm.js": "83", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\apicalls\\plans.js": "84", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\apicalls\\reviews.js": "85", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\admin\\StudyMaterials\\EditStudyMaterialForm.js": "86", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\admin\\StudyMaterials\\StudyMaterialManager.js": "87", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\apicalls\\study.js": "88", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\utils\\quizDataUtils.js": "89", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\XPResultDisplay.js": "90", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\SubscriptionExpiredModal\\SubscriptionExpiredModal.jsx": "91", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\common\\TryForFreeModal.js": "92", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\UpgradeRestrictionModal\\UpgradeRestrictionModal.jsx": "93", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\StudyMaterial\\PDFModal.js": "94", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\WriteExam\\Instructions.js": "95", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\apicalls\\index.js": "96", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\SubscriptionModal\\SubscriptionModal.jsx": "97", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\AdminTopNavigation.js": "98", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\apicalls\\trial.js": "99", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\apicalls\\subtitles.js": "100", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\XPProgressBar.js": "101", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\AchievementBadge.js": "102", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\EnhancedAchievementBadge.js": "103", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\LevelBadge.js": "104"}, {"size": 395, "mtime": 1696247250000, "results": "105", "hashOfConfig": "106"}, {"size": 12628, "mtime": 1752244908865, "results": "107", "hashOfConfig": "106"}, {"size": 362, "mtime": 1696247250000, "results": "108", "hashOfConfig": "106"}, {"size": 430, "mtime": 1736735017645, "results": "109", "hashOfConfig": "106"}, {"size": 20340, "mtime": 1752145561052, "results": "110", "hashOfConfig": "106"}, {"size": 180, "mtime": 1696247250000, "results": "111", "hashOfConfig": "106"}, {"size": 1601, "mtime": 1751575879221, "results": "112", "hashOfConfig": "106"}, {"size": 1410, "mtime": 1751140352157, "results": "113", "hashOfConfig": "106"}, {"size": 3109, "mtime": 1751260973778, "results": "114", "hashOfConfig": "106"}, {"size": 9770, "mtime": 1751495320007, "results": "115", "hashOfConfig": "106"}, {"size": 12711, "mtime": 1751260271529, "results": "116", "hashOfConfig": "106"}, {"size": 416, "mtime": 1696247250000, "results": "117", "hashOfConfig": "106"}, {"size": 334, "mtime": 1696247250000, "results": "118", "hashOfConfig": "106"}, {"size": 404, "mtime": 1736731932223, "results": "119", "hashOfConfig": "106"}, {"size": 6016, "mtime": 1751638273351, "results": "120", "hashOfConfig": "106"}, {"size": 449, "mtime": 1736732007232, "results": "121", "hashOfConfig": "106"}, {"size": 15139, "mtime": 1751582824879, "results": "122", "hashOfConfig": "106"}, {"size": 10989, "mtime": 1751586090664, "results": "123", "hashOfConfig": "106"}, {"size": 37700, "mtime": 1752198101933, "results": "124", "hashOfConfig": "106"}, {"size": 47955, "mtime": 1752190623482, "results": "125", "hashOfConfig": "106"}, {"size": 1140, "mtime": 1751426583568, "results": "126", "hashOfConfig": "106"}, {"size": 32661, "mtime": 1752193722096, "results": "127", "hashOfConfig": "106"}, {"size": 10464, "mtime": 1752187297118, "results": "128", "hashOfConfig": "106"}, {"size": 12434, "mtime": 1751869385895, "results": "129", "hashOfConfig": "106"}, {"size": 16063, "mtime": 1751870755172, "results": "130", "hashOfConfig": "106"}, {"size": 12035, "mtime": 1751869525840, "results": "131", "hashOfConfig": "106"}, {"size": 8883, "mtime": 1751585554937, "results": "132", "hashOfConfig": "106"}, {"size": 22154, "mtime": 1751585459342, "results": "133", "hashOfConfig": "106"}, {"size": 45883, "mtime": 1752135725326, "results": "134", "hashOfConfig": "106"}, {"size": 1327, "mtime": 1709427669270, "results": "135", "hashOfConfig": "106"}, {"size": 8089, "mtime": 1740446459586, "results": "136", "hashOfConfig": "106"}, {"size": 27236, "mtime": 1751873126836, "results": "137", "hashOfConfig": "106"}, {"size": 136450, "mtime": 1752148102332, "results": "138", "hashOfConfig": "106"}, {"size": 21793, "mtime": 1752246467200, "results": "139", "hashOfConfig": "106"}, {"size": 48075, "mtime": 1751478351350, "results": "140", "hashOfConfig": "106"}, {"size": 31161, "mtime": 1752282787667, "results": "141", "hashOfConfig": "106"}, {"size": 6529, "mtime": 1752244945909, "results": "142", "hashOfConfig": "106"}, {"size": 68001, "mtime": 1752145575965, "results": "143", "hashOfConfig": "106"}, {"size": 9620, "mtime": 1752144588374, "results": "144", "hashOfConfig": "106"}, {"size": 4378, "mtime": 1752193825539, "results": "145", "hashOfConfig": "106"}, {"size": 31275, "mtime": 1752186104888, "results": "146", "hashOfConfig": "106"}, {"size": 47615, "mtime": 1752006992088, "results": "147", "hashOfConfig": "106"}, {"size": 2046, "mtime": 1752102606942, "results": "148", "hashOfConfig": "106"}, {"size": 12635, "mtime": 1752021633769, "results": "149", "hashOfConfig": "106"}, {"size": 15500, "mtime": 1751869483167, "results": "150", "hashOfConfig": "106"}, {"size": 22827, "mtime": 1752146802004, "results": "151", "hashOfConfig": "106"}, {"size": 3191, "mtime": 1751940514617, "results": "152", "hashOfConfig": "106"}, {"size": 7315, "mtime": 1751495843287, "results": "153", "hashOfConfig": "106"}, {"size": 6337, "mtime": 1751558223480, "results": "154", "hashOfConfig": "106"}, {"size": 3632, "mtime": 1751487806125, "results": "155", "hashOfConfig": "106"}, {"size": 388, "mtime": 1703845955779, "results": "156", "hashOfConfig": "106"}, {"size": 2455, "mtime": 1751479784424, "results": "157", "hashOfConfig": "106"}, {"size": 3391, "mtime": 1751304153158, "results": "158", "hashOfConfig": "106"}, {"size": 1104, "mtime": 1749936905424, "results": "159", "hashOfConfig": "106"}, {"size": 29870, "mtime": 1752145669266, "results": "160", "hashOfConfig": "106"}, {"size": 5595, "mtime": 1751164672302, "results": "161", "hashOfConfig": "106"}, {"size": 11831, "mtime": 1752096169929, "results": "162", "hashOfConfig": "106"}, {"size": 18256, "mtime": 1751482855935, "results": "163", "hashOfConfig": "106"}, {"size": 6382, "mtime": 1752146723210, "results": "164", "hashOfConfig": "106"}, {"size": 3307, "mtime": 1751855844189, "results": "165", "hashOfConfig": "106"}, {"size": 24195, "mtime": 1752195775706, "results": "166", "hashOfConfig": "106"}, {"size": 10009, "mtime": 1751649332583, "results": "167", "hashOfConfig": "106"}, {"size": 27796, "mtime": 1751916780076, "results": "168", "hashOfConfig": "106"}, {"size": 2913, "mtime": 1751140370241, "results": "169", "hashOfConfig": "106"}, {"size": 3119, "mtime": 1751164996340, "results": "170", "hashOfConfig": "106"}, {"size": 1857, "mtime": 1751140385464, "results": "171", "hashOfConfig": "106"}, {"size": 10040, "mtime": 1751638250072, "results": "172", "hashOfConfig": "106"}, {"size": 2324, "mtime": 1751140401815, "results": "173", "hashOfConfig": "106"}, {"size": 7165, "mtime": 1752195834207, "results": "174", "hashOfConfig": "106"}, {"size": 2504, "mtime": 1751957740575, "results": "175", "hashOfConfig": "106"}, {"size": 17286, "mtime": 1752193750030, "results": "176", "hashOfConfig": "106"}, {"size": 13299, "mtime": 1751249005755, "results": "177", "hashOfConfig": "106"}, {"size": 1787, "mtime": 1734985908268, "results": "178", "hashOfConfig": "106"}, {"size": 944, "mtime": 1750970590507, "results": "179", "hashOfConfig": "106"}, {"size": 3904, "mtime": 1751143777976, "results": "180", "hashOfConfig": "106"}, {"size": 2200, "mtime": 1751563008113, "results": "181", "hashOfConfig": "106"}, {"size": 1717, "mtime": 1751561083661, "results": "182", "hashOfConfig": "106"}, {"size": 12864, "mtime": 1751134045332, "results": "183", "hashOfConfig": "106"}, {"size": 5088, "mtime": 1751143254906, "results": "184", "hashOfConfig": "106"}, {"size": 4989, "mtime": 1751143312418, "results": "185", "hashOfConfig": "106"}, {"size": 6304, "mtime": 1751188593099, "results": "186", "hashOfConfig": "106"}, {"size": 9494, "mtime": 1750995979612, "results": "187", "hashOfConfig": "106"}, {"size": 29072, "mtime": 1750992761364, "results": "188", "hashOfConfig": "106"}, {"size": 279, "mtime": 1736719733927, "results": "189", "hashOfConfig": "106"}, {"size": 578, "mtime": 1705434185826, "results": "190", "hashOfConfig": "106"}, {"size": 17375, "mtime": 1751000106093, "results": "191", "hashOfConfig": "106"}, {"size": 11161, "mtime": 1750999560542, "results": "192", "hashOfConfig": "106"}, {"size": 6669, "mtime": 1750999504134, "results": "193", "hashOfConfig": "106"}, {"size": 9114, "mtime": 1751691985112, "results": "194", "hashOfConfig": "106"}, {"size": 16372, "mtime": 1751479340474, "results": "195", "hashOfConfig": "106"}, {"size": 9653, "mtime": 1752084006410, "results": "196", "hashOfConfig": "106"}, {"size": 8712, "mtime": 1752144572539, "results": "197", "hashOfConfig": "106"}, {"size": 6486, "mtime": 1752078868080, "results": "198", "hashOfConfig": "106"}, {"size": 8101, "mtime": 1750963515173, "results": "199", "hashOfConfig": "106"}, {"size": 3835, "mtime": 1751478376207, "results": "200", "hashOfConfig": "106"}, {"size": 3672, "mtime": 1752017325220, "results": "201", "hashOfConfig": "106"}, {"size": 13621, "mtime": 1752105816006, "results": "202", "hashOfConfig": "106"}, {"size": 6449, "mtime": 1751574434238, "results": "203", "hashOfConfig": "106"}, {"size": 1024, "mtime": 1751637471453, "results": "204", "hashOfConfig": "106"}, {"size": 1524, "mtime": 1750994293078, "results": "205", "hashOfConfig": "106"}, {"size": 8429, "mtime": 1751244672688, "results": "206", "hashOfConfig": "106"}, {"size": 11901, "mtime": 1751236424130, "results": "207", "hashOfConfig": "106"}, {"size": 10081, "mtime": 1751244608756, "results": "208", "hashOfConfig": "106"}, {"size": 7685, "mtime": 1751244700154, "results": "209", "hashOfConfig": "106"}, {"filePath": "210", "messages": "211", "suppressedMessages": "212", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1ymk59w", {"filePath": "213", "messages": "214", "suppressedMessages": "215", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "216", "messages": "217", "suppressedMessages": "218", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "219", "messages": "220", "suppressedMessages": "221", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "222", "messages": "223", "suppressedMessages": "224", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 18, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "225", "messages": "226", "suppressedMessages": "227", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "228", "messages": "229", "suppressedMessages": "230", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "231", "messages": "232", "suppressedMessages": "233", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "234", "messages": "235", "suppressedMessages": "236", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "237", "messages": "238", "suppressedMessages": "239", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "240", "messages": "241", "suppressedMessages": "242", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "243", "messages": "244", "suppressedMessages": "245", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "246", "messages": "247", "suppressedMessages": "248", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "249", "messages": "250", "suppressedMessages": "251", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "252", "messages": "253", "suppressedMessages": "254", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "255", "messages": "256", "suppressedMessages": "257", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "258", "messages": "259", "suppressedMessages": "260", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "261", "messages": "262", "suppressedMessages": "263", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "264", "messages": "265", "suppressedMessages": "266", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "267", "messages": "268", "suppressedMessages": "269", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "270", "messages": "271", "suppressedMessages": "272", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "273", "messages": "274", "suppressedMessages": "275", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "276", "messages": "277", "suppressedMessages": "278", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "279", "messages": "280", "suppressedMessages": "281", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "282", "messages": "283", "suppressedMessages": "284", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "285", "messages": "286", "suppressedMessages": "287", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "288", "messages": "289", "suppressedMessages": "290", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "291", "messages": "292", "suppressedMessages": "293", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "294", "messages": "295", "suppressedMessages": "296", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "297", "messages": "298", "suppressedMessages": "299", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "300", "messages": "301", "suppressedMessages": "302", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "303", "messages": "304", "suppressedMessages": "305", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "306", "messages": "307", "suppressedMessages": "308", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 17, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "309", "messages": "310", "suppressedMessages": "311", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 16, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "312", "messages": "313", "suppressedMessages": "314", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "315", "messages": "316", "suppressedMessages": "317", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 1, "fixableWarningCount": 0, "source": null}, {"filePath": "318", "messages": "319", "suppressedMessages": "320", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "321", "messages": "322", "suppressedMessages": "323", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "324", "messages": "325", "suppressedMessages": "326", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "327", "messages": "328", "suppressedMessages": "329", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "330", "messages": "331", "suppressedMessages": "332", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 9, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "333", "messages": "334", "suppressedMessages": "335", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 12, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "336", "messages": "337", "suppressedMessages": "338", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "339", "messages": "340", "suppressedMessages": "341", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "342", "messages": "343", "suppressedMessages": "344", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "345", "messages": "346", "suppressedMessages": "347", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "348", "messages": "349", "suppressedMessages": "350", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "351", "messages": "352", "suppressedMessages": "353", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "354", "messages": "355", "suppressedMessages": "356", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "357", "messages": "358", "suppressedMessages": "359", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "360", "messages": "361", "suppressedMessages": "362", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "363", "messages": "364", "suppressedMessages": "365", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "366", "messages": "367", "suppressedMessages": "368", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "369", "messages": "370", "suppressedMessages": "371", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "372", "messages": "373", "suppressedMessages": "374", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "375", "messages": "376", "suppressedMessages": "377", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "378", "messages": "379", "suppressedMessages": "380", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "381", "messages": "382", "suppressedMessages": "383", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "384", "messages": "385", "suppressedMessages": "386", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "387", "messages": "388", "suppressedMessages": "389", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "390", "messages": "391", "suppressedMessages": "392", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "393", "messages": "394", "suppressedMessages": "395", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "396", "messages": "397", "suppressedMessages": "398", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "399", "messages": "400", "suppressedMessages": "401", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "402", "messages": "403", "suppressedMessages": "404", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "405", "messages": "406", "suppressedMessages": "407", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "408", "messages": "409", "suppressedMessages": "410", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "411", "messages": "412", "suppressedMessages": "413", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "414", "messages": "415", "suppressedMessages": "416", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "417", "messages": "418", "suppressedMessages": "419", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "420", "messages": "421", "suppressedMessages": "422", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "423", "messages": "424", "suppressedMessages": "425", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "426", "messages": "427", "suppressedMessages": "428", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "429", "messages": "430", "suppressedMessages": "431", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "432", "messages": "433", "suppressedMessages": "434", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "435", "messages": "436", "suppressedMessages": "437", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "438", "messages": "439", "suppressedMessages": "440", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "441", "messages": "442", "suppressedMessages": "443", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "444", "messages": "445", "suppressedMessages": "446", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "447", "messages": "448", "suppressedMessages": "449", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "450", "messages": "451", "suppressedMessages": "452", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "453", "messages": "454", "suppressedMessages": "455", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "456", "messages": "457", "suppressedMessages": "458", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "459", "messages": "460", "suppressedMessages": "461", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "462", "messages": "463", "suppressedMessages": "464", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "465", "messages": "466", "suppressedMessages": "467", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "468", "messages": "469", "suppressedMessages": "470", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "471", "messages": "472", "suppressedMessages": "473", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "474", "messages": "475", "suppressedMessages": "476", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "477", "messages": "478", "suppressedMessages": "479", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "480", "messages": "481", "suppressedMessages": "482", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "483", "messages": "484", "suppressedMessages": "485", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "486", "messages": "487", "suppressedMessages": "488", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "489", "messages": "490", "suppressedMessages": "491", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "492", "messages": "493", "suppressedMessages": "494", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "495", "messages": "496", "suppressedMessages": "497", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "498", "messages": "499", "suppressedMessages": "500", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "501", "messages": "502", "suppressedMessages": "503", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "504", "messages": "505", "suppressedMessages": "506", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "507", "messages": "508", "suppressedMessages": "509", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "510", "messages": "511", "suppressedMessages": "512", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "513", "messages": "514", "suppressedMessages": "515", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "516", "messages": "517", "suppressedMessages": "518", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "519", "messages": "520", "suppressedMessages": "521", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\index.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\App.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\reportWebVitals.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\redux\\store.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\ProtectedRoute.js", ["522", "523", "524", "525", "526", "527", "528", "529", "530", "531", "532", "533", "534", "535", "536", "537", "538", "539"], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\Loader.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\AdminProtectedRoute.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\contexts\\ThemeContext.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\RankingErrorBoundary.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\DebugAuth.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\RankingDemo.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\redux\\loaderSlice.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\redux\\usersSlice.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\redux\\subscriptionSlice.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\trial\\TrialPage.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\redux\\paymentSlice.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\admin\\Exams\\AddEditExam.js", ["540", "541"], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\admin\\Notifications\\AdminNotifications.jsx", [], ["542"], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\Quiz\\QuizPlay.js", ["543"], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\Quiz\\QuizResult.js", ["544"], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\index.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\Quiz\\index.js", ["545", "546", "547", "548", "549", "550"], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\admin\\Exams\\index.js", ["551"], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\admin\\Forum\\index.js", ["552", "553", "554"], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\admin\\AdminReports\\index.js", ["555", "556", "557"], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\admin\\Dashboard\\index.js", ["558", "559", "560", "561", "562"], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\admin\\StudyMaterials\\index.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\admin\\Users\\index.js", ["563", "564", "565", "566"], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\Subscription\\index.js", ["567", "568", "569", "570", "571", "572"], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\Test\\index.js", ["573", "574"], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\AboutUs\\index.js", ["575"], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\UserReports\\index.js", ["576", "577", "578", "579", "580", "581", "582", "583"], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\Ranking\\index.js", ["584", "585", "586", "587", "588", "589", "590", "591", "592", "593", "594", "595", "596", "597", "598", "599", "600"], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\StudyMaterial\\index.js", ["601", "602", "603", "604", "605", "606", "607", "608", "609", "610", "611", "612", "613", "614", "615", "616"], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\WriteExam\\index.js", ["617", "618", "619"], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\VideoLessons\\index.js", ["620", "621", "622", "623"], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\Hub\\index.js", ["624", "625", "626", "627", "628"], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\common\\Home\\index.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\common\\Register\\index.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\common\\Login\\index.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\common\\Forum\\index.js", ["629", "630", "631", "632", "633", "634", "635", "636", "637"], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\common\\Profile\\index.js", ["638", "639", "640", "641", "642", "643", "644", "645", "646", "647", "648", "649"], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\apicalls\\payment.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\ModernSidebar.js", ["650", "651", "652", "653"], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\AdminNavigation.js", ["654", "655", "656"], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\FloatingBrainwaveAI.js", ["657"], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\apicalls\\users.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\apicalls\\syllabus.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\apicalls\\aiQuestions.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\apicalls\\notifications.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\PageTitle.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\apicalls\\exams.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\apicalls\\reports.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\apicalls\\chat.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\UserRankingList.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\ContentRenderer.js", ["658", "659", "660"], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\common\\NotificationBell.js", ["661", "662", "663", "664", "665"], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\UserRankingCard.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\common\\ProfilePicture.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\common\\OnlineStatusIndicator.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\trial\\TrialQuizPlay.js", ["666", "667"], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\trial\\TrialQuizSelection.js", ["668"], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\trial\\TrialQuizResult.js", ["669"], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\Button.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\Loading.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\Card.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\trial\\TrialRegistrationPrompt.js", ["670"], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\Input.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\QuizTimer.js", ["671"], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\LazyImage.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\QuizCard.js", ["672", "673", "674"], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\QuizQuestion.js", ["675"], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\apicalls\\forum.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\data\\Subjects.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\ThemeToggle.js", ["676"], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\AdminLayout.js", ["677"], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\AdminCard.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\admin\\Exams\\AddEditQuestion.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\ErrorBoundary.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\ResponsiveContainer.js", ["678"], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\PerformanceMonitor.js", ["679", "680"], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\admin\\StudyMaterials\\SubtitleManager.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\admin\\StudyMaterials\\AddStudyMaterialForm.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\apicalls\\plans.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\apicalls\\reviews.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\admin\\StudyMaterials\\EditStudyMaterialForm.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\admin\\StudyMaterials\\StudyMaterialManager.js", ["681", "682", "683", "684"], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\apicalls\\study.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\utils\\quizDataUtils.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\XPResultDisplay.js", ["685"], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\SubscriptionExpiredModal\\SubscriptionExpiredModal.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\common\\TryForFreeModal.js", ["686", "687", "688", "689", "690", "691"], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\UpgradeRestrictionModal\\UpgradeRestrictionModal.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\StudyMaterial\\PDFModal.js", [], ["692"], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\WriteExam\\Instructions.js", ["693"], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\apicalls\\index.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\SubscriptionModal\\SubscriptionModal.jsx", ["694", "695", "696", "697", "698", "699", "700", "701"], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\AdminTopNavigation.js", ["702", "703"], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\apicalls\\trial.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\apicalls\\subtitles.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\XPProgressBar.js", ["704"], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\AchievementBadge.js", ["705", "706", "707"], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\EnhancedAchievementBadge.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\LevelBadge.js", [], [], {"ruleId": "708", "severity": 1, "message": "709", "line": 2, "column": 46, "nodeType": "710", "messageId": "711", "endLine": 2, "endColumn": 61}, {"ruleId": "708", "severity": 1, "message": "712", "line": 7, "column": 10, "nodeType": "710", "messageId": "711", "endLine": 7, "endColumn": 21}, {"ruleId": "708", "severity": 1, "message": "713", "line": 7, "column": 23, "nodeType": "710", "messageId": "711", "endLine": 7, "endColumn": 34}, {"ruleId": "708", "severity": 1, "message": "714", "line": 12, "column": 8, "nodeType": "710", "messageId": "711", "endLine": 12, "endColumn": 23}, {"ruleId": "708", "severity": 1, "message": "715", "line": 15, "column": 10, "nodeType": "710", "messageId": "711", "endLine": 15, "endColumn": 16}, {"ruleId": "708", "severity": 1, "message": "716", "line": 15, "column": 18, "nodeType": "710", "messageId": "711", "endLine": 15, "endColumn": 33}, {"ruleId": "708", "severity": 1, "message": "717", "line": 15, "column": 35, "nodeType": "710", "messageId": "711", "endLine": 15, "endColumn": 42}, {"ruleId": "708", "severity": 1, "message": "718", "line": 15, "column": 44, "nodeType": "710", "messageId": "711", "endLine": 15, "endColumn": 47}, {"ruleId": "708", "severity": 1, "message": "719", "line": 15, "column": 49, "nodeType": "710", "messageId": "711", "endLine": 15, "endColumn": 62}, {"ruleId": "708", "severity": 1, "message": "720", "line": 15, "column": 64, "nodeType": "710", "messageId": "711", "endLine": 15, "endColumn": 72}, {"ruleId": "708", "severity": 1, "message": "721", "line": 15, "column": 82, "nodeType": "710", "messageId": "711", "endLine": 15, "endColumn": 92}, {"ruleId": "708", "severity": 1, "message": "722", "line": 15, "column": 94, "nodeType": "710", "messageId": "711", "endLine": 15, "endColumn": 100}, {"ruleId": "708", "severity": 1, "message": "723", "line": 15, "column": 102, "nodeType": "710", "messageId": "711", "endLine": 15, "endColumn": 108}, {"ruleId": "708", "severity": 1, "message": "724", "line": 16, "column": 8, "nodeType": "710", "messageId": "711", "endLine": 16, "endColumn": 29}, {"ruleId": "725", "severity": 1, "message": "726", "line": 120, "column": 6, "nodeType": "727", "endLine": 120, "endColumn": 8, "suggestions": "728"}, {"ruleId": "725", "severity": 1, "message": "729", "line": 189, "column": 6, "nodeType": "727", "endLine": 189, "endColumn": 39, "suggestions": "730"}, {"ruleId": "725", "severity": 1, "message": "731", "line": 200, "column": 6, "nodeType": "727", "endLine": 200, "endColumn": 25, "suggestions": "732"}, {"ruleId": "708", "severity": 1, "message": "733", "line": 231, "column": 9, "nodeType": "710", "messageId": "711", "endLine": 231, "endColumn": 23}, {"ruleId": "708", "severity": 1, "message": "734", "line": 1, "column": 35, "nodeType": "710", "messageId": "711", "endLine": 1, "endColumn": 41}, {"ruleId": "725", "severity": 1, "message": "735", "line": 110, "column": 6, "nodeType": "727", "endLine": 110, "endColumn": 8, "suggestions": "736"}, {"ruleId": "725", "severity": 1, "message": "737", "line": 46, "column": 6, "nodeType": "727", "endLine": 46, "endColumn": 8, "suggestions": "738", "suppressions": "739"}, {"ruleId": "725", "severity": 1, "message": "740", "line": 322, "column": 6, "nodeType": "727", "endLine": 322, "endColumn": 57, "suggestions": "741"}, {"ruleId": "742", "severity": 1, "message": "743", "line": 1074, "column": 29, "nodeType": "744", "endLine": 1083, "endColumn": 31}, {"ruleId": "708", "severity": 1, "message": "745", "line": 12, "column": 3, "nodeType": "710", "messageId": "711", "endLine": 12, "endColumn": 10}, {"ruleId": "708", "severity": 1, "message": "723", "line": 16, "column": 3, "nodeType": "710", "messageId": "711", "endLine": 16, "endColumn": 9}, {"ruleId": "708", "severity": 1, "message": "715", "line": 17, "column": 3, "nodeType": "710", "messageId": "711", "endLine": 17, "endColumn": 9}, {"ruleId": "708", "severity": 1, "message": "746", "line": 18, "column": 3, "nodeType": "710", "messageId": "711", "endLine": 18, "endColumn": 9}, {"ruleId": "725", "severity": 1, "message": "747", "line": 249, "column": 6, "nodeType": "727", "endLine": 249, "endColumn": 8, "suggestions": "748"}, {"ruleId": "708", "severity": 1, "message": "749", "line": 499, "column": 9, "nodeType": "710", "messageId": "711", "endLine": 499, "endColumn": 19}, {"ruleId": "725", "severity": 1, "message": "750", "line": 191, "column": 6, "nodeType": "727", "endLine": 191, "endColumn": 8, "suggestions": "751"}, {"ruleId": "708", "severity": 1, "message": "752", "line": 2, "column": 27, "nodeType": "710", "messageId": "711", "endLine": 2, "endColumn": 32}, {"ruleId": "708", "severity": 1, "message": "753", "line": 2, "column": 34, "nodeType": "710", "messageId": "711", "endLine": 2, "endColumn": 38}, {"ruleId": "725", "severity": 1, "message": "754", "line": 113, "column": 6, "nodeType": "727", "endLine": 113, "endColumn": 8, "suggestions": "755"}, {"ruleId": "708", "severity": 1, "message": "756", "line": 5, "column": 3, "nodeType": "710", "messageId": "711", "endLine": 5, "endColumn": 14}, {"ruleId": "708", "severity": 1, "message": "757", "line": 31, "column": 9, "nodeType": "710", "messageId": "711", "endLine": 31, "endColumn": 17}, {"ruleId": "725", "severity": 1, "message": "758", "line": 241, "column": 6, "nodeType": "727", "endLine": 241, "endColumn": 35, "suggestions": "759"}, {"ruleId": "708", "severity": 1, "message": "760", "line": 12, "column": 3, "nodeType": "710", "messageId": "711", "endLine": 12, "endColumn": 11}, {"ruleId": "708", "severity": 1, "message": "761", "line": 14, "column": 3, "nodeType": "710", "messageId": "711", "endLine": 14, "endColumn": 10}, {"ruleId": "708", "severity": 1, "message": "762", "line": 16, "column": 3, "nodeType": "710", "messageId": "711", "endLine": 16, "endColumn": 8}, {"ruleId": "708", "severity": 1, "message": "763", "line": 40, "column": 10, "nodeType": "710", "messageId": "711", "endLine": 40, "endColumn": 17}, {"ruleId": "725", "severity": 1, "message": "764", "line": 44, "column": 6, "nodeType": "727", "endLine": 44, "endColumn": 8, "suggestions": "765"}, {"ruleId": "708", "severity": 1, "message": "766", "line": 12, "column": 8, "nodeType": "710", "messageId": "711", "endLine": 12, "endColumn": 17}, {"ruleId": "708", "severity": 1, "message": "767", "line": 31, "column": 3, "nodeType": "710", "messageId": "711", "endLine": 31, "endColumn": 9}, {"ruleId": "708", "severity": 1, "message": "768", "line": 43, "column": 19, "nodeType": "710", "messageId": "711", "endLine": 43, "endColumn": 29}, {"ruleId": "725", "severity": 1, "message": "769", "line": 182, "column": 6, "nodeType": "727", "endLine": 182, "endColumn": 8, "suggestions": "770"}, {"ruleId": "708", "severity": 1, "message": "713", "line": 8, "column": 10, "nodeType": "710", "messageId": "711", "endLine": 8, "endColumn": 21}, {"ruleId": "708", "severity": 1, "message": "712", "line": 8, "column": 23, "nodeType": "710", "messageId": "711", "endLine": 8, "endColumn": 34}, {"ruleId": "708", "severity": 1, "message": "771", "line": 28, "column": 10, "nodeType": "710", "messageId": "711", "endLine": 28, "endColumn": 29}, {"ruleId": "708", "severity": 1, "message": "772", "line": 33, "column": 9, "nodeType": "710", "messageId": "711", "endLine": 33, "endColumn": 17}, {"ruleId": "725", "severity": 1, "message": "773", "line": 85, "column": 6, "nodeType": "727", "endLine": 85, "endColumn": 8, "suggestions": "774"}, {"ruleId": "725", "severity": 1, "message": "775", "line": 139, "column": 6, "nodeType": "727", "endLine": 139, "endColumn": 24, "suggestions": "776"}, {"ruleId": "708", "severity": 1, "message": "777", "line": 1, "column": 38, "nodeType": "710", "messageId": "711", "endLine": 1, "endColumn": 46}, {"ruleId": "708", "severity": 1, "message": "778", "line": 8, "column": 12, "nodeType": "710", "messageId": "711", "endLine": 8, "endColumn": 19}, {"ruleId": "725", "severity": 1, "message": "779", "line": 58, "column": 8, "nodeType": "727", "endLine": 58, "endColumn": 10, "suggestions": "780"}, {"ruleId": "708", "severity": 1, "message": "781", "line": 13, "column": 3, "nodeType": "710", "messageId": "711", "endLine": 13, "endColumn": 13}, {"ruleId": "708", "severity": 1, "message": "761", "line": 14, "column": 3, "nodeType": "710", "messageId": "711", "endLine": 14, "endColumn": 10}, {"ruleId": "708", "severity": 1, "message": "782", "line": 15, "column": 3, "nodeType": "710", "messageId": "711", "endLine": 15, "endColumn": 10}, {"ruleId": "708", "severity": 1, "message": "783", "line": 17, "column": 3, "nodeType": "710", "messageId": "711", "endLine": 17, "endColumn": 13}, {"ruleId": "708", "severity": 1, "message": "784", "line": 35, "column": 10, "nodeType": "710", "messageId": "711", "endLine": 35, "endColumn": 18}, {"ruleId": "708", "severity": 1, "message": "785", "line": 35, "column": 20, "nodeType": "710", "messageId": "711", "endLine": 35, "endColumn": 31}, {"ruleId": "725", "severity": 1, "message": "786", "line": 137, "column": 6, "nodeType": "727", "endLine": 137, "endColumn": 8, "suggestions": "787"}, {"ruleId": "725", "severity": 1, "message": "788", "line": 141, "column": 6, "nodeType": "727", "endLine": 141, "endColumn": 60, "suggestions": "789"}, {"ruleId": "708", "severity": 1, "message": "790", "line": 2, "column": 18, "nodeType": "710", "messageId": "711", "endLine": 2, "endColumn": 33}, {"ruleId": "708", "severity": 1, "message": "791", "line": 21, "column": 53, "nodeType": "710", "messageId": "711", "endLine": 21, "endColumn": 67}, {"ruleId": "708", "severity": 1, "message": "724", "line": 24, "column": 8, "nodeType": "710", "messageId": "711", "endLine": 24, "endColumn": 29}, {"ruleId": "708", "severity": 1, "message": "784", "line": 75, "column": 10, "nodeType": "710", "messageId": "711", "endLine": 75, "endColumn": 18}, {"ruleId": "708", "severity": 1, "message": "785", "line": 75, "column": 20, "nodeType": "710", "messageId": "711", "endLine": 75, "endColumn": 31}, {"ruleId": "708", "severity": 1, "message": "792", "line": 76, "column": 10, "nodeType": "710", "messageId": "711", "endLine": 76, "endColumn": 19}, {"ruleId": "708", "severity": 1, "message": "793", "line": 76, "column": 21, "nodeType": "710", "messageId": "711", "endLine": 76, "endColumn": 33}, {"ruleId": "708", "severity": 1, "message": "794", "line": 77, "column": 10, "nodeType": "710", "messageId": "711", "endLine": 77, "endColumn": 24}, {"ruleId": "708", "severity": 1, "message": "795", "line": 80, "column": 10, "nodeType": "710", "messageId": "711", "endLine": 80, "endColumn": 27}, {"ruleId": "708", "severity": 1, "message": "796", "line": 82, "column": 10, "nodeType": "710", "messageId": "711", "endLine": 82, "endColumn": 24}, {"ruleId": "708", "severity": 1, "message": "797", "line": 90, "column": 9, "nodeType": "710", "messageId": "711", "endLine": 90, "endColumn": 18}, {"ruleId": "708", "severity": 1, "message": "798", "line": 91, "column": 9, "nodeType": "710", "messageId": "711", "endLine": 91, "endColumn": 23}, {"ruleId": "725", "severity": 1, "message": "799", "line": 896, "column": 6, "nodeType": "727", "endLine": 896, "endColumn": 8, "suggestions": "800"}, {"ruleId": "708", "severity": 1, "message": "801", "line": 917, "column": 9, "nodeType": "710", "messageId": "711", "endLine": 917, "endColumn": 24}, {"ruleId": "708", "severity": 1, "message": "802", "line": 1072, "column": 9, "nodeType": "710", "messageId": "711", "endLine": 1072, "endColumn": 29}, {"ruleId": "708", "severity": 1, "message": "803", "line": 1560, "column": 39, "nodeType": "710", "messageId": "711", "endLine": 1560, "endColumn": 48}, {"ruleId": "804", "severity": 1, "message": "805", "line": 2155, "column": 27, "nodeType": "806", "messageId": "807", "endLine": 2155, "endColumn": 28}, {"ruleId": "708", "severity": 1, "message": "808", "line": 16, "column": 3, "nodeType": "710", "messageId": "711", "endLine": 16, "endColumn": 16}, {"ruleId": "708", "severity": 1, "message": "809", "line": 17, "column": 3, "nodeType": "710", "messageId": "711", "endLine": 17, "endColumn": 11}, {"ruleId": "708", "severity": 1, "message": "810", "line": 24, "column": 3, "nodeType": "710", "messageId": "711", "endLine": 24, "endColumn": 11}, {"ruleId": "708", "severity": 1, "message": "811", "line": 25, "column": 3, "nodeType": "710", "messageId": "711", "endLine": 25, "endColumn": 11}, {"ruleId": "708", "severity": 1, "message": "812", "line": 26, "column": 3, "nodeType": "710", "messageId": "711", "endLine": 26, "endColumn": 18}, {"ruleId": "708", "severity": 1, "message": "783", "line": 27, "column": 3, "nodeType": "710", "messageId": "711", "endLine": 27, "endColumn": 13}, {"ruleId": "708", "severity": 1, "message": "762", "line": 28, "column": 3, "nodeType": "710", "messageId": "711", "endLine": 28, "endColumn": 8}, {"ruleId": "708", "severity": 1, "message": "781", "line": 29, "column": 3, "nodeType": "710", "messageId": "711", "endLine": 29, "endColumn": 13}, {"ruleId": "708", "severity": 1, "message": "813", "line": 30, "column": 3, "nodeType": "710", "messageId": "711", "endLine": 30, "endColumn": 9}, {"ruleId": "708", "severity": 1, "message": "814", "line": 32, "column": 3, "nodeType": "710", "messageId": "711", "endLine": 32, "endColumn": 14}, {"ruleId": "708", "severity": 1, "message": "718", "line": 33, "column": 3, "nodeType": "710", "messageId": "711", "endLine": 33, "endColumn": 6}, {"ruleId": "708", "severity": 1, "message": "815", "line": 34, "column": 3, "nodeType": "710", "messageId": "711", "endLine": 34, "endColumn": 18}, {"ruleId": "708", "severity": 1, "message": "816", "line": 35, "column": 3, "nodeType": "710", "messageId": "711", "endLine": 35, "endColumn": 15}, {"ruleId": "708", "severity": 1, "message": "817", "line": 36, "column": 3, "nodeType": "710", "messageId": "711", "endLine": 36, "endColumn": 10}, {"ruleId": "725", "severity": 1, "message": "818", "line": 64, "column": 9, "nodeType": "819", "endLine": 68, "endColumn": 29}, {"ruleId": "725", "severity": 1, "message": "820", "line": 247, "column": 6, "nodeType": "727", "endLine": 247, "endColumn": 48, "suggestions": "821"}, {"ruleId": "708", "severity": 1, "message": "822", "line": 17, "column": 10, "nodeType": "710", "messageId": "711", "endLine": 17, "endColumn": 31}, {"ruleId": "708", "severity": 1, "message": "823", "line": 17, "column": 33, "nodeType": "710", "messageId": "711", "endLine": 17, "endColumn": 43}, {"ruleId": "725", "severity": 1, "message": "824", "line": 777, "column": 6, "nodeType": "727", "endLine": 777, "endColumn": 81, "suggestions": "825"}, {"ruleId": "708", "severity": 1, "message": "826", "line": 3, "column": 10, "nodeType": "710", "messageId": "711", "endLine": 3, "endColumn": 16}, {"ruleId": "708", "severity": 1, "message": "790", "line": 3, "column": 18, "nodeType": "710", "messageId": "711", "endLine": 3, "endColumn": 33}, {"ruleId": "827", "severity": 2, "message": "828", "line": 41, "column": 1, "nodeType": "829", "endLine": 41, "endColumn": 98, "fix": "830"}, {"ruleId": "708", "severity": 1, "message": "831", "line": 288, "column": 9, "nodeType": "710", "messageId": "711", "endLine": 288, "endColumn": 23}, {"ruleId": "708", "severity": 1, "message": "832", "line": 8, "column": 3, "nodeType": "710", "messageId": "711", "endLine": 8, "endColumn": 9}, {"ruleId": "708", "severity": 1, "message": "833", "line": 14, "column": 3, "nodeType": "710", "messageId": "711", "endLine": 14, "endColumn": 15}, {"ruleId": "708", "severity": 1, "message": "834", "line": 20, "column": 3, "nodeType": "710", "messageId": "711", "endLine": 20, "endColumn": 10}, {"ruleId": "708", "severity": 1, "message": "835", "line": 21, "column": 3, "nodeType": "710", "messageId": "711", "endLine": 21, "endColumn": 15}, {"ruleId": "708", "severity": 1, "message": "836", "line": 56, "column": 9, "nodeType": "710", "messageId": "711", "endLine": 56, "endColumn": 21}, {"ruleId": "708", "severity": 1, "message": "837", "line": 1, "column": 38, "nodeType": "710", "messageId": "711", "endLine": 1, "endColumn": 44}, {"ruleId": "708", "severity": 1, "message": "838", "line": 4, "column": 40, "nodeType": "710", "messageId": "711", "endLine": 4, "endColumn": 46}, {"ruleId": "708", "severity": 1, "message": "766", "line": 5, "column": 8, "nodeType": "710", "messageId": "711", "endLine": 5, "endColumn": 17}, {"ruleId": "708", "severity": 1, "message": "724", "line": 9, "column": 8, "nodeType": "710", "messageId": "711", "endLine": 9, "endColumn": 29}, {"ruleId": "708", "severity": 1, "message": "839", "line": 18, "column": 8, "nodeType": "710", "messageId": "711", "endLine": 18, "endColumn": 13}, {"ruleId": "708", "severity": 1, "message": "840", "line": 63, "column": 10, "nodeType": "710", "messageId": "711", "endLine": 63, "endColumn": 20}, {"ruleId": "725", "severity": 1, "message": "841", "line": 139, "column": 6, "nodeType": "727", "endLine": 139, "endColumn": 26, "suggestions": "842"}, {"ruleId": "725", "severity": 1, "message": "843", "line": 171, "column": 6, "nodeType": "727", "endLine": 171, "endColumn": 8, "suggestions": "844"}, {"ruleId": "725", "severity": 1, "message": "845", "line": 295, "column": 6, "nodeType": "727", "endLine": 295, "endColumn": 20, "suggestions": "846"}, {"ruleId": "708", "severity": 1, "message": "766", "line": 3, "column": 8, "nodeType": "710", "messageId": "711", "endLine": 3, "endColumn": 17}, {"ruleId": "708", "severity": 1, "message": "753", "line": 9, "column": 10, "nodeType": "710", "messageId": "711", "endLine": 9, "endColumn": 14}, {"ruleId": "708", "severity": 1, "message": "752", "line": 9, "column": 32, "nodeType": "710", "messageId": "711", "endLine": 9, "endColumn": 37}, {"ruleId": "708", "severity": 1, "message": "847", "line": 9, "column": 39, "nodeType": "710", "messageId": "711", "endLine": 9, "endColumn": 45}, {"ruleId": "708", "severity": 1, "message": "848", "line": 19, "column": 10, "nodeType": "710", "messageId": "711", "endLine": 19, "endColumn": 21}, {"ruleId": "708", "severity": 1, "message": "849", "line": 22, "column": 10, "nodeType": "710", "messageId": "711", "endLine": 22, "endColumn": 22}, {"ruleId": "725", "severity": 1, "message": "850", "line": 111, "column": 6, "nodeType": "727", "endLine": 111, "endColumn": 32, "suggestions": "851"}, {"ruleId": "725", "severity": 1, "message": "843", "line": 145, "column": 6, "nodeType": "727", "endLine": 145, "endColumn": 8, "suggestions": "852"}, {"ruleId": "708", "severity": 1, "message": "853", "line": 255, "column": 9, "nodeType": "710", "messageId": "711", "endLine": 255, "endColumn": 32}, {"ruleId": "708", "severity": 1, "message": "854", "line": 303, "column": 9, "nodeType": "710", "messageId": "711", "endLine": 303, "endColumn": 26}, {"ruleId": "725", "severity": 1, "message": "843", "line": 327, "column": 6, "nodeType": "727", "endLine": 327, "endColumn": 8, "suggestions": "855"}, {"ruleId": "725", "severity": 1, "message": "856", "line": 334, "column": 6, "nodeType": "727", "endLine": 334, "endColumn": 19, "suggestions": "857"}, {"ruleId": "708", "severity": 1, "message": "858", "line": 10, "column": 3, "nodeType": "710", "messageId": "711", "endLine": 10, "endColumn": 10}, {"ruleId": "708", "severity": 1, "message": "859", "line": 15, "column": 3, "nodeType": "710", "messageId": "711", "endLine": 15, "endColumn": 15}, {"ruleId": "708", "severity": 1, "message": "860", "line": 25, "column": 11, "nodeType": "710", "messageId": "711", "endLine": 25, "endColumn": 15}, {"ruleId": "861", "severity": 1, "message": "862", "line": 257, "column": 21, "nodeType": "863", "messageId": "864", "endLine": 263, "endColumn": 24}, {"ruleId": "708", "severity": 1, "message": "858", "line": 11, "column": 3, "nodeType": "710", "messageId": "711", "endLine": 11, "endColumn": 10}, {"ruleId": "708", "severity": 1, "message": "721", "line": 18, "column": 3, "nodeType": "710", "messageId": "711", "endLine": 18, "endColumn": 13}, {"ruleId": "708", "severity": 1, "message": "772", "line": 28, "column": 9, "nodeType": "710", "messageId": "711", "endLine": 28, "endColumn": 17}, {"ruleId": "725", "severity": 1, "message": "865", "line": 36, "column": 6, "nodeType": "727", "endLine": 36, "endColumn": 8, "suggestions": "866"}, {"ruleId": "708", "severity": 1, "message": "867", "line": 19, "column": 11, "nodeType": "710", "messageId": "711", "endLine": 19, "endColumn": 24}, {"ruleId": "868", "severity": 1, "message": "869", "line": 73, "column": 111, "nodeType": "870", "messageId": "871", "endLine": 73, "endColumn": 112, "suggestions": "872"}, {"ruleId": "868", "severity": 1, "message": "869", "line": 95, "column": 89, "nodeType": "870", "messageId": "871", "endLine": 95, "endColumn": 90, "suggestions": "873"}, {"ruleId": "708", "severity": 1, "message": "817", "line": 6, "column": 3, "nodeType": "710", "messageId": "711", "endLine": 6, "endColumn": 10}, {"ruleId": "708", "severity": 1, "message": "718", "line": 7, "column": 3, "nodeType": "710", "messageId": "711", "endLine": 7, "endColumn": 6}, {"ruleId": "708", "severity": 1, "message": "721", "line": 8, "column": 3, "nodeType": "710", "messageId": "711", "endLine": 8, "endColumn": 13}, {"ruleId": "708", "severity": 1, "message": "874", "line": 9, "column": 3, "nodeType": "710", "messageId": "711", "endLine": 9, "endColumn": 10}, {"ruleId": "725", "severity": 1, "message": "875", "line": 77, "column": 6, "nodeType": "727", "endLine": 77, "endColumn": 14, "suggestions": "876"}, {"ruleId": "725", "severity": 1, "message": "877", "line": 30, "column": 6, "nodeType": "727", "endLine": 30, "endColumn": 16, "suggestions": "878"}, {"ruleId": "708", "severity": 1, "message": "879", "line": 166, "column": 9, "nodeType": "710", "messageId": "711", "endLine": 166, "endColumn": 21}, {"ruleId": "725", "severity": 1, "message": "880", "line": 14, "column": 6, "nodeType": "727", "endLine": 14, "endColumn": 21, "suggestions": "881"}, {"ruleId": "708", "severity": 1, "message": "882", "line": 23, "column": 10, "nodeType": "710", "messageId": "711", "endLine": 23, "endColumn": 27}, {"ruleId": "708", "severity": 1, "message": "883", "line": 10, "column": 3, "nodeType": "710", "messageId": "711", "endLine": 10, "endColumn": 10}, {"ruleId": "708", "severity": 1, "message": "884", "line": 56, "column": 9, "nodeType": "710", "messageId": "711", "endLine": 56, "endColumn": 22}, {"ruleId": "708", "severity": 1, "message": "762", "line": 11, "column": 3, "nodeType": "710", "messageId": "711", "endLine": 11, "endColumn": 8}, {"ruleId": "708", "severity": 1, "message": "885", "line": 12, "column": 3, "nodeType": "710", "messageId": "711", "endLine": 12, "endColumn": 10}, {"ruleId": "708", "severity": 1, "message": "886", "line": 13, "column": 3, "nodeType": "710", "messageId": "711", "endLine": 13, "endColumn": 9}, {"ruleId": "708", "severity": 1, "message": "887", "line": 1, "column": 38, "nodeType": "710", "messageId": "711", "endLine": 1, "endColumn": 46}, {"ruleId": "708", "severity": 1, "message": "888", "line": 112, "column": 23, "nodeType": "710", "messageId": "711", "endLine": 112, "endColumn": 34}, {"ruleId": "708", "severity": 1, "message": "860", "line": 7, "column": 11, "nodeType": "710", "messageId": "711", "endLine": 7, "endColumn": 15}, {"ruleId": "708", "severity": 1, "message": "826", "line": 2, "column": 10, "nodeType": "710", "messageId": "711", "endLine": 2, "endColumn": 16}, {"ruleId": "889", "severity": 1, "message": "890", "line": 69, "column": 3, "nodeType": "891", "messageId": "892", "endLine": 90, "endColumn": 5}, {"ruleId": "708", "severity": 1, "message": "893", "line": 198, "column": 10, "nodeType": "710", "messageId": "711", "endLine": 198, "endColumn": 22}, {"ruleId": "708", "severity": 1, "message": "894", "line": 20, "column": 3, "nodeType": "710", "messageId": "711", "endLine": 20, "endColumn": 8}, {"ruleId": "708", "severity": 1, "message": "895", "line": 21, "column": 3, "nodeType": "710", "messageId": "711", "endLine": 21, "endColumn": 11}, {"ruleId": "708", "severity": 1, "message": "809", "line": 22, "column": 3, "nodeType": "710", "messageId": "711", "endLine": 22, "endColumn": 11}, {"ruleId": "725", "severity": 1, "message": "896", "line": 95, "column": 6, "nodeType": "727", "endLine": 95, "endColumn": 15, "suggestions": "897"}, {"ruleId": "708", "severity": 1, "message": "898", "line": 128, "column": 5, "nodeType": "710", "messageId": "711", "endLine": 128, "endColumn": 14}, {"ruleId": "708", "severity": 1, "message": "826", "line": 2, "column": 10, "nodeType": "710", "messageId": "711", "endLine": 2, "endColumn": 16}, {"ruleId": "708", "severity": 1, "message": "790", "line": 2, "column": 18, "nodeType": "710", "messageId": "711", "endLine": 2, "endColumn": 33}, {"ruleId": "708", "severity": 1, "message": "899", "line": 15, "column": 9, "nodeType": "710", "messageId": "711", "endLine": 15, "endColumn": 21}, {"ruleId": "708", "severity": 1, "message": "900", "line": 21, "column": 9, "nodeType": "710", "messageId": "711", "endLine": 21, "endColumn": 21}, {"ruleId": "708", "severity": 1, "message": "901", "line": 64, "column": 9, "nodeType": "710", "messageId": "711", "endLine": 64, "endColumn": 22}, {"ruleId": "708", "severity": 1, "message": "902", "line": 84, "column": 9, "nodeType": "710", "messageId": "711", "endLine": 84, "endColumn": 24}, {"ruleId": "725", "severity": 1, "message": "903", "line": 126, "column": 6, "nodeType": "727", "endLine": 126, "endColumn": 32, "suggestions": "904", "suppressions": "905"}, {"ruleId": "708", "severity": 1, "message": "749", "line": 7, "column": 9, "nodeType": "710", "messageId": "711", "endLine": 7, "endColumn": 19}, {"ruleId": "708", "severity": 1, "message": "906", "line": 6, "column": 10, "nodeType": "710", "messageId": "711", "endLine": 6, "endColumn": 24}, {"ruleId": "708", "severity": 1, "message": "907", "line": 7, "column": 8, "nodeType": "710", "messageId": "711", "endLine": 7, "endColumn": 21}, {"ruleId": "708", "severity": 1, "message": "908", "line": 9, "column": 10, "nodeType": "710", "messageId": "711", "endLine": 9, "endColumn": 17}, {"ruleId": "708", "severity": 1, "message": "771", "line": 21, "column": 10, "nodeType": "710", "messageId": "711", "endLine": 21, "endColumn": 29}, {"ruleId": "708", "severity": 1, "message": "909", "line": 22, "column": 10, "nodeType": "710", "messageId": "711", "endLine": 22, "endColumn": 22}, {"ruleId": "708", "severity": 1, "message": "910", "line": 68, "column": 9, "nodeType": "710", "messageId": "711", "endLine": 68, "endColumn": 35}, {"ruleId": "708", "severity": 1, "message": "911", "line": 78, "column": 9, "nodeType": "710", "messageId": "711", "endLine": 78, "endColumn": 23}, {"ruleId": "708", "severity": 1, "message": "912", "line": 104, "column": 13, "nodeType": "710", "messageId": "711", "endLine": 104, "endColumn": 26}, {"ruleId": "708", "severity": 1, "message": "858", "line": 12, "column": 3, "nodeType": "710", "messageId": "711", "endLine": 12, "endColumn": 10}, {"ruleId": "708", "severity": 1, "message": "721", "line": 14, "column": 3, "nodeType": "710", "messageId": "711", "endLine": 14, "endColumn": 13}, {"ruleId": "708", "severity": 1, "message": "913", "line": 62, "column": 9, "nodeType": "710", "messageId": "711", "endLine": 62, "endColumn": 32}, {"ruleId": "708", "severity": 1, "message": "914", "line": 5, "column": 3, "nodeType": "710", "messageId": "711", "endLine": 5, "endColumn": 10}, {"ruleId": "708", "severity": 1, "message": "782", "line": 12, "column": 3, "nodeType": "710", "messageId": "711", "endLine": 12, "endColumn": 10}, {"ruleId": "708", "severity": 1, "message": "915", "line": 13, "column": 3, "nodeType": "710", "messageId": "711", "endLine": 13, "endColumn": 12}, "no-unused-vars", "'startTransition' is defined but never used.", "Identifier", "unusedVar", "'HideLoading' is defined but never used.", "'ShowLoading' is defined but never used.", "'AdminNavigation' is defined but never used.", "'TbHome' is defined but never used.", "'TbBrandTanzania' is defined but never used.", "'TbMenu2' is defined but never used.", "'TbX' is defined but never used.", "'TbChevronDown' is defined but never used.", "'TbLogout' is defined but never used.", "'TbSettings' is defined but never used.", "'TbBell' is defined but never used.", "'TbStar' is defined but never used.", "'OnlineStatusIndicator' is defined but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has missing dependencies: 'dispatch', 'getUserData', 'navigate', and 'user'. Either include them or remove the dependency array.", "ArrayExpression", ["916"], "React Hook useEffect has missing dependencies: 'dispatch' and 'verifyPaymentStatus'. Either include them or remove the dependency array.", ["917"], "React Hook useEffect has a missing dependency: 'verifyPaymentStatus'. Either include it or remove the dependency array.", ["918"], "'getButtonClass' is assigned a value but never used.", "'Select' is defined but never used.", "React Hook useEffect has missing dependencies: 'getExamData' and 'params.id'. Either include them or remove the dependency array.", ["919"], "React Hook useEffect has a missing dependency: 'fetchUsers'. Either include it or remove the dependency array.", ["920"], ["921"], "React Hook useCallback has missing dependencies: 'quiz.category', 'quiz.name', 'quiz.passingMarks', 'quiz.passingPercentage', 'quiz.subject', and 'submitting'. Either include them or remove the dependency array.", ["922"], "jsx-a11y/img-redundant-alt", "Redundant alt attribute. Screen-readers already announce `img` tags as an image. You don’t need to use the words `image`, `photo,` or `picture` (or any specified custom words) in the alt prop.", "JSXOpeningElement", "'TbBrain' is defined but never used.", "'TbBolt' is defined but never used.", "React Hook useEffect has missing dependencies: 'getUserResults' and 'user'. Either include them or remove the dependency array.", ["923"], "'formatTime' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'getExamsData'. Either include it or remove the dependency array.", ["924"], "'Input' is defined but never used.", "'Form' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchQuestions'. Either include it or remove the dependency array.", ["925"], "'TbDashboard' is defined but never used.", "'navigate' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'getData' and 'pagination'. Either include them or remove the dependency array. Mutable values like 'pagination.current' aren't valid dependencies because mutating them doesn't re-render the component.", ["926"], "'TbTarget' is defined but never used.", "'TbClock' is defined but never used.", "'TbEye' is defined but never used.", "'loading' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchDashboardData'. Either include it or remove the dependency array.", ["927"], "'PageTitle' is defined but never used.", "'TbPlus' is defined but never used.", "'setLoading' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'getUsersData'. Either include it or remove the dependency array.", ["928"], "'processingStartTime' is assigned a value but never used.", "'dispatch' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchPlans'. Either include it or remove the dependency array.", ["929"], "React Hook useEffect has a missing dependency: 'isSubscriptionExpired'. Either include it or remove the dependency array.", ["930"], "'Suspense' is defined but never used.", "'isAdmin' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'dispatch' and 'getUserData'. Either include them or remove the dependency array.", ["931"], "'TbCalendar' is defined but never used.", "'TbAward' is defined but never used.", "'TbDownload' is defined but never used.", "'viewMode' is assigned a value but never used.", "'setViewMode' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'getData'. Either include it or remove the dependency array.", ["932"], "React Hook useEffect has a missing dependency: 'applyFilters'. Either include it or remove the dependency array.", ["933"], "'AnimatePresence' is defined but never used.", "'getUserRanking' is defined but never used.", "'showStats' is assigned a value but never used.", "'setShowStats' is assigned a value but never used.", "'animationPhase' is assigned a value but never used.", "'currentUserLeague' is assigned a value but never used.", "'showLeagueView' is assigned a value but never used.", "'headerRef' is assigned a value but never used.", "'currentUserRef' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'fetchFullUserData', 'fetchRankingData', 'motivationalQuotes', 'rankingData', and 'user'. Either include them or remove the dependency array.", ["934"], "'otherPerformers' is assigned a value but never used.", "'getSubscriptionBadge' is assigned a value but never used.", "'leagueKey' is assigned a value but never used.", "no-dupe-keys", "Duplicate key 'y'.", "ObjectExpression", "unexpected", "'FaChevronDown' is defined but never used.", "'FaSearch' is defined but never used.", "'TbSearch' is defined but never used.", "'TbFilter' is defined but never used.", "'TbSortAscending' is defined but never used.", "'TbUser' is defined but never used.", "'TbChevronUp' is defined but never used.", "'TbAlertTriangle' is defined but never used.", "'TbInfoCircle' is defined but never used.", "'TbCheck' is defined but never used.", "The 'allPossibleClasses' conditional could make the dependencies of useCallback Hook (at line 114) change on every render. Move it inside the useCallback callback. Alternatively, wrap the initialization of 'allPossibleClasses' in its own useMemo() Hook.", "VariableDeclarator", "React Hook useMemo has an unnecessary dependency: 'activeTab'. Either exclude it or remove the dependency array.", ["935"], "'extractUserResultData' is defined but never used.", "'safeNumber' is defined but never used.", "React Hook useCallback has a missing dependency: 'startTime'. Either include it or remove the dependency array.", ["936"], "'motion' is defined but never used.", "import/first", "Import in body of module; reorder to top.", "ImportDeclaration", {"range": "937", "text": "938"}, "'handleClearAll' is assigned a value but never used.", "'FaHome' is defined but never used.", "'FaCreditCard' is defined but never used.", "'FaRobot' is defined but never used.", "'FaSignOutAlt' is defined but never used.", "'handleLogout' is assigned a value but never used.", "'useRef' is defined but never used.", "'Avatar' is defined but never used.", "'image' is defined but never used.", "'totalPages' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'clearAllForumCaches' and 'fetchQuestions'. Either include them or remove the dependency array.", ["939"], "React Hook useEffect has a missing dependency: 'getUserData'. Either include it or remove the dependency array.", ["940"], "React Hook useEffect has a missing dependency: 'form2'. Either include it or remove the dependency array.", ["941"], "'Button' is defined but never used.", "'userRanking' is assigned a value but never used.", "'imagePreview' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'getUserStats'. Either include it or remove the dependency array.", ["942"], ["943"], "'handleLevelChangeCancel' is assigned a value but never used.", "'handleImageUpload' is assigned a value but never used.", ["944"], "React Hook useEffect has a missing dependency: 'fetchUserRankingData'. Either include it or remove the dependency array.", ["945"], "'TbRobot' is defined but never used.", "'TbCreditCard' is defined but never used.", "'user' is assigned a value but never used.", "react/jsx-no-duplicate-props", "No duplicate props allowed", "JSXAttribute", "noDuplicateProps", "React Hook useEffect has a missing dependency: 'clearChat'. Either include it or remove the dependency array.", ["946"], "'restoredLines' is assigned a value but never used.", "no-useless-escape", "Unnecessary escape character: \\[.", "Literal", "unnecessaryEscape", ["947", "948"], ["949", "950"], "'TbTrash' is defined but never used.", "React Hook useEffect has missing dependencies: 'fetchNotifications' and 'notifications.length'. Either include them or remove the dependency array.", ["951"], "React Hook useEffect has a missing dependency: 'handleSubmitQuiz'. Either include it or remove the dependency array.", ["952"], "'goToQuestion' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchTrialQuiz'. Either include it or remove the dependency array.", ["953"], "'animationComplete' is assigned a value but never used.", "'TbUsers' is defined but never used.", "'getTimerColor' is assigned a value but never used.", "'TbPhoto' is defined but never used.", "'TbEdit' is defined but never used.", "'Fragment' is defined but never used.", "'toggleTheme' is assigned a value but never used.", "no-unreachable", "Unreachable code.", "ReturnStatement", "unreachableCode", "'containerRef' is assigned a value but never used.", "'FaEye' is defined but never used.", "'FaFilter' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchMaterials'. Either include it or remove the dependency array.", ["954"], "'xpAwarded' is assigned a value but never used.", "'levelOptions' is assigned a value but never used.", "'classOptions' is assigned a value but never used.", "'modalVariants' is assigned a value but never used.", "'overlayVariants' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'renderPDF'. Either include it or remove the dependency array.", ["955"], ["956"], "'updateUserInfo' is defined but never used.", "'axiosInstance' is defined but never used.", "'SetUser' is defined but never used.", "'showTryAgain' is assigned a value but never used.", "'handleCloseProcessingModal' is assigned a value but never used.", "'handleTryAgain' is assigned a value but never used.", "'tryAgainTimer' is assigned a value but never used.", "'triggerLevelUpAnimation' is assigned a value but never used.", "'TbMedal' is defined but never used.", "'TbDiamond' is defined but never used.", {"desc": "957", "fix": "958"}, {"desc": "959", "fix": "960"}, {"desc": "961", "fix": "962"}, {"desc": "963", "fix": "964"}, {"desc": "965", "fix": "966"}, {"kind": "967", "justification": "968"}, {"desc": "969", "fix": "970"}, {"desc": "971", "fix": "972"}, {"desc": "973", "fix": "974"}, {"desc": "975", "fix": "976"}, {"desc": "977", "fix": "978"}, {"desc": "979", "fix": "980"}, {"desc": "981", "fix": "982"}, {"desc": "983", "fix": "984"}, {"desc": "985", "fix": "986"}, {"desc": "987", "fix": "988"}, {"desc": "989", "fix": "990"}, {"desc": "991", "fix": "992"}, {"desc": "993", "fix": "994"}, {"desc": "995", "fix": "996"}, {"desc": "997", "fix": "998"}, [0, 1642], "import React, { useState, useEffect, useCallback, useMemo } from \"react\";\nimport \"./index.css\";\nimport { motion, AnimatePresence } from \"framer-motion\";\nimport { getStudyMaterial } from \"../../../apicalls/study\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { HideLoading, ShowLoading } from \"../../../redux/loaderSlice\";\nimport { primarySubjects, secondarySubjects, advanceSubjects } from \"../../../data/Subjects.jsx\";\n\n// Temporary fix: Use simple text/symbols instead of React Icons to avoid chunk loading issues\nconst IconComponents = {\n  FaPlayCircle: () => <span style={{fontSize: '24px'}}>▶️</span>,\n  FaGraduationCap: () => <span style={{fontSize: '24px'}}>🎓</span>,\n  FaTimes: () => <span style={{fontSize: '18px'}}>✕</span>,\n  FaExpand: () => <span style={{fontSize: '18px'}}>⛶</span>,\n  FaCompress: () => <span style={{fontSize: '18px'}}>⛶</span>,\n  TbVideo: () => <span style={{fontSize: '24px'}}>📹</span>,\n  TbFilter: () => <span style={{fontSize: '18px'}}>🔍</span>,\n  TbSortAscending: () => <span style={{fontSize: '18px'}}>↑</span>,\n  TbSearch: () => <span style={{fontSize: '18px'}}>🔍</span>,\n  TbX: () => <span style={{fontSize: '16px'}}>✕</span>,\n  TbDownload: () => <span style={{fontSize: '18px'}}>↻</span>,\n  TbAlertTriangle: () => <span style={{fontSize: '24px', color: '#ff6b6b'}}>⚠️</span>,\n  TbInfoCircle: () => <span style={{fontSize: '18px'}}>ℹ️</span>\n};\n\n// Destructure for easy use\nconst {\n  FaPlayCircle,\n  FaGraduationCap,\n  FaTimes,\n  FaExpand,\n  FaCompress,\n  TbVideo,\n  TbFilter,\n  TbSortAscending,\n  TbSearch,\n  TbX,\n  TbDownload,\n  TbAlertTriangle,\n  TbInfoCircle\n} = IconComponents;", {"desc": "999", "fix": "1000"}, {"desc": "1001", "fix": "1002"}, {"desc": "1003", "fix": "1004"}, {"desc": "1005", "fix": "1006"}, {"desc": "1001", "fix": "1007"}, {"desc": "1001", "fix": "1008"}, {"desc": "1009", "fix": "1010"}, {"desc": "1011", "fix": "1012"}, {"messageId": "1013", "fix": "1014", "desc": "1015"}, {"messageId": "1016", "fix": "1017", "desc": "1018"}, {"messageId": "1013", "fix": "1019", "desc": "1015"}, {"messageId": "1016", "fix": "1020", "desc": "1018"}, {"desc": "1021", "fix": "1022"}, {"desc": "1023", "fix": "1024"}, {"desc": "1025", "fix": "1026"}, {"desc": "1027", "fix": "1028"}, {"desc": "1029", "fix": "1030"}, {"kind": "967", "justification": "968"}, "Update the dependencies array to be: [dispatch, getUserData, navigate, user]", {"range": "1031", "text": "1032"}, "Update the dependencies array to be: [dispatch, paymentVerificationNeeded, user, verifyPaymentStatus]", {"range": "1033", "text": "1034"}, "Update the dependencies array to be: [user, activeRoute, verifyPaymentStatus]", {"range": "1035", "text": "1036"}, "Update the dependencies array to be: [getExamData, params.id]", {"range": "1037", "text": "1038"}, "Update the dependencies array to be: [fetchUsers]", {"range": "1039", "text": "1040"}, "directive", "", "Update the dependencies array to be: [submitting, user, startTime, questions, quiz.passingMarks, quiz.passingPercentage, quiz.name, quiz.subject, quiz.category, id, navigate, answers]", {"range": "1041", "text": "1042"}, "Update the dependencies array to be: [getUserResults, user]", {"range": "1043", "text": "1044"}, "Update the dependencies array to be: [getExamsData]", {"range": "1045", "text": "1046"}, "Update the dependencies array to be: [fetchQuestions]", {"range": "1047", "text": "1048"}, "Update the dependencies array to be: [filters, getData, pagination]", {"range": "1049", "text": "1050"}, "Update the dependencies array to be: [fetchDashboardData]", {"range": "1051", "text": "1052"}, "Update the dependencies array to be: [getUsersData]", {"range": "1053", "text": "1054"}, "Update the dependencies array to be: [fetchPlans]", {"range": "1055", "text": "1056"}, "Update the dependencies array to be: [isSubscriptionExpired, subscriptionData]", {"range": "1057", "text": "1058"}, "Update the dependencies array to be: [dispatch, getUserData]", {"range": "1059", "text": "1060"}, "Update the dependencies array to be: [getData]", {"range": "1061", "text": "1062"}, "Update the dependencies array to be: [filterSubject, filterVerdict, dateRange, reportsData, applyFilters]", {"range": "1063", "text": "1064"}, "Update the dependencies array to be: [fetchFullUserData, fetchRankingData, motivationalQuotes, rankingData, user]", {"range": "1065", "text": "1066"}, "Update the dependencies array to be: [materials, searchTerm, sortBy]", {"range": "1067", "text": "1068"}, "Update the dependencies array to be: [user, dispatch, questions, startTime, examData?.duration, examData.passingMarks, params.id, navigate, selectedOptions]", {"range": "1069", "text": "1070"}, "Update the dependencies array to be: [clearAllForumCaches, currentPage, fetchQuestions, limit]", {"range": "1071", "text": "1072"}, "Update the dependencies array to be: [getUserData]", {"range": "1073", "text": "1074"}, "Update the dependencies array to be: [editQuestion, form2]", {"range": "1075", "text": "1076"}, "Update the dependencies array to be: [getUserStats, rankingData, userDetails]", {"range": "1077", "text": "1078"}, {"range": "1079", "text": "1074"}, {"range": "1080", "text": "1074"}, "Update the dependencies array to be: [fetchUserRankingData, userDetails]", {"range": "1081", "text": "1082"}, "Update the dependencies array to be: [clearChat]", {"range": "1083", "text": "1084"}, "removeEscape", {"range": "1085", "text": "968"}, "Remove the `\\`. This maintains the current functionality.", "escape<PERSON><PERSON><PERSON><PERSON>", {"range": "1086", "text": "1087"}, "Replace the `\\` with `\\\\` to include the actual backslash character.", {"range": "1088", "text": "968"}, {"range": "1089", "text": "1087"}, "Update the dependencies array to be: [fetchNotifications, isOpen, notifications.length]", {"range": "1090", "text": "1091"}, "Update the dependencies array to be: [handleSubmitQuiz, timeLeft]", {"range": "1092", "text": "1093"}, "Update the dependencies array to be: [fetchTrialQuiz, trialUserInfo]", {"range": "1094", "text": "1095"}, "Update the dependencies array to be: [fetchMaterials, filters]", {"range": "1096", "text": "1097"}, "Update the dependencies array to be: [modalIsOpen, documentUrl, renderPDF]", {"range": "1098", "text": "1099"}, [4456, 4458], "[dispatch, getUserData, navigate, user]", [7410, 7443], "[dispatch, paymentVerificationNeeded, user, verifyPaymentStatus]", [7953, 7972], "[user, activeRoute, verifyPaymentStatus]", [3399, 3401], "[getExamData, params.id]", [1327, 1329], "[fetchUsers]", [11677, 11728], "[submitting, user, startTime, questions, quiz.passingMarks, quiz.passingPercentage, quiz.name, quiz.subject, quiz.category, id, navigate, answers]", [9114, 9116], "[getUser<PERSON><PERSON><PERSON><PERSON>, user]", [5318, 5320], "[getExamsData]", [3074, 3076], "[fetchQuestions]", [7040, 7069], "[filters, getData, pagination]", [1196, 1198], "[fetchDashboardData]", [5587, 5589], "[getUsersData]", [3149, 3151], "[fetchPlans]", [4869, 4887], "[isSubscriptionExpired, subscriptionData]", [1990, 1992], "[dispatch, getUserData]", [4129, 4131], "[getData]", [4184, 4238], "[filterSubject, filterVerdict, dateRange, reportsData, applyFilters]", [34517, 34519], "[fetchFullUserData, fetchRankingData, motivationalQuotes, rankingData, user]", [7963, 8005], "[materials, searchTerm, sortBy]", [28756, 28831], "[user, dispatch, questions, startTime, examData?.duration, examData.passingMarks, params.id, navigate, selectedOptions]", [5683, 5703], "[clearAllForumCaches, currentPage, fetchQuestions, limit]", [6451, 6453], "[getUserData]", [9851, 9865], "[editQuestion, form2]", [3730, 3756], "[getUserStats, rankingData, userDetails]", [4698, 4700], [10265, 10267], [10417, 10430], "[fetchUserRankingData, userDetails]", [1381, 1383], "[clearChat]", [3500, 3501], [3500, 3500], "\\", [5011, 5012], [5011, 5011], [2302, 2310], "[fetchNotifications, isOpen, notifications.length]", [1032, 1042], "[handleSubmitQuiz, timeLeft]", [527, 542], "[fetchTrialQuiz, trialUserInfo]", [2411, 2420], "[fetchMaterials, filters]", [3978, 4004], "[modalIsOpen, documentUrl, renderPDF]"]