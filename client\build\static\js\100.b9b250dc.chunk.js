"use strict";(self.webpackChunkclient=self.webpackChunkclient||[]).push([[100],{2713:(e,a,s)=>{s.d(a,{A4:()=>x,AL:()=>n,FM:()=>h,HH:()=>d,Hh:()=>y,KB:()=>m,MC:()=>v,Qk:()=>u,SL:()=>o,TA:()=>c,X9:()=>i,cN:()=>r,f_:()=>j,o$:()=>l,u8:()=>p});const{default:t}=s(3371),l=async e=>{try{return await t.post("/api/study/get-study-content",e)}catch(a){return a.response}},i=async()=>{try{return(await t.get("/api/study/videos-subtitle-status")).data}catch(a){var e;return(null===(e=a.response)||void 0===e?void 0:e.data)||{success:!1,message:"Failed to fetch videos"}}},r=async function(e){let a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;try{const s=e instanceof FormData?{headers:{"Content-Type":"multipart/form-data"},timeout:6e5,onUploadProgress:a?e=>{const s=Math.round(100*e.loaded/e.total);a(s,e.loaded,e.total)}:void 0}:{timeout:6e4};return await t.post("/api/study/add-video",e,s)}catch(s){return s.response}},n=async e=>{try{return await t.post("/api/study/add-note",e,{headers:{"Content-Type":"multipart/form-data"}})}catch(a){return a.response}},o=async e=>{try{return await t.post("/api/study/add-past-paper",e,{headers:{"Content-Type":"multipart/form-data"}})}catch(a){return a.response}},c=async e=>{try{return await t.post("/api/study/add-book",e,{headers:{"Content-Type":"multipart/form-data"}})}catch(a){return a.response}},d=async(e,a)=>{try{let s={headers:{"Content-Type":"application/json"}};a instanceof FormData&&(s.headers["Content-Type"]="multipart/form-data");return await t.put("/api/study/update-video/".concat(e),a,s)}catch(s){return s.response}},u=async(e,a)=>{try{return await t.put("/api/study/update-note/".concat(e),a,{headers:{"Content-Type":"multipart/form-data"}})}catch(s){return s.response}},m=async(e,a)=>{try{return await t.put("/api/study/update-past-paper/".concat(e),a,{headers:{"Content-Type":"multipart/form-data"}})}catch(s){return s.response}},p=async(e,a)=>{try{return await t.put("/api/study/update-book/".concat(e),a,{headers:{"Content-Type":"multipart/form-data"}})}catch(s){return s.response}},h=async e=>{try{return await t.delete("/api/study/delete-video/".concat(e))}catch(a){return a.response}},j=async e=>{try{return await t.delete("/api/study/delete-note/".concat(e))}catch(a){return a.response}},x=async e=>{try{return await t.delete("/api/study/delete-past-paper/".concat(e))}catch(a){return a.response}},v=async e=>{try{return await t.delete("/api/study/delete-book/".concat(e))}catch(a){return a.response}},y=async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};try{const a=new URLSearchParams;e.materialType&&a.append("materialType",e.materialType),e.level&&a.append("level",e.level),e.className&&a.append("className",e.className),e.subject&&a.append("subject",e.subject);return await t.get("/api/study/admin/all-materials?".concat(a.toString()))}catch(a){return a.response}}},640:(e,a,s)=>{s.d(a,{Z:()=>i});var t=s(2791),l=s(184);const i=function(e){let{title:a}=e;const[s,i]=(0,t.useState)(!1);return(0,t.useEffect)((()=>{window.innerWidth<768&&i(!0)}),[]),(0,l.jsx)("div",{className:"mt-2",children:(0,l.jsx)("h1",{className:s?"text-lg":"",children:a})})}},1304:(e,a,s)=>{s.d(a,{Lo:()=>l,iq:()=>t,vp:()=>i});const t=["Mathematics","Science and Technology","Geography","Kiswahili","SocialStudies","English","Religion","Arithmetic","Sport and Art","Health and Environment","Civic and Moral","French","Historia ya Tanzania"],l=["Physics","Mathematics","Biology","Chemistry","Geography","Civics","History","Computer","English language","Kiswahili","Commerce","Economics","Advanced Mathematics","Basic Applied Mathematics","Accountancy","Agriculture"],i=["Physics","Mathematics","Biology","Chemistry","Geography","Civics","History","Computer","English language","Kiswahili","Commerce","Economics","Advanced Mathematics","Basic Applied Mathematics","Accountancy","Agriculture"]},5100:(e,a,s)=>{s.r(a),s.d(a,{default:()=>V});var t=s(2791),l=s(7027),i=s(6042),r=s(7689),n=s(5526),o=s(640),c=s(1413),d=s(5725),u=s(7846),m=s(9736),p=s(7309),h=s(9434),j=s(8247),x=s(2713),v=s(1304),y=s(2202),b=s(184);const{Option:g}=d.default;const f=function(e){let{materialType:a,onSuccess:s,onCancel:i}=e;const[r]=u.Z.useForm(),n=(0,h.I0)(),[o,f]=(0,t.useState)(!1),[N,w]=(0,t.useState)([]),[Z,S]=(0,t.useState)([]),[C,k]=(0,t.useState)([]),[P,U]=(0,t.useState)("youtube"),[F,T]=(0,t.useState)(0),[I,L]=(0,t.useState)(""),[M,D]=(0,t.useState)(0),[A,E]=(0,t.useState)(0),[O,B]=(0,t.useState)(null),[z,G]=(0,t.useState)(!1),[V,R]=(0,t.useState)(v.iq),[Q,q]=(0,t.useState)(["1","2","3","4","5","6","7"]),[H,Y]=(0,t.useState)([]),_={beforeUpload:()=>!1,maxCount:1,accept:"videos"===a?void 0:".pdf,.doc,.docx,.ppt,.pptx"},K={beforeUpload:()=>!1,maxCount:1,accept:"image/*"},W=()=>{switch(a){case"videos":return"Video";case"study-notes":return"Study Note";case"past-papers":return"Past Paper";case"books":return"Book";default:return"Material"}};return(0,b.jsx)("div",{className:"add-material-form",children:(0,b.jsxs)("div",{className:"form-card",children:[(0,b.jsxs)("div",{className:"form-header-icon",children:[(()=>{switch(a){case"videos":return(0,b.jsx)(y.KoQ,{});case"study-notes":default:return(0,b.jsx)(y.mGS,{});case"past-papers":return(0,b.jsx)(y.nGB,{});case"books":return(0,b.jsx)(y.Mp$,{})}})(),(0,b.jsxs)("h3",{children:["Add New ",W()]})]}),(0,b.jsxs)(u.Z,{form:r,layout:"vertical",onFinish:async e=>{let t;try{let o;if(f(!0),T(0),L(""),n((0,j.YC)()),t=setTimeout((()=>{100===F&&(f(!1),T(0),L(""),n((0,j.Ir)()),l.ZP.success("Video uploaded successfully! Thumbnail generation continues in background."))}),1e4),"videos"===a){const a=(0,c.Z)((0,c.Z)({},e),{},{additionalClasses:H});if("youtube"===P)L("Adding YouTube video..."),o=await(0,x.cN)(a);else if("s3url"===P)if(Z.length>0&&Z[0].originFileObj){const e=new FormData;Object.keys(a).forEach((s=>{void 0!==a[s]&&null!==a[s]&&(Array.isArray(a[s])?a[s].forEach((a=>e.append(s,a))):e.append(s,a[s]))})),e.append("thumbnail",Z[0].originFileObj),L("Uploading thumbnail and adding video..."),o=await(0,x.cN)(e)}else L("Adding S3 video..."),o=await(0,x.cN)(a);else{const e=new FormData;Object.keys(a).forEach((s=>{void 0!==a[s]&&null!==a[s]&&(Array.isArray(a[s])?a[s].forEach((a=>e.append(s,a))):e.append(s,a[s]))})),C.length>0&&C[0].originFileObj&&(e.append("video",C[0].originFileObj),L("Uploading video file...")),Z.length>0&&Z[0].originFileObj&&(console.log("\ud83d\udcce Adding thumbnail to upload:",Z[0].name),e.append("thumbnail",Z[0].originFileObj)),B(Date.now()),o=await(0,x.cN)(e,((e,a,s)=>{if(T(e),O){const t=a||s*e/100,l=t/((Date.now()-O)/1e3),i=(s-t)/l;D(l),E(i)}100===e?L("Finalizing upload..."):e>0&&L("Uploading... ".concat(e,"%"))}))}}else{const s=new FormData;switch(Object.keys(e).forEach((a=>{void 0!==e[a]&&null!==e[a]&&s.append(a,e[a])})),N.length>0&&N[0].originFileObj&&s.append("document",N[0].originFileObj),"books"===a&&Z.length>0&&Z[0].originFileObj&&s.append("thumbnail",Z[0].originFileObj),a){case"study-notes":o=await(0,x.AL)(s);break;case"past-papers":o=await(0,x.SL)(s);break;case"books":o=await(0,x.TA)(s);break;default:throw new Error("Invalid material type")}}if(201===o.status&&o.data.success)l.ZP.success(o.data.message),r.resetFields(),w([]),S([]),k([]),Y([]),U("youtube"),T(0),L(""),s(a);else{var i;const e=(null===(i=o.data)||void 0===i?void 0:i.message)||"Failed to add material";l.ZP.error(e)}}catch(p){var o,d,u;if(console.error("Error adding material:",p),"ECONNABORTED"===p.code)l.ZP.error("Upload timeout. Please try with a smaller file or check your internet connection.");else if(413===(null===(o=p.response)||void 0===o?void 0:o.status))l.ZP.error("File too large. Please use a file smaller than 500MB.");else if(400===(null===(d=p.response)||void 0===d?void 0:d.status)){var m;l.ZP.error((null===(m=p.response.data)||void 0===m?void 0:m.message)||"Invalid file or form data.")}else 500===(null===(u=p.response)||void 0===u?void 0:u.status)?l.ZP.error("Server error. Please try again later."):l.ZP.error("Upload failed. Please check your internet connection and try again.")}finally{t&&clearTimeout(t),f(!1),T(0),L(""),n((0,j.Ir)())}},initialValues:{level:"primary"},className:"material-form",children:[(0,b.jsxs)("div",{className:"form-row",children:[(0,b.jsx)(u.Z.Item,{label:"Level",name:"level",rules:[{required:!0,message:"Please select a level"}],className:"form-item-half",children:(0,b.jsxs)(d.default,{placeholder:"Select level",onChange:e=>{R((e=>{switch(e){case"primary":return v.iq;case"secondary":return v.Lo;case"advance":return v.vp;default:return[]}})(e)),q((e=>{switch(e){case"primary":return["1","2","3","4","5","6","7"];case"secondary":return["1","2","3","4"];case"advance":return["5","6"];default:return[]}})(e)),Y([]),r.setFieldsValue({subject:void 0,className:void 0})},size:"large",children:[(0,b.jsx)(g,{value:"primary",children:"Primary"}),(0,b.jsx)(g,{value:"secondary",children:"Secondary"}),(0,b.jsx)(g,{value:"advance",children:"Advance"})]})}),(0,b.jsx)(u.Z.Item,{label:"Class",name:"className",rules:[{required:!0,message:"Please select a class"}],className:"form-item-half",children:(0,b.jsx)(d.default,{placeholder:"Select class",size:"large",onChange:e=>{const a=H.filter((a=>a!==e));Y(a)},children:Q.map((e=>(0,b.jsx)(g,{value:e,children:e},e)))})})]}),(0,b.jsx)(u.Z.Item,{label:"Subject",name:"subject",rules:[{required:!0,message:"Please select a subject"}],children:(0,b.jsx)(d.default,{placeholder:"Select subject",size:"large",children:V.map((e=>(0,b.jsx)(g,{value:e,children:e},e)))})}),"videos"===a&&(0,b.jsxs)(u.Z.Item,{label:"Additional Classes (Optional)",className:"additional-classes-section",children:[(0,b.jsx)("div",{className:"additional-classes-info",children:(0,b.jsx)("p",{children:"Select additional classes that can access this video (besides the core class selected above)"})}),(0,b.jsx)(d.default,{mode:"multiple",placeholder:"Select additional classes (optional)",size:"large",value:H,onChange:e=>{Y(e)},style:{width:"100%"},children:Q.filter((e=>e!==r.getFieldValue("className"))).map((e=>(0,b.jsx)(g,{value:e,children:e},e)))}),(0,b.jsx)("div",{className:"additional-classes-note",children:(0,b.jsx)("small",{children:"Note: The video will be available to the core class and all selected additional classes"})})]}),(0,b.jsx)(u.Z.Item,{label:"Title",name:"title",rules:[{required:!0,message:"Please enter a title"}],children:(0,b.jsx)("input",{type:"text",placeholder:"Enter ".concat(W().toLowerCase()," title"),className:"form-input"})}),("past-papers"===a||"books"===a)&&(0,b.jsx)(u.Z.Item,{label:"Year",name:"year",rules:[{required:!0,message:"Please enter the year"}],children:(0,b.jsx)("input",{type:"text",placeholder:"Enter year (e.g., 2023)",className:"form-input"})}),"videos"===a&&(0,b.jsxs)(b.Fragment,{children:[(0,b.jsx)(u.Z.Item,{label:"Upload Method",className:"upload-method-section",children:(0,b.jsxs)("div",{className:"upload-method-selector",children:[(0,b.jsxs)("div",{className:"method-option ".concat("youtube"===P?"active":""),onClick:()=>U("youtube"),children:[(0,b.jsx)(y.KoQ,{className:"method-icon"}),(0,b.jsx)("span",{children:"YouTube Video"}),(0,b.jsx)("p",{children:"Add video using YouTube ID"})]}),(0,b.jsxs)("div",{className:"method-option ".concat("s3url"===P?"active":""),onClick:()=>U("s3url"),children:[(0,b.jsx)(y.Qvc,{className:"method-icon"}),(0,b.jsx)("span",{children:"S3 Object URL"}),(0,b.jsx)("p",{children:"Add video using S3 bucket URL"})]}),(0,b.jsxs)("div",{className:"method-option ".concat("upload"===P?"active":""),onClick:()=>U("upload"),children:[(0,b.jsx)(y.Qvc,{className:"method-icon"}),(0,b.jsx)("span",{children:"Upload Video File"}),(0,b.jsx)("p",{children:"Upload video file to server"})]})]})}),"youtube"===P?(0,b.jsxs)(b.Fragment,{children:[(0,b.jsx)(u.Z.Item,{label:"Video ID (YouTube)",name:"videoID",rules:[{required:"youtube"===P,message:"Please enter YouTube video ID"}],children:(0,b.jsx)("input",{type:"text",placeholder:"Enter YouTube video ID (e.g., dQw4w9WgXcQ)",className:"form-input"})}),(0,b.jsx)(u.Z.Item,{label:"Video URL (Optional)",name:"videoUrl",children:(0,b.jsx)("input",{type:"url",placeholder:"Enter video URL (optional)",className:"form-input"})}),(0,b.jsx)(u.Z.Item,{label:"Thumbnail URL (Optional)",name:"thumbnailUrl",children:(0,b.jsx)("input",{type:"url",placeholder:"Enter thumbnail URL (optional)",className:"form-input"})})]}):"s3url"===P?(0,b.jsxs)(b.Fragment,{children:[(0,b.jsx)(u.Z.Item,{label:"S3 Object URL",name:"videoUrl",rules:[{required:"s3url"===P,message:"Please enter S3 object URL"}],children:(0,b.jsx)("input",{type:"url",placeholder:"Enter S3 object URL (e.g., https://your-bucket.s3.amazonaws.com/video.mp4)",className:"form-input"})}),(0,b.jsx)(u.Z.Item,{label:"Drag & Drop Thumbnail (Optional)",className:"upload-section",children:(0,b.jsx)(m.Z,(0,c.Z)((0,c.Z)({},K),{},{fileList:Z,onChange:e=>{let{fileList:a}=e;return S(a)},className:"thumbnail-upload",onDrop:e=>{e.preventDefault(),G(!1);const a=Array.from(e.dataTransfer.files).filter((e=>e.type.startsWith("image/")));if(a.length>0){const e=a[0];if(e.size>5242880)return void l.ZP.error("Thumbnail file size must be less than 5MB");S([{uid:"-1",name:e.name,status:"done",originFileObj:e,url:URL.createObjectURL(e)}]),l.ZP.success("Thumbnail uploaded successfully!")}else l.ZP.error("Please drop an image file (JPG, PNG, GIF)")},onDragOver:e=>{e.preventDefault(),G(!0)},onDragEnter:e=>{e.preventDefault(),G(!0)},onDragLeave:e=>{e.preventDefault(),G(!1)},children:(0,b.jsxs)("div",{className:"upload-area small thumbnail-drop-zone ".concat(z?"drag-over":""),children:[(0,b.jsx)(y.DUB,{className:"upload-icon"}),(0,b.jsx)("p",{children:z?"Drop thumbnail here!":"Drag & drop thumbnail or click to upload"}),(0,b.jsx)("p",{className:"upload-hint",children:"Auto-generated if not provided"}),(0,b.jsx)("p",{className:"upload-hint",children:"Supports JPG, PNG, GIF (Max: 5MB)"})]})}))})]}):(0,b.jsxs)(b.Fragment,{children:[(0,b.jsx)(u.Z.Item,{label:"Upload Video File",className:"upload-section",children:(0,b.jsx)(m.Z,(0,c.Z)((0,c.Z)({},{beforeUpload:()=>!1,maxCount:1,accept:"video/*"}),{},{fileList:C,onChange:e=>{let{fileList:a}=e;return k(a)},className:"video-upload",children:(0,b.jsxs)("div",{className:"upload-area",children:[(0,b.jsx)(y.Qvc,{className:"upload-icon"}),(0,b.jsx)("p",{children:"Click or drag video file to upload"}),(0,b.jsx)("p",{className:"upload-hint",children:"Supports MP4, AVI, MOV, WMV (Max: 500MB)"}),(0,b.jsx)("p",{className:"upload-hint",children:"Large files may take several minutes to upload"})]})}))}),(0,b.jsx)(u.Z.Item,{label:"Upload Custom Thumbnail (Optional)",className:"upload-section",children:(0,b.jsx)(m.Z,(0,c.Z)((0,c.Z)({},K),{},{fileList:Z,onChange:e=>{let{fileList:a}=e;return S(a)},className:"thumbnail-upload",onDrop:e=>{e.preventDefault(),G(!1);const a=Array.from(e.dataTransfer.files).filter((e=>e.type.startsWith("image/")));if(a.length>0){const e=a[0];if(e.size>5242880)return void l.ZP.error("Thumbnail file size must be less than 5MB");S([{uid:"-1",name:e.name,status:"done",originFileObj:e,url:URL.createObjectURL(e)}]),l.ZP.success("Thumbnail uploaded successfully!")}else l.ZP.error("Please drop an image file (JPG, PNG, GIF)")},onDragOver:e=>{e.preventDefault(),G(!0)},onDragEnter:e=>{e.preventDefault(),G(!0)},onDragLeave:e=>{e.preventDefault(),G(!1)},children:(0,b.jsxs)("div",{className:"upload-area small thumbnail-drop-zone ".concat(z?"drag-over":""),children:[(0,b.jsx)(y.DUB,{className:"upload-icon"}),(0,b.jsx)("p",{children:z?"Drop thumbnail here!":"Drag & drop thumbnail or click to upload"}),(0,b.jsx)("p",{className:"upload-hint",children:"Auto-generated if not provided"}),(0,b.jsx)("p",{className:"upload-hint",children:"Supports JPG, PNG, GIF (Max: 5MB)"})]})}))})]})]}),"videos"!==a&&(0,b.jsx)(u.Z.Item,{label:"Upload ".concat(W()," Document"),className:"upload-section",children:(0,b.jsx)(m.Z,(0,c.Z)((0,c.Z)({},_),{},{fileList:N,onChange:e=>{let{fileList:a}=e;return w(a)},className:"document-upload",children:(0,b.jsxs)("div",{className:"upload-area",children:[(0,b.jsx)(y.Qvc,{className:"upload-icon"}),(0,b.jsx)("p",{children:"Click or drag file to upload"}),(0,b.jsx)("p",{className:"upload-hint",children:"Supports PDF, DOC, DOCX, PPT, PPTX"})]})}))}),"books"===a&&(0,b.jsx)(u.Z.Item,{label:"Upload Thumbnail (Optional)",className:"upload-section",children:(0,b.jsx)(m.Z,(0,c.Z)((0,c.Z)({},K),{},{fileList:Z,onChange:e=>{let{fileList:a}=e;return S(a)},className:"thumbnail-upload",children:(0,b.jsxs)("div",{className:"upload-area small",children:[(0,b.jsx)(y.DUB,{className:"upload-icon"}),(0,b.jsx)("p",{children:"Upload book cover"})]})}))}),o&&"upload"===P&&"videos"===a&&(0,b.jsxs)("div",{className:"upload-progress-section",children:[(0,b.jsxs)("div",{className:"progress-header",children:[(0,b.jsxs)("div",{className:"progress-info",children:[(0,b.jsx)("span",{className:"progress-text",children:I}),(0,b.jsxs)("span",{className:"progress-percentage",children:[F,"%"]})]}),M>0&&(0,b.jsxs)("div",{className:"upload-stats",children:[(0,b.jsxs)("span",{className:"upload-speed",children:["\ud83d\udcca ",(M/1048576).toFixed(2)," MB/s"]}),A>0&&A<3600&&(0,b.jsxs)("span",{className:"estimated-time",children:["\u23f1\ufe0f ",Math.ceil(A),"s remaining"]})]})]}),(0,b.jsx)("div",{className:"progress-bar",children:(0,b.jsx)("div",{className:"progress-fill",style:{width:"".concat(F,"%"),transition:"width 0.3s ease"}})}),(0,b.jsx)("div",{className:"progress-details",children:F<100?(0,b.jsxs)("div",{className:"uploading-info",children:[(0,b.jsx)("span",{children:"\ud83d\udce4 Uploading video file to server..."}),(0,b.jsx)("small",{children:"Please keep this tab open until upload completes"})]}):(0,b.jsxs)("div",{className:"processing-info",children:[(0,b.jsx)("span",{children:"\ud83c\udfac Upload complete! Processing video and generating thumbnail..."}),(0,b.jsx)("small",{children:"This may take a few moments for large files"})]})})]}),(0,b.jsxs)("div",{className:"form-actions",children:[(0,b.jsx)(p.ZP,{type:"default",onClick:i,size:"large",className:"cancel-btn",disabled:o,children:"Cancel"}),(0,b.jsx)(p.ZP,{type:"primary",htmlType:"submit",loading:o,size:"large",className:"submit-btn",children:o?"upload"===P&&"videos"===a?"Uploading...":"Adding...":"Add ".concat(W())})]})]})]})})};var N=s(2339),w=s(1046),Z=s(2879),S=s(222),C=s(6275),k=s(2616),P=s(3876),U=s(9771),F=s(2273),T=s(2351),I=s(3605);const{default:L}=s(3371),{Option:M}=d.default,D=()=>{const[e,a]=(0,t.useState)([]),[s,i]=(0,t.useState)(!1),[r,n]=(0,t.useState)(!1),[o,u]=(0,t.useState)(null),[v,y]=(0,t.useState)("en"),[g,f]=(0,t.useState)("English"),[D,A]=(0,t.useState)([]),[E,O]=(0,t.useState)({}),B=(0,h.I0)(),z=[{code:"en",name:"English"},{code:"es",name:"Spanish"},{code:"fr",name:"French"},{code:"de",name:"German"},{code:"it",name:"Italian"},{code:"pt",name:"Portuguese"},{code:"ru",name:"Russian"},{code:"ja",name:"Japanese"},{code:"ko",name:"Korean"},{code:"zh",name:"Chinese"},{code:"ar",name:"Arabic"},{code:"hi",name:"Hindi"},{code:"ur",name:"Urdu"},{code:"bn",name:"Bengali"}];(0,t.useEffect)((()=>{G()}),[]);const G=async()=>{try{i(!0);const e=await(0,x.X9)();e.success?a(e.data):l.ZP.error("Failed to fetch videos")}catch(e){l.ZP.error("Error fetching videos")}finally{i(!1)}},V=async function(e){let a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"en";try{O((a=>(0,c.Z)((0,c.Z)({},a),{},{[e]:!0}))),l.ZP.info("Generating subtitles... This may take a few minutes.");const s=await async function(e){let a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"en";try{return(await L.post("/api/study/generate-subtitles/".concat(e),{language:a})).data}catch(s){return s.response.data}}(e,a);s.success?(l.ZP.success("Subtitles generated successfully!"),G()):l.ZP.error(s.message||"Failed to generate subtitles")}catch(s){l.ZP.error("Error generating subtitles")}finally{O((a=>(0,c.Z)((0,c.Z)({},a),{},{[e]:!1})))}},R=[{title:"Video",dataIndex:"title",key:"title",width:"25%",render:(e,a)=>(0,b.jsxs)("div",{children:[(0,b.jsx)("div",{style:{fontWeight:"bold",marginBottom:"4px"},children:e}),(0,b.jsxs)("div",{style:{fontSize:"12px",color:"#666"},children:[a.subject," \u2022 Class ",a.className," \u2022 ",a.level]})]})},{title:"Subtitle Status",key:"subtitleStatus",width:"30%",render:(e,a)=>(e=>{var a;return"processing"===e.subtitleGenerationStatus?(0,b.jsx)(N.Z,{color:"blue",icon:(0,b.jsx)(k.Z,{}),children:"Generating..."}):e.hasSubtitles&&(null===(a=e.subtitles)||void 0===a?void 0:a.length)>0?(0,b.jsxs)(w.Z,{children:[(0,b.jsxs)(N.Z,{color:"green",icon:(0,b.jsx)(P.Z,{}),children:[e.subtitles.length," subtitle",e.subtitles.length>1?"s":""]}),e.subtitles.map((e=>(0,b.jsxs)(N.Z,{color:e.isAutoGenerated?"blue":"purple",size:"small",children:[e.languageName," ",e.isAutoGenerated?"(Auto)":"(Custom)"]},e.language)))]}):"failed"===e.subtitleGenerationStatus?(0,b.jsx)(N.Z,{color:"red",children:"Generation Failed"}):(0,b.jsx)(N.Z,{color:"default",children:"No Subtitles"})})(a)},{title:"Video Source",key:"videoSource",width:"15%",render:(e,a)=>a.videoUrl&&a.videoUrl.includes("amazonaws.com")?(0,b.jsx)(N.Z,{color:"orange",icon:(0,b.jsx)(U.Z,{}),children:"S3 Video"}):a.videoID&&!a.videoUrl?(0,b.jsx)(N.Z,{color:"red",icon:(0,b.jsx)(F.Z,{}),children:"YouTube"}):(0,b.jsx)(N.Z,{color:"default",children:"Unknown"})},{title:"Actions",key:"actions",width:"30%",render:(e,a)=>(0,b.jsxs)(w.Z,{children:[a.videoUrl&&a.videoUrl.includes("amazonaws.com")&&(0,b.jsx)(Z.Z,{title:"Generate subtitles using AI",children:(0,b.jsx)(p.ZP,{type:"primary",icon:(0,b.jsx)(k.Z,{}),size:"small",loading:E[a._id],onClick:()=>V(a._id),disabled:"processing"===a.subtitleGenerationStatus,children:"Generate"})}),(0,b.jsx)(Z.Z,{title:"Upload custom subtitle file",children:(0,b.jsx)(p.ZP,{icon:(0,b.jsx)(T.Z,{}),size:"small",onClick:()=>{u(a),n(!0)},children:"Upload"})}),(0,b.jsx)(Z.Z,{title:"Refresh video data",children:(0,b.jsx)(p.ZP,{icon:(0,b.jsx)(I.Z,{}),size:"small",onClick:G})})]})}],Q={beforeUpload:e=>e.name.toLowerCase().endsWith(".srt")?(A([e]),!1):(l.ZP.error("Please upload a .srt subtitle file"),!1),fileList:D,onRemove:()=>{A([])}};return(0,b.jsxs)("div",{className:"subtitle-manager",children:[(0,b.jsxs)("div",{className:"subtitle-manager-header",children:[(0,b.jsx)("h2",{children:"Subtitle Management"}),(0,b.jsx)("p",{children:"Manage subtitles for educational videos to enhance learning accessibility"})]}),(0,b.jsx)(S.Z,{columns:R,dataSource:e,rowKey:"_id",loading:s,pagination:{pageSize:10,showSizeChanger:!0,showQuickJumper:!0,showTotal:e=>"Total ".concat(e," videos")},scroll:{x:1e3}}),(0,b.jsxs)(C.Z,{title:"Upload Custom Subtitle",open:r,onOk:async()=>{if(o&&0!==D.length)try{B((0,j.YC)());const e=new FormData;e.append("subtitle",D[0]),e.append("language",v),e.append("languageName",g),e.append("isDefault","en"===v);const a=await(async(e,a)=>{try{return(await L.post("/api/study/upload-subtitle/".concat(e),a,{headers:{"Content-Type":"multipart/form-data"}})).data}catch(s){return s.response.data}})(o._id,e);a.success?(l.ZP.success("Subtitle uploaded successfully!"),n(!1),A([]),u(null),G()):l.ZP.error(a.message||"Failed to upload subtitle")}catch(e){l.ZP.error("Error uploading subtitle")}finally{B((0,j.Ir)())}else l.ZP.warning("Please select a video and upload a subtitle file")},onCancel:()=>{n(!1),A([]),u(null)},okText:"Upload",cancelText:"Cancel",children:[o&&(0,b.jsxs)("div",{style:{marginBottom:"16px"},children:[(0,b.jsx)("strong",{children:"Video:"})," ",o.title]}),(0,b.jsxs)("div",{style:{marginBottom:"16px"},children:[(0,b.jsx)("label",{children:"Language:"}),(0,b.jsx)(d.default,{value:v,onChange:e=>{y(e);const a=z.find((a=>a.code===e));f((null===a||void 0===a?void 0:a.name)||e)},style:{width:"100%",marginTop:"8px"},children:z.map((e=>(0,b.jsx)(M,{value:e.code,children:e.name},e.code)))})]}),(0,b.jsxs)("div",{children:[(0,b.jsx)("label",{children:"Subtitle File (.srt):"}),(0,b.jsx)(m.Z,(0,c.Z)((0,c.Z)({},Q),{},{style:{marginTop:"8px"},children:(0,b.jsx)(p.ZP,{icon:(0,b.jsx)(T.Z,{}),children:"Select SRT File"})}))]})]})]})};var A=s(9389);const{Option:E}=d.default,{Search:O}=A.default;const B=function(e){let{onEdit:a}=e;const s=(0,h.I0)(),[i,r]=(0,t.useState)([]),[n,o]=(0,t.useState)(!1),[u,m]=(0,t.useState)({materialType:"",level:"",className:"",subject:""}),[g,f]=(0,t.useState)(""),k=async()=>{try{o(!0),s((0,j.YC)());const e=await(0,x.Hh)(u);200===e.status&&e.data.success?r(e.data.data||[]):(l.ZP.error("Failed to fetch study materials"),r([]))}catch(e){console.error("Error fetching materials:",e),l.ZP.error("Failed to fetch study materials"),r([])}finally{o(!1),s((0,j.Ir)())}};(0,t.useEffect)((()=>{k()}),[u]);const P=(e,a)=>{m((s=>{const t=(0,c.Z)((0,c.Z)({},s),{},{[e]:a});return"level"===e&&(t.className="",t.subject=""),t}))},U=e=>{switch(e){case"videos":return(0,b.jsx)(y.KoQ,{className:"material-icon video"});case"study-notes":return(0,b.jsx)(y.mGS,{className:"material-icon note"});case"past-papers":return(0,b.jsx)(y.nGB,{className:"material-icon paper"});case"books":return(0,b.jsx)(y.Mp$,{className:"material-icon book"});default:return(0,b.jsx)(y.mGS,{className:"material-icon"})}},F=e=>{switch(e){case"videos":return"Video";case"study-notes":return"Study Note";case"past-papers":return"Past Paper";case"books":return"Book";default:return e}},T=i.filter((e=>e.title.toLowerCase().includes(g.toLowerCase())||e.subject.toLowerCase().includes(g.toLowerCase())||e.className.toLowerCase().includes(g.toLowerCase()))),I=[{title:"Material",key:"material",width:"30%",render:(e,a)=>(0,b.jsx)("div",{className:"material-info",children:(0,b.jsxs)("div",{className:"material-header",children:[U(a.type),(0,b.jsxs)("div",{className:"material-details",children:[(0,b.jsx)("div",{className:"material-title",children:a.title}),(0,b.jsxs)("div",{className:"material-meta",children:[(0,b.jsx)(N.Z,{color:"blue",children:F(a.type)}),(0,b.jsxs)("span",{className:"meta-text",children:[a.subject," \u2022 Class ",a.className]})]})]})]})})},{title:"Level",dataIndex:"level",key:"level",width:"10%",render:e=>(0,b.jsx)(N.Z,{color:"primary"===e?"green":"secondary"===e?"orange":"purple",children:e.charAt(0).toUpperCase()+e.slice(1)})},{title:"Class",dataIndex:"className",key:"className",width:"10%",render:e=>(0,b.jsxs)("span",{className:"class-badge",children:["Class ",e]})},{title:"Subject",dataIndex:"subject",key:"subject",width:"15%"},{title:"Year",dataIndex:"year",key:"year",width:"10%",render:e=>e||"-"},{title:"Actions",key:"actions",width:"25%",render:(e,t)=>(0,b.jsxs)(w.Z,{children:[(0,b.jsx)(Z.Z,{title:"Edit material",children:(0,b.jsx)(p.ZP,{type:"primary",icon:(0,b.jsx)(y.fmQ,{}),size:"small",onClick:()=>a(t),children:"Edit"})}),(0,b.jsx)(Z.Z,{title:"Delete material",children:(0,b.jsx)(p.ZP,{danger:!0,icon:(0,b.jsx)(y.Xm5,{}),size:"small",onClick:()=>(async e=>{C.Z.confirm({title:"Delete ".concat(e.type.replace("-"," ")),content:'Are you sure you want to delete "'.concat(e.title,'"? This action cannot be undone.'),okText:"Delete",okType:"danger",cancelText:"Cancel",onOk:async()=>{try{let t;switch(s((0,j.YC)()),e.type){case"videos":t=await(0,x.FM)(e._id);break;case"study-notes":t=await(0,x.f_)(e._id);break;case"past-papers":t=await(0,x.A4)(e._id);break;case"books":t=await(0,x.MC)(e._id);break;default:throw new Error("Invalid material type")}var a;200===t.status&&t.data.success?(l.ZP.success(t.data.message),k()):l.ZP.error((null===(a=t.data)||void 0===a?void 0:a.message)||"Failed to delete material")}catch(t){console.error("Error deleting material:",t),l.ZP.error("Failed to delete material")}finally{s((0,j.Ir)())}}})})(t),children:"Delete"})})]})}];return(0,b.jsxs)("div",{className:"study-material-manager",children:[(0,b.jsxs)("div",{className:"manager-header",children:[(0,b.jsx)("h2",{children:"Study Materials Management"}),(0,b.jsx)("p",{children:"Manage all uploaded study materials - edit, delete, and organize content"})]}),(0,b.jsx)("div",{className:"filters-section",children:(0,b.jsxs)("div",{className:"filters-row",children:[(0,b.jsxs)("div",{className:"filter-group",children:[(0,b.jsx)("label",{children:"Material Type:"}),(0,b.jsxs)(d.default,{placeholder:"All Types",value:u.materialType||void 0,onChange:e=>P("materialType",e),allowClear:!0,style:{width:150},children:[(0,b.jsx)(E,{value:"videos",children:"Videos"}),(0,b.jsx)(E,{value:"study-notes",children:"Study Notes"}),(0,b.jsx)(E,{value:"past-papers",children:"Past Papers"}),(0,b.jsx)(E,{value:"books",children:"Books"})]})]}),(0,b.jsxs)("div",{className:"filter-group",children:[(0,b.jsx)("label",{children:"Level:"}),(0,b.jsxs)(d.default,{placeholder:"All Levels",value:u.level||void 0,onChange:e=>P("level",e),allowClear:!0,style:{width:120},children:[(0,b.jsx)(E,{value:"primary",children:"Primary"}),(0,b.jsx)(E,{value:"secondary",children:"Secondary"}),(0,b.jsx)(E,{value:"advance",children:"Advance"})]})]}),u.level&&(0,b.jsxs)("div",{className:"filter-group",children:[(0,b.jsx)("label",{children:"Class:"}),(0,b.jsx)(d.default,{placeholder:"All Classes",value:u.className||void 0,onChange:e=>P("className",e),allowClear:!0,style:{width:120},children:(e=>{switch(e){case"primary":return["1","2","3","4","5"];case"secondary":return["6","7","8","9","10","11"];case"advance":return["12","13"];default:return[]}})(u.level).map((e=>(0,b.jsxs)(E,{value:e,children:["Class ",e]},e)))})]}),u.level&&(0,b.jsxs)("div",{className:"filter-group",children:[(0,b.jsx)("label",{children:"Subject:"}),(0,b.jsx)(d.default,{placeholder:"All Subjects",value:u.subject||void 0,onChange:e=>P("subject",e),allowClear:!0,style:{width:150},children:(e=>{switch(e){case"primary":return v.iq;case"secondary":return v.Lo;case"advance":return v.vp;default:return[]}})(u.level).map((e=>(0,b.jsx)(E,{value:e,children:e},e)))})]}),(0,b.jsxs)("div",{className:"filter-group",children:[(0,b.jsx)("label",{children:"Search:"}),(0,b.jsx)(O,{placeholder:"Search materials...",value:g,onChange:e=>f(e.target.value),style:{width:200},allowClear:!0})]})]})}),(0,b.jsx)("div",{className:"materials-table",children:(0,b.jsx)(S.Z,{columns:I,dataSource:T,rowKey:"_id",loading:n,pagination:{pageSize:10,showSizeChanger:!0,showQuickJumper:!0,showTotal:e=>"Total ".concat(e," materials")},scroll:{x:1e3}})})]})},{Option:z}=d.default;const G=function(e){let{material:a,onSuccess:s,onCancel:i}=e;const[r]=u.Z.useForm(),n=(0,h.I0)(),[o,g]=(0,t.useState)(!1),[f,N]=(0,t.useState)([]),[w,Z]=(0,t.useState)([]),[S,C]=(0,t.useState)([]),[k,P]=(0,t.useState)("youtube"),U=e=>{switch(e){case"primary":return["1","2","3","4","5"];case"secondary":return["6","7","8","9","10","11"];case"advance":return["12","13"];default:return[]}};(0,t.useEffect)((()=>{a&&(r.setFieldsValue({level:a.level,className:a.className,subject:a.subject,title:a.title,year:a.year,videoID:a.videoID,videoUrl:a.videoUrl,thumbnailUrl:a.thumbnail}),"videos"===a.type&&a.additionalClasses&&C(a.additionalClasses),"videos"===a.type&&(a.videoID&&!a.videoUrl?P("youtube"):a.videoUrl&&P("s3url")))}),[a,r]);const F=()=>{switch(null===a||void 0===a?void 0:a.type){case"videos":return"Video";case"study-notes":return"Study Note";case"past-papers":return"Past Paper";case"books":return"Book";default:return"Material"}},T={beforeUpload:e=>{if(!("application/pdf"===e.type||e.type.startsWith("application/")||e.type.startsWith("text/")))return l.ZP.error("Please upload a valid document file"),!1;return e.size/1024/1024<50?(N([e]),!1):(l.ZP.error("File must be smaller than 50MB"),!1)},onRemove:()=>{N([])}},I={beforeUpload:e=>{if(!e.type.startsWith("image/"))return l.ZP.error("Please upload an image file"),!1;return e.size/1024/1024<5?(Z([e]),!1):(l.ZP.error("Image must be smaller than 5MB"),!1)},onRemove:()=>{Z([])}};return a?(0,b.jsxs)("div",{className:"edit-study-material-form",children:[(0,b.jsx)("div",{className:"form-header",children:(0,b.jsxs)("div",{className:"header-content",children:[(()=>{switch(null===a||void 0===a?void 0:a.type){case"videos":return(0,b.jsx)(y.KoQ,{className:"form-icon"});case"study-notes":default:return(0,b.jsx)(y.mGS,{className:"form-icon"});case"past-papers":return(0,b.jsx)(y.nGB,{className:"form-icon"});case"books":return(0,b.jsx)(y.Mp$,{className:"form-icon"})}})(),(0,b.jsxs)("div",{children:[(0,b.jsxs)("h2",{children:["Edit ",F()]}),(0,b.jsxs)("p",{children:['Update the details of "',a.title,'"']})]})]})}),(0,b.jsxs)(u.Z,{form:r,layout:"vertical",onFinish:async e=>{try{let i;if(g(!0),n((0,j.YC)()),"videos"===a.type){const s=(0,c.Z)((0,c.Z)({},e),{},{additionalClasses:S});if("youtube"===k)i=await(0,x.HH)(a._id,s);else if("s3url"===k)if(w.length>0&&w[0].originFileObj){const e=new FormData;Object.keys(s).forEach((a=>{"additionalClasses"===a?e.append(a,JSON.stringify(s[a])):e.append(a,s[a])})),e.append("thumbnail",w[0].originFileObj),i=await(0,x.HH)(a._id,e)}else i=await(0,x.HH)(a._id,s)}else{const s=new FormData;switch(Object.keys(e).forEach((a=>{void 0!==e[a]&&null!==e[a]&&s.append(a,e[a])})),f.length>0&&f[0].originFileObj&&s.append("document",f[0].originFileObj),"books"===a.type&&w.length>0&&w[0].originFileObj&&s.append("thumbnail",w[0].originFileObj),a.type){case"study-notes":i=await(0,x.Qk)(a._id,s);break;case"past-papers":i=await(0,x.KB)(a._id,s);break;case"books":i=await(0,x.u8)(a._id,s);break;default:throw new Error("Invalid material type")}}if(200===i.status&&i.data.success)l.ZP.success(i.data.message),s(a.type);else{var t;const e=(null===(t=i.data)||void 0===t?void 0:t.message)||"Failed to update material";l.ZP.error(e)}}catch(i){console.error("Error updating material:",i),l.ZP.error("Failed to update material")}finally{g(!1),n((0,j.Ir)())}},className:"material-form",children:[(0,b.jsxs)("div",{className:"form-row",children:[(0,b.jsx)(u.Z.Item,{label:"Level",name:"level",rules:[{required:!0,message:"Please select a level"}],className:"form-item-half",children:(0,b.jsxs)(d.default,{placeholder:"Select level",onChange:e=>{r.setFieldsValue({className:"",subject:""}),C([])},size:"large",children:[(0,b.jsx)(z,{value:"primary",children:"Primary"}),(0,b.jsx)(z,{value:"secondary",children:"Secondary"}),(0,b.jsx)(z,{value:"advance",children:"Advance"})]})}),(0,b.jsx)(u.Z.Item,{label:"Class",name:"className",rules:[{required:!0,message:"Please select a class"}],className:"form-item-half",children:(0,b.jsx)(d.default,{placeholder:"Select class",size:"large",disabled:!r.getFieldValue("level"),children:U(r.getFieldValue("level")).map((e=>(0,b.jsxs)(z,{value:e,children:["Class ",e]},e)))})})]}),(0,b.jsx)(u.Z.Item,{label:"Subject",name:"subject",rules:[{required:!0,message:"Please select a subject"}],children:(0,b.jsx)(d.default,{placeholder:"Select subject",size:"large",disabled:!r.getFieldValue("level"),children:(e=>{switch(e){case"primary":return v.iq;case"secondary":return v.Lo;case"advance":return v.vp;default:return[]}})(r.getFieldValue("level")).map((e=>(0,b.jsx)(z,{value:e,children:e},e)))})}),"videos"===a.type&&(0,b.jsxs)(u.Z.Item,{label:"Additional Classes (Optional)",className:"additional-classes-section",children:[(0,b.jsx)(d.default,{mode:"multiple",placeholder:"Select additional classes that can access this video",value:S,onChange:e=>{C(e)},size:"large",disabled:!r.getFieldValue("className"),children:(()=>{const e=r.getFieldValue("level"),a=r.getFieldValue("className");return e&&a?U(e).filter((e=>e!==a)):[]})().map((e=>(0,b.jsxs)(z,{value:e,children:["Class ",e]},e)))}),(0,b.jsx)("div",{className:"additional-classes-note",children:(0,b.jsx)("small",{children:"Note: The video will be available to the core class and all selected additional classes"})})]}),(0,b.jsx)(u.Z.Item,{label:"Title",name:"title",rules:[{required:!0,message:"Please enter a title"}],children:(0,b.jsx)("input",{type:"text",placeholder:"Enter ".concat(F().toLowerCase()," title"),className:"form-input"})}),("past-papers"===a.type||"books"===a.type)&&(0,b.jsx)(u.Z.Item,{label:"Year",name:"year",rules:[{required:!0,message:"Please enter the year"}],children:(0,b.jsx)("input",{type:"text",placeholder:"Enter year (e.g., 2023)",className:"form-input"})}),"videos"===a.type&&(0,b.jsxs)(b.Fragment,{children:[(0,b.jsxs)("div",{className:"upload-method-section",children:[(0,b.jsx)("label",{className:"section-label",children:"Video Source"}),(0,b.jsxs)("div",{className:"upload-method-options",children:[(0,b.jsxs)("div",{className:"method-option ".concat("youtube"===k?"active":""),onClick:()=>P("youtube"),children:[(0,b.jsx)(y.KoQ,{className:"method-icon"}),(0,b.jsx)("span",{children:"YouTube Video"})]}),(0,b.jsxs)("div",{className:"method-option ".concat("s3url"===k?"active":""),onClick:()=>P("s3url"),children:[(0,b.jsx)(y.Qvc,{className:"method-icon"}),(0,b.jsx)("span",{children:"S3 URL"})]})]})]}),"youtube"===k&&(0,b.jsx)(u.Z.Item,{label:"YouTube Video ID",name:"videoID",rules:[{required:!0,message:"Please enter YouTube video ID"}],children:(0,b.jsx)("input",{type:"text",placeholder:"Enter YouTube video ID (e.g., dQw4w9WgXcQ)",className:"form-input"})}),"s3url"===k&&(0,b.jsxs)(b.Fragment,{children:[(0,b.jsx)(u.Z.Item,{label:"S3 Video URL",name:"videoUrl",rules:[{required:!0,message:"Please enter S3 video URL"}],children:(0,b.jsx)("input",{type:"url",placeholder:"Enter S3 video URL",className:"form-input"})}),(0,b.jsx)(u.Z.Item,{label:"Thumbnail URL (Optional)",name:"thumbnailUrl",children:(0,b.jsx)("input",{type:"url",placeholder:"Enter thumbnail URL or upload below",className:"form-input"})}),(0,b.jsx)(u.Z.Item,{label:"Upload New Thumbnail (Optional)",className:"upload-section",children:(0,b.jsx)(m.Z,(0,c.Z)((0,c.Z)({},I),{},{fileList:w,onChange:e=>{let{fileList:a}=e;return Z(a)},className:"thumbnail-upload",children:(0,b.jsxs)("div",{className:"upload-area small",children:[(0,b.jsx)(y.DUB,{className:"upload-icon"}),(0,b.jsx)("p",{children:"Drag & drop thumbnail or click to upload"}),(0,b.jsx)("p",{className:"upload-hint",children:"Supports JPG, PNG, GIF (Max: 5MB)"})]})}))})]})]}),"videos"!==a.type&&(0,b.jsx)(u.Z.Item,{label:"Upload New Document (Optional)",className:"upload-section",children:(0,b.jsx)(m.Z,(0,c.Z)((0,c.Z)({},T),{},{fileList:f,onChange:e=>{let{fileList:a}=e;return N(a)},className:"document-upload",children:(0,b.jsxs)("div",{className:"upload-area",children:[(0,b.jsx)(y.DUB,{className:"upload-icon"}),(0,b.jsx)("p",{children:"Click or drag file to upload new document"}),(0,b.jsx)("p",{className:"upload-hint",children:"Leave empty to keep current document"})]})}))}),"books"===a.type&&(0,b.jsx)(u.Z.Item,{label:"Upload New Thumbnail (Optional)",className:"upload-section",children:(0,b.jsx)(m.Z,(0,c.Z)((0,c.Z)({},I),{},{fileList:w,onChange:e=>{let{fileList:a}=e;return Z(a)},className:"thumbnail-upload",children:(0,b.jsxs)("div",{className:"upload-area small",children:[(0,b.jsx)(y.DUB,{className:"upload-icon"}),(0,b.jsx)("p",{children:"Upload new book cover"}),(0,b.jsx)("p",{className:"upload-hint",children:"Leave empty to keep current thumbnail"})]})}))}),(0,b.jsxs)("div",{className:"form-actions",children:[(0,b.jsx)(p.ZP,{type:"default",size:"large",onClick:i,className:"cancel-button",icon:(0,b.jsx)(y.aHS,{}),children:"Cancel"}),(0,b.jsxs)(p.ZP,{type:"primary",htmlType:"submit",size:"large",loading:o,className:"submit-button",icon:(0,b.jsx)(y.TvB,{}),children:["Update ",F()]})]})]})]}):(0,b.jsx)("div",{children:"No material selected for editing"})};const V=function(){var e;const a=(0,r.s0)(),[s,c]=(0,t.useState)(""),[d,u]=(0,t.useState)(!1),[m,p]=(0,t.useState)(!1),[h,j]=(0,t.useState)(!1),[x,v]=(0,t.useState)(!1),[g,N]=(0,t.useState)(null),w=[{key:"videos",title:"Videos",icon:(0,b.jsx)(y.KoQ,{}),description:"Add educational videos for students",color:"#e74c3c"},{key:"study-notes",title:"Study Notes",icon:(0,b.jsx)(y.mGS,{}),description:"Upload study notes and documents",color:"#3498db"},{key:"past-papers",title:"Past Papers",icon:(0,b.jsx)(y.nGB,{}),description:"Add past examination papers",color:"#9b59b6"},{key:"books",title:"Books",icon:(0,b.jsx)(y.Mp$,{}),description:"Upload textbooks and reference materials",color:"#27ae60"}],Z=[{key:"manage-materials",title:"Manage Materials",icon:(0,b.jsx)(y.p4t,{}),description:"Edit, delete, and organize study materials",color:"#34495e"},{key:"subtitles",title:"Subtitle Management",icon:(0,b.jsx)(y.mpV,{}),description:"Manage video subtitles and captions",color:"#f39c12"}],S=()=>{u(!1),c("")},C=()=>{v(!1),N(null)};return(0,b.jsxs)("div",{className:"admin-study-materials",children:[(0,b.jsxs)("div",{className:"flex items-center gap-4 mb-4",children:[(0,b.jsxs)(i.E.button,{whileHover:{scale:1.05},whileTap:{scale:.95},onClick:()=>a("/admin/dashboard"),className:"flex items-center gap-2 px-3 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors duration-200 shadow-md",children:[(0,b.jsx)(n.Qlt,{className:"w-4 h-4"}),(0,b.jsx)("span",{className:"hidden sm:inline text-sm font-medium",children:"Dashboard"})]}),(0,b.jsx)(o.Z,{title:"Study Materials Management"})]}),m?(0,b.jsxs)("div",{className:"subtitle-manager-container",children:[(0,b.jsxs)("div",{className:"form-header",children:[(0,b.jsx)("button",{className:"back-button",onClick:()=>{p(!1)},children:"\u2190 Back to Material Types"}),(0,b.jsx)("h2",{children:"Subtitle Management"})]}),(0,b.jsx)(D,{})]}):h?(0,b.jsxs)("div",{className:"material-manager-container",children:[(0,b.jsxs)("div",{className:"form-header",children:[(0,b.jsx)("button",{className:"back-button",onClick:()=>{j(!1)},children:"\u2190 Back to Main Menu"}),(0,b.jsx)("h2",{children:"Study Materials Management"})]}),(0,b.jsx)(B,{onEdit:e=>{N(e),v(!0)}})]}):x?(0,b.jsxs)("div",{className:"edit-form-container",children:[(0,b.jsx)("div",{className:"form-header",children:(0,b.jsx)("button",{className:"back-button",onClick:C,children:"\u2190 Back to Materials List"})}),(0,b.jsx)(G,{material:g,onSuccess:e=>{l.ZP.success("".concat(e," updated successfully!")),v(!1),N(null)},onCancel:C})]}):d?(0,b.jsxs)("div",{className:"add-form-container",children:[(0,b.jsxs)("div",{className:"form-header",children:[(0,b.jsx)("button",{className:"back-button",onClick:S,children:"\u2190 Back to Main Menu"}),(0,b.jsxs)("h2",{children:["Add ",null===(e=w.find((e=>e.key===s)))||void 0===e?void 0:e.title]})]}),(0,b.jsx)(f,{materialType:s,onSuccess:e=>{l.ZP.success("".concat(e," added successfully!")),u(!1),c("")},onCancel:S})]}):(0,b.jsxs)("div",{className:"main-menu-container",children:[(0,b.jsxs)("div",{className:"header-section",children:[(0,b.jsx)("h2",{children:"Study Materials Administration"}),(0,b.jsx)("p",{children:"Manage your educational content - add new materials or edit existing ones"})]}),(0,b.jsxs)("div",{className:"menu-sections",children:[(0,b.jsxs)("div",{className:"menu-section",children:[(0,b.jsxs)("h3",{children:[(0,b.jsx)(y.wEH,{className:"section-icon"}),"Add New Materials"]}),(0,b.jsx)("p",{children:"Upload new study materials for students"}),(0,b.jsx)("div",{className:"material-types-grid",children:w.map((e=>(0,b.jsxs)("div",{className:"material-type-card",onClick:()=>{return a=e.key,c(a),void u(!0);var a},style:{borderColor:e.color},children:[(0,b.jsx)("div",{className:"card-icon",style:{color:e.color},children:e.icon}),(0,b.jsx)("h4",{children:e.title}),(0,b.jsx)("p",{children:e.description}),(0,b.jsxs)("div",{className:"add-button",style:{backgroundColor:e.color},children:[(0,b.jsx)(y.wEH,{}),(0,b.jsxs)("span",{children:["Add ",e.title]})]})]},e.key)))})]}),(0,b.jsxs)("div",{className:"menu-section",children:[(0,b.jsxs)("h3",{children:[(0,b.jsx)(y.AeW,{className:"section-icon"}),"Manage Existing Materials"]}),(0,b.jsx)("p",{children:"Edit, delete, and organize your study materials"}),(0,b.jsx)("div",{className:"management-options-grid",children:Z.map((e=>(0,b.jsxs)("div",{className:"management-option-card",onClick:()=>(e=>{"subtitles"===e?p(!0):"manage-materials"===e&&j(!0)})(e.key),style:{borderColor:e.color},children:[(0,b.jsx)("div",{className:"card-icon",style:{color:e.color},children:e.icon}),(0,b.jsx)("h4",{children:e.title}),(0,b.jsx)("p",{children:e.description}),(0,b.jsxs)("div",{className:"manage-button",style:{backgroundColor:e.color},children:[(0,b.jsx)(y.p4t,{}),(0,b.jsxs)("span",{children:["Open ",e.title]})]})]},e.key)))})]})]})]})]})}}}]);
//# sourceMappingURL=100.b9b250dc.chunk.js.map