"use strict";(self.webpackChunkclient=self.webpackChunkclient||[]).push([[723],{9723:(o,e,t)=>{t.r(e),t.d(e,{default:()=>c});var r=t(2791),i=t(7689),n=t(9434),a=t(6042),s=(t(7027),t(2202)),l=t(184);const c=()=>{const o=(0,i.s0)(),{user:e}=(0,n.v9)((o=>o.user)),[t,c]=(0,r.useState)(0),u=["Education is the most powerful weapon which you can use to change the world.","The beautiful thing about learning is that no one can take it away from you.","Success is not final, failure is not fatal: it is the courage to continue that counts.","The only way to do great work is to love what you do.","Believe you can and you're halfway there.","Your limitation\u2014it's only your imagination.","Great things never come from comfort zones.","Dream it. Wish it. Do it.","Success doesn't just find you. You have to go out and get it.","The harder you work for something, the greater you'll feel when you achieve it."];(0,r.useEffect)((()=>{const o=setInterval((()=>{c((o=>(o+1)%u.length))}),4e3);return()=>clearInterval(o)}),[u.length]);const h=[{title:"Take Quiz",description:"Test your knowledge",icon:s.MXt,path:"/user/quiz",color:"from-blue-500 to-blue-600",hoverColor:"from-blue-600 to-blue-700"},{title:"Study Materials",description:"Books, notes & papers",icon:s.Mp$,path:"/user/study-material",color:"from-purple-500 to-purple-600",hoverColor:"from-purple-600 to-purple-700"},{title:"Video Lessons",description:"Watch educational videos",icon:s.KoQ,path:"/user/video-lessons",color:"from-red-500 to-red-600",hoverColor:"from-red-600 to-red-700"},{title:"Reports",description:"Track your progress",icon:s.Op,path:"/user/reports",color:"from-green-500 to-green-600",hoverColor:"from-green-600 to-green-700"},{title:"Ranking",description:"See your position",icon:s.yyP,path:"/user/ranking",color:"from-yellow-500 to-yellow-600",hoverColor:"from-yellow-600 to-yellow-700"},{title:"Profile",description:"Manage your account",icon:s.Xws,path:"/profile",color:"from-indigo-500 to-indigo-600",hoverColor:"from-indigo-600 to-indigo-700"},{title:"Forum",description:"Connect with peers",icon:s.OdJ,path:"/forum",color:"from-pink-500 to-pink-600",hoverColor:"from-pink-600 to-pink-700"},{title:"About Us",description:"Learn about our mission",icon:s.DAO,path:"/user/about-us",color:"from-cyan-500 to-cyan-600",hoverColor:"from-cyan-600 to-cyan-700"}];return(0,l.jsx)("div",{className:"hub-container",children:(0,l.jsxs)("div",{className:"hub-content",children:[(0,l.jsxs)("div",{className:"hub-header",children:[(0,l.jsxs)("h1",{className:"hub-welcome",children:["Welcome, ",(null===e||void 0===e?void 0:e.firstName)||(null===e||void 0===e?void 0:e.name)||"Student"]}),(0,l.jsx)("p",{className:"hub-subtitle",children:"Choose your learning path below"}),(0,l.jsxs)("div",{className:"hub-quote",children:[(0,l.jsx)(s.QJe,{style:{color:"#f59e0b",marginRight:"0.5rem"}}),'"',u[t],'"',(0,l.jsx)(s.QJe,{style:{color:"#f59e0b",marginLeft:"0.5rem"}}),(0,l.jsx)("div",{style:{fontSize:"0.875rem",color:"#6b7280",marginTop:"0.5rem"},children:"- BrainWave Team"})]})]}),(0,l.jsxs)("div",{className:"hub-grid-container",children:[(0,l.jsx)("div",{className:"hub-grid",children:h.map(((e,t)=>{const r=e.icon;return(0,l.jsxs)(a.E.div,{initial:{opacity:0,y:30},animate:{opacity:1,y:0},transition:{duration:.6,delay:.1*t},className:"hub-card hover:".concat(e.hoverColor," ").concat(e.color),onClick:()=>o(e.path),tabIndex:0,role:"button",onKeyDown:t=>{"Enter"!==t.key&&" "!==t.key||o(e.path)},style:{cursor:"pointer",touchAction:"manipulation"},children:[(0,l.jsx)("div",{className:"hub-card-icon",children:(0,l.jsx)(r,{})}),(0,l.jsx)("h3",{className:"hub-card-title",children:e.title}),(0,l.jsx)("p",{className:"hub-card-description",children:e.description})]},e.title)}))}),(0,l.jsx)(a.E.div,{initial:{opacity:0},animate:{opacity:1},transition:{duration:.8,delay:.5},className:"hub-bottom-decoration",children:(0,l.jsxs)("div",{className:"decoration-content",children:[(0,l.jsx)(s.nGB,{className:"decoration-icon animate-bounce-gentle"}),(0,l.jsx)("span",{children:"Your learning journey starts here!"}),(0,l.jsx)(s.ef0,{className:"decoration-icon animate-bounce-gentle"})]})})]})]})})}}}]);
//# sourceMappingURL=723.724c7a60.chunk.js.map