{"ast": null, "code": "export function getCellFixedInfo(colStart, colEnd, columns, stickyOffsets, direction, curColumns) {\n  var startColumn = columns[colStart] || {};\n  var endColumn = columns[colEnd] || {};\n  var fixLeft;\n  var fixRight;\n  if (startColumn.fixed === 'left') {\n    fixLeft = stickyOffsets.left[direction === 'rtl' ? colEnd : colStart];\n  } else if (endColumn.fixed === 'right') {\n    fixRight = stickyOffsets.right[direction === 'rtl' ? colStart : colEnd];\n  }\n  var lastFixLeft = false;\n  var firstFixRight = false;\n  var lastFixRight = false;\n  var firstFixLeft = false;\n  var nextColumn = columns[colEnd + 1];\n  var prevColumn = columns[colStart - 1];\n\n  // no children only\n  var canLastFix = !(curColumns !== null && curColumns !== void 0 && curColumns.children);\n  if (direction === 'rtl') {\n    if (fixLeft !== undefined) {\n      var prevFixLeft = prevColumn && prevColumn.fixed === 'left';\n      firstFixLeft = !prevFixLeft && canLastFix;\n    } else if (fixRight !== undefined) {\n      var nextFixRight = nextColumn && nextColumn.fixed === 'right';\n      lastFixRight = !nextFixRight && canLastFix;\n    }\n  } else if (fixLeft !== undefined) {\n    var nextFixLeft = nextColumn && nextColumn.fixed === 'left';\n    lastFixLeft = !nextFixLeft && canLastFix;\n  } else if (fixRight !== undefined) {\n    var prevFixRight = prevColumn && prevColumn.fixed === 'right';\n    firstFixRight = !prevFixRight && canLastFix;\n  }\n  return {\n    fixLeft: fixLeft,\n    fixRight: fixRight,\n    lastFixLeft: lastFixLeft,\n    firstFixRight: firstFixRight,\n    lastFixRight: lastFixRight,\n    firstFixLeft: firstFixLeft,\n    isSticky: stickyOffsets.isSticky\n  };\n}", "map": {"version": 3, "names": ["getCellFixedInfo", "colStart", "colEnd", "columns", "stickyOffsets", "direction", "curColumns", "startColumn", "endColumn", "fixLeft", "fixRight", "fixed", "left", "right", "lastFixLeft", "firstFixRight", "lastFixRight", "firstFixLeft", "nextColumn", "prevColumn", "canLastFix", "children", "undefined", "prevFixLeft", "nextFixRight", "nextFixLeft", "prevFixRight", "isSticky"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/node_modules/rc-table/es/utils/fixUtil.js"], "sourcesContent": ["export function getCellFixedInfo(colStart, colEnd, columns, stickyOffsets, direction, curColumns) {\n  var startColumn = columns[colStart] || {};\n  var endColumn = columns[colEnd] || {};\n  var fixLeft;\n  var fixRight;\n  if (startColumn.fixed === 'left') {\n    fixLeft = stickyOffsets.left[direction === 'rtl' ? colEnd : colStart];\n  } else if (endColumn.fixed === 'right') {\n    fixRight = stickyOffsets.right[direction === 'rtl' ? colStart : colEnd];\n  }\n  var lastFixLeft = false;\n  var firstFixRight = false;\n  var lastFixRight = false;\n  var firstFixLeft = false;\n  var nextColumn = columns[colEnd + 1];\n  var prevColumn = columns[colStart - 1];\n\n  // no children only\n  var canLastFix = !(curColumns !== null && curColumns !== void 0 && curColumns.children);\n  if (direction === 'rtl') {\n    if (fixLeft !== undefined) {\n      var prevFixLeft = prevColumn && prevColumn.fixed === 'left';\n      firstFixLeft = !prevFixLeft && canLastFix;\n    } else if (fixRight !== undefined) {\n      var nextFixRight = nextColumn && nextColumn.fixed === 'right';\n      lastFixRight = !nextFixRight && canLastFix;\n    }\n  } else if (fixLeft !== undefined) {\n    var nextFixLeft = nextColumn && nextColumn.fixed === 'left';\n    lastFixLeft = !nextFixLeft && canLastFix;\n  } else if (fixRight !== undefined) {\n    var prevFixRight = prevColumn && prevColumn.fixed === 'right';\n    firstFixRight = !prevFixRight && canLastFix;\n  }\n  return {\n    fixLeft: fixLeft,\n    fixRight: fixRight,\n    lastFixLeft: lastFixLeft,\n    firstFixRight: firstFixRight,\n    lastFixRight: lastFixRight,\n    firstFixLeft: firstFixLeft,\n    isSticky: stickyOffsets.isSticky\n  };\n}"], "mappings": "AAAA,OAAO,SAASA,gBAAgBA,CAACC,QAAQ,EAAEC,MAAM,EAAEC,OAAO,EAAEC,aAAa,EAAEC,SAAS,EAAEC,UAAU,EAAE;EAChG,IAAIC,WAAW,GAAGJ,OAAO,CAACF,QAAQ,CAAC,IAAI,CAAC,CAAC;EACzC,IAAIO,SAAS,GAAGL,OAAO,CAACD,MAAM,CAAC,IAAI,CAAC,CAAC;EACrC,IAAIO,OAAO;EACX,IAAIC,QAAQ;EACZ,IAAIH,WAAW,CAACI,KAAK,KAAK,MAAM,EAAE;IAChCF,OAAO,GAAGL,aAAa,CAACQ,IAAI,CAACP,SAAS,KAAK,KAAK,GAAGH,MAAM,GAAGD,QAAQ,CAAC;EACvE,CAAC,MAAM,IAAIO,SAAS,CAACG,KAAK,KAAK,OAAO,EAAE;IACtCD,QAAQ,GAAGN,aAAa,CAACS,KAAK,CAACR,SAAS,KAAK,KAAK,GAAGJ,QAAQ,GAAGC,MAAM,CAAC;EACzE;EACA,IAAIY,WAAW,GAAG,KAAK;EACvB,IAAIC,aAAa,GAAG,KAAK;EACzB,IAAIC,YAAY,GAAG,KAAK;EACxB,IAAIC,YAAY,GAAG,KAAK;EACxB,IAAIC,UAAU,GAAGf,OAAO,CAACD,MAAM,GAAG,CAAC,CAAC;EACpC,IAAIiB,UAAU,GAAGhB,OAAO,CAACF,QAAQ,GAAG,CAAC,CAAC;;EAEtC;EACA,IAAImB,UAAU,GAAG,EAAEd,UAAU,KAAK,IAAI,IAAIA,UAAU,KAAK,KAAK,CAAC,IAAIA,UAAU,CAACe,QAAQ,CAAC;EACvF,IAAIhB,SAAS,KAAK,KAAK,EAAE;IACvB,IAAII,OAAO,KAAKa,SAAS,EAAE;MACzB,IAAIC,WAAW,GAAGJ,UAAU,IAAIA,UAAU,CAACR,KAAK,KAAK,MAAM;MAC3DM,YAAY,GAAG,CAACM,WAAW,IAAIH,UAAU;IAC3C,CAAC,MAAM,IAAIV,QAAQ,KAAKY,SAAS,EAAE;MACjC,IAAIE,YAAY,GAAGN,UAAU,IAAIA,UAAU,CAACP,KAAK,KAAK,OAAO;MAC7DK,YAAY,GAAG,CAACQ,YAAY,IAAIJ,UAAU;IAC5C;EACF,CAAC,MAAM,IAAIX,OAAO,KAAKa,SAAS,EAAE;IAChC,IAAIG,WAAW,GAAGP,UAAU,IAAIA,UAAU,CAACP,KAAK,KAAK,MAAM;IAC3DG,WAAW,GAAG,CAACW,WAAW,IAAIL,UAAU;EAC1C,CAAC,MAAM,IAAIV,QAAQ,KAAKY,SAAS,EAAE;IACjC,IAAII,YAAY,GAAGP,UAAU,IAAIA,UAAU,CAACR,KAAK,KAAK,OAAO;IAC7DI,aAAa,GAAG,CAACW,YAAY,IAAIN,UAAU;EAC7C;EACA,OAAO;IACLX,OAAO,EAAEA,OAAO;IAChBC,QAAQ,EAAEA,QAAQ;IAClBI,WAAW,EAAEA,WAAW;IACxBC,aAAa,EAAEA,aAAa;IAC5BC,YAAY,EAAEA,YAAY;IAC1BC,YAAY,EAAEA,YAAY;IAC1BU,QAAQ,EAAEvB,aAAa,CAACuB;EAC1B,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}