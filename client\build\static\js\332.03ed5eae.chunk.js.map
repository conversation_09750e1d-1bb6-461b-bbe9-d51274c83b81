{"version": 3, "file": "static/js/332.03ed5eae.chunk.js", "mappings": "kLAeA,MAAMA,EAAmBC,IAMlB,IANmB,YACxBC,EAAW,KACXC,EAAO,SAAQ,YACfC,GAAc,EAAI,UAClBC,EAAY,GAAE,QACdC,EAAU,MACXL,EAEC,MAAMM,EAAoB,CACxBC,WAAY,CACVC,KAAMC,EAAAA,IACNC,MAAO,cACPC,YAAa,4BACbC,MAAO,4BACPC,QAAS,aACTC,UAAW,iBAEbC,cAAe,CACbP,KAAMQ,EAAAA,IACNN,MAAO,gBACPC,YAAa,0BACbC,MAAO,gCACPC,QAAS,eACTC,UAAW,mBAEbG,SAAU,CACRT,KAAMU,EAAAA,IACNR,MAAO,aACPC,YAAa,6BACbC,MAAO,6BACPC,QAAS,eACTC,UAAW,mBAEbK,UAAW,CACTX,KAAMU,EAAAA,IACNR,MAAO,cACPC,YAAa,8BACbC,MAAO,0BACPC,QAAS,YACTC,UAAW,gBAEbM,UAAW,CACTZ,KAAMU,EAAAA,IACNR,MAAO,iBACPC,YAAa,8BACbC,MAAO,6BACPC,QAAS,YACTC,UAAW,gBAEbO,eAAgB,CACdb,KAAMc,EAAAA,IACNZ,MAAO,iBACPC,YAAa,qBACbC,MAAO,gCACPC,QAAS,eACTC,UAAW,mBAEbS,YAAa,CACXf,KAAMgB,EAAAA,IACNd,MAAO,cACPC,YAAa,gCACbC,MAAO,4BACPC,QAAS,aACTC,UAAW,iBAEbW,mBAAoB,CAClBjB,KAAMkB,EAAAA,IACNhB,MAAO,qBACPC,YAAa,oCACbC,MAAO,8BACPC,QAAS,cACTC,UAAW,kBAEba,iBAAkB,CAChBnB,KAAMoB,EAAAA,IACNlB,MAAO,mBACPC,YAAa,gCACbC,MAAO,gCACPC,QAAS,eACTC,UAAW,oBA0BTe,EAASvB,EAAkBL,EAAY6B,OAASxB,EAAkBC,WAClEwB,EAtBa,CACjBC,MAAO,CACLC,UAAW,YACXzB,KAAM,UACN0B,KAAM,UACNC,QAAS,OAEXC,OAAQ,CACNH,UAAW,YACXzB,KAAM,UACN0B,KAAM,UACNC,QAAS,OAEXE,MAAO,CACLJ,UAAW,YACXzB,KAAM,YACN0B,KAAM,YACNC,QAAS,QAKYjC,GACnBoC,EAAgBT,EAAOrB,KAyBvB+B,EAAe,CACnBC,OAAQ,CAAEC,QAAS,GACnBC,QAAS,CACPD,QAAS,CAAC,GAAK,EAAG,IAClBE,WAAY,CACVC,SAAU,EACVC,OAAQC,IACRC,KAAM,eAKZ,OACEC,EAAAA,EAAAA,MAACC,EAAAA,EAAOC,IAAG,CACTC,SArCkB,CACpBX,OAAQ,CAAEC,QAAS,EAAGW,MAAO,GAAKC,QAAS,IAC3CX,QAAS,CACPD,QAAS,EACTW,MAAO,EACPC,OAAQ,EACRV,WAAY,CACVb,KAAM,SACNwB,UAAW,IACXC,QAAS,KAGbC,MAAO,CACLJ,MAAO,IACPC,OAAQ,EACRV,WAAY,CACVb,KAAM,SACNwB,UAAW,IACXC,QAAS,MAoBXE,QAAQ,SACRC,QAAQ,UACRC,WAAW,QACXtD,QAASA,EACTD,UAAS,oDAAAwD,OAELxD,EAAS,YACXyD,SAAA,EAGFC,EAAAA,EAAAA,KAACb,EAAAA,EAAOC,IAAG,CACTC,SAAUZ,EACVnC,UAAS,4FAAAwD,OAEY/B,EAAOjB,MAAK,gBAAAgD,OAC7B7B,EAAME,UAAS,iBAKrB6B,EAAAA,EAAAA,KAAA,OAAK1D,UAAS,8FAAAwD,OAEO/B,EAAOjB,MAAK,cAAAgD,OAC7B7B,EAAME,UAAS,KAAA2B,OAAI7B,EAAMI,QAAO,mHAGlC0B,UACAC,EAAAA,EAAAA,KAACxB,EAAa,CAAClC,UAAS,GAAAwD,OAAK7B,EAAMvB,KAAI,kCAIxCL,IACC6C,EAAAA,EAAAA,MAACC,EAAAA,EAAOC,IAAG,CACTO,QAAS,CAAEhB,QAAS,EAAGsB,EAAG,GAAIX,MAAO,IACrCO,WAAY,CAAElB,QAAS,EAAGsB,EAAG,EAAGX,MAAO,GACvChD,UAAS,4FAAAwD,OAEL/B,EAAOhB,QAAO,KAAA+C,OAAI/B,EAAOf,UAAS,0IAAA8C,OAIlC7B,EAAMG,KAAI,gBACZ2B,SAAA,EAEFC,EAAAA,EAAAA,KAAA,OAAK1D,UAAU,gBAAeyD,SAAEhC,EAAOnB,SACvCoD,EAAAA,EAAAA,KAAA,OAAK1D,UAAU,qBAAoByD,SAAEhC,EAAOlB,cAC3CV,EAAY+D,UACXhB,EAAAA,EAAAA,MAAA,OAAK5C,UAAU,2BAA0ByD,SAAA,CAAC,YAC9B5D,EAAY+D,WAGzB/D,EAAYgE,WACXH,EAAAA,EAAAA,KAAA,OAAK1D,UAAU,0BAAyByD,SACrC,IAAIK,KAAKjE,EAAYgE,UAAUE,wBAKpCL,EAAAA,EAAAA,KAAA,OAAK1D,UAAS,iMAAAwD,OAIV/B,EAAOhB,QAAQuD,QAAQ,MAAO,aAAY,uBAIvC,EAKJC,EAAkBC,IAMxB,IAADC,EAAAC,EAAAC,EAAA,IAN0B,aAC9BC,EAAe,GAAE,WACjBC,EAAa,EAAC,KACdzE,EAAO,SAAQ,OACf0E,EAAS,aAAY,UACrBxE,EAAY,IACbkE,EACC,MAAMO,EAAsBH,EAAaI,MAAM,EAAGH,GAC5CI,EAAiBC,KAAKC,IAAI,EAAGP,EAAaQ,OAASP,GAGnDQ,EAAa,CACjBnD,MAAO,CACLC,UAAW,YACXC,KAAM,UACNC,QAAS,OAEXC,OAAQ,CACNH,UAAW,YACXC,KAAM,UACNC,QAAS,OAEXE,MAAO,CACLJ,UAAW,YACXC,KAAM,YACNC,QAAS,QAcPiD,EAA2B,SAAXR,EAClB,uDACA,uBAEJ,OACE5B,EAAAA,EAAAA,MAACC,EAAAA,EAAOC,IAAG,CACTC,SAhBsB,CACxBX,OAAQ,CAAEC,QAAS,GACnBC,QAAS,CACPD,QAAS,EACTE,WAAY,CACV0C,gBAAiB,MAYnB5B,QAAQ,SACRC,QAAQ,UACRtD,UAAS,GAAAwD,OAAKwB,EAAa,KAAAxB,OAAIxD,GAAYyD,SAAA,CAE1CgB,EAAoBS,KAAI,CAACrF,EAAasF,KACrCzB,EAAAA,EAAAA,KAAC/D,EAAgB,CAEfE,YAAaA,EACbC,KAAMA,EACNC,aAAa,GAAK,GAAAyD,OAHV3D,EAAY6B,KAAI,KAAA8B,OAAI2B,MAO/BR,EAAiB,IAChB/B,EAAAA,EAAAA,MAACC,EAAAA,EAAOC,IAAG,CACTC,SAAU,CACRX,OAAQ,CAAEC,QAAS,EAAGW,MAAO,IAC7BV,QAAS,CAAED,QAAS,EAAGW,MAAO,IAEhChD,UAAS,8HAAAwD,OAGW,QAHXW,EAGLY,EAAWjF,UAAK,IAAAqE,OAAA,EAAhBA,EAAkBtC,UAAS,KAAA2B,OAAoB,QAApBY,EAAIW,EAAWjF,UAAK,IAAAsE,OAAA,EAAhBA,EAAkBrC,QAAO,2DAAAyB,OAExC,QAFwCa,EAExDU,EAAWjF,UAAK,IAAAuE,OAAA,EAAhBA,EAAkBvC,KAAI,gBACxB2B,SAAA,CACH,IACGkB,OAGK,E,cCjTjB,MAsOA,EAtOsB/E,IAUf,IAVgB,UACrBwF,EAAY,EAAC,QACbC,EAAU,EAAC,aACXC,EAAe,EAAC,cAChBC,EAAgB,IAAG,cACnBC,GAAgB,EAAI,KACpB1F,EAAO,SAAQ,UACf2F,GAAY,EAAI,cAChBC,GAAgB,EAAI,UACpB1F,EAAY,IACbJ,EACC,MAAO+F,EAAYC,IAAiBC,EAAAA,EAAAA,UAAS,IACtCC,EAAcC,IAAmBF,EAAAA,EAAAA,WAAS,GAG3CG,EAAoBX,EAAUE,EAC9BU,EAAoBb,EAAYY,EAChCE,EAAmBb,EAAUW,EAC7BG,EAAqBvB,KAAKwB,IAAI,IAAKxB,KAAKC,IAAI,EAAIoB,EAAoBC,EAAoB,MAwBxFzE,EArBa,CACjBG,MAAO,CACLyE,OAAQ,MACRC,UAAW,kBACXC,SAAU,UACVxE,QAAS,aAEXC,OAAQ,CACNqE,OAAQ,MACRC,UAAW,kBACXC,SAAU,UACVxE,QAAS,aAEXE,MAAO,CACLoE,OAAQ,MACRC,UAAW,sBACXC,SAAU,YACVxE,QAAS,cAIajC,IAG1B0G,EAAAA,EAAAA,YAAU,KACR,GAAIhB,EAAe,CACjB,MAAMiB,EAAQC,YAAW,KACvBd,EAAcR,EAAU,GACvB,KACH,MAAO,IAAMuB,aAAaF,EAC5B,CACEb,EAAcR,EAChB,GACC,CAACA,EAAWI,IA2Bf,OACE5C,EAAAA,EAAAA,MAAA,OAAK5C,UAAS,yBAAAwD,OAA2BxD,GAAYyD,SAAA,EAEnDC,EAAAA,EAAAA,KAACkD,EAAAA,EAAe,CAAAnD,SACbqC,IACCpC,EAAAA,EAAAA,KAACb,EAAAA,EAAOC,IAAG,CACTO,QAAS,CAAEhB,QAAS,EAAGW,MAAO,IAC9BM,QAAS,CAAEjB,QAAS,EAAGW,MAAO,GAC9B6D,KAAM,CAAExE,QAAS,EAAGW,MAAO,IAC3BhD,UAAU,mFAAkFyD,UAE5Fb,EAAAA,EAAAA,MAACC,EAAAA,EAAOC,IAAG,CACTO,QAAS,CAAEM,GAAI,GAAItB,QAAS,GAC5BiB,QAAS,CAAEK,EAAG,EAAGtB,QAAS,GAC1BwE,KAAM,CAAElD,EAAG,GAAItB,QAAS,GACxBrC,UAAU,yGAAwGyD,SAAA,EAElHC,EAAAA,EAAAA,KAACb,EAAAA,EAAOC,IAAG,CACTQ,QAAS,CAAEL,OAAQ,KACnBV,WAAY,CAAEC,SAAU,EAAGC,OAAQC,IAAUC,KAAM,UACnD3C,UAAU,gBAAeyD,SAC1B,kBAGDC,EAAAA,EAAAA,KAAA,MAAI1D,UAAU,0BAAyByD,SAAC,eACxCb,EAAAA,EAAAA,MAAA,KAAG5C,UAAU,UAASyD,SAAA,CAAC,qBAAmB6B,EAAa,eAM/D1C,EAAAA,EAAAA,MAAA,OAAK5C,UAAU,8BAA6ByD,SAAA,CAEzCgC,IACC7C,EAAAA,EAAAA,MAACC,EAAAA,EAAOC,IAAG,CACTS,WAAY,CAAEP,MAAO,KACrB8D,SAAU,CAAE9D,MAAO,KACnBhD,UAAS,mBAAAwD,OACL/B,EAAO6E,UAAS,mFAAA9C,QAxDPuD,EAyDsBzB,EAxDvCyB,GAAS,GAAW,8BACpBA,GAAS,EAAU,gCACnBA,GAAS,EAAU,6BACnBA,GAAS,EAAU,8BACnBA,GAAS,EAAU,8BAChB,6BAmDiD,8HAG9CtD,SAAA,EAGFC,EAAAA,EAAAA,KAAA,OAAK1D,UAAU,kGAGf0D,EAAAA,EAAAA,KAAA,QAAM1D,UAAS,iBAAAwD,OAAmB/B,EAAO8E,UAAW9C,SACjD6B,IAIFA,GAAgB,KACf5B,EAAAA,EAAAA,KAAC9C,EAAAA,IAAQ,CAACZ,UAAU,uDAM1B4C,EAAAA,EAAAA,MAAA,OAAK5C,UAAU,SAAQyD,SAAA,CAEpBiC,IACC9C,EAAAA,EAAAA,MAAA,OAAK5C,UAAS,0CAAAwD,OAA4C/B,EAAO8E,SAAQ,kBAAiB9C,SAAA,EACxFb,EAAAA,EAAAA,MAAA,QAAM5C,UAAU,cAAayD,SAAA,CAC1BkC,EAAWqB,iBAAiB,UAE/BtD,EAAAA,EAAAA,KAAA,QAAM1D,UAAU,gBAAeyD,SAC5B8B,EAAgB,EAAC,GAAA/B,OAAM+B,EAAa,kBAAmB,kBAM9D3C,EAAAA,EAAAA,MAAA,OAAK5C,UAAS,0BAAAwD,OACD/B,EAAO4E,OAAM,0GAExB5C,SAAA,EAEAC,EAAAA,EAAAA,KAAA,OAAK1D,UAAU,iEAGf4C,EAAAA,EAAAA,MAACC,EAAAA,EAAOC,IAAG,CACTO,QAAS,CAAE4D,MAAO,GAClB3D,QAAS,CAAE2D,MAAM,GAADzD,OAAK2C,EAAkB,MACvC5D,WAAY,CAAEC,SAAU,EAAGG,KAAM,WACjC3C,UAAS,8FAAAwD,OA7Ff2C,GAAsB,GAAW,4CACjCA,GAAsB,GAAW,4CACjCA,GAAsB,GAAW,2CACjCA,GAAsB,GAAW,2CAC9B,yCA2F0C,wEAErC1C,SAAA,EAGFC,EAAAA,EAAAA,KAACb,EAAAA,EAAOC,IAAG,CACTQ,QAAS,CAAE4D,EAAG,CAAC,KAAM,SACrB3E,WAAY,CAAEC,SAAU,EAAGC,OAAQC,IAAUC,KAAM,UACnD3C,UAAU,wGAIZ0D,EAAAA,EAAAA,KAAA,OAAK1D,UAAU,qGAIjB0D,EAAAA,EAAAA,KAACkD,EAAAA,EAAe,CAAAnD,SACb+B,IACC9B,EAAAA,EAAAA,KAACb,EAAAA,EAAOC,IAAG,CACTO,QAAS,CAAEhB,QAAS,EAAGsB,EAAG,GAC1BL,QAAS,CAAEjB,QAAS,CAAC,EAAG,EAAG,GAAIsB,GAAI,IACnCkD,KAAM,CAAExE,QAAS,GACjBE,WAAY,CAAEC,SAAU,GACxBxC,UAAU,sDAAqDyD,UAE/DC,EAAAA,EAAAA,KAACtC,EAAAA,IAAM,CAACpB,UAAU,mCAOzBuF,EAAgB,IACf3C,EAAAA,EAAAA,MAAA,OAAK5C,UAAU,4BAA2ByD,SAAA,EACxCb,EAAAA,EAAAA,MAAA,QAAM5C,UAAS,GAAAwD,OAAK/B,EAAO8E,SAAQ,8BAA6B9C,SAAA,CAAC,SACxD6B,MAET1C,EAAAA,EAAAA,MAAA,QAAM5C,UAAS,GAAAwD,OAAK/B,EAAO8E,SAAQ,8BAA6B9C,SAAA,CAAC,SACxD6B,EAAe,WAO7BA,EAAe,IACd1C,EAAAA,EAAAA,MAACC,EAAAA,EAAOC,IAAG,CACTS,WAAY,CAAEP,MAAO,KACrBhD,UAAU,6HAA4HyD,SAAA,EAEtIC,EAAAA,EAAAA,KAAC5C,EAAAA,IAAO,CAACd,UAAU,6BACnB4C,EAAAA,EAAAA,MAAA,QAAM5C,UAAU,sCAAqCyD,SAAA,CAAC,IAC5B,IAApB6B,EAAe,GAAS,mBA9JjByB,KAmKf,ECmCV,EArQmBnH,IAOZ,IAPa,MAClBmH,EAAQ,EAAC,KACTjH,EAAO,SAAQ,UACfqH,GAAY,EAAK,SACjBC,GAAW,EAAI,SACfC,GAAW,EAAI,UACfrH,EAAY,IACbJ,EAEC,MA2BM6B,EA3Ba,CACjBG,MAAO,CACLC,UAAW,UACXC,KAAM,UACN1B,KAAM,UACNkH,UAAW,WAEbtF,OAAQ,CACNH,UAAW,YACXC,KAAM,UACN1B,KAAM,UACNkH,UAAW,WAEbrF,MAAO,CACLJ,UAAW,YACXC,KAAM,UACN1B,KAAM,UACNkH,UAAW,aAEbC,GAAI,CACF1F,UAAW,YACXC,KAAM,UACN1B,KAAM,UACNkH,UAAW,YAIWxH,GAmEpB0H,EAhEkBT,IAClBA,GAAS,GACJ,CACLzG,MAAO,QACPF,KAAMc,EAAAA,IACNuG,SAAU,0CACVC,KAAM,uBACNC,MAAO,UACPC,OAAQ,SACRC,UAAW,UAEJd,GAAS,EACX,CACLzG,MAAO,SACPF,KAAMQ,EAAAA,IACN6G,SAAU,4CACVC,KAAM,uBACNC,MAAO,OACPC,OAAQ,YACRC,UAAW,UAEJd,GAAS,EACX,CACLzG,MAAO,SACPF,KAAM0H,EAAAA,IACNL,SAAU,8CACVC,KAAM,qBACNC,MAAO,QACPC,OAAQ,OACRC,UAAW,SAEJd,GAAS,EACX,CACLzG,MAAO,SACPF,KAAMU,EAAAA,IACN2G,SAAU,2CACVC,KAAM,qBACNC,MAAO,UACPC,OAAQ,OACRC,UAAW,QAEJd,GAAS,EACX,CACLzG,MAAO,UACPF,KAAMgB,EAAAA,IACNqG,SAAU,2CACVC,KAAM,uBACNC,MAAO,SACPC,OAAQ,WACRC,UAAW,SAGN,CACLvH,MAAO,WACPF,KAAMC,EAAAA,IACNoH,SAAU,4BACVC,KAAM,qBACNC,MAAO,SACPC,OAAQ,SACRC,UAAW,QAKGE,CAAehB,GAC7B7E,EAAgBsF,EAAYpH,KAG5B4H,EAAoB,CACxBC,KAAM,CAAC,EACPC,MAAO,CACLlF,MAAO,CAAC,EAAG,KAAM,GACjBT,WAAY,CAAEC,SAAU,EAAGC,OAAQC,MAErCyF,OAAQ,CACNxE,EAAG,CAAC,GAAI,EAAG,GACXpB,WAAY,CAAEC,SAAU,IAAKC,OAAQC,MAEvCO,OAAQ,CACNA,OAAQ,CAAC,EAAG,KACZV,WAAY,CAAEC,SAAU,EAAGC,OAAQC,IAAUC,KAAM,WAErD+E,KAAM,CACJU,UAAW,CACT,mCACA,mCACA,oCAEF7F,WAAY,CAAEC,SAAU,EAAGC,OAAQC,OAsCvC,OACEE,EAAAA,EAAAA,MAAA,OAAK5C,UAAS,yBAAAwD,OAA2BxD,GAAYyD,SAAA,EAEnDb,EAAAA,EAAAA,MAACC,EAAAA,EAAOC,IAAG,CACTC,SAAUiF,EAAkBR,EAAYK,WACxCvE,QAAS+D,EAAWG,EAAYK,UAAY,OAC5CtE,WAAY,CAAEP,MAAO,KACrB8D,SAAU,CAAE9D,MAAO,KACnBhD,UAAS,eAAAwD,OACL/B,EAAOI,UAAS,4EAAA2B,OACEgE,EAAYC,SAAQ,gBAAAjE,OA3CvBmE,KACvB,OAAQA,GACN,IAAK,UACH,MAAO,sBACT,IAAK,OACH,MAAO,iBACT,IAAK,QACH,MAAO,kBACT,IAAK,UACH,MAAO,oBACT,QACE,MAAO,eACX,EAgCQU,CAAgBb,EAAYG,OAAM,gBAAAnE,OA5BnBoE,KACvB,OAAQA,GACN,IAAK,SACH,MAAO,wCACT,IAAK,YACH,MAAO,uCACT,IAAK,OACH,MAAO,qCACT,IAAK,OACH,MAAO,uCACT,IAAK,WACH,MAAO,mCACT,QACE,MAAO,yBACX,EAeQU,CAAgBd,EAAYI,QAAO,gBAAApE,OACnC4D,EAAQ,aAAA5D,OAAgBgE,EAAYE,MAAS,GAAE,wDAEjDjE,SAAA,CAGDsD,GAAS,IACRrD,EAAAA,EAAAA,KAAA,OAAK1D,UAAU,8BAA6ByD,UAC1CC,EAAAA,EAAAA,KAAA,OAAK1D,UAAU,sHAKnB0D,EAAAA,EAAAA,KAAA,OAAK1D,UAAU,sEAAqEyD,SACjFsD,GAAS,GACRrD,EAAAA,EAAAA,KAACxB,EAAa,CAAClC,UAAS,GAAAwD,OAAK/B,EAAOrB,KAAI,sBAExCsD,EAAAA,EAAAA,KAAA,QAAM1D,UAAS,GAAAwD,OAAK/B,EAAOK,KAAI,mBAAkB2B,SAC9CsD,MAMNA,GAAS,IACRnE,EAAAA,EAAAA,MAAA2F,EAAAA,SAAA,CAAA9E,SAAA,EACEC,EAAAA,EAAAA,KAACb,EAAAA,EAAOC,IAAG,CACTQ,QAAS,CACPN,MAAO,CAAC,EAAG,EAAG,GACdC,OAAQ,CAAC,EAAG,IAAK,MAEnBV,WAAY,CACVC,SAAU,EACVC,OAAQC,IACR8F,MAAO,GAETxI,UAAU,+DAEZ0D,EAAAA,EAAAA,KAACb,EAAAA,EAAOC,IAAG,CACTQ,QAAS,CACPN,MAAO,CAAC,EAAG,EAAG,GACdC,OAAQ,CAAC,GAAI,KAAM,MAErBV,WAAY,CACVC,SAAU,EACVC,OAAQC,IACR8F,MAAO,IAETxI,UAAU,8DAMf+G,GAAS,KACRrD,EAAAA,EAAAA,KAAA,OAAK1D,UAAU,mIAKlBmH,IACCvE,EAAAA,EAAAA,MAACC,EAAAA,EAAOC,IAAG,CACTO,QAAS,CAAEhB,QAAS,EAAGsB,EAAG,GAC1BL,QAAS,CAAEjB,QAAS,EAAGsB,EAAG,GAC1B3D,UAAU,mBAAkByD,SAAA,EAE5BC,EAAAA,EAAAA,KAAA,KAAG1D,UAAS,GAAAwD,OAAK/B,EAAO6F,UAAS,gCAA+B7D,SAC7D+D,EAAYlH,SAEfsC,EAAAA,EAAAA,MAAA,KAAG5C,UAAU,wBAAuByD,SAAA,CAAC,SAC5BsD,SAMbnE,EAAAA,EAAAA,MAAA,OAAK5C,UAAU,8NAA6NyD,SAAA,CAAC,SACpOsD,EAAM,MAAIS,EAAYlH,OAC7BoD,EAAAA,EAAAA,KAAA,OAAK1D,UAAU,6GAEb,ECjBV,EAhPiCJ,IAO1B,IAAD6I,EAAA,IAP4B,YAC9B5I,EAAW,KACXC,EAAO,SAAQ,YACf4I,GAAc,EAAI,SAClBrB,GAAW,EAAI,OACfsB,GAAS,EAAK,UACd3I,EAAY,IACfJ,EACG,IAAKC,EAAa,OAAO,KAGzB,MAkBM4B,EAlBa,CACfG,MAAO,CACHC,UAAW,UACXzB,KAAM,UACN0B,KAAM,WAEVE,OAAQ,CACJH,UAAW,UACXzB,KAAM,UACN0B,KAAM,WAEVG,MAAO,CACHJ,UAAW,YACXzB,KAAM,UACN0B,KAAM,cAIYhC,GA2DpBoC,EAxDsB0G,KACxB,MAAMC,EAAU,CAEZ,WAAcvH,EAAAA,IACd,cAAiBwG,EAAAA,IACjB,SAAYhH,EAAAA,IACZ,UAAaA,EAAAA,IACb,UAAaA,EAAAA,IACb,eAAkBI,EAAAA,IAClB,YAAeE,EAAAA,IACf,mBAAsBf,EAAAA,IACtB,iBAAoBO,EAAAA,IAGpB,YAAeU,EAAAA,IACf,cAAiBF,EAAAA,IACjB,cAAiB0G,EAAAA,IACjB,QAAWhH,EAAAA,IACX,YAAegI,EAAAA,IACf,YAAeC,EAAAA,IACf,eAAkBA,EAAAA,IAClB,cAAiB7H,EAAAA,IACjB,aAAgB8H,EAAAA,KAEpB,OAAOH,EAAQD,IAAOC,EAAQhJ,EAAY6B,OAASuH,EAAAA,GAAO,EAgCxCC,CAAmBrJ,EAAY+I,IAC/CO,EAAgBtJ,EAAY+H,OA7BVA,KACpB,MAAMwB,EAAe,CACjB,OAAU,4BACV,SAAY,8BACZ,KAAQ,4BACR,KAAQ,gCACR,UAAa,gCACb,OAAU,+BAEd,OAAOA,EAAaxB,IAAWwB,EAAaC,MAAM,EAqBlDC,CAAezJ,EAAY+H,QAhBV,CACb,WAAc,8BACd,cAAiB,gCACjB,SAAY,6BACZ,UAAa,0BACb,UAAa,2BACb,eAAkB,gCAClB,YAAe,4BACf,mBAAsB,gCACtB,iBAAoB,6BAQJ/H,EAAY6B,OANP,4BAiD7B,OACIkB,EAAAA,EAAAA,MAAA,OAAK5C,UAAS,oCAAAwD,OAAsCxD,GAAYyD,SAAA,EAC5Db,EAAAA,EAAAA,MAACC,EAAAA,EAAOC,IAAG,CACPS,WAAY,CAAEP,MAAO,KACrB8D,SAAU,CAAE9D,MAAO,KACnBM,QAAS+D,EAhCOO,KACxB,OAAQA,GACJ,IAAK,YACL,IAAK,SACD,MAAO,CACH3E,OAAQ,CAAC,EAAG,GAAI,EAAG,GACnBD,MAAO,CAAC,EAAG,KAAM,GACjBT,WAAY,CAAEC,SAAU,EAAGC,OAAQC,IAAU6G,YAAa,IAElE,IAAK,OACD,MAAO,CACH5F,EAAG,CAAC,GAAI,EAAG,GACXpB,WAAY,CAAEC,SAAU,EAAGC,OAAQC,IAAU6G,YAAa,IAElE,IAAK,OACD,MAAO,CACHvG,MAAO,CAAC,EAAG,KAAM,GACjBT,WAAY,CAAEC,SAAU,EAAGC,OAAQC,MAE3C,QACI,MAAO,CACHO,OAAQ,CAAC,EAAG,GAAI,EAAG,GACnBV,WAAY,CAAEC,SAAU,EAAGC,OAAQC,IAAU6G,YAAa,IAEtE,EAQ4BC,CAAmB3J,EAAY+H,QAAU,CAAC,EAC9D5H,UAAS,yBAAAwD,OACH/B,EAAOI,UAAS,0FAAA2B,OACE2F,EAAa,oCAAA3F,OAhD1BoE,KACnB,MAAM6B,EAAU,CACZ,OAAU,qBACV,SAAY,sBACZ,KAAQ,qBACR,KAAQ,uBACR,UAAa,uBACb,OAAU,sBAEd,OAAOA,EAAQ7B,IAAW6B,EAAQJ,MAAM,EAwChBK,CAAc7J,EAAY+H,QAAO,8HAG/CnE,SAAA,CAGD5D,EAAY+H,QAAU,CAAC,OAAQ,OAAQ,YAAa,UAAU+B,SAAS9J,EAAY+H,UAChFlE,EAAAA,EAAAA,KAACb,EAAAA,EAAOC,IAAG,CACPQ,QAAS,CACL4D,EAAG,CAAC,QAAS,QACb3E,WAAY,CAAEC,SAAU,EAAGC,OAAQC,IAAU6G,YAAa,IAE9DvJ,UAAU,uGAKjBH,EAAY+H,QAAU,CAAC,YAAa,UAAU+B,SAAS9J,EAAY+H,UAChEhF,EAAAA,EAAAA,MAAA2F,EAAAA,SAAA,CAAA9E,SAAA,EACIC,EAAAA,EAAAA,KAACb,EAAAA,EAAOC,IAAG,CACPQ,QAAS,CACLN,MAAO,CAAC,EAAG,EAAG,GACdC,OAAQ,CAAC,EAAG,IAAK,MAErBV,WAAY,CACRC,SAAU,EACVC,OAAQC,IACR8F,MAAO,GAEXxI,UAAU,+DAEd0D,EAAAA,EAAAA,KAACb,EAAAA,EAAOC,IAAG,CACPQ,QAAS,CACLN,MAAO,CAAC,EAAG,EAAG,GACdC,OAAQ,CAAC,GAAI,KAAM,MAEvBV,WAAY,CACRC,SAAU,EACVC,OAAQC,IACR8F,MAAO,GAEXxI,UAAU,+DAMtB0D,EAAAA,EAAAA,KAACxB,EAAa,CAAClC,UAAS,GAAAwD,OAAK/B,EAAOrB,KAAI,8CAGvCuI,GAAU9I,EAAY+J,WACnBlG,EAAAA,EAAAA,KAAA,OAAK1D,UAAU,4HAA2HyD,SACrI5D,EAAY+J,cAMxBlB,IACG9F,EAAAA,EAAAA,MAAA,OAAK5C,UAAU,oPAAmPyD,SAAA,EAC9PC,EAAAA,EAAAA,KAAA,OAAK1D,UAAU,gBAAeyD,SACzB5D,EAAYgK,OAAwB,QAApBpB,EAAI5I,EAAY6B,YAAI,IAAA+G,OAAA,EAAhBA,EAAkBzE,QAAQ,KAAM,KAAKA,QAAQ,SAAS8F,GAAKA,EAAEC,mBAErFlK,EAAYU,cACTmD,EAAAA,EAAAA,KAAA,OAAK1D,UAAU,6BAA4ByD,SACtC5D,EAAYU,cAGpBV,EAAY+H,SACTlE,EAAAA,EAAAA,KAAA,OAAK1D,UAAS,4BAAAwD,OACa,WAAvB3D,EAAY+H,OAAsB,gBACX,cAAvB/H,EAAY+H,OAAyB,kBACd,SAAvB/H,EAAY+H,OAAoB,kBACT,SAAvB/H,EAAY+H,OAAoB,gBACT,aAAvB/H,EAAY+H,OAAwB,iBACpC,iBACDnE,SACE5D,EAAY+H,OAAOoC,OAAO,GAAGD,cAAgBlK,EAAY+H,OAAOlD,MAAM,KAG9E7E,EAAY+J,WACThH,EAAAA,EAAAA,MAAA,OAAK5C,UAAU,+BAA8ByD,SAAA,CAAC,IACxC5D,EAAY+J,SAAS,SAG9B/J,EAAYgE,WACTjB,EAAAA,EAAAA,MAAA,OAAK5C,UAAU,6BAA4ByD,SAAA,CAAC,WAC/B,IAAIK,KAAKjE,EAAYgE,UAAUE,yBAGhDL,EAAAA,EAAAA,KAAA,OAAK1D,UAAU,6GAGrB,E,cCxOd,MAsZA,EAtZwBJ,IASjB,IATkB,KACrBqK,EAAI,KACJC,EAAI,UACJC,EAAS,cACTC,GAAgB,EAAK,OACrB5F,EAAS,aAAY,KACrB1E,EAAO,SAAQ,UACfuK,GAAY,EAAI,UAChBrK,EAAY,IACfJ,EAEG,MAwBM6B,EAxBa,CACfG,MAAO,CACH0I,OAAQ,YACRxI,KAAM,UACNyI,QAAS,UACTxI,QAAS,MACTyI,QAAS,aAEbxI,OAAQ,CACJsI,OAAQ,YACRxI,KAAM,YACNyI,QAAS,UACTxI,QAAS,MACTyI,QAAS,aAEbvI,MAAO,CACHqI,OAAQ,YACRxI,KAAM,UACNyI,QAAS,YACTxI,QAAS,MACTyI,QAAS,cAIS1K,GAmDpB2K,EAhDyBC,MAC3B,MAGMC,IAHyB,OAAJV,QAAI,IAAJA,OAAI,EAAJA,EAAMW,sBAA0B,OAAJX,QAAI,IAAJA,OAAI,EAAJA,EAAMY,+BAAgC,QAGjDC,cAE5C,MAAyB,WAArBH,GAAsD,YAArBA,EAC1B,CACHI,YAAa,yDACbC,MAAO,iBACPtD,KAAM,4DACNuD,WAAY,UACZC,UAAW,eACXC,YAAa,yBACbC,QAAS,gHACTC,OAAQ,6DACR3K,UAAW,kBACX4K,YAAa,qBAEW,SAArBX,EACA,CACHI,YAAa,uDACbC,MAAO,cACPtD,KAAM,wDACNuD,WAAY,OACZC,UAAW,eACXC,YAAa,uBACbC,QAAS,2GACTC,OAAQ,0DACR3K,UAAW,gBACX4K,YAAa,mBAGV,CACHP,YAAa,iEACbC,MAAO,iBACPtD,KAAM,sDACNuD,WAAY,UACZC,UAAW,SACXC,YAAa,sBACbC,QAAS,qGACTC,OAAQ,sDACR3K,UAAW,eACX4K,YAAa,iBAErB,EAGYZ,GAkBVa,EAfiBC,MACnB,MAAMC,EAAWvB,GAAQ,EACzB,OAAiB,IAAbuB,EACO,CAAErL,KAAMc,EAAAA,IAASV,MAAO,kBAAmBkL,GAAI,gBAClC,IAAbD,EACA,CAAErL,KAAM6I,EAAAA,IAASzI,MAAO,gBAAiBkL,GAAI,cAChC,IAAbD,EACA,CAAErL,KAAMQ,EAAAA,IAAUJ,MAAO,iBAAkBkL,GAAI,eAC/CD,GAAY,IAAMA,EAAW,EAC7B,CAAErL,KAAMC,EAAAA,IAAQG,MAAO,gBAAiBkL,GAAI,cAE5C,CAAEtL,KAAM,KAAMI,MAAO,gBAAiBkL,GAAI,aACrD,EAGgBF,GACdG,EAAWJ,EAAYnL,KAM7B,MAAe,aAAXoE,GAEI5B,EAAAA,EAAAA,MAAA,OACI5C,UAAS,6EAAAwD,OACiD/B,EAAOM,QAAO,0BAAAyB,OAClEiH,EAAQY,QAAU,WAAU,uBAAA7H,OAAsBiH,EAAQa,aAAe,kBAAiB,0BAAA9H,OAC1FiH,EAAQ/C,KAAI,2JAAAlE,OAEZ4G,EAAgB,uDAAyD,GAAE,0BAAA5G,OAC3ExD,EAAS,sBACbyD,SAAA,EAGFb,EAAAA,EAAAA,MAAA,OAAK5C,UAAU,mCAAkCyD,SAAA,EAE7CC,EAAAA,EAAAA,KAAA,OAAK1D,UAAS,uHAAAwD,OAER+H,EAAYG,GAAE,KAAAlI,OAAI+H,EAAY/K,MAAK,0BACvCiD,SACGkI,GACGjI,EAAAA,EAAAA,KAACiI,EAAQ,CAAC3L,UAAU,aAEpB4C,EAAAA,EAAAA,MAAA,QAAM5C,UAAU,oBAAmByD,SAAA,CAAC,IAAEyG,GAAQ,SAKrDC,IACGzG,EAAAA,EAAAA,KAAA,OAAK1D,UAAU,kFAAiFyD,UAC5Fb,EAAAA,EAAAA,MAAA,QAAM5C,UAAU,oBAAmByD,SAAA,CAAC,IAAE0G,WAMlDzG,EAAAA,EAAAA,KAAA,OAAK1D,UAAU,OAAMyD,UACjBC,EAAAA,EAAAA,KAACkI,EAAAA,EAAc,CACX3B,KAAMA,EACNnK,KAAK,KACL+L,kBAAkB,EAClB7L,UAAU,oDACV8L,MAAO,CACH7E,MAAOxF,EAAO6I,OAAOX,SAAS,QAAU,OACjClI,EAAO6I,OAAOX,SAAS,QAAU,OAAS,OACjDtD,OAAQ5E,EAAO6I,OAAOX,SAAS,QAAU,OACjClI,EAAO6I,OAAOX,SAAS,QAAU,OAAS,aAM9D/G,EAAAA,EAAAA,MAAA,OAAK5C,UAAU,YAAWyD,SAAA,EACtBC,EAAAA,EAAAA,KAAA,MAAI1D,UAAS,iBAAAwD,OAAmB/B,EAAOK,KAAI,oCAAmC2B,UACrE,OAAJwG,QAAI,IAAJA,OAAI,EAAJA,EAAMJ,OAAQ,kBAInBjH,EAAAA,EAAAA,MAAA,OAAK5C,UAAU,8BAA6ByD,SAAA,EACxCC,EAAAA,EAAAA,KAACqI,EAAU,CACPhF,OAAW,OAAJkD,QAAI,IAAJA,OAAI,EAAJA,EAAM3E,eAAgB,EAC7BxF,KAAK,QACLqH,WAAW,EACXE,UAAU,KAEdzE,EAAAA,EAAAA,MAAA,OAAK5C,UAAU,gBAAeyD,SAAA,EAC1Bb,EAAAA,EAAAA,MAAA,KAAG5C,UAAS,GAAAwD,OAAK/B,EAAO8I,QAAO,8BAA6B9G,SAAA,EACnD,OAAJwG,QAAI,IAAJA,OAAI,EAAJA,EAAM5E,UAAW,EAAE,UAEnB,OAAJ4E,QAAI,IAAJA,OAAI,EAAJA,EAAM1E,eAAgB,IACnB3C,EAAAA,EAAAA,MAAA,KAAG5C,UAAS,wBAA0ByD,SAAA,CACjCwG,EAAK1E,cAAc,qBAOpC3C,EAAAA,EAAAA,MAAA,KAAG5C,UAAS,wBAA0ByD,SAAA,EAC7B,OAAJwG,QAAI,IAAJA,OAAI,EAAJA,EAAM+B,cAAe,EAAE,oBAIvB,OAAJ/B,QAAI,IAAJA,OAAI,EAAJA,EAAMgC,gBACHrJ,EAAAA,EAAAA,MAAA,KAAG5C,UAAS,GAAAwD,OAAK/B,EAAO8I,QAAO,kBAAiB9G,SAAA,CAAC,QACvCwG,EAAKgC,aAAa,QAI3B,OAAJhC,QAAI,IAAJA,OAAI,EAAJA,EAAMiC,eAAgB,IACnBtJ,EAAAA,EAAAA,MAAA,OAAK5C,UAAU,8BAA6ByD,SAAA,EACxCC,EAAAA,EAAAA,KAAC5C,EAAAA,IAAO,CAACd,UAAU,6BACnB0D,EAAAA,EAAAA,KAAA,QAAM1D,UAAS,GAAAwD,OAAK/B,EAAO8I,QAAO,gCAA+B9G,SAC5DwG,EAAKiC,oBAMb,OAAJjC,QAAI,IAAJA,OAAI,EAAJA,EAAM3F,eAAgB2F,EAAK3F,aAAaQ,OAAS,IAC9ClC,EAAAA,EAAAA,MAAA,OAAK5C,UAAU,8BAA6ByD,SAAA,CACvCwG,EAAK3F,aAAaI,MAAM,EAAG,GAAGQ,KAAI,CAACrF,EAAasF,KAC7CzB,EAAAA,EAAAA,KAACyI,EAAwB,CAErBtM,YAAaA,EACbC,KAAK,QACL4I,aAAa,EACbrB,UAAU,EACVsB,QAAQ,GALH9I,EAAY+I,IAAM/I,EAAY6B,MAAQyD,KAQlD8E,EAAK3F,aAAaQ,OAAS,IACxBlC,EAAAA,EAAAA,MAAA,QAAM5C,UAAU,6BAA4ByD,SAAA,CAAC,IACvCwG,EAAK3F,aAAaQ,OAAS,SAO7ClC,EAAAA,EAAAA,MAAA,OAAK5C,UAAS,wIAAAwD,OAERiH,EAAQW,QAAO,0HAEnB3H,SAAA,EACEC,EAAAA,EAAAA,KAAA,QAAM1D,UAAU,UAASyD,SAAEgH,EAAQS,aACnCxH,EAAAA,EAAAA,KAAA,QAAAD,SAAOgH,EAAQQ,uBAS/BrI,EAAAA,EAAAA,MAAA,OACI5C,UAAS,oDAAAwD,OAC4B/B,EAAO+I,QAAO,KAAAhH,OAAI/B,EAAOM,QAAO,+MAAAyB,OAG/D4G,EAAgB,uDAAyD,GAAE,sBAAA5G,OAC3ExD,EAAS,kBACbyD,SAAA,EAGFb,EAAAA,EAAAA,MAAA,OAAK5C,UAAU,4CAA2CyD,SAAA,EAEtDC,EAAAA,EAAAA,KAAA,OAAK1D,UAAS,iHAAAwD,OAER+H,EAAYG,GAAE,KAAAlI,OAAI+H,EAAY/K,MAAK,sBACvCiD,SACGkI,GACGjI,EAAAA,EAAAA,KAACiI,EAAQ,CAAC3L,UAAU,aAEpB4C,EAAAA,EAAAA,MAAA,QAAM5C,UAAU,oBAAmByD,SAAA,CAAC,IAAEyG,GAAQ,SAKrDC,IACGzG,EAAAA,EAAAA,KAAA,OAAK1D,UAAU,oFAAmFyD,UAC9Fb,EAAAA,EAAAA,MAAA,QAAM5C,UAAU,oBAAmByD,SAAA,CAAC,IAAE0G,WAMlDzG,EAAAA,EAAAA,KAAA,OAAK1D,UAAU,gBAAeyD,UAC1BC,EAAAA,EAAAA,KAACkI,EAAAA,EAAc,CACX3B,KAAMA,EACNnK,KAAe,UAATA,EAAmB,KAAgB,UAATA,EAAmB,KAAO,KAC1D+L,kBAAkB,EAClB7L,UAAU,yDAKlB4C,EAAAA,EAAAA,MAAA,OAAK5C,UAAU,iBAAgByD,SAAA,EAC3Bb,EAAAA,EAAAA,MAAA,OAAK5C,UAAU,mCAAkCyD,SAAA,EAC7CC,EAAAA,EAAAA,KAAA,MAAI1D,UAAS,iBAAAwD,OAAmB/B,EAAOK,KAAI,2BAA0B2B,UAC5D,OAAJwG,QAAI,IAAJA,OAAI,EAAJA,EAAMJ,OAAQ,kBAEnBnG,EAAAA,EAAAA,KAACqI,EAAU,CACPhF,OAAW,OAAJkD,QAAI,IAAJA,OAAI,EAAJA,EAAM3E,eAAgB,EAC7BxF,KAAK,QACLqH,WAAW,EACXE,UAAU,KAEd3D,EAAAA,EAAAA,KAAA,QAAM1D,UAAS,+GAAAwD,OAETiH,EAAQW,QAAO,0BACnB3H,SACGgH,EAAQQ,gBAIhBZ,IACGzH,EAAAA,EAAAA,MAAA,OAAK5C,UAAU,8BAA6ByD,SAAA,EAExCb,EAAAA,EAAAA,MAAA,OAAK5C,UAAU,8BAA6ByD,SAAA,EACxCC,EAAAA,EAAAA,KAACtC,EAAAA,IAAM,CAACpB,UAAU,2BAClB4C,EAAAA,EAAAA,MAAA,QAAM5C,UAAS,GAAAwD,OAAK/B,EAAO8I,QAAO,8BAA6B9G,SAAA,EACtD,OAAJwG,QAAI,IAAJA,OAAI,EAAJA,EAAM5E,UAAW,EAAE,aAK5BzC,EAAAA,EAAAA,MAAA,QAAM5C,UAAS,wBAA0ByD,SAAA,EAChC,OAAJwG,QAAI,IAAJA,OAAI,EAAJA,EAAM+B,cAAe,EAAE,eAGAI,KAAvB,OAAJnC,QAAI,IAAJA,OAAI,EAAJA,EAAMoC,oBACHzJ,EAAAA,EAAAA,MAAA,QAAM5C,UAAS,GAAAwD,OAAK/B,EAAO8I,QAAO,mBAAkB9G,SAAA,CAC/CwG,EAAKoC,iBAAiB,kBAGPD,KAAnB,OAAJnC,QAAI,IAAJA,OAAI,EAAJA,EAAMqC,gBACH1J,EAAAA,EAAAA,MAAA,QAAM5C,UAAS,GAAAwD,OAAK/B,EAAO8I,QAAO,kBAAiB9G,SAAA,CAC9CwG,EAAKqC,aAAa,eAGtB,OAAJrC,QAAI,IAAJA,OAAI,EAAJA,EAAMgC,gBACHrJ,EAAAA,EAAAA,MAAA,QAAM5C,UAAS,GAAAwD,OAAK/B,EAAO8I,QAAO,kBAAiB9G,SAAA,CAC9CwG,EAAKgC,aAAa,YAGtB,OAAJhC,QAAI,IAAJA,OAAI,EAAJA,EAAMiC,eAAgB,IACnBtJ,EAAAA,EAAAA,MAAA,OAAK5C,UAAU,8BAA6ByD,SAAA,EACxCC,EAAAA,EAAAA,KAAC5C,EAAAA,IAAO,CAACd,UAAU,6BACnB0D,EAAAA,EAAAA,KAAA,QAAM1D,UAAS,GAAAwD,OAAK/B,EAAO8I,QAAO,gCAA+B9G,SAC5DwG,EAAKiC,uBAQrB,OAAJjC,QAAI,IAAJA,OAAI,EAAJA,EAAM1E,eAAgB,IACnB7B,EAAAA,EAAAA,KAAA,OAAK1D,UAAU,OAAMyD,UACjBC,EAAAA,EAAAA,KAAC6I,EAAa,CACVnH,UAAW6E,EAAK5E,SAAW,EAC3BA,SAAU4E,EAAK5E,SAAW,IAAM4E,EAAK1E,eAAiB,GACtDD,aAAc2E,EAAK3E,cAAgB,EACnCC,cAAe0E,EAAK1E,eAAiB,EACrCzF,KAAK,QACL2F,WAAW,EACXC,eAAe,EACfF,eAAe,OAMtB,OAAJyE,QAAI,IAAJA,OAAI,EAAJA,EAAM3F,eAAgB2F,EAAK3F,aAAaQ,OAAS,IAC9CpB,EAAAA,EAAAA,KAAA,OAAK1D,UAAU,OAAMyD,UACjBC,EAAAA,EAAAA,KAACO,EAAe,CACZK,aAAc2F,EAAK3F,aACnBC,WAAY,EACZzE,KAAK,QACL0E,OAAO,qBAOvB5B,EAAAA,EAAAA,MAAA,OAAK5C,UAAU,qCAAoCyD,SAAA,EAC/Cb,EAAAA,EAAAA,MAAA,OAAAa,SAAA,EACIC,EAAAA,EAAAA,KAAA,OAAK1D,UAAS,aAAAwD,OAAe/B,EAAOK,KAAI,KAAA0B,OAAI4G,EAAgB,gBAAkB,iBAAkB3G,UAC1FwG,EAAKuC,cAAgBvC,EAAKwC,OAASxC,EAAK5E,SAAW4E,EAAK+B,aAAe,GAAGhF,oBAEhFtD,EAAAA,EAAAA,KAAA,OAAK1D,UAAS,GAAAwD,OAAK/B,EAAO8I,QAAO,kBAAiB9G,SAC7CwG,EAAKuC,aAAe,cAAgBvC,EAAK5E,QAAU,KAAO,WAE9D4E,EAAKyC,YACF9J,EAAAA,EAAAA,MAAA,OAAK5C,UAAU,6BAA4ByD,SAAA,CAAC,QAClCwG,EAAK5E,SAAW,GAAG2B,wBAMrCpE,EAAAA,EAAAA,MAAA,OAAK5C,UAAS,4IAAAwD,OAERiH,EAAQW,QAAO,kHAEnB3H,SAAA,EACEC,EAAAA,EAAAA,KAAA,QAAM1D,UAAU,UAASyD,SAAEgH,EAAQS,aACnCxH,EAAAA,EAAAA,KAAA,QAAAD,SAAOgH,EAAQQ,qBAGrB,ECuGd,EA7fwBrL,IAYjB,IAZkB,MACrB+M,EAAQ,GAAE,cACVC,EAAgB,KAAI,OACpBpI,EAAS,aAAY,KACrB1E,EAAO,SAAQ,UACfuK,GAAY,EAAI,UAChBrK,EAAY,GAAE,eACd6M,EAAiB,KAAI,WACrBC,GAAa,EAAK,YAClBC,EAAc,KAAI,YAClBC,GAAc,EAAK,oBACnBC,EAAsB,MACzBrN,EAEG,MAAOsN,EAAYC,IAAiBtH,EAAAA,EAAAA,UAAS,KACtCuH,EAAYC,IAAiBxH,EAAAA,EAAAA,UAAS,QACtCyH,EAAQC,IAAa1H,EAAAA,EAAAA,UAAS,SAC9B2H,EAAUC,IAAe5H,EAAAA,EAAAA,UAAS,SAClC6H,EAAiBC,IAAsB9H,EAAAA,EAAAA,WAAS,IAChD+H,EAAiBC,IAAsBhI,EAAAA,EAAAA,WAAS,GACjDiI,GAAsBC,EAAAA,EAAAA,QAAO,MAG7BC,EAAUnB,GAAkBiB,EAC5BG,EAAenB,GAAcc,EAG7BM,EAAcvB,EAAMwB,MAAKlE,GAAQA,EAAKmE,SAAWxB,GAAiB3C,EAAKoE,MAAQzB,IAC/E0B,EAA8B,OAAXJ,QAAW,IAAXA,OAAW,EAAXA,EAAaK,MAChCC,EAA8B,OAAXN,QAAW,IAAXA,OAAW,EAAXA,EAAanH,MAGhC0H,EAAgB9B,EAAM+B,QAAOzE,IAAS,IAAD0E,EAAAC,EAAAC,EAAAC,EAAAC,EAEvC,MAAMC,GAAyB,QAATL,EAAA1E,EAAKJ,YAAI,IAAA8E,OAAA,EAATA,EAAW7D,cAAcnB,SAASuD,EAAWpC,kBACrC,QADmD8D,EAC7D3E,EAAKgF,aAAK,IAAAL,OAAA,EAAVA,EAAY9D,cAAcnB,SAASuD,EAAWpC,kBACpC,QADkD+D,EAC5D5E,EAAKsE,aAAK,IAAAM,OAAA,EAAVA,EAAY/D,cAAcnB,SAASuD,EAAWpC,gBAG5DoE,GAAgBxB,GAAmBzD,EAAKsE,QAAUD,EAGlDa,GAA8C,QAAjCL,EAAA7E,EAAKY,oCAA4B,IAAAiE,OAAA,EAAjCA,EAAmChE,iBAAwC,QAA3BiE,EAAI9E,EAAKW,0BAAkB,IAAAmE,OAAA,EAAvBA,EAAyBjE,gBAAiB,OACjH,IAAIsE,GAAgB,EAEpB,OAAQhC,GACJ,IAAK,UACDgC,EAA+B,YAAfD,GAA2C,WAAfA,EAC5C,MACJ,IAAK,UACDC,EAA+B,YAAfD,EAChB,MACJ,IAAK,OACDC,EAA+B,SAAfD,EAChB,MACJ,QACIC,GAAgB,EAGxB,OAAOJ,GAAiBI,GAAiBF,CAAY,IACtDG,MAAK,CAACC,EAAGC,KACR,OAAQjC,GACJ,IAAK,KACD,OAAQiC,EAAElK,SAAW,IAAMiK,EAAEjK,SAAW,GAC5C,IAAK,OACD,OAAQiK,EAAEzF,MAAQ,IAAI2F,cAAcD,EAAE1F,MAAQ,IAClD,IAAK,QACD,OAAQ0F,EAAE/C,cAAgB+C,EAAE9C,OAAS,IAAM6C,EAAE9C,cAAgB8C,EAAE7C,OAAS,GAC5E,IAAK,QACD,OAAQ6C,EAAEf,OAAS,IAAIiB,cAAcD,EAAEhB,OAAS,IACpD,QACI,OAAQe,EAAEpF,MAAQ,IAAMqF,EAAErF,MAAQ,GAC1C,IAIEuF,EAAqBhB,EAAcvJ,KAAI+E,IAEzC,MACME,EADiBsE,EAAcC,QAAOgB,GAAKA,EAAEnB,QAAUtE,EAAKsE,QACjCoB,WAAUD,GAAKA,EAAErB,MAAQpE,EAAKoE,KAAOqB,EAAEtB,SAAWnE,EAAKmE,SAAU,EAClG,OAAAwB,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GAAY3F,GAAI,IAAEE,aAAS,IAIzB0F,EAAsBA,KACpB7B,EAAQ8B,UACR9B,EAAQ8B,QAAQC,eAAe,CAC3BC,SAAU,SACVC,MAAO,WAEXpC,GAAmB,GAEnBnH,YAAW,IAAMmH,GAAmB,IAAQ,KAChD,EAuBEqC,EAAavD,EAAM7H,OACnBqL,EAAexD,EAAM+B,QAAOgB,GACL,WAAzBA,EAAE9E,oBACuB,YAAzB8E,EAAE9E,oBACiC,YAAnC8E,EAAE7E,+BACJ/F,OAGIsL,EAAWzD,EAAM7H,OAAS,EAAIF,KAAKC,OAAO8H,EAAMzH,KAAIwK,GACtDA,EAAElD,cAAgBkD,EAAErK,SAAWqK,EAAE1D,aAAe,KAC/C,EAGCqE,EAAc1D,EAAM+B,QAAOgB,IAAMA,EAAEY,mBAAqB,GAAK,IAAGxL,OAChEyL,EAAY5D,EAAM7H,OAAS,EAC7BF,KAAK4L,MAAM7D,EAAM8D,QAAO,CAACC,EAAKhB,IAAMgB,GAAOhB,EAAErK,SAAW,IAAI,GAAKsH,EAAM7H,QAAU,EAErF,OACIlC,EAAAA,EAAAA,MAAA,OAAK5C,UAAS,aAAAwD,OAAexD,GAAYyD,SAAA,CAEpC4G,IACGzH,EAAAA,EAAAA,MAAA,OAAK5C,UAAU,oGAAmGyD,SAAA,EAE9Gb,EAAAA,EAAAA,MAAA,OAAK5C,UAAU,oFAAmFyD,SAAA,EAC9Fb,EAAAA,EAAAA,MAAA,OAAK5C,UAAU,sBAAqByD,SAAA,EAEhCb,EAAAA,EAAAA,MAAA,OAAK5C,UAAU,kCAAiCyD,SAAA,EAE5Cb,EAAAA,EAAAA,MAAA,OAAK5C,UAAU,kBAAiByD,SAAA,EAC5BC,EAAAA,EAAAA,KAACiN,EAAAA,IAAQ,CAAC3Q,UAAU,8EACpB0D,EAAAA,EAAAA,KAAA,SACIkF,GAAG,iBACHiB,KAAK,iBACLnI,KAAK,OACLkP,YAAY,qCACZC,MAAO3D,EACP4D,SAAWC,GAAM5D,EAAc4D,EAAEC,OAAOH,OACxCI,aAAa,MACbjR,UAAU,sKAEbkN,IACGxJ,EAAAA,EAAAA,KAAA,UACIzD,QAASA,IAAMkN,EAAc,IAC7BnN,UAAU,wFAAuFyD,SACpG,eAOTC,EAAAA,EAAAA,KAAA,OAAK1D,UAAU,aAAYyD,UACvBC,EAAAA,EAAAA,KAAA,UACIzD,QAASA,IAAM0N,GAAoBD,GACnC1N,UAAS,gEAAAwD,OACLkK,EACM,mCACA,+CACPjK,SACN,wBAOTb,EAAAA,EAAAA,MAAA,OAAK5C,UAAU,kCAAiCyD,SAAA,EAE5Cb,EAAAA,EAAAA,MAAA,OAAK5C,UAAU,WAAUyD,SAAA,EACrBC,EAAAA,EAAAA,KAACwN,EAAAA,IAAQ,CAAClR,UAAU,8EACpB4C,EAAAA,EAAAA,MAAA,UACIgG,GAAG,iBACHiB,KAAK,iBACLgH,MAAOzD,EACP0D,SAAWC,GAAM1D,EAAc0D,EAAEC,OAAOH,OACxCI,aAAa,MACbjR,UAAU,6JAA4JyD,SAAA,EAEtKC,EAAAA,EAAAA,KAAA,UAAQmN,MAAM,MAAKpN,SAAC,uBACpBC,EAAAA,EAAAA,KAAA,UAAQmN,MAAM,UAASpN,SAAC,gCACxBC,EAAAA,EAAAA,KAAA,UAAQmN,MAAM,OAAMpN,SAAC,6BACrBC,EAAAA,EAAAA,KAAA,UAAQmN,MAAM,UAASpN,SAAC,gCAKhCb,EAAAA,EAAAA,MAAA,UACIgG,GAAG,eACHiB,KAAK,eACLgH,MAAOvD,EACPwD,SAAWC,GAAMxD,EAAUwD,EAAEC,OAAOH,OACpCI,aAAa,MACbjR,UAAU,uJAAsJyD,SAAA,EAEhKC,EAAAA,EAAAA,KAAA,UAAQmN,MAAM,OAAMpN,SAAC,+BACrBC,EAAAA,EAAAA,KAAA,UAAQmN,MAAM,KAAIpN,SAAC,uBACnBC,EAAAA,EAAAA,KAAA,UAAQmN,MAAM,QAAOpN,SAAC,gCACtBC,EAAAA,EAAAA,KAAA,UAAQmN,MAAM,OAAMpN,SAAC,+BACrBC,EAAAA,EAAAA,KAAA,UAAQmN,MAAM,QAAOpN,SAAC,mCAI1Bb,EAAAA,EAAAA,MAAA,OAAK5C,UAAU,kCAAiCyD,SAAA,EAC5CC,EAAAA,EAAAA,KAAA,UACIzD,QAASA,IAAMwN,EAAY,QAC3BzN,UAAS,wEAAAwD,OACQ,SAAbgK,EACM,mCACA,qCACP/J,SACN,WAGDC,EAAAA,EAAAA,KAAA,UACIzD,QAASA,IAAMwN,EAAY,WAC3BzN,UAAS,wEAAAwD,OACQ,YAAbgK,EACM,mCACA,qCACP/J,SACN,sBAQbb,EAAAA,EAAAA,MAAA,OAAK5C,UAAU,mFAAkFyD,SAAA,EAC7Fb,EAAAA,EAAAA,MAAA,OAAK5C,UAAU,wBAAuByD,SAAA,EAClCC,EAAAA,EAAAA,KAAA,QAAM1D,UAAU,8BAA6ByD,SACxCgM,EAAmB3K,SACjB,QAAIpB,EAAAA,EAAAA,KAAA,QAAM1D,UAAU,8BAA6ByD,SAAEkJ,EAAM7H,SAAc,SAC7EoI,IACGtK,EAAAA,EAAAA,MAAA,QAAM5C,UAAU,OAAMyD,SAAA,CAAC,aACVb,EAAAA,EAAAA,MAAA,QAAM5C,UAAU,4BAA2ByD,SAAA,CAAC,IAAEyJ,EAAW,UAG1D,QAAfE,IACG1J,EAAAA,EAAAA,KAAA,QAAM1D,UAAU,4EAA2EyD,SACtF2J,IAGRM,GAAmBY,IAChB5K,EAAAA,EAAAA,KAAA,QAAM1D,UAAU,8EAA6EyD,SACnE,YAArB+K,EAA8B,SAAAhL,OAAY8K,GAAqBA,QAM5E1L,EAAAA,EAAAA,MAAA,OAAK5C,UAAU,mCAAkCyD,SAAA,EAC7Cb,EAAAA,EAAAA,MAAA,QAAAa,SAAA,CAAM,qBAASmB,KAAKC,OAAO4K,EAAmBvK,KAAIwK,GAAKA,EAAElD,cAAgBkD,EAAErK,SAAW,KAAI2B,qBAC1FpE,EAAAA,EAAAA,MAAA,QAAAa,SAAA,CAAM,qBAASmB,KAAK4L,MAAMf,EAAmBgB,QAAO,CAACC,EAAKhB,IAAMgB,GAAOhB,EAAElD,cAAgBkD,EAAErK,SAAW,IAAI,GAAKoK,EAAmB3K,QAAU,GAAGkC,8BAI3JpE,EAAAA,EAAAA,MAAA,OAAK5C,UAAU,yCAAwCyD,SAAA,EACnDb,EAAAA,EAAAA,MAAA,OAAK5C,UAAU,8BAA6ByD,SAAA,EACxCb,EAAAA,EAAAA,MAAA,OAAK5C,UAAU,8BAA6ByD,SAAA,EACxCC,EAAAA,EAAAA,KAAA,OAAK1D,UAAU,0FAAyFyD,UACpGC,EAAAA,EAAAA,KAAC9C,EAAAA,IAAQ,CAACZ,UAAU,0BAExB4C,EAAAA,EAAAA,MAAA,OAAAa,SAAA,EACIC,EAAAA,EAAAA,KAAA,MAAI1D,UAAU,4HAA2HyD,SAAC,iBAG1IC,EAAAA,EAAAA,KAAA,KAAG1D,UAAU,oCAAmCyD,SAAC,2CAIxDsJ,IACGnK,EAAAA,EAAAA,MAAA,OAAK5C,UAAU,qFAAoFyD,SAAA,EAC/FC,EAAAA,EAAAA,KAACyN,EAAAA,IAAO,CAACnR,UAAU,2BACnB4C,EAAAA,EAAAA,MAAA,QAAM5C,UAAU,oCAAmCyD,SAAA,CAAC,WACvC,IAAIK,KAAKiJ,GAAaqE,+BAM/CxO,EAAAA,EAAAA,MAAA,OAAK5C,UAAU,8BAA6ByD,SAAA,CAEvCwJ,IACGrK,EAAAA,EAAAA,MAAA,UACI3C,QAASgN,EACTjN,UAAS,4HAAAwD,OACLwJ,EACM,6CACA,+CACPvJ,SAAA,CAEFuJ,GAActJ,EAAAA,EAAAA,KAAC2N,EAAAA,IAAa,CAACrR,UAAU,aAAe0D,EAAAA,EAAAA,KAAC4N,EAAAA,IAAY,CAACtR,UAAU,aAC/E0D,EAAAA,EAAAA,KAAA,QAAM1D,UAAU,mBAAkByD,SAC7BuJ,EAAc,OAAS,cAQnCJ,IACGhK,EAAAA,EAAAA,MAAA,UACI3C,QAAS4P,EACT7P,UAAU,wKAAuKyD,SAAA,EAEjLC,EAAAA,EAAAA,KAAC6N,EAAAA,IAAM,CAACvR,UAAU,aAClB0D,EAAAA,EAAAA,KAAA,QAAM1D,UAAU,mBAAkByD,SAAC,sBAMnDb,EAAAA,EAAAA,MAAA,OAAK5C,UAAU,wCAAuCyD,SAAA,EAClDb,EAAAA,EAAAA,MAAA,OAAK5C,UAAU,0JAAyJyD,SAAA,EACpKb,EAAAA,EAAAA,MAAA,OAAK5C,UAAU,mCAAkCyD,SAAA,EAC7CC,EAAAA,EAAAA,KAAA,OAAK1D,UAAU,6BAA4ByD,UACvCC,EAAAA,EAAAA,KAACsF,EAAAA,IAAO,CAAChJ,UAAU,0BAEvB0D,EAAAA,EAAAA,KAAA,QAAM1D,UAAU,sCAAqCyD,SAAC,oBAE1DC,EAAAA,EAAAA,KAAA,OAAK1D,UAAU,yCAAwCyD,SAAEyM,KACzDtN,EAAAA,EAAAA,MAAA,OAAK5C,UAAU,oCAAmCyD,SAAA,CAAE4M,EAAY,iBAGpEzN,EAAAA,EAAAA,MAAA,OAAK5C,UAAU,8JAA6JyD,SAAA,EACxKb,EAAAA,EAAAA,MAAA,OAAK5C,UAAU,mCAAkCyD,SAAA,EAC7CC,EAAAA,EAAAA,KAAA,OAAK1D,UAAU,gEAA+DyD,UAC1EC,EAAAA,EAAAA,KAAC9C,EAAAA,IAAQ,CAACZ,UAAU,0BAExB0D,EAAAA,EAAAA,KAAA,QAAM1D,UAAU,wCAAuCyD,SAAC,sBAE5DC,EAAAA,EAAAA,KAAA,OAAK1D,UAAU,2CAA0CyD,SAAE0M,KAC3DvN,EAAAA,EAAAA,MAAA,OAAK5C,UAAU,sCAAqCyD,SAAA,CAC/CyM,EAAa,EAAItL,KAAK4L,MAAOL,EAAeD,EAAc,KAAO,EAAE,mBAI5EtN,EAAAA,EAAAA,MAAA,OAAK5C,UAAU,6JAA4JyD,SAAA,EACvKb,EAAAA,EAAAA,MAAA,OAAK5C,UAAU,mCAAkCyD,SAAA,EAC7CC,EAAAA,EAAAA,KAAA,OAAK1D,UAAU,8BAA6ByD,UACxCC,EAAAA,EAAAA,KAAC6N,EAAAA,IAAM,CAACvR,UAAU,0BAEtB0D,EAAAA,EAAAA,KAAA,QAAM1D,UAAU,uCAAsCyD,SAAC,kBAE3DC,EAAAA,EAAAA,KAAA,OAAK1D,UAAU,0CAAyCyD,SAAE2M,EAASpJ,oBACnEtD,EAAAA,EAAAA,KAAA,OAAK1D,UAAU,qCAAoCyD,SAAC,uBAGxDb,EAAAA,EAAAA,MAAA,OAAK5C,UAAU,4JAA2JyD,SAAA,EACtKb,EAAAA,EAAAA,MAAA,OAAK5C,UAAU,mCAAkCyD,SAAA,EAC7CC,EAAAA,EAAAA,KAAA,OAAK1D,UAAU,+BAA8ByD,UACzCC,EAAAA,EAAAA,KAAC9C,EAAAA,IAAQ,CAACZ,UAAU,0BAExB0D,EAAAA,EAAAA,KAAA,QAAM1D,UAAU,wCAAuCyD,SAAC,eAE5DC,EAAAA,EAAAA,KAAA,OAAK1D,UAAU,2CAA0CyD,SAAE8M,EAAUvJ,oBACrEtD,EAAAA,EAAAA,KAAA,OAAK1D,UAAU,6BAA4ByD,SAAC,gCAO5DC,EAAAA,EAAAA,KAAA,OAAK1D,UAAS,oBAAAwD,OA5RGgO,MACrB,GAAiB,YAAbhE,EACA,MAAO,YAGX,OAAQhJ,GACJ,IAAK,WACD,MAAO,sEACX,IAAK,OACD,MAAO,uDAEX,QACI,MAAO,YACf,EA+QwCgN,IAAqB/N,SACpDgM,EAAmBvK,KAAI,CAAC+E,EAAM9E,KAC3B,MAAMiF,EAAgBH,EAAKmE,SAAWxB,GAAiB3C,EAAKoE,MAAQzB,EAC9D1C,EAAOD,EAAKC,MAAQ/E,EAAQ,EAGL,IAADjB,EAAAuN,EAAAC,EAAAC,EAAAC,EAAAC,EAA5B,MAAiB,YAAbrE,GAEI9J,EAAAA,EAAAA,KAAA,OAEIoO,IAAK1H,EAAgB4D,EAAU,KAC/BhO,UAAS,yEAAAwD,OACL4G,GAAiB6D,EACX,0GACA7D,EACA,kDACA,kEACP3G,UAEHb,EAAAA,EAAAA,MAAA,OAAK5C,UAAU,oCAAmCyD,SAAA,EAC9Cb,EAAAA,EAAAA,MAAA,OAAK5C,UAAU,8BAA6ByD,SAAA,EAExCC,EAAAA,EAAAA,KAAA,OAAK1D,UAAS,2EAAAwD,OACV0G,GAAQ,EAAI,4DACZA,GAAQ,GAAK,wDACb,6BACDzG,SACEyG,KAILtH,EAAAA,EAAAA,MAAA,OAAK5C,UAAU,8BAA6ByD,SAAA,EACxCC,EAAAA,EAAAA,KAAA,OACIqO,IAAK9H,EAAK+H,gBAAkB,sBAC5BC,IAAKhI,EAAKJ,KACV7J,UAAU,gEAEd4C,EAAAA,EAAAA,MAAA,OAAAa,SAAA,EACIC,EAAAA,EAAAA,KAAA,OAAK1D,UAAU,sCAAqCyD,SAAEwG,EAAKJ,QAC3DnG,EAAAA,EAAAA,KAAA,OAAK1D,UAAU,wBAAuByD,SAClB,YAAfwG,EAAKlD,MAAmB,SAAAvD,OAAYyG,EAAKsE,OAAUtE,EAAKsE,kBAOzE3L,EAAAA,EAAAA,MAAA,OAAK5C,UAAU,8BAA6ByD,SAAA,EACxCb,EAAAA,EAAAA,MAAA,OAAK5C,UAAU,aAAYyD,SAAA,EACvBC,EAAAA,EAAAA,KAAA,OAAK1D,UAAU,kCAAiCyD,UAC1CwG,EAAKuC,cAAgBvC,EAAK5E,SAAW,GAAG2B,oBAE9CtD,EAAAA,EAAAA,KAAA,OAAK1D,UAAU,wBAAuByD,SACjCwG,EAAKuC,aAAe,MAAQ,WAKrC9I,EAAAA,EAAAA,KAAA,OAAK1D,UAAS,8CAAAwD,OACwE,aAArB,QAA7DU,EAAC+F,EAAKY,8BAAgCZ,EAAKW,0BAAkB,IAAA1G,OAAA,EAA7DA,EAAgE4G,gBACkB,YAArB,QAA7D2G,EAACxH,EAAKY,8BAAgCZ,EAAKW,0BAAkB,IAAA6G,OAAA,EAA7DA,EAAgE3G,eAC1D,gCACkF,aAArB,QAA7D4G,EAACzH,EAAKY,8BAAgCZ,EAAKW,0BAAkB,IAAA8G,OAAA,EAA7DA,EAAgE5G,eAChE,0BACA,6BACPrH,SACoF,aAArB,QAA7DkO,EAAC1H,EAAKY,8BAAgCZ,EAAKW,0BAAkB,IAAA+G,OAAA,EAA7DA,EAAgE7G,gBACkB,YAArB,QAA7D8G,EAAC3H,EAAKY,8BAAgCZ,EAAKW,0BAAkB,IAAAgH,OAAA,EAA7DA,EAAgE9G,eAA6B,eACX,aAArB,QAA7D+G,EAAC5H,EAAKY,8BAAgCZ,EAAKW,0BAAkB,IAAAiH,OAAA,EAA7DA,EAAgE/G,eAA8B,SAAM,wBA3D5Gb,EAAKmE,QAAUnE,EAAKoE,MAqEjC3K,EAAAA,EAAAA,KAAA,OAEIoO,IAAK1H,EAAgB4D,EAAU,KAC/BhO,UAAS,mDAAAwD,OACL4G,GAAiB6D,EACX,8GACA7D,EACA,gDACI,IACP3G,UAEHC,EAAAA,EAAAA,KAACwO,EAAe,CACZjI,KAAMA,EACNC,KAAMA,EACNC,UAAWF,EAAKE,UAChBC,cAAeA,EACf5F,OAAQA,EACR1E,KAAMA,EACNuK,UAAWA,KAjBdJ,EAAKmE,QAAUnE,EAAKoE,IAmBnB,MAMS,IAA9BoB,EAAmB3K,SAChBlC,EAAAA,EAAAA,MAAA,OAAK5C,UAAU,gFAA+EyD,SAAA,EAC1FC,EAAAA,EAAAA,KAACsF,EAAAA,IAAO,CAAChJ,UAAU,0CACnB0D,EAAAA,EAAAA,KAAA,MAAI1D,UAAU,2CAA0CyD,SAAC,oBACzDC,EAAAA,EAAAA,KAAA,KAAG1D,UAAU,gBAAeyD,SACN,IAAjBkJ,EAAM7H,OAAe,6BAA+B,qDAMhE8H,GAAiB6C,EAAmB3K,OAAS,KAC1CpB,EAAAA,EAAAA,KAAA,UACIzD,QAAS4P,EACT7P,UAAU,6LACVM,MAAM,qBAAoBmD,UAE1BC,EAAAA,EAAAA,KAAC6N,EAAAA,IAAM,CAACvR,UAAU,gBAGxB,ECrOd,EAnRoBmS,KAChB,MAAO3N,EAAQ4N,IAAavM,EAAAA,EAAAA,UAAS,eAC9B/F,EAAMuS,IAAWxM,EAAAA,EAAAA,UAAS,UAG3ByM,EAAc,CAChB,CACIlE,OAAQ,IACRvE,KAAM,gBACNmI,eAAgB,8FAChBpH,mBAAoB,SACpBoB,YAAa,KACbK,iBAAkB,GAClBC,aAAc,GACdG,MAAO,KACPvC,KAAM,GAEV,CACIkE,OAAQ,IACRvE,KAAM,YACNmI,eAAgB,8FAChBpH,mBAAoB,OACpBoB,YAAa,KACbK,iBAAkB,GAClBC,aAAc,GACdG,MAAO,KACPvC,KAAM,GAEV,CACIkE,OAAQ,IACRvE,KAAM,cACNmI,eAAgB,8FAChBpH,mBAAoB,UACpBoB,YAAa,KACbK,iBAAkB,GAClBC,aAAc,GACdG,MAAO,KACPvC,KAAM,GAEV,CACIkE,OAAQ,IACRvE,KAAM,eACNmI,eAAgB,8FAChBpH,mBAAoB,UACpBoB,YAAa,KACbK,iBAAkB,GAClBC,aAAc,GACdG,MAAO,KACPvC,KAAM,GAEV,CACIkE,OAAQ,IACRvE,KAAM,aACNmI,eAAgB,2FAChBpH,mBAAoB,SACpBoB,YAAa,KACbK,iBAAkB,GAClBC,aAAc,GACdG,MAAO,KACPvC,KAAM,GAEV,CACIkE,OAAQ,IACRvE,KAAM,eACNmI,eAAgB,8FAChBpH,mBAAoB,OACpBoB,YAAa,KACbK,iBAAkB,EAClBC,aAAc,GACdG,MAAO,KACPvC,KAAM,GAEV,CACIkE,OAAQ,IACRvE,KAAM,YACNmI,eAAgB,8FAChBpH,mBAAoB,UACpBoB,YAAa,KACbK,iBAAkB,EAClBC,aAAc,GACdG,MAAO,KACPvC,KAAM,GAEV,CACIkE,OAAQ,IACRvE,KAAM,eACNmI,eAAgB,8FAChBpH,mBAAoB,OACpBoB,YAAa,KACbK,iBAAkB,EAClBC,aAAc,GACdG,MAAO,KACPvC,KAAM,IAId,OACIxG,EAAAA,EAAAA,KAAA,OAAK1D,UAAU,+BAA8ByD,UACzCb,EAAAA,EAAAA,MAAA,OAAK5C,UAAU,yCAAwCyD,SAAA,EAEnDb,EAAAA,EAAAA,MAACC,EAAAA,EAAOC,IAAG,CACPO,QAAS,CAAEhB,QAAS,EAAGsB,GAAI,IAC3BL,QAAS,CAAEjB,QAAS,EAAGsB,EAAG,GAC1B3D,UAAU,mBAAkByD,SAAA,EAE5BC,EAAAA,EAAAA,KAAA,MAAI1D,UAAU,wCAAuCyD,SAAC,mCAGtDC,EAAAA,EAAAA,KAAA,KAAG1D,UAAU,0CAAyCyD,SAAC,gJAO3Db,EAAAA,EAAAA,MAACC,EAAAA,EAAOC,IAAG,CACPO,QAAS,CAAEhB,QAAS,EAAGsB,GAAI,IAC3BL,QAAS,CAAEjB,QAAS,EAAGsB,EAAG,GAC1BpB,WAAY,CAAEiG,MAAO,IACrBxI,UAAU,gEAA+DyD,SAAA,EAEzEC,EAAAA,EAAAA,KAAA,MAAI1D,UAAU,2CAA0CyD,SAAC,2BAEzDb,EAAAA,EAAAA,MAAA,OAAK5C,UAAU,wCAAuCyD,SAAA,EAElDb,EAAAA,EAAAA,MAAA,OAAAa,SAAA,EACIC,EAAAA,EAAAA,KAAA,SAAO1D,UAAU,+CAA8CyD,SAAC,kBAGhEC,EAAAA,EAAAA,KAAA,OAAK1D,UAAU,YAAWyD,SACrB,CAAC,aAAc,WAAY,QAAQyB,KAAKqN,IACrC3P,EAAAA,EAAAA,MAAA,SAA0B5C,UAAU,oBAAmByD,SAAA,EACnDC,EAAAA,EAAAA,KAAA,SACIkF,GAAE,UAAApF,OAAY+O,GACd7Q,KAAK,QACLmI,KAAK,SACLgH,MAAO0B,EACPC,QAAShO,IAAW+N,EACpBzB,SAAWC,GAAMqB,EAAUrB,EAAEC,OAAOH,OACpC7Q,UAAU,wBAEd0D,EAAAA,EAAAA,KAAA,QAAM1D,UAAU,aAAYyD,SAAE8O,MAVtBA,WAiBxB3P,EAAAA,EAAAA,MAAA,OAAAa,SAAA,EACIC,EAAAA,EAAAA,KAAA,SAAO1D,UAAU,+CAA8CyD,SAAC,oBAGhEC,EAAAA,EAAAA,KAAA,OAAK1D,UAAU,YAAWyD,SACrB,CAAC,QAAS,SAAU,SAASyB,KAAKuN,IAC/B7P,EAAAA,EAAAA,MAAA,SAAwB5C,UAAU,oBAAmByD,SAAA,EACjDC,EAAAA,EAAAA,KAAA,SACIkF,GAAE,QAAApF,OAAUiP,GACZ/Q,KAAK,QACLmI,KAAK,OACLgH,MAAO4B,EACPD,QAAS1S,IAAS2S,EAClB3B,SAAWC,GAAMsB,EAAQtB,EAAEC,OAAOH,OAClC7Q,UAAU,wBAEd0D,EAAAA,EAAAA,KAAA,QAAM1D,UAAU,aAAYyD,SAAEgP,MAVtBA,iBAmBhC7P,EAAAA,EAAAA,MAACC,EAAAA,EAAOC,IAAG,CACPO,QAAS,CAAEhB,QAAS,EAAGsB,EAAG,IAC1BL,QAAS,CAAEjB,QAAS,EAAGsB,EAAG,GAC1BpB,WAAY,CAAEiG,MAAO,IACrBxI,UAAU,OAAMyD,SAAA,EAEhBC,EAAAA,EAAAA,KAAA,MAAI1D,UAAU,wCAAuCyD,SAAC,8BAEtDb,EAAAA,EAAAA,MAAA,OAAK5C,UAAU,wCAAuCyD,SAAA,EAElDb,EAAAA,EAAAA,MAAA,OAAK5C,UAAU,iDAAgDyD,SAAA,EAC3DC,EAAAA,EAAAA,KAAA,MAAI1D,UAAU,2CAA0CyD,SAAC,8BACzDC,EAAAA,EAAAA,KAACwO,EAAe,CACZjI,KAAMqI,EAAY,GAClBpI,KAAM,EACNE,eAAe,EACf5F,OAAO,aACP1E,KAAMA,EACNuK,WAAW,QAKnBzH,EAAAA,EAAAA,MAAA,OAAK5C,UAAU,iDAAgDyD,SAAA,EAC3DC,EAAAA,EAAAA,KAAA,MAAI1D,UAAU,2CAA0CyD,SAAC,gCACzDC,EAAAA,EAAAA,KAACwO,EAAe,CACZjI,KAAMqI,EAAY,GAClBpI,KAAM,EACNE,eAAe,EACf5F,OAAO,aACP1E,KAAMA,EACNuK,WAAW,cAO3BzH,EAAAA,EAAAA,MAACC,EAAAA,EAAOC,IAAG,CACPO,QAAS,CAAEhB,QAAS,EAAGsB,EAAG,IAC1BL,QAAS,CAAEjB,QAAS,EAAGsB,EAAG,GAC1BpB,WAAY,CAAEiG,MAAO,IAAM/E,SAAA,EAE3BC,EAAAA,EAAAA,KAAA,MAAI1D,UAAU,wCAAuCyD,SAAC,2BAEtDC,EAAAA,EAAAA,KAACgP,EAAe,CACZ/F,MAAO2F,EACP1F,cAAc,IACdpI,OAAQA,EACR1E,KAAMA,EACN6S,YAAY,EACZC,aAAa,EACbvI,WAAW,QAKnBzH,EAAAA,EAAAA,MAACC,EAAAA,EAAOC,IAAG,CACPO,QAAS,CAAEhB,QAAS,EAAGsB,EAAG,IAC1BL,QAAS,CAAEjB,QAAS,EAAGsB,EAAG,GAC1BpB,WAAY,CAAEiG,MAAO,IACrBxI,UAAU,uDAAsDyD,SAAA,EAEhEC,EAAAA,EAAAA,KAAA,MAAI1D,UAAU,wCAAuCyD,SAAC,kBAEtDb,EAAAA,EAAAA,MAAA,OAAK5C,UAAU,uDAAsDyD,SAAA,EACjEb,EAAAA,EAAAA,MAAA,OAAK5C,UAAU,YAAWyD,SAAA,EACtBC,EAAAA,EAAAA,KAAA,MAAI1D,UAAU,8BAA6ByD,SAAC,uCAC5CC,EAAAA,EAAAA,KAAA,KAAG1D,UAAU,wBAAuByD,SAAC,6EAGzCb,EAAAA,EAAAA,MAAA,OAAK5C,UAAU,YAAWyD,SAAA,EACtBC,EAAAA,EAAAA,KAAA,MAAI1D,UAAU,8BAA6ByD,SAAC,oCAC5CC,EAAAA,EAAAA,KAAA,KAAG1D,UAAU,wBAAuByD,SAAC,gEAGzCb,EAAAA,EAAAA,MAAA,OAAK5C,UAAU,YAAWyD,SAAA,EACtBC,EAAAA,EAAAA,KAAA,MAAI1D,UAAU,8BAA6ByD,SAAC,kCAC5CC,EAAAA,EAAAA,KAAA,KAAG1D,UAAU,wBAAuByD,SAAC,8DAGzCb,EAAAA,EAAAA,MAAA,OAAK5C,UAAU,YAAWyD,SAAA,EACtBC,EAAAA,EAAAA,KAAA,MAAI1D,UAAU,8BAA6ByD,SAAC,8BAC5CC,EAAAA,EAAAA,KAAA,KAAG1D,UAAU,wBAAuByD,SAAC,mEAGzCb,EAAAA,EAAAA,MAAA,OAAK5C,UAAU,YAAWyD,SAAA,EACtBC,EAAAA,EAAAA,KAAA,MAAI1D,UAAU,8BAA6ByD,SAAC,kCAC5CC,EAAAA,EAAAA,KAAA,KAAG1D,UAAU,wBAAuByD,SAAC,oDAGzCb,EAAAA,EAAAA,MAAA,OAAK5C,UAAU,YAAWyD,SAAA,EACtBC,EAAAA,EAAAA,KAAA,MAAI1D,UAAU,8BAA6ByD,SAAC,qCAC5CC,EAAAA,EAAAA,KAAA,KAAG1D,UAAU,wBAAuByD,SAAC,oEAKnD,C,0DCjRd,SAASoP,IACL,MAAMC,GAAY/E,EAAAA,EAAAA,SAAO,GAOzB,OANAgF,EAAAA,EAAAA,IAA0B,KACtBD,EAAUhD,SAAU,EACb,KACHgD,EAAUhD,SAAU,CAAK,IAE9B,IACIgD,CACX,C,qCCLA,MAAME,UAAwBC,EAAAA,UAC1BC,uBAAAA,CAAwBC,GACpB,MAAMC,EAAUC,KAAKC,MAAMC,SAASzD,QACpC,GAAIsD,GAAWD,EAAUK,YAAcH,KAAKC,MAAME,UAAW,CACzD,MAAM1T,EAAOuT,KAAKC,MAAMG,QAAQ3D,QAChChQ,EAAKuG,OAAS+M,EAAQM,cAAgB,EACtC5T,EAAKmH,MAAQmM,EAAQO,aAAe,EACpC7T,EAAK8T,IAAMR,EAAQS,UACnB/T,EAAKgU,KAAOV,EAAQW,UACxB,CACA,OAAO,IACX,CAIAC,kBAAAA,GAAuB,CACvBC,MAAAA,GACI,OAAOZ,KAAKC,MAAM7P,QACtB,EAEJ,SAASyQ,EAAQtU,GAA0B,IAAzB,SAAE6D,EAAQ,UAAE+P,GAAW5T,EACrC,MAAMgJ,GAAKuL,EAAAA,EAAAA,SACLrC,GAAM/D,EAAAA,EAAAA,QAAO,MACbjO,GAAOiO,EAAAA,EAAAA,QAAO,CAChB9G,MAAO,EACPZ,OAAQ,EACRuN,IAAK,EACLE,KAAM,IAiCV,OAtBAM,EAAAA,EAAAA,qBAAmB,KACf,MAAM,MAAEnN,EAAK,OAAEZ,EAAM,IAAEuN,EAAG,KAAEE,GAAShU,EAAKgQ,QAC1C,GAAI0D,IAAc1B,EAAIhC,UAAY7I,IAAUZ,EACxC,OACJyL,EAAIhC,QAAQuE,QAAQC,YAAc1L,EAClC,MAAMkD,EAAQyI,SAASC,cAAc,SAarC,OAZAD,SAASE,KAAKC,YAAY5I,GACtBA,EAAM6I,OACN7I,EAAM6I,MAAMC,WAAW,oCAADpR,OACDoF,EAAE,yEAAApF,OAEdyD,EAAK,wCAAAzD,OACJ6C,EAAM,qCAAA7C,OACToQ,EAAG,sCAAApQ,OACFsQ,EAAI,0CAIT,KACHS,SAASE,KAAKI,YAAY/I,EAAM,CACnC,GACF,CAAC0H,IACIP,EAAAA,cAAoBD,EAAiB,CAAEQ,UAAWA,EAAWD,SAAUzB,EAAK2B,QAAS3T,GAAQmT,EAAAA,aAAmBxP,EAAU,CAAEqO,QACxI,CC9DA,MAAMgD,EAAgBlV,IAA4F,IAA3F,SAAE6D,EAAQ,QAAEJ,EAAO,UAAEmQ,EAAS,eAAEuB,EAAc,OAAEC,EAAM,sBAAEC,EAAqB,KAAEC,GAAOtV,EACzG,MAAMuV,GAAmBC,EAAAA,EAAAA,GAAYC,GAC/BzM,GAAKuL,EAAAA,EAAAA,SACLmB,GAAUC,EAAAA,EAAAA,UAAQ,KAAM,CAC1B3M,KACAvF,UACAmQ,YACAwB,SACAD,eAAiBS,IACbL,EAAiBM,IAAID,GAAS,GAC9B,IAAK,MAAME,KAAcP,EAAiBQ,SACtC,IAAKD,EACD,OAERX,GAAkBA,GAAgB,EAEtCa,SAAWJ,IACPL,EAAiBM,IAAID,GAAS,GACvB,IAAML,EAAiBU,OAAOL,OAQ7CP,OAAwB7I,EAAY,CAACoH,IAiBrC,OAhBA+B,EAAAA,EAAAA,UAAQ,KACJJ,EAAiBW,SAAQ,CAACC,EAAGC,IAAQb,EAAiBM,IAAIO,GAAK,IAAO,GACvE,CAACxC,IAKJP,EAAAA,WAAgB,MACXO,IACI2B,EAAiBrV,MAClBiV,GACAA,GAAgB,GACrB,CAACvB,IACS,cAAT0B,IACAzR,EAAWwP,EAAAA,cAAoBiB,EAAU,CAAEV,UAAWA,GAAa/P,IAE/DwP,EAAAA,cAAoBgD,EAAAA,EAAgBC,SAAU,CAAErF,MAAOyE,GAAW7R,EAAS,EAEvF,SAAS4R,IACL,OAAO,IAAIc,GACf,C,4BC3CA,MAAMC,EAAeC,GAAUA,EAAML,KAAO,GAiD5C,MAAMpP,EAAkBhH,IAAyH,IAAxH,SAAE6D,EAAQ,OAAEuR,EAAM,QAAE3R,GAAU,EAAI,eAAE0R,EAAc,gBAAEuB,EAAe,sBAAErB,GAAwB,EAAI,KAAEC,EAAO,QAAStV,GACxI2W,EAAAA,EAAAA,IAAWD,EAAiB,4CAG5B,MAAME,GAAcC,EAAAA,EAAAA,YAAWC,EAAAA,GAAoBF,aC3DvD,WACI,MAAM1D,EAAYD,KACX8D,EAAmBC,IAAwB/Q,EAAAA,EAAAA,UAAS,GACrD2Q,GAAcK,EAAAA,EAAAA,cAAY,KAC5B/D,EAAUhD,SAAW8G,EAAqBD,EAAoB,EAAE,GACjE,CAACA,IAMJ,MAAO,EADqBE,EAAAA,EAAAA,cAAY,IAAMC,EAAAA,GAAMC,WAAWP,IAAc,CAACA,IACjDG,EACjC,CD+CsEK,GAAiB,GAC7ElE,EAAYD,IAEZoE,EAjDV,SAAsBxT,GAClB,MAAMyT,EAAW,GAMjB,OAJAC,EAAAA,SAASrB,QAAQrS,GAAW4S,KACpBe,EAAAA,EAAAA,gBAAef,IACfa,EAASG,KAAKhB,EAAM,IAErBa,CACX,CAyC6BI,CAAa7T,GACtC,IAAI8T,EAAmBN,EACvB,MAAMO,GAAkBzJ,EAAAA,EAAAA,QAAO,IAAIoI,KAAOrG,QAGpC2H,GAAkB1J,EAAAA,EAAAA,QAAOwJ,GAEzBG,GAAc3J,EAAAA,EAAAA,QAAO,IAAIoI,KAAOrG,QAGhC6H,GAAkB5J,EAAAA,EAAAA,SAAO,GE1EnC,IAA0B6J,EFqFtB,IAVA7E,EAAAA,EAAAA,IAA0B,KACtB4E,EAAgB7H,SAAU,EAnElC,SAA2BrM,EAAUiU,GACjCjU,EAASqS,SAASO,IACd,MAAML,EAAMI,EAAYC,GACxBqB,EAAYjC,IAAIO,EAAKK,EAAM,GAEnC,CA+DQwB,CAAkBZ,EAAkBS,GACpCD,EAAgB3H,QAAUyH,CAAgB,IE9ExBK,EFgFL,KACbD,EAAgB7H,SAAU,EAC1B4H,EAAYI,QACZN,EAAgBM,OAAO,GElFpBtR,EAAAA,EAAAA,YAAU,IAAM,IAAMoR,KAAY,IFoFrCD,EAAgB7H,QAChB,OAAQmD,EAAAA,cAAoBA,EAAAA,SAAgB,KAAMsE,EAAiBrS,KAAKmR,GAAWpD,EAAAA,cAAoB6B,EAAe,CAAEkB,IAAKI,EAAYC,GAAQ7C,WAAW,EAAMnQ,UAASA,QAAU+I,EAAmB6I,sBAAuBA,EAAuBC,KAAMA,GAAQmB,MAGxQkB,EAAmB,IAAIA,GAGvB,MAAMQ,EAAcN,EAAgB3H,QAAQ5K,IAAIkR,GAC1C4B,EAAaf,EAAiB/R,IAAIkR,GAElC6B,EAAaF,EAAYjT,OAC/B,IAAK,IAAIoT,EAAI,EAAGA,EAAID,EAAYC,IAAK,CACjC,MAAMlC,EAAM+B,EAAYG,IACS,IAA7BF,EAAWG,QAAQnC,IAAgBwB,EAAgBY,IAAIpC,IACvDwB,EAAgB/B,IAAIO,OAAK5J,EAEjC,CA4DA,MAzDa,SAAT8I,GAAmBsC,EAAgB1X,OACnCyX,EAAmB,IAIvBC,EAAgB1B,SAAQ,CAACuC,EAAWrC,KAEhC,IAAiC,IAA7BgC,EAAWG,QAAQnC,GACnB,OACJ,MAAMK,EAAQqB,EAAYY,IAAItC,GAC9B,IAAKK,EACD,OACJ,MAAMkC,EAAiBR,EAAYI,QAAQnC,GAC3C,IAAIwC,EAAmBH,EACvB,IAAKG,EAAkB,CACnB,MAAMC,EAASA,KAEXjB,EAAgB3B,OAAOG,GAIvB,MAAM0C,EAAeC,MAAMC,KAAKlB,EAAYmB,QAAQnK,QAAQoK,IAAcd,EAAWrO,SAASmP,KAa9F,GAXAJ,EAAa5C,SAASiD,GAAgBrB,EAAY7B,OAAOkD,KAEzDtB,EAAgB3H,QAAUmH,EAAiBvI,QAAQsK,IAC/C,MAAMC,EAAkB7C,EAAY4C,GACpC,OAEAC,IAAoBjD,GAEhB0C,EAAa/O,SAASsP,EAAiB,KAG1CzB,EAAgB1X,KAAM,CACvB,IAA0B,IAAtBgT,EAAUhD,QACV,OACJ0G,IACAzB,GAAkBA,GACtB,GAEJyD,EAAoBvF,EAAAA,cAAoB6B,EAAe,CAAEkB,IAAKI,EAAYC,GAAQ7C,WAAW,EAAOuB,eAAgB0D,EAAQzD,OAAQA,EAAQC,sBAAuBA,EAAuBC,KAAMA,GAAQmB,GACxMmB,EAAgB/B,IAAIO,EAAKwC,EAC7B,CACAjB,EAAiB2B,OAAOX,EAAgB,EAAGC,EAAiB,IAIhEjB,EAAmBA,EAAiBrS,KAAKmR,IACrC,MAAML,EAAMK,EAAML,IAClB,OAAOwB,EAAgBY,IAAIpC,GAAQK,EAAUpD,EAAAA,cAAoB6B,EAAe,CAAEkB,IAAKI,EAAYC,GAAQ7C,WAAW,EAAMyB,sBAAuBA,EAAuBC,KAAMA,GAAQmB,EAAO,IAO3LpD,EAAAA,cAAoBA,EAAAA,SAAgB,KAAMuE,EAAgB1X,KAC5DyX,EACAA,EAAiBrS,KAAKmR,IAAU8C,EAAAA,EAAAA,cAAa9C,KAAQ,C", "sources": ["components/modern/AchievementBadge.js", "components/modern/XPProgressBar.js", "components/modern/LevelBadge.js", "components/modern/EnhancedAchievementBadge.js", "components/modern/UserRankingCard.js", "components/modern/UserRankingList.js", "components/modern/RankingDemo.js", "../node_modules/framer-motion/dist/es/utils/use-is-mounted.mjs", "../node_modules/framer-motion/dist/es/components/AnimatePresence/PopChild.mjs", "../node_modules/framer-motion/dist/es/components/AnimatePresence/PresenceChild.mjs", "../node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs", "../node_modules/framer-motion/dist/es/utils/use-force-update.mjs", "../node_modules/framer-motion/dist/es/utils/use-unmount-effect.mjs"], "sourcesContent": ["import React from 'react';\nimport { motion } from 'framer-motion';\nimport { \n  TbTrophy, \n  TbMedal, \n  TbCrown, \n  TbStar, \n  TbFlame, \n  TbTarget, \n  TbTrendingUp, \n  TbBolt,\n  TbAward,\n  TbDiamond\n} from 'react-icons/tb';\n\nconst AchievementBadge = ({ \n  achievement, \n  size = 'medium', \n  showDetails = true, \n  className = '',\n  onClick = null \n}) => {\n  // Achievement type configurations\n  const achievementConfig = {\n    first_quiz: {\n      icon: TbStar,\n      title: 'First Steps',\n      description: 'Completed your first quiz',\n      color: 'from-blue-400 to-blue-600',\n      bgColor: 'bg-blue-50',\n      textColor: 'text-blue-700'\n    },\n    perfect_score: {\n      icon: TbTrophy,\n      title: 'Perfect Score',\n      description: 'Achieved 100% on a quiz',\n      color: 'from-yellow-400 to-yellow-600',\n      bgColor: 'bg-yellow-50',\n      textColor: 'text-yellow-700'\n    },\n    streak_5: {\n      icon: TbFlame,\n      title: 'Hot Streak',\n      description: '5 correct answers in a row',\n      color: 'from-orange-400 to-red-500',\n      bgColor: 'bg-orange-50',\n      textColor: 'text-orange-700'\n    },\n    streak_10: {\n      icon: TbFlame,\n      title: 'Fire Streak',\n      description: '10 correct answers in a row',\n      color: 'from-red-400 to-red-600',\n      bgColor: 'bg-red-50',\n      textColor: 'text-red-700'\n    },\n    streak_20: {\n      icon: TbFlame,\n      title: 'Blazing Streak',\n      description: '20 correct answers in a row',\n      color: 'from-red-500 to-purple-600',\n      bgColor: 'bg-red-50',\n      textColor: 'text-red-700'\n    },\n    subject_master: {\n      icon: TbCrown,\n      title: 'Subject Master',\n      description: 'Mastered a subject',\n      color: 'from-purple-400 to-purple-600',\n      bgColor: 'bg-purple-50',\n      textColor: 'text-purple-700'\n    },\n    speed_demon: {\n      icon: TbBolt,\n      title: 'Speed Demon',\n      description: 'Completed quiz in record time',\n      color: 'from-cyan-400 to-blue-500',\n      bgColor: 'bg-cyan-50',\n      textColor: 'text-cyan-700'\n    },\n    consistent_learner: {\n      icon: TbTarget,\n      title: 'Consistent Learner',\n      description: 'Maintained consistent performance',\n      color: 'from-green-400 to-green-600',\n      bgColor: 'bg-green-50',\n      textColor: 'text-green-700'\n    },\n    improvement_star: {\n      icon: TbTrendingUp,\n      title: 'Improvement Star',\n      description: 'Showed remarkable improvement',\n      color: 'from-indigo-400 to-indigo-600',\n      bgColor: 'bg-indigo-50',\n      textColor: 'text-indigo-700'\n    }\n  };\n\n  // Size configurations\n  const sizeConfig = {\n    small: {\n      container: 'w-12 h-12',\n      icon: 'w-6 h-6',\n      text: 'text-xs',\n      padding: 'p-2'\n    },\n    medium: {\n      container: 'w-16 h-16',\n      icon: 'w-8 h-8',\n      text: 'text-sm',\n      padding: 'p-3'\n    },\n    large: {\n      container: 'w-20 h-20',\n      icon: 'w-10 h-10',\n      text: 'text-base',\n      padding: 'p-4'\n    }\n  };\n\n  const config = achievementConfig[achievement.type] || achievementConfig.first_quiz;\n  const sizes = sizeConfig[size];\n  const IconComponent = config.icon;\n\n  const badgeVariants = {\n    hidden: { opacity: 0, scale: 0.8, rotate: -10 },\n    visible: { \n      opacity: 1, \n      scale: 1, \n      rotate: 0,\n      transition: {\n        type: \"spring\",\n        stiffness: 300,\n        damping: 20\n      }\n    },\n    hover: { \n      scale: 1.1, \n      rotate: 5,\n      transition: {\n        type: \"spring\",\n        stiffness: 400,\n        damping: 10\n      }\n    }\n  };\n\n  const glowVariants = {\n    hidden: { opacity: 0 },\n    visible: { \n      opacity: [0.5, 1, 0.5],\n      transition: {\n        duration: 2,\n        repeat: Infinity,\n        ease: \"easeInOut\"\n      }\n    }\n  };\n\n  return (\n    <motion.div\n      variants={badgeVariants}\n      initial=\"hidden\"\n      animate=\"visible\"\n      whileHover=\"hover\"\n      onClick={onClick}\n      className={`\n        relative cursor-pointer group\n        ${className}\n      `}\n    >\n      {/* Glow effect */}\n      <motion.div\n        variants={glowVariants}\n        className={`\n          absolute inset-0 rounded-full blur-md opacity-75\n          bg-gradient-to-r ${config.color}\n          ${sizes.container}\n        `}\n      />\n      \n      {/* Main badge */}\n      <div className={`\n        relative flex items-center justify-center rounded-full\n        bg-gradient-to-r ${config.color}\n        ${sizes.container} ${sizes.padding}\n        shadow-lg border-2 border-white\n        group-hover:shadow-xl transition-shadow duration-200\n      `}>\n        <IconComponent className={`${sizes.icon} text-white drop-shadow-sm`} />\n      </div>\n\n      {/* Achievement details tooltip */}\n      {showDetails && (\n        <motion.div\n          initial={{ opacity: 0, y: 10, scale: 0.9 }}\n          whileHover={{ opacity: 1, y: 0, scale: 1 }}\n          className={`\n            absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2\n            ${config.bgColor} ${config.textColor}\n            px-3 py-2 rounded-lg shadow-lg border\n            whitespace-nowrap z-10\n            pointer-events-none\n            ${sizes.text}\n          `}\n        >\n          <div className=\"font-semibold\">{config.title}</div>\n          <div className=\"text-xs opacity-75\">{config.description}</div>\n          {achievement.subject && (\n            <div className=\"text-xs font-medium mt-1\">\n              Subject: {achievement.subject}\n            </div>\n          )}\n          {achievement.earnedAt && (\n            <div className=\"text-xs opacity-60 mt-1\">\n              {new Date(achievement.earnedAt).toLocaleDateString()}\n            </div>\n          )}\n          \n          {/* Tooltip arrow */}\n          <div className={`\n            absolute top-full left-1/2 transform -translate-x-1/2\n            w-0 h-0 border-l-4 border-r-4 border-t-4\n            border-l-transparent border-r-transparent\n            ${config.bgColor.replace('bg-', 'border-t-')}\n          `} />\n        </motion.div>\n      )}\n    </motion.div>\n  );\n};\n\n// Achievement list component\nexport const AchievementList = ({\n  achievements = [],\n  maxDisplay = 5,\n  size = 'medium',\n  layout = 'horizontal', // 'horizontal' or 'grid'\n  className = ''\n}) => {\n  const displayAchievements = achievements.slice(0, maxDisplay);\n  const remainingCount = Math.max(0, achievements.length - maxDisplay);\n\n  // Size configurations for the list\n  const sizeConfig = {\n    small: {\n      container: 'w-12 h-12',\n      text: 'text-xs',\n      padding: 'p-2'\n    },\n    medium: {\n      container: 'w-16 h-16',\n      text: 'text-sm',\n      padding: 'p-3'\n    },\n    large: {\n      container: 'w-20 h-20',\n      text: 'text-base',\n      padding: 'p-4'\n    }\n  };\n\n  const containerVariants = {\n    hidden: { opacity: 0 },\n    visible: {\n      opacity: 1,\n      transition: {\n        staggerChildren: 0.1\n      }\n    }\n  };\n\n  const layoutClasses = layout === 'grid'\n    ? 'grid grid-cols-3 sm:grid-cols-4 md:grid-cols-5 gap-4'\n    : 'flex flex-wrap gap-2';\n\n  return (\n    <motion.div\n      variants={containerVariants}\n      initial=\"hidden\"\n      animate=\"visible\"\n      className={`${layoutClasses} ${className}`}\n    >\n      {displayAchievements.map((achievement, index) => (\n        <AchievementBadge\n          key={`${achievement.type}-${index}`}\n          achievement={achievement}\n          size={size}\n          showDetails={true}\n        />\n      ))}\n\n      {remainingCount > 0 && (\n        <motion.div\n          variants={{\n            hidden: { opacity: 0, scale: 0.8 },\n            visible: { opacity: 1, scale: 1 }\n          }}\n          className={`\n            flex items-center justify-center rounded-full\n            bg-gray-100 border-2 border-gray-200\n            ${sizeConfig[size]?.container} ${sizeConfig[size]?.padding}\n            text-gray-600 font-semibold\n            ${sizeConfig[size]?.text}\n          `}\n        >\n          +{remainingCount}\n        </motion.div>\n      )}\n    </motion.div>\n  );\n};\n\n// Achievement notification component\nexport const AchievementNotification = ({\n  achievement,\n  onClose,\n  autoClose = true,\n  duration = 4000\n}) => {\n  // Achievement type configurations for notifications\n  const achievementConfig = {\n    first_quiz: {\n      icon: TbStar,\n      title: 'First Steps',\n      description: 'Completed your first quiz',\n      color: 'from-blue-400 to-blue-600',\n      bgColor: 'bg-blue-50',\n      textColor: 'text-blue-700'\n    },\n    perfect_score: {\n      icon: TbTrophy,\n      title: 'Perfect Score',\n      description: 'Achieved 100% on a quiz',\n      color: 'from-yellow-400 to-yellow-600',\n      bgColor: 'bg-yellow-50',\n      textColor: 'text-yellow-700'\n    },\n    streak_5: {\n      icon: TbFlame,\n      title: 'Hot Streak',\n      description: '5 correct answers in a row',\n      color: 'from-orange-400 to-red-500',\n      bgColor: 'bg-orange-50',\n      textColor: 'text-orange-700'\n    },\n    streak_10: {\n      icon: TbFlame,\n      title: 'Fire Streak',\n      description: '10 correct answers in a row',\n      color: 'from-red-400 to-red-600',\n      bgColor: 'bg-red-50',\n      textColor: 'text-red-700'\n    },\n    streak_20: {\n      icon: TbFlame,\n      title: 'Blazing Streak',\n      description: '20 correct answers in a row',\n      color: 'from-red-500 to-purple-600',\n      bgColor: 'bg-red-50',\n      textColor: 'text-red-700'\n    },\n    subject_master: {\n      icon: TbCrown,\n      title: 'Subject Master',\n      description: 'Mastered a subject',\n      color: 'from-purple-400 to-purple-600',\n      bgColor: 'bg-purple-50',\n      textColor: 'text-purple-700'\n    },\n    speed_demon: {\n      icon: TbBolt,\n      title: 'Speed Demon',\n      description: 'Completed quiz in record time',\n      color: 'from-cyan-400 to-blue-500',\n      bgColor: 'bg-cyan-50',\n      textColor: 'text-cyan-700'\n    },\n    consistent_learner: {\n      icon: TbTarget,\n      title: 'Consistent Learner',\n      description: 'Maintained consistent performance',\n      color: 'from-green-400 to-green-600',\n      bgColor: 'bg-green-50',\n      textColor: 'text-green-700'\n    },\n    improvement_star: {\n      icon: TbTrendingUp,\n      title: 'Improvement Star',\n      description: 'Showed remarkable improvement',\n      color: 'from-indigo-400 to-indigo-600',\n      bgColor: 'bg-indigo-50',\n      textColor: 'text-indigo-700'\n    }\n  };\n\n  React.useEffect(() => {\n    if (autoClose && onClose) {\n      const timer = setTimeout(onClose, duration);\n      return () => clearTimeout(timer);\n    }\n  }, [autoClose, duration, onClose]);\n\n  const config = achievementConfig[achievement.type] || achievementConfig.first_quiz;\n  const IconComponent = config.icon;\n\n  return (\n    <motion.div\n      initial={{ opacity: 0, y: -50, scale: 0.9 }}\n      animate={{ opacity: 1, y: 0, scale: 1 }}\n      exit={{ opacity: 0, y: -50, scale: 0.9 }}\n      className={`\n        fixed top-4 right-4 z-50\n        ${config.bgColor} border border-gray-200\n        rounded-lg shadow-lg p-4 max-w-sm\n      `}\n    >\n      <div className=\"flex items-center space-x-3\">\n        <div className={`\n          flex items-center justify-center w-12 h-12 rounded-full\n          bg-gradient-to-r ${config.color}\n        `}>\n          <IconComponent className=\"w-6 h-6 text-white\" />\n        </div>\n        \n        <div className=\"flex-1\">\n          <div className={`font-semibold ${config.textColor}`}>\n            Achievement Unlocked!\n          </div>\n          <div className=\"text-sm text-gray-600\">\n            {config.title}\n          </div>\n          <div className=\"text-xs text-gray-500\">\n            {config.description}\n          </div>\n        </div>\n        \n        {onClose && (\n          <button\n            onClick={onClose}\n            className=\"text-gray-400 hover:text-gray-600 transition-colors\"\n          >\n            ×\n          </button>\n        )}\n      </div>\n    </motion.div>\n  );\n};\n\nexport default AchievementBadge;\n", "import React, { useState, useEffect } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { Tb<PERSON>lam<PERSON>, TbTrophy, TbBolt } from 'react-icons/tb';\n\nconst XPProgressBar = ({\n  currentXP = 0,\n  totalXP = 0,\n  currentLevel = 1,\n  xpToNextLevel = 100,\n  showAnimation = true,\n  size = 'medium', // 'small', 'medium', 'large'\n  showLevel = true,\n  showXPNumbers = true,\n  className = ''\n}) => {\n  const [animatedXP, setAnimatedXP] = useState(0);\n  const [isLevelingUp, setIsLevelingUp] = useState(false);\n\n  // Calculate progress percentage\n  const xpForCurrentLevel = totalXP - xpToNextLevel;\n  const xpProgressInLevel = currentXP - xpForCurrentLevel;\n  const xpNeededForLevel = totalXP - xpForCurrentLevel;\n  const progressPercentage = Math.min(100, Math.max(0, (xpProgressInLevel / xpNeededForLevel) * 100));\n\n  // Size configurations\n  const sizeConfig = {\n    small: {\n      height: 'h-2',\n      levelSize: 'w-6 h-6 text-xs',\n      textSize: 'text-xs',\n      padding: 'px-2 py-1'\n    },\n    medium: {\n      height: 'h-3',\n      levelSize: 'w-8 h-8 text-sm',\n      textSize: 'text-sm',\n      padding: 'px-3 py-2'\n    },\n    large: {\n      height: 'h-4',\n      levelSize: 'w-10 h-10 text-base',\n      textSize: 'text-base',\n      padding: 'px-4 py-3'\n    }\n  };\n\n  const config = sizeConfig[size];\n\n  // Animate XP changes\n  useEffect(() => {\n    if (showAnimation) {\n      const timer = setTimeout(() => {\n        setAnimatedXP(currentXP);\n      }, 100);\n      return () => clearTimeout(timer);\n    } else {\n      setAnimatedXP(currentXP);\n    }\n  }, [currentXP, showAnimation]);\n\n  // Level up animation\n  const triggerLevelUpAnimation = () => {\n    setIsLevelingUp(true);\n    setTimeout(() => setIsLevelingUp(false), 2000);\n  };\n\n  // Get level color based on level\n  const getLevelColor = (level) => {\n    if (level >= 10) return 'from-purple-600 to-pink-600';\n    if (level >= 8) return 'from-yellow-500 to-orange-600';\n    if (level >= 6) return 'from-green-500 to-blue-600';\n    if (level >= 4) return 'from-blue-500 to-purple-600';\n    if (level >= 2) return 'from-indigo-500 to-blue-600';\n    return 'from-gray-500 to-gray-600';\n  };\n\n  // Get XP bar gradient based on progress\n  const getXPBarGradient = () => {\n    if (progressPercentage >= 90) return 'from-yellow-400 via-orange-500 to-red-500';\n    if (progressPercentage >= 70) return 'from-green-400 via-blue-500 to-purple-500';\n    if (progressPercentage >= 50) return 'from-blue-400 via-purple-500 to-pink-500';\n    if (progressPercentage >= 25) return 'from-indigo-400 via-blue-500 to-cyan-500';\n    return 'from-gray-400 via-gray-500 to-gray-600';\n  };\n\n  return (\n    <div className={`xp-progress-container ${className}`}>\n      {/* Level Up Animation Overlay */}\n      <AnimatePresence>\n        {isLevelingUp && (\n          <motion.div\n            initial={{ opacity: 0, scale: 0.5 }}\n            animate={{ opacity: 1, scale: 1 }}\n            exit={{ opacity: 0, scale: 0.5 }}\n            className=\"fixed inset-0 z-50 flex items-center justify-center bg-black/50 backdrop-blur-sm\"\n          >\n            <motion.div\n              initial={{ y: -50, opacity: 0 }}\n              animate={{ y: 0, opacity: 1 }}\n              exit={{ y: 50, opacity: 0 }}\n              className=\"bg-gradient-to-r from-yellow-400 to-orange-500 text-white px-8 py-6 rounded-2xl shadow-2xl text-center\"\n            >\n              <motion.div\n                animate={{ rotate: 360 }}\n                transition={{ duration: 1, repeat: Infinity, ease: \"linear\" }}\n                className=\"text-4xl mb-2\"\n              >\n                🎉\n              </motion.div>\n              <h2 className=\"text-2xl font-bold mb-1\">LEVEL UP!</h2>\n              <p className=\"text-lg\">You reached Level {currentLevel}!</p>\n            </motion.div>\n          </motion.div>\n        )}\n      </AnimatePresence>\n\n      <div className=\"flex items-center space-x-3\">\n        {/* Level Badge */}\n        {showLevel && (\n          <motion.div\n            whileHover={{ scale: 1.1 }}\n            whileTap={{ scale: 0.95 }}\n            className={`\n              ${config.levelSize} rounded-full flex items-center justify-center\n              bg-gradient-to-r ${getLevelColor(currentLevel)}\n              text-white font-bold shadow-lg border-2 border-white\n              relative overflow-hidden\n            `}\n          >\n            {/* Level glow effect */}\n            <div className=\"absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent animate-pulse\" />\n            \n            {/* Level number */}\n            <span className={`relative z-10 ${config.textSize}`}>\n              {currentLevel}\n            </span>\n\n            {/* Level icon for high levels */}\n            {currentLevel >= 10 && (\n              <TbTrophy className=\"absolute top-0 right-0 w-3 h-3 text-yellow-300\" />\n            )}\n          </motion.div>\n        )}\n\n        {/* XP Progress Bar Container */}\n        <div className=\"flex-1\">\n          {/* XP Numbers */}\n          {showXPNumbers && (\n            <div className={`flex justify-between items-center mb-1 ${config.textSize} text-gray-600`}>\n              <span className=\"font-medium\">\n                {animatedXP.toLocaleString()} XP\n              </span>\n              <span className=\"text-gray-500\">\n                {xpToNextLevel > 0 ? `${xpToNextLevel} to next level` : 'Max Level'}\n              </span>\n            </div>\n          )}\n\n          {/* Progress Bar */}\n          <div className={`\n            relative ${config.height} bg-gray-200 rounded-full overflow-hidden\n            shadow-inner border border-gray-300\n          `}>\n            {/* Background gradient */}\n            <div className=\"absolute inset-0 bg-gradient-to-r from-gray-100 to-gray-200\" />\n            \n            {/* Progress fill */}\n            <motion.div\n              initial={{ width: 0 }}\n              animate={{ width: `${progressPercentage}%` }}\n              transition={{ duration: 1, ease: \"easeOut\" }}\n              className={`\n                absolute inset-y-0 left-0 rounded-full\n                bg-gradient-to-r ${getXPBarGradient()}\n                shadow-lg relative overflow-hidden\n              `}\n            >\n              {/* Animated shine effect */}\n              <motion.div\n                animate={{ x: ['0%', '100%'] }}\n                transition={{ duration: 2, repeat: Infinity, ease: \"linear\" }}\n                className=\"absolute inset-0 bg-gradient-to-r from-transparent via-white/30 to-transparent transform skew-x-12\"\n              />\n              \n              {/* Progress glow */}\n              <div className=\"absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent animate-pulse\" />\n            </motion.div>\n\n            {/* XP gain animation particles */}\n            <AnimatePresence>\n              {showAnimation && (\n                <motion.div\n                  initial={{ opacity: 0, y: 0 }}\n                  animate={{ opacity: [0, 1, 0], y: -20 }}\n                  exit={{ opacity: 0 }}\n                  transition={{ duration: 1 }}\n                  className=\"absolute right-2 top-1/2 transform -translate-y-1/2\"\n                >\n                  <TbBolt className=\"w-4 h-4 text-yellow-400\" />\n                </motion.div>\n              )}\n            </AnimatePresence>\n          </div>\n\n          {/* Level progress indicators */}\n          {xpToNextLevel > 0 && (\n            <div className=\"flex justify-between mt-1\">\n              <span className={`${config.textSize} text-gray-500 font-medium`}>\n                Level {currentLevel}\n              </span>\n              <span className={`${config.textSize} text-gray-500 font-medium`}>\n                Level {currentLevel + 1}\n              </span>\n            </div>\n          )}\n        </div>\n\n        {/* XP Boost Indicator */}\n        {currentLevel > 1 && (\n          <motion.div\n            whileHover={{ scale: 1.1 }}\n            className=\"flex items-center space-x-1 bg-gradient-to-r from-yellow-100 to-orange-100 px-2 py-1 rounded-full border border-yellow-300\"\n          >\n            <TbFlame className=\"w-3 h-3 text-orange-500\" />\n            <span className=\"text-xs font-medium text-orange-700\">\n              +{((currentLevel - 1) * 10)}% XP\n            </span>\n          </motion.div>\n        )}\n      </div>\n    </div>\n  );\n};\n\nexport default XPProgressBar;\n", "import React from 'react';\nimport { motion } from 'framer-motion';\nimport { TbStar, TbCrown, TbTrophy, TbDiamond, TbFlame, TbBolt } from 'react-icons/tb';\n\nconst LevelBadge = ({\n  level = 1,\n  size = 'medium', // 'small', 'medium', 'large', 'xl'\n  showTitle = false,\n  showGlow = true,\n  animated = true,\n  className = ''\n}) => {\n  // Size configurations\n  const sizeConfig = {\n    small: {\n      container: 'w-8 h-8',\n      text: 'text-xs',\n      icon: 'w-3 h-3',\n      titleText: 'text-xs'\n    },\n    medium: {\n      container: 'w-12 h-12',\n      text: 'text-sm',\n      icon: 'w-4 h-4',\n      titleText: 'text-sm'\n    },\n    large: {\n      container: 'w-16 h-16',\n      text: 'text-lg',\n      icon: 'w-5 h-5',\n      titleText: 'text-base'\n    },\n    xl: {\n      container: 'w-20 h-20',\n      text: 'text-xl',\n      icon: 'w-6 h-6',\n      titleText: 'text-lg'\n    }\n  };\n\n  const config = sizeConfig[size];\n\n  // Level configurations with colors, shapes, and titles\n  const getLevelConfig = (level) => {\n    if (level >= 10) {\n      return {\n        title: 'Elite',\n        icon: TbCrown,\n        gradient: 'from-purple-600 via-pink-600 to-red-600',\n        glow: 'shadow-purple-500/50',\n        shape: 'diamond',\n        rarity: 'mythic',\n        animation: 'rotate'\n      };\n    } else if (level >= 8) {\n      return {\n        title: 'Legend',\n        icon: TbTrophy,\n        gradient: 'from-yellow-500 via-orange-500 to-red-500',\n        glow: 'shadow-yellow-500/50',\n        shape: 'star',\n        rarity: 'legendary',\n        animation: 'bounce'\n      };\n    } else if (level >= 6) {\n      return {\n        title: 'Master',\n        icon: TbDiamond,\n        gradient: 'from-emerald-500 via-blue-500 to-purple-500',\n        glow: 'shadow-blue-500/50',\n        shape: 'crown',\n        rarity: 'epic',\n        animation: 'pulse'\n      };\n    } else if (level >= 4) {\n      return {\n        title: 'Expert',\n        icon: TbFlame,\n        gradient: 'from-blue-500 via-purple-500 to-pink-500',\n        glow: 'shadow-blue-500/50',\n        shape: 'hexagon',\n        rarity: 'rare',\n        animation: 'glow'\n      };\n    } else if (level >= 2) {\n      return {\n        title: 'Student',\n        icon: TbBolt,\n        gradient: 'from-indigo-500 via-blue-500 to-cyan-500',\n        glow: 'shadow-indigo-500/50',\n        shape: 'circle',\n        rarity: 'uncommon',\n        animation: 'pulse'\n      };\n    } else {\n      return {\n        title: 'Beginner',\n        icon: TbStar,\n        gradient: 'from-gray-500 to-gray-600',\n        glow: 'shadow-gray-500/50',\n        shape: 'circle',\n        rarity: 'common',\n        animation: 'none'\n      };\n    }\n  };\n\n  const levelConfig = getLevelConfig(level);\n  const IconComponent = levelConfig.icon;\n\n  // Animation variants\n  const animationVariants = {\n    none: {},\n    pulse: {\n      scale: [1, 1.05, 1],\n      transition: { duration: 2, repeat: Infinity }\n    },\n    bounce: {\n      y: [0, -2, 0],\n      transition: { duration: 1.5, repeat: Infinity }\n    },\n    rotate: {\n      rotate: [0, 360],\n      transition: { duration: 3, repeat: Infinity, ease: \"linear\" }\n    },\n    glow: {\n      boxShadow: [\n        '0 0 10px rgba(59, 130, 246, 0.5)',\n        '0 0 20px rgba(59, 130, 246, 0.8)',\n        '0 0 10px rgba(59, 130, 246, 0.5)'\n      ],\n      transition: { duration: 2, repeat: Infinity }\n    }\n  };\n\n  // Shape variants\n  const getShapeClasses = (shape) => {\n    switch (shape) {\n      case 'diamond':\n        return 'transform rotate-45';\n      case 'star':\n        return 'clip-path-star';\n      case 'crown':\n        return 'clip-path-crown';\n      case 'hexagon':\n        return 'clip-path-hexagon';\n      default:\n        return 'rounded-full';\n    }\n  };\n\n  // Rarity border effects\n  const getRarityBorder = (rarity) => {\n    switch (rarity) {\n      case 'mythic':\n        return 'border-4 border-purple-400 shadow-2xl';\n      case 'legendary':\n        return 'border-3 border-yellow-400 shadow-xl';\n      case 'epic':\n        return 'border-2 border-blue-400 shadow-lg';\n      case 'rare':\n        return 'border-2 border-purple-300 shadow-md';\n      case 'uncommon':\n        return 'border border-blue-300 shadow-sm';\n      default:\n        return 'border border-gray-300';\n    }\n  };\n\n  return (\n    <div className={`level-badge-container ${className}`}>\n      {/* Main Badge */}\n      <motion.div\n        variants={animationVariants[levelConfig.animation]}\n        animate={animated ? levelConfig.animation : 'none'}\n        whileHover={{ scale: 1.1 }}\n        whileTap={{ scale: 0.95 }}\n        className={`\n          ${config.container} relative flex items-center justify-center\n          bg-gradient-to-br ${levelConfig.gradient}\n          ${getShapeClasses(levelConfig.shape)}\n          ${getRarityBorder(levelConfig.rarity)}\n          ${showGlow ? `shadow-lg ${levelConfig.glow}` : ''}\n          cursor-pointer overflow-hidden\n        `}\n      >\n        {/* Background pattern for high-level badges */}\n        {level >= 6 && (\n          <div className=\"absolute inset-0 opacity-20\">\n            <div className=\"absolute inset-0 bg-gradient-to-r from-transparent via-white to-transparent transform -skew-x-12 animate-pulse\" />\n          </div>\n        )}\n\n        {/* Level number or icon */}\n        <div className=\"relative z-10 flex items-center justify-center text-white font-bold\">\n          {level >= 8 ? (\n            <IconComponent className={`${config.icon} drop-shadow-lg`} />\n          ) : (\n            <span className={`${config.text} drop-shadow-lg`}>\n              {level}\n            </span>\n          )}\n        </div>\n\n        {/* Sparkle effects for legendary+ levels */}\n        {level >= 8 && (\n          <>\n            <motion.div\n              animate={{ \n                scale: [0, 1, 0],\n                rotate: [0, 180, 360]\n              }}\n              transition={{ \n                duration: 2, \n                repeat: Infinity,\n                delay: 0\n              }}\n              className=\"absolute top-1 right-1 w-1 h-1 bg-yellow-300 rounded-full\"\n            />\n            <motion.div\n              animate={{ \n                scale: [0, 1, 0],\n                rotate: [0, -180, -360]\n              }}\n              transition={{ \n                duration: 2, \n                repeat: Infinity,\n                delay: 0.5\n              }}\n              className=\"absolute bottom-1 left-1 w-1 h-1 bg-white rounded-full\"\n            />\n          </>\n        )}\n\n        {/* Premium glow ring for mythic levels */}\n        {level >= 10 && (\n          <div className=\"absolute -inset-1 bg-gradient-to-r from-purple-600 via-pink-600 to-purple-600 rounded-full opacity-75 blur-sm animate-pulse\" />\n        )}\n      </motion.div>\n\n      {/* Level Title */}\n      {showTitle && (\n        <motion.div\n          initial={{ opacity: 0, y: 5 }}\n          animate={{ opacity: 1, y: 0 }}\n          className=\"mt-2 text-center\"\n        >\n          <p className={`${config.titleText} font-semibold text-gray-700`}>\n            {levelConfig.title}\n          </p>\n          <p className=\"text-xs text-gray-500\">\n            Level {level}\n          </p>\n        </motion.div>\n      )}\n\n      {/* Tooltip on hover */}\n      <div className=\"absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-2 py-1 bg-gray-900 text-white text-xs rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap z-20\">\n        Level {level} - {levelConfig.title}\n        <div className=\"absolute top-full left-1/2 transform -translate-x-1/2 border-4 border-transparent border-t-gray-900\" />\n      </div>\n    </div>\n  );\n};\n\nexport default LevelBadge;\n", "import React from 'react';\nimport { motion } from 'framer-motion';\nimport { TbTrophy, TbMedal, TbStar, TbFlame, TbTarget, TbCrown, TbDiamond, TbBolt, TbUsers, TbBrain, TbRocket } from 'react-icons/tb';\n\nconst EnhancedAchievementBadge = ({\n    achievement,\n    size = 'medium', // 'small', 'medium', 'large'\n    showTooltip = true,\n    animated = true,\n    showXP = false,\n    className = ''\n}) => {\n    if (!achievement) return null;\n\n    // Size configurations\n    const sizeConfig = {\n        small: {\n            container: 'w-6 h-6',\n            icon: 'w-3 h-3',\n            text: 'text-xs'\n        },\n        medium: {\n            container: 'w-8 h-8',\n            icon: 'w-4 h-4',\n            text: 'text-sm'\n        },\n        large: {\n            container: 'w-12 h-12',\n            icon: 'w-6 h-6',\n            text: 'text-base'\n        }\n    };\n\n    const config = sizeConfig[size];\n\n    // Enhanced achievement mapping with new XP system achievements\n    const getAchievementIcon = (id) => {\n        const iconMap = {\n            // Legacy achievements\n            'first_quiz': TbTarget,\n            'perfect_score': TbDiamond,\n            'streak_5': TbFlame,\n            'streak_10': TbFlame,\n            'streak_20': TbFlame,\n            'subject_master': TbCrown,\n            'speed_demon': TbBolt,\n            'consistent_learner': TbStar,\n            'improvement_star': TbTrophy,\n            \n            // New XP system achievements\n            'first_steps': TbTarget,\n            'quick_learner': TbBolt,\n            'perfectionist': TbDiamond,\n            'on_fire': TbFlame,\n            'unstoppable': TbRocket,\n            'math_master': TbBrain,\n            'science_genius': TbBrain,\n            'top_performer': TbCrown,\n            'helping_hand': TbUsers\n        };\n        return iconMap[id] || iconMap[achievement.type] || TbMedal;\n    };\n\n    // Enhanced rarity-based colors\n    const getRarityColor = (rarity) => {\n        const rarityColors = {\n            'common': 'from-gray-400 to-gray-600',\n            'uncommon': 'from-green-400 to-green-600',\n            'rare': 'from-blue-400 to-blue-600',\n            'epic': 'from-purple-400 to-purple-600',\n            'legendary': 'from-yellow-400 to-orange-500',\n            'mythic': 'from-pink-500 to-purple-600'\n        };\n        return rarityColors[rarity] || rarityColors.common;\n    };\n\n    // Fallback to type-based colors for legacy achievements\n    const getAchievementColor = (type) => {\n        const colorMap = {\n            'first_quiz': 'from-green-400 to-green-600',\n            'perfect_score': 'from-purple-400 to-purple-600',\n            'streak_5': 'from-orange-400 to-red-500',\n            'streak_10': 'from-red-400 to-red-600',\n            'streak_20': 'from-red-500 to-pink-600',\n            'subject_master': 'from-yellow-400 to-yellow-600',\n            'speed_demon': 'from-blue-400 to-blue-600',\n            'consistent_learner': 'from-indigo-400 to-indigo-600',\n            'improvement_star': 'from-pink-400 to-pink-600'\n        };\n        return colorMap[type] || 'from-gray-400 to-gray-600';\n    };\n\n    const IconComponent = getAchievementIcon(achievement.id);\n    const colorGradient = achievement.rarity ? \n        getRarityColor(achievement.rarity) : \n        getAchievementColor(achievement.type);\n\n    // Get rarity glow effect\n    const getRarityGlow = (rarity) => {\n        const glowMap = {\n            'common': 'shadow-gray-500/50',\n            'uncommon': 'shadow-green-500/50',\n            'rare': 'shadow-blue-500/50',\n            'epic': 'shadow-purple-500/50',\n            'legendary': 'shadow-yellow-500/50',\n            'mythic': 'shadow-pink-500/50'\n        };\n        return glowMap[rarity] || glowMap.common;\n    };\n\n    // Enhanced animation based on rarity\n    const getRarityAnimation = (rarity) => {\n        switch (rarity) {\n            case 'legendary':\n            case 'mythic':\n                return {\n                    rotate: [0, 5, -5, 0],\n                    scale: [1, 1.05, 1],\n                    transition: { duration: 2, repeat: Infinity, repeatDelay: 2 }\n                };\n            case 'epic':\n                return {\n                    y: [0, -2, 0],\n                    transition: { duration: 2, repeat: Infinity, repeatDelay: 3 }\n                };\n            case 'rare':\n                return {\n                    scale: [1, 1.02, 1],\n                    transition: { duration: 3, repeat: Infinity }\n                };\n            default:\n                return {\n                    rotate: [0, 2, -2, 0],\n                    transition: { duration: 4, repeat: Infinity, repeatDelay: 5 }\n                };\n        }\n    };\n\n    return (\n        <div className={`achievement-badge relative group ${className}`}>\n            <motion.div\n                whileHover={{ scale: 1.1 }}\n                whileTap={{ scale: 0.95 }}\n                animate={animated ? getRarityAnimation(achievement.rarity) : {}}\n                className={`\n                    ${config.container} rounded-full flex items-center justify-center\n                    bg-gradient-to-br ${colorGradient}\n                    shadow-lg ${getRarityGlow(achievement.rarity)}\n                    border-2 border-white\n                    cursor-pointer relative overflow-hidden\n                `}\n            >\n                {/* Enhanced shine effect for rare+ achievements */}\n                {achievement.rarity && ['rare', 'epic', 'legendary', 'mythic'].includes(achievement.rarity) && (\n                    <motion.div\n                        animate={{\n                            x: ['-100%', '100%'],\n                            transition: { duration: 2, repeat: Infinity, repeatDelay: 4 }\n                        }}\n                        className=\"absolute inset-0 bg-gradient-to-r from-transparent via-white/40 to-transparent transform skew-x-12\"\n                    />\n                )}\n                \n                {/* Sparkle effects for legendary+ */}\n                {achievement.rarity && ['legendary', 'mythic'].includes(achievement.rarity) && (\n                    <>\n                        <motion.div\n                            animate={{ \n                                scale: [0, 1, 0],\n                                rotate: [0, 180, 360]\n                            }}\n                            transition={{ \n                                duration: 2, \n                                repeat: Infinity,\n                                delay: 0\n                            }}\n                            className=\"absolute top-0 right-0 w-1 h-1 bg-yellow-300 rounded-full\"\n                        />\n                        <motion.div\n                            animate={{ \n                                scale: [0, 1, 0],\n                                rotate: [0, -180, -360]\n                            }}\n                            transition={{ \n                                duration: 2, \n                                repeat: Infinity,\n                                delay: 1\n                            }}\n                            className=\"absolute bottom-0 left-0 w-1 h-1 bg-white rounded-full\"\n                        />\n                    </>\n                )}\n                \n                {/* Icon */}\n                <IconComponent className={`${config.icon} text-white drop-shadow-lg relative z-10`} />\n\n                {/* XP indicator for achievements with XP rewards */}\n                {showXP && achievement.xpReward && (\n                    <div className=\"absolute -top-1 -right-1 bg-yellow-500 text-white text-xs rounded-full w-4 h-4 flex items-center justify-center font-bold\">\n                        {achievement.xpReward}\n                    </div>\n                )}\n            </motion.div>\n\n            {/* Enhanced Tooltip */}\n            {showTooltip && (\n                <div className=\"absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-3 py-2 bg-gray-900 text-white text-xs rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap z-20 shadow-xl max-w-xs\">\n                    <div className=\"font-semibold\">\n                        {achievement.name || achievement.type?.replace(/_/g, ' ').replace(/\\b\\w/g, l => l.toUpperCase())}\n                    </div>\n                    {achievement.description && (\n                        <div className=\"text-gray-300 text-xs mt-1\">\n                            {achievement.description}\n                        </div>\n                    )}\n                    {achievement.rarity && (\n                        <div className={`text-xs mt-1 font-medium ${\n                            achievement.rarity === 'mythic' ? 'text-pink-300' :\n                            achievement.rarity === 'legendary' ? 'text-yellow-300' :\n                            achievement.rarity === 'epic' ? 'text-purple-300' :\n                            achievement.rarity === 'rare' ? 'text-blue-300' :\n                            achievement.rarity === 'uncommon' ? 'text-green-300' :\n                            'text-gray-300'\n                        }`}>\n                            {achievement.rarity.charAt(0).toUpperCase() + achievement.rarity.slice(1)}\n                        </div>\n                    )}\n                    {achievement.xpReward && (\n                        <div className=\"text-yellow-300 text-xs mt-1\">\n                            +{achievement.xpReward} XP\n                        </div>\n                    )}\n                    {achievement.earnedAt && (\n                        <div className=\"text-gray-400 text-xs mt-1\">\n                            Earned: {new Date(achievement.earnedAt).toLocaleDateString()}\n                        </div>\n                    )}\n                    <div className=\"absolute top-full left-1/2 transform -translate-x-1/2 border-4 border-transparent border-t-gray-900\" />\n                </div>\n            )}\n        </div>\n    );\n};\n\nexport default EnhancedAchievementBadge;\n", "import React from 'react';\nimport { Tb<PERSON>rophy, TbMedal, TbCrown, TbStar, TbFlame, TbBolt } from 'react-icons/tb';\nimport { AchievementList } from './AchievementBadge';\nimport XPProgressBar from './XPProgressBar';\nimport LevelBadge from './LevelBadge';\nimport EnhancedAchievementBadge from './EnhancedAchievementBadge';\nimport ProfilePicture from '../common/ProfilePicture';\n\nconst UserRankingCard = ({\n    user,\n    rank,\n    classRank,\n    isCurrentUser = false,\n    layout = 'horizontal', // 'horizontal' or 'vertical'\n    size = 'medium', // 'small', 'medium', 'large'\n    showStats = true,\n    className = ''\n}) => {\n    // Size configurations - Optimized profile circle sizes for better visibility\n    const sizeConfig = {\n        small: {\n            avatar: 'w-12 h-12',\n            text: 'text-sm',\n            subtext: 'text-xs',\n            padding: 'p-3',\n            spacing: 'space-x-3'\n        },\n        medium: {\n            avatar: 'w-14 h-14',\n            text: 'text-base',\n            subtext: 'text-sm',\n            padding: 'p-4',\n            spacing: 'space-x-4'\n        },\n        large: {\n            avatar: 'w-16 h-16',\n            text: 'text-lg',\n            subtext: 'text-base',\n            padding: 'p-5',\n            spacing: 'space-x-5'\n        }\n    };\n\n    const config = sizeConfig[size];\n\n    // Get subscription status styling with improved status detection\n    const getSubscriptionStyling = () => {\n        const subscriptionStatus = user?.subscriptionStatus || user?.normalizedSubscriptionStatus || 'free';\n\n        // Normalize status for better handling\n        const normalizedStatus = subscriptionStatus.toLowerCase();\n\n        if (normalizedStatus === 'active' || normalizedStatus === 'premium') {\n            return {\n                avatarClass: 'ring-4 ring-yellow-400 ring-offset-2 ring-offset-white',\n                badge: 'status-premium',\n                glow: 'shadow-xl shadow-yellow-500/30 hover:shadow-yellow-500/40',\n                statusText: 'Premium',\n                badgeIcon: '👑',\n                borderClass: 'ring-2 ring-yellow-400',\n                bgClass: 'bg-gradient-to-r from-yellow-500 via-amber-500 to-orange-500 text-white shadow-lg border border-yellow-400/50',\n                cardBg: 'bg-gradient-to-br from-yellow-50 via-amber-50 to-orange-50',\n                textColor: 'text-yellow-700',\n                borderColor: 'border-yellow-200'\n            };\n        } else if (normalizedStatus === 'free') {\n            return {\n                avatarClass: 'ring-2 ring-blue-300 ring-offset-2 ring-offset-white',\n                badge: 'status-free',\n                glow: 'shadow-md shadow-blue-500/20 hover:shadow-blue-500/30',\n                statusText: 'Free',\n                badgeIcon: '🆓',\n                borderClass: 'ring-2 ring-blue-400',\n                bgClass: 'bg-gradient-to-r from-blue-500 via-blue-600 to-indigo-600 text-white shadow-md border border-blue-400/50',\n                cardBg: 'bg-gradient-to-br from-blue-50 via-indigo-50 to-blue-50',\n                textColor: 'text-blue-700',\n                borderColor: 'border-blue-200'\n            };\n        } else {\n            return {\n                avatarClass: 'ring-2 ring-red-400 ring-offset-2 ring-offset-white opacity-75',\n                badge: 'status-expired',\n                glow: 'shadow-lg shadow-red-500/25 hover:shadow-red-500/35',\n                statusText: 'Expired',\n                badgeIcon: '⏰',\n                borderClass: 'ring-2 ring-red-400',\n                bgClass: 'bg-gradient-to-r from-red-500 via-red-600 to-red-700 text-white shadow-lg border border-red-400/50',\n                cardBg: 'bg-gradient-to-br from-red-50 via-pink-50 to-red-50',\n                textColor: 'text-red-700',\n                borderColor: 'border-red-200'\n            };\n        }\n    };\n\n    const styling = getSubscriptionStyling();\n\n    // Get rank icon and color\n    const getRankDisplay = () => {\n        const safeRank = rank || 0;\n        if (safeRank === 1) {\n            return { icon: TbCrown, color: 'text-yellow-500', bg: 'bg-yellow-50' };\n        } else if (safeRank === 2) {\n            return { icon: TbMedal, color: 'text-gray-400', bg: 'bg-gray-50' };\n        } else if (safeRank === 3) {\n            return { icon: TbTrophy, color: 'text-amber-600', bg: 'bg-amber-50' };\n        } else if (safeRank <= 10 && safeRank > 0) {\n            return { icon: TbStar, color: 'text-blue-500', bg: 'bg-blue-50' };\n        } else {\n            return { icon: null, color: 'text-gray-500', bg: 'bg-gray-50' };\n        }\n    };\n\n    const rankDisplay = getRankDisplay();\n    const RankIcon = rankDisplay.icon;\n\n\n\n    // Avatar wrapper with subscription styling (removed unused component)\n\n    if (layout === 'vertical') {\n        return (\n            <div\n                className={`\n                    ranking-card flex flex-col items-center text-center ${config.padding}\n                    ${styling.cardBg || 'bg-white'} rounded-xl border ${styling.borderColor || 'border-gray-200'}\n                    ${styling.glow} transition-all duration-300 hover:scale-105 hover:shadow-xl\n                    transform hover:-translate-y-1 animate-fadeInUp\n                    ${isCurrentUser ? 'current-user-card ring-2 ring-blue-500 ring-offset-2' : ''}\n                    ${className}\n                `}\n            >\n                {/* Rank Badges */}\n                <div className=\"flex items-center space-x-2 mb-3\">\n                    {/* Overall Rank */}\n                    <div className={`\n                        rank-badge flex items-center justify-center w-8 h-8 rounded-full\n                        ${rankDisplay.bg} ${rankDisplay.color}\n                    `}>\n                        {RankIcon ? (\n                            <RankIcon className=\"w-4 h-4\" />\n                        ) : (\n                            <span className=\"text-xs font-bold\">#{rank || '?'}</span>\n                        )}\n                    </div>\n\n                    {/* Class Rank */}\n                    {classRank && (\n                        <div className=\"flex items-center justify-center w-8 h-8 rounded-full bg-blue-100 text-blue-700\">\n                            <span className=\"text-xs font-bold\">C{classRank}</span>\n                        </div>\n                    )}\n                </div>\n\n                {/* Avatar with Online Status */}\n                <div className=\"mb-3\">\n                    <ProfilePicture\n                        user={user}\n                        size=\"md\"\n                        showOnlineStatus={true}\n                        className=\"hover:scale-105 transition-transform duration-200\"\n                        style={{\n                            width: config.avatar.includes('w-12') ? '48px' :\n                                   config.avatar.includes('w-10') ? '40px' : '32px',\n                            height: config.avatar.includes('w-12') ? '48px' :\n                                    config.avatar.includes('w-10') ? '40px' : '32px'\n                        }}\n                    />\n                </div>\n\n                {/* User Info */}\n                <div className=\"space-y-1\">\n                    <h3 className={`font-semibold ${config.text} text-gray-900 truncate max-w-24`}>\n                        {user?.name || 'Unknown User'}\n                    </h3>\n\n                    {/* XP and Level Info */}\n                    <div className=\"flex items-center space-x-2\">\n                        <LevelBadge\n                            level={user?.currentLevel || 1}\n                            size=\"small\"\n                            showTitle={false}\n                            animated={true}\n                        />\n                        <div className=\"flex flex-col\">\n                            <p className={`${config.subtext} text-blue-600 font-medium`}>\n                                {user?.totalXP || 0} XP\n                            </p>\n                            {user?.xpToNextLevel > 0 && (\n                                <p className={`text-xs text-gray-400`}>\n                                    {user.xpToNextLevel} to next\n                                </p>\n                            )}\n                        </div>\n                    </div>\n\n                    {/* Legacy Points (smaller) */}\n                    <p className={`text-xs text-gray-400`}>\n                        {user?.totalPoints || 0} pts (legacy)\n                    </p>\n\n                    {/* Enhanced Stats */}\n                    {user?.averageScore && (\n                        <p className={`${config.subtext} text-gray-500`}>\n                            Avg: {user.averageScore}%\n                        </p>\n                    )}\n\n                    {user?.currentStreak > 0 && (\n                        <div className=\"flex items-center space-x-1\">\n                            <TbFlame className=\"w-3 h-3 text-orange-500\" />\n                            <span className={`${config.subtext} text-orange-600 font-medium`}>\n                                {user.currentStreak}\n                            </span>\n                        </div>\n                    )}\n\n                    {/* Enhanced Achievements */}\n                    {user?.achievements && user.achievements.length > 0 && (\n                        <div className=\"flex items-center space-x-1\">\n                            {user.achievements.slice(0, 3).map((achievement, index) => (\n                                <EnhancedAchievementBadge\n                                    key={achievement.id || achievement.type || index}\n                                    achievement={achievement}\n                                    size=\"small\"\n                                    showTooltip={true}\n                                    animated={true}\n                                    showXP={false}\n                                />\n                            ))}\n                            {user.achievements.length > 3 && (\n                                <span className=\"text-xs text-gray-500 ml-1\">\n                                    +{user.achievements.length - 3}\n                                </span>\n                            )}\n                        </div>\n                    )}\n\n                    {/* Modern Subscription Badge */}\n                    <div className={`\n                        inline-flex items-center space-x-1 px-3 py-1.5 rounded-full text-xs font-semibold\n                        ${styling.bgClass} transform hover:scale-105 transition-all duration-200\n                        backdrop-blur-sm\n                    `}>\n                        <span className=\"text-sm\">{styling.badgeIcon}</span>\n                        <span>{styling.statusText}</span>\n                    </div>\n                </div>\n            </div>\n        );\n    }\n\n    // Horizontal layout (default)\n    return (\n        <div\n            className={`\n                ranking-card flex items-center ${config.spacing} ${config.padding}\n                bg-white rounded-xl border border-gray-200 hover:scale-105 transition-all duration-300\n                hover:shadow-xl transform hover:-translate-y-1 animate-fadeInUp\n                ${isCurrentUser ? 'current-user-card ring-2 ring-blue-500 ring-offset-2' : ''}\n                ${className}\n            `}\n        >\n            {/* Rank Badges */}\n            <div className=\"flex items-center space-x-2 flex-shrink-0\">\n                {/* Overall Rank */}\n                <div className={`\n                    rank-badge flex items-center justify-center w-10 h-10 rounded-full\n                    ${rankDisplay.bg} ${rankDisplay.color}\n                `}>\n                    {RankIcon ? (\n                        <RankIcon className=\"w-5 h-5\" />\n                    ) : (\n                        <span className=\"text-sm font-bold\">#{rank || '?'}</span>\n                    )}\n                </div>\n\n                {/* Class Rank */}\n                {classRank && (\n                    <div className=\"flex items-center justify-center w-10 h-10 rounded-full bg-blue-100 text-blue-700\">\n                        <span className=\"text-sm font-bold\">C{classRank}</span>\n                    </div>\n                )}\n            </div>\n\n            {/* Avatar with Online Status */}\n            <div className=\"flex-shrink-0\">\n                <ProfilePicture\n                    user={user}\n                    size={size === 'small' ? 'sm' : size === 'large' ? 'lg' : 'md'}\n                    showOnlineStatus={true}\n                    className=\"hover:scale-105 transition-transform duration-200\"\n                />\n            </div>\n\n            {/* User Info */}\n            <div className=\"flex-1 min-w-0\">\n                <div className=\"flex items-center space-x-2 mb-1\">\n                    <h3 className={`font-semibold ${config.text} text-gray-900 truncate`}>\n                        {user?.name || 'Unknown User'}\n                    </h3>\n                    <LevelBadge\n                        level={user?.currentLevel || 1}\n                        size=\"small\"\n                        showTitle={false}\n                        animated={true}\n                    />\n                    <span className={`\n                        px-2 py-1 rounded-full text-xs font-medium flex-shrink-0\n                        ${styling.bgClass}\n                    `}>\n                        {styling.statusText}\n                    </span>\n                </div>\n                \n                {showStats && (\n                    <div className=\"flex items-center space-x-4\">\n                        {/* XP Display */}\n                        <div className=\"flex items-center space-x-1\">\n                            <TbBolt className=\"w-3 h-3 text-blue-500\" />\n                            <span className={`${config.subtext} text-blue-600 font-medium`}>\n                                {user?.totalXP || 0} XP\n                            </span>\n                        </div>\n\n                        {/* Legacy Points (smaller) */}\n                        <span className={`text-xs text-gray-400`}>\n                            {user?.totalPoints || 0} pts\n                        </span>\n\n                        {user?.passedExamsCount !== undefined && (\n                            <span className={`${config.subtext} text-green-600`}>\n                                {user.passedExamsCount} passed\n                            </span>\n                        )}\n                        {user?.quizzesTaken !== undefined && (\n                            <span className={`${config.subtext} text-blue-600`}>\n                                {user.quizzesTaken} quizzes\n                            </span>\n                        )}\n                        {user?.averageScore && (\n                            <span className={`${config.subtext} text-gray-600`}>\n                                {user.averageScore}% avg\n                            </span>\n                        )}\n                        {user?.currentStreak > 0 && (\n                            <div className=\"flex items-center space-x-1\">\n                                <TbFlame className=\"w-3 h-3 text-orange-500\" />\n                                <span className={`${config.subtext} text-orange-600 font-medium`}>\n                                    {user.currentStreak}\n                                </span>\n                            </div>\n                        )}\n                    </div>\n                )}\n\n                {/* XP Progress Bar */}\n                {user?.xpToNextLevel > 0 && (\n                    <div className=\"mt-2\">\n                        <XPProgressBar\n                            currentXP={user.totalXP || 0}\n                            totalXP={(user.totalXP || 0) + (user.xpToNextLevel || 0)}\n                            currentLevel={user.currentLevel || 1}\n                            xpToNextLevel={user.xpToNextLevel || 0}\n                            size=\"small\"\n                            showLevel={false}\n                            showXPNumbers={false}\n                            showAnimation={false}\n                        />\n                    </div>\n                )}\n\n                {/* Achievements for horizontal layout */}\n                {user?.achievements && user.achievements.length > 0 && (\n                    <div className=\"mt-2\">\n                        <AchievementList\n                            achievements={user.achievements}\n                            maxDisplay={5}\n                            size=\"small\"\n                            layout=\"horizontal\"\n                        />\n                    </div>\n                )}\n            </div>\n\n            {/* Score and Subscription Badge */}\n            <div className=\"text-right flex-shrink-0 space-y-2\">\n                <div>\n                    <div className={`font-bold ${config.text} ${isCurrentUser ? 'text-blue-600' : 'text-gray-900'}`}>\n                        {(user.rankingScore || user.score || user.totalXP || user.totalPoints || 0).toLocaleString()}\n                    </div>\n                    <div className={`${config.subtext} text-gray-500`}>\n                        {user.rankingScore ? 'ranking pts' : user.totalXP ? 'XP' : 'points'}\n                    </div>\n                    {user.breakdown && (\n                        <div className=\"text-xs text-gray-400 mt-1\">\n                            XP: {(user.totalXP || 0).toLocaleString()}\n                        </div>\n                    )}\n                </div>\n\n                {/* Modern Subscription Badge for Horizontal Layout */}\n                <div className={`\n                    inline-flex items-center space-x-1 px-2 py-1 rounded-full text-xs font-semibold flex-shrink-0\n                    ${styling.bgClass} transform hover:scale-105 transition-all duration-200\n                    backdrop-blur-sm\n                `}>\n                    <span className=\"text-xs\">{styling.badgeIcon}</span>\n                    <span>{styling.statusText}</span>\n                </div>\n            </div>\n        </div>\n    );\n};\n\nexport default UserRankingCard;\n", "import React, { useState, useRef } from 'react';\nimport { TbUser, TbUsers, Tb<PERSON>rophy, TbPlayerPlay, TbPlayerPause, Tb<PERSON>lock, TbSearch, TbFilter } from 'react-icons/tb';\nimport UserRankingCard from './UserRankingCard';\n\nconst UserRankingList = ({\n    users = [],\n    currentUserId = null,\n    layout = 'horizontal', // 'horizontal', 'vertical', 'grid'\n    size = 'medium',\n    showStats = true,\n    className = '',\n    currentUserRef = null,\n    showFindMe = false,\n    lastUpdated = null,\n    autoRefresh = false,\n    onAutoRefreshToggle = null\n}) => {\n    // State for search and filtering\n    const [searchTerm, setSearchTerm] = useState('');\n    const [filterType, setFilterType] = useState('all'); // 'all', 'premium', 'free', 'expired'\n    const [sortBy, setSortBy] = useState('rank'); // 'rank', 'xp', 'name'\n    const [viewMode, setViewMode] = useState('card'); // 'card', 'compact', 'detailed'\n    const [showOnlyMyClass, setShowOnlyMyClass] = useState(false);\n    const [localShowFindMe, setLocalShowFindMe] = useState(false);\n    const localCurrentUserRef = useRef(null);\n\n    // Use passed refs or local ones\n    const userRef = currentUserRef || localCurrentUserRef;\n    const findMeActive = showFindMe || localShowFindMe;\n\n    // Get current user's class and level for filtering\n    const currentUser = users.find(user => user.userId === currentUserId || user._id === currentUserId);\n    const currentUserClass = currentUser?.class;\n    const currentUserLevel = currentUser?.level;\n\n    // Filter and search users\n    const filteredUsers = users.filter(user => {\n        // Search filter\n        const matchesSearch = user.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||\n                            user.email?.toLowerCase().includes(searchTerm.toLowerCase()) ||\n                            user.class?.toLowerCase().includes(searchTerm.toLowerCase());\n\n        // Class filter\n        const matchesClass = !showOnlyMyClass || user.class === currentUserClass;\n\n        // Subscription filter\n        const userStatus = user.normalizedSubscriptionStatus?.toLowerCase() || user.subscriptionStatus?.toLowerCase() || 'free';\n        let matchesFilter = true;\n\n        switch (filterType) {\n            case 'premium':\n                matchesFilter = userStatus === 'premium' || userStatus === 'active';\n                break;\n            case 'expired':\n                matchesFilter = userStatus === 'expired';\n                break;\n            case 'free':\n                matchesFilter = userStatus === 'free';\n                break;\n            default:\n                matchesFilter = true;\n        }\n\n        return matchesSearch && matchesFilter && matchesClass;\n    }).sort((a, b) => {\n        switch (sortBy) {\n            case 'xp':\n                return (b.totalXP || 0) - (a.totalXP || 0);\n            case 'name':\n                return (a.name || '').localeCompare(b.name || '');\n            case 'score':\n                return (b.rankingScore || b.score || 0) - (a.rankingScore || a.score || 0);\n            case 'class':\n                return (a.class || '').localeCompare(b.class || '');\n            default:\n                return (a.rank || 0) - (b.rank || 0);\n        }\n    });\n\n    // Calculate class ranks for filtered users\n    const usersWithClassRank = filteredUsers.map(user => {\n        // Group users by class and calculate class rank\n        const sameClassUsers = filteredUsers.filter(u => u.class === user.class);\n        const classRank = sameClassUsers.findIndex(u => u._id === user._id || u.userId === user.userId) + 1;\n        return { ...user, classRank };\n    });\n\n    // Scroll to current user\n    const scrollToCurrentUser = () => {\n        if (userRef.current) {\n            userRef.current.scrollIntoView({\n                behavior: 'smooth',\n                block: 'center'\n            });\n            setLocalShowFindMe(true);\n            // Hide the highlight after 3 seconds\n            setTimeout(() => setLocalShowFindMe(false), 3000);\n        }\n    };\n\n    // Get layout classes based on view mode and layout\n    const getLayoutClasses = () => {\n        if (viewMode === 'compact') {\n            return 'space-y-2';\n        }\n\n        switch (layout) {\n            case 'vertical':\n                return 'grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-4';\n            case 'grid':\n                return 'grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6';\n            case 'horizontal':\n            default:\n                return 'space-y-4';\n        }\n    };\n\n\n\n    // Stats summary with enhanced calculations\n    const totalUsers = users.length;\n    const premiumUsers = users.filter(u =>\n        u.subscriptionStatus === 'active' ||\n        u.subscriptionStatus === 'premium' ||\n        u.normalizedSubscriptionStatus === 'premium'\n    ).length;\n\n    // Use ranking score or XP as the primary metric\n    const topScore = users.length > 0 ? Math.max(...users.map(u =>\n        u.rankingScore || u.totalXP || u.totalPoints || 0\n    )) : 0;\n\n    // Calculate additional stats\n    const activeUsers = users.filter(u => (u.totalQuizzesTaken || 0) > 0).length;\n    const averageXP = users.length > 0 ?\n        Math.round(users.reduce((sum, u) => sum + (u.totalXP || 0), 0) / users.length) : 0;\n\n    return (\n        <div className={`space-y-6 ${className}`}>\n            {/* Header with Stats */}\n            {showStats && (\n                <div className=\"bg-gradient-to-r from-blue-50 to-purple-50 rounded-xl p-6 border border-blue-200 animate-fadeInUp\">\n                    {/* Enhanced Search and Filter Section */}\n                    <div className=\"mb-6 bg-white/90 backdrop-blur-sm rounded-xl p-6 border border-white/50 shadow-lg\">\n                        <div className=\"flex flex-col gap-4\">\n                            {/* Top Row - Search and Quick Filters */}\n                            <div className=\"flex flex-col sm:flex-row gap-4\">\n                                {/* Search Input */}\n                                <div className=\"flex-1 relative\">\n                                    <TbSearch className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5\" />\n                                    <input\n                                        id=\"ranking-search\"\n                                        name=\"ranking-search\"\n                                        type=\"text\"\n                                        placeholder=\"Search by name, email, or class...\"\n                                        value={searchTerm}\n                                        onChange={(e) => setSearchTerm(e.target.value)}\n                                        autoComplete=\"off\"\n                                        className=\"w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 bg-white shadow-sm\"\n                                    />\n                                    {searchTerm && (\n                                        <button\n                                            onClick={() => setSearchTerm('')}\n                                            className=\"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600\"\n                                        >\n                                            ✕\n                                        </button>\n                                    )}\n                                </div>\n\n                                {/* Quick Filter Buttons */}\n                                <div className=\"flex gap-2\">\n                                    <button\n                                        onClick={() => setShowOnlyMyClass(!showOnlyMyClass)}\n                                        className={`px-4 py-2 rounded-lg font-medium transition-all duration-200 ${\n                                            showOnlyMyClass\n                                                ? 'bg-blue-500 text-white shadow-md'\n                                                : 'bg-gray-100 text-gray-700 hover:bg-gray-200'\n                                        }`}\n                                    >\n                                        My Class Only\n                                    </button>\n                                </div>\n                            </div>\n\n                            {/* Bottom Row - Dropdowns and View Mode */}\n                            <div className=\"flex flex-col sm:flex-row gap-4\">\n                                {/* Filter Dropdown */}\n                                <div className=\"relative\">\n                                    <TbFilter className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5\" />\n                                    <select\n                                        id=\"ranking-filter\"\n                                        name=\"ranking-filter\"\n                                        value={filterType}\n                                        onChange={(e) => setFilterType(e.target.value)}\n                                        autoComplete=\"off\"\n                                        className=\"pl-10 pr-8 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 bg-white shadow-sm\"\n                                    >\n                                        <option value=\"all\">All Subscriptions</option>\n                                        <option value=\"premium\">👑 Premium Users</option>\n                                        <option value=\"free\">🆓 Free Users</option>\n                                        <option value=\"expired\">⏰ Expired Users</option>\n                                    </select>\n                                </div>\n\n                                {/* Sort Dropdown */}\n                                <select\n                                    id=\"ranking-sort\"\n                                    name=\"ranking-sort\"\n                                    value={sortBy}\n                                    onChange={(e) => setSortBy(e.target.value)}\n                                    autoComplete=\"off\"\n                                    className=\"px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 bg-white shadow-sm\"\n                                >\n                                    <option value=\"rank\">🏆 Sort by Rank</option>\n                                    <option value=\"xp\">⚡ Sort by XP</option>\n                                    <option value=\"score\">📊 Sort by Score</option>\n                                    <option value=\"name\">📝 Sort by Name</option>\n                                    <option value=\"class\">🎓 Sort by Class</option>\n                                </select>\n\n                                {/* View Mode Toggle */}\n                                <div className=\"flex bg-gray-100 rounded-lg p-1\">\n                                    <button\n                                        onClick={() => setViewMode('card')}\n                                        className={`px-3 py-2 rounded-md text-sm font-medium transition-all duration-200 ${\n                                            viewMode === 'card'\n                                                ? 'bg-white text-blue-600 shadow-sm'\n                                                : 'text-gray-600 hover:text-gray-800'\n                                        }`}\n                                    >\n                                        Cards\n                                    </button>\n                                    <button\n                                        onClick={() => setViewMode('compact')}\n                                        className={`px-3 py-2 rounded-md text-sm font-medium transition-all duration-200 ${\n                                            viewMode === 'compact'\n                                                ? 'bg-white text-blue-600 shadow-sm'\n                                                : 'text-gray-600 hover:text-gray-800'\n                                        }`}\n                                    >\n                                        Compact\n                                    </button>\n                                </div>\n                            </div>\n                        </div>\n\n                        {/* Enhanced Results Count and Stats */}\n                        <div className=\"mt-4 flex flex-col sm:flex-row justify-between items-start sm:items-center gap-2\">\n                            <div className=\"text-sm text-gray-600\">\n                                <span className=\"font-semibold text-gray-800\">\n                                    {usersWithClassRank.length}\n                                </span> of <span className=\"font-semibold text-gray-800\">{users.length}</span> users\n                                {searchTerm && (\n                                    <span className=\"ml-1\">\n                                        matching <span className=\"font-medium text-blue-600\">\"{searchTerm}\"</span>\n                                    </span>\n                                )}\n                                {filterType !== 'all' && (\n                                    <span className=\"ml-1 px-2 py-1 bg-blue-100 text-blue-700 rounded-full text-xs font-medium\">\n                                        {filterType}\n                                    </span>\n                                )}\n                                {showOnlyMyClass && currentUserClass && (\n                                    <span className=\"ml-1 px-2 py-1 bg-green-100 text-green-700 rounded-full text-xs font-medium\">\n                                        {currentUserLevel === 'primary' ? `Class ${currentUserClass}` : currentUserClass}\n                                    </span>\n                                )}\n                            </div>\n\n                            {/* Quick Stats */}\n                            <div className=\"flex gap-4 text-xs text-gray-500\">\n                                <span>🏆 Top: {Math.max(...usersWithClassRank.map(u => u.rankingScore || u.totalXP || 0)).toLocaleString()}</span>\n                                <span>📊 Avg: {Math.round(usersWithClassRank.reduce((sum, u) => sum + (u.rankingScore || u.totalXP || 0), 0) / usersWithClassRank.length || 0).toLocaleString()}</span>\n                            </div>\n                        </div>\n                    </div>\n                    <div className=\"flex items-center justify-between mb-6\">\n                        <div className=\"flex items-center space-x-4\">\n                            <div className=\"flex items-center space-x-3\">\n                                <div className=\"p-3 bg-gradient-to-br from-yellow-400 via-yellow-500 to-orange-500 rounded-xl shadow-lg\">\n                                    <TbTrophy className=\"w-7 h-7 text-white\" />\n                                </div>\n                                <div>\n                                    <h2 className=\"text-3xl font-black text-gray-900 bg-gradient-to-r from-gray-900 via-blue-900 to-purple-900 bg-clip-text text-transparent\">\n                                        Leaderboard\n                                    </h2>\n                                    <p className=\"text-sm text-gray-600 font-medium\">Top performers across all levels</p>\n                                </div>\n                            </div>\n\n                            {lastUpdated && (\n                                <div className=\"flex items-center space-x-2 px-3 py-2 bg-blue-50 rounded-lg border border-blue-200\">\n                                    <TbClock className=\"w-4 h-4 text-blue-600\" />\n                                    <span className=\"text-sm text-blue-700 font-medium\">\n                                        Updated {new Date(lastUpdated).toLocaleTimeString()}\n                                    </span>\n                                </div>\n                            )}\n                        </div>\n\n                        <div className=\"flex items-center space-x-2\">\n                            {/* Auto-refresh toggle */}\n                            {onAutoRefreshToggle && (\n                                <button\n                                    onClick={onAutoRefreshToggle}\n                                    className={`px-3 py-2 rounded-lg font-medium transition-all duration-200 flex items-center space-x-2 hover:scale-105 active:scale-95 ${\n                                        autoRefresh\n                                            ? 'bg-green-500 hover:bg-green-600 text-white'\n                                            : 'bg-gray-200 hover:bg-gray-300 text-gray-700'\n                                    }`}\n                                >\n                                    {autoRefresh ? <TbPlayerPause className=\"w-4 h-4\" /> : <TbPlayerPlay className=\"w-4 h-4\" />}\n                                    <span className=\"hidden sm:inline\">\n                                        {autoRefresh ? 'Auto' : 'Manual'}\n                                    </span>\n                                </button>\n                            )}\n\n\n\n                            {/* Find Me button */}\n                            {currentUserId && (\n                                <button\n                                    onClick={scrollToCurrentUser}\n                                    className=\"bg-purple-500 hover:bg-purple-600 text-white px-3 py-2 rounded-lg font-medium transition-all duration-200 flex items-center space-x-2 hover:scale-105 active:scale-95\"\n                                >\n                                    <TbUser className=\"w-4 h-4\" />\n                                    <span className=\"hidden sm:inline\">Find Me</span>\n                                </button>\n                            )}\n                        </div>\n                    </div>\n\n                    <div className=\"grid grid-cols-2 sm:grid-cols-4 gap-4\">\n                        <div className=\"bg-gradient-to-br from-blue-50 to-indigo-50 rounded-xl p-5 border border-blue-200 shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105\">\n                            <div className=\"flex items-center space-x-3 mb-3\">\n                                <div className=\"p-2 bg-blue-500 rounded-lg\">\n                                    <TbUsers className=\"w-5 h-5 text-white\" />\n                                </div>\n                                <span className=\"text-sm font-semibold text-blue-700\">Total Users</span>\n                            </div>\n                            <div className=\"text-3xl font-black text-blue-900 mb-1\">{totalUsers}</div>\n                            <div className=\"text-xs text-blue-600 font-medium\">{activeUsers} active</div>\n                        </div>\n\n                        <div className=\"bg-gradient-to-br from-yellow-50 to-orange-50 rounded-xl p-5 border border-yellow-200 shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105\">\n                            <div className=\"flex items-center space-x-3 mb-3\">\n                                <div className=\"p-2 bg-gradient-to-r from-yellow-500 to-orange-500 rounded-lg\">\n                                    <TbTrophy className=\"w-5 h-5 text-white\" />\n                                </div>\n                                <span className=\"text-sm font-semibold text-yellow-700\">Premium Users</span>\n                            </div>\n                            <div className=\"text-3xl font-black text-yellow-900 mb-1\">{premiumUsers}</div>\n                            <div className=\"text-xs text-yellow-600 font-medium\">\n                                {totalUsers > 0 ? Math.round((premiumUsers / totalUsers) * 100) : 0}% premium\n                            </div>\n                        </div>\n\n                        <div className=\"bg-gradient-to-br from-green-50 to-emerald-50 rounded-xl p-5 border border-green-200 shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105\">\n                            <div className=\"flex items-center space-x-3 mb-3\">\n                                <div className=\"p-2 bg-green-500 rounded-lg\">\n                                    <TbUser className=\"w-5 h-5 text-white\" />\n                                </div>\n                                <span className=\"text-sm font-semibold text-green-700\">Top Score</span>\n                            </div>\n                            <div className=\"text-3xl font-black text-green-900 mb-1\">{topScore.toLocaleString()}</div>\n                            <div className=\"text-xs text-green-600 font-medium\">ranking points</div>\n                        </div>\n\n                        <div className=\"bg-gradient-to-br from-purple-50 to-pink-50 rounded-xl p-5 border border-purple-200 shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105\">\n                            <div className=\"flex items-center space-x-3 mb-3\">\n                                <div className=\"p-2 bg-purple-500 rounded-lg\">\n                                    <TbTrophy className=\"w-5 h-5 text-white\" />\n                                </div>\n                                <span className=\"text-sm font-semibold text-purple-700\">Avg XP</span>\n                            </div>\n                            <div className=\"text-3xl font-black text-purple-900 mb-1\">{averageXP.toLocaleString()}</div>\n                            <div className=\"text-xs text-gray-500 mt-1\">experience points</div>\n                        </div>\n                    </div>\n                </div>\n            )}\n\n            {/* User List */}\n            <div className={`animate-fadeInUp ${getLayoutClasses()}`}>\n                {usersWithClassRank.map((user, index) => {\n                    const isCurrentUser = user.userId === currentUserId || user._id === currentUserId;\n                    const rank = user.rank || index + 1;\n\n                    // Render compact view for better performance with large lists\n                    if (viewMode === 'compact') {\n                        return (\n                            <div\n                                key={user.userId || user._id}\n                                ref={isCurrentUser ? userRef : null}\n                                className={`animate-slideInLeft transition-all duration-200 p-3 rounded-lg border ${\n                                    isCurrentUser && findMeActive\n                                        ? 'find-me-highlight ring-2 ring-yellow-400 bg-gradient-to-r from-yellow-50 to-orange-50 border-yellow-200'\n                                        : isCurrentUser\n                                        ? 'ring-2 ring-blue-400 bg-blue-50 border-blue-200'\n                                        : 'bg-white border-gray-200 hover:border-gray-300 hover:shadow-md'\n                                }`}\n                            >\n                                <div className=\"flex items-center justify-between\">\n                                    <div className=\"flex items-center space-x-3\">\n                                        {/* Rank Badge */}\n                                        <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-bold ${\n                                            rank <= 3 ? 'bg-gradient-to-r from-yellow-400 to-orange-500 text-white' :\n                                            rank <= 10 ? 'bg-gradient-to-r from-blue-400 to-blue-500 text-white' :\n                                            'bg-gray-100 text-gray-600'\n                                        }`}>\n                                            {rank}\n                                        </div>\n\n                                        {/* User Info */}\n                                        <div className=\"flex items-center space-x-2\">\n                                            <img\n                                                src={user.profilePicture || '/default-avatar.png'}\n                                                alt={user.name}\n                                                className=\"w-8 h-8 rounded-full object-cover border-2 border-gray-200\"\n                                            />\n                                            <div>\n                                                <div className=\"font-semibold text-gray-900 text-sm\">{user.name}</div>\n                                                <div className=\"text-xs text-gray-500\">\n                                                    {user.level === 'primary' ? `Class ${user.class}` : user.class}\n                                                </div>\n                                            </div>\n                                        </div>\n                                    </div>\n\n                                    {/* Score and Badge */}\n                                    <div className=\"flex items-center space-x-3\">\n                                        <div className=\"text-right\">\n                                            <div className=\"font-bold text-gray-900 text-sm\">\n                                                {(user.rankingScore || user.totalXP || 0).toLocaleString()}\n                                            </div>\n                                            <div className=\"text-xs text-gray-500\">\n                                                {user.rankingScore ? 'pts' : 'XP'}\n                                            </div>\n                                        </div>\n\n                                        {/* Subscription Badge */}\n                                        <div className={`px-2 py-1 rounded-full text-xs font-medium ${\n                                            (user.normalizedSubscriptionStatus || user.subscriptionStatus)?.toLowerCase() === 'premium' ||\n                                            (user.normalizedSubscriptionStatus || user.subscriptionStatus)?.toLowerCase() === 'active'\n                                                ? 'bg-yellow-100 text-yellow-800'\n                                                : (user.normalizedSubscriptionStatus || user.subscriptionStatus)?.toLowerCase() === 'expired'\n                                                ? 'bg-red-100 text-red-800'\n                                                : 'bg-blue-100 text-blue-800'\n                                        }`}>\n                                            {(user.normalizedSubscriptionStatus || user.subscriptionStatus)?.toLowerCase() === 'premium' ||\n                                             (user.normalizedSubscriptionStatus || user.subscriptionStatus)?.toLowerCase() === 'active' ? '👑' :\n                                             (user.normalizedSubscriptionStatus || user.subscriptionStatus)?.toLowerCase() === 'expired' ? '⏰' : '🆓'}\n                                        </div>\n                                    </div>\n                                </div>\n                            </div>\n                        );\n                    }\n\n                    // Regular card view\n                    return (\n                        <div\n                            key={user.userId || user._id}\n                            ref={isCurrentUser ? userRef : null}\n                            className={`animate-slideInLeft transition-all duration-300 ${\n                                isCurrentUser && findMeActive\n                                    ? 'find-me-highlight ring-4 ring-yellow-400 bg-gradient-to-r from-yellow-50 to-orange-50 rounded-xl shadow-2xl'\n                                    : isCurrentUser\n                                    ? 'ring-2 ring-blue-400 bg-blue-50/50 rounded-lg'\n                                        : ''\n                                }`}\n                            >\n                                <UserRankingCard\n                                    user={user}\n                                    rank={rank}\n                                    classRank={user.classRank}\n                                    isCurrentUser={isCurrentUser}\n                                    layout={layout}\n                                    size={size}\n                                    showStats={showStats}\n                                />\n                            </div>\n                        );\n                    })}\n            </div>\n\n            {/* Empty State */}\n            {usersWithClassRank.length === 0 && (\n                <div className=\"text-center py-12 bg-white rounded-xl border border-gray-200 animate-fadeInUp\">\n                    <TbUsers className=\"w-16 h-16 text-gray-400 mx-auto mb-4\" />\n                    <h3 className=\"text-lg font-semibold text-gray-900 mb-2\">No users found</h3>\n                    <p className=\"text-gray-500\">\n                        {users.length === 0 ? 'No ranking data available.' : 'Try adjusting your search or filter criteria.'}\n                    </p>\n                </div>\n            )}\n\n            {/* Floating Action Button for Current User */}\n            {currentUserId && usersWithClassRank.length > 10 && (\n                <button\n                    onClick={scrollToCurrentUser}\n                    className=\"fixed bottom-6 right-6 bg-blue-500 hover:bg-blue-600 text-white p-3 rounded-full shadow-lg hover:shadow-xl transition-all duration-200 z-50 hover:scale-110 active:scale-95 animate-bounce\"\n                    title=\"Find me in ranking\"\n                >\n                    <TbUser className=\"w-6 h-6\" />\n                </button>\n            )}\n        </div>\n    );\n};\n\nexport default UserRankingList;\n", "import React, { useState } from 'react';\nimport { motion } from 'framer-motion';\nimport UserRankingList from './UserRankingList';\nimport UserRankingCard from './UserRankingCard';\n\nconst RankingDemo = () => {\n    const [layout, setLayout] = useState('horizontal');\n    const [size, setSize] = useState('medium');\n\n    // Sample user data\n    const sampleUsers = [\n        {\n            userId: '1',\n            name: '<PERSON>',\n            profilePicture: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face',\n            subscriptionStatus: 'active',\n            totalPoints: 2850,\n            passedExamsCount: 15,\n            quizzesTaken: 23,\n            score: 2850,\n            rank: 1\n        },\n        {\n            userId: '2',\n            name: '<PERSON>',\n            profilePicture: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face',\n            subscriptionStatus: 'free',\n            totalPoints: 2720,\n            passedExamsCount: 12,\n            quizzesTaken: 20,\n            score: 2720,\n            rank: 2\n        },\n        {\n            userId: '3',\n            name: '<PERSON>',\n            profilePicture: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150&h=150&fit=crop&crop=face',\n            subscriptionStatus: 'premium',\n            totalPoints: 2650,\n            passedExamsCount: 14,\n            quizzesTaken: 19,\n            score: 2650,\n            rank: 3\n        },\n        {\n            userId: '4',\n            name: 'David Wilson',\n            profilePicture: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face',\n            subscriptionStatus: 'expired',\n            totalPoints: 2400,\n            passedExamsCount: 10,\n            quizzesTaken: 18,\n            score: 2400,\n            rank: 4\n        },\n        {\n            userId: '5',\n            name: 'Emma Brown',\n            profilePicture: 'https://images.unsplash.com/photo-1544005313-94ddf0286df2?w=150&h=150&fit=crop&crop=face',\n            subscriptionStatus: 'active',\n            totalPoints: 2350,\n            passedExamsCount: 11,\n            quizzesTaken: 16,\n            score: 2350,\n            rank: 5\n        },\n        {\n            userId: '6',\n            name: 'Frank Miller',\n            profilePicture: 'https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=150&h=150&fit=crop&crop=face',\n            subscriptionStatus: 'free',\n            totalPoints: 2200,\n            passedExamsCount: 9,\n            quizzesTaken: 15,\n            score: 2200,\n            rank: 6\n        },\n        {\n            userId: '7',\n            name: 'Grace Lee',\n            profilePicture: 'https://images.unsplash.com/photo-1534528741775-53994a69daeb?w=150&h=150&fit=crop&crop=face',\n            subscriptionStatus: 'premium',\n            totalPoints: 2100,\n            passedExamsCount: 8,\n            quizzesTaken: 14,\n            score: 2100,\n            rank: 7\n        },\n        {\n            userId: '8',\n            name: 'Henry Taylor',\n            profilePicture: 'https://images.unsplash.com/photo-1506794778202-cad84cf45f1d?w=150&h=150&fit=crop&crop=face',\n            subscriptionStatus: 'free',\n            totalPoints: 1950,\n            passedExamsCount: 7,\n            quizzesTaken: 13,\n            score: 1950,\n            rank: 8\n        }\n    ];\n\n    return (\n        <div className=\"min-h-screen bg-gray-50 py-8\">\n            <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n                {/* Header */}\n                <motion.div\n                    initial={{ opacity: 0, y: -20 }}\n                    animate={{ opacity: 1, y: 0 }}\n                    className=\"text-center mb-8\"\n                >\n                    <h1 className=\"text-4xl font-bold text-gray-900 mb-4\">\n                        Modern User Ranking Component\n                    </h1>\n                    <p className=\"text-lg text-gray-600 max-w-3xl mx-auto\">\n                        A beautiful, responsive ranking component with Instagram-style profile circles, \n                        premium user highlighting, and multiple layout options.\n                    </p>\n                </motion.div>\n\n                {/* Controls */}\n                <motion.div\n                    initial={{ opacity: 0, y: -10 }}\n                    animate={{ opacity: 1, y: 0 }}\n                    transition={{ delay: 0.2 }}\n                    className=\"bg-white rounded-xl p-6 mb-8 shadow-sm border border-gray-200\"\n                >\n                    <h2 className=\"text-xl font-semibold text-gray-900 mb-4\">Customization Options</h2>\n                    \n                    <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n                        {/* Layout Options */}\n                        <div>\n                            <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                                Layout Style\n                            </label>\n                            <div className=\"space-y-2\">\n                                {['horizontal', 'vertical', 'grid'].map((layoutOption) => (\n                                    <label key={layoutOption} className=\"flex items-center\">\n                                        <input\n                                            id={`layout-${layoutOption}`}\n                                            type=\"radio\"\n                                            name=\"layout\"\n                                            value={layoutOption}\n                                            checked={layout === layoutOption}\n                                            onChange={(e) => setLayout(e.target.value)}\n                                            className=\"mr-2 text-blue-600\"\n                                        />\n                                        <span className=\"capitalize\">{layoutOption}</span>\n                                    </label>\n                                ))}\n                            </div>\n                        </div>\n\n                        {/* Size Options */}\n                        <div>\n                            <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                                Component Size\n                            </label>\n                            <div className=\"space-y-2\">\n                                {['small', 'medium', 'large'].map((sizeOption) => (\n                                    <label key={sizeOption} className=\"flex items-center\">\n                                        <input\n                                            id={`size-${sizeOption}`}\n                                            type=\"radio\"\n                                            name=\"size\"\n                                            value={sizeOption}\n                                            checked={size === sizeOption}\n                                            onChange={(e) => setSize(e.target.value)}\n                                            className=\"mr-2 text-blue-600\"\n                                        />\n                                        <span className=\"capitalize\">{sizeOption}</span>\n                                    </label>\n                                ))}\n                            </div>\n                        </div>\n                    </div>\n                </motion.div>\n\n                {/* Individual Card Examples */}\n                <motion.div\n                    initial={{ opacity: 0, y: 20 }}\n                    animate={{ opacity: 1, y: 0 }}\n                    transition={{ delay: 0.3 }}\n                    className=\"mb-8\"\n                >\n                    <h2 className=\"text-2xl font-bold text-gray-900 mb-6\">Individual Card Examples</h2>\n                    \n                    <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n                        {/* Premium User Card */}\n                        <div className=\"bg-white rounded-xl p-6 border border-gray-200\">\n                            <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">Premium User (Gold Glow)</h3>\n                            <UserRankingCard\n                                user={sampleUsers[0]}\n                                rank={1}\n                                isCurrentUser={false}\n                                layout=\"horizontal\"\n                                size={size}\n                                showStats={true}\n                            />\n                        </div>\n\n                        {/* Current User Card */}\n                        <div className=\"bg-white rounded-xl p-6 border border-gray-200\">\n                            <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">Current User (Highlighted)</h3>\n                            <UserRankingCard\n                                user={sampleUsers[1]}\n                                rank={2}\n                                isCurrentUser={true}\n                                layout=\"horizontal\"\n                                size={size}\n                                showStats={true}\n                            />\n                        </div>\n                    </div>\n                </motion.div>\n\n                {/* Full Ranking List */}\n                <motion.div\n                    initial={{ opacity: 0, y: 20 }}\n                    animate={{ opacity: 1, y: 0 }}\n                    transition={{ delay: 0.4 }}\n                >\n                    <h2 className=\"text-2xl font-bold text-gray-900 mb-6\">Complete Ranking List</h2>\n                    \n                    <UserRankingList\n                        users={sampleUsers}\n                        currentUserId=\"3\" // Carol Davis as current user\n                        layout={layout}\n                        size={size}\n                        showSearch={true}\n                        showFilters={true}\n                        showStats={true}\n                    />\n                </motion.div>\n\n                {/* Features List */}\n                <motion.div\n                    initial={{ opacity: 0, y: 20 }}\n                    animate={{ opacity: 1, y: 0 }}\n                    transition={{ delay: 0.5 }}\n                    className=\"mt-12 bg-white rounded-xl p-8 border border-gray-200\"\n                >\n                    <h2 className=\"text-2xl font-bold text-gray-900 mb-6\">Key Features</h2>\n                    \n                    <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\n                        <div className=\"space-y-2\">\n                            <h3 className=\"font-semibold text-gray-900\">🎨 Premium Highlighting</h3>\n                            <p className=\"text-gray-600 text-sm\">Gold gradient glow for premium users with vibrant visual distinction</p>\n                        </div>\n                        \n                        <div className=\"space-y-2\">\n                            <h3 className=\"font-semibold text-gray-900\">📱 Responsive Design</h3>\n                            <p className=\"text-gray-600 text-sm\">Adapts perfectly to mobile, tablet, and desktop screens</p>\n                        </div>\n                        \n                        <div className=\"space-y-2\">\n                            <h3 className=\"font-semibold text-gray-900\">🔍 Search & Filter</h3>\n                            <p className=\"text-gray-600 text-sm\">Real-time search and filtering by subscription status</p>\n                        </div>\n                        \n                        <div className=\"space-y-2\">\n                            <h3 className=\"font-semibold text-gray-900\">✨ Smooth Animations</h3>\n                            <p className=\"text-gray-600 text-sm\">Framer Motion powered animations for engaging interactions</p>\n                        </div>\n                        \n                        <div className=\"space-y-2\">\n                            <h3 className=\"font-semibold text-gray-900\">🏆 Rank Indicators</h3>\n                            <p className=\"text-gray-600 text-sm\">Special icons and colors for top performers</p>\n                        </div>\n                        \n                        <div className=\"space-y-2\">\n                            <h3 className=\"font-semibold text-gray-900\">🎯 Current User Focus</h3>\n                            <p className=\"text-gray-600 text-sm\">Automatic highlighting and scroll-to functionality</p>\n                        </div>\n                    </div>\n                </motion.div>\n            </div>\n        </div>\n    );\n};\n\nexport default RankingDemo;\n", "import { useRef } from 'react';\nimport { useIsomorphicLayoutEffect } from './use-isomorphic-effect.mjs';\n\nfunction useIsMounted() {\n    const isMounted = useRef(false);\n    useIsomorphicLayoutEffect(() => {\n        isMounted.current = true;\n        return () => {\n            isMounted.current = false;\n        };\n    }, []);\n    return isMounted;\n}\n\nexport { useIsMounted };\n", "import * as React from 'react';\nimport { useId, useRef, useInsertionEffect } from 'react';\n\n/**\n * Measurement functionality has to be within a separate component\n * to leverage snapshot lifecycle.\n */\nclass PopChildMeasure extends React.Component {\n    getSnapshotBeforeUpdate(prevProps) {\n        const element = this.props.childRef.current;\n        if (element && prevProps.isPresent && !this.props.isPresent) {\n            const size = this.props.sizeRef.current;\n            size.height = element.offsetHeight || 0;\n            size.width = element.offsetWidth || 0;\n            size.top = element.offsetTop;\n            size.left = element.offsetLeft;\n        }\n        return null;\n    }\n    /**\n     * Required with getSnapshotBeforeUpdate to stop React complaining.\n     */\n    componentDidUpdate() { }\n    render() {\n        return this.props.children;\n    }\n}\nfunction PopChild({ children, isPresent }) {\n    const id = useId();\n    const ref = useRef(null);\n    const size = useRef({\n        width: 0,\n        height: 0,\n        top: 0,\n        left: 0,\n    });\n    /**\n     * We create and inject a style block so we can apply this explicit\n     * sizing in a non-destructive manner by just deleting the style block.\n     *\n     * We can't apply size via render as the measurement happens\n     * in getSnapshotBeforeUpdate (post-render), likewise if we apply the\n     * styles directly on the DOM node, we might be overwriting\n     * styles set via the style prop.\n     */\n    useInsertionEffect(() => {\n        const { width, height, top, left } = size.current;\n        if (isPresent || !ref.current || !width || !height)\n            return;\n        ref.current.dataset.motionPopId = id;\n        const style = document.createElement(\"style\");\n        document.head.appendChild(style);\n        if (style.sheet) {\n            style.sheet.insertRule(`\n          [data-motion-pop-id=\"${id}\"] {\n            position: absolute !important;\n            width: ${width}px !important;\n            height: ${height}px !important;\n            top: ${top}px !important;\n            left: ${left}px !important;\n          }\n        `);\n        }\n        return () => {\n            document.head.removeChild(style);\n        };\n    }, [isPresent]);\n    return (React.createElement(PopChildMeasure, { isPresent: isPresent, childRef: ref, sizeRef: size }, React.cloneElement(children, { ref })));\n}\n\nexport { PopChild };\n", "import * as React from 'react';\nimport { useId, useMemo } from 'react';\nimport { PresenceContext } from '../../context/PresenceContext.mjs';\nimport { useConstant } from '../../utils/use-constant.mjs';\nimport { PopChild } from './PopChild.mjs';\n\nconst PresenceChild = ({ children, initial, isPresent, onExitComplete, custom, presenceAffectsLayout, mode, }) => {\n    const presenceChildren = useConstant(newChildrenMap);\n    const id = useId();\n    const context = useMemo(() => ({\n        id,\n        initial,\n        isPresent,\n        custom,\n        onExitComplete: (childId) => {\n            presenceChildren.set(childId, true);\n            for (const isComplete of presenceChildren.values()) {\n                if (!isComplete)\n                    return; // can stop searching when any is incomplete\n            }\n            onExitComplete && onExitComplete();\n        },\n        register: (childId) => {\n            presenceChildren.set(childId, false);\n            return () => presenceChildren.delete(childId);\n        },\n    }), \n    /**\n     * If the presence of a child affects the layout of the components around it,\n     * we want to make a new context value to ensure they get re-rendered\n     * so they can detect that layout change.\n     */\n    presenceAffectsLayout ? undefined : [isPresent]);\n    useMemo(() => {\n        presenceChildren.forEach((_, key) => presenceChildren.set(key, false));\n    }, [isPresent]);\n    /**\n     * If there's no `motion` components to fire exit animations, we want to remove this\n     * component immediately.\n     */\n    React.useEffect(() => {\n        !isPresent &&\n            !presenceChildren.size &&\n            onExitComplete &&\n            onExitComplete();\n    }, [isPresent]);\n    if (mode === \"popLayout\") {\n        children = React.createElement(PopChild, { isPresent: isPresent }, children);\n    }\n    return (React.createElement(PresenceContext.Provider, { value: context }, children));\n};\nfunction newChildrenMap() {\n    return new Map();\n}\n\nexport { PresenceChild };\n", "import * as React from 'react';\nimport { useContext, useRef, cloneElement, Children, isValidElement } from 'react';\nimport { useForceUpdate } from '../../utils/use-force-update.mjs';\nimport { useIsMounted } from '../../utils/use-is-mounted.mjs';\nimport { PresenceChild } from './PresenceChild.mjs';\nimport { LayoutGroupContext } from '../../context/LayoutGroupContext.mjs';\nimport { useIsomorphicLayoutEffect } from '../../utils/use-isomorphic-effect.mjs';\nimport { useUnmountEffect } from '../../utils/use-unmount-effect.mjs';\nimport { invariant } from '../../utils/errors.mjs';\n\nconst getChildKey = (child) => child.key || \"\";\nfunction updateChildLookup(children, allChildren) {\n    children.forEach((child) => {\n        const key = getChildKey(child);\n        allChildren.set(key, child);\n    });\n}\nfunction onlyElements(children) {\n    const filtered = [];\n    // We use forEach here instead of map as map mutates the component key by preprending `.$`\n    Children.forEach(children, (child) => {\n        if (isValidElement(child))\n            filtered.push(child);\n    });\n    return filtered;\n}\n/**\n * `AnimatePresence` enables the animation of components that have been removed from the tree.\n *\n * When adding/removing more than a single child, every child **must** be given a unique `key` prop.\n *\n * Any `motion` components that have an `exit` property defined will animate out when removed from\n * the tree.\n *\n * ```jsx\n * import { motion, AnimatePresence } from 'framer-motion'\n *\n * export const Items = ({ items }) => (\n *   <AnimatePresence>\n *     {items.map(item => (\n *       <motion.div\n *         key={item.id}\n *         initial={{ opacity: 0 }}\n *         animate={{ opacity: 1 }}\n *         exit={{ opacity: 0 }}\n *       />\n *     ))}\n *   </AnimatePresence>\n * )\n * ```\n *\n * You can sequence exit animations throughout a tree using variants.\n *\n * If a child contains multiple `motion` components with `exit` props, it will only unmount the child\n * once all `motion` components have finished animating out. Likewise, any components using\n * `usePresence` all need to call `safeToRemove`.\n *\n * @public\n */\nconst AnimatePresence = ({ children, custom, initial = true, onExitComplete, exitBeforeEnter, presenceAffectsLayout = true, mode = \"sync\", }) => {\n    invariant(!exitBeforeEnter, \"Replace exitBeforeEnter with mode='wait'\");\n    // We want to force a re-render once all exiting animations have finished. We\n    // either use a local forceRender function, or one from a parent context if it exists.\n    const forceRender = useContext(LayoutGroupContext).forceRender || useForceUpdate()[0];\n    const isMounted = useIsMounted();\n    // Filter out any children that aren't ReactElements. We can only track ReactElements with a props.key\n    const filteredChildren = onlyElements(children);\n    let childrenToRender = filteredChildren;\n    const exitingChildren = useRef(new Map()).current;\n    // Keep a living record of the children we're actually rendering so we\n    // can diff to figure out which are entering and exiting\n    const presentChildren = useRef(childrenToRender);\n    // A lookup table to quickly reference components by key\n    const allChildren = useRef(new Map()).current;\n    // If this is the initial component render, just deal with logic surrounding whether\n    // we play onMount animations or not.\n    const isInitialRender = useRef(true);\n    useIsomorphicLayoutEffect(() => {\n        isInitialRender.current = false;\n        updateChildLookup(filteredChildren, allChildren);\n        presentChildren.current = childrenToRender;\n    });\n    useUnmountEffect(() => {\n        isInitialRender.current = true;\n        allChildren.clear();\n        exitingChildren.clear();\n    });\n    if (isInitialRender.current) {\n        return (React.createElement(React.Fragment, null, childrenToRender.map((child) => (React.createElement(PresenceChild, { key: getChildKey(child), isPresent: true, initial: initial ? undefined : false, presenceAffectsLayout: presenceAffectsLayout, mode: mode }, child)))));\n    }\n    // If this is a subsequent render, deal with entering and exiting children\n    childrenToRender = [...childrenToRender];\n    // Diff the keys of the currently-present and target children to update our\n    // exiting list.\n    const presentKeys = presentChildren.current.map(getChildKey);\n    const targetKeys = filteredChildren.map(getChildKey);\n    // Diff the present children with our target children and mark those that are exiting\n    const numPresent = presentKeys.length;\n    for (let i = 0; i < numPresent; i++) {\n        const key = presentKeys[i];\n        if (targetKeys.indexOf(key) === -1 && !exitingChildren.has(key)) {\n            exitingChildren.set(key, undefined);\n        }\n    }\n    // If we currently have exiting children, and we're deferring rendering incoming children\n    // until after all current children have exiting, empty the childrenToRender array\n    if (mode === \"wait\" && exitingChildren.size) {\n        childrenToRender = [];\n    }\n    // Loop through all currently exiting components and clone them to overwrite `animate`\n    // with any `exit` prop they might have defined.\n    exitingChildren.forEach((component, key) => {\n        // If this component is actually entering again, early return\n        if (targetKeys.indexOf(key) !== -1)\n            return;\n        const child = allChildren.get(key);\n        if (!child)\n            return;\n        const insertionIndex = presentKeys.indexOf(key);\n        let exitingComponent = component;\n        if (!exitingComponent) {\n            const onExit = () => {\n                // clean up the exiting children map\n                exitingChildren.delete(key);\n                // compute the keys of children that were rendered once but are no longer present\n                // this could happen in case of too many fast consequent renderings\n                // @link https://github.com/framer/motion/issues/2023\n                const leftOverKeys = Array.from(allChildren.keys()).filter((childKey) => !targetKeys.includes(childKey));\n                // clean up the all children map\n                leftOverKeys.forEach((leftOverKey) => allChildren.delete(leftOverKey));\n                // make sure to render only the children that are actually visible\n                presentChildren.current = filteredChildren.filter((presentChild) => {\n                    const presentChildKey = getChildKey(presentChild);\n                    return (\n                    // filter out the node exiting\n                    presentChildKey === key ||\n                        // filter out the leftover children\n                        leftOverKeys.includes(presentChildKey));\n                });\n                // Defer re-rendering until all exiting children have indeed left\n                if (!exitingChildren.size) {\n                    if (isMounted.current === false)\n                        return;\n                    forceRender();\n                    onExitComplete && onExitComplete();\n                }\n            };\n            exitingComponent = (React.createElement(PresenceChild, { key: getChildKey(child), isPresent: false, onExitComplete: onExit, custom: custom, presenceAffectsLayout: presenceAffectsLayout, mode: mode }, child));\n            exitingChildren.set(key, exitingComponent);\n        }\n        childrenToRender.splice(insertionIndex, 0, exitingComponent);\n    });\n    // Add `MotionContext` even to children that don't need it to ensure we're rendering\n    // the same tree between renders\n    childrenToRender = childrenToRender.map((child) => {\n        const key = child.key;\n        return exitingChildren.has(key) ? (child) : (React.createElement(PresenceChild, { key: getChildKey(child), isPresent: true, presenceAffectsLayout: presenceAffectsLayout, mode: mode }, child));\n    });\n    if (process.env.NODE_ENV !== \"production\" &&\n        mode === \"wait\" &&\n        childrenToRender.length > 1) {\n        console.warn(`You're attempting to animate multiple children within AnimatePresence, but its mode is set to \"wait\". This will lead to odd visual behaviour.`);\n    }\n    return (React.createElement(React.Fragment, null, exitingChildren.size\n        ? childrenToRender\n        : childrenToRender.map((child) => cloneElement(child))));\n};\n\nexport { AnimatePresence };\n", "import { useState, useCallback } from 'react';\nimport { useIsMounted } from './use-is-mounted.mjs';\nimport { frame } from '../frameloop/frame.mjs';\n\nfunction useForceUpdate() {\n    const isMounted = useIsMounted();\n    const [forcedRenderCount, setForcedRenderCount] = useState(0);\n    const forceRender = useCallback(() => {\n        isMounted.current && setForcedRenderCount(forcedRenderCount + 1);\n    }, [forcedRenderCount]);\n    /**\n     * Defer this to the end of the next animation frame in case there are multiple\n     * synchronous calls.\n     */\n    const deferredForceRender = useCallback(() => frame.postRender(forceRender), [forceRender]);\n    return [deferredForceRender, forcedRenderCount];\n}\n\nexport { useForceUpdate };\n", "import { useEffect } from 'react';\n\nfunction useUnmountEffect(callback) {\n    return useEffect(() => () => callback(), []);\n}\n\nexport { useUnmountEffect };\n"], "names": ["AchievementBadge", "_ref", "achievement", "size", "showDetails", "className", "onClick", "achievementConfig", "first_quiz", "icon", "TbStar", "title", "description", "color", "bgColor", "textColor", "perfect_score", "TbTrophy", "streak_5", "TbFlame", "streak_10", "streak_20", "subject_master", "TbCrown", "speed_demon", "TbBolt", "consistent_learner", "TbTarget", "improvement_star", "TbTrendingUp", "config", "type", "sizes", "small", "container", "text", "padding", "medium", "large", "IconComponent", "glowVariants", "hidden", "opacity", "visible", "transition", "duration", "repeat", "Infinity", "ease", "_jsxs", "motion", "div", "variants", "scale", "rotate", "stiffness", "damping", "hover", "initial", "animate", "whileHover", "concat", "children", "_jsx", "y", "subject", "earnedAt", "Date", "toLocaleDateString", "replace", "AchievementList", "_ref2", "_sizeConfig$size", "_sizeConfig$size2", "_sizeConfig$size3", "achievements", "maxDisplay", "layout", "displayAchievements", "slice", "remainingCount", "Math", "max", "length", "sizeConfig", "layoutClasses", "stagger<PERSON><PERSON><PERSON><PERSON>", "map", "index", "currentXP", "totalXP", "currentLevel", "xpToNextLevel", "showAnimation", "showLevel", "showXPNumbers", "animatedXP", "setAnimatedXP", "useState", "isLevelingUp", "setIsLevelingUp", "xpForCurrentLevel", "xpProgressInLevel", "xpNeededForLevel", "progressPercentage", "min", "height", "levelSize", "textSize", "useEffect", "timer", "setTimeout", "clearTimeout", "AnimatePresence", "exit", "whileTap", "level", "toLocaleString", "width", "x", "showTitle", "showGlow", "animated", "titleText", "xl", "levelConfig", "gradient", "glow", "shape", "rarity", "animation", "TbDiamond", "getLevelConfig", "animationVariants", "none", "pulse", "bounce", "boxShadow", "getShapeClasses", "getRarityBorder", "_Fragment", "delay", "_achievement$type", "showTooltip", "showXP", "id", "iconMap", "TbRocket", "TbBrain", "TbUsers", "TbMedal", "getAchievementIcon", "colorGradient", "rarityColors", "common", "getRarityColor", "repeatDelay", "getRarityAnimation", "glowMap", "getRarityGlow", "includes", "xpReward", "name", "l", "toUpperCase", "char<PERSON>t", "user", "rank", "classRank", "isCurrentUser", "showStats", "avatar", "subtext", "spacing", "styling", "getSubscriptionStyling", "normalizedStatus", "subscriptionStatus", "normalizedSubscriptionStatus", "toLowerCase", "avatarClass", "badge", "statusText", "badgeIcon", "borderClass", "bgClass", "cardBg", "borderColor", "rankDisplay", "getRankDisplay", "safeRank", "bg", "RankIcon", "ProfilePicture", "showOnlineStatus", "style", "LevelBadge", "totalPoints", "averageScore", "currentStreak", "EnhancedAchievementBadge", "undefined", "passedExamsCount", "quizzesTaken", "XPProgressBar", "rankingScore", "score", "breakdown", "users", "currentUserId", "currentUserRef", "showFindMe", "lastUpdated", "autoRefresh", "onAutoRefreshToggle", "searchTerm", "setSearchTerm", "filterType", "setFilterType", "sortBy", "setSortBy", "viewMode", "setViewMode", "showOnlyMyClass", "setShowOnlyMyClass", "localShowFindMe", "setLocalShowFindMe", "localCurrentUserRef", "useRef", "userRef", "findMeActive", "currentUser", "find", "userId", "_id", "currentUserClass", "class", "currentUserLevel", "filteredUsers", "filter", "_user$name", "_user$email", "_user$class", "_user$normalizedSubsc", "_user$subscriptionSta", "matchesSearch", "email", "matchesClass", "userStatus", "matchesFilter", "sort", "a", "b", "localeCompare", "usersWithClassRank", "u", "findIndex", "_objectSpread", "scrollToCurrentUser", "current", "scrollIntoView", "behavior", "block", "totalUsers", "premiumUsers", "topScore", "activeUsers", "totalQuizzesTaken", "averageXP", "round", "reduce", "sum", "TbSearch", "placeholder", "value", "onChange", "e", "target", "autoComplete", "Tb<PERSON><PERSON>er", "TbClock", "toLocaleTimeString", "TbPlayerPause", "TbPlayerPlay", "TbUser", "getLayoutClasses", "_ref3", "_ref4", "_ref5", "_ref6", "_ref7", "ref", "src", "profilePicture", "alt", "UserRankingCard", "RankingDemo", "setLayout", "setSize", "sampleUsers", "layoutOption", "checked", "sizeOption", "UserRankingList", "showSearch", "showFilters", "useIsMounted", "isMounted", "useIsomorphicLayoutEffect", "PopChildMeasure", "React", "getSnapshotBeforeUpdate", "prevProps", "element", "this", "props", "childRef", "isPresent", "sizeRef", "offsetHeight", "offsetWidth", "top", "offsetTop", "left", "offsetLeft", "componentDidUpdate", "render", "PopChild", "useId", "useInsertionEffect", "dataset", "motionPopId", "document", "createElement", "head", "append<PERSON><PERSON><PERSON>", "sheet", "insertRule", "<PERSON><PERSON><PERSON><PERSON>", "Presence<PERSON><PERSON><PERSON>", "onExitComplete", "custom", "presenceAffectsLayout", "mode", "presenceC<PERSON><PERSON>n", "useConstant", "newChildrenMap", "context", "useMemo", "childId", "set", "isComplete", "values", "register", "delete", "for<PERSON>ach", "_", "key", "PresenceContext", "Provider", "Map", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "child", "exitBeforeEnter", "invariant", "forceRender", "useContext", "LayoutGroupContext", "forcedRenderCount", "setForcedRenderCount", "useCallback", "frame", "postRender", "useForceUpdate", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "filtered", "Children", "isValidElement", "push", "onlyElements", "children<PERSON><PERSON><PERSON><PERSON>", "exiting<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "allChildren", "isInitialRender", "callback", "updateChildLookup", "clear", "present<PERSON><PERSON>s", "targetKeys", "numPresent", "i", "indexOf", "has", "component", "get", "insertionIndex", "exitingComponent", "onExit", "leftOverKeys", "Array", "from", "keys", "<PERSON><PERSON><PERSON>", "leftOverKey", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "splice", "cloneElement"], "sourceRoot": ""}