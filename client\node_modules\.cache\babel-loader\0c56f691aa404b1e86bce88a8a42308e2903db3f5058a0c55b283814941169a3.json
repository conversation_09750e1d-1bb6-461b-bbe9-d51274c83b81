{"ast": null, "code": "import * as React from 'react';\nimport { Item } from '../Item';\nexport default function useChildren(list, startIndex, endIndex, setNodeRef, renderFunc, _ref) {\n  var getKey = _ref.getKey;\n  return list.slice(startIndex, endIndex + 1).map(function (item, index) {\n    var eleIndex = startIndex + index;\n    var node = renderFunc(item, eleIndex, {\n      // style: status === 'MEASURE_START' ? { visibility: 'hidden' } : {},\n    });\n    var key = getKey(item);\n    return /*#__PURE__*/React.createElement(Item, {\n      key: key,\n      setRef: function setRef(ele) {\n        return setNodeRef(item, ele);\n      }\n    }, node);\n  });\n}", "map": {"version": 3, "names": ["React", "<PERSON><PERSON>", "useChildren", "list", "startIndex", "endIndex", "setNodeRef", "renderFunc", "_ref", "<PERSON><PERSON><PERSON>", "slice", "map", "item", "index", "eleIndex", "node", "key", "createElement", "setRef", "ele"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/node_modules/rc-virtual-list/es/hooks/useChildren.js"], "sourcesContent": ["import * as React from 'react';\nimport { Item } from '../Item';\nexport default function useChildren(list, startIndex, endIndex, setNodeRef, renderFunc, _ref) {\n  var getKey = _ref.getKey;\n  return list.slice(startIndex, endIndex + 1).map(function (item, index) {\n    var eleIndex = startIndex + index;\n    var node = renderFunc(item, eleIndex, {\n      // style: status === 'MEASURE_START' ? { visibility: 'hidden' } : {},\n    });\n    var key = getKey(item);\n    return /*#__PURE__*/React.createElement(Item, {\n      key: key,\n      setRef: function setRef(ele) {\n        return setNodeRef(item, ele);\n      }\n    }, node);\n  });\n}"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,IAAI,QAAQ,SAAS;AAC9B,eAAe,SAASC,WAAWA,CAACC,IAAI,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,UAAU,EAAEC,UAAU,EAAEC,IAAI,EAAE;EAC5F,IAAIC,MAAM,GAAGD,IAAI,CAACC,MAAM;EACxB,OAAON,IAAI,CAACO,KAAK,CAACN,UAAU,EAAEC,QAAQ,GAAG,CAAC,CAAC,CAACM,GAAG,CAAC,UAAUC,IAAI,EAAEC,KAAK,EAAE;IACrE,IAAIC,QAAQ,GAAGV,UAAU,GAAGS,KAAK;IACjC,IAAIE,IAAI,GAAGR,UAAU,CAACK,IAAI,EAAEE,QAAQ,EAAE;MACpC;IAAA,CACD,CAAC;IACF,IAAIE,GAAG,GAAGP,MAAM,CAACG,IAAI,CAAC;IACtB,OAAO,aAAaZ,KAAK,CAACiB,aAAa,CAAChB,IAAI,EAAE;MAC5Ce,GAAG,EAAEA,GAAG;MACRE,MAAM,EAAE,SAASA,MAAMA,CAACC,GAAG,EAAE;QAC3B,OAAOb,UAAU,CAACM,IAAI,EAAEO,GAAG,CAAC;MAC9B;IACF,CAAC,EAAEJ,IAAI,CAAC;EACV,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}