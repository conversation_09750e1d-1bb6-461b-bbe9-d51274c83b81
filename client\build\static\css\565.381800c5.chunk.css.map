{"version": 3, "file": "static/css/565.381800c5.chunk.css", "mappings": "AACA,yBAEE,kDAA6D,CAC7D,6DAAmE,CAFnE,gBAGF,CAGA,sBAEE,kCAA2B,CAA3B,0BAA2B,CAD3B,sDAA0F,CAE1F,iCAA8C,CAE9C,kBAAmB,CADnB,cAEF,CAEA,gBAOE,cAAe,CACf,QAAS,CAHT,6BAA8B,CAH9B,aAAc,CADd,gBAAiB,CAEjB,cAMF,CAEA,6BALE,kBAAmB,CAFnB,YAWF,CAJA,aAGE,UACF,CAEA,aAME,kBAAmB,CAHnB,kDAAqD,CACrD,kBAAmB,CAMnB,+BAA+C,CAF/C,UAAY,CAHZ,YAAa,CAIb,cAAe,CAPf,WAAY,CAKZ,sBAAuB,CANvB,UAUF,CAEA,gBAGE,UAAY,CAFZ,gBAAiB,CACjB,eAAgB,CAEhB,QAAS,CACT,+BACF,CAEA,eAEE,eAA+B,CAD/B,gBAAiB,CAEjB,gBACF,CAEA,eAGE,kBAAmB,CAFnB,YAAa,CACb,QAEF,CAEA,8BAEE,kCAA2B,CAA3B,0BAA2B,CAD3B,oBAAoC,CAIpC,sBAA0C,CAD1C,kBAAmB,CADnB,qBAGF,CAEA,0BAGE,eAA+B,CAF/B,aAAc,CACd,eAAiB,CAIjB,mBAAqB,CAFrB,oBAAsB,CACtB,wBAEF,CAEA,0BAIE,UAAY,CAHZ,aAAc,CACd,gBAAiB,CACjB,eAEF,CAGA,uBAEE,aAAc,CADd,gBAAiB,CAEjB,mBACF,CAGA,gBAEE,kCAA2B,CAA3B,0BAA2B,CAD3B,oBAAqC,CAMrC,sBAA0C,CAJ1C,kBAAmB,CAGnB,+BAAyC,CADzC,kBAAmB,CADnB,cAIF,CAEA,cAGE,aAAS,CAFT,YAAa,CAEb,QAAS,CADT,wDAA2D,CAE3D,kBACF,CAEA,eACE,YAAa,CACb,qBAAsB,CACtB,SACF,CAEA,eAEE,kBAAmB,CAInB,aAAc,CALd,YAAa,CAGb,eAAiB,CACjB,eAAgB,CAFhB,SAAW,CAKX,mBAAqB,CADrB,wBAEF,CAEA,mBAEE,aAAc,CADd,cAEF,CAEA,gBAIE,eAAiB,CAFjB,wBAAyB,CACzB,iBAAkB,CAIlB,aAAc,CAEd,cAAe,CAJf,eAAiB,CACjB,eAAgB,CALhB,mBAAqB,CAOrB,uBAEF,CAEA,sBAEE,oBAAqB,CACrB,8BAA8C,CAF9C,YAGF,CAEA,sBACE,oBACF,CAEA,cACE,kDAAqD,CAErD,kBAAyB,CADzB,UAEF,CAEA,oBACE,8BACF,CAGA,YAGE,kBAAmB,CAFnB,YAAa,CAGb,cAAe,CAFf,QAGF,CAEA,kBACE,QAAO,CAEP,eAAgB,CADhB,iBAEF,CAEA,cAKE,eAAiB,CAFjB,wBAAyB,CACzB,kBAAmB,CAGnB,aAAc,CADd,cAAe,CAJf,+BAAkC,CAMlC,uBAAyB,CAPzB,UAQF,CAEA,oBAEE,oBAAqB,CACrB,8BAA8C,CAF9C,YAGF,CAEA,2BACE,aACF,CAEA,aAKE,aAAc,CACd,gBAAiB,CAJjB,SAKF,CAEA,+BARE,iBAAkB,CAElB,OAAQ,CACR,0BAsBF,CAjBA,kBAeE,kBAAmB,CAVnB,kBAAmB,CAEnB,WAAY,CAEZ,iBAAkB,CAHlB,UAAY,CAMZ,cAAe,CAEf,YAAa,CAJb,eAAiB,CACjB,eAAgB,CAKhB,UAAY,CARZ,kBAAoB,CANpB,WAAa,CAWb,uBAIF,CAEA,wBACE,kBAAmB,CACnB,sCACF,CAEA,aAWE,kBAAmB,CAVnB,kDAAqD,CAErD,WAAY,CAEZ,kBAAmB,CAHnB,UAAY,CAMZ,cAAe,CAEf,YAAa,CAJb,eAAiB,CACjB,eAAgB,CAKhB,SAAW,CARX,qBAAuB,CAKvB,uBAAyB,CAIzB,kBACF,CAEA,mBACE,kDAAqD,CAErD,+BAA8C,CAD9C,0BAEF,CAGA,aAGE,eAAW,CAFX,YAAa,CAEb,UAAW,CADX,yDAA4D,CAE5D,cACF,CAEA,YAOE,sBAA0C,CAL1C,kBAAmB,CAEnB,+BAIF,CAEA,kBAGE,oBAAqB,CADrB,gCAA2C,CAD3C,0BAGF,CAEA,sBAKE,kBAAmB,CAFnB,YAAa,CACb,eAAgB,CAHhB,iBAAkB,CAClB,UAIF,CAEA,iBAEE,WAAY,CACZ,gBAAiB,CACjB,6BAA+B,CAH/B,UAIF,CAEA,mCACE,qBACF,CAEA,cAUE,kBAAmB,CALnB,gBAA8B,CAC9B,iBAAkB,CAGlB,YAAa,CADb,WAAY,CAGZ,sBAAuB,CARvB,QAAS,CAUT,SAAU,CAZV,iBAAkB,CAClB,OAAQ,CAER,8BAAgC,CAQhC,uBAAyB,CALzB,UAOF,CAEA,gCACE,SAAU,CACV,yCACF,CAOA,gBAIE,gBAA8B,CAF9B,UAAW,CAMX,gBAAkB,CAClB,eACF,CAEA,gCALE,iBAAkB,CAFlB,UAAY,CACZ,eAAgB,CALhB,iBAAkB,CAElB,SAsBF,CAbA,gBAWE,kBAAmB,CAPnB,oBAAoC,CAMpC,YAAa,CAFb,eAAiB,CACjB,eAAgB,CAGhB,OAAQ,CAVR,OAWF,CAEA,oBACE,eACF,CAEA,aAOE,oBAAqB,CACrB,2BAA4B,CAL5B,aAAc,CAGd,mBAAoB,CALpB,gBAAiB,CACjB,eAAgB,CAGhB,eAAgB,CADhB,iBAAqB,CAKrB,eACF,CAEA,YAGE,kBAAmB,CADnB,6BAA8B,CAE9B,oBACF,CAEA,eACE,kDAAqD,CAGrD,kBAAmB,CAFnB,UAAY,CAGZ,eAAiB,CACjB,eAAgB,CAHhB,gBAIF,CAEA,aACE,aAAc,CACd,gBAAkB,CAClB,eACF,CAEA,YACE,YAAa,CAEb,cAAe,CADf,SAEF,CAEA,WACE,kBAAmB,CACnB,aAAc,CAKd,yBACF,CAEA,sBANE,kBAAmB,CACnB,eAAiB,CACjB,eAAgB,CAHhB,eAcF,CAPA,WACE,kBAAmB,CACnB,aAKF,CAEA,YACE,kDAAqD,CAGrD,kBAAmB,CAGnB,8BAA6C,CAL7C,UAAY,CAGZ,eAAiB,CACjB,eAAgB,CAHhB,eAKF,CAGA,kBACE,kBAAmB,CACnB,4BAA6B,CAE7B,gBAAiB,CACjB,eAAgB,CAFhB,cAGF,CAEA,gBAEE,kBAAmB,CAInB,aAAc,CALd,YAAa,CAGb,gBAAiB,CACjB,eAAgB,CAFhB,SAAW,CAIX,eACF,CAEA,oBACE,aACF,CAEA,aACE,oBACF,CAEA,yBACE,YAAa,CACb,qBAAsB,CACtB,UACF,CAEA,eAGE,wBAAyB,CACzB,iBAAkB,CAElB,mBAAoB,CADpB,eAAiB,CAGjB,eAAgB,CANhB,cAAgB,CAKhB,eAAgB,CAEhB,gCAAkC,CARlC,UASF,CAEA,qBAEE,oBAAqB,CACrB,8BAA8C,CAF9C,YAGF,CAEA,4BACE,aACF,CAEA,oBACE,mBAAoB,CACpB,kDAAqD,CAErD,WAAY,CAEZ,iBAAkB,CAHlB,UAAY,CAMZ,cAAe,CAFf,eAAiB,CACjB,eAAgB,CAHhB,oBAAsB,CAKtB,uBACF,CAEA,yCACE,kDAAqD,CACrD,0BACF,CAEA,6BAEE,kBAAmB,CADnB,UAEF,CAEA,eAGE,QACF,CAEA,4BALE,YAAa,CACb,qBAWF,CAPA,aAGE,kBAAmB,CAEnB,aAAc,CADd,YAAa,CAEb,iBACF,CAEA,iBACE,cAAe,CACf,mBAAqB,CACrB,UACF,CAEA,SACE,eAAiB,CACjB,wBAAyB,CACzB,kBAAmB,CACnB,YAAa,CACb,8BACF,CAEA,eACE,8BACF,CAEA,gBAGE,kBAAmB,CAFnB,YAAa,CACb,6BAA8B,CAE9B,mBACF,CAEA,gBAEE,aAAc,CACd,eAAiB,CAFjB,eAGF,CAEA,cAEE,aAAc,CADd,eAEF,CAEA,cACE,aAAc,CACd,eAAgB,CAChB,oBACF,CAEA,iBACE,YAAa,CACb,SACF,CAEA,WACE,eAAgB,CAChB,WAAY,CAMZ,iBAAkB,CALlB,aAAc,CAGd,cAAe,CAFf,eAAiB,CACjB,eAAgB,CAEhB,oBAAuB,CAEvB,uBACF,CAEA,iBACE,oBAAoC,CACpC,aACF,CAEA,uBAGE,kBAAmB,CAEnB,wBAAyB,CADzB,iBAAkB,CAHlB,iBAAmB,CACnB,cAIF,CAEA,aAGE,wBAAyB,CACzB,iBAAkB,CAElB,mBAAoB,CADpB,gBAAkB,CAGlB,mBAAqB,CANrB,aAAe,CAKf,eAAgB,CANhB,UAQF,CAEA,mBAEE,oBAAqB,CACrB,8BAA8C,CAF9C,YAGF,CAEA,eACE,YAAa,CACb,SAAW,CACX,wBACF,CAEA,kBACE,kBAAmB,CAEnB,WAAY,CAEZ,iBAAkB,CAHlB,UAAY,CAMZ,cAAe,CAFf,eAAiB,CACjB,eAAgB,CAHhB,kBAAoB,CAKpB,uBACF,CAEA,uCACE,kBACF,CAEA,2BAEE,kBAAmB,CADnB,UAEF,CAEA,kBACE,kBAAmB,CAEnB,WAAY,CAEZ,iBAAkB,CAHlB,aAAc,CAMd,cAAe,CAFf,eAAiB,CACjB,eAAgB,CAHhB,kBAAoB,CAKpB,uBACF,CAEA,wBACE,kBACF,CAEA,SAGE,6BAA8B,CAF9B,iBAAmB,CACnB,iBAEF,CAEA,OACE,kBAAmB,CACnB,wBAAyB,CACzB,iBAAkB,CAElB,mBAAqB,CADrB,cAEF,CAEA,kBACE,eACF,CAEA,cAGE,kBAAmB,CAFnB,YAAa,CACb,6BAA8B,CAE9B,mBACF,CAEA,cAEE,aAAc,CACd,gBAAkB,CAFlB,eAGF,CAEA,YAEE,aAAc,CADd,gBAEF,CAEA,YACE,aAAc,CAEd,eAAiB,CADjB,eAEF,CAGA,eAGE,kBAAmB,CAGnB,UAAY,CALZ,YAAa,CACb,qBAAsB,CAEtB,sBAAuB,CACvB,iBAAkB,CAElB,iBACF,CAEA,iBAME,iCAAkC,CAFlC,0BAA2B,CAC3B,iBAAkB,CADlB,qBAA2B,CAF3B,WAAY,CAKZ,kBAAmB,CANnB,UAOF,CAEA,gBACE,GAAK,sBAAyB,CAC9B,GAAO,uBAA2B,CACpC,CAEA,iBACE,gBAAiB,CACjB,QACF,CAEA,aAGE,kBAAmB,CAGnB,UAAY,CALZ,YAAa,CACb,qBAAsB,CAEtB,sBAAuB,CACvB,iBAAkB,CAElB,iBACF,CAEA,yBAEE,aAAc,CADd,cAAe,CAEf,kBACF,CAEA,gBAGE,UAAY,CAFZ,gBAAiB,CACjB,eAEF,CAEA,eACE,cAAe,CACf,eAAkB,CAClB,UACF,CAEA,WACE,kDAAqD,CAErD,WAAY,CAEZ,kBAAmB,CAHnB,UAAY,CAMZ,cAAe,CAFf,cAAe,CACf,eAAgB,CAHhB,mBAAqB,CAKrB,uBACF,CAEA,iBACE,kDAAqD,CAErD,+BAA+C,CAD/C,0BAEF,CAEA,aAGE,kBAAmB,CAGnB,UAAY,CALZ,YAAa,CACb,qBAAsB,CAEtB,sBAAuB,CACvB,iBAAkB,CAElB,iBACF,CAEA,yBAEE,WAA+B,CAD/B,cAAe,CAEf,oBACF,CAEA,gBAGE,UAAY,CAFZ,gBAAiB,CACjB,eAEF,CAEA,eACE,cAAe,CACf,gBAAoB,CACpB,UACF,CAEA,yBACE,eAAiB,CAEjB,iBAAkB,CADlB,UAEF,CAGA,0BACE,aACE,yDACF,CACF,CAEA,0BACE,gBACE,qBAAsB,CAEtB,UAAW,CADX,iBAEF,CAEA,eAEE,cAAe,CADf,sBAEF,CAEA,cAEE,UAAY,CADZ,wDAEF,CAEA,aAEE,QAAS,CADT,yDAEF,CAEA,sBACE,YACF,CAEA,kBACE,gBACF,CACF,CAEA,yBACE,sBACE,gBACF,CAEA,gBACE,cACF,CAEA,gBACE,cACF,CAEA,eACE,qBAAsB,CACtB,QAAS,CACT,UACF,CAEA,8BACE,iBACF,CAEA,uBACE,mBACF,CAEA,gBACE,YACF,CAEA,cAEE,UAAY,CADZ,yBAEF,CAEA,YACE,qBAAsB,CACtB,UACF,CAEA,kBACE,cACF,CAEA,aAEE,sBAAuB,CADvB,UAEF,CAEA,aAEE,QAAS,CADT,yBAEF,CAEA,sBACE,YACF,CAEA,kBAEE,gBAAiB,CADjB,YAEF,CAEA,yBACE,SACF,CAEA,eACE,qBAAsB,CACtB,UACF,CAEA,oCACE,UACF,CACF,CAEA,yBACE,aACE,qBAAsB,CACtB,QACF,CAEA,aAGE,cAAe,CADf,WAAY,CADZ,UAGF,CAEA,gBACE,iBACF,CAEA,eACE,cACF,CAEA,gBACE,cACF,CAEA,cAEE,eAAiB,CADjB,iCAEF,CAEA,aACE,WACF,CAEA,kBAKE,eAAiB,CAFjB,gBAAkB,CAFlB,eAAgB,CAChB,cAAe,CAEf,UAEF,CAEA,oBACE,cACF,CAEA,aACE,gBACF,CAEA,YAEE,sBAAuB,CADvB,qBAAsB,CAEtB,UACF,CAEA,YACE,cAAe,CACf,UACF,CAEA,kBAEE,gBAAiB,CADjB,cAEF,CAEA,gBACE,cACF,CAEA,eAEE,gBAAkB,CADlB,eAEF,CAEA,SACE,cACF,CAEA,gBAEE,sBAAuB,CADvB,qBAAsB,CAEtB,UACF,CAEA,cACE,eACF,CACF,CAGA,YAGE,eAAW,CAFX,YAAa,CAEb,UAAW,CADX,yDAA4D,CAE5D,cACF,CAEA,YACE,eAAiB,CACjB,kBAAmB,CAEnB,8BAAwC,CAExC,cAAe,CAHf,eAAgB,CAEhB,uBAEF,CAEA,kBAEE,+BAA0C,CAD1C,0BAEF,CAEA,2BAKE,cAAe,CAFf,YAAa,CACb,eAAgB,CAHhB,iBAAkB,CAClB,UAIF,CAEA,iBAEE,WAAY,CACZ,gBAAiB,CACjB,6BAA+B,CAH/B,UAIF,CAEA,kDACE,qBACF,CAEA,oBAUE,kBAAmB,CALnB,oBAA8B,CAC9B,iBAAkB,CAGlB,YAAa,CADb,WAAY,CAGZ,sBAAuB,CARvB,QAAS,CAFT,iBAAkB,CAClB,OAAQ,CAER,8BAAgC,CAQhC,uBAAyB,CALzB,UAMF,CAEA,0BACE,gBAA8B,CAC9B,yCACF,CAEA,WACE,UAAY,CACZ,cACF,CAEA,sBAIE,gBAA8B,CAG9B,iBAAkB,CALlB,UAAW,CAGX,UAAY,CAGZ,eAAiB,CACjB,eAAgB,CAHhB,eAAgB,CALhB,iBAAkB,CAElB,SAOF,CAEA,oBACE,YACF,CAEA,kBAOE,oBAAqB,CACrB,2BAA4B,CAJ5B,aAAc,CAEd,mBAAoB,CALpB,gBAAiB,CACjB,eAAgB,CAGhB,eAAgB,CAFhB,mBAAqB,CAMrB,eACF,CAEA,iBAEE,kBAAmB,CAGnB,aAAc,CAJd,YAAa,CAKb,eAAiB,CACjB,eAAgB,CAJhB,SAAW,CACX,mBAIF,CAEA,iBAGE,kBAAmB,CAFnB,YAAa,CACb,6BAA8B,CAE9B,kBACF,CAEA,oBACE,kBAAmB,CAGnB,iBAAkB,CAFlB,aAAc,CAGd,eAAiB,CACjB,eAAgB,CAHhB,eAIF,CAEA,kBACE,aAAc,CACd,eACF,CAGA,eAQE,kBAAmB,CAFnB,oBAA8B,CAD9B,QAAS,CAET,YAAa,CAEb,sBAAuB,CANvB,MAAO,CAQP,YAAa,CAVb,cAAe,CAGf,OAAQ,CAFR,KAAM,CAQN,YAEF,CAEA,wBACE,SACF,CAEA,aACE,eAAiB,CACjB,kBAAmB,CAGnB,eAAgB,CADhB,cAAe,CADf,eAAgB,CAIhB,iBAAkB,CADlB,UAEF,CAEA,sBAGE,eAAgB,CADhB,gBAAiB,CADjB,eAGF,CAEA,cAGE,kBAAmB,CAEnB,kBAAmB,CACnB,+BAAgC,CALhC,YAAa,CACb,6BAA8B,CAE9B,YAGF,CAEA,eAEE,aAAc,CACd,gBAAiB,CACjB,eAAgB,CAHhB,QAIF,CAEA,YAKE,aAAc,CAJd,YAAa,CAGb,eAAiB,CAFjB,QAAS,CACT,gBAGF,CAEA,gBACE,YAAa,CACb,SACF,CAEA,aACE,eAAgB,CAChB,WAAY,CAEZ,iBAAkB,CAElB,aAAc,CADd,cAAe,CAFf,aAAe,CAIf,uBACF,CAEA,mBACE,kBAAmB,CACnB,aACF,CAEA,iBAEE,eAAgB,CADhB,iBAEF,CAEA,cAGE,WAAY,CADZ,YAAa,CADb,UAGF,CAEA,oCACE,yBACF,CAEA,oBAEE,kBAAmB,CAGnB,oBAA8B,CAG9B,iBAAkB,CAFlB,UAAY,CALZ,YAAa,CAMb,eAAiB,CAJjB,SAAW,CAMX,gBAAkB,CALlB,aAMF,CAEA,eACE,cACF,CAEA,qBAQE,kBAAmB,CAFnB,gBAA8B,CAD9B,QAAS,CAKT,UAAY,CAHZ,YAAa,CAEb,sBAAuB,CANvB,MAAO,CAFP,iBAAkB,CAGlB,OAAQ,CAOR,iBAAkB,CATlB,KAUF,CAEA,eACE,YACF,CAEA,2BAGE,aAAc,CAFd,cAAe,CACf,kBAEF,CAEA,mBACE,kBAAmB,CAEnB,WAAY,CAEZ,iBAAkB,CAHlB,UAAY,CAIZ,cAAe,CACf,eAAgB,CAHhB,kBAAoB,CAIpB,8BACF,CAEA,yBACE,kBACF,CAEA,aAGE,kBAAmB,CAInB,UAAY,CANZ,YAAa,CACb,qBAAsB,CAEtB,sBAAuB,CAIvB,gBAAiB,CAHjB,YAAa,CACb,iBAGF,CAEA,yBACE,cAAe,CACf,kBACF,CAEA,gBAEE,UAAY,CADZ,kBAEF,CAEA,mBACE,kBAAmB,CAInB,iBAAkB,CAHlB,UAAY,CAIZ,oBAAqB,CACrB,eAAgB,CAHhB,qBAAuB,CADvB,oBAAqB,CAKrB,8BACF,CAEA,yBACE,kBAAmB,CACnB,UAAY,CACZ,oBACF,CAGA,yBACE,YAEE,QAAS,CADT,yBAEF,CAEA,eACE,YACF,CAEA,cAEE,sBAAuB,CADvB,qBAAsB,CAEtB,QACF,CAEA,YACE,qBAAsB,CACtB,SACF,CAEA,cACE,YACF,CAEA,oCACE,0BACF,CACF", "sources": ["pages/user/VideoLessons/index.css"], "sourcesContent": ["/* Enhanced Video Lessons Styles */\n.video-lessons-container {\n  min-height: 100vh;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;\n}\n\n/* Enhanced Header */\n.video-lessons-header {\n  background: linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%);\n  backdrop-filter: blur(10px);\n  border-bottom: 1px solid rgba(255,255,255,0.1);\n  padding: 2rem 0;\n  margin-bottom: 2rem;\n}\n\n.header-content {\n  max-width: 1200px;\n  margin: 0 auto;\n  padding: 0 2rem;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  flex-wrap: wrap;\n  gap: 2rem;\n}\n\n.header-main {\n  display: flex;\n  align-items: center;\n  gap: 1.5rem;\n}\n\n.header-icon {\n  width: 60px;\n  height: 60px;\n  background: linear-gradient(135deg, #ff6b6b, #ee5a24);\n  border-radius: 16px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  color: white;\n  font-size: 28px;\n  box-shadow: 0 8px 32px rgba(255, 107, 107, 0.3);\n}\n\n.header-text h1 {\n  font-size: 2.5rem;\n  font-weight: 700;\n  color: white;\n  margin: 0;\n  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);\n}\n\n.header-text p {\n  font-size: 1.1rem;\n  color: rgba(255, 255, 255, 0.9);\n  margin: 0.5rem 0 0 0;\n}\n\n.level-display {\n  display: flex;\n  gap: 2rem;\n  align-items: center;\n}\n\n.current-level, .current-class {\n  background: rgba(255, 255, 255, 0.1);\n  backdrop-filter: blur(10px);\n  padding: 0.75rem 1.5rem;\n  border-radius: 12px;\n  border: 1px solid rgba(255, 255, 255, 0.2);\n}\n\n.level-label, .class-label {\n  display: block;\n  font-size: 0.8rem;\n  color: rgba(255, 255, 255, 0.7);\n  margin-bottom: 0.25rem;\n  text-transform: uppercase;\n  letter-spacing: 0.5px;\n}\n\n.level-value, .class-value {\n  display: block;\n  font-size: 1.1rem;\n  font-weight: 600;\n  color: white;\n}\n\n/* Content Area */\n.video-lessons-content {\n  max-width: 1200px;\n  margin: 0 auto;\n  padding: 0 2rem 2rem 2rem;\n}\n\n/* Enhanced Controls */\n.video-controls {\n  background: rgba(255, 255, 255, 0.95);\n  backdrop-filter: blur(10px);\n  border-radius: 16px;\n  padding: 1.5rem;\n  margin-bottom: 2rem;\n  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);\n  border: 1px solid rgba(255, 255, 255, 0.2);\n}\n\n.controls-row {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n  gap: 1rem;\n  margin-bottom: 1rem;\n}\n\n.control-group {\n  display: flex;\n  flex-direction: column;\n  gap: 0.5rem;\n}\n\n.control-label {\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n  font-size: 0.9rem;\n  font-weight: 600;\n  color: #4a5568;\n  text-transform: uppercase;\n  letter-spacing: 0.5px;\n}\n\n.control-label svg {\n  font-size: 1rem;\n  color: #667eea;\n}\n\n.control-select {\n  padding: 0.75rem 1rem;\n  border: 2px solid #e2e8f0;\n  border-radius: 8px;\n  background: white;\n  font-size: 0.9rem;\n  font-weight: 500;\n  color: #2d3748;\n  transition: all 0.2s ease;\n  cursor: pointer;\n}\n\n.control-select:focus {\n  outline: none;\n  border-color: #667eea;\n  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);\n}\n\n.control-select:hover {\n  border-color: #cbd5e0;\n}\n\n.level-select {\n  background: linear-gradient(135deg, #667eea, #764ba2);\n  color: white;\n  border-color: transparent;\n}\n\n.level-select:focus {\n  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.3);\n}\n\n/* Search Row */\n.search-row {\n  display: flex;\n  gap: 1rem;\n  align-items: center;\n  flex-wrap: wrap;\n}\n\n.search-container {\n  flex: 1;\n  position: relative;\n  min-width: 300px;\n}\n\n.search-input {\n  width: 100%;\n  padding: 0.75rem 1rem 0.75rem 3rem;\n  border: 2px solid #e2e8f0;\n  border-radius: 12px;\n  background: white;\n  font-size: 1rem;\n  color: #2d3748;\n  transition: all 0.2s ease;\n}\n\n.search-input:focus {\n  outline: none;\n  border-color: #667eea;\n  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);\n}\n\n.search-input::placeholder {\n  color: #a0aec0;\n}\n\n.search-icon {\n  position: absolute;\n  left: 1rem;\n  top: 50%;\n  transform: translateY(-50%);\n  color: #a0aec0;\n  font-size: 1.1rem;\n}\n\n.clear-search-btn {\n  position: absolute;\n  right: 0.5rem;\n  top: 50%;\n  transform: translateY(-50%);\n  background: #f56565;\n  color: white;\n  border: none;\n  padding: 0.5rem 1rem;\n  border-radius: 8px;\n  font-size: 0.8rem;\n  font-weight: 500;\n  cursor: pointer;\n  transition: all 0.2s ease;\n  display: flex;\n  align-items: center;\n  gap: 0.25rem;\n}\n\n.clear-search-btn:hover {\n  background: #e53e3e;\n  transform: translateY(-50%) scale(1.05);\n}\n\n.refresh-btn {\n  background: linear-gradient(135deg, #48bb78, #38a169);\n  color: white;\n  border: none;\n  padding: 0.75rem 1.5rem;\n  border-radius: 12px;\n  font-size: 0.9rem;\n  font-weight: 600;\n  cursor: pointer;\n  transition: all 0.2s ease;\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n  white-space: nowrap;\n}\n\n.refresh-btn:hover {\n  background: linear-gradient(135deg, #38a169, #2f855a);\n  transform: translateY(-2px);\n  box-shadow: 0 4px 12px rgba(72, 187, 120, 0.3);\n}\n\n/* Enhanced Video Grid - Horizontal Layout */\n.videos-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));\n  gap: 1.5rem;\n  padding: 1rem 0;\n}\n\n.video-card {\n  background: white;\n  border-radius: 16px;\n  overflow: hidden;\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);\n  transition: all 0.3s ease;\n  cursor: pointer;\n  border: 1px solid rgba(255, 255, 255, 0.2);\n}\n\n.video-card:hover {\n  transform: translateY(-8px);\n  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);\n  border-color: #667eea;\n}\n\n.video-card-thumbnail {\n  position: relative;\n  width: 100%;\n  height: 180px;\n  overflow: hidden;\n  background: #f7fafc;\n}\n\n.thumbnail-image {\n  width: 100%;\n  height: 100%;\n  object-fit: cover;\n  transition: transform 0.3s ease;\n}\n\n.video-card:hover .thumbnail-image {\n  transform: scale(1.05);\n}\n\n.play-overlay {\n  position: absolute;\n  top: 50%;\n  left: 50%;\n  transform: translate(-50%, -50%);\n  background: rgba(0, 0, 0, 0.8);\n  border-radius: 50%;\n  width: 60px;\n  height: 60px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  transition: all 0.3s ease;\n  opacity: 0;\n}\n\n.video-card:hover .play-overlay {\n  opacity: 1;\n  transform: translate(-50%, -50%) scale(1.1);\n}\n\n.play-icon {\n  color: white;\n  font-size: 24px;\n}\n\n.video-duration {\n  position: absolute;\n  bottom: 8px;\n  right: 8px;\n  background: rgba(0, 0, 0, 0.8);\n  color: white;\n  padding: 4px 8px;\n  border-radius: 6px;\n  font-size: 0.75rem;\n  font-weight: 500;\n}\n\n.subtitle-badge {\n  position: absolute;\n  top: 8px;\n  right: 8px;\n  background: rgba(102, 126, 234, 0.9);\n  color: white;\n  padding: 4px 8px;\n  border-radius: 6px;\n  font-size: 0.7rem;\n  font-weight: 600;\n  display: flex;\n  align-items: center;\n  gap: 2px;\n}\n\n.video-card-content {\n  padding: 1.25rem;\n}\n\n.video-title {\n  font-size: 1.1rem;\n  font-weight: 600;\n  color: #2d3748;\n  margin: 0 0 0.75rem 0;\n  line-height: 1.4;\n  display: -webkit-box;\n  -webkit-line-clamp: 2;\n  -webkit-box-orient: vertical;\n  overflow: hidden;\n}\n\n.video-meta {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 0.75rem;\n}\n\n.video-subject {\n  background: linear-gradient(135deg, #667eea, #764ba2);\n  color: white;\n  padding: 4px 12px;\n  border-radius: 20px;\n  font-size: 0.8rem;\n  font-weight: 500;\n}\n\n.video-class {\n  color: #718096;\n  font-size: 0.85rem;\n  font-weight: 500;\n}\n\n.video-tags {\n  display: flex;\n  gap: 0.5rem;\n  flex-wrap: wrap;\n}\n\n.level-tag {\n  background: #e2e8f0;\n  color: #4a5568;\n  padding: 2px 8px;\n  border-radius: 12px;\n  font-size: 0.7rem;\n  font-weight: 500;\n  text-transform: capitalize;\n}\n\n.topic-tag {\n  background: #fed7d7;\n  color: #c53030;\n  padding: 2px 8px;\n  border-radius: 12px;\n  font-size: 0.7rem;\n  font-weight: 500;\n}\n\n.shared-tag {\n  background: linear-gradient(135deg, #fbbf24, #f59e0b);\n  color: white;\n  padding: 2px 8px;\n  border-radius: 12px;\n  font-size: 0.7rem;\n  font-weight: 500;\n  box-shadow: 0 2px 4px rgba(251, 191, 36, 0.3);\n}\n\n/* Comments Section */\n.comments-section {\n  background: #f8fafc;\n  border-top: 1px solid #e2e8f0;\n  padding: 1.5rem;\n  max-height: 400px;\n  overflow-y: auto;\n}\n\n.comments-title {\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n  font-size: 1.1rem;\n  font-weight: 600;\n  color: #2d3748;\n  margin: 0 0 1rem 0;\n}\n\n.comments-title svg {\n  color: #667eea;\n}\n\n.add-comment {\n  margin-bottom: 1.5rem;\n}\n\n.comment-input-container {\n  display: flex;\n  flex-direction: column;\n  gap: 0.75rem;\n}\n\n.comment-input {\n  width: 100%;\n  padding: 0.75rem;\n  border: 2px solid #e2e8f0;\n  border-radius: 8px;\n  font-size: 0.9rem;\n  font-family: inherit;\n  resize: vertical;\n  min-height: 80px;\n  transition: border-color 0.2s ease;\n}\n\n.comment-input:focus {\n  outline: none;\n  border-color: #667eea;\n  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);\n}\n\n.comment-input::placeholder {\n  color: #a0aec0;\n}\n\n.comment-submit-btn {\n  align-self: flex-end;\n  background: linear-gradient(135deg, #667eea, #764ba2);\n  color: white;\n  border: none;\n  padding: 0.5rem 1.5rem;\n  border-radius: 8px;\n  font-size: 0.9rem;\n  font-weight: 500;\n  cursor: pointer;\n  transition: all 0.2s ease;\n}\n\n.comment-submit-btn:hover:not(:disabled) {\n  background: linear-gradient(135deg, #5a67d8, #6b46c1);\n  transform: translateY(-1px);\n}\n\n.comment-submit-btn:disabled {\n  opacity: 0.5;\n  cursor: not-allowed;\n}\n\n.comments-list {\n  display: flex;\n  flex-direction: column;\n  gap: 1rem;\n}\n\n.no-comments {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  padding: 2rem;\n  color: #718096;\n  text-align: center;\n}\n\n.no-comments svg {\n  font-size: 2rem;\n  margin-bottom: 0.5rem;\n  opacity: 0.5;\n}\n\n.comment {\n  background: white;\n  border: 1px solid #e2e8f0;\n  border-radius: 12px;\n  padding: 1rem;\n  transition: box-shadow 0.2s ease;\n}\n\n.comment:hover {\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n}\n\n.comment-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 0.5rem;\n}\n\n.comment-author {\n  font-weight: 600;\n  color: #2d3748;\n  font-size: 0.9rem;\n}\n\n.comment-time {\n  font-size: 0.8rem;\n  color: #718096;\n}\n\n.comment-text {\n  color: #4a5568;\n  line-height: 1.5;\n  margin-bottom: 0.75rem;\n}\n\n.comment-actions {\n  display: flex;\n  gap: 0.5rem;\n}\n\n.reply-btn {\n  background: none;\n  border: none;\n  color: #667eea;\n  font-size: 0.8rem;\n  font-weight: 500;\n  cursor: pointer;\n  padding: 0.25rem 0.5rem;\n  border-radius: 4px;\n  transition: all 0.2s ease;\n}\n\n.reply-btn:hover {\n  background: rgba(102, 126, 234, 0.1);\n  color: #5a67d8;\n}\n\n.reply-input-container {\n  margin-top: 0.75rem;\n  padding: 0.75rem;\n  background: #f7fafc;\n  border-radius: 8px;\n  border: 1px solid #e2e8f0;\n}\n\n.reply-input {\n  width: 100%;\n  padding: 0.5rem;\n  border: 1px solid #e2e8f0;\n  border-radius: 6px;\n  font-size: 0.85rem;\n  font-family: inherit;\n  resize: vertical;\n  margin-bottom: 0.5rem;\n}\n\n.reply-input:focus {\n  outline: none;\n  border-color: #667eea;\n  box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.1);\n}\n\n.reply-actions {\n  display: flex;\n  gap: 0.5rem;\n  justify-content: flex-end;\n}\n\n.reply-submit-btn {\n  background: #667eea;\n  color: white;\n  border: none;\n  padding: 0.4rem 1rem;\n  border-radius: 6px;\n  font-size: 0.8rem;\n  font-weight: 500;\n  cursor: pointer;\n  transition: all 0.2s ease;\n}\n\n.reply-submit-btn:hover:not(:disabled) {\n  background: #5a67d8;\n}\n\n.reply-submit-btn:disabled {\n  opacity: 0.5;\n  cursor: not-allowed;\n}\n\n.reply-cancel-btn {\n  background: #e2e8f0;\n  color: #4a5568;\n  border: none;\n  padding: 0.4rem 1rem;\n  border-radius: 6px;\n  font-size: 0.8rem;\n  font-weight: 500;\n  cursor: pointer;\n  transition: all 0.2s ease;\n}\n\n.reply-cancel-btn:hover {\n  background: #cbd5e0;\n}\n\n.replies {\n  margin-top: 0.75rem;\n  padding-left: 1rem;\n  border-left: 3px solid #e2e8f0;\n}\n\n.reply {\n  background: #f7fafc;\n  border: 1px solid #e2e8f0;\n  border-radius: 8px;\n  padding: 0.75rem;\n  margin-bottom: 0.5rem;\n}\n\n.reply:last-child {\n  margin-bottom: 0;\n}\n\n.reply-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 0.4rem;\n}\n\n.reply-author {\n  font-weight: 600;\n  color: #2d3748;\n  font-size: 0.85rem;\n}\n\n.reply-time {\n  font-size: 0.75rem;\n  color: #718096;\n}\n\n.reply-text {\n  color: #4a5568;\n  line-height: 1.4;\n  font-size: 0.9rem;\n}\n\n/* Loading, Error, and Empty States */\n.loading-state {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  padding: 4rem 2rem;\n  color: white;\n  text-align: center;\n}\n\n.loading-spinner {\n  width: 50px;\n  height: 50px;\n  border: 4px solid rgba(255, 255, 255, 0.3);\n  border-top: 4px solid white;\n  border-radius: 50%;\n  animation: spin 1s linear infinite;\n  margin-bottom: 1rem;\n}\n\n@keyframes spin {\n  0% { transform: rotate(0deg); }\n  100% { transform: rotate(360deg); }\n}\n\n.loading-state p {\n  font-size: 1.1rem;\n  margin: 0;\n}\n\n.error-state {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  padding: 4rem 2rem;\n  color: white;\n  text-align: center;\n}\n\n.error-state .error-icon {\n  font-size: 4rem;\n  color: #f56565;\n  margin-bottom: 1rem;\n}\n\n.error-state h3 {\n  font-size: 1.5rem;\n  margin: 0 0 1rem 0;\n  color: white;\n}\n\n.error-state p {\n  font-size: 1rem;\n  margin: 0 0 2rem 0;\n  opacity: 0.9;\n}\n\n.retry-btn {\n  background: linear-gradient(135deg, #f56565, #e53e3e);\n  color: white;\n  border: none;\n  padding: 0.75rem 2rem;\n  border-radius: 12px;\n  font-size: 1rem;\n  font-weight: 600;\n  cursor: pointer;\n  transition: all 0.2s ease;\n}\n\n.retry-btn:hover {\n  background: linear-gradient(135deg, #e53e3e, #c53030);\n  transform: translateY(-2px);\n  box-shadow: 0 4px 12px rgba(245, 101, 101, 0.3);\n}\n\n.empty-state {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  padding: 4rem 2rem;\n  color: white;\n  text-align: center;\n}\n\n.empty-state .empty-icon {\n  font-size: 4rem;\n  color: rgba(255, 255, 255, 0.6);\n  margin-bottom: 1.5rem;\n}\n\n.empty-state h3 {\n  font-size: 1.5rem;\n  margin: 0 0 1rem 0;\n  color: white;\n}\n\n.empty-state p {\n  font-size: 1rem;\n  margin: 0 0 0.5rem 0;\n  opacity: 0.9;\n}\n\n.empty-state .suggestion {\n  font-size: 0.9rem;\n  opacity: 0.7;\n  font-style: italic;\n}\n\n/* Enhanced Responsive Design */\n@media (max-width: 1200px) {\n  .videos-grid {\n    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));\n  }\n}\n\n@media (max-width: 1024px) {\n  .header-content {\n    flex-direction: column;\n    text-align: center;\n    gap: 1.5rem;\n  }\n\n  .level-display {\n    justify-content: center;\n    flex-wrap: wrap;\n  }\n\n  .controls-row {\n    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n    gap: 0.75rem;\n  }\n\n  .videos-grid {\n    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));\n    gap: 1rem;\n  }\n\n  .video-card-thumbnail {\n    height: 160px;\n  }\n\n  .comments-section {\n    max-height: 300px;\n  }\n}\n\n@media (max-width: 768px) {\n  .video-lessons-header {\n    padding: 1.5rem 0;\n  }\n\n  .header-content {\n    padding: 0 1rem;\n  }\n\n  .header-text h1 {\n    font-size: 2rem;\n  }\n\n  .level-display {\n    flex-direction: column;\n    gap: 1rem;\n    width: 100%;\n  }\n\n  .current-level, .current-class {\n    text-align: center;\n  }\n\n  .video-lessons-content {\n    padding: 0 1rem 2rem 1rem;\n  }\n\n  .video-controls {\n    padding: 1rem;\n  }\n\n  .controls-row {\n    grid-template-columns: 1fr;\n    gap: 0.75rem;\n  }\n\n  .search-row {\n    flex-direction: column;\n    gap: 0.75rem;\n  }\n\n  .search-container {\n    min-width: auto;\n  }\n\n  .refresh-btn {\n    width: 100%;\n    justify-content: center;\n  }\n\n  .videos-grid {\n    grid-template-columns: 1fr;\n    gap: 1rem;\n  }\n\n  .video-card-thumbnail {\n    height: 200px;\n  }\n\n  .comments-section {\n    padding: 1rem;\n    max-height: 250px;\n  }\n\n  .comment-input-container {\n    gap: 0.5rem;\n  }\n\n  .reply-actions {\n    flex-direction: column;\n    gap: 0.25rem;\n  }\n\n  .reply-submit-btn, .reply-cancel-btn {\n    width: 100%;\n  }\n}\n\n@media (max-width: 480px) {\n  .header-main {\n    flex-direction: column;\n    gap: 1rem;\n  }\n\n  .header-icon {\n    width: 50px;\n    height: 50px;\n    font-size: 24px;\n  }\n\n  .header-text h1 {\n    font-size: 1.75rem;\n  }\n\n  .header-text p {\n    font-size: 1rem;\n  }\n\n  .video-controls {\n    padding: 0.75rem;\n  }\n\n  .search-input {\n    padding: 0.75rem 1rem 0.75rem 2.5rem;\n    font-size: 0.9rem;\n  }\n\n  .search-icon {\n    left: 0.75rem;\n  }\n\n  .clear-search-btn {\n    position: static;\n    transform: none;\n    margin-top: 0.5rem;\n    width: 100%;\n    font-size: 0.8rem;\n  }\n\n  .video-card-content {\n    padding: 0.75rem;\n  }\n\n  .video-title {\n    font-size: 0.95rem;\n  }\n\n  .video-meta {\n    flex-direction: column;\n    align-items: flex-start;\n    gap: 0.25rem;\n  }\n\n  .video-tags {\n    flex-wrap: wrap;\n    gap: 0.25rem;\n  }\n\n  .comments-section {\n    padding: 0.75rem;\n    max-height: 200px;\n  }\n\n  .comments-title {\n    font-size: 1rem;\n  }\n\n  .comment-input {\n    min-height: 60px;\n    font-size: 0.85rem;\n  }\n\n  .comment {\n    padding: 0.75rem;\n  }\n\n  .comment-header {\n    flex-direction: column;\n    align-items: flex-start;\n    gap: 0.25rem;\n  }\n\n  .comment-text {\n    font-size: 0.9rem;\n  }\n}\n\n/* Video grid specific styles */\n.video-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));\n  gap: 1.5rem;\n  padding: 1rem 0;\n}\n\n.video-card {\n  background: white;\n  border-radius: 12px;\n  overflow: hidden;\n  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);\n  transition: all 0.3s ease;\n  cursor: pointer;\n}\n\n.video-card:hover {\n  transform: translateY(-4px);\n  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);\n}\n\n.video-thumbnail-container {\n  position: relative;\n  width: 100%;\n  height: 200px;\n  overflow: hidden;\n  cursor: pointer;\n}\n\n.video-thumbnail {\n  width: 100%;\n  height: 100%;\n  object-fit: cover;\n  transition: transform 0.3s ease;\n}\n\n.video-thumbnail-container:hover .video-thumbnail {\n  transform: scale(1.05);\n}\n\n.video-play-overlay {\n  position: absolute;\n  top: 50%;\n  left: 50%;\n  transform: translate(-50%, -50%);\n  background: rgba(0, 0, 0, 0.7);\n  border-radius: 50%;\n  width: 60px;\n  height: 60px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  transition: all 0.3s ease;\n}\n\n.video-play-overlay:hover {\n  background: rgba(0, 0, 0, 0.8);\n  transform: translate(-50%, -50%) scale(1.1);\n}\n\n.play-icon {\n  color: white;\n  font-size: 24px;\n}\n\n.video-duration-badge {\n  position: absolute;\n  bottom: 8px;\n  right: 8px;\n  background: rgba(0, 0, 0, 0.8);\n  color: white;\n  padding: 4px 8px;\n  border-radius: 4px;\n  font-size: 0.8rem;\n  font-weight: 500;\n}\n\n.video-card-content {\n  padding: 1rem;\n}\n\n.video-card-title {\n  font-size: 1.1rem;\n  font-weight: 600;\n  margin-bottom: 0.5rem;\n  color: #2d3748;\n  line-height: 1.4;\n  display: -webkit-box;\n  -webkit-line-clamp: 2;\n  -webkit-box-orient: vertical;\n  overflow: hidden;\n}\n\n.video-card-meta {\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n  margin-bottom: 0.5rem;\n  color: #667eea;\n  font-size: 0.9rem;\n  font-weight: 500;\n}\n\n.video-card-info {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 1rem;\n}\n\n.video-card-subject {\n  background: #e2e8f0;\n  color: #4a5568;\n  padding: 4px 8px;\n  border-radius: 4px;\n  font-size: 0.8rem;\n  font-weight: 500;\n}\n\n.video-card-class {\n  color: #718096;\n  font-size: 0.8rem;\n}\n\n/* Video modal styles */\n.video-overlay {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: rgba(0, 0, 0, 0.9);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  z-index: 1000;\n  padding: 2rem;\n}\n\n.video-overlay.expanded {\n  padding: 0;\n}\n\n.video-modal {\n  background: white;\n  border-radius: 12px;\n  overflow: hidden;\n  max-width: 90vw;\n  max-height: 90vh;\n  width: 100%;\n  position: relative;\n}\n\n.video-modal.expanded {\n  max-width: 100vw;\n  max-height: 100vh;\n  border-radius: 0;\n}\n\n.video-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 1rem;\n  background: #f7fafc;\n  border-bottom: 1px solid #e2e8f0;\n}\n\n.video-info h3 {\n  margin: 0;\n  color: #2d3748;\n  font-size: 1.2rem;\n  font-weight: 600;\n}\n\n.video-meta {\n  display: flex;\n  gap: 1rem;\n  margin-top: 0.5rem;\n  font-size: 0.9rem;\n  color: #718096;\n}\n\n.video-controls {\n  display: flex;\n  gap: 0.5rem;\n}\n\n.control-btn {\n  background: none;\n  border: none;\n  padding: 0.5rem;\n  border-radius: 6px;\n  cursor: pointer;\n  color: #4a5568;\n  transition: all 0.2s ease;\n}\n\n.control-btn:hover {\n  background: #e2e8f0;\n  color: #2d3748;\n}\n\n.video-container {\n  position: relative;\n  background: #000;\n}\n\n.video-iframe {\n  width: 100%;\n  height: 400px;\n  border: none;\n}\n\n.video-modal.expanded .video-iframe {\n  height: calc(100vh - 80px);\n}\n\n.subtitle-indicator {\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n  padding: 0.5rem;\n  background: rgba(0, 0, 0, 0.7);\n  color: white;\n  font-size: 0.8rem;\n  border-radius: 4px;\n  margin-top: 0.5rem;\n}\n\n.subtitle-icon {\n  font-size: 1rem;\n}\n\n.video-error-overlay {\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: rgba(0, 0, 0, 0.8);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  color: white;\n  text-align: center;\n}\n\n.error-content {\n  padding: 2rem;\n}\n\n.error-content .error-icon {\n  font-size: 2rem;\n  margin-bottom: 1rem;\n  color: #f56565;\n}\n\n.dismiss-error-btn {\n  background: #667eea;\n  color: white;\n  border: none;\n  padding: 0.5rem 1rem;\n  border-radius: 6px;\n  cursor: pointer;\n  margin-top: 1rem;\n  transition: background 0.2s ease;\n}\n\n.dismiss-error-btn:hover {\n  background: #5a67d8;\n}\n\n.video-error {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  padding: 3rem;\n  text-align: center;\n  color: white;\n  min-height: 400px;\n}\n\n.video-error .error-icon {\n  font-size: 3rem;\n  margin-bottom: 1rem;\n}\n\n.video-error h3 {\n  margin-bottom: 1rem;\n  color: white;\n}\n\n.external-link-btn {\n  background: #667eea;\n  color: white;\n  text-decoration: none;\n  padding: 0.75rem 1.5rem;\n  border-radius: 6px;\n  display: inline-block;\n  margin-top: 1rem;\n  transition: background 0.2s ease;\n}\n\n.external-link-btn:hover {\n  background: #5a67d8;\n  color: white;\n  text-decoration: none;\n}\n\n/* Responsive design */\n@media (max-width: 768px) {\n  .video-grid {\n    grid-template-columns: 1fr;\n    gap: 1rem;\n  }\n  \n  .video-overlay {\n    padding: 1rem;\n  }\n  \n  .video-header {\n    flex-direction: column;\n    align-items: flex-start;\n    gap: 1rem;\n  }\n  \n  .video-meta {\n    flex-direction: column;\n    gap: 0.5rem;\n  }\n  \n  .video-iframe {\n    height: 250px;\n  }\n  \n  .video-modal.expanded .video-iframe {\n    height: calc(100vh - 120px);\n  }\n}\n"], "names": [], "sourceRoot": ""}