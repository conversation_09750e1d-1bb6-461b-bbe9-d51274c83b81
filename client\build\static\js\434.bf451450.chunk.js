(self.webpackChunkclient=self.webpackChunkclient||[]).push([[434],{1046:(e,t,n)=>{"use strict";n.d(t,{Z:()=>b});var o=n(1694),r=n.n(o),l=n(5501),i=n(2791),a=n(9911),c=n(1929),s=n(11);const d=i.createContext({latestIndex:0,horizontalSize:0,verticalSize:0,supportFlexGap:!1}),u=d.Provider,p=e=>{let{className:t,direction:n,index:o,marginDirection:r,children:l,split:a,wrap:c,style:s}=e;const{horizontalSize:u,verticalSize:p,latestIndex:f,supportFlexGap:m}=i.useContext(d);let g={};return m||("vertical"===n?o<f&&(g={marginBottom:u/(a?2:1)}):g=Object.assign(Object.assign({},o<f&&{[r]:u/(a?2:1)}),c&&{paddingBottom:p})),null===l||void 0===l?null:i.createElement(i.Fragment,null,i.createElement("div",{className:t,style:Object.assign(Object.assign({},g),s)},l),o<f&&a&&i.createElement("span",{className:"".concat(t,"-split"),style:g},a))};var f=n(1294),m=function(e,t){var n={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&t.indexOf(o)<0&&(n[o]=e[o]);if(null!=e&&"function"===typeof Object.getOwnPropertySymbols){var r=0;for(o=Object.getOwnPropertySymbols(e);r<o.length;r++)t.indexOf(o[r])<0&&Object.prototype.propertyIsEnumerable.call(e,o[r])&&(n[o[r]]=e[o[r]])}return n};const g={small:8,middle:16,large:24};const v=i.forwardRef(((e,t)=>{var n,o;const{getPrefixCls:s,space:d,direction:v}=i.useContext(c.E_),{size:y=(null===d||void 0===d?void 0:d.size)||"small",align:b,className:h,rootClassName:x,children:O,direction:w="horizontal",prefixCls:E,split:S,style:j,wrap:C=!1,classNames:k,styles:z}=e,R=m(e,["size","align","className","rootClassName","children","direction","prefixCls","split","style","wrap","classNames","styles"]),D=(0,a.Z)(),[N,P]=i.useMemo((()=>(Array.isArray(y)?y:[y,y]).map((e=>function(e){return"string"===typeof e?g[e]:e||0}(e)))),[y]),T=(0,l.Z)(O,{keepEmpty:!0}),Z=void 0===b&&"horizontal"===w?"center":b,I=s("space",E),[M,H]=(0,f.Z)(I),B=r()(I,null===d||void 0===d?void 0:d.className,H,"".concat(I,"-").concat(w),{["".concat(I,"-rtl")]:"rtl"===v,["".concat(I,"-align-").concat(Z)]:Z},h,x),A=r()("".concat(I,"-item"),null!==(n=null===k||void 0===k?void 0:k.item)&&void 0!==n?n:null===(o=null===d||void 0===d?void 0:d.classNames)||void 0===o?void 0:o.item),L="rtl"===v?"marginLeft":"marginRight";let W=0;const F=T.map(((e,t)=>{var n,o;null!==e&&void 0!==e&&(W=t);const r=e&&e.key||"".concat(A,"-").concat(t);return i.createElement(p,{className:A,key:r,direction:w,index:t,marginDirection:L,split:S,wrap:C,style:null!==(n=null===z||void 0===z?void 0:z.item)&&void 0!==n?n:null===(o=null===d||void 0===d?void 0:d.styles)||void 0===o?void 0:o.item},e)})),G=i.useMemo((()=>({horizontalSize:N,verticalSize:P,latestIndex:W,supportFlexGap:D})),[N,P,W,D]);if(0===T.length)return null;const K={};return C&&(K.flexWrap="wrap",D||(K.marginBottom=-P)),D&&(K.columnGap=N,K.rowGap=P),M(i.createElement("div",Object.assign({ref:t,className:B,style:Object.assign(Object.assign(Object.assign({},K),null===d||void 0===d?void 0:d.style),j)},R),i.createElement(u,{value:G},F)))}));const y=v;y.Compact=s.ZP;const b=y},1325:(e,t,n)=>{"use strict";n.d(t,{N:()=>o});const o=e=>({color:e.colorLink,textDecoration:"none",outline:"none",cursor:"pointer",transition:"color ".concat(e.motionDurationSlow),"&:focus, &:hover":{color:e.colorLinkHover},"&:active":{color:e.colorLinkActive}})},3655:(e,t,n)=>{"use strict";n.d(t,{default:()=>he});var o=n(2791),r=n(7575),l=n(7462);const i={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M832 64H296c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h496v688c0 4.4 3.6 8 8 8h56c4.4 0 8-3.6 8-8V96c0-17.7-14.3-32-32-32zM704 192H192c-17.7 0-32 14.3-32 32v530.7c0 8.5 3.4 16.6 9.4 22.6l173.3 173.3c2.2 2.2 4.7 4 7.4 5.5v1.9h4.2c3.5 1.3 7.2 2 11 2H704c17.7 0 32-14.3 32-32V224c0-17.7-14.3-32-32-32zM350 856.2L263.9 770H350v86.2zM664 888H414V746c0-22.1-17.9-40-40-40H232V264h432v624z"}}]},name:"copy",theme:"outlined"};var a=n(4291),c=function(e,t){return o.createElement(a.Z,(0,l.Z)({},e,{ref:t,icon:i}))};const s=o.forwardRef(c);const d={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M257.7 752c2 0 4-.2 6-.5L431.9 722c2-.4 3.9-1.3 5.3-2.8l423.9-423.9a9.96 9.96 0 000-14.1L694.9 114.9c-1.9-1.9-4.4-2.9-7.1-2.9s-5.2 1-7.1 2.9L256.8 538.8c-1.5 1.5-2.4 3.3-2.8 5.3l-29.5 168.2a33.5 33.5 0 009.4 29.8c6.6 6.4 14.9 9.9 23.8 9.9zm67.4-174.4L687.8 215l73.3 73.3-362.7 362.6-88.9 15.7 15.6-89zM880 836H144c-17.7 0-32 14.3-32 32v36c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-36c0-17.7-14.3-32-32-32z"}}]},name:"edit",theme:"outlined"};var u=function(e,t){return o.createElement(a.Z,(0,l.Z)({},e,{ref:t,icon:d}))};const p=o.forwardRef(u);var f=n(1694),m=n.n(f),g=n(6998),v=n.n(g),y=n(1143),b=n(5501),h=n(1605),x=n(5179),O=n(1818),w=n(8834),E=n(2748),S=n(1354),j=function(e,t){var n={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&t.indexOf(o)<0&&(n[o]=e[o]);if(null!=e&&"function"===typeof Object.getOwnPropertySymbols){var r=0;for(o=Object.getOwnPropertySymbols(e);r<o.length;r++)t.indexOf(o[r])<0&&Object.prototype.propertyIsEnumerable.call(e,o[r])&&(n[o[r]]=e[o[r]])}return n};const C={border:0,background:"transparent",padding:0,lineHeight:"inherit",display:"inline-block"},k=o.forwardRef(((e,t)=>{const{style:n,noStyle:r,disabled:l}=e,i=j(e,["style","noStyle","disabled"]);let a={};return r||(a=Object.assign({},C)),l&&(a.pointerEvents="none"),a=Object.assign(Object.assign({},a),n),o.createElement("div",Object.assign({role:"button",tabIndex:0,ref:t},i,{onKeyDown:e=>{const{keyCode:t}=e;t===S.Z.ENTER&&e.preventDefault()},onKeyUp:t=>{const{keyCode:n}=t,{onClick:o}=e;n===S.Z.ENTER&&o&&o()},style:a}))})),z=k;var R=n(1929),D=n(4e3),N=n(2879);const P={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M864 170h-60c-4.4 0-8 3.6-8 8v518H310v-73c0-6.7-7.8-10.5-13-6.3l-141.9 112a8 8 0 000 12.6l141.9 112c5.3 4.2 13 .4 13-6.3v-75h498c35.3 0 64-28.7 64-64V178c0-4.4-3.6-8-8-8z"}}]},name:"enter",theme:"outlined"};var T=function(e,t){return o.createElement(a.Z,(0,l.Z)({},e,{ref:t,icon:P}))};const Z=o.forwardRef(T);var I=n(1113),M=n(6641),H=n(1325),B=n(5564),A=n(3742),L=n(6264);const W=e=>{const t={};return[1,2,3,4,5].forEach((n=>{t["\n      h".concat(n,"&,\n      div&-h").concat(n,",\n      div&-h").concat(n," > textarea,\n      h").concat(n,"\n    ")]=((e,t,n,o)=>{const{titleMarginBottom:r,fontWeightStrong:l}=o;return{marginBottom:r,color:n,fontWeight:l,fontSize:e,lineHeight:t}})(e["fontSizeHeading".concat(n)],e["lineHeightHeading".concat(n)],e.colorTextHeading,e)})),t},F=e=>{const{componentCls:t}=e;return{"a&, a":Object.assign(Object.assign({},(0,H.N)(e)),{textDecoration:e.linkDecoration,"&:active, &:hover":{textDecoration:e.linkHoverDecoration},["&[disabled], &".concat(t,"-disabled")]:{color:e.colorTextDisabled,cursor:"not-allowed","&:active, &:hover":{color:e.colorTextDisabled},"&:active":{pointerEvents:"none"}}})}},G=e=>({code:{margin:"0 0.2em",paddingInline:"0.4em",paddingBlock:"0.2em 0.1em",fontSize:"85%",fontFamily:e.fontFamilyCode,background:"rgba(150, 150, 150, 0.1)",border:"1px solid rgba(100, 100, 100, 0.2)",borderRadius:3},kbd:{margin:"0 0.2em",paddingInline:"0.4em",paddingBlock:"0.15em 0.1em",fontSize:"90%",fontFamily:e.fontFamilyCode,background:"rgba(150, 150, 150, 0.06)",border:"1px solid rgba(100, 100, 100, 0.2)",borderBottomWidth:2,borderRadius:3},mark:{padding:0,backgroundColor:A.EV[2]},"u, ins":{textDecoration:"underline",textDecorationSkipInk:"auto"},"s, del":{textDecoration:"line-through"},strong:{fontWeight:600},"ul, ol":{marginInline:0,marginBlock:"0 1em",padding:0,li:{marginInline:"20px 0",marginBlock:0,paddingInline:"4px 0",paddingBlock:0}},ul:{listStyleType:"circle",ul:{listStyleType:"disc"}},ol:{listStyleType:"decimal"},"pre, blockquote":{margin:"1em 0"},pre:{padding:"0.4em 0.6em",whiteSpace:"pre-wrap",wordWrap:"break-word",background:"rgba(150, 150, 150, 0.1)",border:"1px solid rgba(100, 100, 100, 0.2)",borderRadius:3,fontFamily:e.fontFamilyCode,code:{display:"inline",margin:0,padding:0,fontSize:"inherit",fontFamily:"inherit",background:"transparent",border:0}},blockquote:{paddingInline:"0.6em 0",paddingBlock:0,borderInlineStart:"4px solid rgba(100, 100, 100, 0.2)",opacity:.85}}),K=e=>{const{componentCls:t}=e,n=(0,L.e5)(e).inputPaddingVertical+1;return{"&-edit-content":{position:"relative","div&":{insetInlineStart:-e.paddingSM,marginTop:-n,marginBottom:"calc(1em - ".concat(n,"px)")},["".concat(t,"-edit-content-confirm")]:{position:"absolute",insetInlineEnd:e.marginXS+2,insetBlockEnd:e.marginXS,color:e.colorTextDescription,fontWeight:"normal",fontSize:e.fontSize,fontStyle:"normal",pointerEvents:"none"},textarea:{margin:"0!important",MozTransition:"none",height:"1em"}}}},U=e=>({"&-copy-success":{"\n    &,\n    &:hover,\n    &:focus":{color:e.colorSuccess}}}),V=e=>{const{componentCls:t,titleMarginTop:n}=e;return{[t]:Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({color:e.colorText,wordBreak:"break-word",lineHeight:e.lineHeight,["&".concat(t,"-secondary")]:{color:e.colorTextDescription},["&".concat(t,"-success")]:{color:e.colorSuccess},["&".concat(t,"-warning")]:{color:e.colorWarning},["&".concat(t,"-danger")]:{color:e.colorError,"a&:active, a&:focus":{color:e.colorErrorActive},"a&:hover":{color:e.colorErrorHover}},["&".concat(t,"-disabled")]:{color:e.colorTextDisabled,cursor:"not-allowed",userSelect:"none"},"\n        div&,\n        p\n      ":{marginBottom:"1em"}},W(e)),{["\n      & + h1".concat(t,",\n      & + h2").concat(t,",\n      & + h3").concat(t,",\n      & + h4").concat(t,",\n      & + h5").concat(t,"\n      ")]:{marginTop:n},"\n      div,\n      ul,\n      li,\n      p,\n      h1,\n      h2,\n      h3,\n      h4,\n      h5":{"\n        + h1,\n        + h2,\n        + h3,\n        + h4,\n        + h5\n        ":{marginTop:n}}}),G(e)),F(e)),{["\n        ".concat(t,"-expand,\n        ").concat(t,"-edit,\n        ").concat(t,"-copy\n      ")]:Object.assign(Object.assign({},(0,H.N)(e)),{marginInlineStart:e.marginXXS})}),K(e)),U(e)),{"\n  a&-ellipsis,\n  span&-ellipsis\n  ":{display:"inline-block",maxWidth:"100%"},"&-single-line":{whiteSpace:"nowrap"},"&-ellipsis-single-line":{overflow:"hidden",textOverflow:"ellipsis","a&, span&":{verticalAlign:"bottom"}},"&-ellipsis-multiple-line":{display:"-webkit-box",overflow:"hidden",WebkitLineClamp:3,WebkitBoxOrient:"vertical"}}),{"&-rtl":{direction:"rtl"}})}},X=(0,B.Z)("Typography",(e=>[V(e)]),(()=>({titleMarginTop:"1.2em",titleMarginBottom:"0.5em"}))),_=e=>{const{prefixCls:t,"aria-label":n,className:r,style:l,direction:i,maxLength:a,autoSize:c=!0,value:s,onSave:d,onCancel:u,onEnd:p,component:f,enterIcon:g=o.createElement(Z,null)}=e,v=o.useRef(null),y=o.useRef(!1),b=o.useRef(),[h,x]=o.useState(s);o.useEffect((()=>{x(s)}),[s]),o.useEffect((()=>{if(v.current&&v.current.resizableTextArea){const{textArea:e}=v.current.resizableTextArea;e.focus();const{length:t}=e.value;e.setSelectionRange(t,t)}}),[]);const O=()=>{d(h.trim())},w=f?"".concat(t,"-").concat(f):"",[E,j]=X(t),C=m()(t,"".concat(t,"-edit-content"),{["".concat(t,"-rtl")]:"rtl"===i},r,w,j);return E(o.createElement("div",{className:C,style:l},o.createElement(M.Z,{ref:v,maxLength:a,value:h,onChange:e=>{let{target:t}=e;x(t.value.replace(/[\n\r]/g,""))},onKeyDown:e=>{let{keyCode:t}=e;y.current||(b.current=t)},onKeyUp:e=>{let{keyCode:t,ctrlKey:n,altKey:o,metaKey:r,shiftKey:l}=e;b.current!==t||y.current||n||o||r||l||(t===S.Z.ENTER?(O(),null===p||void 0===p||p()):t===S.Z.ESC&&u())},onCompositionStart:()=>{y.current=!0},onCompositionEnd:()=>{y.current=!1},onBlur:()=>{O()},"aria-label":n,rows:1,autoSize:c}),null!==g?(0,I.Tm)(g,{className:"".concat(t,"-edit-content-confirm")}):null))};var q=function(e,t){var n={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&t.indexOf(o)<0&&(n[o]=e[o]);if(null!=e&&"function"===typeof Object.getOwnPropertySymbols){var r=0;for(o=Object.getOwnPropertySymbols(e);r<o.length;r++)t.indexOf(o[r])<0&&Object.prototype.propertyIsEnumerable.call(e,o[r])&&(n[o[r]]=e[o[r]])}return n};const Q=o.forwardRef(((e,t)=>{const{prefixCls:n,component:r="article",className:l,rootClassName:i,setContentRef:a,children:c,direction:s,style:d}=e,u=q(e,["prefixCls","component","className","rootClassName","setContentRef","children","direction","style"]),{getPrefixCls:p,direction:f,typography:g}=o.useContext(R.E_),v=null!==s&&void 0!==s?s:f;let y=t;a&&(y=(0,w.sQ)(t,a));const b=p("typography",n),[h,x]=X(b),O=m()(b,null===g||void 0===g?void 0:g.className,{["".concat(b,"-rtl")]:"rtl"===v},l,i,x),E=Object.assign(Object.assign({},null===g||void 0===g?void 0:g.style),d);return h(o.createElement(r,Object.assign({className:O,style:E,ref:y},u),c))}));const J=Q;function Y(e,t){return o.useMemo((()=>{const n=!!e;return[n,Object.assign(Object.assign({},t),n&&"object"===typeof e?e:null)]}),[e])}const $=(e,t)=>{const n=o.useRef(!1);o.useEffect((()=>{n.current?e():n.current=!0}),t)};function ee(e){const t=typeof e;return"string"===t||"number"===t}function te(e,t){let n=0;const o=[];for(let r=0;r<e.length;r+=1){if(n===t)return o;const l=e[r],i=n+(ee(l)?String(l).length:1);if(i>t){const e=t-n;return o.push(String(l).slice(0,e)),o}o.push(l),n=i}return e}const ne=e=>{let{enabledMeasure:t,children:n,text:r,width:l,fontSize:i,rows:a,onEllipsis:c}=e;const[[s,d,u],p]=o.useState([0,0,0]),[f,m]=o.useState(0),[g,v]=o.useState(0),y=o.useRef(null),x=o.useRef(null),O=o.useMemo((()=>(0,b.Z)(r)),[r]),w=o.useMemo((()=>function(e){let t=0;return e.forEach((e=>{ee(e)?t+=String(e).length:t+=1})),t}(O)),[O]),E=o.useMemo((()=>t&&3===f?n(te(O,d),d<w):n(O,!1)),[t,f,n,O,d,w]);(0,h.Z)((()=>{t&&l&&i&&w&&(m(1),p([0,Math.ceil(w/2),w]))}),[t,l,i,r,w,a]),(0,h.Z)((()=>{var e;1===f&&v((null===(e=y.current)||void 0===e?void 0:e.offsetHeight)||0)}),[f]),(0,h.Z)((()=>{var e,t;if(g)if(1===f){((null===(e=x.current)||void 0===e?void 0:e.offsetHeight)||0)<=a*g?(m(4),c(!1)):m(2)}else if(2===f)if(s!==u){const e=(null===(t=x.current)||void 0===t?void 0:t.offsetHeight)||0;let n=s,o=u;s===u-1?o=s:e<=a*g?n=d:o=d;const r=Math.ceil((n+o)/2);p([n,r,o])}else m(3),c(!0)}),[f,s,u,a,g]);const S={width:l,whiteSpace:"normal",margin:0,padding:0},j=(e,t,n)=>o.createElement("span",{"aria-hidden":!0,ref:t,style:Object.assign({position:"fixed",display:"block",left:0,top:0,zIndex:-9999,visibility:"hidden",pointerEvents:"none",fontSize:2*Math.floor(i/2)},n)},e);return o.createElement(o.Fragment,null,E,t&&3!==f&&4!==f&&o.createElement(o.Fragment,null,j("lg",y,{wordBreak:"keep-all",whiteSpace:"nowrap"}),1===f?j(n(O,!1),x,S):((e,t)=>{const o=te(O,e);return j(n(o,!0),t,S)})(d,x)))};const oe=e=>{let{enabledEllipsis:t,isEllipsis:n,children:r,tooltipProps:l}=e;return(null===l||void 0===l?void 0:l.title)&&t?o.createElement(N.Z,Object.assign({open:!!n&&void 0},l),r):r};var re=function(e,t){var n={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&t.indexOf(o)<0&&(n[o]=e[o]);if(null!=e&&"function"===typeof Object.getOwnPropertySymbols){var r=0;for(o=Object.getOwnPropertySymbols(e);r<o.length;r++)t.indexOf(o[r])<0&&Object.prototype.propertyIsEnumerable.call(e,o[r])&&(n[o[r]]=e[o[r]])}return n};function le(e,t,n){return!0===e||void 0===e?t:e||n&&t}function ie(e){return!1===e?[!1,!1]:Array.isArray(e)?e:[e]}const ae=o.forwardRef(((e,t)=>{var n,l,i;const{prefixCls:a,className:c,style:d,type:u,disabled:f,children:g,ellipsis:S,editable:j,copyable:C,component:k,title:P}=e,T=re(e,["prefixCls","className","style","type","disabled","children","ellipsis","editable","copyable","component","title"]),{getPrefixCls:Z,direction:I}=o.useContext(R.E_),[M]=(0,D.Z)("Text"),H=o.useRef(null),B=o.useRef(null),A=Z("typography",a),L=(0,O.Z)(T,["mark","code","delete","underline","strong","keyboard","italic"]),[W,F]=Y(j),[G,K]=(0,x.Z)(!1,{value:F.editing}),{triggerType:U=["icon"]}=F,V=e=>{var t;e&&(null===(t=F.onStart)||void 0===t||t.call(F)),K(e)};$((()=>{var e;G||null===(e=B.current)||void 0===e||e.focus()}),[G]);const X=e=>{null===e||void 0===e||e.preventDefault(),V(!0)},q=e=>{var t;null===(t=F.onChange)||void 0===t||t.call(F,e),V(!1)},Q=()=>{var e;null===(e=F.onCancel)||void 0===e||e.call(F),V(!1)},[ee,te]=Y(C),[ae,ce]=o.useState(!1),se=o.useRef(null),de={};te.format&&(de.format=te.format);const ue=()=>{se.current&&clearTimeout(se.current)},pe=e=>{var t;null===e||void 0===e||e.preventDefault(),null===e||void 0===e||e.stopPropagation(),v()(te.text||String(g)||"",de),ce(!0),ue(),se.current=setTimeout((()=>{ce(!1)}),3e3),null===(t=te.onCopy)||void 0===t||t.call(te,e)};o.useEffect((()=>ue),[]);const[fe,me]=o.useState(!1),[ge,ve]=o.useState(!1),[ye,be]=o.useState(!1),[he,xe]=o.useState(!1),[Oe,we]=o.useState(!1),[Ee,Se]=o.useState(!0),[je,Ce]=Y(S,{expandable:!1}),ke=je&&!ye,{rows:ze=1}=Ce,Re=o.useMemo((()=>!ke||void 0!==Ce.suffix||Ce.onEllipsis||Ce.expandable||W||ee),[ke,Ce,W,ee]);(0,h.Z)((()=>{je&&!Re&&(me((0,E.G)("webkitLineClamp")),ve((0,E.G)("textOverflow")))}),[Re,je]);const De=o.useMemo((()=>!Re&&(1===ze?ge:fe)),[Re,ge,fe]),Ne=ke&&(De?Oe:he),Pe=ke&&1===ze&&De,Te=ke&&ze>1&&De,Ze=e=>{var t;be(!0),null===(t=Ce.onExpand)||void 0===t||t.call(Ce,e)},[Ie,Me]=o.useState(0),[He,Be]=o.useState(0),Ae=e=>{var t;xe(e),he!==e&&(null===(t=Ce.onEllipsis)||void 0===t||t.call(Ce,e))};o.useEffect((()=>{const e=H.current;if(je&&De&&e){const t=Te?e.offsetHeight<e.scrollHeight:e.offsetWidth<e.scrollWidth;Oe!==t&&we(t)}}),[je,De,g,Te,Ee]),o.useEffect((()=>{const e=H.current;if("undefined"===typeof IntersectionObserver||!e||!De||!ke)return;const t=new IntersectionObserver((()=>{Se(!!e.offsetParent)}));return t.observe(e),()=>{t.disconnect()}}),[De,ke]);let Le={};Le=!0===Ce.tooltip?{title:null!==(n=F.text)&&void 0!==n?n:g}:o.isValidElement(Ce.tooltip)?{title:Ce.tooltip}:"object"===typeof Ce.tooltip?Object.assign({title:null!==(l=F.text)&&void 0!==l?l:g},Ce.tooltip):{title:Ce.tooltip};const We=o.useMemo((()=>{const e=e=>["string","number"].includes(typeof e);if(je&&!De)return e(F.text)?F.text:e(g)?g:e(P)?P:e(Le.title)?Le.title:void 0}),[je,De,P,Le.title,Ne]);if(G)return o.createElement(_,{value:null!==(i=F.text)&&void 0!==i?i:"string"===typeof g?g:"",onSave:q,onCancel:Q,onEnd:F.onEnd,prefixCls:A,className:c,style:d,direction:I,component:k,maxLength:F.maxLength,autoSize:F.autoSize,enterIcon:F.enterIcon});const Fe=()=>{const{expandable:e,symbol:t}=Ce;if(!e)return null;let n;return n=t||(null===M||void 0===M?void 0:M.expand),o.createElement("a",{key:"expand",className:"".concat(A,"-expand"),onClick:Ze,"aria-label":null===M||void 0===M?void 0:M.expand},n)},Ge=()=>{if(!W)return;const{icon:e,tooltip:t}=F,n=(0,b.Z)(t)[0]||(null===M||void 0===M?void 0:M.edit),r="string"===typeof n?n:"";return U.includes("icon")?o.createElement(N.Z,{key:"edit",title:!1===t?"":n},o.createElement(z,{ref:B,className:"".concat(A,"-edit"),onClick:X,"aria-label":r},e||o.createElement(p,{role:"button"}))):null},Ke=()=>{if(!ee)return;const{tooltips:e,icon:t}=te,n=ie(e),l=ie(t),i=ae?le(n[1],null===M||void 0===M?void 0:M.copied):le(n[0],null===M||void 0===M?void 0:M.copy),a=ae?null===M||void 0===M?void 0:M.copied:null===M||void 0===M?void 0:M.copy,c="string"===typeof i?i:a;return o.createElement(N.Z,{key:"copy",title:i},o.createElement(z,{className:m()("".concat(A,"-copy"),ae&&"".concat(A,"-copy-success")),onClick:pe,"aria-label":c},ae?le(l[1],o.createElement(r.Z,null),!0):le(l[0],o.createElement(s,null),!0)))};return o.createElement(y.Z,{onResize:(e,t)=>{let{offsetWidth:n}=e;var o;Me(n),Be(parseInt(null===(o=window.getComputedStyle)||void 0===o?void 0:o.call(window,t).fontSize,10)||0)},disabled:!ke||De},(n=>o.createElement(oe,{tooltipProps:Le,enabledEllipsis:ke,isEllipsis:Ne},o.createElement(J,Object.assign({className:m()({["".concat(A,"-").concat(u)]:u,["".concat(A,"-disabled")]:f,["".concat(A,"-ellipsis")]:je,["".concat(A,"-single-line")]:ke&&1===ze,["".concat(A,"-ellipsis-single-line")]:Pe,["".concat(A,"-ellipsis-multiple-line")]:Te},c),prefixCls:a,style:Object.assign(Object.assign({},d),{WebkitLineClamp:Te?ze:void 0}),component:k,ref:(0,w.sQ)(n,H,t),direction:I,onClick:U.includes("text")?X:void 0,"aria-label":null===We||void 0===We?void 0:We.toString(),title:P},L),o.createElement(ne,{enabledMeasure:ke&&!De,text:g,rows:ze,width:Ie,fontSize:He,onEllipsis:Ae},((t,n)=>{let r=t;t.length&&n&&We&&(r=o.createElement("span",{key:"show-content","aria-hidden":!0},r));const l=function(e,t){let{mark:n,code:r,underline:l,delete:i,strong:a,keyboard:c,italic:s}=e,d=t;function u(e,t){t&&(d=o.createElement(e,{},d))}return u("strong",a),u("u",l),u("del",i),u("code",r),u("mark",n),u("kbd",c),u("i",s),d}(e,o.createElement(o.Fragment,null,r,(e=>{return[e&&o.createElement("span",{"aria-hidden":!0,key:"ellipsis"},"..."),Ce.suffix,(t=e,[t&&Fe(),Ge(),Ke()])];var t})(n)));return l}))))))})),ce=ae;var se=function(e,t){var n={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&t.indexOf(o)<0&&(n[o]=e[o]);if(null!=e&&"function"===typeof Object.getOwnPropertySymbols){var r=0;for(o=Object.getOwnPropertySymbols(e);r<o.length;r++)t.indexOf(o[r])<0&&Object.prototype.propertyIsEnumerable.call(e,o[r])&&(n[o[r]]=e[o[r]])}return n};const de=o.forwardRef(((e,t)=>{var{ellipsis:n,rel:r}=e,l=se(e,["ellipsis","rel"]);const i=Object.assign(Object.assign({},l),{rel:void 0===r&&"_blank"===l.target?"noopener noreferrer":r});return delete i.navigate,o.createElement(ce,Object.assign({},i,{ref:t,ellipsis:!!n,component:"a"}))})),ue=o.forwardRef(((e,t)=>o.createElement(ce,Object.assign({ref:t},e,{component:"div"}))));var pe=function(e,t){var n={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&t.indexOf(o)<0&&(n[o]=e[o]);if(null!=e&&"function"===typeof Object.getOwnPropertySymbols){var r=0;for(o=Object.getOwnPropertySymbols(e);r<o.length;r++)t.indexOf(o[r])<0&&Object.prototype.propertyIsEnumerable.call(e,o[r])&&(n[o[r]]=e[o[r]])}return n};const fe=(e,t)=>{var{ellipsis:n}=e,r=pe(e,["ellipsis"]);const l=o.useMemo((()=>n&&"object"===typeof n?(0,O.Z)(n,["expandable","rows"]):n),[n]);return o.createElement(ce,Object.assign({ref:t},r,{ellipsis:l,component:"span"}))},me=o.forwardRef(fe);var ge=function(e,t){var n={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&t.indexOf(o)<0&&(n[o]=e[o]);if(null!=e&&"function"===typeof Object.getOwnPropertySymbols){var r=0;for(o=Object.getOwnPropertySymbols(e);r<o.length;r++)t.indexOf(o[r])<0&&Object.prototype.propertyIsEnumerable.call(e,o[r])&&(n[o[r]]=e[o[r]])}return n};const ve=[1,2,3,4,5],ye=o.forwardRef(((e,t)=>{const{level:n=1}=e,r=ge(e,["level"]);let l;return l=ve.includes(n)?"h".concat(n):"h1",o.createElement(ce,Object.assign({ref:t},r,{component:l}))})),be=J;be.Text=me,be.Link=de,be.Title=ye,be.Paragraph=ue;const he=be},6998:(e,t,n)=>{"use strict";var o=n(2458),r={"text/plain":"Text","text/html":"Url",default:"Text"};e.exports=function(e,t){var n,l,i,a,c,s,d=!1;t||(t={}),n=t.debug||!1;try{if(i=o(),a=document.createRange(),c=document.getSelection(),(s=document.createElement("span")).textContent=e,s.ariaHidden="true",s.style.all="unset",s.style.position="fixed",s.style.top=0,s.style.clip="rect(0, 0, 0, 0)",s.style.whiteSpace="pre",s.style.webkitUserSelect="text",s.style.MozUserSelect="text",s.style.msUserSelect="text",s.style.userSelect="text",s.addEventListener("copy",(function(o){if(o.stopPropagation(),t.format)if(o.preventDefault(),"undefined"===typeof o.clipboardData){n&&console.warn("unable to use e.clipboardData"),n&&console.warn("trying IE specific stuff"),window.clipboardData.clearData();var l=r[t.format]||r.default;window.clipboardData.setData(l,e)}else o.clipboardData.clearData(),o.clipboardData.setData(t.format,e);t.onCopy&&(o.preventDefault(),t.onCopy(o.clipboardData))})),document.body.appendChild(s),a.selectNodeContents(s),c.addRange(a),!document.execCommand("copy"))throw new Error("copy command was unsuccessful");d=!0}catch(u){n&&console.error("unable to copy using execCommand: ",u),n&&console.warn("trying IE specific stuff");try{window.clipboardData.setData(t.format||"text",e),t.onCopy&&t.onCopy(window.clipboardData),d=!0}catch(u){n&&console.error("unable to copy using clipboardData: ",u),n&&console.error("falling back to prompt"),l=function(e){var t=(/mac os x/i.test(navigator.userAgent)?"\u2318":"Ctrl")+"+C";return e.replace(/#{\s*key\s*}/g,t)}("message"in t?t.message:"Copy to clipboard: #{key}, Enter"),window.prompt(l,e)}}finally{c&&("function"==typeof c.removeRange?c.removeRange(a):c.removeAllRanges()),s&&document.body.removeChild(s),i()}return d}},2748:(e,t,n)=>{"use strict";n.d(t,{G:()=>i});var o=n(4937),r=function(e){if((0,o.Z)()&&window.document.documentElement){var t=Array.isArray(e)?e:[e],n=window.document.documentElement;return t.some((function(e){return e in n.style}))}return!1},l=function(e,t){if(!r(e))return!1;var n=document.createElement("div"),o=n.style[e];return n.style[e]=t,n.style[e]!==o};function i(e,t){return Array.isArray(e)||void 0===t?r(e):l(e,t)}},2458:e=>{e.exports=function(){var e=document.getSelection();if(!e.rangeCount)return function(){};for(var t=document.activeElement,n=[],o=0;o<e.rangeCount;o++)n.push(e.getRangeAt(o));switch(t.tagName.toUpperCase()){case"INPUT":case"TEXTAREA":t.blur();break;default:t=null}return e.removeAllRanges(),function(){"Caret"===e.type&&e.removeAllRanges(),e.rangeCount||n.forEach((function(t){e.addRange(t)})),t&&t.focus()}}}}]);
//# sourceMappingURL=434.bf451450.chunk.js.map