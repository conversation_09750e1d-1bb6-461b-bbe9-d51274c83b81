{"version": 3, "file": "static/css/132.f3625151.chunk.css", "mappings": "AACA,8CAKE,kDAA6D,CAD7D,WAAY,CAHZ,kBAAmB,CAEnB,gCAA0C,CAD1C,eAAgB,CAIhB,SACF,CAEA,2CACE,SACF,CAEA,4CAGE,UAAY,CACZ,cAAe,CACf,UAAY,CAHZ,UAAW,CADX,QAAS,CAKT,uBACF,CAEA,kDACE,SAAU,CACV,oBACF,CAEA,6BACE,eAAiB,CACjB,kBAAmB,CACnB,UAAW,CACX,eACF,CA8BA,aACE,cAIF,CAUA,mBAEE,kDAA6D,CAG7D,wBAAyB,CAFzB,kBAAmB,CAGnB,+BAA+C,CAL/C,WAAY,CAGZ,YAGF,CAQA,WASE,kBAAmB,CAFnB,kBAAmB,CACnB,YAAa,CAPb,cAAe,CAKf,WAAY,CAIZ,sBAAuB,CAPvB,iBAAkB,CAElB,UAMF,CAaA,wBApBE,oBAAkC,CAFlC,aA6BF,CAPA,aAME,kBAAmB,CAJnB,cAAe,CACf,eAAgB,CAEhB,gBAEF,CAGA,kBACE,kBACF,CAeA,gBAIE,oBAAmC,CAEnC,kBAAmB,CAHnB,aAAc,CAFd,cAAe,CACf,eAAgB,CAGhB,gBAEF,CAGA,sBAGE,aAAS,CAAT,QAAS,CADT,6BAEF,CAEA,aAME,wBACF,CAEA,aAEE,aAEF,CAwBA,cACE,kDAA6D,CAC7D,wBAAyB,CAGzB,kBAAmB,CACnB,iBACF,CAiBA,6BArBE,kBAAmB,CACnB,YAwBF,CAJA,eACE,kBAGF,CAEA,gBAGE,aAAc,CAFd,cAAe,CACf,eAAgB,CAEhB,eACF,CAEA,UACE,eAAgB,CAEhB,QAAS,CADT,SAEF,CAEA,cAEE,kBAAmB,CAGnB,UAAW,CAJX,YAAa,CAGb,cAAe,CADf,aAGF,CAEA,cACE,aAAc,CAEd,cAAe,CADf,iBAEF,CAqBA,uBAEE,+BAA+C,CAD/C,0BAEF,CAEA,cAME,wBAAyB,CAHzB,kBAAmB,CAFnB,QAAO,CAGP,cAAe,CACf,eAAgB,CAHhB,WAAY,CAKZ,uBACF,CAEA,oBACE,oBAAqB,CACrB,aAAc,CACd,0BACF,CAkBA,yBACE,8CAEE,kBAAmB,CADnB,WAEF,CAEA,cACE,sBACF,CAEA,aACE,cACF,CAEA,oCAEE,WACF,CAEA,sBACE,yBACF,CAEA,gBACE,qBAAsB,CACtB,kBACF,CAEA,+BAEE,SACF,CACF,CCrVA,+CAKE,kDAA6D,CAD7D,WAAY,CAHZ,kBAAmB,CAEnB,gCAA8C,CAD9C,eAAgB,CAIhB,SACF,CAEA,4CACE,SACF,CAEA,uBACE,eAAiB,CACjB,kBAAmB,CACnB,UAAW,CAEX,gBAAiB,CADjB,eAEF,CAGA,gBACE,kDAA6D,CAG7D,UAAY,CAFZ,sBAAuB,CAGvB,iBAAkB,CAFlB,iBAGF,CAEA,cAIE,2BAA4B,CAF5B,aAAc,CADd,cAAe,CAEf,kBAEF,CAiBA,aACE,cAAe,CACf,eAAgB,CAChB,cAAiB,CACjB,+BACF,CAEA,gBACE,cAAe,CAGf,eAAgB,CADhB,QAAS,CADT,UAGF,CAGA,mBAEE,kDAA6D,CAG7D,wBAAyB,CAFzB,kBAAmB,CAGnB,+BAA+C,CAL/C,WAAY,CAGZ,YAGF,CAEA,aAGE,kBACF,CAEA,2BAJE,kBAAmB,CADnB,YAgBF,CAXA,cAIE,oBAAkC,CAGlC,kBAAmB,CALnB,aAAc,CADd,cAAe,CAKf,WAAY,CAIZ,sBAAuB,CAPvB,iBAAkB,CAElB,UAMF,CAEA,WACE,QACF,CAEA,WAGE,aAAc,CAFd,cAAe,CACf,eAAgB,CAEhB,cACF,CAEA,gBAIE,oBAAkC,CAElC,kBAAmB,CALnB,aAAc,CACd,cAAe,CACf,eAAgB,CAEhB,gBAEF,CAGA,oBAGE,aAAS,CAFT,YAAa,CAEb,QAAS,CADT,6BAA8B,CAE9B,kBACF,CAEA,aAIE,eAAiB,CAEjB,wBAAyB,CADzB,kBAAmB,CAFnB,YAIF,CAEA,aAEE,aAAc,CADd,cAAe,CAEf,iBACF,CAEA,gBACE,YAAa,CACb,qBACF,CAEA,cAEE,UAAW,CADX,cAAe,CAEf,iBACF,CAEA,cAGE,aAAc,CAFd,cAAe,CACf,eAEF,CAGA,kBACE,eACF,CAEA,iBAGE,kBAAmB,CAFnB,YAAa,CACb,6BAA8B,CAE9B,kBACF,CAEA,gBAGE,UAAW,CAFX,cAAe,CACf,eAEF,CAEA,eAIE,oBAAkC,CAElC,kBAAmB,CAHnB,aAAc,CAFd,cAAe,CACf,eAAgB,CAGhB,gBAEF,CAGA,iBACE,kBACF,CAEA,iBACE,kDAA6D,CAC7D,wBAAyB,CACzB,kBAAmB,CAEnB,kBAAmB,CADnB,YAAa,CAEb,iBACF,CAEA,eAGE,aAAc,CAFd,cAAe,CACf,eAAgB,CAEhB,eACF,CAEA,cAEE,aAAc,CADd,cAAe,CAGf,eAAgB,CADhB,QAEF,CAGA,mBACE,kBAAmB,CACnB,kBAAmB,CACnB,YACF,CAEA,kBAGE,aAAc,CAFd,cAAe,CACf,eAAgB,CAEhB,eACF,CAEA,iBAME,YAAQ,CAFR,YAAa,CAEb,OAAQ,CADR,6BAA8B,CAJ9B,eAAgB,CAEhB,QAAS,CADT,SAKF,CAEA,oBAEE,UAAW,CADX,cAAe,CAEf,aACF,CAGA,gBACE,YAAa,CACb,QAAS,CACT,kBACF,CAEA,cAME,kDAA6D,CAC7D,WAAY,CAJZ,kBAAmB,CAKnB,+BAA+C,CAP/C,QAAO,CAGP,cAAe,CACf,eAAgB,CAHhB,WAAY,CAOZ,uBACF,CAEA,oBAEE,+BAA+C,CAD/C,0BAEF,CAEA,cAME,wBAAyB,CAHzB,kBAAmB,CAFnB,QAAO,CAGP,cAAe,CACf,eAAgB,CAHhB,WAAY,CAKZ,uBACF,CAEA,oBACE,oBAAqB,CACrB,aAAc,CACd,0BACF,CAGA,aACE,kBAAmB,CAEnB,4BAA6B,CAD7B,iBAAkB,CAElB,iBACF,CAEA,eAGE,UAAW,CADX,cAAe,CAEf,eAAgB,CAHhB,QAIF,CAGA,cACE,kDAA6D,CAG7D,UAAY,CAFZ,sBAAuB,CAGvB,iBACF,CAEA,YAIE,4BAA6B,CAF7B,UAAc,CADd,cAAe,CAEf,kBAEF,CAcA,YAGE,aAAS,CAAT,QAAS,CADT,6BAA8B,CAE9B,WACF,CAEA,WAEE,wBAAyB,CACzB,kBAAmB,CAEnB,cAAe,CADf,YAAa,CAEb,uBAEF,CAEA,iBACE,oBAAqB,CAErB,+BAA+C,CAD/C,0BAEF,CAEA,oBAEE,kDAA6D,CAD7D,oBAAqB,CAErB,+BACF,CAEA,YAGE,aAAc,CAFd,cAAe,CACf,eAAgB,CAEhB,eACF,CAEA,YACE,kDAA6D,CAI7D,kBAAmB,CAFnB,cAAe,CAGf,eAAgB,CAFhB,eAGF,CAEA,cACE,aAEF,CAEA,YAEE,oBAAqB,CADrB,YAAa,CAEb,sBAAuB,CACvB,iBACF,CAEA,UAEE,UAAW,CADX,cAAe,CAEf,gBACF,CAEA,QAGE,aAAc,CAFd,cAAe,CACf,eAEF,CAEA,gBAEE,kBAAmB,CADnB,YAAa,CAGb,OAAQ,CADR,sBAAuB,CAEvB,iBACF,CAEA,gBAEE,UAAW,CADX,cAGF,CAEA,UAEE,aAAc,CADd,cAAe,CAEf,eACF,CAEA,UAEE,UAAW,CADX,cAAe,CAEf,eACF,CAEA,eACE,eACF,CAEA,cAIE,UAAW,CADX,cAAe,CAEf,iBACF,CAEA,cACE,aAAc,CAEd,cAAe,CADf,gBAEF,CAEA,oBAIE,kBAAmB,CAInB,kBAAmB,CAHnB,UAAY,CACZ,cAAe,CAGf,eAAgB,CAFhB,eAAgB,CANhB,iBAAkB,CAElB,UAAW,CADX,QAQF,CAEA,iBAME,kDAA6D,CAC7D,WAAY,CAJZ,kBAAmB,CAKnB,+BAA+C,CAP/C,QAAO,CAGP,cAAe,CACf,eAAgB,CAHhB,WAAY,CAOZ,uBACF,CAEA,sCAEE,+BAA+C,CAD/C,0BAEF,CAEA,0BAEE,kBAAmB,CADnB,UAEF,CAEA,aAME,wBAAyB,CAHzB,kBAAmB,CAFnB,QAAO,CAGP,cAAe,CACf,eAAgB,CAHhB,WAAY,CAKZ,uBACF,CAEA,mBACE,oBAAqB,CACrB,aAAc,CACd,0BACF,CAGA,yBACE,+CAEE,kBAAmB,CADnB,WAEF,CAEA,cACE,sBACF,CAEA,aACE,cACF,CAEA,gDAGE,WACF,CAOA,iDACE,yBACF,CAEA,gBACE,qBAAsB,CACtB,kBACF,CAEA,0DAIE,SACF,CACF,CC9gBA,mBAEE,kDAA6D,CAD7D,gBAAiB,CAIjB,iBAAkB,CAFlB,iBAGF,CAGA,yBACE,mBAEE,gBAAiB,CADjB,oBAEF,CACF,CAEA,+CACE,mBACE,mBACF,CACF,CAEA,gDACE,mBACE,mBACF,CACF,CAEA,iDACE,mBACE,mBACF,CACF,CAEA,0BACE,mBAGE,aAAc,CADd,gBAAiB,CADjB,mBAGF,CACF,CAGA,wBAME,kBAAmB,CAFnB,YAAa,CACb,qBAAsB,CAHtB,aAAc,CADd,gBAAiB,CAEjB,UAIF,CAGA,qBAEE,kBAAmB,CAEnB,eAAgB,CAChB,cAAe,CAJf,iBAAkB,CAElB,UAGF,CAEA,YAME,kBAAmB,CAHnB,UAAY,CAEZ,YAAa,CAJb,gBAAiB,CACjB,eAAgB,CAMhB,QAAS,CADT,sBAAuB,CAGvB,eAAgB,CANhB,kBAAmB,CAKnB,+BAEF,CAEA,YACE,aAAc,CACd,aACF,CAEA,eAEE,eAA+B,CAD/B,gBAAiB,CAIjB,eAAgB,CADhB,aAAc,CADd,eAGF,CAIA,yBACE,qBACE,oBAAqB,CACrB,eACF,CAEA,YAEE,qBAAsB,CADtB,gBAAiB,CAEjB,SAAW,CACX,mBACF,CAEA,YACE,iBACF,CAEA,eACE,eAAiB,CACjB,eAAgB,CAChB,SACF,CACF,CAGA,+CACE,qBACE,kBACF,CAEA,YAEE,qBAAsB,CADtB,iBAAkB,CAElB,SACF,CAEA,YACE,cACF,CAEA,eACE,cAAe,CACf,eACF,CACF,CAGA,gDACE,qBACE,oBACF,CAMA,wBACE,iBACF,CAEA,eACE,iBAAkB,CAClB,eACF,CACF,CAGA,iDACE,YACE,iBACF,CAEA,YACE,gBACF,CAEA,eACE,iBAAkB,CAClB,eACF,CACF,CAGA,0BACE,qBACE,oBACF,CAEA,YACE,cACF,CAEA,YACE,iBACF,CAEA,eACE,gBAAiB,CACjB,eACF,CACF,CAEA,eAGE,aAAc,CAFd,gBAAiB,CACjB,eAAgB,CAEhB,mBAAqB,CACrB,iBACF,CAEA,kBAEE,aAAc,CADd,cAAe,CAGf,kBAAmB,CAEnB,gBAAiB,CACjB,iBAAkB,CAFlB,eAAgB,CAFhB,iBAKF,CAGA,sBACE,kBACF,CAEA,mBACE,eAAiB,CAIjB,sBAA6B,CAH7B,kBAAmB,CAEnB,8BAAwC,CADxC,YAAa,CAGb,uBACF,CAEA,0BAEE,kDAAqD,CADrD,oBAEF,CAEA,2BAEE,kDAAqD,CADrD,oBAEF,CAEA,wBAEE,kDAAqD,CADrD,oBAEF,CAEA,qBAEE,kBAAmB,CADnB,YAAa,CAEb,UAAY,CACZ,oBACF,CAEA,aACE,gBACF,CAEA,oBACE,aACF,CAEA,qBACE,aACF,CAEA,kBACE,aACF,CAEA,aAGE,aAAc,CAFd,iBAGF,CAEA,sBAEE,aAAS,CADT,YAAa,CACb,QACF,CAEA,aAEE,kBAAmB,CAGnB,aAAc,CAJd,YAAa,CAGb,cAAe,CADf,UAGF,CAEA,aACE,aAAc,CACd,cACF,CAEA,kCAIE,oBAAmC,CACnC,mBAAqB,CACrB,aAAc,CACd,eAAgB,CALhB,eAAgB,CAChB,YAKF,CAGA,YAGE,aAAS,CAFT,YAAa,CAEb,QAAS,CADT,wDAA2D,CAI3D,gBAAiB,CACjB,iBAAkB,CAHlB,eAAgB,CAChB,gBAAiB,CAIjB,cAAe,CADf,UAEF,CAIA,yBACE,YAEE,QAAS,CADT,yBAA0B,CAE1B,iBAAkB,CAElB,cAAe,CADf,eAEF,CACF,CAGA,+CACE,YAEE,UAAW,CADX,yBAA0B,CAE1B,iBAAkB,CAElB,eAAgB,CADhB,cAEF,CACF,CAGA,gDACE,YAEE,WAAY,CADZ,mCAAqC,CAErC,eAAgB,CAChB,cACF,CACF,CAGA,iDACE,YAEE,QAAS,CADT,mCAAqC,CAErC,gBAAiB,CACjB,gBACF,CACF,CAGA,0BACE,YAEE,UAAW,CADX,mCAAqC,CAErC,gBAAiB,CACjB,cACF,CACF,CAGA,WACE,eAAiB,CAIjB,wBAAyB,CAHzB,oBAAqB,CAErB,+BAA0C,CAK1C,YAAa,CACb,qBAAsB,CACtB,WAAY,CAEZ,cAAe,CALf,eAAgB,CALhB,mBAAoB,CAIpB,iBAAkB,CADlB,uBAAyB,CAMzB,UAEF,CAEA,iBACE,oBAAqB,CAErB,gCAAgD,CADhD,0BAEF,CAIA,yBACE,WAEE,kBAAmB,CACnB,QAAS,CAFT,oBAGF,CAEA,iBACE,0BACF,CACF,CAGA,+CACE,WAEE,qBAAsB,CACtB,QAAS,CAFT,sBAGF,CAEA,iBACE,0BACF,CACF,CAGA,gDACE,WAEE,oBAAqB,CACrB,QAAS,CAFT,mBAGF,CACF,CAGA,iDACE,WAEE,oBAAqB,CACrB,QAAS,CAFT,mBAGF,CACF,CAGA,0BACE,WAEE,qBAAsB,CACtB,QAAS,CAFT,mBAGF,CACF,CAEA,aAEE,kBAAmB,CACnB,iBAAkB,CAFlB,iBAGF,CAEA,YAGE,aAAc,CAFd,iBAAkB,CAClB,eAAgB,CAGhB,sBAAwB,CADxB,oBAEF,CAEA,YAYE,2BAA4B,CAR5B,kDAAqD,CAGrD,kBAAmB,CAInB,+BAA6C,CAN7C,UAAY,CAGZ,iBAAmB,CACnB,eAAgB,CAHhB,kBAAoB,CALpB,iBAAkB,CAElB,aAAc,CADd,WAAY,CAQZ,uBAGF,CASA,yBACE,aACE,qBACF,CAEA,YACE,iBAAkB,CAClB,mBACF,CAEA,YAKE,oBAAqB,CADrB,eAAiB,CADjB,mBAAsB,CADtB,WAAY,CADZ,SAKF,CACF,CAGA,+CACE,aACE,oBACF,CAEA,YACE,gBAAiB,CACjB,mBACF,CAEA,YAIE,gBAAkB,CADlB,mBAAsB,CADtB,cAAe,CADf,YAIF,CACF,CAGA,gDACE,YACE,kBACF,CACF,CAGA,0BACE,aACE,qBACF,CAEA,YACE,cAAe,CACf,kBACF,CAEA,YAIE,cAAe,CADf,oBAAsB,CADtB,cAAe,CADf,YAIF,CACF,CAEA,cAIE,kDAA6D,CAE7D,wBAAyB,CADzB,kBAAmB,CAHnB,oBAAqB,CACrB,cAAe,CAFf,iBAMF,CAEA,eAGE,kBAAmB,CADnB,YAAa,CAIb,cAAe,CADf,QAAS,CADT,sBAAuB,CAHvB,kBAMF,CAEA,eAKE,oBAAqB,CAFrB,aAAc,CACd,YAAa,CAHb,gBAAiB,CACjB,eAAgB,CAIhB,UACF,CAEA,UAGE,aAAc,CAFd,iBAAkB,CAClB,eAEF,CAEA,gBAEE,aAAc,CADd,gBAAiB,CAIjB,eAAgB,CADhB,iBAAkB,CADlB,4BAGF,CAEA,uBAME,4BAA6B,CAL7B,WAAY,CAIZ,iBAAmB,CAHnB,iBAAkB,CAElB,YAAc,CADd,UAIF,CAOA,gBACE,kDAAqD,CAGrD,kBAAmB,CAFnB,UAAY,CAGZ,iBAAmB,CACnB,eAAgB,CAChB,iBAAmB,CAJnB,qBAKF,CAEA,eACE,aAAc,CAEd,kBAAmB,CADnB,eAAgB,CAEhB,gBACF,CAEA,oBACE,aAAc,CACd,eACF,CAIA,yBACE,cACE,oBAAqB,CACrB,YACF,CAEA,eAIE,kBAAmB,CADnB,qBAAsB,CADtB,SAAW,CADX,oBAIF,CAEA,eACE,iBACF,CAEA,UACE,cACF,CAEA,gBACE,kBACF,CAEA,uBACE,gBAAkB,CAElB,YAAc,CADd,UAEF,CAEA,gBACE,gBAAkB,CAElB,kBAAoB,CADpB,mBAEF,CAEA,eACE,eACF,CACF,CAGA,+CACE,cACE,kBAAmB,CACnB,eACF,CAEA,eACE,UACF,CAEA,eACE,cACF,CAEA,UACE,gBACF,CAEA,gBACE,iBACF,CAEA,gBACE,eAAiB,CACjB,mBACF,CAEA,eACE,cACF,CACF,CAGA,gDACE,eACE,iBACF,CAEA,gBACE,kBACF,CACF,CAGA,0BACE,cACE,kBAAmB,CACnB,YACF,CAEA,eAEE,WAAY,CADZ,qBAEF,CAEA,eACE,cACF,CAEA,UACE,gBACF,CAEA,gBACE,iBACF,CAEA,uBACE,cAAe,CAEf,YAAc,CADd,UAEF,CAEA,gBACE,cAAe,CAEf,kBAAoB,CADpB,mBAEF,CAEA,eACE,iBAAkB,CAClB,iBACF,CACF,CAEA,eACE,kBACF,CAEA,cAME,aAAc,CADd,gBAAkB,CAFlB,UAAY,CACZ,oBAGF,CAEA,cACE,aAAc,CAEd,aAAc,CADd,eAEF,CAEA,iBAYE,kBAAmB,CAVnB,kDAAqD,CAErD,WAAY,CACZ,oBAAsB,CAFtB,UAAY,CAMZ,cAAe,CAEf,YAAa,CAJb,cAAe,CACf,eAAgB,CAMhB,SAAW,CADX,sBAAuB,CAPvB,mBAAoB,CAIpB,uBAAyB,CATzB,UAcF,CAEA,uBACE,kDAAqD,CACrD,0BACF,CAEA,0BAEE,kBAAmB,CADnB,UAAY,CAEZ,cACF,CAIA,yBACE,eACE,qBACF,CAEA,cAGE,gBAAkB,CAFlB,SAAW,CACX,mBAEF,CAEA,cACE,eACF,CAEA,iBAGE,qBAAuB,CADvB,eAAiB,CADjB,uBAGF,CACF,CAGA,+CACE,eACE,oBACF,CAEA,cAGE,eAAiB,CAFjB,WAAa,CACb,qBAEF,CAEA,cACE,gBACF,CAEA,iBAGE,sBAAwB,CADxB,gBAAkB,CADlB,yBAGF,CACF,CAGA,gDACE,cACE,kBACF,CACF,CAGA,0BACE,eACE,oBACF,CAEA,cAGE,mBAAoB,CAFpB,QAAS,CACT,kBAEF,CAEA,cACE,cACF,CAEA,iBAGE,kBAAmB,CADnB,kBAAmB,CAEnB,UAAY,CAHZ,oBAIF,CACF,CAEA,UACE,eACF,CAGA,eACE,eACF,CAEA,iBAME,kBAAmB,CALnB,kDAAqD,CACrD,wBAAyB,CACzB,kBAAmB,CAEnB,YAAa,CAEb,QAAS,CAHT,cAIF,CAEA,cACE,aAAc,CAEd,aAAc,CADd,gBAEF,CAEA,oBAEE,aAAc,CACd,eAAgB,CAFhB,gBAGF,CAEA,mBAEE,aAAc,CADd,eAEF,CAEA,kBACE,kBAAmB,CAEnB,WAAY,CACZ,mBAAqB,CAFrB,UAAY,CAKZ,cAAe,CADf,eAAgB,CADhB,kBAAoB,CAGpB,8BACF,CAEA,wBACE,kBACF,CAGA,eAGE,aAAc,CADd,YAAa,CADb,iBAGF,CAEA,SAIE,wBAA6B,CAA7B,wBAA6B,CAF7B,WAAY,CAKZ,kBAAmB,CANnB,UAOF,CAGA,gBAGE,aAAc,CADd,YAAa,CADb,iBAGF,CAEA,eACE,cAAe,CACf,kBACF,CAEA,mBACE,aAAc,CACd,kBACF,CAEA,kBACE,kBAAmB,CAEnB,gBAAiB,CACjB,iBAAkB,CAFlB,eAGF,CAEA,aACE,kBAAmB,CAEnB,WAAY,CACZ,mBAAqB,CAFrB,UAAY,CAKZ,cAAe,CADf,eAAgB,CADhB,qBAAuB,CAGvB,8BACF,CAEA,mBACE,kBACF,CAQA,uBAWE,iCAA0B,CAA1B,yBAA0B,CAL1B,gBAMF,CAEA,eASE,mCAAqC,CARrC,eAAiB,CACjB,kBAAmB,CAMnB,gCAA0C,CAE1C,YAAa,CACb,qBAAsB,CALtB,eAAgB,CAFhB,eAAgB,CAGhB,eAAgB,CAJhB,cAAe,CAEf,SAOF,CAEA,6BAEE,eAAgB,CADhB,eAAgB,CAEhB,eACF,CAEA,wBACE,GACE,SAAU,CACV,qCACF,CACA,GACE,SAAU,CACV,gCACF,CACF,CAEA,uBAIE,YAAa,CADb,QAAO,CAEP,qBAAsB,CACtB,6BAA8B,CAC9B,eAAgB,CALhB,iBAAkB,CADlB,iBAOF,CAGA,qBAcE,kBAAmB,CAVnB,oBAAoC,CACpC,wBAAyB,CAMzB,iBAAkB,CAOlB,8BAAwC,CAZxC,UAAW,CAMX,cAAe,CACf,YAAa,CANb,cAAe,CACf,eAAiB,CAEjB,WAAY,CAKZ,sBAAuB,CAdvB,iBAAkB,CAElB,WAAY,CADZ,SAAU,CAcV,uBAAyB,CAPzB,UAAW,CAQX,UAEF,CAEA,2BACE,oBAAkC,CAElC,oBAAqB,CADrB,aAAc,CAEd,oBACF,CAGA,mBAGE,oBAAkC,CAElC,0BAAwC,CADxC,kBAAmB,CAHnB,eAAgB,CAChB,YAIF,CAEA,kBACE,aAAc,CACd,cAAe,CAEf,eAAgB,CADhB,kBAEF,CAEA,kBACE,kDAA6D,CAE7D,WAAY,CAEZ,iBAAkB,CAKlB,8BAA6C,CAR7C,UAAY,CAMZ,cAAe,CAFf,cAAe,CACf,eAAgB,CAHhB,iBAAkB,CAKlB,uBAEF,CAEA,wBACE,kDAA6D,CAE7D,+BAA8C,CAD9C,0BAEF,CAGA,mBACE,oBAAoC,CACpC,0BAAyC,CACzC,kBAAmB,CAEnB,aAAc,CADd,YAAa,CAEb,iBACF,CAEA,sBACE,aAAc,CACd,cAAe,CACf,eAAgB,CAChB,cACF,CAEA,cAIE,oBAAmC,CAEnC,kBAAmB,CALnB,aAAc,CAMd,oBAAqB,CAJrB,eAAgB,CAKhB,kBAAmB,CAHnB,gBAIF,CAEA,kBACE,aAAc,CACd,cAAe,CACf,eAAgB,CAChB,QAAS,CACT,eACF,CAEA,gBACE,aAAc,CACd,cAAe,CAEf,iBAAkB,CADlB,kBAEF,CAQA,kBACE,kBACE,uBACF,CACA,IACE,2BACF,CACA,IACE,0BACF,CACF,CAEA,4BACE,yDACF,CAEA,4BACE,oDACF,CAEA,+BACE,GACE,SAAU,CACV,sCACF,CACA,GACE,SAAU,CACV,gCACF,CACF,CAEA,0BACE,GACE,SAAU,CACV,qCACF,CACA,GACE,SAAU,CACV,gCACF,CACF,CAGA,gBAIE,qBAAuB,CAHvB,uBAAyB,CACzB,yBAA2B,CAC3B,oBAEF,CAGA,mBACE,iBAAkB,CAClB,SACF,CAGA,oBAQE,4BAA8B,CAE9B,+BAAiC,CAHjC,sBAAwB,CAFxB,sBAAwB,CAIxB,gCAAkC,CANlC,gBAAkB,CAFlB,wBAA0B,CAC1B,eAAiB,CAEjB,qBAAuB,CAEvB,uBAKF,CAGA,sBAIE,gEAAwE,CAFxE,kBAAoB,CADpB,2BAA6B,CAE7B,iCAEF,CAEA,4BACE,GACE,SAAU,CACV,sCACF,CACA,GACE,SAAU,CACV,gCACF,CACF,CAKA,yBACE,oBACE,sBACF,CAEA,sBAIE,4BAA8B,CAD9B,uCAAyC,CADzC,sCAAwC,CADxC,oBAIF,CAEA,sCAEE,qCAAuC,CADvC,sBAEF,CAEA,yCAEE,kBAAoB,CADpB,sBAEF,CAEA,kBAIE,wBAA0B,CAF1B,wBAA0B,CAC1B,2BAA6B,CAF7B,oBAIF,CACF,CAGA,gDACE,oBACE,sBACF,CAEA,sBAGE,4BAA8B,CAD9B,yBAA2B,CAD3B,yBAGF,CAEA,yCACE,yBACF,CAEA,sCAEE,qCAAuC,CADvC,sBAEF,CAEA,yCAEE,kBAAoB,CADpB,sBAEF,CAEA,kBAEE,uBAAyB,CADzB,yBAEF,CACF,CAGA,0BACE,oBACE,sBACF,CAEA,sBAGE,4BAA8B,CAD9B,yBAA2B,CAD3B,yBAGF,CAEA,yCAEE,yBAA2B,CAD3B,yBAEF,CAEA,sCAEE,qCAAuC,CADvC,sBAEF,CAEA,yCAEE,kBAAoB,CADpB,sBAEF,CAEA,kBAEE,uBAAyB,CADzB,yBAEF,CACF,CAGA,yBAEE,eAGE,WAAY,CACZ,eAAgB,CAFhB,eAAgB,CAGhB,eAAgB,CAJhB,SAKF,CAEA,uBACE,iBACF,CAEA,qBAKE,cAAe,CADf,WAAY,CAFZ,UAAW,CADX,QAAS,CAET,UAGF,CAEA,mBAEE,aAAc,CADd,YAEF,CAEA,sBACE,cACF,CAEA,cACE,cAAe,CACf,gBACF,CAEA,kBACE,cACF,CAEA,mBAEE,eAAgB,CADhB,YAEF,CAEA,kBAGE,cAAe,CADf,iBAAkB,CADlB,UAGF,CAEA,8BACE,mBACF,CACF,CAEA,gDAEE,eAEE,eAAgB,CADhB,SAEF,CAEA,uBACE,iBACF,CAEA,mBACE,YACF,CAEA,sBACE,cACF,CAEA,cACE,cACF,CAEA,kBAEE,cAAe,CADf,iBAEF,CACF,CAEA,0BAEE,eAEE,eAAgB,CADhB,SAEF,CAEA,uBACE,iBACF,CAEA,mBACE,YACF,CAEA,sBACE,cACF,CAEA,cACE,cACF,CAEA,kBAEE,cAAe,CADf,iBAEF,CACF,CAKA,yBACE,YAEE,kBAAoB,CADpB,mCAAqC,CAErC,yBACF,CAEA,WAGE,4BAA8B,CAD9B,kBAAoB,CADpB,8BAGF,CAEA,YACE,0BAA4B,CAC5B,6BACF,CAEA,YAIE,yBAA4B,CAD5B,8BAAkC,CADlC,uBAA0B,CAD1B,qBAIF,CAEA,cAEE,4BAA8B,CAD9B,wBAEF,CAEA,eACE,2BACF,CAEA,gBACE,wBACF,CAEA,eACE,yBACF,CAEA,eACE,+BACF,CAEA,cACE,yBAA4B,CAE5B,6BAAgC,CADhC,yBAEF,CAEA,iBAGE,4BAA8B,CAD9B,yBAA4B,CAD5B,8BAGF,CACF,CAGA,+CACE,YAEE,oBAAsB,CADtB,6CAAgD,CAEhD,wBACF,CAEA,WAGE,+BAAiC,CADjC,kBAAoB,CADpB,gCAGF,CAEA,YACE,0BAA4B,CAC5B,8BACF,CAEA,YAIE,0BAA6B,CAD7B,gCAAoC,CADpC,wBAA2B,CAD3B,sBAIF,CAEA,cAEE,+BAAiC,CADjC,sBAEF,CAEA,eACE,wBACF,CAEA,gBACE,2BACF,CAEA,eACE,wBACF,CAEA,eACE,8BACF,CAEA,cACE,2BAA8B,CAE9B,6BAAgC,CADhC,yBAEF,CAEA,iBAGE,4BAA8B,CAD9B,0BAA6B,CAD7B,8BAGF,CACF,CAGA,gDACE,YAEE,qBAAuB,CADvB,6CAAgD,CAGhD,uBAAyB,CADzB,yBAEF,CAEA,WAGE,8BAAgC,CADhC,kBAAoB,CADpB,6BAGF,CAEA,YACE,0BAA4B,CAC5B,8BACF,CAEA,YAIE,yBAA4B,CAD5B,4BAA+B,CAD/B,qBAAuB,CADvB,mBAIF,CAEA,cAEE,8BAAgC,CADhC,yBAEF,CAEA,eACE,2BACF,CAEA,gBACE,0BACF,CAEA,eACE,0BACF,CAEA,eACE,+BACF,CAEA,cACE,yBAA4B,CAE5B,6BAAgC,CADhC,yBAEF,CAEA,iBAGE,4BAA8B,CAD9B,wBAA0B,CAD1B,6BAGF,CACF,CAGA,0BACE,YAEE,kBAAoB,CADpB,6CAAgD,CAGhD,uBAAyB,CADzB,0BAEF,CAEA,WAGE,8BAAgC,CADhC,kBAAoB,CADpB,6BAGF,CAEA,YACE,2BAA6B,CAC7B,8BACF,CAEA,YAIE,0BAA6B,CAD7B,4BAA+B,CAD/B,qBAAuB,CADvB,mBAIF,CAEA,cAEE,4BAA8B,CAD9B,wBAEF,CAEA,eACE,0BACF,CAEA,gBACE,0BACF,CAEA,eACE,4BACF,CAEA,eACE,4BACF,CAEA,cACE,0BAA6B,CAE7B,8BAAiC,CADjC,0BAEF,CAEA,iBAGE,4BAA8B,CAD9B,wBAA0B,CAD1B,6BAGF,CACF,CAEA,gDACE,WACE,mBACF,CAEA,YACE,kBACF,CAEA,eACE,iBACF,CAEA,gBACE,kBACF,CAEA,eACE,mBACF,CAEA,cACE,kBACF,CACF,CAEA,0BACE,WACE,mBACF,CAEA,YACE,iBACF,CAEA,eACE,gBACF,CAEA,gBACE,gBACF,CAEA,eACE,kBACF,CAEA,cACE,cACF,CACF,CAGA,8BAGE,WAAY,CACZ,kBAAmB,CAHnB,iBAAkB,CAClB,UAGF,CAEA,iBAME,iCAAkC,CAFlC,wBAA6B,CAC7B,iBAAkB,CADlB,wBAA6B,CAF7B,WAAY,CAOZ,SAAU,CAFV,iBAAkB,CAClB,QAAS,CAPT,UASF,CAEA,eAKE,uCAAwC,CAFxC,wBAAyB,CACzB,iBAAkB,CAFlB,YAAa,CAMb,MAAO,CAFP,iBAAkB,CAClB,KAAM,CANN,WAQF,CAiBA,kBACE,aAAc,CAEd,gBAAiB,CADjB,kBAEF,CAEA,gBACE,aAAc,CAEd,gBAAiB,CADjB,kBAEF,CAEA,mBACE,kBAAmB,CACnB,kBAAmB,CAEnB,cAAgB,CADhB,cAEF,CAEA,sBACE,aAAc,CAEd,cAAe,CADf,oBAEF,CAEA,qBACE,aAAc,CAEd,cAAe,CADf,gBAEF,CAEA,sBACE,kBAAmB,CACnB,kBAAmB,CAEnB,iBAAkB,CADlB,YAEF,CAEA,wBAEE,aAAc,CACd,eAAiB,CAFjB,cAGF,CAGA,mBAEE,kBAAmB,CADnB,iBAEF,CAEA,mBAEE,oCAAsC,CADtC,cAEF,CAEA,yBACE,GAEE,SAAU,CADV,kBAEF,CACA,IAEE,SAAU,CADV,oBAEF,CACA,GAEE,SAAU,CADV,kBAEF,CACF,CAEA,kBASE,8BAA+B,CAF/B,yEAA+E,CAC/E,iBAAkB,CAFlB,YAAa,CAHb,QAAS,CAOT,SAAU,CATV,iBAAkB,CAClB,SAAU,CAEV,0BAA2B,CAC3B,WAMF,CAEA,oBACE,GAEE,SAAU,CADV,mCAEF,CACA,IAEE,UAAY,CADZ,qCAEF,CACA,GAEE,SAAU,CADV,mCAEF,CACF,CAEA,iBACE,aAAc,CACd,gBAAiB,CAEjB,eAAgB,CADhB,kBAEF,CAEA,mBACE,kBAAmB,CACnB,wBAAyB,CACzB,kBAAmB,CAEnB,aAAc,CADd,YAEF,CAEA,sBACE,aAAc,CACd,mBACF,CAEA,qBACE,aAAc,CACd,eACF,CAEA,kBAEE,kBAAmB,CACnB,kBAAmB,CAEnB,eAAgB,CADhB,cAAe,CAHf,eAKF,CAEA,qBACE,aAAc,CACd,kBAAmB,CACnB,iBACF,CAEA,qBACE,eAAgB,CAEhB,QAAS,CADT,SAEF,CAEA,qBACE,aAAc,CAEd,gBAAkB,CADlB,cAEF,CAEA,iBACE,YAAa,CAGb,cAAe,CAFf,QAAS,CACT,eAEF,CAEA,aAGE,WAAY,CACZ,iBAAkB,CAElB,cAAe,CALf,QAAO,CAIP,eAAgB,CAGhB,eAAgB,CANhB,qBAAuB,CAKvB,kBAEF,CAEA,qBACE,kBAAmB,CACnB,UACF,CAEA,2BACE,kBAAmB,CACnB,0BACF,CAEA,uBACE,kBAAmB,CAEnB,wBAAyB,CADzB,aAEF,CAEA,6BACE,kBACF,CAGA,yBACE,eAKE,kBAAmB,CAHnB,aAAe,CADf,cAKF,CAEA,4CALE,eAAgB,CAChB,SAOF,CAEA,uBAEE,SAAW,CADX,gBAEF,CAEA,qBAGE,cAAe,CADf,WAAY,CAGZ,SAAU,CADV,OAAQ,CAHR,UAKF,CAEA,8BAEE,WAAY,CACZ,mBAAqB,CAFrB,UAGF,CAEA,mBAGE,iBAAkB,CADlB,YAAa,CADb,WAGF,CAEA,sBACE,cAAe,CACf,cACF,CAEA,qBACE,cAAe,CACf,YACF,CAEA,mBAGE,iBAAkB,CADlB,cAAgB,CADhB,aAGF,CAEA,sBACE,cAAe,CACf,mBACF,CAEA,qBACE,cAAe,CACf,cACF,CAEA,iBACE,qBAAsB,CACtB,OAAQ,CACR,iBACF,CAEA,aAIE,iBAAkB,CADlB,cAAe,CADf,gBAAiB,CADjB,UAIF,CAGA,iBAEE,4BAA8B,CAD9B,sBAEF,CAEA,oBACE,wBAA0B,CAC1B,2BACF,CAEA,mBACE,wBAA0B,CAC1B,sBACF,CAEA,kBAEE,4BAA8B,CAD9B,qBAEF,CAEA,qBACE,wBAA0B,CAC1B,2BACF,CAEA,oBACE,wBAA0B,CAC1B,sBACF,CACF,CAGA,+CACE,eAKE,kBAAmB,CAHnB,YAAc,CACd,eAAgB,CAFhB,YAAa,CAGb,SAEF,CAEA,6BACE,eAAgB,CAEhB,eAAgB,CADhB,SAEF,CAEA,uBAEE,UAAY,CADZ,eAEF,CAEA,qBAGE,cAAe,CADf,WAAY,CAGZ,UAAW,CADX,QAAS,CAHT,UAKF,CAEA,8BAEE,WAAY,CACZ,oBAAsB,CAFtB,UAGF,CAEA,mBAGE,kBAAmB,CADnB,YAAa,CADb,YAGF,CAEA,sBACE,cAAe,CACf,cACF,CAEA,qBACE,cAAe,CACf,YACF,CAEA,mBAGE,kBAAmB,CADnB,cAAgB,CADhB,aAGF,CAEA,sBACE,cAAe,CACf,mBACF,CAEA,qBACE,cAAe,CACf,eACF,CAEA,iBACE,kBAAmB,CACnB,QAAS,CAET,sBAAuB,CADvB,eAEF,CAEA,aAGE,iBAAkB,CADlB,cAAe,CAEf,eAAgB,CAHhB,iBAIF,CAGA,iBAEE,4BAA8B,CAD9B,sBAEF,CAEA,oBACE,wBAA0B,CAC1B,2BACF,CAEA,mBACE,wBAA0B,CAC1B,sBACF,CAEA,kBAEE,4BAA8B,CAD9B,sBAEF,CAEA,qBACE,wBAA0B,CAC1B,2BACF,CAEA,oBACE,wBAA0B,CAC1B,sBACF,CACF,CAGA,yBACE,eAME,kBAAmB,CAJnB,WAAY,CACZ,eAAgB,CAEhB,eAAgB,CAJhB,cAAe,CAGf,SAGF,CAEA,6BACE,eAAgB,CAEhB,eAAgB,CADhB,SAEF,CAEA,uBAEE,QAAS,CADT,gBAEF,CAEA,qBAGE,cAAe,CADf,WAAY,CAGZ,UAAW,CADX,QAAS,CAHT,UAKF,CAEA,8BAEE,WAAY,CACZ,kBAAmB,CAFnB,UAGF,CAEA,mBAGE,kBAAmB,CADnB,aAAc,CADd,YAGF,CAEA,sBACE,cAAe,CACf,cACF,CAEA,qBACE,cAAe,CACf,YACF,CAEA,mBAGE,kBAAmB,CADnB,cAAgB,CADhB,cAGF,CAEA,sBACE,cAAe,CACf,oBACF,CAEA,qBACE,cAAe,CACf,gBACF,CAEA,iBACE,kBAAmB,CACnB,QAAS,CAET,sBAAuB,CADvB,iBAEF,CAEA,aAGE,iBAAkB,CADlB,cAAe,CAEf,eAAgB,CAHhB,iBAIF,CAGA,iBAEE,4BAA8B,CAD9B,sBAEF,CAEA,oBACE,wBAA0B,CAC1B,2BACF,CAEA,mBACE,wBAA0B,CAC1B,sBACF,CAEA,kBAEE,4BAA8B,CAD9B,sBAEF,CAEA,qBACE,wBAA0B,CAC1B,2BACF,CAEA,oBACE,wBAA0B,CAC1B,sBACF,CACF,CAGA,yBACE,mBACE,kBACF,CAEA,YACE,cACF,CAEA,YAEE,UAAW,CADX,yBAEF,CAEA,WACE,cACF,CAEA,iBACE,qBAAsB,CACtB,iBACF,CACF,CAGA,wBACE,GACE,4BACF,CACA,IACE,+BACF,CACA,GACE,4BACF,CACF,CAEA,yBACE,gEACF,CAGA,aAEE,eAAgB,CADhB,iBAEF,CAEA,oBAOE,uDAAmF,CANnF,UAAW,CAKX,WAAY,CAFZ,UAAW,CAFX,iBAAkB,CAClB,KAAM,CAKN,mBAAqB,CAHrB,UAIF,CAEA,0BACE,SACF,CAKA,uBAUE,kBAAmB,CAInB,6BAA+B,CAP/B,yBAA0B,CAC1B,iCAAkC,CAFlC,oBAAiC,CADjC,QAAS,CAIT,YAAa,CAEb,sBAAuB,CARvB,MAAO,CAYP,aAAc,CACd,2BAA4B,CAH5B,YAAa,CAZb,cAAe,CAGf,OAAQ,CAFR,KAAM,CAUN,aAKF,CAGA,8BAQE,4EAMC,CARD,QAAS,CALT,UAAW,CAGX,MAAO,CAGP,mBAAoB,CALpB,iBAAkB,CAGlB,OAAQ,CAFR,KAAM,CAYN,UACF,CAGA,6BAaE,qCAAsC,CARtC,oBAAmC,CAGnC,kBAAmB,CALnB,WAAY,CAGZ,UAAY,CALZ,iCAAkC,CAQlC,cAAe,CACf,eAAgB,CAIhB,UAAY,CAPZ,gBAAiB,CAIjB,mBAAoB,CATpB,cAAe,CAEf,UAAW,CAQX,aAGF,CAEA,2BACE,MAAW,UAAc,CACzB,IAAM,UAAc,CACtB,CAEA,kBACE,GAAO,SAAY,CACnB,GAAK,SAAY,CACnB,CAGA,yBAWE,8BAAgC,CAVhC,eAAmB,CAWnB,0BAA0C,CAV1C,kBAAmB,CACnB,0DAEqC,CAQrC,aAAc,CALd,eAAgB,CADhB,eAAgB,CAEhB,eAAgB,CAChB,iBAAkB,CAJlB,UAQF,CAEA,iCACE,eACF,CAGA,gDACE,yBACE,eACF,CAEA,iCACE,eACF,CACF,CAGA,0BACE,yBACE,eACF,CAEA,iCACE,eACF,CACF,CAGA,mBACE,GACE,SAAU,CACV,qCACF,CACA,GACE,SAAU,CACV,gCACF,CACF,CAGA,iBAYE,kBAAmB,CALnB,oBAAiC,CADjC,WAAY,CAEZ,kBAAmB,CACnB,aAAc,CACd,cAAe,CACf,YAAa,CANb,WAAY,CAQZ,sBAAuB,CAZvB,iBAAkB,CAElB,UAAW,CADX,QAAS,CAYT,uBAAyB,CAVzB,UAAW,CAWX,UACF,CAEA,uBACE,oBAAkC,CAClC,aAAc,CACd,qBACF,CAGA,cAGE,+BAAgC,CAFhC,sBAAuB,CACvB,iBAEF,CAEA,yBACE,kDAA6D,CAE7D,kBAAmB,CADnB,UAEF,CAEA,sBACE,kDAA6D,CAE7D,kBAAmB,CADnB,UAEF,CAEA,iBAEE,cAAe,CACf,eAAgB,CAChB,eAAgB,CAHhB,iBAIF,CAEA,gBAEE,cAAe,CAEf,eAAgB,CAHhB,QAAS,CAET,UAEF,CAGA,iBAGE,WAAY,CACZ,aAAc,CAHd,iBAAkB,CAClB,UAGF,CAEA,SAOE,iCAAkC,CAFlC,0BAA2B,CAC3B,iBAAkB,CADlB,qBAA2B,CAF3B,WAAY,CAFZ,iBAAkB,CAClB,UAMF,CAEA,gBACE,GAAK,sBAAyB,CAC9B,GAAO,uBAA2B,CACpC,CAEA,cAKE,UAAY,CAFZ,QAAS,CAFT,iBAAkB,CAClB,OAAQ,CAER,8BAEF,CAEA,cAIE,+BAAiC,CAFjC,WAAY,CACZ,aAAc,CAFd,UAIF,CAEA,oBACE,GAA4B,SAAU,CAAjC,mBAAmC,CACxC,IAAM,qBAAwB,CAC9B,IAAM,mBAAuB,CAC7B,GAA4B,SAAU,CAA/B,kBAAiC,CAC1C,CAGA,eAQE,gCAAiC,CANjC,6BAA8B,CAE9B,iBAAkB,CADlB,eAAgB,CAFhB,sBAAuB,CAQvB,iBAAkB,CAFlB,sBAAuB,CADvB,+BAAgC,CADhC,oBAKF,CAGA,kCACE,UACF,CAEA,wCACE,kBAAmB,CACnB,iBAAkB,CAElB,kCAA4C,CAD5C,YAEF,CAEA,wCACE,kDAA6D,CAE7D,wBAAyB,CADzB,iBAAkB,CAGlB,4BAA2C,CAD3C,+BAAyB,CAAzB,uBAEF,CAEA,8CACE,kDAA6D,CAC7D,qBACF,CAEA,+CACE,kBACF,CAGA,uBACE,MAAW,UAAc,CACzB,IAAM,UAAc,CACtB,CAGA,sBAOE,yDAAiF,CANjF,UAAW,CAKX,UAAW,CAFX,MAAO,CAKP,SAAU,CAPV,uBAAgB,CAAhB,eAAgB,CAGhB,OAAQ,CAFR,KAAM,CAON,2BAA6B,CAF7B,SAGF,CAEA,iCAEE,iCAAkC,CADlC,SAEF,CAGA,qBAOE,gEAAiH,CAJjH,QAAS,CAFT,UAAW,CAKX,WAAY,CAFZ,MAAO,CAMP,SAAU,CAFV,mBAAoB,CANpB,uBAAgB,CAAhB,eAAgB,CAGhB,OAAQ,CAMR,2BAA6B,CAF7B,SAGF,CAEA,gCACE,SACF,CAGA,iCAGE,kBAAmB,CAMnB,mEAA4H,CAC5H,+BAAgC,CALhC,aAAc,CAJd,6BAA8B,CAC9B,YAAa,CAIb,cAAe,CACf,eAAgB,CAChB,WAAY,CAJZ,sBAAuB,CAQvB,eAAgB,CADhB,wBAEF,CAGA,aAOE,kBAAmB,CANnB,kDAA6D,CAC7D,wBAAyB,CACzB,kBAAmB,CAGnB,YAAa,CAEb,QAAS,CAHT,kBAAmB,CADnB,YAKF,CAEA,kBAGE,iBAAkB,CAClB,aAAc,CAFd,WAAY,CADZ,UAIF,CAEA,6BAEE,2BAA4B,CAD5B,kBAEF,CAEA,iBACE,MAAW,SAAY,CACvB,IAAM,UAAc,CACtB,CAEA,aAEE,aAAc,CAEd,cAAe,CADf,eAAgB,CAFhB,QAIF,CAGA,gBACE,kBAAmB,CACnB,wBAAyB,CACzB,kBAAmB,CAEnB,kBAAmB,CADnB,YAEF,CAEA,mBAEE,aAAc,CACd,cAAe,CACf,eAAgB,CAHhB,eAIF,CAEA,cACE,YAAa,CACb,qBAAsB,CACtB,QACF,CAEA,YAGE,kBAAmB,CAEnB,eAAiB,CAEjB,wBAAyB,CADzB,kBAAmB,CALnB,YAAa,CACb,6BAA8B,CAE9B,iBAIF,CAEA,iBACE,aAAc,CACd,eACF,CAEA,mBACE,aAAc,CACd,eACF,CAEA,iCAEE,kBAAmB,CAEnB,kBAAmB,CAGnB,iBAAkB,CAFlB,aAAc,CAJd,YAAa,CAQb,cAAe,CADf,eAAgB,CALhB,OAAQ,CAGR,gBAIF,CAGA,kBACE,kDAA6D,CAC7D,wBAAyB,CACzB,kBAAmB,CAEnB,kBAAmB,CADnB,YAEF,CAEA,oBAEE,kBAAmB,CADnB,YAAa,CAEb,QAKF,CAEA,kCALE,aAAc,CAEd,cAAe,CADf,eAAgB,CAFhB,kBAgBF,CAVA,cACE,oBAAmC,CAQnC,0BAAyC,CALzC,kBAAmB,CADnB,iBAAkB,CAElB,iBAKF,CAEA,mBACE,YAAa,CACb,qBAAsB,CACtB,OACF,CAEA,MACE,aAAc,CACd,cAAe,CACf,eAAgB,CAChB,gBAAiB,CACjB,iBACF,CAEA,aAEE,aAAc,CADd,WAAY,CAEZ,eAAiB,CAEjB,SAAU,CADV,iBAEF,CAGA,gBACE,kDAA6D,CAC7D,wBAAyB,CACzB,kBAAmB,CAGnB,kBAAmB,CAFnB,YAAa,CACb,iBAEF,CAEA,kBAEE,aAAc,CACd,eAAgB,CAFhB,eAGF,CAEA,eASE,kBAAmB,CARnB,kDAA6D,CAE7D,WAAY,CAEZ,kBAAmB,CAHnB,UAAY,CAKZ,cAAe,CACf,YAAa,CAFb,eAAgB,CAIhB,OAAQ,CACR,aAAc,CAPd,iBAAkB,CAQlB,uBACF,CAEA,qBAEE,+BAA8C,CAD9C,0BAEF,CAGA,gBAOE,kBAAmB,CANnB,kDAA6D,CAC7D,wBAAyB,CACzB,kBAAmB,CAGnB,YAAa,CAEb,QAAS,CAHT,kBAAmB,CADnB,YAKF,CAEA,gBAEE,mCAAoC,CADpC,aAEF,CAEA,kBACE,GAAO,sBAAyB,CAChC,GAAK,uBAA2B,CAClC,CAEA,kBAEE,aAAc,CACd,eAAgB,CAFhB,QAGF,CAEA,mBACE,kBAAmB,CACnB,wBAAyB,CACzB,kBAAmB,CAEnB,kBAAmB,CADnB,YAEF,CAEA,sBAEE,aAAc,CACd,cAAe,CACf,eAAgB,CAHhB,eAIF,CAEA,eACE,kDAA6D,CAC7D,wBAAyB,CACzB,kBAAmB,CAEnB,kBAAmB,CADnB,YAEF,CAEA,kBAEE,aAAc,CACd,cAAe,CACf,eAAgB,CAHhB,eAAgB,CAIhB,iBACF,CAEA,eAGE,aAAS,CAFT,YAAa,CAEb,QAAS,CADT,wDAEF,CAEA,cAEE,kBAAmB,CAEnB,gBAAoC,CAEpC,kBAAmB,CACnB,aAAc,CANd,YAAa,CAQb,cAAe,CADf,eAAgB,CALhB,QAAS,CAET,iBAKF,CAEA,kBACE,aAAc,CACd,aACF,CAGA,eAIE,cAAe,CAFf,QAAS,CAGT,cACF,CAEA,4BAPE,YAAa,CAEb,sBAoBF,CAfA,aAUE,kBAAmB,CATnB,kDAA6D,CAE7D,WAAY,CAEZ,kBAAmB,CAHnB,UAAY,CAMZ,cAAe,CADf,cAAe,CADf,eAAgB,CAKhB,OAAQ,CAER,eAAgB,CAThB,iBAAkB,CAQlB,uBAGF,CAEA,mBAEE,+BAA8C,CAD9C,0BAEF,CAEA,eACE,kBAAmB,CAEnB,wBAAyB,CAEzB,kBAAmB,CAHnB,aAAc,CAMd,cAAe,CADf,cAAe,CADf,eAAgB,CAIhB,eAAgB,CANhB,iBAAkB,CAKlB,uBAEF,CAEA,qBACE,kBAAmB,CACnB,aAAc,CACd,0BACF,CAGA,yBACE,uBAEE,yBAA0B,CAC1B,iCAAkC,CAFlC,YAGF,CAEA,yBAEE,WAAY,CACZ,6BAA8B,CAF9B,4BAGF,CAEA,cACE,sBACF,CAEA,eAME,0CAA4C,CAJ5C,wCAA0C,CAE1C,2BAA6B,CAD7B,yBAA2B,CAF3B,YAAa,CAIb,gCAEF,CAGA,kCACE,mBACF,CAEA,wCACE,4BAA8B,CAC9B,2BACF,CAEA,wCACE,4DAAwE,CAExE,kCAAoC,CADpC,2BAEF,CAEA,iBACE,cACF,CAEA,eACE,yBACF,CAEA,eACE,qBACF,CAEA,4BAEE,UACF,CACF,CAGA,yBACE,uBAEE,yBAA0B,CAC1B,iCAAkC,CAClC,oBAAkC,CAHlC,YAIF,CAEA,yBAIE,kBAAmB,CAFnB,WAAY,CACZ,6BAA8B,CAF9B,4BAIF,CAEA,cACE,sBACF,CAEA,eAME,0CAA4C,CAJ5C,wCAA0C,CAE1C,2BAA6B,CAD7B,yBAA2B,CAF3B,YAAa,CAIb,gCAEF,CAGA,kCACE,mBACF,CAEA,wCACE,4BAA8B,CAC9B,2BACF,CAEA,wCACE,4DAAwE,CAExE,kCAAoC,CADpC,2BAEF,CAEA,YAEE,sBAAuB,CADvB,qBAAsB,CAEtB,OACF,CACF", "sources": ["components/UpgradeRestrictionModal/UpgradeRestrictionModal.css", "components/SubscriptionExpiredModal/SubscriptionExpiredModal.css", "pages/user/Subscription/Subscription.css"], "sourcesContent": ["/* Upgrade Restriction Modal Styles */\n.upgrade-restriction-modal .ant-modal-content {\n  border-radius: 20px;\n  overflow: hidden;\n  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);\n  border: none;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  padding: 0;\n}\n\n.upgrade-restriction-modal .ant-modal-body {\n  padding: 0;\n}\n\n.upgrade-restriction-modal .ant-modal-close {\n  top: 15px;\n  right: 15px;\n  color: white;\n  font-size: 18px;\n  opacity: 0.8;\n  transition: all 0.3s ease;\n}\n\n.upgrade-restriction-modal .ant-modal-close:hover {\n  opacity: 1;\n  transform: scale(1.1);\n}\n\n.upgrade-restriction-content {\n  background: white;\n  border-radius: 20px;\n  margin: 3px;\n  overflow: hidden;\n}\n\n/* Header Section */\n.modal-header {\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  padding: 30px 30px 25px;\n  text-align: center;\n  color: white;\n  position: relative;\n}\n\n.crown-icon {\n  font-size: 48px;\n  color: #ffd700;\n  margin-bottom: 15px;\n  animation: bounce 2s infinite;\n}\n\n@keyframes bounce {\n  0%, 20%, 50%, 80%, 100% {\n    transform: translateY(0);\n  }\n  40% {\n    transform: translateY(-10px);\n  }\n  60% {\n    transform: translateY(-5px);\n  }\n}\n\n.modal-title {\n  font-size: 28px;\n  font-weight: 700;\n  margin: 0 0 8px 0;\n  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);\n}\n\n.modal-subtitle {\n  font-size: 16px;\n  opacity: 0.9;\n  margin: 0;\n  font-weight: 400;\n}\n\n/* Current Plan Card */\n.current-plan-card {\n  margin: 25px;\n  background: linear-gradient(135deg, #f8f9ff 0%, #e8f2ff 100%);\n  border-radius: 16px;\n  padding: 25px;\n  border: 2px solid #e6f0ff;\n  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.1);\n}\n\n.plan-header {\n  display: flex;\n  align-items: center;\n  margin-bottom: 20px;\n}\n\n.plan-icon {\n  font-size: 24px;\n  color: #52c41a;\n  margin-right: 15px;\n  background: rgba(82, 196, 26, 0.1);\n  width: 48px;\n  height: 48px;\n  border-radius: 12px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.plan-info {\n  flex: 1;\n}\n\n.plan-name {\n  font-size: 20px;\n  font-weight: 600;\n  color: #1a1a1a;\n  margin: 0 0 4px 0;\n}\n\n.plan-status {\n  color: #52c41a;\n  font-size: 14px;\n  font-weight: 500;\n  background: rgba(82, 196, 26, 0.1);\n  padding: 4px 12px;\n  border-radius: 20px;\n}\n\n/* Progress Section */\n.progress-section {\n  margin-bottom: 20px;\n}\n\n.progress-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 12px;\n}\n\n.progress-label {\n  font-size: 14px;\n  font-weight: 500;\n  color: #666;\n}\n\n.days-remaining {\n  font-size: 14px;\n  font-weight: 600;\n  color: #1890ff;\n  background: rgba(24, 144, 255, 0.1);\n  padding: 4px 10px;\n  border-radius: 12px;\n}\n\n/* Subscription Details */\n.subscription-details {\n  display: grid;\n  grid-template-columns: 1fr 1fr;\n  gap: 15px;\n}\n\n.detail-item {\n  display: flex;\n  align-items: center;\n  padding: 12px;\n  background: white;\n  border-radius: 10px;\n  border: 1px solid #e8f2ff;\n}\n\n.detail-icon {\n  font-size: 18px;\n  color: #1890ff;\n  margin-right: 12px;\n}\n\n.detail-content {\n  display: flex;\n  flex-direction: column;\n}\n\n.detail-label {\n  font-size: 12px;\n  color: #666;\n  margin-bottom: 2px;\n}\n\n.detail-value {\n  font-size: 14px;\n  font-weight: 600;\n  color: #1a1a1a;\n}\n\n/* Message Section */\n.message-section {\n  margin: 0 25px 25px;\n}\n\n.message-card {\n  background: linear-gradient(135deg, #fff7e6 0%, #fff2d9 100%);\n  border: 2px solid #ffd591;\n  border-radius: 12px;\n  padding: 20px;\n  margin-bottom: 20px;\n  text-align: center;\n}\n\n.message-title {\n  font-size: 18px;\n  font-weight: 600;\n  color: #d46b08;\n  margin: 0 0 10px 0;\n}\n\n.message-text {\n  font-size: 14px;\n  color: #8c4a00;\n  margin: 0;\n  line-height: 1.6;\n}\n\n/* Benefits List */\n.benefits-list {\n  background: #f9f9f9;\n  border-radius: 12px;\n  padding: 20px;\n}\n\n.benefits-title {\n  font-size: 16px;\n  font-weight: 600;\n  color: #1a1a1a;\n  margin: 0 0 15px 0;\n}\n\n.benefits {\n  list-style: none;\n  padding: 0;\n  margin: 0;\n}\n\n.benefit-item {\n  display: flex;\n  align-items: center;\n  padding: 8px 0;\n  font-size: 14px;\n  color: #333;\n}\n\n.benefit-icon {\n  color: #52c41a;\n  margin-right: 10px;\n  font-size: 16px;\n}\n\n/* Action Buttons */\n.action-buttons {\n  display: flex;\n  gap: 12px;\n  margin: 0 25px 25px;\n}\n\n.continue-button {\n  flex: 2;\n  height: 48px;\n  border-radius: 12px;\n  font-size: 16px;\n  font-weight: 600;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  border: none;\n  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);\n  transition: all 0.3s ease;\n}\n\n.continue-button:hover {\n  transform: translateY(-2px);\n  box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);\n}\n\n.close-button {\n  flex: 1;\n  height: 48px;\n  border-radius: 12px;\n  font-size: 16px;\n  font-weight: 500;\n  border: 2px solid #d9d9d9;\n  transition: all 0.3s ease;\n}\n\n.close-button:hover {\n  border-color: #1890ff;\n  color: #1890ff;\n  transform: translateY(-1px);\n}\n\n/* Footer Note */\n.footer-note {\n  background: #f0f8ff;\n  padding: 15px 25px;\n  border-top: 1px solid #e6f0ff;\n  text-align: center;\n}\n\n.footer-note p {\n  margin: 0;\n  font-size: 13px;\n  color: #666;\n  line-height: 1.5;\n}\n\n/* Responsive Design */\n@media (max-width: 768px) {\n  .upgrade-restriction-modal .ant-modal-content {\n    margin: 10px;\n    border-radius: 16px;\n  }\n  \n  .modal-header {\n    padding: 25px 20px 20px;\n  }\n  \n  .modal-title {\n    font-size: 24px;\n  }\n  \n  .current-plan-card,\n  .message-section {\n    margin: 20px;\n  }\n  \n  .subscription-details {\n    grid-template-columns: 1fr;\n  }\n  \n  .action-buttons {\n    flex-direction: column;\n    margin: 0 20px 20px;\n  }\n  \n  .continue-button,\n  .close-button {\n    flex: none;\n  }\n}\n", "/* Subscription Expired Modal Styles */\n.subscription-expired-modal .ant-modal-content {\n  border-radius: 20px;\n  overflow: hidden;\n  box-shadow: 0 25px 80px rgba(255, 77, 79, 0.3);\n  border: none;\n  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);\n  padding: 0;\n}\n\n.subscription-expired-modal .ant-modal-body {\n  padding: 0;\n}\n\n.expired-modal-content {\n  background: white;\n  border-radius: 20px;\n  margin: 3px;\n  overflow: hidden;\n  min-height: 500px;\n}\n\n/* Expired Header */\n.expired-header {\n  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);\n  padding: 30px 30px 25px;\n  text-align: center;\n  color: white;\n  position: relative;\n}\n\n.warning-icon {\n  font-size: 56px;\n  color: #fff3cd;\n  margin-bottom: 15px;\n  animation: pulse 2s infinite;\n}\n\n@keyframes pulse {\n  0% {\n    transform: scale(1);\n    opacity: 1;\n  }\n  50% {\n    transform: scale(1.1);\n    opacity: 0.8;\n  }\n  100% {\n    transform: scale(1);\n    opacity: 1;\n  }\n}\n\n.modal-title {\n  font-size: 32px;\n  font-weight: 700;\n  margin: 0 0 8px 0;\n  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);\n}\n\n.modal-subtitle {\n  font-size: 16px;\n  opacity: 0.9;\n  margin: 0;\n  font-weight: 400;\n}\n\n/* Expired Plan Card */\n.expired-plan-card {\n  margin: 25px;\n  background: linear-gradient(135deg, #fff5f5 0%, #ffe8e8 100%);\n  border-radius: 16px;\n  padding: 25px;\n  border: 2px solid #ffcccb;\n  box-shadow: 0 8px 25px rgba(255, 107, 107, 0.1);\n}\n\n.plan-header {\n  display: flex;\n  align-items: center;\n  margin-bottom: 20px;\n}\n\n.expired-icon {\n  font-size: 24px;\n  color: #ff4d4f;\n  margin-right: 15px;\n  background: rgba(255, 77, 79, 0.1);\n  width: 48px;\n  height: 48px;\n  border-radius: 12px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.plan-info {\n  flex: 1;\n}\n\n.plan-name {\n  font-size: 20px;\n  font-weight: 600;\n  color: #1a1a1a;\n  margin: 0 0 4px 0;\n}\n\n.expired-status {\n  color: #ff4d4f;\n  font-size: 14px;\n  font-weight: 500;\n  background: rgba(255, 77, 79, 0.1);\n  padding: 4px 12px;\n  border-radius: 20px;\n}\n\n/* Expiration Details */\n.expiration-details {\n  display: grid;\n  grid-template-columns: 1fr 1fr;\n  gap: 15px;\n  margin-bottom: 20px;\n}\n\n.detail-item {\n  display: flex;\n  align-items: center;\n  padding: 12px;\n  background: white;\n  border-radius: 10px;\n  border: 1px solid #ffcccb;\n}\n\n.detail-icon {\n  font-size: 18px;\n  color: #ff4d4f;\n  margin-right: 12px;\n}\n\n.detail-content {\n  display: flex;\n  flex-direction: column;\n}\n\n.detail-label {\n  font-size: 12px;\n  color: #666;\n  margin-bottom: 2px;\n}\n\n.detail-value {\n  font-size: 14px;\n  font-weight: 600;\n  color: #1a1a1a;\n}\n\n/* Progress Section */\n.progress-section {\n  margin-bottom: 0;\n}\n\n.progress-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 12px;\n}\n\n.progress-label {\n  font-size: 14px;\n  font-weight: 500;\n  color: #666;\n}\n\n.expired-badge {\n  font-size: 14px;\n  font-weight: 600;\n  color: #ff4d4f;\n  background: rgba(255, 77, 79, 0.1);\n  padding: 4px 10px;\n  border-radius: 12px;\n}\n\n/* Message Section */\n.message-section {\n  margin: 0 25px 25px;\n}\n\n.expired-message {\n  background: linear-gradient(135deg, #fff2e6 0%, #ffe7d9 100%);\n  border: 2px solid #ffb366;\n  border-radius: 12px;\n  padding: 20px;\n  margin-bottom: 20px;\n  text-align: center;\n}\n\n.message-title {\n  font-size: 18px;\n  font-weight: 600;\n  color: #d46b08;\n  margin: 0 0 10px 0;\n}\n\n.message-text {\n  font-size: 14px;\n  color: #8c4a00;\n  margin: 0;\n  line-height: 1.6;\n}\n\n/* Restricted Access */\n.restricted-access {\n  background: #f9f9f9;\n  border-radius: 12px;\n  padding: 20px;\n}\n\n.restricted-title {\n  font-size: 16px;\n  font-weight: 600;\n  color: #1a1a1a;\n  margin: 0 0 15px 0;\n}\n\n.restricted-list {\n  list-style: none;\n  padding: 0;\n  margin: 0;\n  display: grid;\n  grid-template-columns: 1fr 1fr;\n  gap: 8px;\n}\n\n.restricted-list li {\n  font-size: 14px;\n  color: #666;\n  padding: 4px 0;\n}\n\n/* Action Buttons */\n.action-buttons {\n  display: flex;\n  gap: 12px;\n  margin: 0 25px 25px;\n}\n\n.renew-button {\n  flex: 2;\n  height: 48px;\n  border-radius: 12px;\n  font-size: 16px;\n  font-weight: 600;\n  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);\n  border: none;\n  box-shadow: 0 4px 15px rgba(255, 107, 107, 0.3);\n  transition: all 0.3s ease;\n}\n\n.renew-button:hover {\n  transform: translateY(-2px);\n  box-shadow: 0 6px 20px rgba(255, 107, 107, 0.4);\n}\n\n.later-button {\n  flex: 1;\n  height: 48px;\n  border-radius: 12px;\n  font-size: 16px;\n  font-weight: 500;\n  border: 2px solid #d9d9d9;\n  transition: all 0.3s ease;\n}\n\n.later-button:hover {\n  border-color: #ff6b6b;\n  color: #ff6b6b;\n  transform: translateY(-1px);\n}\n\n/* Footer Note */\n.footer-note {\n  background: #f0f8ff;\n  padding: 15px 25px;\n  border-top: 1px solid #e6f0ff;\n  text-align: center;\n}\n\n.footer-note p {\n  margin: 0;\n  font-size: 13px;\n  color: #666;\n  line-height: 1.5;\n}\n\n/* Plan Selection Styles */\n.modal-header {\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  padding: 30px 30px 25px;\n  text-align: center;\n  color: white;\n  position: relative;\n}\n\n.crown-icon {\n  font-size: 48px;\n  color: #ffd700;\n  margin-bottom: 15px;\n  animation: bounce 2s infinite;\n}\n\n@keyframes bounce {\n  0%, 20%, 50%, 80%, 100% {\n    transform: translateY(0);\n  }\n  40% {\n    transform: translateY(-10px);\n  }\n  60% {\n    transform: translateY(-5px);\n  }\n}\n\n.plans-grid {\n  display: grid;\n  grid-template-columns: 1fr 1fr;\n  gap: 20px;\n  margin: 25px;\n}\n\n.plan-card {\n  background: white;\n  border: 2px solid #e8f2ff;\n  border-radius: 16px;\n  padding: 20px;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  position: relative;\n}\n\n.plan-card:hover {\n  border-color: #1890ff;\n  transform: translateY(-2px);\n  box-shadow: 0 8px 25px rgba(24, 144, 255, 0.15);\n}\n\n.plan-card.selected {\n  border-color: #1890ff;\n  background: linear-gradient(135deg, #f8f9ff 0%, #e8f2ff 100%);\n  box-shadow: 0 8px 25px rgba(24, 144, 255, 0.2);\n}\n\n.plan-title {\n  font-size: 18px;\n  font-weight: 600;\n  color: #1a1a1a;\n  margin: 0 0 10px 0;\n}\n\n.plan-badge {\n  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);\n  color: white;\n  font-size: 12px;\n  padding: 4px 8px;\n  border-radius: 12px;\n  font-weight: 500;\n}\n\n.plan-pricing {\n  margin: 15px 0;\n  text-align: center;\n}\n\n.price-main {\n  display: flex;\n  align-items: baseline;\n  justify-content: center;\n  margin-bottom: 5px;\n}\n\n.currency {\n  font-size: 14px;\n  color: #666;\n  margin-right: 4px;\n}\n\n.amount {\n  font-size: 24px;\n  font-weight: 700;\n  color: #1890ff;\n}\n\n.price-original {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  gap: 8px;\n  margin-bottom: 5px;\n}\n\n.original-price {\n  font-size: 12px;\n  color: #999;\n  text-decoration: line-through;\n}\n\n.discount {\n  font-size: 12px;\n  color: #52c41a;\n  font-weight: 600;\n}\n\n.duration {\n  font-size: 14px;\n  color: #666;\n  font-weight: 500;\n}\n\n.plan-features {\n  margin-top: 15px;\n}\n\n.feature-item {\n  display: flex;\n  align-items: center;\n  font-size: 13px;\n  color: #333;\n  margin-bottom: 8px;\n}\n\n.feature-icon {\n  color: #1890ff;\n  margin-right: 8px;\n  font-size: 12px;\n}\n\n.selected-indicator {\n  position: absolute;\n  top: 10px;\n  right: 10px;\n  background: #1890ff;\n  color: white;\n  font-size: 12px;\n  padding: 4px 8px;\n  border-radius: 12px;\n  font-weight: 500;\n}\n\n.continue-button {\n  flex: 2;\n  height: 48px;\n  border-radius: 12px;\n  font-size: 16px;\n  font-weight: 600;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  border: none;\n  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);\n  transition: all 0.3s ease;\n}\n\n.continue-button:hover:not(:disabled) {\n  transform: translateY(-2px);\n  box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);\n}\n\n.continue-button:disabled {\n  opacity: 0.5;\n  cursor: not-allowed;\n}\n\n.back-button {\n  flex: 1;\n  height: 48px;\n  border-radius: 12px;\n  font-size: 16px;\n  font-weight: 500;\n  border: 2px solid #d9d9d9;\n  transition: all 0.3s ease;\n}\n\n.back-button:hover {\n  border-color: #1890ff;\n  color: #1890ff;\n  transform: translateY(-1px);\n}\n\n/* Responsive Design */\n@media (max-width: 768px) {\n  .subscription-expired-modal .ant-modal-content {\n    margin: 10px;\n    border-radius: 16px;\n  }\n  \n  .modal-header {\n    padding: 25px 20px 20px;\n  }\n  \n  .modal-title {\n    font-size: 28px;\n  }\n  \n  .expired-plan-card,\n  .message-section,\n  .plans-grid {\n    margin: 20px;\n  }\n  \n  .expiration-details,\n  .restricted-list {\n    grid-template-columns: 1fr;\n  }\n  \n  .plans-grid {\n    grid-template-columns: 1fr;\n  }\n  \n  .action-buttons {\n    flex-direction: column;\n    margin: 0 20px 20px;\n  }\n  \n  .renew-button,\n  .later-button,\n  .continue-button,\n  .back-button {\n    flex: none;\n  }\n}\n", "/* ===== RESPONSIVE SUBSCRIPTION PAGE STYLES ===== */\n.subscription-page {\n  min-height: 100vh;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  padding: 2rem 1rem;\n  position: relative;\n  overflow-x: hidden;\n}\n\n/* Enhanced Responsive Padding - Mobile First Approach */\n@media (max-width: 480px) {\n  .subscription-page {\n    padding: 0.75rem 0.5rem;\n    min-height: 100vh;\n  }\n}\n\n@media (min-width: 481px) and (max-width: 768px) {\n  .subscription-page {\n    padding: 1rem 0.75rem;\n  }\n}\n\n@media (min-width: 769px) and (max-width: 1024px) {\n  .subscription-page {\n    padding: 1.5rem 1rem;\n  }\n}\n\n@media (min-width: 1025px) and (max-width: 1440px) {\n  .subscription-page {\n    padding: 2rem 1.5rem;\n  }\n}\n\n@media (min-width: 1441px) {\n  .subscription-page {\n    padding: 2.5rem 2rem;\n    max-width: 1600px;\n    margin: 0 auto;\n  }\n}\n\n/* Enhanced Subscription Container */\n.subscription-container {\n  max-width: 1200px;\n  margin: 0 auto;\n  width: 100%;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n}\n\n/* Enhanced Subscription Header */\n.subscription-header {\n  text-align: center;\n  margin-bottom: 3rem;\n  width: 100%;\n  max-width: 900px;\n  padding: 0 1rem;\n}\n\n.page-title {\n  font-size: 2.5rem;\n  font-weight: 700;\n  color: white;\n  margin-bottom: 1rem;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  gap: 1rem;\n  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);\n  line-height: 1.2;\n}\n\n.title-icon {\n  color: #fbbf24;\n  flex-shrink: 0;\n}\n\n.page-subtitle {\n  font-size: 1.1rem;\n  color: rgba(255, 255, 255, 0.9);\n  max-width: 600px;\n  margin: 0 auto;\n  line-height: 1.6;\n}\n\n/* Enhanced Responsive Header - Mobile First */\n/* Small Mobile (320px - 480px) */\n@media (max-width: 480px) {\n  .subscription-header {\n    margin-bottom: 1.5rem;\n    padding: 0 0.5rem;\n  }\n\n  .page-title {\n    font-size: 1.5rem;\n    flex-direction: column;\n    gap: 0.5rem;\n    margin-bottom: 0.5rem;\n  }\n\n  .title-icon {\n    font-size: 1.75rem;\n  }\n\n  .page-subtitle {\n    font-size: 0.9rem;\n    line-height: 1.5;\n    padding: 0;\n  }\n}\n\n/* Mobile (481px - 768px) */\n@media (min-width: 481px) and (max-width: 768px) {\n  .subscription-header {\n    margin-bottom: 2rem;\n  }\n\n  .page-title {\n    font-size: 1.75rem;\n    flex-direction: column;\n    gap: 0.5rem;\n  }\n\n  .title-icon {\n    font-size: 2rem;\n  }\n\n  .page-subtitle {\n    font-size: 1rem;\n    padding: 0 0.5rem;\n  }\n}\n\n/* Tablet (769px - 1024px) */\n@media (min-width: 769px) and (max-width: 1024px) {\n  .subscription-header {\n    margin-bottom: 2.5rem;\n  }\n\n  .page-title {\n    font-size: 2.25rem;\n  }\n\n  .title-icon {\n    font-size: 2.25rem;\n  }\n\n  .page-subtitle {\n    font-size: 1.05rem;\n    max-width: 550px;\n  }\n}\n\n/* Laptop (1025px - 1440px) */\n@media (min-width: 1025px) and (max-width: 1440px) {\n  .page-title {\n    font-size: 2.75rem;\n  }\n\n  .title-icon {\n    font-size: 2.5rem;\n  }\n\n  .page-subtitle {\n    font-size: 1.15rem;\n    max-width: 650px;\n  }\n}\n\n/* Desktop (1441px+) */\n@media (min-width: 1441px) {\n  .subscription-header {\n    margin-bottom: 3.5rem;\n  }\n\n  .page-title {\n    font-size: 3rem;\n  }\n\n  .title-icon {\n    font-size: 2.75rem;\n  }\n\n  .page-subtitle {\n    font-size: 1.2rem;\n    max-width: 700px;\n  }\n}\n\n.section-title {\n  font-size: 1.8rem;\n  font-weight: 600;\n  color: #1f2937;\n  margin-bottom: 0.5rem;\n  text-align: center;\n}\n\n.section-subtitle {\n  font-size: 1rem;\n  color: #6b7280;\n  text-align: center;\n  margin-bottom: 2rem;\n  max-width: 600px;\n  margin-left: auto;\n  margin-right: auto;\n}\n\n/* Current Subscription */\n.current-subscription {\n  margin-bottom: 3rem;\n}\n\n.subscription-card {\n  background: white;\n  border-radius: 1rem;\n  padding: 2rem;\n  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);\n  border: 2px solid transparent;\n  transition: all 0.3s ease;\n}\n\n.subscription-card.active {\n  border-color: #10b981;\n  background: linear-gradient(135deg, #ecfdf5, #f0fdf4);\n}\n\n.subscription-card.expired {\n  border-color: #ef4444;\n  background: linear-gradient(135deg, #fef2f2, #fef7f7);\n}\n\n.subscription-card.none {\n  border-color: #6b7280;\n  background: linear-gradient(135deg, #f9fafb, #f3f4f6);\n}\n\n.subscription-status {\n  display: flex;\n  align-items: center;\n  gap: 0.75rem;\n  margin-bottom: 1.5rem;\n}\n\n.status-icon {\n  font-size: 1.5rem;\n}\n\n.status-icon.active {\n  color: #10b981;\n}\n\n.status-icon.expired {\n  color: #ef4444;\n}\n\n.status-icon.none {\n  color: #6b7280;\n}\n\n.status-text {\n  font-size: 1.25rem;\n  font-weight: 600;\n  color: #1f2937;\n}\n\n.subscription-details {\n  display: grid;\n  gap: 1rem;\n}\n\n.detail-item {\n  display: flex;\n  align-items: center;\n  gap: 0.75rem;\n  font-size: 1rem;\n  color: #374151;\n}\n\n.detail-icon {\n  color: #6b7280;\n  font-size: 1rem;\n}\n\n.renewal-message,\n.upgrade-message {\n  margin-top: 1rem;\n  padding: 1rem;\n  background: rgba(59, 130, 246, 0.1);\n  border-radius: 0.5rem;\n  color: #1e40af;\n  font-weight: 500;\n}\n\n/* Enhanced Responsive Plans Grid */\n.plans-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));\n  gap: 2rem;\n  margin-top: 2rem;\n  max-width: 1200px;\n  margin-left: auto;\n  margin-right: auto;\n  width: 100%;\n  padding: 0 1rem;\n}\n\n/* Enhanced Responsive Grid - Mobile First */\n/* Small Mobile (320px - 480px) */\n@media (max-width: 480px) {\n  .plans-grid {\n    grid-template-columns: 1fr;\n    gap: 1rem;\n    margin-top: 1.5rem;\n    padding: 0 0.5rem;\n    max-width: 100%;\n  }\n}\n\n/* Mobile (481px - 768px) */\n@media (min-width: 481px) and (max-width: 768px) {\n  .plans-grid {\n    grid-template-columns: 1fr;\n    gap: 1.5rem;\n    margin-top: 1.5rem;\n    padding: 0 1rem;\n    max-width: 500px;\n  }\n}\n\n/* Tablet (769px - 1024px) */\n@media (min-width: 769px) and (max-width: 1024px) {\n  .plans-grid {\n    grid-template-columns: repeat(2, 1fr);\n    gap: 1.75rem;\n    max-width: 800px;\n    padding: 0 1rem;\n  }\n}\n\n/* Laptop (1025px - 1440px) */\n@media (min-width: 1025px) and (max-width: 1440px) {\n  .plans-grid {\n    grid-template-columns: repeat(3, 1fr);\n    gap: 2rem;\n    max-width: 1200px;\n    padding: 0 1.5rem;\n  }\n}\n\n/* Desktop (1441px+) */\n@media (min-width: 1441px) {\n  .plans-grid {\n    grid-template-columns: repeat(3, 1fr);\n    gap: 2.5rem;\n    max-width: 1400px;\n    padding: 0 2rem;\n  }\n}\n\n/* Enhanced Responsive Plan Card */\n.plan-card {\n  background: white;\n  border-radius: 1.5rem;\n  padding: 2.5rem 2rem;\n  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.08);\n  border: 2px solid #f1f5f9;\n  transition: all 0.4s ease;\n  position: relative;\n  overflow: hidden;\n  display: flex;\n  flex-direction: column;\n  height: 100%;\n  width: 100%;\n  max-width: 100%;\n}\n\n.plan-card:hover {\n  border-color: #3b82f6;\n  transform: translateY(-8px);\n  box-shadow: 0 20px 40px rgba(59, 130, 246, 0.15);\n}\n\n/* Enhanced Plan Card Responsive Styles */\n/* Small Mobile (320px - 480px) */\n@media (max-width: 480px) {\n  .plan-card {\n    padding: 1.25rem 1rem;\n    border-radius: 1rem;\n    margin: 0;\n  }\n\n  .plan-card:hover {\n    transform: translateY(-4px);\n  }\n}\n\n/* Mobile (481px - 768px) */\n@media (min-width: 481px) and (max-width: 768px) {\n  .plan-card {\n    padding: 1.5rem 1.25rem;\n    border-radius: 1.25rem;\n    margin: 0;\n  }\n\n  .plan-card:hover {\n    transform: translateY(-6px);\n  }\n}\n\n/* Tablet (769px - 1024px) */\n@media (min-width: 769px) and (max-width: 1024px) {\n  .plan-card {\n    padding: 2rem 1.5rem;\n    border-radius: 1.5rem;\n    margin: 0;\n  }\n}\n\n/* Laptop (1025px - 1440px) */\n@media (min-width: 1025px) and (max-width: 1440px) {\n  .plan-card {\n    padding: 2.5rem 2rem;\n    border-radius: 1.5rem;\n    margin: 0;\n  }\n}\n\n/* Desktop (1441px+) */\n@media (min-width: 1441px) {\n  .plan-card {\n    padding: 3rem 2.5rem;\n    border-radius: 1.75rem;\n    margin: 0;\n  }\n}\n\n.plan-header {\n  text-align: center;\n  margin-bottom: 2rem;\n  position: relative;\n}\n\n.plan-title {\n  font-size: 1.75rem;\n  font-weight: 800;\n  color: #1e293b;\n  margin-bottom: 0.75rem;\n  letter-spacing: -0.025em;\n}\n\n.plan-badge {\n  position: absolute;\n  top: -1.5rem;\n  right: -1.5rem;\n  background: linear-gradient(135deg, #ef4444, #dc2626);\n  color: white;\n  padding: 0.5rem 1rem;\n  border-radius: 2rem;\n  font-size: 0.875rem;\n  font-weight: 700;\n  transform: rotate(12deg);\n  box-shadow: 0 4px 12px rgba(239, 68, 68, 0.3);\n  animation: pulse 2s infinite;\n}\n\n@keyframes pulse {\n  0%, 100% { transform: rotate(12deg) scale(1); }\n  50% { transform: rotate(12deg) scale(1.05); }\n}\n\n/* Responsive Plan Header Styles */\n/* Small Mobile (320px - 480px) */\n@media (max-width: 480px) {\n  .plan-header {\n    margin-bottom: 1.25rem;\n  }\n\n  .plan-title {\n    font-size: 1.25rem;\n    margin-bottom: 0.5rem;\n  }\n\n  .plan-badge {\n    top: -1rem;\n    right: -1rem;\n    padding: 0.3rem 0.6rem;\n    font-size: 0.7rem;\n    border-radius: 1.5rem;\n  }\n}\n\n/* Mobile (481px - 768px) */\n@media (min-width: 481px) and (max-width: 768px) {\n  .plan-header {\n    margin-bottom: 1.5rem;\n  }\n\n  .plan-title {\n    font-size: 1.5rem;\n    margin-bottom: 0.6rem;\n  }\n\n  .plan-badge {\n    top: -1.25rem;\n    right: -1.25rem;\n    padding: 0.4rem 0.8rem;\n    font-size: 0.75rem;\n  }\n}\n\n/* Tablet (769px - 1024px) */\n@media (min-width: 769px) and (max-width: 1024px) {\n  .plan-title {\n    font-size: 1.625rem;\n  }\n}\n\n/* Desktop (1441px+) */\n@media (min-width: 1441px) {\n  .plan-header {\n    margin-bottom: 2.25rem;\n  }\n\n  .plan-title {\n    font-size: 2rem;\n    margin-bottom: 1rem;\n  }\n\n  .plan-badge {\n    top: -1.75rem;\n    right: -1.75rem;\n    padding: 0.6rem 1.2rem;\n    font-size: 1rem;\n  }\n}\n\n.plan-pricing {\n  text-align: center;\n  margin-bottom: 2.5rem;\n  padding: 1.5rem;\n  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);\n  border-radius: 1rem;\n  border: 1px solid #e2e8f0;\n}\n\n.price-display {\n  margin-bottom: 1rem;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  gap: 1rem;\n  flex-wrap: wrap;\n}\n\n.current-price {\n  font-size: 2.5rem;\n  font-weight: 900;\n  color: #059669;\n  display: flex;\n  align-items: baseline;\n  gap: 0.25rem;\n}\n\n.currency {\n  font-size: 1.25rem;\n  font-weight: 600;\n  color: #6b7280;\n}\n\n.original-price {\n  font-size: 1.5rem;\n  color: #ef4444;\n  text-decoration: line-through;\n  position: relative;\n  font-weight: 600;\n}\n\n.original-price::before {\n  content: \"❌\";\n  position: absolute;\n  top: -0.5rem;\n  right: -0.5rem;\n  font-size: 0.875rem;\n  animation: bounce 1s infinite;\n}\n\n@keyframes bounce {\n  0%, 100% { transform: translateY(0); }\n  50% { transform: translateY(-2px); }\n}\n\n.discount-badge {\n  background: linear-gradient(135deg, #ef4444, #dc2626);\n  color: white;\n  padding: 0.25rem 0.75rem;\n  border-radius: 1rem;\n  font-size: 0.875rem;\n  font-weight: 700;\n  margin-left: 0.5rem;\n}\n\n.plan-duration {\n  color: #475569;\n  font-weight: 600;\n  font-size: 1.125rem;\n  margin-top: 0.5rem;\n}\n\n.duration-highlight {\n  color: #3b82f6;\n  font-weight: 700;\n}\n\n/* Responsive Plan Pricing Styles */\n/* Small Mobile (320px - 480px) */\n@media (max-width: 480px) {\n  .plan-pricing {\n    margin-bottom: 1.5rem;\n    padding: 1rem;\n  }\n\n  .price-display {\n    margin-bottom: 0.75rem;\n    gap: 0.5rem;\n    flex-direction: column;\n    align-items: center;\n  }\n\n  .current-price {\n    font-size: 1.75rem;\n  }\n\n  .currency {\n    font-size: 1rem;\n  }\n\n  .original-price {\n    font-size: 1.125rem;\n  }\n\n  .original-price::before {\n    font-size: 0.75rem;\n    top: -0.3rem;\n    right: -0.3rem;\n  }\n\n  .discount-badge {\n    font-size: 0.75rem;\n    padding: 0.2rem 0.5rem;\n    margin-left: 0.25rem;\n  }\n\n  .plan-duration {\n    font-size: 0.9rem;\n  }\n}\n\n/* Mobile (481px - 768px) */\n@media (min-width: 481px) and (max-width: 768px) {\n  .plan-pricing {\n    margin-bottom: 2rem;\n    padding: 1.25rem;\n  }\n\n  .price-display {\n    gap: 0.75rem;\n  }\n\n  .current-price {\n    font-size: 2rem;\n  }\n\n  .currency {\n    font-size: 1.1rem;\n  }\n\n  .original-price {\n    font-size: 1.25rem;\n  }\n\n  .discount-badge {\n    font-size: 0.8rem;\n    padding: 0.2rem 0.6rem;\n  }\n\n  .plan-duration {\n    font-size: 1rem;\n  }\n}\n\n/* Tablet (769px - 1024px) */\n@media (min-width: 769px) and (max-width: 1024px) {\n  .current-price {\n    font-size: 2.25rem;\n  }\n\n  .original-price {\n    font-size: 1.375rem;\n  }\n}\n\n/* Desktop (1441px+) */\n@media (min-width: 1441px) {\n  .plan-pricing {\n    margin-bottom: 3rem;\n    padding: 2rem;\n  }\n\n  .price-display {\n    margin-bottom: 1.25rem;\n    gap: 1.25rem;\n  }\n\n  .current-price {\n    font-size: 3rem;\n  }\n\n  .currency {\n    font-size: 1.5rem;\n  }\n\n  .original-price {\n    font-size: 1.75rem;\n  }\n\n  .original-price::before {\n    font-size: 1rem;\n    top: -0.6rem;\n    right: -0.6rem;\n  }\n\n  .discount-badge {\n    font-size: 1rem;\n    padding: 0.3rem 0.9rem;\n    margin-left: 0.75rem;\n  }\n\n  .plan-duration {\n    font-size: 1.25rem;\n    margin-top: 0.75rem;\n  }\n}\n\n.plan-features {\n  margin-bottom: 2rem;\n}\n\n.feature-item {\n  display: flex;\n  align-items: center;\n  gap: 0.75rem;\n  margin-bottom: 0.75rem;\n  font-size: 0.95rem;\n  color: #374151;\n}\n\n.feature-icon {\n  color: #10b981;\n  font-size: 0.9rem;\n  flex-shrink: 0;\n}\n\n.select-plan-btn {\n  width: 100%;\n  background: linear-gradient(135deg, #3b82f6, #1d4ed8);\n  color: white;\n  border: none;\n  border-radius: 0.75rem;\n  padding: 1rem 1.5rem;\n  font-size: 1rem;\n  font-weight: 600;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  gap: 0.5rem;\n}\n\n.select-plan-btn:hover {\n  background: linear-gradient(135deg, #1d4ed8, #1e40af);\n  transform: translateY(-1px);\n}\n\n.select-plan-btn:disabled {\n  opacity: 0.6;\n  cursor: not-allowed;\n  transform: none;\n}\n\n/* Responsive Plan Features & Button Styles */\n/* Small Mobile (320px - 480px) */\n@media (max-width: 480px) {\n  .plan-features {\n    margin-bottom: 1.25rem;\n  }\n\n  .feature-item {\n    gap: 0.5rem;\n    margin-bottom: 0.5rem;\n    font-size: 0.85rem;\n  }\n\n  .feature-icon {\n    font-size: 0.8rem;\n  }\n\n  .select-plan-btn {\n    padding: 0.875rem 1.25rem;\n    font-size: 0.9rem;\n    border-radius: 0.625rem;\n  }\n}\n\n/* Mobile (481px - 768px) */\n@media (min-width: 481px) and (max-width: 768px) {\n  .plan-features {\n    margin-bottom: 1.5rem;\n  }\n\n  .feature-item {\n    gap: 0.625rem;\n    margin-bottom: 0.625rem;\n    font-size: 0.9rem;\n  }\n\n  .feature-icon {\n    font-size: 0.85rem;\n  }\n\n  .select-plan-btn {\n    padding: 0.9375rem 1.375rem;\n    font-size: 0.95rem;\n    border-radius: 0.6875rem;\n  }\n}\n\n/* Tablet (769px - 1024px) */\n@media (min-width: 769px) and (max-width: 1024px) {\n  .feature-item {\n    font-size: 0.9375rem;\n  }\n}\n\n/* Desktop (1441px+) */\n@media (min-width: 1441px) {\n  .plan-features {\n    margin-bottom: 2.5rem;\n  }\n\n  .feature-item {\n    gap: 1rem;\n    margin-bottom: 1rem;\n    font-size: 1.0625rem;\n  }\n\n  .feature-icon {\n    font-size: 1rem;\n  }\n\n  .select-plan-btn {\n    padding: 1.25rem 2rem;\n    font-size: 1.125rem;\n    border-radius: 1rem;\n    gap: 0.75rem;\n  }\n}\n\n.btn-icon {\n  font-size: 0.9rem;\n}\n\n/* Phone Warning */\n.phone-warning {\n  margin-top: 2rem;\n}\n\n.warning-content {\n  background: linear-gradient(135deg, #fef3c7, #fde68a);\n  border: 2px solid #f59e0b;\n  border-radius: 1rem;\n  padding: 1.5rem;\n  display: flex;\n  align-items: center;\n  gap: 1rem;\n}\n\n.warning-icon {\n  color: #d97706;\n  font-size: 1.5rem;\n  flex-shrink: 0;\n}\n\n.warning-content h4 {\n  margin: 0 0 0.5rem 0;\n  color: #92400e;\n  font-weight: 600;\n}\n\n.warning-content p {\n  margin: 0 0 1rem 0;\n  color: #a16207;\n}\n\n.update-phone-btn {\n  background: #f59e0b;\n  color: white;\n  border: none;\n  border-radius: 0.5rem;\n  padding: 0.5rem 1rem;\n  font-weight: 600;\n  cursor: pointer;\n  transition: background 0.3s ease;\n}\n\n.update-phone-btn:hover {\n  background: #d97706;\n}\n\n/* Loading State */\n.loading-state {\n  text-align: center;\n  padding: 3rem;\n  color: #6b7280;\n}\n\n.spinner {\n  width: 40px;\n  height: 40px;\n  border: 4px solid #e5e7eb;\n  border-top: 4px solid #3b82f6;\n  border-radius: 50%;\n  animation: spin 1s linear infinite;\n  margin: 0 auto 1rem;\n}\n\n/* No Plans State */\n.no-plans-state {\n  text-align: center;\n  padding: 3rem;\n  color: #6b7280;\n}\n\n.no-plans-icon {\n  font-size: 3rem;\n  margin-bottom: 1rem;\n}\n\n.no-plans-state h3 {\n  color: #374151;\n  margin-bottom: 1rem;\n}\n\n.no-plans-state p {\n  margin-bottom: 2rem;\n  max-width: 400px;\n  margin-left: auto;\n  margin-right: auto;\n}\n\n.refresh-btn {\n  background: #3b82f6;\n  color: white;\n  border: none;\n  border-radius: 0.5rem;\n  padding: 0.75rem 1.5rem;\n  font-weight: 600;\n  cursor: pointer;\n  transition: background 0.3s ease;\n}\n\n.refresh-btn:hover {\n  background: #2563eb;\n}\n\n@keyframes spin {\n  0% { transform: rotate(0deg); }\n  100% { transform: rotate(360deg); }\n}\n\n/* Payment Modal Styles */\n.payment-modal-overlay {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: rgba(0, 0, 0, 0.8);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  z-index: 10000;\n  backdrop-filter: blur(5px);\n}\n\n.payment-modal {\n  background: white;\n  border-radius: 20px;\n  padding: 1.5rem;\n  max-width: 500px;\n  width: 90%;\n  max-height: 95vh;\n  overflow: hidden;\n  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.3);\n  animation: modalSlideIn 0.3s ease-out;\n  display: flex;\n  flex-direction: column;\n}\n\n.payment-modal.success-modal {\n  max-width: 650px;\n  max-height: 95vh;\n  overflow: hidden;\n}\n\n@keyframes modalSlideIn {\n  from {\n    opacity: 0;\n    transform: translateY(-50px) scale(0.9);\n  }\n  to {\n    opacity: 1;\n    transform: translateY(0) scale(1);\n  }\n}\n\n.payment-modal-content {\n  text-align: center;\n  position: relative;\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n  justify-content: space-between;\n  overflow: hidden;\n}\n\n/* Payment Modal Close Button */\n.payment-modal-close {\n  position: absolute;\n  top: -10px;\n  right: -10px;\n  background: rgba(255, 255, 255, 0.9);\n  border: 2px solid #e5e7eb;\n  color: #666;\n  font-size: 18px;\n  font-weight: bold;\n  width: 35px;\n  height: 35px;\n  border-radius: 50%;\n  cursor: pointer;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  transition: all 0.3s ease;\n  z-index: 10;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n}\n\n.payment-modal-close:hover {\n  background: rgba(255, 77, 79, 0.1);\n  color: #ff4d4f;\n  border-color: #ff4d4f;\n  transform: scale(1.1);\n}\n\n/* Try Again Section */\n.payment-try-again {\n  margin-top: 20px;\n  padding: 15px;\n  background: rgba(255, 193, 7, 0.1);\n  border-radius: 10px;\n  border: 1px solid rgba(255, 193, 7, 0.3);\n}\n\n.connection-issue {\n  color: #d46b08;\n  font-size: 14px;\n  margin-bottom: 10px;\n  font-weight: 500;\n}\n\n.try-again-button {\n  background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%);\n  color: white;\n  border: none;\n  padding: 10px 20px;\n  border-radius: 8px;\n  font-size: 14px;\n  font-weight: 600;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.3);\n}\n\n.try-again-button:hover {\n  background: linear-gradient(135deg, #096dd9 0%, #0050b3 100%);\n  transform: translateY(-1px);\n  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.4);\n}\n\n/* Phone Instructions */\n.phone-instruction {\n  background: rgba(24, 144, 255, 0.05);\n  border: 1px solid rgba(24, 144, 255, 0.2);\n  border-radius: 12px;\n  padding: 15px;\n  margin: 10px 0;\n  text-align: center;\n}\n\n.phone-instruction h4 {\n  color: #1890ff;\n  font-size: 16px;\n  font-weight: 600;\n  margin: 0 0 8px 0;\n}\n\n.phone-number {\n  color: #1890ff;\n  font-size: 16px;\n  font-weight: 600;\n  background: rgba(24, 144, 255, 0.1);\n  padding: 8px 16px;\n  border-radius: 20px;\n  display: inline-block;\n  margin-bottom: 15px;\n}\n\n.instruction-text {\n  color: #4a5568;\n  font-size: 14px;\n  line-height: 1.6;\n  margin: 0;\n  text-align: left;\n}\n\n.try-again-text {\n  color: #8c4a00;\n  font-size: 13px;\n  margin-bottom: 10px;\n  font-style: italic;\n}\n\n/* Professional Payment Modal Animations */\n@keyframes spin {\n  0% { transform: rotate(0deg); }\n  100% { transform: rotate(360deg); }\n}\n\n@keyframes bounce {\n  0%, 20%, 50%, 80%, 100% {\n    transform: translateY(0);\n  }\n  40% {\n    transform: translateY(-10px);\n  }\n  60% {\n    transform: translateY(-5px);\n  }\n}\n\n.professional-payment-modal {\n  animation: professionalSlideIn 0.4s cubic-bezier(0.4, 0, 0.2, 1);\n}\n\n.professional-success-modal {\n  animation: successSlideIn 0.5s cubic-bezier(0.4, 0, 0.2, 1);\n}\n\n@keyframes professionalSlideIn {\n  from {\n    opacity: 0;\n    transform: translateY(-30px) scale(0.95);\n  }\n  to {\n    opacity: 1;\n    transform: translateY(0) scale(1);\n  }\n}\n\n@keyframes successSlideIn {\n  from {\n    opacity: 0;\n    transform: translateY(-40px) scale(0.9);\n  }\n  to {\n    opacity: 1;\n    transform: translateY(0) scale(1);\n  }\n}\n\n/* Enable background scrolling when modals are open for better UX */\nbody.modal-open {\n  overflow: auto !important; /* Allow scrolling */\n  position: static !important; /* Normal positioning */\n  width: auto !important;\n  height: auto !important;\n}\n\n/* Ensure subscription page remains scrollable behind modal */\n.subscription-page {\n  position: relative;\n  z-index: 1; /* Below modal but above other content */\n}\n\n/* Best Design Modal Overlay - Perfect Centering */\n.modal-overlay-best {\n  position: fixed !important;\n  top: 0 !important;\n  left: 0 !important;\n  width: 100vw !important;\n  height: 100vh !important;\n  z-index: 10000 !important;\n  display: flex !important;\n  align-items: center !important;\n  justify-content: center !important;\n  box-sizing: border-box !important;\n}\n\n/* Best Design Modal Container */\n.modal-container-best {\n  position: relative !important;\n  margin: 0 !important;\n  transform: translateZ(0) !important;\n  animation: modalSlideInBest 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;\n}\n\n@keyframes modalSlideInBest {\n  from {\n    opacity: 0;\n    transform: translateY(-20px) scale(0.95);\n  }\n  to {\n    opacity: 1;\n    transform: translateY(0) scale(1);\n  }\n}\n\n/* Best Design Responsive - Perfect Centering All Devices */\n\n/* Mobile (320px - 768px) */\n@media (max-width: 768px) {\n  .modal-overlay-best {\n    padding: 16px !important;\n  }\n\n  .modal-container-best {\n    width: 100% !important;\n    max-width: calc(100vw - 32px) !important;\n    max-height: calc(100vh - 32px) !important;\n    border-radius: 16px !important;\n  }\n\n  .modal-container-best > div:first-child {\n    padding: 16px !important;\n    border-radius: 16px 16px 0 0 !important;\n  }\n\n  .modal-container-best > div:last-child > div {\n    padding: 16px !important;\n    gap: 12px !important;\n  }\n\n  .try-again-button {\n    width: 100% !important;\n    max-width: none !important;\n    padding: 12px 16px !important;\n    font-size: 14px !important;\n  }\n}\n\n/* Tablet (769px - 1024px) */\n@media (min-width: 769px) and (max-width: 1024px) {\n  .modal-overlay-best {\n    padding: 24px !important;\n  }\n\n  .modal-container-best {\n    max-width: 500px !important;\n    max-height: 90vh !important;\n    border-radius: 20px !important;\n  }\n\n  .modal-container-best.success-modal-best {\n    max-width: 600px !important;\n  }\n\n  .modal-container-best > div:first-child {\n    padding: 20px !important;\n    border-radius: 20px 20px 0 0 !important;\n  }\n\n  .modal-container-best > div:last-child > div {\n    padding: 20px !important;\n    gap: 16px !important;\n  }\n\n  .try-again-button {\n    max-width: 200px !important;\n    margin: 0 auto !important;\n  }\n}\n\n/* Desktop (1025px+) */\n@media (min-width: 1025px) {\n  .modal-overlay-best {\n    padding: 32px !important;\n  }\n\n  .modal-container-best {\n    max-width: 480px !important;\n    max-height: 85vh !important;\n    border-radius: 24px !important;\n  }\n\n  .modal-container-best.success-modal-best {\n    max-width: 600px !important;\n    max-height: 90vh !important;\n  }\n\n  .modal-container-best > div:first-child {\n    padding: 24px !important;\n    border-radius: 24px 24px 0 0 !important;\n  }\n\n  .modal-container-best > div:last-child > div {\n    padding: 24px !important;\n    gap: 20px !important;\n  }\n\n  .try-again-button {\n    max-width: 200px !important;\n    margin: 0 auto !important;\n  }\n}\n\n/* Responsive Design for Payment Modal */\n@media (max-width: 768px) {\n  /* Mobile Styles */\n  .payment-modal {\n    width: 95%;\n    max-width: 400px;\n    margin: 10px;\n    max-height: 90vh;\n    overflow-y: auto;\n  }\n\n  .payment-modal-content {\n    padding: 20px 15px;\n  }\n\n  .payment-modal-close {\n    top: -5px;\n    right: -5px;\n    width: 30px;\n    height: 30px;\n    font-size: 16px;\n  }\n\n  .phone-instruction {\n    padding: 15px;\n    margin: 10px 0;\n  }\n\n  .phone-instruction h4 {\n    font-size: 16px;\n  }\n\n  .phone-number {\n    font-size: 14px;\n    padding: 6px 12px;\n  }\n\n  .instruction-text {\n    font-size: 13px;\n  }\n\n  .payment-try-again {\n    padding: 12px;\n    margin-top: 15px;\n  }\n\n  .try-again-button {\n    width: 100%;\n    padding: 12px 16px;\n    font-size: 14px;\n  }\n\n  .payment-processing-animation {\n    transform: scale(0.8);\n  }\n}\n\n@media (min-width: 769px) and (max-width: 1024px) {\n  /* Tablet Styles */\n  .payment-modal {\n    width: 80%;\n    max-width: 500px;\n  }\n\n  .payment-modal-content {\n    padding: 25px 20px;\n  }\n\n  .phone-instruction {\n    padding: 18px;\n  }\n\n  .phone-instruction h4 {\n    font-size: 17px;\n  }\n\n  .phone-number {\n    font-size: 15px;\n  }\n\n  .try-again-button {\n    padding: 11px 18px;\n    font-size: 14px;\n  }\n}\n\n@media (min-width: 1025px) {\n  /* Desktop Styles */\n  .payment-modal {\n    width: 60%;\n    max-width: 550px;\n  }\n\n  .payment-modal-content {\n    padding: 30px 25px;\n  }\n\n  .phone-instruction {\n    padding: 22px;\n  }\n\n  .phone-instruction h4 {\n    font-size: 18px;\n  }\n\n  .phone-number {\n    font-size: 16px;\n  }\n\n  .try-again-button {\n    padding: 12px 20px;\n    font-size: 15px;\n  }\n}\n\n/* Enhanced Plan Card Responsive Styles */\n\n/* Mobile Devices (320px - 480px) */\n@media (max-width: 480px) {\n  .plans-grid {\n    grid-template-columns: 1fr !important;\n    gap: 1rem !important;\n    padding: 0 0.5rem !important;\n  }\n\n  .plan-card {\n    padding: 1.25rem 1rem !important;\n    margin: 0 !important;\n    border-radius: 1rem !important;\n  }\n\n  .plan-title {\n    font-size: 1.4rem !important;\n    margin-bottom: 0.5rem !important;\n  }\n\n  .plan-badge {\n    top: -0.75rem !important;\n    right: -0.75rem !important;\n    padding: 0.25rem 0.5rem !important;\n    font-size: 0.7rem !important;\n  }\n\n  .plan-pricing {\n    padding: 0.75rem !important;\n    margin-bottom: 1rem !important;\n  }\n\n  .current-price {\n    font-size: 1.75rem !important;\n  }\n\n  .original-price {\n    font-size: 1rem !important;\n  }\n\n  .plan-duration {\n    font-size: 0.9rem !important;\n  }\n\n  .plan-features {\n    margin-bottom: 1.25rem !important;\n  }\n\n  .feature-item {\n    font-size: 0.8rem !important;\n    padding: 0.4rem 0 !important;\n    margin-bottom: 0.5rem !important;\n  }\n\n  .select-plan-btn {\n    padding: 0.875rem 1rem !important;\n    font-size: 0.9rem !important;\n    border-radius: 10px !important;\n  }\n}\n\n/* Tablet Devices (481px - 768px) */\n@media (min-width: 481px) and (max-width: 768px) {\n  .plans-grid {\n    grid-template-columns: repeat(2, 1fr) !important;\n    gap: 1.5rem !important;\n    padding: 0 1rem !important;\n  }\n\n  .plan-card {\n    padding: 1.5rem 1.25rem !important;\n    margin: 0 !important;\n    border-radius: 1.25rem !important;\n  }\n\n  .plan-title {\n    font-size: 1.5rem !important;\n    margin-bottom: 0.75rem !important;\n  }\n\n  .plan-badge {\n    top: -0.875rem !important;\n    right: -0.875rem !important;\n    padding: 0.375rem 0.75rem !important;\n    font-size: 0.75rem !important;\n  }\n\n  .plan-pricing {\n    padding: 1rem !important;\n    margin-bottom: 1.25rem !important;\n  }\n\n  .current-price {\n    font-size: 2rem !important;\n  }\n\n  .original-price {\n    font-size: 1.25rem !important;\n  }\n\n  .plan-duration {\n    font-size: 1rem !important;\n  }\n\n  .plan-features {\n    margin-bottom: 1.5rem !important;\n  }\n\n  .feature-item {\n    font-size: 0.875rem !important;\n    padding: 0.5rem 0 !important;\n    margin-bottom: 0.6rem !important;\n  }\n\n  .select-plan-btn {\n    padding: 1rem 1.25rem !important;\n    font-size: 0.95rem !important;\n    border-radius: 11px !important;\n  }\n}\n\n/* Laptop Devices (769px - 1024px) */\n@media (min-width: 769px) and (max-width: 1024px) {\n  .plans-grid {\n    grid-template-columns: repeat(3, 1fr) !important;\n    gap: 1.75rem !important;\n    max-width: 900px !important;\n    margin: 0 auto !important;\n  }\n\n  .plan-card {\n    padding: 2rem 1.5rem !important;\n    margin: 0 !important;\n    border-radius: 1.5rem !important;\n  }\n\n  .plan-title {\n    font-size: 1.6rem !important;\n    margin-bottom: 0.75rem !important;\n  }\n\n  .plan-badge {\n    top: -1rem !important;\n    right: -1rem !important;\n    padding: 0.5rem 1rem !important;\n    font-size: 0.8rem !important;\n  }\n\n  .plan-pricing {\n    padding: 1.25rem !important;\n    margin-bottom: 1.5rem !important;\n  }\n\n  .current-price {\n    font-size: 2.25rem !important;\n  }\n\n  .original-price {\n    font-size: 1.4rem !important;\n  }\n\n  .plan-duration {\n    font-size: 1.1rem !important;\n  }\n\n  .plan-features {\n    margin-bottom: 1.75rem !important;\n  }\n\n  .feature-item {\n    font-size: 0.9rem !important;\n    padding: 0.6rem 0 !important;\n    margin-bottom: 0.7rem !important;\n  }\n\n  .select-plan-btn {\n    padding: 1rem 1.5rem !important;\n    font-size: 1rem !important;\n    border-radius: 12px !important;\n  }\n}\n\n/* Desktop Devices (1025px+) */\n@media (min-width: 1025px) {\n  .plans-grid {\n    grid-template-columns: repeat(3, 1fr) !important;\n    gap: 2rem !important;\n    max-width: 1200px !important;\n    margin: 0 auto !important;\n  }\n\n  .plan-card {\n    padding: 2.5rem 2rem !important;\n    margin: 0 !important;\n    border-radius: 1.5rem !important;\n  }\n\n  .plan-title {\n    font-size: 1.75rem !important;\n    margin-bottom: 0.75rem !important;\n  }\n\n  .plan-badge {\n    top: -1rem !important;\n    right: -1rem !important;\n    padding: 0.5rem 1rem !important;\n    font-size: 0.85rem !important;\n  }\n\n  .plan-pricing {\n    padding: 1.5rem !important;\n    margin-bottom: 2rem !important;\n  }\n\n  .current-price {\n    font-size: 2.5rem !important;\n  }\n\n  .original-price {\n    font-size: 1.5rem !important;\n  }\n\n  .plan-duration {\n    font-size: 1.125rem !important;\n  }\n\n  .plan-features {\n    margin-bottom: 2rem !important;\n  }\n\n  .feature-item {\n    font-size: 0.95rem !important;\n    padding: 0.75rem 0 !important;\n    margin-bottom: 0.75rem !important;\n  }\n\n  .select-plan-btn {\n    padding: 1rem 1.5rem !important;\n    font-size: 1rem !important;\n    border-radius: 12px !important;\n  }\n}\n\n@media (min-width: 769px) and (max-width: 1024px) {\n  .plan-card {\n    padding: 2rem 1.5rem;\n  }\n\n  .plan-title {\n    font-size: 1.625rem;\n  }\n\n  .current-price {\n    font-size: 2.25rem;\n  }\n\n  .original-price {\n    font-size: 1.375rem;\n  }\n\n  .plan-duration {\n    font-size: 1.0625rem;\n  }\n\n  .feature-item {\n    font-size: 0.9375rem;\n  }\n}\n\n@media (min-width: 1025px) {\n  .plan-card {\n    padding: 2.5rem 2rem;\n  }\n\n  .plan-title {\n    font-size: 1.75rem;\n  }\n\n  .current-price {\n    font-size: 2.5rem;\n  }\n\n  .original-price {\n    font-size: 1.5rem;\n  }\n\n  .plan-duration {\n    font-size: 1.125rem;\n  }\n\n  .feature-item {\n    font-size: 1rem;\n  }\n}\n\n/* Payment Processing Animation */\n.payment-processing-animation {\n  position: relative;\n  width: 80px;\n  height: 80px;\n  margin: 0 auto 1rem;\n}\n\n.payment-spinner {\n  width: 80px;\n  height: 80px;\n  border: 4px solid #e5e7eb;\n  border-top: 4px solid #3b82f6;\n  border-radius: 50%;\n  animation: spin 1s linear infinite;\n  position: absolute;\n  top: 10px;\n  left: 10px;\n}\n\n.payment-pulse {\n  width: 100px;\n  height: 100px;\n  border: 2px solid #3b82f6;\n  border-radius: 50%;\n  animation: pulse 2s ease-in-out infinite;\n  position: absolute;\n  top: 0;\n  left: 0;\n}\n\n@keyframes pulse {\n  0% {\n    transform: scale(0.8);\n    opacity: 1;\n  }\n  50% {\n    transform: scale(1.1);\n    opacity: 0.5;\n  }\n  100% {\n    transform: scale(0.8);\n    opacity: 1;\n  }\n}\n\n.payment-modal h3 {\n  color: #1f2937;\n  margin-bottom: 1rem;\n  font-size: 1.5rem;\n}\n\n.payment-status {\n  color: #6b7280;\n  margin-bottom: 2rem;\n  font-size: 1.1rem;\n}\n\n.payment-plan-info {\n  background: #f8fafc;\n  border-radius: 10px;\n  padding: 0.75rem;\n  margin: 0.5rem 0;\n}\n\n.payment-plan-info h4 {\n  color: #1f2937;\n  margin-bottom: 0.25rem;\n  font-size: 16px;\n}\n\n.payment-plan-info p {\n  color: #6b7280;\n  margin: 0.125rem 0;\n  font-size: 14px;\n}\n\n.payment-instructions {\n  background: #e0f2fe;\n  border-radius: 10px;\n  padding: 1rem;\n  margin-top: 1.5rem;\n}\n\n.payment-instructions p {\n  margin: 0.5rem 0;\n  color: #0c4a6e;\n  font-size: 0.9rem;\n}\n\n/* Success Modal Styles */\n.success-animation {\n  position: relative;\n  margin-bottom: 2rem;\n}\n\n.success-checkmark {\n  font-size: 4rem;\n  animation: successBounce 0.6s ease-out;\n}\n\n@keyframes successBounce {\n  0% {\n    transform: scale(0);\n    opacity: 0;\n  }\n  50% {\n    transform: scale(1.2);\n    opacity: 1;\n  }\n  100% {\n    transform: scale(1);\n    opacity: 1;\n  }\n}\n\n.success-confetti {\n  position: absolute;\n  top: -20px;\n  left: 50%;\n  transform: translateX(-50%);\n  width: 100px;\n  height: 100px;\n  background: linear-gradient(45deg, #ff6b6b, #4ecdc4, #45b7d1, #96ceb4, #feca57);\n  border-radius: 50%;\n  animation: confetti 1s ease-out;\n  opacity: 0;\n}\n\n@keyframes confetti {\n  0% {\n    transform: translateX(-50%) scale(0);\n    opacity: 1;\n  }\n  50% {\n    transform: translateX(-50%) scale(1.5);\n    opacity: 0.8;\n  }\n  100% {\n    transform: translateX(-50%) scale(0);\n    opacity: 0;\n  }\n}\n\n.success-message {\n  color: #059669;\n  font-size: 1.1rem;\n  margin-bottom: 2rem;\n  font-weight: 500;\n}\n\n.success-plan-info {\n  background: #ecfdf5;\n  border: 1px solid #10b981;\n  border-radius: 10px;\n  padding: 1rem;\n  margin: 1rem 0;\n}\n\n.success-plan-info h4 {\n  color: #065f46;\n  margin-bottom: 0.5rem;\n}\n\n.success-plan-info p {\n  color: #047857;\n  margin: 0.25rem 0;\n}\n\n.success-features {\n  text-align: left;\n  background: #f8fafc;\n  border-radius: 10px;\n  padding: 1.5rem;\n  margin: 1.5rem 0;\n}\n\n.success-features h4 {\n  color: #1f2937;\n  margin-bottom: 1rem;\n  text-align: center;\n}\n\n.success-features ul {\n  list-style: none;\n  padding: 0;\n  margin: 0;\n}\n\n.success-features li {\n  color: #374151;\n  margin: 0.5rem 0;\n  font-size: 0.95rem;\n}\n\n.success-actions {\n  display: flex;\n  gap: 1rem;\n  margin-top: 2rem;\n  flex-wrap: wrap;\n}\n\n.success-btn {\n  flex: 1;\n  padding: 0.75rem 1.5rem;\n  border: none;\n  border-radius: 8px;\n  font-weight: 600;\n  cursor: pointer;\n  transition: all 0.3s;\n  min-width: 120px;\n}\n\n.success-btn.primary {\n  background: #3b82f6;\n  color: white;\n}\n\n.success-btn.primary:hover {\n  background: #2563eb;\n  transform: translateY(-1px);\n}\n\n.success-btn.secondary {\n  background: #f3f4f6;\n  color: #374151;\n  border: 1px solid #d1d5db;\n}\n\n.success-btn.secondary:hover {\n  background: #e5e7eb;\n}\n\n/* Mobile Devices (320px - 480px) */\n@media (max-width: 480px) {\n  .payment-modal {\n    padding: 0.75rem;\n    margin: 0.25rem;\n    max-height: 98vh;\n    width: 98%;\n    border-radius: 16px;\n  }\n\n  .payment-modal.success-modal {\n    max-height: 98vh;\n    width: 98%;\n  }\n\n  .payment-modal-content {\n    padding: 0.25rem 0;\n    gap: 0.5rem;\n  }\n\n  .payment-modal-close {\n    width: 28px;\n    height: 28px;\n    font-size: 14px;\n    top: 8px;\n    right: 8px;\n  }\n\n  .payment-processing-animation {\n    width: 50px;\n    height: 50px;\n    margin: 0 auto 0.5rem;\n  }\n\n  .phone-instruction {\n    padding: 8px;\n    margin: 6px 0;\n    border-radius: 8px;\n  }\n\n  .phone-instruction h4 {\n    font-size: 13px;\n    margin: 0 0 4px 0;\n  }\n\n  .phone-instruction p {\n    font-size: 11px;\n    margin: 2px 0;\n  }\n\n  .payment-plan-info {\n    padding: 0.4rem;\n    margin: 0.2rem 0;\n    border-radius: 8px;\n  }\n\n  .payment-plan-info h4 {\n    font-size: 13px;\n    margin-bottom: 0.2rem;\n  }\n\n  .payment-plan-info p {\n    font-size: 11px;\n    margin: 0.1rem 0;\n  }\n\n  .success-actions {\n    flex-direction: column;\n    gap: 6px;\n    margin-top: 0.75rem;\n  }\n\n  .success-btn {\n    width: 100%;\n    padding: 8px 12px;\n    font-size: 12px;\n    border-radius: 6px;\n  }\n\n  /* Success Modal Specific */\n  .success-details {\n    padding: 10px !important;\n    margin-bottom: 10px !important;\n  }\n\n  .success-details h3 {\n    font-size: 14px !important;\n    margin-bottom: 6px !important;\n  }\n\n  .success-details p {\n    font-size: 11px !important;\n    margin: 1px 0 !important;\n  }\n\n  .success-features {\n    padding: 8px !important;\n    margin-bottom: 10px !important;\n  }\n\n  .success-features h3 {\n    font-size: 12px !important;\n    margin-bottom: 6px !important;\n  }\n\n  .success-features p {\n    font-size: 10px !important;\n    margin: 1px 0 !important;\n  }\n}\n\n/* Tablet Devices (481px - 768px) */\n@media (min-width: 481px) and (max-width: 768px) {\n  .payment-modal {\n    padding: 1rem;\n    margin: 0.5rem;\n    max-height: 95vh;\n    width: 95%;\n    border-radius: 18px;\n  }\n\n  .payment-modal.success-modal {\n    max-height: 95vh;\n    width: 95%;\n    max-width: 600px;\n  }\n\n  .payment-modal-content {\n    padding: 0.5rem 0;\n    gap: 0.75rem;\n  }\n\n  .payment-modal-close {\n    width: 32px;\n    height: 32px;\n    font-size: 16px;\n    top: 12px;\n    right: 12px;\n  }\n\n  .payment-processing-animation {\n    width: 70px;\n    height: 70px;\n    margin: 0 auto 0.75rem;\n  }\n\n  .phone-instruction {\n    padding: 12px;\n    margin: 8px 0;\n    border-radius: 10px;\n  }\n\n  .phone-instruction h4 {\n    font-size: 15px;\n    margin: 0 0 6px 0;\n  }\n\n  .phone-instruction p {\n    font-size: 13px;\n    margin: 3px 0;\n  }\n\n  .payment-plan-info {\n    padding: 0.6rem;\n    margin: 0.3rem 0;\n    border-radius: 10px;\n  }\n\n  .payment-plan-info h4 {\n    font-size: 15px;\n    margin-bottom: 0.3rem;\n  }\n\n  .payment-plan-info p {\n    font-size: 13px;\n    margin: 0.15rem 0;\n  }\n\n  .success-actions {\n    flex-direction: row;\n    gap: 10px;\n    margin-top: 1rem;\n    justify-content: center;\n  }\n\n  .success-btn {\n    padding: 10px 18px;\n    font-size: 13px;\n    border-radius: 8px;\n    min-width: 130px;\n  }\n\n  /* Success Modal Specific */\n  .success-details {\n    padding: 12px !important;\n    margin-bottom: 12px !important;\n  }\n\n  .success-details h3 {\n    font-size: 15px !important;\n    margin-bottom: 8px !important;\n  }\n\n  .success-details p {\n    font-size: 13px !important;\n    margin: 2px 0 !important;\n  }\n\n  .success-features {\n    padding: 10px !important;\n    margin-bottom: 12px !important;\n  }\n\n  .success-features h3 {\n    font-size: 13px !important;\n    margin-bottom: 8px !important;\n  }\n\n  .success-features p {\n    font-size: 11px !important;\n    margin: 2px 0 !important;\n  }\n}\n\n/* Laptop/Desktop Devices (769px+) */\n@media (min-width: 769px) {\n  .payment-modal {\n    padding: 1.5rem;\n    margin: 1rem;\n    max-height: 90vh;\n    width: 90%;\n    max-width: 500px;\n    border-radius: 20px;\n  }\n\n  .payment-modal.success-modal {\n    max-height: 90vh;\n    width: 90%;\n    max-width: 650px;\n  }\n\n  .payment-modal-content {\n    padding: 0.75rem 0;\n    gap: 1rem;\n  }\n\n  .payment-modal-close {\n    width: 36px;\n    height: 36px;\n    font-size: 18px;\n    top: 16px;\n    right: 16px;\n  }\n\n  .payment-processing-animation {\n    width: 80px;\n    height: 80px;\n    margin: 0 auto 1rem;\n  }\n\n  .phone-instruction {\n    padding: 15px;\n    margin: 10px 0;\n    border-radius: 12px;\n  }\n\n  .phone-instruction h4 {\n    font-size: 16px;\n    margin: 0 0 8px 0;\n  }\n\n  .phone-instruction p {\n    font-size: 14px;\n    margin: 4px 0;\n  }\n\n  .payment-plan-info {\n    padding: 0.75rem;\n    margin: 0.5rem 0;\n    border-radius: 10px;\n  }\n\n  .payment-plan-info h4 {\n    font-size: 16px;\n    margin-bottom: 0.25rem;\n  }\n\n  .payment-plan-info p {\n    font-size: 14px;\n    margin: 0.125rem 0;\n  }\n\n  .success-actions {\n    flex-direction: row;\n    gap: 12px;\n    margin-top: 1.5rem;\n    justify-content: center;\n  }\n\n  .success-btn {\n    padding: 10px 20px;\n    font-size: 14px;\n    border-radius: 8px;\n    min-width: 140px;\n  }\n\n  /* Success Modal Specific */\n  .success-details {\n    padding: 15px !important;\n    margin-bottom: 15px !important;\n  }\n\n  .success-details h3 {\n    font-size: 16px !important;\n    margin-bottom: 8px !important;\n  }\n\n  .success-details p {\n    font-size: 14px !important;\n    margin: 2px 0 !important;\n  }\n\n  .success-features {\n    padding: 12px !important;\n    margin-bottom: 16px !important;\n  }\n\n  .success-features h3 {\n    font-size: 14px !important;\n    margin-bottom: 8px !important;\n  }\n\n  .success-features p {\n    font-size: 12px !important;\n    margin: 2px 0 !important;\n  }\n}\n\n/* Responsive Design */\n@media (max-width: 768px) {\n  .subscription-page {\n    padding: 1rem 0.5rem;\n  }\n\n  .page-title {\n    font-size: 2rem;\n  }\n\n  .plans-grid {\n    grid-template-columns: 1fr;\n    gap: 1.5rem;\n  }\n\n  .plan-card {\n    padding: 1.5rem;\n  }\n\n  .warning-content {\n    flex-direction: column;\n    text-align: center;\n  }\n}\n\n/* Enhanced Success Modal Animations */\n@keyframes successPulse {\n  0% {\n    box-shadow: 0 0 0 0 rgba(82, 196, 26, 0.7);\n  }\n  70% {\n    box-shadow: 0 0 0 20px rgba(82, 196, 26, 0);\n  }\n  100% {\n    box-shadow: 0 0 0 0 rgba(82, 196, 26, 0);\n  }\n}\n\n.success-modal.immediate {\n  animation: modalSlideIn 0.4s ease-out, successPulse 2s ease-out 0.4s;\n}\n\n/* Enhanced button hover effects */\n.success-btn {\n  position: relative;\n  overflow: hidden;\n}\n\n.success-btn::before {\n  content: '';\n  position: absolute;\n  top: 0;\n  left: -100%;\n  width: 100%;\n  height: 100%;\n  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);\n  transition: left 0.5s;\n}\n\n.success-btn:hover::before {\n  left: 100%;\n}\n\n/* ===== PROFESSIONAL PAYMENT MODALS ===== */\n\n/* Enhanced Modal Overlay with Background Scrolling */\n.payment-modal-overlay {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: rgba(15, 23, 42, 0.3); /* More transparent to show background */\n  backdrop-filter: blur(4px); /* Less blur to keep background visible */\n  -webkit-backdrop-filter: blur(4px);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  z-index: 10000;\n  padding: 20px;\n  animation: fadeIn 0.3s ease-out;\n  overflow: auto; /* Allow scrolling within overlay */\n  overscroll-behavior: contain; /* Prevent scroll chaining */\n}\n\n/* Background scroll indicator */\n.payment-modal-overlay::before {\n  content: '';\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  pointer-events: none; /* Allow clicks to pass through */\n  background: linear-gradient(\n    to bottom,\n    transparent 0%,\n    rgba(59, 130, 246, 0.05) 20%,\n    rgba(59, 130, 246, 0.05) 80%,\n    transparent 100%\n  );\n  z-index: -1; /* Behind modal but above background */\n}\n\n/* Scroll hint for background */\n.payment-modal-overlay::after {\n  content: '↕ Background scrollable';\n  position: fixed;\n  bottom: 20px;\n  right: 20px;\n  background: rgba(59, 130, 246, 0.9);\n  color: white;\n  padding: 8px 12px;\n  border-radius: 20px;\n  font-size: 12px;\n  font-weight: 600;\n  pointer-events: none;\n  z-index: 10001;\n  animation: scrollHintPulse 3s infinite;\n  opacity: 0.8;\n}\n\n@keyframes scrollHintPulse {\n  0%, 100% { opacity: 0.8; }\n  50% { opacity: 0.4; }\n}\n\n@keyframes fadeIn {\n  from { opacity: 0; }\n  to { opacity: 1; }\n}\n\n/* Optimal Modal Container for Best User Experience */\n.payment-modal-container {\n  background: #ffffff;\n  border-radius: 24px;\n  box-shadow:\n    0 25px 50px -12px rgba(0, 0, 0, 0.25),\n    0 0 0 1px rgba(255, 255, 255, 0.05);\n  width: 100%;\n  max-width: 500px;\n  max-height: 90vh;\n  overflow: hidden;\n  position: relative;\n  animation: slideUp 0.4s ease-out;\n  border: 1px solid rgba(226, 232, 240, 0.8);\n  margin: 0 auto;\n}\n\n.payment-modal-container.success {\n  max-width: 600px;\n}\n\n/* Tablet Optimization */\n@media (min-width: 769px) and (max-width: 1024px) {\n  .payment-modal-container {\n    max-width: 520px;\n  }\n\n  .payment-modal-container.success {\n    max-width: 620px;\n  }\n}\n\n/* Desktop Optimization */\n@media (min-width: 1025px) {\n  .payment-modal-container {\n    max-width: 500px;\n  }\n\n  .payment-modal-container.success {\n    max-width: 600px;\n  }\n}\n\n/* Smooth, Professional Modal Animation */\n@keyframes slideUp {\n  from {\n    opacity: 0;\n    transform: translateY(20px) scale(0.95);\n  }\n  to {\n    opacity: 1;\n    transform: translateY(0) scale(1);\n  }\n}\n\n/* Close Button */\n.modal-close-btn {\n  position: absolute;\n  top: 16px;\n  right: 16px;\n  width: 40px;\n  height: 40px;\n  border: none;\n  background: rgba(15, 23, 42, 0.1);\n  border-radius: 12px;\n  color: #64748b;\n  cursor: pointer;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  transition: all 0.2s ease;\n  z-index: 10;\n}\n\n.modal-close-btn:hover {\n  background: rgba(15, 23, 42, 0.15);\n  color: #334155;\n  transform: scale(1.05);\n}\n\n/* Modal Header */\n.modal-header {\n  padding: 32px 32px 24px;\n  text-align: center;\n  border-bottom: 1px solid #f1f5f9;\n}\n\n.modal-header.processing {\n  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);\n  color: white;\n  border-bottom: none;\n}\n\n.modal-header.success {\n  background: linear-gradient(135deg, #10b981 0%, #059669 100%);\n  color: white;\n  border-bottom: none;\n}\n\n.modal-header h2 {\n  margin: 16px 0 8px;\n  font-size: 24px;\n  font-weight: 700;\n  line-height: 1.2;\n}\n\n.modal-header p {\n  margin: 0;\n  font-size: 16px;\n  opacity: 0.9;\n  font-weight: 500;\n}\n\n/* Icons */\n.processing-icon {\n  position: relative;\n  width: 64px;\n  height: 64px;\n  margin: 0 auto;\n}\n\n.spinner {\n  position: absolute;\n  width: 64px;\n  height: 64px;\n  border: 3px solid rgba(255, 255, 255, 0.3);\n  border-top: 3px solid white;\n  border-radius: 50%;\n  animation: spin 1s linear infinite;\n}\n\n@keyframes spin {\n  0% { transform: rotate(0deg); }\n  100% { transform: rotate(360deg); }\n}\n\n.payment-icon {\n  position: absolute;\n  top: 50%;\n  left: 50%;\n  transform: translate(-50%, -50%);\n  color: white;\n}\n\n.success-icon {\n  width: 64px;\n  height: 64px;\n  margin: 0 auto;\n  animation: bounceIn 0.6s ease-out;\n}\n\n@keyframes bounceIn {\n  0% { transform: scale(0.3); opacity: 0; }\n  50% { transform: scale(1.05); }\n  70% { transform: scale(0.9); }\n  100% { transform: scale(1); opacity: 1; }\n}\n\n/* Enhanced Modal Content with Highly Visible Scrolling */\n.modal-content {\n  padding: 24px 32px 32px;\n  max-height: calc(90vh - 200px);\n  overflow-y: auto;\n  overflow-x: hidden;\n  scrollbar-width: thin;\n  scrollbar-color: #3b82f6 #e2e8f0; /* More visible blue scrollbar */\n  scroll-behavior: smooth;\n  -webkit-overflow-scrolling: touch; /* iOS smooth scrolling */\n  position: relative; /* For scroll indicators */\n}\n\n/* Highly Visible Scrollbar Styling */\n.modal-content::-webkit-scrollbar {\n  width: 10px; /* Wider scrollbar */\n}\n\n.modal-content::-webkit-scrollbar-track {\n  background: #e2e8f0;\n  border-radius: 5px;\n  margin: 4px 0;\n  box-shadow: inset 0 0 3px rgba(0, 0, 0, 0.1);\n}\n\n.modal-content::-webkit-scrollbar-thumb {\n  background: linear-gradient(180deg, #3b82f6 0%, #1d4ed8 100%); /* Vibrant blue gradient */\n  border-radius: 5px;\n  border: 1px solid #bfdbfe;\n  transition: all 0.2s ease;\n  box-shadow: 0 0 3px rgba(59, 130, 246, 0.3);\n}\n\n.modal-content::-webkit-scrollbar-thumb:hover {\n  background: linear-gradient(180deg, #2563eb 0%, #1e40af 100%);\n  transform: scaleX(1.1);\n}\n\n.modal-content::-webkit-scrollbar-thumb:active {\n  background: #1e40af;\n}\n\n/* Scroll Indicator Animation */\n@keyframes scrollPulse {\n  0%, 100% { opacity: 0.8; }\n  50% { opacity: 0.4; }\n}\n\n/* Enhanced Scroll Indicators for High Visibility */\n.modal-content::before {\n  content: '';\n  position: sticky;\n  top: 0;\n  left: 0;\n  right: 0;\n  height: 4px; /* Thicker indicator */\n  background: linear-gradient(90deg, transparent 0%, #3b82f6 50%, transparent 100%); /* Blue gradient */\n  z-index: 1;\n  opacity: 0;\n  transition: opacity 0.3s ease;\n}\n\n.modal-content.has-scroll::before {\n  opacity: 1;\n  animation: scrollPulse 2s infinite; /* Pulsing animation */\n}\n\n/* Prominent Scroll Fade Effect */\n.modal-content::after {\n  content: '';\n  position: sticky;\n  bottom: 0;\n  left: 0;\n  right: 0;\n  height: 30px; /* Taller fade */\n  background: linear-gradient(to top, rgba(255, 255, 255, 0.95) 0%, rgba(255, 255, 255, 0.7) 40%, transparent 100%);\n  pointer-events: none;\n  z-index: 1;\n  opacity: 0;\n  transition: opacity 0.3s ease;\n}\n\n.modal-content.has-scroll::after {\n  opacity: 1;\n}\n\n/* Explicit Scroll Indicator */\n.modal-content.has-scroll::before {\n  content: '⟱ Scroll for more ⟱';\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  color: #1e40af;\n  font-size: 12px;\n  font-weight: 600;\n  height: 24px;\n  background: linear-gradient(90deg, rgba(219, 234, 254, 0.7) 0%, rgba(191, 219, 254, 0.9) 50%, rgba(219, 234, 254, 0.7) 100%);\n  border-bottom: 1px solid #bfdbfe;\n  text-shadow: 0 1px 0 white;\n  padding-top: 4px;\n}\n\n/* Status Card */\n.status-card {\n  background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%);\n  border: 1px solid #3b82f6;\n  border-radius: 16px;\n  padding: 20px;\n  margin-bottom: 24px;\n  display: flex;\n  align-items: center;\n  gap: 16px;\n}\n\n.status-indicator {\n  width: 12px;\n  height: 12px;\n  border-radius: 50%;\n  flex-shrink: 0;\n}\n\n.status-indicator.processing {\n  background: #3b82f6;\n  animation: pulse 2s infinite;\n}\n\n@keyframes pulse {\n  0%, 100% { opacity: 1; }\n  50% { opacity: 0.5; }\n}\n\n.status-text {\n  margin: 0;\n  color: #1e40af;\n  font-weight: 600;\n  font-size: 15px;\n}\n\n/* Plan Info Card */\n.plan-info-card {\n  background: #f8fafc;\n  border: 1px solid #e2e8f0;\n  border-radius: 16px;\n  padding: 20px;\n  margin-bottom: 24px;\n}\n\n.plan-info-card h3 {\n  margin: 0 0 16px;\n  color: #1e293b;\n  font-size: 18px;\n  font-weight: 700;\n}\n\n.plan-details {\n  display: flex;\n  flex-direction: column;\n  gap: 12px;\n}\n\n.detail-row {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 12px 16px;\n  background: white;\n  border-radius: 12px;\n  border: 1px solid #e2e8f0;\n}\n\n.detail-row span {\n  color: #64748b;\n  font-weight: 500;\n}\n\n.detail-row strong {\n  color: #1e293b;\n  font-weight: 700;\n}\n\n.detail-row.status .status-badge {\n  display: flex;\n  align-items: center;\n  gap: 6px;\n  background: #dcfce7;\n  color: #166534;\n  padding: 6px 12px;\n  border-radius: 8px;\n  font-weight: 600;\n  font-size: 14px;\n}\n\n/* Instruction Card */\n.instruction-card {\n  background: linear-gradient(135deg, #fefce8 0%, #fef3c7 100%);\n  border: 1px solid #f59e0b;\n  border-radius: 16px;\n  padding: 20px;\n  margin-bottom: 24px;\n}\n\n.instruction-header {\n  display: flex;\n  align-items: center;\n  gap: 12px;\n  margin-bottom: 16px;\n  color: #92400e;\n  font-weight: 700;\n  font-size: 16px;\n}\n\n.phone-number {\n  background: rgba(245, 158, 11, 0.2);\n  color: #92400e;\n  padding: 12px 16px;\n  border-radius: 12px;\n  text-align: center;\n  font-weight: 700;\n  font-size: 16px;\n  margin-bottom: 16px;\n  border: 1px solid rgba(245, 158, 11, 0.3);\n}\n\n.instruction-steps {\n  display: flex;\n  flex-direction: column;\n  gap: 8px;\n}\n\n.step {\n  color: #78350f;\n  font-size: 14px;\n  font-weight: 500;\n  padding-left: 8px;\n  position: relative;\n}\n\n.step::before {\n  content: '•';\n  color: #f59e0b;\n  font-weight: bold;\n  position: absolute;\n  left: -8px;\n}\n\n/* Try Again Card */\n.try-again-card {\n  background: linear-gradient(135deg, #fef2f2 0%, #fee2e2 100%);\n  border: 1px solid #ef4444;\n  border-radius: 16px;\n  padding: 20px;\n  text-align: center;\n  margin-bottom: 24px;\n}\n\n.try-again-card p {\n  margin: 0 0 16px;\n  color: #dc2626;\n  font-weight: 600;\n}\n\n.try-again-btn {\n  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);\n  color: white;\n  border: none;\n  padding: 12px 24px;\n  border-radius: 12px;\n  font-weight: 600;\n  cursor: pointer;\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  margin: 0 auto;\n  transition: all 0.2s ease;\n}\n\n.try-again-btn:hover {\n  transform: translateY(-1px);\n  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4);\n}\n\n/* Success Modal Specific */\n.countdown-card {\n  background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%);\n  border: 1px solid #3b82f6;\n  border-radius: 16px;\n  padding: 16px;\n  margin-bottom: 24px;\n  display: flex;\n  align-items: center;\n  gap: 12px;\n}\n\n.countdown-icon {\n  color: #3b82f6;\n  animation: rotate 2s linear infinite;\n}\n\n@keyframes rotate {\n  from { transform: rotate(0deg); }\n  to { transform: rotate(360deg); }\n}\n\n.countdown-card p {\n  margin: 0;\n  color: #1e40af;\n  font-weight: 600;\n}\n\n.plan-summary-card {\n  background: #f8fafc;\n  border: 1px solid #e2e8f0;\n  border-radius: 16px;\n  padding: 24px;\n  margin-bottom: 24px;\n}\n\n.plan-summary-card h3 {\n  margin: 0 0 20px;\n  color: #1e293b;\n  font-size: 18px;\n  font-weight: 700;\n}\n\n.features-card {\n  background: linear-gradient(135deg, #f0fdf4 0%, #dcfce7 100%);\n  border: 1px solid #22c55e;\n  border-radius: 16px;\n  padding: 24px;\n  margin-bottom: 24px;\n}\n\n.features-card h3 {\n  margin: 0 0 20px;\n  color: #166534;\n  font-size: 18px;\n  font-weight: 700;\n  text-align: center;\n}\n\n.features-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n  gap: 12px;\n}\n\n.feature-item {\n  display: flex;\n  align-items: center;\n  gap: 12px;\n  background: rgba(255, 255, 255, 0.8);\n  padding: 12px 16px;\n  border-radius: 12px;\n  color: #166534;\n  font-weight: 600;\n  font-size: 14px;\n}\n\n.feature-item svg {\n  color: #22c55e;\n  flex-shrink: 0;\n}\n\n/* Modal Actions */\n.modal-actions {\n  display: flex;\n  gap: 12px;\n  justify-content: center;\n  flex-wrap: wrap;\n  margin-top: 8px;\n}\n\n.primary-btn {\n  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);\n  color: white;\n  border: none;\n  padding: 16px 24px;\n  border-radius: 12px;\n  font-weight: 700;\n  font-size: 16px;\n  cursor: pointer;\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  transition: all 0.2s ease;\n  min-width: 180px;\n  justify-content: center;\n}\n\n.primary-btn:hover {\n  transform: translateY(-2px);\n  box-shadow: 0 8px 25px rgba(59, 130, 246, 0.4);\n}\n\n.secondary-btn {\n  background: #f8fafc;\n  color: #64748b;\n  border: 1px solid #e2e8f0;\n  padding: 16px 24px;\n  border-radius: 12px;\n  font-weight: 600;\n  font-size: 16px;\n  cursor: pointer;\n  transition: all 0.2s ease;\n  min-width: 120px;\n}\n\n.secondary-btn:hover {\n  background: #f1f5f9;\n  color: #475569;\n  transform: translateY(-1px);\n}\n\n/* Optimal Responsive Design for Best User Experience */\n@media (max-width: 768px) {\n  .payment-modal-overlay {\n    padding: 16px;\n    backdrop-filter: blur(3px); /* Less blur on mobile for better background visibility */\n    -webkit-backdrop-filter: blur(3px);\n  }\n\n  .payment-modal-container {\n    max-width: calc(100vw - 32px);\n    margin: 16px;\n    max-height: calc(100vh - 32px);\n  }\n\n  .modal-header {\n    padding: 24px 20px 20px;\n  }\n\n  .modal-content {\n    padding: 20px;\n    max-height: calc(100vh - 160px) !important; /* Enhanced mobile height */\n    overflow-y: auto !important;\n    overflow-x: hidden !important;\n    scroll-behavior: smooth !important;\n    -webkit-overflow-scrolling: touch !important; /* iOS smooth scrolling */\n  }\n\n  /* Enhanced Mobile Scrollbar - Highly Visible */\n  .modal-content::-webkit-scrollbar {\n    width: 8px !important; /* Wider for mobile */\n  }\n\n  .modal-content::-webkit-scrollbar-track {\n    background: #e2e8f0 !important;\n    border-radius: 4px !important;\n  }\n\n  .modal-content::-webkit-scrollbar-thumb {\n    background: linear-gradient(180deg, #3b82f6 0%, #1d4ed8 100%) !important; /* Blue gradient */\n    border-radius: 4px !important;\n    border: 1px solid #bfdbfe !important;\n  }\n\n  .modal-header h2 {\n    font-size: 20px;\n  }\n\n  .features-grid {\n    grid-template-columns: 1fr;\n  }\n\n  .modal-actions {\n    flex-direction: column;\n  }\n\n  .primary-btn,\n  .secondary-btn {\n    width: 100%;\n  }\n}\n\n/* Small Mobile Optimization */\n@media (max-width: 480px) {\n  .payment-modal-overlay {\n    padding: 12px;\n    backdrop-filter: blur(2px); /* Minimal blur on small screens */\n    -webkit-backdrop-filter: blur(2px);\n    background: rgba(15, 23, 42, 0.25); /* Even more transparent */\n  }\n\n  .payment-modal-container {\n    max-width: calc(100vw - 24px);\n    margin: 12px;\n    max-height: calc(100vh - 24px);\n    border-radius: 16px;\n  }\n\n  .modal-header {\n    padding: 20px 16px 16px;\n  }\n\n  .modal-content {\n    padding: 16px;\n    max-height: calc(100vh - 140px) !important; /* Maximum height for small screens */\n    overflow-y: auto !important;\n    overflow-x: hidden !important;\n    scroll-behavior: smooth !important;\n    -webkit-overflow-scrolling: touch !important;\n  }\n\n  /* Small Mobile Scrollbar - Still Visible */\n  .modal-content::-webkit-scrollbar {\n    width: 6px !important; /* Wider for visibility */\n  }\n\n  .modal-content::-webkit-scrollbar-track {\n    background: #e2e8f0 !important;\n    border-radius: 3px !important;\n  }\n\n  .modal-content::-webkit-scrollbar-thumb {\n    background: linear-gradient(180deg, #3b82f6 0%, #1d4ed8 100%) !important; /* Blue gradient */\n    border-radius: 3px !important;\n    border: 1px solid #bfdbfe !important;\n  }\n\n  .detail-row {\n    flex-direction: column;\n    align-items: flex-start;\n    gap: 4px;\n  }\n}\n"], "names": [], "sourceRoot": ""}