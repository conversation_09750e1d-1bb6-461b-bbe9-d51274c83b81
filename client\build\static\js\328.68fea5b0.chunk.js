/*! For license information please see 328.68fea5b0.chunk.js.LICENSE.txt */
(self.webpackChunkclient=self.webpackChunkclient||[]).push([[328],{2618:(t,e,s)=>{var i;!function(){"use strict";var a=!("undefined"===typeof window||!window.document||!window.document.createElement),n={canUseDOM:a,canUseWorkers:"undefined"!==typeof Worker,canUseEventListeners:a&&!(!window.addEventListener&&!window.attachEvent),canUseViewport:a&&!!window.screen};void 0===(i=function(){return n}.call(e,s,e,t))||(t.exports=i)}()},3661:(module,__unused_webpack_exports,__webpack_require__)=>{var _objectSpread=__webpack_require__(2122).default,_classStaticPrivateMethodGet=__webpack_require__(5764).default,_classPrivateMethodInitSpec=__webpack_require__(1860).default,_classPrivateMethodGet=__webpack_require__(4467).default,_classPrivateFieldInitSpec=__webpack_require__(9159).default,_classPrivateFieldSet=__webpack_require__(5661).default,_classPrivateFieldGet=__webpack_require__(468).default,_classStaticPrivateFieldSpecSet=__webpack_require__(2306).default,_classStaticPrivateFieldSpecGet=__webpack_require__(1280).default,_defineProperty=__webpack_require__(8416).default,factory;globalThis,factory=()=>(()=>{"use strict";var __webpack_modules__=[,(t,e,s)=>{Object.defineProperty(e,"__esModule",{value:!0}),e.VerbosityLevel=e.Util=e.UnknownErrorException=e.UnexpectedResponseException=e.UNSUPPORTED_FEATURES=e.TextRenderingMode=e.StreamType=e.RenderingIntentFlag=e.PermissionFlag=e.PasswordResponses=e.PasswordException=e.PageActionEventType=e.OPS=e.MissingPDFException=e.LINE_FACTOR=e.LINE_DESCENT_FACTOR=e.InvalidPDFException=e.ImageKind=e.IDENTITY_MATRIX=e.FormatError=e.FontType=e.FeatureTest=e.FONT_IDENTITY_MATRIX=e.DocumentActionEventType=e.CMapCompressionType=e.BaseException=e.AnnotationType=e.AnnotationStateModelType=e.AnnotationReviewState=e.AnnotationReplyType=e.AnnotationMode=e.AnnotationMarkedState=e.AnnotationFlag=e.AnnotationFieldFlag=e.AnnotationEditorType=e.AnnotationEditorPrefix=e.AnnotationEditorParamsType=e.AnnotationBorderStyleType=e.AnnotationActionEventType=e.AbortException=void 0,e.arrayByteLength=d,e.arraysToBytes=function(t){const e=t.length;if(1===e&&t[0]instanceof Uint8Array)return t[0];let s=0;for(let n=0;n<e;n++)s+=d(t[n]);let i=0;const a=new Uint8Array(s);for(let n=0;n<e;n++){let e=t[n];e instanceof Uint8Array||(e="string"===typeof e?c(e):new Uint8Array(e));const s=e.byteLength;a.set(e,i),i+=s}return a},e.assert=function(t,e){t||r(e)},e.bytesToString=function(t){"object"===typeof t&&null!==t&&void 0!==t.length||r("Invalid argument for bytesToString");const e=t.length,s=8192;if(e<s)return String.fromCharCode.apply(null,t);const i=[];for(let a=0;a<e;a+=s){const n=Math.min(a+s,e),r=t.subarray(a,n);i.push(String.fromCharCode.apply(null,r))}return i.join("")},e.createPromiseCapability=function(){const t=Object.create(null);let e=!1;return Object.defineProperty(t,"settled",{get:()=>e}),t.promise=new Promise((function(s,i){t.resolve=function(t){e=!0,s(t)},t.reject=function(t){e=!0,i(t)}})),t},e.createValidAbsoluteUrl=function(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null,s=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null;if(!t)return null;try{if(s&&"string"===typeof t){if(s.addDefaultProtocol&&t.startsWith("www.")){const e=t.match(/\./g);e&&e.length>=2&&(t="http://".concat(t))}if(s.tryConvertEncoding)try{t=f(t)}catch(i){}}const a=e?new URL(t,e):new URL(t);if(function(t){if(!t)return!1;switch(t.protocol){case"http:":case"https:":case"ftp:":case"mailto:":case"tel:":return!0;default:return!1}}(a))return a}catch(i){}return null},e.escapeString=function(t){return t.replace(/([()\\\n\r])/g,(t=>"\n"===t?"\\n":"\r"===t?"\\r":"\\".concat(t)))},e.getModificationDate=function(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:new Date;return[t.getUTCFullYear().toString(),(t.getUTCMonth()+1).toString().padStart(2,"0"),t.getUTCDate().toString().padStart(2,"0"),t.getUTCHours().toString().padStart(2,"0"),t.getUTCMinutes().toString().padStart(2,"0"),t.getUTCSeconds().toString().padStart(2,"0")].join("")},e.getVerbosityLevel=function(){return a},e.info=function(t){a>=i.INFOS&&console.log("Info: ".concat(t))},e.isArrayBuffer=function(t){return"object"===typeof t&&null!==t&&void 0!==t.byteLength},e.isArrayEqual=function(t,e){if(t.length!==e.length)return!1;for(let s=0,i=t.length;s<i;s++)if(t[s]!==e[s])return!1;return!0},e.isAscii=function(t){return/^[\x00-\x7F]*$/.test(t)},e.objectFromMap=function(t){const e=Object.create(null);for(const[s,i]of t)e[s]=i;return e},e.objectSize=function(t){return Object.keys(t).length},e.setVerbosityLevel=function(t){Number.isInteger(t)&&(a=t)},e.shadow=o,e.string32=function(t){return String.fromCharCode(t>>24&255,t>>16&255,t>>8&255,255&t)},e.stringToBytes=c,e.stringToPDFString=function(t){if(t[0]>="\xef"){let e;if("\xfe"===t[0]&&"\xff"===t[1]?e="utf-16be":"\xff"===t[0]&&"\xfe"===t[1]?e="utf-16le":"\xef"===t[0]&&"\xbb"===t[1]&&"\xbf"===t[2]&&(e="utf-8"),e)try{const s=new TextDecoder(e,{fatal:!0}),i=c(t);return s.decode(i)}catch(s){n('stringToPDFString: "'.concat(s,'".'))}}const e=[];for(let i=0,a=t.length;i<a;i++){const s=p[t.charCodeAt(i)];e.push(s?String.fromCharCode(s):t.charAt(i))}return e.join("")},e.stringToUTF16BEString=function(t){const e=["\xfe\xff"];for(let s=0,i=t.length;s<i;s++){const i=t.charCodeAt(s);e.push(String.fromCharCode(i>>8&255),String.fromCharCode(255&i))}return e.join("")},e.stringToUTF8String=f,e.unreachable=r,e.utf8StringToString=function(t){return unescape(encodeURIComponent(t))},e.warn=n,s(2),e.IDENTITY_MATRIX=[1,0,0,1,0,0],e.FONT_IDENTITY_MATRIX=[.001,0,0,.001,0,0],e.LINE_FACTOR=1.35,e.LINE_DESCENT_FACTOR=.35,e.RenderingIntentFlag={ANY:1,DISPLAY:2,PRINT:4,ANNOTATIONS_FORMS:16,ANNOTATIONS_STORAGE:32,ANNOTATIONS_DISABLE:64,OPLIST:256},e.AnnotationMode={DISABLE:0,ENABLE:1,ENABLE_FORMS:2,ENABLE_STORAGE:3},e.AnnotationEditorPrefix="pdfjs_internal_editor_",e.AnnotationEditorType={DISABLE:-1,NONE:0,FREETEXT:3,INK:15},e.AnnotationEditorParamsType={FREETEXT_SIZE:1,FREETEXT_COLOR:2,FREETEXT_OPACITY:3,INK_COLOR:11,INK_THICKNESS:12,INK_OPACITY:13},e.PermissionFlag={PRINT:4,MODIFY_CONTENTS:8,COPY:16,MODIFY_ANNOTATIONS:32,FILL_INTERACTIVE_FORMS:256,COPY_FOR_ACCESSIBILITY:512,ASSEMBLE:1024,PRINT_HIGH_QUALITY:2048},e.TextRenderingMode={FILL:0,STROKE:1,FILL_STROKE:2,INVISIBLE:3,FILL_ADD_TO_PATH:4,STROKE_ADD_TO_PATH:5,FILL_STROKE_ADD_TO_PATH:6,ADD_TO_PATH:7,FILL_STROKE_MASK:3,ADD_TO_PATH_FLAG:4},e.ImageKind={GRAYSCALE_1BPP:1,RGB_24BPP:2,RGBA_32BPP:3},e.AnnotationType={TEXT:1,LINK:2,FREETEXT:3,LINE:4,SQUARE:5,CIRCLE:6,POLYGON:7,POLYLINE:8,HIGHLIGHT:9,UNDERLINE:10,SQUIGGLY:11,STRIKEOUT:12,STAMP:13,CARET:14,INK:15,POPUP:16,FILEATTACHMENT:17,SOUND:18,MOVIE:19,WIDGET:20,SCREEN:21,PRINTERMARK:22,TRAPNET:23,WATERMARK:24,THREED:25,REDACT:26},e.AnnotationStateModelType={MARKED:"Marked",REVIEW:"Review"},e.AnnotationMarkedState={MARKED:"Marked",UNMARKED:"Unmarked"},e.AnnotationReviewState={ACCEPTED:"Accepted",REJECTED:"Rejected",CANCELLED:"Cancelled",COMPLETED:"Completed",NONE:"None"},e.AnnotationReplyType={GROUP:"Group",REPLY:"R"},e.AnnotationFlag={INVISIBLE:1,HIDDEN:2,PRINT:4,NOZOOM:8,NOROTATE:16,NOVIEW:32,READONLY:64,LOCKED:128,TOGGLENOVIEW:256,LOCKEDCONTENTS:512},e.AnnotationFieldFlag={READONLY:1,REQUIRED:2,NOEXPORT:4,MULTILINE:4096,PASSWORD:8192,NOTOGGLETOOFF:16384,RADIO:32768,PUSHBUTTON:65536,COMBO:131072,EDIT:262144,SORT:524288,FILESELECT:1048576,MULTISELECT:2097152,DONOTSPELLCHECK:4194304,DONOTSCROLL:8388608,COMB:16777216,RICHTEXT:33554432,RADIOSINUNISON:33554432,COMMITONSELCHANGE:67108864},e.AnnotationBorderStyleType={SOLID:1,DASHED:2,BEVELED:3,INSET:4,UNDERLINE:5},e.AnnotationActionEventType={E:"Mouse Enter",X:"Mouse Exit",D:"Mouse Down",U:"Mouse Up",Fo:"Focus",Bl:"Blur",PO:"PageOpen",PC:"PageClose",PV:"PageVisible",PI:"PageInvisible",K:"Keystroke",F:"Format",V:"Validate",C:"Calculate"},e.DocumentActionEventType={WC:"WillClose",WS:"WillSave",DS:"DidSave",WP:"WillPrint",DP:"DidPrint"},e.PageActionEventType={O:"PageOpen",C:"PageClose"},e.StreamType={UNKNOWN:"UNKNOWN",FLATE:"FLATE",LZW:"LZW",DCT:"DCT",JPX:"JPX",JBIG:"JBIG",A85:"A85",AHX:"AHX",CCF:"CCF",RLX:"RLX"},e.FontType={UNKNOWN:"UNKNOWN",TYPE1:"TYPE1",TYPE1STANDARD:"TYPE1STANDARD",TYPE1C:"TYPE1C",CIDFONTTYPE0:"CIDFONTTYPE0",CIDFONTTYPE0C:"CIDFONTTYPE0C",TRUETYPE:"TRUETYPE",CIDFONTTYPE2:"CIDFONTTYPE2",TYPE3:"TYPE3",OPENTYPE:"OPENTYPE",TYPE0:"TYPE0",MMTYPE1:"MMTYPE1"};const i={ERRORS:0,WARNINGS:1,INFOS:5};e.VerbosityLevel=i,e.CMapCompressionType={NONE:0,BINARY:1,STREAM:2},e.OPS={dependency:1,setLineWidth:2,setLineCap:3,setLineJoin:4,setMiterLimit:5,setDash:6,setRenderingIntent:7,setFlatness:8,setGState:9,save:10,restore:11,transform:12,moveTo:13,lineTo:14,curveTo:15,curveTo2:16,curveTo3:17,closePath:18,rectangle:19,stroke:20,closeStroke:21,fill:22,eoFill:23,fillStroke:24,eoFillStroke:25,closeFillStroke:26,closeEOFillStroke:27,endPath:28,clip:29,eoClip:30,beginText:31,endText:32,setCharSpacing:33,setWordSpacing:34,setHScale:35,setLeading:36,setFont:37,setTextRenderingMode:38,setTextRise:39,moveText:40,setLeadingMoveText:41,setTextMatrix:42,nextLine:43,showText:44,showSpacedText:45,nextLineShowText:46,nextLineSetSpacingShowText:47,setCharWidth:48,setCharWidthAndBounds:49,setStrokeColorSpace:50,setFillColorSpace:51,setStrokeColor:52,setStrokeColorN:53,setFillColor:54,setFillColorN:55,setStrokeGray:56,setFillGray:57,setStrokeRGBColor:58,setFillRGBColor:59,setStrokeCMYKColor:60,setFillCMYKColor:61,shadingFill:62,beginInlineImage:63,beginImageData:64,endInlineImage:65,paintXObject:66,markPoint:67,markPointProps:68,beginMarkedContent:69,beginMarkedContentProps:70,endMarkedContent:71,beginCompat:72,endCompat:73,paintFormXObjectBegin:74,paintFormXObjectEnd:75,beginGroup:76,endGroup:77,beginAnnotations:78,endAnnotations:79,beginAnnotation:80,endAnnotation:81,paintJpegXObject:82,paintImageMaskXObject:83,paintImageMaskXObjectGroup:84,paintImageXObject:85,paintInlineImageXObject:86,paintInlineImageXObjectGroup:87,paintImageXObjectRepeat:88,paintImageMaskXObjectRepeat:89,paintSolidColorImageMask:90,constructPath:91},e.UNSUPPORTED_FEATURES={unknown:"unknown",forms:"forms",javaScript:"javaScript",signatures:"signatures",smask:"smask",shadingPattern:"shadingPattern",font:"font",errorTilingPattern:"errorTilingPattern",errorExtGState:"errorExtGState",errorXObject:"errorXObject",errorFontLoadType3:"errorFontLoadType3",errorFontState:"errorFontState",errorFontMissing:"errorFontMissing",errorFontTranslate:"errorFontTranslate",errorColorSpace:"errorColorSpace",errorOperatorList:"errorOperatorList",errorFontToUnicode:"errorFontToUnicode",errorFontLoadNative:"errorFontLoadNative",errorFontBuildPath:"errorFontBuildPath",errorFontGetPath:"errorFontGetPath",errorMarkedContent:"errorMarkedContent",errorContentSubStream:"errorContentSubStream"},e.PasswordResponses={NEED_PASSWORD:1,INCORRECT_PASSWORD:2};let a=i.WARNINGS;function n(t){a>=i.WARNINGS&&console.log("Warning: ".concat(t))}function r(t){throw new Error(t)}function o(t,e,s){return Object.defineProperty(t,e,{value:s,enumerable:!0,configurable:!0,writable:!1}),s}const l=function(){function t(e,s){this.constructor===t&&r("Cannot initialize BaseException."),this.message=e,this.name=s}return t.prototype=new Error,t.constructor=t,t}();function c(t){"string"!==typeof t&&r("Invalid argument for stringToBytes");const e=t.length,s=new Uint8Array(e);for(let i=0;i<e;++i)s[i]=255&t.charCodeAt(i);return s}function d(t){return void 0!==t.length?t.length:void 0!==t.byteLength?t.byteLength:void r("Invalid argument for arrayByteLength")}e.BaseException=l,e.PasswordException=class extends l{constructor(t,e){super(t,"PasswordException"),this.code=e}},e.UnknownErrorException=class extends l{constructor(t,e){super(t,"UnknownErrorException"),this.details=e}},e.InvalidPDFException=class extends l{constructor(t){super(t,"InvalidPDFException")}},e.MissingPDFException=class extends l{constructor(t){super(t,"MissingPDFException")}},e.UnexpectedResponseException=class extends l{constructor(t,e){super(t,"UnexpectedResponseException"),this.status=e}},e.FormatError=class extends l{constructor(t){super(t,"FormatError")}},e.AbortException=class extends l{constructor(t){super(t,"AbortException")}},e.FeatureTest=class{static get isLittleEndian(){return o(this,"isLittleEndian",function(){const t=new Uint8Array(4);return t[0]=1,1===new Uint32Array(t.buffer,0,1)[0]}())}static get isEvalSupported(){return o(this,"isEvalSupported",function(){try{return new Function(""),!0}catch(t){return!1}}())}static get isOffscreenCanvasSupported(){return o(this,"isOffscreenCanvasSupported","undefined"!==typeof OffscreenCanvas)}};const h=[...Array(256).keys()].map((t=>t.toString(16).padStart(2,"0")));class u{static makeHexColor(t,e,s){return"#".concat(h[t]).concat(h[e]).concat(h[s])}static scaleMinMax(t,e){let s;t[0]?(t[0]<0&&(s=e[0],e[0]=e[1],e[1]=s),e[0]*=t[0],e[1]*=t[0],t[3]<0&&(s=e[2],e[2]=e[3],e[3]=s),e[2]*=t[3],e[3]*=t[3]):(s=e[0],e[0]=e[2],e[2]=s,s=e[1],e[1]=e[3],e[3]=s,t[1]<0&&(s=e[2],e[2]=e[3],e[3]=s),e[2]*=t[1],e[3]*=t[1],t[2]<0&&(s=e[0],e[0]=e[1],e[1]=s),e[0]*=t[2],e[1]*=t[2]),e[0]+=t[4],e[1]+=t[4],e[2]+=t[5],e[3]+=t[5]}static transform(t,e){return[t[0]*e[0]+t[2]*e[1],t[1]*e[0]+t[3]*e[1],t[0]*e[2]+t[2]*e[3],t[1]*e[2]+t[3]*e[3],t[0]*e[4]+t[2]*e[5]+t[4],t[1]*e[4]+t[3]*e[5]+t[5]]}static applyTransform(t,e){return[t[0]*e[0]+t[1]*e[2]+e[4],t[0]*e[1]+t[1]*e[3]+e[5]]}static applyInverseTransform(t,e){const s=e[0]*e[3]-e[1]*e[2];return[(t[0]*e[3]-t[1]*e[2]+e[2]*e[5]-e[4]*e[3])/s,(-t[0]*e[1]+t[1]*e[0]+e[4]*e[1]-e[5]*e[0])/s]}static getAxialAlignedBoundingBox(t,e){const s=u.applyTransform(t,e),i=u.applyTransform(t.slice(2,4),e),a=u.applyTransform([t[0],t[3]],e),n=u.applyTransform([t[2],t[1]],e);return[Math.min(s[0],i[0],a[0],n[0]),Math.min(s[1],i[1],a[1],n[1]),Math.max(s[0],i[0],a[0],n[0]),Math.max(s[1],i[1],a[1],n[1])]}static inverseTransform(t){const e=t[0]*t[3]-t[1]*t[2];return[t[3]/e,-t[1]/e,-t[2]/e,t[0]/e,(t[2]*t[5]-t[4]*t[3])/e,(t[4]*t[1]-t[5]*t[0])/e]}static apply3dTransform(t,e){return[t[0]*e[0]+t[1]*e[1]+t[2]*e[2],t[3]*e[0]+t[4]*e[1]+t[5]*e[2],t[6]*e[0]+t[7]*e[1]+t[8]*e[2]]}static singularValueDecompose2dScale(t){const e=[t[0],t[2],t[1],t[3]],s=t[0]*e[0]+t[1]*e[2],i=t[0]*e[1]+t[1]*e[3],a=t[2]*e[0]+t[3]*e[2],n=t[2]*e[1]+t[3]*e[3],r=(s+n)/2,o=Math.sqrt((s+n)**2-4*(s*n-a*i))/2,l=r+o||1,c=r-o||1;return[Math.sqrt(l),Math.sqrt(c)]}static normalizeRect(t){const e=t.slice(0);return t[0]>t[2]&&(e[0]=t[2],e[2]=t[0]),t[1]>t[3]&&(e[1]=t[3],e[3]=t[1]),e}static intersect(t,e){const s=Math.max(Math.min(t[0],t[2]),Math.min(e[0],e[2])),i=Math.min(Math.max(t[0],t[2]),Math.max(e[0],e[2]));if(s>i)return null;const a=Math.max(Math.min(t[1],t[3]),Math.min(e[1],e[3])),n=Math.min(Math.max(t[1],t[3]),Math.max(e[1],e[3]));return a>n?null:[s,a,i,n]}static bezierBoundingBox(t,e,s,i,a,n,r,o){const l=[],c=[[],[]];let d,h,u,p,f,m,g,v;for(let A=0;A<2;++A)if(0===A?(h=6*t-12*s+6*a,d=-3*t+9*s-9*a+3*r,u=3*s-3*t):(h=6*e-12*i+6*n,d=-3*e+9*i-9*n+3*o,u=3*i-3*e),Math.abs(d)<1e-12){if(Math.abs(h)<1e-12)continue;p=-u/h,0<p&&p<1&&l.push(p)}else g=h*h-4*u*d,v=Math.sqrt(g),g<0||(f=(-h+v)/(2*d),0<f&&f<1&&l.push(f),m=(-h-v)/(2*d),0<m&&m<1&&l.push(m));let _,b=l.length;const y=b;for(;b--;)p=l[b],_=1-p,c[0][b]=_*_*_*t+3*_*_*p*s+3*_*p*p*a+p*p*p*r,c[1][b]=_*_*_*e+3*_*_*p*i+3*_*p*p*n+p*p*p*o;return c[0][y]=t,c[1][y]=e,c[0][y+1]=r,c[1][y+1]=o,c[0].length=c[1].length=y+2,[Math.min(...c[0]),Math.min(...c[1]),Math.max(...c[0]),Math.max(...c[1])]}}e.Util=u;const p=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,728,711,710,729,733,731,730,732,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,8226,8224,8225,8230,8212,8211,402,8260,8249,8250,8722,8240,8222,8220,8221,8216,8217,8218,8482,64257,64258,321,338,352,376,381,305,322,339,353,382,0,8364];function f(t){return decodeURIComponent(escape(t))}},(t,e,s)=>{s(3)},(t,e)=>{Object.defineProperty(e,"__esModule",{value:!0}),e.isNodeJS=void 0;const s="object"===typeof process&&process+""==="[object process]"&&!process.versions.nw&&!(process.versions.electron&&process.type&&"browser"!==process.type);e.isNodeJS=s},(t,e,s)=>{var i;Object.defineProperty(e,"__esModule",{value:!0}),e.StatTimer=e.RenderingCancelledException=e.PixelsPerInch=e.PageViewport=e.PDFDateString=e.DOMStandardFontDataFactory=e.DOMSVGFactory=e.DOMCanvasFactory=e.DOMCMapReaderFactory=void 0,e.binarySearchFirstItem=function(t,e){let s=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,i=t.length-1;if(i<0||!e(t[i]))return t.length;if(e(t[s]))return s;for(;s<i;){const a=s+i>>1;e(t[a])?i=a:s=a+1}return s},e.deprecated=function(t){console.log("Deprecated API usage: "+t)},e.getColorValues=function(t){const e=document.createElement("span");e.style.visibility="hidden",document.body.append(e);for(const s of t.keys()){e.style.color=s;const i=window.getComputedStyle(e).color;t.set(s,v(i))}e.remove()},e.getFilenameFromUrl=function(t){const e=t.indexOf("#"),s=t.indexOf("?"),i=Math.min(e>0?e:t.length,s>0?s:t.length);return t.substring(t.lastIndexOf("/",i)+1,i)},e.getPdfFilenameFromUrl=function(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"document.pdf";if("string"!==typeof t)return e;if(f(t))return(0,n.warn)('getPdfFilenameFromUrl: ignore "data:"-URL for performance reasons.'),e;const s=/[^/?#=]+\.pdf\b(?!.*\.pdf\b)/i,i=/^(?:(?:[^:]+:)?\/\/[^/]+)?([^?#]*)(\?[^#]*)?(#.*)?$/.exec(t);let a=s.exec(i[1])||s.exec(i[2])||s.exec(i[3]);if(a&&(a=a[0],a.includes("%")))try{a=s.exec(decodeURIComponent(a))[0]}catch(r){}return a||e},e.getRGB=v,e.getXfaPageViewport=function(t,e){let{scale:s=1,rotation:i=0}=e;const{width:a,height:n}=t.attributes.style,r=[0,0,parseInt(a),parseInt(n)];return new u({viewBox:r,scale:s,rotation:i})},e.isDataScheme=f,e.isPdfFile=function(t){return"string"===typeof t&&/\.pdf$/i.test(t)},e.isValidFetchUrl=m,e.loadScript=function(t){let e=arguments.length>1&&void 0!==arguments[1]&&arguments[1];return new Promise(((s,i)=>{const a=document.createElement("script");a.src=t,a.onload=function(t){e&&a.remove(),s(t)},a.onerror=function(){i(new Error("Cannot load script at: ".concat(a.src)))},(document.head||document.documentElement).append(a)}))};var a=s(5),n=s(1);class r{}i=r,_defineProperty(r,"CSS",96),_defineProperty(r,"PDF",72),_defineProperty(r,"PDF_TO_CSS_UNITS",i.CSS/i.PDF),e.PixelsPerInch=r;class o extends a.BaseCanvasFactory{constructor(){let{ownerDocument:t=globalThis.document}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};super(),this._document=t}_createCanvas(t,e){const s=this._document.createElement("canvas");return s.width=t,s.height=e,s}}async function l(t){let e=arguments.length>1&&void 0!==arguments[1]&&arguments[1];if(m(t,document.baseURI)){const s=await fetch(t);if(!s.ok)throw new Error(s.statusText);return e?new Uint8Array(await s.arrayBuffer()):(0,n.stringToBytes)(await s.text())}return new Promise(((s,i)=>{const a=new XMLHttpRequest;a.open("GET",t,!0),e&&(a.responseType="arraybuffer"),a.onreadystatechange=()=>{if(a.readyState===XMLHttpRequest.DONE){if(200===a.status||0===a.status){let t;if(e&&a.response?t=new Uint8Array(a.response):!e&&a.responseText&&(t=(0,n.stringToBytes)(a.responseText)),t)return void s(t)}i(new Error(a.statusText))}},a.send(null)}))}e.DOMCanvasFactory=o;class c extends a.BaseCMapReaderFactory{_fetchData(t,e){return l(t,this.isCompressed).then((t=>({cMapData:t,compressionType:e})))}}e.DOMCMapReaderFactory=c;class d extends a.BaseStandardFontDataFactory{_fetchData(t){return l(t,!0)}}e.DOMStandardFontDataFactory=d;class h extends a.BaseSVGFactory{_createSVG(t){return document.createElementNS("http://www.w3.org/2000/svg",t)}}e.DOMSVGFactory=h;class u{constructor(t){let{viewBox:e,scale:s,rotation:i,offsetX:a=0,offsetY:n=0,dontFlip:r=!1}=t;this.viewBox=e,this.scale=s,this.rotation=i,this.offsetX=a,this.offsetY=n;const o=(e[2]+e[0])/2,l=(e[3]+e[1])/2;let c,d,h,u,p,f,m,g;switch(i%=360,i<0&&(i+=360),i){case 180:c=-1,d=0,h=0,u=1;break;case 90:c=0,d=1,h=1,u=0;break;case 270:c=0,d=-1,h=-1,u=0;break;case 0:c=1,d=0,h=0,u=-1;break;default:throw new Error("PageViewport: Invalid rotation, must be a multiple of 90 degrees.")}r&&(h=-h,u=-u),0===c?(p=Math.abs(l-e[1])*s+a,f=Math.abs(o-e[0])*s+n,m=Math.abs(e[3]-e[1])*s,g=Math.abs(e[2]-e[0])*s):(p=Math.abs(o-e[0])*s+a,f=Math.abs(l-e[1])*s+n,m=Math.abs(e[2]-e[0])*s,g=Math.abs(e[3]-e[1])*s),this.transform=[c*s,d*s,h*s,u*s,p-c*s*o-h*s*l,f-d*s*o-u*s*l],this.width=m,this.height=g}clone(){let{scale:t=this.scale,rotation:e=this.rotation,offsetX:s=this.offsetX,offsetY:i=this.offsetY,dontFlip:a=!1}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return new u({viewBox:this.viewBox.slice(),scale:t,rotation:e,offsetX:s,offsetY:i,dontFlip:a})}convertToViewportPoint(t,e){return n.Util.applyTransform([t,e],this.transform)}convertToViewportRectangle(t){const e=n.Util.applyTransform([t[0],t[1]],this.transform),s=n.Util.applyTransform([t[2],t[3]],this.transform);return[e[0],e[1],s[0],s[1]]}convertToPdfPoint(t,e){return n.Util.applyInverseTransform([t,e],this.transform)}}e.PageViewport=u;class p extends n.BaseException{constructor(t,e){super(t,"RenderingCancelledException"),this.type=e}}function f(t){const e=t.length;let s=0;for(;s<e&&""===t[s].trim();)s++;return"data:"===t.substring(s,s+5).toLowerCase()}function m(t,e){try{const{protocol:s}=e?new URL(t,e):new URL(t);return"http:"===s||"https:"===s}catch(s){return!1}}let g;function v(t){if(t.startsWith("#")){const e=parseInt(t.slice(1),16);return[(16711680&e)>>16,(65280&e)>>8,255&e]}return t.startsWith("rgb(")?t.slice(4,-1).split(",").map((t=>parseInt(t))):t.startsWith("rgba(")?t.slice(5,-1).split(",").map((t=>parseInt(t))).slice(0,3):((0,n.warn)('Not a valid color format: "'.concat(t,'"')),[0,0,0])}e.RenderingCancelledException=p,e.StatTimer=class{constructor(){this.started=Object.create(null),this.times=[]}time(t){t in this.started&&(0,n.warn)("Timer is already running for ".concat(t)),this.started[t]=Date.now()}timeEnd(t){t in this.started||(0,n.warn)("Timer has not been started for ".concat(t)),this.times.push({name:t,start:this.started[t],end:Date.now()}),delete this.started[t]}toString(){const t=[];let e=0;for(const s of this.times){const t=s.name;t.length>e&&(e=t.length)}for(const s of this.times){const i=s.end-s.start;t.push("".concat(s.name.padEnd(e)," ").concat(i,"ms\n"))}return t.join("")}},e.PDFDateString=class{static toDateObject(t){if(!t||"string"!==typeof t)return null;g||(g=new RegExp("^D:(\\d{4})(\\d{2})?(\\d{2})?(\\d{2})?(\\d{2})?(\\d{2})?([Z|+|-])?(\\d{2})?'?(\\d{2})?'?"));const e=g.exec(t);if(!e)return null;const s=parseInt(e[1],10);let i=parseInt(e[2],10);i=i>=1&&i<=12?i-1:0;let a=parseInt(e[3],10);a=a>=1&&a<=31?a:1;let n=parseInt(e[4],10);n=n>=0&&n<=23?n:0;let r=parseInt(e[5],10);r=r>=0&&r<=59?r:0;let o=parseInt(e[6],10);o=o>=0&&o<=59?o:0;const l=e[7]||"Z";let c=parseInt(e[8],10);c=c>=0&&c<=23?c:0;let d=parseInt(e[9],10)||0;return d=d>=0&&d<=59?d:0,"-"===l?(n+=c,r+=d):"+"===l&&(n-=c,r-=d),new Date(Date.UTC(s,i,a,n,r,o))}}},(t,e,s)=>{Object.defineProperty(e,"__esModule",{value:!0}),e.BaseStandardFontDataFactory=e.BaseSVGFactory=e.BaseCanvasFactory=e.BaseCMapReaderFactory=void 0;var i=s(1);class a{constructor(){this.constructor===a&&(0,i.unreachable)("Cannot initialize BaseCanvasFactory.")}create(t,e){if(t<=0||e<=0)throw new Error("Invalid canvas size");const s=this._createCanvas(t,e);return{canvas:s,context:s.getContext("2d")}}reset(t,e,s){if(!t.canvas)throw new Error("Canvas is not specified");if(e<=0||s<=0)throw new Error("Invalid canvas size");t.canvas.width=e,t.canvas.height=s}destroy(t){if(!t.canvas)throw new Error("Canvas is not specified");t.canvas.width=0,t.canvas.height=0,t.canvas=null,t.context=null}_createCanvas(t,e){(0,i.unreachable)("Abstract method `_createCanvas` called.")}}e.BaseCanvasFactory=a;class n{constructor(t){let{baseUrl:e=null,isCompressed:s=!1}=t;this.constructor===n&&(0,i.unreachable)("Cannot initialize BaseCMapReaderFactory."),this.baseUrl=e,this.isCompressed=s}async fetch(t){let{name:e}=t;if(!this.baseUrl)throw new Error('The CMap "baseUrl" parameter must be specified, ensure that the "cMapUrl" and "cMapPacked" API parameters are provided.');if(!e)throw new Error("CMap name must be specified.");const s=this.baseUrl+e+(this.isCompressed?".bcmap":""),a=this.isCompressed?i.CMapCompressionType.BINARY:i.CMapCompressionType.NONE;return this._fetchData(s,a).catch((t=>{throw new Error("Unable to load ".concat(this.isCompressed?"binary ":"","CMap at: ").concat(s))}))}_fetchData(t,e){(0,i.unreachable)("Abstract method `_fetchData` called.")}}e.BaseCMapReaderFactory=n;class r{constructor(t){let{baseUrl:e=null}=t;this.constructor===r&&(0,i.unreachable)("Cannot initialize BaseStandardFontDataFactory."),this.baseUrl=e}async fetch(t){let{filename:e}=t;if(!this.baseUrl)throw new Error('The standard font "baseUrl" parameter must be specified, ensure that the "standardFontDataUrl" API parameter is provided.');if(!e)throw new Error("Font filename must be specified.");const s="".concat(this.baseUrl).concat(e);return this._fetchData(s).catch((t=>{throw new Error("Unable to load font data at: ".concat(s))}))}_fetchData(t){(0,i.unreachable)("Abstract method `_fetchData` called.")}}e.BaseStandardFontDataFactory=r;class o{constructor(){this.constructor===o&&(0,i.unreachable)("Cannot initialize BaseSVGFactory.")}create(t,e){let s=arguments.length>2&&void 0!==arguments[2]&&arguments[2];if(t<=0||e<=0)throw new Error("Invalid SVG dimensions");const i=this._createSVG("svg:svg");return i.setAttribute("version","1.1"),s||(i.setAttribute("width","".concat(t,"px")),i.setAttribute("height","".concat(e,"px"))),i.setAttribute("preserveAspectRatio","none"),i.setAttribute("viewBox","0 0 ".concat(t," ").concat(e)),i}createElement(t){if("string"!==typeof t)throw new Error("Invalid SVG element type");return this._createSVG(t)}_createSVG(t){(0,i.unreachable)("Abstract method `_createSVG` called.")}}e.BaseSVGFactory=o},(__unused_webpack_module,exports,__w_pdfjs_require__)=>{Object.defineProperty(exports,"__esModule",{value:!0}),exports.build=exports.RenderTask=exports.PDFWorkerUtil=exports.PDFWorker=exports.PDFPageProxy=exports.PDFDocumentProxy=exports.PDFDocumentLoadingTask=exports.PDFDataRangeTransport=exports.LoopbackPort=exports.DefaultStandardFontDataFactory=exports.DefaultCanvasFactory=exports.DefaultCMapReaderFactory=void 0,exports.getDocument=getDocument,exports.setPDFNetworkStreamFactory=setPDFNetworkStreamFactory,exports.version=void 0;var _util=__w_pdfjs_require__(1),_annotation_storage=__w_pdfjs_require__(7),_display_utils=__w_pdfjs_require__(4),_font_loader=__w_pdfjs_require__(11),_canvas=__w_pdfjs_require__(12),_worker_options=__w_pdfjs_require__(15),_is_node=__w_pdfjs_require__(3),_message_handler=__w_pdfjs_require__(16),_metadata=__w_pdfjs_require__(17),_optional_content_config=__w_pdfjs_require__(18),_transport_stream=__w_pdfjs_require__(19),_xfa_text=__w_pdfjs_require__(20);const DEFAULT_RANGE_CHUNK_SIZE=65536,RENDERING_CANCELLED_TIMEOUT=100;let DefaultCanvasFactory=_display_utils.DOMCanvasFactory;exports.DefaultCanvasFactory=DefaultCanvasFactory;let DefaultCMapReaderFactory=_display_utils.DOMCMapReaderFactory;exports.DefaultCMapReaderFactory=DefaultCMapReaderFactory;let DefaultStandardFontDataFactory=_display_utils.DOMStandardFontDataFactory,createPDFNetworkStream;if(exports.DefaultStandardFontDataFactory=DefaultStandardFontDataFactory,_is_node.isNodeJS){const{NodeCanvasFactory:t,NodeCMapReaderFactory:e,NodeStandardFontDataFactory:s}=__w_pdfjs_require__(21);exports.DefaultCanvasFactory=DefaultCanvasFactory=t,exports.DefaultCMapReaderFactory=DefaultCMapReaderFactory=e,exports.DefaultStandardFontDataFactory=DefaultStandardFontDataFactory=s}function setPDFNetworkStreamFactory(t){createPDFNetworkStream=t}function getDocument(t){const e=new PDFDocumentLoadingTask;let s;if("string"===typeof t||t instanceof URL)s={url:t};else if((0,_util.isArrayBuffer)(t))s={data:t};else if(t instanceof PDFDataRangeTransport)s={range:t};else{if("object"!==typeof t)throw new Error("Invalid parameter in getDocument, need either string, URL, Uint8Array, or parameter object.");if(!t.url&&!t.data&&!t.range)throw new Error("Invalid parameter object: need either .data, .range or .url");s=t}const i=Object.create(null);let a=null,n=null;for(const l in s){const t=s[l];switch(l){case"url":if("undefined"!==typeof window)try{i[l]=new URL(t,window.location).href;continue}catch(o){(0,_util.warn)('Cannot create valid URL: "'.concat(o,'".'))}else if("string"===typeof t||t instanceof URL){i[l]=t.toString();continue}throw new Error("Invalid PDF url data: either string or URL-object is expected in the url property.");case"range":a=t;continue;case"worker":n=t;continue;case"data":if(_is_node.isNodeJS&&"undefined"!==typeof Buffer&&t instanceof Buffer)i[l]=new Uint8Array(t);else{if(t instanceof Uint8Array)break;if("string"===typeof t)i[l]=(0,_util.stringToBytes)(t);else if("object"!==typeof t||null===t||isNaN(t.length)){if(!(0,_util.isArrayBuffer)(t))throw new Error("Invalid PDF binary data: either typed array, string, or array-like object is expected in the data property.");i[l]=new Uint8Array(t)}else i[l]=new Uint8Array(t)}continue}i[l]=t}if(i.CMapReaderFactory=i.CMapReaderFactory||DefaultCMapReaderFactory,i.StandardFontDataFactory=i.StandardFontDataFactory||DefaultStandardFontDataFactory,i.ignoreErrors=!0!==i.stopAtErrors,i.fontExtraProperties=!0===i.fontExtraProperties,i.pdfBug=!0===i.pdfBug,i.enableXfa=!0===i.enableXfa,(!Number.isInteger(i.rangeChunkSize)||i.rangeChunkSize<1)&&(i.rangeChunkSize=DEFAULT_RANGE_CHUNK_SIZE),("string"!==typeof i.docBaseUrl||(0,_display_utils.isDataScheme)(i.docBaseUrl))&&(i.docBaseUrl=null),(!Number.isInteger(i.maxImageSize)||i.maxImageSize<-1)&&(i.maxImageSize=-1),"string"!==typeof i.cMapUrl&&(i.cMapUrl=null),"string"!==typeof i.standardFontDataUrl&&(i.standardFontDataUrl=null),"boolean"!==typeof i.useWorkerFetch&&(i.useWorkerFetch=i.CMapReaderFactory===_display_utils.DOMCMapReaderFactory&&i.StandardFontDataFactory===_display_utils.DOMStandardFontDataFactory),"boolean"!==typeof i.isEvalSupported&&(i.isEvalSupported=!0),"boolean"!==typeof i.disableFontFace&&(i.disableFontFace=_is_node.isNodeJS),"boolean"!==typeof i.useSystemFonts&&(i.useSystemFonts=!_is_node.isNodeJS&&!i.disableFontFace),"object"===typeof i.ownerDocument&&null!==i.ownerDocument||(i.ownerDocument=globalThis.document),"boolean"!==typeof i.disableRange&&(i.disableRange=!1),"boolean"!==typeof i.disableStream&&(i.disableStream=!1),"boolean"!==typeof i.disableAutoFetch&&(i.disableAutoFetch=!1),(0,_util.setVerbosityLevel)(i.verbosity),!n){const t={verbosity:i.verbosity,port:_worker_options.GlobalWorkerOptions.workerPort};n=t.port?PDFWorker.fromPort(t):new PDFWorker(t),e._worker=n}const r=e.docId;return n.promise.then((function(){if(e.destroyed)throw new Error("Loading aborted");const t=_fetchDocument(n,i,a,r),s=new Promise((function(t){let e;a?e=new _transport_stream.PDFDataTransportStream({length:i.length,initialData:i.initialData,progressiveDone:i.progressiveDone,contentDispositionFilename:i.contentDispositionFilename,disableRange:i.disableRange,disableStream:i.disableStream},a):i.data||(e=createPDFNetworkStream({url:i.url,length:i.length,httpHeaders:i.httpHeaders,withCredentials:i.withCredentials,rangeChunkSize:i.rangeChunkSize,disableRange:i.disableRange,disableStream:i.disableStream})),t(e)}));return Promise.all([t,s]).then((function(t){let[s,a]=t;if(e.destroyed)throw new Error("Loading aborted");const o=new _message_handler.MessageHandler(r,s,n.port),l=new WorkerTransport(o,e,a,i);e._transport=l,o.send("Ready",null)}))})).catch(e._capability.reject),e}async function _fetchDocument(t,e,s,i){if(t.destroyed)throw new Error("Worker was destroyed");s&&(e.length=s.length,e.initialData=s.initialData,e.progressiveDone=s.progressiveDone,e.contentDispositionFilename=s.contentDispositionFilename);const a=await t.messageHandler.sendWithPromise("GetDocRequest",{docId:i,apiVersion:"2.15.349",source:{data:e.data,url:e.url,password:e.password,disableAutoFetch:e.disableAutoFetch,rangeChunkSize:e.rangeChunkSize,length:e.length},maxImageSize:e.maxImageSize,disableFontFace:e.disableFontFace,docBaseUrl:e.docBaseUrl,ignoreErrors:e.ignoreErrors,isEvalSupported:e.isEvalSupported,fontExtraProperties:e.fontExtraProperties,enableXfa:e.enableXfa,useSystemFonts:e.useSystemFonts,cMapUrl:e.useWorkerFetch?e.cMapUrl:null,standardFontDataUrl:e.useWorkerFetch?e.standardFontDataUrl:null});if(e.data&&(e.data=null),t.destroyed)throw new Error("Worker was destroyed");return a}class PDFDocumentLoadingTask{constructor(){var t,e;this._capability=(0,_util.createPromiseCapability)(),this._transport=null,this._worker=null,this.docId="d".concat((_classStaticPrivateFieldSpecSet(PDFDocumentLoadingTask,PDFDocumentLoadingTask,_docId,(t=_classStaticPrivateFieldSpecGet(PDFDocumentLoadingTask,PDFDocumentLoadingTask,_docId),e=t++,t)),e)),this.destroyed=!1,this.onPassword=null,this.onProgress=null,this.onUnsupportedFeature=null}get promise(){return this._capability.promise}async destroy(){var t;this.destroyed=!0,await(null===(t=this._transport)||void 0===t?void 0:t.destroy()),this._transport=null,this._worker&&(this._worker.destroy(),this._worker=null)}}var _docId={writable:!0,value:0};exports.PDFDocumentLoadingTask=PDFDocumentLoadingTask;class PDFDataRangeTransport{constructor(t,e){let s=arguments.length>2&&void 0!==arguments[2]&&arguments[2],i=arguments.length>3&&void 0!==arguments[3]?arguments[3]:null;this.length=t,this.initialData=e,this.progressiveDone=s,this.contentDispositionFilename=i,this._rangeListeners=[],this._progressListeners=[],this._progressiveReadListeners=[],this._progressiveDoneListeners=[],this._readyCapability=(0,_util.createPromiseCapability)()}addRangeListener(t){this._rangeListeners.push(t)}addProgressListener(t){this._progressListeners.push(t)}addProgressiveReadListener(t){this._progressiveReadListeners.push(t)}addProgressiveDoneListener(t){this._progressiveDoneListeners.push(t)}onDataRange(t,e){for(const s of this._rangeListeners)s(t,e)}onDataProgress(t,e){this._readyCapability.promise.then((()=>{for(const s of this._progressListeners)s(t,e)}))}onDataProgressiveRead(t){this._readyCapability.promise.then((()=>{for(const e of this._progressiveReadListeners)e(t)}))}onDataProgressiveDone(){this._readyCapability.promise.then((()=>{for(const t of this._progressiveDoneListeners)t()}))}transportReady(){this._readyCapability.resolve()}requestDataRange(t,e){(0,_util.unreachable)("Abstract method PDFDataRangeTransport.requestDataRange")}abort(){}}exports.PDFDataRangeTransport=PDFDataRangeTransport;class PDFDocumentProxy{constructor(t,e){this._pdfInfo=t,this._transport=e,Object.defineProperty(this,"fingerprint",{get(){return(0,_display_utils.deprecated)("`PDFDocumentProxy.fingerprint`, please use `PDFDocumentProxy.fingerprints` instead."),this.fingerprints[0]}}),Object.defineProperty(this,"getStats",{value:async()=>((0,_display_utils.deprecated)("`PDFDocumentProxy.getStats`, please use the `PDFDocumentProxy.stats`-getter instead."),this.stats||{streamTypes:{},fontTypes:{}})})}get annotationStorage(){return this._transport.annotationStorage}get numPages(){return this._pdfInfo.numPages}get fingerprints(){return this._pdfInfo.fingerprints}get stats(){return this._transport.stats}get isPureXfa(){return!!this._transport._htmlForXfa}get allXfaHtml(){return this._transport._htmlForXfa}getPage(t){return this._transport.getPage(t)}getPageIndex(t){return this._transport.getPageIndex(t)}getDestinations(){return this._transport.getDestinations()}getDestination(t){return this._transport.getDestination(t)}getPageLabels(){return this._transport.getPageLabels()}getPageLayout(){return this._transport.getPageLayout()}getPageMode(){return this._transport.getPageMode()}getViewerPreferences(){return this._transport.getViewerPreferences()}getOpenAction(){return this._transport.getOpenAction()}getAttachments(){return this._transport.getAttachments()}getJavaScript(){return this._transport.getJavaScript()}getJSActions(){return this._transport.getDocJSActions()}getOutline(){return this._transport.getOutline()}getOptionalContentConfig(){return this._transport.getOptionalContentConfig()}getPermissions(){return this._transport.getPermissions()}getMetadata(){return this._transport.getMetadata()}getMarkInfo(){return this._transport.getMarkInfo()}getData(){return this._transport.getData()}getDownloadInfo(){return this._transport.downloadInfoCapability.promise}cleanup(){let t=arguments.length>0&&void 0!==arguments[0]&&arguments[0];return this._transport.startCleanup(t||this.isPureXfa)}destroy(){return this.loadingTask.destroy()}get loadingParams(){return this._transport.loadingParams}get loadingTask(){return this._transport.loadingTask}saveDocument(){return this._transport.annotationStorage.size<=0&&(0,_display_utils.deprecated)("saveDocument called while `annotationStorage` is empty, please use the getData-method instead."),this._transport.saveDocument()}getFieldObjects(){return this._transport.getFieldObjects()}hasJSActions(){return this._transport.hasJSActions()}getCalculationOrderIds(){return this._transport.getCalculationOrderIds()}}exports.PDFDocumentProxy=PDFDocumentProxy;class PDFPageProxy{constructor(t,e,s,i){let a=arguments.length>4&&void 0!==arguments[4]&&arguments[4];this._pageIndex=t,this._pageInfo=e,this._ownerDocument=i,this._transport=s,this._stats=a?new _display_utils.StatTimer:null,this._pdfBug=a,this.commonObjs=s.commonObjs,this.objs=new PDFObjects,this._bitmaps=new Set,this.cleanupAfterRender=!1,this.pendingCleanup=!1,this._intentStates=new Map,this._annotationPromises=new Map,this.destroyed=!1}get pageNumber(){return this._pageIndex+1}get rotate(){return this._pageInfo.rotate}get ref(){return this._pageInfo.ref}get userUnit(){return this._pageInfo.userUnit}get view(){return this._pageInfo.view}getViewport(){let{scale:t,rotation:e=this.rotate,offsetX:s=0,offsetY:i=0,dontFlip:a=!1}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return new _display_utils.PageViewport({viewBox:this.view,scale:t,rotation:e,offsetX:s,offsetY:i,dontFlip:a})}getAnnotations(){let{intent:t="display"}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};const e=this._transport.getRenderingIntent(t);let s=this._annotationPromises.get(e.cacheKey);return s||(s=this._transport.getAnnotations(this._pageIndex,e.renderingIntent),this._annotationPromises.set(e.cacheKey,s),s=s.then((t=>{for(const e of t)void 0!==e.titleObj&&Object.defineProperty(e,"title",{get:()=>((0,_display_utils.deprecated)("`title`-property on annotation, please use `titleObj` instead."),e.titleObj.str)}),void 0!==e.contentsObj&&Object.defineProperty(e,"contents",{get:()=>((0,_display_utils.deprecated)("`contents`-property on annotation, please use `contentsObj` instead."),e.contentsObj.str)});return t}))),s}getJSActions(){return this._jsActionsPromise||(this._jsActionsPromise=this._transport.getPageJSActions(this._pageIndex))}async getXfa(){var t;return(null===(t=this._transport._htmlForXfa)||void 0===t?void 0:t.children[this._pageIndex])||null}render(t){var e,s,i;let{canvasContext:a,viewport:n,intent:r="display",annotationMode:o=_util.AnnotationMode.ENABLE,transform:l=null,imageLayer:c=null,canvasFactory:d=null,background:h=null,optionalContentConfigPromise:u=null,annotationCanvasMap:p=null,pageColors:f=null,printAnnotationStorage:m=null}=t;void 0!==(null===(e=arguments[0])||void 0===e?void 0:e.renderInteractiveForms)&&((0,_display_utils.deprecated)("render no longer accepts the `renderInteractiveForms`-option, please use the `annotationMode`-option instead."),!0===arguments[0].renderInteractiveForms&&o===_util.AnnotationMode.ENABLE&&(o=_util.AnnotationMode.ENABLE_FORMS)),void 0!==(null===(s=arguments[0])||void 0===s?void 0:s.includeAnnotationStorage)&&((0,_display_utils.deprecated)("render no longer accepts the `includeAnnotationStorage`-option, please use the `annotationMode`-option instead."),!0===arguments[0].includeAnnotationStorage&&o===_util.AnnotationMode.ENABLE&&(o=_util.AnnotationMode.ENABLE_STORAGE)),this._stats&&this._stats.time("Overall");const g=this._transport.getRenderingIntent(r,o,m);this.pendingCleanup=!1,u||(u=this._transport.getOptionalContentConfig());let v=this._intentStates.get(g.cacheKey);v||(v=Object.create(null),this._intentStates.set(g.cacheKey,v)),v.streamReaderCancelTimeout&&(clearTimeout(v.streamReaderCancelTimeout),v.streamReaderCancelTimeout=null);const _=d||new DefaultCanvasFactory({ownerDocument:this._ownerDocument}),b=!!(g.renderingIntent&_util.RenderingIntentFlag.PRINT);v.displayReadyCapability||(v.displayReadyCapability=(0,_util.createPromiseCapability)(),v.operatorList={fnArray:[],argsArray:[],lastChunk:!1,separateAnnots:null},this._stats&&this._stats.time("Page Request"),this._pumpOperatorList(g));const y=t=>{v.renderTasks.delete(A),(this.cleanupAfterRender||b)&&(this.pendingCleanup=!0),this._tryCleanup(),t?(A.capability.reject(t),this._abortOperatorList({intentState:v,reason:t instanceof Error?t:new Error(t)})):A.capability.resolve(),this._stats&&(this._stats.timeEnd("Rendering"),this._stats.timeEnd("Overall"))},A=new InternalRenderTask({callback:y,params:{canvasContext:a,viewport:n,transform:l,imageLayer:c,background:h},objs:this.objs,commonObjs:this.commonObjs,annotationCanvasMap:p,operatorList:v.operatorList,pageIndex:this._pageIndex,canvasFactory:_,useRequestAnimationFrame:!b,pdfBug:this._pdfBug,pageColors:f});((i=v).renderTasks||(i.renderTasks=new Set)).add(A);const P=A.task;return Promise.all([v.displayReadyCapability.promise,u]).then((t=>{let[e,s]=t;this.pendingCleanup?y():(this._stats&&this._stats.time("Rendering"),A.initializeGraphics({transparency:e,optionalContentConfig:s}),A.operatorListChanged())})).catch(y),P}getOperatorList(){let{intent:t="display",annotationMode:e=_util.AnnotationMode.ENABLE,printAnnotationStorage:s=null}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};const i=this._transport.getRenderingIntent(t,e,s,!0);let a,n=this._intentStates.get(i.cacheKey);var r;return n||(n=Object.create(null),this._intentStates.set(i.cacheKey,n)),n.opListReadCapability||(a=Object.create(null),a.operatorListChanged=function(){n.operatorList.lastChunk&&(n.opListReadCapability.resolve(n.operatorList),n.renderTasks.delete(a))},n.opListReadCapability=(0,_util.createPromiseCapability)(),((r=n).renderTasks||(r.renderTasks=new Set)).add(a),n.operatorList={fnArray:[],argsArray:[],lastChunk:!1,separateAnnots:null},this._stats&&this._stats.time("Page Request"),this._pumpOperatorList(i)),n.opListReadCapability.promise}streamTextContent(){let{disableCombineTextItems:t=!1,includeMarkedContent:e=!1}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return this._transport.messageHandler.sendWithStream("GetTextContent",{pageIndex:this._pageIndex,combineTextItems:!0!==t,includeMarkedContent:!0===e},{highWaterMark:100,size:t=>t.items.length})}getTextContent(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};if(this._transport._htmlForXfa)return this.getXfa().then((t=>_xfa_text.XfaText.textContent(t)));const e=this.streamTextContent(t);return new Promise((function(t,s){const i=e.getReader(),a={items:[],styles:Object.create(null)};!function e(){i.read().then((function(s){let{value:i,done:n}=s;n?t(a):(Object.assign(a.styles,i.styles),a.items.push(...i.items),e())}),s)}()}))}getStructTree(){return this._structTreePromise||(this._structTreePromise=this._transport.getStructTree(this._pageIndex))}_destroy(){this.destroyed=!0;const t=[];for(const e of this._intentStates.values())if(this._abortOperatorList({intentState:e,reason:new Error("Page was destroyed."),force:!0}),!e.opListReadCapability)for(const s of e.renderTasks)t.push(s.completed),s.cancel();this.objs.clear();for(const e of this._bitmaps)e.close();return this._bitmaps.clear(),this._annotationPromises.clear(),this._jsActionsPromise=null,this._structTreePromise=null,this.pendingCleanup=!1,Promise.all(t)}cleanup(){let t=arguments.length>0&&void 0!==arguments[0]&&arguments[0];return this.pendingCleanup=!0,this._tryCleanup(t)}_tryCleanup(){let t=arguments.length>0&&void 0!==arguments[0]&&arguments[0];if(!this.pendingCleanup)return!1;for(const{renderTasks:e,operatorList:s}of this._intentStates.values())if(e.size>0||!s.lastChunk)return!1;this._intentStates.clear(),this.objs.clear(),this._annotationPromises.clear(),this._jsActionsPromise=null,this._structTreePromise=null,t&&this._stats&&(this._stats=new _display_utils.StatTimer);for(const e of this._bitmaps)e.close();return this._bitmaps.clear(),this.pendingCleanup=!1,!0}_startRenderPage(t,e){const s=this._intentStates.get(e);s&&(this._stats&&this._stats.timeEnd("Page Request"),s.displayReadyCapability&&s.displayReadyCapability.resolve(t))}_renderPageChunk(t,e){for(let s=0,i=t.length;s<i;s++)e.operatorList.fnArray.push(t.fnArray[s]),e.operatorList.argsArray.push(t.argsArray[s]);e.operatorList.lastChunk=t.lastChunk,e.operatorList.separateAnnots=t.separateAnnots;for(const s of e.renderTasks)s.operatorListChanged();t.lastChunk&&this._tryCleanup()}_pumpOperatorList(t){let{renderingIntent:e,cacheKey:s,annotationStorageMap:i}=t;const a=this._transport.messageHandler.sendWithStream("GetOperatorList",{pageIndex:this._pageIndex,intent:e,cacheKey:s,annotationStorage:i}).getReader(),n=this._intentStates.get(s);n.streamReader=a;const r=()=>{a.read().then((t=>{let{value:e,done:s}=t;s?n.streamReader=null:this._transport.destroyed||(this._renderPageChunk(e,n),r())}),(t=>{if(n.streamReader=null,!this._transport.destroyed){if(n.operatorList){n.operatorList.lastChunk=!0;for(const t of n.renderTasks)t.operatorListChanged();this._tryCleanup()}if(n.displayReadyCapability)n.displayReadyCapability.reject(t);else{if(!n.opListReadCapability)throw t;n.opListReadCapability.reject(t)}}}))};r()}_abortOperatorList(t){let{intentState:e,reason:s,force:i=!1}=t;if(e.streamReader){if(!i){if(e.renderTasks.size>0)return;if(s instanceof _display_utils.RenderingCancelledException)return void(e.streamReaderCancelTimeout=setTimeout((()=>{this._abortOperatorList({intentState:e,reason:s,force:!0}),e.streamReaderCancelTimeout=null}),RENDERING_CANCELLED_TIMEOUT))}if(e.streamReader.cancel(new _util.AbortException(s.message)).catch((()=>{})),e.streamReader=null,!this._transport.destroyed){for(const[t,s]of this._intentStates)if(s===e){this._intentStates.delete(t);break}this.cleanup()}}}get stats(){return this._stats}}exports.PDFPageProxy=PDFPageProxy;class LoopbackPort{constructor(){this._listeners=[],this._deferred=Promise.resolve()}postMessage(t,e){const s={data:structuredClone(t,e)};this._deferred.then((()=>{for(const t of this._listeners)t.call(this,s)}))}addEventListener(t,e){this._listeners.push(e)}removeEventListener(t,e){const s=this._listeners.indexOf(e);this._listeners.splice(s,1)}terminate(){this._listeners.length=0}}exports.LoopbackPort=LoopbackPort;const PDFWorkerUtil={isWorkerDisabled:!1,fallbackWorkerSrc:null,fakeWorkerId:0};if(exports.PDFWorkerUtil=PDFWorkerUtil,_is_node.isNodeJS)PDFWorkerUtil.isWorkerDisabled=!0,PDFWorkerUtil.fallbackWorkerSrc="./pdf.worker.js";else if("object"===typeof document){var _document;const t=null===(_document=document)||void 0===_document||null===(_document=_document.currentScript)||void 0===_document?void 0:_document.src;t&&(PDFWorkerUtil.fallbackWorkerSrc=t.replace(/(\.(?:min\.)?js)(\?.*)?$/i,".worker$1$2"))}PDFWorkerUtil.isSameOrigin=function(t,e){let s;try{if(s=new URL(t),!s.origin||"null"===s.origin)return!1}catch(a){return!1}const i=new URL(e,s);return s.origin===i.origin},PDFWorkerUtil.createCDNWrapper=function(t){const e='importScripts("'.concat(t,'");');return URL.createObjectURL(new Blob([e]))};class PDFWorker{constructor(){let{name:t=null,port:e=null,verbosity:s=(0,_util.getVerbosityLevel)()}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};if(e&&_classStaticPrivateFieldSpecGet(PDFWorker,PDFWorker,_workerPorts).has(e))throw new Error("Cannot use more than one PDFWorker per port.");if(this.name=t,this.destroyed=!1,this.verbosity=s,this._readyCapability=(0,_util.createPromiseCapability)(),this._port=null,this._webWorker=null,this._messageHandler=null,e)return _classStaticPrivateFieldSpecGet(PDFWorker,PDFWorker,_workerPorts).set(e,this),void this._initializeFromPort(e);this._initialize()}get promise(){return this._readyCapability.promise}get port(){return this._port}get messageHandler(){return this._messageHandler}_initializeFromPort(t){this._port=t,this._messageHandler=new _message_handler.MessageHandler("main","worker",t),this._messageHandler.on("ready",(function(){})),this._readyCapability.resolve()}_initialize(){if("undefined"!==typeof Worker&&!PDFWorkerUtil.isWorkerDisabled&&!PDFWorker._mainThreadWorkerMessageHandler){let e=PDFWorker.workerSrc;try{PDFWorkerUtil.isSameOrigin(window.location.href,e)||(e=PDFWorkerUtil.createCDNWrapper(new URL(e,window.location).href));const t=new Worker(e),s=new _message_handler.MessageHandler("main","worker",t),i=()=>{t.removeEventListener("error",a),s.destroy(),t.terminate(),this.destroyed?this._readyCapability.reject(new Error("Worker was destroyed")):this._setupFakeWorker()},a=()=>{this._webWorker||i()};t.addEventListener("error",a),s.on("test",(e=>{t.removeEventListener("error",a),this.destroyed?i():e?(this._messageHandler=s,this._port=t,this._webWorker=t,this._readyCapability.resolve(),s.send("configure",{verbosity:this.verbosity})):(this._setupFakeWorker(),s.destroy(),t.terminate())})),s.on("ready",(e=>{if(t.removeEventListener("error",a),this.destroyed)i();else try{n()}catch(s){this._setupFakeWorker()}}));const n=()=>{const t=new Uint8Array;s.send("test",t,[t.buffer])};return void n()}catch(t){(0,_util.info)("The worker has been disabled.")}}this._setupFakeWorker()}_setupFakeWorker(){PDFWorkerUtil.isWorkerDisabled||((0,_util.warn)("Setting up fake worker."),PDFWorkerUtil.isWorkerDisabled=!0),PDFWorker._setupFakeWorkerGlobal.then((t=>{if(this.destroyed)return void this._readyCapability.reject(new Error("Worker was destroyed"));const e=new LoopbackPort;this._port=e;const s="fake".concat(PDFWorkerUtil.fakeWorkerId++),i=new _message_handler.MessageHandler(s+"_worker",s,e);t.setup(i,e);const a=new _message_handler.MessageHandler(s,s+"_worker",e);this._messageHandler=a,this._readyCapability.resolve(),a.send("configure",{verbosity:this.verbosity})})).catch((t=>{this._readyCapability.reject(new Error('Setting up fake worker failed: "'.concat(t.message,'".')))}))}destroy(){this.destroyed=!0,this._webWorker&&(this._webWorker.terminate(),this._webWorker=null),_classStaticPrivateFieldSpecGet(PDFWorker,PDFWorker,_workerPorts).delete(this._port),this._port=null,this._messageHandler&&(this._messageHandler.destroy(),this._messageHandler=null)}static fromPort(t){if(null===t||void 0===t||!t.port)throw new Error("PDFWorker.fromPort - invalid method signature.");return _classStaticPrivateFieldSpecGet(this,PDFWorker,_workerPorts).has(t.port)?_classStaticPrivateFieldSpecGet(this,PDFWorker,_workerPorts).get(t.port):new PDFWorker(t)}static get workerSrc(){if(_worker_options.GlobalWorkerOptions.workerSrc)return _worker_options.GlobalWorkerOptions.workerSrc;if(null!==PDFWorkerUtil.fallbackWorkerSrc)return _is_node.isNodeJS||(0,_display_utils.deprecated)('No "GlobalWorkerOptions.workerSrc" specified.'),PDFWorkerUtil.fallbackWorkerSrc;throw new Error('No "GlobalWorkerOptions.workerSrc" specified.')}static get _mainThreadWorkerMessageHandler(){try{var t;return(null===(t=globalThis.pdfjsWorker)||void 0===t?void 0:t.WorkerMessageHandler)||null}catch(e){return null}}static get _setupFakeWorkerGlobal(){const loader=async()=>{const mainWorkerMessageHandler=this._mainThreadWorkerMessageHandler;if(mainWorkerMessageHandler)return mainWorkerMessageHandler;if(_is_node.isNodeJS){const worker=eval("require")(this.workerSrc);return worker.WorkerMessageHandler}return await(0,_display_utils.loadScript)(this.workerSrc),window.pdfjsWorker.WorkerMessageHandler};return(0,_util.shadow)(this,"_setupFakeWorkerGlobal",loader())}}var _workerPorts={writable:!0,value:new WeakMap};exports.PDFWorker=PDFWorker,PDFWorker.getWorkerSrc=function(){return(0,_display_utils.deprecated)("`PDFWorker.getWorkerSrc()`, please use `PDFWorker.workerSrc` instead."),this.workerSrc};var _docStats=new WeakMap,_pageCache=new WeakMap,_pagePromises=new WeakMap,_metadataPromise=new WeakMap;class WorkerTransport{constructor(t,e,s,i){_classPrivateFieldInitSpec(this,_docStats,{writable:!0,value:null}),_classPrivateFieldInitSpec(this,_pageCache,{writable:!0,value:new Map}),_classPrivateFieldInitSpec(this,_pagePromises,{writable:!0,value:new Map}),_classPrivateFieldInitSpec(this,_metadataPromise,{writable:!0,value:null}),this.messageHandler=t,this.loadingTask=e,this.commonObjs=new PDFObjects,this.fontLoader=new _font_loader.FontLoader({docId:e.docId,onUnsupportedFeature:this._onUnsupportedFeature.bind(this),ownerDocument:i.ownerDocument,styleElement:i.styleElement}),this._params=i,i.useWorkerFetch||(this.CMapReaderFactory=new i.CMapReaderFactory({baseUrl:i.cMapUrl,isCompressed:i.cMapPacked}),this.StandardFontDataFactory=new i.StandardFontDataFactory({baseUrl:i.standardFontDataUrl})),this.destroyed=!1,this.destroyCapability=null,this._passwordCapability=null,this._networkStream=s,this._fullReader=null,this._lastProgress=null,this.downloadInfoCapability=(0,_util.createPromiseCapability)(),this.setupMessageHandler()}get annotationStorage(){return(0,_util.shadow)(this,"annotationStorage",new _annotation_storage.AnnotationStorage)}get stats(){return _classPrivateFieldGet(this,_docStats)}getRenderingIntent(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:_util.AnnotationMode.ENABLE,s=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null,i=arguments.length>3&&void 0!==arguments[3]&&arguments[3],a=_util.RenderingIntentFlag.DISPLAY,n=null;switch(t){case"any":a=_util.RenderingIntentFlag.ANY;break;case"display":break;case"print":a=_util.RenderingIntentFlag.PRINT;break;default:(0,_util.warn)("getRenderingIntent - invalid intent: ".concat(t))}switch(e){case _util.AnnotationMode.DISABLE:a+=_util.RenderingIntentFlag.ANNOTATIONS_DISABLE;break;case _util.AnnotationMode.ENABLE:break;case _util.AnnotationMode.ENABLE_FORMS:a+=_util.RenderingIntentFlag.ANNOTATIONS_FORMS;break;case _util.AnnotationMode.ENABLE_STORAGE:a+=_util.RenderingIntentFlag.ANNOTATIONS_STORAGE,n=(a&_util.RenderingIntentFlag.PRINT&&s instanceof _annotation_storage.PrintAnnotationStorage?s:this.annotationStorage).serializable;break;default:(0,_util.warn)("getRenderingIntent - invalid annotationMode: ".concat(e))}return i&&(a+=_util.RenderingIntentFlag.OPLIST),{renderingIntent:a,cacheKey:"".concat(a,"_").concat(_annotation_storage.AnnotationStorage.getHash(n)),annotationStorageMap:n}}destroy(){if(this.destroyCapability)return this.destroyCapability.promise;this.destroyed=!0,this.destroyCapability=(0,_util.createPromiseCapability)(),this._passwordCapability&&this._passwordCapability.reject(new Error("Worker was destroyed during onPassword callback"));const t=[];for(const s of _classPrivateFieldGet(this,_pageCache).values())t.push(s._destroy());_classPrivateFieldGet(this,_pageCache).clear(),_classPrivateFieldGet(this,_pagePromises).clear(),this.hasOwnProperty("annotationStorage")&&this.annotationStorage.resetModified();const e=this.messageHandler.sendWithPromise("Terminate",null);return t.push(e),Promise.all(t).then((()=>{this.commonObjs.clear(),this.fontLoader.clear(),_classPrivateFieldSet(this,_metadataPromise,null),this._getFieldObjectsPromise=null,this._hasJSActionsPromise=null,this._networkStream&&this._networkStream.cancelAllRequests(new _util.AbortException("Worker was terminated.")),this.messageHandler&&(this.messageHandler.destroy(),this.messageHandler=null),this.destroyCapability.resolve()}),this.destroyCapability.reject),this.destroyCapability.promise}setupMessageHandler(){const{messageHandler:t,loadingTask:e}=this;t.on("GetReader",((t,e)=>{(0,_util.assert)(this._networkStream,"GetReader - no `IPDFStream` instance available."),this._fullReader=this._networkStream.getFullReader(),this._fullReader.onProgress=t=>{this._lastProgress={loaded:t.loaded,total:t.total}},e.onPull=()=>{this._fullReader.read().then((function(t){let{value:s,done:i}=t;i?e.close():((0,_util.assert)((0,_util.isArrayBuffer)(s),"GetReader - expected an ArrayBuffer."),e.enqueue(new Uint8Array(s),1,[s]))})).catch((t=>{e.error(t)}))},e.onCancel=t=>{this._fullReader.cancel(t),e.ready.catch((t=>{if(!this.destroyed)throw t}))}})),t.on("ReaderHeadersReady",(t=>{const s=(0,_util.createPromiseCapability)(),i=this._fullReader;return i.headersReady.then((()=>{var t;i.isStreamingSupported&&i.isRangeSupported||(this._lastProgress&&(null===(t=e.onProgress)||void 0===t||t.call(e,this._lastProgress)),i.onProgress=t=>{var s;null===(s=e.onProgress)||void 0===s||s.call(e,{loaded:t.loaded,total:t.total})});s.resolve({isStreamingSupported:i.isStreamingSupported,isRangeSupported:i.isRangeSupported,contentLength:i.contentLength})}),s.reject),s.promise})),t.on("GetRangeReader",((t,e)=>{(0,_util.assert)(this._networkStream,"GetRangeReader - no `IPDFStream` instance available.");const s=this._networkStream.getRangeReader(t.begin,t.end);s?(e.onPull=()=>{s.read().then((function(t){let{value:s,done:i}=t;i?e.close():((0,_util.assert)((0,_util.isArrayBuffer)(s),"GetRangeReader - expected an ArrayBuffer."),e.enqueue(new Uint8Array(s),1,[s]))})).catch((t=>{e.error(t)}))},e.onCancel=t=>{s.cancel(t),e.ready.catch((t=>{if(!this.destroyed)throw t}))}):e.close()})),t.on("GetDoc",(t=>{let{pdfInfo:s}=t;this._numPages=s.numPages,this._htmlForXfa=s.htmlForXfa,delete s.htmlForXfa,e._capability.resolve(new PDFDocumentProxy(s,this))})),t.on("DocException",(function(t){let s;switch(t.name){case"PasswordException":s=new _util.PasswordException(t.message,t.code);break;case"InvalidPDFException":s=new _util.InvalidPDFException(t.message);break;case"MissingPDFException":s=new _util.MissingPDFException(t.message);break;case"UnexpectedResponseException":s=new _util.UnexpectedResponseException(t.message,t.status);break;case"UnknownErrorException":s=new _util.UnknownErrorException(t.message,t.details);break;default:(0,_util.unreachable)("DocException - expected a valid Error.")}e._capability.reject(s)})),t.on("PasswordRequest",(t=>{if(this._passwordCapability=(0,_util.createPromiseCapability)(),e.onPassword){const i=t=>{t instanceof Error?this._passwordCapability.reject(t):this._passwordCapability.resolve({password:t})};try{e.onPassword(i,t.code)}catch(s){this._passwordCapability.reject(s)}}else this._passwordCapability.reject(new _util.PasswordException(t.message,t.code));return this._passwordCapability.promise})),t.on("DataLoaded",(t=>{var s;null===(s=e.onProgress)||void 0===s||s.call(e,{loaded:t.length,total:t.length}),this.downloadInfoCapability.resolve(t)})),t.on("StartRenderPage",(t=>{this.destroyed||_classPrivateFieldGet(this,_pageCache).get(t.pageIndex)._startRenderPage(t.transparency,t.cacheKey)})),t.on("commonobj",(e=>{var s;let[i,a,n]=e;if(!this.destroyed&&!this.commonObjs.has(i))switch(a){case"Font":const e=this._params;if("error"in n){const t=n.error;(0,_util.warn)("Error during font loading: ".concat(t)),this.commonObjs.resolve(i,t);break}let r=null;e.pdfBug&&null!==(s=globalThis.FontInspector)&&void 0!==s&&s.enabled&&(r={registerFont(t,e){globalThis.FontInspector.fontAdded(t,e)}});const o=new _font_loader.FontFaceObject(n,{isEvalSupported:e.isEvalSupported,disableFontFace:e.disableFontFace,ignoreErrors:e.ignoreErrors,onUnsupportedFeature:this._onUnsupportedFeature.bind(this),fontRegistry:r});this.fontLoader.bind(o).catch((e=>t.sendWithPromise("FontFallback",{id:i}))).finally((()=>{!e.fontExtraProperties&&o.data&&(o.data=null),this.commonObjs.resolve(i,o)}));break;case"FontPath":case"Image":this.commonObjs.resolve(i,n);break;default:throw new Error("Got unknown common object type ".concat(a))}})),t.on("obj",(t=>{let[e,s,i,a]=t;if(this.destroyed)return;const n=_classPrivateFieldGet(this,_pageCache).get(s);if(!n.objs.has(e))switch(i){case"Image":n.objs.resolve(e,a);const t=8e6;if(a){let e;if(a.bitmap){const{bitmap:t,width:s,height:i}=a;e=s*i*4,n._bitmaps.add(t)}else{var r;e=(null===(r=a.data)||void 0===r?void 0:r.length)||0}e>t&&(n.cleanupAfterRender=!0)}break;case"Pattern":n.objs.resolve(e,a);break;default:throw new Error("Got unknown object type ".concat(i))}})),t.on("DocProgress",(t=>{var s;this.destroyed||null===(s=e.onProgress)||void 0===s||s.call(e,{loaded:t.loaded,total:t.total})})),t.on("DocStats",(t=>{this.destroyed||_classPrivateFieldSet(this,_docStats,Object.freeze({streamTypes:Object.freeze(t.streamTypes),fontTypes:Object.freeze(t.fontTypes)}))})),t.on("UnsupportedFeature",this._onUnsupportedFeature.bind(this)),t.on("FetchBuiltInCMap",(t=>this.destroyed?Promise.reject(new Error("Worker was destroyed.")):this.CMapReaderFactory?this.CMapReaderFactory.fetch(t):Promise.reject(new Error("CMapReaderFactory not initialized, see the `useWorkerFetch` parameter.")))),t.on("FetchStandardFontData",(t=>this.destroyed?Promise.reject(new Error("Worker was destroyed.")):this.StandardFontDataFactory?this.StandardFontDataFactory.fetch(t):Promise.reject(new Error("StandardFontDataFactory not initialized, see the `useWorkerFetch` parameter."))))}_onUnsupportedFeature(t){var e,s;let{featureId:i}=t;this.destroyed||null===(e=(s=this.loadingTask).onUnsupportedFeature)||void 0===e||e.call(s,i)}getData(){return this.messageHandler.sendWithPromise("GetData",null)}getPage(t){if(!Number.isInteger(t)||t<=0||t>this._numPages)return Promise.reject(new Error("Invalid page request."));const e=t-1,s=_classPrivateFieldGet(this,_pagePromises).get(e);if(s)return s;const i=this.messageHandler.sendWithPromise("GetPage",{pageIndex:e}).then((t=>{if(this.destroyed)throw new Error("Transport destroyed");const s=new PDFPageProxy(e,t,this,this._params.ownerDocument,this._params.pdfBug);return _classPrivateFieldGet(this,_pageCache).set(e,s),s}));return _classPrivateFieldGet(this,_pagePromises).set(e,i),i}getPageIndex(t){return"object"!==typeof t||null===t||!Number.isInteger(t.num)||t.num<0||!Number.isInteger(t.gen)||t.gen<0?Promise.reject(new Error("Invalid pageIndex request.")):this.messageHandler.sendWithPromise("GetPageIndex",{num:t.num,gen:t.gen})}getAnnotations(t,e){return this.messageHandler.sendWithPromise("GetAnnotations",{pageIndex:t,intent:e})}saveDocument(){var t,e;return this.messageHandler.sendWithPromise("SaveDocument",{isPureXfa:!!this._htmlForXfa,numPages:this._numPages,annotationStorage:this.annotationStorage.serializable,filename:null!==(t=null===(e=this._fullReader)||void 0===e?void 0:e.filename)&&void 0!==t?t:null}).finally((()=>{this.annotationStorage.resetModified()}))}getFieldObjects(){return this._getFieldObjectsPromise||(this._getFieldObjectsPromise=this.messageHandler.sendWithPromise("GetFieldObjects",null))}hasJSActions(){return this._hasJSActionsPromise||(this._hasJSActionsPromise=this.messageHandler.sendWithPromise("HasJSActions",null))}getCalculationOrderIds(){return this.messageHandler.sendWithPromise("GetCalculationOrderIds",null)}getDestinations(){return this.messageHandler.sendWithPromise("GetDestinations",null)}getDestination(t){return"string"!==typeof t?Promise.reject(new Error("Invalid destination request.")):this.messageHandler.sendWithPromise("GetDestination",{id:t})}getPageLabels(){return this.messageHandler.sendWithPromise("GetPageLabels",null)}getPageLayout(){return this.messageHandler.sendWithPromise("GetPageLayout",null)}getPageMode(){return this.messageHandler.sendWithPromise("GetPageMode",null)}getViewerPreferences(){return this.messageHandler.sendWithPromise("GetViewerPreferences",null)}getOpenAction(){return this.messageHandler.sendWithPromise("GetOpenAction",null)}getAttachments(){return this.messageHandler.sendWithPromise("GetAttachments",null)}getJavaScript(){return this.messageHandler.sendWithPromise("GetJavaScript",null)}getDocJSActions(){return this.messageHandler.sendWithPromise("GetDocJSActions",null)}getPageJSActions(t){return this.messageHandler.sendWithPromise("GetPageJSActions",{pageIndex:t})}getStructTree(t){return this.messageHandler.sendWithPromise("GetStructTree",{pageIndex:t})}getOutline(){return this.messageHandler.sendWithPromise("GetOutline",null)}getOptionalContentConfig(){return this.messageHandler.sendWithPromise("GetOptionalContentConfig",null).then((t=>new _optional_content_config.OptionalContentConfig(t)))}getPermissions(){return this.messageHandler.sendWithPromise("GetPermissions",null)}getMetadata(){return _classPrivateFieldGet(this,_metadataPromise)||_classPrivateFieldSet(this,_metadataPromise,this.messageHandler.sendWithPromise("GetMetadata",null).then((t=>{var e,s,i,a;return{info:t[0],metadata:t[1]?new _metadata.Metadata(t[1]):null,contentDispositionFilename:null!==(e=null===(s=this._fullReader)||void 0===s?void 0:s.filename)&&void 0!==e?e:null,contentLength:null!==(i=null===(a=this._fullReader)||void 0===a?void 0:a.contentLength)&&void 0!==i?i:null}})))}getMarkInfo(){return this.messageHandler.sendWithPromise("GetMarkInfo",null)}async startCleanup(){let t=arguments.length>0&&void 0!==arguments[0]&&arguments[0];if(await this.messageHandler.sendWithPromise("Cleanup",null),!this.destroyed){for(const t of _classPrivateFieldGet(this,_pageCache).values())if(!t.cleanup())throw new Error("startCleanup: Page ".concat(t.pageNumber," is currently rendering."));this.commonObjs.clear(),t||this.fontLoader.clear(),_classPrivateFieldSet(this,_metadataPromise,null),this._getFieldObjectsPromise=null,this._hasJSActionsPromise=null}}get loadingParams(){const t=this._params;return(0,_util.shadow)(this,"loadingParams",{disableAutoFetch:t.disableAutoFetch,enableXfa:t.enableXfa})}}var _objs=new WeakMap,_ensureObj=new WeakSet;class PDFObjects{constructor(){_classPrivateMethodInitSpec(this,_ensureObj),_classPrivateFieldInitSpec(this,_objs,{writable:!0,value:Object.create(null)})}get(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;if(e){const s=_classPrivateMethodGet(this,_ensureObj,_ensureObj2).call(this,t);return s.capability.promise.then((()=>e(s.data))),null}const s=_classPrivateFieldGet(this,_objs)[t];if(null===s||void 0===s||!s.capability.settled)throw new Error("Requesting object that isn't resolved yet ".concat(t,"."));return s.data}has(t){const e=_classPrivateFieldGet(this,_objs)[t];return(null===e||void 0===e?void 0:e.capability.settled)||!1}resolve(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;const s=_classPrivateMethodGet(this,_ensureObj,_ensureObj2).call(this,t);s.data=e,s.capability.resolve()}clear(){_classPrivateFieldSet(this,_objs,Object.create(null))}}function _ensureObj2(t){const e=_classPrivateFieldGet(this,_objs)[t];return e||(_classPrivateFieldGet(this,_objs)[t]={capability:(0,_util.createPromiseCapability)(),data:null})}var _internalRenderTask=new WeakMap;class RenderTask{constructor(t){_classPrivateFieldInitSpec(this,_internalRenderTask,{writable:!0,value:null}),_classPrivateFieldSet(this,_internalRenderTask,t),this.onContinue=null}get promise(){return _classPrivateFieldGet(this,_internalRenderTask).capability.promise}cancel(){_classPrivateFieldGet(this,_internalRenderTask).cancel()}get separateAnnots(){const{separateAnnots:t}=_classPrivateFieldGet(this,_internalRenderTask).operatorList;if(!t)return!1;const{annotationCanvasMap:e}=_classPrivateFieldGet(this,_internalRenderTask);return t.form||t.canvas&&(null===e||void 0===e?void 0:e.size)>0}}exports.RenderTask=RenderTask;class InternalRenderTask{constructor(t){let{callback:e,params:s,objs:i,commonObjs:a,annotationCanvasMap:n,operatorList:r,pageIndex:o,canvasFactory:l,useRequestAnimationFrame:c=!1,pdfBug:d=!1,pageColors:h=null}=t;this.callback=e,this.params=s,this.objs=i,this.commonObjs=a,this.annotationCanvasMap=n,this.operatorListIdx=null,this.operatorList=r,this._pageIndex=o,this.canvasFactory=l,this._pdfBug=d,this.pageColors=h,this.running=!1,this.graphicsReadyCallback=null,this.graphicsReady=!1,this._useRequestAnimationFrame=!0===c&&"undefined"!==typeof window,this.cancelled=!1,this.capability=(0,_util.createPromiseCapability)(),this.task=new RenderTask(this),this._cancelBound=this.cancel.bind(this),this._continueBound=this._continue.bind(this),this._scheduleNextBound=this._scheduleNext.bind(this),this._nextBound=this._next.bind(this),this._canvas=s.canvasContext.canvas}get completed(){return this.capability.promise.catch((function(){}))}initializeGraphics(t){var e;let{transparency:s=!1,optionalContentConfig:i}=t;if(this.cancelled)return;if(this._canvas){if(_classStaticPrivateFieldSpecGet(InternalRenderTask,InternalRenderTask,_canvasInUse).has(this._canvas))throw new Error("Cannot use the same canvas during multiple render() operations. Use different canvas or ensure previous operations were cancelled or completed.");_classStaticPrivateFieldSpecGet(InternalRenderTask,InternalRenderTask,_canvasInUse).add(this._canvas)}this._pdfBug&&null!==(e=globalThis.StepperManager)&&void 0!==e&&e.enabled&&(this.stepper=globalThis.StepperManager.create(this._pageIndex),this.stepper.init(this.operatorList),this.stepper.nextBreakPoint=this.stepper.getNextBreakPoint());const{canvasContext:a,viewport:n,transform:r,imageLayer:o,background:l}=this.params;this.gfx=new _canvas.CanvasGraphics(a,this.commonObjs,this.objs,this.canvasFactory,o,i,this.annotationCanvasMap,this.pageColors),this.gfx.beginDrawing({transform:r,viewport:n,transparency:s,background:l}),this.operatorListIdx=0,this.graphicsReady=!0,this.graphicsReadyCallback&&this.graphicsReadyCallback()}cancel(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null;this.running=!1,this.cancelled=!0,this.gfx&&this.gfx.endDrawing(),this._canvas&&_classStaticPrivateFieldSpecGet(InternalRenderTask,InternalRenderTask,_canvasInUse).delete(this._canvas),this.callback(t||new _display_utils.RenderingCancelledException("Rendering cancelled, page ".concat(this._pageIndex+1),"canvas"))}operatorListChanged(){this.graphicsReady?(this.stepper&&this.stepper.updateOperatorList(this.operatorList),this.running||this._continue()):this.graphicsReadyCallback||(this.graphicsReadyCallback=this._continueBound)}_continue(){this.running=!0,this.cancelled||(this.task.onContinue?this.task.onContinue(this._scheduleNextBound):this._scheduleNext())}_scheduleNext(){this._useRequestAnimationFrame?window.requestAnimationFrame((()=>{this._nextBound().catch(this._cancelBound)})):Promise.resolve().then(this._nextBound).catch(this._cancelBound)}async _next(){this.cancelled||(this.operatorListIdx=this.gfx.executeOperatorList(this.operatorList,this.operatorListIdx,this._continueBound,this.stepper),this.operatorListIdx===this.operatorList.argsArray.length&&(this.running=!1,this.operatorList.lastChunk&&(this.gfx.endDrawing(),this._canvas&&_classStaticPrivateFieldSpecGet(InternalRenderTask,InternalRenderTask,_canvasInUse).delete(this._canvas),this.callback())))}}var _canvasInUse={writable:!0,value:new WeakSet};const version="2.15.349";exports.version=version;const build="b8aa9c622";exports.build=build},(t,e,s)=>{Object.defineProperty(e,"__esModule",{value:!0}),e.PrintAnnotationStorage=e.AnnotationStorage=void 0;var i=s(1),a=s(8),n=s(10),r=new WeakSet;class o{constructor(){_classPrivateMethodInitSpec(this,r),this._storage=new Map,this._modified=!1,this.onSetModified=null,this.onResetModified=null}getValue(t,e){const s=this._storage.get(t);return void 0===s?e:Object.assign(e,s)}getRawValue(t){return this._storage.get(t)}removeKey(t){this._storage.delete(t),0===this._storage.size&&this.resetModified()}setValue(t,e){const s=this._storage.get(t);let i=!1;if(void 0!==s)for(const[a,n]of Object.entries(e))s[a]!==n&&(i=!0,s[a]=n);else i=!0,this._storage.set(t,e);i&&_classPrivateMethodGet(this,r,l).call(this)}has(t){return this._storage.has(t)}getAll(){return this._storage.size>0?(0,i.objectFromMap)(this._storage):null}get size(){return this._storage.size}resetModified(){this._modified&&(this._modified=!1,"function"===typeof this.onResetModified&&this.onResetModified())}get print(){return new d(this)}get serializable(){if(0===this._storage.size)return null;const t=new Map;for(const[e,s]of this._storage){const i=s instanceof a.AnnotationEditor?s.serialize():s;i&&t.set(e,i)}return t}static getHash(t){if(!t)return"";const e=new n.MurmurHash3_64;for(const[s,i]of t)e.update("".concat(s,":").concat(JSON.stringify(i)));return e.hexdigest()}}function l(){this._modified||(this._modified=!0,"function"===typeof this.onSetModified&&this.onSetModified())}e.AnnotationStorage=o;var c=new WeakMap;class d extends o{constructor(t){super(),_classPrivateFieldInitSpec(this,c,{writable:!0,value:null}),_classPrivateFieldSet(this,c,structuredClone(t.serializable))}get print(){(0,i.unreachable)("Should not call PrintAnnotationStorage.print")}get serializable(){return _classPrivateFieldGet(this,c)}}e.PrintAnnotationStorage=d},(t,e,s)=>{Object.defineProperty(e,"__esModule",{value:!0}),e.AnnotationEditor=void 0;var i=s(9),a=s(1),n=new WeakMap,r=new WeakMap,o=new WeakMap,l=new WeakMap,c=new WeakMap,d=new WeakMap;class h{constructor(t){_classPrivateFieldInitSpec(this,n,{writable:!0,value:this.focusin.bind(this)}),_classPrivateFieldInitSpec(this,r,{writable:!0,value:this.focusout.bind(this)}),_classPrivateFieldInitSpec(this,o,{writable:!0,value:!1}),_classPrivateFieldInitSpec(this,l,{writable:!0,value:!1}),_classPrivateFieldInitSpec(this,c,{writable:!0,value:!1}),_classPrivateFieldInitSpec(this,d,{writable:!0,value:h._zIndex++}),this.constructor===h&&(0,a.unreachable)("Cannot initialize AnnotationEditor."),this.parent=t.parent,this.id=t.id,this.width=this.height=null,this.pageIndex=t.parent.pageIndex,this.name=t.name,this.div=null;const[e,s]=this.parent.viewportBaseDimensions;this.x=t.x/e,this.y=t.y/s,this.rotation=this.parent.viewport.rotation,this.isAttachedToDOM=!1}static get _defaultLineColor(){return(0,a.shadow)(this,"_defaultLineColor",this._colorManager.getHexCode("CanvasText"))}setInBackground(){this.div.style.zIndex=0}setInForeground(){this.div.style.zIndex=_classPrivateFieldGet(this,d)}focusin(t){_classPrivateFieldGet(this,o)?_classPrivateFieldSet(this,o,!1):this.parent.setSelected(this)}focusout(t){if(!this.isAttachedToDOM)return;const e=t.relatedTarget;null!==e&&void 0!==e&&e.closest("#".concat(this.id))||(t.preventDefault(),this.parent.isMultipleSelection||this.commitOrRemove())}commitOrRemove(){this.isEmpty()?this.remove():this.commit()}commit(){this.parent.addToAnnotationStorage(this)}dragstart(t){const e=this.parent.div.getBoundingClientRect();this.startX=t.clientX-e.x,this.startY=t.clientY-e.y,t.dataTransfer.setData("text/plain",this.id),t.dataTransfer.effectAllowed="move"}setAt(t,e,s,i){const[a,n]=this.parent.viewportBaseDimensions;[s,i]=this.screenToPageTranslation(s,i),this.x=(t+s)/a,this.y=(e+i)/n,this.div.style.left="".concat(100*this.x,"%"),this.div.style.top="".concat(100*this.y,"%")}translate(t,e){const[s,i]=this.parent.viewportBaseDimensions;[t,e]=this.screenToPageTranslation(t,e),this.x+=t/s,this.y+=e/i,this.div.style.left="".concat(100*this.x,"%"),this.div.style.top="".concat(100*this.y,"%")}screenToPageTranslation(t,e){const{rotation:s}=this.parent.viewport;switch(s){case 90:return[e,-t];case 180:return[-t,-e];case 270:return[-e,t];default:return[t,e]}}setDims(t,e){const[s,i]=this.parent.viewportBaseDimensions;this.div.style.width="".concat(100*t/s,"%"),this.div.style.height="".concat(100*e/i,"%")}getInitialTranslation(){return[0,0]}render(){this.div=document.createElement("div"),this.div.setAttribute("data-editor-rotation",(360-this.rotation)%360),this.div.className=this.name,this.div.setAttribute("id",this.id),this.div.setAttribute("tabIndex",0),this.setInForeground(),this.div.addEventListener("focusin",_classPrivateFieldGet(this,n)),this.div.addEventListener("focusout",_classPrivateFieldGet(this,r));const[t,e]=this.getInitialTranslation();return this.translate(t,e),(0,i.bindEvents)(this,this.div,["dragstart","pointerdown"]),this.div}pointerdown(t){const e=i.KeyboardManager.platform.isMac;0!==t.button||t.ctrlKey&&e?t.preventDefault():(t.ctrlKey&&!e||t.shiftKey||t.metaKey&&e?this.parent.toggleSelected(this):this.parent.setSelected(this),_classPrivateFieldSet(this,o,!0))}getRect(t,e){const[s,i]=this.parent.viewportBaseDimensions,[a,n]=this.parent.pageDimensions,r=a*t/s,o=n*e/i,l=this.x*a,c=this.y*n,d=this.width*a,h=this.height*n;switch(this.rotation){case 0:return[l+r,n-c-o-h,l+r+d,n-c-o];case 90:return[l+o,n-c+r,l+o+h,n-c+r+d];case 180:return[l-r-d,n-c+o,l-r,n-c+o+h];case 270:return[l-o-h,n-c-r-d,l-o,n-c-r];default:throw new Error("Invalid rotation")}}getRectInCurrentCoords(t,e){const[s,i,a,n]=t,r=a-s,o=n-i;switch(this.rotation){case 0:return[s,e-n,r,o];case 90:return[s,e-i,o,r];case 180:return[a,e-i,r,o];case 270:return[a,e-n,o,r];default:throw new Error("Invalid rotation")}}onceAdded(){}isEmpty(){return!1}enableEditMode(){_classPrivateFieldSet(this,c,!0)}disableEditMode(){_classPrivateFieldSet(this,c,!1)}isInEditMode(){return _classPrivateFieldGet(this,c)}shouldGetKeyboardEvents(){return!1}needsToBeRebuilt(){return this.div&&!this.isAttachedToDOM}rebuild(){var t;null===(t=this.div)||void 0===t||t.addEventListener("focusin",_classPrivateFieldGet(this,n))}serialize(){(0,a.unreachable)("An editor must be serializable")}static deserialize(t,e){const s=new this.prototype.constructor({parent:e,id:e.getNextId()});s.rotation=t.rotation;const[i,a]=e.pageDimensions,[n,r,o,l]=s.getRectInCurrentCoords(t.rect,a);return s.x=n/i,s.y=r/a,s.width=o/i,s.height=l/a,s}remove(){this.div.removeEventListener("focusin",_classPrivateFieldGet(this,n)),this.div.removeEventListener("focusout",_classPrivateFieldGet(this,r)),this.isEmpty()||this.commit(),this.parent.remove(this)}select(){var t;null===(t=this.div)||void 0===t||t.classList.add("selectedEditor")}unselect(){var t;null===(t=this.div)||void 0===t||t.classList.remove("selectedEditor")}updateParams(t,e){}disableEditing(){}enableEditing(){}getIdForTextLayer(){return this.id}get propertiesToUpdate(){return{}}get contentDiv(){return this.div}get isEditing(){return _classPrivateFieldGet(this,l)}set isEditing(t){_classPrivateFieldSet(this,l,t),t?(this.parent.setSelected(this),this.parent.setActiveEditor(this)):this.parent.setActiveEditor(null)}}_defineProperty(h,"_colorManager",new i.ColorManager),_defineProperty(h,"_zIndex",1),e.AnnotationEditor=h},(t,e,s)=>{var i;Object.defineProperty(e,"__esModule",{value:!0}),e.KeyboardManager=e.CommandManager=e.ColorManager=e.AnnotationEditorUIManager=void 0,e.bindEvents=function(t,e,s){for(const i of s)e.addEventListener(i,t[i].bind(t))},e.opacityToHex=function(t){return Math.round(Math.min(255,Math.max(1,255*t))).toString(16).padStart(2,"0")};var a=s(1),n=s(4),r=new WeakMap;class o{constructor(){_classPrivateFieldInitSpec(this,r,{writable:!0,value:0})}getId(){var t,e;return"".concat(a.AnnotationEditorPrefix).concat((_classPrivateFieldSet(this,r,(t=_classPrivateFieldGet(this,r),e=t++,t)),e))}}var l=new WeakMap,c=new WeakMap,d=new WeakMap,h=new WeakMap;class u{constructor(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:128;_classPrivateFieldInitSpec(this,l,{writable:!0,value:[]}),_classPrivateFieldInitSpec(this,c,{writable:!0,value:!1}),_classPrivateFieldInitSpec(this,d,{writable:!0,value:void 0}),_classPrivateFieldInitSpec(this,h,{writable:!0,value:-1}),_classPrivateFieldSet(this,d,t)}add(t){let{cmd:e,undo:s,mustExec:i,type:a=NaN,overwriteIfSameType:n=!1,keepUndo:r=!1}=t;if(i&&e(),_classPrivateFieldGet(this,c))return;const o={cmd:e,undo:s,type:a};if(-1===_classPrivateFieldGet(this,h))return _classPrivateFieldGet(this,l).length>0&&(_classPrivateFieldGet(this,l).length=0),_classPrivateFieldSet(this,h,0),void _classPrivateFieldGet(this,l).push(o);if(n&&_classPrivateFieldGet(this,l)[_classPrivateFieldGet(this,h)].type===a)return r&&(o.undo=_classPrivateFieldGet(this,l)[_classPrivateFieldGet(this,h)].undo),void(_classPrivateFieldGet(this,l)[_classPrivateFieldGet(this,h)]=o);const u=_classPrivateFieldGet(this,h)+1;u===_classPrivateFieldGet(this,d)?_classPrivateFieldGet(this,l).splice(0,1):(_classPrivateFieldSet(this,h,u),u<_classPrivateFieldGet(this,l).length&&_classPrivateFieldGet(this,l).splice(u)),_classPrivateFieldGet(this,l).push(o)}undo(){-1!==_classPrivateFieldGet(this,h)&&(_classPrivateFieldSet(this,c,!0),_classPrivateFieldGet(this,l)[_classPrivateFieldGet(this,h)].undo(),_classPrivateFieldSet(this,c,!1),_classPrivateFieldSet(this,h,_classPrivateFieldGet(this,h)-1))}redo(){_classPrivateFieldGet(this,h)<_classPrivateFieldGet(this,l).length-1&&(_classPrivateFieldSet(this,h,_classPrivateFieldGet(this,h)+1),_classPrivateFieldSet(this,c,!0),_classPrivateFieldGet(this,l)[_classPrivateFieldGet(this,h)].cmd(),_classPrivateFieldSet(this,c,!1))}hasSomethingToUndo(){return-1!==_classPrivateFieldGet(this,h)}hasSomethingToRedo(){return _classPrivateFieldGet(this,h)<_classPrivateFieldGet(this,l).length-1}destroy(){_classPrivateFieldSet(this,l,null)}}e.CommandManager=u;var p=new WeakSet;class f{constructor(t){_classPrivateMethodInitSpec(this,p),this.buffer=[],this.callbacks=new Map,this.allKeys=new Set;const e=f.platform.isMac;for(const[s,i]of t)for(const t of s){const s=t.startsWith("mac+");e&&s?(this.callbacks.set(t.slice(4),i),this.allKeys.add(t.split("+").at(-1))):e||s||(this.callbacks.set(t,i),this.allKeys.add(t.split("+").at(-1)))}}static get platform(){const t="undefined"!==typeof navigator?navigator.platform:"";return(0,a.shadow)(this,"platform",{isWin:t.includes("Win"),isMac:t.includes("Mac")})}exec(t,e){if(!this.allKeys.has(e.key))return;const s=this.callbacks.get(_classPrivateMethodGet(this,p,m).call(this,e));s&&(s.bind(t)(),e.stopPropagation(),e.preventDefault())}}function m(t){t.altKey&&this.buffer.push("alt"),t.ctrlKey&&this.buffer.push("ctrl"),t.metaKey&&this.buffer.push("meta"),t.shiftKey&&this.buffer.push("shift"),this.buffer.push(t.key);const e=this.buffer.join("+");return this.buffer.length=0,e}e.KeyboardManager=f;var g=new WeakMap;class v{constructor(){_classPrivateFieldInitSpec(this,g,{writable:!0,value:null})}copy(t){t&&(Array.isArray(t)?_classPrivateFieldSet(this,g,t.map((t=>t.serialize()))):_classPrivateFieldSet(this,g,[t.serialize()]),_classPrivateFieldSet(this,g,_classPrivateFieldGet(this,g).filter((t=>!!t))),0===_classPrivateFieldGet(this,g).length&&_classPrivateFieldSet(this,g,null))}paste(){return _classPrivateFieldGet(this,g)}isEmpty(){return null===_classPrivateFieldGet(this,g)}destroy(){_classPrivateFieldSet(this,g,null)}}class _{get _colors(){const t=new Map([["CanvasText",null],["Canvas",null]]);return(0,n.getColorValues)(t),(0,a.shadow)(this,"_colors",t)}convert(t){const e=(0,n.getRGB)(t);if(!window.matchMedia("(forced-colors: active)").matches)return e;for(const[s,i]of this._colors)if(i.every(((t,s)=>t===e[s])))return _._colorsMapping.get(s);return e}getHexCode(t){const e=this._colors.get(t);return e?a.Util.makeHexColor(...e):t}}_defineProperty(_,"_colorsMapping",new Map([["CanvasText",[0,0,0]],["Canvas",[255,255,255]]])),e.ColorManager=_;var b=new WeakMap,y=new WeakMap,A=new WeakMap,P=new WeakMap,S=new WeakMap,x=new WeakMap,F=new WeakMap,w=new WeakMap,C=new WeakMap,M=new WeakMap,k=new WeakMap,E=new WeakMap,T=new WeakMap,R=new WeakMap,O=new WeakMap,I=new WeakMap,D=new WeakMap,L=new WeakMap,N=new WeakSet,G=new WeakSet,j=new WeakSet,W=new WeakSet,U=new WeakSet,B=new WeakSet,q=new WeakSet,z=new WeakSet,H=new WeakSet;class V{constructor(t,e){_classPrivateMethodInitSpec(this,H),_classPrivateMethodInitSpec(this,z),_classPrivateMethodInitSpec(this,q),_classPrivateMethodInitSpec(this,B),_classPrivateMethodInitSpec(this,U),_classPrivateMethodInitSpec(this,W),_classPrivateMethodInitSpec(this,j),_classPrivateMethodInitSpec(this,G),_classPrivateMethodInitSpec(this,N),_classPrivateFieldInitSpec(this,b,{writable:!0,value:null}),_classPrivateFieldInitSpec(this,y,{writable:!0,value:new Map}),_classPrivateFieldInitSpec(this,A,{writable:!0,value:new Map}),_classPrivateFieldInitSpec(this,P,{writable:!0,value:new v}),_classPrivateFieldInitSpec(this,S,{writable:!0,value:new u}),_classPrivateFieldInitSpec(this,x,{writable:!0,value:0}),_classPrivateFieldInitSpec(this,F,{writable:!0,value:null}),_classPrivateFieldInitSpec(this,w,{writable:!0,value:null}),_classPrivateFieldInitSpec(this,C,{writable:!0,value:new o}),_classPrivateFieldInitSpec(this,M,{writable:!0,value:!1}),_classPrivateFieldInitSpec(this,k,{writable:!0,value:a.AnnotationEditorType.NONE}),_classPrivateFieldInitSpec(this,E,{writable:!0,value:new Set}),_classPrivateFieldInitSpec(this,T,{writable:!0,value:this.keydown.bind(this)}),_classPrivateFieldInitSpec(this,R,{writable:!0,value:this.onEditingAction.bind(this)}),_classPrivateFieldInitSpec(this,O,{writable:!0,value:this.onPageChanging.bind(this)}),_classPrivateFieldInitSpec(this,I,{writable:!0,value:this.onTextLayerRendered.bind(this)}),_classPrivateFieldInitSpec(this,D,{writable:!0,value:{isEditing:!1,isEmpty:!0,hasEmptyClipboard:!0,hasSomethingToUndo:!1,hasSomethingToRedo:!1,hasSelectedEditor:!1}}),_classPrivateFieldInitSpec(this,L,{writable:!0,value:null}),_classPrivateFieldSet(this,L,t),_classPrivateFieldSet(this,w,e),_classPrivateFieldGet(this,w)._on("editingaction",_classPrivateFieldGet(this,R)),_classPrivateFieldGet(this,w)._on("pagechanging",_classPrivateFieldGet(this,O)),_classPrivateFieldGet(this,w)._on("textlayerrendered",_classPrivateFieldGet(this,I))}destroy(){_classPrivateMethodGet(this,G,Y).call(this),_classPrivateFieldGet(this,w)._off("editingaction",_classPrivateFieldGet(this,R)),_classPrivateFieldGet(this,w)._off("pagechanging",_classPrivateFieldGet(this,O)),_classPrivateFieldGet(this,w)._off("textlayerrendered",_classPrivateFieldGet(this,I));for(const t of _classPrivateFieldGet(this,A).values())t.destroy();_classPrivateFieldGet(this,A).clear(),_classPrivateFieldGet(this,y).clear(),_classPrivateFieldSet(this,b,null),_classPrivateFieldGet(this,E).clear(),_classPrivateFieldGet(this,P).destroy(),_classPrivateFieldGet(this,S).destroy()}onPageChanging(t){let{pageNumber:e}=t;_classPrivateFieldSet(this,x,e-1)}onTextLayerRendered(t){let{pageNumber:e}=t;const s=e-1,i=_classPrivateFieldGet(this,A).get(s);null===i||void 0===i||i.onTextLayerRendered()}focusMainContainer(){_classPrivateFieldGet(this,L).focus()}keydown(t){var e;null!==(e=this.getActive())&&void 0!==e&&e.shouldGetKeyboardEvents()||V._keyboardManager.exec(this,t)}onEditingAction(t){["undo","redo","cut","copy","paste","delete","selectAll"].includes(t.name)&&this[t.name]()}setEditingState(t){t?(_classPrivateMethodGet(this,N,X).call(this),_classPrivateMethodGet(this,j,K).call(this,{isEditing:_classPrivateFieldGet(this,k)!==a.AnnotationEditorType.NONE,isEmpty:_classPrivateMethodGet(this,z,tt).call(this),hasSomethingToUndo:_classPrivateFieldGet(this,S).hasSomethingToUndo(),hasSomethingToRedo:_classPrivateFieldGet(this,S).hasSomethingToRedo(),hasSelectedEditor:!1,hasEmptyClipboard:_classPrivateFieldGet(this,P).isEmpty()})):(_classPrivateMethodGet(this,G,Y).call(this),_classPrivateMethodGet(this,j,K).call(this,{isEditing:!1}))}registerEditorTypes(t){_classPrivateFieldSet(this,F,t);for(const e of _classPrivateFieldGet(this,F))_classPrivateMethodGet(this,W,J).call(this,e.defaultPropertiesToUpdate)}getId(){return _classPrivateFieldGet(this,C).getId()}addLayer(t){_classPrivateFieldGet(this,A).set(t.pageIndex,t),_classPrivateFieldGet(this,M)?t.enable():t.disable()}removeLayer(t){_classPrivateFieldGet(this,A).delete(t.pageIndex)}updateMode(t){if(_classPrivateFieldSet(this,k,t),t===a.AnnotationEditorType.NONE)this.setEditingState(!1),_classPrivateMethodGet(this,B,Z).call(this);else{this.setEditingState(!0),_classPrivateMethodGet(this,U,Q).call(this);for(const e of _classPrivateFieldGet(this,A).values())e.updateMode(t)}}updateToolbar(t){t!==_classPrivateFieldGet(this,k)&&_classPrivateFieldGet(this,w).dispatch("switchannotationeditormode",{source:this,mode:t})}updateParams(t,e){for(const s of _classPrivateFieldGet(this,E))s.updateParams(t,e);for(const s of _classPrivateFieldGet(this,F))s.updateDefaultParams(t,e)}getEditors(t){const e=[];for(const s of _classPrivateFieldGet(this,y).values())s.pageIndex===t&&e.push(s);return e}getEditor(t){return _classPrivateFieldGet(this,y).get(t)}addEditor(t){_classPrivateFieldGet(this,y).set(t.id,t)}removeEditor(t){_classPrivateFieldGet(this,y).delete(t.id),this.unselect(t)}setActiveEditor(t){_classPrivateFieldGet(this,b)!==t&&(_classPrivateFieldSet(this,b,t),t&&_classPrivateMethodGet(this,W,J).call(this,t.propertiesToUpdate))}toggleSelected(t){if(_classPrivateFieldGet(this,E).has(t))return _classPrivateFieldGet(this,E).delete(t),t.unselect(),void _classPrivateMethodGet(this,j,K).call(this,{hasSelectedEditor:this.hasSelection});_classPrivateFieldGet(this,E).add(t),t.select(),_classPrivateMethodGet(this,W,J).call(this,t.propertiesToUpdate),_classPrivateMethodGet(this,j,K).call(this,{hasSelectedEditor:!0})}setSelected(t){for(const e of _classPrivateFieldGet(this,E))e!==t&&e.unselect();_classPrivateFieldGet(this,E).clear(),_classPrivateFieldGet(this,E).add(t),t.select(),_classPrivateMethodGet(this,W,J).call(this,t.propertiesToUpdate),_classPrivateMethodGet(this,j,K).call(this,{hasSelectedEditor:!0})}isSelected(t){return _classPrivateFieldGet(this,E).has(t)}unselect(t){t.unselect(),_classPrivateFieldGet(this,E).delete(t),_classPrivateMethodGet(this,j,K).call(this,{hasSelectedEditor:this.hasSelection})}get hasSelection(){return 0!==_classPrivateFieldGet(this,E).size}undo(){_classPrivateFieldGet(this,S).undo(),_classPrivateMethodGet(this,j,K).call(this,{hasSomethingToUndo:_classPrivateFieldGet(this,S).hasSomethingToUndo(),hasSomethingToRedo:!0,isEmpty:_classPrivateMethodGet(this,z,tt).call(this)})}redo(){_classPrivateFieldGet(this,S).redo(),_classPrivateMethodGet(this,j,K).call(this,{hasSomethingToUndo:!0,hasSomethingToRedo:_classPrivateFieldGet(this,S).hasSomethingToRedo(),isEmpty:_classPrivateMethodGet(this,z,tt).call(this)})}addCommands(t){_classPrivateFieldGet(this,S).add(t),_classPrivateMethodGet(this,j,K).call(this,{hasSomethingToUndo:!0,hasSomethingToRedo:!1,isEmpty:_classPrivateMethodGet(this,z,tt).call(this)})}delete(){if(_classPrivateFieldGet(this,b)&&_classPrivateFieldGet(this,b).commitOrRemove(),!this.hasSelection)return;const t=[..._classPrivateFieldGet(this,E)];this.addCommands({cmd:()=>{for(const e of t)e.remove()},undo:()=>{for(const e of t)_classPrivateMethodGet(this,q,$).call(this,e)},mustExec:!0})}copy(){if(_classPrivateFieldGet(this,b)&&_classPrivateFieldGet(this,b).commitOrRemove(),this.hasSelection){const t=[];for(const e of _classPrivateFieldGet(this,E))e.isEmpty()||t.push(e);if(0===t.length)return;_classPrivateFieldGet(this,P).copy(t),_classPrivateMethodGet(this,j,K).call(this,{hasEmptyClipboard:!1})}}cut(){this.copy(),this.delete()}paste(){if(_classPrivateFieldGet(this,P).isEmpty())return;this.unselectAll();const t=_classPrivateFieldGet(this,A).get(_classPrivateFieldGet(this,x)),e=_classPrivateFieldGet(this,P).paste().map((e=>t.deserialize(e)));this.addCommands({cmd:()=>{for(const t of e)_classPrivateMethodGet(this,q,$).call(this,t);_classPrivateMethodGet(this,H,et).call(this,e)},undo:()=>{for(const t of e)t.remove()},mustExec:!0})}selectAll(){for(const t of _classPrivateFieldGet(this,E))t.commit();_classPrivateMethodGet(this,H,et).call(this,_classPrivateFieldGet(this,y).values())}unselectAll(){if(_classPrivateFieldGet(this,b))_classPrivateFieldGet(this,b).commitOrRemove();else if(0!==_classPrivateMethodGet(this,H,et).size){for(const t of _classPrivateFieldGet(this,E))t.unselect();_classPrivateFieldGet(this,E).clear(),_classPrivateMethodGet(this,j,K).call(this,{hasSelectedEditor:!1})}}isActive(t){return _classPrivateFieldGet(this,b)===t}getActive(){return _classPrivateFieldGet(this,b)}getMode(){return _classPrivateFieldGet(this,k)}}function X(){_classPrivateFieldGet(this,L).addEventListener("keydown",_classPrivateFieldGet(this,T))}function Y(){_classPrivateFieldGet(this,L).removeEventListener("keydown",_classPrivateFieldGet(this,T))}function K(t){Object.entries(t).some((t=>{let[e,s]=t;return _classPrivateFieldGet(this,D)[e]!==s}))&&_classPrivateFieldGet(this,w).dispatch("annotationeditorstateschanged",{source:this,details:Object.assign(_classPrivateFieldGet(this,D),t)})}function J(t){_classPrivateFieldGet(this,w).dispatch("annotationeditorparamschanged",{source:this,details:t})}function Q(){if(!_classPrivateFieldGet(this,M)){_classPrivateFieldSet(this,M,!0);for(const t of _classPrivateFieldGet(this,A).values())t.enable()}}function Z(){if(this.unselectAll(),_classPrivateFieldGet(this,M)){_classPrivateFieldSet(this,M,!1);for(const t of _classPrivateFieldGet(this,A).values())t.disable()}}function $(t){const e=_classPrivateFieldGet(this,A).get(t.pageIndex);e?e.addOrRebuild(t):this.addEditor(t)}function tt(){if(0===_classPrivateFieldGet(this,y).size)return!0;if(1===_classPrivateFieldGet(this,y).size)for(const t of _classPrivateFieldGet(this,y).values())return t.isEmpty();return!1}function et(t){_classPrivateFieldGet(this,E).clear();for(const e of t)e.isEmpty()||(_classPrivateFieldGet(this,E).add(e),e.select());_classPrivateMethodGet(this,j,K).call(this,{hasSelectedEditor:!0})}_defineProperty(V,"_keyboardManager",new f([[["ctrl+a","mac+meta+a"],(i=V).prototype.selectAll],[["ctrl+c","mac+meta+c"],i.prototype.copy],[["ctrl+v","mac+meta+v"],i.prototype.paste],[["ctrl+x","mac+meta+x"],i.prototype.cut],[["ctrl+z","mac+meta+z"],i.prototype.undo],[["ctrl+y","ctrl+shift+Z","mac+meta+shift+Z"],i.prototype.redo],[["Backspace","alt+Backspace","ctrl+Backspace","shift+Backspace","mac+Backspace","mac+alt+Backspace","mac+ctrl+Backspace","Delete","ctrl+Delete","shift+Delete"],i.prototype.delete],[["Escape","mac+Escape"],i.prototype.unselectAll]])),e.AnnotationEditorUIManager=V},(t,e,s)=>{Object.defineProperty(e,"__esModule",{value:!0}),e.MurmurHash3_64=void 0;var i=s(1);const a=3285377520,n=4294901760,r=65535;e.MurmurHash3_64=class{constructor(t){this.h1=t?4294967295&t:a,this.h2=t?4294967295&t:a}update(t){let e,s;if("string"===typeof t){e=new Uint8Array(2*t.length),s=0;for(let i=0,a=t.length;i<a;i++){const a=t.charCodeAt(i);a<=255?e[s++]=a:(e[s++]=a>>>8,e[s++]=255&a)}}else{if(!(0,i.isArrayBuffer)(t))throw new Error("Wrong data format in MurmurHash3_64_update. Input must be a string or array.");e=t.slice(),s=e.byteLength}const a=s>>2,o=s-4*a,l=new Uint32Array(e.buffer,0,a);let c=0,d=0,h=this.h1,u=this.h2;const p=3432918353,f=461845907,m=11601,g=13715;for(let i=0;i<a;i++)1&i?(c=l[i],c=c*p&n|c*m&r,c=c<<15|c>>>17,c=c*f&n|c*g&r,h^=c,h=h<<13|h>>>19,h=5*h+3864292196):(d=l[i],d=d*p&n|d*m&r,d=d<<15|d>>>17,d=d*f&n|d*g&r,u^=d,u=u<<13|u>>>19,u=5*u+3864292196);switch(c=0,o){case 3:c^=e[4*a+2]<<16;case 2:c^=e[4*a+1]<<8;case 1:c^=e[4*a],c=c*p&n|c*m&r,c=c<<15|c>>>17,c=c*f&n|c*g&r,1&a?h^=c:u^=c}this.h1=h,this.h2=u}hexdigest(){let t=this.h1,e=this.h2;t^=e>>>1,t=3981806797*t&n|36045*t&r,e=4283543511*e&n|(2950163797*(e<<16|t>>>16)&n)>>>16,t^=e>>>1,t=444984403*t&n|60499*t&r,e=3301882366*e&n|(3120437893*(e<<16|t>>>16)&n)>>>16,t^=e>>>1;const s=(t>>>0).toString(16),i=(e>>>0).toString(16);return s.padStart(8,"0")+i.padStart(8,"0")}}},(t,e,s)=>{Object.defineProperty(e,"__esModule",{value:!0}),e.FontLoader=e.FontFaceObject=void 0;var i=s(1);class a{constructor(t){let{docId:e,onUnsupportedFeature:s,ownerDocument:n=globalThis.document,styleElement:r=null}=t;this.constructor===a&&(0,i.unreachable)("Cannot initialize BaseFontLoader."),this.docId=e,this._onUnsupportedFeature=s,this._document=n,this.nativeFontFaces=[],this.styleElement=null}addNativeFontFace(t){this.nativeFontFaces.push(t),this._document.fonts.add(t)}insertRule(t){let e=this.styleElement;e||(e=this.styleElement=this._document.createElement("style"),e.id="PDFJS_FONT_STYLE_TAG_".concat(this.docId),this._document.documentElement.getElementsByTagName("head")[0].append(e));const s=e.sheet;s.insertRule(t,s.cssRules.length)}clear(){for(const t of this.nativeFontFaces)this._document.fonts.delete(t);this.nativeFontFaces.length=0,this.styleElement&&(this.styleElement.remove(),this.styleElement=null)}async bind(t){if(t.attached||t.missingFile)return;if(t.attached=!0,this.isFontLoadingAPISupported){const e=t.createNativeFontFace();if(e){this.addNativeFontFace(e);try{await e.loaded}catch(s){throw this._onUnsupportedFeature({featureId:i.UNSUPPORTED_FEATURES.errorFontLoadNative}),(0,i.warn)("Failed to load font '".concat(e.family,"': '").concat(s,"'.")),t.disableFontFace=!0,s}}return}const e=t.createFontFaceRule();if(e){if(this.insertRule(e),this.isSyncFontLoadingSupported)return;await new Promise((s=>{const i=this._queueLoadingCallback(s);this._prepareFontLoadEvent([e],[t],i)}))}}_queueLoadingCallback(t){(0,i.unreachable)("Abstract method `_queueLoadingCallback`.")}get isFontLoadingAPISupported(){var t;const e=!(null===(t=this._document)||void 0===t||!t.fonts);return(0,i.shadow)(this,"isFontLoadingAPISupported",e)}get isSyncFontLoadingSupported(){(0,i.unreachable)("Abstract method `isSyncFontLoadingSupported`.")}get _loadTestFont(){(0,i.unreachable)("Abstract method `_loadTestFont`.")}_prepareFontLoadEvent(t,e,s){(0,i.unreachable)("Abstract method `_prepareFontLoadEvent`.")}}let n;e.FontLoader=n,e.FontLoader=n=class extends a{constructor(t){super(t),this.loadingContext={requests:[],nextRequestId:0},this.loadTestFontId=0}get isSyncFontLoadingSupported(){let t=!1;if("undefined"===typeof navigator)t=!0;else{const e=/Mozilla\/5.0.*?rv:(\d+).*? Gecko/.exec(navigator.userAgent);(null===e||void 0===e?void 0:e[1])>=14&&(t=!0)}return(0,i.shadow)(this,"isSyncFontLoadingSupported",t)}_queueLoadingCallback(t){const e=this.loadingContext,s={id:"pdfjs-font-loading-".concat(e.nextRequestId++),done:!1,complete:function(){for((0,i.assert)(!s.done,"completeRequest() cannot be called twice."),s.done=!0;e.requests.length>0&&e.requests[0].done;){const t=e.requests.shift();setTimeout(t.callback,0)}},callback:t};return e.requests.push(s),s}get _loadTestFont(){return(0,i.shadow)(this,"_loadTestFont",atob("T1RUTwALAIAAAwAwQ0ZGIDHtZg4AAAOYAAAAgUZGVE1lkzZwAAAEHAAAABxHREVGABQAFQAABDgAAAAeT1MvMlYNYwkAAAEgAAAAYGNtYXABDQLUAAACNAAAAUJoZWFk/xVFDQAAALwAAAA2aGhlYQdkA+oAAAD0AAAAJGhtdHgD6AAAAAAEWAAAAAZtYXhwAAJQAAAAARgAAAAGbmFtZVjmdH4AAAGAAAAAsXBvc3T/hgAzAAADeAAAACAAAQAAAAEAALZRFsRfDzz1AAsD6AAAAADOBOTLAAAAAM4KHDwAAAAAA+gDIQAAAAgAAgAAAAAAAAABAAADIQAAAFoD6AAAAAAD6AABAAAAAAAAAAAAAAAAAAAAAQAAUAAAAgAAAAQD6AH0AAUAAAKKArwAAACMAooCvAAAAeAAMQECAAACAAYJAAAAAAAAAAAAAQAAAAAAAAAAAAAAAFBmRWQAwAAuAC4DIP84AFoDIQAAAAAAAQAAAAAAAAAAACAAIAABAAAADgCuAAEAAAAAAAAAAQAAAAEAAAAAAAEAAQAAAAEAAAAAAAIAAQAAAAEAAAAAAAMAAQAAAAEAAAAAAAQAAQAAAAEAAAAAAAUAAQAAAAEAAAAAAAYAAQAAAAMAAQQJAAAAAgABAAMAAQQJAAEAAgABAAMAAQQJAAIAAgABAAMAAQQJAAMAAgABAAMAAQQJAAQAAgABAAMAAQQJAAUAAgABAAMAAQQJAAYAAgABWABYAAAAAAAAAwAAAAMAAAAcAAEAAAAAADwAAwABAAAAHAAEACAAAAAEAAQAAQAAAC7//wAAAC7////TAAEAAAAAAAABBgAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAMAAAAAAAD/gwAyAAAAAQAAAAAAAAAAAAAAAAAAAAABAAQEAAEBAQJYAAEBASH4DwD4GwHEAvgcA/gXBIwMAYuL+nz5tQXkD5j3CBLnEQACAQEBIVhYWFhYWFhYWFhYWFhYWFhYWFhYWFhYWFhYWFhYWFhYAAABAQAADwACAQEEE/t3Dov6fAH6fAT+fPp8+nwHDosMCvm1Cvm1DAz6fBQAAAAAAAABAAAAAMmJbzEAAAAAzgTjFQAAAADOBOQpAAEAAAAAAAAADAAUAAQAAAABAAAAAgABAAAAAAAAAAAD6AAAAAAAAA=="))}_prepareFontLoadEvent(t,e,s){function a(t,e){return t.charCodeAt(e)<<24|t.charCodeAt(e+1)<<16|t.charCodeAt(e+2)<<8|255&t.charCodeAt(e+3)}function n(t,e,s,i){return t.substring(0,e)+i+t.substring(e+s)}let r,o;const l=this._document.createElement("canvas");l.width=1,l.height=1;const c=l.getContext("2d");let d=0;const h="lt".concat(Date.now()).concat(this.loadTestFontId++);let u=this._loadTestFont;u=n(u,976,h.length,h);const p=1482184792;let f=a(u,16);for(r=0,o=h.length-3;r<o;r+=4)f=f-p+a(h,r)|0;r<h.length&&(f=f-p+a(h+"XXX",r)|0),u=n(u,16,4,(0,i.string32)(f));const m="url(data:font/opentype;base64,".concat(btoa(u),");"),g='@font-face {font-family:"'.concat(h,'";src:').concat(m,"}");this.insertRule(g);const v=[];for(const i of e)v.push(i.loadedName);v.push(h);const _=this._document.createElement("div");_.style.visibility="hidden",_.style.width=_.style.height="10px",_.style.position="absolute",_.style.top=_.style.left="0px";for(const i of v){const t=this._document.createElement("span");t.textContent="Hi",t.style.fontFamily=i,_.append(t)}this._document.body.append(_),function t(e,s){if(d++,d>30)return(0,i.warn)("Load test font never loaded."),void s();c.font="30px "+e,c.fillText(".",0,20),c.getImageData(0,0,1,1).data[3]>0?s():setTimeout(t.bind(null,e,s))}(h,(()=>{_.remove(),s.complete()}))}},e.FontFaceObject=class{constructor(t,e){let{isEvalSupported:s=!0,disableFontFace:i=!1,ignoreErrors:a=!1,onUnsupportedFeature:n,fontRegistry:r=null}=e;this.compiledGlyphs=Object.create(null);for(const o in t)this[o]=t[o];this.isEvalSupported=!1!==s,this.disableFontFace=!0===i,this.ignoreErrors=!0===a,this._onUnsupportedFeature=n,this.fontRegistry=r}createNativeFontFace(){if(!this.data||this.disableFontFace)return null;let t;if(this.cssFontInfo){const e={weight:this.cssFontInfo.fontWeight};this.cssFontInfo.italicAngle&&(e.style="oblique ".concat(this.cssFontInfo.italicAngle,"deg")),t=new FontFace(this.cssFontInfo.fontFamily,this.data,e)}else t=new FontFace(this.loadedName,this.data,{});return this.fontRegistry&&this.fontRegistry.registerFont(this),t}createFontFaceRule(){if(!this.data||this.disableFontFace)return null;const t=(0,i.bytesToString)(this.data),e="url(data:".concat(this.mimetype,";base64,").concat(btoa(t),");");let s;if(this.cssFontInfo){let t="font-weight: ".concat(this.cssFontInfo.fontWeight,";");this.cssFontInfo.italicAngle&&(t+="font-style: oblique ".concat(this.cssFontInfo.italicAngle,"deg;")),s='@font-face {font-family:"'.concat(this.cssFontInfo.fontFamily,'";').concat(t,"src:").concat(e,"}")}else s='@font-face {font-family:"'.concat(this.loadedName,'";src:').concat(e,"}");return this.fontRegistry&&this.fontRegistry.registerFont(this,e),s}getPathGenerator(t,e){if(void 0!==this.compiledGlyphs[e])return this.compiledGlyphs[e];let s;try{s=t.get(this.loadedName+"_path_"+e)}catch(a){if(!this.ignoreErrors)throw a;return this._onUnsupportedFeature({featureId:i.UNSUPPORTED_FEATURES.errorFontGetPath}),(0,i.warn)('getPathGenerator - ignoring character: "'.concat(a,'".')),this.compiledGlyphs[e]=function(t,e){}}if(this.isEvalSupported&&i.FeatureTest.isEvalSupported){const t=[];for(const e of s){const s=void 0!==e.args?e.args.join(","):"";t.push("c.",e.cmd,"(",s,");\n")}return this.compiledGlyphs[e]=new Function("c","size",t.join(""))}return this.compiledGlyphs[e]=function(t,e){for(const i of s)"scale"===i.cmd&&(i.args=[e,-e]),t[i.cmd].apply(t,i.args)}}}},(t,e,s)=>{Object.defineProperty(e,"__esModule",{value:!0}),e.CanvasGraphics=void 0;var i=s(1),a=s(4),n=s(13),r=s(14),o=s(3);const l=4096,c=16;function d(t){if(t._transformStack&&(t._transformStack=[]),!t.mozCurrentTransform){t._originalSave=t.save,t._originalRestore=t.restore,t._originalRotate=t.rotate,t._originalScale=t.scale,t._originalTranslate=t.translate,t._originalTransform=t.transform,t._originalSetTransform=t.setTransform,t._originalResetTransform=t.resetTransform,t._transformMatrix=t._transformMatrix||[1,0,0,1,0,0],t._transformStack=[];try{const e=Object.getOwnPropertyDescriptor(Object.getPrototypeOf(t),"lineWidth");t._setLineWidth=e.set,t._getLineWidth=e.get,Object.defineProperty(t,"lineWidth",{set:function(t){this._setLineWidth(1.000001*t)},get:function(){return this._getLineWidth()}})}catch(e){}Object.defineProperty(t,"mozCurrentTransform",{get:function(){return this._transformMatrix}}),Object.defineProperty(t,"mozCurrentTransformInverse",{get:function(){const[t,e,s,i,a,n]=this._transformMatrix,r=t*i-e*s,o=e*s-t*i;return[i/r,e/o,s/o,t/r,(i*a-s*n)/o,(e*a-t*n)/r]}}),t.save=function(){const t=this._transformMatrix;this._transformStack.push(t),this._transformMatrix=t.slice(0,6),this._originalSave()},t.restore=function(){0===this._transformStack.length&&(0,i.warn)("Tried to restore a ctx when the stack was already empty.");const t=this._transformStack.pop();t&&(this._transformMatrix=t,this._originalRestore())},t.translate=function(t,e){const s=this._transformMatrix;s[4]=s[0]*t+s[2]*e+s[4],s[5]=s[1]*t+s[3]*e+s[5],this._originalTranslate(t,e)},t.scale=function(t,e){const s=this._transformMatrix;s[0]*=t,s[1]*=t,s[2]*=e,s[3]*=e,this._originalScale(t,e)},t.transform=function(e,s,i,a,n,r){const o=this._transformMatrix;this._transformMatrix=[o[0]*e+o[2]*s,o[1]*e+o[3]*s,o[0]*i+o[2]*a,o[1]*i+o[3]*a,o[0]*n+o[2]*r+o[4],o[1]*n+o[3]*r+o[5]],t._originalTransform(e,s,i,a,n,r)},t.setTransform=function(e,s,i,a,n,r){this._transformMatrix=[e,s,i,a,n,r],t._originalSetTransform(e,s,i,a,n,r)},t.resetTransform=function(){this._transformMatrix=[1,0,0,1,0,0],t._originalResetTransform()},t.rotate=function(t){const e=Math.cos(t),s=Math.sin(t),i=this._transformMatrix;this._transformMatrix=[i[0]*e+i[2]*s,i[1]*e+i[3]*s,i[0]*-s+i[2]*e,i[1]*-s+i[3]*e,i[4],i[5]],this._originalRotate(t)}}}class h{constructor(t){this.canvasFactory=t,this.cache=Object.create(null)}getCanvas(t,e,s,i){let a;return void 0!==this.cache[t]?(a=this.cache[t],this.canvasFactory.reset(a,e,s),a.context.setTransform(1,0,0,1,0,0)):(a=this.canvasFactory.create(e,s),this.cache[t]=a),i&&d(a.context),a}delete(t){delete this.cache[t]}clear(){for(const t in this.cache){const e=this.cache[t];this.canvasFactory.destroy(e),delete this.cache[t]}}}function u(t,e,s,i,a,n,r,o,l,c){const[d,h,u,p,f,m]=t.mozCurrentTransform;if(0===h&&0===u){const g=r*d+f,v=Math.round(g),_=o*p+m,b=Math.round(_),y=(r+l)*d+f,A=Math.abs(Math.round(y)-v)||1,P=(o+c)*p+m,S=Math.abs(Math.round(P)-b)||1;return t.setTransform(Math.sign(d),0,0,Math.sign(p),v,b),t.drawImage(e,s,i,a,n,0,0,A,S),t.setTransform(d,h,u,p,f,m),[A,S]}if(0===d&&0===p){const g=o*u+f,v=Math.round(g),_=r*h+m,b=Math.round(_),y=(o+c)*u+f,A=Math.abs(Math.round(y)-v)||1,P=(r+l)*h+m,S=Math.abs(Math.round(P)-b)||1;return t.setTransform(0,Math.sign(h),Math.sign(u),0,v,b),t.drawImage(e,s,i,a,n,0,0,S,A),t.setTransform(d,h,u,p,f,m),[S,A]}return t.drawImage(e,s,i,a,n,r,o,l,c),[Math.hypot(d,h)*l,Math.hypot(u,p)*c]}class p{constructor(t,e){this.alphaIsShape=!1,this.fontSize=0,this.fontSizeScale=1,this.textMatrix=i.IDENTITY_MATRIX,this.textMatrixScale=1,this.fontMatrix=i.FONT_IDENTITY_MATRIX,this.leading=0,this.x=0,this.y=0,this.lineX=0,this.lineY=0,this.charSpacing=0,this.wordSpacing=0,this.textHScale=1,this.textRenderingMode=i.TextRenderingMode.FILL,this.textRise=0,this.fillColor="#000000",this.strokeColor="#000000",this.patternFill=!1,this.fillAlpha=1,this.strokeAlpha=1,this.lineWidth=1,this.activeSMask=null,this.transferMaps=null,this.startNewPathAndClipBox([0,0,t,e])}clone(){const t=Object.create(this);return t.clipBox=this.clipBox.slice(),t}setCurrentPoint(t,e){this.x=t,this.y=e}updatePathMinMax(t,e,s){[e,s]=i.Util.applyTransform([e,s],t),this.minX=Math.min(this.minX,e),this.minY=Math.min(this.minY,s),this.maxX=Math.max(this.maxX,e),this.maxY=Math.max(this.maxY,s)}updateRectMinMax(t,e){const s=i.Util.applyTransform(e,t),a=i.Util.applyTransform(e.slice(2),t);this.minX=Math.min(this.minX,s[0],a[0]),this.minY=Math.min(this.minY,s[1],a[1]),this.maxX=Math.max(this.maxX,s[0],a[0]),this.maxY=Math.max(this.maxY,s[1],a[1])}updateScalingPathMinMax(t,e){i.Util.scaleMinMax(t,e),this.minX=Math.min(this.minX,e[0]),this.maxX=Math.max(this.maxX,e[1]),this.minY=Math.min(this.minY,e[2]),this.maxY=Math.max(this.maxY,e[3])}updateCurvePathMinMax(t,e,s,a,n,r,o,l,c,d){const h=i.Util.bezierBoundingBox(e,s,a,n,r,o,l,c);if(d)return d[0]=Math.min(d[0],h[0],h[2]),d[1]=Math.max(d[1],h[0],h[2]),d[2]=Math.min(d[2],h[1],h[3]),void(d[3]=Math.max(d[3],h[1],h[3]));this.updateRectMinMax(t,h)}getPathBoundingBox(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:n.PathType.FILL,e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;const s=[this.minX,this.minY,this.maxX,this.maxY];if(t===n.PathType.STROKE){e||(0,i.unreachable)("Stroke bounding box must include transform.");const t=i.Util.singularValueDecompose2dScale(e),a=t[0]*this.lineWidth/2,n=t[1]*this.lineWidth/2;s[0]-=a,s[1]-=n,s[2]+=a,s[3]+=n}return s}updateClipFromPath(){const t=i.Util.intersect(this.clipBox,this.getPathBoundingBox());this.startNewPathAndClipBox(t||[0,0,0,0])}isEmptyClip(){return this.minX===1/0}startNewPathAndClipBox(t){this.clipBox=t,this.minX=1/0,this.minY=1/0,this.maxX=0,this.maxY=0}getClippedPathBoundingBox(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:n.PathType.FILL,e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;return i.Util.intersect(this.clipBox,this.getPathBoundingBox(t,e))}}function f(t,e){let s=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null;if("undefined"!==typeof ImageData&&e instanceof ImageData)return void t.putImageData(e,0,0);const a=e.height,n=e.width,r=a%c,o=(a-r)/c,l=0===r?o:o+1,d=t.createImageData(n,c);let h,u=0;const p=e.data,f=d.data;let m,g,v,_,b,y,A,P;if(s)switch(s.length){case 1:b=s[0],y=s[0],A=s[0],P=s[0];break;case 4:b=s[0],y=s[1],A=s[2],P=s[3]}if(e.kind===i.ImageKind.GRAYSCALE_1BPP){const e=p.byteLength,s=new Uint32Array(f.buffer,0,f.byteLength>>2),a=s.length,_=n+7>>3;let b=4294967295,y=i.FeatureTest.isLittleEndian?4278190080:255;for(P&&255===P[0]&&0===P[255]&&([b,y]=[y,b]),m=0;m<l;m++){for(v=m<o?c:r,h=0,g=0;g<v;g++){const t=e-u;let i=0;const a=t>_?n:8*t-7,r=-8&a;let o=0,l=0;for(;i<r;i+=8)l=p[u++],s[h++]=128&l?b:y,s[h++]=64&l?b:y,s[h++]=32&l?b:y,s[h++]=16&l?b:y,s[h++]=8&l?b:y,s[h++]=4&l?b:y,s[h++]=2&l?b:y,s[h++]=1&l?b:y;for(;i<a;i++)0===o&&(l=p[u++],o=128),s[h++]=l&o?b:y,o>>=1}for(;h<a;)s[h++]=0;t.putImageData(d,0,m*c)}}else if(e.kind===i.ImageKind.RGBA_32BPP){const e=!!(b||y||A);for(g=0,_=n*c*4,m=0;m<o;m++){if(f.set(p.subarray(u,u+_)),u+=_,e)for(let t=0;t<_;t+=4)b&&(f[t+0]=b[f[t+0]]),y&&(f[t+1]=y[f[t+1]]),A&&(f[t+2]=A[f[t+2]]);t.putImageData(d,0,g),g+=c}if(m<l){if(_=n*r*4,f.set(p.subarray(u,u+_)),e)for(let t=0;t<_;t+=4)b&&(f[t+0]=b[f[t+0]]),y&&(f[t+1]=y[f[t+1]]),A&&(f[t+2]=A[f[t+2]]);t.putImageData(d,0,g)}}else{if(e.kind!==i.ImageKind.RGB_24BPP)throw new Error("bad image kind: ".concat(e.kind));{const e=!!(b||y||A);for(v=c,_=n*v,m=0;m<l;m++){for(m>=o&&(v=r,_=n*v),h=0,g=_;g--;)f[h++]=p[u++],f[h++]=p[u++],f[h++]=p[u++],f[h++]=255;if(e)for(let t=0;t<h;t+=4)b&&(f[t+0]=b[f[t+0]]),y&&(f[t+1]=y[f[t+1]]),A&&(f[t+2]=A[f[t+2]]);t.putImageData(d,0,m*c)}}}}function m(t,e){if(e.bitmap)return void t.drawImage(e.bitmap,0,0);const s=e.height,i=e.width,a=s%c,n=(s-a)/c,o=0===a?n:n+1,l=t.createImageData(i,c);let d=0;const h=e.data,u=l.data;for(let p=0;p<o;p++){const e=p<n?c:a;({srcPos:d}=(0,r.applyMaskImageData)({src:h,srcPos:d,dest:u,width:i,height:e})),t.putImageData(l,0,p*c)}}function g(t,e){const s=["strokeStyle","fillStyle","fillRule","globalAlpha","lineWidth","lineCap","lineJoin","miterLimit","globalCompositeOperation","font"];for(let i=0,a=s.length;i<a;i++){const a=s[i];void 0!==t[a]&&(e[a]=t[a])}void 0!==t.setLineDash&&(e.setLineDash(t.getLineDash()),e.lineDashOffset=t.lineDashOffset)}function v(t,e){t.strokeStyle=t.fillStyle=e||"#000000",t.fillRule="nonzero",t.globalAlpha=1,t.lineWidth=1,t.lineCap="butt",t.lineJoin="miter",t.miterLimit=10,t.globalCompositeOperation="source-over",t.font="10px sans-serif",void 0!==t.setLineDash&&(t.setLineDash([]),t.lineDashOffset=0)}function _(t,e,s,i){const a=t.length;for(let n=3;n<a;n+=4){const a=t[n];if(0===a)t[n-3]=e,t[n-2]=s,t[n-1]=i;else if(a<255){const r=255-a;t[n-3]=t[n-3]*a+e*r>>8,t[n-2]=t[n-2]*a+s*r>>8,t[n-1]=t[n-1]*a+i*r>>8}}}function b(t,e,s){const i=t.length;for(let a=3;a<i;a+=4){const i=s?s[t[a]]:t[a];e[a]=e[a]*i*.00392156862745098|0}}function y(t,e,s){const i=t.length;for(let a=3;a<i;a+=4){const i=77*t[a-3]+152*t[a-2]+28*t[a-1];e[a]=s?e[a]*s[i>>8]>>8:e[a]*i>>16}}function A(t,e,s,i){const a=i[0],n=i[1],r=i[2]-a,o=i[3]-n;0!==r&&0!==o&&(function(t,e,s,i,a,n,r,o,l,c,d){const h=!!n,u=h?n[0]:0,p=h?n[1]:0,f=h?n[2]:0;let m;m="Luminosity"===a?y:b;const g=Math.min(i,Math.ceil(1048576/s));for(let v=0;v<i;v+=g){const a=Math.min(g,i-v),n=t.getImageData(o-c,v+(l-d),s,a),b=e.getImageData(o,v+l,s,a);h&&_(n.data,u,p,f),m(n.data,b.data,r),e.putImageData(b,o,v+l)}}(e.context,s,r,o,e.subtype,e.backdrop,e.transferMap,a,n,e.offsetX,e.offsetY),t.save(),t.globalAlpha=1,t.globalCompositeOperation="source-over",t.setTransform(1,0,0,1,0,0),t.drawImage(s.canvas,0,0),t.restore())}function P(t,e){const s=i.Util.singularValueDecompose2dScale(t);s[0]=Math.fround(s[0]),s[1]=Math.fround(s[1]);const n=Math.fround((globalThis.devicePixelRatio||1)*a.PixelsPerInch.PDF_TO_CSS_UNITS);return void 0!==e?e:s[0]<=n||s[1]<=n}const S=["butt","round","square"],x=["miter","round","bevel"],F={},w={};var C=new WeakSet;class M{constructor(t,e,s,i,a,n,r,o){_classPrivateMethodInitSpec(this,C),this.ctx=t,this.current=new p(this.ctx.canvas.width,this.ctx.canvas.height),this.stateStack=[],this.pendingClip=null,this.pendingEOFill=!1,this.res=null,this.xobjs=null,this.commonObjs=e,this.objs=s,this.canvasFactory=i,this.imageLayer=a,this.groupStack=[],this.processingType3=null,this.baseTransform=null,this.baseTransformStack=[],this.groupLevel=0,this.smaskStack=[],this.smaskCounter=0,this.tempSMask=null,this.suspendedCtx=null,this.contentVisible=!0,this.markedContentStack=[],this.optionalContentConfig=n,this.cachedCanvases=new h(this.canvasFactory),this.cachedPatterns=new Map,this.annotationCanvasMap=r,this.viewportScale=1,this.outputScaleX=1,this.outputScaleY=1,this.backgroundColor=(null===o||void 0===o?void 0:o.background)||null,this.foregroundColor=(null===o||void 0===o?void 0:o.foreground)||null,t&&d(t),this._cachedScaleForStroking=null,this._cachedGetSinglePixelWidth=null,this._cachedBitmapsMap=new Map}getObject(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;return"string"===typeof t?t.startsWith("g_")?this.commonObjs.get(t):this.objs.get(t):e}beginDrawing(t){let{transform:e,viewport:s,transparency:i=!1,background:n=null}=t;const r=this.ctx.canvas.width,o=this.ctx.canvas.height,l=n||"#ffffff";if(this.ctx.save(),this.foregroundColor&&this.backgroundColor){this.ctx.fillStyle=this.foregroundColor;const t=this.foregroundColor=this.ctx.fillStyle;this.ctx.fillStyle=this.backgroundColor;const e=this.backgroundColor=this.ctx.fillStyle;let s=!0,i=l;if(this.ctx.fillStyle=l,i=this.ctx.fillStyle,s="string"===typeof i&&/^#[0-9A-Fa-f]{6}$/.test(i),"#000000"===t&&"#ffffff"===e||t===e||!s)this.foregroundColor=this.backgroundColor=null;else{const[s,n,r]=(0,a.getRGB)(i),o=t=>(t/=255)<=.03928?t/12.92:((t+.055)/1.055)**2.4,l=Math.round(.2126*o(s)+.7152*o(n)+.0722*o(r));this.selectColor=(s,i,a)=>{const n=.2126*o(s)+.7152*o(i)+.0722*o(a);return Math.round(n)===l?e:t}}}if(this.ctx.fillStyle=this.backgroundColor||l,this.ctx.fillRect(0,0,r,o),this.ctx.restore(),i){const t=this.cachedCanvases.getCanvas("transparent",r,o,!0);this.compositeCtx=this.ctx,this.transparentCanvas=t.canvas,this.ctx=t.context,this.ctx.save(),this.ctx.transform.apply(this.ctx,this.compositeCtx.mozCurrentTransform)}this.ctx.save(),v(this.ctx,this.foregroundColor),e&&(this.ctx.transform.apply(this.ctx,e),this.outputScaleX=e[0],this.outputScaleY=e[0]),this.ctx.transform.apply(this.ctx,s.transform),this.viewportScale=s.scale,this.baseTransform=this.ctx.mozCurrentTransform.slice(),this.imageLayer&&this.imageLayer.beginLayout()}executeOperatorList(t,e,s,a){const n=t.argsArray,r=t.fnArray;let o=e||0;const l=n.length;if(l===o)return o;const c=l-o>10&&"function"===typeof s,d=c?Date.now()+15:0;let h=0;const u=this.commonObjs,p=this.objs;let f;for(;;){if(void 0!==a&&o===a.nextBreakPoint)return a.breakIt(o,s),o;if(f=r[o],f!==i.OPS.dependency)this[f].apply(this,n[o]);else for(const t of n[o]){const e=t.startsWith("g_")?u:p;if(!e.has(t))return e.get(t,s),o}if(o++,o===l)return o;if(c&&++h>10){if(Date.now()>d)return s(),o;h=0}}}endDrawing(){_classPrivateMethodGet(this,C,k).call(this),this.cachedCanvases.clear(),this.cachedPatterns.clear();for(const t of this._cachedBitmapsMap.values()){for(const e of t.values())"undefined"!==typeof HTMLCanvasElement&&e instanceof HTMLCanvasElement&&(e.width=e.height=0);t.clear()}this._cachedBitmapsMap.clear(),this.imageLayer&&this.imageLayer.endLayout()}_scaleImage(t,e){const s=t.width,i=t.height;let a,n,r=Math.max(Math.hypot(e[0],e[1]),1),o=Math.max(Math.hypot(e[2],e[3]),1),l=s,c=i,d="prescale1";for(;r>2&&l>1||o>2&&c>1;){let e=l,s=c;r>2&&l>1&&(e=Math.ceil(l/2),r/=l/e),o>2&&c>1&&(s=Math.ceil(c/2),o/=c/s),a=this.cachedCanvases.getCanvas(d,e,s,!1),n=a.context,n.clearRect(0,0,e,s),n.drawImage(t,0,0,l,c,0,0,e,s),t=a.canvas,l=e,c=s,d="prescale1"===d?"prescale2":"prescale1"}return{img:t,paintWidth:l,paintHeight:c}}_createMaskCanvas(t){const e=this.ctx,{width:s,height:a}=t,r=this.current.fillColor,o=this.current.patternFill,l=e.mozCurrentTransform;let c,d,h,p;if((t.bitmap||t.data)&&t.count>1){const e=t.bitmap||t.data.buffer,s=l.slice(0,4);d=JSON.stringify(o?s:[s,r]),c=this._cachedBitmapsMap.get(e),c||(c=new Map,this._cachedBitmapsMap.set(e,c));const i=c.get(d);if(i&&!o)return{canvas:i,offsetX:Math.round(Math.min(l[0],l[2])+l[4]),offsetY:Math.round(Math.min(l[1],l[3])+l[5])};h=i}h||(p=this.cachedCanvases.getCanvas("maskCanvas",s,a,!1),m(p.context,t));let f=i.Util.transform(l,[1/s,0,0,-1/a,0,0]);f=i.Util.transform(f,[1,0,0,1,0,-a]);const g=i.Util.applyTransform([0,0],f),v=i.Util.applyTransform([s,a],f),_=i.Util.normalizeRect([g[0],g[1],v[0],v[1]]),b=Math.round(_[2]-_[0])||1,y=Math.round(_[3]-_[1])||1,A=this.cachedCanvases.getCanvas("fillCanvas",b,y,!0),S=A.context,x=Math.min(g[0],v[0]),F=Math.min(g[1],v[1]);S.translate(-x,-F),S.transform.apply(S,f),h||(h=this._scaleImage(p.canvas,S.mozCurrentTransformInverse),h=h.img,c&&o&&c.set(d,h)),S.imageSmoothingEnabled=P(S.mozCurrentTransform,t.interpolate),u(S,h,0,0,h.width,h.height,0,0,s,a),S.globalCompositeOperation="source-in";const w=i.Util.transform(S.mozCurrentTransformInverse,[1,0,0,1,-x,-F]);return S.fillStyle=o?r.getPattern(e,this,w,n.PathType.FILL):r,S.fillRect(0,0,s,a),c&&!o&&(this.cachedCanvases.delete("fillCanvas"),c.set(d,A.canvas)),{canvas:A.canvas,offsetX:Math.round(x),offsetY:Math.round(F)}}setLineWidth(t){t!==this.current.lineWidth&&(this._cachedScaleForStroking=null),this.current.lineWidth=t,this.ctx.lineWidth=t}setLineCap(t){this.ctx.lineCap=S[t]}setLineJoin(t){this.ctx.lineJoin=x[t]}setMiterLimit(t){this.ctx.miterLimit=t}setDash(t,e){const s=this.ctx;void 0!==s.setLineDash&&(s.setLineDash(t),s.lineDashOffset=e)}setRenderingIntent(t){}setFlatness(t){}setGState(t){for(let e=0,s=t.length;e<s;e++){const s=t[e],i=s[0],a=s[1];switch(i){case"LW":this.setLineWidth(a);break;case"LC":this.setLineCap(a);break;case"LJ":this.setLineJoin(a);break;case"ML":this.setMiterLimit(a);break;case"D":this.setDash(a[0],a[1]);break;case"RI":this.setRenderingIntent(a);break;case"FL":this.setFlatness(a);break;case"Font":this.setFont(a[0],a[1]);break;case"CA":this.current.strokeAlpha=s[1];break;case"ca":this.current.fillAlpha=s[1],this.ctx.globalAlpha=s[1];break;case"BM":this.ctx.globalCompositeOperation=a;break;case"SMask":this.current.activeSMask=a?this.tempSMask:null,this.tempSMask=null,this.checkSMaskState();break;case"TR":this.current.transferMaps=a}}}get inSMaskMode(){return!!this.suspendedCtx}checkSMaskState(){const t=this.inSMaskMode;this.current.activeSMask&&!t?this.beginSMaskMode():!this.current.activeSMask&&t&&this.endSMaskMode()}beginSMaskMode(){if(this.inSMaskMode)throw new Error("beginSMaskMode called while already in smask mode");const t=this.ctx.canvas.width,e=this.ctx.canvas.height,s="smaskGroupAt"+this.groupLevel,i=this.cachedCanvases.getCanvas(s,t,e,!0);this.suspendedCtx=this.ctx,this.ctx=i.context;const a=this.ctx;a.setTransform.apply(a,this.suspendedCtx.mozCurrentTransform),g(this.suspendedCtx,a),function(t,e){if(t._removeMirroring)throw new Error("Context is already forwarding operations.");t.__originalSave=t.save,t.__originalRestore=t.restore,t.__originalRotate=t.rotate,t.__originalScale=t.scale,t.__originalTranslate=t.translate,t.__originalTransform=t.transform,t.__originalSetTransform=t.setTransform,t.__originalResetTransform=t.resetTransform,t.__originalClip=t.clip,t.__originalMoveTo=t.moveTo,t.__originalLineTo=t.lineTo,t.__originalBezierCurveTo=t.bezierCurveTo,t.__originalRect=t.rect,t.__originalClosePath=t.closePath,t.__originalBeginPath=t.beginPath,t._removeMirroring=()=>{t.save=t.__originalSave,t.restore=t.__originalRestore,t.rotate=t.__originalRotate,t.scale=t.__originalScale,t.translate=t.__originalTranslate,t.transform=t.__originalTransform,t.setTransform=t.__originalSetTransform,t.resetTransform=t.__originalResetTransform,t.clip=t.__originalClip,t.moveTo=t.__originalMoveTo,t.lineTo=t.__originalLineTo,t.bezierCurveTo=t.__originalBezierCurveTo,t.rect=t.__originalRect,t.closePath=t.__originalClosePath,t.beginPath=t.__originalBeginPath,delete t._removeMirroring},t.save=function(){e.save(),this.__originalSave()},t.restore=function(){e.restore(),this.__originalRestore()},t.translate=function(t,s){e.translate(t,s),this.__originalTranslate(t,s)},t.scale=function(t,s){e.scale(t,s),this.__originalScale(t,s)},t.transform=function(t,s,i,a,n,r){e.transform(t,s,i,a,n,r),this.__originalTransform(t,s,i,a,n,r)},t.setTransform=function(t,s,i,a,n,r){e.setTransform(t,s,i,a,n,r),this.__originalSetTransform(t,s,i,a,n,r)},t.resetTransform=function(){e.resetTransform(),this.__originalResetTransform()},t.rotate=function(t){e.rotate(t),this.__originalRotate(t)},t.clip=function(t){e.clip(t),this.__originalClip(t)},t.moveTo=function(t,s){e.moveTo(t,s),this.__originalMoveTo(t,s)},t.lineTo=function(t,s){e.lineTo(t,s),this.__originalLineTo(t,s)},t.bezierCurveTo=function(t,s,i,a,n,r){e.bezierCurveTo(t,s,i,a,n,r),this.__originalBezierCurveTo(t,s,i,a,n,r)},t.rect=function(t,s,i,a){e.rect(t,s,i,a),this.__originalRect(t,s,i,a)},t.closePath=function(){e.closePath(),this.__originalClosePath()},t.beginPath=function(){e.beginPath(),this.__originalBeginPath()}}(a,this.suspendedCtx),this.setGState([["BM","source-over"],["ca",1],["CA",1]])}endSMaskMode(){if(!this.inSMaskMode)throw new Error("endSMaskMode called while not in smask mode");this.ctx._removeMirroring(),g(this.ctx,this.suspendedCtx),this.ctx=this.suspendedCtx,this.suspendedCtx=null}compose(t){if(!this.current.activeSMask)return;t?(t[0]=Math.floor(t[0]),t[1]=Math.floor(t[1]),t[2]=Math.ceil(t[2]),t[3]=Math.ceil(t[3])):t=[0,0,this.ctx.canvas.width,this.ctx.canvas.height];const e=this.current.activeSMask;A(this.suspendedCtx,e,this.ctx,t),this.ctx.save(),this.ctx.setTransform(1,0,0,1,0,0),this.ctx.clearRect(0,0,this.ctx.canvas.width,this.ctx.canvas.height),this.ctx.restore()}save(){this.inSMaskMode?(g(this.ctx,this.suspendedCtx),this.suspendedCtx.save()):this.ctx.save();const t=this.current;this.stateStack.push(t),this.current=t.clone()}restore(){0===this.stateStack.length&&this.inSMaskMode&&this.endSMaskMode(),0!==this.stateStack.length&&(this.current=this.stateStack.pop(),this.inSMaskMode?(this.suspendedCtx.restore(),g(this.suspendedCtx,this.ctx)):this.ctx.restore(),this.checkSMaskState(),this.pendingClip=null,this._cachedScaleForStroking=null,this._cachedGetSinglePixelWidth=null)}transform(t,e,s,i,a,n){this.ctx.transform(t,e,s,i,a,n),this._cachedScaleForStroking=null,this._cachedGetSinglePixelWidth=null}constructPath(t,e,s){const a=this.ctx,n=this.current;let r,o,l=n.x,c=n.y;const d=a.mozCurrentTransform,h=0===d[0]&&0===d[3]||0===d[1]&&0===d[2],u=h?s.slice(0):null;for(let p=0,f=0,m=t.length;p<m;p++)switch(0|t[p]){case i.OPS.rectangle:l=e[f++],c=e[f++];const t=e[f++],s=e[f++],p=l+t,m=c+s;a.moveTo(l,c),0===t||0===s?a.lineTo(p,m):(a.lineTo(p,c),a.lineTo(p,m),a.lineTo(l,m)),h||n.updateRectMinMax(d,[l,c,p,m]),a.closePath();break;case i.OPS.moveTo:l=e[f++],c=e[f++],a.moveTo(l,c),h||n.updatePathMinMax(d,l,c);break;case i.OPS.lineTo:l=e[f++],c=e[f++],a.lineTo(l,c),h||n.updatePathMinMax(d,l,c);break;case i.OPS.curveTo:r=l,o=c,l=e[f+4],c=e[f+5],a.bezierCurveTo(e[f],e[f+1],e[f+2],e[f+3],l,c),n.updateCurvePathMinMax(d,r,o,e[f],e[f+1],e[f+2],e[f+3],l,c,u),f+=6;break;case i.OPS.curveTo2:r=l,o=c,a.bezierCurveTo(l,c,e[f],e[f+1],e[f+2],e[f+3]),n.updateCurvePathMinMax(d,r,o,l,c,e[f],e[f+1],e[f+2],e[f+3],u),l=e[f+2],c=e[f+3],f+=4;break;case i.OPS.curveTo3:r=l,o=c,l=e[f+2],c=e[f+3],a.bezierCurveTo(e[f],e[f+1],l,c,l,c),n.updateCurvePathMinMax(d,r,o,e[f],e[f+1],l,c,l,c,u),f+=4;break;case i.OPS.closePath:a.closePath()}h&&n.updateScalingPathMinMax(d,u),n.setCurrentPoint(l,c)}closePath(){this.ctx.closePath()}stroke(t){t="undefined"===typeof t||t;const e=this.ctx,s=this.current.strokeColor;e.globalAlpha=this.current.strokeAlpha,this.contentVisible&&("object"===typeof s&&null!==s&&void 0!==s&&s.getPattern?(e.save(),e.strokeStyle=s.getPattern(e,this,e.mozCurrentTransformInverse,n.PathType.STROKE),this.rescaleAndStroke(!1),e.restore()):this.rescaleAndStroke(!0)),t&&this.consumePath(this.current.getClippedPathBoundingBox()),e.globalAlpha=this.current.fillAlpha}closeStroke(){this.closePath(),this.stroke()}fill(t){t="undefined"===typeof t||t;const e=this.ctx,s=this.current.fillColor;let i=!1;this.current.patternFill&&(e.save(),e.fillStyle=s.getPattern(e,this,e.mozCurrentTransformInverse,n.PathType.FILL),i=!0);const a=this.current.getClippedPathBoundingBox();this.contentVisible&&null!==a&&(this.pendingEOFill?(e.fill("evenodd"),this.pendingEOFill=!1):e.fill()),i&&e.restore(),t&&this.consumePath(a)}eoFill(){this.pendingEOFill=!0,this.fill()}fillStroke(){this.fill(!1),this.stroke(!1),this.consumePath()}eoFillStroke(){this.pendingEOFill=!0,this.fillStroke()}closeFillStroke(){this.closePath(),this.fillStroke()}closeEOFillStroke(){this.pendingEOFill=!0,this.closePath(),this.fillStroke()}endPath(){this.consumePath()}clip(){this.pendingClip=F}eoClip(){this.pendingClip=w}beginText(){this.current.textMatrix=i.IDENTITY_MATRIX,this.current.textMatrixScale=1,this.current.x=this.current.lineX=0,this.current.y=this.current.lineY=0}endText(){const t=this.pendingTextPaths,e=this.ctx;if(void 0!==t){e.save(),e.beginPath();for(const s of t)e.setTransform.apply(e,s.transform),e.translate(s.x,s.y),s.addToPath(e,s.fontSize);e.restore(),e.clip(),e.beginPath(),delete this.pendingTextPaths}else e.beginPath()}setCharSpacing(t){this.current.charSpacing=t}setWordSpacing(t){this.current.wordSpacing=t}setHScale(t){this.current.textHScale=t/100}setLeading(t){this.current.leading=-t}setFont(t,e){const s=this.commonObjs.get(t),a=this.current;if(!s)throw new Error("Can't find font for ".concat(t));if(a.fontMatrix=s.fontMatrix||i.FONT_IDENTITY_MATRIX,0!==a.fontMatrix[0]&&0!==a.fontMatrix[3]||(0,i.warn)("Invalid font matrix for font "+t),e<0?(e=-e,a.fontDirection=-1):a.fontDirection=1,this.current.font=s,this.current.fontSize=e,s.isType3Font)return;const n=s.loadedName||"sans-serif";let r="normal";s.black?r="900":s.bold&&(r="bold");const o=s.italic?"italic":"normal",l='"'.concat(n,'", ').concat(s.fallbackName);let c=e;e<16?c=16:e>100&&(c=100),this.current.fontSizeScale=e/c,this.ctx.font="".concat(o," ").concat(r," ").concat(c,"px ").concat(l)}setTextRenderingMode(t){this.current.textRenderingMode=t}setTextRise(t){this.current.textRise=t}moveText(t,e){this.current.x=this.current.lineX+=t,this.current.y=this.current.lineY+=e}setLeadingMoveText(t,e){this.setLeading(-e),this.moveText(t,e)}setTextMatrix(t,e,s,i,a,n){this.current.textMatrix=[t,e,s,i,a,n],this.current.textMatrixScale=Math.hypot(t,e),this.current.x=this.current.lineX=0,this.current.y=this.current.lineY=0}nextLine(){this.moveText(0,this.current.leading)}paintChar(t,e,s,a){const n=this.ctx,r=this.current,o=r.font,l=r.textRenderingMode,c=r.fontSize/r.fontSizeScale,d=l&i.TextRenderingMode.FILL_STROKE_MASK,h=!!(l&i.TextRenderingMode.ADD_TO_PATH_FLAG),u=r.patternFill&&!o.missingFile;let p;(o.disableFontFace||h||u)&&(p=o.getPathGenerator(this.commonObjs,t)),o.disableFontFace||u?(n.save(),n.translate(e,s),n.beginPath(),p(n,c),a&&n.setTransform.apply(n,a),d!==i.TextRenderingMode.FILL&&d!==i.TextRenderingMode.FILL_STROKE||n.fill(),d!==i.TextRenderingMode.STROKE&&d!==i.TextRenderingMode.FILL_STROKE||n.stroke(),n.restore()):(d!==i.TextRenderingMode.FILL&&d!==i.TextRenderingMode.FILL_STROKE||n.fillText(t,e,s),d!==i.TextRenderingMode.STROKE&&d!==i.TextRenderingMode.FILL_STROKE||n.strokeText(t,e,s)),h&&(this.pendingTextPaths||(this.pendingTextPaths=[])).push({transform:n.mozCurrentTransform,x:e,y:s,fontSize:c,addToPath:p})}get isFontSubpixelAAEnabled(){const{context:t}=this.cachedCanvases.getCanvas("isFontSubpixelAAEnabled",10,10,!1);t.scale(1.5,1),t.fillText("I",0,10);const e=t.getImageData(0,0,10,10).data;let s=!1;for(let i=3;i<e.length;i+=4)if(e[i]>0&&e[i]<255){s=!0;break}return(0,i.shadow)(this,"isFontSubpixelAAEnabled",s)}showText(t){const e=this.current,s=e.font;if(s.isType3Font)return this.showType3Text(t);const a=e.fontSize;if(0===a)return;const r=this.ctx,o=e.fontSizeScale,l=e.charSpacing,c=e.wordSpacing,d=e.fontDirection,h=e.textHScale*d,u=t.length,p=s.vertical,f=p?1:-1,m=s.defaultVMetrics,g=a*e.fontMatrix[0],v=e.textRenderingMode===i.TextRenderingMode.FILL&&!s.disableFontFace&&!e.patternFill;let _;if(r.save(),r.transform.apply(r,e.textMatrix),r.translate(e.x,e.y+e.textRise),d>0?r.scale(h,-1):r.scale(h,1),e.patternFill){r.save();const t=e.fillColor.getPattern(r,this,r.mozCurrentTransformInverse,n.PathType.FILL);_=r.mozCurrentTransform,r.restore(),r.fillStyle=t}let b=e.lineWidth;const y=e.textMatrixScale;if(0===y||0===b){const t=e.textRenderingMode&i.TextRenderingMode.FILL_STROKE_MASK;t!==i.TextRenderingMode.STROKE&&t!==i.TextRenderingMode.FILL_STROKE||(b=this.getSinglePixelWidth())}else b/=y;1!==o&&(r.scale(o,o),b/=o),r.lineWidth=b;let A,P=0;for(A=0;A<u;++A){const e=t[A];if("number"===typeof e){P+=f*e*a/1e3;continue}let i=!1;const n=(e.isSpace?c:0)+l,h=e.fontChar,u=e.accent;let b,y,S,x=e.width;if(p){const t=e.vmetric||m,s=-(e.vmetric?t[1]:.5*x)*g,i=t[2]*g;x=t?-t[0]:x,b=s/o,y=(P+i)/o}else b=P/o,y=0;if(s.remeasure&&x>0){const t=1e3*r.measureText(h).width/a*o;if(x<t&&this.isFontSubpixelAAEnabled){const e=x/t;i=!0,r.save(),r.scale(e,1),b/=e}else x!==t&&(b+=(x-t)/2e3*a/o)}if(this.contentVisible&&(e.isInFont||s.missingFile))if(v&&!u)r.fillText(h,b,y);else if(this.paintChar(h,b,y,_),u){const t=b+a*u.offset.x/o,e=y-a*u.offset.y/o;this.paintChar(u.fontChar,t,e,_)}S=p?x*g-n*d:x*g+n*d,P+=S,i&&r.restore()}p?e.y-=P:e.x+=P*h,r.restore(),this.compose()}showType3Text(t){const e=this.ctx,s=this.current,a=s.font,n=s.fontSize,r=s.fontDirection,o=a.vertical?1:-1,l=s.charSpacing,c=s.wordSpacing,d=s.textHScale*r,h=s.fontMatrix||i.FONT_IDENTITY_MATRIX,u=t.length;let p,f,m,g;if(s.textRenderingMode!==i.TextRenderingMode.INVISIBLE&&0!==n){for(this._cachedScaleForStroking=null,this._cachedGetSinglePixelWidth=null,e.save(),e.transform.apply(e,s.textMatrix),e.translate(s.x,s.y),e.scale(d,r),p=0;p<u;++p){if(f=t[p],"number"===typeof f){g=o*f*n/1e3,this.ctx.translate(g,0),s.x+=g*d;continue}const r=(f.isSpace?c:0)+l,u=a.charProcOperatorList[f.operatorListId];u?(this.contentVisible&&(this.processingType3=f,this.save(),e.scale(n,n),e.transform.apply(e,h),this.executeOperatorList(u),this.restore()),m=i.Util.applyTransform([f.width,0],h)[0]*n+r,e.translate(m,0),s.x+=m*d):(0,i.warn)('Type3 character "'.concat(f.operatorListId,'" is not available.'))}e.restore(),this.processingType3=null}}setCharWidth(t,e){}setCharWidthAndBounds(t,e,s,i,a,n){this.ctx.rect(s,i,a-s,n-i),this.ctx.clip(),this.endPath()}getColorN_Pattern(t){let e;if("TilingPattern"===t[0]){const s=t[1],i=this.baseTransform||this.ctx.mozCurrentTransform.slice(),a={createCanvasGraphics:t=>new M(t,this.commonObjs,this.objs,this.canvasFactory)};e=new n.TilingPattern(t,s,this.ctx,a,i)}else e=this._getPattern(t[1],t[2]);return e}setStrokeColorN(){this.current.strokeColor=this.getColorN_Pattern(arguments)}setFillColorN(){this.current.fillColor=this.getColorN_Pattern(arguments),this.current.patternFill=!0}setStrokeRGBColor(t,e,s){var a;const n=(null===(a=this.selectColor)||void 0===a?void 0:a.call(this,t,e,s))||i.Util.makeHexColor(t,e,s);this.ctx.strokeStyle=n,this.current.strokeColor=n}setFillRGBColor(t,e,s){var a;const n=(null===(a=this.selectColor)||void 0===a?void 0:a.call(this,t,e,s))||i.Util.makeHexColor(t,e,s);this.ctx.fillStyle=n,this.current.fillColor=n,this.current.patternFill=!1}_getPattern(t){let e,s=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;return this.cachedPatterns.has(t)?e=this.cachedPatterns.get(t):(e=(0,n.getShadingPattern)(this.objs.get(t)),this.cachedPatterns.set(t,e)),s&&(e.matrix=s),e}shadingFill(t){if(!this.contentVisible)return;const e=this.ctx;this.save();const s=this._getPattern(t);e.fillStyle=s.getPattern(e,this,e.mozCurrentTransformInverse,n.PathType.SHADING);const a=e.mozCurrentTransformInverse;if(a){const t=e.canvas,s=t.width,n=t.height,r=i.Util.applyTransform([0,0],a),o=i.Util.applyTransform([0,n],a),l=i.Util.applyTransform([s,0],a),c=i.Util.applyTransform([s,n],a),d=Math.min(r[0],o[0],l[0],c[0]),h=Math.min(r[1],o[1],l[1],c[1]),u=Math.max(r[0],o[0],l[0],c[0]),p=Math.max(r[1],o[1],l[1],c[1]);this.ctx.fillRect(d,h,u-d,p-h)}else this.ctx.fillRect(-1e10,-1e10,2e10,2e10);this.compose(this.current.getClippedPathBoundingBox()),this.restore()}beginInlineImage(){(0,i.unreachable)("Should not call beginInlineImage")}beginImageData(){(0,i.unreachable)("Should not call beginImageData")}paintFormXObjectBegin(t,e){if(this.contentVisible&&(this.save(),this.baseTransformStack.push(this.baseTransform),Array.isArray(t)&&6===t.length&&this.transform.apply(this,t),this.baseTransform=this.ctx.mozCurrentTransform,e)){const t=e[2]-e[0],s=e[3]-e[1];this.ctx.rect(e[0],e[1],t,s),this.current.updateRectMinMax(this.ctx.mozCurrentTransform,e),this.clip(),this.endPath()}}paintFormXObjectEnd(){this.contentVisible&&(this.restore(),this.baseTransform=this.baseTransformStack.pop())}beginGroup(t){if(!this.contentVisible)return;this.save(),this.inSMaskMode&&(this.endSMaskMode(),this.current.activeSMask=null);const e=this.ctx;t.isolated||(0,i.info)("TODO: Support non-isolated groups."),t.knockout&&(0,i.warn)("Knockout groups not supported.");const s=e.mozCurrentTransform;if(t.matrix&&e.transform.apply(e,t.matrix),!t.bbox)throw new Error("Bounding box is required.");let a=i.Util.getAxialAlignedBoundingBox(t.bbox,e.mozCurrentTransform);const n=[0,0,e.canvas.width,e.canvas.height];a=i.Util.intersect(a,n)||[0,0,0,0];const r=Math.floor(a[0]),o=Math.floor(a[1]);let c=Math.max(Math.ceil(a[2])-r,1),d=Math.max(Math.ceil(a[3])-o,1),h=1,u=1;c>l&&(h=c/l,c=l),d>l&&(u=d/l,d=l),this.current.startNewPathAndClipBox([0,0,c,d]);let p="groupAt"+this.groupLevel;t.smask&&(p+="_smask_"+this.smaskCounter++%2);const f=this.cachedCanvases.getCanvas(p,c,d,!0),m=f.context;m.scale(1/h,1/u),m.translate(-r,-o),m.transform.apply(m,s),t.smask?this.smaskStack.push({canvas:f.canvas,context:m,offsetX:r,offsetY:o,scaleX:h,scaleY:u,subtype:t.smask.subtype,backdrop:t.smask.backdrop,transferMap:t.smask.transferMap||null,startTransformInverse:null}):(e.setTransform(1,0,0,1,0,0),e.translate(r,o),e.scale(h,u),e.save()),g(e,m),this.ctx=m,this.setGState([["BM","source-over"],["ca",1],["CA",1]]),this.groupStack.push(e),this.groupLevel++}endGroup(t){if(!this.contentVisible)return;this.groupLevel--;const e=this.ctx,s=this.groupStack.pop();if(this.ctx=s,this.ctx.imageSmoothingEnabled=!1,t.smask)this.tempSMask=this.smaskStack.pop(),this.restore();else{this.ctx.restore();const t=this.ctx.mozCurrentTransform;this.restore(),this.ctx.save(),this.ctx.setTransform.apply(this.ctx,t);const s=i.Util.getAxialAlignedBoundingBox([0,0,e.canvas.width,e.canvas.height],t);this.ctx.drawImage(e.canvas,0,0),this.ctx.restore(),this.compose(s)}}beginAnnotation(t,e,s,a,n){if(_classPrivateMethodGet(this,C,k).call(this),v(this.ctx,this.foregroundColor),this.ctx.save(),this.save(),this.baseTransform&&this.ctx.setTransform.apply(this.ctx,this.baseTransform),Array.isArray(e)&&4===e.length){const a=e[2]-e[0],r=e[3]-e[1];if(n&&this.annotationCanvasMap){(s=s.slice())[4]-=e[0],s[5]-=e[1],(e=e.slice())[0]=e[1]=0,e[2]=a,e[3]=r;const[n,o]=i.Util.singularValueDecompose2dScale(this.ctx.mozCurrentTransform),{viewportScale:l}=this,c=Math.ceil(a*this.outputScaleX*l),h=Math.ceil(r*this.outputScaleY*l);this.annotationCanvas=this.canvasFactory.create(c,h);const{canvas:u,context:p}=this.annotationCanvas;this.annotationCanvasMap.set(t,u),this.annotationCanvas.savedCtx=this.ctx,this.ctx=p,d(this.ctx),this.ctx.setTransform(n,0,0,-o,0,r*o),v(this.ctx,this.foregroundColor)}else v(this.ctx,this.foregroundColor),this.ctx.rect(e[0],e[1],a,r),this.ctx.clip(),this.endPath()}this.current=new p(this.ctx.canvas.width,this.ctx.canvas.height),this.transform.apply(this,s),this.transform.apply(this,a)}endAnnotation(){this.annotationCanvas&&(this.ctx=this.annotationCanvas.savedCtx,delete this.annotationCanvas.savedCtx,delete this.annotationCanvas)}paintImageMaskXObject(t){if(!this.contentVisible)return;const e=t.count;(t=this.getObject(t.data,t)).count=e;const s=this.ctx,i=this.processingType3;if(i&&(void 0===i.compiled&&(i.compiled=function(t){const{width:e,height:s}=t;if(e>1e3||s>1e3)return null;const i=new Uint8Array([0,2,4,0,1,0,5,4,8,10,0,8,0,2,1,0]),a=e+1;let n,r,l,c=new Uint8Array(a*(s+1));const d=e+7&-8;let h=new Uint8Array(d*s),u=0;for(const o of t.data){let t=128;for(;t>0;)h[u++]=o&t?0:255,t>>=1}let p=0;for(u=0,0!==h[u]&&(c[0]=1,++p),r=1;r<e;r++)h[u]!==h[u+1]&&(c[r]=h[u]?2:1,++p),u++;for(0!==h[u]&&(c[r]=2,++p),n=1;n<s;n++){u=n*d,l=n*a,h[u-d]!==h[u]&&(c[l]=h[u]?1:8,++p);let t=(h[u]?4:0)+(h[u-d]?8:0);for(r=1;r<e;r++)t=(t>>2)+(h[u+1]?4:0)+(h[u-d+1]?8:0),i[t]&&(c[l+r]=i[t],++p),u++;if(h[u-d]!==h[u]&&(c[l+r]=h[u]?2:4,++p),p>1e3)return null}for(u=d*(s-1),l=n*a,0!==h[u]&&(c[l]=8,++p),r=1;r<e;r++)h[u]!==h[u+1]&&(c[l+r]=h[u]?4:8,++p),u++;if(0!==h[u]&&(c[l+r]=4,++p),p>1e3)return null;const f=new Int32Array([0,a,-1,0,-a,0,0,0,1]);let m,g,v;for(o.isNodeJS?g=[]:m=new Path2D,n=0;p&&n<=s;n++){let t=n*a;const s=t+e;for(;t<s&&!c[t];)t++;if(t===s)continue;m?m.moveTo(t%a,n):v=[t%a,n];const i=t;let r=c[t];do{const e=f[r];do{t+=e}while(!c[t]);const s=c[t];5!==s&&10!==s?(r=s,c[t]=0):(r=s&51*r>>4,c[t]&=r>>2|r<<2),m?m.lineTo(t%a,t/a|0):v.push(t%a,t/a|0),c[t]||--p}while(i!==t);m||g.push(v),--n}return h=null,c=null,function(t){if(t.save(),t.scale(1/e,-1/s),t.translate(0,-s),m)t.fill(m);else{t.beginPath();for(const e of g){t.moveTo(e[0],e[1]);for(let s=2,i=e.length;s<i;s+=2)t.lineTo(e[s],e[s+1])}t.fill()}t.beginPath(),t.restore()}}(t)),i.compiled))return void i.compiled(s);const a=this._createMaskCanvas(t),n=a.canvas;s.save(),s.setTransform(1,0,0,1,0,0),s.drawImage(n,a.offsetX,a.offsetY),s.restore(),this.compose()}paintImageMaskXObjectRepeat(t,e){let s=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,a=arguments.length>3&&void 0!==arguments[3]?arguments[3]:0,n=arguments.length>4?arguments[4]:void 0,r=arguments.length>5?arguments[5]:void 0;if(!this.contentVisible)return;t=this.getObject(t.data,t);const o=this.ctx;o.save();const l=o.mozCurrentTransform;o.transform(e,s,a,n,0,0);const c=this._createMaskCanvas(t);o.setTransform(1,0,0,1,0,0);for(let d=0,h=r.length;d<h;d+=2){const t=i.Util.transform(l,[e,s,a,n,r[d],r[d+1]]),[h,u]=i.Util.applyTransform([0,0],t);o.drawImage(c.canvas,h,u)}o.restore(),this.compose()}paintImageMaskXObjectGroup(t){if(!this.contentVisible)return;const e=this.ctx,s=this.current.fillColor,i=this.current.patternFill;for(const a of t){const{data:t,width:r,height:o,transform:l}=a,c=this.cachedCanvases.getCanvas("maskCanvas",r,o,!1),d=c.context;d.save(),m(d,this.getObject(t,a)),d.globalCompositeOperation="source-in",d.fillStyle=i?s.getPattern(d,this,e.mozCurrentTransformInverse,n.PathType.FILL):s,d.fillRect(0,0,r,o),d.restore(),e.save(),e.transform.apply(e,l),e.scale(1,-1),u(e,c.canvas,0,0,r,o,0,-1,1,1),e.restore()}this.compose()}paintImageXObject(t){if(!this.contentVisible)return;const e=this.getObject(t);e?this.paintInlineImageXObject(e):(0,i.warn)("Dependent image isn't ready yet")}paintImageXObjectRepeat(t,e,s,a){if(!this.contentVisible)return;const n=this.getObject(t);if(!n)return void(0,i.warn)("Dependent image isn't ready yet");const r=n.width,o=n.height,l=[];for(let i=0,c=a.length;i<c;i+=2)l.push({transform:[e,0,0,s,a[i],a[i+1]],x:0,y:0,w:r,h:o});this.paintInlineImageXObjectGroup(n,l)}paintInlineImageXObject(t){if(!this.contentVisible)return;const e=t.width,s=t.height,i=this.ctx;let a;if(this.save(),i.scale(1/e,-1/s),"function"===typeof HTMLElement&&t instanceof HTMLElement||!t.data)a=t;else{const i=this.cachedCanvases.getCanvas("inlineImage",e,s,!1);f(i.context,t,this.current.transferMaps),a=i.canvas}const n=this._scaleImage(a,i.mozCurrentTransformInverse);i.imageSmoothingEnabled=P(i.mozCurrentTransform,t.interpolate);const[r,o]=u(i,n.img,0,0,n.paintWidth,n.paintHeight,0,-s,e,s);if(this.imageLayer){const e=this.getCanvasPosition(0,-s);this.imageLayer.appendImage({imgData:t,left:e[0],top:e[1],width:r,height:o})}this.compose(),this.restore()}paintInlineImageXObjectGroup(t,e){if(!this.contentVisible)return;const s=this.ctx,i=t.width,a=t.height,n=this.cachedCanvases.getCanvas("inlineImage",i,a,!1);f(n.context,t,this.current.transferMaps);for(let r=0,o=e.length;r<o;r++){const o=e[r];if(s.save(),s.transform.apply(s,o.transform),s.scale(1,-1),u(s,n.canvas,o.x,o.y,o.w,o.h,0,-1,1,1),this.imageLayer){const e=this.getCanvasPosition(o.x,o.y);this.imageLayer.appendImage({imgData:t,left:e[0],top:e[1],width:i,height:a})}s.restore()}this.compose()}paintSolidColorImageMask(){this.contentVisible&&(this.ctx.fillRect(0,0,1,1),this.compose())}markPoint(t){}markPointProps(t,e){}beginMarkedContent(t){this.markedContentStack.push({visible:!0})}beginMarkedContentProps(t,e){"OC"===t?this.markedContentStack.push({visible:this.optionalContentConfig.isVisible(e)}):this.markedContentStack.push({visible:!0}),this.contentVisible=this.isContentVisible()}endMarkedContent(){this.markedContentStack.pop(),this.contentVisible=this.isContentVisible()}beginCompat(){}endCompat(){}consumePath(t){const e=this.current.isEmptyClip();this.pendingClip&&this.current.updateClipFromPath(),this.pendingClip||this.compose(t);const s=this.ctx;this.pendingClip&&(e||(this.pendingClip===w?s.clip("evenodd"):s.clip()),this.pendingClip=null),this.current.startNewPathAndClipBox(this.current.clipBox),s.beginPath()}getSinglePixelWidth(){if(!this._cachedGetSinglePixelWidth){const t=this.ctx.mozCurrentTransform;if(0===t[1]&&0===t[2])this._cachedGetSinglePixelWidth=1/Math.min(Math.abs(t[0]),Math.abs(t[3]));else{const e=Math.abs(t[0]*t[3]-t[2]*t[1]),s=Math.hypot(t[0],t[2]),i=Math.hypot(t[1],t[3]);this._cachedGetSinglePixelWidth=Math.max(s,i)/e}}return this._cachedGetSinglePixelWidth}getScaleForStroking(){if(!this._cachedScaleForStroking){const{lineWidth:t}=this.current,e=this.ctx.mozCurrentTransform;let s,i;if(0===e[1]&&0===e[2]){const a=Math.abs(e[0]),n=Math.abs(e[3]);if(0===t)s=1/a,i=1/n;else{const e=a*t,r=n*t;s=e<1?1/e:1,i=r<1?1/r:1}}else{const a=Math.abs(e[0]*e[3]-e[2]*e[1]),n=Math.hypot(e[0],e[1]),r=Math.hypot(e[2],e[3]);if(0===t)s=r/a,i=n/a;else{const e=t*a;s=r>e?r/e:1,i=n>e?n/e:1}}this._cachedScaleForStroking=[s,i]}return this._cachedScaleForStroking}rescaleAndStroke(t){const{ctx:e}=this,{lineWidth:s}=this.current,[i,a]=this.getScaleForStroking();if(e.lineWidth=s||1,1===i&&1===a)return void e.stroke();let n,r,o;t&&(n=e.mozCurrentTransform.slice(),r=e.getLineDash().slice(),o=e.lineDashOffset),e.scale(i,a);const l=Math.max(i,a);e.setLineDash(e.getLineDash().map((t=>t/l))),e.lineDashOffset/=l,e.stroke(),t&&(e.setTransform(...n),e.setLineDash(r),e.lineDashOffset=o)}getCanvasPosition(t,e){const s=this.ctx.mozCurrentTransform;return[s[0]*t+s[2]*e+s[4],s[1]*t+s[3]*e+s[5]]}isContentVisible(){for(let t=this.markedContentStack.length-1;t>=0;t--)if(!this.markedContentStack[t].visible)return!1;return!0}}function k(){for(;this.stateStack.length||this.inSMaskMode;)this.restore();this.ctx.restore(),this.transparentCanvas&&(this.ctx=this.compositeCtx,this.ctx.save(),this.ctx.setTransform(1,0,0,1,0,0),this.ctx.drawImage(this.transparentCanvas,0,0),this.ctx.restore(),this.transparentCanvas=null)}e.CanvasGraphics=M;for(const E in i.OPS)void 0!==M.prototype[E]&&(M.prototype[i.OPS[E]]=M.prototype[E])},(t,e,s)=>{Object.defineProperty(e,"__esModule",{value:!0}),e.TilingPattern=e.PathType=void 0,e.getShadingPattern=function(t){switch(t[0]){case"RadialAxial":return new l(t);case"Mesh":return new h(t);case"Dummy":return new u}throw new Error("Unknown IR type: ".concat(t[0]))};var i=s(1),a=s(3);const n={FILL:"Fill",STROKE:"Stroke",SHADING:"Shading"};function r(t,e){if(!e||a.isNodeJS)return;const s=e[2]-e[0],i=e[3]-e[1],n=new Path2D;n.rect(e[0],e[1],s,i),t.clip(n)}e.PathType=n;class o{constructor(){this.constructor===o&&(0,i.unreachable)("Cannot initialize BaseShadingPattern.")}getPattern(){(0,i.unreachable)("Abstract method `getPattern` called.")}}class l extends o{constructor(t){super(),this._type=t[1],this._bbox=t[2],this._colorStops=t[3],this._p0=t[4],this._p1=t[5],this._r0=t[6],this._r1=t[7],this.matrix=null}_createGradient(t){let e;"axial"===this._type?e=t.createLinearGradient(this._p0[0],this._p0[1],this._p1[0],this._p1[1]):"radial"===this._type&&(e=t.createRadialGradient(this._p0[0],this._p0[1],this._r0,this._p1[0],this._p1[1],this._r1));for(const s of this._colorStops)e.addColorStop(s[0],s[1]);return e}getPattern(t,e,s,a){let o;if(a===n.STROKE||a===n.FILL){const n=e.current.getClippedPathBoundingBox(a,t.mozCurrentTransform)||[0,0,0,0],c=Math.ceil(n[2]-n[0])||1,d=Math.ceil(n[3]-n[1])||1,h=e.cachedCanvases.getCanvas("pattern",c,d,!0),u=h.context;u.clearRect(0,0,u.canvas.width,u.canvas.height),u.beginPath(),u.rect(0,0,u.canvas.width,u.canvas.height),u.translate(-n[0],-n[1]),s=i.Util.transform(s,[1,0,0,1,n[0],n[1]]),u.transform.apply(u,e.baseTransform),this.matrix&&u.transform.apply(u,this.matrix),r(u,this._bbox),u.fillStyle=this._createGradient(u),u.fill(),o=t.createPattern(h.canvas,"no-repeat");const p=new DOMMatrix(s);try{o.setTransform(p)}catch(l){(0,i.warn)('RadialAxialShadingPattern.getPattern: "'.concat(null===l||void 0===l?void 0:l.message,'".'))}}else r(t,this._bbox),o=this._createGradient(t);return o}}function c(t,e,s,i,a,n,r,o){const l=e.coords,c=e.colors,d=t.data,h=4*t.width;let u;l[s+1]>l[i+1]&&(u=s,s=i,i=u,u=n,n=r,r=u),l[i+1]>l[a+1]&&(u=i,i=a,a=u,u=r,r=o,o=u),l[s+1]>l[i+1]&&(u=s,s=i,i=u,u=n,n=r,r=u);const p=(l[s]+e.offsetX)*e.scaleX,f=(l[s+1]+e.offsetY)*e.scaleY,m=(l[i]+e.offsetX)*e.scaleX,g=(l[i+1]+e.offsetY)*e.scaleY,v=(l[a]+e.offsetX)*e.scaleX,_=(l[a+1]+e.offsetY)*e.scaleY;if(f>=_)return;const b=c[n],y=c[n+1],A=c[n+2],P=c[r],S=c[r+1],x=c[r+2],F=c[o],w=c[o+1],C=c[o+2],M=Math.round(f),k=Math.round(_);let E,T,R,O,I,D,L,N;for(let G=M;G<=k;G++){if(G<g){let t;t=G<f?0:(f-G)/(f-g),E=p-(p-m)*t,T=b-(b-P)*t,R=y-(y-S)*t,O=A-(A-x)*t}else{let t;t=G>_?1:g===_?0:(g-G)/(g-_),E=m-(m-v)*t,T=P-(P-F)*t,R=S-(S-w)*t,O=x-(x-C)*t}let t;t=G<f?0:G>_?1:(f-G)/(f-_),I=p-(p-v)*t,D=b-(b-F)*t,L=y-(y-w)*t,N=A-(A-C)*t;const e=Math.round(Math.min(E,I)),s=Math.round(Math.max(E,I));let i=h*G+4*e;for(let a=e;a<=s;a++)t=(E-a)/(E-I),t<0?t=0:t>1&&(t=1),d[i++]=T-(T-D)*t|0,d[i++]=R-(R-L)*t|0,d[i++]=O-(O-N)*t|0,d[i++]=255}}function d(t,e,s){const i=e.coords,a=e.colors;let n,r;switch(e.type){case"lattice":const o=e.verticesPerRow,l=Math.floor(i.length/o)-1,d=o-1;for(n=0;n<l;n++){let e=n*o;for(let n=0;n<d;n++,e++)c(t,s,i[e],i[e+1],i[e+o],a[e],a[e+1],a[e+o]),c(t,s,i[e+o+1],i[e+1],i[e+o],a[e+o+1],a[e+1],a[e+o])}break;case"triangles":for(n=0,r=i.length;n<r;n+=3)c(t,s,i[n],i[n+1],i[n+2],a[n],a[n+1],a[n+2]);break;default:throw new Error("illegal figure")}}class h extends o{constructor(t){super(),this._coords=t[2],this._colors=t[3],this._figures=t[4],this._bounds=t[5],this._bbox=t[7],this._background=t[8],this.matrix=null}_createMeshCanvas(t,e,s){const i=Math.floor(this._bounds[0]),a=Math.floor(this._bounds[1]),n=Math.ceil(this._bounds[2])-i,r=Math.ceil(this._bounds[3])-a,o=Math.min(Math.ceil(Math.abs(n*t[0]*1.1)),3e3),l=Math.min(Math.ceil(Math.abs(r*t[1]*1.1)),3e3),c=n/o,h=r/l,u={coords:this._coords,colors:this._colors,offsetX:-i,offsetY:-a,scaleX:1/c,scaleY:1/h},p=o+4,f=l+4,m=s.getCanvas("mesh",p,f,!1),g=m.context,v=g.createImageData(o,l);if(e){const t=v.data;for(let s=0,i=t.length;s<i;s+=4)t[s]=e[0],t[s+1]=e[1],t[s+2]=e[2],t[s+3]=255}for(const _ of this._figures)d(v,_,u);return g.putImageData(v,2,2),{canvas:m.canvas,offsetX:i-2*c,offsetY:a-2*h,scaleX:c,scaleY:h}}getPattern(t,e,s,a){let o;if(r(t,this._bbox),a===n.SHADING)o=i.Util.singularValueDecompose2dScale(t.mozCurrentTransform);else if(o=i.Util.singularValueDecompose2dScale(e.baseTransform),this.matrix){const t=i.Util.singularValueDecompose2dScale(this.matrix);o=[o[0]*t[0],o[1]*t[1]]}const l=this._createMeshCanvas(o,a===n.SHADING?null:this._background,e.cachedCanvases);return a!==n.SHADING&&(t.setTransform.apply(t,e.baseTransform),this.matrix&&t.transform.apply(t,this.matrix)),t.translate(l.offsetX,l.offsetY),t.scale(l.scaleX,l.scaleY),t.createPattern(l.canvas,"no-repeat")}}class u extends o{getPattern(){return"hotpink"}}const p=1,f=2;class m{static get MAX_PATTERN_SIZE(){return(0,i.shadow)(this,"MAX_PATTERN_SIZE",3e3)}constructor(t,e,s,i,a){this.operatorList=t[2],this.matrix=t[3]||[1,0,0,1,0,0],this.bbox=t[4],this.xstep=t[5],this.ystep=t[6],this.paintType=t[7],this.tilingType=t[8],this.color=e,this.ctx=s,this.canvasGraphicsFactory=i,this.baseTransform=a}createPatternCanvas(t){const e=this.operatorList,s=this.bbox,a=this.xstep,n=this.ystep,r=this.paintType,o=this.tilingType,l=this.color,c=this.canvasGraphicsFactory;(0,i.info)("TilingType: "+o);const d=s[0],h=s[1],u=s[2],p=s[3],f=i.Util.singularValueDecompose2dScale(this.matrix),m=i.Util.singularValueDecompose2dScale(this.baseTransform),g=[f[0]*m[0],f[1]*m[1]],v=this.getSizeAndScale(a,this.ctx.canvas.width,g[0]),_=this.getSizeAndScale(n,this.ctx.canvas.height,g[1]),b=t.cachedCanvases.getCanvas("pattern",v.size,_.size,!0),y=b.context,A=c.createCanvasGraphics(y);A.groupLevel=t.groupLevel,this.setFillAndStrokeStyleToContext(A,r,l);let P=d,S=h,x=u,F=p;return d<0&&(P=0,x+=Math.abs(d)),h<0&&(S=0,F+=Math.abs(h)),y.translate(-v.scale*P,-_.scale*S),A.transform(v.scale,0,0,_.scale,0,0),y.save(),this.clipBbox(A,P,S,x,F),A.baseTransform=A.ctx.mozCurrentTransform.slice(),A.executeOperatorList(e),A.endDrawing(),{canvas:b.canvas,scaleX:v.scale,scaleY:_.scale,offsetX:P,offsetY:S}}getSizeAndScale(t,e,s){t=Math.abs(t);const i=Math.max(m.MAX_PATTERN_SIZE,e);let a=Math.ceil(t*s);return a>=i?a=i:s=a/t,{scale:s,size:a}}clipBbox(t,e,s,i,a){const n=i-e,r=a-s;t.ctx.rect(e,s,n,r),t.current.updateRectMinMax(t.ctx.mozCurrentTransform,[e,s,i,a]),t.clip(),t.endPath()}setFillAndStrokeStyleToContext(t,e,s){const a=t.ctx,n=t.current;switch(e){case p:const t=this.ctx;a.fillStyle=t.fillStyle,a.strokeStyle=t.strokeStyle,n.fillColor=t.fillStyle,n.strokeColor=t.strokeStyle;break;case f:const r=i.Util.makeHexColor(s[0],s[1],s[2]);a.fillStyle=r,a.strokeStyle=r,n.fillColor=r,n.strokeColor=r;break;default:throw new i.FormatError("Unsupported paint type: ".concat(e))}}getPattern(t,e,s,a){let r=s;a!==n.SHADING&&(r=i.Util.transform(r,e.baseTransform),this.matrix&&(r=i.Util.transform(r,this.matrix)));const o=this.createPatternCanvas(e);let l=new DOMMatrix(r);l=l.translate(o.offsetX,o.offsetY),l=l.scale(1/o.scaleX,1/o.scaleY);const c=t.createPattern(o.canvas,"repeat");try{c.setTransform(l)}catch(d){(0,i.warn)('TilingPattern.getPattern: "'.concat(null===d||void 0===d?void 0:d.message,'".'))}return c}}e.TilingPattern=m},(t,e,s)=>{Object.defineProperty(e,"__esModule",{value:!0}),e.applyMaskImageData=function(t){let{src:e,srcPos:s=0,dest:a,destPos:n=0,width:r,height:o,inverseDecode:l=!1}=t;const c=i.FeatureTest.isLittleEndian?4278190080:255,[d,h]=l?[0,c]:[c,0],u=r>>3,p=7&r,f=e.length;a=new Uint32Array(a.buffer);for(let i=0;i<o;i++){for(const i=s+u;s<i;s++){const t=s<f?e[s]:255;a[n++]=128&t?h:d,a[n++]=64&t?h:d,a[n++]=32&t?h:d,a[n++]=16&t?h:d,a[n++]=8&t?h:d,a[n++]=4&t?h:d,a[n++]=2&t?h:d,a[n++]=1&t?h:d}if(0===p)continue;const t=s<f?e[s++]:255;for(let e=0;e<p;e++)a[n++]=t&1<<7-e?h:d}return{srcPos:s,destPos:n}};var i=s(1)},(t,e)=>{Object.defineProperty(e,"__esModule",{value:!0}),e.GlobalWorkerOptions=void 0;const s=Object.create(null);e.GlobalWorkerOptions=s,s.workerPort=void 0===s.workerPort?null:s.workerPort,s.workerSrc=void 0===s.workerSrc?"":s.workerSrc},(t,e,s)=>{Object.defineProperty(e,"__esModule",{value:!0}),e.MessageHandler=void 0;var i=s(1);const a=1,n=2,r=1,o=2,l=3,c=4,d=5,h=6,u=7,p=8;function f(t){switch(t instanceof Error||"object"===typeof t&&null!==t||(0,i.unreachable)('wrapReason: Expected "reason" to be a (possibly cloned) Error.'),t.name){case"AbortException":return new i.AbortException(t.message);case"MissingPDFException":return new i.MissingPDFException(t.message);case"PasswordException":return new i.PasswordException(t.message,t.code);case"UnexpectedResponseException":return new i.UnexpectedResponseException(t.message,t.status);case"UnknownErrorException":return new i.UnknownErrorException(t.message,t.details);default:return new i.UnknownErrorException(t.message,t.toString())}}e.MessageHandler=class{constructor(t,e,s){this.sourceName=t,this.targetName=e,this.comObj=s,this.callbackId=1,this.streamId=1,this.streamSinks=Object.create(null),this.streamControllers=Object.create(null),this.callbackCapabilities=Object.create(null),this.actionHandler=Object.create(null),this._onComObjOnMessage=t=>{const e=t.data;if(e.targetName!==this.sourceName)return;if(e.stream)return void this._processStreamMessage(e);if(e.callback){const t=e.callbackId,s=this.callbackCapabilities[t];if(!s)throw new Error("Cannot resolve callback ".concat(t));if(delete this.callbackCapabilities[t],e.callback===a)s.resolve(e.data);else{if(e.callback!==n)throw new Error("Unexpected callback case");s.reject(f(e.reason))}return}const i=this.actionHandler[e.action];if(!i)throw new Error("Unknown action from worker: ".concat(e.action));if(e.callbackId){const t=this.sourceName,r=e.sourceName;new Promise((function(t){t(i(e.data))})).then((function(i){s.postMessage({sourceName:t,targetName:r,callback:a,callbackId:e.callbackId,data:i})}),(function(i){s.postMessage({sourceName:t,targetName:r,callback:n,callbackId:e.callbackId,reason:f(i)})}))}else e.streamId?this._createStreamSink(e):i(e.data)},s.addEventListener("message",this._onComObjOnMessage)}on(t,e){const s=this.actionHandler;if(s[t])throw new Error('There is already an actionName called "'.concat(t,'"'));s[t]=e}send(t,e,s){this.comObj.postMessage({sourceName:this.sourceName,targetName:this.targetName,action:t,data:e},s)}sendWithPromise(t,e,s){const a=this.callbackId++,n=(0,i.createPromiseCapability)();this.callbackCapabilities[a]=n;try{this.comObj.postMessage({sourceName:this.sourceName,targetName:this.targetName,action:t,callbackId:a,data:e},s)}catch(r){n.reject(r)}return n.promise}sendWithStream(t,e,s,a){const n=this.streamId++,o=this.sourceName,l=this.targetName,c=this.comObj;return new ReadableStream({start:s=>{const r=(0,i.createPromiseCapability)();return this.streamControllers[n]={controller:s,startCall:r,pullCall:null,cancelCall:null,isClosed:!1},c.postMessage({sourceName:o,targetName:l,action:t,streamId:n,data:e,desiredSize:s.desiredSize},a),r.promise},pull:t=>{const e=(0,i.createPromiseCapability)();return this.streamControllers[n].pullCall=e,c.postMessage({sourceName:o,targetName:l,stream:h,streamId:n,desiredSize:t.desiredSize}),e.promise},cancel:t=>{(0,i.assert)(t instanceof Error,"cancel must have a valid reason");const e=(0,i.createPromiseCapability)();return this.streamControllers[n].cancelCall=e,this.streamControllers[n].isClosed=!0,c.postMessage({sourceName:o,targetName:l,stream:r,streamId:n,reason:f(t)}),e.promise}},s)}_createStreamSink(t){const e=t.streamId,s=this.sourceName,a=t.sourceName,n=this.comObj,r=this,o=this.actionHandler[t.action],h={enqueue(t){let r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1,o=arguments.length>2?arguments[2]:void 0;if(this.isCancelled)return;const l=this.desiredSize;this.desiredSize-=r,l>0&&this.desiredSize<=0&&(this.sinkCapability=(0,i.createPromiseCapability)(),this.ready=this.sinkCapability.promise),n.postMessage({sourceName:s,targetName:a,stream:c,streamId:e,chunk:t},o)},close(){this.isCancelled||(this.isCancelled=!0,n.postMessage({sourceName:s,targetName:a,stream:l,streamId:e}),delete r.streamSinks[e])},error(t){(0,i.assert)(t instanceof Error,"error must have a valid reason"),this.isCancelled||(this.isCancelled=!0,n.postMessage({sourceName:s,targetName:a,stream:d,streamId:e,reason:f(t)}))},sinkCapability:(0,i.createPromiseCapability)(),onPull:null,onCancel:null,isCancelled:!1,desiredSize:t.desiredSize,ready:null};h.sinkCapability.resolve(),h.ready=h.sinkCapability.promise,this.streamSinks[e]=h,new Promise((function(e){e(o(t.data,h))})).then((function(){n.postMessage({sourceName:s,targetName:a,stream:p,streamId:e,success:!0})}),(function(t){n.postMessage({sourceName:s,targetName:a,stream:p,streamId:e,reason:f(t)})}))}_processStreamMessage(t){const e=t.streamId,s=this.sourceName,a=t.sourceName,n=this.comObj,m=this.streamControllers[e],g=this.streamSinks[e];switch(t.stream){case p:t.success?m.startCall.resolve():m.startCall.reject(f(t.reason));break;case u:t.success?m.pullCall.resolve():m.pullCall.reject(f(t.reason));break;case h:if(!g){n.postMessage({sourceName:s,targetName:a,stream:u,streamId:e,success:!0});break}g.desiredSize<=0&&t.desiredSize>0&&g.sinkCapability.resolve(),g.desiredSize=t.desiredSize,new Promise((function(t){t(g.onPull&&g.onPull())})).then((function(){n.postMessage({sourceName:s,targetName:a,stream:u,streamId:e,success:!0})}),(function(t){n.postMessage({sourceName:s,targetName:a,stream:u,streamId:e,reason:f(t)})}));break;case c:if((0,i.assert)(m,"enqueue should have stream controller"),m.isClosed)break;m.controller.enqueue(t.chunk);break;case l:if((0,i.assert)(m,"close should have stream controller"),m.isClosed)break;m.isClosed=!0,m.controller.close(),this._deleteStreamController(m,e);break;case d:(0,i.assert)(m,"error should have stream controller"),m.controller.error(f(t.reason)),this._deleteStreamController(m,e);break;case o:t.success?m.cancelCall.resolve():m.cancelCall.reject(f(t.reason)),this._deleteStreamController(m,e);break;case r:if(!g)break;new Promise((function(e){e(g.onCancel&&g.onCancel(f(t.reason)))})).then((function(){n.postMessage({sourceName:s,targetName:a,stream:o,streamId:e,success:!0})}),(function(t){n.postMessage({sourceName:s,targetName:a,stream:o,streamId:e,reason:f(t)})})),g.sinkCapability.reject(f(t.reason)),g.isCancelled=!0,delete this.streamSinks[e];break;default:throw new Error("Unexpected stream case")}}async _deleteStreamController(t,e){await Promise.allSettled([t.startCall&&t.startCall.promise,t.pullCall&&t.pullCall.promise,t.cancelCall&&t.cancelCall.promise]),delete this.streamControllers[e]}destroy(){this.comObj.removeEventListener("message",this._onComObjOnMessage)}}},(t,e,s)=>{Object.defineProperty(e,"__esModule",{value:!0}),e.Metadata=void 0;var i=s(1),a=new WeakMap,n=new WeakMap;e.Metadata=class{constructor(t){let{parsedData:e,rawData:s}=t;_classPrivateFieldInitSpec(this,a,{writable:!0,value:void 0}),_classPrivateFieldInitSpec(this,n,{writable:!0,value:void 0}),_classPrivateFieldSet(this,a,e),_classPrivateFieldSet(this,n,s)}getRaw(){return _classPrivateFieldGet(this,n)}get(t){var e;return null!==(e=_classPrivateFieldGet(this,a).get(t))&&void 0!==e?e:null}getAll(){return(0,i.objectFromMap)(_classPrivateFieldGet(this,a))}has(t){return _classPrivateFieldGet(this,a).has(t)}}},(t,e,s)=>{Object.defineProperty(e,"__esModule",{value:!0}),e.OptionalContentConfig=void 0;var i=s(1);const a=Symbol("INTERNAL");var n=new WeakMap;class r{constructor(t,e){_classPrivateFieldInitSpec(this,n,{writable:!0,value:!0}),this.name=t,this.intent=e}get visible(){return _classPrivateFieldGet(this,n)}_setVisible(t,e){t!==a&&(0,i.unreachable)("Internal method `_setVisible` called."),_classPrivateFieldSet(this,n,e)}}var o=new WeakMap,l=new WeakMap,c=new WeakMap,d=new WeakMap,h=new WeakSet;function u(t){const e=t.length;if(e<2)return!0;const s=t[0];for(let a=1;a<e;a++){const e=t[a];let n;if(Array.isArray(e))n=_classPrivateMethodGet(this,h,u).call(this,e);else{if(!_classPrivateFieldGet(this,l).has(e))return(0,i.warn)("Optional content group not found: ".concat(e)),!0;n=_classPrivateFieldGet(this,l).get(e).visible}switch(s){case"And":if(!n)return!1;break;case"Or":if(n)return!0;break;case"Not":return!n;default:return!0}}return"And"===s}e.OptionalContentConfig=class{constructor(t){if(_classPrivateMethodInitSpec(this,h),_classPrivateFieldInitSpec(this,o,{writable:!0,value:!0}),_classPrivateFieldInitSpec(this,l,{writable:!0,value:new Map}),_classPrivateFieldInitSpec(this,c,{writable:!0,value:null}),_classPrivateFieldInitSpec(this,d,{writable:!0,value:null}),this.name=null,this.creator=null,null!==t){this.name=t.name,this.creator=t.creator,_classPrivateFieldSet(this,d,t.order);for(const e of t.groups)_classPrivateFieldGet(this,l).set(e.id,new r(e.name,e.intent));if("OFF"===t.baseState)for(const t of _classPrivateFieldGet(this,l).values())t._setVisible(a,!1);for(const e of t.on)_classPrivateFieldGet(this,l).get(e)._setVisible(a,!0);for(const e of t.off)_classPrivateFieldGet(this,l).get(e)._setVisible(a,!1);_classPrivateFieldSet(this,c,new Map);for(const[t,e]of _classPrivateFieldGet(this,l))_classPrivateFieldGet(this,c).set(t,e.visible)}}isVisible(t){if(0===_classPrivateFieldGet(this,l).size)return!0;if(!t)return(0,i.warn)("Optional content group not defined."),!0;if("OCG"===t.type)return _classPrivateFieldGet(this,l).has(t.id)?_classPrivateFieldGet(this,l).get(t.id).visible:((0,i.warn)("Optional content group not found: ".concat(t.id)),!0);if("OCMD"===t.type){if(t.expression)return _classPrivateMethodGet(this,h,u).call(this,t.expression);if(!t.policy||"AnyOn"===t.policy){for(const e of t.ids){if(!_classPrivateFieldGet(this,l).has(e))return(0,i.warn)("Optional content group not found: ".concat(e)),!0;if(_classPrivateFieldGet(this,l).get(e).visible)return!0}return!1}if("AllOn"===t.policy){for(const e of t.ids){if(!_classPrivateFieldGet(this,l).has(e))return(0,i.warn)("Optional content group not found: ".concat(e)),!0;if(!_classPrivateFieldGet(this,l).get(e).visible)return!1}return!0}if("AnyOff"===t.policy){for(const e of t.ids){if(!_classPrivateFieldGet(this,l).has(e))return(0,i.warn)("Optional content group not found: ".concat(e)),!0;if(!_classPrivateFieldGet(this,l).get(e).visible)return!0}return!1}if("AllOff"===t.policy){for(const e of t.ids){if(!_classPrivateFieldGet(this,l).has(e))return(0,i.warn)("Optional content group not found: ".concat(e)),!0;if(_classPrivateFieldGet(this,l).get(e).visible)return!1}return!0}return(0,i.warn)("Unknown optional content policy ".concat(t.policy,".")),!0}return(0,i.warn)("Unknown group type ".concat(t.type,".")),!0}setVisibility(t){let e=!(arguments.length>1&&void 0!==arguments[1])||arguments[1];_classPrivateFieldGet(this,l).has(t)?(_classPrivateFieldGet(this,l).get(t)._setVisible(a,!!e),_classPrivateFieldSet(this,o,null)):(0,i.warn)("Optional content group not found: ".concat(t))}get hasInitialVisibility(){if(null!==_classPrivateFieldGet(this,o))return _classPrivateFieldGet(this,o);for(const[t,e]of _classPrivateFieldGet(this,l)){const s=_classPrivateFieldGet(this,c).get(t);if(e.visible!==s)return _classPrivateFieldSet(this,o,!1)}return _classPrivateFieldSet(this,o,!0)}getOrder(){return _classPrivateFieldGet(this,l).size?_classPrivateFieldGet(this,d)?_classPrivateFieldGet(this,d).slice():[..._classPrivateFieldGet(this,l).keys()]:null}getGroups(){return _classPrivateFieldGet(this,l).size>0?(0,i.objectFromMap)(_classPrivateFieldGet(this,l)):null}getGroup(t){return _classPrivateFieldGet(this,l).get(t)||null}}},(t,e,s)=>{Object.defineProperty(e,"__esModule",{value:!0}),e.PDFDataTransportStream=void 0;var i=s(1),a=s(4);e.PDFDataTransportStream=class{constructor(t,e){(0,i.assert)(e,'PDFDataTransportStream - missing required "pdfDataRangeTransport" argument.'),this._queuedChunks=[],this._progressiveDone=t.progressiveDone||!1,this._contentDispositionFilename=t.contentDispositionFilename||null;const s=t.initialData;if((null===s||void 0===s?void 0:s.length)>0){const t=new Uint8Array(s).buffer;this._queuedChunks.push(t)}this._pdfDataRangeTransport=e,this._isStreamingSupported=!t.disableStream,this._isRangeSupported=!t.disableRange,this._contentLength=t.length,this._fullRequestReader=null,this._rangeReaders=[],this._pdfDataRangeTransport.addRangeListener(((t,e)=>{this._onReceiveData({begin:t,chunk:e})})),this._pdfDataRangeTransport.addProgressListener(((t,e)=>{this._onProgress({loaded:t,total:e})})),this._pdfDataRangeTransport.addProgressiveReadListener((t=>{this._onReceiveData({chunk:t})})),this._pdfDataRangeTransport.addProgressiveDoneListener((()=>{this._onProgressiveDone()})),this._pdfDataRangeTransport.transportReady()}_onReceiveData(t){const e=new Uint8Array(t.chunk).buffer;if(void 0===t.begin)this._fullRequestReader?this._fullRequestReader._enqueue(e):this._queuedChunks.push(e);else{const s=this._rangeReaders.some((function(s){return s._begin===t.begin&&(s._enqueue(e),!0)}));(0,i.assert)(s,"_onReceiveData - no `PDFDataTransportStreamRangeReader` instance found.")}}get _progressiveDataLength(){var t,e;return null!==(t=null===(e=this._fullRequestReader)||void 0===e?void 0:e._loaded)&&void 0!==t?t:0}_onProgress(t){if(void 0===t.total){const e=this._rangeReaders[0];null!==e&&void 0!==e&&e.onProgress&&e.onProgress({loaded:t.loaded})}else{const e=this._fullRequestReader;null!==e&&void 0!==e&&e.onProgress&&e.onProgress({loaded:t.loaded,total:t.total})}}_onProgressiveDone(){this._fullRequestReader&&this._fullRequestReader.progressiveDone(),this._progressiveDone=!0}_removeRangeReader(t){const e=this._rangeReaders.indexOf(t);e>=0&&this._rangeReaders.splice(e,1)}getFullReader(){(0,i.assert)(!this._fullRequestReader,"PDFDataTransportStream.getFullReader can only be called once.");const t=this._queuedChunks;return this._queuedChunks=null,new n(this,t,this._progressiveDone,this._contentDispositionFilename)}getRangeReader(t,e){if(e<=this._progressiveDataLength)return null;const s=new r(this,t,e);return this._pdfDataRangeTransport.requestDataRange(t,e),this._rangeReaders.push(s),s}cancelAllRequests(t){this._fullRequestReader&&this._fullRequestReader.cancel(t);for(const e of this._rangeReaders.slice(0))e.cancel(t);this._pdfDataRangeTransport.abort()}};class n{constructor(t,e){let s=arguments.length>2&&void 0!==arguments[2]&&arguments[2],i=arguments.length>3&&void 0!==arguments[3]?arguments[3]:null;this._stream=t,this._done=s||!1,this._filename=(0,a.isPdfFile)(i)?i:null,this._queuedChunks=e||[],this._loaded=0;for(const a of this._queuedChunks)this._loaded+=a.byteLength;this._requests=[],this._headersReady=Promise.resolve(),t._fullRequestReader=this,this.onProgress=null}_enqueue(t){this._done||(this._requests.length>0?this._requests.shift().resolve({value:t,done:!1}):this._queuedChunks.push(t),this._loaded+=t.byteLength)}get headersReady(){return this._headersReady}get filename(){return this._filename}get isRangeSupported(){return this._stream._isRangeSupported}get isStreamingSupported(){return this._stream._isStreamingSupported}get contentLength(){return this._stream._contentLength}async read(){if(this._queuedChunks.length>0)return{value:this._queuedChunks.shift(),done:!1};if(this._done)return{value:void 0,done:!0};const t=(0,i.createPromiseCapability)();return this._requests.push(t),t.promise}cancel(t){this._done=!0;for(const e of this._requests)e.resolve({value:void 0,done:!0});this._requests.length=0}progressiveDone(){this._done||(this._done=!0)}}class r{constructor(t,e,s){this._stream=t,this._begin=e,this._end=s,this._queuedChunk=null,this._requests=[],this._done=!1,this.onProgress=null}_enqueue(t){if(!this._done){if(0===this._requests.length)this._queuedChunk=t;else{this._requests.shift().resolve({value:t,done:!1});for(const t of this._requests)t.resolve({value:void 0,done:!0});this._requests.length=0}this._done=!0,this._stream._removeRangeReader(this)}}get isStreamingSupported(){return!1}async read(){if(this._queuedChunk){const t=this._queuedChunk;return this._queuedChunk=null,{value:t,done:!1}}if(this._done)return{value:void 0,done:!0};const t=(0,i.createPromiseCapability)();return this._requests.push(t),t.promise}cancel(t){this._done=!0;for(const e of this._requests)e.resolve({value:void 0,done:!0});this._requests.length=0,this._stream._removeRangeReader(this)}}},(t,e)=>{Object.defineProperty(e,"__esModule",{value:!0}),e.XfaText=void 0;class s{static textContent(t){const e=[],i={items:e,styles:Object.create(null)};return function t(i){var a;if(!i)return;let n=null;const r=i.name;if("#text"===r)n=i.value;else{if(!s.shouldBuildText(r))return;null!==i&&void 0!==i&&null!==(a=i.attributes)&&void 0!==a&&a.textContent?n=i.attributes.textContent:i.value&&(n=i.value)}if(null!==n&&e.push({str:n}),i.children)for(const e of i.children)t(e)}(t),i}static shouldBuildText(t){return!("textarea"===t||"input"===t||"option"===t||"select"===t)}}e.XfaText=s},(t,e,s)=>{Object.defineProperty(e,"__esModule",{value:!0}),e.NodeStandardFontDataFactory=e.NodeCanvasFactory=e.NodeCMapReaderFactory=void 0;var i=s(5);const a=function(t){return new Promise(((e,s)=>{__webpack_require__(172).readFile(t,((t,i)=>{!t&&i?e(new Uint8Array(i)):s(new Error(t))}))}))};class n extends i.BaseCanvasFactory{_createCanvas(t,e){return __webpack_require__(3414).createCanvas(t,e)}}e.NodeCanvasFactory=n;class r extends i.BaseCMapReaderFactory{_fetchData(t,e){return a(t).then((t=>({cMapData:t,compressionType:e})))}}e.NodeCMapReaderFactory=r;class o extends i.BaseStandardFontDataFactory{_fetchData(t){return a(t)}}e.NodeStandardFontDataFactory=o},(t,e,s)=>{Object.defineProperty(e,"__esModule",{value:!0}),e.AnnotationEditorLayer=void 0;var i=s(1),a=s(9),n=s(4),r=s(23),o=s(24),l=new WeakMap,c=new WeakMap,d=new WeakMap,h=new WeakMap,u=new WeakMap,p=new WeakMap,f=new WeakMap,m=new WeakMap,g=new WeakMap,v=new WeakMap,_=new WeakSet,b=new WeakSet,y=new WeakSet,A=new WeakSet;class P{constructor(t){_classPrivateMethodInitSpec(this,A),_classPrivateMethodInitSpec(this,y),_classPrivateMethodInitSpec(this,b),_classPrivateMethodInitSpec(this,_),_classPrivateFieldInitSpec(this,v,{get:S,set:void 0}),_classPrivateFieldInitSpec(this,l,{writable:!0,value:!1}),_classPrivateFieldInitSpec(this,c,{writable:!0,value:this.pointerup.bind(this)}),_classPrivateFieldInitSpec(this,d,{writable:!0,value:this.pointerdown.bind(this)}),_classPrivateFieldInitSpec(this,h,{writable:!0,value:new Map}),_classPrivateFieldInitSpec(this,u,{writable:!0,value:!1}),_classPrivateFieldInitSpec(this,p,{writable:!0,value:new WeakMap}),_classPrivateFieldInitSpec(this,f,{writable:!0,value:new Map}),_classPrivateFieldInitSpec(this,m,{writable:!0,value:void 0}),_classPrivateFieldInitSpec(this,g,{writable:!0,value:new Set}),P._initialized||(P._initialized=!0,r.FreeTextEditor.initialize(t.l10n),o.InkEditor.initialize(t.l10n),t.uiManager.registerEditorTypes([r.FreeTextEditor,o.InkEditor])),_classPrivateFieldSet(this,m,t.uiManager),this.annotationStorage=t.annotationStorage,this.pageIndex=t.pageIndex,this.div=t.div,_classPrivateFieldGet(this,m).addLayer(this)}get textLayerElements(){const t=this.div.parentNode.getElementsByClassName("textLayer").item(0);if(!t)return(0,i.shadow)(this,"textLayerElements",null);let e=_classPrivateFieldGet(this,p).get(t);return e||(e=t.querySelectorAll('span[role="presentation"]'),0===e.length?(0,i.shadow)(this,"textLayerElements",null):(e=Array.from(e),e.sort(_classStaticPrivateMethodGet(P,P,F)),_classPrivateFieldGet(this,p).set(t,e),e))}updateToolbar(t){_classPrivateFieldGet(this,m).updateToolbar(t)}updateMode(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:_classPrivateFieldGet(this,m).getMode();_classPrivateMethodGet(this,A,M).call(this),t===i.AnnotationEditorType.INK?(this.addInkEditorIfNeeded(!1),this.disableClick()):this.enableClick(),_classPrivateFieldGet(this,m).unselectAll()}addInkEditorIfNeeded(t){if(t||_classPrivateFieldGet(this,m).getMode()===i.AnnotationEditorType.INK){if(!t)for(const t of _classPrivateFieldGet(this,h).values())if(t.isEmpty())return void t.setInBackground();_classPrivateMethodGet(this,y,C).call(this,{offsetX:0,offsetY:0}).setInBackground()}}setEditingState(t){_classPrivateFieldGet(this,m).setEditingState(t)}addCommands(t){_classPrivateFieldGet(this,m).addCommands(t)}enable(){this.div.style.pointerEvents="auto";for(const t of _classPrivateFieldGet(this,h).values())t.enableEditing()}disable(){this.div.style.pointerEvents="none";for(const t of _classPrivateFieldGet(this,h).values())t.disableEditing()}setActiveEditor(t){_classPrivateFieldGet(this,m).getActive()!==t&&_classPrivateFieldGet(this,m).setActiveEditor(t)}enableClick(){this.div.addEventListener("pointerdown",_classPrivateFieldGet(this,d)),this.div.addEventListener("pointerup",_classPrivateFieldGet(this,c))}disableClick(){this.div.removeEventListener("pointerdown",_classPrivateFieldGet(this,d)),this.div.removeEventListener("pointerup",_classPrivateFieldGet(this,c))}attach(t){_classPrivateFieldGet(this,h).set(t.id,t)}detach(t){_classPrivateFieldGet(this,h).delete(t.id),this.removePointerInTextLayer(t)}remove(t){_classPrivateFieldGet(this,m).removeEditor(t),this.detach(t),this.annotationStorage.removeKey(t.id),t.div.style.display="none",setTimeout((()=>{t.div.style.display="",t.div.remove(),t.isAttachedToDOM=!1,document.activeElement===document.body&&_classPrivateFieldGet(this,m).focusMainContainer()}),0),_classPrivateFieldGet(this,u)||this.addInkEditorIfNeeded(!1)}onTextLayerRendered(){_classPrivateFieldGet(this,f).clear();for(const t of _classPrivateFieldGet(this,g))t.isAttachedToDOM&&this.addPointerInTextLayer(t);_classPrivateFieldGet(this,g).clear()}removePointerInTextLayer(t){var e;if(!_classPrivateFieldGet(this,v))return void _classPrivateFieldGet(this,g).delete(t);const{id:s}=t,i=_classPrivateFieldGet(this,f).get(s);if(!i)return;_classPrivateFieldGet(this,f).delete(s);let a=i.getAttribute("aria-owns");null!==(e=a)&&void 0!==e&&e.includes(s)&&(a=a.split(" ").filter((t=>t!==s)).join(" "),a?i.setAttribute("aria-owns",a):(i.removeAttribute("aria-owns"),i.setAttribute("role","presentation")))}addPointerInTextLayer(t){if(!_classPrivateFieldGet(this,v))return void _classPrivateFieldGet(this,g).add(t);this.removePointerInTextLayer(t);const e=this.textLayerElements;if(!e)return;const{contentDiv:s}=t,i=t.getIdForTextLayer(),a=(0,n.binarySearchFirstItem)(e,(t=>_classStaticPrivateMethodGet(P,P,F).call(P,s,t)<0)),r=e[Math.max(0,a-1)],o=r.getAttribute("aria-owns");null!==o&&void 0!==o&&o.includes(i)||r.setAttribute("aria-owns",o?"".concat(o," ").concat(i):i),r.removeAttribute("role"),_classPrivateFieldGet(this,f).set(i,r)}moveDivInDOM(t){this.addPointerInTextLayer(t);const{div:e,contentDiv:s}=t;if(!this.div.hasChildNodes())return void this.div.append(e);const i=Array.from(this.div.childNodes).filter((t=>t!==e));if(0===i.length)return;const a=(0,n.binarySearchFirstItem)(i,(t=>_classStaticPrivateMethodGet(P,P,F).call(P,s,t)<0));0===a?i[0].before(e):i[a-1].after(e)}add(t){if(_classPrivateMethodGet(this,_,x).call(this,t),this.addToAnnotationStorage(t),_classPrivateFieldGet(this,m).addEditor(t),this.attach(t),!t.isAttachedToDOM){const e=t.render();this.div.append(e),t.isAttachedToDOM=!0}this.moveDivInDOM(t),t.onceAdded()}addToAnnotationStorage(t){t.isEmpty()||this.annotationStorage.has(t.id)||this.annotationStorage.setValue(t.id,t)}addOrRebuild(t){t.needsToBeRebuilt()?t.rebuild():this.add(t)}addANewEditor(t){this.addCommands({cmd:()=>{this.addOrRebuild(t)},undo:()=>{t.remove()},mustExec:!0})}addUndoableEditor(t){this.addCommands({cmd:()=>{this.addOrRebuild(t)},undo:()=>{t.remove()},mustExec:!1})}getNextId(){return _classPrivateFieldGet(this,m).getId()}deserialize(t){switch(t.annotationType){case i.AnnotationEditorType.FREETEXT:return r.FreeTextEditor.deserialize(t,this);case i.AnnotationEditorType.INK:return o.InkEditor.deserialize(t,this)}return null}setSelected(t){_classPrivateFieldGet(this,m).setSelected(t)}toggleSelected(t){_classPrivateFieldGet(this,m).toggleSelected(t)}isSelected(t){return _classPrivateFieldGet(this,m).isSelected(t)}unselect(t){_classPrivateFieldGet(this,m).unselect(t)}pointerup(t){const e=a.KeyboardManager.platform.isMac;0!==t.button||t.ctrlKey&&e||t.target===this.div&&(_classPrivateFieldGet(this,l)?_classPrivateMethodGet(this,y,C).call(this,t):_classPrivateFieldSet(this,l,!0))}pointerdown(t){const e=a.KeyboardManager.platform.isMac;if(0!==t.button||t.ctrlKey&&e)return;if(t.target!==this.div)return;const s=_classPrivateFieldGet(this,m).getActive();_classPrivateFieldSet(this,l,!s||s.isEmpty())}drop(t){const e=t.dataTransfer.getData("text/plain"),s=_classPrivateFieldGet(this,m).getEditor(e);if(!s)return;t.preventDefault(),t.dataTransfer.dropEffect="move",_classPrivateMethodGet(this,_,x).call(this,s);const i=this.div.getBoundingClientRect(),a=t.clientX-i.x,n=t.clientY-i.y;s.translate(a-s.startX,n-s.startY),this.moveDivInDOM(s),s.div.focus()}dragover(t){t.preventDefault()}destroy(){var t;(null===(t=_classPrivateFieldGet(this,m).getActive())||void 0===t?void 0:t.parent)===this&&_classPrivateFieldGet(this,m).setActiveEditor(null);for(const e of _classPrivateFieldGet(this,h).values())this.removePointerInTextLayer(e),e.isAttachedToDOM=!1,e.div.remove(),e.parent=null;_classPrivateFieldGet(this,f).clear(),this.div=null,_classPrivateFieldGet(this,h).clear(),_classPrivateFieldGet(this,g).clear(),_classPrivateFieldGet(this,m).removeLayer(this)}render(t){this.viewport=t.viewport,(0,a.bindEvents)(this,this.div,["dragover","drop"]),this.setDimensions();for(const e of _classPrivateFieldGet(this,m).getEditors(this.pageIndex))this.add(e);this.updateMode()}update(t){this.viewport=t.viewport,this.setDimensions(),this.updateMode()}get scaleFactor(){return this.viewport.scale}get pageDimensions(){const[t,e,s,i]=this.viewport.viewBox;return[s-t,i-e]}get viewportBaseDimensions(){const{width:t,height:e,rotation:s}=this.viewport;return s%180===0?[t,e]:[e,t]}setDimensions(){const{width:t,height:e,rotation:s}=this.viewport,i=s%180!==0,a=Math.floor(t)+"px",n=Math.floor(e)+"px";this.div.style.width=i?n:a,this.div.style.height=i?a:n,this.div.setAttribute("data-main-rotation",s)}}function S(){return!!this.div.parentNode.querySelector(".textLayer .endOfContent")}function x(t){var e;t.parent!==this&&(this.attach(t),t.pageIndex=this.pageIndex,null===(e=t.parent)||void 0===e||e.detach(t),t.parent=this,t.div&&t.isAttachedToDOM&&(t.div.remove(),this.div.append(t.div)))}function F(t,e){const s=t.getBoundingClientRect(),i=e.getBoundingClientRect();return s.y+s.height<=i.y?-1:i.y+i.height<=s.y?1:s.x+s.width/2-(i.x+i.width/2)}function w(t){switch(_classPrivateFieldGet(this,m).getMode()){case i.AnnotationEditorType.FREETEXT:return new r.FreeTextEditor(t);case i.AnnotationEditorType.INK:return new o.InkEditor(t)}return null}function C(t){const e=this.getNextId(),s=_classPrivateMethodGet(this,b,w).call(this,{parent:this,id:e,x:t.offsetX,y:t.offsetY});return s&&this.add(s),s}function M(){_classPrivateFieldSet(this,u,!0);for(const t of _classPrivateFieldGet(this,h).values())t.isEmpty()&&t.remove();_classPrivateFieldSet(this,u,!1)}_defineProperty(P,"_initialized",!1),e.AnnotationEditorLayer=P},(t,e,s)=>{var i;Object.defineProperty(e,"__esModule",{value:!0}),e.FreeTextEditor=void 0;var a=s(1),n=s(9),r=s(8),o=new WeakMap,l=new WeakMap,c=new WeakMap,d=new WeakMap,h=new WeakMap,u=new WeakMap,p=new WeakMap,f=new WeakMap,m=new WeakSet,g=new WeakSet,v=new WeakSet,_=new WeakSet;class b extends r.AnnotationEditor{constructor(t){super(_objectSpread(_objectSpread({},t),{},{name:"freeTextEditor"})),_classPrivateMethodInitSpec(this,_),_classPrivateMethodInitSpec(this,v),_classPrivateMethodInitSpec(this,g),_classPrivateMethodInitSpec(this,m),_classPrivateFieldInitSpec(this,o,{writable:!0,value:this.editorDivBlur.bind(this)}),_classPrivateFieldInitSpec(this,l,{writable:!0,value:this.editorDivFocus.bind(this)}),_classPrivateFieldInitSpec(this,c,{writable:!0,value:this.editorDivKeydown.bind(this)}),_classPrivateFieldInitSpec(this,d,{writable:!0,value:void 0}),_classPrivateFieldInitSpec(this,h,{writable:!0,value:""}),_classPrivateFieldInitSpec(this,u,{writable:!0,value:""}),_classPrivateFieldInitSpec(this,p,{writable:!0,value:!1}),_classPrivateFieldInitSpec(this,f,{writable:!0,value:void 0}),_classPrivateFieldSet(this,d,t.color||b._defaultColor||r.AnnotationEditor._defaultLineColor),_classPrivateFieldSet(this,f,t.fontSize||b._defaultFontSize)}static initialize(t){this._l10nPromise=new Map(["free_text_default_content","editor_free_text_aria_label"].map((e=>[e,t.get(e)])));const e=getComputedStyle(document.documentElement);this._internalPadding=parseFloat(e.getPropertyValue("--freetext-padding"))}static updateDefaultParams(t,e){switch(t){case a.AnnotationEditorParamsType.FREETEXT_SIZE:b._defaultFontSize=e;break;case a.AnnotationEditorParamsType.FREETEXT_COLOR:b._defaultColor=e}}updateParams(t,e){switch(t){case a.AnnotationEditorParamsType.FREETEXT_SIZE:_classPrivateMethodGet(this,m,y).call(this,e);break;case a.AnnotationEditorParamsType.FREETEXT_COLOR:_classPrivateMethodGet(this,g,A).call(this,e)}}static get defaultPropertiesToUpdate(){return[[a.AnnotationEditorParamsType.FREETEXT_SIZE,b._defaultFontSize],[a.AnnotationEditorParamsType.FREETEXT_COLOR,b._defaultColor||r.AnnotationEditor._defaultLineColor]]}get propertiesToUpdate(){return[[a.AnnotationEditorParamsType.FREETEXT_SIZE,_classPrivateFieldGet(this,f)],[a.AnnotationEditorParamsType.FREETEXT_COLOR,_classPrivateFieldGet(this,d)]]}getInitialTranslation(){return[-b._internalPadding*this.parent.scaleFactor,-(b._internalPadding+_classPrivateFieldGet(this,f))*this.parent.scaleFactor]}rebuild(){super.rebuild(),null!==this.div&&(this.isAttachedToDOM||this.parent.add(this))}enableEditMode(){this.isInEditMode()||(this.parent.setEditingState(!1),this.parent.updateToolbar(a.AnnotationEditorType.FREETEXT),super.enableEditMode(),this.overlayDiv.classList.remove("enabled"),this.editorDiv.contentEditable=!0,this.div.draggable=!1,this.editorDiv.addEventListener("keydown",_classPrivateFieldGet(this,c)),this.editorDiv.addEventListener("focus",_classPrivateFieldGet(this,l)),this.editorDiv.addEventListener("blur",_classPrivateFieldGet(this,o)))}disableEditMode(){this.isInEditMode()&&(this.parent.setEditingState(!0),super.disableEditMode(),this.overlayDiv.classList.add("enabled"),this.editorDiv.contentEditable=!1,this.div.draggable=!0,this.editorDiv.removeEventListener("keydown",_classPrivateFieldGet(this,c)),this.editorDiv.removeEventListener("focus",_classPrivateFieldGet(this,l)),this.editorDiv.removeEventListener("blur",_classPrivateFieldGet(this,o)),this.div.focus(),this.isEditing=!1)}focusin(t){super.focusin(t),t.target!==this.editorDiv&&this.editorDiv.focus()}onceAdded(){this.width||(this.enableEditMode(),this.editorDiv.focus())}isEmpty(){return!this.editorDiv||""===this.editorDiv.innerText.trim()}remove(){this.isEditing=!1,this.parent.setEditingState(!0),super.remove()}commit(){super.commit(),_classPrivateFieldGet(this,p)||(_classPrivateFieldSet(this,p,!0),this.parent.addUndoableEditor(this)),this.disableEditMode(),_classPrivateFieldSet(this,u,this.editorDiv.innerHTML),_classPrivateFieldSet(this,h,_classPrivateMethodGet(this,v,P).call(this).trimEnd()),_classPrivateMethodGet(this,_,S).call(this)}shouldGetKeyboardEvents(){return this.isInEditMode()}dblclick(t){this.enableEditMode(),this.editorDiv.focus()}keydown(t){t.target===this.div&&"Enter"===t.key&&(this.enableEditMode(),this.editorDiv.focus())}editorDivKeydown(t){b._keyboardManager.exec(this,t)}editorDivFocus(t){this.isEditing=!0}editorDivBlur(t){this.isEditing=!1}disableEditing(){this.editorDiv.setAttribute("role","comment"),this.editorDiv.removeAttribute("aria-multiline")}enableEditing(){this.editorDiv.setAttribute("role","textbox"),this.editorDiv.setAttribute("aria-multiline",!0)}getIdForTextLayer(){return this.editorDiv.id}render(){if(this.div)return this.div;let t,e;this.width&&(t=this.x,e=this.y),super.render(),this.editorDiv=document.createElement("div"),this.editorDiv.className="internal",this.editorDiv.setAttribute("id","".concat(this.id,"-editor")),this.enableEditing(),b._l10nPromise.get("editor_free_text_aria_label").then((t=>{var e;return null===(e=this.editorDiv)||void 0===e?void 0:e.setAttribute("aria-label",t)})),b._l10nPromise.get("free_text_default_content").then((t=>{var e;return null===(e=this.editorDiv)||void 0===e?void 0:e.setAttribute("default-content",t)})),this.editorDiv.contentEditable=!0;const{style:s}=this.editorDiv;if(s.fontSize="calc(".concat(_classPrivateFieldGet(this,f),"px * var(--scale-factor))"),s.color=_classPrivateFieldGet(this,d),this.div.append(this.editorDiv),this.overlayDiv=document.createElement("div"),this.overlayDiv.classList.add("overlay","enabled"),this.div.append(this.overlayDiv),(0,n.bindEvents)(this,this.div,["dblclick","keydown"]),this.width){const[s,i]=this.parent.viewportBaseDimensions;this.setAt(t*s,e*i,this.width*s,this.height*i),this.editorDiv.innerHTML=_classPrivateFieldGet(this,u),this.div.draggable=!0,this.editorDiv.contentEditable=!1}else this.div.draggable=!1,this.editorDiv.contentEditable=!0;return this.div}get contentDiv(){return this.editorDiv}static deserialize(t,e){const s=super.deserialize(t,e);return _classPrivateFieldSet(s,f,t.fontSize),_classPrivateFieldSet(s,d,a.Util.makeHexColor(...t.color)),_classPrivateFieldSet(s,h,t.value),_classPrivateFieldSet(s,u,t.value.split("\n").map((t=>"<div>".concat(t,"</div>"))).join("")),s}serialize(){if(this.isEmpty())return null;const t=b._internalPadding*this.parent.scaleFactor,e=this.getRect(t,t),s=r.AnnotationEditor._colorManager.convert(getComputedStyle(this.editorDiv).color);return{annotationType:a.AnnotationEditorType.FREETEXT,color:s,fontSize:_classPrivateFieldGet(this,f),value:_classPrivateFieldGet(this,h),pageIndex:this.parent.pageIndex,rect:e,rotation:this.rotation}}}function y(t){const e=t=>{this.editorDiv.style.fontSize="calc(".concat(t,"px * var(--scale-factor))"),this.translate(0,-(t-_classPrivateFieldGet(this,f))*this.parent.scaleFactor),_classPrivateFieldSet(this,f,t),_classPrivateMethodGet(this,_,S).call(this)},s=_classPrivateFieldGet(this,f);this.parent.addCommands({cmd:()=>{e(t)},undo:()=>{e(s)},mustExec:!0,type:a.AnnotationEditorParamsType.FREETEXT_SIZE,overwriteIfSameType:!0,keepUndo:!0})}function A(t){const e=_classPrivateFieldGet(this,d);this.parent.addCommands({cmd:()=>{_classPrivateFieldSet(this,d,t),this.editorDiv.style.color=t},undo:()=>{_classPrivateFieldSet(this,d,e),this.editorDiv.style.color=e},mustExec:!0,type:a.AnnotationEditorParamsType.FREETEXT_COLOR,overwriteIfSameType:!0,keepUndo:!0})}function P(){const t=this.editorDiv.getElementsByTagName("div");if(0===t.length)return this.editorDiv.innerText;const e=[];for(let s=0,i=t.length;s<i;s++){const i=t[s].firstChild;"#text"===(null===i||void 0===i?void 0:i.nodeName)?e.push(i.data):e.push("")}return e.join("\n")}function S(){const[t,e]=this.parent.viewportBaseDimensions,s=this.div.getBoundingClientRect();this.width=s.width/t,this.height=s.height/e}i=b,_defineProperty(b,"_freeTextDefaultContent",""),_defineProperty(b,"_l10nPromise",void 0),_defineProperty(b,"_internalPadding",0),_defineProperty(b,"_defaultColor",null),_defineProperty(b,"_defaultFontSize",10),_defineProperty(b,"_keyboardManager",new n.KeyboardManager([[["ctrl+Enter","mac+meta+Enter","Escape","mac+Escape"],i.prototype.commitOrRemove]])),e.FreeTextEditor=b},(t,e,s)=>{var i;Object.defineProperty(e,"__esModule",{value:!0}),e.InkEditor=void 0,Object.defineProperty(e,"fitCurve",{enumerable:!0,get:function(){return r.fitCurve}});var a=s(1),n=s(8),r=s(25),o=s(9);const l=16;var c=new WeakMap,d=new WeakMap,h=new WeakMap,u=new WeakMap,p=new WeakMap,f=new WeakMap,m=new WeakMap,g=new WeakMap,v=new WeakMap,_=new WeakMap,b=new WeakMap,y=new WeakMap,A=new WeakMap,P=new WeakMap,S=new WeakSet,x=new WeakSet,F=new WeakSet,w=new WeakSet,C=new WeakSet,M=new WeakSet,k=new WeakSet,E=new WeakSet,T=new WeakSet,R=new WeakSet,O=new WeakSet,I=new WeakSet,D=new WeakSet,L=new WeakSet,N=new WeakSet,G=new WeakSet,j=new WeakSet,W=new WeakSet,U=new WeakSet,B=new WeakSet,q=new WeakSet,z=new WeakSet;class H extends n.AnnotationEditor{constructor(t){super(_objectSpread(_objectSpread({},t),{},{name:"inkEditor"})),_classPrivateMethodInitSpec(this,z),_classPrivateMethodInitSpec(this,q),_classPrivateMethodInitSpec(this,B),_classPrivateMethodInitSpec(this,U),_classPrivateMethodInitSpec(this,W),_classPrivateMethodInitSpec(this,j),_classPrivateMethodInitSpec(this,G),_classPrivateMethodInitSpec(this,N),_classPrivateMethodInitSpec(this,L),_classPrivateMethodInitSpec(this,D),_classPrivateMethodInitSpec(this,I),_classPrivateMethodInitSpec(this,O),_classPrivateMethodInitSpec(this,R),_classPrivateMethodInitSpec(this,T),_classPrivateMethodInitSpec(this,E),_classPrivateMethodInitSpec(this,k),_classPrivateMethodInitSpec(this,M),_classPrivateMethodInitSpec(this,C),_classPrivateMethodInitSpec(this,w),_classPrivateMethodInitSpec(this,F),_classPrivateMethodInitSpec(this,x),_classPrivateMethodInitSpec(this,S),_classPrivateFieldInitSpec(this,c,{writable:!0,value:0}),_classPrivateFieldInitSpec(this,d,{writable:!0,value:0}),_classPrivateFieldInitSpec(this,h,{writable:!0,value:0}),_classPrivateFieldInitSpec(this,u,{writable:!0,value:this.canvasPointermove.bind(this)}),_classPrivateFieldInitSpec(this,p,{writable:!0,value:this.canvasPointerleave.bind(this)}),_classPrivateFieldInitSpec(this,f,{writable:!0,value:this.canvasPointerup.bind(this)}),_classPrivateFieldInitSpec(this,m,{writable:!0,value:this.canvasPointerdown.bind(this)}),_classPrivateFieldInitSpec(this,g,{writable:!0,value:!1}),_classPrivateFieldInitSpec(this,v,{writable:!0,value:!1}),_classPrivateFieldInitSpec(this,_,{writable:!0,value:null}),_classPrivateFieldInitSpec(this,b,{writable:!0,value:null}),_classPrivateFieldInitSpec(this,y,{writable:!0,value:0}),_classPrivateFieldInitSpec(this,A,{writable:!0,value:0}),_classPrivateFieldInitSpec(this,P,{writable:!0,value:null}),this.color=t.color||null,this.thickness=t.thickness||null,this.opacity=t.opacity||null,this.paths=[],this.bezierPath2D=[],this.currentPath=[],this.scaleFactor=1,this.translationX=this.translationY=0,this.x=0,this.y=0}static initialize(t){this._l10nPromise=new Map(["editor_ink_canvas_aria_label","editor_ink_aria_label"].map((e=>[e,t.get(e)])))}static updateDefaultParams(t,e){switch(t){case a.AnnotationEditorParamsType.INK_THICKNESS:H._defaultThickness=e;break;case a.AnnotationEditorParamsType.INK_COLOR:H._defaultColor=e;break;case a.AnnotationEditorParamsType.INK_OPACITY:H._defaultOpacity=e/100}}updateParams(t,e){switch(t){case a.AnnotationEditorParamsType.INK_THICKNESS:_classPrivateMethodGet(this,S,V).call(this,e);break;case a.AnnotationEditorParamsType.INK_COLOR:_classPrivateMethodGet(this,x,X).call(this,e);break;case a.AnnotationEditorParamsType.INK_OPACITY:_classPrivateMethodGet(this,F,Y).call(this,e)}}static get defaultPropertiesToUpdate(){return[[a.AnnotationEditorParamsType.INK_THICKNESS,H._defaultThickness],[a.AnnotationEditorParamsType.INK_COLOR,H._defaultColor||n.AnnotationEditor._defaultLineColor],[a.AnnotationEditorParamsType.INK_OPACITY,Math.round(100*H._defaultOpacity)]]}get propertiesToUpdate(){var t;return[[a.AnnotationEditorParamsType.INK_THICKNESS,this.thickness||H._defaultThickness],[a.AnnotationEditorParamsType.INK_COLOR,this.color||H._defaultColor||n.AnnotationEditor._defaultLineColor],[a.AnnotationEditorParamsType.INK_OPACITY,Math.round(100*(null!==(t=this.opacity)&&void 0!==t?t:H._defaultOpacity))]]}rebuild(){super.rebuild(),null!==this.div&&(this.canvas||(_classPrivateMethodGet(this,O,st).call(this),_classPrivateMethodGet(this,I,it).call(this)),this.isAttachedToDOM||(this.parent.add(this),_classPrivateMethodGet(this,D,at).call(this)),_classPrivateMethodGet(this,q,pt).call(this))}remove(){null!==this.canvas&&(this.isEmpty()||this.commit(),this.canvas.width=this.canvas.height=0,this.canvas.remove(),this.canvas=null,_classPrivateFieldGet(this,b).disconnect(),_classPrivateFieldSet(this,b,null),super.remove())}enableEditMode(){_classPrivateFieldGet(this,g)||null===this.canvas||(super.enableEditMode(),this.div.draggable=!1,this.canvas.addEventListener("pointerdown",_classPrivateFieldGet(this,m)),this.canvas.addEventListener("pointerup",_classPrivateFieldGet(this,f)))}disableEditMode(){this.isInEditMode()&&null!==this.canvas&&(super.disableEditMode(),this.div.draggable=!this.isEmpty(),this.div.classList.remove("editing"),this.canvas.removeEventListener("pointerdown",_classPrivateFieldGet(this,m)),this.canvas.removeEventListener("pointerup",_classPrivateFieldGet(this,f)))}onceAdded(){this.div.draggable=!this.isEmpty()}isEmpty(){return 0===this.paths.length||1===this.paths.length&&0===this.paths[0].length}commit(){_classPrivateFieldGet(this,g)||(super.commit(),this.isEditing=!1,this.disableEditMode(),this.setInForeground(),_classPrivateFieldSet(this,g,!0),this.div.classList.add("disabled"),_classPrivateMethodGet(this,q,pt).call(this,!0),this.parent.addInkEditorIfNeeded(!0),this.parent.moveDivInDOM(this),this.div.focus())}focusin(t){super.focusin(t),this.enableEditMode()}canvasPointerdown(t){0===t.button&&this.isInEditMode()&&!_classPrivateFieldGet(this,g)&&(this.setInForeground(),"mouse"!==t.type&&this.div.focus(),t.stopPropagation(),this.canvas.addEventListener("pointerleave",_classPrivateFieldGet(this,p)),this.canvas.addEventListener("pointermove",_classPrivateFieldGet(this,u)),_classPrivateMethodGet(this,M,Q).call(this,t.offsetX,t.offsetY))}canvasPointermove(t){t.stopPropagation(),_classPrivateMethodGet(this,k,Z).call(this,t.offsetX,t.offsetY)}canvasPointerup(t){0===t.button&&this.isInEditMode()&&0!==this.currentPath.length&&(t.stopPropagation(),_classPrivateMethodGet(this,R,et).call(this,t),this.setInBackground())}canvasPointerleave(t){_classPrivateMethodGet(this,R,et).call(this,t),this.setInBackground()}render(){if(this.div)return this.div;let t,e;this.width&&(t=this.x,e=this.y),super.render(),H._l10nPromise.get("editor_ink_aria_label").then((t=>{var e;return null===(e=this.div)||void 0===e?void 0:e.setAttribute("aria-label",t)}));const[s,i,a,n]=_classPrivateMethodGet(this,w,K).call(this);if(this.setAt(s,i,0,0),this.setDims(a,n),_classPrivateMethodGet(this,O,st).call(this),this.width){const[s,i]=this.parent.viewportBaseDimensions;this.setAt(t*s,e*i,this.width*s,this.height*i),_classPrivateFieldSet(this,v,!0),_classPrivateMethodGet(this,D,at).call(this),this.setDims(this.width*s,this.height*i),_classPrivateMethodGet(this,T,tt).call(this),_classPrivateMethodGet(this,z,ft).call(this),this.div.classList.add("disabled")}else this.div.classList.add("editing"),this.enableEditMode();return _classPrivateMethodGet(this,I,it).call(this),this.div}setDimensions(t,e){const s=Math.round(t),i=Math.round(e);if(_classPrivateFieldGet(this,y)===s&&_classPrivateFieldGet(this,A)===i)return;_classPrivateFieldSet(this,y,s),_classPrivateFieldSet(this,A,i),this.canvas.style.visibility="hidden",_classPrivateFieldGet(this,c)&&Math.abs(_classPrivateFieldGet(this,c)-t/e)>.01&&(e=Math.ceil(t/_classPrivateFieldGet(this,c)),this.setDims(t,e));const[a,n]=this.parent.viewportBaseDimensions;this.width=t/a,this.height=e/n,_classPrivateFieldGet(this,g)&&_classPrivateMethodGet(this,L,nt).call(this,t,e),_classPrivateMethodGet(this,D,at).call(this),_classPrivateMethodGet(this,T,tt).call(this),this.canvas.style.visibility="visible"}static deserialize(t,e){const s=super.deserialize(t,e);s.thickness=t.thickness,s.color=a.Util.makeHexColor(...t.color),s.opacity=t.opacity;const[i,n]=e.pageDimensions,r=s.width*i,o=s.height*n,l=e.scaleFactor,u=t.thickness/2;_classPrivateFieldSet(s,c,r/o),_classPrivateFieldSet(s,g,!0),_classPrivateFieldSet(s,y,Math.round(r)),_classPrivateFieldSet(s,A,Math.round(o));for(const{bezier:a}of t.paths){const t=[];s.paths.push(t);let e=l*(a[0]-u),i=l*(o-a[1]-u);for(let s=2,r=a.length;s<r;s+=6){const n=l*(a[s]-u),r=l*(o-a[s+1]-u),c=l*(a[s+2]-u),d=l*(o-a[s+3]-u),h=l*(a[s+4]-u),p=l*(o-a[s+5]-u);t.push([[e,i],[n,r],[c,d],[h,p]]),e=h,i=p}const n=_classStaticPrivateMethodGet(this,H,ot).call(this,t);s.bezierPath2D.push(n)}const p=_classPrivateMethodGet(s,U,ht).call(s);return _classPrivateFieldSet(s,h,p[2]-p[0]),_classPrivateFieldSet(s,d,p[3]-p[1]),_classPrivateMethodGet(s,L,nt).call(s,r,o),s}serialize(){if(this.isEmpty())return null;const t=this.getRect(0,0),e=this.rotation%180===0?t[3]-t[1]:t[2]-t[0],s=n.AnnotationEditor._colorManager.convert(this.ctx.strokeStyle);return{annotationType:a.AnnotationEditorType.INK,color:s,thickness:this.thickness,opacity:this.opacity,paths:_classPrivateMethodGet(this,G,lt).call(this,this.scaleFactor/this.parent.scaleFactor,this.translationX,this.translationY,e),pageIndex:this.parent.pageIndex,rect:t,rotation:this.rotation}}}function V(t){const e=this.thickness;this.parent.addCommands({cmd:()=>{this.thickness=t,_classPrivateMethodGet(this,q,pt).call(this)},undo:()=>{this.thickness=e,_classPrivateMethodGet(this,q,pt).call(this)},mustExec:!0,type:a.AnnotationEditorParamsType.INK_THICKNESS,overwriteIfSameType:!0,keepUndo:!0})}function X(t){const e=this.color;this.parent.addCommands({cmd:()=>{this.color=t,_classPrivateMethodGet(this,T,tt).call(this)},undo:()=>{this.color=e,_classPrivateMethodGet(this,T,tt).call(this)},mustExec:!0,type:a.AnnotationEditorParamsType.INK_COLOR,overwriteIfSameType:!0,keepUndo:!0})}function Y(t){t/=100;const e=this.opacity;this.parent.addCommands({cmd:()=>{this.opacity=t,_classPrivateMethodGet(this,T,tt).call(this)},undo:()=>{this.opacity=e,_classPrivateMethodGet(this,T,tt).call(this)},mustExec:!0,type:a.AnnotationEditorParamsType.INK_OPACITY,overwriteIfSameType:!0,keepUndo:!0})}function K(){const{width:t,height:e,rotation:s}=this.parent.viewport;switch(s){case 90:return[0,t,t,e];case 180:return[t,e,t,e];case 270:return[e,0,t,e];default:return[0,0,t,e]}}function J(){this.ctx.lineWidth=this.thickness*this.parent.scaleFactor/this.scaleFactor,this.ctx.lineCap="round",this.ctx.lineJoin="round",this.ctx.miterLimit=10,this.ctx.strokeStyle="".concat(this.color).concat((0,o.opacityToHex)(this.opacity))}function Q(t,e){var s;this.isEditing=!0,_classPrivateFieldGet(this,v)||(_classPrivateFieldSet(this,v,!0),_classPrivateMethodGet(this,D,at).call(this),this.thickness||(this.thickness=i._defaultThickness),this.color||(this.color=i._defaultColor||n.AnnotationEditor._defaultLineColor),null!==(s=this.opacity)&&void 0!==s||(this.opacity=i._defaultOpacity)),this.currentPath.push([t,e]),_classPrivateFieldSet(this,_,null),_classPrivateMethodGet(this,C,J).call(this),this.ctx.beginPath(),this.ctx.moveTo(t,e),_classPrivateFieldSet(this,P,(()=>{_classPrivateFieldGet(this,P)&&(_classPrivateFieldGet(this,_)&&(this.isEmpty()?(this.ctx.setTransform(1,0,0,1,0,0),this.ctx.clearRect(0,0,this.canvas.width,this.canvas.height)):_classPrivateMethodGet(this,T,tt).call(this),this.ctx.lineTo(..._classPrivateFieldGet(this,_)),_classPrivateFieldSet(this,_,null),this.ctx.stroke()),window.requestAnimationFrame(_classPrivateFieldGet(this,P)))})),window.requestAnimationFrame(_classPrivateFieldGet(this,P))}function Z(t,e){const[s,i]=this.currentPath.at(-1);t===s&&e===i||(this.currentPath.push([t,e]),_classPrivateFieldSet(this,_,[t,e]))}function $(t,e){this.ctx.closePath(),_classPrivateFieldSet(this,P,null),t=Math.min(Math.max(t,0),this.canvas.width),e=Math.min(Math.max(e,0),this.canvas.height);const[s,a]=this.currentPath.at(-1);let n;if(t===s&&e===a||this.currentPath.push([t,e]),1!==this.currentPath.length)n=(0,r.fitCurve)(this.currentPath,30,null);else{const s=[t,e];n=[[s,s.slice(),s.slice(),s]]}const o=_classStaticPrivateMethodGet(i,i,ot).call(i,n);this.currentPath.length=0,this.parent.addCommands({cmd:()=>{this.paths.push(n),this.bezierPath2D.push(o),this.rebuild()},undo:()=>{this.paths.pop(),this.bezierPath2D.pop(),0===this.paths.length?this.remove():(this.canvas||(_classPrivateMethodGet(this,O,st).call(this),_classPrivateMethodGet(this,I,it).call(this)),_classPrivateMethodGet(this,q,pt).call(this))},mustExec:!0})}function tt(){if(this.isEmpty())return void _classPrivateMethodGet(this,N,rt).call(this);_classPrivateMethodGet(this,C,J).call(this);const{canvas:t,ctx:e}=this;e.setTransform(1,0,0,1,0,0),e.clearRect(0,0,t.width,t.height),_classPrivateMethodGet(this,N,rt).call(this);for(const s of this.bezierPath2D)e.stroke(s)}function et(t){_classPrivateMethodGet(this,E,$).call(this,t.offsetX,t.offsetY),this.canvas.removeEventListener("pointerleave",_classPrivateFieldGet(this,p)),this.canvas.removeEventListener("pointermove",_classPrivateFieldGet(this,u)),this.parent.addToAnnotationStorage(this)}function st(){this.canvas=document.createElement("canvas"),this.canvas.width=this.canvas.height=0,this.canvas.className="inkEditorCanvas",i._l10nPromise.get("editor_ink_canvas_aria_label").then((t=>{var e;return null===(e=this.canvas)||void 0===e?void 0:e.setAttribute("aria-label",t)})),this.div.append(this.canvas),this.ctx=this.canvas.getContext("2d")}function it(){_classPrivateFieldSet(this,b,new ResizeObserver((t=>{const e=t[0].contentRect;e.width&&e.height&&this.setDimensions(e.width,e.height)}))),_classPrivateFieldGet(this,b).observe(this.div)}function at(){if(!_classPrivateFieldGet(this,v))return;const[t,e]=this.parent.viewportBaseDimensions;this.canvas.width=Math.ceil(this.width*t),this.canvas.height=Math.ceil(this.height*e),_classPrivateMethodGet(this,N,rt).call(this)}function nt(t,e){const s=_classPrivateMethodGet(this,B,ut).call(this),i=(t-s)/_classPrivateFieldGet(this,h),a=(e-s)/_classPrivateFieldGet(this,d);this.scaleFactor=Math.min(i,a)}function rt(){const t=_classPrivateMethodGet(this,B,ut).call(this)/2;this.ctx.setTransform(this.scaleFactor,0,0,this.scaleFactor,this.translationX*this.scaleFactor+t,this.translationY*this.scaleFactor+t)}function ot(t){const e=new Path2D;for(let s=0,i=t.length;s<i;s++){const[i,a,n,r]=t[s];0===s&&e.moveTo(...i),e.bezierCurveTo(a[0],a[1],n[0],n[1],r[0],r[1])}return e}function lt(t,e,s,i){const a=[],n=this.thickness/2;let r,o;for(const l of this.paths){r=[],o=[];for(let a=0,c=l.length;a<c;a++){const[c,d,h,u]=l[a],p=t*(c[0]+e)+n,f=i-t*(c[1]+s)-n,m=t*(d[0]+e)+n,g=i-t*(d[1]+s)-n,v=t*(h[0]+e)+n,_=i-t*(h[1]+s)-n,b=t*(u[0]+e)+n,y=i-t*(u[1]+s)-n;0===a&&(r.push(p,f),o.push(p,f)),r.push(m,g,v,_,b,y),_classPrivateMethodGet(this,j,ct).call(this,p,f,m,g,v,_,b,y,4,o)}a.push({bezier:r,points:o})}return a}function ct(t,e,s,i,a,n,r,o,l,c){if(_classPrivateMethodGet(this,W,dt).call(this,t,e,s,i,a,n,r,o))c.push(r,o);else{for(let d=1;d<l-1;d++){const h=d/l,u=1-h;let p=h*t+u*s,f=h*e+u*i,m=h*s+u*a,g=h*i+u*n;p=h*p+u*m,f=h*f+u*g,m=h*m+u*(h*a+u*r),g=h*g+u*(h*n+u*o),p=h*p+u*m,f=h*f+u*g,c.push(p,f)}c.push(r,o)}}function dt(t,e,s,i,a,n,r,o){const l=(3*s-2*t-r)**2,c=(3*i-2*e-o)**2,d=(3*a-t-2*r)**2,h=(3*n-e-2*o)**2;return Math.max(l,d)+Math.max(c,h)<=10}function ht(){let t=1/0,e=-1/0,s=1/0,i=-1/0;for(const n of this.paths)for(const[r,o,l,c]of n){const n=a.Util.bezierBoundingBox(...r,...o,...l,...c);t=Math.min(t,n[0]),s=Math.min(s,n[1]),e=Math.max(e,n[2]),i=Math.max(i,n[3])}return[t,s,e,i]}function ut(){return _classPrivateFieldGet(this,g)?Math.ceil(this.thickness*this.parent.scaleFactor):0}function pt(){let t=arguments.length>0&&void 0!==arguments[0]&&arguments[0];if(this.isEmpty())return;if(!_classPrivateFieldGet(this,g))return void _classPrivateMethodGet(this,T,tt).call(this);const e=_classPrivateMethodGet(this,U,ht).call(this),s=_classPrivateMethodGet(this,B,ut).call(this);_classPrivateFieldSet(this,h,Math.max(l,e[2]-e[0])),_classPrivateFieldSet(this,d,Math.max(l,e[3]-e[1]));const i=Math.ceil(s+_classPrivateFieldGet(this,h)*this.scaleFactor),a=Math.ceil(s+_classPrivateFieldGet(this,d)*this.scaleFactor),[n,r]=this.parent.viewportBaseDimensions;this.width=i/n,this.height=a/r,_classPrivateFieldSet(this,c,i/a),_classPrivateMethodGet(this,z,ft).call(this);const o=this.translationX,u=this.translationY;this.translationX=-e[0],this.translationY=-e[1],_classPrivateMethodGet(this,D,at).call(this),_classPrivateMethodGet(this,T,tt).call(this),_classPrivateFieldSet(this,y,i),_classPrivateFieldSet(this,A,a),this.setDims(i,a);const p=t?s/this.scaleFactor/2:0;this.translate(o-this.translationX-p,u-this.translationY-p)}function ft(){const{style:t}=this.div;_classPrivateFieldGet(this,c)>=1?(t.minHeight="".concat(l,"px"),t.minWidth="".concat(Math.round(_classPrivateFieldGet(this,c)*l),"px")):(t.minWidth="".concat(l,"px"),t.minHeight="".concat(Math.round(l/_classPrivateFieldGet(this,c)),"px"))}i=H,_defineProperty(H,"_defaultColor",null),_defineProperty(H,"_defaultOpacity",1),_defineProperty(H,"_defaultThickness",1),_defineProperty(H,"_l10nPromise",void 0),e.InkEditor=H},(t,e,s)=>{Object.defineProperty(e,"__esModule",{value:!0}),e.fitCurve=void 0;const i=s(26);e.fitCurve=i},t=>{function e(t,a,n,r,o){var c,d,h,u,p,f,m,g,v,_,b,y,A;if(2===t.length)return y=l.vectorLen(l.subtract(t[0],t[1]))/3,[c=[t[0],l.addArrays(t[0],l.mulItems(a,y)),l.addArrays(t[1],l.mulItems(n,y)),t[1]]];if(d=function(t){var e,s,i,a=[];return t.forEach(((t,n)=>{e=n?s+l.vectorLen(l.subtract(t,i)):0,a.push(e),s=e,i=t})),a=a.map((t=>t/s))}(t),[c,u,f]=s(t,d,d,a,n,o),0===u||u<r)return[c];if(u<r*r)for(h=d,p=u,m=f,A=0;A<20;A++){if(h=i(c,t,h),[c,u,f]=s(t,d,h,a,n,o),u<r)return[c];if(f===m){let t=u/p;if(t>.9999&&t<1.0001)break}p=u,m=f}return b=[],(g=l.subtract(t[f-1],t[f+1])).every((t=>0===t))&&(g=l.subtract(t[f-1],t[f]),[g[0],g[1]]=[-g[1],g[0]]),v=l.normalize(g),_=l.mulItems(v,-1),b=(b=b.concat(e(t.slice(0,f+1),a,v,r,o))).concat(e(t.slice(f),_,n,r,o))}function s(t,e,s,i,a,o){var d,h,u;return d=function(t,e,s,i){var a,n,r,o,d,h,u,p,f,m,g,v,_,b,y,A,P,S=t[0],x=t[t.length-1];for(a=[S,null,null,x],n=l.zeros_Xx2x2(e.length),_=0,b=e.length;_<b;_++)P=1-(A=e[_]),(r=n[_])[0]=l.mulItems(s,3*A*(P*P)),r[1]=l.mulItems(i,3*P*(A*A));for(o=[[0,0],[0,0]],d=[0,0],_=0,b=t.length;_<b;_++)A=e[_],r=n[_],o[0][0]+=l.dot(r[0],r[0]),o[0][1]+=l.dot(r[0],r[1]),o[1][0]+=l.dot(r[0],r[1]),o[1][1]+=l.dot(r[1],r[1]),y=l.subtract(t[_],c.q([S,S,x,x],A)),d[0]+=l.dot(r[0],y),d[1]+=l.dot(r[1],y);return h=o[0][0]*o[1][1]-o[1][0]*o[0][1],u=o[0][0]*d[1]-o[1][0]*d[0],p=d[0]*o[1][1]-d[1]*o[0][1],f=0===h?0:p/h,m=0===h?0:u/h,v=l.vectorLen(l.subtract(S,x)),f<(g=1e-6*v)||m<g?(a[1]=l.addArrays(S,l.mulItems(s,v/3)),a[2]=l.addArrays(x,l.mulItems(i,v/3))):(a[1]=l.addArrays(S,l.mulItems(s,f)),a[2]=l.addArrays(x,l.mulItems(i,m))),a}(t,s,i,a),[h,u]=function(t,e,s){var i,a,o,d,h,u,p,f;a=0,o=Math.floor(t.length/2);const m=n(e,10);for(h=0,u=t.length;h<u;h++)p=t[h],f=r(0,s[h],m,10),(i=(d=l.subtract(c.q(e,f),p))[0]*d[0]+d[1]*d[1])>a&&(a=i,o=h);return[a,o]}(t,d,e),o&&o({bez:d,points:t,params:e,maxErr:h,maxPoint:u}),[d,h,u]}function i(t,e,s){return s.map(((s,i)=>a(t,e[i],s)))}function a(t,e,s){var i=l.subtract(c.q(t,s),e),a=c.qprime(t,s),n=l.mulMatrix(i,a),r=l.sum(l.squareItems(a))+2*l.mulMatrix(i,c.qprimeprime(t,s));return 0===r?s:s-n/r}var n=function(t,e){for(var s,i=[0],a=t[0],n=0,r=1;r<=e;r++)s=c.q(t,r/e),n+=l.vectorLen(l.subtract(s,a)),i.push(n),a=s;return i=i.map((t=>t/n))};function r(t,e,s,i){if(e<0)return 0;if(e>1)return 1;for(var a,n,r,o,l=1;l<=i;l++)if(e<=s[l]){r=(l-1)/i,n=l/i,o=(e-(a=s[l-1]))/(s[l]-a)*(n-r)+r;break}return o}function o(t,e){return l.normalize(l.subtract(t,e))}class l{static zeros_Xx2x2(t){for(var e=[];t--;)e.push([0,0]);return e}static mulItems(t,e){return t.map((t=>t*e))}static mulMatrix(t,e){return t.reduce(((t,s,i)=>t+s*e[i]),0)}static subtract(t,e){return t.map(((t,s)=>t-e[s]))}static addArrays(t,e){return t.map(((t,s)=>t+e[s]))}static addItems(t,e){return t.map((t=>t+e))}static sum(t){return t.reduce(((t,e)=>t+e))}static dot(t,e){return l.mulMatrix(t,e)}static vectorLen(t){return Math.hypot(...t)}static divItems(t,e){return t.map((t=>t/e))}static squareItems(t){return t.map((t=>t*t))}static normalize(t){return this.divItems(t,this.vectorLen(t))}}class c{static q(t,e){var s=1-e,i=l.mulItems(t[0],s*s*s),a=l.mulItems(t[1],3*s*s*e),n=l.mulItems(t[2],3*s*e*e),r=l.mulItems(t[3],e*e*e);return l.addArrays(l.addArrays(i,a),l.addArrays(n,r))}static qprime(t,e){var s=1-e,i=l.mulItems(l.subtract(t[1],t[0]),3*s*s),a=l.mulItems(l.subtract(t[2],t[1]),6*s*e),n=l.mulItems(l.subtract(t[3],t[2]),3*e*e);return l.addArrays(l.addArrays(i,a),n)}static qprimeprime(t,e){return l.addArrays(l.mulItems(l.addArrays(l.subtract(t[2],l.mulItems(t[1],2)),t[0]),6*(1-e)),l.mulItems(l.addArrays(l.subtract(t[3],l.mulItems(t[2],2)),t[1]),6*e))}}t.exports=function(t,s,i){if(!Array.isArray(t))throw new TypeError("First argument should be an array");if(t.forEach((e=>{if(!Array.isArray(e)||e.some((t=>"number"!==typeof t))||e.length!==t[0].length)throw Error("Each point should be an array of numbers. Each point should have the same amount of numbers.")})),(t=t.filter(((e,s)=>0===s||!e.every(((e,i)=>e===t[s-1][i]))))).length<2)return[];const a=t.length,n=o(t[1],t[0]),r=o(t[a-2],t[a-1]);return e(t,n,r,s,i)},t.exports.fitCubic=e,t.exports.createTangent=o},(t,e,s)=>{Object.defineProperty(e,"__esModule",{value:!0}),e.AnnotationLayer=void 0;var i=s(1),a=s(4),n=s(7),r=s(28),o=s(29);const l=1e3,c=new WeakSet;function d(t){return{width:t[2]-t[0],height:t[3]-t[1]}}class h{static create(t){switch(t.data.annotationType){case i.AnnotationType.LINK:return new p(t);case i.AnnotationType.TEXT:return new f(t);case i.AnnotationType.WIDGET:switch(t.data.fieldType){case"Tx":return new g(t);case"Btn":return t.data.radioButton?new _(t):t.data.checkBox?new v(t):new b(t);case"Ch":return new y(t)}return new m(t);case i.AnnotationType.POPUP:return new A(t);case i.AnnotationType.FREETEXT:return new S(t);case i.AnnotationType.LINE:return new x(t);case i.AnnotationType.SQUARE:return new F(t);case i.AnnotationType.CIRCLE:return new w(t);case i.AnnotationType.POLYLINE:return new C(t);case i.AnnotationType.CARET:return new k(t);case i.AnnotationType.INK:return new E(t);case i.AnnotationType.POLYGON:return new M(t);case i.AnnotationType.HIGHLIGHT:return new T(t);case i.AnnotationType.UNDERLINE:return new R(t);case i.AnnotationType.SQUIGGLY:return new O(t);case i.AnnotationType.STRIKEOUT:return new I(t);case i.AnnotationType.STAMP:return new D(t);case i.AnnotationType.FILEATTACHMENT:return new L(t);default:return new u(t)}}}class u{constructor(t){let{isRenderable:e=!1,ignoreBorder:s=!1,createQuadrilaterals:i=!1}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};this.isRenderable=e,this.data=t.data,this.layer=t.layer,this.page=t.page,this.viewport=t.viewport,this.linkService=t.linkService,this.downloadManager=t.downloadManager,this.imageResourcesPath=t.imageResourcesPath,this.renderForms=t.renderForms,this.svgFactory=t.svgFactory,this.annotationStorage=t.annotationStorage,this.enableScripting=t.enableScripting,this.hasJSActions=t.hasJSActions,this._fieldObjects=t.fieldObjects,this._mouseState=t.mouseState,e&&(this.container=this._createContainer(s)),i&&(this.quadrilaterals=this._createQuadrilaterals(s))}_createContainer(){let t=arguments.length>0&&void 0!==arguments[0]&&arguments[0];const e=this.data,s=this.page,a=this.viewport,n=document.createElement("section"),{width:r,height:o}=d(e.rect),[l,c,h,u]=a.viewBox,p=h-l,f=u-c;n.setAttribute("data-annotation-id",e.id);const m=i.Util.normalizeRect([e.rect[0],s.view[3]-e.rect[1]+s.view[1],e.rect[2],s.view[3]-e.rect[3]+s.view[1]]);if(!t&&e.borderStyle.width>0){n.style.borderWidth="".concat(e.borderStyle.width,"px");const t=e.borderStyle.horizontalCornerRadius,s=e.borderStyle.verticalCornerRadius;if(t>0||s>0){const e="calc(".concat(t,"px * var(--scale-factor)) / calc(").concat(s,"px * var(--scale-factor))");n.style.borderRadius=e}switch(e.borderStyle.style){case i.AnnotationBorderStyleType.SOLID:n.style.borderStyle="solid";break;case i.AnnotationBorderStyleType.DASHED:n.style.borderStyle="dashed";break;case i.AnnotationBorderStyleType.BEVELED:(0,i.warn)("Unimplemented border style: beveled");break;case i.AnnotationBorderStyleType.INSET:(0,i.warn)("Unimplemented border style: inset");break;case i.AnnotationBorderStyleType.UNDERLINE:n.style.borderBottomStyle="solid"}const a=e.borderColor||null;a?n.style.borderColor=i.Util.makeHexColor(0|a[0],0|a[1],0|a[2]):n.style.borderWidth=0}n.style.left="".concat(100*(m[0]-l)/p,"%"),n.style.top="".concat(100*(m[1]-c)/f,"%");const{rotation:g}=e;return e.hasOwnCanvas||0===g?(n.style.width="".concat(100*r/p,"%"),n.style.height="".concat(100*o/f,"%")):this.setRotation(g,n),n}setRotation(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.container;const[s,i,a,n]=this.viewport.viewBox,r=a-s,o=n-i,{width:l,height:c}=d(this.data.rect);let h,u;t%180===0?(h=100*l/r,u=100*c/o):(h=100*c/r,u=100*l/o),e.style.width="".concat(h,"%"),e.style.height="".concat(u,"%"),e.setAttribute("data-main-rotation",(360-t)%360)}get _commonActions(){const t=(t,e,s)=>{const i=s.detail[t];s.target.style[e]=r.ColorConverters["".concat(i[0],"_HTML")](i.slice(1))};return(0,i.shadow)(this,"_commonActions",{display:t=>{const e=t.detail.display%2===1;this.container.style.visibility=e?"hidden":"visible",this.annotationStorage.setValue(this.data.id,{hidden:e,print:0===t.detail.display||3===t.detail.display})},print:t=>{this.annotationStorage.setValue(this.data.id,{print:t.detail.print})},hidden:t=>{this.container.style.visibility=t.detail.hidden?"hidden":"visible",this.annotationStorage.setValue(this.data.id,{hidden:t.detail.hidden})},focus:t=>{setTimeout((()=>t.target.focus({preventScroll:!1})),0)},userName:t=>{t.target.title=t.detail.userName},readonly:t=>{t.detail.readonly?t.target.setAttribute("readonly",""):t.target.removeAttribute("readonly")},required:t=>{this._setRequired(t.target,t.detail.required)},bgColor:e=>{t("bgColor","backgroundColor",e)},fillColor:e=>{t("fillColor","backgroundColor",e)},fgColor:e=>{t("fgColor","color",e)},textColor:e=>{t("textColor","color",e)},borderColor:e=>{t("borderColor","borderColor",e)},strokeColor:e=>{t("strokeColor","borderColor",e)},rotation:t=>{const e=t.detail.rotation;this.setRotation(e),this.annotationStorage.setValue(this.data.id,{rotation:e})}})}_dispatchEventFromSandbox(t,e){const s=this._commonActions;for(const i of Object.keys(e.detail)){const a=t[i]||s[i];a&&a(e)}}_setDefaultPropertiesFromJS(t){if(!this.enableScripting)return;const e=this.annotationStorage.getRawValue(this.data.id);if(!e)return;const s=this._commonActions;for(const[i,a]of Object.entries(e)){const n=s[i];n&&(n({detail:a,target:t}),delete e[i])}}_createQuadrilaterals(){let t=arguments.length>0&&void 0!==arguments[0]&&arguments[0];if(!this.data.quadPoints)return null;const e=[],s=this.data.rect;for(const i of this.data.quadPoints)this.data.rect=[i[2].x,i[2].y,i[1].x,i[1].y],e.push(this._createContainer(t));return this.data.rect=s,e}_createPopup(t,e){let s=this.container;this.quadrilaterals&&(t=t||this.quadrilaterals,s=this.quadrilaterals[0]),t||((t=document.createElement("div")).className="popupTriggerArea",s.append(t));const i=new P({container:s,trigger:t,color:e.color,titleObj:e.titleObj,modificationDate:e.modificationDate,contentsObj:e.contentsObj,richText:e.richText,hideWrapper:!0}).render();i.style.left="100%",s.append(i)}_renderQuadrilaterals(t){for(const e of this.quadrilaterals)e.className=t;return this.quadrilaterals}render(){(0,i.unreachable)("Abstract method `AnnotationElement.render` called")}_getElementsByName(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;const s=[];if(this._fieldObjects){const a=this._fieldObjects[t];if(a)for(const{page:t,id:n,exportValues:r}of a){if(-1===t)continue;if(n===e)continue;const a="string"===typeof r?r:null,o=document.querySelector('[data-element-id="'.concat(n,'"]'));!o||c.has(o)?s.push({id:n,exportValue:a,domElement:o}):(0,i.warn)("_getElementsByName - element not allowed: ".concat(n))}return s}for(const i of document.getElementsByName(t)){const{id:t,exportValue:a}=i;t!==e&&c.has(i)&&s.push({id:t,exportValue:a,domElement:i})}return s}static get platform(){const t="undefined"!==typeof navigator?navigator.platform:"";return(0,i.shadow)(this,"platform",{isWin:t.includes("Win"),isMac:t.includes("Mac")})}}class p extends u{constructor(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;super(t,{isRenderable:!0,ignoreBorder:!(null===e||void 0===e||!e.ignoreBorder),createQuadrilaterals:!0}),this.isTooltipOnly=t.data.isTooltipOnly}render(){const{data:t,linkService:e}=this,s=document.createElement("a");s.setAttribute("data-element-id",t.id);let i=!1;return t.url?(e.addLinkAttributes(s,t.url,t.newWindow),i=!0):t.action?(this._bindNamedAction(s,t.action),i=!0):t.dest?(this._bindLink(s,t.dest),i=!0):(t.actions&&(t.actions.Action||t.actions["Mouse Up"]||t.actions["Mouse Down"])&&this.enableScripting&&this.hasJSActions&&(this._bindJSAction(s,t),i=!0),t.resetForm?(this._bindResetFormAction(s,t.resetForm),i=!0):this.isTooltipOnly&&!i&&(this._bindLink(s,""),i=!0)),this.quadrilaterals?this._renderQuadrilaterals("linkAnnotation").map(((t,e)=>{const i=0===e?s:s.cloneNode();return t.append(i),t})):(this.container.className="linkAnnotation",i&&this.container.append(s),this.container)}_bindLink(t,e){t.href=this.linkService.getDestinationHash(e),t.onclick=()=>(e&&this.linkService.goToDestination(e),!1),(e||""===e)&&(t.className="internalLink")}_bindNamedAction(t,e){t.href=this.linkService.getAnchorUrl(""),t.onclick=()=>(this.linkService.executeNamedAction(e),!1),t.className="internalLink"}_bindJSAction(t,e){t.href=this.linkService.getAnchorUrl("");const s=new Map([["Action","onclick"],["Mouse Up","onmouseup"],["Mouse Down","onmousedown"]]);for(const i of Object.keys(e.actions)){const a=s.get(i);a&&(t[a]=()=>{var t;return null===(t=this.linkService.eventBus)||void 0===t||t.dispatch("dispatcheventinsandbox",{source:this,detail:{id:e.id,name:i}}),!1})}t.onclick||(t.onclick=()=>!1),t.className="internalLink"}_bindResetFormAction(t,e){const s=t.onclick;if(s||(t.href=this.linkService.getAnchorUrl("")),t.className="internalLink",!this._fieldObjects)return(0,i.warn)('_bindResetFormAction - "resetForm" action not supported, ensure that the `fieldObjects` parameter is provided.'),void(s||(t.onclick=()=>!1));t.onclick=()=>{s&&s();const{fields:t,refs:a,include:n}=e,r=[];if(0!==t.length||0!==a.length){const e=new Set(a);for(const s of t){const t=this._fieldObjects[s]||[];for(const{id:s}of t)e.add(s)}for(const t of Object.values(this._fieldObjects))for(const s of t)e.has(s.id)===n&&r.push(s)}else for(const e of Object.values(this._fieldObjects))r.push(...e);const o=this.annotationStorage,l=[];for(const e of r){const{id:t}=e;switch(l.push(t),e.type){case"text":{const s=e.defaultValue||"";o.setValue(t,{value:s});break}case"checkbox":case"radiobutton":{const s=e.defaultValue===e.exportValues;o.setValue(t,{value:s});break}case"combobox":case"listbox":{const s=e.defaultValue||"";o.setValue(t,{value:s});break}default:continue}const s=document.querySelector('[data-element-id="'.concat(t,'"]'));s&&(c.has(s)?s.dispatchEvent(new Event("resetform")):(0,i.warn)("_bindResetFormAction - element not allowed: ".concat(t)))}var d;return this.enableScripting&&(null===(d=this.linkService.eventBus)||void 0===d||d.dispatch("dispatcheventinsandbox",{source:this,detail:{id:"app",ids:l,name:"ResetForm"}})),!1}}}class f extends u{constructor(t){var e,s,i;super(t,{isRenderable:!!(t.data.hasPopup||null!==(e=t.data.titleObj)&&void 0!==e&&e.str||null!==(s=t.data.contentsObj)&&void 0!==s&&s.str||null!==(i=t.data.richText)&&void 0!==i&&i.str)})}render(){this.container.className="textAnnotation";const t=document.createElement("img");return t.src=this.imageResourcesPath+"annotation-"+this.data.name.toLowerCase()+".svg",t.alt="[{{type}} Annotation]",t.dataset.l10nId="text_annotation_type",t.dataset.l10nArgs=JSON.stringify({type:this.data.name}),this.data.hasPopup||this._createPopup(t,this.data),this.container.append(t),this.container}}class m extends u{render(){return this.data.alternativeText&&(this.container.title=this.data.alternativeText),this.container}_getKeyModifier(t){const{isWin:e,isMac:s}=u.platform;return e&&t.ctrlKey||s&&t.metaKey}_setEventListener(t,e,s,i){e.includes("mouse")?t.addEventListener(e,(t=>{var e;null===(e=this.linkService.eventBus)||void 0===e||e.dispatch("dispatcheventinsandbox",{source:this,detail:{id:this.data.id,name:s,value:i(t),shift:t.shiftKey,modifier:this._getKeyModifier(t)}})})):t.addEventListener(e,(t=>{var e;null===(e=this.linkService.eventBus)||void 0===e||e.dispatch("dispatcheventinsandbox",{source:this,detail:{id:this.data.id,name:s,value:i(t)}})}))}_setEventListeners(t,e,s){for(const[a,n]of e){var i;("Action"===n||null!==(i=this.data.actions)&&void 0!==i&&i[n])&&this._setEventListener(t,a,n,s)}}_setBackgroundColor(t){const e=this.data.backgroundColor||null;t.style.backgroundColor=null===e?"transparent":i.Util.makeHexColor(e[0],e[1],e[2])}_setTextStyle(t){const e=["left","center","right"],{fontColor:s}=this.data.defaultAppearanceData,a=this.data.defaultAppearanceData.fontSize||9,n=t.style;let r;if(this.data.multiLine){const t=Math.abs(this.data.rect[3]-this.data.rect[1]),e=t/(Math.round(t/(i.LINE_FACTOR*a))||1);r=Math.min(a,Math.round(e/i.LINE_FACTOR))}else{const t=Math.abs(this.data.rect[3]-this.data.rect[1]);r=Math.min(a,Math.round(t/i.LINE_FACTOR))}n.fontSize="calc(".concat(r,"px * var(--scale-factor))"),n.color=i.Util.makeHexColor(s[0],s[1],s[2]),null!==this.data.textAlignment&&(n.textAlign=e[this.data.textAlignment])}_setRequired(t,e){e?t.setAttribute("required",!0):t.removeAttribute("required"),t.setAttribute("aria-required",e)}}class g extends m{constructor(t){super(t,{isRenderable:t.renderForms||!t.data.hasAppearance&&!!t.data.fieldValue})}setPropertyOnSiblings(t,e,s,i){const a=this.annotationStorage;for(const n of this._getElementsByName(t.name,t.id))n.domElement&&(n.domElement[e]=s),a.setValue(n.id,{[i]:s})}render(){const t=this.annotationStorage,e=this.data.id;this.container.className="textWidgetAnnotation";let s=null;if(this.renderForms){const a=t.getValue(e,{value:this.data.fieldValue}),n=a.formattedValue||a.value||"",r={userValue:n,formattedValue:null,valueOnFocus:""};this.data.multiLine?(s=document.createElement("textarea"),s.textContent=n,this.data.doNotScroll&&(s.style.overflowY="hidden")):(s=document.createElement("input"),s.type="text",s.setAttribute("value",n),this.data.doNotScroll&&(s.style.overflowX="hidden")),c.add(s),s.setAttribute("data-element-id",e),s.disabled=this.data.readOnly,s.name=this.data.fieldName,s.tabIndex=l,this._setRequired(s,this.data.required),s.addEventListener("input",(i=>{t.setValue(e,{value:i.target.value}),this.setPropertyOnSiblings(s,"value",i.target.value,"value")})),s.addEventListener("resetform",(t=>{var e;const i=null!==(e=this.data.defaultFieldValue)&&void 0!==e?e:"";s.value=r.userValue=i,r.formattedValue=null}));let o=t=>{const{formattedValue:e}=r;null!==e&&void 0!==e&&(t.target.value=e),t.target.scrollLeft=0};if(this.enableScripting&&this.hasJSActions){var i;s.addEventListener("focus",(t=>{r.userValue&&(t.target.value=r.userValue),r.valueOnFocus=t.target.value})),s.addEventListener("updatefromsandbox",(s=>{const i={value(s){var i;r.userValue=null!==(i=s.detail.value)&&void 0!==i?i:"",t.setValue(e,{value:r.userValue.toString()}),s.target.value=r.userValue},formattedValue(s){const{formattedValue:i}=s.detail;r.formattedValue=i,null!==i&&void 0!==i&&s.target!==document.activeElement&&(s.target.value=i),t.setValue(e,{formattedValue:i})},selRange(t){t.target.setSelectionRange(...t.detail.selRange)}};this._dispatchEventFromSandbox(i,s)})),s.addEventListener("keydown",(t=>{var s;let i=-1;if("Escape"===t.key?i=0:"Enter"===t.key?i=2:"Tab"===t.key&&(i=3),-1===i)return;const{value:a}=t.target;r.valueOnFocus!==a&&(r.userValue=a,null===(s=this.linkService.eventBus)||void 0===s||s.dispatch("dispatcheventinsandbox",{source:this,detail:{id:e,name:"Keystroke",value:a,willCommit:!0,commitKey:i,selStart:t.target.selectionStart,selEnd:t.target.selectionEnd}}))}));const a=o;o=null,s.addEventListener("blur",(t=>{const{value:s}=t.target;var i;r.userValue=s,this._mouseState.isDown&&r.valueOnFocus!==s&&(null===(i=this.linkService.eventBus)||void 0===i||i.dispatch("dispatcheventinsandbox",{source:this,detail:{id:e,name:"Keystroke",value:s,willCommit:!0,commitKey:1,selStart:t.target.selectionStart,selEnd:t.target.selectionEnd}})),a(t)})),null!==(i=this.data.actions)&&void 0!==i&&i.Keystroke&&s.addEventListener("beforeinput",(t=>{var s;const{data:i,target:a}=t,{value:n,selectionStart:r,selectionEnd:o}=a;let l=r,c=o;switch(t.inputType){case"deleteWordBackward":{const t=n.substring(0,r).match(/\w*[^\w]*$/);t&&(l-=t[0].length);break}case"deleteWordForward":{const t=n.substring(r).match(/^[^\w]*\w*/);t&&(c+=t[0].length);break}case"deleteContentBackward":r===o&&(l-=1);break;case"deleteContentForward":r===o&&(c+=1)}t.preventDefault(),null===(s=this.linkService.eventBus)||void 0===s||s.dispatch("dispatcheventinsandbox",{source:this,detail:{id:e,name:"Keystroke",value:n,change:i||"",willCommit:!1,selStart:l,selEnd:c}})})),this._setEventListeners(s,[["focus","Focus"],["blur","Blur"],["mousedown","Mouse Down"],["mouseenter","Mouse Enter"],["mouseleave","Mouse Exit"],["mouseup","Mouse Up"]],(t=>t.target.value))}if(o&&s.addEventListener("blur",o),null!==this.data.maxLen&&(s.maxLength=this.data.maxLen),this.data.comb){const t=(this.data.rect[2]-this.data.rect[0])/this.data.maxLen;s.classList.add("comb"),s.style.letterSpacing="calc(".concat(t,"px * var(--scale-factor) - 1ch)")}}else s=document.createElement("div"),s.textContent=this.data.fieldValue,s.style.verticalAlign="middle",s.style.display="table-cell";return this._setTextStyle(s),this._setBackgroundColor(s),this._setDefaultPropertiesFromJS(s),this.container.append(s),this.container}}class v extends m{constructor(t){super(t,{isRenderable:t.renderForms})}render(){const t=this.annotationStorage,e=this.data,s=e.id;let i=t.getValue(s,{value:e.exportValue===e.fieldValue}).value;"string"===typeof i&&(i="Off"!==i,t.setValue(s,{value:i})),this.container.className="buttonWidgetAnnotation checkBox";const a=document.createElement("input");return c.add(a),a.setAttribute("data-element-id",s),a.disabled=e.readOnly,this._setRequired(a,this.data.required),a.type="checkbox",a.name=e.fieldName,i&&a.setAttribute("checked",!0),a.setAttribute("exportValue",e.exportValue),a.tabIndex=l,a.addEventListener("change",(i=>{const{name:a,checked:n}=i.target;for(const r of this._getElementsByName(a,s)){const s=n&&r.exportValue===e.exportValue;r.domElement&&(r.domElement.checked=s),t.setValue(r.id,{value:s})}t.setValue(s,{value:n})})),a.addEventListener("resetform",(t=>{const s=e.defaultFieldValue||"Off";t.target.checked=s===e.exportValue})),this.enableScripting&&this.hasJSActions&&(a.addEventListener("updatefromsandbox",(e=>{const i={value(e){e.target.checked="Off"!==e.detail.value,t.setValue(s,{value:e.target.checked})}};this._dispatchEventFromSandbox(i,e)})),this._setEventListeners(a,[["change","Validate"],["change","Action"],["focus","Focus"],["blur","Blur"],["mousedown","Mouse Down"],["mouseenter","Mouse Enter"],["mouseleave","Mouse Exit"],["mouseup","Mouse Up"]],(t=>t.target.checked))),this._setBackgroundColor(a),this._setDefaultPropertiesFromJS(a),this.container.append(a),this.container}}class _ extends m{constructor(t){super(t,{isRenderable:t.renderForms})}render(){this.container.className="buttonWidgetAnnotation radioButton";const t=this.annotationStorage,e=this.data,s=e.id;let i=t.getValue(s,{value:e.fieldValue===e.buttonValue}).value;"string"===typeof i&&(i=i!==e.buttonValue,t.setValue(s,{value:i}));const a=document.createElement("input");if(c.add(a),a.setAttribute("data-element-id",s),a.disabled=e.readOnly,this._setRequired(a,this.data.required),a.type="radio",a.name=e.fieldName,i&&a.setAttribute("checked",!0),a.tabIndex=l,a.addEventListener("change",(e=>{const{name:i,checked:a}=e.target;for(const n of this._getElementsByName(i,s))t.setValue(n.id,{value:!1});t.setValue(s,{value:a})})),a.addEventListener("resetform",(t=>{const s=e.defaultFieldValue;t.target.checked=null!==s&&void 0!==s&&s===e.buttonValue})),this.enableScripting&&this.hasJSActions){const i=e.buttonValue;a.addEventListener("updatefromsandbox",(e=>{const a={value:e=>{const a=i===e.detail.value;for(const i of this._getElementsByName(e.target.name)){const e=a&&i.id===s;i.domElement&&(i.domElement.checked=e),t.setValue(i.id,{value:e})}}};this._dispatchEventFromSandbox(a,e)})),this._setEventListeners(a,[["change","Validate"],["change","Action"],["focus","Focus"],["blur","Blur"],["mousedown","Mouse Down"],["mouseenter","Mouse Enter"],["mouseleave","Mouse Exit"],["mouseup","Mouse Up"]],(t=>t.target.checked))}return this._setBackgroundColor(a),this._setDefaultPropertiesFromJS(a),this.container.append(a),this.container}}class b extends p{constructor(t){super(t,{ignoreBorder:t.data.hasAppearance})}render(){const t=super.render();t.className="buttonWidgetAnnotation pushButton",this.data.alternativeText&&(t.title=this.data.alternativeText);const e=t.lastChild;return this.enableScripting&&this.hasJSActions&&e&&(this._setDefaultPropertiesFromJS(e),e.addEventListener("updatefromsandbox",(t=>{this._dispatchEventFromSandbox({},t)}))),t}}class y extends m{constructor(t){super(t,{isRenderable:t.renderForms})}render(){this.container.className="choiceWidgetAnnotation";const t=this.annotationStorage,e=this.data.id,s=t.getValue(e,{value:this.data.fieldValue}),i=document.createElement("select");c.add(i),i.setAttribute("data-element-id",e),i.disabled=this.data.readOnly,this._setRequired(i,this.data.required),i.name=this.data.fieldName,i.tabIndex=l;let a=this.data.combo&&this.data.options.length>0;this.data.combo||(i.size=this.data.options.length,this.data.multiSelect&&(i.multiple=!0)),i.addEventListener("resetform",(t=>{const e=this.data.defaultFieldValue;for(const s of i.options)s.selected=s.value===e}));for(const l of this.data.options){const t=document.createElement("option");t.textContent=l.displayValue,t.value=l.exportValue,s.value.includes(l.exportValue)&&(t.setAttribute("selected",!0),a=!1),i.append(t)}let n=null;if(a){const t=document.createElement("option");t.value=" ",t.setAttribute("hidden",!0),t.setAttribute("selected",!0),i.prepend(t),n=()=>{t.remove(),i.removeEventListener("input",n),n=null},i.addEventListener("input",n)}const r=(t,e)=>{const s=e?"value":"textContent",i=t.target.options;return t.target.multiple?Array.prototype.filter.call(i,(t=>t.selected)).map((t=>t[s])):-1===i.selectedIndex?null:i[i.selectedIndex][s]},o=t=>{const e=t.target.options;return Array.prototype.map.call(e,(t=>({displayValue:t.textContent,exportValue:t.value})))};return this.enableScripting&&this.hasJSActions?(i.addEventListener("updatefromsandbox",(s=>{const a={value(s){var a;null===(a=n)||void 0===a||a();const o=s.detail.value,l=new Set(Array.isArray(o)?o:[o]);for(const t of i.options)t.selected=l.has(t.value);t.setValue(e,{value:r(s,!0)})},multipleSelection(t){i.multiple=!0},remove(s){const a=i.options,n=s.detail.remove;a[n].selected=!1,i.remove(n),a.length>0&&-1===Array.prototype.findIndex.call(a,(t=>t.selected))&&(a[0].selected=!0),t.setValue(e,{value:r(s,!0),items:o(s)})},clear(s){for(;0!==i.length;)i.remove(0);t.setValue(e,{value:null,items:[]})},insert(s){const{index:a,displayValue:n,exportValue:l}=s.detail.insert,c=i.children[a],d=document.createElement("option");d.textContent=n,d.value=l,c?c.before(d):i.append(d),t.setValue(e,{value:r(s,!0),items:o(s)})},items(s){const{items:a}=s.detail;for(;0!==i.length;)i.remove(0);for(const t of a){const{displayValue:e,exportValue:s}=t,a=document.createElement("option");a.textContent=e,a.value=s,i.append(a)}i.options.length>0&&(i.options[0].selected=!0),t.setValue(e,{value:r(s,!0),items:o(s)})},indices(s){const i=new Set(s.detail.indices);for(const t of s.target.options)t.selected=i.has(t.index);t.setValue(e,{value:r(s,!0)})},editable(t){t.target.disabled=!t.detail.editable}};this._dispatchEventFromSandbox(a,s)})),i.addEventListener("input",(s=>{var i;const a=r(s,!0),n=r(s,!1);t.setValue(e,{value:a}),null===(i=this.linkService.eventBus)||void 0===i||i.dispatch("dispatcheventinsandbox",{source:this,detail:{id:e,name:"Keystroke",value:n,changeEx:a,willCommit:!0,commitKey:1,keyDown:!1}})})),this._setEventListeners(i,[["focus","Focus"],["blur","Blur"],["mousedown","Mouse Down"],["mouseenter","Mouse Enter"],["mouseleave","Mouse Exit"],["mouseup","Mouse Up"],["input","Action"]],(t=>t.target.checked))):i.addEventListener("input",(function(s){t.setValue(e,{value:r(s,!0)})})),this.data.combo&&this._setTextStyle(i),this._setBackgroundColor(i),this._setDefaultPropertiesFromJS(i),this.container.append(i),this.container}}class A extends u{constructor(t){var e,s,i;super(t,{isRenderable:!!(null!==(e=t.data.titleObj)&&void 0!==e&&e.str||null!==(s=t.data.contentsObj)&&void 0!==s&&s.str||null!==(i=t.data.richText)&&void 0!==i&&i.str)})}render(){if(this.container.className="popupAnnotation",["Line","Square","Circle","PolyLine","Polygon","Ink"].includes(this.data.parentType))return this.container;const t='[data-annotation-id="'.concat(this.data.parentId,'"]'),e=this.layer.querySelectorAll(t);if(0===e.length)return this.container;const s=new P({container:this.container,trigger:Array.from(e),color:this.data.color,titleObj:this.data.titleObj,modificationDate:this.data.modificationDate,contentsObj:this.data.contentsObj,richText:this.data.richText}),a=this.page,n=i.Util.normalizeRect([this.data.parentRect[0],a.view[3]-this.data.parentRect[1]+a.view[1],this.data.parentRect[2],a.view[3]-this.data.parentRect[3]+a.view[1]]),r=n[0]+this.data.parentRect[2]-this.data.parentRect[0],o=n[1],[l,c,d,h]=this.viewport.viewBox,u=d-l,p=h-c;return this.container.style.left="".concat(100*(r-l)/u,"%"),this.container.style.top="".concat(100*(o-c)/p,"%"),this.container.append(s.render()),this.container}}class P{constructor(t){this.container=t.container,this.trigger=t.trigger,this.color=t.color,this.titleObj=t.titleObj,this.modificationDate=t.modificationDate,this.contentsObj=t.contentsObj,this.richText=t.richText,this.hideWrapper=t.hideWrapper||!1,this.pinned=!1}render(){var t,e;const s=document.createElement("div");s.className="popupWrapper",this.hideElement=this.hideWrapper?s:this.container,this.hideElement.hidden=!0;const n=document.createElement("div");n.className="popup";const r=this.color;if(r){const t=.7*(255-r[0])+r[0],e=.7*(255-r[1])+r[1],s=.7*(255-r[2])+r[2];n.style.backgroundColor=i.Util.makeHexColor(0|t,0|e,0|s)}const l=document.createElement("h1");l.dir=this.titleObj.dir,l.textContent=this.titleObj.str,n.append(l);const c=a.PDFDateString.toDateObject(this.modificationDate);if(c){const t=document.createElement("span");t.className="popupDate",t.textContent="{{date}}, {{time}}",t.dataset.l10nId="annotation_date_string",t.dataset.l10nArgs=JSON.stringify({date:c.toLocaleDateString(),time:c.toLocaleTimeString()}),n.append(t)}if(null===(t=this.richText)||void 0===t||!t.str||null!==(e=this.contentsObj)&&void 0!==e&&e.str&&this.contentsObj.str!==this.richText.str){const t=this._formatContents(this.contentsObj);n.append(t)}else o.XfaLayer.render({xfaHtml:this.richText.html,intent:"richText",div:n}),n.lastChild.className="richText popupContent";Array.isArray(this.trigger)||(this.trigger=[this.trigger]);for(const i of this.trigger)i.addEventListener("click",this._toggle.bind(this)),i.addEventListener("mouseover",this._show.bind(this,!1)),i.addEventListener("mouseout",this._hide.bind(this,!1));return n.addEventListener("click",this._hide.bind(this,!0)),s.append(n),s}_formatContents(t){let{str:e,dir:s}=t;const i=document.createElement("p");i.className="popupContent",i.dir=s;const a=e.split(/(?:\r\n?|\n)/);for(let n=0,r=a.length;n<r;++n){const t=a[n];i.append(document.createTextNode(t)),n<r-1&&i.append(document.createElement("br"))}return i}_toggle(){this.pinned?this._hide(!0):this._show(!0)}_show(){arguments.length>0&&void 0!==arguments[0]&&arguments[0]&&(this.pinned=!0),this.hideElement.hidden&&(this.hideElement.hidden=!1,this.container.style.zIndex+=1)}_hide(){(!(arguments.length>0&&void 0!==arguments[0])||arguments[0])&&(this.pinned=!1),this.hideElement.hidden||this.pinned||(this.hideElement.hidden=!0,this.container.style.zIndex-=1)}}class S extends u{constructor(t){var e,s,i;super(t,{isRenderable:!!(t.data.hasPopup||null!==(e=t.data.titleObj)&&void 0!==e&&e.str||null!==(s=t.data.contentsObj)&&void 0!==s&&s.str||null!==(i=t.data.richText)&&void 0!==i&&i.str),ignoreBorder:!0})}render(){return this.container.className="freeTextAnnotation",this.data.hasPopup||this._createPopup(null,this.data),this.container}}class x extends u{constructor(t){var e,s,i;super(t,{isRenderable:!!(t.data.hasPopup||null!==(e=t.data.titleObj)&&void 0!==e&&e.str||null!==(s=t.data.contentsObj)&&void 0!==s&&s.str||null!==(i=t.data.richText)&&void 0!==i&&i.str),ignoreBorder:!0})}render(){this.container.className="lineAnnotation";const t=this.data,{width:e,height:s}=d(t.rect),i=this.svgFactory.create(e,s,!0),a=this.svgFactory.createElement("svg:line");return a.setAttribute("x1",t.rect[2]-t.lineCoordinates[0]),a.setAttribute("y1",t.rect[3]-t.lineCoordinates[1]),a.setAttribute("x2",t.rect[2]-t.lineCoordinates[2]),a.setAttribute("y2",t.rect[3]-t.lineCoordinates[3]),a.setAttribute("stroke-width",t.borderStyle.width||1),a.setAttribute("stroke","transparent"),a.setAttribute("fill","transparent"),i.append(a),this.container.append(i),this._createPopup(a,t),this.container}}class F extends u{constructor(t){var e,s,i;super(t,{isRenderable:!!(t.data.hasPopup||null!==(e=t.data.titleObj)&&void 0!==e&&e.str||null!==(s=t.data.contentsObj)&&void 0!==s&&s.str||null!==(i=t.data.richText)&&void 0!==i&&i.str),ignoreBorder:!0})}render(){this.container.className="squareAnnotation";const t=this.data,{width:e,height:s}=d(t.rect),i=this.svgFactory.create(e,s,!0),a=t.borderStyle.width,n=this.svgFactory.createElement("svg:rect");return n.setAttribute("x",a/2),n.setAttribute("y",a/2),n.setAttribute("width",e-a),n.setAttribute("height",s-a),n.setAttribute("stroke-width",a||1),n.setAttribute("stroke","transparent"),n.setAttribute("fill","transparent"),i.append(n),this.container.append(i),this._createPopup(n,t),this.container}}class w extends u{constructor(t){var e,s,i;super(t,{isRenderable:!!(t.data.hasPopup||null!==(e=t.data.titleObj)&&void 0!==e&&e.str||null!==(s=t.data.contentsObj)&&void 0!==s&&s.str||null!==(i=t.data.richText)&&void 0!==i&&i.str),ignoreBorder:!0})}render(){this.container.className="circleAnnotation";const t=this.data,{width:e,height:s}=d(t.rect),i=this.svgFactory.create(e,s,!0),a=t.borderStyle.width,n=this.svgFactory.createElement("svg:ellipse");return n.setAttribute("cx",e/2),n.setAttribute("cy",s/2),n.setAttribute("rx",e/2-a/2),n.setAttribute("ry",s/2-a/2),n.setAttribute("stroke-width",a||1),n.setAttribute("stroke","transparent"),n.setAttribute("fill","transparent"),i.append(n),this.container.append(i),this._createPopup(n,t),this.container}}class C extends u{constructor(t){var e,s,i;super(t,{isRenderable:!!(t.data.hasPopup||null!==(e=t.data.titleObj)&&void 0!==e&&e.str||null!==(s=t.data.contentsObj)&&void 0!==s&&s.str||null!==(i=t.data.richText)&&void 0!==i&&i.str),ignoreBorder:!0}),this.containerClassName="polylineAnnotation",this.svgElementName="svg:polyline"}render(){this.container.className=this.containerClassName;const t=this.data,{width:e,height:s}=d(t.rect),i=this.svgFactory.create(e,s,!0);let a=[];for(const r of t.vertices){const e=r.x-t.rect[0],s=t.rect[3]-r.y;a.push(e+","+s)}a=a.join(" ");const n=this.svgFactory.createElement(this.svgElementName);return n.setAttribute("points",a),n.setAttribute("stroke-width",t.borderStyle.width||1),n.setAttribute("stroke","transparent"),n.setAttribute("fill","transparent"),i.append(n),this.container.append(i),this._createPopup(n,t),this.container}}class M extends C{constructor(t){super(t),this.containerClassName="polygonAnnotation",this.svgElementName="svg:polygon"}}class k extends u{constructor(t){var e,s,i;super(t,{isRenderable:!!(t.data.hasPopup||null!==(e=t.data.titleObj)&&void 0!==e&&e.str||null!==(s=t.data.contentsObj)&&void 0!==s&&s.str||null!==(i=t.data.richText)&&void 0!==i&&i.str),ignoreBorder:!0})}render(){return this.container.className="caretAnnotation",this.data.hasPopup||this._createPopup(null,this.data),this.container}}class E extends u{constructor(t){var e,s,i;super(t,{isRenderable:!!(t.data.hasPopup||null!==(e=t.data.titleObj)&&void 0!==e&&e.str||null!==(s=t.data.contentsObj)&&void 0!==s&&s.str||null!==(i=t.data.richText)&&void 0!==i&&i.str),ignoreBorder:!0}),this.containerClassName="inkAnnotation",this.svgElementName="svg:polyline"}render(){this.container.className=this.containerClassName;const t=this.data,{width:e,height:s}=d(t.rect),i=this.svgFactory.create(e,s,!0);for(const a of t.inkLists){let e=[];for(const i of a){const s=i.x-t.rect[0],a=t.rect[3]-i.y;e.push("".concat(s,",").concat(a))}e=e.join(" ");const s=this.svgFactory.createElement(this.svgElementName);s.setAttribute("points",e),s.setAttribute("stroke-width",t.borderStyle.width||1),s.setAttribute("stroke","transparent"),s.setAttribute("fill","transparent"),this._createPopup(s,t),i.append(s)}return this.container.append(i),this.container}}class T extends u{constructor(t){var e,s,i;super(t,{isRenderable:!!(t.data.hasPopup||null!==(e=t.data.titleObj)&&void 0!==e&&e.str||null!==(s=t.data.contentsObj)&&void 0!==s&&s.str||null!==(i=t.data.richText)&&void 0!==i&&i.str),ignoreBorder:!0,createQuadrilaterals:!0})}render(){return this.data.hasPopup||this._createPopup(null,this.data),this.quadrilaterals?this._renderQuadrilaterals("highlightAnnotation"):(this.container.className="highlightAnnotation",this.container)}}class R extends u{constructor(t){var e,s,i;super(t,{isRenderable:!!(t.data.hasPopup||null!==(e=t.data.titleObj)&&void 0!==e&&e.str||null!==(s=t.data.contentsObj)&&void 0!==s&&s.str||null!==(i=t.data.richText)&&void 0!==i&&i.str),ignoreBorder:!0,createQuadrilaterals:!0})}render(){return this.data.hasPopup||this._createPopup(null,this.data),this.quadrilaterals?this._renderQuadrilaterals("underlineAnnotation"):(this.container.className="underlineAnnotation",this.container)}}class O extends u{constructor(t){var e,s,i;super(t,{isRenderable:!!(t.data.hasPopup||null!==(e=t.data.titleObj)&&void 0!==e&&e.str||null!==(s=t.data.contentsObj)&&void 0!==s&&s.str||null!==(i=t.data.richText)&&void 0!==i&&i.str),ignoreBorder:!0,createQuadrilaterals:!0})}render(){return this.data.hasPopup||this._createPopup(null,this.data),this.quadrilaterals?this._renderQuadrilaterals("squigglyAnnotation"):(this.container.className="squigglyAnnotation",this.container)}}class I extends u{constructor(t){var e,s,i;super(t,{isRenderable:!!(t.data.hasPopup||null!==(e=t.data.titleObj)&&void 0!==e&&e.str||null!==(s=t.data.contentsObj)&&void 0!==s&&s.str||null!==(i=t.data.richText)&&void 0!==i&&i.str),ignoreBorder:!0,createQuadrilaterals:!0})}render(){return this.data.hasPopup||this._createPopup(null,this.data),this.quadrilaterals?this._renderQuadrilaterals("strikeoutAnnotation"):(this.container.className="strikeoutAnnotation",this.container)}}class D extends u{constructor(t){var e,s,i;super(t,{isRenderable:!!(t.data.hasPopup||null!==(e=t.data.titleObj)&&void 0!==e&&e.str||null!==(s=t.data.contentsObj)&&void 0!==s&&s.str||null!==(i=t.data.richText)&&void 0!==i&&i.str),ignoreBorder:!0})}render(){return this.container.className="stampAnnotation",this.data.hasPopup||this._createPopup(null,this.data),this.container}}class L extends u{constructor(t){var e;super(t,{isRenderable:!0});const{filename:s,content:i}=this.data.file;this.filename=(0,a.getFilenameFromUrl)(s),this.content=i,null===(e=this.linkService.eventBus)||void 0===e||e.dispatch("fileattachmentannotation",{source:this,filename:s,content:i})}render(){var t,e;this.container.className="fileAttachmentAnnotation";const s=document.createElement("div");return s.className="popupTriggerArea",s.addEventListener("dblclick",this._download.bind(this)),!this.data.hasPopup&&(null!==(t=this.data.titleObj)&&void 0!==t&&t.str||null!==(e=this.data.contentsObj)&&void 0!==e&&e.str||this.data.richText)&&this._createPopup(s,this.data),this.container.append(s),this.container}_download(){var t;null===(t=this.downloadManager)||void 0===t||t.openOrDownloadData(this.container,this.content,this.filename)}}class N{static render(t){const{annotations:e,div:s,viewport:r}=t;_classStaticPrivateMethodGet(this,N,G).call(this,s,r);const o=[],l=[];for(const a of e){if(!a)continue;if(a.annotationType===i.AnnotationType.POPUP){l.push(a);continue}const{width:t,height:e}=d(a.rect);t<=0||e<=0||o.push(a)}l.length&&o.push(...l);for(const i of o){const e=h.create({data:i,layer:s,page:t.page,viewport:r,linkService:t.linkService,downloadManager:t.downloadManager,imageResourcesPath:t.imageResourcesPath||"",renderForms:!1!==t.renderForms,svgFactory:new a.DOMSVGFactory,annotationStorage:t.annotationStorage||new n.AnnotationStorage,enableScripting:t.enableScripting,hasJSActions:t.hasJSActions,fieldObjects:t.fieldObjects,mouseState:t.mouseState||{isDown:!1}});if(e.isRenderable){const t=e.render();if(i.hidden&&(t.style.visibility="hidden"),Array.isArray(t))for(const e of t)s.append(e);else e instanceof A?s.prepend(t):s.append(t)}}_classStaticPrivateMethodGet(this,N,j).call(this,s,t.annotationCanvasMap)}static update(t){const{annotationCanvasMap:e,div:s,viewport:i}=t;_classStaticPrivateMethodGet(this,N,G).call(this,s,i),_classStaticPrivateMethodGet(this,N,j).call(this,s,e),s.hidden=!1}}function G(t,e){let{width:s,height:i,rotation:a}=e;const{style:n}=t,r=a%180!==0,o=Math.floor(s)+"px",l=Math.floor(i)+"px";n.width=r?l:o,n.height=r?o:l,t.setAttribute("data-main-rotation",a)}function j(t,e){if(e){for(const[s,i]of e){const e=t.querySelector('[data-annotation-id="'.concat(s,'"]'));if(!e)continue;const{firstChild:a}=e;a?"CANVAS"===a.nodeName?a.replaceWith(i):a.before(i):e.append(i)}e.clear()}}e.AnnotationLayer=N},(t,e)=>{function s(t){return Math.floor(255*Math.max(0,Math.min(1,t))).toString(16).padStart(2,"0")}Object.defineProperty(e,"__esModule",{value:!0}),e.ColorConverters=void 0,e.ColorConverters=class{static CMYK_G(t){let[e,s,i,a]=t;return["G",1-Math.min(1,.3*e+.59*i+.11*s+a)]}static G_CMYK(t){let[e]=t;return["CMYK",0,0,0,1-e]}static G_RGB(t){let[e]=t;return["RGB",e,e,e]}static G_HTML(t){let[e]=t;const i=s(e);return"#".concat(i).concat(i).concat(i)}static RGB_G(t){let[e,s,i]=t;return["G",.3*e+.59*s+.11*i]}static RGB_HTML(t){let[e,i,a]=t;const n=s(e),r=s(i),o=s(a);return"#".concat(n).concat(r).concat(o)}static T_HTML(){return"#00000000"}static CMYK_RGB(t){let[e,s,i,a]=t;return["RGB",1-Math.min(1,e+a),1-Math.min(1,i+a),1-Math.min(1,s+a)]}static CMYK_HTML(t){const e=this.CMYK_RGB(t).slice(1);return this.RGB_HTML(e)}static RGB_CMYK(t){let[e,s,i]=t;const a=1-e,n=1-s,r=1-i;return["CMYK",a,n,r,Math.min(a,n,r)]}}},(t,e,s)=>{Object.defineProperty(e,"__esModule",{value:!0}),e.XfaLayer=void 0;var i=s(20);e.XfaLayer=class{static setupStorage(t,e,s,i,a){const n=i.getValue(e,{value:null});switch(s.name){case"textarea":if(null!==n.value&&(t.textContent=n.value),"print"===a)break;t.addEventListener("input",(t=>{i.setValue(e,{value:t.target.value})}));break;case"input":if("radio"===s.attributes.type||"checkbox"===s.attributes.type){if(n.value===s.attributes.xfaOn?t.setAttribute("checked",!0):n.value===s.attributes.xfaOff&&t.removeAttribute("checked"),"print"===a)break;t.addEventListener("change",(t=>{i.setValue(e,{value:t.target.checked?t.target.getAttribute("xfaOn"):t.target.getAttribute("xfaOff")})}))}else{if(null!==n.value&&t.setAttribute("value",n.value),"print"===a)break;t.addEventListener("input",(t=>{i.setValue(e,{value:t.target.value})}))}break;case"select":if(null!==n.value)for(const t of s.children)t.attributes.value===n.value&&(t.attributes.selected=!0);t.addEventListener("input",(t=>{const s=t.target.options,a=-1===s.selectedIndex?"":s[s.selectedIndex].value;i.setValue(e,{value:a})}))}}static setAttributes(t){let{html:e,element:s,storage:i=null,intent:a,linkService:n}=t;const{attributes:r}=s,o=e instanceof HTMLAnchorElement;"radio"===r.type&&(r.name="".concat(r.name,"-").concat(a));for(const[l,c]of Object.entries(r))if(null!==c&&void 0!==c)switch(l){case"class":c.length&&e.setAttribute(l,c.join(" "));break;case"dataId":break;case"id":e.setAttribute("data-element-id",c);break;case"style":Object.assign(e.style,c);break;case"textContent":e.textContent=c;break;default:(!o||"href"!==l&&"newWindow"!==l)&&e.setAttribute(l,c)}o&&n.addLinkAttributes(e,r.href,r.newWindow),i&&r.dataId&&this.setupStorage(e,r.dataId,s,i)}static render(t){const e=t.annotationStorage,s=t.linkService,a=t.xfaHtml,n=t.intent||"display",r=document.createElement(a.name);a.attributes&&this.setAttributes({html:r,element:a,intent:n,linkService:s});const o=[[a,-1,r]],l=t.div;if(l.append(r),t.viewport){const e="matrix(".concat(t.viewport.transform.join(","),")");l.style.transform=e}"richText"!==n&&l.setAttribute("class","xfaLayer xfaFont");const c=[];for(;o.length>0;){var d;const[t,a,r]=o.at(-1);if(a+1===t.children.length){o.pop();continue}const l=t.children[++o.at(-1)[1]];if(null===l)continue;const{name:h}=l;if("#text"===h){const t=document.createTextNode(l.value);c.push(t),r.append(t);continue}let u;if(u=null!==l&&void 0!==l&&null!==(d=l.attributes)&&void 0!==d&&d.xmlns?document.createElementNS(l.attributes.xmlns,h):document.createElement(h),r.append(u),l.attributes&&this.setAttributes({html:u,element:l,storage:e,intent:n,linkService:s}),l.children&&l.children.length>0)o.push([l,-1,u]);else if(l.value){const t=document.createTextNode(l.value);i.XfaText.shouldBuildText(h)&&c.push(t),u.append(t)}}for(const i of l.querySelectorAll(".xfaNonInteractive input, .xfaNonInteractive textarea"))i.setAttribute("readOnly",!0);return{textDivs:c}}static update(t){const e="matrix(".concat(t.viewport.transform.join(","),")");t.div.style.transform=e,t.div.hidden=!1}}},(t,e,s)=>{Object.defineProperty(e,"__esModule",{value:!0}),e.TextLayerRenderTask=void 0,e.renderTextLayer=function(t){const e=new f({textContent:t.textContent,textContentStream:t.textContentStream,container:t.container,viewport:t.viewport,textDivs:t.textDivs,textContentItemsStr:t.textContentItemsStr,enhanceTextSelection:t.enhanceTextSelection});return e._render(t.timeout),e};var i=s(1),a=s(4);const n=30,r=.8,o=new Map,l=/^\s+$/g;function c(t,e,s,a){const c=document.createElement("span"),d=t._enhanceTextSelection?{angle:0,canvasWidth:0,hasText:""!==e.str,hasEOL:e.hasEOL,originalTransform:null,paddingBottom:0,paddingLeft:0,paddingRight:0,paddingTop:0,scale:1,fontSize:0}:{angle:0,canvasWidth:0,hasText:""!==e.str,hasEOL:e.hasEOL,fontSize:0};t._textDivs.push(c);const h=i.Util.transform(t._viewport.transform,e.transform);let u=Math.atan2(h[1],h[0]);const p=s[e.fontName];p.vertical&&(u+=Math.PI/2);const f=Math.hypot(h[2],h[3]),m=f*function(t,e){const s=o.get(t);if(s)return s;e.save(),e.font="".concat(n,"px ").concat(t);const i=e.measureText("");let a=i.fontBoundingBoxAscent,l=Math.abs(i.fontBoundingBoxDescent);if(a){e.restore();const s=a/(a+l);return o.set(t,s),s}e.strokeStyle="red",e.clearRect(0,0,n,n),e.strokeText("g",0,0);let c=e.getImageData(0,0,n,n).data;l=0;for(let r=c.length-1-3;r>=0;r-=4)if(c[r]>0){l=Math.ceil(r/4/n);break}e.clearRect(0,0,n,n),e.strokeText("A",0,n),c=e.getImageData(0,0,n,n).data,a=0;for(let r=0,o=c.length;r<o;r+=4)if(c[r]>0){a=n-Math.floor(r/4/n);break}if(e.restore(),a){const e=a/(a+l);return o.set(t,e),e}return o.set(t,r),r}(p.fontFamily,a);let g,v;0===u?(g=h[4],v=h[5]-m):(g=h[4]+m*Math.sin(u),v=h[5]-m*Math.cos(u)),c.style.left="".concat(g,"px"),c.style.top="".concat(v,"px"),c.style.fontSize="".concat(f,"px"),c.style.fontFamily=p.fontFamily,d.fontSize=f,c.setAttribute("role","presentation"),c.textContent=e.str,c.dir=e.dir,t._fontInspectorEnabled&&(c.dataset.fontName=e.fontName),0!==u&&(d.angle=u*(180/Math.PI));let _=!1;if(e.str.length>1||t._enhanceTextSelection&&l.test(e.str))_=!0;else if(" "!==e.str&&e.transform[0]!==e.transform[3]){const t=Math.abs(e.transform[0]),s=Math.abs(e.transform[3]);t!==s&&Math.max(t,s)/Math.min(t,s)>1.5&&(_=!0)}if(_&&(p.vertical?d.canvasWidth=e.height*t._viewport.scale:d.canvasWidth=e.width*t._viewport.scale),t._textDivProperties.set(c,d),t._textContentStream&&t._layoutText(c),t._enhanceTextSelection&&d.hasText){let s=1,a=0;0!==u&&(s=Math.cos(u),a=Math.sin(u));const n=(p.vertical?e.height:e.width)*t._viewport.scale,r=f;let o,l;0!==u?(o=[s,a,-a,s,g,v],l=i.Util.getAxialAlignedBoundingBox([0,0,n,r],o)):l=[g,v,g+n,v+r],t._bounds.push({left:l[0],top:l[1],right:l[2],bottom:l[3],div:c,size:[n,r],m:o})}}function d(t){if(t._canceled)return;const e=t._textDivs,s=t._capability,i=e.length;if(i>1e5)return t._renderingDone=!0,void s.resolve();if(!t._textContentStream)for(let a=0;a<i;a++)t._layoutText(e[a]);t._renderingDone=!0,s.resolve()}function h(t,e,s){let i=0;for(let a=0;a<s;a++){const s=t[e++];s>0&&(i=i?Math.min(s,i):s)}return i}function u(t){const e=t._bounds,s=t._viewport,a=function(t,e,s){const i=s.map((function(t,e){return{x1:t.left,y1:t.top,x2:t.right,y2:t.bottom,index:e,x1New:void 0,x2New:void 0}}));p(t,i);const a=new Array(s.length);for(const n of i){const t=n.index;a[t]={left:n.x1New,top:0,right:n.x2New,bottom:0}}s.map((function(e,s){const n=a[s],r=i[s];r.x1=e.top,r.y1=t-n.right,r.x2=e.bottom,r.y2=t-n.left,r.index=s,r.x1New=void 0,r.x2New=void 0})),p(e,i);for(const n of i){const t=n.index;a[t].top=n.x1New,a[t].bottom=n.x2New}return a}(s.width,s.height,e);for(let n=0;n<a.length;n++){const s=e[n].div,r=t._textDivProperties.get(s);if(0===r.angle){r.paddingLeft=e[n].left-a[n].left,r.paddingTop=e[n].top-a[n].top,r.paddingRight=a[n].right-e[n].right,r.paddingBottom=a[n].bottom-e[n].bottom,t._textDivProperties.set(s,r);continue}const o=a[n],l=e[n],c=l.m,d=c[0],u=c[1],p=[[0,0],[0,l.size[1]],[l.size[0],0],l.size],f=new Float64Array(64);for(let t=0,e=p.length;t<e;t++){const e=i.Util.applyTransform(p[t],c);f[t+0]=d&&(o.left-e[0])/d,f[t+4]=u&&(o.top-e[1])/u,f[t+8]=d&&(o.right-e[0])/d,f[t+12]=u&&(o.bottom-e[1])/u,f[t+16]=u&&(o.left-e[0])/-u,f[t+20]=d&&(o.top-e[1])/d,f[t+24]=u&&(o.right-e[0])/-u,f[t+28]=d&&(o.bottom-e[1])/d,f[t+32]=d&&(o.left-e[0])/-d,f[t+36]=u&&(o.top-e[1])/-u,f[t+40]=d&&(o.right-e[0])/-d,f[t+44]=u&&(o.bottom-e[1])/-u,f[t+48]=u&&(o.left-e[0])/u,f[t+52]=d&&(o.top-e[1])/-d,f[t+56]=u&&(o.right-e[0])/u,f[t+60]=d&&(o.bottom-e[1])/-d}const m=1+Math.min(Math.abs(d),Math.abs(u));r.paddingLeft=h(f,32,16)/m,r.paddingTop=h(f,48,16)/m,r.paddingRight=h(f,0,16)/m,r.paddingBottom=h(f,16,16)/m,t._textDivProperties.set(s,r)}}function p(t,e){e.sort((function(t,e){return t.x1-e.x1||t.index-e.index}));const s=[{start:-1/0,end:1/0,boundary:{x1:-1/0,y1:-1/0,x2:0,y2:1/0,index:-1,x1New:0,x2New:0}}];for(const i of e){let t=0;for(;t<s.length&&s[t].end<=i.y1;)t++;let e,a,n=s.length-1;for(;n>=0&&s[n].start>=i.y2;)n--;let r,o,l=-1/0;for(r=t;r<=n;r++){let t;e=s[r],a=e.boundary,t=a.x2>i.x1?a.index>i.index?a.x1New:i.x1:void 0===a.x2New?(a.x2+i.x1)/2:a.x2New,t>l&&(l=t)}for(i.x1New=l,r=t;r<=n;r++)e=s[r],a=e.boundary,void 0===a.x2New?a.x2>i.x1?a.index>i.index&&(a.x2New=a.x2):a.x2New=l:a.x2New>l&&(a.x2New=Math.max(l,a.x2));const c=[];let d=null;for(r=t;r<=n;r++){e=s[r],a=e.boundary;const t=a.x2>i.x2?a:i;d===t?c.at(-1).end=e.end:(c.push({start:e.start,end:e.end,boundary:t}),d=t)}for(s[t].start<i.y1&&(c[0].start=i.y1,c.unshift({start:s[t].start,end:i.y1,boundary:s[t].boundary})),i.y2<s[n].end&&(c.at(-1).end=i.y2,c.push({start:i.y2,end:s[n].end,boundary:s[n].boundary})),r=t;r<=n;r++){if(e=s[r],a=e.boundary,void 0!==a.x2New)continue;let i=!1;for(o=t-1;!i&&o>=0&&s[o].start>=a.y1;o--)i=s[o].boundary===a;for(o=n+1;!i&&o<s.length&&s[o].end<=a.y2;o++)i=s[o].boundary===a;for(o=0;!i&&o<c.length;o++)i=c[o].boundary===a;i||(a.x2New=l)}Array.prototype.splice.apply(s,[t,n-t+1,...c])}for(const i of s){const e=i.boundary;void 0===e.x2New&&(e.x2New=Math.max(t,e.x2))}}class f{constructor(t){var e;let{textContent:s,textContentStream:n,container:r,viewport:o,textDivs:l,textContentItemsStr:c,enhanceTextSelection:d}=t;d&&(0,a.deprecated)("The `enhanceTextSelection` functionality will be removed in the future."),this._textContent=s,this._textContentStream=n,this._container=r,this._document=r.ownerDocument,this._viewport=o,this._textDivs=l||[],this._textContentItemsStr=c||[],this._enhanceTextSelection=!!d,this._fontInspectorEnabled=!(null===(e=globalThis.FontInspector)||void 0===e||!e.enabled),this._reader=null,this._layoutTextLastFontSize=null,this._layoutTextLastFontFamily=null,this._layoutTextCtx=null,this._textDivProperties=new WeakMap,this._renderingDone=!1,this._canceled=!1,this._capability=(0,i.createPromiseCapability)(),this._renderTimer=null,this._bounds=[],this._devicePixelRatio=globalThis.devicePixelRatio||1,this._capability.promise.finally((()=>{this._enhanceTextSelection||(this._textDivProperties=null),this._layoutTextCtx&&(this._layoutTextCtx.canvas.width=0,this._layoutTextCtx.canvas.height=0,this._layoutTextCtx=null)})).catch((()=>{}))}get promise(){return this._capability.promise}cancel(){this._canceled=!0,this._reader&&(this._reader.cancel(new i.AbortException("TextLayer task cancelled.")).catch((()=>{})),this._reader=null),null!==this._renderTimer&&(clearTimeout(this._renderTimer),this._renderTimer=null),this._capability.reject(new Error("TextLayer task cancelled."))}_processItems(t,e){for(let s=0,i=t.length;s<i;s++)if(void 0!==t[s].str)this._textContentItemsStr.push(t[s].str),c(this,t[s],e,this._layoutTextCtx);else if("beginMarkedContentProps"===t[s].type||"beginMarkedContent"===t[s].type){const e=this._container;this._container=document.createElement("span"),this._container.classList.add("markedContent"),null!==t[s].id&&this._container.setAttribute("id","".concat(t[s].id)),e.append(this._container)}else"endMarkedContent"===t[s].type&&(this._container=this._container.parentNode)}_layoutText(t){const e=this._textDivProperties.get(t);let s="";if(0!==e.canvasWidth&&e.hasText){const{fontFamily:i}=t.style,{fontSize:a}=e;a===this._layoutTextLastFontSize&&i===this._layoutTextLastFontFamily||(this._layoutTextCtx.font="".concat(a*this._devicePixelRatio,"px ").concat(i),this._layoutTextLastFontSize=a,this._layoutTextLastFontFamily=i);const{width:n}=this._layoutTextCtx.measureText(t.textContent);if(n>0){const t=this._devicePixelRatio*e.canvasWidth/n;this._enhanceTextSelection&&(e.scale=t),s="scaleX(".concat(t,")")}}if(0!==e.angle&&(s="rotate(".concat(e.angle,"deg) ").concat(s)),s.length>0&&(this._enhanceTextSelection&&(e.originalTransform=s),t.style.transform=s),e.hasText&&this._container.append(t),e.hasEOL){const t=document.createElement("br");t.setAttribute("role","presentation"),this._container.append(t)}}_render(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0;const e=(0,i.createPromiseCapability)();let s=Object.create(null);const a=this._document.createElement("canvas");if(a.height=a.width=n,this._layoutTextCtx=a.getContext("2d",{alpha:!1}),this._textContent){const t=this._textContent.items,s=this._textContent.styles;this._processItems(t,s),e.resolve()}else{if(!this._textContentStream)throw new Error('Neither "textContent" nor "textContentStream" parameters specified.');{const t=()=>{this._reader.read().then((i=>{let{value:a,done:n}=i;n?e.resolve():(Object.assign(s,a.styles),this._processItems(a.items,s),t())}),e.reject)};this._reader=this._textContentStream.getReader(),t()}}e.promise.then((()=>{s=null,t?this._renderTimer=setTimeout((()=>{d(this),this._renderTimer=null}),t):d(this)}),this._capability.reject)}expandTextDivs(){let t=arguments.length>0&&void 0!==arguments[0]&&arguments[0];if(!this._enhanceTextSelection||!this._renderingDone)return;null!==this._bounds&&(u(this),this._bounds=null);const e=[],s=[];for(let i=0,a=this._textDivs.length;i<a;i++){const a=this._textDivs[i],n=this._textDivProperties.get(a);n.hasText&&(t?(e.length=0,s.length=0,n.originalTransform&&e.push(n.originalTransform),n.paddingTop>0?(s.push("".concat(n.paddingTop,"px")),e.push("translateY(".concat(-n.paddingTop,"px)"))):s.push(0),n.paddingRight>0?s.push("".concat(n.paddingRight/n.scale,"px")):s.push(0),n.paddingBottom>0?s.push("".concat(n.paddingBottom,"px")):s.push(0),n.paddingLeft>0?(s.push("".concat(n.paddingLeft/n.scale,"px")),e.push("translateX(".concat(-n.paddingLeft/n.scale,"px)"))):s.push(0),a.style.padding=s.join(" "),e.length&&(a.style.transform=e.join(" "))):(a.style.padding=null,a.style.transform=n.originalTransform))}}}e.TextLayerRenderTask=f},(t,e,s)=>{Object.defineProperty(e,"__esModule",{value:!0}),e.SVGGraphics=void 0;var i=s(4),a=s(1),n=s(3);let r=class{constructor(){(0,a.unreachable)("Not implemented: SVGGraphics")}};e.SVGGraphics=r;{const o={fontStyle:"normal",fontWeight:"normal",fillColor:"#000000"},l="http://www.w3.org/XML/1998/namespace",c="http://www.w3.org/1999/xlink",d=["butt","round","square"],h=["miter","round","bevel"],u=function(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"",s=arguments.length>2&&void 0!==arguments[2]&&arguments[2];if(URL.createObjectURL&&"undefined"!==typeof Blob&&!s)return URL.createObjectURL(new Blob([t],{type:e}));const i="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=";let a="data:".concat(e,";base64,");for(let n=0,r=t.length;n<r;n+=3){const e=255&t[n],s=255&t[n+1],o=255&t[n+2];a+=i[e>>2]+i[(3&e)<<4|s>>4]+i[n+1<r?(15&s)<<2|o>>6:64]+i[n+2<r?63&o:64]}return a},p=function(){const t=new Uint8Array([137,80,78,71,13,10,26,10]),e=new Int32Array(256);for(let a=0;a<256;a++){let t=a;for(let e=0;e<8;e++)t=1&t?3988292384^t>>1&2147483647:t>>1&2147483647;e[a]=t}function s(t,s,i,a){let n=a;const r=s.length;i[n]=r>>24&255,i[n+1]=r>>16&255,i[n+2]=r>>8&255,i[n+3]=255&r,n+=4,i[n]=255&t.charCodeAt(0),i[n+1]=255&t.charCodeAt(1),i[n+2]=255&t.charCodeAt(2),i[n+3]=255&t.charCodeAt(3),n+=4,i.set(s,n),n+=s.length;const o=function(t,s,i){let a=-1;for(let n=s;n<i;n++){const s=255&(a^t[n]);a=a>>>8^e[s]}return-1^a}(i,a+4,n);i[n]=o>>24&255,i[n+1]=o>>16&255,i[n+2]=o>>8&255,i[n+3]=255&o}function i(t){let e=t.length;const s=65535,i=Math.ceil(e/s),a=new Uint8Array(2+e+5*i+4);let n=0;a[n++]=120,a[n++]=156;let r=0;for(;e>s;)a[n++]=0,a[n++]=255,a[n++]=255,a[n++]=0,a[n++]=0,a.set(t.subarray(r,r+s),n),n+=s,r+=s,e-=s;a[n++]=1,a[n++]=255&e,a[n++]=e>>8&255,a[n++]=255&~e,a[n++]=(65535&~e)>>8&255,a.set(t.subarray(r),n),n+=t.length-r;const o=function(t,e,s){let i=1,a=0;for(let n=e;n<s;++n)i=(i+(255&t[n]))%65521,a=(a+i)%65521;return a<<16|i}(t,0,t.length);return a[n++]=o>>24&255,a[n++]=o>>16&255,a[n++]=o>>8&255,a[n++]=255&o,a}function r(e,r,o,l){const c=e.width,d=e.height;let h,p,f;const m=e.data;switch(r){case a.ImageKind.GRAYSCALE_1BPP:p=0,h=1,f=c+7>>3;break;case a.ImageKind.RGB_24BPP:p=2,h=8,f=3*c;break;case a.ImageKind.RGBA_32BPP:p=6,h=8,f=4*c;break;default:throw new Error("invalid format")}const g=new Uint8Array((1+f)*d);let v=0,_=0;for(let t=0;t<d;++t)g[v++]=0,g.set(m.subarray(_,_+f),v),_+=f,v+=f;if(r===a.ImageKind.GRAYSCALE_1BPP&&l){v=0;for(let t=0;t<d;t++){v++;for(let t=0;t<f;t++)g[v++]^=255}}const b=new Uint8Array([c>>24&255,c>>16&255,c>>8&255,255&c,d>>24&255,d>>16&255,d>>8&255,255&d,h,p,0,0,0]),y=function(t){if(!n.isNodeJS)return i(t);try{let e;e=parseInt(process.versions.node)>=8?t:Buffer.from(t);const s=__webpack_require__(2258).deflateSync(e,{level:9});return s instanceof Uint8Array?s:new Uint8Array(s)}catch(e){(0,a.warn)("Not compressing PNG because zlib.deflateSync is unavailable: "+e)}return i(t)}(g),A=t.length+36+b.length+y.length,P=new Uint8Array(A);let S=0;return P.set(t,S),S+=t.length,s("IHDR",b,P,S),S+=12+b.length,s("IDATA",y,P,S),S+=12+y.length,s("IEND",new Uint8Array(0),P,S),u(P,"image/png",o)}return function(t,e,s){return r(t,void 0===t.kind?a.ImageKind.GRAYSCALE_1BPP:t.kind,e,s)}}();class f{constructor(){this.fontSizeScale=1,this.fontWeight=o.fontWeight,this.fontSize=0,this.textMatrix=a.IDENTITY_MATRIX,this.fontMatrix=a.FONT_IDENTITY_MATRIX,this.leading=0,this.textRenderingMode=a.TextRenderingMode.FILL,this.textMatrixScale=1,this.x=0,this.y=0,this.lineX=0,this.lineY=0,this.charSpacing=0,this.wordSpacing=0,this.textHScale=1,this.textRise=0,this.fillColor=o.fillColor,this.strokeColor="#000000",this.fillAlpha=1,this.strokeAlpha=1,this.lineWidth=1,this.lineJoin="",this.lineCap="",this.miterLimit=0,this.dashArray=[],this.dashPhase=0,this.dependencies=[],this.activeClipUrl=null,this.clipGroup=null,this.maskId=""}clone(){return Object.create(this)}setCurrentPoint(t,e){this.x=t,this.y=e}}function m(t){let e=[];const s=[];for(const i of t)"save"!==i.fn?"restore"===i.fn?e=s.pop():e.push(i):(e.push({fnId:92,fn:"group",items:[]}),s.push(e),e=e.at(-1).items);return e}function g(t){if(Number.isInteger(t))return t.toString();const e=t.toFixed(10);let s=e.length-1;if("0"!==e[s])return e;do{s--}while("0"===e[s]);return e.substring(0,"."===e[s]?s:s+1)}function v(t){if(0===t[4]&&0===t[5]){if(0===t[1]&&0===t[2])return 1===t[0]&&1===t[3]?"":"scale(".concat(g(t[0])," ").concat(g(t[3]),")");if(t[0]===t[3]&&t[1]===-t[2]){const e=180*Math.acos(t[0])/Math.PI;return"rotate(".concat(g(e),")")}}else if(1===t[0]&&0===t[1]&&0===t[2]&&1===t[3])return"translate(".concat(g(t[4])," ").concat(g(t[5]),")");return"matrix(".concat(g(t[0])," ").concat(g(t[1])," ").concat(g(t[2])," ").concat(g(t[3])," ").concat(g(t[4])," ")+"".concat(g(t[5]),")")}let _=0,b=0,y=0;e.SVGGraphics=r=class{constructor(t,e){let s=arguments.length>2&&void 0!==arguments[2]&&arguments[2];(0,i.deprecated)("The SVG back-end is no longer maintained and *may* be removed in the future."),this.svgFactory=new i.DOMSVGFactory,this.current=new f,this.transformMatrix=a.IDENTITY_MATRIX,this.transformStack=[],this.extraStack=[],this.commonObjs=t,this.objs=e,this.pendingClip=null,this.pendingEOFill=!1,this.embedFonts=!1,this.embeddedFonts=Object.create(null),this.cssStyle=null,this.forceDataSchema=!!s,this._operatorIdMapping=[];for(const i in a.OPS)this._operatorIdMapping[a.OPS[i]]=i}save(){this.transformStack.push(this.transformMatrix);const t=this.current;this.extraStack.push(t),this.current=t.clone()}restore(){this.transformMatrix=this.transformStack.pop(),this.current=this.extraStack.pop(),this.pendingClip=null,this.tgrp=null}group(t){this.save(),this.executeOpTree(t),this.restore()}loadDependencies(t){const e=t.fnArray,s=t.argsArray;for(let i=0,n=e.length;i<n;i++)if(e[i]===a.OPS.dependency)for(const t of s[i]){const e=t.startsWith("g_")?this.commonObjs:this.objs,s=new Promise((s=>{e.get(t,s)}));this.current.dependencies.push(s)}return Promise.all(this.current.dependencies)}transform(t,e,s,i,n,r){const o=[t,e,s,i,n,r];this.transformMatrix=a.Util.transform(this.transformMatrix,o),this.tgrp=null}getSVG(t,e){this.viewport=e;const s=this._initialize(e);return this.loadDependencies(t).then((()=>(this.transformMatrix=a.IDENTITY_MATRIX,this.executeOpTree(this.convertOpList(t)),s)))}convertOpList(t){const e=this._operatorIdMapping,s=t.argsArray,i=t.fnArray,a=[];for(let n=0,r=i.length;n<r;n++){const t=i[n];a.push({fnId:t,fn:e[t],args:s[n]})}return m(a)}executeOpTree(t){for(const e of t){const t=e.fn,s=e.fnId,i=e.args;switch(0|s){case a.OPS.beginText:this.beginText();break;case a.OPS.dependency:break;case a.OPS.setLeading:this.setLeading(i);break;case a.OPS.setLeadingMoveText:this.setLeadingMoveText(i[0],i[1]);break;case a.OPS.setFont:this.setFont(i);break;case a.OPS.showText:case a.OPS.showSpacedText:this.showText(i[0]);break;case a.OPS.endText:this.endText();break;case a.OPS.moveText:this.moveText(i[0],i[1]);break;case a.OPS.setCharSpacing:this.setCharSpacing(i[0]);break;case a.OPS.setWordSpacing:this.setWordSpacing(i[0]);break;case a.OPS.setHScale:this.setHScale(i[0]);break;case a.OPS.setTextMatrix:this.setTextMatrix(i[0],i[1],i[2],i[3],i[4],i[5]);break;case a.OPS.setTextRise:this.setTextRise(i[0]);break;case a.OPS.setTextRenderingMode:this.setTextRenderingMode(i[0]);break;case a.OPS.setLineWidth:this.setLineWidth(i[0]);break;case a.OPS.setLineJoin:this.setLineJoin(i[0]);break;case a.OPS.setLineCap:this.setLineCap(i[0]);break;case a.OPS.setMiterLimit:this.setMiterLimit(i[0]);break;case a.OPS.setFillRGBColor:this.setFillRGBColor(i[0],i[1],i[2]);break;case a.OPS.setStrokeRGBColor:this.setStrokeRGBColor(i[0],i[1],i[2]);break;case a.OPS.setStrokeColorN:this.setStrokeColorN(i);break;case a.OPS.setFillColorN:this.setFillColorN(i);break;case a.OPS.shadingFill:this.shadingFill(i[0]);break;case a.OPS.setDash:this.setDash(i[0],i[1]);break;case a.OPS.setRenderingIntent:this.setRenderingIntent(i[0]);break;case a.OPS.setFlatness:this.setFlatness(i[0]);break;case a.OPS.setGState:this.setGState(i[0]);break;case a.OPS.fill:this.fill();break;case a.OPS.eoFill:this.eoFill();break;case a.OPS.stroke:this.stroke();break;case a.OPS.fillStroke:this.fillStroke();break;case a.OPS.eoFillStroke:this.eoFillStroke();break;case a.OPS.clip:this.clip("nonzero");break;case a.OPS.eoClip:this.clip("evenodd");break;case a.OPS.paintSolidColorImageMask:this.paintSolidColorImageMask();break;case a.OPS.paintImageXObject:this.paintImageXObject(i[0]);break;case a.OPS.paintInlineImageXObject:this.paintInlineImageXObject(i[0]);break;case a.OPS.paintImageMaskXObject:this.paintImageMaskXObject(i[0]);break;case a.OPS.paintFormXObjectBegin:this.paintFormXObjectBegin(i[0],i[1]);break;case a.OPS.paintFormXObjectEnd:this.paintFormXObjectEnd();break;case a.OPS.closePath:this.closePath();break;case a.OPS.closeStroke:this.closeStroke();break;case a.OPS.closeFillStroke:this.closeFillStroke();break;case a.OPS.closeEOFillStroke:this.closeEOFillStroke();break;case a.OPS.nextLine:this.nextLine();break;case a.OPS.transform:this.transform(i[0],i[1],i[2],i[3],i[4],i[5]);break;case a.OPS.constructPath:this.constructPath(i[0],i[1]);break;case a.OPS.endPath:this.endPath();break;case 92:this.group(e.items);break;default:(0,a.warn)("Unimplemented operator ".concat(t))}}}setWordSpacing(t){this.current.wordSpacing=t}setCharSpacing(t){this.current.charSpacing=t}nextLine(){this.moveText(0,this.current.leading)}setTextMatrix(t,e,s,i,a,n){const r=this.current;r.textMatrix=r.lineMatrix=[t,e,s,i,a,n],r.textMatrixScale=Math.hypot(t,e),r.x=r.lineX=0,r.y=r.lineY=0,r.xcoords=[],r.ycoords=[],r.tspan=this.svgFactory.createElement("svg:tspan"),r.tspan.setAttributeNS(null,"font-family",r.fontFamily),r.tspan.setAttributeNS(null,"font-size","".concat(g(r.fontSize),"px")),r.tspan.setAttributeNS(null,"y",g(-r.y)),r.txtElement=this.svgFactory.createElement("svg:text"),r.txtElement.append(r.tspan)}beginText(){const t=this.current;t.x=t.lineX=0,t.y=t.lineY=0,t.textMatrix=a.IDENTITY_MATRIX,t.lineMatrix=a.IDENTITY_MATRIX,t.textMatrixScale=1,t.tspan=this.svgFactory.createElement("svg:tspan"),t.txtElement=this.svgFactory.createElement("svg:text"),t.txtgrp=this.svgFactory.createElement("svg:g"),t.xcoords=[],t.ycoords=[]}moveText(t,e){const s=this.current;s.x=s.lineX+=t,s.y=s.lineY+=e,s.xcoords=[],s.ycoords=[],s.tspan=this.svgFactory.createElement("svg:tspan"),s.tspan.setAttributeNS(null,"font-family",s.fontFamily),s.tspan.setAttributeNS(null,"font-size","".concat(g(s.fontSize),"px")),s.tspan.setAttributeNS(null,"y",g(-s.y))}showText(t){const e=this.current,s=e.font,i=e.fontSize;if(0===i)return;const n=e.fontSizeScale,r=e.charSpacing,c=e.wordSpacing,d=e.fontDirection,h=e.textHScale*d,u=s.vertical,p=u?1:-1,f=s.defaultVMetrics,m=i*e.fontMatrix[0];let _=0;for(const a of t){if(null===a){_+=d*c;continue}if("number"===typeof a){_+=p*a*i/1e3;continue}const t=(a.isSpace?c:0)+r,o=a.fontChar;let l,h,g,v=a.width;if(u){let t;const e=a.vmetric||f;t=a.vmetric?e[1]:.5*v,t=-t*m;const s=e[2]*m;v=e?-e[0]:v,l=t/n,h=(_+s)/n}else l=_/n,h=0;(a.isInFont||s.missingFile)&&(e.xcoords.push(e.x+l),u&&e.ycoords.push(-e.y+h),e.tspan.textContent+=o),g=u?v*m-t*d:v*m+t*d,_+=g}e.tspan.setAttributeNS(null,"x",e.xcoords.map(g).join(" ")),u?e.tspan.setAttributeNS(null,"y",e.ycoords.map(g).join(" ")):e.tspan.setAttributeNS(null,"y",g(-e.y)),u?e.y-=_:e.x+=_*h,e.tspan.setAttributeNS(null,"font-family",e.fontFamily),e.tspan.setAttributeNS(null,"font-size","".concat(g(e.fontSize),"px")),e.fontStyle!==o.fontStyle&&e.tspan.setAttributeNS(null,"font-style",e.fontStyle),e.fontWeight!==o.fontWeight&&e.tspan.setAttributeNS(null,"font-weight",e.fontWeight);const b=e.textRenderingMode&a.TextRenderingMode.FILL_STROKE_MASK;if(b===a.TextRenderingMode.FILL||b===a.TextRenderingMode.FILL_STROKE?(e.fillColor!==o.fillColor&&e.tspan.setAttributeNS(null,"fill",e.fillColor),e.fillAlpha<1&&e.tspan.setAttributeNS(null,"fill-opacity",e.fillAlpha)):e.textRenderingMode===a.TextRenderingMode.ADD_TO_PATH?e.tspan.setAttributeNS(null,"fill","transparent"):e.tspan.setAttributeNS(null,"fill","none"),b===a.TextRenderingMode.STROKE||b===a.TextRenderingMode.FILL_STROKE){const t=1/(e.textMatrixScale||1);this._setStrokeAttributes(e.tspan,t)}let y=e.textMatrix;0!==e.textRise&&(y=y.slice(),y[5]+=e.textRise),e.txtElement.setAttributeNS(null,"transform","".concat(v(y)," scale(").concat(g(h),", -1)")),e.txtElement.setAttributeNS(l,"xml:space","preserve"),e.txtElement.append(e.tspan),e.txtgrp.append(e.txtElement),this._ensureTransformGroup().append(e.txtElement)}setLeadingMoveText(t,e){this.setLeading(-e),this.moveText(t,e)}addFontStyle(t){if(!t.data)throw new Error('addFontStyle: No font data available, ensure that the "fontExtraProperties" API parameter is set.');this.cssStyle||(this.cssStyle=this.svgFactory.createElement("svg:style"),this.cssStyle.setAttributeNS(null,"type","text/css"),this.defs.append(this.cssStyle));const e=u(t.data,t.mimetype,this.forceDataSchema);this.cssStyle.textContent+='@font-face { font-family: "'.concat(t.loadedName,'";')+" src: url(".concat(e,"); }\n")}setFont(t){const e=this.current,s=this.commonObjs.get(t[0]);let i=t[1];e.font=s,!this.embedFonts||s.missingFile||this.embeddedFonts[s.loadedName]||(this.addFontStyle(s),this.embeddedFonts[s.loadedName]=s),e.fontMatrix=s.fontMatrix||a.FONT_IDENTITY_MATRIX;let n="normal";s.black?n="900":s.bold&&(n="bold");const r=s.italic?"italic":"normal";i<0?(i=-i,e.fontDirection=-1):e.fontDirection=1,e.fontSize=i,e.fontFamily=s.loadedName,e.fontWeight=n,e.fontStyle=r,e.tspan=this.svgFactory.createElement("svg:tspan"),e.tspan.setAttributeNS(null,"y",g(-e.y)),e.xcoords=[],e.ycoords=[]}endText(){var t;const e=this.current;e.textRenderingMode&a.TextRenderingMode.ADD_TO_PATH_FLAG&&null!==(t=e.txtElement)&&void 0!==t&&t.hasChildNodes()&&(e.element=e.txtElement,this.clip("nonzero"),this.endPath())}setLineWidth(t){t>0&&(this.current.lineWidth=t)}setLineCap(t){this.current.lineCap=d[t]}setLineJoin(t){this.current.lineJoin=h[t]}setMiterLimit(t){this.current.miterLimit=t}setStrokeAlpha(t){this.current.strokeAlpha=t}setStrokeRGBColor(t,e,s){this.current.strokeColor=a.Util.makeHexColor(t,e,s)}setFillAlpha(t){this.current.fillAlpha=t}setFillRGBColor(t,e,s){this.current.fillColor=a.Util.makeHexColor(t,e,s),this.current.tspan=this.svgFactory.createElement("svg:tspan"),this.current.xcoords=[],this.current.ycoords=[]}setStrokeColorN(t){this.current.strokeColor=this._makeColorN_Pattern(t)}setFillColorN(t){this.current.fillColor=this._makeColorN_Pattern(t)}shadingFill(t){const e=this.viewport.width,s=this.viewport.height,i=a.Util.inverseTransform(this.transformMatrix),n=a.Util.applyTransform([0,0],i),r=a.Util.applyTransform([0,s],i),o=a.Util.applyTransform([e,0],i),l=a.Util.applyTransform([e,s],i),c=Math.min(n[0],r[0],o[0],l[0]),d=Math.min(n[1],r[1],o[1],l[1]),h=Math.max(n[0],r[0],o[0],l[0]),u=Math.max(n[1],r[1],o[1],l[1]),p=this.svgFactory.createElement("svg:rect");p.setAttributeNS(null,"x",c),p.setAttributeNS(null,"y",d),p.setAttributeNS(null,"width",h-c),p.setAttributeNS(null,"height",u-d),p.setAttributeNS(null,"fill",this._makeShadingPattern(t)),this.current.fillAlpha<1&&p.setAttributeNS(null,"fill-opacity",this.current.fillAlpha),this._ensureTransformGroup().append(p)}_makeColorN_Pattern(t){return"TilingPattern"===t[0]?this._makeTilingPattern(t):this._makeShadingPattern(t)}_makeTilingPattern(t){const e=t[1],s=t[2],i=t[3]||a.IDENTITY_MATRIX,[n,r,o,l]=t[4],c=t[5],d=t[6],h=t[7],u="shading".concat(y++),[p,f,m,g]=a.Util.normalizeRect([...a.Util.applyTransform([n,r],i),...a.Util.applyTransform([o,l],i)]),[v,_]=a.Util.singularValueDecompose2dScale(i),b=c*v,A=d*_,P=this.svgFactory.createElement("svg:pattern");P.setAttributeNS(null,"id",u),P.setAttributeNS(null,"patternUnits","userSpaceOnUse"),P.setAttributeNS(null,"width",b),P.setAttributeNS(null,"height",A),P.setAttributeNS(null,"x","".concat(p)),P.setAttributeNS(null,"y","".concat(f));const S=this.svg,x=this.transformMatrix,F=this.current.fillColor,w=this.current.strokeColor,C=this.svgFactory.create(m-p,g-f);if(this.svg=C,this.transformMatrix=i,2===h){const t=a.Util.makeHexColor(...e);this.current.fillColor=t,this.current.strokeColor=t}return this.executeOpTree(this.convertOpList(s)),this.svg=S,this.transformMatrix=x,this.current.fillColor=F,this.current.strokeColor=w,P.append(C.childNodes[0]),this.defs.append(P),"url(#".concat(u,")")}_makeShadingPattern(t){switch("string"===typeof t&&(t=this.objs.get(t)),t[0]){case"RadialAxial":const e="shading".concat(y++),s=t[3];let i;switch(t[1]){case"axial":const s=t[4],a=t[5];i=this.svgFactory.createElement("svg:linearGradient"),i.setAttributeNS(null,"id",e),i.setAttributeNS(null,"gradientUnits","userSpaceOnUse"),i.setAttributeNS(null,"x1",s[0]),i.setAttributeNS(null,"y1",s[1]),i.setAttributeNS(null,"x2",a[0]),i.setAttributeNS(null,"y2",a[1]);break;case"radial":const n=t[4],r=t[5],o=t[6],l=t[7];i=this.svgFactory.createElement("svg:radialGradient"),i.setAttributeNS(null,"id",e),i.setAttributeNS(null,"gradientUnits","userSpaceOnUse"),i.setAttributeNS(null,"cx",r[0]),i.setAttributeNS(null,"cy",r[1]),i.setAttributeNS(null,"r",l),i.setAttributeNS(null,"fx",n[0]),i.setAttributeNS(null,"fy",n[1]),i.setAttributeNS(null,"fr",o);break;default:throw new Error("Unknown RadialAxial type: ".concat(t[1]))}for(const t of s){const e=this.svgFactory.createElement("svg:stop");e.setAttributeNS(null,"offset",t[0]),e.setAttributeNS(null,"stop-color",t[1]),i.append(e)}return this.defs.append(i),"url(#".concat(e,")");case"Mesh":return(0,a.warn)("Unimplemented pattern Mesh"),null;case"Dummy":return"hotpink";default:throw new Error("Unknown IR type: ".concat(t[0]))}}setDash(t,e){this.current.dashArray=t,this.current.dashPhase=e}constructPath(t,e){const s=this.current;let i=s.x,n=s.y,r=[],o=0;for(const l of t)switch(0|l){case a.OPS.rectangle:i=e[o++],n=e[o++];const t=i+e[o++],s=n+e[o++];r.push("M",g(i),g(n),"L",g(t),g(n),"L",g(t),g(s),"L",g(i),g(s),"Z");break;case a.OPS.moveTo:i=e[o++],n=e[o++],r.push("M",g(i),g(n));break;case a.OPS.lineTo:i=e[o++],n=e[o++],r.push("L",g(i),g(n));break;case a.OPS.curveTo:i=e[o+4],n=e[o+5],r.push("C",g(e[o]),g(e[o+1]),g(e[o+2]),g(e[o+3]),g(i),g(n)),o+=6;break;case a.OPS.curveTo2:r.push("C",g(i),g(n),g(e[o]),g(e[o+1]),g(e[o+2]),g(e[o+3])),i=e[o+2],n=e[o+3],o+=4;break;case a.OPS.curveTo3:i=e[o+2],n=e[o+3],r.push("C",g(e[o]),g(e[o+1]),g(i),g(n),g(i),g(n)),o+=4;break;case a.OPS.closePath:r.push("Z")}r=r.join(" "),s.path&&t.length>0&&t[0]!==a.OPS.rectangle&&t[0]!==a.OPS.moveTo?r=s.path.getAttributeNS(null,"d")+r:(s.path=this.svgFactory.createElement("svg:path"),this._ensureTransformGroup().append(s.path)),s.path.setAttributeNS(null,"d",r),s.path.setAttributeNS(null,"fill","none"),s.element=s.path,s.setCurrentPoint(i,n)}endPath(){const t=this.current;if(t.path=null,!this.pendingClip)return;if(!t.element)return void(this.pendingClip=null);const e="clippath".concat(_++),s=this.svgFactory.createElement("svg:clipPath");s.setAttributeNS(null,"id",e),s.setAttributeNS(null,"transform",v(this.transformMatrix));const i=t.element.cloneNode(!0);if("evenodd"===this.pendingClip?i.setAttributeNS(null,"clip-rule","evenodd"):i.setAttributeNS(null,"clip-rule","nonzero"),this.pendingClip=null,s.append(i),this.defs.append(s),t.activeClipUrl){t.clipGroup=null;for(const t of this.extraStack)t.clipGroup=null;s.setAttributeNS(null,"clip-path",t.activeClipUrl)}t.activeClipUrl="url(#".concat(e,")"),this.tgrp=null}clip(t){this.pendingClip=t}closePath(){const t=this.current;if(t.path){const e="".concat(t.path.getAttributeNS(null,"d"),"Z");t.path.setAttributeNS(null,"d",e)}}setLeading(t){this.current.leading=-t}setTextRise(t){this.current.textRise=t}setTextRenderingMode(t){this.current.textRenderingMode=t}setHScale(t){this.current.textHScale=t/100}setRenderingIntent(t){}setFlatness(t){}setGState(t){for(const[e,s]of t)switch(e){case"LW":this.setLineWidth(s);break;case"LC":this.setLineCap(s);break;case"LJ":this.setLineJoin(s);break;case"ML":this.setMiterLimit(s);break;case"D":this.setDash(s[0],s[1]);break;case"RI":this.setRenderingIntent(s);break;case"FL":this.setFlatness(s);break;case"Font":this.setFont(s);break;case"CA":this.setStrokeAlpha(s);break;case"ca":this.setFillAlpha(s);break;default:(0,a.warn)("Unimplemented graphic state operator ".concat(e))}}fill(){const t=this.current;t.element&&(t.element.setAttributeNS(null,"fill",t.fillColor),t.element.setAttributeNS(null,"fill-opacity",t.fillAlpha),this.endPath())}stroke(){const t=this.current;t.element&&(this._setStrokeAttributes(t.element),t.element.setAttributeNS(null,"fill","none"),this.endPath())}_setStrokeAttributes(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1;const s=this.current;let i=s.dashArray;1!==e&&i.length>0&&(i=i.map((function(t){return e*t}))),t.setAttributeNS(null,"stroke",s.strokeColor),t.setAttributeNS(null,"stroke-opacity",s.strokeAlpha),t.setAttributeNS(null,"stroke-miterlimit",g(s.miterLimit)),t.setAttributeNS(null,"stroke-linecap",s.lineCap),t.setAttributeNS(null,"stroke-linejoin",s.lineJoin),t.setAttributeNS(null,"stroke-width",g(e*s.lineWidth)+"px"),t.setAttributeNS(null,"stroke-dasharray",i.map(g).join(" ")),t.setAttributeNS(null,"stroke-dashoffset",g(e*s.dashPhase)+"px")}eoFill(){this.current.element&&this.current.element.setAttributeNS(null,"fill-rule","evenodd"),this.fill()}fillStroke(){this.stroke(),this.fill()}eoFillStroke(){this.current.element&&this.current.element.setAttributeNS(null,"fill-rule","evenodd"),this.fillStroke()}closeStroke(){this.closePath(),this.stroke()}closeFillStroke(){this.closePath(),this.fillStroke()}closeEOFillStroke(){this.closePath(),this.eoFillStroke()}paintSolidColorImageMask(){const t=this.svgFactory.createElement("svg:rect");t.setAttributeNS(null,"x","0"),t.setAttributeNS(null,"y","0"),t.setAttributeNS(null,"width","1px"),t.setAttributeNS(null,"height","1px"),t.setAttributeNS(null,"fill",this.current.fillColor),this._ensureTransformGroup().append(t)}paintImageXObject(t){const e=t.startsWith("g_")?this.commonObjs.get(t):this.objs.get(t);e?this.paintInlineImageXObject(e):(0,a.warn)("Dependent image with object ID ".concat(t," is not ready yet"))}paintInlineImageXObject(t,e){const s=t.width,i=t.height,a=p(t,this.forceDataSchema,!!e),n=this.svgFactory.createElement("svg:rect");n.setAttributeNS(null,"x","0"),n.setAttributeNS(null,"y","0"),n.setAttributeNS(null,"width",g(s)),n.setAttributeNS(null,"height",g(i)),this.current.element=n,this.clip("nonzero");const r=this.svgFactory.createElement("svg:image");r.setAttributeNS(c,"xlink:href",a),r.setAttributeNS(null,"x","0"),r.setAttributeNS(null,"y",g(-i)),r.setAttributeNS(null,"width",g(s)+"px"),r.setAttributeNS(null,"height",g(i)+"px"),r.setAttributeNS(null,"transform","scale(".concat(g(1/s)," ").concat(g(-1/i),")")),e?e.append(r):this._ensureTransformGroup().append(r)}paintImageMaskXObject(t){const e=this.current,s=t.width,i=t.height,a=e.fillColor;e.maskId="mask".concat(b++);const n=this.svgFactory.createElement("svg:mask");n.setAttributeNS(null,"id",e.maskId);const r=this.svgFactory.createElement("svg:rect");r.setAttributeNS(null,"x","0"),r.setAttributeNS(null,"y","0"),r.setAttributeNS(null,"width",g(s)),r.setAttributeNS(null,"height",g(i)),r.setAttributeNS(null,"fill",a),r.setAttributeNS(null,"mask","url(#".concat(e.maskId,")")),this.defs.append(n),this._ensureTransformGroup().append(r),this.paintInlineImageXObject(t,n)}paintFormXObjectBegin(t,e){if(Array.isArray(t)&&6===t.length&&this.transform(t[0],t[1],t[2],t[3],t[4],t[5]),e){const t=e[2]-e[0],s=e[3]-e[1],i=this.svgFactory.createElement("svg:rect");i.setAttributeNS(null,"x",e[0]),i.setAttributeNS(null,"y",e[1]),i.setAttributeNS(null,"width",g(t)),i.setAttributeNS(null,"height",g(s)),this.current.element=i,this.clip("nonzero"),this.endPath()}}paintFormXObjectEnd(){}_initialize(t){const e=this.svgFactory.create(t.width,t.height),s=this.svgFactory.createElement("svg:defs");e.append(s),this.defs=s;const i=this.svgFactory.createElement("svg:g");return i.setAttributeNS(null,"transform",v(t.transform)),e.append(i),this.svg=i,e}_ensureClipGroup(){if(!this.current.clipGroup){const t=this.svgFactory.createElement("svg:g");t.setAttributeNS(null,"clip-path",this.current.activeClipUrl),this.svg.append(t),this.current.clipGroup=t}return this.current.clipGroup}_ensureTransformGroup(){return this.tgrp||(this.tgrp=this.svgFactory.createElement("svg:g"),this.tgrp.setAttributeNS(null,"transform",v(this.transformMatrix)),this.current.activeClipUrl?this._ensureClipGroup().append(this.tgrp):this.svg.append(this.tgrp)),this.tgrp}}}},(t,e,s)=>{Object.defineProperty(e,"__esModule",{value:!0}),e.PDFNodeStream=void 0;var i=s(1),a=s(33);const n=__webpack_require__(172),r=__webpack_require__(7441),o=__webpack_require__(3779),l=__webpack_require__(6558),c=/^file:\/\/\/[a-zA-Z]:\//;e.PDFNodeStream=class{constructor(t){this.source=t,this.url=function(t){const e=l.parse(t);return"file:"===e.protocol||e.host?e:/^[a-z]:[/\\]/i.test(t)?l.parse("file:///".concat(t)):(e.host||(e.protocol="file:"),e)}(t.url),this.isHttp="http:"===this.url.protocol||"https:"===this.url.protocol,this.isFsUrl="file:"===this.url.protocol,this.httpHeaders=this.isHttp&&t.httpHeaders||{},this._fullRequestReader=null,this._rangeRequestReaders=[]}get _progressiveDataLength(){var t,e;return null!==(t=null===(e=this._fullRequestReader)||void 0===e?void 0:e._loaded)&&void 0!==t?t:0}getFullReader(){return(0,i.assert)(!this._fullRequestReader,"PDFNodeStream.getFullReader can only be called once."),this._fullRequestReader=this.isFsUrl?new m(this):new p(this),this._fullRequestReader}getRangeReader(t,e){if(e<=this._progressiveDataLength)return null;const s=this.isFsUrl?new g(this,t,e):new f(this,t,e);return this._rangeRequestReaders.push(s),s}cancelAllRequests(t){this._fullRequestReader&&this._fullRequestReader.cancel(t);for(const e of this._rangeRequestReaders.slice(0))e.cancel(t)}};class d{constructor(t){this._url=t.url,this._done=!1,this._storedError=null,this.onProgress=null;const e=t.source;this._contentLength=e.length,this._loaded=0,this._filename=null,this._disableRange=e.disableRange||!1,this._rangeChunkSize=e.rangeChunkSize,this._rangeChunkSize||this._disableRange||(this._disableRange=!0),this._isStreamingSupported=!e.disableStream,this._isRangeSupported=!e.disableRange,this._readableStream=null,this._readCapability=(0,i.createPromiseCapability)(),this._headersCapability=(0,i.createPromiseCapability)()}get headersReady(){return this._headersCapability.promise}get filename(){return this._filename}get contentLength(){return this._contentLength}get isRangeSupported(){return this._isRangeSupported}get isStreamingSupported(){return this._isStreamingSupported}async read(){if(await this._readCapability.promise,this._done)return{value:void 0,done:!0};if(this._storedError)throw this._storedError;const t=this._readableStream.read();return null===t?(this._readCapability=(0,i.createPromiseCapability)(),this.read()):(this._loaded+=t.length,this.onProgress&&this.onProgress({loaded:this._loaded,total:this._contentLength}),{value:new Uint8Array(t).buffer,done:!1})}cancel(t){this._readableStream?this._readableStream.destroy(t):this._error(t)}_error(t){this._storedError=t,this._readCapability.resolve()}_setReadableStream(t){this._readableStream=t,t.on("readable",(()=>{this._readCapability.resolve()})),t.on("end",(()=>{t.destroy(),this._done=!0,this._readCapability.resolve()})),t.on("error",(t=>{this._error(t)})),!this._isStreamingSupported&&this._isRangeSupported&&this._error(new i.AbortException("streaming is disabled")),this._storedError&&this._readableStream.destroy(this._storedError)}}class h{constructor(t){this._url=t.url,this._done=!1,this._storedError=null,this.onProgress=null,this._loaded=0,this._readableStream=null,this._readCapability=(0,i.createPromiseCapability)();const e=t.source;this._isStreamingSupported=!e.disableStream}get isStreamingSupported(){return this._isStreamingSupported}async read(){if(await this._readCapability.promise,this._done)return{value:void 0,done:!0};if(this._storedError)throw this._storedError;const t=this._readableStream.read();return null===t?(this._readCapability=(0,i.createPromiseCapability)(),this.read()):(this._loaded+=t.length,this.onProgress&&this.onProgress({loaded:this._loaded}),{value:new Uint8Array(t).buffer,done:!1})}cancel(t){this._readableStream?this._readableStream.destroy(t):this._error(t)}_error(t){this._storedError=t,this._readCapability.resolve()}_setReadableStream(t){this._readableStream=t,t.on("readable",(()=>{this._readCapability.resolve()})),t.on("end",(()=>{t.destroy(),this._done=!0,this._readCapability.resolve()})),t.on("error",(t=>{this._error(t)})),this._storedError&&this._readableStream.destroy(this._storedError)}}function u(t,e){return{protocol:t.protocol,auth:t.auth,host:t.hostname,port:t.port,path:t.path,method:"GET",headers:e}}class p extends d{constructor(t){super(t);const e=e=>{if(404===e.statusCode){const t=new i.MissingPDFException('Missing PDF "'.concat(this._url,'".'));return this._storedError=t,void this._headersCapability.reject(t)}this._headersCapability.resolve(),this._setReadableStream(e);const s=t=>this._readableStream.headers[t.toLowerCase()],{allowRangeRequests:n,suggestedLength:r}=(0,a.validateRangeRequestCapabilities)({getResponseHeader:s,isHttp:t.isHttp,rangeChunkSize:this._rangeChunkSize,disableRange:this._disableRange});this._isRangeSupported=n,this._contentLength=r||this._contentLength,this._filename=(0,a.extractFilenameFromHeader)(s)};this._request=null,"http:"===this._url.protocol?this._request=r.request(u(this._url,t.httpHeaders),e):this._request=o.request(u(this._url,t.httpHeaders),e),this._request.on("error",(t=>{this._storedError=t,this._headersCapability.reject(t)})),this._request.end()}}class f extends h{constructor(t,e,s){super(t),this._httpHeaders={};for(const i in t.httpHeaders){const e=t.httpHeaders[i];"undefined"!==typeof e&&(this._httpHeaders[i]=e)}this._httpHeaders.Range="bytes=".concat(e,"-").concat(s-1);const a=t=>{if(404!==t.statusCode)this._setReadableStream(t);else{const t=new i.MissingPDFException('Missing PDF "'.concat(this._url,'".'));this._storedError=t}};this._request=null,"http:"===this._url.protocol?this._request=r.request(u(this._url,this._httpHeaders),a):this._request=o.request(u(this._url,this._httpHeaders),a),this._request.on("error",(t=>{this._storedError=t})),this._request.end()}}class m extends d{constructor(t){super(t);let e=decodeURIComponent(this._url.path);c.test(this._url.href)&&(e=e.replace(/^\//,"")),n.lstat(e,((t,s)=>{if(t)return"ENOENT"===t.code&&(t=new i.MissingPDFException('Missing PDF "'.concat(e,'".'))),this._storedError=t,void this._headersCapability.reject(t);this._contentLength=s.size,this._setReadableStream(n.createReadStream(e)),this._headersCapability.resolve()}))}}class g extends h{constructor(t,e,s){super(t);let i=decodeURIComponent(this._url.path);c.test(this._url.href)&&(i=i.replace(/^\//,"")),this._setReadableStream(n.createReadStream(i,{start:e,end:s-1}))}}},(t,e,s)=>{Object.defineProperty(e,"__esModule",{value:!0}),e.createResponseStatusError=function(t,e){return 404===t||0===t&&e.startsWith("file:")?new i.MissingPDFException('Missing PDF "'+e+'".'):new i.UnexpectedResponseException("Unexpected server response (".concat(t,') while retrieving PDF "').concat(e,'".'),t)},e.extractFilenameFromHeader=function(t){const e=t("Content-Disposition");if(e){let t=(0,a.getFilenameFromContentDispositionHeader)(e);if(t.includes("%"))try{t=decodeURIComponent(t)}catch(s){}if((0,n.isPdfFile)(t))return t}return null},e.validateRangeRequestCapabilities=function(t){let{getResponseHeader:e,isHttp:s,rangeChunkSize:i,disableRange:a}=t;const n={allowRangeRequests:!1,suggestedLength:void 0},r=parseInt(e("Content-Length"),10);return Number.isInteger(r)?(n.suggestedLength=r,r<=2*i||a||!s||"bytes"!==e("Accept-Ranges")||"identity"!==(e("Content-Encoding")||"identity")||(n.allowRangeRequests=!0),n):n},e.validateResponseStatus=function(t){return 200===t||206===t};var i=s(1),a=s(34),n=s(4)},(t,e,s)=>{Object.defineProperty(e,"__esModule",{value:!0}),e.getFilenameFromContentDispositionHeader=function(t){let e=!0,s=a("filename\\*","i").exec(t);if(s){s=s[1];let t=o(s);return t=unescape(t),t=l(t),t=c(t),r(t)}if(s=function(t){const e=[];let s;const i=a("filename\\*((?!0\\d)\\d+)(\\*?)","ig");for(;null!==(s=i.exec(t));){let[,t,i,a]=s;if(t=parseInt(t,10),t in e){if(0===t)break}else e[t]=[i,a]}const n=[];for(let a=0;a<e.length&&a in e;++a){let[t,s]=e[a];s=o(s),t&&(s=unescape(s),0===a&&(s=l(s))),n.push(s)}return n.join("")}(t),s)return r(c(s));if(s=a("filename","i").exec(t),s){s=s[1];let t=o(s);return t=c(t),r(t)}function a(t,e){return new RegExp("(?:^|;)\\s*"+t+'\\s*=\\s*([^";\\s][^;\\s]*|"(?:[^"\\\\]|\\\\"?)+"?)',e)}function n(t,s){if(t){if(!/^[\x00-\xFF]+$/.test(s))return s;try{const a=new TextDecoder(t,{fatal:!0}),n=(0,i.stringToBytes)(s);s=a.decode(n),e=!1}catch(a){}}return s}function r(t){return e&&/[\x80-\xff]/.test(t)&&(t=n("utf-8",t),e&&(t=n("iso-8859-1",t))),t}function o(t){if(t.startsWith('"')){const e=t.slice(1).split('\\"');for(let t=0;t<e.length;++t){const s=e[t].indexOf('"');-1!==s&&(e[t]=e[t].slice(0,s),e.length=t+1),e[t]=e[t].replace(/\\(.)/g,"$1")}t=e.join('"')}return t}function l(t){const e=t.indexOf("'");return-1===e?t:n(t.slice(0,e),t.slice(e+1).replace(/^[^']*'/,""))}function c(t){return!t.startsWith("=?")||/[\x00-\x19\x80-\xff]/.test(t)?t:t.replace(/=\?([\w-]*)\?([QqBb])\?((?:[^?]|\?(?!=))*)\?=/g,(function(t,e,s,i){if("q"===s||"Q"===s)return n(e,i=(i=i.replace(/_/g," ")).replace(/=([0-9a-fA-F]{2})/g,(function(t,e){return String.fromCharCode(parseInt(e,16))})));try{i=atob(i)}catch(a){}return n(e,i)}))}return""};var i=s(1)},(t,e,s)=>{Object.defineProperty(e,"__esModule",{value:!0}),e.PDFNetworkStream=void 0;var i=s(1),a=s(33);class n{constructor(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};this.url=t,this.isHttp=/^https?:/i.test(t),this.httpHeaders=this.isHttp&&e.httpHeaders||Object.create(null),this.withCredentials=e.withCredentials||!1,this.getXhr=e.getXhr||function(){return new XMLHttpRequest},this.currXhrId=0,this.pendingRequests=Object.create(null)}requestRange(t,e,s){const i={begin:t,end:e};for(const a in s)i[a]=s[a];return this.request(i)}requestFull(t){return this.request(t)}request(t){const e=this.getXhr(),s=this.currXhrId++,i=this.pendingRequests[s]={xhr:e};e.open("GET",this.url),e.withCredentials=this.withCredentials;for(const a in this.httpHeaders){const t=this.httpHeaders[a];"undefined"!==typeof t&&e.setRequestHeader(a,t)}return this.isHttp&&"begin"in t&&"end"in t?(e.setRequestHeader("Range","bytes=".concat(t.begin,"-").concat(t.end-1)),i.expectedStatus=206):i.expectedStatus=200,e.responseType="arraybuffer",t.onError&&(e.onerror=function(s){t.onError(e.status)}),e.onreadystatechange=this.onStateChange.bind(this,s),e.onprogress=this.onProgress.bind(this,s),i.onHeadersReceived=t.onHeadersReceived,i.onDone=t.onDone,i.onError=t.onError,i.onProgress=t.onProgress,e.send(null),s}onProgress(t,e){var s;const i=this.pendingRequests[t];i&&(null===(s=i.onProgress)||void 0===s||s.call(i,e))}onStateChange(t,e){const s=this.pendingRequests[t];if(!s)return;const a=s.xhr;if(a.readyState>=2&&s.onHeadersReceived&&(s.onHeadersReceived(),delete s.onHeadersReceived),4!==a.readyState)return;if(!(t in this.pendingRequests))return;var n;if(delete this.pendingRequests[t],0===a.status&&this.isHttp)return void(null===(n=s.onError)||void 0===n||n.call(s,a.status));const r=a.status||200;var o;if((200!==r||206!==s.expectedStatus)&&r!==s.expectedStatus)return void(null===(o=s.onError)||void 0===o||o.call(s,a.status));const l=function(t){const e=t.response;return"string"!==typeof e?e:(0,i.stringToBytes)(e).buffer}(a);if(206===r){const t=a.getResponseHeader("Content-Range"),e=/bytes (\d+)-(\d+)\/(\d+)/.exec(t);s.onDone({begin:parseInt(e[1],10),chunk:l})}else if(l)s.onDone({begin:0,chunk:l});else{var c;null===(c=s.onError)||void 0===c||c.call(s,a.status)}}getRequestXhr(t){return this.pendingRequests[t].xhr}isPendingRequest(t){return t in this.pendingRequests}abortRequest(t){const e=this.pendingRequests[t].xhr;delete this.pendingRequests[t],e.abort()}}e.PDFNetworkStream=class{constructor(t){this._source=t,this._manager=new n(t.url,{httpHeaders:t.httpHeaders,withCredentials:t.withCredentials}),this._rangeChunkSize=t.rangeChunkSize,this._fullRequestReader=null,this._rangeRequestReaders=[]}_onRangeRequestReaderClosed(t){const e=this._rangeRequestReaders.indexOf(t);e>=0&&this._rangeRequestReaders.splice(e,1)}getFullReader(){return(0,i.assert)(!this._fullRequestReader,"PDFNetworkStream.getFullReader can only be called once."),this._fullRequestReader=new r(this._manager,this._source),this._fullRequestReader}getRangeReader(t,e){const s=new o(this._manager,t,e);return s.onClosed=this._onRangeRequestReaderClosed.bind(this),this._rangeRequestReaders.push(s),s}cancelAllRequests(t){var e;null===(e=this._fullRequestReader)||void 0===e||e.cancel(t);for(const s of this._rangeRequestReaders.slice(0))s.cancel(t)}};class r{constructor(t,e){this._manager=t;const s={onHeadersReceived:this._onHeadersReceived.bind(this),onDone:this._onDone.bind(this),onError:this._onError.bind(this),onProgress:this._onProgress.bind(this)};this._url=e.url,this._fullRequestId=t.requestFull(s),this._headersReceivedCapability=(0,i.createPromiseCapability)(),this._disableRange=e.disableRange||!1,this._contentLength=e.length,this._rangeChunkSize=e.rangeChunkSize,this._rangeChunkSize||this._disableRange||(this._disableRange=!0),this._isStreamingSupported=!1,this._isRangeSupported=!1,this._cachedChunks=[],this._requests=[],this._done=!1,this._storedError=void 0,this._filename=null,this.onProgress=null}_onHeadersReceived(){const t=this._fullRequestId,e=this._manager.getRequestXhr(t),s=t=>e.getResponseHeader(t),{allowRangeRequests:i,suggestedLength:n}=(0,a.validateRangeRequestCapabilities)({getResponseHeader:s,isHttp:this._manager.isHttp,rangeChunkSize:this._rangeChunkSize,disableRange:this._disableRange});i&&(this._isRangeSupported=!0),this._contentLength=n||this._contentLength,this._filename=(0,a.extractFilenameFromHeader)(s),this._isRangeSupported&&this._manager.abortRequest(t),this._headersReceivedCapability.resolve()}_onDone(t){if(t&&(this._requests.length>0?this._requests.shift().resolve({value:t.chunk,done:!1}):this._cachedChunks.push(t.chunk)),this._done=!0,!(this._cachedChunks.length>0)){for(const t of this._requests)t.resolve({value:void 0,done:!0});this._requests.length=0}}_onError(t){this._storedError=(0,a.createResponseStatusError)(t,this._url),this._headersReceivedCapability.reject(this._storedError);for(const e of this._requests)e.reject(this._storedError);this._requests.length=0,this._cachedChunks.length=0}_onProgress(t){var e;null===(e=this.onProgress)||void 0===e||e.call(this,{loaded:t.loaded,total:t.lengthComputable?t.total:this._contentLength})}get filename(){return this._filename}get isRangeSupported(){return this._isRangeSupported}get isStreamingSupported(){return this._isStreamingSupported}get contentLength(){return this._contentLength}get headersReady(){return this._headersReceivedCapability.promise}async read(){if(this._storedError)throw this._storedError;if(this._cachedChunks.length>0)return{value:this._cachedChunks.shift(),done:!1};if(this._done)return{value:void 0,done:!0};const t=(0,i.createPromiseCapability)();return this._requests.push(t),t.promise}cancel(t){this._done=!0,this._headersReceivedCapability.reject(t);for(const e of this._requests)e.resolve({value:void 0,done:!0});this._requests.length=0,this._manager.isPendingRequest(this._fullRequestId)&&this._manager.abortRequest(this._fullRequestId),this._fullRequestReader=null}}class o{constructor(t,e,s){this._manager=t;const i={onDone:this._onDone.bind(this),onError:this._onError.bind(this),onProgress:this._onProgress.bind(this)};this._url=t.url,this._requestId=t.requestRange(e,s,i),this._requests=[],this._queuedChunk=null,this._done=!1,this._storedError=void 0,this.onProgress=null,this.onClosed=null}_close(){var t;null===(t=this.onClosed)||void 0===t||t.call(this,this)}_onDone(t){const e=t.chunk;this._requests.length>0?this._requests.shift().resolve({value:e,done:!1}):this._queuedChunk=e,this._done=!0;for(const s of this._requests)s.resolve({value:void 0,done:!0});this._requests.length=0,this._close()}_onError(t){this._storedError=(0,a.createResponseStatusError)(t,this._url);for(const e of this._requests)e.reject(this._storedError);this._requests.length=0,this._queuedChunk=null}_onProgress(t){var e;this.isStreamingSupported||null===(e=this.onProgress)||void 0===e||e.call(this,{loaded:t.loaded})}get isStreamingSupported(){return!1}async read(){if(this._storedError)throw this._storedError;if(null!==this._queuedChunk){const t=this._queuedChunk;return this._queuedChunk=null,{value:t,done:!1}}if(this._done)return{value:void 0,done:!0};const t=(0,i.createPromiseCapability)();return this._requests.push(t),t.promise}cancel(t){this._done=!0;for(const e of this._requests)e.resolve({value:void 0,done:!0});this._requests.length=0,this._manager.isPendingRequest(this._requestId)&&this._manager.abortRequest(this._requestId),this._close()}}},(t,e,s)=>{Object.defineProperty(e,"__esModule",{value:!0}),e.PDFFetchStream=void 0;var i=s(1),a=s(33);function n(t,e,s){return{method:"GET",headers:t,signal:null===s||void 0===s?void 0:s.signal,mode:"cors",credentials:e?"include":"same-origin",redirect:"follow"}}function r(t){const e=new Headers;for(const s in t){const i=t[s];"undefined"!==typeof i&&e.append(s,i)}return e}e.PDFFetchStream=class{constructor(t){this.source=t,this.isHttp=/^https?:/i.test(t.url),this.httpHeaders=this.isHttp&&t.httpHeaders||{},this._fullRequestReader=null,this._rangeRequestReaders=[]}get _progressiveDataLength(){var t,e;return null!==(t=null===(e=this._fullRequestReader)||void 0===e?void 0:e._loaded)&&void 0!==t?t:0}getFullReader(){return(0,i.assert)(!this._fullRequestReader,"PDFFetchStream.getFullReader can only be called once."),this._fullRequestReader=new o(this),this._fullRequestReader}getRangeReader(t,e){if(e<=this._progressiveDataLength)return null;const s=new l(this,t,e);return this._rangeRequestReaders.push(s),s}cancelAllRequests(t){this._fullRequestReader&&this._fullRequestReader.cancel(t);for(const e of this._rangeRequestReaders.slice(0))e.cancel(t)}};class o{constructor(t){this._stream=t,this._reader=null,this._loaded=0,this._filename=null;const e=t.source;this._withCredentials=e.withCredentials||!1,this._contentLength=e.length,this._headersCapability=(0,i.createPromiseCapability)(),this._disableRange=e.disableRange||!1,this._rangeChunkSize=e.rangeChunkSize,this._rangeChunkSize||this._disableRange||(this._disableRange=!0),"undefined"!==typeof AbortController&&(this._abortController=new AbortController),this._isStreamingSupported=!e.disableStream,this._isRangeSupported=!e.disableRange,this._headers=r(this._stream.httpHeaders);const s=e.url;fetch(s,n(this._headers,this._withCredentials,this._abortController)).then((t=>{if(!(0,a.validateResponseStatus)(t.status))throw(0,a.createResponseStatusError)(t.status,s);this._reader=t.body.getReader(),this._headersCapability.resolve();const e=e=>t.headers.get(e),{allowRangeRequests:n,suggestedLength:r}=(0,a.validateRangeRequestCapabilities)({getResponseHeader:e,isHttp:this._stream.isHttp,rangeChunkSize:this._rangeChunkSize,disableRange:this._disableRange});this._isRangeSupported=n,this._contentLength=r||this._contentLength,this._filename=(0,a.extractFilenameFromHeader)(e),!this._isStreamingSupported&&this._isRangeSupported&&this.cancel(new i.AbortException("Streaming is disabled."))})).catch(this._headersCapability.reject),this.onProgress=null}get headersReady(){return this._headersCapability.promise}get filename(){return this._filename}get contentLength(){return this._contentLength}get isRangeSupported(){return this._isRangeSupported}get isStreamingSupported(){return this._isStreamingSupported}async read(){await this._headersCapability.promise;const{value:t,done:e}=await this._reader.read();return e?{value:t,done:e}:(this._loaded+=t.byteLength,this.onProgress&&this.onProgress({loaded:this._loaded,total:this._contentLength}),{value:new Uint8Array(t).buffer,done:!1})}cancel(t){this._reader&&this._reader.cancel(t),this._abortController&&this._abortController.abort()}}class l{constructor(t,e,s){this._stream=t,this._reader=null,this._loaded=0;const o=t.source;this._withCredentials=o.withCredentials||!1,this._readCapability=(0,i.createPromiseCapability)(),this._isStreamingSupported=!o.disableStream,"undefined"!==typeof AbortController&&(this._abortController=new AbortController),this._headers=r(this._stream.httpHeaders),this._headers.append("Range","bytes=".concat(e,"-").concat(s-1));const l=o.url;fetch(l,n(this._headers,this._withCredentials,this._abortController)).then((t=>{if(!(0,a.validateResponseStatus)(t.status))throw(0,a.createResponseStatusError)(t.status,l);this._readCapability.resolve(),this._reader=t.body.getReader()})).catch(this._readCapability.reject),this.onProgress=null}get isStreamingSupported(){return this._isStreamingSupported}async read(){await this._readCapability.promise;const{value:t,done:e}=await this._reader.read();return e?{value:t,done:e}:(this._loaded+=t.byteLength,this.onProgress&&this.onProgress({loaded:this._loaded}),{value:new Uint8Array(t).buffer,done:!1})}cancel(t){this._reader&&this._reader.cancel(t),this._abortController&&this._abortController.abort()}}}],__webpack_module_cache__={};function __w_pdfjs_require__(t){var e=__webpack_module_cache__[t];if(void 0!==e)return e.exports;var s=__webpack_module_cache__[t]={exports:{}};return __webpack_modules__[t](s,s.exports,__w_pdfjs_require__),s.exports}var __nested_webpack_exports__={};return(()=>{var t=__nested_webpack_exports__;Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"AnnotationEditorLayer",{enumerable:!0,get:function(){return a.AnnotationEditorLayer}}),Object.defineProperty(t,"AnnotationEditorParamsType",{enumerable:!0,get:function(){return e.AnnotationEditorParamsType}}),Object.defineProperty(t,"AnnotationEditorType",{enumerable:!0,get:function(){return e.AnnotationEditorType}}),Object.defineProperty(t,"AnnotationEditorUIManager",{enumerable:!0,get:function(){return n.AnnotationEditorUIManager}}),Object.defineProperty(t,"AnnotationLayer",{enumerable:!0,get:function(){return r.AnnotationLayer}}),Object.defineProperty(t,"AnnotationMode",{enumerable:!0,get:function(){return e.AnnotationMode}}),Object.defineProperty(t,"CMapCompressionType",{enumerable:!0,get:function(){return e.CMapCompressionType}}),Object.defineProperty(t,"GlobalWorkerOptions",{enumerable:!0,get:function(){return o.GlobalWorkerOptions}}),Object.defineProperty(t,"InvalidPDFException",{enumerable:!0,get:function(){return e.InvalidPDFException}}),Object.defineProperty(t,"LoopbackPort",{enumerable:!0,get:function(){return i.LoopbackPort}}),Object.defineProperty(t,"MissingPDFException",{enumerable:!0,get:function(){return e.MissingPDFException}}),Object.defineProperty(t,"OPS",{enumerable:!0,get:function(){return e.OPS}}),Object.defineProperty(t,"PDFDataRangeTransport",{enumerable:!0,get:function(){return i.PDFDataRangeTransport}}),Object.defineProperty(t,"PDFDateString",{enumerable:!0,get:function(){return s.PDFDateString}}),Object.defineProperty(t,"PDFWorker",{enumerable:!0,get:function(){return i.PDFWorker}}),Object.defineProperty(t,"PasswordResponses",{enumerable:!0,get:function(){return e.PasswordResponses}}),Object.defineProperty(t,"PermissionFlag",{enumerable:!0,get:function(){return e.PermissionFlag}}),Object.defineProperty(t,"PixelsPerInch",{enumerable:!0,get:function(){return s.PixelsPerInch}}),Object.defineProperty(t,"RenderingCancelledException",{enumerable:!0,get:function(){return s.RenderingCancelledException}}),Object.defineProperty(t,"SVGGraphics",{enumerable:!0,get:function(){return d.SVGGraphics}}),Object.defineProperty(t,"UNSUPPORTED_FEATURES",{enumerable:!0,get:function(){return e.UNSUPPORTED_FEATURES}}),Object.defineProperty(t,"UnexpectedResponseException",{enumerable:!0,get:function(){return e.UnexpectedResponseException}}),Object.defineProperty(t,"Util",{enumerable:!0,get:function(){return e.Util}}),Object.defineProperty(t,"VerbosityLevel",{enumerable:!0,get:function(){return e.VerbosityLevel}}),Object.defineProperty(t,"XfaLayer",{enumerable:!0,get:function(){return h.XfaLayer}}),Object.defineProperty(t,"binarySearchFirstItem",{enumerable:!0,get:function(){return s.binarySearchFirstItem}}),Object.defineProperty(t,"build",{enumerable:!0,get:function(){return i.build}}),Object.defineProperty(t,"createPromiseCapability",{enumerable:!0,get:function(){return e.createPromiseCapability}}),Object.defineProperty(t,"createValidAbsoluteUrl",{enumerable:!0,get:function(){return e.createValidAbsoluteUrl}}),Object.defineProperty(t,"getDocument",{enumerable:!0,get:function(){return i.getDocument}}),Object.defineProperty(t,"getFilenameFromUrl",{enumerable:!0,get:function(){return s.getFilenameFromUrl}}),Object.defineProperty(t,"getPdfFilenameFromUrl",{enumerable:!0,get:function(){return s.getPdfFilenameFromUrl}}),Object.defineProperty(t,"getXfaPageViewport",{enumerable:!0,get:function(){return s.getXfaPageViewport}}),Object.defineProperty(t,"isPdfFile",{enumerable:!0,get:function(){return s.isPdfFile}}),Object.defineProperty(t,"loadScript",{enumerable:!0,get:function(){return s.loadScript}}),Object.defineProperty(t,"renderTextLayer",{enumerable:!0,get:function(){return c.renderTextLayer}}),Object.defineProperty(t,"shadow",{enumerable:!0,get:function(){return e.shadow}}),Object.defineProperty(t,"version",{enumerable:!0,get:function(){return i.version}});var e=__w_pdfjs_require__(1),s=__w_pdfjs_require__(4),i=__w_pdfjs_require__(6),a=__w_pdfjs_require__(22),n=__w_pdfjs_require__(9),r=__w_pdfjs_require__(27),o=__w_pdfjs_require__(15),l=__w_pdfjs_require__(3),c=__w_pdfjs_require__(30),d=__w_pdfjs_require__(31),h=__w_pdfjs_require__(29);if(l.isNodeJS){const{PDFNodeStream:t}=__w_pdfjs_require__(32);(0,i.setPDFNetworkStreamFactory)((e=>new t(e)))}else{const{PDFNetworkStream:t}=__w_pdfjs_require__(35),{PDFFetchStream:e}=__w_pdfjs_require__(36);(0,i.setPDFNetworkStreamFactory)((i=>(0,s.isValidFetchUrl)(i.url)?new e(i):new t(i)))}})(),__nested_webpack_exports__})(),module.exports=factory()},3688:(t,e,s)=>{"use strict";function i(){var t=this.constructor.getDerivedStateFromProps(this.props,this.state);null!==t&&void 0!==t&&this.setState(t)}function a(t){this.setState(function(e){var s=this.constructor.getDerivedStateFromProps(t,e);return null!==s&&void 0!==s?s:null}.bind(this))}function n(t,e){try{var s=this.props,i=this.state;this.props=t,this.state=e,this.__reactInternalSnapshotFlag=!0,this.__reactInternalSnapshot=this.getSnapshotBeforeUpdate(s,i)}finally{this.props=s,this.state=i}}function r(t){var e=t.prototype;if(!e||!e.isReactComponent)throw new Error("Can only polyfill class components");if("function"!==typeof t.getDerivedStateFromProps&&"function"!==typeof e.getSnapshotBeforeUpdate)return t;var s=null,r=null,o=null;if("function"===typeof e.componentWillMount?s="componentWillMount":"function"===typeof e.UNSAFE_componentWillMount&&(s="UNSAFE_componentWillMount"),"function"===typeof e.componentWillReceiveProps?r="componentWillReceiveProps":"function"===typeof e.UNSAFE_componentWillReceiveProps&&(r="UNSAFE_componentWillReceiveProps"),"function"===typeof e.componentWillUpdate?o="componentWillUpdate":"function"===typeof e.UNSAFE_componentWillUpdate&&(o="UNSAFE_componentWillUpdate"),null!==s||null!==r||null!==o){var l=t.displayName||t.name,c="function"===typeof t.getDerivedStateFromProps?"getDerivedStateFromProps()":"getSnapshotBeforeUpdate()";throw Error("Unsafe legacy lifecycles will not be called for components using new component APIs.\n\n"+l+" uses "+c+" but also contains the following legacy lifecycles:"+(null!==s?"\n  "+s:"")+(null!==r?"\n  "+r:"")+(null!==o?"\n  "+o:"")+"\n\nThe above lifecycles should be removed. Learn more about this warning here:\nhttps://fb.me/react-async-component-lifecycle-hooks")}if("function"===typeof t.getDerivedStateFromProps&&(e.componentWillMount=i,e.componentWillReceiveProps=a),"function"===typeof e.getSnapshotBeforeUpdate){if("function"!==typeof e.componentDidUpdate)throw new Error("Cannot polyfill getSnapshotBeforeUpdate() for components that do not define componentDidUpdate() on the prototype");e.componentWillUpdate=n;var d=e.componentDidUpdate;e.componentDidUpdate=function(t,e,s){var i=this.__reactInternalSnapshotFlag?this.__reactInternalSnapshot:s;d.call(this,t,e,i)}}return t}s.r(e),s.d(e,{polyfill:()=>r}),i.__suppressDeprecationWarning=!0,a.__suppressDeprecationWarning=!0,n.__suppressDeprecationWarning=!0},2240:(t,e,s)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.bodyOpenClassName=e.portalClassName=void 0;var i=Object.assign||function(t){for(var e=1;e<arguments.length;e++){var s=arguments[e];for(var i in s)Object.prototype.hasOwnProperty.call(s,i)&&(t[i]=s[i])}return t},a=function(){function t(t,e){for(var s=0;s<e.length;s++){var i=e[s];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(t,i.key,i)}}return function(e,s,i){return s&&t(e.prototype,s),i&&t(e,i),e}}(),n=s(2791),r=f(n),o=f(s(4164)),l=f(s(2007)),c=f(s(8367)),d=function(t){if(t&&t.__esModule)return t;var e={};if(null!=t)for(var s in t)Object.prototype.hasOwnProperty.call(t,s)&&(e[s]=t[s]);return e.default=t,e}(s(5858)),h=s(3663),u=f(h),p=s(3688);function f(t){return t&&t.__esModule?t:{default:t}}function m(t,e){if(!t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!e||"object"!==typeof e&&"function"!==typeof e?t:e}var g=e.portalClassName="ReactModalPortal",v=e.bodyOpenClassName="ReactModal__Body--open",_=h.canUseDOM&&void 0!==o.default.createPortal,b=function(t){return document.createElement(t)},y=function(){return _?o.default.createPortal:o.default.unstable_renderSubtreeIntoContainer};function A(t){return t()}var P=function(t){function e(){var t,s,a;!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,e);for(var n=arguments.length,l=Array(n),d=0;d<n;d++)l[d]=arguments[d];return s=a=m(this,(t=e.__proto__||Object.getPrototypeOf(e)).call.apply(t,[this].concat(l))),a.removePortal=function(){!_&&o.default.unmountComponentAtNode(a.node);var t=A(a.props.parentSelector);t&&t.contains(a.node)?t.removeChild(a.node):console.warn('React-Modal: "parentSelector" prop did not returned any DOM element. Make sure that the parent element is unmounted to avoid any memory leaks.')},a.portalRef=function(t){a.portal=t},a.renderPortal=function(t){var s=y()(a,r.default.createElement(c.default,i({defaultStyles:e.defaultStyles},t)),a.node);a.portalRef(s)},m(a,s)}return function(t,e){if("function"!==typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}(e,t),a(e,[{key:"componentDidMount",value:function(){h.canUseDOM&&(_||(this.node=b("div")),this.node.className=this.props.portalClassName,A(this.props.parentSelector).appendChild(this.node),!_&&this.renderPortal(this.props))}},{key:"getSnapshotBeforeUpdate",value:function(t){return{prevParent:A(t.parentSelector),nextParent:A(this.props.parentSelector)}}},{key:"componentDidUpdate",value:function(t,e,s){if(h.canUseDOM){var i=this.props,a=i.isOpen,n=i.portalClassName;t.portalClassName!==n&&(this.node.className=n);var r=s.prevParent,o=s.nextParent;o!==r&&(r.removeChild(this.node),o.appendChild(this.node)),(t.isOpen||a)&&!_&&this.renderPortal(this.props)}}},{key:"componentWillUnmount",value:function(){if(h.canUseDOM&&this.node&&this.portal){var t=this.portal.state,e=Date.now(),s=t.isOpen&&this.props.closeTimeoutMS&&(t.closesAt||e+this.props.closeTimeoutMS);s?(t.beforeClose||this.portal.closeWithTimeout(),setTimeout(this.removePortal,s-e)):this.removePortal()}}},{key:"render",value:function(){return h.canUseDOM&&_?(!this.node&&_&&(this.node=b("div")),y()(r.default.createElement(c.default,i({ref:this.portalRef,defaultStyles:e.defaultStyles},this.props)),this.node)):null}}],[{key:"setAppElement",value:function(t){d.setElement(t)}}]),e}(n.Component);P.propTypes={isOpen:l.default.bool.isRequired,style:l.default.shape({content:l.default.object,overlay:l.default.object}),portalClassName:l.default.string,bodyOpenClassName:l.default.string,htmlOpenClassName:l.default.string,className:l.default.oneOfType([l.default.string,l.default.shape({base:l.default.string.isRequired,afterOpen:l.default.string.isRequired,beforeClose:l.default.string.isRequired})]),overlayClassName:l.default.oneOfType([l.default.string,l.default.shape({base:l.default.string.isRequired,afterOpen:l.default.string.isRequired,beforeClose:l.default.string.isRequired})]),appElement:l.default.oneOfType([l.default.instanceOf(u.default),l.default.instanceOf(h.SafeHTMLCollection),l.default.instanceOf(h.SafeNodeList),l.default.arrayOf(l.default.instanceOf(u.default))]),onAfterOpen:l.default.func,onRequestClose:l.default.func,closeTimeoutMS:l.default.number,ariaHideApp:l.default.bool,shouldFocusAfterRender:l.default.bool,shouldCloseOnOverlayClick:l.default.bool,shouldReturnFocusAfterClose:l.default.bool,preventScroll:l.default.bool,parentSelector:l.default.func,aria:l.default.object,data:l.default.object,role:l.default.string,contentLabel:l.default.string,shouldCloseOnEsc:l.default.bool,overlayRef:l.default.func,contentRef:l.default.func,id:l.default.string,overlayElement:l.default.func,contentElement:l.default.func},P.defaultProps={isOpen:!1,portalClassName:g,bodyOpenClassName:v,role:"dialog",ariaHideApp:!0,closeTimeoutMS:0,shouldFocusAfterRender:!0,shouldCloseOnEsc:!0,shouldCloseOnOverlayClick:!0,shouldReturnFocusAfterClose:!0,preventScroll:!1,parentSelector:function(){return document.body},overlayElement:function(t,e){return r.default.createElement("div",t,e)},contentElement:function(t,e){return r.default.createElement("div",t,e)}},P.defaultStyles={overlay:{position:"fixed",top:0,left:0,right:0,bottom:0,backgroundColor:"rgba(255, 255, 255, 0.75)"},content:{position:"absolute",top:"40px",left:"40px",right:"40px",bottom:"40px",border:"1px solid #ccc",background:"#fff",overflow:"auto",WebkitOverflowScrolling:"touch",borderRadius:"4px",outline:"none",padding:"20px"}},(0,p.polyfill)(P),e.default=P},8367:(t,e,s)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0});var i=Object.assign||function(t){for(var e=1;e<arguments.length;e++){var s=arguments[e];for(var i in s)Object.prototype.hasOwnProperty.call(s,i)&&(t[i]=s[i])}return t},a="function"===typeof Symbol&&"symbol"===typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"===typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},n=function(){function t(t,e){for(var s=0;s<e.length;s++){var i=e[s];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(t,i.key,i)}}return function(e,s,i){return s&&t(e.prototype,s),i&&t(e,i),e}}(),r=s(2791),o=g(s(2007)),l=m(s(8844)),c=g(s(870)),d=m(s(5858)),h=m(s(6554)),u=s(3663),p=g(u),f=g(s(8484));function m(t){if(t&&t.__esModule)return t;var e={};if(null!=t)for(var s in t)Object.prototype.hasOwnProperty.call(t,s)&&(e[s]=t[s]);return e.default=t,e}function g(t){return t&&t.__esModule?t:{default:t}}s(5670);var v={overlay:"ReactModal__Overlay",content:"ReactModal__Content"},_=0,b=function(t){function e(t){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,e);var s=function(t,e){if(!t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!e||"object"!==typeof e&&"function"!==typeof e?t:e}(this,(e.__proto__||Object.getPrototypeOf(e)).call(this,t));return s.setOverlayRef=function(t){s.overlay=t,s.props.overlayRef&&s.props.overlayRef(t)},s.setContentRef=function(t){s.content=t,s.props.contentRef&&s.props.contentRef(t)},s.afterClose=function(){var t=s.props,e=t.appElement,i=t.ariaHideApp,a=t.htmlOpenClassName,n=t.bodyOpenClassName,r=t.parentSelector,o=r&&r().ownerDocument||document;n&&h.remove(o.body,n),a&&h.remove(o.getElementsByTagName("html")[0],a),i&&_>0&&0===(_-=1)&&d.show(e),s.props.shouldFocusAfterRender&&(s.props.shouldReturnFocusAfterClose?(l.returnFocus(s.props.preventScroll),l.teardownScopedFocus()):l.popWithoutFocus()),s.props.onAfterClose&&s.props.onAfterClose(),f.default.deregister(s)},s.open=function(){s.beforeOpen(),s.state.afterOpen&&s.state.beforeClose?(clearTimeout(s.closeTimer),s.setState({beforeClose:!1})):(s.props.shouldFocusAfterRender&&(l.setupScopedFocus(s.node),l.markForFocusLater()),s.setState({isOpen:!0},(function(){s.openAnimationFrame=requestAnimationFrame((function(){s.setState({afterOpen:!0}),s.props.isOpen&&s.props.onAfterOpen&&s.props.onAfterOpen({overlayEl:s.overlay,contentEl:s.content})}))})))},s.close=function(){s.props.closeTimeoutMS>0?s.closeWithTimeout():s.closeWithoutTimeout()},s.focusContent=function(){return s.content&&!s.contentHasFocus()&&s.content.focus({preventScroll:!0})},s.closeWithTimeout=function(){var t=Date.now()+s.props.closeTimeoutMS;s.setState({beforeClose:!0,closesAt:t},(function(){s.closeTimer=setTimeout(s.closeWithoutTimeout,s.state.closesAt-Date.now())}))},s.closeWithoutTimeout=function(){s.setState({beforeClose:!1,isOpen:!1,afterOpen:!1,closesAt:null},s.afterClose)},s.handleKeyDown=function(t){(function(t){return"Tab"===t.code||9===t.keyCode})(t)&&(0,c.default)(s.content,t),s.props.shouldCloseOnEsc&&function(t){return"Escape"===t.code||27===t.keyCode}(t)&&(t.stopPropagation(),s.requestClose(t))},s.handleOverlayOnClick=function(t){null===s.shouldClose&&(s.shouldClose=!0),s.shouldClose&&s.props.shouldCloseOnOverlayClick&&(s.ownerHandlesClose()?s.requestClose(t):s.focusContent()),s.shouldClose=null},s.handleContentOnMouseUp=function(){s.shouldClose=!1},s.handleOverlayOnMouseDown=function(t){s.props.shouldCloseOnOverlayClick||t.target!=s.overlay||t.preventDefault()},s.handleContentOnClick=function(){s.shouldClose=!1},s.handleContentOnMouseDown=function(){s.shouldClose=!1},s.requestClose=function(t){return s.ownerHandlesClose()&&s.props.onRequestClose(t)},s.ownerHandlesClose=function(){return s.props.onRequestClose},s.shouldBeClosed=function(){return!s.state.isOpen&&!s.state.beforeClose},s.contentHasFocus=function(){return document.activeElement===s.content||s.content.contains(document.activeElement)},s.buildClassName=function(t,e){var i="object"===("undefined"===typeof e?"undefined":a(e))?e:{base:v[t],afterOpen:v[t]+"--after-open",beforeClose:v[t]+"--before-close"},n=i.base;return s.state.afterOpen&&(n=n+" "+i.afterOpen),s.state.beforeClose&&(n=n+" "+i.beforeClose),"string"===typeof e&&e?n+" "+e:n},s.attributesFromObject=function(t,e){return Object.keys(e).reduce((function(s,i){return s[t+"-"+i]=e[i],s}),{})},s.state={afterOpen:!1,beforeClose:!1},s.shouldClose=null,s.moveFromContentToOverlay=null,s}return function(t,e){if("function"!==typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}(e,t),n(e,[{key:"componentDidMount",value:function(){this.props.isOpen&&this.open()}},{key:"componentDidUpdate",value:function(t,e){this.props.isOpen&&!t.isOpen?this.open():!this.props.isOpen&&t.isOpen&&this.close(),this.props.shouldFocusAfterRender&&this.state.isOpen&&!e.isOpen&&this.focusContent()}},{key:"componentWillUnmount",value:function(){this.state.isOpen&&this.afterClose(),clearTimeout(this.closeTimer),cancelAnimationFrame(this.openAnimationFrame)}},{key:"beforeOpen",value:function(){var t=this.props,e=t.appElement,s=t.ariaHideApp,i=t.htmlOpenClassName,a=t.bodyOpenClassName,n=t.parentSelector,r=n&&n().ownerDocument||document;a&&h.add(r.body,a),i&&h.add(r.getElementsByTagName("html")[0],i),s&&(_+=1,d.hide(e)),f.default.register(this)}},{key:"render",value:function(){var t=this.props,e=t.id,s=t.className,a=t.overlayClassName,n=t.defaultStyles,r=t.children,o=s?{}:n.content,l=a?{}:n.overlay;if(this.shouldBeClosed())return null;var c={ref:this.setOverlayRef,className:this.buildClassName("overlay",a),style:i({},l,this.props.style.overlay),onClick:this.handleOverlayOnClick,onMouseDown:this.handleOverlayOnMouseDown},d=i({id:e,ref:this.setContentRef,style:i({},o,this.props.style.content),className:this.buildClassName("content",s),tabIndex:"-1",onKeyDown:this.handleKeyDown,onMouseDown:this.handleContentOnMouseDown,onMouseUp:this.handleContentOnMouseUp,onClick:this.handleContentOnClick,role:this.props.role,"aria-label":this.props.contentLabel},this.attributesFromObject("aria",i({modal:!0},this.props.aria)),this.attributesFromObject("data",this.props.data||{}),{"data-testid":this.props.testId}),h=this.props.contentElement(d,r);return this.props.overlayElement(c,h)}}]),e}(r.Component);b.defaultProps={style:{overlay:{},content:{}},defaultStyles:{}},b.propTypes={isOpen:o.default.bool.isRequired,defaultStyles:o.default.shape({content:o.default.object,overlay:o.default.object}),style:o.default.shape({content:o.default.object,overlay:o.default.object}),className:o.default.oneOfType([o.default.string,o.default.object]),overlayClassName:o.default.oneOfType([o.default.string,o.default.object]),parentSelector:o.default.func,bodyOpenClassName:o.default.string,htmlOpenClassName:o.default.string,ariaHideApp:o.default.bool,appElement:o.default.oneOfType([o.default.instanceOf(p.default),o.default.instanceOf(u.SafeHTMLCollection),o.default.instanceOf(u.SafeNodeList),o.default.arrayOf(o.default.instanceOf(p.default))]),onAfterOpen:o.default.func,onAfterClose:o.default.func,onRequestClose:o.default.func,closeTimeoutMS:o.default.number,shouldFocusAfterRender:o.default.bool,shouldCloseOnOverlayClick:o.default.bool,shouldReturnFocusAfterClose:o.default.bool,preventScroll:o.default.bool,role:o.default.string,contentLabel:o.default.string,aria:o.default.object,data:o.default.object,children:o.default.node,shouldCloseOnEsc:o.default.bool,overlayRef:o.default.func,contentRef:o.default.func,id:o.default.string,overlayElement:o.default.func,contentElement:o.default.func,testId:o.default.string},e.default=b,t.exports=e.default},5858:(t,e,s)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.resetState=function(){o&&(o.removeAttribute?o.removeAttribute("aria-hidden"):null!=o.length?o.forEach((function(t){return t.removeAttribute("aria-hidden")})):document.querySelectorAll(o).forEach((function(t){return t.removeAttribute("aria-hidden")})));o=null},e.log=function(){0},e.assertNodeList=l,e.setElement=function(t){var e=t;if("string"===typeof e&&r.canUseDOM){var s=document.querySelectorAll(e);l(s,e),e=s}return o=e||o},e.validateElement=c,e.hide=function(t){var e=!0,s=!1,i=void 0;try{for(var a,n=c(t)[Symbol.iterator]();!(e=(a=n.next()).done);e=!0){a.value.setAttribute("aria-hidden","true")}}catch(r){s=!0,i=r}finally{try{!e&&n.return&&n.return()}finally{if(s)throw i}}},e.show=function(t){var e=!0,s=!1,i=void 0;try{for(var a,n=c(t)[Symbol.iterator]();!(e=(a=n.next()).done);e=!0){a.value.removeAttribute("aria-hidden")}}catch(r){s=!0,i=r}finally{try{!e&&n.return&&n.return()}finally{if(s)throw i}}},e.documentNotReadyOrSSRTesting=function(){o=null};var i,a=s(2391),n=(i=a)&&i.__esModule?i:{default:i},r=s(3663);var o=null;function l(t,e){if(!t||!t.length)throw new Error("react-modal: No elements were found for selector "+e+".")}function c(t){var e=t||o;return e?Array.isArray(e)||e instanceof HTMLCollection||e instanceof NodeList?e:[e]:((0,n.default)(!1,["react-modal: App element is not defined.","Please use `Modal.setAppElement(el)` or set `appElement={el}`.","This is needed so screen readers don't see main content","when modal is opened. It is not recommended, but you can opt-out","by setting `ariaHideApp={false}`."].join(" ")),[])}},5670:(t,e,s)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.resetState=function(){for(var t=[r,o],e=0;e<t.length;e++){var s=t[e];s&&(s.parentNode&&s.parentNode.removeChild(s))}r=o=null,l=[]},e.log=function(){console.log("bodyTrap ----------"),console.log(l.length);for(var t=[r,o],e=0;e<t.length;e++){var s=t[e]||{};console.log(s.nodeName,s.className,s.id)}console.log("edn bodyTrap ----------")};var i,a=s(8484),n=(i=a)&&i.__esModule?i:{default:i};var r=void 0,o=void 0,l=[];function c(){0!==l.length&&l[l.length-1].focusContent()}n.default.subscribe((function(t,e){r||o||((r=document.createElement("div")).setAttribute("data-react-modal-body-trap",""),r.style.position="absolute",r.style.opacity="0",r.setAttribute("tabindex","0"),r.addEventListener("focus",c),(o=r.cloneNode()).addEventListener("focus",c)),(l=e).length>0?(document.body.firstChild!==r&&document.body.insertBefore(r,document.body.firstChild),document.body.lastChild!==o&&document.body.appendChild(o)):(r.parentElement&&r.parentElement.removeChild(r),o.parentElement&&o.parentElement.removeChild(o))}))},6554:(t,e)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.resetState=function(){var t=document.getElementsByTagName("html")[0];for(var e in s)a(t,s[e]);var n=document.body;for(var r in i)a(n,i[r]);s={},i={}},e.log=function(){0};var s={},i={};function a(t,e){t.classList.remove(e)}e.add=function(t,e){return a=t.classList,n="html"==t.nodeName.toLowerCase()?s:i,void e.split(" ").forEach((function(t){!function(t,e){t[e]||(t[e]=0),t[e]+=1}(n,t),a.add(t)}));var a,n},e.remove=function(t,e){return a=t.classList,n="html"==t.nodeName.toLowerCase()?s:i,void e.split(" ").forEach((function(t){!function(t,e){t[e]&&(t[e]-=1)}(n,t),0===n[t]&&a.remove(t)}));var a,n}},8844:(t,e,s)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.resetState=function(){r=[]},e.log=function(){0},e.handleBlur=c,e.handleFocus=d,e.markForFocusLater=function(){r.push(document.activeElement)},e.returnFocus=function(){var t=arguments.length>0&&void 0!==arguments[0]&&arguments[0],e=null;try{return void(0!==r.length&&(e=r.pop()).focus({preventScroll:t}))}catch(s){console.warn(["You tried to return focus to",e,"but it is not in the DOM anymore"].join(" "))}},e.popWithoutFocus=function(){r.length>0&&r.pop()},e.setupScopedFocus=function(t){o=t,window.addEventListener?(window.addEventListener("blur",c,!1),document.addEventListener("focus",d,!0)):(window.attachEvent("onBlur",c),document.attachEvent("onFocus",d))},e.teardownScopedFocus=function(){o=null,window.addEventListener?(window.removeEventListener("blur",c),document.removeEventListener("focus",d)):(window.detachEvent("onBlur",c),document.detachEvent("onFocus",d))};var i,a=s(9750),n=(i=a)&&i.__esModule?i:{default:i};var r=[],o=null,l=!1;function c(){l=!0}function d(){if(l){if(l=!1,!o)return;setTimeout((function(){o.contains(document.activeElement)||((0,n.default)(o)[0]||o).focus()}),0)}}},8484:(t,e)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.log=function(){console.log("portalOpenInstances ----------"),console.log(i.openInstances.length),i.openInstances.forEach((function(t){return console.log(t)})),console.log("end portalOpenInstances ----------")},e.resetState=function(){i=new s};var s=function t(){var e=this;!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),this.register=function(t){-1===e.openInstances.indexOf(t)&&(e.openInstances.push(t),e.emit("register"))},this.deregister=function(t){var s=e.openInstances.indexOf(t);-1!==s&&(e.openInstances.splice(s,1),e.emit("deregister"))},this.subscribe=function(t){e.subscribers.push(t)},this.emit=function(t){e.subscribers.forEach((function(s){return s(t,e.openInstances.slice())}))},this.openInstances=[],this.subscribers=[]},i=new s;e.default=i},3663:(t,e,s)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.canUseDOM=e.SafeNodeList=e.SafeHTMLCollection=void 0;var i,a=s(2618);var n=((i=a)&&i.__esModule?i:{default:i}).default,r=n.canUseDOM?window.HTMLElement:{};e.SafeHTMLCollection=n.canUseDOM?window.HTMLCollection:{},e.SafeNodeList=n.canUseDOM?window.NodeList:{},e.canUseDOM=n.canUseDOM;e.default=r},870:(t,e,s)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.default=function(t,e){var s=(0,n.default)(t);if(!s.length)return void e.preventDefault();var i=void 0,a=e.shiftKey,o=s[0],l=s[s.length-1],c=r();if(t===c){if(!a)return;i=l}l!==c||a||(i=o);o===c&&a&&(i=l);if(i)return e.preventDefault(),void i.focus();var d=/(\bChrome\b|\bSafari\b)\//.exec(navigator.userAgent);if(null==d||"Chrome"==d[1]||null!=/\biPod\b|\biPad\b/g.exec(navigator.userAgent))return;var h=s.indexOf(c);h>-1&&(h+=a?-1:1);if("undefined"===typeof(i=s[h]))return e.preventDefault(),void(i=a?l:o).focus();e.preventDefault(),i.focus()};var i,a=s(9750),n=(i=a)&&i.__esModule?i:{default:i};function r(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:document;return t.activeElement.shadowRoot?r(t.activeElement.shadowRoot):t.activeElement}t.exports=e.default},9750:(t,e)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.default=function t(e){var s=[].slice.call(e.querySelectorAll("*"),0).reduce((function(e,s){return e.concat(s.shadowRoot?t(s.shadowRoot):[s])}),[]);return s.filter(o)};var s="none",i="contents",a=/^(input|select|textarea|button|object|iframe)$/;function n(t){var e=t.offsetWidth<=0&&t.offsetHeight<=0;if(e&&!t.innerHTML)return!0;try{var a=window.getComputedStyle(t),n=a.getPropertyValue("display");return e?n!==i&&function(t,e){return"visible"!==e.getPropertyValue("overflow")||t.scrollWidth<=0&&t.scrollHeight<=0}(t,a):n===s}catch(r){return console.warn("Failed to inspect element style"),!1}}function r(t,e){var s=t.nodeName.toLowerCase();return(a.test(s)&&!t.disabled||"a"===s&&t.href||e)&&function(t){for(var e=t,s=t.getRootNode&&t.getRootNode();e&&e!==document.body;){if(s&&e===s&&(e=s.host.parentNode),n(e))return!1;e=e.parentNode}return!0}(t)}function o(t){var e=t.getAttribute("tabindex");null===e&&(e=void 0);var s=isNaN(e);return(s||e>=0)&&r(t,!s)}t.exports=e.default},7948:(t,e,s)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0});var i,a=s(2240),n=(i=a)&&i.__esModule?i:{default:i};e.default=n.default,t.exports=e.default},2391:t=>{"use strict";var e=function(){};t.exports=e},6521:t=>{t.exports=function(t,e){if(e.has(t))throw new TypeError("Cannot initialize the same private elements twice on an object")},t.exports.__esModule=!0,t.exports.default=t.exports},8912:t=>{t.exports=function(t,e){return e.get?e.get.call(t):e.value},t.exports.__esModule=!0,t.exports.default=t.exports},3448:t=>{t.exports=function(t,e,s){if(e.set)e.set.call(t,s);else{if(!e.writable)throw new TypeError("attempted to set read only private field");e.value=s}},t.exports.__esModule=!0,t.exports.default=t.exports},2625:t=>{t.exports=function(t,e){if(t!==e)throw new TypeError("Private static access of wrong provenance")},t.exports.__esModule=!0,t.exports.default=t.exports},2506:t=>{t.exports=function(t,e){if(void 0===t)throw new TypeError("attempted to "+e+" private static field before its declaration")},t.exports.__esModule=!0,t.exports.default=t.exports},4069:t=>{t.exports=function(t,e,s){if(!e.has(t))throw new TypeError("attempted to "+s+" private field on non-instance");return e.get(t)},t.exports.__esModule=!0,t.exports.default=t.exports},468:(t,e,s)=>{var i=s(8912),a=s(4069);t.exports=function(t,e){var s=a(t,e,"get");return i(t,s)},t.exports.__esModule=!0,t.exports.default=t.exports},9159:(t,e,s)=>{var i=s(6521);t.exports=function(t,e,s){i(t,e),e.set(t,s)},t.exports.__esModule=!0,t.exports.default=t.exports},5661:(t,e,s)=>{var i=s(3448),a=s(4069);t.exports=function(t,e,s){var n=a(t,e,"set");return i(t,n,s),s},t.exports.__esModule=!0,t.exports.default=t.exports},4467:t=>{t.exports=function(t,e,s){if(!e.has(t))throw new TypeError("attempted to get private field on non-instance");return s},t.exports.__esModule=!0,t.exports.default=t.exports},1860:(t,e,s)=>{var i=s(6521);t.exports=function(t,e){i(t,e),e.add(t)},t.exports.__esModule=!0,t.exports.default=t.exports},1280:(t,e,s)=>{var i=s(8912),a=s(2625),n=s(2506);t.exports=function(t,e,s){return a(t,e),n(s,"get"),i(t,s)},t.exports.__esModule=!0,t.exports.default=t.exports},2306:(t,e,s)=>{var i=s(3448),a=s(2625),n=s(2506);t.exports=function(t,e,s,r){return a(t,e),n(s,"set"),i(t,s,r),r},t.exports.__esModule=!0,t.exports.default=t.exports},5764:(t,e,s)=>{var i=s(2625);t.exports=function(t,e,s){return i(t,e),s},t.exports.__esModule=!0,t.exports.default=t.exports},8416:(t,e,s)=>{var i=s(4062);t.exports=function(t,e,s){return(e=i(e))in t?Object.defineProperty(t,e,{value:s,enumerable:!0,configurable:!0,writable:!0}):t[e]=s,t},t.exports.__esModule=!0,t.exports.default=t.exports},2122:(t,e,s)=>{var i=s(8416);function a(t,e){var s=Object.keys(t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);e&&(i=i.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),s.push.apply(s,i)}return s}t.exports=function(t){for(var e=1;e<arguments.length;e++){var s=null!=arguments[e]?arguments[e]:{};e%2?a(Object(s),!0).forEach((function(e){i(t,e,s[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(s)):a(Object(s)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(s,e))}))}return t},t.exports.__esModule=!0,t.exports.default=t.exports},5036:(t,e,s)=>{var i=s(8698).default;t.exports=function(t,e){if("object"!==i(t)||null===t)return t;var s=t[Symbol.toPrimitive];if(void 0!==s){var a=s.call(t,e||"default");if("object"!==i(a))return a;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)},t.exports.__esModule=!0,t.exports.default=t.exports},4062:(t,e,s)=>{var i=s(8698).default,a=s(5036);t.exports=function(t){var e=a(t,"string");return"symbol"===i(e)?e:String(e)},t.exports.__esModule=!0,t.exports.default=t.exports},8698:t=>{function e(s){return t.exports=e="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},t.exports.__esModule=!0,t.exports.default=t.exports,e(s)}t.exports=e,t.exports.__esModule=!0,t.exports.default=t.exports},3791:(t,e,s)=>{"use strict";s.d(e,{M:()=>g});var i=s(2791),a=s(2199);function n(){const t=(0,i.useRef)(!1);return(0,a.L)((()=>(t.current=!0,()=>{t.current=!1})),[]),t}var r=s(8771);var o=s(131),l=s(1421);class c extends i.Component{getSnapshotBeforeUpdate(t){const e=this.props.childRef.current;if(e&&t.isPresent&&!this.props.isPresent){const t=this.props.sizeRef.current;t.height=e.offsetHeight||0,t.width=e.offsetWidth||0,t.top=e.offsetTop,t.left=e.offsetLeft}return null}componentDidUpdate(){}render(){return this.props.children}}function d(t){let{children:e,isPresent:s}=t;const a=(0,i.useId)(),n=(0,i.useRef)(null),r=(0,i.useRef)({width:0,height:0,top:0,left:0});return(0,i.useInsertionEffect)((()=>{const{width:t,height:e,top:i,left:o}=r.current;if(s||!n.current||!t||!e)return;n.current.dataset.motionPopId=a;const l=document.createElement("style");return document.head.appendChild(l),l.sheet&&l.sheet.insertRule('\n          [data-motion-pop-id="'.concat(a,'"] {\n            position: absolute !important;\n            width: ').concat(t,"px !important;\n            height: ").concat(e,"px !important;\n            top: ").concat(i,"px !important;\n            left: ").concat(o,"px !important;\n          }\n        ")),()=>{document.head.removeChild(l)}}),[s]),i.createElement(c,{isPresent:s,childRef:n,sizeRef:r},i.cloneElement(e,{ref:n}))}const h=t=>{let{children:e,initial:s,isPresent:a,onExitComplete:n,custom:r,presenceAffectsLayout:c,mode:h}=t;const p=(0,l.h)(u),f=(0,i.useId)(),m=(0,i.useMemo)((()=>({id:f,initial:s,isPresent:a,custom:r,onExitComplete:t=>{p.set(t,!0);for(const e of p.values())if(!e)return;n&&n()},register:t=>(p.set(t,!1),()=>p.delete(t))})),c?void 0:[a]);return(0,i.useMemo)((()=>{p.forEach(((t,e)=>p.set(e,!1)))}),[a]),i.useEffect((()=>{!a&&!p.size&&n&&n()}),[a]),"popLayout"===h&&(e=i.createElement(d,{isPresent:a},e)),i.createElement(o.O.Provider,{value:m},e)};function u(){return new Map}var p=s(7497);var f=s(5956);const m=t=>t.key||"";const g=t=>{let{children:e,custom:s,initial:o=!0,onExitComplete:l,exitBeforeEnter:c,presenceAffectsLayout:d=!0,mode:u="sync"}=t;(0,f.k)(!c,"Replace exitBeforeEnter with mode='wait'");const g=(0,i.useContext)(p.p).forceRender||function(){const t=n(),[e,s]=(0,i.useState)(0),a=(0,i.useCallback)((()=>{t.current&&s(e+1)}),[e]);return[(0,i.useCallback)((()=>r.Wi.postRender(a)),[a]),e]}()[0],v=n(),_=function(t){const e=[];return i.Children.forEach(t,(t=>{(0,i.isValidElement)(t)&&e.push(t)})),e}(e);let b=_;const y=(0,i.useRef)(new Map).current,A=(0,i.useRef)(b),P=(0,i.useRef)(new Map).current,S=(0,i.useRef)(!0);var x;if((0,a.L)((()=>{S.current=!1,function(t,e){t.forEach((t=>{const s=m(t);e.set(s,t)}))}(_,P),A.current=b})),x=()=>{S.current=!0,P.clear(),y.clear()},(0,i.useEffect)((()=>()=>x()),[]),S.current)return i.createElement(i.Fragment,null,b.map((t=>i.createElement(h,{key:m(t),isPresent:!0,initial:!!o&&void 0,presenceAffectsLayout:d,mode:u},t))));b=[...b];const F=A.current.map(m),w=_.map(m),C=F.length;for(let i=0;i<C;i++){const t=F[i];-1!==w.indexOf(t)||y.has(t)||y.set(t,void 0)}return"wait"===u&&y.size&&(b=[]),y.forEach(((t,e)=>{if(-1!==w.indexOf(e))return;const a=P.get(e);if(!a)return;const n=F.indexOf(e);let r=t;if(!r){const t=()=>{y.delete(e);const t=Array.from(P.keys()).filter((t=>!w.includes(t)));if(t.forEach((t=>P.delete(t))),A.current=_.filter((s=>{const i=m(s);return i===e||t.includes(i)})),!y.size){if(!1===v.current)return;g(),l&&l()}};r=i.createElement(h,{key:m(a),isPresent:!1,onExitComplete:t,custom:s,presenceAffectsLayout:d,mode:u},a),y.set(e,r)}b.splice(n,0,r)})),b=b.map((t=>{const e=t.key;return y.has(e)?t:i.createElement(h,{key:m(t),isPresent:!0,presenceAffectsLayout:d,mode:u},t)})),i.createElement(i.Fragment,null,y.size?b:b.map((t=>(0,i.cloneElement)(t))))}}}]);
//# sourceMappingURL=328.68fea5b0.chunk.js.map