{"ast": null, "code": "import KeyCode from \"rc-util/es/KeyCode\";\nimport * as React from 'react';\nconst onKeyDown = event => {\n  const {\n    keyCode\n  } = event;\n  if (keyCode === KeyCode.ENTER) {\n    event.stopPropagation();\n  }\n};\nconst FilterDropdownMenuWrapper = /*#__PURE__*/React.forwardRef((props, ref) => /*#__PURE__*/React.createElement(\"div\", {\n  className: props.className,\n  onClick: e => e.stopPropagation(),\n  onKeyDown: onKeyDown,\n  ref: ref\n}, props.children));\nif (process.env.NODE_ENV !== 'production') {\n  FilterDropdownMenuWrapper.displayName = 'FilterDropdownMenuWrapper';\n}\nexport default FilterDropdownMenuWrapper;", "map": {"version": 3, "names": ["KeyCode", "React", "onKeyDown", "event", "keyCode", "ENTER", "stopPropagation", "FilterDropdownMenuWrapper", "forwardRef", "props", "ref", "createElement", "className", "onClick", "e", "children", "process", "env", "NODE_ENV", "displayName"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/node_modules/antd/es/table/hooks/useFilter/FilterWrapper.js"], "sourcesContent": ["import KeyCode from \"rc-util/es/KeyCode\";\nimport * as React from 'react';\nconst onKeyDown = event => {\n  const {\n    keyCode\n  } = event;\n  if (keyCode === KeyCode.ENTER) {\n    event.stopPropagation();\n  }\n};\nconst FilterDropdownMenuWrapper = /*#__PURE__*/React.forwardRef((props, ref) => /*#__PURE__*/React.createElement(\"div\", {\n  className: props.className,\n  onClick: e => e.stopPropagation(),\n  onKeyDown: onKeyDown,\n  ref: ref\n}, props.children));\nif (process.env.NODE_ENV !== 'production') {\n  FilterDropdownMenuWrapper.displayName = 'FilterDropdownMenuWrapper';\n}\nexport default FilterDropdownMenuWrapper;"], "mappings": "AAAA,OAAOA,OAAO,MAAM,oBAAoB;AACxC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,MAAMC,SAAS,GAAGC,KAAK,IAAI;EACzB,MAAM;IACJC;EACF,CAAC,GAAGD,KAAK;EACT,IAAIC,OAAO,KAAKJ,OAAO,CAACK,KAAK,EAAE;IAC7BF,KAAK,CAACG,eAAe,CAAC,CAAC;EACzB;AACF,CAAC;AACD,MAAMC,yBAAyB,GAAG,aAAaN,KAAK,CAACO,UAAU,CAAC,CAACC,KAAK,EAAEC,GAAG,KAAK,aAAaT,KAAK,CAACU,aAAa,CAAC,KAAK,EAAE;EACtHC,SAAS,EAAEH,KAAK,CAACG,SAAS;EAC1BC,OAAO,EAAEC,CAAC,IAAIA,CAAC,CAACR,eAAe,CAAC,CAAC;EACjCJ,SAAS,EAAEA,SAAS;EACpBQ,GAAG,EAAEA;AACP,CAAC,EAAED,KAAK,CAACM,QAAQ,CAAC,CAAC;AACnB,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzCX,yBAAyB,CAACY,WAAW,GAAG,2BAA2B;AACrE;AACA,eAAeZ,yBAAyB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}