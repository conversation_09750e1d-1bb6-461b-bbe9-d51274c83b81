{"version": 3, "file": "static/js/79.f7c56f29.chunk.js", "mappings": "6JAAA,MAAQA,QAASC,GAAkBC,EAAQ,MAG9BC,EAAcC,UACvB,IAEI,aADuBH,EAAcI,KAAK,0BAA2BC,IACrDC,IACpB,CAAE,MAAOC,GACL,OAAOA,EAAMC,SAASF,IAC1B,GAISG,EAAWN,UACpB,IAEI,aADuBH,EAAcI,KAAK,uBAAwBC,IAClDC,IACpB,CAAE,MAAOC,GACL,OAAOA,EAAMC,SAASF,IAC1B,GAISI,EAAkBP,UAA4B,IAArB,KAAEQ,EAAI,MAAEC,GAAOC,EACjD,IAEI,aADuBb,EAAcc,IAAI,qCAADC,OAAsCJ,EAAI,WAAAI,OAAUH,KAC5EN,IACpB,CAAE,MAAOC,GACL,OAAOA,EAAMC,SAASF,IAC1B,GAISU,EAAiBb,UAC1B,IAEI,aADuBH,EAAciB,OAAO,8BAADF,OAA+BG,KAC1DZ,IACpB,CAAE,MAAOC,GACL,OAAOA,EAAMC,SAASF,IAC1B,GAISa,EAAiBhB,MAAOE,EAASa,KAC1C,IAEI,aADuBlB,EAAcoB,IAAI,8BAADL,OAA+BG,GAAcb,IACrEC,IACpB,CAAE,MAAOC,GACL,OAAOA,EAAMC,SAASF,IAC1B,GAISe,EAAoBlB,MAAOE,EAASa,KAC7C,IAEI,aADuBlB,EAAcoB,IAAI,kCAADL,OAAmCG,GAAcb,IACzEC,IACpB,CAAE,MAAOC,GACL,OAAOA,EAAMC,SAASF,IAC1B,E,wDC3CJ,QAdA,SAAkBO,GAAa,IAAZ,MAAES,GAAOT,EAC1B,MAAOU,EAAUC,IAAeC,EAAAA,EAAAA,WAAS,GAMzC,OALAC,EAAAA,EAAAA,YAAU,KACJC,OAAOC,WAAa,KACtBJ,GAAY,EACd,GACC,KAEDK,EAAAA,EAAAA,KAAA,OAAKC,UAAU,OAAMC,UACnBF,EAAAA,EAAAA,KAAA,MAAIC,UAAWP,EAAW,UAAY,GAAGQ,SAAET,KAGjD,C,qOCQA,MA8sBA,EA9sBcU,KACZ,MAAOC,EAASC,IAAcT,EAAAA,EAAAA,WAAS,IAChCU,EAAUC,IAAeX,EAAAA,EAAAA,UAAS,KAClCY,EAAWC,IAAgBb,EAAAA,EAAAA,UAAS,KACpCc,EAAiBC,IAAsBf,EAAAA,EAAAA,UAAS,CAAC,IACjDgB,EAAoBC,IAAyBjB,EAAAA,EAAAA,WAAS,IACtDkB,EAAiBC,IAAsBnB,EAAAA,EAAAA,UAAS,OAChDoB,EAAcC,IAAmBrB,EAAAA,EAAAA,UAAS,OAC1CsB,GAAQC,EAAAA,EAAKC,WACbC,EAAeC,IAAoB1B,EAAAA,EAAAA,WAAS,GAG7C2B,EAAgBA,KACpBC,EAAAA,EAAAA,MAAA,OAAKvB,UAAU,kBAAiBC,SAAA,EAC9BsB,EAAAA,EAAAA,MAAA,OAAKvB,UAAU,eAAcC,SAAA,EAC3BF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,qDACfD,EAAAA,EAAAA,KAAA,OAAKC,UAAU,oDAEjBD,EAAAA,EAAAA,KAAA,OAAKC,UAAU,gBAAeC,SAC3B,IAAIuB,MAAM,IAAIC,KAAI,CAACC,EAAGC,KACrB5B,EAAAA,EAAAA,KAAA,OAAaC,UAAU,2CAA0CC,UAC/DsB,EAAAA,EAAAA,MAAA,OAAKvB,UAAU,6BAA4BC,SAAA,EACzCF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,sDACfuB,EAAAA,EAAAA,MAAA,OAAKvB,UAAU,SAAQC,SAAA,EACrBF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,sDACfD,EAAAA,EAAAA,KAAA,OAAKC,UAAU,sDACfD,EAAAA,EAAAA,KAAA,OAAKC,UAAU,uDACfD,EAAAA,EAAAA,KAAA,OAAKC,UAAU,uDAPX2B,WAeXC,GAASV,EAAAA,EAAKC,UACfU,GAAWC,EAAAA,EAAAA,OACVC,EAAWC,IAAgBrC,EAAAA,EAAAA,UAAS,CAAC,IAErCsC,EAAaC,IAAkBvC,EAAAA,EAAAA,UAAS,IACxCwC,EAAYC,IAAiBzC,EAAAA,EAAAA,UAAS,IACtCb,IAASa,EAAAA,EAAAA,UAAS,KAClB0C,EAAgBC,IAAqB3C,EAAAA,EAAAA,UAAS,GAG/C4C,EAAsBA,KACR,CAAC,UAAW,YAAa,WACjCC,SAAQC,IAChB,IAAK,IAAIC,EAAI,EAAGA,GAAK,GAAIA,IACvBC,aAAaC,WAAW,mBAAD3D,OAAoBwD,EAAK,KAAAxD,OAAIyD,EAAC,KAAAzD,OAAIH,IACzD6D,aAAaC,WAAW,mBAAD3D,OAAoBwD,EAAK,KAAAxD,OAAIyD,EAAC,KAAAzD,OAAIH,EAAK,SAChE,GACA,EAGE+D,EAAiBxE,UACrB,IAEE,MAAMyE,GAAoB,OAARzC,QAAQ,IAARA,OAAQ,EAARA,EAAUoC,QAAS,UACnB,CAAC,UAAW,YAAa,WACjCD,SAAQC,IAChB,GAAIA,IAAUK,EAEZ,IAAK,IAAIJ,EAAI,EAAGA,GAAK,GAAIA,IACvBC,aAAaC,WAAW,mBAAD3D,OAAoBwD,EAAK,KAAAxD,OAAIyD,EAAC,KAAAzD,OAAIH,IACzD6D,aAAaC,WAAW,mBAAD3D,OAAoBwD,EAAK,KAAAxD,OAAIyD,EAAC,KAAAzD,OAAIH,EAAK,SAElE,IAIF,MAAMiE,EAAQ,mBAAA9D,OAAsB6D,EAAS,KAAA7D,OAAIJ,EAAI,KAAAI,OAAIH,GACnDkE,EAAaL,aAAaM,QAAQF,GAClCG,EAAYP,aAAaM,QAAQ,GAADhE,OAAI8D,EAAQ,UAC5CI,EAAMC,KAAKD,MAGjB,GAAIH,GAAcE,GAAcC,EAAME,SAASH,GAAc,IAAQ,CACnE,MAAMI,EAASC,KAAKC,MAAMR,GAI1B,OAHAxC,EAAa8C,EAAO/C,WACpB+B,EAAkBgB,EAAOjB,qBACzBD,EAAckB,EAAOnB,WAEvB,CAEAN,GAAS4B,EAAAA,EAAAA,OACT,MAAM/E,QAAiBE,EAAAA,EAAAA,IAAgB,CAAEC,OAAMC,UAC3CJ,EAASgF,SACXC,QAAQC,IAAIlF,EAASF,MACrBgC,EAAa9B,EAASF,MACtB8D,EAAkB5D,EAAS2D,gBAC3BD,EAAc1D,EAASyD,YAGvBQ,aAAakB,QAAQd,EAAUQ,KAAKO,UAAU,CAC5CvD,UAAW7B,EAASF,KACpB6D,eAAgB3D,EAAS2D,eACzBF,WAAYzD,EAASyD,cAEvBQ,aAAakB,QAAQ,GAAD5E,OAAI8D,EAAQ,SAASI,EAAIY,aAE7CC,EAAAA,GAAQvF,MAAMC,EAASsF,QAE3B,CAAE,MAAOvF,GACPuF,EAAAA,GAAQvF,MAAMA,EAAMuF,QACtB,CAAC,QACCnC,GAASoC,EAAAA,EAAAA,MACX,IAGFrE,EAAAA,EAAAA,YAAU,KAER2C,IACAM,EAAeZ,GAAaiC,SAAQ,KAClC7C,GAAiB,EAAM,GACvB,GACD,CAACY,EAAanD,KA4BjBc,EAAAA,EAAAA,YAAU,KACJ+C,aAAaM,QAAQ,UAvBP5E,WAClBwD,GAAS4B,EAAAA,EAAAA,OACT,IACE,MAAM/E,QAAiByF,EAAAA,EAAAA,MACnBzF,EAASgF,QACPhF,EAASF,KAAK2B,SAChBC,GAAW,GACXE,EAAY5B,EAASF,YACfqE,MAENzC,GAAW,GACXE,EAAY5B,EAASF,YACfqE,KAGRmB,EAAAA,GAAQvF,MAAMC,EAASsF,QAE3B,CAAE,MAAOvF,GACPuF,EAAAA,GAAQvF,MAAMA,EAAMuF,QACtB,GAKEI,EACF,GACC,IAEH,MA4BMC,EAAoBhG,UACxB,IACE,MAAME,EAAU,CACda,WAAYyB,EACZyD,KAAMC,EAAOD,MAET5F,QAAiBC,EAAAA,EAAAA,IAASJ,GAC5BG,EAASgF,SACXM,EAAAA,GAAQN,QAAQhF,EAASsF,SACzBlD,EAAmB,MACnBG,EAAKuD,cACLjC,UACMM,KAENmB,EAAAA,GAAQvF,MAAMC,EAASsF,QAE3B,CAAE,MAAOvF,GACPuF,EAAAA,GAAQvF,MAAMA,EAAMuF,QACtB,IAGFpE,EAAAA,EAAAA,YAAU,KACJiB,IAAoBkB,EAAUlB,IAChCmB,GAAcyC,IAAQC,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACjBD,GAAQ,IACX,CAAC5D,GAAkB8D,EAAAA,eAEvB,GACC,CAAC9D,EAAiBkB,KAErBnC,EAAAA,EAAAA,YAAU,KACJiB,GAAmBkB,EAAUlB,IAC/BkB,EAAUlB,GAAiB+D,QAAQC,eAAe,CAAEC,SAAU,UAChE,GACC,CAACjE,EAAiBkB,IAErB,MAyBMgD,EAAuB1G,UAC3B,IACE,MAAMK,QAAiBW,EAAAA,EAAAA,IAAekF,EAAQxD,EAAaiE,KACvDtG,EAASgF,SACXM,EAAAA,GAAQN,QAAQhF,EAASsF,SACzBhD,EAAgB,MAChBuB,UACMM,KAENmB,EAAAA,GAAQvF,MAAMC,EAASsF,QAE3B,CAAE,MAAOvF,GACPuF,EAAAA,GAAQvF,MAAMA,EAAMuF,QACtB,GAGIiB,EAAqBA,KACzBjE,EAAgB,GAAG,GAOrBpB,EAAAA,EAAAA,YAAU,KACJmB,EACFa,EAAMsD,eAAe,CACnB1F,MAAOuB,EAAavB,MACpB2F,KAAMpE,EAAaoE,OAGrBvD,EAAM4C,aACR,GACC,CAACzD,IAkBJ,OAAIK,GAAsC,IAArBb,EAAU6E,QACtBrF,EAAAA,EAAAA,KAACuB,EAAa,KAIrBvB,EAAAA,EAAAA,KAAA,OAAKC,UAAU,qEAAoEC,UACjFsB,EAAAA,EAAAA,MAAA,OAAKvB,UAAU,4EAA2EC,SAAA,EAExFsB,EAAAA,EAAAA,MAAA,OAAKvB,UAAU,qCAAoCC,SAAA,EACjDF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,kLAAiLC,UAC9LF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,iEAEfuB,EAAAA,EAAAA,MAAA,MAAIvB,UAAU,6EAA4EC,SAAA,CAAC,cAC/EF,EAAAA,EAAAA,KAAA,QAAMC,UAAU,6EAA4EC,SAAC,cAEzGF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,gGAA+FC,SAAC,wHAK7GsB,EAAAA,EAAAA,MAAA,UACE8D,QAASA,IAAMzE,GAAsB,GACrCZ,UAAU,yPAAwPC,SAAA,EAElQF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,wCAA0C,uBAM1DW,IACCY,EAAAA,EAAAA,MAAA,OAAKvB,UAAU,qGAAoGC,SAAA,EACjHsB,EAAAA,EAAAA,MAAA,OAAKvB,UAAU,iCAAgCC,SAAA,EAC7CF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,gIAA+HC,UAC5IF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,sDAEfD,EAAAA,EAAAA,KAAA,MAAIC,UAAU,8CAA6CC,SAAC,uBAG9DsB,EAAAA,EAAAA,MAACL,EAAAA,EAAI,CAACD,KAAMA,EAAMqE,SA5KFjH,UACxB,IACE,MAAMK,QAAiBN,EAAAA,EAAAA,IAAYmG,GAC/B7F,EAASgF,SACXM,EAAAA,GAAQN,QAAQhF,EAASsF,SACzBpD,GAAsB,GACtBK,EAAKuD,cACLjC,UACMM,KAENmB,EAAAA,GAAQvF,MAAMC,EAASsF,QAE3B,CAAE,MAAOvF,GACPuF,EAAAA,GAAQvF,MAAMA,EAAMuF,QACtB,GA8JuDuB,OAAO,WAAWvF,UAAU,cAAaC,SAAA,EACtFF,EAAAA,EAAAA,KAACmB,EAAAA,EAAKsE,KAAI,CACRC,KAAK,QACLC,MAAM,iBACNC,MAAO,CAAC,CAAEC,UAAU,EAAM5B,QAAS,qCAAsC/D,UAEzEF,EAAAA,EAAAA,KAAC8F,EAAAA,QAAK,CACJC,YAAY,+BACZ9F,UAAU,oBAGdD,EAAAA,EAAAA,KAACmB,EAAAA,EAAKsE,KAAI,CACRC,KAAK,OACLC,MAAM,mBACNC,MAAO,CAAC,CAAEC,UAAU,EAAM5B,QAAS,oDAAqD/D,UAExFF,EAAAA,EAAAA,KAAC8F,EAAAA,QAAME,SAAQ,CACbC,KAAM,EACNF,YAAY,yGACZ9F,UAAU,iBAGdD,EAAAA,EAAAA,KAACmB,EAAAA,EAAKsE,KAAI,CAACxF,UAAU,OAAMC,UACzBsB,EAAAA,EAAAA,MAAA,OAAKvB,UAAU,8BAA6BC,SAAA,EAC1CsB,EAAAA,EAAAA,MAAC0E,EAAAA,GAAM,CACLC,KAAK,UACLC,SAAS,SACTnG,UAAU,wGAAuGC,SAAA,EAEjHF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,4BAA8B,oBAG7CD,EAAAA,EAAAA,KAACkG,EAAAA,GAAM,CACLZ,QAxGMe,KACtBxF,GAAsB,GACtBK,EAAKuD,aAAa,EAuGFxE,UAAU,gFAA+EC,SAC1F,sBAUW,IAArBM,EAAU6E,SACT7D,EAAAA,EAAAA,MAAA,OAAKvB,UAAU,oBAAmBC,SAAA,EAChCF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,kFAAiFC,UAC9FF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,4DAEfD,EAAAA,EAAAA,KAAA,KAAGC,UAAU,gBAAeC,SAAC,+BAKjCF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,yBAAwBC,SACpCM,EAAU8F,QAAOC,GAAYA,GAAYA,EAASC,OAAM9E,KAAK6E,IAAQ,IAAAE,EAAAC,EAAA,OACpElF,EAAAA,EAAAA,MAAA,OAAwBvB,UAAU,kIAAiIC,SAAA,EAEjKF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,sCAAqCC,UAClDsB,EAAAA,EAAAA,MAAA,OAAKvB,UAAU,mCAAkCC,SAAA,EAC/CsB,EAAAA,EAAAA,MAAA,OAAKvB,UAAU,2CAA0CC,SAAA,EACvDsB,EAAAA,EAAAA,MAAA,OAAKvB,UAAU,WAAUC,SAAA,EACvBF,EAAAA,EAAAA,KAAC2G,EAAAA,EAAc,CACbH,KAAMD,EAASC,KACfI,KAAK,KACLC,kBAAkB,EAClBC,MAAO,CACLC,MAAO,OACPC,OAAQ,UAIXT,EAASC,MAAQD,EAASC,KAAKS,WAC9BjH,EAAAA,EAAAA,KAAA,OACE8G,MAAO,CACLI,SAAU,WACVC,OAAQ,OACRC,MAAO,OACPL,MAAO,OACPC,OAAQ,OACRK,gBAAiB,UACjBC,aAAc,MACdC,OAAQ,oBACRC,UAAW,mCACXC,OAAQ,IAEVhI,MAAM,eAIZ+B,EAAAA,EAAAA,MAAA,OAAKvB,UAAU,iBAAgBC,SAAA,EAC7BF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,4DAA2DC,UAAe,QAAbuG,EAAAF,EAASC,YAAI,IAAAC,OAAA,EAAbA,EAAef,OAAQ,kBAClG1F,EAAAA,EAAAA,KAAA,KAAGC,UAAU,mCAAkCC,SAC5C,IAAImD,KAAKkD,EAASmB,WAAWC,mBAAmB,QAAS,CACxDC,KAAM,UACNC,MAAO,QACPC,IAAK,UACLC,KAAM,UACNC,OAAQ,qBAOhBhI,EAAAA,EAAAA,KAAA,OAAKC,UAAU,gDAA+CC,UAC1DI,EAAS2E,OAAqB,QAAlByB,EAAKH,EAASC,YAAI,IAAAE,OAAA,EAAbA,EAAezB,MAAO3E,EAASF,WAChDoB,EAAAA,EAAAA,MAAAyG,EAAAA,SAAA,CAAA/H,SAAA,EACEF,EAAAA,EAAAA,KAAA,UACEsF,QAASA,IAjObiB,KAClBtF,EAAgBsF,EAAS,EAgOY2B,CAAW3B,GAC1BtG,UAAU,+GAA8GC,UAExHF,EAAAA,EAAAA,KAACmI,EAAAA,IAAW,CAAClI,UAAU,6BAEzBD,EAAAA,EAAAA,KAAA,UACEsF,QAASA,IAnOZhH,WACnB,IAIE,IAHsBwB,OAAOsI,QAC3B,kDAGA,OAEF,MAAMzJ,QAAiBQ,EAAAA,EAAAA,IAAeoH,EAAStB,KAC3CtG,EAASgF,SACXM,EAAAA,GAAQN,QAAQhF,EAASsF,SACzBzB,UACMM,KAENmB,EAAAA,GAAQvF,MAAMC,EAASsF,QAE3B,CAAE,MAAOvF,GACPuF,EAAAA,GAAQvF,MAAMA,EAAMuF,QACtB,GAiNqCoE,CAAa9B,GAC5BtG,UAAU,6GAA4GC,UAEtHF,EAAAA,EAAAA,KAACsI,EAAAA,IAAQ,CAACrI,UAAU,uCAShCuB,EAAAA,EAAAA,MAAA,OAAKvB,UAAU,aAAYC,SAAA,EACzBF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,gEAA+DC,SAAEqG,EAAS9G,SACxFO,EAAAA,EAAAA,KAAA,KAAGC,UAAU,qCAAoCC,SAAEqG,EAASnB,QAG5D5D,EAAAA,EAAAA,MAAA,OAAKvB,UAAU,oHAAmHC,SAAA,EAChIsB,EAAAA,EAAAA,MAAA,OAAKvB,UAAU,2DAA0DC,SAAA,EACvEsB,EAAAA,EAAAA,MAAA,UACE8D,QAASA,KAAMiD,OA3TZlJ,EA2T0BkH,EAAStB,SA1TxDtE,GAAoB6H,IAAmB7D,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GAClC6D,GAAmB,IACtB,CAACnJ,IAAcmJ,EAAoBnJ,OAHhBA,KA2TwC,EAC3CY,UAAU,0KAAyKC,SAAA,EAEnLF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,kDACZS,EAAgB6F,EAAStB,KAAO,eAAiB,mBAEpDzD,EAAAA,EAAAA,MAAA,UACE8D,QAASA,KAAMmD,OA1SdpJ,EA0S0BkH,EAAStB,SAzStDlE,EAAmB1B,GADAA,KA0SwC,EACzCY,UAAU,4KAA2KC,SAAA,EAErLF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,oDAAsD,eAKvEuB,EAAAA,EAAAA,MAAA,OAAKvB,UAAU,8FAA6FC,SAAA,EAC1GF,EAAAA,EAAAA,KAAC0I,EAAAA,IAAS,CAACzI,UAAU,sDACrBD,EAAAA,EAAAA,KAAA,QAAMC,UAAU,+CAA8CC,SAAEqG,EAASoC,QAAQtD,kBAMtFrE,GAAgBA,EAAaiE,MAAQsB,EAAStB,MAC7CzD,EAAAA,EAAAA,MAACL,EAAAA,EAAI,CACLD,KAAMW,EACN0D,SAAUP,EACVQ,OAAO,WACPoD,cAAe,CACbnJ,MAAOuB,EAAavB,MACpB2F,KAAMpE,EAAaoE,MACnBlF,SAAA,EAEFF,EAAAA,EAAAA,KAACmB,EAAAA,EAAKsE,KAAI,CACRC,KAAK,QACLC,MAAM,QACNC,MAAO,CACL,CAAEC,UAAU,EAAM5B,QAAS,2BAC3B/D,UAEFF,EAAAA,EAAAA,KAAC8F,EAAAA,QAAK,CAACgB,MAAO,CAAE+B,QAAS,kBAE3B7I,EAAAA,EAAAA,KAACmB,EAAAA,EAAKsE,KAAI,CACRC,KAAK,OACLC,MAAM,OACNC,MAAO,CAAC,CAAEC,UAAU,EAAM5B,QAAS,0BAA2B/D,UAE9DF,EAAAA,EAAAA,KAAC8F,EAAAA,QAAME,SAAQ,OAEjBxE,EAAAA,EAAAA,MAACL,EAAAA,EAAKsE,KAAI,CAAAvF,SAAA,EACRF,EAAAA,EAAAA,KAACkG,EAAAA,GAAM,CAACC,KAAK,UAAUC,SAAS,SAAQlG,SAAC,qBAGzCF,EAAAA,EAAAA,KAACkG,EAAAA,GAAM,CACLZ,QAASJ,EACT4B,MAAO,CAAEgC,WAAY,IAAK5I,SAC3B,iBAMNQ,EAAgB6F,EAAStB,OACxBzD,EAAAA,EAAAA,MAAA,OAAKvB,UAAU,qFAAoFC,SAAA,EACjGsB,EAAAA,EAAAA,MAAA,MAAIvB,UAAU,kFAAiFC,SAAA,EAC7FF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,2DAA6D,YAChEsG,EAASoC,QAAQrC,QAAOyC,GAASA,GAASA,EAAMvC,OAAMnB,OAAO,OAExEkB,EAASoC,QAAQrC,QAAOyC,GAASA,GAASA,EAAMvC,OAAM9E,KAAI,CAACqH,EAAOC,KAAK,IAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAA,OACtEvJ,EAAAA,EAAAA,KAAA,OAEEC,UAAS,uDAAAf,OACG,QAAV+J,EAAAF,EAAMvC,YAAI,IAAAyC,GAAVA,EAAY7I,QACR,iCACA2I,EAAMS,WACN,+BACA,mBACHtJ,UAEHsB,EAAAA,EAAAA,MAAA,OAAKvB,UAAU,0CAAyCC,SAAA,EAEtDsB,EAAAA,EAAAA,MAAA,OAAKvB,UAAU,yBAAwBC,SAAA,EACrCF,EAAAA,EAAAA,KAAC2G,EAAAA,EAAc,CACbH,KAAMuC,EAAMvC,KACZI,KAAK,KACLC,kBAAkB,EAClBC,MAAO,CACLC,MAAO,OACPC,OAAQ,QAEV/G,UAAU,kBAGX8I,EAAMvC,MAAQuC,EAAMvC,KAAKS,WACxBjH,EAAAA,EAAAA,KAAA,OACE8G,MAAO,CACLI,SAAU,WACVC,OAAQ,OACRC,MAAO,OACPL,MAAO,MACPC,OAAQ,MACRK,gBAAiB,UACjBC,aAAc,MACdC,OAAQ,oBACRC,UAAW,mCACXC,OAAQ,IAEVxH,UAAU,gBACVR,MAAM,eAMZ+B,EAAAA,EAAAA,MAAA,OAAKvB,UAAU,iBAAgBC,SAAA,EAE7BsB,EAAAA,EAAAA,MAAA,OAAKvB,UAAU,8DAA6DC,SAAA,EAC1EsB,EAAAA,EAAAA,MAAA,OAAKvB,UAAU,0DAAyDC,SAAA,EACtEF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,4DAA2DC,UAAY,QAAVgJ,EAAAH,EAAMvC,YAAI,IAAA0C,OAAA,EAAVA,EAAYxD,OAAQ,kBACpF,QAAVyD,EAAAJ,EAAMvC,YAAI,IAAA2C,OAAA,EAAVA,EAAY/I,WACXJ,EAAAA,EAAAA,KAAA,QAAMC,UAAU,iHAAgHC,SAAC,UAIlI6I,EAAMS,cAAyB,QAAXJ,EAACL,EAAMvC,YAAI,IAAA4C,GAAVA,EAAYhJ,WAChCoB,EAAAA,EAAAA,MAAA,OAAKvB,UAAU,8BAA6BC,SAAA,EAC1CF,EAAAA,EAAAA,KAACyJ,EAAAA,IAAO,CAACxJ,UAAU,0CACnBD,EAAAA,EAAAA,KAAA,QAAMC,UAAU,+GAA8GC,SAAC,oBAMrIF,EAAAA,EAAAA,KAAA,QAAMC,UAAU,qDAAoDC,SACjE,MACC,IACE,MAAMwJ,EAAO,IAAIrG,KAAK0F,EAAMrB,WAC5B,OAAIiC,MAAMD,EAAKE,WACN,eAEFF,EAAK/B,mBAAmB,QAAS,CACtCE,MAAO,QACPC,IAAK,UACLC,KAAM,UACNC,OAAQ,WAEZ,CAAE,MAAOtJ,GACP,MAAO,cACT,CACD,EAfA,SAoBLsB,EAAAA,EAAAA,KAAA,OAAKC,UAAS,6CAAAf,QACZ6J,EAAMS,YAAyB,QAAXH,EAACN,EAAMvC,YAAI,IAAA6C,GAAVA,EAAYjJ,QAEnB,QAAVkJ,EAAAP,EAAMvC,YAAI,IAAA8C,GAAVA,EAAYlJ,QACZ,8BACA,gBAHA,8BAIHF,SACA6I,EAAMxE,OAIRnE,KAAsB,QAAXmJ,EAACR,EAAMvC,YAAI,IAAA+C,GAAVA,EAAYnJ,WACvBJ,EAAAA,EAAAA,KAAA,OAAKC,UAAU,mBAAkBC,UAC/BF,EAAAA,EAAAA,KAAA,UACEsF,QAASA,IAvWVhH,OAAOe,EAAYwK,EAASC,KACrD,IACE,MAAMnL,QAAiBa,EAAAA,EAAAA,IAAkB,CAAEqK,UAASC,UAAUzK,GAC1DV,EAASgF,SACXM,EAAAA,GAAQN,QAAQhF,EAASsF,SACzBzB,UACMM,KAENmB,EAAAA,GAAQvF,MAAMC,EAASsF,QAE3B,CAAE,MAAOvF,GACPuF,EAAAA,GAAQvF,MAAMA,EAAMuF,QACtB,GA4V4B8F,CACExD,EAAStB,IACT8D,EAAM9D,KACL8D,EAAMS,YAGXvJ,UAAS,wGAAAf,OACP6J,EAAMS,WACF,2CACA,kDACHtJ,SAEF6I,EAAMS,WAAa,aAAe,qBA/GxCT,EAAM9D,IAqHP,QAIZjF,EAAAA,EAAAA,KAAA,OAAKgK,IAAKhI,EAAUuE,EAAStB,KAAMhF,UAAU,eAAcC,SACxDY,IAAoByF,EAAStB,MAC5BjF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,sEAAqEC,UAClFsB,EAAAA,EAAAA,MAACL,EAAAA,EAAI,CACHD,KAAMA,EACNqE,SAAUjB,EACVkB,OAAO,WAAUtF,SAAA,EAEjBF,EAAAA,EAAAA,KAACmB,EAAAA,EAAKsE,KAAI,CACRC,KAAK,OACLC,OAAO3F,EAAAA,EAAAA,KAAA,QAAMC,UAAU,mCAAkCC,SAAC,eAC1D0F,MAAO,CACL,CAAEC,UAAU,EAAM5B,QAAS,4BAC3B/D,UAEFF,EAAAA,EAAAA,KAAC8F,EAAAA,QAAME,SAAQ,CACbC,KAAM,EACNhG,UAAU,uBACV8F,YAAY,gCAGhB/F,EAAAA,EAAAA,KAACmB,EAAAA,EAAKsE,KAAI,CAACxF,UAAU,OAAMC,UACzBsB,EAAAA,EAAAA,MAAA,OAAKvB,UAAU,2CAA0CC,SAAA,EACvDF,EAAAA,EAAAA,KAACkG,EAAAA,GAAM,CACLC,KAAK,UACLC,SAAS,SACTnG,UAAU,mBACV2G,KAAK,QAAO1G,SACb,kBAGDF,EAAAA,EAAAA,KAACkG,EAAAA,GAAM,CACLZ,QAASA,IAAMvE,EAAmB,MAClCd,UAAU,mBACV2G,KAAK,QAAO1G,SACb,yBAjTHqG,EAAStB,IA0Tb,OAKVjF,EAAAA,EAAAA,KAACiK,EAAAA,EAAU,CACTpF,QAAS3C,EACTgI,MAAO5H,EACP6H,SAAUpL,EACVqL,SA/kBkBtL,IACxBqD,EAAerD,EAAK,EA+kBdgI,MAAO,CAAEuD,UAAW,OAAQC,UAAW,UACvCC,iBAAiB,QAGjB,C", "sources": ["apicalls/forum.js", "components/PageTitle.js", "pages/common/Forum/index.js"], "sourcesContent": ["const { default: axiosInstance } = require(\".\");\r\n\r\n// add question\r\nexport const addQuestion = async (payload) => {\r\n    try {\r\n        const response = await axiosInstance.post(\"/api/forum/add-question\", payload);\r\n        return response.data;\r\n    } catch (error) {\r\n        return error.response.data;\r\n    }\r\n}\r\n\r\n// add reply\r\nexport const addReply = async (payload) => {\r\n    try {\r\n        const response = await axiosInstance.post(\"/api/forum/add-reply\", payload);\r\n        return response.data;\r\n    } catch (error) {\r\n        return error.response.data;\r\n    }\r\n}\r\n\r\n// get all questions\r\nexport const getAllQuestions = async ({ page, limit }) => {\r\n    try {\r\n        const response = await axiosInstance.get(`/api/forum/get-all-questions?page=${page}&limit=${limit}`);\r\n        return response.data;\r\n    } catch (error) {\r\n        return error.response.data;\r\n    }\r\n}\r\n\r\n// delete question\r\nexport const deleteQuestion = async (questionId) => {\r\n    try {\r\n        const response = await axiosInstance.delete(`/api/forum/delete-question/${questionId}`);\r\n        return response.data;\r\n    } catch (error) {\r\n        return error.response.data;\r\n    }\r\n}\r\n\r\n// delete question\r\nexport const updateQuestion = async (payload, questionId) => {\r\n    try {\r\n        const response = await axiosInstance.put(`/api/forum/update-question/${questionId}`, payload);\r\n        return response.data;\r\n    } catch (error) {\r\n        return error.response.data;\r\n    }\r\n}\r\n\r\n// approve reply\r\nexport const updateReplyStatus = async (payload, questionId) => {\r\n    try {\r\n        const response = await axiosInstance.put(`/api/forum/update-reply-status/${questionId}`, payload);\r\n        return response.data;\r\n    } catch (error) {\r\n        return error.response.data;\r\n    }\r\n}\r\n\r\n\r\n\r\n", "import React, { useState, useEffect } from 'react'\r\n\r\nfunction PageTitle({ title }) {\r\n  const [isMobile, setIsMobile] = useState(false);\r\n  useEffect(() => {\r\n    if (window.innerWidth < 768) {\r\n      setIsMobile(true);\r\n    }\r\n  }, []);\r\n  return (\r\n    <div className='mt-2'>\r\n      <h1 className={isMobile ? 'text-lg' : ''}>{title}</h1>\r\n    </div>\r\n  )\r\n}\r\n\r\nexport default PageTitle", "import React, { useState, useEffect, useRef } from \"react\";\r\nimport \"./index.css\";\r\nimport { getUserInfo } from \"../../../apicalls/users\";\r\nimport { message, Button, Input, Form, Avatar, Pagination } from \"antd\";\r\nimport PageTitle from \"../../../components/PageTitle\";\r\nimport { useDispatch } from \"react-redux\";\r\nimport { HideLoading, ShowLoading } from \"../../../redux/loaderSlice\";\r\nimport ProfilePicture from \"../../../components/common/ProfilePicture\";\r\nimport OnlineStatusIndicator from \"../../../components/common/OnlineStatusIndicator\";\r\nimport {\r\n  addQuestion,\r\n  addReply,\r\n  getAllQuestions,\r\n  deleteQuestion,\r\n  updateQuestion,\r\n  updateReplyStatus,\r\n} from \"../../../apicalls/forum\";\r\nimport image from \"../../../assets/person.png\";\r\nimport { FaPencilAlt } from \"react-icons/fa\";\r\nimport { MdDelete, MdMessage } from \"react-icons/md\";\r\nimport { FaCheck } from \"react-icons/fa\";\r\n\r\nconst Forum = () => {\r\n  const [isAdmin, setIsAdmin] = useState(false);\r\n  const [userData, setUserData] = useState(\"\");\r\n  const [questions, setQuestions] = useState([]);\r\n  const [expandedReplies, setExpandedReplies] = useState({});\r\n  const [askQuestionVisible, setAskQuestionVisible] = useState(false);\r\n  const [replyQuestionId, setReplyQuestionId] = useState(null);\r\n  const [editQuestion, setEditQuestion] = useState(null);\r\n  const [form] = Form.useForm();\r\n  const [isInitialLoad, setIsInitialLoad] = useState(true);\r\n\r\n  // Skeleton loader component\r\n  const ForumSkeleton = () => (\r\n    <div className=\"forum-container\">\r\n      <div className=\"forum-header\">\r\n        <div className=\"h-8 bg-gray-200 rounded w-48 mb-4 animate-pulse\"></div>\r\n        <div className=\"h-10 bg-blue-100 rounded w-32 animate-pulse\"></div>\r\n      </div>\r\n      <div className=\"forum-content\">\r\n        {[...Array(5)].map((_, i) => (\r\n          <div key={i} className=\"question-card mb-4 p-4 border rounded-lg\">\r\n            <div className=\"flex items-start space-x-3\">\r\n              <div className=\"w-10 h-10 bg-gray-200 rounded-full animate-pulse\"></div>\r\n              <div className=\"flex-1\">\r\n                <div className=\"h-4 bg-gray-200 rounded w-3/4 mb-2 animate-pulse\"></div>\r\n                <div className=\"h-3 bg-gray-100 rounded w-1/2 mb-3 animate-pulse\"></div>\r\n                <div className=\"h-3 bg-gray-100 rounded w-full mb-2 animate-pulse\"></div>\r\n                <div className=\"h-3 bg-gray-100 rounded w-2/3 animate-pulse\"></div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        ))}\r\n      </div>\r\n    </div>\r\n  );\r\n  const [form2] = Form.useForm();\r\n  const dispatch = useDispatch();\r\n  const [replyRefs, setReplyRefs] = useState({});\r\n\r\n  const [currentPage, setCurrentPage] = useState(1);\r\n  const [totalPages, setTotalPages] = useState(1);\r\n  const [limit] = useState(10);\r\n  const [totalQuestions, setTotalQuestions] = useState(0);\r\n\r\n  // Function to clear all forum caches\r\n  const clearAllForumCaches = () => {\r\n    const allLevels = ['primary', 'secondary', 'advance'];\r\n    allLevels.forEach(level => {\r\n      for (let p = 1; p <= 20; p++) { // Clear up to 20 pages\r\n        localStorage.removeItem(`forum_questions_${level}_${p}_${limit}`);\r\n        localStorage.removeItem(`forum_questions_${level}_${p}_${limit}_time`);\r\n      }\r\n    });\r\n  };\r\n\r\n  const fetchQuestions = async (page) => {\r\n    try {\r\n      // Clear caches for other levels to prevent contamination\r\n      const userLevel = userData?.level || 'primary';\r\n      const allLevels = ['primary', 'secondary', 'advance'];\r\n      allLevels.forEach(level => {\r\n        if (level !== userLevel) {\r\n          // Clear cache for other levels\r\n          for (let p = 1; p <= 10; p++) { // Clear up to 10 pages\r\n            localStorage.removeItem(`forum_questions_${level}_${p}_${limit}`);\r\n            localStorage.removeItem(`forum_questions_${level}_${p}_${limit}_time`);\r\n          }\r\n        }\r\n      });\r\n\r\n      // Check cache first - make it level-specific to prevent cross-level contamination\r\n      const cacheKey = `forum_questions_${userLevel}_${page}_${limit}`;\r\n      const cachedData = localStorage.getItem(cacheKey);\r\n      const cacheTime = localStorage.getItem(`${cacheKey}_time`);\r\n      const now = Date.now();\r\n\r\n      // Use cache if less than 5 minutes old\r\n      if (cachedData && cacheTime && (now - parseInt(cacheTime)) < 300000) {\r\n        const cached = JSON.parse(cachedData);\r\n        setQuestions(cached.questions);\r\n        setTotalQuestions(cached.totalQuestions);\r\n        setTotalPages(cached.totalPages);\r\n        return;\r\n      }\r\n\r\n      dispatch(ShowLoading());\r\n      const response = await getAllQuestions({ page, limit }); // Pass query params to API call\r\n      if (response.success) {\r\n        console.log(response.data);\r\n        setQuestions(response.data); // No need to reverse as backend will handle order\r\n        setTotalQuestions(response.totalQuestions);\r\n        setTotalPages(response.totalPages);\r\n\r\n        // Cache the data with level-specific key\r\n        localStorage.setItem(cacheKey, JSON.stringify({\r\n          questions: response.data,\r\n          totalQuestions: response.totalQuestions,\r\n          totalPages: response.totalPages\r\n        }));\r\n        localStorage.setItem(`${cacheKey}_time`, now.toString());\r\n      } else {\r\n        message.error(response.message);\r\n      }\r\n    } catch (error) {\r\n      message.error(error.message);\r\n    } finally {\r\n      dispatch(HideLoading());\r\n    }\r\n  };\r\n\r\n  useEffect(() => {\r\n    // Clear caches for other levels when component mounts or page changes\r\n    clearAllForumCaches();\r\n    fetchQuestions(currentPage).finally(() => {\r\n      setIsInitialLoad(false);\r\n    });\r\n  }, [currentPage, limit]);\r\n\r\n  const handlePageChange = (page) => {\r\n    setCurrentPage(page);\r\n  };\r\n\r\n  const getUserData = async () => {\r\n    dispatch(ShowLoading());\r\n    try {\r\n      const response = await getUserInfo();\r\n      if (response.success) {\r\n        if (response.data.isAdmin) {\r\n          setIsAdmin(true);\r\n          setUserData(response.data);\r\n          await fetchQuestions();\r\n        } else {\r\n          setIsAdmin(false);\r\n          setUserData(response.data);\r\n          await fetchQuestions();\r\n        }\r\n      } else {\r\n        message.error(response.message);\r\n      }\r\n    } catch (error) {\r\n      message.error(error.message);\r\n    }\r\n  };\r\n\r\n  useEffect(() => {\r\n    if (localStorage.getItem(\"token\")) {\r\n      getUserData();\r\n    }\r\n  }, []);\r\n\r\n  const toggleReplies = (questionId) => {\r\n    setExpandedReplies((prevExpandedReplies) => ({\r\n      ...prevExpandedReplies,\r\n      [questionId]: !prevExpandedReplies[questionId],\r\n    }));\r\n  };\r\n\r\n  const handleAskQuestion = async (values) => {\r\n    try {\r\n      const response = await addQuestion(values);\r\n      if (response.success) {\r\n        message.success(response.message);\r\n        setAskQuestionVisible(false);\r\n        form.resetFields();\r\n        clearAllForumCaches(); // Clear cache when new question is added\r\n        await fetchQuestions();\r\n      } else {\r\n        message.error(response.message);\r\n      }\r\n    } catch (error) {\r\n      message.error(error.message);\r\n    }\r\n  };\r\n\r\n  const handleReply = (questionId) => {\r\n    setReplyQuestionId(questionId);\r\n  };\r\n\r\n  const handleReplySubmit = async (values) => {\r\n    try {\r\n      const payload = {\r\n        questionId: replyQuestionId,\r\n        text: values.text,\r\n      };\r\n      const response = await addReply(payload);\r\n      if (response.success) {\r\n        message.success(response.message);\r\n        setReplyQuestionId(null);\r\n        form.resetFields();\r\n        clearAllForumCaches(); // Clear cache when new reply is added\r\n        await fetchQuestions();\r\n      } else {\r\n        message.error(response.message);\r\n      }\r\n    } catch (error) {\r\n      message.error(error.message);\r\n    }\r\n  };\r\n\r\n  useEffect(() => {\r\n    if (replyQuestionId && !replyRefs[replyQuestionId]) {\r\n      setReplyRefs((prevRefs) => ({\r\n        ...prevRefs,\r\n        [replyQuestionId]: React.createRef(),\r\n      }));\r\n    }\r\n  }, [replyQuestionId, replyRefs]);\r\n\r\n  useEffect(() => {\r\n    if (replyQuestionId && replyRefs[replyQuestionId]) {\r\n      replyRefs[replyQuestionId].current.scrollIntoView({ behavior: \"smooth\" });\r\n    }\r\n  }, [replyQuestionId, replyRefs]);\r\n\r\n  const handleEdit = (question) => {\r\n    setEditQuestion(question);\r\n  };\r\n\r\n  const handleDelete = async (question) => {\r\n    try {\r\n      const confirmDelete = window.confirm(\r\n        \"Are you sure you want to delete this question?\"\r\n      );\r\n      if (!confirmDelete) {\r\n        return;\r\n      }\r\n      const response = await deleteQuestion(question._id);\r\n      if (response.success) {\r\n        message.success(response.message);\r\n        clearAllForumCaches(); // Clear cache when question is deleted\r\n        await fetchQuestions();\r\n      } else {\r\n        message.error(response.message);\r\n      }\r\n    } catch (error) {\r\n      message.error(error.message);\r\n    }\r\n  };\r\n\r\n  const handleUpdateQuestion = async (values) => {\r\n    try {\r\n      const response = await updateQuestion(values, editQuestion._id);\r\n      if (response.success) {\r\n        message.success(response.message);\r\n        setEditQuestion(null);\r\n        clearAllForumCaches(); // Clear cache when question is updated\r\n        await fetchQuestions();\r\n      } else {\r\n        message.error(response.message);\r\n      }\r\n    } catch (error) {\r\n      message.error(error.message);\r\n    }\r\n  };\r\n\r\n  const handleCancelUpdate = () => {\r\n    setEditQuestion(\"\");\r\n  };\r\n  const handleCancelAdd = () => {\r\n    setAskQuestionVisible(false);\r\n    form.resetFields();\r\n  };\r\n\r\n  useEffect(() => {\r\n    if (editQuestion) {\r\n      form2.setFieldsValue({\r\n        title: editQuestion.title,\r\n        body: editQuestion.body,\r\n      });\r\n    } else {\r\n      form2.resetFields();\r\n    }\r\n  }, [editQuestion]);\r\n\r\n  const handleUpdateStatus = async (questionId, replyId, status) => {\r\n    try {\r\n      const response = await updateReplyStatus({ replyId, status }, questionId);\r\n      if (response.success) {\r\n        message.success(response.message);\r\n        clearAllForumCaches(); // Clear cache when reply status is updated\r\n        await fetchQuestions();\r\n      } else {\r\n        message.error(response.message);\r\n      }\r\n    } catch (error) {\r\n      message.error(error.message);\r\n    }\r\n  };\r\n\r\n  // Show skeleton on initial load\r\n  if (isInitialLoad && questions.length === 0) {\r\n    return <ForumSkeleton />;\r\n  }\r\n\r\n  return (\r\n    <div className=\"min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50\">\r\n      <div className=\"Forum max-w-7xl mx-auto px-3 sm:px-4 md:px-6 lg:px-8 py-4 sm:py-6 lg:py-8\">\r\n        {/* Modern Header Section */}\r\n        <div className=\"text-center mb-8 sm:mb-10 lg:mb-12\">\r\n          <div className=\"inline-flex items-center justify-center w-12 h-12 sm:w-14 sm:h-14 lg:w-16 lg:h-16 bg-gradient-to-r from-blue-600 to-indigo-600 rounded-xl sm:rounded-2xl mb-4 sm:mb-6 shadow-lg\">\r\n            <i className=\"ri-discuss-line text-lg sm:text-xl lg:text-2xl text-white\"></i>\r\n          </div>\r\n          <h1 className=\"text-2xl sm:text-3xl lg:text-4xl font-bold text-gray-900 mb-3 sm:mb-4 px-4\">\r\n            Community <span className=\"text-transparent bg-clip-text bg-gradient-to-r from-blue-600 to-indigo-600\">Forum</span>\r\n          </h1>\r\n          <p className=\"text-sm sm:text-base lg:text-lg text-gray-600 max-w-xl sm:max-w-2xl mx-auto mb-6 sm:mb-8 px-4\">\r\n            Connect with fellow learners, ask questions, share knowledge, and grow together in our vibrant learning community.\r\n          </p>\r\n\r\n          {/* Ask Question Button */}\r\n          <button\r\n            onClick={() => setAskQuestionVisible(true)}\r\n            className=\"inline-flex items-center px-6 py-3 sm:px-8 sm:py-4 bg-gradient-to-r from-blue-600 to-indigo-600 text-white font-semibold rounded-lg sm:rounded-xl shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300 text-sm sm:text-base\"\r\n          >\r\n            <i className=\"ri-add-line text-lg sm:text-xl mr-2\"></i>\r\n            Ask a Question\r\n          </button>\r\n        </div>\r\n\r\n        {/* Modern Ask Question Form */}\r\n        {askQuestionVisible && (\r\n          <div className=\"bg-white rounded-xl sm:rounded-2xl shadow-xl p-4 sm:p-6 lg:p-8 mb-6 sm:mb-8 border border-gray-100\">\r\n            <div className=\"flex items-center mb-4 sm:mb-6\">\r\n              <div className=\"w-8 h-8 sm:w-10 sm:h-10 bg-gradient-to-r from-blue-600 to-indigo-600 rounded-lg flex items-center justify-center mr-3 sm:mr-4\">\r\n                <i className=\"ri-question-line text-white text-sm sm:text-lg\"></i>\r\n              </div>\r\n              <h2 className=\"text-xl sm:text-2xl font-bold text-gray-900\">Ask a Question</h2>\r\n            </div>\r\n\r\n            <Form form={form} onFinish={handleAskQuestion} layout=\"vertical\" className=\"modern-form\">\r\n              <Form.Item\r\n                name=\"title\"\r\n                label=\"Question Title\"\r\n                rules={[{ required: true, message: \"Please enter a descriptive title\" }]}\r\n              >\r\n                <Input\r\n                  placeholder=\"What would you like to know?\"\r\n                  className=\"h-12 text-lg\"\r\n                />\r\n              </Form.Item>\r\n              <Form.Item\r\n                name=\"body\"\r\n                label=\"Question Details\"\r\n                rules={[{ required: true, message: \"Please provide more details about your question\" }]}\r\n              >\r\n                <Input.TextArea\r\n                  rows={6}\r\n                  placeholder=\"Describe your question in detail. The more information you provide, the better answers you'll receive.\"\r\n                  className=\"text-base\"\r\n                />\r\n              </Form.Item>\r\n              <Form.Item className=\"mb-0\">\r\n                <div className=\"flex items-center space-x-4\">\r\n                  <Button\r\n                    type=\"primary\"\r\n                    htmlType=\"submit\"\r\n                    className=\"h-12 px-8 bg-gradient-to-r from-blue-600 to-indigo-600 border-none rounded-lg font-semibold text-base\"\r\n                  >\r\n                    <i className=\"ri-send-plane-line mr-2\"></i>\r\n                    Post Question\r\n                  </Button>\r\n                  <Button\r\n                    onClick={handleCancelAdd}\r\n                    className=\"h-12 px-6 border-gray-300 rounded-lg font-semibold text-base hover:bg-gray-50\"\r\n                  >\r\n                    Cancel\r\n                  </Button>\r\n                </div>\r\n              </Form.Item>\r\n            </Form>\r\n          </div>\r\n        )}\r\n\r\n        {/* Loading State */}\r\n        {questions.length === 0 && (\r\n          <div className=\"text-center py-12\">\r\n            <div className=\"inline-flex items-center justify-center w-16 h-16 bg-gray-100 rounded-full mb-4\">\r\n              <i className=\"ri-loader-4-line text-2xl text-gray-400 animate-spin\"></i>\r\n            </div>\r\n            <p className=\"text-gray-500\">Loading discussions...</p>\r\n          </div>\r\n        )}\r\n\r\n        {/* Questions Grid */}\r\n        <div className=\"space-y-4 sm:space-y-6\">\r\n          {questions.filter(question => question && question.user).map((question) => (\r\n            <div key={question._id} className=\"bg-white rounded-xl sm:rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 overflow-hidden border border-gray-100\">\r\n              {/* Question Header */}\r\n              <div className=\"p-4 sm:p-6 border-b border-gray-100\">\r\n                <div className=\"flex items-start justify-between\">\r\n                  <div className=\"flex items-center space-x-3 sm:space-x-4\">\r\n                    <div className=\"relative\">\r\n                      <ProfilePicture\r\n                        user={question.user}\r\n                        size=\"sm\"\r\n                        showOnlineStatus={false}\r\n                        style={{\r\n                          width: '32px',\r\n                          height: '32px'\r\n                        }}\r\n                      />\r\n                      {/* Only show online dot if user exists and is actually online */}\r\n                      {question.user && question.user.isOnline && (\r\n                        <div\r\n                          style={{\r\n                            position: 'absolute',\r\n                            bottom: '-2px',\r\n                            right: '-2px',\r\n                            width: '12px',\r\n                            height: '12px',\r\n                            backgroundColor: '#22c55e',\r\n                            borderRadius: '50%',\r\n                            border: '2px solid #ffffff',\r\n                            boxShadow: '0 2px 8px rgba(34, 197, 94, 0.6)',\r\n                            zIndex: 10\r\n                          }}\r\n                          title=\"Online\"\r\n                        />\r\n                      )}\r\n                    </div>\r\n                    <div className=\"min-w-0 flex-1\">\r\n                      <h4 className=\"font-semibold text-gray-900 text-sm sm:text-base truncate\">{question.user?.name || 'Unknown User'}</h4>\r\n                      <p className=\"text-xs sm:text-sm text-gray-500\">\r\n                        {new Date(question.createdAt).toLocaleDateString('en-US', {\r\n                          year: 'numeric',\r\n                          month: 'short',\r\n                          day: 'numeric',\r\n                          hour: '2-digit',\r\n                          minute: '2-digit'\r\n                        })}\r\n                      </p>\r\n                    </div>\r\n                  </div>\r\n\r\n                  {/* Action Buttons */}\r\n                  <div className=\"flex items-center space-x-1 sm:space-x-2 ml-2\">\r\n                    {(userData._id === question.user?._id || userData.isAdmin) && (\r\n                      <>\r\n                        <button\r\n                          onClick={() => handleEdit(question)}\r\n                          className=\"flex items-center px-2 py-2 sm:px-3 text-blue-600 hover:bg-blue-50 rounded-lg transition-colors duration-200\"\r\n                        >\r\n                          <FaPencilAlt className=\"w-3 h-3 sm:w-4 sm:h-4\" />\r\n                        </button>\r\n                        <button\r\n                          onClick={() => handleDelete(question)}\r\n                          className=\"flex items-center px-2 py-2 sm:px-3 text-red-600 hover:bg-red-50 rounded-lg transition-colors duration-200\"\r\n                        >\r\n                          <MdDelete className=\"w-3 h-3 sm:w-4 sm:h-4\" />\r\n                        </button>\r\n                      </>\r\n                    )}\r\n                  </div>\r\n                </div>\r\n              </div>\r\n\r\n              {/* Question Content */}\r\n              <div className=\"p-4 sm:p-6\">\r\n                <h3 className=\"text-lg sm:text-xl font-bold text-gray-900 mb-3 leading-tight\">{question.title}</h3>\r\n                <p className=\"text-gray-700 leading-relaxed mb-6\">{question.body}</p>\r\n\r\n                {/* Action Bar */}\r\n                <div className=\"flex flex-col sm:flex-row sm:items-center sm:justify-between pt-3 sm:pt-4 border-t border-gray-100 gap-3 sm:gap-0\">\r\n                  <div className=\"flex items-center space-x-2 sm:space-x-4 overflow-x-auto\">\r\n                    <button\r\n                      onClick={() => toggleReplies(question._id)}\r\n                      className=\"flex items-center px-3 py-2 sm:px-4 text-gray-600 hover:text-blue-600 hover:bg-blue-50 rounded-lg transition-colors duration-200 text-sm sm:text-base whitespace-nowrap\"\r\n                    >\r\n                      <i className=\"ri-eye-line mr-1 sm:mr-2 text-sm sm:text-base\"></i>\r\n                      {expandedReplies[question._id] ? \"Hide Replies\" : \"View Replies\"}\r\n                    </button>\r\n                    <button\r\n                      onClick={() => handleReply(question._id)}\r\n                      className=\"flex items-center px-3 py-2 sm:px-4 text-gray-600 hover:text-green-600 hover:bg-green-50 rounded-lg transition-colors duration-200 text-sm sm:text-base whitespace-nowrap\"\r\n                    >\r\n                      <i className=\"ri-reply-line mr-1 sm:mr-2 text-sm sm:text-base\"></i>\r\n                      Reply\r\n                    </button>\r\n                  </div>\r\n\r\n                  <div className=\"flex items-center px-2 py-1.5 sm:px-3 sm:py-2 bg-gray-50 rounded-lg self-start sm:self-auto\">\r\n                    <MdMessage className=\"w-3 h-3 sm:w-4 sm:h-4 text-gray-500 mr-1 sm:mr-2\" />\r\n                    <span className=\"text-xs sm:text-sm font-medium text-gray-700\">{question.replies.length}</span>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n\r\n              {/* Edit Question Form */}\r\n              {editQuestion && editQuestion._id === question._id && (\r\n                <Form\r\n                form={form2}\r\n                onFinish={handleUpdateQuestion}\r\n                layout=\"vertical\"\r\n                initialValues={{\r\n                  title: editQuestion.title,\r\n                  body: editQuestion.body,\r\n                }}\r\n              >\r\n                <Form.Item\r\n                  name=\"title\"\r\n                  label=\"Title\"\r\n                  rules={[\r\n                    { required: true, message: \"Please enter the title\" },\r\n                  ]}\r\n                >\r\n                  <Input style={{ padding: \"18px 12px\" }} />\r\n                </Form.Item>\r\n                <Form.Item\r\n                  name=\"body\"\r\n                  label=\"Body\"\r\n                  rules={[{ required: true, message: \"Please enter the body\" }]}\r\n                >\r\n                  <Input.TextArea />\r\n                </Form.Item>\r\n                <Form.Item>\r\n                  <Button type=\"primary\" htmlType=\"submit\">\r\n                    Update Question\r\n                  </Button>\r\n                  <Button\r\n                    onClick={handleCancelUpdate}\r\n                    style={{ marginLeft: 10 }}\r\n                  >\r\n                    Cancel\r\n                  </Button>\r\n                </Form.Item>\r\n              </Form>\r\n              )}\r\n            {expandedReplies[question._id] && (\r\n              <div className=\"mt-4 sm:mt-6 space-y-3 sm:space-y-4 bg-gray-50 rounded-lg sm:rounded-xl p-3 sm:p-4\">\r\n                <h4 className=\"text-base sm:text-lg font-semibold text-gray-800 mb-3 sm:mb-4 flex items-center\">\r\n                  <i className=\"ri-chat-3-line mr-2 text-blue-600 text-sm sm:text-base\"></i>\r\n                  Replies ({question.replies.filter(reply => reply && reply.user).length})\r\n                </h4>\r\n                {question.replies.filter(reply => reply && reply.user).map((reply, index) => (\r\n                  <div\r\n                    key={reply._id}\r\n                    className={`bg-white rounded-lg p-3 sm:p-4 shadow-sm border-l-4 ${\r\n                      reply.user?.isAdmin\r\n                        ? \"border-purple-500 bg-purple-50\"\r\n                        : reply.isVerified\r\n                        ? \"border-green-500 bg-green-50\"\r\n                        : \"border-gray-300\"\r\n                    }`}\r\n                  >\r\n                    <div className=\"flex items-start space-x-2 sm:space-x-3\">\r\n                      {/* Avatar with Online Status */}\r\n                      <div className=\"flex-shrink-0 relative\">\r\n                        <ProfilePicture\r\n                          user={reply.user}\r\n                          size=\"xs\"\r\n                          showOnlineStatus={false}\r\n                          style={{\r\n                            width: '20px',\r\n                            height: '20px'\r\n                          }}\r\n                          className=\"sm:w-6 sm:h-6\"\r\n                        />\r\n                        {/* Only show online dot if user exists and is actually online */}\r\n                        {reply.user && reply.user.isOnline && (\r\n                          <div\r\n                            style={{\r\n                              position: 'absolute',\r\n                              bottom: '-1px',\r\n                              right: '-1px',\r\n                              width: '6px',\r\n                              height: '6px',\r\n                              backgroundColor: '#22c55e',\r\n                              borderRadius: '50%',\r\n                              border: '1px solid #ffffff',\r\n                              boxShadow: '0 1px 4px rgba(34, 197, 94, 0.6)',\r\n                              zIndex: 10\r\n                            }}\r\n                            className=\"sm:w-2 sm:h-2\"\r\n                            title=\"Online\"\r\n                          />\r\n                        )}\r\n                      </div>\r\n\r\n                      {/* Reply Content */}\r\n                      <div className=\"flex-1 min-w-0\">\r\n                        {/* Header */}\r\n                        <div className=\"flex items-start sm:items-center justify-between mb-2 gap-2\">\r\n                          <div className=\"flex items-center space-x-1 sm:space-x-2 min-w-0 flex-1\">\r\n                            <h5 className=\"font-semibold text-gray-900 text-sm sm:text-base truncate\">{reply.user?.name || 'Unknown User'}</h5>\r\n                            {reply.user?.isAdmin && (\r\n                              <span className=\"px-1.5 py-0.5 sm:px-2 sm:py-1 text-xs font-medium bg-purple-100 text-purple-800 rounded-full whitespace-nowrap\">\r\n                                Admin\r\n                              </span>\r\n                            )}\r\n                            {reply.isVerified && !reply.user?.isAdmin && (\r\n                              <div className=\"flex items-center space-x-1\">\r\n                                <FaCheck className=\"w-3 h-3 sm:w-4 sm:h-4 text-green-600\" />\r\n                                <span className=\"px-1.5 py-0.5 sm:px-2 sm:py-1 text-xs font-medium bg-green-100 text-green-800 rounded-full whitespace-nowrap\">\r\n                                  Verified\r\n                                </span>\r\n                              </div>\r\n                            )}\r\n                          </div>\r\n                          <span className=\"text-xs sm:text-sm text-gray-500 whitespace-nowrap\">\r\n                            {(() => {\r\n                              try {\r\n                                const date = new Date(reply.createdAt);\r\n                                if (isNaN(date.getTime())) {\r\n                                  return 'Invalid date';\r\n                                }\r\n                                return date.toLocaleDateString('en-US', {\r\n                                  month: \"short\",\r\n                                  day: \"numeric\",\r\n                                  hour: \"2-digit\",\r\n                                  minute: \"2-digit\"\r\n                                });\r\n                              } catch (error) {\r\n                                return 'Invalid date';\r\n                              }\r\n                            })()}\r\n                          </span>\r\n                        </div>\r\n\r\n                        {/* Reply Text */}\r\n                        <div className={`leading-relaxed mb-3 text-sm sm:text-base ${\r\n                          reply.isVerified && !reply.user?.isAdmin\r\n                            ? 'text-green-800 font-medium'\r\n                            : reply.user?.isAdmin\r\n                            ? 'text-purple-800 font-medium'\r\n                            : 'text-gray-700'\r\n                        }`}>\r\n                          {reply.text}\r\n                        </div>\r\n\r\n                        {/* Admin Actions */}\r\n                        {isAdmin && !reply.user?.isAdmin && (\r\n                          <div className=\"flex justify-end\">\r\n                            <button\r\n                              onClick={() =>\r\n                                handleUpdateStatus(\r\n                                  question._id,\r\n                                  reply._id,\r\n                                  !reply.isVerified\r\n                                )\r\n                              }\r\n                              className={`px-3 py-1.5 sm:px-4 sm:py-2 text-xs sm:text-sm font-medium rounded-lg transition-colors duration-200 ${\r\n                                reply.isVerified\r\n                                  ? \"bg-red-100 text-red-700 hover:bg-red-200\"\r\n                                  : \"bg-green-100 text-green-700 hover:bg-green-200\"\r\n                              }`}\r\n                            >\r\n                              {reply.isVerified ? \"Disapprove\" : \"Approve\"}\r\n                            </button>\r\n                          </div>\r\n                        )}\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                ))}\r\n              </div>\r\n            )}\r\n            <div ref={replyRefs[question._id]} className=\"mt-4 sm:mt-6\">\r\n              {replyQuestionId === question._id && (\r\n                <div className=\"bg-white rounded-lg sm:rounded-xl p-4 sm:p-6 border border-gray-200\">\r\n                  <Form\r\n                    form={form}\r\n                    onFinish={handleReplySubmit}\r\n                    layout=\"vertical\"\r\n                  >\r\n                    <Form.Item\r\n                      name=\"text\"\r\n                      label={<span className=\"text-sm sm:text-base font-medium\">Your Reply</span>}\r\n                      rules={[\r\n                        { required: true, message: \"Please enter your reply\" },\r\n                      ]}\r\n                    >\r\n                      <Input.TextArea\r\n                        rows={3}\r\n                        className=\"text-sm sm:text-base\"\r\n                        placeholder=\"Write your reply here...\"\r\n                      />\r\n                    </Form.Item>\r\n                    <Form.Item className=\"mb-0\">\r\n                      <div className=\"flex flex-col sm:flex-row gap-2 sm:gap-3\">\r\n                        <Button\r\n                          type=\"primary\"\r\n                          htmlType=\"submit\"\r\n                          className=\"w-full sm:w-auto\"\r\n                          size=\"large\"\r\n                        >\r\n                          Submit Reply\r\n                        </Button>\r\n                        <Button\r\n                          onClick={() => setReplyQuestionId(null)}\r\n                          className=\"w-full sm:w-auto\"\r\n                          size=\"large\"\r\n                        >\r\n                          Cancel\r\n                        </Button>\r\n                      </div>\r\n                    </Form.Item>\r\n                  </Form>\r\n                </div>\r\n              )}\r\n            </div>\r\n            </div>\r\n          ))}\r\n\r\n        </div>\r\n\r\n        <Pagination\r\n          current={currentPage}\r\n          total={totalQuestions}\r\n          pageSize={limit}\r\n          onChange={handlePageChange}\r\n          style={{ marginTop: \"20px\", textAlign: \"center\" }}\r\n          showSizeChanger={false}\r\n        />\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default Forum;\r\n"], "names": ["default", "axiosInstance", "require", "addQuestion", "async", "post", "payload", "data", "error", "response", "addReply", "getAllQuestions", "page", "limit", "_ref", "get", "concat", "deleteQuestion", "delete", "questionId", "updateQuestion", "put", "updateReplyStatus", "title", "isMobile", "setIsMobile", "useState", "useEffect", "window", "innerWidth", "_jsx", "className", "children", "Forum", "isAdmin", "setIsAdmin", "userData", "setUserData", "questions", "setQuestions", "expandedReplies", "setExpandedReplies", "askQuestionVisible", "setAskQuestionVisible", "replyQuestionId", "setReplyQuestionId", "editQuestion", "setEditQuestion", "form", "Form", "useForm", "isInitialLoad", "setIsInitialLoad", "ForumSkeleton", "_jsxs", "Array", "map", "_", "i", "form2", "dispatch", "useDispatch", "replyRefs", "setReplyRefs", "currentPage", "setCurrentPage", "totalPages", "setTotalPages", "totalQuestions", "setTotalQuestions", "clearAllForumCaches", "for<PERSON>ach", "level", "p", "localStorage", "removeItem", "fetchQuestions", "userLevel", "cache<PERSON>ey", "cachedData", "getItem", "cacheTime", "now", "Date", "parseInt", "cached", "JSON", "parse", "ShowLoading", "success", "console", "log", "setItem", "stringify", "toString", "message", "HideLoading", "finally", "getUserInfo", "getUserData", "handleReplySubmit", "text", "values", "resetFields", "prevRefs", "_objectSpread", "React", "current", "scrollIntoView", "behavior", "handleUpdateQuestion", "_id", "handleCancelUpdate", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "body", "length", "onClick", "onFinish", "layout", "<PERSON><PERSON>", "name", "label", "rules", "required", "Input", "placeholder", "TextArea", "rows", "<PERSON><PERSON>", "type", "htmlType", "handleCancelAdd", "filter", "question", "user", "_question$user", "_question$user2", "ProfilePicture", "size", "showOnlineStatus", "style", "width", "height", "isOnline", "position", "bottom", "right", "backgroundColor", "borderRadius", "border", "boxShadow", "zIndex", "createdAt", "toLocaleDateString", "year", "month", "day", "hour", "minute", "_Fragment", "handleEdit", "FaPencilAlt", "confirm", "handleDelete", "MdDelete", "toggleReplies", "prevExpandedReplies", "handleReply", "MdMessage", "replies", "initialValues", "padding", "marginLeft", "reply", "index", "_reply$user", "_reply$user2", "_reply$user3", "_reply$user4", "_reply$user5", "_reply$user6", "_reply$user7", "isVerified", "FaCheck", "date", "isNaN", "getTime", "replyId", "status", "handleUpdateStatus", "ref", "Pagination", "total", "pageSize", "onChange", "marginTop", "textAlign", "showSizeChanger"], "sourceRoot": ""}