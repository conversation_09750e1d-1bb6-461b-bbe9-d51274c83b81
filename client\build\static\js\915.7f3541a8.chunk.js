"use strict";(self.webpackChunkclient=self.webpackChunkclient||[]).push([[915],{272:(e,s,t)=>{t.d(s,{$s:()=>o,I1:()=>i,Ss:()=>r,cq:()=>l,dM:()=>c,uH:()=>n});const{default:a}=t(3371),l=async e=>{try{return(await a.post("/api/reports/add-report",e)).data}catch(s){return s.response.data}},i=async e=>{try{return(await a.post("/api/reports/get-all-reports",e)).data}catch(s){return s.response.data}},r=async e=>{try{return(await a.post("/api/reports/get-all-reports-by-user",e)).data}catch(s){return s.response.data}},n=async e=>{try{return(await a.get("/api/reports/get-all-reports-for-ranking")).data}catch(s){return s.response.data}},c=async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};try{const s=new URLSearchParams;e.limit&&s.append("limit",e.limit),e.classFilter&&s.append("classFilter",e.classFilter),e.levelFilter&&s.append("levelFilter",e.levelFilter),e.seasonFilter&&s.append("seasonFilter",e.seasonFilter),e.includeInactive&&s.append("includeInactive",e.includeInactive);return(await a.get("/api/quiz/xp-leaderboard?".concat(s.toString()))).data}catch(s){return s.response.data}},o=async function(e){let s=arguments.length>1&&void 0!==arguments[1]?arguments[1]:5;try{return(await a.get("/api/quiz/user-ranking/".concat(e,"?context=").concat(s))).data}catch(t){return t.response.data}}},9976:(e,s,t)=>{t.d(s,{Z:()=>i});t(2791);var a=t(6042),l=t(184);const i=e=>{let{children:s,title:t,subtitle:i,actions:r,className:n="",noPadding:c=!1,loading:o=!1}=e;return(0,l.jsxs)(a.E.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.3},className:"bg-white rounded-xl sm:rounded-2xl shadow-lg border border-slate-200/50 hover:shadow-xl transition-all duration-300 ".concat(n),children:[(t||r)&&(0,l.jsx)("div",{className:"px-4 sm:px-6 py-4 sm:py-5 border-b border-slate-100",children:(0,l.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3",children:[t&&(0,l.jsxs)("div",{children:[(0,l.jsx)("h3",{className:"text-lg sm:text-xl font-semibold text-slate-800",children:t}),i&&(0,l.jsx)("p",{className:"text-sm text-slate-600 mt-1",children:i})]}),r&&(0,l.jsx)("div",{className:"flex flex-wrap gap-2",children:r})]})}),(0,l.jsx)("div",{className:c?"":"p-4 sm:p-6",children:o?(0,l.jsx)("div",{className:"flex items-center justify-center py-8 sm:py-12",children:(0,l.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 sm:h-12 sm:w-12 border-b-2 border-blue-600"})}):s})]})}},6345:(e,s,t)=>{t.d(s,{Z:()=>o});t(2791);var a=t(6042),l=t(9434),i=t(7689),r=t(5526),n=t(184);const c=()=>{const e=(0,i.s0)(),s=(0,i.TH)(),{user:t}=(0,l.v9)((e=>e.user)),c=[{title:"Dashboard",icon:r.Qlt,path:"/admin/dashboard",color:"text-blue-500"},{title:"Users",icon:r.HLl,path:"/admin/users",color:"text-green-500"},{title:"Exams",icon:r.RxU,path:"/admin/exams",color:"text-purple-500"},{title:"Study Materials",icon:r.NQR,path:"/admin/study-materials",color:"text-orange-500"},{title:"Reports",icon:r.hWk,path:"/admin/reports",color:"text-indigo-500"},{title:"Notifications",icon:r.CMq,path:"/admin/notifications",color:"text-yellow-500"}],o=(()=>{const e=s.pathname;return c.find((s=>e.startsWith(s.path)))||{title:"Admin Panel",icon:r.Qlt}})(),d="/admin/dashboard"===s.pathname;return(0,n.jsx)("div",{className:"bg-white border-b border-slate-200 sticky top-0 z-50",children:(0,n.jsxs)("div",{className:"px-4 sm:px-6 lg:px-8",children:[(0,n.jsxs)("div",{className:"flex items-center justify-between h-16",children:[(0,n.jsxs)("div",{className:"flex items-center space-x-4",children:[!d&&(0,n.jsx)(a.E.button,{whileHover:{scale:1.05},whileTap:{scale:.95},onClick:()=>e("/admin/dashboard"),className:"p-2 rounded-lg bg-slate-100 hover:bg-slate-200 transition-colors duration-200",children:(0,n.jsx)(r.F4Y,{className:"w-5 h-5 text-slate-600"})}),(0,n.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,n.jsx)("div",{className:"w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center",children:(0,n.jsx)(o.icon,{className:"w-5 h-5 text-white"})}),(0,n.jsxs)("div",{children:[(0,n.jsx)("h1",{className:"text-lg font-semibold text-slate-900",children:o.title}),(0,n.jsx)("p",{className:"text-xs text-slate-500",children:"BrainWave Admin"})]})]})]}),(0,n.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,n.jsxs)("div",{className:"hidden sm:flex items-center space-x-3",children:[(0,n.jsx)("div",{className:"w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center",children:(0,n.jsx)(r.S8z,{className:"w-4 h-4 text-white"})}),(0,n.jsxs)("div",{className:"text-right",children:[(0,n.jsx)("p",{className:"text-sm font-medium text-slate-900",children:null===t||void 0===t?void 0:t.name}),(0,n.jsx)("p",{className:"text-xs text-slate-500",children:"Administrator"})]})]}),(0,n.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,n.jsx)(a.E.button,{whileHover:{scale:1.05},whileTap:{scale:.95},onClick:()=>e("/"),className:"p-2 rounded-lg bg-slate-100 hover:bg-slate-200 transition-colors duration-200",title:"View Site",children:(0,n.jsx)(r.diY,{className:"w-4 h-4 text-slate-600"})}),(0,n.jsx)(a.E.button,{whileHover:{scale:1.05},whileTap:{scale:.95},onClick:()=>{try{localStorage.removeItem("token"),localStorage.removeItem("user"),e("/login")}catch(s){console.error("Error logging out:",s)}},className:"p-2 rounded-lg bg-red-100 hover:bg-red-200 transition-colors duration-200",title:"Logout",children:(0,n.jsx)(r.VUx,{className:"w-4 h-4 text-red-600"})})]})]})]}),(0,n.jsxs)("div",{className:"pb-4",children:[(0,n.jsx)("div",{className:"flex items-center space-x-1 overflow-x-auto scrollbar-hide",children:c.map((t=>{const l=t.icon,i=(r=t.path,s.pathname.startsWith(r));var r;return(0,n.jsxs)(a.E.button,{whileHover:{scale:1.02},whileTap:{scale:.98},onClick:()=>e(t.path),className:"flex items-center space-x-2 px-3 py-2 rounded-lg text-sm font-medium transition-all duration-200 whitespace-nowrap flex-shrink-0 ".concat(i?"bg-blue-100 text-blue-700 shadow-sm border border-blue-200":"text-slate-600 hover:bg-slate-100 hover:text-slate-900"),children:[(0,n.jsx)(l,{className:"w-4 h-4 ".concat(i?"text-blue-600":t.color)}),(0,n.jsx)("span",{className:"hidden sm:inline",children:t.title}),(0,n.jsx)("span",{className:"sm:hidden text-xs",children:t.title.split(" ")[0]})]},t.path)}))}),(0,n.jsx)("div",{className:"sm:hidden mt-2 flex justify-center",children:(0,n.jsx)("div",{className:"text-xs text-slate-400",children:"\u2190 Swipe to see more options \u2192"})})]})]})})},o=e=>{let{children:s,title:t,subtitle:i,actions:r,showHeader:o=!0}=e;const{user:d}=(0,l.v9)((e=>e.user));return(0,n.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50",children:[(0,n.jsx)(c,{}),(0,n.jsxs)("div",{className:"p-4 sm:p-6 lg:p-8 max-w-7xl mx-auto",children:[o&&(t||i||r)&&(0,n.jsx)(a.E.div,{initial:{opacity:0,y:-20},animate:{opacity:1,y:0},transition:{duration:.5},className:"mb-6 sm:mb-8",children:(0,n.jsx)("div",{className:"bg-white rounded-2xl shadow-lg border border-slate-200/50 p-6 sm:p-8",children:(0,n.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4",children:[(0,n.jsxs)("div",{className:"flex-1",children:[t&&(0,n.jsx)("h1",{className:"text-2xl sm:text-3xl lg:text-4xl font-bold bg-gradient-to-r from-slate-800 to-blue-600 bg-clip-text text-transparent mb-2",children:t}),i&&(0,n.jsx)("p",{className:"text-slate-600 text-sm sm:text-base lg:text-lg",children:i})]}),r&&(0,n.jsx)("div",{className:"flex flex-wrap gap-2 sm:gap-3",children:r})]})})}),(0,n.jsx)(a.E.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.5,delay:.1},className:"space-y-6",children:s})]})]})}},915:(e,s,t)=>{t.r(s),t.d(s,{default:()=>b});var a=t(2791),l=t(6042),i=t(9434),r=t(7689),n=t(5273),c=t(5526),o=t(8262),d=t(1652),x=t(272),m=t(8247),h=t(6345),p=t(9976),u=t(184);const b=()=>{const e=(0,i.I0)(),s=(0,r.s0)(),{user:t}=(0,i.v9)((e=>e.user)),[b,g]=(0,a.useState)({totalUsers:0,activeUsers:0,totalExams:0,totalReports:0,averageScore:0,completionRate:0}),[j,N]=(0,a.useState)(!0);(0,a.useEffect)((()=>{v()}),[]);const v=async()=>{try{N(!0),e((0,m.YC)());const s=await(0,o.AW)(),t=s.success?s.users:[],a=await(0,d.h_)(),l=a.success?a.data:[],i=await(0,x.I1)({examName:"",userName:"",page:1,limit:1e3}),r=i.success?i.data:[],n=t.length,c=t.filter((e=>!e.isBlocked)).length,h=l.length,p=r.length,u=r.length>0?r.reduce(((e,s)=>e+(s.percentage||0)),0)/r.length:0,b=n>0?p/n*100:0;g({totalUsers:n,activeUsers:c,totalExams:h,totalReports:p,averageScore:Math.round(u),completionRate:Math.round(b)}),N(!1),e((0,m.Ir)())}catch(s){console.error("Error fetching dashboard data:",s),N(!1),e((0,m.Ir)())}},f=[{title:"Manage Users",description:"View and manage student accounts",icon:c.HLl,path:"/admin/users",color:"bg-blue-500"},{title:"Create Exam",description:"Add new exams and questions",icon:c.RxU,path:"/admin/exams/add",color:"bg-green-500"},{title:"Study Materials",description:"Manage learning resources",icon:c.NQR,path:"/admin/study-materials",color:"bg-orange-500"},{title:"View Reports",description:"Analytics and performance",icon:c.hWk,path:"/admin/reports",color:"bg-indigo-500"},{title:"Notifications",description:"Send announcements",icon:c.CMq,path:"/admin/notifications",color:"bg-pink-500"},{title:"Forum Management",description:"Manage community forum",icon:c.YsT,path:"/admin/forum",color:"bg-purple-500"}],w=[(0,u.jsxs)(l.E.button,{whileHover:{scale:1.02},whileTap:{scale:.98},onClick:()=>s("/admin/users"),className:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors duration-200 flex items-center gap-2",children:[(0,u.jsx)(c.HLl,{className:"w-4 h-4"}),(0,u.jsx)("span",{className:"hidden sm:inline",children:"Manage Users"})]},"users"),(0,u.jsxs)(l.E.button,{whileHover:{scale:1.02},whileTap:{scale:.98},onClick:()=>s("/admin/exams/add"),className:"px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors duration-200 flex items-center gap-2",children:[(0,u.jsx)(c.rIf,{className:"w-4 h-4"}),(0,u.jsx)("span",{className:"hidden sm:inline",children:"Create Exam"})]},"exams"),(0,u.jsxs)(l.E.button,{whileHover:{scale:1.02},whileTap:{scale:.98},onClick:()=>s("/admin/ai-questions"),className:"px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors duration-200 flex items-center gap-2",children:[(0,u.jsx)(c.bAs,{className:"w-4 h-4"}),(0,u.jsx)("span",{className:"hidden sm:inline",children:"AI Questions"})]},"ai")];return(0,u.jsxs)(h.Z,{showHeader:!1,children:[(0,u.jsx)("div",{className:"mb-6 sm:mb-8",children:(0,u.jsx)("div",{className:"bg-gradient-to-r from-blue-600 to-purple-600 rounded-2xl p-6 sm:p-8 text-white",children:(0,u.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4",children:[(0,u.jsxs)("div",{children:[(0,u.jsxs)("h1",{className:"text-2xl sm:text-3xl font-bold mb-2",children:["Welcome back, ",null===t||void 0===t?void 0:t.name,"! \ud83d\udc4b"]}),(0,u.jsx)("p",{className:"text-blue-100 text-sm sm:text-base",children:"Here's what's happening with your educational platform today."})]}),(0,u.jsx)("div",{className:"flex flex-wrap gap-2",children:w})]})})}),(0,u.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6 mb-6 sm:mb-8",children:[(0,u.jsx)(p.Z,{className:"bg-gradient-to-br from-blue-500 to-blue-600 text-white border-0",children:(0,u.jsxs)("div",{className:"flex items-center justify-between",children:[(0,u.jsxs)("div",{children:[(0,u.jsx)("p",{className:"text-blue-100 text-sm font-medium mb-1",children:"Total Students"}),(0,u.jsx)("p",{className:"text-2xl sm:text-3xl font-bold",children:b.totalUsers}),(0,u.jsx)("p",{className:"text-blue-200 text-xs mt-1",children:"Registered users"})]}),(0,u.jsx)("div",{className:"w-12 h-12 bg-white/20 rounded-xl flex items-center justify-center",children:(0,u.jsx)(c.HLl,{className:"w-6 h-6"})})]})}),(0,u.jsx)(p.Z,{className:"bg-gradient-to-br from-green-500 to-green-600 text-white border-0",children:(0,u.jsxs)("div",{className:"flex items-center justify-between",children:[(0,u.jsxs)("div",{children:[(0,u.jsx)("p",{className:"text-green-100 text-sm font-medium mb-1",children:"Active Users"}),(0,u.jsx)("p",{className:"text-2xl sm:text-3xl font-bold",children:b.activeUsers}),(0,u.jsx)("p",{className:"text-green-200 text-xs mt-1",children:"Currently active"})]}),(0,u.jsx)("div",{className:"w-12 h-12 bg-white/20 rounded-xl flex items-center justify-center",children:(0,u.jsx)(c.ehl,{className:"w-6 h-6"})})]})}),(0,u.jsx)(p.Z,{className:"bg-gradient-to-br from-purple-500 to-purple-600 text-white border-0",children:(0,u.jsxs)("div",{className:"flex items-center justify-between",children:[(0,u.jsxs)("div",{children:[(0,u.jsx)("p",{className:"text-purple-100 text-sm font-medium mb-1",children:"Total Exams"}),(0,u.jsx)("p",{className:"text-2xl sm:text-3xl font-bold",children:b.totalExams}),(0,u.jsx)("p",{className:"text-purple-200 text-xs mt-1",children:"Available exams"})]}),(0,u.jsx)("div",{className:"w-12 h-12 bg-white/20 rounded-xl flex items-center justify-center",children:(0,u.jsx)(c.RxU,{className:"w-6 h-6"})})]})}),(0,u.jsx)(p.Z,{className:"bg-gradient-to-br from-orange-500 to-orange-600 text-white border-0",children:(0,u.jsxs)("div",{className:"flex items-center justify-between",children:[(0,u.jsxs)("div",{children:[(0,u.jsx)("p",{className:"text-orange-100 text-sm font-medium mb-1",children:"Avg Score"}),(0,u.jsxs)("p",{className:"text-2xl sm:text-3xl font-bold",children:[b.averageScore,"%"]}),(0,u.jsx)("p",{className:"text-orange-200 text-xs mt-1",children:"Overall performance"})]}),(0,u.jsx)("div",{className:"w-12 h-12 bg-white/20 rounded-xl flex items-center justify-center",children:(0,u.jsx)(c.iy9,{className:"w-6 h-6"})})]})})]}),(0,u.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6 sm:mb-8",children:[(0,u.jsx)(p.Z,{title:"Quick Actions",subtitle:"Common administrative tasks",children:(0,u.jsx)("div",{className:"grid grid-cols-1 sm:grid-cols-2 gap-4",children:f.map(((e,t)=>{const a=e.icon;return(0,u.jsxs)(l.E.button,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.3,delay:.1*t},onClick:()=>s(e.path),className:"p-4 rounded-xl ".concat(e.color," text-white hover:shadow-lg transition-all duration-300 transform hover:scale-105 text-left"),children:[(0,u.jsx)(a,{className:"w-8 h-8 mb-3"}),(0,u.jsx)("h3",{className:"font-semibold text-sm sm:text-base mb-1",children:e.title}),(0,u.jsx)("p",{className:"text-xs sm:text-sm opacity-90",children:e.description})]},e.title)}))})}),(0,u.jsx)(p.Z,{title:"Performance Analytics",subtitle:"System performance overview",children:(0,u.jsxs)("div",{className:"space-y-6",children:[(0,u.jsxs)("div",{children:[(0,u.jsxs)("div",{className:"flex justify-between items-center mb-2",children:[(0,u.jsx)("span",{className:"text-sm font-medium text-slate-700",children:"Completion Rate"}),(0,u.jsxs)("span",{className:"text-sm font-bold text-slate-900",children:[b.completionRate,"%"]})]}),(0,u.jsx)(n.Z,{percent:b.completionRate,strokeColor:{"0%":"#3B82F6","100%":"#8B5CF6"},className:"mb-4"})]}),(0,u.jsxs)("div",{children:[(0,u.jsxs)("div",{className:"flex justify-between items-center mb-2",children:[(0,u.jsx)("span",{className:"text-sm font-medium text-slate-700",children:"Average Score"}),(0,u.jsxs)("span",{className:"text-sm font-bold text-slate-900",children:[b.averageScore,"%"]})]}),(0,u.jsx)(n.Z,{percent:b.averageScore,strokeColor:{"0%":"#10B981","100%":"#059669"},className:"mb-4"})]}),(0,u.jsxs)("div",{className:"grid grid-cols-2 gap-4 pt-4 border-t border-slate-100",children:[(0,u.jsxs)("div",{className:"text-center",children:[(0,u.jsx)("p",{className:"text-2xl font-bold text-slate-900",children:b.totalReports}),(0,u.jsx)("p",{className:"text-xs text-slate-600",children:"Total Attempts"})]}),(0,u.jsxs)("div",{className:"text-center",children:[(0,u.jsxs)("p",{className:"text-2xl font-bold text-slate-900",children:[Math.round(b.activeUsers/b.totalUsers*100)||0,"%"]}),(0,u.jsx)("p",{className:"text-xs text-slate-600",children:"User Activity"})]})]})]})})]})]})}}}]);
//# sourceMappingURL=915.7f3541a8.chunk.js.map