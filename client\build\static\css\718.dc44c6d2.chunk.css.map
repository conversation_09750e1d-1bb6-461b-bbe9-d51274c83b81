{"version": 3, "file": "static/css/718.dc44c6d2.chunk.css", "mappings": "AACA,mBAEE,2DAA0E,CAD1E,gBAEF,CAGA,oBACE,GACE,SAAU,CACV,0BACF,CACA,GACE,SAAU,CACV,uBACF,CACF,CAEA,iBACE,MACE,kBACF,CACA,IACE,qBACF,CACF,CAGA,UAEE,kBAAmB,CACnB,eAAgB,CAFhB,0CAGF,CAEA,gBAEE,gCAA0C,CAD1C,0BAEF,CAGA,iBACE,iBACF,CAEA,oBAEE,kBAAmB,CADnB,iBAEF,CAGA,iBACE,kBAAmB,CAInB,+BAA8C,CAH9C,eAAgB,CAChB,WAAY,CACZ,iBAAkB,CAElB,uBACF,CAEA,uBAEE,+BAA8C,CAD9C,0BAEF,CAGA,iCAGE,kCAAoC,CADpC,4BAA8B,CAE9B,iCACF,CAEA,6DAEE,8BAAgC,CAChC,wCACF,CAGA,cAEE,oBAAqB,CACrB,2BAA4B,CAF5B,mBAAoB,CAGpB,eACF,CAGA,6CACE,wBACF,CAEA,2CACE,wBACF,CAGA,sCACE,+BAAgC,CAChC,iBACF,CAEA,oCACE,YACF,CAEA,sCACE,4BAA6B,CAC7B,iBAAkB,CAClB,iBACF,CAEA,iDAEE,aAAc,CADd,eAEF,CAEA,mDACE,aACF,CAGA,yCACE,mBACE,YACF,CAEA,UACE,kBACF,CAEA,eACE,sBACF,CAEA,qBACE,wBAA0B,CAC1B,2BACF,CAEA,uBACE,wBACF,CAOA,8CAEE,wBAA0B,CAD1B,yBAEF,CAEA,gBACE,uBACF,CAEA,qBAEE,qBAAuB,CACvB,0BAA4B,CAF5B,wBAGF,CACF,CAEA,yCACE,SAEE,iBAAkB,CADlB,UAEF,CAMA,wBACE,oBACF,CAEA,qBACE,uBACF,CAEA,uBACE,wBACF,CAEA,sBACE,2BACF,CAEA,wBACE,sBACF,CACF,CAEA,gEACE,qBACE,wBACF,CAEA,uBACE,wBACF,CAOA,8CAEE,wBAA0B,CAD1B,0BAEF,CACF,CAGA,mCACE,mBACE,8DACF,CAEA,UACE,kBAAmB,CACnB,oBACF,CAEA,yBACE,aACF,CACF", "sources": ["pages/user/UserReports/index.css"], "sourcesContent": ["/* Modern Reports Styles */\r\n.reports-container {\r\n  min-height: 100vh;\r\n  background: linear-gradient(135deg, #f0f9ff 0%, #ffffff 50%, #eef2ff 100%);\r\n}\r\n\r\n/* Custom animations */\r\n@keyframes fadeInUp {\r\n  from {\r\n    opacity: 0;\r\n    transform: translateY(30px);\r\n  }\r\n  to {\r\n    opacity: 1;\r\n    transform: translateY(0);\r\n  }\r\n}\r\n\r\n@keyframes pulse {\r\n  0%, 100% {\r\n    transform: scale(1);\r\n  }\r\n  50% {\r\n    transform: scale(1.05);\r\n  }\r\n}\r\n\r\n/* Card hover effects */\r\n.ant-card {\r\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\r\n  border-radius: 16px;\r\n  overflow: hidden;\r\n}\r\n\r\n.ant-card:hover {\r\n  transform: translateY(-4px);\r\n  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n/* Progress bar customization */\r\n.ant-progress-bg {\r\n  border-radius: 8px;\r\n}\r\n\r\n.ant-progress-inner {\r\n  border-radius: 8px;\r\n  background: #f3f4f6;\r\n}\r\n\r\n/* Button customization */\r\n.ant-btn-primary {\r\n  border-radius: 12px;\r\n  font-weight: 600;\r\n  height: auto;\r\n  padding: 12px 24px;\r\n  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.ant-btn-primary:hover {\r\n  transform: translateY(-2px);\r\n  box-shadow: 0 8px 20px rgba(59, 130, 246, 0.4);\r\n}\r\n\r\n/* Select and DatePicker customization */\r\n.ant-select-selector,\r\n.ant-picker {\r\n  border-radius: 12px !important;\r\n  border: 2px solid #e5e7eb !important;\r\n  transition: all 0.3s ease !important;\r\n}\r\n\r\n.ant-select-focused .ant-select-selector,\r\n.ant-picker-focused {\r\n  border-color: #3b82f6 !important;\r\n  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1) !important;\r\n}\r\n\r\n/* Text truncation utility */\r\n.line-clamp-2 {\r\n  display: -webkit-box;\r\n  -webkit-line-clamp: 2;\r\n  -webkit-box-orient: vertical;\r\n  overflow: hidden;\r\n}\r\n\r\n/* Responsive statistic styling */\r\n.responsive-statistic .ant-statistic-content {\r\n  font-size: 24px !important;\r\n}\r\n\r\n.responsive-statistic .ant-statistic-title {\r\n  font-size: 12px !important;\r\n}\r\n\r\n/* Modal styling */\r\n.exam-details-modal .ant-modal-header {\r\n  border-bottom: 2px solid #f0f0f0;\r\n  padding: 20px 24px;\r\n}\r\n\r\n.exam-details-modal .ant-modal-body {\r\n  padding: 24px;\r\n}\r\n\r\n.exam-details-modal .ant-modal-footer {\r\n  border-top: 2px solid #f0f0f0;\r\n  padding: 16px 24px;\r\n  text-align: center;\r\n}\r\n\r\n.exam-details-modal .ant-descriptions-item-label {\r\n  font-weight: 600;\r\n  color: #374151;\r\n}\r\n\r\n.exam-details-modal .ant-descriptions-item-content {\r\n  color: #6b7280;\r\n}\r\n\r\n/* Responsive design */\r\n@media only screen and (max-width: 768px) {\r\n  .reports-container {\r\n    padding: 12px;\r\n  }\r\n\r\n  .ant-card {\r\n    margin-bottom: 12px;\r\n  }\r\n\r\n  .ant-card-body {\r\n    padding: 12px !important;\r\n  }\r\n\r\n  .ant-statistic-title {\r\n    font-size: 10px !important;\r\n    margin-bottom: 4px !important;\r\n  }\r\n\r\n  .ant-statistic-content {\r\n    font-size: 16px !important;\r\n  }\r\n\r\n  .ant-table-thead > tr > th {\r\n    padding: 8px 4px !important;\r\n    font-size: 12px !important;\r\n  }\r\n\r\n  .ant-table-tbody > tr > td {\r\n    padding: 8px 4px !important;\r\n    font-size: 12px !important;\r\n  }\r\n\r\n  .ant-pagination {\r\n    margin: 16px 0 !important;\r\n  }\r\n\r\n  .ant-pagination-item {\r\n    min-width: 28px !important;\r\n    height: 28px !important;\r\n    line-height: 26px !important;\r\n  }\r\n}\r\n\r\n@media only screen and (max-width: 640px) {\r\n  .ant-btn {\r\n    width: 100%;\r\n    margin-bottom: 8px;\r\n  }\r\n\r\n  .ant-select {\r\n    width: 100% !important;\r\n  }\r\n\r\n  .ant-picker {\r\n    width: 100% !important;\r\n  }\r\n\r\n  .ant-statistic-title {\r\n    font-size: 9px !important;\r\n  }\r\n\r\n  .ant-statistic-content {\r\n    font-size: 14px !important;\r\n  }\r\n\r\n  .ant-table-pagination {\r\n    text-align: center !important;\r\n  }\r\n\r\n  .ant-pagination-options {\r\n    display: none !important;\r\n  }\r\n}\r\n\r\n@media only screen and (min-width: 769px) and (max-width: 1024px) {\r\n  .ant-statistic-title {\r\n    font-size: 11px !important;\r\n  }\r\n\r\n  .ant-statistic-content {\r\n    font-size: 20px !important;\r\n  }\r\n\r\n  .ant-table-thead > tr > th {\r\n    padding: 12px 8px !important;\r\n    font-size: 13px !important;\r\n  }\r\n\r\n  .ant-table-tbody > tr > td {\r\n    padding: 12px 8px !important;\r\n    font-size: 13px !important;\r\n  }\r\n}\r\n\r\n/* Dark mode support */\r\n@media (prefers-color-scheme: dark) {\r\n  .reports-container {\r\n    background: linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #334155 100%);\r\n  }\r\n\r\n  .ant-card {\r\n    background: #1e293b;\r\n    border-color: #334155;\r\n  }\r\n\r\n  .ant-card .ant-card-body {\r\n    color: #f1f5f9;\r\n  }\r\n}"], "names": [], "sourceRoot": ""}