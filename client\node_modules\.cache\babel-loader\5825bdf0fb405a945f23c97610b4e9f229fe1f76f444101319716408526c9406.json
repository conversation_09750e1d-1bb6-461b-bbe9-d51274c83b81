{"ast": null, "code": "import SearchOutlined from \"@ant-design/icons/es/icons/SearchOutlined\";\nimport * as React from 'react';\nimport Input from '../../../input';\nfunction FilterSearch(_ref) {\n  let {\n    value,\n    onChange,\n    filterSearch,\n    tablePrefixCls,\n    locale\n  } = _ref;\n  if (!filterSearch) {\n    return null;\n  }\n  return /*#__PURE__*/React.createElement(\"div\", {\n    className: `${tablePrefixCls}-filter-dropdown-search`\n  }, /*#__PURE__*/React.createElement(Input, {\n    prefix: /*#__PURE__*/React.createElement(SearchOutlined, null),\n    placeholder: locale.filterSearchPlaceholder,\n    onChange: onChange,\n    value: value,\n    // for skip min-width of input\n    htmlSize: 1,\n    className: `${tablePrefixCls}-filter-dropdown-search-input`\n  }));\n}\nexport default FilterSearch;", "map": {"version": 3, "names": ["SearchOutlined", "React", "Input", "FilterSearch", "_ref", "value", "onChange", "filterSearch", "tablePrefixCls", "locale", "createElement", "className", "prefix", "placeholder", "filterSearchPlaceholder", "htmlSize"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/node_modules/antd/es/table/hooks/useFilter/FilterSearch.js"], "sourcesContent": ["import SearchOutlined from \"@ant-design/icons/es/icons/SearchOutlined\";\nimport * as React from 'react';\nimport Input from '../../../input';\nfunction FilterSearch(_ref) {\n  let {\n    value,\n    onChange,\n    filterSearch,\n    tablePrefixCls,\n    locale\n  } = _ref;\n  if (!filterSearch) {\n    return null;\n  }\n  return /*#__PURE__*/React.createElement(\"div\", {\n    className: `${tablePrefixCls}-filter-dropdown-search`\n  }, /*#__PURE__*/React.createElement(Input, {\n    prefix: /*#__PURE__*/React.createElement(SearchOutlined, null),\n    placeholder: locale.filterSearchPlaceholder,\n    onChange: onChange,\n    value: value,\n    // for skip min-width of input\n    htmlSize: 1,\n    className: `${tablePrefixCls}-filter-dropdown-search-input`\n  }));\n}\nexport default FilterSearch;"], "mappings": "AAAA,OAAOA,cAAc,MAAM,2CAA2C;AACtE,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,KAAK,MAAM,gBAAgB;AAClC,SAASC,YAAYA,CAACC,IAAI,EAAE;EAC1B,IAAI;IACFC,KAAK;IACLC,QAAQ;IACRC,YAAY;IACZC,cAAc;IACdC;EACF,CAAC,GAAGL,IAAI;EACR,IAAI,CAACG,YAAY,EAAE;IACjB,OAAO,IAAI;EACb;EACA,OAAO,aAAaN,KAAK,CAACS,aAAa,CAAC,KAAK,EAAE;IAC7CC,SAAS,EAAG,GAAEH,cAAe;EAC/B,CAAC,EAAE,aAAaP,KAAK,CAACS,aAAa,CAACR,KAAK,EAAE;IACzCU,MAAM,EAAE,aAAaX,KAAK,CAACS,aAAa,CAACV,cAAc,EAAE,IAAI,CAAC;IAC9Da,WAAW,EAAEJ,MAAM,CAACK,uBAAuB;IAC3CR,QAAQ,EAAEA,QAAQ;IAClBD,KAAK,EAAEA,KAAK;IACZ;IACAU,QAAQ,EAAE,CAAC;IACXJ,SAAS,EAAG,GAAEH,cAAe;EAC/B,CAAC,CAAC,CAAC;AACL;AACA,eAAeL,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}