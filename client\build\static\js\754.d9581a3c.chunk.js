"use strict";(self.webpackChunkclient=self.webpackChunkclient||[]).push([[754],{9976:(e,s,t)=>{t.d(s,{Z:()=>i});t(2791);var l=t(6042),a=t(184);const i=e=>{let{children:s,title:t,subtitle:i,actions:r,className:n="",noPadding:c=!1,loading:d=!1}=e;return(0,a.jsxs)(l.E.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.3},className:"bg-white rounded-xl sm:rounded-2xl shadow-lg border border-slate-200/50 hover:shadow-xl transition-all duration-300 ".concat(n),children:[(t||r)&&(0,a.jsx)("div",{className:"px-4 sm:px-6 py-4 sm:py-5 border-b border-slate-100",children:(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3",children:[t&&(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-lg sm:text-xl font-semibold text-slate-800",children:t}),i&&(0,a.jsx)("p",{className:"text-sm text-slate-600 mt-1",children:i})]}),r&&(0,a.jsx)("div",{className:"flex flex-wrap gap-2",children:r})]})}),(0,a.jsx)("div",{className:c?"":"p-4 sm:p-6",children:d?(0,a.jsx)("div",{className:"flex items-center justify-center py-8 sm:py-12",children:(0,a.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 sm:h-12 sm:w-12 border-b-2 border-blue-600"})}):s})]})}},6345:(e,s,t)=>{t.d(s,{Z:()=>d});t(2791);var l=t(6042),a=t(9434),i=t(7689),r=t(5526),n=t(184);const c=()=>{const e=(0,i.s0)(),s=(0,i.TH)(),{user:t}=(0,a.v9)((e=>e.user)),c=[{title:"Dashboard",icon:r.Qlt,path:"/admin/dashboard",color:"text-blue-500"},{title:"Users",icon:r.HLl,path:"/admin/users",color:"text-green-500"},{title:"Exams",icon:r.RxU,path:"/admin/exams",color:"text-purple-500"},{title:"Study Materials",icon:r.NQR,path:"/admin/study-materials",color:"text-orange-500"},{title:"Reports",icon:r.hWk,path:"/admin/reports",color:"text-indigo-500"},{title:"Notifications",icon:r.CMq,path:"/admin/notifications",color:"text-yellow-500"}],d=(()=>{const e=s.pathname;return c.find((s=>e.startsWith(s.path)))||{title:"Admin Panel",icon:r.Qlt}})(),o="/admin/dashboard"===s.pathname;return(0,n.jsx)("div",{className:"bg-white border-b border-slate-200 sticky top-0 z-50",children:(0,n.jsxs)("div",{className:"px-4 sm:px-6 lg:px-8",children:[(0,n.jsxs)("div",{className:"flex items-center justify-between h-16",children:[(0,n.jsxs)("div",{className:"flex items-center space-x-4",children:[!o&&(0,n.jsx)(l.E.button,{whileHover:{scale:1.05},whileTap:{scale:.95},onClick:()=>e("/admin/dashboard"),className:"p-2 rounded-lg bg-slate-100 hover:bg-slate-200 transition-colors duration-200",children:(0,n.jsx)(r.F4Y,{className:"w-5 h-5 text-slate-600"})}),(0,n.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,n.jsx)("div",{className:"w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center",children:(0,n.jsx)(d.icon,{className:"w-5 h-5 text-white"})}),(0,n.jsxs)("div",{children:[(0,n.jsx)("h1",{className:"text-lg font-semibold text-slate-900",children:d.title}),(0,n.jsx)("p",{className:"text-xs text-slate-500",children:"BrainWave Admin"})]})]})]}),(0,n.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,n.jsxs)("div",{className:"hidden sm:flex items-center space-x-3",children:[(0,n.jsx)("div",{className:"w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center",children:(0,n.jsx)(r.S8z,{className:"w-4 h-4 text-white"})}),(0,n.jsxs)("div",{className:"text-right",children:[(0,n.jsx)("p",{className:"text-sm font-medium text-slate-900",children:null===t||void 0===t?void 0:t.name}),(0,n.jsx)("p",{className:"text-xs text-slate-500",children:"Administrator"})]})]}),(0,n.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,n.jsx)(l.E.button,{whileHover:{scale:1.05},whileTap:{scale:.95},onClick:()=>e("/"),className:"p-2 rounded-lg bg-slate-100 hover:bg-slate-200 transition-colors duration-200",title:"View Site",children:(0,n.jsx)(r.diY,{className:"w-4 h-4 text-slate-600"})}),(0,n.jsx)(l.E.button,{whileHover:{scale:1.05},whileTap:{scale:.95},onClick:()=>{try{localStorage.removeItem("token"),localStorage.removeItem("user"),e("/login")}catch(s){console.error("Error logging out:",s)}},className:"p-2 rounded-lg bg-red-100 hover:bg-red-200 transition-colors duration-200",title:"Logout",children:(0,n.jsx)(r.VUx,{className:"w-4 h-4 text-red-600"})})]})]})]}),(0,n.jsxs)("div",{className:"pb-4",children:[(0,n.jsx)("div",{className:"flex items-center space-x-1 overflow-x-auto scrollbar-hide",children:c.map((t=>{const a=t.icon,i=(r=t.path,s.pathname.startsWith(r));var r;return(0,n.jsxs)(l.E.button,{whileHover:{scale:1.02},whileTap:{scale:.98},onClick:()=>e(t.path),className:"flex items-center space-x-2 px-3 py-2 rounded-lg text-sm font-medium transition-all duration-200 whitespace-nowrap flex-shrink-0 ".concat(i?"bg-blue-100 text-blue-700 shadow-sm border border-blue-200":"text-slate-600 hover:bg-slate-100 hover:text-slate-900"),children:[(0,n.jsx)(a,{className:"w-4 h-4 ".concat(i?"text-blue-600":t.color)}),(0,n.jsx)("span",{className:"hidden sm:inline",children:t.title}),(0,n.jsx)("span",{className:"sm:hidden text-xs",children:t.title.split(" ")[0]})]},t.path)}))}),(0,n.jsx)("div",{className:"sm:hidden mt-2 flex justify-center",children:(0,n.jsx)("div",{className:"text-xs text-slate-400",children:"\u2190 Swipe to see more options \u2192"})})]})]})})},d=e=>{let{children:s,title:t,subtitle:i,actions:r,showHeader:d=!0}=e;const{user:o}=(0,a.v9)((e=>e.user));return(0,n.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50",children:[(0,n.jsx)(c,{}),(0,n.jsxs)("div",{className:"p-4 sm:p-6 lg:p-8 max-w-7xl mx-auto",children:[d&&(t||i||r)&&(0,n.jsx)(l.E.div,{initial:{opacity:0,y:-20},animate:{opacity:1,y:0},transition:{duration:.5},className:"mb-6 sm:mb-8",children:(0,n.jsx)("div",{className:"bg-white rounded-2xl shadow-lg border border-slate-200/50 p-6 sm:p-8",children:(0,n.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4",children:[(0,n.jsxs)("div",{className:"flex-1",children:[t&&(0,n.jsx)("h1",{className:"text-2xl sm:text-3xl lg:text-4xl font-bold bg-gradient-to-r from-slate-800 to-blue-600 bg-clip-text text-transparent mb-2",children:t}),i&&(0,n.jsx)("p",{className:"text-slate-600 text-sm sm:text-base lg:text-lg",children:i})]}),r&&(0,n.jsx)("div",{className:"flex flex-wrap gap-2 sm:gap-3",children:r})]})})}),(0,n.jsx)(l.E.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.5,delay:.1},className:"space-y-6",children:s})]})]})}},640:(e,s,t)=>{t.d(s,{Z:()=>i});var l=t(2791),a=t(184);const i=function(e){let{title:s}=e;const[t,i]=(0,l.useState)(!1);return(0,l.useEffect)((()=>{window.innerWidth<768&&i(!0)}),[]),(0,a.jsx)("div",{className:"mt-2",children:(0,a.jsx)("h1",{className:t?"text-lg":"",children:s})})}},8754:(e,s,t)=>{t.r(s),t.d(s,{default:()=>p});var l=t(7027),a=t(2791),i=t(9434),r=t(7689),n=t(6042),c=t(8262),d=(t(640),t(8247)),o=t(2134),x=t(6345),m=t(9976),h=t(5526),u=t(184);const p=function(){const e=(0,r.s0)(),[s,t]=(0,a.useState)([]),[p,j]=(0,a.useState)([]),[b,g]=(0,a.useState)(""),[N,f]=(0,a.useState)("all"),[v,w]=(0,a.useState)("all"),[y,k]=(0,a.useState)(!1),E=(0,i.I0)(),C=e=>{const s=new Date,t=e.paymentRequired,l=e.subscriptionEndDate;e.subscriptionStartDate;if(!t)return"no-plan";if(t){if(l){return new Date(l)<s?"expired-plan":"on-plan"}return"on-plan"}return"no-plan"},P=async()=>{try{E((0,d.YC)());const e=await(0,c.AW)();E((0,d.Ir)()),e.success?(t(e.users),console.log("users loaded:",e.users.length)):l.ZP.error(e.message)}catch(e){E((0,d.Ir)()),l.ZP.error(e.message)}};(0,a.useEffect)((()=>{let e=s;b&&(e=e.filter((e=>{var s,t,l,a;return(null===(s=e.name)||void 0===s?void 0:s.toLowerCase().includes(b.toLowerCase()))||(null===(t=e.email)||void 0===t?void 0:t.toLowerCase().includes(b.toLowerCase()))||(null===(l=e.school)||void 0===l?void 0:l.toLowerCase().includes(b.toLowerCase()))||(null===(a=e.class)||void 0===a?void 0:a.toLowerCase().includes(b.toLowerCase()))}))),"all"!==N&&(e=e.filter((e=>"blocked"===N?e.isBlocked:"active"!==N||!e.isBlocked))),"all"!==v&&(e=e.filter((e=>C(e)===v))),j(e)}),[s,b,N,v]),(0,a.useEffect)((()=>{P()}),[]);const S=e=>{let{user:s}=e;const t=C(s),a=e=>e?new Date(e).toLocaleDateString():"N/A";return(0,u.jsx)(n.E.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},whileHover:{y:-2},transition:{duration:.2},children:(0,u.jsx)(o.Zb,{className:"p-6 hover:shadow-large",children:(0,u.jsxs)("div",{className:"flex items-start justify-between",children:[(0,u.jsxs)("div",{className:"flex items-start space-x-4",children:[(0,u.jsx)("div",{className:"w-12 h-12 rounded-full flex items-center justify-center ".concat(s.isBlocked?"bg-error-100":"bg-primary-100"),children:(0,u.jsx)(h.S8z,{className:"w-6 h-6 ".concat(s.isBlocked?"text-error-600":"text-primary-600")})}),(0,u.jsxs)("div",{className:"flex-1",children:[(0,u.jsxs)("div",{className:"flex items-center space-x-2 mb-2",children:[(0,u.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:s.name}),(0,u.jsx)("span",{className:"badge-modern ".concat(s.isBlocked?"bg-error-100 text-error-800":"bg-success-100 text-success-800"),children:s.isBlocked?"Blocked":"Active"}),(()=>{switch(t){case"on-plan":return(0,u.jsxs)("span",{className:"badge-modern bg-green-100 text-green-800 flex items-center space-x-1",children:[(0,u.jsx)(h.PWB,{className:"w-3 h-3"}),(0,u.jsx)("span",{children:"On Plan"})]});case"expired-plan":return(0,u.jsxs)("span",{className:"badge-modern bg-orange-100 text-orange-800 flex items-center space-x-1",children:[(0,u.jsx)(h.rfE,{className:"w-3 h-3"}),(0,u.jsx)("span",{children:"Expired"})]});case"no-plan":return(0,u.jsxs)("span",{className:"badge-modern bg-gray-100 text-gray-800 flex items-center space-x-1",children:[(0,u.jsx)(h.lhV,{className:"w-3 h-3"}),(0,u.jsx)("span",{children:"No Plan"})]});default:return null}})()]}),(0,u.jsxs)("div",{className:"space-y-1 text-sm text-gray-600",children:[(0,u.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,u.jsx)(h.QOK,{className:"w-4 h-4"}),(0,u.jsx)("span",{children:s.email})]}),(0,u.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,u.jsx)(h.EW2,{className:"w-4 h-4"}),(0,u.jsx)("span",{children:s.school||"No school specified"})]}),(0,u.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,u.jsx)(h.HLl,{className:"w-4 h-4"}),(0,u.jsxs)("span",{children:["Class: ",s.class||"Not assigned"]})]}),s.subscriptionPlan&&(0,u.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,u.jsx)(h.PWB,{className:"w-4 h-4"}),(0,u.jsxs)("span",{children:["Plan: ",s.subscriptionPlan]})]}),s.subscriptionStartDate&&(0,u.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,u.jsx)(h.rfE,{className:"w-4 h-4"}),(0,u.jsxs)("span",{children:["Started: ",a(s.subscriptionStartDate)]})]}),s.subscriptionEndDate&&(0,u.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,u.jsx)(h.rfE,{className:"w-4 h-4"}),(0,u.jsxs)("span",{className:new Date(s.subscriptionEndDate)<new Date?"text-red-600 font-medium":"text-green-600",children:[new Date(s.subscriptionEndDate)<new Date?"Expired: ":"Expires: ",a(s.subscriptionEndDate)]})]}),void 0!==s.paymentRequired&&(0,u.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,u.jsx)(h.PWB,{className:"w-4 h-4"}),(0,u.jsx)("span",{className:s.paymentRequired?"text-blue-600":"text-gray-600",children:s.paymentRequired?"Paid Subscription":"Free Account"})]}),s.totalQuizzesTaken>0&&(0,u.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,u.jsx)(h.S8z,{className:"w-4 h-4"}),(0,u.jsxs)("span",{children:["Quizzes: ",s.totalQuizzesTaken]})]}),s.lastActivity&&(0,u.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,u.jsx)(h.rfE,{className:"w-4 h-4"}),(0,u.jsxs)("span",{children:["Last Active: ",a(s.lastActivity)]})]})]})]})]}),(0,u.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,u.jsx)(o.zx,{variant:s.isBlocked?"success":"warning",size:"sm",onClick:()=>(async e=>{try{E((0,d.YC)());const s=await(0,c.dN)({studentId:e});E((0,d.Ir)()),s.success?(l.ZP.success(s.message),P()):l.ZP.error(s.message)}catch(s){E((0,d.Ir)()),l.ZP.error(s.message)}})(s.studentId),icon:s.isBlocked?(0,u.jsx)(h.ZHQ,{}):(0,u.jsx)(h.GTS,{}),children:s.isBlocked?"Unblock":"Block"}),(0,u.jsx)(o.zx,{variant:"error",size:"sm",onClick:()=>{window.confirm("Are you sure you want to delete this user?")&&(async e=>{try{E((0,d.YC)());const s=await(0,c.JS)({studentId:e});E((0,d.Ir)()),s.success?(l.ZP.success("User deleted successfully"),P()):l.ZP.error(s.message)}catch(s){E((0,d.Ir)()),l.ZP.error(s.message)}})(s.studentId)},icon:(0,u.jsx)(h.EF5,{}),children:"Delete"})]})]})})})},Z=[(0,u.jsxs)(n.E.button,{whileHover:{scale:1.02},whileTap:{scale:.98},onClick:()=>e("/admin/reports"),className:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors duration-200 flex items-center gap-2",children:[(0,u.jsx)(h.f7Q,{className:"w-4 h-4"}),(0,u.jsx)("span",{className:"hidden sm:inline",children:"View Reports"})]},"reports"),(0,u.jsxs)(n.E.button,{whileHover:{scale:1.02},whileTap:{scale:.98},onClick:()=>{},className:"px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors duration-200 flex items-center gap-2",children:[(0,u.jsx)(h.HXz,{className:"w-4 h-4"}),(0,u.jsx)("span",{className:"hidden sm:inline",children:"Export"})]},"export")];return(0,u.jsxs)(x.Z,{showHeader:!1,children:[(0,u.jsx)("div",{className:"mb-6 sm:mb-8",children:(0,u.jsx)("div",{className:"bg-gradient-to-r from-green-600 to-blue-600 rounded-2xl p-6 sm:p-8 text-white",children:(0,u.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4",children:[(0,u.jsxs)("div",{className:"flex items-center gap-4",children:[(0,u.jsxs)(n.E.button,{whileHover:{scale:1.05},whileTap:{scale:.95},onClick:()=>e("/admin/dashboard"),className:"flex items-center gap-2 px-3 py-2 bg-white/20 hover:bg-white/30 rounded-lg transition-colors duration-200 text-white border border-white/30",children:[(0,u.jsx)(h.Qlt,{className:"w-4 h-4"}),(0,u.jsx)("span",{className:"hidden sm:inline text-sm font-medium",children:"Dashboard"})]}),(0,u.jsxs)("div",{children:[(0,u.jsx)("h1",{className:"text-2xl sm:text-3xl font-bold mb-2",children:"User Management"}),(0,u.jsx)("p",{className:"text-green-100 text-sm sm:text-base",children:"Manage student accounts, permissions, and access controls"})]})]}),(0,u.jsx)("div",{className:"flex flex-wrap gap-2",children:Z})]})})}),(0,u.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6 mb-6 sm:mb-8",children:[(0,u.jsx)(m.Z,{className:"bg-gradient-to-br from-blue-500 to-blue-600 text-white border-0",children:(0,u.jsxs)("div",{className:"flex items-center justify-between",children:[(0,u.jsxs)("div",{children:[(0,u.jsx)("p",{className:"text-blue-100 text-sm font-medium mb-1",children:"Total Users"}),(0,u.jsx)("p",{className:"text-2xl sm:text-3xl font-bold",children:s.length}),(0,u.jsx)("p",{className:"text-blue-200 text-xs mt-1",children:"All registered"})]}),(0,u.jsx)("div",{className:"w-12 h-12 bg-white/20 rounded-xl flex items-center justify-center",children:(0,u.jsx)(h.HLl,{className:"w-6 h-6"})})]})}),(0,u.jsx)(m.Z,{className:"bg-gradient-to-br from-green-500 to-green-600 text-white border-0",children:(0,u.jsxs)("div",{className:"flex items-center justify-between",children:[(0,u.jsxs)("div",{children:[(0,u.jsx)("p",{className:"text-green-100 text-sm font-medium mb-1",children:"Active Users"}),(0,u.jsx)("p",{className:"text-2xl sm:text-3xl font-bold",children:s.filter((e=>!e.isBlocked)).length}),(0,u.jsx)("p",{className:"text-green-200 text-xs mt-1",children:"Not blocked"})]}),(0,u.jsx)("div",{className:"w-12 h-12 bg-white/20 rounded-xl flex items-center justify-center",children:(0,u.jsx)(h.ZHQ,{className:"w-6 h-6"})})]})}),(0,u.jsx)(m.Z,{className:"bg-gradient-to-br from-orange-500 to-orange-600 text-white border-0",children:(0,u.jsxs)("div",{className:"flex items-center justify-between",children:[(0,u.jsxs)("div",{children:[(0,u.jsx)("p",{className:"text-orange-100 text-sm font-medium mb-1",children:"Expired Plans"}),(0,u.jsx)("p",{className:"text-2xl sm:text-3xl font-bold",children:s.filter((e=>"expired-plan"===C(e))).length}),(0,u.jsx)("p",{className:"text-orange-200 text-xs mt-1",children:"Need renewal"})]}),(0,u.jsx)("div",{className:"w-12 h-12 bg-white/20 rounded-xl flex items-center justify-center",children:(0,u.jsx)(h.rfE,{className:"w-6 h-6"})})]})}),(0,u.jsx)(m.Z,{className:"bg-gradient-to-br from-purple-500 to-purple-600 text-white border-0",children:(0,u.jsxs)("div",{className:"flex items-center justify-between",children:[(0,u.jsxs)("div",{children:[(0,u.jsx)("p",{className:"text-purple-100 text-sm font-medium mb-1",children:"No Plan"}),(0,u.jsx)("p",{className:"text-2xl sm:text-3xl font-bold",children:s.filter((e=>"no-plan"===C(e))).length}),(0,u.jsx)("p",{className:"text-purple-200 text-xs mt-1",children:"Free users"})]}),(0,u.jsx)("div",{className:"w-12 h-12 bg-white/20 rounded-xl flex items-center justify-center",children:(0,u.jsx)(h.lhV,{className:"w-6 h-6"})})]})})]}),(0,u.jsxs)(m.Z,{title:"Search & Filter",subtitle:"Find and filter users by various criteria",className:"mb-6 sm:mb-8",children:[(0,u.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4",children:[(0,u.jsxs)("div",{className:"lg:col-span-2",children:[(0,u.jsx)("label",{className:"block text-sm font-medium text-slate-700 mb-2",children:"Search Users"}),(0,u.jsx)(o.II,{placeholder:"Search by name, email, school, or class...",value:b,onChange:e=>g(e.target.value),icon:(0,u.jsx)(h.adB,{})})]}),(0,u.jsxs)("div",{children:[(0,u.jsx)("label",{className:"block text-sm font-medium text-slate-700 mb-2",children:"Filter by Status"}),(0,u.jsxs)("select",{value:N,onChange:e=>f(e.target.value),className:"w-full px-3 py-2 border border-slate-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors",children:[(0,u.jsx)("option",{value:"all",children:"All Users"}),(0,u.jsx)("option",{value:"active",children:"Active Only"}),(0,u.jsx)("option",{value:"blocked",children:"Blocked Only"})]})]}),(0,u.jsxs)("div",{children:[(0,u.jsx)("label",{className:"block text-sm font-medium text-slate-700 mb-2",children:"Filter by Plan"}),(0,u.jsxs)("select",{value:v,onChange:e=>w(e.target.value),className:"w-full px-3 py-2 border border-slate-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors",children:[(0,u.jsx)("option",{value:"all",children:"All Plans"}),(0,u.jsx)("option",{value:"on-plan",children:"On Plan"}),(0,u.jsx)("option",{value:"expired-plan",children:"Expired Plan"}),(0,u.jsx)("option",{value:"no-plan",children:"No Plan"})]})]})]}),(0,u.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 mt-4",children:[(0,u.jsx)("div",{children:(b||"all"!==N||"all"!==v)&&(0,u.jsxs)("span",{className:"text-sm text-slate-600",children:["Showing ",p.length," of ",s.length," users","all"!==v&&(0,u.jsxs)("span",{className:"ml-2 px-2 py-1 bg-blue-100 text-blue-800 rounded-full text-xs",children:["on-plan"===v&&"On Plan","expired-plan"===v&&"Expired Plan","no-plan"===v&&"No Plan"]})]})}),(0,u.jsxs)(n.E.button,{whileHover:{scale:1.02},whileTap:{scale:.98},onClick:()=>{g(""),f("all"),w("all")},className:"px-4 py-2 bg-slate-600 text-white rounded-lg hover:bg-slate-700 transition-colors duration-200 flex items-center gap-2",children:[(0,u.jsx)(h.a9n,{className:"w-4 h-4"}),"Clear Filters"]})]})]}),(0,u.jsx)(m.Z,{title:"Users (".concat(p.length,")"),subtitle:"Manage individual user accounts and permissions",loading:y,children:y?(0,u.jsx)("div",{className:"flex justify-center py-12",children:(0,u.jsx)(o.gb,{text:"Loading users..."})}):p.length>0?(0,u.jsx)("div",{className:"space-y-4",children:p.map(((e,s)=>(0,u.jsx)(n.E.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.05*s},children:(0,u.jsx)(S,{user:e})},e.studentId)))}):(0,u.jsxs)("div",{className:"text-center py-12",children:[(0,u.jsx)(h.HLl,{className:"w-16 h-16 text-slate-400 mx-auto mb-4"}),(0,u.jsx)("h3",{className:"text-xl font-semibold text-slate-900 mb-2",children:"No Users Found"}),(0,u.jsx)("p",{className:"text-slate-600",children:b||"all"!==N||"all"!==v?"Try adjusting your search or filter criteria":"No users have been registered yet"})]})})]})}}}]);
//# sourceMappingURL=754.d9581a3c.chunk.js.map