"use strict";(self.webpackChunkclient=self.webpackChunkclient||[]).push([[646],{5646:(e,n,t)=>{t.r(n),t.d(n,{default:()=>x});var a=t(1413),r=t(2791),s=t(7689),o=t(9434),i=t(5526),l=t(7870),d=t(7276),c=t(184);const x=()=>{const e=(0,s.s0)(),n=(0,s.TH)(),{id:t}=(0,s.UO)(),{user:x}=(0,o.v9)((e=>e.user)),p=n.state||{percentage:0,correctAnswers:0,totalQuestions:0,timeTaken:0,resultDetails:[],xpData:null,quizName:"Quiz",quizSubject:"General",passingPercentage:60,verdict:"Fail"},{percentage:m,correctAnswers:g,totalQuestions:h,timeTaken:u,xpData:f,quizName:b,quizSubject:w,passingPercentage:y,verdict:j,resultDetails:F}=p,v="Pass"===j||m>=(y||60),[N,C]=(0,r.useState)([]),[B,k]=(0,r.useState)({}),[z,D]=(0,r.useState)({}),[S,W]=(0,r.useState)(!1);(0,r.useEffect)((()=>{if(v){const e=[];for(let n=0;n<200;n++){const t=["#FFD700","#FFA500","#FF6B6B","#4ECDC4","#45B7D1","#96CEB4","#FF69B4","#32CD32","#FF4500","#9370DB","#00CED1","#FF1493","#00FF7F","#FF8C00","#DA70D6"];e.push({id:"confetti_".concat(n),left:20+60*Math.random(),delay:2*Math.random(),duration:4+3*Math.random(),color:t[Math.floor(Math.random()*t.length)],shape:["circle","square","triangle"][Math.floor(3*Math.random())],size:3+6*Math.random(),type:"confetti",randomX:200*(Math.random()-.5)})}for(let n=0;n<50;n++)e.push({id:"sparkle_".concat(n),left:100*Math.random(),delay:3*Math.random(),duration:2+2*Math.random(),color:["#FFD700","#FFFFFF","#FFF700","#FFFF00"][Math.floor(4*Math.random())],size:1+3*Math.random(),type:"sparkle"});for(let n=0;n<100;n++)e.push({id:"burst_".concat(n),left:45+10*Math.random(),delay:.5*Math.random(),duration:3+2*Math.random(),color:["#FFD700","#FF6B6B","#4ECDC4","#FF69B4"][Math.floor(4*Math.random())],shape:"circle",size:2+4*Math.random(),type:"burst",randomX:300*(Math.random()-.5)});C(e),setTimeout((()=>C([])),1e4)}else{const e=[],n=["\ud83d\udcaa","\ud83c\udf1f","\ud83d\udcda","\ud83c\udfaf","\ud83d\ude80","\ud83d\udca1","\u2b50","\u2728","\ud83d\udd25","\ud83d\udc8e"];for(let t=0;t<30;t++)e.push({id:"motivate_".concat(t),left:100*Math.random(),delay:4*Math.random(),duration:5+3*Math.random(),emoji:n[Math.floor(Math.random()*n.length)],isMotivational:!0,size:2+2*Math.random()});C(e),setTimeout((()=>C([])),8e3)}(()=>{W(!0),setTimeout((()=>W(!1)),3200);try{if(v){const e=new(window.AudioContext||window.webkitAudioContext),n=function(n,t,a){let r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:"sine";const s=e.createOscillator(),o=e.createGain();s.connect(o),o.connect(e.destination),s.frequency.setValueAtTime(n,t),s.type=r,o.gain.setValueAtTime(0,t),o.gain.linearRampToValueAtTime(.1,t+.01),o.gain.exponentialRampToValueAtTime(.01,t+a),s.start(t),s.stop(t+a)},t=n=>{const t=e.createBufferSource(),a=e.createBuffer(1,.1*e.sampleRate,e.sampleRate),r=a.getChannelData(0);for(let e=0;e<r.length;e++)r[e]=2*Math.random()-1;t.buffer=a;const s=e.createBiquadFilter();s.type="highpass",s.frequency.setValueAtTime(1e3,n);const o=e.createGain();o.gain.setValueAtTime(0,n),o.gain.linearRampToValueAtTime(.3,n+.01),o.gain.exponentialRampToValueAtTime(.01,n+.1),t.connect(s),s.connect(o),o.connect(e.destination),t.start(n),t.stop(n+.1)},a=e.currentTime;n(523.25,a,.2),n(659.25,a+.1,.2),n(783.99,a+.2,.2),n(1046.5,a+.3,.4),t(a+.5),t(a+.7),t(a+.9),t(a+1.1)}else{const e=new(window.AudioContext||window.webkitAudioContext),n=(n,t,a)=>{const r=e.createOscillator(),s=e.createGain();r.connect(s),s.connect(e.destination),r.frequency.setValueAtTime(n,t),r.type="sine",s.gain.setValueAtTime(0,t),s.gain.linearRampToValueAtTime(.08,t+.01),s.gain.exponentialRampToValueAtTime(.01,t+a),r.start(t),r.stop(t+a)},t=e.currentTime;n(440,t,.3),n(349.23,t+.2,.4)}}catch(e){console.log("Audio not supported")}})()}),[v]);return(0,c.jsxs)("div",{className:"min-h-screen flex items-center justify-center relative overflow-hidden ".concat(v?"bg-gradient-to-br from-green-50 via-emerald-50 to-teal-100":"bg-gradient-to-br from-red-50 via-pink-50 to-orange-100"," ").concat(S?v?"flash-green":"flash-red":""),style:{padding:window.innerWidth<=768?"8px":window.innerWidth<=1024?"16px":"24px"},children:[N.map((e=>e.isMotivational?(0,c.jsx)("div",{className:"absolute opacity-90",style:{left:"".concat(e.left,"%"),top:"".concat(20+60*Math.random(),"%"),fontSize:"".concat(e.size||2,"rem"),animation:"heart-beat ".concat(e.duration,"s ease-in-out ").concat(e.delay,"s infinite"),zIndex:100},children:e.emoji},e.id):"sparkle"===e.type?(0,c.jsx)("div",{className:"absolute",style:{left:"".concat(e.left,"%"),width:"".concat(e.size,"px"),height:"".concat(e.size,"px"),background:"radial-gradient(circle, ".concat(e.color,", transparent)"),borderRadius:"50%",animation:"premium-sparkle ".concat(e.duration,"s ease-in-out ").concat(e.delay,"s infinite"),top:"".concat(100*Math.random(),"%"),boxShadow:"0 0 ".concat(2*e.size,"px ").concat(e.color),zIndex:100}},e.id):"burst"===e.type?(0,c.jsx)("div",{className:"absolute opacity-90",style:{left:"".concat(e.left,"%"),width:"".concat(e.size,"px"),height:"".concat(e.size,"px"),backgroundColor:e.color,borderRadius:"circle"===e.shape?"50%":"triangle"===e.shape?"0":"0%",clipPath:"triangle"===e.shape?"polygon(50% 0%, 0% 100%, 100% 100%)":"none",animation:"confetti-burst ".concat(e.duration,"s ease-out ").concat(e.delay,"s forwards"),top:"40%","--random-x":"".concat(e.randomX,"px"),boxShadow:"0 0 ".concat(e.size,"px ").concat(e.color,"40"),zIndex:100}},e.id):(0,c.jsx)("div",{className:"absolute opacity-90",style:{left:"".concat(e.left,"%"),width:"".concat(e.size,"px"),height:"".concat(e.size,"px"),backgroundColor:e.color,borderRadius:"circle"===e.shape?"50%":"triangle"===e.shape?"0":"0%",clipPath:"triangle"===e.shape?"polygon(50% 0%, 0% 100%, 100% 100%)":"none",animation:"confetti-fall ".concat(e.duration,"s ease-out ").concat(e.delay,"s forwards"),top:"-20px",boxShadow:"0 0 ".concat(e.size,"px ").concat(e.color,"60"),border:"1px solid ".concat(e.color),background:"linear-gradient(45deg, ".concat(e.color,", ").concat(e.color,"80)"),zIndex:100}},e.id))),(0,c.jsx)("style",{jsx:!0,children:"\n        @keyframes confetti-fall {\n          0% {\n            transform: translateY(-20px) rotate(0deg) scale(0);\n            opacity: 0;\n          }\n          10% {\n            opacity: 1;\n            transform: translateY(0px) rotate(36deg) scale(1);\n          }\n          100% {\n            transform: translateY(100vh) rotate(1080deg) scale(0.3);\n            opacity: 0;\n          }\n        }\n\n        @keyframes confetti-burst {\n          0% {\n            transform: translateY(-10px) translateX(0px) rotate(0deg) scale(0);\n            opacity: 0;\n          }\n          15% {\n            transform: translateY(-50px) translateX(var(--random-x)) rotate(180deg) scale(1.2);\n            opacity: 1;\n          }\n          100% {\n            transform: translateY(100vh) translateX(calc(var(--random-x) * 2)) rotate(1440deg) scale(0);\n            opacity: 0;\n          }\n        }\n\n        @keyframes premium-sparkle {\n          0%, 100% {\n            transform: scale(0) rotate(0deg);\n            opacity: 0;\n          }\n          50% {\n            transform: scale(1.5) rotate(180deg);\n            opacity: 1;\n          }\n        }\n\n\n\n        @keyframes heart-beat {\n          0%, 100% { transform: scale(1); }\n          50% { transform: scale(1.3); }\n        }\n\n        @keyframes text-bounce {\n          0%, 20%, 50%, 80%, 100% { transform: translateY(0) scale(1); }\n          40% { transform: translateY(-20px) scale(1.1); }\n          60% { transform: translateY(-10px) scale(1.05); }\n        }\n\n        @keyframes text-glow {\n          0%, 100% {\n            text-shadow: 0 0 10px currentColor, 0 0 20px currentColor, 0 0 30px currentColor;\n            transform: scale(1);\n          }\n          50% {\n            text-shadow: 0 0 20px currentColor, 0 0 40px currentColor, 0 0 60px currentColor;\n            transform: scale(1.05);\n          }\n        }\n\n        @keyframes rainbow-text {\n          0% {\n            color: #FF6B6B;\n            text-shadow: 0 0 20px #FF6B6B, 0 0 40px #FF6B6B;\n          }\n          16% {\n            color: #FFD93D;\n            text-shadow: 0 0 20px #FFD93D, 0 0 40px #FFD93D;\n          }\n          33% {\n            color: #6BCF7F;\n            text-shadow: 0 0 20px #6BCF7F, 0 0 40px #6BCF7F;\n          }\n          50% {\n            color: #4D96FF;\n            text-shadow: 0 0 20px #4D96FF, 0 0 40px #4D96FF;\n          }\n          66% {\n            color: #9B59B6;\n            text-shadow: 0 0 20px #9B59B6, 0 0 40px #9B59B6;\n          }\n          83% {\n            color: #FF69B4;\n            text-shadow: 0 0 20px #FF69B4, 0 0 40px #FF69B4;\n          }\n          100% {\n            color: #FF6B6B;\n            text-shadow: 0 0 20px #FF6B6B, 0 0 40px #FF6B6B;\n          }\n        }\n\n        @keyframes shake-celebrate {\n          0%, 100% { transform: translateX(0) rotate(0deg); }\n          10% { transform: translateX(-5px) rotate(-1deg); }\n          20% { transform: translateX(5px) rotate(1deg); }\n          30% { transform: translateX(-5px) rotate(-1deg); }\n          40% { transform: translateX(5px) rotate(1deg); }\n          50% { transform: translateX(-3px) rotate(-0.5deg); }\n          60% { transform: translateX(3px) rotate(0.5deg); }\n          70% { transform: translateX(-2px) rotate(-0.5deg); }\n          80% { transform: translateX(2px) rotate(0.5deg); }\n          90% { transform: translateX(-1px) rotate(0deg); }\n        }\n\n        @keyframes zoom-in-out {\n          0%, 100% { transform: scale(1); }\n          50% { transform: scale(1.2); }\n        }\n\n        .animate-text-bounce {\n          animation: text-bounce 2s ease-in-out infinite;\n        }\n\n        .animate-text-glow {\n          animation: text-glow 2s ease-in-out infinite;\n        }\n\n        .animate-rainbow {\n          animation: rainbow-text 3s linear infinite;\n        }\n\n        .animate-shake-celebrate {\n          animation: shake-celebrate 1s ease-in-out infinite;\n        }\n\n        .animate-zoom {\n          animation: zoom-in-out 2s ease-in-out infinite;\n        }\n\n        @keyframes elegant-scale {\n          0%, 100% {\n            transform: scale(1) rotateY(0deg);\n            text-shadow: 0 0 20px currentColor;\n          }\n          50% {\n            transform: scale(1.1) rotateY(5deg);\n            text-shadow: 0 0 40px currentColor, 0 0 60px currentColor;\n          }\n        }\n\n        @keyframes premium-pulse {\n          0%, 100% {\n            transform: scale(1);\n            opacity: 1;\n          }\n          50% {\n            transform: scale(1.05);\n            opacity: 0.9;\n          }\n        }\n\n        @keyframes smooth-glow {\n          0%, 100% {\n            text-shadow: 0 0 15px currentColor, 0 0 30px currentColor, 0 0 45px currentColor;\n            filter: brightness(1) saturate(1.2);\n          }\n          50% {\n            text-shadow: 0 0 40px currentColor, 0 0 60px currentColor, 0 0 80px currentColor, 0 0 100px currentColor;\n            filter: brightness(1.3) saturate(1.5);\n          }\n        }\n\n        @keyframes rainbow-glow {\n          0% {\n            color: #FF6B6B;\n            text-shadow: 0 0 20px #FF6B6B, 0 0 40px #FF6B6B, 0 0 60px #FF6B6B;\n            filter: brightness(1.2) saturate(1.3);\n          }\n          16% {\n            color: #FFD93D;\n            text-shadow: 0 0 20px #FFD93D, 0 0 40px #FFD93D, 0 0 60px #FFD93D;\n            filter: brightness(1.2) saturate(1.3);\n          }\n          33% {\n            color: #6BCF7F;\n            text-shadow: 0 0 20px #6BCF7F, 0 0 40px #6BCF7F, 0 0 60px #6BCF7F;\n            filter: brightness(1.2) saturate(1.3);\n          }\n          50% {\n            color: #4D96FF;\n            text-shadow: 0 0 20px #4D96FF, 0 0 40px #4D96FF, 0 0 60px #4D96FF;\n            filter: brightness(1.2) saturate(1.3);\n          }\n          66% {\n            color: #9B59B6;\n            text-shadow: 0 0 20px #9B59B6, 0 0 40px #9B59B6, 0 0 60px #9B59B6;\n            filter: brightness(1.2) saturate(1.3);\n          }\n          83% {\n            color: #FF69B4;\n            text-shadow: 0 0 20px #FF69B4, 0 0 40px #FF69B4, 0 0 60px #FF69B4;\n            filter: brightness(1.2) saturate(1.3);\n          }\n          100% {\n            color: #FF6B6B;\n            text-shadow: 0 0 20px #FF6B6B, 0 0 40px #FF6B6B, 0 0 60px #FF6B6B;\n            filter: brightness(1.2) saturate(1.3);\n          }\n        }\n\n        @keyframes celebration-bounce {\n          0%, 20%, 50%, 80%, 100% {\n            transform: translateY(0) scale(1);\n          }\n          40% {\n            transform: translateY(-15px) scale(1.05);\n          }\n          60% {\n            transform: translateY(-8px) scale(1.02);\n          }\n        }\n\n        .animate-elegant {\n          animation: elegant-scale 3s ease-in-out infinite;\n        }\n\n        .animate-premium-pulse {\n          animation: premium-pulse 2s ease-in-out infinite;\n        }\n\n        .animate-smooth-glow {\n          animation: smooth-glow 2.5s ease-in-out infinite;\n        }\n\n        .animate-celebration {\n          animation: celebration-bounce 2s ease-in-out infinite;\n        }\n\n        .animate-rainbow {\n          animation: rainbow-text 3s linear infinite;\n        }\n\n        .animate-rainbow-glow {\n          animation: rainbow-glow 3s linear infinite;\n        }\n\n        @keyframes red-glow {\n          0%, 100% {\n            color: #EF4444;\n            text-shadow: 0 0 20px #EF4444, 0 0 40px #EF4444, 0 0 60px #EF4444;\n            filter: brightness(1.2) saturate(1.3);\n          }\n          25% {\n            color: #DC2626;\n            text-shadow: 0 0 25px #DC2626, 0 0 50px #DC2626, 0 0 75px #DC2626;\n            filter: brightness(1.3) saturate(1.4);\n          }\n          50% {\n            color: #B91C1C;\n            text-shadow: 0 0 30px #B91C1C, 0 0 60px #B91C1C, 0 0 90px #B91C1C;\n            filter: brightness(1.4) saturate(1.5);\n          }\n          75% {\n            color: #DC2626;\n            text-shadow: 0 0 25px #DC2626, 0 0 50px #DC2626, 0 0 75px #DC2626;\n            filter: brightness(1.3) saturate(1.4);\n          }\n        }\n\n        .animate-red-glow {\n          animation: red-glow 2.5s ease-in-out infinite;\n        }\n\n        @keyframes premium-green-flash {\n          0% {\n            background: linear-gradient(45deg, transparent, transparent);\n            box-shadow: none;\n          }\n          25% {\n            background: linear-gradient(45deg, rgba(34, 197, 94, 0.1), rgba(16, 185, 129, 0.1));\n            box-shadow: inset 0 0 100px rgba(34, 197, 94, 0.2);\n          }\n          50% {\n            background: linear-gradient(45deg, rgba(34, 197, 94, 0.4), rgba(16, 185, 129, 0.4));\n            box-shadow: inset 0 0 200px rgba(34, 197, 94, 0.5), 0 0 50px rgba(34, 197, 94, 0.3);\n          }\n          75% {\n            background: linear-gradient(45deg, rgba(34, 197, 94, 0.2), rgba(16, 185, 129, 0.2));\n            box-shadow: inset 0 0 150px rgba(34, 197, 94, 0.3);\n          }\n          100% {\n            background: linear-gradient(45deg, transparent, transparent);\n            box-shadow: none;\n          }\n        }\n\n        @keyframes premium-red-flash {\n          0% {\n            background: linear-gradient(45deg, transparent, transparent);\n            box-shadow: none;\n          }\n          25% {\n            background: linear-gradient(45deg, rgba(239, 68, 68, 0.1), rgba(220, 38, 127, 0.1));\n            box-shadow: inset 0 0 100px rgba(239, 68, 68, 0.2);\n          }\n          50% {\n            background: linear-gradient(45deg, rgba(239, 68, 68, 0.4), rgba(220, 38, 127, 0.4));\n            box-shadow: inset 0 0 200px rgba(239, 68, 68, 0.5), 0 0 50px rgba(239, 68, 68, 0.3);\n          }\n          75% {\n            background: linear-gradient(45deg, rgba(239, 68, 68, 0.2), rgba(220, 38, 127, 0.2));\n            box-shadow: inset 0 0 150px rgba(239, 68, 68, 0.3);\n          }\n          100% {\n            background: linear-gradient(45deg, transparent, transparent);\n            box-shadow: none;\n          }\n        }\n\n        .flash-green {\n          animation: premium-green-flash 0.8s ease-in-out 2;\n        }\n\n        .flash-red {\n          animation: premium-red-flash 0.8s ease-in-out 2;\n        }\n      "}),S&&(0,c.jsx)("div",{className:"fixed inset-0 pointer-events-none",style:{background:v?"radial-gradient(circle at center, rgba(34, 197, 94, 0.3) 0%, rgba(16, 185, 129, 0.2) 50%, transparent 100%)":"radial-gradient(circle at center, rgba(239, 68, 68, 0.3) 0%, rgba(220, 38, 127, 0.2) 50%, transparent 100%)",animation:v?"premium-green-flash 0.8s ease-in-out infinite":"premium-red-flash 0.8s ease-in-out infinite",zIndex:5}}),(0,c.jsxs)("div",{className:"bg-white rounded-2xl shadow-2xl border-2 w-full relative ".concat(v?"border-green-400 shadow-green-200":"border-red-400 shadow-red-200"," ").concat(S?"shadow-3xl":""),style:{background:S?v?"linear-gradient(135deg, rgba(255,255,255,0.95), rgba(34, 197, 94, 0.05))":"linear-gradient(135deg, rgba(255,255,255,0.95), rgba(239, 68, 68, 0.05))":"white",boxShadow:S?v?"0 25px 50px rgba(34, 197, 94, 0.3), 0 0 100px rgba(34, 197, 94, 0.2)":"0 25px 50px rgba(239, 68, 68, 0.3), 0 0 100px rgba(239, 68, 68, 0.2)":"0 25px 50px rgba(0,0,0,0.15)",zIndex:10,padding:window.innerWidth<=768?"16px":window.innerWidth<=1024?"24px":"32px",maxWidth:window.innerWidth<=768?"100%":window.innerWidth<=1024?"90%":"800px"},children:[(0,c.jsxs)("div",{className:"text-center",style:{marginBottom:window.innerWidth<=768?"16px":"32px"},children:[(0,c.jsx)("div",{className:"inline-flex items-center justify-center rounded-full mb-4 relative ".concat(v?"bg-gradient-to-br from-green-100 to-emerald-100":"bg-gradient-to-br from-red-100 to-pink-100"),style:{width:window.innerWidth<=768?"60px":"96px",height:window.innerWidth<=768?"60px":"96px"},children:(0,c.jsx)(i.gBl,{className:"".concat(v?"text-yellow-500":"text-gray-500"),style:{width:window.innerWidth<=768?"30px":"48px",height:window.innerWidth<=768?"30px":"48px"}})}),(0,c.jsx)("h1",{className:"font-bold mb-4 ".concat(v?"text-green-600 animate-elegant animate-smooth-glow":"animate-premium-pulse animate-red-glow"),style:{fontSize:window.innerWidth<=768?"24px":window.innerWidth<=1024?"36px":"48px"},children:v?(0,c.jsxs)("span",{className:"flex items-center justify-center",style:{gap:window.innerWidth<=768?"8px":"16px",flexWrap:"wrap"},children:[(0,c.jsx)("span",{className:"animate-celebration",style:{fontSize:window.innerWidth<=768?"32px":"56px"},children:"\ud83c\udf89"}),(0,c.jsx)("span",{className:"animate-rainbow-glow animate-elegant",children:"Congratulations!"}),(0,c.jsx)("span",{className:"animate-celebration",style:{fontSize:window.innerWidth<=768?"32px":"56px"},children:"\ud83c\udf89"})]}):(0,c.jsxs)("span",{className:"flex items-center justify-center",style:{gap:window.innerWidth<=768?"8px":"16px",flexWrap:"wrap"},children:[(0,c.jsx)("span",{className:"animate-premium-pulse",style:{fontSize:window.innerWidth<=768?"32px":"56px"},children:"\ud83d\udcaa"}),(0,c.jsx)("span",{className:"animate-red-glow animate-elegant",children:"Keep Going!"}),(0,c.jsx)("span",{className:"animate-premium-pulse",style:{fontSize:window.innerWidth<=768?"32px":"56px"},children:"\ud83d\udcaa"})]})}),(0,c.jsx)("div",{className:"text-3xl font-bold mb-4 ".concat(v?"animate-celebration animate-rainbow-glow":"animate-premium-pulse animate-red-glow"),children:v?(0,c.jsx)("span",{className:"animate-elegant animate-rainbow-glow",children:"\u2728 You Passed! \u2728"}):(0,c.jsx)("span",{className:"animate-red-glow animate-elegant",children:"\ud83c\udf1f You Can Do It! \ud83c\udf1f"})}),(0,c.jsxs)("p",{className:"text-gray-600 mt-2 text-lg",children:["\ud83d\udcda ",b," - ",w]})]}),(0,c.jsx)("div",{className:"text-center mb-8",children:(0,c.jsxs)("div",{className:"inline-block px-8 py-4 rounded-2xl ".concat(v?"bg-green-50 border-2 border-green-200":"bg-red-50 border-2 border-red-200"),children:[(0,c.jsxs)("div",{className:"text-5xl font-bold mb-2 ".concat(v?"text-green-600":"text-red-600"),children:[m,"%"]}),(0,c.jsx)("div",{className:"text-gray-600",children:"Your Score"})]})}),(0,c.jsxs)("div",{className:"flex gap-2 mb-2 justify-center items-center",style:{flexWrap:"wrap"},children:[(0,c.jsxs)("div",{style:{backgroundColor:"#ffffff",padding:"4px 8px",border:"1px solid #e5e7eb",borderRadius:"6px",display:"flex",alignItems:"center",gap:"8px",fontSize:"12px"},children:[(0,c.jsxs)("div",{style:{display:"flex",alignItems:"center",gap:"2px"},children:[(0,c.jsx)("span",{style:{fontSize:"10px"},children:"\u2705"}),(0,c.jsx)("span",{style:{fontSize:"14px",fontWeight:"bold",color:"#16a34a"},children:g})]}),(0,c.jsxs)("div",{style:{display:"flex",alignItems:"center",gap:"2px"},children:[(0,c.jsx)("span",{style:{fontSize:"10px"},children:"\u274c"}),(0,c.jsx)("span",{style:{fontSize:"14px",fontWeight:"bold",color:"#dc2626"},children:h-g})]})]}),(0,c.jsxs)("div",{style:{backgroundColor:"#ffffff",padding:"4px 8px",border:"1px solid #e5e7eb",borderRadius:"6px",display:"flex",alignItems:"center",gap:"4px",fontSize:"12px"},children:[(0,c.jsx)("span",{style:{fontSize:"10px"},children:"\ud83d\udcca"}),(0,c.jsxs)("span",{style:{fontSize:"14px",fontWeight:"bold",color:v?"#16a34a":"#dc2626"},children:[m,"%"]})]}),u&&u>0&&(0,c.jsxs)("div",{style:{backgroundColor:"#ffffff",padding:"4px 8px",border:"1px solid #e5e7eb",borderRadius:"6px",display:"flex",alignItems:"center",gap:"4px",fontSize:"12px"},children:[(0,c.jsx)("span",{style:{fontSize:"10px"},children:"\u23f1\ufe0f"}),(0,c.jsxs)("span",{style:{fontSize:"14px",fontWeight:"bold",color:"#2563eb"},children:[Math.floor(u/60),":",(u%60).toString().padStart(2,"0")]})]})]}),f&&(0,c.jsx)("div",{className:"bg-gradient-to-r from-purple-50 to-indigo-50 rounded-xl p-4 mb-6 border border-purple-200",children:(0,c.jsxs)("div",{className:"flex items-center justify-between",children:[(0,c.jsxs)("div",{className:"flex items-center gap-3",children:[(0,c.jsx)("div",{className:"w-12 h-12 bg-purple-500 rounded-full flex items-center justify-center",children:(0,c.jsx)(i.jsT,{className:"w-6 h-6 text-white"})}),(0,c.jsxs)("div",{children:[(0,c.jsxs)("div",{className:"text-purple-600 font-bold text-xl",children:["+",f.xpAwarded||0," XP"]}),(0,c.jsx)("div",{className:"text-sm text-gray-600",children:"Earned"})]})]}),(0,c.jsxs)("div",{className:"text-right",children:[(0,c.jsx)("div",{className:"text-2xl font-bold text-indigo-600",children:(((null===x||void 0===x?void 0:x.totalXP)||0)+(f.xpAwarded||0)).toLocaleString()}),(0,c.jsxs)("div",{className:"text-sm text-gray-600",children:["Total XP \u2022 Level ",(null===x||void 0===x?void 0:x.currentLevel)||1]})]})]})}),!f&&x&&(0,c.jsx)("div",{className:"bg-gradient-to-r from-indigo-50 to-purple-50 rounded-xl p-4 mb-6 border border-indigo-200",children:(0,c.jsxs)("div",{className:"flex items-center justify-between",children:[(0,c.jsxs)("div",{className:"flex items-center gap-3",children:[(0,c.jsx)("div",{className:"w-12 h-12 bg-indigo-500 rounded-full flex items-center justify-center",children:(0,c.jsx)(i.jsT,{className:"w-6 h-6 text-white"})}),(0,c.jsx)("div",{children:(0,c.jsx)("div",{className:"text-sm text-gray-600",children:"Your Progress"})})]}),(0,c.jsxs)("div",{className:"text-right",children:[(0,c.jsx)("div",{className:"text-2xl font-bold text-indigo-600",children:(x.totalXP||0).toLocaleString()}),(0,c.jsxs)("div",{className:"text-sm text-gray-600",children:["Total XP \u2022 Level ",x.currentLevel||1]})]})]})}),(0,c.jsx)("div",{className:"bg-gradient-to-r from-blue-50 to-cyan-50 rounded-xl p-6 mb-6 border border-blue-200",children:(0,c.jsxs)("div",{className:"flex items-center gap-3 mb-4",children:[(0,c.jsx)("div",{className:"w-10 h-10 bg-blue-500 rounded-full flex items-center justify-center",children:(0,c.jsx)(i.Kkf,{className:"w-6 h-6 text-white"})}),(0,c.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"\ud83d\udcda Learning Summary"})]})}),F&&F.length>0&&(0,c.jsxs)("div",{className:"bg-gradient-to-r from-gray-50 to-slate-50 rounded-xl p-6 mb-6 border border-gray-200",children:[(0,c.jsxs)("div",{className:"flex items-center gap-3 mb-4",children:[(0,c.jsx)("div",{className:"w-10 h-10 bg-gray-600 rounded-full flex items-center justify-center",children:(0,c.jsx)(i.hWk,{className:"w-6 h-6 text-white"})}),(0,c.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"\ud83d\udccb Question by Question Review"})]}),(0,c.jsx)("div",{className:"space-y-4 max-h-96 overflow-y-auto",children:F.map(((e,n)=>(console.log("Question ".concat(n+1," data:"),e),(0,c.jsxs)("div",{className:"rounded-xl overflow-hidden transition-all duration-300 hover:shadow-xl ".concat(e.isCorrect?"bg-gradient-to-r from-green-100 to-emerald-100 shadow-lg shadow-green-300":"bg-gradient-to-r from-red-100 to-pink-100 shadow-lg shadow-red-300"),style:{border:e.isCorrect?"4px solid #16a34a":"4px solid #dc2626",boxShadow:e.isCorrect?"0 10px 25px rgba(34, 197, 94, 0.4), 0 0 0 2px rgba(34, 197, 94, 0.2)":"0 10px 25px rgba(239, 68, 68, 0.4), 0 0 0 2px rgba(239, 68, 68, 0.2)"},children:[(0,c.jsx)("div",{className:"p-4 ".concat(e.isCorrect?"bg-green-300 border-b-4 border-green-500":"bg-red-300 border-b-4 border-red-500"),children:(0,c.jsxs)("div",{className:"flex items-center gap-3",children:[(0,c.jsx)("div",{className:"w-10 h-10 rounded-full flex items-center justify-center font-bold ".concat(e.isCorrect?"bg-green-500 text-white shadow-lg":"bg-red-500 text-white shadow-lg"),children:e.isCorrect?(0,c.jsx)(i.e6w,{className:"w-5 h-5"}):(0,c.jsx)(i.lhV,{className:"w-5 h-5"})}),(0,c.jsxs)("div",{className:"flex-1",children:[(0,c.jsxs)("h4",{className:"font-bold text-gray-900 text-lg",children:["Question ",n+1]}),(0,c.jsx)("div",{className:"flex items-center gap-2 mt-1",children:(0,c.jsx)("span",{className:"px-3 py-1 rounded-full text-xs font-bold ".concat(e.isCorrect?"bg-green-500 text-white":"bg-red-500 text-white"),children:e.isCorrect?"\u2705 CORRECT":"\u274c WRONG"})})]})]})}),(0,c.jsxs)("div",{className:"p-4",children:[(0,c.jsx)("div",{className:"mb-4",children:(0,c.jsx)("p",{className:"text-gray-700 bg-white p-3 rounded-lg border",children:e.questionText||e.questionName})}),("image"===e.questionType||e.questionImage||e.image||e.imageUrl)&&(e.questionImage||e.image||e.imageUrl)&&(0,c.jsx)("div",{className:"mb-4",children:(0,c.jsxs)("div",{className:"p-3 rounded-lg border-2 ".concat(e.isCorrect?"bg-green-50 border-green-200":"bg-red-50 border-red-200"),children:[(0,c.jsxs)("div",{className:"flex items-center gap-2 mb-2",children:[(0,c.jsx)("span",{className:"text-sm font-semibold text-gray-700",children:"\ud83d\udcf7 Question Image:"}),(0,c.jsx)("span",{className:"px-2 py-1 rounded-full text-xs font-bold ".concat(e.isCorrect?"bg-green-500 text-white":"bg-red-500 text-white"),children:e.isCorrect?"\u2705 CORRECT":"\u274c WRONG"})]}),(0,c.jsxs)("div",{className:"bg-white p-2 rounded-lg border",children:[(0,c.jsx)("img",{src:e.questionImage||e.image||e.imageUrl,alt:"Question Image",className:"max-w-full h-auto rounded-lg shadow-sm mx-auto block",style:{maxHeight:"300px"},onError:e=>{e.target.style.display="none",e.target.nextSibling.style.display="block"}}),(0,c.jsx)("div",{className:"text-center text-gray-500 text-sm p-4 bg-gray-100 rounded-lg",style:{display:"none"},children:"\ud83d\udcf7 Image could not be loaded"})]})]})}),(0,c.jsxs)("div",{className:"space-y-3",children:[(0,c.jsxs)("div",{className:"p-4 rounded-lg ".concat(e.isCorrect?"bg-green-50":"bg-red-50"),style:{border:e.isCorrect?"3px solid #16a34a":"3px solid #dc2626"},children:[(0,c.jsxs)("div",{className:"flex items-center gap-3 mb-2",children:[(0,c.jsx)("div",{className:"w-6 h-6 rounded-full flex items-center justify-center ".concat(e.isCorrect?"bg-green-500":"bg-red-500"),children:e.isCorrect?(0,c.jsx)(i.e6w,{className:"w-4 h-4 text-white"}):(0,c.jsx)(i.lhV,{className:"w-4 h-4 text-white"})}),(0,c.jsx)("span",{className:"font-semibold text-gray-700",children:"Your Answer:"})]}),(0,c.jsx)("div",{className:"p-3 rounded-lg font-bold text-lg ".concat(e.isCorrect?"bg-green-100 text-green-700 border border-green-200":"bg-red-100 text-red-700 border border-red-200"),children:e.userAnswer||"No answer provided"})]}),!e.isCorrect&&(0,c.jsxs)("div",{className:"bg-green-50 p-4 rounded-lg border-4 border-green-500",children:[(0,c.jsxs)("div",{className:"flex items-center gap-3 mb-2",children:[(0,c.jsx)("div",{className:"w-6 h-6 rounded-full bg-green-500 flex items-center justify-center",children:(0,c.jsx)(i.e6w,{className:"w-4 h-4 text-white"})}),(0,c.jsx)("span",{className:"font-semibold text-gray-700",children:"Correct Answer:"})]}),(0,c.jsx)("div",{className:"bg-green-100 p-3 rounded-lg border-2 border-green-400 font-bold text-lg text-green-700",children:e.correctAnswer})]}),!e.isCorrect&&(0,c.jsxs)("div",{className:"mt-3",children:[(0,c.jsx)("button",{className:"w-full font-semibold py-3 px-4 rounded-lg transition-all duration-200 flex items-center justify-center gap-2 ".concat(z["question_".concat(n)]?"bg-gray-400 cursor-not-allowed":"bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white shadow-lg hover:shadow-xl"),onClick:()=>(async(e,n)=>{const t="question_".concat(e);if(!z[t]&&!B[t])try{D((e=>(0,a.Z)((0,a.Z)({},e),{},{[t]:!0})));const e=await(0,l.u$)({question:n.questionText||n.questionName,expectedAnswer:n.correctAnswer,userAnswer:n.userAnswer,imageUrl:n.questionImage||n.image||n.imageUrl||null});e.success?k((n=>(0,a.Z)((0,a.Z)({},n),{},{[t]:e.explanation}))):(console.error("Failed to fetch explanation:",e.error),k((e=>(0,a.Z)((0,a.Z)({},e),{},{[t]:"Sorry, we could not generate an explanation at this time. Please try again later."}))))}catch(r){console.error("Error fetching explanation:",r),k((e=>(0,a.Z)((0,a.Z)({},e),{},{[t]:"Sorry, we could not generate an explanation at this time. Please try again later."})))}finally{D((e=>(0,a.Z)((0,a.Z)({},e),{},{[t]:!1})))}})(n,e),disabled:z["question_".concat(n)],children:z["question_".concat(n)]?(0,c.jsxs)(c.Fragment,{children:[(0,c.jsx)("div",{className:"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"}),"Getting Explanation..."]}):(0,c.jsxs)(c.Fragment,{children:[(0,c.jsx)(i.Kkf,{className:"w-5 h-5"}),"Get Explanation"]})}),B["question_".concat(n)]&&(0,c.jsxs)("div",{className:"mt-3 p-4 bg-gradient-to-r from-blue-50 to-purple-50 border-2 border-blue-200 rounded-lg",children:[(0,c.jsxs)("div",{className:"flex items-center gap-2 mb-3",children:[(0,c.jsx)(i.Kkf,{className:"w-5 h-5 text-blue-600"}),(0,c.jsx)("h6",{className:"font-bold text-blue-800",children:"\ud83d\udca1 Explanation:"})]}),(0,c.jsx)("div",{className:"text-blue-700 leading-relaxed",children:(0,c.jsx)(d.Z,{text:B["question_".concat(n)]})})]})]})]})]})]},e.questionId||n))))})]}),(0,c.jsxs)("div",{className:"flex gap-4",style:{flexDirection:window.innerWidth<=768?"column":"row"},children:[(0,c.jsxs)("button",{onClick:n=>{n.preventDefault(),console.log("\ud83d\udd25 More Quizzes button clicked!"),console.log("\ud83c\udfe0 Navigating to quiz listing..."),(0,r.startTransition)((()=>{e("/user/quiz")}))},className:"flex-1 flex items-center justify-center gap-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-all font-medium cursor-pointer",style:{padding:window.innerWidth<=768?"12px 16px":"12px 24px",fontSize:window.innerWidth<=768?"14px":"16px"},type:"button",children:[(0,c.jsx)(i.diY,{style:{width:window.innerWidth<=768?"16px":"20px",height:window.innerWidth<=768?"16px":"20px"}}),"More Quizzes"]}),(0,c.jsxs)("button",{onClick:n=>{n.preventDefault(),console.log("\ud83d\udd25 Retake Quiz button clicked!"),console.log("\ud83d\udd04 Retaking quiz with ID:",t),t?(0,r.startTransition)((()=>{e("/quiz/".concat(t,"/play"))})):(console.log("\u274c No quiz ID available, going to quiz listing"),(0,r.startTransition)((()=>{e("/user/quiz")})))},className:"flex-1 flex items-center justify-center gap-2 bg-green-600 text-white rounded-lg hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 transition-all font-medium cursor-pointer",style:{padding:window.innerWidth<=768?"12px 16px":"12px 24px",fontSize:window.innerWidth<=768?"14px":"16px"},type:"button",children:[(0,c.jsx)(i.gBl,{style:{width:window.innerWidth<=768?"16px":"20px",height:window.innerWidth<=768?"16px":"20px"}}),"Retake Quiz"]})]})]})]})}}}]);
//# sourceMappingURL=646.c7724f48.chunk.js.map