{"version": 3, "file": "static/css/100.51b88b54.chunk.css", "mappings": "AAAA,mBACE,cACF,CAEA,WACE,eAAiB,CAIjB,wBAAyB,CAHzB,kBAAmB,CAEnB,+BAAyC,CADzC,YAGF,CAEA,kBAIE,+BAAgC,CAFhC,kBAAmB,CACnB,mBAAoB,CAFpB,iBAIF,CAEA,sBAEE,aAAc,CADd,cAAe,CAEf,mBACF,CAEA,qBAGE,aAAc,CAFd,gBAAiB,CACjB,eAAgB,CAEhB,QACF,CAEA,eACE,cACF,CAEA,UAGE,aAAS,CAAT,QACF,CAEA,gBACE,kBACF,CAEA,YAOE,eAAiB,CAJjB,wBAAyB,CAEzB,cAAe,CAHf,cAAgB,CAIhB,gCAEF,CAQA,yBACE,aACF,CAGA,2BAGE,cACF,CAEA,qBACE,kCAAoC,CAGpC,qBAAuB,CADvB,8BAGF,CAYA,6DAHE,wBAA0B,CAD1B,0BAQF,CAJA,kCAEE,uBAEF,CAGA,uBACE,kBACF,CAEA,wBAGE,aAAS,CAFT,YAAa,CAEb,QAAS,CADT,iCAAkC,CAElC,gBACF,CAEA,eAOE,eAAiB,CANjB,wBAAyB,CAEzB,cAAe,CACf,iBAIF,CAEA,qBAEE,kBACF,CAEA,sBAEE,kBAAmB,CACnB,8BACF,CAEA,aAEE,aAAc,CADd,cAAe,CAEf,mBACF,CAEA,oBAGE,aAAc,CAFd,aAAc,CAId,cAAe,CAHf,eAAgB,CAEhB,oBAEF,CAEA,iBAEE,aAAc,CADd,iBAAmB,CAEnB,QACF,CAGA,gBACE,oBACF,CAEA,oGAGE,eACF,CAEA,aAKE,kBAAmB,CAJnB,yBAA0B,CAE1B,YAKF,CAOA,mBACE,cACF,CAGA,qBACE,iBAAkB,CAClB,uBACF,CAEA,2BAEE,kDAA6D,CAD7D,oBAAqB,CAGrB,+BAA+C,CAD/C,0BAEF,CAEA,+BAIE,2BAA4B,CAF5B,kDAA6D,CAD7D,oBAAqB,CAErB,kBAEF,CAQA,aACE,gBAAiB,CAEjB,mBAAqB,CACrB,uBACF,CAEA,wCACE,aAAc,CACd,oBACF,CAEA,eAEE,aAAc,CADd,eAGF,CAEA,aAEE,uBAAyB,CADzB,iBAGF,CAGA,yBAGE,kBAAmB,CACnB,wBAAyB,CACzB,kBAAmB,CACnB,8BAAwC,CALxC,aAAc,CACd,cAKF,CAEA,iBACE,kBACF,CAEA,eAGE,kBAAmB,CAFnB,YAAa,CACb,6BAA8B,CAE9B,mBACF,CAEA,cAIE,aAAc,CAHd,YAAa,CAEb,gBAAkB,CADlB,QAAS,CAGT,gBACF,CAEA,8BAGE,kBAAmB,CADnB,YAAa,CAEb,UACF,CAEA,eAEE,aAAc,CACd,cAAe,CAFf,eAGF,CAEA,qBAEE,aAAc,CACd,gBAAiB,CAFjB,eAGF,CAEA,cAGE,kBAAmB,CACnB,iBAAkB,CAFlB,UAAW,CAIX,kBAAmB,CADnB,eAAgB,CAJhB,UAMF,CAEA,eAEE,iDAAoD,CACpD,iBAAkB,CAFlB,WAAY,CAIZ,iBAAkB,CADlB,yBAEF,CAEA,qBAQE,6BAA8B,CAD9B,uDAAmF,CADnF,QAAS,CALT,UAAW,CAGX,MAAO,CAFP,iBAAkB,CAGlB,OAAQ,CAFR,KAMF,CAEA,mBACE,GAAK,2BAA8B,CACnC,GAAO,0BAA6B,CACtC,CAEA,kBAEE,eAAgB,CADhB,iBAEF,CAEA,iCAGE,iBAAkB,CAClB,eAAgB,CAFhB,cAGF,CAEA,gBACE,kBAAmB,CACnB,wBAAyB,CACzB,aACF,CAEA,iBACE,kBAAmB,CACnB,wBAAyB,CACzB,aACF,CAEA,2CAEE,aAAc,CAEd,eAAgB,CADhB,oBAEF,CAEA,6CAEE,UAAW,CACX,eAAiB,CACjB,eACF,CAGA,cAME,4BAA6B,CAJ7B,QAAS,CAET,eAAgB,CAChB,kBAEF,CAEA,YACE,4BAA8B,CAC9B,8BAAgC,CAKhC,2BAA6B,CAJ7B,oBAAuB,CACvB,yBAA2B,CAC3B,qBAAuB,CACvB,wBAEF,CAEA,kBACE,4BAA8B,CAC9B,8BACF,CAEA,YACE,4BAA8B,CAC9B,8BAAgC,CAIhC,2BAA6B,CAH7B,yBAA2B,CAC3B,qBAAuB,CACvB,wBAEF,CAEA,kBACE,4BAA8B,CAC9B,8BACF,CAGA,sBAGE,4BAA8B,CAD9B,kCAAoC,CADpC,2BAGF,CAEA,2BACE,uBAAyB,CACzB,yBACF,CAGA,0BACE,wBAEE,UAAY,CADZ,6BAEF,CACF,CAEA,yBACE,WAEE,cAAgB,CADhB,cAEF,CAEA,UAEE,KAAM,CADN,yBAEF,CAEA,cACE,qBAAsB,CACtB,UACF,CAEA,wBAEE,UACF,CAEA,aACE,cACF,CAEA,aACE,cACF,CACF,CAEA,yBACE,WACE,YACF,CAEA,sBACE,gBACF,CAEA,qBACE,gBACF,CAEA,aACE,YACF,CAEA,eACE,eACF,CAEA,wBAEE,UAAY,CADZ,yBAEF,CAEA,eACE,YACF,CAEA,aACE,gBACF,CAEA,yBACE,eAAgB,CAChB,YACF,CAEA,eAEE,sBAAuB,CADvB,qBAAsB,CAEtB,SACF,CAEA,iBAEE,eAAiB,CADjB,aAEF,CACF,CAGA,4BACE,oBACF,CAEA,yBACE,oBACF,CAEA,2BAEE,UAAW,CACX,eAAiB,CACjB,eAAgB,CAHhB,QAIF,CAEA,yBACE,gBACF,CAEA,+BACE,UAAW,CAEX,aAAc,CACd,iBACF,CAGA,gDACE,kBAAmB,CACnB,wBAAyB,CAEzB,iBAAkB,CADlB,aAAc,CAEd,gBACF,CAEA,uDACE,aACF,CAEA,6DACE,aACF,CCnhBA,kBAEE,eAAgB,CAChB,iBAAkB,CAClB,8BAAwC,CAHxC,YAIF,CAEA,yBAGE,+BAAgC,CAFhC,kBAAmB,CACnB,mBAEF,CAEA,4BAEE,aAAc,CACd,cAAe,CACf,eAAgB,CAHhB,cAIF,CAEA,2BAEE,UAAW,CACX,cAAe,CAFf,QAGF,CAEA,sBACE,YAAa,CACb,qBAAsB,CACtB,OACF,CAEA,oBACE,YAAa,CACb,cAAe,CACf,OAAQ,CACR,cACF,CAEA,YACE,YAAa,CACb,qBACF,CAEA,aAEE,aAAc,CADd,eAAgB,CAEhB,iBACF,CAEA,YAEE,aAAc,CADd,cAEF,CAEA,cACE,YAAa,CAEb,cAAe,CADf,OAEF,CAEA,cACE,kDAA6D,CAC7D,WAAY,CACZ,UACF,CAEA,oBACE,kDAA6D,CAC7D,UACF,CAEA,YACE,kBAAmB,CACnB,oBAAqB,CACrB,UACF,CAEA,kBACE,kBAAmB,CACnB,oBAAqB,CACrB,UACF,CAEA,aACE,kBAAmB,CACnB,oBAAqB,CACrB,UACF,CAEA,mBACE,kBAAmB,CACnB,oBAAqB,CACrB,UACF,CAEA,uCACE,YACF,CAEA,mCACE,UACF,CAEA,wCACE,cACF,CAEA,iBACE,kBACF,CAEA,uBAIE,aAAc,CAHd,aAAc,CAEd,eAAgB,CADhB,iBAGF,CAEA,qBACE,eACF,CAEA,2BAIE,aAAc,CAHd,aAAc,CAEd,eAAgB,CADhB,iBAGF,CAEA,iBACE,kBAAmB,CAEnB,iBAAkB,CAClB,kBAAmB,CAFnB,YAGF,CAEA,wBACE,aACF,CAGA,mBAEE,6BAA8B,CAD9B,aAEF,CAEA,kBACE,aACF,CAEA,eACE,aACF,CAEA,aACE,aACF,CAEA,iBACE,GACE,SACF,CACA,IACE,UACF,CACA,GACE,SACF,CACF,CAGA,yBACE,kBACE,YACF,CAEA,cACE,qBACF,CAEA,uBACE,UACF,CACF,CAGA,yCACE,kBAAmB,CAEnB,aAAc,CADd,eAEF,CAEA,+CACE,kBACF,CAEA,2BAEE,iBAAkB,CADlB,UAEF,CAGA,qBAEE,kBAAmB,CADnB,YAAa,CAEb,OAAQ,CACR,cACF,CAEA,mCACE,QACF,CAGA,uBACE,YAAa,CACb,qBAAsB,CACtB,OACF,CAEA,eAEE,kBAAmB,CAGnB,kBAAmB,CAEnB,6BAA8B,CAD9B,iBAAkB,CALlB,YAAa,CAEb,6BAA8B,CAC9B,gBAIF,CAEA,oBAEE,kBAAmB,CADnB,YAAa,CAEb,OACF,CAEA,uBACE,YAAa,CACb,OACF,CAEA,sBACE,kDAKF,CAEA,6CAHE,kBAAmB,CAHnB,UAAY,CACZ,cAAe,CACf,eAUF,CANA,uBACE,kDAKF,CChQA,wBAEE,kBAAmB,CACnB,gBAAiB,CAFjB,YAGF,CAEA,gBACE,kBAAmB,CACnB,iBACF,CAEA,mBACE,aAAc,CACd,cAAe,CACf,eAAgB,CAChB,iBACF,CAEA,kBACE,aAAc,CACd,cAAe,CACf,QACF,CAGA,iBACE,eAAiB,CAEjB,kBAAmB,CACnB,8BAAwC,CACxC,kBAAmB,CAHnB,YAIF,CAEA,aAIE,eAAgB,CAHhB,YAAa,CACb,cAAe,CACf,QAEF,CAEA,cACE,YAAa,CACb,qBAAsB,CACtB,OACF,CAEA,oBAEE,aAAc,CACd,cAAe,CAFf,eAGF,CAGA,iBACE,eAAiB,CAEjB,8BAAwC,CACxC,eACF,CAEA,6CALE,kBAOF,CAEA,wCACE,kBAAmB,CACnB,+BAAgC,CAEhC,aAAc,CADd,eAEF,CAEA,8CACE,kBACF,CAQA,gCAHE,kBAAmB,CADnB,YASF,CALA,iBAGE,QAAS,CACT,UACF,CAEA,eAIE,kBAAmB,CADnB,iBAAkB,CAFlB,cAAe,CACf,WAGF,CAEA,qBAEE,kBAAmB,CADnB,aAEF,CAEA,oBAEE,kBAAmB,CADnB,aAEF,CAEA,qBAEE,kBAAmB,CADnB,aAEF,CAEA,oBAEE,kBAAmB,CADnB,aAEF,CAEA,kBACE,QACF,CAEA,gBAEE,aAAc,CACd,cAAe,CAFf,eAAgB,CAIhB,eAAgB,CADhB,iBAEF,CAEA,eAEE,kBAAmB,CADnB,YAAa,CAGb,cAAe,CADf,OAEF,CAEA,WACE,aAAc,CACd,cACF,CAGA,aACE,kBAAmB,CAGnB,iBAAkB,CAFlB,aAAc,CAGd,cAAe,CACf,eAAgB,CAHhB,eAIF,CAGA,0BAIE,kBAAmB,CAHnB,iBAAkB,CAElB,YAAa,CADb,eAAgB,CAGhB,OACF,CAEA,kCACE,kBAAmB,CACnB,oBACF,CAEA,wCACE,kBAAmB,CACnB,oBACF,CAEA,oCACE,kBAAmB,CACnB,oBAAqB,CACrB,UACF,CAEA,0CACE,kBAAmB,CACnB,oBACF,CAGA,SACE,iBAAkB,CAElB,cAAe,CADf,eAAgB,CAEhB,eACF,CAGA,yBACE,wBACE,YACF,CAEA,aACE,qBAAsB,CACtB,QACF,CAEA,cACE,UACF,CAEA,mDAEE,oBACF,CAEA,iBAGE,OACF,CAEA,gCAJE,sBAAuB,CADvB,qBASF,CAJA,eAGE,OACF,CAEA,4BACE,cACF,CAEA,gBACE,cACF,CACF,CAGA,uBAGE,aAAc,CAFd,YAAa,CACb,iBAEF,CAEA,oBACE,gBACF,CAGA,WACE,YACF,CAEA,uBACE,aAAc,CACd,cACF,CAGA,gBACE,eAAgB,CAChB,iBACF,CAEA,2BACE,aAAc,CACd,cACF,CAGA,yBACE,aAAc,CACd,eACF,CAEA,2BACE,aAAc,CACd,cACF,CAGA,kBACE,iBACF,CAEA,6BACE,yBACF,CAEA,2CACE,yBACF,CAGA,YACE,iBACF,CAEA,qBACE,2BACF,CAGA,mBACE,kBAAmB,CACnB,iBACF,CAEA,2BACE,kBACF,CC7SA,0BACE,eAAiB,CACjB,kBAAmB,CACnB,+BAAyC,CACzC,eACF,CAEA,aACE,kDAA6D,CAE7D,UAAY,CADZ,YAEF,CAEA,gBAEE,kBAAmB,CADnB,YAAa,CAEb,QACF,CAEA,WAGE,gBAAoC,CACpC,kBAAmB,CAHnB,cAAe,CACf,YAGF,CAEA,gBACE,UAAY,CACZ,cAGF,CAEA,eACE,eAA+B,CAE/B,cAAe,CADf,cAEF,CAGA,eACE,YACF,CAEA,UAGE,aAAS,CAFT,YAAa,CAEb,QAAS,CADT,6BAEF,CAEA,gBACE,kBACF,CAEA,2BAEE,aAAc,CACd,cAAe,CAFf,eAGF,CAEA,YAOE,kBAAmB,CAJnB,wBAAyB,CACzB,iBAAkB,CAClB,cAAe,CAHf,iBAAkB,CAIlB,uBAAyB,CALzB,UAOF,CAEA,kBAGE,eAAiB,CADjB,oBAAqB,CAErB,8BAA6C,CAH7C,YAIF,CAGA,uBACE,kBACF,CAEA,eAGE,aAAc,CAFd,aAAc,CAGd,cAAe,CAFf,eAAgB,CAGhB,kBACF,CAEA,uBAGE,aAAS,CAFT,YAAa,CAEb,QAAS,CADT,6BAEF,CAEA,eAGE,kBAAmB,CAMnB,kBAAmB,CAJnB,wBAAyB,CACzB,kBAAmB,CACnB,cAAe,CANf,YAAa,CACb,qBAAsB,CAEtB,YAAa,CAIb,uBAEF,CAEA,qBAEE,kBAAmB,CADnB,oBAEF,CAEA,sBAEE,kBAAmB,CADnB,oBAAqB,CAErB,aACF,CAEA,aAGE,aAAc,CAFd,cAAe,CACf,iBAEF,CAEA,mCACE,aACF,CAEA,oBAEE,cAAe,CADf,eAEF,CAGA,iDACE,iBACF,CAEA,yBACE,cACF,CAEA,+BACE,aAAc,CACd,iBACF,CAGA,gBACE,kBACF,CAEA,aAKE,kBAAmB,CAJnB,yBAA0B,CAC1B,kBAAmB,CAKnB,cAAe,CAJf,iBAAkB,CAClB,iBAAkB,CAElB,uBAEF,CAEA,mBAEE,kBAAmB,CADnB,oBAEF,CAEA,mBACE,iBACF,CAEA,aAEE,aAAc,CADd,cAAe,CAEf,kBACF,CAEA,eAEE,aAAc,CACd,eAAgB,CAFhB,gBAGF,CAEA,aACE,uBAAyB,CACzB,wBAA0B,CAC1B,yBAA8B,CAC9B,wBACF,CAGA,cAME,4BAA6B,CAL7B,YAAa,CAEb,QAAS,CADT,wBAAyB,CAEzB,eAAgB,CAChB,gBAEF,CAEA,eAQE,kBAAmB,CAHnB,wBAAyB,CAFzB,iBAAkB,CAGlB,aAAc,CACd,YAAa,CAHb,eAAgB,CAKhB,OAAQ,CAPR,WAAY,CADZ,iBASF,CAEA,qBACE,oBAAqB,CACrB,aACF,CAEA,eAQE,kBAAmB,CAHnB,kBAAmB,CACnB,oBAAqB,CAHrB,iBAAkB,CAIlB,YAAa,CAHb,eAAgB,CAKhB,OAAQ,CAPR,WAAY,CADZ,iBASF,CAEA,qBACE,kBAAmB,CACnB,oBACF,CAGA,YACE,iBACF,CAEA,qBAGE,4BAA8B,CAD9B,kCAAoC,CADpC,2BAA6B,CAI7B,yBAA2B,CAD3B,0BAEF,CAEA,yCAEE,yBAA4B,CAD5B,8BAAgC,CAEhC,wCACF,CAEA,YACE,UACF,CAEA,iBACE,eACF,CAEA,sBAGE,kBAAmB,CADnB,wBAAyB,CADzB,iBAGF,CAGA,yBACE,0BAEE,iBAAkB,CADlB,WAEF,CAEA,aACE,YACF,CAEA,gBACE,qBAAsB,CAEtB,QAAS,CADT,iBAEF,CAEA,eACE,YACF,CAEA,UAEE,QACF,CAEA,iCAJE,yBAMF,CAEA,cACE,6BAA8B,CAC9B,QACF,CAEA,8BAGE,sBAAuB,CADvB,UAEF,CAEA,aACE,iBACF,CAEA,mBACE,iBACF,CACF,CAGA,iBACE,mBACF,CAGA,8CACE,8BACF,CAEA,qCACE,oBACF,CAEA,6BACE,aAAc,CACd,cAAe,CACf,cACF,CAGA,gDACE,8BACF,CAEA,uCACE,oBACF,CAGA,0CACE,4BAA8B,CAC9B,uBAAyB,CACzB,4BACF,CAEA,qBACE,kBAAmB,CACnB,aAAc,CACd,kBACF,CAGA,uCACE,oBACF,CAEA,iBAEE,kBAAmB,CADnB,kBAEF,CAGA,qBACE,iBAAkB,CAClB,+BACF,CAEA,iBACE,iBAAkB,CAClB,cACF,CAEA,iCACE,kBAAmB,CACnB,aAAc,CACd,eACF,CCjYA,uBAEE,aAAc,CADd,gBAAiB,CAEjB,iBACF,CAEA,0BACE,iBACF,CAEA,gBACE,kBACF,CAEA,mBAGE,aAAc,CAFd,gBAAiB,CACjB,eAAgB,CAEhB,mBACF,CAEA,kBAEE,aAAc,CADd,gBAAiB,CAEjB,QACF,CAEA,qBAGE,aAAS,CAFT,YAAa,CAEb,QAAS,CADT,wDAA2D,CAE3D,eACF,CAEA,oBACE,eAAiB,CACjB,wBAAyB,CACzB,kBAAmB,CAInB,8BAAyC,CAFzC,cAAe,CAIf,eAAgB,CALhB,YAAa,CAIb,iBAAkB,CAFlB,uBAIF,CAEA,0BAGE,yBAA0B,CAD1B,gCAA2C,CAD3C,0BAGF,CAEA,sCAEE,SAAU,CADV,uBAEF,CAEA,WAGE,YAAa,CAFb,cAAe,CAGf,sBAAuB,CAFvB,kBAGF,CAEA,uBAGE,aAAc,CAFd,gBAAiB,CACjB,eAAgB,CAEhB,mBACF,CAEA,sBACE,aAAc,CACd,cAAe,CACf,eAAgB,CAChB,oBACF,CAEA,YAEE,kBAAmB,CAGnB,kBAAmB,CAGnB,iBAAkB,CAFlB,UAAY,CALZ,YAAa,CAQb,eAAgB,CALhB,SAAW,CADX,sBAAuB,CAUvB,aAAc,CAFd,SAAU,CAJV,qBAAuB,CAGvB,0BAA2B,CAE3B,uBAAyB,CAEzB,yBAAkB,CAAlB,iBACF,CAEA,oBAEE,aAAc,CADd,eAEF,CAEA,aAEE,kBAAmB,CAInB,+BAAgC,CALhC,YAAa,CAEb,QAAS,CACT,kBAAmB,CACnB,mBAEF,CAEA,aACE,kBAAmB,CAEnB,WAAY,CAEZ,iBAAkB,CAHlB,UAAY,CAIZ,cAAe,CACf,eAAiB,CAHjB,kBAAoB,CAIpB,oCACF,CAEA,mBACE,kBACF,CAEA,gBAGE,aAAc,CAFd,gBAAiB,CACjB,eAAgB,CAEhB,QACF,CAGA,yBACE,uBACE,kBACF,CAEA,qBAEE,UAAW,CADX,yBAEF,CAEA,oBACE,cACF,CAEA,mBACE,cACF,CAEA,aAEE,sBAAuB,CADvB,qBAAsB,CAEtB,SACF,CAEA,gBACE,gBACF,CACF,CAEA,yBACE,oBACE,YACF,CAEA,WACE,gBACF,CAEA,uBACE,gBACF,CAEA,mBACE,gBACF,CACF,CAGA,qBACE,iBACF,CAEA,eACE,YAAa,CACb,qBAAsB,CACtB,QAAS,CACT,eACF,CAEA,cACE,eAAiB,CACjB,kBAAmB,CAEnB,+BAAyC,CADzC,YAAa,CAEb,eACF,CAEA,iBAEE,kBAAmB,CAEnB,aAAc,CAHd,YAAa,CAIb,cAAe,CACf,eAAgB,CAHhB,QAAS,CAIT,iBACF,CAEA,cAEE,aAAc,CADd,cAEF,CAEA,gBACE,aAAc,CACd,cAAe,CACf,kBACF,CAEA,yBAGE,aAAS,CAFT,YAAa,CAEb,QAAS,CADT,wDAEF,CAEA,wBACE,kBAAmB,CACnB,wBAAyB,CACzB,kBAAmB,CAEnB,cAAe,CAIf,eAAgB,CALhB,YAAa,CAIb,iBAAkB,CADlB,iBAAkB,CADlB,uBAIF,CAEA,8BAGE,yBAA0B,CAD1B,+BAA0C,CAD1C,0BAGF,CAEA,2BACE,aAAc,CACd,cAAe,CACf,eAAgB,CAChB,iBACF,CAEA,0BACE,aAAc,CACd,cAAe,CAEf,eAAgB,CADhB,kBAEF,CAEA,eAEE,kBAAmB,CAKnB,iBAAkB,CAFlB,UAAY,CAJZ,YAAa,CAQb,cAAe,CADf,eAAgB,CAJhB,OAAQ,CADR,sBAAuB,CAOvB,eAAgB,CAJhB,iBAKF,CAGA,iDAGE,aAAc,CADd,gBAAiB,CAEjB,YACF,CAGA,yBACE,eACE,QACF,CAEA,cACE,YACF,CAEA,iBAEE,qBAAsB,CADtB,cAAe,CAEf,OAAQ,CACR,iBACF,CAEA,yBACE,yBACF,CAEA,wBACE,YACF,CACF", "sources": ["pages/admin/StudyMaterials/AddStudyMaterialForm.css", "pages/admin/StudyMaterials/SubtitleManager.css", "pages/admin/StudyMaterials/StudyMaterialManager.css", "pages/admin/StudyMaterials/EditStudyMaterialForm.css", "pages/admin/StudyMaterials/index.css"], "sourcesContent": [".add-material-form {\n  max-width: 100%;\n}\n\n.form-card {\n  background: white;\n  border-radius: 12px;\n  padding: 2rem;\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\n  border: 1px solid #e8e8e8;\n}\n\n.form-header-icon {\n  text-align: center;\n  margin-bottom: 2rem;\n  padding-bottom: 1rem;\n  border-bottom: 2px solid #f0f0f0;\n}\n\n.form-header-icon svg {\n  font-size: 3rem;\n  color: #3498db;\n  margin-bottom: 0.5rem;\n}\n\n.form-header-icon h3 {\n  font-size: 1.5rem;\n  font-weight: 600;\n  color: #2c3e50;\n  margin: 0;\n}\n\n.material-form {\n  max-width: 100%;\n}\n\n.form-row {\n  display: grid;\n  grid-template-columns: 1fr 1fr;\n  gap: 1rem;\n}\n\n.form-item-half {\n  margin-bottom: 1rem;\n}\n\n.form-input {\n  width: 100%;\n  padding: 0.75rem;\n  border: 2px solid #e8e8e8;\n  border-radius: 8px;\n  font-size: 1rem;\n  transition: border-color 0.3s ease;\n  background: white;\n}\n\n.form-input:focus {\n  outline: none;\n  border-color: #3498db;\n  box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);\n}\n\n.form-input::placeholder {\n  color: #95a5a6;\n}\n\n/* Ant Design Form Item Styling */\n.ant-form-item-label > label {\n  font-weight: 600;\n  color: #2c3e50;\n  font-size: 1rem;\n}\n\n.ant-select-selector {\n  border: 2px solid #e8e8e8 !important;\n  border-radius: 8px !important;\n  padding: 0.25rem 0.5rem !important;\n  height: auto !important;\n  min-height: 48px !important;\n}\n\n.ant-select-focused .ant-select-selector {\n  border-color: #3498db !important;\n  box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1) !important;\n}\n\n.ant-select-selection-item {\n  line-height: 32px !important;\n  font-size: 1rem !important;\n}\n\n.ant-select-selection-placeholder {\n  line-height: 32px !important;\n  color: #95a5a6 !important;\n  font-size: 1rem !important;\n}\n\n/* Upload Method Selector */\n.upload-method-section {\n  margin-bottom: 2rem;\n}\n\n.upload-method-selector {\n  display: grid;\n  grid-template-columns: 1fr 1fr 1fr;\n  gap: 1rem;\n  margin-top: 0.5rem;\n}\n\n.method-option {\n  border: 2px solid #e8e8e8;\n  border-radius: 12px;\n  padding: 1.5rem;\n  text-align: center;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  background: white;\n}\n\n.method-option:hover {\n  border-color: #3498db;\n  background: #f8fafc;\n}\n\n.method-option.active {\n  border-color: #3498db;\n  background: #f0f8ff;\n  box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);\n}\n\n.method-icon {\n  font-size: 2rem;\n  color: #3498db;\n  margin-bottom: 0.5rem;\n}\n\n.method-option span {\n  display: block;\n  font-weight: 600;\n  color: #2c3e50;\n  margin-bottom: 0.25rem;\n  font-size: 1rem;\n}\n\n.method-option p {\n  font-size: 0.875rem;\n  color: #7f8c8d;\n  margin: 0;\n}\n\n/* Upload Section Styling */\n.upload-section {\n  margin-bottom: 1.5rem;\n}\n\n.document-upload .ant-upload-list,\n.thumbnail-upload .ant-upload-list,\n.video-upload .ant-upload-list {\n  margin-top: 1rem;\n}\n\n.upload-area {\n  border: 2px dashed #d1d5db;\n  border-radius: 12px;\n  padding: 2rem;\n  text-align: center;\n  background: #f8fafc;\n  transition: all 0.3s ease;\n  cursor: pointer;\n}\n\n.upload-area:hover {\n  border-color: #3498db;\n  background: #f0f8ff;\n}\n\n.upload-area.small {\n  padding: 1.5rem;\n}\n\n/* Enhanced drag-and-drop styles for thumbnail */\n.thumbnail-drop-zone {\n  position: relative;\n  transition: all 0.3s ease;\n}\n\n.thumbnail-drop-zone:hover {\n  border-color: #3498db;\n  background: linear-gradient(135deg, #f0f8ff 0%, #e6f3ff 100%);\n  transform: translateY(-2px);\n  box-shadow: 0 4px 12px rgba(52, 152, 219, 0.15);\n}\n\n.thumbnail-drop-zone.drag-over {\n  border-color: #2ecc71;\n  background: linear-gradient(135deg, #f0fff4 0%, #e6ffed 100%);\n  border-style: solid;\n  animation: pulse 1s infinite;\n}\n\n@keyframes pulse {\n  0% { transform: scale(1); }\n  50% { transform: scale(1.02); }\n  100% { transform: scale(1); }\n}\n\n.upload-icon {\n  font-size: 2.5rem;\n  color: #3498db;\n  margin-bottom: 0.5rem;\n  transition: all 0.3s ease;\n}\n\n.thumbnail-drop-zone:hover .upload-icon {\n  color: #2980b9;\n  transform: scale(1.1);\n}\n\n.upload-area p {\n  margin: 0.25rem 0;\n  color: #374151;\n  font-weight: 500;\n}\n\n.upload-hint {\n  font-size: 0.875rem;\n  color: #6b7280 !important;\n  font-weight: 400 !important;\n}\n\n/* Upload Progress Section */\n.upload-progress-section {\n  margin: 2rem 0;\n  padding: 1.5rem;\n  background: #f8fafc;\n  border: 2px solid #e2e8f0;\n  border-radius: 12px;\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\n}\n\n.progress-header {\n  margin-bottom: 1rem;\n}\n\n.progress-info {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 0.5rem;\n}\n\n.upload-stats {\n  display: flex;\n  gap: 1rem;\n  font-size: 0.85rem;\n  color: #6c757d;\n  margin-top: 0.5rem;\n}\n\n.upload-speed,\n.estimated-time {\n  display: flex;\n  align-items: center;\n  gap: 0.25rem;\n}\n\n.progress-text {\n  font-weight: 600;\n  color: #2c3e50;\n  font-size: 1rem;\n}\n\n.progress-percentage {\n  font-weight: 700;\n  color: #3498db;\n  font-size: 1.1rem;\n}\n\n.progress-bar {\n  width: 100%;\n  height: 8px;\n  background: #e2e8f0;\n  border-radius: 4px;\n  overflow: hidden;\n  margin-bottom: 1rem;\n}\n\n.progress-fill {\n  height: 100%;\n  background: linear-gradient(90deg, #3498db, #2980b9);\n  border-radius: 4px;\n  transition: width 0.3s ease;\n  position: relative;\n}\n\n.progress-fill::after {\n  content: '';\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);\n  animation: shimmer 2s infinite;\n}\n\n@keyframes shimmer {\n  0% { transform: translateX(-100%); }\n  100% { transform: translateX(100%); }\n}\n\n.progress-details {\n  text-align: center;\n  margin-top: 1rem;\n}\n\n.uploading-info,\n.processing-info {\n  padding: 0.75rem;\n  border-radius: 8px;\n  font-weight: 500;\n}\n\n.uploading-info {\n  background: #e3f2fd;\n  border: 1px solid #bbdefb;\n  color: #1976d2;\n}\n\n.processing-info {\n  background: #e8f5e8;\n  border: 1px solid #c8e6c9;\n  color: #388e3c;\n}\n\n.uploading-info span,\n.processing-info span {\n  display: block;\n  margin-bottom: 0.25rem;\n  font-weight: 600;\n}\n\n.uploading-info small,\n.processing-info small {\n  color: #666;\n  font-size: 0.8rem;\n  font-weight: 400;\n}\n\n/* Form Actions */\n.form-actions {\n  display: flex;\n  gap: 1rem;\n  justify-content: flex-end;\n  margin-top: 2rem;\n  padding-top: 1.5rem;\n  border-top: 2px solid #f0f0f0;\n}\n\n.cancel-btn {\n  background: #95a5a6 !important;\n  border-color: #95a5a6 !important;\n  color: white !important;\n  font-weight: 500 !important;\n  height: 48px !important;\n  padding: 0 2rem !important;\n  border-radius: 8px !important;\n}\n\n.cancel-btn:hover {\n  background: #7f8c8d !important;\n  border-color: #7f8c8d !important;\n}\n\n.submit-btn {\n  background: #3498db !important;\n  border-color: #3498db !important;\n  font-weight: 500 !important;\n  height: 48px !important;\n  padding: 0 2rem !important;\n  border-radius: 8px !important;\n}\n\n.submit-btn:hover {\n  background: #2980b9 !important;\n  border-color: #2980b9 !important;\n}\n\n/* File Upload List Styling */\n.ant-upload-list-item {\n  border-radius: 8px !important;\n  border: 1px solid #e8e8e8 !important;\n  background: #f8fafc !important;\n}\n\n.ant-upload-list-item-name {\n  color: #2c3e50 !important;\n  font-weight: 500 !important;\n}\n\n/* Responsive Design */\n@media (max-width: 1024px) {\n  .upload-method-selector {\n    grid-template-columns: 1fr 1fr;\n    gap: 0.75rem;\n  }\n}\n\n@media (max-width: 768px) {\n  .form-card {\n    padding: 1.5rem;\n    margin: 0 0.5rem;\n  }\n  \n  .form-row {\n    grid-template-columns: 1fr;\n    gap: 0;\n  }\n  \n  .form-actions {\n    flex-direction: column;\n    gap: 0.75rem;\n  }\n  \n  .cancel-btn,\n  .submit-btn {\n    width: 100%;\n  }\n  \n  .upload-area {\n    padding: 1.5rem;\n  }\n  \n  .upload-icon {\n    font-size: 2rem;\n  }\n}\n\n@media (max-width: 480px) {\n  .form-card {\n    padding: 1rem;\n  }\n  \n  .form-header-icon svg {\n    font-size: 2.5rem;\n  }\n  \n  .form-header-icon h3 {\n    font-size: 1.3rem;\n  }\n  \n  .upload-area {\n    padding: 1rem;\n  }\n  \n  .upload-area p {\n    font-size: 0.9rem;\n  }\n\n  .upload-method-selector {\n    grid-template-columns: 1fr;\n    gap: 0.75rem;\n  }\n\n  .method-option {\n    padding: 1rem;\n  }\n\n  .method-icon {\n    font-size: 1.5rem;\n  }\n\n  .upload-progress-section {\n    margin: 1.5rem 0;\n    padding: 1rem;\n  }\n\n  .progress-info {\n    flex-direction: column;\n    align-items: flex-start;\n    gap: 0.5rem;\n  }\n\n  .processing-info {\n    padding: 0.5rem;\n    font-size: 0.9rem;\n  }\n}\n\n/* Additional Classes Section */\n.additional-classes-section {\n  margin-bottom: 1.5rem;\n}\n\n.additional-classes-info {\n  margin-bottom: 0.75rem;\n}\n\n.additional-classes-info p {\n  margin: 0;\n  color: #666;\n  font-size: 0.9rem;\n  line-height: 1.4;\n}\n\n.additional-classes-note {\n  margin-top: 0.5rem;\n}\n\n.additional-classes-note small {\n  color: #888;\n  font-style: italic;\n  display: block;\n  margin-top: 0.25rem;\n}\n\n/* Multi-select styling */\n.ant-select-multiple .ant-select-selection-item {\n  background: #e3f2fd;\n  border: 1px solid #2196f3;\n  color: #1976d2;\n  border-radius: 4px;\n  font-size: 0.85rem;\n}\n\n.ant-select-multiple .ant-select-selection-item-remove {\n  color: #1976d2;\n}\n\n.ant-select-multiple .ant-select-selection-item-remove:hover {\n  color: #d32f2f;\n}\n", ".subtitle-manager {\n  padding: 24px;\n  background: #fff;\n  border-radius: 8px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n}\n\n.subtitle-manager-header {\n  margin-bottom: 24px;\n  padding-bottom: 16px;\n  border-bottom: 1px solid #f0f0f0;\n}\n\n.subtitle-manager-header h2 {\n  margin: 0 0 8px 0;\n  color: #1890ff;\n  font-size: 24px;\n  font-weight: 600;\n}\n\n.subtitle-manager-header p {\n  margin: 0;\n  color: #666;\n  font-size: 14px;\n}\n\n.subtitle-status-cell {\n  display: flex;\n  flex-direction: column;\n  gap: 4px;\n}\n\n.subtitle-languages {\n  display: flex;\n  flex-wrap: wrap;\n  gap: 4px;\n  margin-top: 4px;\n}\n\n.video-info {\n  display: flex;\n  flex-direction: column;\n}\n\n.video-title {\n  font-weight: 600;\n  color: #262626;\n  margin-bottom: 4px;\n}\n\n.video-meta {\n  font-size: 12px;\n  color: #8c8c8c;\n}\n\n.actions-cell {\n  display: flex;\n  gap: 8px;\n  flex-wrap: wrap;\n}\n\n.generate-btn {\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  border: none;\n  color: white;\n}\n\n.generate-btn:hover {\n  background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);\n  color: white;\n}\n\n.upload-btn {\n  background: #52c41a;\n  border-color: #52c41a;\n  color: white;\n}\n\n.upload-btn:hover {\n  background: #73d13d;\n  border-color: #73d13d;\n  color: white;\n}\n\n.refresh-btn {\n  background: #fa8c16;\n  border-color: #fa8c16;\n  color: white;\n}\n\n.refresh-btn:hover {\n  background: #ffa940;\n  border-color: #ffa940;\n  color: white;\n}\n\n.subtitle-upload-modal .ant-modal-body {\n  padding: 24px;\n}\n\n.subtitle-upload-modal .ant-upload {\n  width: 100%;\n}\n\n.subtitle-upload-modal .ant-upload-list {\n  margin-top: 8px;\n}\n\n.language-select {\n  margin-bottom: 16px;\n}\n\n.language-select label {\n  display: block;\n  margin-bottom: 8px;\n  font-weight: 500;\n  color: #262626;\n}\n\n.file-upload-section {\n  margin-top: 16px;\n}\n\n.file-upload-section label {\n  display: block;\n  margin-bottom: 8px;\n  font-weight: 500;\n  color: #262626;\n}\n\n.video-selection {\n  background: #f5f5f5;\n  padding: 12px;\n  border-radius: 6px;\n  margin-bottom: 16px;\n}\n\n.video-selection strong {\n  color: #1890ff;\n}\n\n/* Status indicators */\n.status-generating {\n  color: #1890ff;\n  animation: pulse 1.5s infinite;\n}\n\n.status-completed {\n  color: #52c41a;\n}\n\n.status-failed {\n  color: #ff4d4f;\n}\n\n.status-none {\n  color: #8c8c8c;\n}\n\n@keyframes pulse {\n  0% {\n    opacity: 1;\n  }\n  50% {\n    opacity: 0.5;\n  }\n  100% {\n    opacity: 1;\n  }\n}\n\n/* Responsive design */\n@media (max-width: 768px) {\n  .subtitle-manager {\n    padding: 16px;\n  }\n  \n  .actions-cell {\n    flex-direction: column;\n  }\n  \n  .actions-cell .ant-btn {\n    width: 100%;\n  }\n}\n\n/* Table customizations */\n.subtitle-manager .ant-table-thead > tr > th {\n  background: #fafafa;\n  font-weight: 600;\n  color: #262626;\n}\n\n.subtitle-manager .ant-table-tbody > tr:hover > td {\n  background: #f5f5f5;\n}\n\n.subtitle-manager .ant-tag {\n  margin: 2px;\n  border-radius: 4px;\n}\n\n/* Progress indicator for generation */\n.generation-progress {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  margin-top: 4px;\n}\n\n.generation-progress .ant-progress {\n  flex: 1;\n}\n\n/* Subtitle info display */\n.subtitle-info-display {\n  display: flex;\n  flex-direction: column;\n  gap: 8px;\n}\n\n.subtitle-item {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  padding: 8px 12px;\n  background: #f9f9f9;\n  border-radius: 4px;\n  border-left: 3px solid #1890ff;\n}\n\n.subtitle-item-info {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n}\n\n.subtitle-item-actions {\n  display: flex;\n  gap: 4px;\n}\n\n.auto-generated-badge {\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: white;\n  font-size: 10px;\n  padding: 2px 6px;\n  border-radius: 10px;\n}\n\n.custom-uploaded-badge {\n  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);\n  color: white;\n  font-size: 10px;\n  padding: 2px 6px;\n  border-radius: 10px;\n}\n", ".study-material-manager {\n  padding: 20px;\n  background: #f8f9fa;\n  min-height: 100vh;\n}\n\n.manager-header {\n  margin-bottom: 30px;\n  text-align: center;\n}\n\n.manager-header h2 {\n  color: #2c3e50;\n  font-size: 28px;\n  font-weight: 600;\n  margin-bottom: 8px;\n}\n\n.manager-header p {\n  color: #7f8c8d;\n  font-size: 16px;\n  margin: 0;\n}\n\n/* Filters Section */\n.filters-section {\n  background: white;\n  padding: 20px;\n  border-radius: 12px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n  margin-bottom: 20px;\n}\n\n.filters-row {\n  display: flex;\n  flex-wrap: wrap;\n  gap: 20px;\n  align-items: end;\n}\n\n.filter-group {\n  display: flex;\n  flex-direction: column;\n  gap: 5px;\n}\n\n.filter-group label {\n  font-weight: 500;\n  color: #2c3e50;\n  font-size: 14px;\n}\n\n/* Materials Table */\n.materials-table {\n  background: white;\n  border-radius: 12px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n  overflow: hidden;\n}\n\n.materials-table .ant-table {\n  border-radius: 12px;\n}\n\n.materials-table .ant-table-thead > tr > th {\n  background: #f8f9fa;\n  border-bottom: 2px solid #e9ecef;\n  font-weight: 600;\n  color: #2c3e50;\n}\n\n.materials-table .ant-table-tbody > tr:hover > td {\n  background: #f8f9fa;\n}\n\n/* Material Info */\n.material-info {\n  display: flex;\n  align-items: center;\n}\n\n.material-header {\n  display: flex;\n  align-items: center;\n  gap: 12px;\n  width: 100%;\n}\n\n.material-icon {\n  font-size: 20px;\n  padding: 8px;\n  border-radius: 8px;\n  background: #f8f9fa;\n}\n\n.material-icon.video {\n  color: #e74c3c;\n  background: #fdf2f2;\n}\n\n.material-icon.note {\n  color: #3498db;\n  background: #f2f8fd;\n}\n\n.material-icon.paper {\n  color: #9b59b6;\n  background: #f8f4fd;\n}\n\n.material-icon.book {\n  color: #27ae60;\n  background: #f2fdf6;\n}\n\n.material-details {\n  flex: 1;\n}\n\n.material-title {\n  font-weight: 600;\n  color: #2c3e50;\n  font-size: 16px;\n  margin-bottom: 4px;\n  line-height: 1.3;\n}\n\n.material-meta {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  flex-wrap: wrap;\n}\n\n.meta-text {\n  color: #7f8c8d;\n  font-size: 13px;\n}\n\n/* Class Badge */\n.class-badge {\n  background: #e3f2fd;\n  color: #1976d2;\n  padding: 4px 8px;\n  border-radius: 6px;\n  font-size: 12px;\n  font-weight: 500;\n}\n\n/* Action Buttons */\n.materials-table .ant-btn {\n  border-radius: 6px;\n  font-weight: 500;\n  display: flex;\n  align-items: center;\n  gap: 4px;\n}\n\n.materials-table .ant-btn-primary {\n  background: #3498db;\n  border-color: #3498db;\n}\n\n.materials-table .ant-btn-primary:hover {\n  background: #2980b9;\n  border-color: #2980b9;\n}\n\n.materials-table .ant-btn-dangerous {\n  background: #e74c3c;\n  border-color: #e74c3c;\n  color: white;\n}\n\n.materials-table .ant-btn-dangerous:hover {\n  background: #c0392b;\n  border-color: #c0392b;\n}\n\n/* Tags */\n.ant-tag {\n  border-radius: 6px;\n  font-weight: 500;\n  font-size: 11px;\n  padding: 2px 8px;\n}\n\n/* Responsive Design */\n@media (max-width: 768px) {\n  .study-material-manager {\n    padding: 15px;\n  }\n\n  .filters-row {\n    flex-direction: column;\n    gap: 15px;\n  }\n\n  .filter-group {\n    width: 100%;\n  }\n\n  .filter-group .ant-select,\n  .filter-group .ant-input {\n    width: 100% !important;\n  }\n\n  .material-header {\n    flex-direction: column;\n    align-items: flex-start;\n    gap: 8px;\n  }\n\n  .material-meta {\n    flex-direction: column;\n    align-items: flex-start;\n    gap: 4px;\n  }\n\n  .materials-table .ant-table {\n    font-size: 12px;\n  }\n\n  .material-title {\n    font-size: 14px;\n  }\n}\n\n/* Loading States */\n.ant-table-placeholder {\n  padding: 40px;\n  text-align: center;\n  color: #7f8c8d;\n}\n\n.ant-spin-container {\n  min-height: 200px;\n}\n\n/* Empty State */\n.ant-empty {\n  padding: 40px;\n}\n\n.ant-empty-description {\n  color: #7f8c8d;\n  font-size: 14px;\n}\n\n/* Pagination */\n.ant-pagination {\n  margin-top: 20px;\n  text-align: center;\n}\n\n.ant-pagination-total-text {\n  color: #7f8c8d;\n  font-size: 14px;\n}\n\n/* Modal Styles */\n.ant-modal-confirm-title {\n  color: #2c3e50;\n  font-weight: 600;\n}\n\n.ant-modal-confirm-content {\n  color: #7f8c8d;\n  margin-top: 8px;\n}\n\n/* Search Input */\n.ant-input-search {\n  border-radius: 6px;\n}\n\n.ant-input-search .ant-input {\n  border-radius: 6px 0 0 6px;\n}\n\n.ant-input-search .ant-input-search-button {\n  border-radius: 0 6px 6px 0;\n}\n\n/* Select Dropdown */\n.ant-select {\n  border-radius: 6px;\n}\n\n.ant-select-selector {\n  border-radius: 6px !important;\n}\n\n/* Tooltip */\n.ant-tooltip-inner {\n  background: #2c3e50;\n  border-radius: 6px;\n}\n\n.ant-tooltip-arrow-content {\n  background: #2c3e50;\n}\n", ".edit-study-material-form {\n  background: white;\n  border-radius: 12px;\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\n  overflow: hidden;\n}\n\n.form-header {\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  padding: 30px;\n  color: white;\n}\n\n.header-content {\n  display: flex;\n  align-items: center;\n  gap: 15px;\n}\n\n.form-icon {\n  font-size: 32px;\n  padding: 12px;\n  background: rgba(255, 255, 255, 0.2);\n  border-radius: 12px;\n}\n\n.form-header h2 {\n  color: white;\n  font-size: 24px;\n  font-weight: 600;\n  margin: 0;\n}\n\n.form-header p {\n  color: rgba(255, 255, 255, 0.9);\n  margin: 4px 0 0 0;\n  font-size: 14px;\n}\n\n/* Form Styles */\n.material-form {\n  padding: 30px;\n}\n\n.form-row {\n  display: grid;\n  grid-template-columns: 1fr 1fr;\n  gap: 20px;\n}\n\n.form-item-half {\n  margin-bottom: 20px;\n}\n\n.ant-form-item-label > label {\n  font-weight: 600;\n  color: #2c3e50;\n  font-size: 14px;\n}\n\n.form-input {\n  width: 100%;\n  padding: 12px 16px;\n  border: 2px solid #e9ecef;\n  border-radius: 8px;\n  font-size: 14px;\n  transition: all 0.3s ease;\n  background: #f8f9fa;\n}\n\n.form-input:focus {\n  outline: none;\n  border-color: #3498db;\n  background: white;\n  box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);\n}\n\n/* Upload Method Section */\n.upload-method-section {\n  margin-bottom: 25px;\n}\n\n.section-label {\n  display: block;\n  font-weight: 600;\n  color: #2c3e50;\n  font-size: 14px;\n  margin-bottom: 12px;\n}\n\n.upload-method-options {\n  display: grid;\n  grid-template-columns: 1fr 1fr;\n  gap: 15px;\n}\n\n.method-option {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  padding: 20px;\n  border: 2px solid #e9ecef;\n  border-radius: 12px;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  background: #f8f9fa;\n}\n\n.method-option:hover {\n  border-color: #3498db;\n  background: #f0f8ff;\n}\n\n.method-option.active {\n  border-color: #3498db;\n  background: #e3f2fd;\n  color: #1976d2;\n}\n\n.method-icon {\n  font-size: 24px;\n  margin-bottom: 8px;\n  color: #7f8c8d;\n}\n\n.method-option.active .method-icon {\n  color: #1976d2;\n}\n\n.method-option span {\n  font-weight: 500;\n  font-size: 14px;\n}\n\n/* Additional Classes Section */\n.additional-classes-section .ant-form-item-label {\n  margin-bottom: 8px;\n}\n\n.additional-classes-note {\n  margin-top: 8px;\n}\n\n.additional-classes-note small {\n  color: #7f8c8d;\n  font-style: italic;\n}\n\n/* Upload Areas */\n.upload-section {\n  margin-bottom: 25px;\n}\n\n.upload-area {\n  border: 2px dashed #d1ecf1;\n  border-radius: 12px;\n  padding: 40px 20px;\n  text-align: center;\n  background: #f8fdff;\n  transition: all 0.3s ease;\n  cursor: pointer;\n}\n\n.upload-area:hover {\n  border-color: #3498db;\n  background: #f0f8ff;\n}\n\n.upload-area.small {\n  padding: 30px 20px;\n}\n\n.upload-icon {\n  font-size: 32px;\n  color: #3498db;\n  margin-bottom: 12px;\n}\n\n.upload-area p {\n  margin: 8px 0 4px 0;\n  color: #2c3e50;\n  font-weight: 500;\n}\n\n.upload-hint {\n  color: #7f8c8d !important;\n  font-size: 12px !important;\n  font-weight: normal !important;\n  margin: 4px 0 0 0 !important;\n}\n\n/* Form Actions */\n.form-actions {\n  display: flex;\n  justify-content: flex-end;\n  gap: 15px;\n  margin-top: 30px;\n  padding-top: 20px;\n  border-top: 1px solid #e9ecef;\n}\n\n.cancel-button {\n  padding: 12px 24px;\n  height: auto;\n  border-radius: 8px;\n  font-weight: 500;\n  border: 2px solid #e9ecef;\n  color: #7f8c8d;\n  display: flex;\n  align-items: center;\n  gap: 8px;\n}\n\n.cancel-button:hover {\n  border-color: #bdc3c7;\n  color: #2c3e50;\n}\n\n.submit-button {\n  padding: 12px 24px;\n  height: auto;\n  border-radius: 8px;\n  font-weight: 500;\n  background: #3498db;\n  border-color: #3498db;\n  display: flex;\n  align-items: center;\n  gap: 8px;\n}\n\n.submit-button:hover {\n  background: #2980b9;\n  border-color: #2980b9;\n}\n\n/* Ant Design Overrides */\n.ant-select {\n  border-radius: 8px;\n}\n\n.ant-select-selector {\n  border-radius: 8px !important;\n  border: 2px solid #e9ecef !important;\n  background: #f8f9fa !important;\n  padding: 8px 12px !important;\n  min-height: 48px !important;\n}\n\n.ant-select-focused .ant-select-selector {\n  border-color: #3498db !important;\n  background: white !important;\n  box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1) !important;\n}\n\n.ant-upload {\n  width: 100%;\n}\n\n.ant-upload-list {\n  margin-top: 15px;\n}\n\n.ant-upload-list-item {\n  border-radius: 8px;\n  border: 1px solid #e9ecef;\n  background: #f8f9fa;\n}\n\n/* Responsive Design */\n@media (max-width: 768px) {\n  .edit-study-material-form {\n    margin: 10px;\n    border-radius: 8px;\n  }\n\n  .form-header {\n    padding: 20px;\n  }\n\n  .header-content {\n    flex-direction: column;\n    text-align: center;\n    gap: 10px;\n  }\n\n  .material-form {\n    padding: 20px;\n  }\n\n  .form-row {\n    grid-template-columns: 1fr;\n    gap: 15px;\n  }\n\n  .upload-method-options {\n    grid-template-columns: 1fr;\n  }\n\n  .form-actions {\n    flex-direction: column-reverse;\n    gap: 10px;\n  }\n\n  .cancel-button,\n  .submit-button {\n    width: 100%;\n    justify-content: center;\n  }\n\n  .upload-area {\n    padding: 30px 15px;\n  }\n\n  .upload-area.small {\n    padding: 25px 15px;\n  }\n}\n\n/* Loading States */\n.ant-btn-loading {\n  pointer-events: none;\n}\n\n/* Form Validation */\n.ant-form-item-has-error .ant-select-selector {\n  border-color: #e74c3c !important;\n}\n\n.ant-form-item-has-error .form-input {\n  border-color: #e74c3c;\n}\n\n.ant-form-item-explain-error {\n  color: #e74c3c;\n  font-size: 12px;\n  margin-top: 4px;\n}\n\n/* Success States */\n.ant-form-item-has-success .ant-select-selector {\n  border-color: #27ae60 !important;\n}\n\n.ant-form-item-has-success .form-input {\n  border-color: #27ae60;\n}\n\n/* Disabled States */\n.ant-select-disabled .ant-select-selector {\n  background: #f5f5f5 !important;\n  color: #bfbfbf !important;\n  cursor: not-allowed !important;\n}\n\n.form-input:disabled {\n  background: #f5f5f5;\n  color: #bfbfbf;\n  cursor: not-allowed;\n}\n\n/* Upload Drag States */\n.ant-upload-drag.ant-upload-drag-hover {\n  border-color: #3498db;\n}\n\n.ant-upload-drag {\n  border-radius: 12px;\n  background: #f8fdff;\n}\n\n/* Select Dropdown */\n.ant-select-dropdown {\n  border-radius: 8px;\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\n}\n\n.ant-select-item {\n  border-radius: 6px;\n  margin: 2px 8px;\n}\n\n.ant-select-item-option-selected {\n  background: #e3f2fd;\n  color: #1976d2;\n  font-weight: 500;\n}\n", ".admin-study-materials {\n  max-width: 1200px;\n  margin: 0 auto;\n  padding: 2rem 1rem;\n}\n\n.material-types-container {\n  text-align: center;\n}\n\n.header-section {\n  margin-bottom: 3rem;\n}\n\n.header-section h2 {\n  font-size: 2.5rem;\n  font-weight: 600;\n  color: #2c3e50;\n  margin-bottom: 0.5rem;\n}\n\n.header-section p {\n  font-size: 1.1rem;\n  color: #7f8c8d;\n  margin: 0;\n}\n\n.material-types-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));\n  gap: 2rem;\n  margin-top: 2rem;\n}\n\n.material-type-card {\n  background: white;\n  border: 3px solid #ecf0f1;\n  border-radius: 16px;\n  padding: 2rem;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);\n  position: relative;\n  overflow: hidden;\n}\n\n.material-type-card:hover {\n  transform: translateY(-8px);\n  box-shadow: 0 12px 24px rgba(0, 0, 0, 0.15);\n  border-color: currentColor;\n}\n\n.material-type-card:hover .add-button {\n  transform: translateY(0);\n  opacity: 1;\n}\n\n.card-icon {\n  font-size: 3rem;\n  margin-bottom: 1rem;\n  display: flex;\n  justify-content: center;\n}\n\n.material-type-card h3 {\n  font-size: 1.5rem;\n  font-weight: 600;\n  color: #2c3e50;\n  margin-bottom: 0.5rem;\n}\n\n.material-type-card p {\n  color: #7f8c8d;\n  font-size: 1rem;\n  line-height: 1.5;\n  margin-bottom: 1.5rem;\n}\n\n.add-button {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  gap: 0.5rem;\n  background: #3498db;\n  color: white;\n  padding: 0.75rem 1.5rem;\n  border-radius: 8px;\n  font-weight: 500;\n  transform: translateY(10px);\n  opacity: 0;\n  transition: all 0.3s ease;\n  margin: 0 auto;\n  width: fit-content;\n}\n\n.add-form-container {\n  max-width: 800px;\n  margin: 0 auto;\n}\n\n.form-header {\n  display: flex;\n  align-items: center;\n  gap: 1rem;\n  margin-bottom: 2rem;\n  padding-bottom: 1rem;\n  border-bottom: 2px solid #ecf0f1;\n}\n\n.back-button {\n  background: #95a5a6;\n  color: white;\n  border: none;\n  padding: 0.5rem 1rem;\n  border-radius: 6px;\n  cursor: pointer;\n  font-size: 0.9rem;\n  transition: background-color 0.2s ease;\n}\n\n.back-button:hover {\n  background: #7f8c8d;\n}\n\n.form-header h2 {\n  font-size: 1.8rem;\n  font-weight: 600;\n  color: #2c3e50;\n  margin: 0;\n}\n\n/* Responsive Design */\n@media (max-width: 768px) {\n  .admin-study-materials {\n    padding: 1rem 0.5rem;\n  }\n  \n  .material-types-grid {\n    grid-template-columns: 1fr;\n    gap: 1.5rem;\n  }\n  \n  .material-type-card {\n    padding: 1.5rem;\n  }\n  \n  .header-section h2 {\n    font-size: 2rem;\n  }\n  \n  .form-header {\n    flex-direction: column;\n    align-items: flex-start;\n    gap: 0.5rem;\n  }\n  \n  .form-header h2 {\n    font-size: 1.5rem;\n  }\n}\n\n@media (max-width: 480px) {\n  .material-type-card {\n    padding: 1rem;\n  }\n  \n  .card-icon {\n    font-size: 2.5rem;\n  }\n  \n  .material-type-card h3 {\n    font-size: 1.3rem;\n  }\n  \n  .header-section h2 {\n    font-size: 1.8rem;\n  }\n}\n\n/* Main Menu Styles */\n.main-menu-container {\n  text-align: center;\n}\n\n.menu-sections {\n  display: flex;\n  flex-direction: column;\n  gap: 40px;\n  margin-top: 2rem;\n}\n\n.menu-section {\n  background: white;\n  border-radius: 16px;\n  padding: 30px;\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\n  text-align: left;\n}\n\n.menu-section h3 {\n  display: flex;\n  align-items: center;\n  gap: 12px;\n  color: #2c3e50;\n  font-size: 22px;\n  font-weight: 600;\n  margin-bottom: 8px;\n}\n\n.section-icon {\n  font-size: 20px;\n  color: #3498db;\n}\n\n.menu-section > p {\n  color: #7f8c8d;\n  font-size: 16px;\n  margin-bottom: 25px;\n}\n\n.management-options-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));\n  gap: 20px;\n}\n\n.management-option-card {\n  background: #f8f9fa;\n  border: 2px solid #e9ecef;\n  border-radius: 12px;\n  padding: 25px;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  text-align: center;\n  position: relative;\n  overflow: hidden;\n}\n\n.management-option-card:hover {\n  transform: translateY(-2px);\n  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);\n  border-color: currentColor;\n}\n\n.management-option-card h4 {\n  color: #2c3e50;\n  font-size: 18px;\n  font-weight: 600;\n  margin: 15px 0 8px 0;\n}\n\n.management-option-card p {\n  color: #7f8c8d;\n  font-size: 14px;\n  margin-bottom: 20px;\n  line-height: 1.5;\n}\n\n.manage-button {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  gap: 8px;\n  color: white;\n  padding: 12px 20px;\n  border-radius: 8px;\n  font-weight: 500;\n  font-size: 14px;\n  margin-top: auto;\n}\n\n/* Container Styles */\n.material-manager-container,\n.edit-form-container {\n  max-width: 1200px;\n  margin: 0 auto;\n  padding: 20px;\n}\n\n/* Responsive adjustments for new components */\n@media (max-width: 768px) {\n  .menu-sections {\n    gap: 25px;\n  }\n\n  .menu-section {\n    padding: 20px;\n  }\n\n  .menu-section h3 {\n    font-size: 18px;\n    flex-direction: column;\n    gap: 8px;\n    text-align: center;\n  }\n\n  .management-options-grid {\n    grid-template-columns: 1fr;\n  }\n\n  .management-option-card {\n    padding: 20px;\n  }\n}\n"], "names": [], "sourceRoot": ""}