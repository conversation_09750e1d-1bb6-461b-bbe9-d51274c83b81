{"ast": null, "code": "import _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport classNames from 'classnames';\nimport React, { useRef } from 'react';\nimport useColorDrag from \"../hooks/useColorDrag\";\nimport { calculateColor, calculateOffset } from \"../util\";\nimport Palette from \"./Palette\";\nimport Gradient from \"./Gradient\";\nimport Handler from \"./Handler\";\nimport Transform from \"./Transform\";\nvar Slider = function Slider(_ref) {\n  var gradientColors = _ref.gradientColors,\n    direction = _ref.direction,\n    _ref$type = _ref.type,\n    type = _ref$type === void 0 ? 'hue' : _ref$type,\n    color = _ref.color,\n    value = _ref.value,\n    onChange = _ref.onChange,\n    onChangeComplete = _ref.onChangeComplete,\n    disabled = _ref.disabled,\n    prefixCls = _ref.prefixCls;\n  var sliderRef = useRef();\n  var transformRef = useRef();\n  var colorRef = useRef(color);\n  var _useColorDrag = useColorDrag({\n      color: color,\n      targetRef: transformRef,\n      containerRef: sliderRef,\n      calculate: function calculate(containerRef) {\n        return calculateOffset(containerRef, transformRef, color, type);\n      },\n      onDragChange: function onDragChange(offsetValue) {\n        var calcColor = calculateColor({\n          offset: offsetValue,\n          targetRef: transformRef,\n          containerRef: sliderRef,\n          color: color,\n          type: type\n        });\n        colorRef.current = calcColor;\n        onChange(calcColor);\n      },\n      onDragChangeComplete: function onDragChangeComplete() {\n        onChangeComplete === null || onChangeComplete === void 0 ? void 0 : onChangeComplete(colorRef.current, type);\n      },\n      direction: 'x',\n      disabledDrag: disabled\n    }),\n    _useColorDrag2 = _slicedToArray(_useColorDrag, 2),\n    offset = _useColorDrag2[0],\n    dragStartHandle = _useColorDrag2[1];\n  return /*#__PURE__*/React.createElement(\"div\", {\n    ref: sliderRef,\n    className: classNames(\"\".concat(prefixCls, \"-slider\"), \"\".concat(prefixCls, \"-slider-\").concat(type)),\n    onMouseDown: dragStartHandle,\n    onTouchStart: dragStartHandle\n  }, /*#__PURE__*/React.createElement(Palette, {\n    prefixCls: prefixCls\n  }, /*#__PURE__*/React.createElement(Transform, {\n    offset: offset,\n    ref: transformRef\n  }, /*#__PURE__*/React.createElement(Handler, {\n    size: \"small\",\n    color: value,\n    prefixCls: prefixCls\n  })), /*#__PURE__*/React.createElement(Gradient, {\n    colors: gradientColors,\n    direction: direction,\n    type: type,\n    prefixCls: prefixCls\n  })));\n};\nexport default Slider;", "map": {"version": 3, "names": ["_slicedToArray", "classNames", "React", "useRef", "useColorDrag", "calculateColor", "calculateOffset", "Palette", "Gradient", "Handler", "Transform", "Slide<PERSON>", "_ref", "gradientColors", "direction", "_ref$type", "type", "color", "value", "onChange", "onChangeComplete", "disabled", "prefixCls", "sliderRef", "transformRef", "colorRef", "_useColorDrag", "targetRef", "containerRef", "calculate", "onDragChange", "offsetValue", "calcColor", "offset", "current", "onDragChangeComplete", "disabledDrag", "_useColorDrag2", "dragStartHandle", "createElement", "ref", "className", "concat", "onMouseDown", "onTouchStart", "size", "colors"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/node_modules/@rc-component/color-picker/es/components/Slider.js"], "sourcesContent": ["import _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport classNames from 'classnames';\nimport React, { useRef } from 'react';\nimport useColorDrag from \"../hooks/useColorDrag\";\nimport { calculateColor, calculateOffset } from \"../util\";\nimport Palette from \"./Palette\";\nimport Gradient from \"./Gradient\";\nimport Handler from \"./Handler\";\nimport Transform from \"./Transform\";\nvar Slider = function Slider(_ref) {\n  var gradientColors = _ref.gradientColors,\n    direction = _ref.direction,\n    _ref$type = _ref.type,\n    type = _ref$type === void 0 ? 'hue' : _ref$type,\n    color = _ref.color,\n    value = _ref.value,\n    onChange = _ref.onChange,\n    onChangeComplete = _ref.onChangeComplete,\n    disabled = _ref.disabled,\n    prefixCls = _ref.prefixCls;\n  var sliderRef = useRef();\n  var transformRef = useRef();\n  var colorRef = useRef(color);\n  var _useColorDrag = useColorDrag({\n      color: color,\n      targetRef: transformRef,\n      containerRef: sliderRef,\n      calculate: function calculate(containerRef) {\n        return calculateOffset(containerRef, transformRef, color, type);\n      },\n      onDragChange: function onDragChange(offsetValue) {\n        var calcColor = calculateColor({\n          offset: offsetValue,\n          targetRef: transformRef,\n          containerRef: sliderRef,\n          color: color,\n          type: type\n        });\n        colorRef.current = calcColor;\n        onChange(calcColor);\n      },\n      onDragChangeComplete: function onDragChangeComplete() {\n        onChangeComplete === null || onChangeComplete === void 0 ? void 0 : onChangeComplete(colorRef.current, type);\n      },\n      direction: 'x',\n      disabledDrag: disabled\n    }),\n    _useColorDrag2 = _slicedToArray(_useColorDrag, 2),\n    offset = _useColorDrag2[0],\n    dragStartHandle = _useColorDrag2[1];\n  return /*#__PURE__*/React.createElement(\"div\", {\n    ref: sliderRef,\n    className: classNames(\"\".concat(prefixCls, \"-slider\"), \"\".concat(prefixCls, \"-slider-\").concat(type)),\n    onMouseDown: dragStartHandle,\n    onTouchStart: dragStartHandle\n  }, /*#__PURE__*/React.createElement(Palette, {\n    prefixCls: prefixCls\n  }, /*#__PURE__*/React.createElement(Transform, {\n    offset: offset,\n    ref: transformRef\n  }, /*#__PURE__*/React.createElement(Handler, {\n    size: \"small\",\n    color: value,\n    prefixCls: prefixCls\n  })), /*#__PURE__*/React.createElement(Gradient, {\n    colors: gradientColors,\n    direction: direction,\n    type: type,\n    prefixCls: prefixCls\n  })));\n};\nexport default Slider;"], "mappings": "AAAA,OAAOA,cAAc,MAAM,0CAA0C;AACrE,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAOC,KAAK,IAAIC,MAAM,QAAQ,OAAO;AACrC,OAAOC,YAAY,MAAM,uBAAuB;AAChD,SAASC,cAAc,EAAEC,eAAe,QAAQ,SAAS;AACzD,OAAOC,OAAO,MAAM,WAAW;AAC/B,OAAOC,QAAQ,MAAM,YAAY;AACjC,OAAOC,OAAO,MAAM,WAAW;AAC/B,OAAOC,SAAS,MAAM,aAAa;AACnC,IAAIC,MAAM,GAAG,SAASA,MAAMA,CAACC,IAAI,EAAE;EACjC,IAAIC,cAAc,GAAGD,IAAI,CAACC,cAAc;IACtCC,SAAS,GAAGF,IAAI,CAACE,SAAS;IAC1BC,SAAS,GAAGH,IAAI,CAACI,IAAI;IACrBA,IAAI,GAAGD,SAAS,KAAK,KAAK,CAAC,GAAG,KAAK,GAAGA,SAAS;IAC/CE,KAAK,GAAGL,IAAI,CAACK,KAAK;IAClBC,KAAK,GAAGN,IAAI,CAACM,KAAK;IAClBC,QAAQ,GAAGP,IAAI,CAACO,QAAQ;IACxBC,gBAAgB,GAAGR,IAAI,CAACQ,gBAAgB;IACxCC,QAAQ,GAAGT,IAAI,CAACS,QAAQ;IACxBC,SAAS,GAAGV,IAAI,CAACU,SAAS;EAC5B,IAAIC,SAAS,GAAGpB,MAAM,CAAC,CAAC;EACxB,IAAIqB,YAAY,GAAGrB,MAAM,CAAC,CAAC;EAC3B,IAAIsB,QAAQ,GAAGtB,MAAM,CAACc,KAAK,CAAC;EAC5B,IAAIS,aAAa,GAAGtB,YAAY,CAAC;MAC7Ba,KAAK,EAAEA,KAAK;MACZU,SAAS,EAAEH,YAAY;MACvBI,YAAY,EAAEL,SAAS;MACvBM,SAAS,EAAE,SAASA,SAASA,CAACD,YAAY,EAAE;QAC1C,OAAOtB,eAAe,CAACsB,YAAY,EAAEJ,YAAY,EAAEP,KAAK,EAAED,IAAI,CAAC;MACjE,CAAC;MACDc,YAAY,EAAE,SAASA,YAAYA,CAACC,WAAW,EAAE;QAC/C,IAAIC,SAAS,GAAG3B,cAAc,CAAC;UAC7B4B,MAAM,EAAEF,WAAW;UACnBJ,SAAS,EAAEH,YAAY;UACvBI,YAAY,EAAEL,SAAS;UACvBN,KAAK,EAAEA,KAAK;UACZD,IAAI,EAAEA;QACR,CAAC,CAAC;QACFS,QAAQ,CAACS,OAAO,GAAGF,SAAS;QAC5Bb,QAAQ,CAACa,SAAS,CAAC;MACrB,CAAC;MACDG,oBAAoB,EAAE,SAASA,oBAAoBA,CAAA,EAAG;QACpDf,gBAAgB,KAAK,IAAI,IAAIA,gBAAgB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,gBAAgB,CAACK,QAAQ,CAACS,OAAO,EAAElB,IAAI,CAAC;MAC9G,CAAC;MACDF,SAAS,EAAE,GAAG;MACdsB,YAAY,EAAEf;IAChB,CAAC,CAAC;IACFgB,cAAc,GAAGrC,cAAc,CAAC0B,aAAa,EAAE,CAAC,CAAC;IACjDO,MAAM,GAAGI,cAAc,CAAC,CAAC,CAAC;IAC1BC,eAAe,GAAGD,cAAc,CAAC,CAAC,CAAC;EACrC,OAAO,aAAanC,KAAK,CAACqC,aAAa,CAAC,KAAK,EAAE;IAC7CC,GAAG,EAAEjB,SAAS;IACdkB,SAAS,EAAExC,UAAU,CAAC,EAAE,CAACyC,MAAM,CAACpB,SAAS,EAAE,SAAS,CAAC,EAAE,EAAE,CAACoB,MAAM,CAACpB,SAAS,EAAE,UAAU,CAAC,CAACoB,MAAM,CAAC1B,IAAI,CAAC,CAAC;IACrG2B,WAAW,EAAEL,eAAe;IAC5BM,YAAY,EAAEN;EAChB,CAAC,EAAE,aAAapC,KAAK,CAACqC,aAAa,CAAChC,OAAO,EAAE;IAC3Ce,SAAS,EAAEA;EACb,CAAC,EAAE,aAAapB,KAAK,CAACqC,aAAa,CAAC7B,SAAS,EAAE;IAC7CuB,MAAM,EAAEA,MAAM;IACdO,GAAG,EAAEhB;EACP,CAAC,EAAE,aAAatB,KAAK,CAACqC,aAAa,CAAC9B,OAAO,EAAE;IAC3CoC,IAAI,EAAE,OAAO;IACb5B,KAAK,EAAEC,KAAK;IACZI,SAAS,EAAEA;EACb,CAAC,CAAC,CAAC,EAAE,aAAapB,KAAK,CAACqC,aAAa,CAAC/B,QAAQ,EAAE;IAC9CsC,MAAM,EAAEjC,cAAc;IACtBC,SAAS,EAAEA,SAAS;IACpBE,IAAI,EAAEA,IAAI;IACVM,SAAS,EAAEA;EACb,CAAC,CAAC,CAAC,CAAC;AACN,CAAC;AACD,eAAeX,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}