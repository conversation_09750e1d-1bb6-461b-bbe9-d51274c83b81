{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport Cell from \"../Cell\";\nimport TableContext from \"../context/TableContext\";\nimport { useContext } from '@rc-component/context';\nimport { getCellFixedInfo } from \"../utils/fixUtil\";\nimport SummaryContext from \"./SummaryContext\";\nexport default function SummaryCell(_ref) {\n  var className = _ref.className,\n    index = _ref.index,\n    children = _ref.children,\n    _ref$colSpan = _ref.colSpan,\n    colSpan = _ref$colSpan === void 0 ? 1 : _ref$colSpan,\n    rowSpan = _ref.rowSpan,\n    align = _ref.align;\n  var _useContext = useContext(TableContext, ['prefixCls', 'direction']),\n    prefixCls = _useContext.prefixCls,\n    direction = _useContext.direction;\n  var _React$useContext = React.useContext(SummaryContext),\n    scrollColumnIndex = _React$useContext.scrollColumnIndex,\n    stickyOffsets = _React$useContext.stickyOffsets,\n    flattenColumns = _React$useContext.flattenColumns,\n    columns = _React$useContext.columns;\n  var lastIndex = index + colSpan - 1;\n  var mergedColSpan = lastIndex + 1 === scrollColumnIndex ? colSpan + 1 : colSpan;\n  var fixedInfo = getCellFixedInfo(index, index + mergedColSpan - 1, flattenColumns, stickyOffsets, direction, columns === null || columns === void 0 ? void 0 : columns[index]);\n  return /*#__PURE__*/React.createElement(Cell, _extends({\n    className: className,\n    index: index,\n    component: \"td\",\n    prefixCls: prefixCls,\n    record: null,\n    dataIndex: null,\n    align: align,\n    colSpan: mergedColSpan,\n    rowSpan: rowSpan,\n    render: function render() {\n      return children;\n    }\n  }, fixedInfo));\n}", "map": {"version": 3, "names": ["_extends", "React", "Cell", "TableContext", "useContext", "getCellFixedInfo", "SummaryContext", "<PERSON><PERSON>ryCell", "_ref", "className", "index", "children", "_ref$colSpan", "colSpan", "rowSpan", "align", "_useContext", "prefixCls", "direction", "_React$useContext", "scrollColumnIndex", "stickyOffsets", "flattenColumns", "columns", "lastIndex", "mergedColSpan", "fixedInfo", "createElement", "component", "record", "dataIndex", "render"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/node_modules/rc-table/es/Footer/Cell.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport Cell from \"../Cell\";\nimport TableContext from \"../context/TableContext\";\nimport { useContext } from '@rc-component/context';\nimport { getCellFixedInfo } from \"../utils/fixUtil\";\nimport SummaryContext from \"./SummaryContext\";\nexport default function SummaryCell(_ref) {\n  var className = _ref.className,\n    index = _ref.index,\n    children = _ref.children,\n    _ref$colSpan = _ref.colSpan,\n    colSpan = _ref$colSpan === void 0 ? 1 : _ref$colSpan,\n    rowSpan = _ref.rowSpan,\n    align = _ref.align;\n  var _useContext = useContext(TableContext, ['prefixCls', 'direction']),\n    prefixCls = _useContext.prefixCls,\n    direction = _useContext.direction;\n  var _React$useContext = React.useContext(SummaryContext),\n    scrollColumnIndex = _React$useContext.scrollColumnIndex,\n    stickyOffsets = _React$useContext.stickyOffsets,\n    flattenColumns = _React$useContext.flattenColumns,\n    columns = _React$useContext.columns;\n  var lastIndex = index + colSpan - 1;\n  var mergedColSpan = lastIndex + 1 === scrollColumnIndex ? colSpan + 1 : colSpan;\n  var fixedInfo = getCellFixedInfo(index, index + mergedColSpan - 1, flattenColumns, stickyOffsets, direction, columns === null || columns === void 0 ? void 0 : columns[index]);\n  return /*#__PURE__*/React.createElement(Cell, _extends({\n    className: className,\n    index: index,\n    component: \"td\",\n    prefixCls: prefixCls,\n    record: null,\n    dataIndex: null,\n    align: align,\n    colSpan: mergedColSpan,\n    rowSpan: rowSpan,\n    render: function render() {\n      return children;\n    }\n  }, fixedInfo));\n}"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,IAAI,MAAM,SAAS;AAC1B,OAAOC,YAAY,MAAM,yBAAyB;AAClD,SAASC,UAAU,QAAQ,uBAAuB;AAClD,SAASC,gBAAgB,QAAQ,kBAAkB;AACnD,OAAOC,cAAc,MAAM,kBAAkB;AAC7C,eAAe,SAASC,WAAWA,CAACC,IAAI,EAAE;EACxC,IAAIC,SAAS,GAAGD,IAAI,CAACC,SAAS;IAC5BC,KAAK,GAAGF,IAAI,CAACE,KAAK;IAClBC,QAAQ,GAAGH,IAAI,CAACG,QAAQ;IACxBC,YAAY,GAAGJ,IAAI,CAACK,OAAO;IAC3BA,OAAO,GAAGD,YAAY,KAAK,KAAK,CAAC,GAAG,CAAC,GAAGA,YAAY;IACpDE,OAAO,GAAGN,IAAI,CAACM,OAAO;IACtBC,KAAK,GAAGP,IAAI,CAACO,KAAK;EACpB,IAAIC,WAAW,GAAGZ,UAAU,CAACD,YAAY,EAAE,CAAC,WAAW,EAAE,WAAW,CAAC,CAAC;IACpEc,SAAS,GAAGD,WAAW,CAACC,SAAS;IACjCC,SAAS,GAAGF,WAAW,CAACE,SAAS;EACnC,IAAIC,iBAAiB,GAAGlB,KAAK,CAACG,UAAU,CAACE,cAAc,CAAC;IACtDc,iBAAiB,GAAGD,iBAAiB,CAACC,iBAAiB;IACvDC,aAAa,GAAGF,iBAAiB,CAACE,aAAa;IAC/CC,cAAc,GAAGH,iBAAiB,CAACG,cAAc;IACjDC,OAAO,GAAGJ,iBAAiB,CAACI,OAAO;EACrC,IAAIC,SAAS,GAAGd,KAAK,GAAGG,OAAO,GAAG,CAAC;EACnC,IAAIY,aAAa,GAAGD,SAAS,GAAG,CAAC,KAAKJ,iBAAiB,GAAGP,OAAO,GAAG,CAAC,GAAGA,OAAO;EAC/E,IAAIa,SAAS,GAAGrB,gBAAgB,CAACK,KAAK,EAAEA,KAAK,GAAGe,aAAa,GAAG,CAAC,EAAEH,cAAc,EAAED,aAAa,EAAEH,SAAS,EAAEK,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACb,KAAK,CAAC,CAAC;EAC9K,OAAO,aAAaT,KAAK,CAAC0B,aAAa,CAACzB,IAAI,EAAEF,QAAQ,CAAC;IACrDS,SAAS,EAAEA,SAAS;IACpBC,KAAK,EAAEA,KAAK;IACZkB,SAAS,EAAE,IAAI;IACfX,SAAS,EAAEA,SAAS;IACpBY,MAAM,EAAE,IAAI;IACZC,SAAS,EAAE,IAAI;IACff,KAAK,EAAEA,KAAK;IACZF,OAAO,EAAEY,aAAa;IACtBX,OAAO,EAAEA,OAAO;IAChBiB,MAAM,EAAE,SAASA,MAAMA,CAAA,EAAG;MACxB,OAAOpB,QAAQ;IACjB;EACF,CAAC,EAAEe,SAAS,CAAC,CAAC;AAChB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}