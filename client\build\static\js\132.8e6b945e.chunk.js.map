{"version": 3, "file": "static/js/132.8e6b945e.chunk.js", "mappings": "gHAAA,MAAQA,QAASC,GAAkBC,EAAQ,MAE9BC,EAAWC,UACpB,IAEE,aADuBH,EAAcI,IAAI,aAAcC,IACvCC,IAClB,CAAE,MAAOC,GACP,OAAOA,EAAMC,SAASF,IACxB,E,kGCNJ,QAD0B,CAAE,KAAQ,CAAE,IAAO,MAAO,MAAS,CAAE,QAAW,gBAAiB,UAAa,SAAW,SAAY,CAAC,CAAE,IAAO,OAAQ,MAAS,CAAE,EAAK,+LAAkM,CAAE,IAAO,OAAQ,MAAS,CAAE,EAAK,oLAAwL,KAAQ,eAAgB,MAAS,Y,cCMzlBG,EAAsB,SAA6BC,EAAOC,GAC5D,OAAoBC,EAAAA,cAAoBC,EAAAA,GAAUC,EAAAA,EAAAA,GAAS,CAAC,EAAGJ,EAAO,CACpEC,IAAKA,EACLI,KAAMC,IAEV,EAIA,QAA4BJ,EAAAA,WAAiBH,G,+BCX7C,MAkLA,EAlLgCQ,IAMzB,IAADC,EAAA,IAN2B,QAC/BC,EAAO,QACPC,EAAO,YACPC,EAAW,aACXC,EAAY,KACZC,GACDN,EAEC,MAsBMO,EAtByBC,MAC7B,GAAiB,OAAZH,QAAY,IAAZA,IAAAA,EAAcI,QAAS,OAAO,EACnC,MAEMC,EAFU,IAAIC,KAAKN,EAAaI,SACxB,IAAIE,KAEZC,EAAWC,KAAKC,KAAKJ,EAAQ,OACnC,OAAOG,KAAKE,IAAI,EAAGH,EAAS,EAgBRJ,GAChBQ,EAboBC,MACxB,GAAiB,OAAZZ,QAAY,IAAZA,IAAAA,EAAca,WAA0B,OAAZb,QAAY,IAAZA,IAAAA,EAAcI,QAAS,OAAO,EAC/D,MAAMS,EAAY,IAAIP,KAAKN,EAAaa,WAIlCC,GAHU,IAAIR,KAAKN,EAAaI,SAGTS,GAAS,MAChCE,GAHQ,IAAIT,KAGQO,GAAS,MAEnC,OAAOL,KAAKQ,IAAI,IAAKR,KAAKE,IAAI,EAAIK,EAAWD,EAAa,KAAK,EAIhDF,GACXK,GAAuB,OAAXlB,QAAW,IAAXA,OAAW,EAAXA,EAAamB,SAAqB,OAAZlB,QAAY,IAAZA,OAAY,EAAZA,EAAciB,YAAa,eAC7Db,EAAsB,OAAZJ,QAAY,IAAZA,GAAAA,EAAcI,QAAU,IAAIE,KAAKN,EAAaI,SAASe,qBAAuB,MAE9F,OACEC,EAAAA,EAAAA,KAACC,EAAAA,EAAK,CACJC,KAAMzB,EACN0B,SAAUzB,EACV0B,OAAQ,KACRC,MAAO,IACPC,UAAQ,EACRC,UAAU,4BACVC,UAAW,CAAEC,gBAAiB,sBAAuBC,UAErDC,EAAAA,EAAAA,MAAA,OAAKJ,UAAU,8BAA6BG,SAAA,EAE1CC,EAAAA,EAAAA,MAAA,OAAKJ,UAAU,eAAcG,SAAA,EAC3BV,EAAAA,EAAAA,KAAA,OAAKO,UAAU,aAAYG,UACzBV,EAAAA,EAAAA,KAACY,EAAAA,EAAa,OAEhBZ,EAAAA,EAAAA,KAAA,MAAIO,UAAU,cAAaG,SAAC,6BAC5BV,EAAAA,EAAAA,KAAA,KAAGO,UAAU,iBAAgBG,SAAC,mDAIhCC,EAAAA,EAAAA,MAAA,OAAKJ,UAAU,oBAAmBG,SAAA,EAChCC,EAAAA,EAAAA,MAAA,OAAKJ,UAAU,cAAaG,SAAA,EAC1BV,EAAAA,EAAAA,KAAA,OAAKO,UAAU,YAAWG,UACxBV,EAAAA,EAAAA,KAACjC,EAAmB,OAEtB4C,EAAAA,EAAAA,MAAA,OAAKJ,UAAU,YAAWG,SAAA,EACxBV,EAAAA,EAAAA,KAAA,MAAIO,UAAU,YAAWG,SAAEb,KAC3BG,EAAAA,EAAAA,KAAA,QAAMO,UAAU,cAAaG,SAAC,+BAKlCC,EAAAA,EAAAA,MAAA,OAAKJ,UAAU,mBAAkBG,SAAA,EAC/BC,EAAAA,EAAAA,MAAA,OAAKJ,UAAU,kBAAiBG,SAAA,EAC9BV,EAAAA,EAAAA,KAAA,QAAMO,UAAU,iBAAgBG,SAAC,2BACjCC,EAAAA,EAAAA,MAAA,QAAMJ,UAAU,iBAAgBG,SAAA,CAAE5B,EAAc,yBAElDkB,EAAAA,EAAAA,KAACa,EAAAA,EAAQ,CACPC,QAASvB,EACTwB,YAAa,CACX,KAAM,UACN,MAAO,UACP,OAAQ,WAEVC,WAAW,UACXC,YAAa,EACbC,UAAU,QAKdP,EAAAA,EAAAA,MAAA,OAAKJ,UAAU,uBAAsBG,SAAA,EACnCC,EAAAA,EAAAA,MAAA,OAAKJ,UAAU,cAAaG,SAAA,EAC1BV,EAAAA,EAAAA,KAACmB,EAAAA,EAAgB,CAACZ,UAAU,iBAC5BI,EAAAA,EAAAA,MAAA,OAAKJ,UAAU,iBAAgBG,SAAA,EAC7BV,EAAAA,EAAAA,KAAA,QAAMO,UAAU,eAAcG,SAAC,gBAC/BV,EAAAA,EAAAA,KAAA,QAAMO,UAAU,eAAcG,SAAE1B,WAGpC2B,EAAAA,EAAAA,MAAA,OAAKJ,UAAU,cAAaG,SAAA,EAC1BV,EAAAA,EAAAA,KAACoB,EAAAA,EAAmB,CAACb,UAAU,iBAC/BI,EAAAA,EAAAA,MAAA,OAAKJ,UAAU,iBAAgBG,SAAA,EAC7BV,EAAAA,EAAAA,KAAA,QAAMO,UAAU,eAAcG,SAAC,oBAC/BV,EAAAA,EAAAA,KAAA,QAAMO,UAAU,eAAcG,SAC3B5B,EAAgB,EAAC,GAAAuC,OAAMvC,EAAa,SAAU,yBAQzD6B,EAAAA,EAAAA,MAAA,OAAKJ,UAAU,kBAAiBG,SAAA,EAC9BC,EAAAA,EAAAA,MAAA,OAAKJ,UAAU,eAAcG,SAAA,EAC3BV,EAAAA,EAAAA,KAAA,MAAIO,UAAU,gBAAeG,SAAC,kCAC9BC,EAAAA,EAAAA,MAAA,KAAGJ,UAAU,eAAcG,SAAA,CAAC,6DAC+BV,EAAAA,EAAAA,KAAA,UAAAU,SAASb,IAAmB,gGAKzFc,EAAAA,EAAAA,MAAA,OAAKJ,UAAU,gBAAeG,SAAA,EAC5BV,EAAAA,EAAAA,KAAA,MAAIO,UAAU,iBAAgBG,SAAC,4BAC/BV,EAAAA,EAAAA,KAAA,MAAIO,UAAU,WAAUG,UACV,OAAX/B,QAAW,IAAXA,GAAqB,QAAVH,EAAXG,EAAa2C,gBAAQ,IAAA9C,OAAV,EAAXA,EAAuB+C,MAAM,EAAG,GAAGC,KAAI,CAACC,EAASC,KAChDf,EAAAA,EAAAA,MAAA,MAAgBJ,UAAU,eAAcG,SAAA,EACtCV,EAAAA,EAAAA,KAACjC,EAAmB,CAACwC,UAAU,iBAC9BkB,IAFMC,OAIL,EACJf,EAAAA,EAAAA,MAAA,MAAYJ,UAAU,eAAcG,SAAA,EAClCV,EAAAA,EAAAA,KAACjC,EAAmB,CAACwC,UAAU,iBAAiB,gCAD1C,MAIRI,EAAAA,EAAAA,MAAA,MAAYJ,UAAU,eAAcG,SAAA,EAClCV,EAAAA,EAAAA,KAACjC,EAAmB,CAACwC,UAAU,iBAAiB,mCAD1C,MAIRI,EAAAA,EAAAA,MAAA,MAAYJ,UAAU,eAAcG,SAAA,EAClCV,EAAAA,EAAAA,KAACjC,EAAmB,CAACwC,UAAU,iBAAiB,uBAD1C,MAIRI,EAAAA,EAAAA,MAAA,MAAYJ,UAAU,eAAcG,SAAA,EAClCV,EAAAA,EAAAA,KAACjC,EAAmB,CAACwC,UAAU,iBAAiB,4BAD1C,eAUhBI,EAAAA,EAAAA,MAAA,OAAKJ,UAAU,iBAAgBG,SAAA,EAC7BV,EAAAA,EAAAA,KAAC2B,EAAAA,GAAM,CACLC,KAAK,UACLC,KAAK,QACLC,QAASpD,EACT6B,UAAU,kBAAiBG,SAC5B,uBAGDV,EAAAA,EAAAA,KAAC2B,EAAAA,GAAM,CACLC,KAAK,UACLC,KAAK,QACLC,QAASpD,EACT6B,UAAU,eAAcG,SACzB,cAMHV,EAAAA,EAAAA,KAAA,OAAKO,UAAU,cAAaG,UAC1BC,EAAAA,EAAAA,MAAA,KAAAD,SAAA,CAAG,iBACEV,EAAAA,EAAAA,KAAA,UAAAU,SAAQ,SAAa,oFAAgFV,EAAAA,EAAAA,KAAA,UAAAU,SAAS1B,aAIjH,C,qLCjLZ,QADgC,CAAE,KAAQ,CAAE,IAAO,MAAO,MAAS,CAAE,QAAW,gBAAiB,UAAa,SAAW,SAAY,CAAC,CAAE,IAAO,OAAQ,MAAS,CAAE,EAAK,kLAAqL,CAAE,IAAO,OAAQ,MAAS,CAAE,EAAK,uIAA2I,KAAQ,qBAAsB,MAAS,Y,cCM3iB+C,EAA4B,SAAmC/D,EAAOC,GACxE,OAAoBC,EAAAA,cAAoBC,EAAAA,GAAUC,EAAAA,EAAAA,GAAS,CAAC,EAAGJ,EAAO,CACpEC,IAAKA,EACLI,KAAM2D,IAEV,EAIA,QAA4B9D,EAAAA,WAAiB6D,G,sBCd7C,QADqB,CAAE,KAAQ,CAAE,IAAO,MAAO,MAAS,CAAE,QAAW,gBAAiB,UAAa,SAAW,SAAY,CAAC,CAAE,IAAO,OAAQ,MAAS,CAAE,EAAK,w7BAA47B,KAAQ,SAAU,MAAS,YCMnnC,IAAIE,EAAiB,SAAwBjE,EAAOC,GAClD,OAAoBC,EAAAA,cAAoBC,EAAAA,GAAUC,EAAAA,EAAAA,GAAS,CAAC,EAAGJ,EAAO,CACpEC,IAAKA,EACLI,KAAM6D,IAEV,EAIA,QAA4BhE,EAAAA,WAAiB+D,G,cCd7C,QADmB,CAAE,KAAQ,CAAE,IAAO,MAAO,MAAS,CAAE,QAAW,gBAAiB,UAAa,SAAW,SAAY,CAAC,CAAE,IAAO,OAAQ,MAAS,CAAE,EAAK,keAAse,KAAQ,OAAQ,MAAS,YCMzpB,IAAIE,EAAe,SAAsBnE,EAAOC,GAC9C,OAAoBC,EAAAA,cAAoBC,EAAAA,GAAUC,EAAAA,EAAAA,GAAS,CAAC,EAAGJ,EAAO,CACpEC,IAAKA,EACLI,KAAM+D,IAEV,EAIA,QAA4BlE,EAAAA,WAAiBiE,G,aCJ7C,MAoQA,EApQiC5D,IAO1B,IAP2B,QAChCE,EAAO,QACPC,EAAO,QACP2D,EAAO,aACPzD,EAAY,KACZC,EAAI,MACJyD,EAAQ,IACT/D,EACC,MAAOgE,EAAcC,IAAmBC,EAAAA,EAAAA,UAAS,OAC1CC,EAAWC,IAAgBF,EAAAA,EAAAA,WAAS,GAYrCG,EATuBC,MAC3B,GAAiB,OAAZjE,QAAY,IAAZA,IAAAA,EAAcI,QAAS,OAAO,EACnC,MAAMA,EAAU,IAAIE,KAAKN,EAAaI,SAEhCC,EADQ,IAAIC,KACOF,EACnBG,EAAWC,KAAKC,KAAKJ,EAAQ,OACnC,OAAOG,KAAKE,IAAI,EAAGH,EAAS,EAGV0D,GACdhD,GAAwB,OAAZjB,QAAY,IAAZA,OAAY,EAAZA,EAAciB,YAAa,eACvCb,EAAsB,OAAZJ,QAAY,IAAZA,GAAAA,EAAcI,QAAU,IAAIE,KAAKN,EAAaI,SAASe,qBAAuB,MAcxF+C,EAAiBR,EAAMS,OAAS,EAAIT,EAXrB,CACnB,CACEU,IAAK,eACLlD,MAAO,eACPmD,gBAAiB,KACjBC,YAAa,IACbC,SAAU,EACV7B,SAAU,CAAC,uBAAwB,oBAAqB,qBAAsB,0BAA2B,sBAoB7G,OACEtB,EAAAA,EAAAA,KAACC,EAAAA,EAAK,CACJC,KAAMzB,EACN0B,SAAUzB,EACV0B,OAAQ,KACRC,MAAO,IACPC,UAAQ,EACRC,UAAU,6BACVC,UAAW,CAAEC,gBAAiB,sBAC9B2C,UAAU,EAAM1C,UAEhBV,EAAAA,EAAAA,KAAA,OAAKO,UAAU,wBAAuBG,SAClCgC,GA+GA/B,EAAAA,EAAAA,MAAA,OAAKJ,UAAU,iBAAgBG,SAAA,EAC7BC,EAAAA,EAAAA,MAAA,OAAKJ,UAAU,eAAcG,SAAA,EAC3BV,EAAAA,EAAAA,KAAA,OAAKO,UAAU,aAAYG,UACzBV,EAAAA,EAAAA,KAACY,EAAAA,EAAa,OAEhBZ,EAAAA,EAAAA,KAAA,MAAIO,UAAU,cAAaG,SAAC,sBAC5BV,EAAAA,EAAAA,KAAA,KAAGO,UAAU,iBAAgBG,SAAC,wDAGhCV,EAAAA,EAAAA,KAAA,OAAKO,UAAU,aAAYG,SACxBoC,EAAetB,KAAK6B,IAAI,IAAAC,EAAAC,EAAAC,EAAAC,EAAA,OACvB9C,EAAAA,EAAAA,MAAA,OAEEJ,UAAS,aAAAc,QAA2B,OAAZkB,QAAY,IAAZA,OAAY,EAAZA,EAAcS,OAAQK,EAAKL,IAAM,WAAa,IACtElB,QAASA,IAvJCuB,KACxBb,EAAgBa,EAAK,EAsJQK,CAAiBL,GAAM3C,SAAA,EAEtCC,EAAAA,EAAAA,MAAA,OAAKJ,UAAU,cAAaG,SAAA,EAC1BV,EAAAA,EAAAA,KAAA,MAAIO,UAAU,aAAYG,SAAE2C,EAAKvD,SACtB,QAAVwD,EAAAD,EAAKvD,aAAK,IAAAwD,OAAA,EAAVA,EAAYK,cAAcC,SAAS,eAClC5D,EAAAA,EAAAA,KAAA,QAAMO,UAAU,aAAYG,SAAC,6BAIjCC,EAAAA,EAAAA,MAAA,OAAKJ,UAAU,eAAcG,SAAA,EAC3BC,EAAAA,EAAAA,MAAA,OAAKJ,UAAU,aAAYG,SAAA,EACzBV,EAAAA,EAAAA,KAAA,QAAMO,UAAU,WAAUG,SAAC,SAC3BV,EAAAA,EAAAA,KAAA,QAAMO,UAAU,SAAQG,SAAsB,QAAtB6C,EAAEF,EAAKJ,uBAAe,IAAAM,OAAA,EAApBA,EAAsBM,sBAEjDR,EAAKH,YAAcG,EAAKJ,kBACvBtC,EAAAA,EAAAA,MAAA,OAAKJ,UAAU,iBAAgBG,SAAA,EAC7BC,EAAAA,EAAAA,MAAA,QAAMJ,UAAU,iBAAgBG,SAAA,CAAC,OAAqB,QAAjB8C,EAACH,EAAKH,mBAAW,IAAAM,OAAA,EAAhBA,EAAkBK,qBACxDlD,EAAAA,EAAAA,MAAA,QAAMJ,UAAU,WAAUG,SAAA,CACvBtB,KAAK0E,OAAQT,EAAKH,YAAcG,EAAKJ,iBAAmBI,EAAKH,YAAe,KAAK,eAIxFvC,EAAAA,EAAAA,MAAA,OAAKJ,UAAU,WAAUG,SAAA,CAAE2C,EAAKF,SAAS,SAAOE,EAAKF,SAAW,EAAI,IAAM,UAG5EnD,EAAAA,EAAAA,KAAA,OAAKO,UAAU,gBAAeG,SACd,QADc+C,EAC3BJ,EAAK/B,gBAAQ,IAAAmC,OAAA,EAAbA,EAAelC,MAAM,EAAG,GAAGC,KAAI,CAACC,EAASC,KACxCf,EAAAA,EAAAA,MAAA,OAAiBJ,UAAU,eAAcG,SAAA,EACvCV,EAAAA,EAAAA,KAACmC,EAAY,CAAC5B,UAAU,iBACvBkB,IAFOC,QAOD,OAAZa,QAAY,IAAZA,OAAY,EAAZA,EAAcS,OAAQK,EAAKL,MAC1BhD,EAAAA,EAAAA,KAAA,OAAKO,UAAU,qBAAoBG,SAAC,sBArCjC2C,EAAKL,IAyCN,OAIVrC,EAAAA,EAAAA,MAAA,OAAKJ,UAAU,iBAAgBG,SAAA,EAC7BC,EAAAA,EAAAA,MAACgB,EAAAA,GAAM,CACLC,KAAK,UACLC,KAAK,QACLC,QAlMkBiC,KAC1BxB,GAAgBF,GAClBA,EAAQE,EACV,EAgMYyB,UAAWzB,EACXhC,UAAU,kBACVlC,MAAM2B,EAAAA,EAAAA,KAACiC,EAAc,IAAIvB,SAAA,CAC1B,kBAC4B,OAAZ6B,QAAY,IAAZA,OAAY,EAAZA,EAAczC,QAAS,oBAExCE,EAAAA,EAAAA,KAAC2B,EAAAA,GAAM,CACLC,KAAK,UACLC,KAAK,QACLC,QAASA,IAAMa,GAAa,GAC5BpC,UAAU,cAAaG,SACxB,uBAtLLC,EAAAA,EAAAA,MAAA,OAAKJ,UAAU,oBAAmBG,SAAA,EAEhCC,EAAAA,EAAAA,MAAA,OAAKJ,UAAU,8BAA6BG,SAAA,EAC1CV,EAAAA,EAAAA,KAAA,OAAKO,UAAU,eAAcG,UAC3BV,EAAAA,EAAAA,KAAC+B,EAAyB,OAE5B/B,EAAAA,EAAAA,KAAA,MAAIO,UAAU,cAAaG,SAAC,2BAC5BV,EAAAA,EAAAA,KAAA,KAAGO,UAAU,iBAAgBG,SAAC,sCAIhCC,EAAAA,EAAAA,MAAA,OAAKJ,UAAU,oBAAmBG,SAAA,EAChCC,EAAAA,EAAAA,MAAA,OAAKJ,UAAU,cAAaG,SAAA,EAC1BV,EAAAA,EAAAA,KAAA,OAAKO,UAAU,yBAAwBG,UACrCV,EAAAA,EAAAA,KAACoB,EAAAA,EAAmB,OAEtBT,EAAAA,EAAAA,MAAA,OAAKJ,UAAU,YAAWG,SAAA,EACxBV,EAAAA,EAAAA,KAAA,MAAIO,UAAU,YAAWG,SAAEb,KAC3BG,EAAAA,EAAAA,KAAA,QAAMO,UAAU,6BAA4BG,SAAC,mBAKjDC,EAAAA,EAAAA,MAAA,OAAKJ,UAAU,qBAAoBG,SAAA,EACjCC,EAAAA,EAAAA,MAAA,OAAKJ,UAAU,cAAaG,SAAA,EAC1BV,EAAAA,EAAAA,KAACmB,EAAAA,EAAgB,CAACZ,UAAU,iBAC5BI,EAAAA,EAAAA,MAAA,OAAKJ,UAAU,iBAAgBG,SAAA,EAC7BV,EAAAA,EAAAA,KAAA,QAAMO,UAAU,eAAcG,SAAC,gBAC/BV,EAAAA,EAAAA,KAAA,QAAMO,UAAU,eAAcG,SAAE1B,WAGpC2B,EAAAA,EAAAA,MAAA,OAAKJ,UAAU,cAAaG,SAAA,EAC1BV,EAAAA,EAAAA,KAACoB,EAAAA,EAAmB,CAACb,UAAU,iBAC/BI,EAAAA,EAAAA,MAAA,OAAKJ,UAAU,iBAAgBG,SAAA,EAC7BV,EAAAA,EAAAA,KAAA,QAAMO,UAAU,eAAcG,SAAC,2BAC/BV,EAAAA,EAAAA,KAAA,QAAMO,UAAU,eAAcG,SACX,IAAhBkC,EAAoB,QAAO,GAAAvB,OAAMuB,EAAW,yBAOrDjC,EAAAA,EAAAA,MAAA,OAAKJ,UAAU,mBAAkBG,SAAA,EAC/BC,EAAAA,EAAAA,MAAA,OAAKJ,UAAU,kBAAiBG,SAAA,EAC9BV,EAAAA,EAAAA,KAAA,QAAMO,UAAU,iBAAgBG,SAAC,yBACjCV,EAAAA,EAAAA,KAAA,QAAMO,UAAU,gBAAeG,SAAC,gBAElCV,EAAAA,EAAAA,KAACa,EAAAA,EAAQ,CACPC,QAAS,IACTC,YAAY,UACZC,WAAW,UACXC,YAAa,EACbC,UAAU,WAMhBP,EAAAA,EAAAA,MAAA,OAAKJ,UAAU,kBAAiBG,SAAA,EAC9BC,EAAAA,EAAAA,MAAA,OAAKJ,UAAU,+BAA8BG,SAAA,EAC3CV,EAAAA,EAAAA,KAAA,MAAIO,UAAU,gBAAeG,SAAC,2BAC9BC,EAAAA,EAAAA,MAAA,KAAGJ,UAAU,eAAcG,SAAA,CAAC,SACrBV,EAAAA,EAAAA,KAAA,UAAAU,SAASb,IAAmB,6BAAyBG,EAAAA,EAAAA,KAAA,UAAAU,SAAS1B,IAAiB,6FAKxF2B,EAAAA,EAAAA,MAAA,OAAKJ,UAAU,oBAAmBG,SAAA,EAChCV,EAAAA,EAAAA,KAAA,MAAIO,UAAU,mBAAkBG,SAAC,wCACjCC,EAAAA,EAAAA,MAAA,MAAIJ,UAAU,kBAAiBG,SAAA,EAC7BV,EAAAA,EAAAA,KAAA,MAAAU,SAAI,wBACJV,EAAAA,EAAAA,KAAA,MAAAU,SAAI,+BACJV,EAAAA,EAAAA,KAAA,MAAAU,SAAI,oCACJV,EAAAA,EAAAA,KAAA,MAAAU,SAAI,uCAMVC,EAAAA,EAAAA,MAAA,OAAKJ,UAAU,iBAAgBG,SAAA,EAC7BV,EAAAA,EAAAA,KAAC2B,EAAAA,GAAM,CACLC,KAAK,UACLC,KAAK,QACLC,QAtGUmC,KACtBtB,GAAa,EAAK,EAsGNpC,UAAU,eACVlC,MAAM2B,EAAAA,EAAAA,KAACiC,EAAc,IAAIvB,SAC1B,qBAGDV,EAAAA,EAAAA,KAAC2B,EAAAA,GAAM,CACLC,KAAK,UACLC,KAAK,QACLC,QAASpD,EACT6B,UAAU,eAAcG,SACzB,oBAMHV,EAAAA,EAAAA,KAAA,OAAKO,UAAU,cAAaG,UAC1BC,EAAAA,EAAAA,MAAA,KAAAD,SAAA,CAAG,iBACEV,EAAAA,EAAAA,KAAA,UAAAU,SAAQ,eAAmB,sFAsFlC,EC6zBZ,EA7jCqBwD,KAAO,IAADC,EAAAC,EAAAC,EACzB,MAAO/B,EAAOgC,IAAY7B,EAAAA,EAAAA,UAAS,KAC5B8B,EAASC,IAAc/B,EAAAA,EAAAA,WAAS,IAChCgC,EAAgBC,IAAqBjC,EAAAA,EAAAA,UAAS,OAC9CkC,EAAqBC,IAA0BnC,EAAAA,EAAAA,WAAS,IAG/DoC,EAAAA,EAAAA,YAAU,KACRC,QAAQC,IAAI,qDAA4CJ,EAAoB,GAC3E,CAACA,IACJ,MAAOK,EAAkBC,IAAuBxC,EAAAA,EAAAA,WAAS,IAClDF,EAAcC,IAAmBC,EAAAA,EAAAA,UAAS,OAC1CyC,EAAeC,IAAoB1C,EAAAA,EAAAA,UAAS,KAC5C2C,EAAwBC,IAA6B5C,EAAAA,EAAAA,WAAS,IAC9D6C,EAAkBC,IAAuB9C,EAAAA,EAAAA,WAAS,IAClD+C,EAAqBC,IAA0BhD,EAAAA,EAAAA,UAAS,OACxDiD,EAAcC,IAAmBlD,EAAAA,EAAAA,WAAS,IAC1CmD,EAAuBC,IAA4BpD,EAAAA,EAAAA,UAAS,OAC7D,KAAE5D,IAASiH,EAAAA,EAAAA,KAAaC,GAAUA,EAAMlH,QACxC,iBAAEmH,IAAqBF,EAAAA,EAAAA,KAAaC,GAAUA,EAAMnH,eAIpDqH,IAHWC,EAAAA,EAAAA,MAGG,CAClB,CACElD,IAAK,oBACLlD,MAAO,mBACPwB,SAAU,CACR,sBACA,oBACA,uBACA,2BACA,gCACA,cACA,cACA,QACA,kBACA,gCAEF4B,YAAa,MACbD,gBAAiB,IACjBkD,mBAAoB,GACpBhD,SAAU,EACViD,QAAQ,GAEV,CACEpD,IAAK,sBACLlD,MAAO,eACPwB,SAAU,CACR,sBACA,oBACA,uBACA,2BACA,gCACA,cACA,cACA,QACA,kBACA,+BACA,oBAEF4B,YAAa,KACbD,gBAAiB,KACjBkD,mBAAoB,GACpBhD,SAAU,EACViD,QAAQ,MAIZvB,EAAAA,EAAAA,YAAU,KACRwB,IACAC,GAA0B,GACzB,KAGHzB,EAAAA,EAAAA,YAAU,KAER0B,SAASC,KAAKC,MAAMC,SAAW,GAC/BH,SAASC,KAAKC,MAAME,SAAW,GAC/BJ,SAASC,KAAKC,MAAMpG,MAAQ,GAC5BkG,SAASC,KAAKC,MAAMG,OAAS,GAGtB,KACLL,SAASC,KAAKC,MAAMC,SAAW,GAC/BH,SAASC,KAAKC,MAAME,SAAW,GAC/BJ,SAASC,KAAKC,MAAMpG,MAAQ,GAC5BkG,SAASC,KAAKC,MAAMG,OAAS,EAAE,IAEhC,CAACjC,EAAqBK,KAGzBH,EAAAA,EAAAA,YAAU,KACR,MAAMgC,EAA0BA,KACRN,SAASO,iBAAiB,kBAClCC,SAAQC,IAChBA,EAAQC,aAAeD,EAAQE,aACjCF,EAAQG,UAAUC,IAAI,cAEtBJ,EAAQG,UAAUE,OAAO,aAC3B,GACA,EAIJ,GAAI1C,GAAuBK,EAOzB,OALAsC,WAAWT,EAAyB,KAGpCU,OAAOC,iBAAiB,SAAUX,GAE3B,KACLU,OAAOE,oBAAoB,SAAUZ,EAAwB,CAEjE,GACC,CAAClC,EAAqBK,KAGzBH,EAAAA,EAAAA,YAAU,KACJmB,GAAoB0B,KACtB5C,QAAQC,IAAI,oDACZQ,GAAoB,IAEpBA,GAAoB,EACtB,GACC,CAACS,IAEJ,MAAMK,EAAa5I,UACjB,IACE+G,GAAW,GACXM,QAAQC,IAAI,qBACZ,MAAMjH,QAAiBN,EAAAA,EAAAA,KACvBsH,QAAQC,IAAI,kBAAmBjH,GAE3BA,EAAS6J,SAAW7J,EAASF,MAAQE,EAASF,KAAKmF,OAAS,GAC9DuB,EAASxG,EAASF,MAClBkH,QAAQC,IAAI,sCAAuCjH,EAASF,OACnDgK,MAAMC,QAAQ/J,IAAaA,EAASiF,OAAS,GAEtDuB,EAASxG,GACTgH,QAAQC,IAAI,kCAAmCjH,KAE/CgH,QAAQgD,KAAK,yCACbxD,EAAS2B,GACT8B,EAAAA,GAAQC,KAAK,uDAEjB,CAAE,MAAOnK,GACPiH,QAAQjH,MAAM,gCAAiCA,GAC/CiH,QAAQC,IAAI,+BACZT,EAAS2B,GACT8B,EAAAA,GAAQE,QAAQ,kEAClB,CAAC,QACCzD,GAAW,EACb,GAGI8B,EAA2B7I,UAC/B,IACE,MAAMK,QAAiBoK,EAAAA,EAAAA,KACvBpD,QAAQC,IAAI,wBAAyBjH,EACvC,CAAE,MAAOD,GACPiH,QAAQC,IAAI,+BACd,GAII2C,EAAwBA,KAC5B,IAAK1B,EAAkB,OAAO,EAG9B,IAAKA,EAAiBhH,QAAS,OAAO,EAGtC,GAAuC,SAAnCgH,EAAiBd,cAA0B,OAAO,EAGtD,GAAgC,WAA5Bc,EAAiBI,OAAqB,OAAO,EAGjD,MAAMpH,EAAU,IAAIE,KAAK8G,EAAiBhH,SACpCmJ,EAAQ,IAAIjJ,KAIlB,OAHAiJ,EAAMC,SAAS,EAAG,EAAG,EAAG,GACxBpJ,EAAQoJ,SAAS,EAAG,EAAG,EAAG,GAEnBpJ,EAAUmJ,CAAK,EA4ClBzE,EAAmBjG,UAEvB,GAAIuI,GAAgD,WAA5BA,EAAiBI,QAA0D,SAAnCJ,EAAiBd,cAG/E,OAFAJ,QAAQC,IAAI,qDAA4CiB,QACxDX,GAA0B,GAI5B,GAAKxG,EAAKwJ,aAAgB,iBAAiBC,KAAKzJ,EAAKwJ,aAArD,CAKAvD,QAAQC,IAAI,uDAEZ,IAAK,IAADwD,EACFzD,QAAQC,IAAI,0CAAiC1B,EAAKvD,OAClDgF,QAAQC,IAAI,wDAGZvC,EAAgBa,GAChBqB,EAAkBrB,EAAKL,KACvB4B,GAAuB,GACvBe,GAAgB,GAChBF,EAAuBvG,KAAKsJ,OAC5BrD,EAAiB,kDAEjBL,QAAQC,IAAI,uDAGN,IAAI0D,SAAQC,GAAWpB,WAAWoB,EAAS,OAGjDpB,YAAW,KACT3B,GAAgB,EAAK,GACpB,KAEH,MAAMgD,EAAc,CAClBtF,KAAMA,EACNuF,OAAQ/J,EAAKmE,IACb6F,UAAWhK,EAAKwJ,YAChBS,UAAWjK,EAAKkK,OAAK,GAAA1H,OAAgB,QAAhBkH,EAAO1J,EAAKmK,YAAI,IAAAT,OAAA,EAATA,EAAWU,QAAQ,OAAQ,IAAItF,cAAa,oBAG1EwB,EAAiB,sDACjB,MAAMrH,QAAiBoL,EAAAA,EAAAA,GAAWP,GAElC,IAAI7K,EAAS6J,QAsBX,MAAM,IAAIwB,MAAMrL,EAASiK,SAAW,kBAtBhB,CAAC,IAADqB,EACpBjE,EAAiB,0DAEjBL,QAAQC,IAAI,iCAAwBjH,GACpCgH,QAAQC,IAAI,yBAAgBjH,EAASuL,UAGrCtB,EAAAA,GAAQJ,QAAQ,CACdX,QAAQ,kEAAD3F,OAAgDxC,EAAKwJ,YAAW,wCACvElF,SAAU,EACVsD,MAAO,CACL6C,UAAW,UAKf,MAAMC,EAAiBzL,EAASuL,WAAyB,QAAjBD,EAAItL,EAASF,YAAI,IAAAwL,OAAA,EAAbA,EAAeC,WAAY,aACvEvE,QAAQC,IAAI,8DAAqDwE,GAEjEC,EAAyBD,EAE3B,CAGF,CAAE,MAAO1L,GACPiH,QAAQjH,MAAM,yBAAqBA,GACnC+G,GAAuB,GACvBmD,EAAAA,GAAQlK,MAAM,mBAAqBA,EAAMkK,SACzCrD,EAAkB,KACpB,CAjEA,MAFEqD,EAAAA,GAAQlK,MAAM,qEAmEhB,EAGI2L,EAA2B/L,UAC/BqH,QAAQC,IAAI,8DAAqD0E,GACjE,IACIC,EADAC,GAAY,EAGhB,IACExE,EAAiB,qFAGjB,IAAIyE,EAAW,EACf,MAAMC,EAAc,IAEdC,EAAoBrM,UACxBmM,IACA9E,QAAQC,IAAI,6CAAD1D,OAAoCuI,EAAQ,KAAAvI,OAAIwI,EAAW,eAAeJ,GAErF,IACE,MAAMM,QAAuB7B,EAAAA,EAAAA,GAAmB,CAAEuB,YAMlD,GALA3E,QAAQC,IAAI,wCAA+BgF,GAC3CjF,QAAQC,IAAI,6CACZD,QAAQC,IAAI,oBAAuD,UAApB,OAAdgF,QAAc,IAAdA,OAAc,EAAdA,EAAgB7E,gBAAuD,YAAb,OAAd6E,QAAc,IAAdA,OAAc,EAAdA,EAAgB3D,SAC7FtB,QAAQC,IAAI,oBAAgD,eAAb,OAAdgF,QAAc,IAAdA,OAAc,EAAdA,EAAgB3D,UAAsD,KAAd,OAAd2D,QAAc,IAAdA,OAAc,EAAdA,EAAgBpC,UAEvFoC,IACgC,SAAjCA,EAAe7E,eAAsD,WAA1B6E,EAAe3D,QAChC,cAA1B2D,EAAe3D,SAAqD,IAA3B2D,EAAepC,SACxD,CAEDgC,GAAY,EACRD,GACFnD,SAASkB,oBAAoB,mBAAoBiC,GAGnDvE,EAAiB,mEACjBL,QAAQC,IAAI,gEAGZD,QAAQC,IAAI,wEACZH,GAAuB,GACvBK,GAAoB,GACpBP,EAAkB,MAClBI,QAAQC,IAAI,0CAGZuB,IAGAyB,EAAAA,GAAQJ,QAAQ,CACdX,QAAS,iEACT7D,SAAU,EACVsD,MAAO,CACL6C,UAAW,OACXU,SAAU,UAKdnE,EAAyB,GACzB,MAAMoE,EAAoBC,aAAY,KACpCrE,GAAyBsE,GACnBA,GAAQ,GACVC,cAAcH,GACdnF,QAAQC,IAAI,mEACZE,GAAoB,GACpBsC,OAAO8C,SAASC,KAAO,YAChB,MAEFH,EAAO,GACd,GACD,IAEL,MAAWP,GAAYC,GAErBF,GAAY,EACRD,GACFnD,SAASkB,oBAAoB,mBAAoBiC,GAGnDvE,EAAiB,qFAEjBmC,YAAW,KACT1C,GAAuB,GACvBF,EAAkB,MAClBqD,EAAAA,GAAQE,QAAQ,2GAA2G,GAC1H,OAIH9C,EAAiB,qFACjBmC,WAAWwC,EAAmB,KAGlC,CAAE,MAAOjM,GAIP,GAHAiH,QAAQjH,MAAM,8BAA+BA,GAGzCA,EAAMkK,SAAWlK,EAAMkK,QAAQnE,SAAS,OAS1C,OARAkB,QAAQjH,MAAM,kDACd8L,GAAY,EACRD,GACFnD,SAASkB,oBAAoB,mBAAoBiC,GAEnD9E,GAAuB,GACvBF,EAAkB,WAClBqD,EAAAA,GAAQlK,MAAM,+HAIhB,GAAIA,EAAMkK,SAAWlK,EAAMkK,QAAQnE,SAAS,OAS1C,OARAkB,QAAQjH,MAAM,2DACd8L,GAAY,EACRD,GACFnD,SAASkB,oBAAoB,mBAAoBiC,GAEnD9E,GAAuB,GACvBF,EAAkB,WAClBqD,EAAAA,GAAQlK,MAAM,+CAIZ+L,GAAYC,GACdF,GAAY,EACRD,GACFnD,SAASkB,oBAAoB,mBAAoBiC,GAEnD9E,GAAuB,GACvBF,EAAkB,MAClBqD,EAAAA,GAAQlK,MAAM,sFAGdyJ,WAAWwC,EAAmB,IAElC,GAIFJ,EAAyBA,MAClBnD,SAASgE,QAAUZ,IACtB7E,QAAQC,IAAI,gEACZI,EAAiB,2CAEjBmC,YAAW,IAAMwC,KAAqB,KACxC,EAGFvD,SAASiB,iBAAiB,mBAAoBkC,GAG9CpC,WAAWwC,EAAmB,IAEhC,CAAE,MAAOjM,GACP8L,GAAY,EACRD,GACFnD,SAASkB,oBAAoB,mBAAoBiC,GAEnD9E,GAAuB,GACvBmD,EAAAA,GAAQlK,MAAM,gCAAkCA,EAAMkK,SACtDrD,EAAkB,KACpB,GAmBI8F,EAAcC,GACbA,EACE,IAAIvL,KAAKuL,GAAY1K,mBAAmB,QAAS,CACtD2K,KAAM,UACNC,MAAO,OACPC,IAAK,YAJiB,MAiBpBC,EAlCwBC,MAC5B,GAAI9E,GAAuD,SAAnCA,EAAiBd,eAAwD,WAA5Bc,EAAiBI,OAAqB,CAGzG,GAFgB,IAAIlH,KAAK8G,EAAiBhH,SAC9B,IAAIE,KAEd,MAAO,QAEX,CAEA,MAAiC,aAAzB,OAAJL,QAAI,IAAJA,OAAI,EAAJA,EAAMgM,qBAAqC7E,GAAgD,YAA5BA,EAAiBI,OAC3E,UAGF,MAAM,EAqBY0E,GAE3B,OACE9K,EAAAA,EAAAA,KAAA,OAAKO,UAAU,oBAAmBG,UAChCC,EAAAA,EAAAA,MAAA,OAAKJ,UAAU,yBAAwBG,SAAA,EAErCC,EAAAA,EAAAA,MAACoK,EAAAA,EAAOC,IAAG,CACTC,QAAS,CAAEC,QAAS,EAAGC,GAAI,IAC3BC,QAAS,CAAEF,QAAS,EAAGC,EAAG,GAC1BE,WAAY,CAAElI,SAAU,IACxB5C,UAAU,sBAAqBG,SAAA,EAG/BV,EAAAA,EAAAA,KAAA,UACE8B,QAASA,KACPgD,QAAQC,IAAI,yCACZvC,EAAgBF,EAAM,IAAM,CAAExC,MAAO,YAAaqD,SAAU,EAAGF,gBAAiB,OAChFgC,GAAoB,EAAK,EAE3BwB,MAAO,CACLE,SAAU,QACV2E,IAAK,OACLC,MAAO,OACPC,WAAY,UACZC,MAAO,QACPC,OAAQ,OACRC,QAAS,WACTC,aAAc,MACdC,OAAQ,UACRC,OAAQ,MACRpL,SACH,qCAGDC,EAAAA,EAAAA,MAAA,MAAIJ,UAAU,aAAYG,SAAA,EACxBV,EAAAA,EAAAA,KAAC+L,EAAAA,IAAO,CAACxL,UAAU,eAAe,8BAGpCP,EAAAA,EAAAA,KAAA,KAAGO,UAAU,gBAAeG,SAAC,6DAI/BC,EAAAA,EAAAA,MAACoK,EAAAA,EAAOC,IAAG,CACTC,QAAS,CAAEC,QAAS,EAAGC,EAAG,IAC1BC,QAAS,CAAEF,QAAS,EAAGC,EAAG,GAC1BE,WAAY,CAAElI,SAAU,GAAK6I,MAAO,IACpCzL,UAAU,uBAAsBG,SAAA,EAEhCV,EAAAA,EAAAA,KAAA,MAAIO,UAAU,gBAAeG,SAAC,yBAEN,WAAvBmK,IACClK,EAAAA,EAAAA,MAAA,OAAKJ,UAAU,2BAA0BG,SAAA,EACvCC,EAAAA,EAAAA,MAAA,OAAKJ,UAAU,sBAAqBG,SAAA,EAClCV,EAAAA,EAAAA,KAACiM,EAAAA,IAAa,CAAC1L,UAAU,wBACzBP,EAAAA,EAAAA,KAAA,QAAMO,UAAU,cAAaG,SAAC,4BAEhCC,EAAAA,EAAAA,MAAA,OAAKJ,UAAU,uBAAsBG,SAAA,EACnCC,EAAAA,EAAAA,MAAA,OAAKJ,UAAU,cAAaG,SAAA,EAC1BV,EAAAA,EAAAA,KAAC+L,EAAAA,IAAO,CAACxL,UAAU,iBACnBI,EAAAA,EAAAA,MAAA,QAAAD,SAAA,CAAM,UAAuB,OAAhBsF,QAAgB,IAAhBA,GAA4B,QAAZ7B,EAAhB6B,EAAkBkG,kBAAU,IAAA/H,OAAZ,EAAhBA,EAA8BrE,QAAS,sBAEtDa,EAAAA,EAAAA,MAAA,OAAKJ,UAAU,cAAaG,SAAA,EAC1BV,EAAAA,EAAAA,KAACmM,EAAAA,IAAa,CAAC5L,UAAU,iBACzBI,EAAAA,EAAAA,MAAA,QAAAD,SAAA,CAAM,YAAU8J,EAA2B,OAAhBxE,QAAgB,IAAhBA,OAAgB,EAAhBA,EAAkBhH,gBAE/C2B,EAAAA,EAAAA,MAAA,OAAKJ,UAAU,cAAaG,SAAA,EAC1BV,EAAAA,EAAAA,KAACiM,EAAAA,IAAa,CAAC1L,UAAU,iBACzBI,EAAAA,EAAAA,MAAA,QAAAD,SAAA,CAAM,mBA5EG0L,MACvB,GAAqB,OAAhBpG,QAAgB,IAAhBA,IAAAA,EAAkBhH,QAAS,OAAO,EACvC,MAEMC,EAFU,IAAIC,KAAK8G,EAAiBhH,SAC9B,IAAIE,KAEVC,EAAWC,KAAKC,KAAKJ,EAAQ,OACnC,OAAOG,KAAKE,IAAI,EAAGH,EAAS,EAsESiN,eAMP,YAAvBvB,IACClK,EAAAA,EAAAA,MAAA,OAAKJ,UAAU,4BAA2BG,SAAA,EACxCC,EAAAA,EAAAA,MAAA,OAAKJ,UAAU,sBAAqBG,SAAA,EAClCV,EAAAA,EAAAA,KAACqM,EAAAA,IAAa,CAAC9L,UAAU,yBACzBP,EAAAA,EAAAA,KAAA,QAAMO,UAAU,cAAaG,SAAC,6BAEhCC,EAAAA,EAAAA,MAAA,OAAKJ,UAAU,uBAAsBG,SAAA,EACnCC,EAAAA,EAAAA,MAAA,OAAKJ,UAAU,cAAaG,SAAA,EAC1BV,EAAAA,EAAAA,KAACmM,EAAAA,IAAa,CAAC5L,UAAU,iBACzBI,EAAAA,EAAAA,MAAA,QAAAD,SAAA,CAAM,YAAU8J,EAA2B,OAAhBxE,QAAgB,IAAhBA,OAAgB,EAAhBA,EAAkBhH,gBAE/CgB,EAAAA,EAAAA,KAAA,KAAGO,UAAU,kBAAiBG,SAAC,yGAOb,SAAvBmK,IACClK,EAAAA,EAAAA,MAAA,OAAKJ,UAAU,yBAAwBG,SAAA,EACrCC,EAAAA,EAAAA,MAAA,OAAKJ,UAAU,sBAAqBG,SAAA,EAClCV,EAAAA,EAAAA,KAACsM,EAAAA,IAAM,CAAC/L,UAAU,sBAClBP,EAAAA,EAAAA,KAAA,QAAMO,UAAU,cAAaG,SAAC,qBAEhCV,EAAAA,EAAAA,KAAA,OAAKO,UAAU,uBAAsBG,UACnCV,EAAAA,EAAAA,KAAA,KAAGO,UAAU,kBAAiBG,SAAC,sGASvCC,EAAAA,EAAAA,MAACoK,EAAAA,EAAOC,IAAG,CACTC,QAAS,CAAEC,QAAS,EAAGC,EAAG,IAC1BC,QAAS,CAAEF,QAAS,EAAGC,EAAG,GAC1BE,WAAY,CAAElI,SAAU,GAAK6I,MAAO,IACpCzL,UAAU,kBAAiBG,SAAA,EAE3BV,EAAAA,EAAAA,KAAA,MAAIO,UAAU,gBAAeG,SACH,WAAvBmK,EACG,iCACuB,YAAvBA,EACE,uCACA,mCAKRlK,EAAAA,EAAAA,MAAA,OAAK8F,MAAO,CAAE8F,QAAS,OAAQC,IAAK,OAAQC,eAAgB,SAAUC,aAAc,QAAShM,SAAA,EAC3FV,EAAAA,EAAAA,KAAA,UACE8B,QAtZgB6K,KAC1B7H,QAAQC,IAAI,4CACZH,GAAuB,GACvBO,EAAiB,+BACjB3C,EAAgBF,EAAM,IAAM,CAAExC,MAAO,YAAamD,gBAAiB,IAAME,SAAU,GAAI,EAmZ7EsD,MAAO,CACL+E,WAAY,UACZC,MAAO,QACPC,OAAQ,OACRC,QAAS,WACTC,aAAc,MACdC,OAAQ,UACR7B,SAAU,QACVtJ,SACH,wCAGDV,EAAAA,EAAAA,KAAA,UACE8B,QA5aa8K,KACvB9H,QAAQC,IAAI,yCACZH,GAAuB,GACvBK,GAAoB,GACpBP,EAAkB,KAAK,EAyab+B,MAAO,CACL+E,WAAY,UACZC,MAAO,QACPC,OAAQ,OACRC,QAAS,WACTC,aAAc,MACdC,OAAQ,UACR7B,SAAU,QACVtJ,SACH,wCAIHV,EAAAA,EAAAA,KAAA,KAAGO,UAAU,mBAAkBG,SACL,WAAvBmK,EACG,gEACuB,YAAvBA,EACE,kFACA,8FAIPtG,GACC5D,EAAAA,EAAAA,MAAA,OAAKJ,UAAU,gBAAeG,SAAA,EAC5BV,EAAAA,EAAAA,KAAA,OAAKO,UAAU,aACfP,EAAAA,EAAAA,KAAA,KAAAU,SAAG,wBAEc,IAAjB4B,EAAMS,QACRpC,EAAAA,EAAAA,MAAA,OAAKJ,UAAU,iBAAgBG,SAAA,EAC7BV,EAAAA,EAAAA,KAAA,OAAKO,UAAU,gBAAeG,SAAC,kBAC/BV,EAAAA,EAAAA,KAAA,MAAAU,SAAI,wBACJV,EAAAA,EAAAA,KAAA,KAAAU,SAAG,mFACHV,EAAAA,EAAAA,KAAA,UAAQO,UAAU,cAAcuB,QAASuE,EAAW3F,SAAC,mCAKvDV,EAAAA,EAAAA,KAAA,OAAKO,UAAU,aAAYG,SACxB4B,EAAMd,KAAK6B,IAAI,IAAAC,EAAAC,EAAAC,EAAAC,EAAA,OACd9C,EAAAA,EAAAA,MAACoK,EAAAA,EAAOC,IAAG,CAET6B,WAAY,CAAEC,MAAO,MACrBC,SAAU,CAAED,MAAO,KACnBvM,UAAU,YAAWG,SAAA,EAErBC,EAAAA,EAAAA,MAAA,OAAKJ,UAAU,cAAaG,SAAA,EAC1BV,EAAAA,EAAAA,KAAA,MAAIO,UAAU,aAAYG,SAAE2C,EAAKvD,SACtB,QAAVwD,EAAAD,EAAKvD,aAAK,IAAAwD,OAAA,EAAVA,EAAYK,cAAcC,SAAS,eAClC5D,EAAAA,EAAAA,KAAA,QAAMO,UAAU,aAAYG,SAAC,6BAIjCC,EAAAA,EAAAA,MAAA,OAAKJ,UAAU,eAAcG,SAAA,EAC3BC,EAAAA,EAAAA,MAAA,OAAKJ,UAAU,gBAAeG,SAAA,EAC5BC,EAAAA,EAAAA,MAAA,OAAKJ,UAAU,gBAAeG,SAAA,EAC5BV,EAAAA,EAAAA,KAAA,QAAMO,UAAU,WAAUG,SAAC,QACN,QADgB6C,EACpCF,EAAKJ,uBAAe,IAAAM,OAAA,EAApBA,EAAsBM,oBAExBR,EAAKH,YAAcG,EAAKJ,kBACvBtC,EAAAA,EAAAA,MAAAqM,EAAAA,SAAA,CAAAtM,SAAA,EACEC,EAAAA,EAAAA,MAAA,QAAMJ,UAAU,iBAAgBG,SAAA,CAAkB,QAAlB8C,EAAEH,EAAKH,mBAAW,IAAAM,OAAA,EAAhBA,EAAkBK,iBAAiB,WACrElD,EAAAA,EAAAA,MAAA,QAAMJ,UAAU,iBAAgBG,SAAA,CAC7BtB,KAAK0E,OAAQT,EAAKH,YAAcG,EAAKJ,iBAAmBI,EAAKH,YAAe,KAAK,kBAK1FvC,EAAAA,EAAAA,MAAA,OAAKJ,UAAU,gBAAeG,SAAA,EAC5BV,EAAAA,EAAAA,KAAA,QAAMO,UAAU,qBAAoBG,SAAE2C,EAAKF,WAAgB,SAAOE,EAAKF,SAAW,EAAI,IAAM,GAAG,iBAInGnD,EAAAA,EAAAA,KAAA,OAAKO,UAAU,gBAAeG,SACd,QADc+C,EAC3BJ,EAAK/B,gBAAQ,IAAAmC,OAAA,EAAbA,EAAelC,MAAM,EAAG,GAAGC,KAAI,CAACC,EAASC,KACxCf,EAAAA,EAAAA,MAAA,OAAiBJ,UAAU,eAAcG,SAAA,EACvCV,EAAAA,EAAAA,KAACiM,EAAAA,IAAa,CAAC1L,UAAU,kBACzBP,EAAAA,EAAAA,KAAA,QAAAU,SAAOe,MAFCC,QAOdf,EAAAA,EAAAA,MAAA,UACEJ,UAAU,kBACVuB,QAASA,IAAM4B,EAAiBL,GAChCW,SAAUS,IAAmBpB,EAAKL,IAClCyD,MAAO,CACL+E,WAAY,4CACZC,MAAO,QACPC,OAAQ,OACRE,aAAc,OACdD,QAAS,cACT3B,SAAU,OACViD,WAAY,MACZpB,OAAQpH,IAAmBpB,EAAKL,IAAM,cAAgB,UACtDqI,WAAY,gBACZkB,QAAS,OACTW,WAAY,SACZT,eAAgB,SAChBD,IAAK,SACLnM,MAAO,OACP6K,QAASzG,IAAmBpB,EAAKL,IAAM,GAAM,GAE/CmK,aAAeC,IACT3I,IAAmBpB,EAAKL,MAC1BoK,EAAEC,OAAO5G,MAAM+E,WAAa,4CAC5B4B,EAAEC,OAAO5G,MAAM6G,UAAY,mBAC3BF,EAAEC,OAAO5G,MAAM8G,UAAY,qCAC7B,EAEFC,aAAeJ,IACT3I,IAAmBpB,EAAKL,MAC1BoK,EAAEC,OAAO5G,MAAM+E,WAAa,4CAC5B4B,EAAEC,OAAO5G,MAAM6G,UAAY,gBAC3BF,EAAEC,OAAO5G,MAAM8G,UAAY,qCAC7B,EACA7M,SAAA,EAEFV,EAAAA,EAAAA,KAACyN,EAAAA,IAAY,CAAClN,UAAU,aACvBkE,IAAmBpB,EAAKL,IACrB,gBACuB,WAAvB6H,EACE,mBACuB,YAAvBA,EACE,iBACA,oBApFLxH,EAAKL,IAuFC,WAOlBnE,EAAKwJ,cAAgB,iBAAiBC,KAAKzJ,EAAKwJ,gBACjDrI,EAAAA,EAAAA,KAAC+K,EAAAA,EAAOC,IAAG,CACTC,QAAS,CAAEC,QAAS,EAAGC,EAAG,IAC1BC,QAAS,CAAEF,QAAS,EAAGC,EAAG,GAC1BE,WAAY,CAAElI,SAAU,GAAK6I,MAAO,IACpCzL,UAAU,gBAAeG,UAEzBC,EAAAA,EAAAA,MAAA,OAAKJ,UAAU,kBAAiBG,SAAA,EAC9BV,EAAAA,EAAAA,KAACqM,EAAAA,IAAa,CAAC9L,UAAU,kBACzBI,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEV,EAAAA,EAAAA,KAAA,MAAAU,SAAI,2BACJV,EAAAA,EAAAA,KAAA,KAAAU,SAAG,6EACHV,EAAAA,EAAAA,KAAA,UACEO,UAAU,mBACVuB,QAASA,IAAMyF,OAAO8C,SAASC,KAAO,WAAW5J,SAClD,gCASRiE,IACC3E,EAAAA,EAAAA,KAAA,OACEO,UAAU,wBACVuB,QAAUsL,IAEJA,EAAEC,SAAWD,EAAEM,eAEjBN,EAAEO,iBACJ,EACAjN,UAEFC,EAAAA,EAAAA,MAAA,OAAKJ,UAAU,qCAAoCG,SAAA,EAEjDV,EAAAA,EAAAA,KAAA,UACEO,UAAU,kBACVuB,QA7mBqB8L,KACjChJ,GAAuB,GACvBF,EAAkB,MAClBiB,GAAgB,GAChBF,EAAuB,MACvBN,EAAiB,IACjB4C,EAAAA,GAAQC,KAAK,wDAAwD,EAwmBzD,aAAW,cAAatH,UAExBV,EAAAA,EAAAA,KAAA,OAAKK,MAAM,KAAKuG,OAAO,KAAKiH,QAAQ,YAAYC,KAAK,OAAMpN,UACzDV,EAAAA,EAAAA,KAAA,QAAM+N,EAAE,uBAAuBC,OAAO,eAAe/M,YAAY,IAAIgN,cAAc,eAKvFtN,EAAAA,EAAAA,MAAA,OAAKJ,UAAU,0BAAyBG,SAAA,EACtCC,EAAAA,EAAAA,MAAA,OAAKJ,UAAU,kBAAiBG,SAAA,EAC9BV,EAAAA,EAAAA,KAAA,OAAKO,UAAU,aACfI,EAAAA,EAAAA,MAAA,OAAKJ,UAAU,eAAeF,MAAM,KAAKuG,OAAO,KAAKiH,QAAQ,YAAYC,KAAK,OAAMpN,SAAA,EAClFV,EAAAA,EAAAA,KAAA,QAAM+N,EAAE,yOAAyOC,OAAO,eAAe/M,YAAY,SACnRjB,EAAAA,EAAAA,KAAA,QAAM+N,EAAE,WAAWC,OAAO,eAAe/M,YAAY,MAAMgN,cAAc,WACzEjO,EAAAA,EAAAA,KAAA,QAAM+N,EAAE,cAAcC,OAAO,eAAe/M,YAAY,MAAMgN,cAAc,WAC5EjO,EAAAA,EAAAA,KAAA,QAAM+N,EAAE,cAAcC,OAAO,eAAe/M,YAAY,MAAMgN,cAAc,iBAGhFjO,EAAAA,EAAAA,KAAA,MAAAU,SAAI,wBACJV,EAAAA,EAAAA,KAAA,KAAAU,SAAG,uCAILC,EAAAA,EAAAA,MAAA,OAAKJ,UAAU,gBAAeG,SAAA,EAE5BC,EAAAA,EAAAA,MAAA,OAAKJ,UAAU,cAAaG,SAAA,EAC1BV,EAAAA,EAAAA,KAAA,OAAKO,UAAU,iCACfP,EAAAA,EAAAA,KAAA,KAAGO,UAAU,cAAaG,SAAEwE,QAI9BvE,EAAAA,EAAAA,MAAA,OAAKJ,UAAU,iBAAgBG,SAAA,EAC7BV,EAAAA,EAAAA,KAAA,MAAAU,SAAiB,OAAZ6B,QAAY,IAAZA,OAAY,EAAZA,EAAczC,SACnBa,EAAAA,EAAAA,MAAA,OAAKJ,UAAU,eAAcG,SAAA,EAC3BC,EAAAA,EAAAA,MAAA,OAAKJ,UAAU,aAAYG,SAAA,EACzBV,EAAAA,EAAAA,KAAA,QAAAU,SAAM,YACNC,EAAAA,EAAAA,MAAA,UAAAD,SAAA,CAAqB,OAAZ6B,QAAY,IAAZA,GAA6B,QAAjB6B,EAAZ7B,EAAcU,uBAAe,IAAAmB,OAAjB,EAAZA,EAA+BP,iBAAiB,cAE3DlD,EAAAA,EAAAA,MAAA,OAAKJ,UAAU,aAAYG,SAAA,EACzBV,EAAAA,EAAAA,KAAA,QAAAU,SAAM,cACNC,EAAAA,EAAAA,MAAA,UAAAD,SAAA,CAAqB,OAAZ6B,QAAY,IAAZA,OAAY,EAAZA,EAAcY,SAAS,UAAmB,OAAZZ,QAAY,IAAZA,OAAY,EAAZA,EAAcY,UAAW,EAAI,IAAM,gBAMhFxC,EAAAA,EAAAA,MAAA,OAAKJ,UAAU,mBAAkBG,SAAA,EAC/BC,EAAAA,EAAAA,MAAA,OAAKJ,UAAU,qBAAoBG,SAAA,EACjCV,EAAAA,EAAAA,KAAA,OAAKK,MAAM,KAAKuG,OAAO,KAAKiH,QAAQ,YAAYC,KAAK,OAAMpN,UACzDV,EAAAA,EAAAA,KAAA,QAAM+N,EAAE,+kBAA+kBD,KAAK,oBAE9lB9N,EAAAA,EAAAA,KAAA,QAAAU,SAAM,yBAERV,EAAAA,EAAAA,KAAA,OAAKO,UAAU,eAAcG,SAAM,OAAJ7B,QAAI,IAAJA,OAAI,EAAJA,EAAMwJ,eACrC1H,EAAAA,EAAAA,MAAA,OAAKJ,UAAU,oBAAmBG,SAAA,EAChCV,EAAAA,EAAAA,KAAA,OAAKO,UAAU,OAAMG,SAAC,wDACtBV,EAAAA,EAAAA,KAAA,OAAKO,UAAU,OAAMG,SAAC,gDACtBV,EAAAA,EAAAA,KAAA,OAAKO,UAAU,OAAMG,SAAC,mDAKzBgF,IACC/E,EAAAA,EAAAA,MAAA,OAAKJ,UAAU,iBAAgBG,SAAA,EAC7BV,EAAAA,EAAAA,KAAA,KAAAU,SAAG,kCACHC,EAAAA,EAAAA,MAAA,UAAQJ,UAAU,gBAAgBuB,QArqB7BoM,KACjB3L,IACFoD,GAAgB,GAChBF,EAAuB,MACvB/B,EAAiBnB,GACnB,EAgqB0E7B,SAAA,EACxDC,EAAAA,EAAAA,MAAA,OAAKN,MAAM,KAAKuG,OAAO,KAAKiH,QAAQ,YAAYC,KAAK,OAAMpN,SAAA,EACzDV,EAAAA,EAAAA,KAAA,QAAM+N,EAAE,wBAAwBC,OAAO,eAAe/M,YAAY,IAAIgN,cAAc,WACpFjO,EAAAA,EAAAA,KAAA,QAAM+N,EAAE,kBAAkBC,OAAO,eAAe/M,YAAY,IAAIgN,cAAc,QAAQE,eAAe,aACjG,0BAanBnJ,IACChF,EAAAA,EAAAA,KAAA,OACEO,UAAU,wBACVuB,QAAUsL,IAEJA,EAAEC,SAAWD,EAAEM,eACjBN,EAAEO,iBACJ,EACAjN,UAEFC,EAAAA,EAAAA,MAAA,OAAKJ,UAAU,kCAAiCG,SAAA,EAE9CV,EAAAA,EAAAA,KAAA,UACEO,UAAU,kBACVuB,QAASA,KACP+D,EAAyB,MACzBZ,GAAoB,EAAM,EAE5B,aAAW,cAAavE,UAExBV,EAAAA,EAAAA,KAAA,OAAKK,MAAM,KAAKuG,OAAO,KAAKiH,QAAQ,YAAYC,KAAK,OAAMpN,UACzDV,EAAAA,EAAAA,KAAA,QAAM+N,EAAE,uBAAuBC,OAAO,eAAe/M,YAAY,IAAIgN,cAAc,eAKvFtN,EAAAA,EAAAA,MAAA,OAAKJ,UAAU,uBAAsBG,SAAA,EACnCV,EAAAA,EAAAA,KAAA,OAAKO,UAAU,eAAcG,UAC3BC,EAAAA,EAAAA,MAAA,OAAKN,MAAM,KAAKuG,OAAO,KAAKiH,QAAQ,YAAYC,KAAK,OAAMpN,SAAA,EACzDV,EAAAA,EAAAA,KAAA,QAAM+N,EAAE,oHAAoHD,KAAK,UAAUM,YAAY,SACvJpO,EAAAA,EAAAA,KAAA,QAAM+N,EAAE,uBAAuBC,OAAO,UAAU/M,YAAY,IAAIgN,cAAc,QAAQE,eAAe,WACrGnO,EAAAA,EAAAA,KAAA,QAAM+N,EAAE,oHAAoHC,OAAO,UAAU/M,YAAY,YAG7JjB,EAAAA,EAAAA,KAAA,MAAAU,SAAI,yBACJC,EAAAA,EAAAA,MAAA,KAAAD,SAAA,CAAG,cAAwB,OAAZ6B,QAAY,IAAZA,OAAY,EAAZA,EAAczC,MAAM,WAGrCa,EAAAA,EAAAA,MAAA,OAAKJ,UAAU,gBAAeG,SAAA,CAE3BkF,IACCjF,EAAAA,EAAAA,MAAA,OAAKJ,UAAU,iBAAgBG,SAAA,EAC7BV,EAAAA,EAAAA,KAAA,OAAKO,UAAU,iBAAgBG,UAC7BC,EAAAA,EAAAA,MAAA,OAAKN,MAAM,KAAKuG,OAAO,KAAKiH,QAAQ,YAAYC,KAAK,OAAMpN,SAAA,EACzDV,EAAAA,EAAAA,KAAA,QAAM+N,EAAE,2HAA2HC,OAAO,eAAe/M,YAAY,IAAIgN,cAAc,WACvLjO,EAAAA,EAAAA,KAAA,UAAQqO,GAAG,KAAKC,GAAG,KAAKC,EAAE,IAAIP,OAAO,eAAe/M,YAAY,YAGpEN,EAAAA,EAAAA,MAAA,KAAAD,SAAA,CAAG,yBAAuBkF,EAAsB,qBAKpDjF,EAAAA,EAAAA,MAAA,OAAKJ,UAAU,oBAAmBG,SAAA,EAChCV,EAAAA,EAAAA,KAAA,MAAAU,SAAI,4BACJC,EAAAA,EAAAA,MAAA,OAAKJ,UAAU,eAAcG,SAAA,EAC3BC,EAAAA,EAAAA,MAAA,OAAKJ,UAAU,aAAYG,SAAA,EACzBV,EAAAA,EAAAA,KAAA,QAAAU,SAAM,UACNV,EAAAA,EAAAA,KAAA,UAAAU,SAAqB,OAAZ6B,QAAY,IAAZA,OAAY,EAAZA,EAAczC,YAEzBa,EAAAA,EAAAA,MAAA,OAAKJ,UAAU,aAAYG,SAAA,EACzBV,EAAAA,EAAAA,KAAA,QAAAU,SAAM,cACNC,EAAAA,EAAAA,MAAA,UAAAD,SAAA,CAAqB,OAAZ6B,QAAY,IAAZA,OAAY,EAAZA,EAAcY,SAAS,UAAmB,OAAZZ,QAAY,IAAZA,OAAY,EAAZA,EAAcY,UAAW,EAAI,IAAM,UAE5ExC,EAAAA,EAAAA,MAAA,OAAKJ,UAAU,aAAYG,SAAA,EACzBV,EAAAA,EAAAA,KAAA,QAAAU,SAAM,iBACNC,EAAAA,EAAAA,MAAA,UAAAD,SAAA,CAAqB,OAAZ6B,QAAY,IAAZA,GAA6B,QAAjB8B,EAAZ9B,EAAcU,uBAAe,IAAAoB,OAAjB,EAAZA,EAA+BR,iBAAiB,cAE3DlD,EAAAA,EAAAA,MAAA,OAAKJ,UAAU,oBAAmBG,SAAA,EAChCV,EAAAA,EAAAA,KAAA,QAAAU,SAAM,YACNC,EAAAA,EAAAA,MAAA,OAAKJ,UAAU,eAAcG,SAAA,EAC3BC,EAAAA,EAAAA,MAAA,OAAKN,MAAM,KAAKuG,OAAO,KAAKiH,QAAQ,YAAYC,KAAK,OAAMpN,SAAA,EACzDV,EAAAA,EAAAA,KAAA,QAAM+N,EAAE,oBAAoBC,OAAO,eAAe/M,YAAY,IAAIgN,cAAc,QAAQE,eAAe,WACvGnO,EAAAA,EAAAA,KAAA,UAAQqO,GAAG,KAAKC,GAAG,KAAKC,EAAE,IAAIP,OAAO,eAAe/M,YAAY,SAC5D,sBAQdN,EAAAA,EAAAA,MAAA,OAAKJ,UAAU,gBAAeG,SAAA,EAC5BV,EAAAA,EAAAA,KAAA,MAAAU,SAAI,4CACJC,EAAAA,EAAAA,MAAA,OAAKJ,UAAU,gBAAeG,SAAA,EAC5BC,EAAAA,EAAAA,MAAA,OAAKJ,UAAU,eAAcG,SAAA,EAC3BC,EAAAA,EAAAA,MAAA,OAAKN,MAAM,KAAKuG,OAAO,KAAKiH,QAAQ,YAAYC,KAAK,OAAMpN,SAAA,EACzDV,EAAAA,EAAAA,KAAA,QAAM+N,EAAE,oBAAoBC,OAAO,eAAe/M,YAAY,IAAIgN,cAAc,QAAQE,eAAe,WACvGnO,EAAAA,EAAAA,KAAA,UAAQqO,GAAG,KAAKC,GAAG,KAAKC,EAAE,IAAIP,OAAO,eAAe/M,YAAY,UAElEjB,EAAAA,EAAAA,KAAA,QAAAU,SAAM,0BAERC,EAAAA,EAAAA,MAAA,OAAKJ,UAAU,eAAcG,SAAA,EAC3BC,EAAAA,EAAAA,MAAA,OAAKN,MAAM,KAAKuG,OAAO,KAAKiH,QAAQ,YAAYC,KAAK,OAAMpN,SAAA,EACzDV,EAAAA,EAAAA,KAAA,QAAM+N,EAAE,oBAAoBC,OAAO,eAAe/M,YAAY,IAAIgN,cAAc,QAAQE,eAAe,WACvGnO,EAAAA,EAAAA,KAAA,UAAQqO,GAAG,KAAKC,GAAG,KAAKC,EAAE,IAAIP,OAAO,eAAe/M,YAAY,UAElEjB,EAAAA,EAAAA,KAAA,QAAAU,SAAM,qBAERC,EAAAA,EAAAA,MAAA,OAAKJ,UAAU,eAAcG,SAAA,EAC3BC,EAAAA,EAAAA,MAAA,OAAKN,MAAM,KAAKuG,OAAO,KAAKiH,QAAQ,YAAYC,KAAK,OAAMpN,SAAA,EACzDV,EAAAA,EAAAA,KAAA,QAAM+N,EAAE,oBAAoBC,OAAO,eAAe/M,YAAY,IAAIgN,cAAc,QAAQE,eAAe,WACvGnO,EAAAA,EAAAA,KAAA,UAAQqO,GAAG,KAAKC,GAAG,KAAKC,EAAE,IAAIP,OAAO,eAAe/M,YAAY,UAElEjB,EAAAA,EAAAA,KAAA,QAAAU,SAAM,wBAERC,EAAAA,EAAAA,MAAA,OAAKJ,UAAU,eAAcG,SAAA,EAC3BC,EAAAA,EAAAA,MAAA,OAAKN,MAAM,KAAKuG,OAAO,KAAKiH,QAAQ,YAAYC,KAAK,OAAMpN,SAAA,EACzDV,EAAAA,EAAAA,KAAA,QAAM+N,EAAE,oBAAoBC,OAAO,eAAe/M,YAAY,IAAIgN,cAAc,QAAQE,eAAe,WACvGnO,EAAAA,EAAAA,KAAA,UAAQqO,GAAG,KAAKC,GAAG,KAAKC,EAAE,IAAIP,OAAO,eAAe/M,YAAY,UAElEjB,EAAAA,EAAAA,KAAA,QAAAU,SAAM,0BAERC,EAAAA,EAAAA,MAAA,OAAKJ,UAAU,eAAcG,SAAA,EAC3BC,EAAAA,EAAAA,MAAA,OAAKN,MAAM,KAAKuG,OAAO,KAAKiH,QAAQ,YAAYC,KAAK,OAAMpN,SAAA,EACzDV,EAAAA,EAAAA,KAAA,QAAM+N,EAAE,oBAAoBC,OAAO,eAAe/M,YAAY,IAAIgN,cAAc,QAAQE,eAAe,WACvGnO,EAAAA,EAAAA,KAAA,UAAQqO,GAAG,KAAKC,GAAG,KAAKC,EAAE,IAAIP,OAAO,eAAe/M,YAAY,UAElEjB,EAAAA,EAAAA,KAAA,QAAAU,SAAM,wBAERC,EAAAA,EAAAA,MAAA,OAAKJ,UAAU,eAAcG,SAAA,EAC3BC,EAAAA,EAAAA,MAAA,OAAKN,MAAM,KAAKuG,OAAO,KAAKiH,QAAQ,YAAYC,KAAK,OAAMpN,SAAA,EACzDV,EAAAA,EAAAA,KAAA,QAAM+N,EAAE,oBAAoBC,OAAO,eAAe/M,YAAY,IAAIgN,cAAc,QAAQE,eAAe,WACvGnO,EAAAA,EAAAA,KAAA,UAAQqO,GAAG,KAAKC,GAAG,KAAKC,EAAE,IAAIP,OAAO,eAAe/M,YAAY,UAElEjB,EAAAA,EAAAA,KAAA,QAAAU,SAAM,2BAMZC,EAAAA,EAAAA,MAAA,OAAKJ,UAAU,gBAAeG,SAAA,EAC5BC,EAAAA,EAAAA,MAAA,UACEJ,UAAU,cACVuB,QAASA,KACP+D,EAAyB,MACzBZ,GAAoB,GACpBsC,OAAO8C,SAASC,KAAO,WAAW,EAClC5J,SAAA,EAEFC,EAAAA,EAAAA,MAAA,OAAKN,MAAM,KAAKuG,OAAO,KAAKiH,QAAQ,YAAYC,KAAK,OAAMpN,SAAA,EACzDV,EAAAA,EAAAA,KAAA,QAAM+N,EAAE,+KAA+KC,OAAO,eAAe/M,YAAY,OACzNjB,EAAAA,EAAAA,KAAA,YAAUwO,OAAO,wBAAwBR,OAAO,eAAe/M,YAAY,SACvE,mBACW2E,EAAqB,IAAAvE,OAAOuE,EAAqB,MAAO,OAE3E5F,EAAAA,EAAAA,KAAA,UACEO,UAAU,gBACVuB,QAASA,KACP+D,EAAyB,MACzBZ,GAAoB,EAAM,EAC1BvE,SACH,sBAYXV,EAAAA,EAAAA,KAACyO,EAAAA,EAAuB,CACtBhQ,QAAS2G,EACT1G,QAASA,IAAM2G,GAA0B,GACzC1G,YAAa2D,EAAMoM,MAAKC,GAAKA,EAAE3L,OAAwB,OAAhBgD,QAAgB,IAAhBA,OAAgB,EAAhBA,EAAkBkG,gBAA+B,OAAhBlG,QAAgB,IAAhBA,OAAgB,EAAhBA,EAAkB3C,MAC1FzE,aAAcoH,EACdnH,KAAMA,KAIRmB,EAAAA,EAAAA,KAAC4O,EAAwB,CACvBnQ,QAAS6G,EACT5G,QAASA,IAAM6G,GAAoB,GACnClD,QAt3BwB5E,UAC9B8H,GAAoB,SACd7B,EAAiBnB,EAAa,EAq3B9B3D,aAAcoH,EACdnH,KAAMA,EACNyD,MAAOA,QAGP,C,0DCnkCV,QADuB,CAAE,KAAQ,CAAE,IAAO,MAAO,MAAS,CAAE,QAAW,gBAAiB,UAAa,SAAW,SAAY,CAAC,CAAE,IAAO,OAAQ,MAAS,CAAE,EAAK,mWAAuW,KAAQ,WAAY,MAAS,Y,cCM9hBnB,EAAmB,SAA0BnD,EAAOC,GACtD,OAAoBC,EAAAA,cAAoBC,EAAAA,GAAUC,EAAAA,EAAAA,GAAS,CAAC,EAAGJ,EAAO,CACpEC,IAAKA,EACLI,KAAMwQ,IAEV,EAIA,QAA4B3Q,EAAAA,WAAiBiD,E,wDCd7C,QAD0B,CAAE,KAAQ,CAAE,IAAO,MAAO,MAAS,CAAE,QAAW,gBAAiB,UAAa,SAAW,SAAY,CAAC,CAAE,IAAO,OAAQ,MAAS,CAAE,EAAK,kLAAqL,CAAE,IAAO,OAAQ,MAAS,CAAE,EAAK,yKAA6K,KAAQ,eAAgB,MAAS,Y,cCMjkBC,EAAsB,SAA6BpD,EAAOC,GAC5D,OAAoBC,EAAAA,cAAoBC,EAAAA,GAAUC,EAAAA,EAAAA,GAAS,CAAC,EAAGJ,EAAO,CACpEC,IAAKA,EACLI,KAAMyQ,IAEV,EAIA,QAA4B5Q,EAAAA,WAAiBkD,E,0DCd7C,QADoB,CAAE,KAAQ,CAAE,IAAO,MAAO,MAAS,CAAE,QAAW,gBAAiB,UAAa,SAAW,SAAY,CAAC,CAAE,IAAO,OAAQ,MAAS,CAAE,EAAK,wfAA4f,KAAQ,QAAS,MAAS,Y,cCM7qBR,EAAgB,SAAuB5C,EAAOC,GAChD,OAAoBC,EAAAA,cAAoBC,EAAAA,GAAUC,EAAAA,EAAAA,GAAS,CAAC,EAAGJ,EAAO,CACpEC,IAAKA,EACLI,KAAM0Q,IAEV,EAIA,QAA4B7Q,EAAAA,WAAiB0C,E", "sources": ["apicalls/plans.js", "../node_modules/@ant-design/icons-svg/es/asn/CheckCircleOutlined.js", "../node_modules/@ant-design/icons/es/icons/CheckCircleOutlined.js", "components/UpgradeRestrictionModal/UpgradeRestrictionModal.jsx", "../node_modules/@ant-design/icons-svg/es/asn/ExclamationCircleOutlined.js", "../node_modules/@ant-design/icons/es/icons/ExclamationCircleOutlined.js", "../node_modules/@ant-design/icons-svg/es/asn/RocketOutlined.js", "../node_modules/@ant-design/icons/es/icons/RocketOutlined.js", "../node_modules/@ant-design/icons-svg/es/asn/StarOutlined.js", "../node_modules/@ant-design/icons/es/icons/StarOutlined.js", "components/SubscriptionExpiredModal/SubscriptionExpiredModal.jsx", "pages/user/Subscription/index.js", "../node_modules/@ant-design/icons-svg/es/asn/CalendarOutlined.js", "../node_modules/@ant-design/icons/es/icons/CalendarOutlined.js", "../node_modules/@ant-design/icons-svg/es/asn/ClockCircleOutlined.js", "../node_modules/@ant-design/icons/es/icons/ClockCircleOutlined.js", "../node_modules/@ant-design/icons-svg/es/asn/CrownOutlined.js", "../node_modules/@ant-design/icons/es/icons/CrownOutlined.js"], "sourcesContent": ["const { default: axiosInstance } = require(\".\");\r\n\r\nexport const getPlans = async (payload) => {\r\n    try {\r\n      const response = await axiosInstance.get(\"/api/plans\", payload);\r\n      return response.data;\r\n    } catch (error) {\r\n      return error.response.data;\r\n    }\r\n  };", "// This icon file is generated automatically.\nvar CheckCircleOutlined = { \"icon\": { \"tag\": \"svg\", \"attrs\": { \"viewBox\": \"64 64 896 896\", \"focusable\": \"false\" }, \"children\": [{ \"tag\": \"path\", \"attrs\": { \"d\": \"M699 353h-46.9c-10.2 0-19.9 4.9-25.9 13.3L469 584.3l-71.2-98.8c-6-8.3-15.6-13.3-25.9-13.3H325c-6.5 0-10.3 7.4-6.5 12.7l124.6 172.8a31.8 31.8 0 0051.7 0l210.6-292c3.9-5.3.1-12.7-6.4-12.7z\" } }, { \"tag\": \"path\", \"attrs\": { \"d\": \"M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372z\" } }] }, \"name\": \"check-circle\", \"theme\": \"outlined\" };\nexport default CheckCircleOutlined;\n", "import _extends from \"@babel/runtime/helpers/esm/extends\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react';\nimport CheckCircleOutlinedSvg from \"@ant-design/icons-svg/es/asn/CheckCircleOutlined\";\nimport AntdIcon from \"../components/AntdIcon\";\nvar CheckCircleOutlined = function CheckCircleOutlined(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {\n    ref: ref,\n    icon: CheckCircleOutlinedSvg\n  }));\n};\nif (process.env.NODE_ENV !== 'production') {\n  CheckCircleOutlined.displayName = 'CheckCircleOutlined';\n}\nexport default /*#__PURE__*/React.forwardRef(CheckCircleOutlined);", "import React from 'react';\nimport { Modal, Button, Progress } from 'antd';\nimport { ClockCircleOutlined, CrownOutlined, CalendarOutlined, CheckCircleOutlined } from '@ant-design/icons';\nimport './UpgradeRestrictionModal.css';\n\nconst UpgradeRestrictionModal = ({ \n  visible, \n  onClose, \n  currentPlan, \n  subscription,\n  user \n}) => {\n  // Calculate days remaining\n  const calculateDaysRemaining = () => {\n    if (!subscription?.endDate) return 0;\n    const endDate = new Date(subscription.endDate);\n    const today = new Date();\n    const diffTime = endDate - today;\n    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));\n    return Math.max(0, diffDays);\n  };\n\n  // Calculate progress percentage\n  const calculateProgress = () => {\n    if (!subscription?.startDate || !subscription?.endDate) return 0;\n    const startDate = new Date(subscription.startDate);\n    const endDate = new Date(subscription.endDate);\n    const today = new Date();\n    \n    const totalDays = (endDate - startDate) / (1000 * 60 * 60 * 24);\n    const usedDays = (today - startDate) / (1000 * 60 * 60 * 24);\n    \n    return Math.min(100, Math.max(0, (usedDays / totalDays) * 100));\n  };\n\n  const daysRemaining = calculateDaysRemaining();\n  const progress = calculateProgress();\n  const planTitle = currentPlan?.title || subscription?.planTitle || 'Premium Plan';\n  const endDate = subscription?.endDate ? new Date(subscription.endDate).toLocaleDateString() : 'N/A';\n\n  return (\n    <Modal\n      open={visible}\n      onCancel={onClose}\n      footer={null}\n      width={500}\n      centered\n      className=\"upgrade-restriction-modal\"\n      maskStyle={{ backgroundColor: 'rgba(0, 0, 0, 0.7)' }}\n    >\n      <div className=\"upgrade-restriction-content\">\n        {/* Header with Crown Icon */}\n        <div className=\"modal-header\">\n          <div className=\"crown-icon\">\n            <CrownOutlined />\n          </div>\n          <h2 className=\"modal-title\">Already Premium Member!</h2>\n          <p className=\"modal-subtitle\">You're currently enjoying premium features</p>\n        </div>\n\n        {/* Current Plan Card */}\n        <div className=\"current-plan-card\">\n          <div className=\"plan-header\">\n            <div className=\"plan-icon\">\n              <CheckCircleOutlined />\n            </div>\n            <div className=\"plan-info\">\n              <h3 className=\"plan-name\">{planTitle}</h3>\n              <span className=\"plan-status\">Active Subscription</span>\n            </div>\n          </div>\n\n          {/* Progress Bar */}\n          <div className=\"progress-section\">\n            <div className=\"progress-header\">\n              <span className=\"progress-label\">Subscription Progress</span>\n              <span className=\"days-remaining\">{daysRemaining} days remaining</span>\n            </div>\n            <Progress \n              percent={progress} \n              strokeColor={{\n                '0%': '#52c41a',\n                '50%': '#faad14', \n                '100%': '#ff4d4f',\n              }}\n              trailColor=\"#f0f0f0\"\n              strokeWidth={8}\n              showInfo={false}\n            />\n          </div>\n\n          {/* Subscription Details */}\n          <div className=\"subscription-details\">\n            <div className=\"detail-item\">\n              <CalendarOutlined className=\"detail-icon\" />\n              <div className=\"detail-content\">\n                <span className=\"detail-label\">Expires On</span>\n                <span className=\"detail-value\">{endDate}</span>\n              </div>\n            </div>\n            <div className=\"detail-item\">\n              <ClockCircleOutlined className=\"detail-icon\" />\n              <div className=\"detail-content\">\n                <span className=\"detail-label\">Time Remaining</span>\n                <span className=\"detail-value\">\n                  {daysRemaining > 0 ? `${daysRemaining} days` : 'Expired'}\n                </span>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Message Section */}\n        <div className=\"message-section\">\n          <div className=\"message-card\">\n            <h4 className=\"message-title\">🎉 You're All Set!</h4>\n            <p className=\"message-text\">\n              You're currently enjoying all premium features with your <strong>{planTitle}</strong>. \n              To upgrade to a different plan, please wait until your current subscription expires.\n            </p>\n          </div>\n\n          <div className=\"benefits-list\">\n            <h5 className=\"benefits-title\">Your Current Benefits:</h5>\n            <ul className=\"benefits\">\n              {currentPlan?.features?.slice(0, 4).map((feature, index) => (\n                <li key={index} className=\"benefit-item\">\n                  <CheckCircleOutlined className=\"benefit-icon\" />\n                  {feature}\n                </li>\n              )) || [\n                <li key=\"1\" className=\"benefit-item\">\n                  <CheckCircleOutlined className=\"benefit-icon\" />\n                  Full access to all features\n                </li>,\n                <li key=\"2\" className=\"benefit-item\">\n                  <CheckCircleOutlined className=\"benefit-icon\" />\n                  Unlimited quizzes and practice\n                </li>,\n                <li key=\"3\" className=\"benefit-item\">\n                  <CheckCircleOutlined className=\"benefit-icon\" />\n                  AI chat assistance\n                </li>,\n                <li key=\"4\" className=\"benefit-item\">\n                  <CheckCircleOutlined className=\"benefit-icon\" />\n                  Premium study materials\n                </li>\n              ]}\n            </ul>\n          </div>\n        </div>\n\n        {/* Action Buttons */}\n        <div className=\"action-buttons\">\n          <Button \n            type=\"primary\" \n            size=\"large\" \n            onClick={onClose}\n            className=\"continue-button\"\n          >\n            Continue Learning\n          </Button>\n          <Button \n            type=\"default\" \n            size=\"large\" \n            onClick={onClose}\n            className=\"close-button\"\n          >\n            Close\n          </Button>\n        </div>\n\n        {/* Footer Note */}\n        <div className=\"footer-note\">\n          <p>\n            💡 <strong>Tip:</strong> You can upgrade to a different plan after your current subscription expires on <strong>{endDate}</strong>\n          </p>\n        </div>\n      </div>\n    </Modal>\n  );\n};\n\nexport default UpgradeRestrictionModal;\n", "// This icon file is generated automatically.\nvar ExclamationCircleOutlined = { \"icon\": { \"tag\": \"svg\", \"attrs\": { \"viewBox\": \"64 64 896 896\", \"focusable\": \"false\" }, \"children\": [{ \"tag\": \"path\", \"attrs\": { \"d\": \"M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372z\" } }, { \"tag\": \"path\", \"attrs\": { \"d\": \"M464 688a48 48 0 1096 0 48 48 0 10-96 0zm24-112h48c4.4 0 8-3.6 8-8V296c0-4.4-3.6-8-8-8h-48c-4.4 0-8 3.6-8 8v272c0 4.4 3.6 8 8 8z\" } }] }, \"name\": \"exclamation-circle\", \"theme\": \"outlined\" };\nexport default ExclamationCircleOutlined;\n", "import _extends from \"@babel/runtime/helpers/esm/extends\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react';\nimport ExclamationCircleOutlinedSvg from \"@ant-design/icons-svg/es/asn/ExclamationCircleOutlined\";\nimport AntdIcon from \"../components/AntdIcon\";\nvar ExclamationCircleOutlined = function ExclamationCircleOutlined(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {\n    ref: ref,\n    icon: ExclamationCircleOutlinedSvg\n  }));\n};\nif (process.env.NODE_ENV !== 'production') {\n  ExclamationCircleOutlined.displayName = 'ExclamationCircleOutlined';\n}\nexport default /*#__PURE__*/React.forwardRef(ExclamationCircleOutlined);", "// This icon file is generated automatically.\nvar RocketOutlined = { \"icon\": { \"tag\": \"svg\", \"attrs\": { \"viewBox\": \"64 64 896 896\", \"focusable\": \"false\" }, \"children\": [{ \"tag\": \"path\", \"attrs\": { \"d\": \"M864 736c0-111.6-65.4-208-160-252.9V317.3c0-15.1-5.3-29.7-15.1-41.2L536.5 95.4C530.1 87.8 521 84 512 84s-18.1 3.8-24.5 11.4L335.1 276.1a63.97 63.97 0 00-15.1 41.2v165.8C225.4 528 160 624.4 160 736h156.5c-2.3 7.2-3.5 15-3.5 23.8 0 22.1 7.6 43.7 21.4 60.8a97.2 97.2 0 0043.1 30.6c23.1 54 75.6 88.8 134.5 88.8 29.1 0 57.3-8.6 81.4-24.8 23.6-15.8 41.9-37.9 53-64a97 97 0 0043.1-30.5 97.52 97.52 0 0021.4-60.8c0-8.4-1.1-16.4-3.1-23.8H864zM762.3 621.4c9.4 14.6 17 30.3 22.5 46.6H700V558.7a211.6 211.6 0 0162.3 62.7zM388 483.1V318.8l124-147 124 147V668H388V483.1zM239.2 668c5.5-16.3 13.1-32 22.5-46.6 16.3-25.2 37.5-46.5 62.3-62.7V668h-84.8zm388.9 116.2c-5.2 3-11.2 4.2-17.1 3.4l-19.5-2.4-2.8 19.4c-5.4 37.9-38.4 66.5-76.7 66.5-38.3 0-71.3-28.6-76.7-66.5l-2.8-19.5-19.5 2.5a27.7 27.7 0 01-17.1-3.5c-8.7-5-14.1-14.3-14.1-24.4 0-10.6 5.9-19.4 14.6-23.8h231.3c8.8 4.5 14.6 13.3 14.6 23.8-.1 10.2-5.5 19.6-14.2 24.5zM464 400a48 48 0 1096 0 48 48 0 10-96 0z\" } }] }, \"name\": \"rocket\", \"theme\": \"outlined\" };\nexport default RocketOutlined;\n", "import _extends from \"@babel/runtime/helpers/esm/extends\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react';\nimport RocketOutlinedSvg from \"@ant-design/icons-svg/es/asn/RocketOutlined\";\nimport AntdIcon from \"../components/AntdIcon\";\nvar RocketOutlined = function RocketOutlined(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {\n    ref: ref,\n    icon: RocketOutlinedSvg\n  }));\n};\nif (process.env.NODE_ENV !== 'production') {\n  RocketOutlined.displayName = 'RocketOutlined';\n}\nexport default /*#__PURE__*/React.forwardRef(RocketOutlined);", "// This icon file is generated automatically.\nvar StarOutlined = { \"icon\": { \"tag\": \"svg\", \"attrs\": { \"viewBox\": \"64 64 896 896\", \"focusable\": \"false\" }, \"children\": [{ \"tag\": \"path\", \"attrs\": { \"d\": \"M908.1 353.1l-253.9-36.9L540.7 86.1c-3.1-6.3-8.2-11.4-14.5-14.5-15.8-7.8-35-1.3-42.9 14.5L369.8 316.2l-253.9 36.9c-7 1-13.4 4.3-18.3 9.3a32.05 32.05 0 00.6 45.3l183.7 179.1-43.4 252.9a31.95 31.95 0 0046.4 33.7L512 754l227.1 119.4c6.2 3.3 13.4 4.4 20.3 3.2 17.4-3 29.1-19.5 26.1-36.9l-43.4-252.9 183.7-179.1c5-4.9 8.3-11.3 9.3-18.3 2.7-17.5-9.5-33.7-27-36.3zM664.8 561.6l36.1 210.3L512 672.7 323.1 772l36.1-210.3-152.8-149L417.6 382 512 190.7 606.4 382l211.2 30.7-152.8 148.9z\" } }] }, \"name\": \"star\", \"theme\": \"outlined\" };\nexport default StarOutlined;\n", "import _extends from \"@babel/runtime/helpers/esm/extends\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react';\nimport StarOutlinedSvg from \"@ant-design/icons-svg/es/asn/StarOutlined\";\nimport AntdIcon from \"../components/AntdIcon\";\nvar StarOutlined = function StarOutlined(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {\n    ref: ref,\n    icon: StarOutlinedSvg\n  }));\n};\nif (process.env.NODE_ENV !== 'production') {\n  StarOutlined.displayName = 'StarOutlined';\n}\nexport default /*#__PURE__*/React.forwardRef(StarOutlined);", "import React, { useState } from 'react';\nimport { Modal, Button, Progress } from 'antd';\nimport {\n  ClockCircleOutlined,\n  CrownOutlined,\n  CalendarOutlined,\n  ExclamationCircleOutlined,\n  RocketOutlined,\n  StarOutlined\n} from '@ant-design/icons';\nimport './SubscriptionExpiredModal.css';\n\nconst SubscriptionExpiredModal = ({ \n  visible, \n  onClose, \n  onRenew,\n  subscription,\n  user,\n  plans = []\n}) => {\n  const [selectedPlan, setSelectedPlan] = useState(null);\n  const [showPlans, setShowPlans] = useState(false);\n\n  // Calculate how long ago the subscription expired\n  const calculateExpiredDays = () => {\n    if (!subscription?.endDate) return 0;\n    const endDate = new Date(subscription.endDate);\n    const today = new Date();\n    const diffTime = today - endDate;\n    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));\n    return Math.max(0, diffDays);\n  };\n\n  const expiredDays = calculateExpiredDays();\n  const planTitle = subscription?.planTitle || 'Premium Plan';\n  const endDate = subscription?.endDate ? new Date(subscription.endDate).toLocaleDateString() : 'N/A';\n\n  // Sample plans if none provided\n  const defaultPlans = [\n    {\n      _id: 'premium-plan',\n      title: 'Premium Plan',\n      discountedPrice: 13000,\n      actualPrice: 30000,\n      duration: 3,\n      features: ['3-months full access', 'Unlimited quizzes', 'AI chat assistance', 'Premium study materials', 'Priority support']\n    }\n  ];\n\n  const availablePlans = plans.length > 0 ? plans : defaultPlans;\n\n  const handlePlanSelect = (plan) => {\n    setSelectedPlan(plan);\n  };\n\n  const handleRenewSubscription = () => {\n    if (selectedPlan && onRenew) {\n      onRenew(selectedPlan);\n    }\n  };\n\n  const handleShowPlans = () => {\n    setShowPlans(true);\n  };\n\n  return (\n    <Modal\n      open={visible}\n      onCancel={onClose}\n      footer={null}\n      width={600}\n      centered\n      className=\"subscription-expired-modal\"\n      maskStyle={{ backgroundColor: 'rgba(0, 0, 0, 0.8)' }}\n      closable={false}\n    >\n      <div className=\"expired-modal-content\">\n        {!showPlans ? (\n          // Expiration Notice\n          <div className=\"expiration-notice\">\n            {/* Header with Warning Icon */}\n            <div className=\"modal-header expired-header\">\n              <div className=\"warning-icon\">\n                <ExclamationCircleOutlined />\n              </div>\n              <h2 className=\"modal-title\">Subscription Expired!</h2>\n              <p className=\"modal-subtitle\">Your premium access has ended</p>\n            </div>\n\n            {/* Expired Plan Card */}\n            <div className=\"expired-plan-card\">\n              <div className=\"plan-header\">\n                <div className=\"plan-icon expired-icon\">\n                  <ClockCircleOutlined />\n                </div>\n                <div className=\"plan-info\">\n                  <h3 className=\"plan-name\">{planTitle}</h3>\n                  <span className=\"plan-status expired-status\">Expired</span>\n                </div>\n              </div>\n\n              {/* Expiration Details */}\n              <div className=\"expiration-details\">\n                <div className=\"detail-item\">\n                  <CalendarOutlined className=\"detail-icon\" />\n                  <div className=\"detail-content\">\n                    <span className=\"detail-label\">Expired On</span>\n                    <span className=\"detail-value\">{endDate}</span>\n                  </div>\n                </div>\n                <div className=\"detail-item\">\n                  <ClockCircleOutlined className=\"detail-icon\" />\n                  <div className=\"detail-content\">\n                    <span className=\"detail-label\">Days Since Expiration</span>\n                    <span className=\"detail-value\">\n                      {expiredDays === 0 ? 'Today' : `${expiredDays} days ago`}\n                    </span>\n                  </div>\n                </div>\n              </div>\n\n              {/* Progress Bar (Full - Expired) */}\n              <div className=\"progress-section\">\n                <div className=\"progress-header\">\n                  <span className=\"progress-label\">Subscription Status</span>\n                  <span className=\"expired-badge\">Expired</span>\n                </div>\n                <Progress \n                  percent={100} \n                  strokeColor=\"#ff4d4f\"\n                  trailColor=\"#f0f0f0\"\n                  strokeWidth={8}\n                  showInfo={false}\n                />\n              </div>\n            </div>\n\n            {/* Message Section */}\n            <div className=\"message-section\">\n              <div className=\"message-card expired-message\">\n                <h4 className=\"message-title\">⏰ Time to Renew!</h4>\n                <p className=\"message-text\">\n                  Your <strong>{planTitle}</strong> subscription expired on <strong>{endDate}</strong>. \n                  To continue enjoying all premium features, please choose a new subscription plan.\n                </p>\n              </div>\n\n              <div className=\"restricted-access\">\n                <h5 className=\"restricted-title\">🚫 Currently Restricted:</h5>\n                <ul className=\"restricted-list\">\n                  <li>❌ Quiz access</li>\n                  <li>❌ AI chat assistance</li>\n                  <li>❌ Premium study materials</li>\n                  <li>❌ Progress tracking</li>\n                </ul>\n              </div>\n            </div>\n\n            {/* Action Buttons */}\n            <div className=\"action-buttons\">\n              <Button \n                type=\"primary\" \n                size=\"large\" \n                onClick={handleShowPlans}\n                className=\"renew-button\"\n                icon={<RocketOutlined />}\n              >\n                Choose New Plan\n              </Button>\n              <Button \n                type=\"default\" \n                size=\"large\" \n                onClick={onClose}\n                className=\"later-button\"\n              >\n                Maybe Later\n              </Button>\n            </div>\n\n            {/* Footer Note */}\n            <div className=\"footer-note\">\n              <p>\n                💡 <strong>Good News:</strong> You can access your profile and subscription settings anytime to renew!\n              </p>\n            </div>\n          </div>\n        ) : (\n          // Plan Selection\n          <div className=\"plan-selection\">\n            <div className=\"modal-header\">\n              <div className=\"crown-icon\">\n                <CrownOutlined />\n              </div>\n              <h2 className=\"modal-title\">Choose Your Plan</h2>\n              <p className=\"modal-subtitle\">Select a plan to continue your learning journey</p>\n            </div>\n\n            <div className=\"plans-grid\">\n              {availablePlans.map((plan) => (\n                <div \n                  key={plan._id} \n                  className={`plan-card ${selectedPlan?._id === plan._id ? 'selected' : ''}`}\n                  onClick={() => handlePlanSelect(plan)}\n                >\n                  <div className=\"plan-header\">\n                    <h3 className=\"plan-title\">{plan.title}</h3>\n                    {plan.title?.toLowerCase().includes('standard') && (\n                      <span className=\"plan-badge\">🔥 Popular</span>\n                    )}\n                  </div>\n\n                  <div className=\"plan-pricing\">\n                    <div className=\"price-main\">\n                      <span className=\"currency\">TZS</span>\n                      <span className=\"amount\">{plan.discountedPrice?.toLocaleString()}</span>\n                    </div>\n                    {plan.actualPrice > plan.discountedPrice && (\n                      <div className=\"price-original\">\n                        <span className=\"original-price\">TZS {plan.actualPrice?.toLocaleString()}</span>\n                        <span className=\"discount\">\n                          {Math.round(((plan.actualPrice - plan.discountedPrice) / plan.actualPrice) * 100)}% OFF\n                        </span>\n                      </div>\n                    )}\n                    <div className=\"duration\">{plan.duration} month{plan.duration > 1 ? 's' : ''}</div>\n                  </div>\n\n                  <div className=\"plan-features\">\n                    {plan.features?.slice(0, 4).map((feature, index) => (\n                      <div key={index} className=\"feature-item\">\n                        <StarOutlined className=\"feature-icon\" />\n                        {feature}\n                      </div>\n                    ))}\n                  </div>\n\n                  {selectedPlan?._id === plan._id && (\n                    <div className=\"selected-indicator\">\n                      ✓ Selected\n                    </div>\n                  )}\n                </div>\n              ))}\n            </div>\n\n            <div className=\"action-buttons\">\n              <Button \n                type=\"primary\" \n                size=\"large\" \n                onClick={handleRenewSubscription}\n                disabled={!selectedPlan}\n                className=\"continue-button\"\n                icon={<RocketOutlined />}\n              >\n                Continue with {selectedPlan?.title || 'Selected Plan'}\n              </Button>\n              <Button \n                type=\"default\" \n                size=\"large\" \n                onClick={() => setShowPlans(false)}\n                className=\"back-button\"\n              >\n                ← Back\n              </Button>\n            </div>\n          </div>\n        )}\n      </div>\n    </Modal>\n  );\n};\n\nexport default SubscriptionExpiredModal;\n", "import React, { useState, useEffect } from 'react';\nimport { useSelector, useDispatch } from 'react-redux';\nimport { motion } from 'framer-motion';\nimport { message } from 'antd';\nimport { FaCrown, FaCalendarAlt, FaCheckCircle, FaTimesCircle, FaCreditCard, FaUser } from 'react-icons/fa';\nimport { getPlans } from '../../../apicalls/plans';\nimport { addPayment, checkPaymentStatus } from '../../../apicalls/payment';\nimport { ShowLoading, HideLoading } from '../../../redux/loaderSlice';\nimport UpgradeRestrictionModal from '../../../components/UpgradeRestrictionModal/UpgradeRestrictionModal';\nimport SubscriptionExpiredModal from '../../../components/SubscriptionExpiredModal/SubscriptionExpiredModal';\nimport './Subscription.css';\n\nconst Subscription = () => {\n  const [plans, setPlans] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [paymentLoading, setPaymentLoading] = useState(null); // Changed to store plan ID instead of boolean\n  const [showProcessingModal, setShowProcessingModal] = useState(false);\n\n  // Debug: Log showProcessingModal state changes\n  useEffect(() => {\n    console.log('🔍 showProcessingModal state changed to:', showProcessingModal);\n  }, [showProcessingModal]);\n  const [showSuccessModal, setShowSuccessModal] = useState(false);\n  const [selectedPlan, setSelectedPlan] = useState(null);\n  const [paymentStatus, setPaymentStatus] = useState('');\n  const [showUpgradeRestriction, setShowUpgradeRestriction] = useState(false);\n  const [showExpiredModal, setShowExpiredModal] = useState(false);\n  const [processingStartTime, setProcessingStartTime] = useState(null);\n  const [showTryAgain, setShowTryAgain] = useState(false);\n  const [autoNavigateCountdown, setAutoNavigateCountdown] = useState(null);\n  const { user } = useSelector((state) => state.user);\n  const { subscriptionData } = useSelector((state) => state.subscription);\n  const dispatch = useDispatch();\n\n  // Fallback sample plans in case API fails\n  const samplePlans = [\n    {\n      _id: \"basic-plan-sample\",\n      title: \"Basic Membership\",\n      features: [\n        \"2-month full access\",\n        \"Unlimited quizzes\",\n        \"Personalized profile\",\n        \"AI chat for instant help\",\n        \"Forum for student discussions\",\n        \"Study notes\",\n        \"Past papers\",\n        \"Books\",\n        \"Learning videos\",\n        \"Track progress with rankings\"\n      ],\n      actualPrice: 28570,\n      discountedPrice: 20000,\n      discountPercentage: 30,\n      duration: 2,\n      status: true\n    },\n    {\n      _id: \"premium-plan-sample\",\n      title: \"Premium Plan\",\n      features: [\n        \"3-month full access\",\n        \"Unlimited quizzes\",\n        \"Personalized profile\",\n        \"AI chat for instant help\",\n        \"Forum for student discussions\",\n        \"Study notes\",\n        \"Past papers\",\n        \"Books\",\n        \"Learning videos\",\n        \"Track progress with rankings\",\n        \"Priority support\"\n      ],\n      actualPrice: 45000,\n      discountedPrice: 35000,\n      discountPercentage: 22,\n      duration: 3,\n      status: true\n    }\n  ];\n\n  useEffect(() => {\n    fetchPlans();\n    checkCurrentSubscription();\n  }, []);\n\n  // Enable background scrolling when modals are open for better UX\n  useEffect(() => {\n    // Always allow background scrolling - remove any scroll restrictions\n    document.body.style.overflow = '';\n    document.body.style.position = '';\n    document.body.style.width = '';\n    document.body.style.height = '';\n\n    // Cleanup on unmount\n    return () => {\n      document.body.style.overflow = '';\n      document.body.style.position = '';\n      document.body.style.width = '';\n      document.body.style.height = '';\n    };\n  }, [showProcessingModal, showSuccessModal]);\n\n  // Enhanced scroll detection for modal content\n  useEffect(() => {\n    const detectScrollableContent = () => {\n      const modalContents = document.querySelectorAll('.modal-content');\n      modalContents.forEach(content => {\n        if (content.scrollHeight > content.clientHeight) {\n          content.classList.add('has-scroll');\n        } else {\n          content.classList.remove('has-scroll');\n        }\n      });\n    };\n\n    // Detect on modal open\n    if (showProcessingModal || showSuccessModal) {\n      // Small delay to ensure modal is rendered\n      setTimeout(detectScrollableContent, 100);\n\n      // Re-detect on window resize\n      window.addEventListener('resize', detectScrollableContent);\n\n      return () => {\n        window.removeEventListener('resize', detectScrollableContent);\n      };\n    }\n  }, [showProcessingModal, showSuccessModal]);\n\n  // Check for expired subscription and show modal\n  useEffect(() => {\n    if (subscriptionData && isSubscriptionExpired()) {\n      console.log('🚫 Subscription expired, showing modal');\n      setShowExpiredModal(true);\n    } else {\n      setShowExpiredModal(false);\n    }\n  }, [subscriptionData]);\n\n  const fetchPlans = async () => {\n    try {\n      setLoading(true);\n      console.log('Fetching plans...');\n      const response = await getPlans();\n      console.log('Plans response:', response);\n\n      if (response.success && response.data && response.data.length > 0) {\n        setPlans(response.data);\n        console.log('Plans loaded successfully from API:', response.data);\n      } else if (Array.isArray(response) && response.length > 0) {\n        // Handle case where response is directly an array of plans\n        setPlans(response);\n        console.log('Plans loaded as array from API:', response);\n      } else {\n        console.warn('No plans from API, using sample plans');\n        setPlans(samplePlans);\n        message.info('Showing sample plans. Please check your connection.');\n      }\n    } catch (error) {\n      console.error('Error loading plans from API:', error);\n      console.log('Using fallback sample plans');\n      setPlans(samplePlans);\n      message.warning('Using sample plans. Please check your connection and try again.');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const checkCurrentSubscription = async () => {\n    try {\n      const response = await checkPaymentStatus();\n      console.log('Current subscription:', response);\n    } catch (error) {\n      console.log('No active subscription found');\n    }\n  };\n\n  // Check if subscription is expired\n  const isSubscriptionExpired = () => {\n    if (!subscriptionData) return true;\n\n    // If no subscription data, consider expired\n    if (!subscriptionData.endDate) return true;\n\n    // If payment status is not paid, consider expired\n    if (subscriptionData.paymentStatus !== 'paid') return true;\n\n    // If status is not active, consider expired\n    if (subscriptionData.status !== 'active') return true;\n\n    // Check if end date has passed\n    const endDate = new Date(subscriptionData.endDate);\n    const today = new Date();\n    today.setHours(0, 0, 0, 0); // Reset time to start of day\n    endDate.setHours(0, 0, 0, 0); // Reset time to start of day\n\n    return endDate < today;\n  };\n\n  // Handle subscription renewal from expired modal\n  const handleRenewSubscription = async (selectedPlan) => {\n    setShowExpiredModal(false);\n    await handlePlanSelect(selectedPlan);\n  };\n\n  // Handle closing payment processing modal\n  const handleCloseProcessingModal = () => {\n    setShowProcessingModal(false);\n    setPaymentLoading(null); // Reset to null instead of false\n    setShowTryAgain(false);\n    setProcessingStartTime(null);\n    setPaymentStatus('');\n    message.info('Payment process cancelled. You can try again anytime.');\n  };\n\n  // Handle try again functionality\n  const handleTryAgain = () => {\n    if (selectedPlan) {\n      setShowTryAgain(false);\n      setProcessingStartTime(null);\n      handlePlanSelect(selectedPlan);\n    }\n  };\n\n  // Test success modal (for debugging)\n  const testSuccessModal = () => {\n    console.log('🧪 Testing success modal...');\n    setShowProcessingModal(false);\n    setShowSuccessModal(true);\n    setPaymentLoading(null);\n  };\n\n  // Test processing modal (for debugging)\n  const testProcessingModal = () => {\n    console.log('🧪 Testing processing modal...');\n    setShowProcessingModal(true);\n    setPaymentStatus('Testing processing modal...');\n    setSelectedPlan(plans[0] || { title: 'Test Plan', discountedPrice: 5000, duration: 1 });\n  };\n\n  const handlePlanSelect = async (plan) => {\n    // Check if user already has an active subscription\n    if (subscriptionData && subscriptionData.status === 'active' && subscriptionData.paymentStatus === 'paid') {\n      console.log('🚫 User already has active subscription:', subscriptionData);\n      setShowUpgradeRestriction(true);\n      return;\n    }\n\n    if (!user.phoneNumber || !/^(06|07)\\d{8}$/.test(user.phoneNumber)) {\n      message.error('Please update your phone number in your profile before subscribing');\n      return;\n    }\n\n    console.log('🎯 Modal positioned optimally for best UX');\n\n    try {\n      console.log('🚀 Starting payment for plan:', plan.title);\n      console.log('🔧 IMMEDIATELY showing processing modal...');\n\n      // IMMEDIATELY show processing modal when user chooses plan\n      setSelectedPlan(plan);\n      setPaymentLoading(plan._id);\n      setShowProcessingModal(true);\n      setShowTryAgain(false);\n      setProcessingStartTime(Date.now());\n      setPaymentStatus('🚀 Preparing your payment request...');\n\n      console.log('✅ Processing modal IMMEDIATELY displayed');\n\n      // Small delay to ensure modal is visible before API call\n      await new Promise(resolve => setTimeout(resolve, 200));\n\n      // Set timer for try again button (10 seconds)\n      setTimeout(() => {\n        setShowTryAgain(true);\n      }, 10000);\n\n      const paymentData = {\n        plan: plan,\n        userId: user._id,\n        userPhone: user.phoneNumber,\n        userEmail: user.email || `${user.name?.replace(/\\s+/g, '').toLowerCase()}@brainwave.temp`\n      };\n\n      setPaymentStatus('📤 Sending payment request to ZenoPay...');\n      const response = await addPayment(paymentData);\n\n      if (response.success) {\n        setPaymentStatus('Payment sent! Check your phone for SMS confirmation...');\n\n        console.log('💳 Payment response:', response);\n        console.log('🆔 Order ID:', response.order_id);\n\n        // Show confirmation message to user\n        message.success({\n          content: `💳 Payment initiated! 📱 Check your phone (${user.phoneNumber}) for SMS confirmation from ZenoPay.`,\n          duration: 8,\n          style: {\n            marginTop: '20vh',\n          }\n        });\n\n        // Start checking payment status immediately\n        const orderIdToCheck = response.order_id || response.data?.order_id || 'demo_order';\n        console.log('🔍 Starting payment confirmation check for order:', orderIdToCheck);\n\n        checkPaymentConfirmation(orderIdToCheck);\n\n      } else {\n        throw new Error(response.message || 'Payment failed');\n      }\n    } catch (error) {\n      console.error('❌ Payment failed:', error);\n      setShowProcessingModal(false);\n      message.error('Payment failed: ' + error.message);\n      setPaymentLoading(null); // Reset to null\n    }\n  };\n\n  const checkPaymentConfirmation = async (orderId) => {\n    console.log('🚀 Starting payment confirmation check for order:', orderId);\n    let isPolling = true;\n    let handleVisibilityChange;\n\n    try {\n      setPaymentStatus('📱 Complete the payment on your phone, we\\'ll detect it automatically...');\n\n      // Poll payment status every 2 seconds for optimal responsiveness\n      let attempts = 0;\n      const maxAttempts = 150; // 150 attempts * 2 seconds = 5 minutes\n\n      const pollPaymentStatus = async () => {\n        attempts++;\n        console.log(`🔍 Payment status check attempt ${attempts}/${maxAttempts} for order:`, orderId);\n\n        try {\n          const statusResponse = await checkPaymentStatus({ orderId });\n          console.log('📊 Payment status response:', statusResponse);\n          console.log('🔍 Checking payment conditions:');\n          console.log('  - Live payment:', statusResponse?.paymentStatus === 'paid' && statusResponse?.status === 'active');\n          console.log('  - Demo payment:', statusResponse?.status === 'completed' && statusResponse?.success === true);\n\n          if (statusResponse && (\n            (statusResponse.paymentStatus === 'paid' && statusResponse.status === 'active') ||\n            (statusResponse.status === 'completed' && statusResponse.success === true)\n          )) {\n            // Payment confirmed immediately!\n            isPolling = false; // Stop polling\n            if (handleVisibilityChange) {\n              document.removeEventListener('visibilitychange', handleVisibilityChange); // Clean up listener\n            }\n\n            setPaymentStatus('🎉 Payment confirmed! Activating your subscription...');\n            console.log('✅ Payment confirmed, preparing to show success modal...');\n\n            // Show success INSTANTLY - no delay\n            console.log('🔄 Setting modal states - Processing: false, Success: true');\n            setShowProcessingModal(false);\n            setShowSuccessModal(true);\n            setPaymentLoading(null);\n            console.log('✅ Success modal state set to true');\n\n            // Refresh subscription data\n            checkCurrentSubscription();\n\n            // Show immediate success message\n            message.success({\n              content: '🎉 Payment confirmed! All features are now unlocked!',\n              duration: 5,\n              style: {\n                marginTop: '20vh',\n                fontSize: '16px'\n              }\n            });\n\n            // Start countdown for auto-navigation to hub\n            setAutoNavigateCountdown(5);\n            const countdownInterval = setInterval(() => {\n              setAutoNavigateCountdown(prev => {\n                if (prev <= 1) {\n                  clearInterval(countdownInterval);\n                  console.log('🏠 Auto-navigating to hub after successful payment...');\n                  setShowSuccessModal(false);\n                  window.location.href = '/user/hub';\n                  return null;\n                }\n                return prev - 1;\n              });\n            }, 1000);\n\n          } else if (attempts >= maxAttempts) {\n            // Timeout - but don't fail completely\n            isPolling = false; // Stop polling\n            if (handleVisibilityChange) {\n              document.removeEventListener('visibilitychange', handleVisibilityChange); // Clean up listener\n            }\n\n            setPaymentStatus('⏰ Still waiting for confirmation. Please complete the payment on your phone.');\n\n            setTimeout(() => {\n              setShowProcessingModal(false);\n              setPaymentLoading(null); // Reset to null\n              message.warning('Payment confirmation is taking longer than expected. Please check your subscription status or try again.');\n            }, 2000);\n\n          } else {\n            // Continue polling - NO TIME INDICATION, just encouraging message\n            setPaymentStatus('📱 Complete the payment on your phone, we\\'ll detect it automatically...');\n            setTimeout(pollPaymentStatus, 2000); // Check every 2 seconds for better performance\n          }\n\n        } catch (error) {\n          console.error('Payment status check error:', error);\n\n          // Handle specific error types\n          if (error.message && error.message.includes('404')) {\n            console.error('❌ Payment status endpoint not found (404)');\n            isPolling = false; // Stop polling\n            if (handleVisibilityChange) {\n              document.removeEventListener('visibilitychange', handleVisibilityChange);\n            }\n            setShowProcessingModal(false);\n            setPaymentLoading(null); // Reset to null\n            message.error('Payment verification service is temporarily unavailable. Please contact support or check your subscription status manually.');\n            return;\n          }\n\n          if (error.message && error.message.includes('401')) {\n            console.error('❌ Authentication required for payment status check');\n            isPolling = false; // Stop polling\n            if (handleVisibilityChange) {\n              document.removeEventListener('visibilitychange', handleVisibilityChange);\n            }\n            setShowProcessingModal(false);\n            setPaymentLoading(null); // Reset to null\n            message.error('Please login again to check payment status.');\n            return;\n          }\n\n          if (attempts >= maxAttempts) {\n            isPolling = false; // Stop polling\n            if (handleVisibilityChange) {\n              document.removeEventListener('visibilitychange', handleVisibilityChange); // Clean up listener\n            }\n            setShowProcessingModal(false);\n            setPaymentLoading(null); // Reset to null\n            message.error('Unable to confirm payment status. Please check your subscription status manually.');\n          } else {\n            // Continue polling even if there's an error (unless it's a critical error)\n            setTimeout(pollPaymentStatus, 1000);\n          }\n        }\n      };\n\n      // Add visibility change listener to check immediately when user returns to tab\n      handleVisibilityChange = () => {\n        if (!document.hidden && isPolling) {\n          console.log('User returned to tab, checking payment status immediately...');\n          setPaymentStatus('🔍 Checking payment status...');\n          // Trigger immediate check\n          setTimeout(() => pollPaymentStatus(), 100);\n        }\n      };\n\n      document.addEventListener('visibilitychange', handleVisibilityChange);\n\n      // Start polling immediately (no delay) - check right away\n      setTimeout(pollPaymentStatus, 500); // Start checking after 0.5 seconds\n\n    } catch (error) {\n      isPolling = false; // Stop polling\n      if (handleVisibilityChange) {\n        document.removeEventListener('visibilitychange', handleVisibilityChange); // Clean up listener\n      }\n      setShowProcessingModal(false);\n      message.error('Payment confirmation failed: ' + error.message);\n      setPaymentLoading(null); // Reset to null\n    }\n  };\n\n  const getSubscriptionStatus = () => {\n    if (subscriptionData && subscriptionData.paymentStatus === 'paid' && subscriptionData.status === 'active') {\n      const endDate = new Date(subscriptionData.endDate);\n      const now = new Date();\n      if (endDate > now) {\n        return 'active';\n      }\n    }\n    \n    if (user?.subscriptionStatus === 'expired' || (subscriptionData && subscriptionData.status === 'expired')) {\n      return 'expired';\n    }\n    \n    return 'none';\n  };\n\n  const formatDate = (dateString) => {\n    if (!dateString) return 'N/A';\n    return new Date(dateString).toLocaleDateString('en-US', {\n      year: 'numeric',\n      month: 'long',\n      day: 'numeric'\n    });\n  };\n\n  const getDaysRemaining = () => {\n    if (!subscriptionData?.endDate) return 0;\n    const endDate = new Date(subscriptionData.endDate);\n    const now = new Date();\n    const diffTime = endDate - now;\n    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));\n    return Math.max(0, diffDays);\n  };\n\n  const subscriptionStatus = getSubscriptionStatus();\n\n  return (\n    <div className=\"subscription-page\">\n      <div className=\"subscription-container\">\n        {/* Header */}\n        <motion.div\n          initial={{ opacity: 0, y: -20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.6 }}\n          className=\"subscription-header\"\n        >\n          {/* Debug button - remove in production */}\n          <button\n            onClick={() => {\n              console.log('🧪 Testing success modal...');\n              setSelectedPlan(plans[0] || { title: 'Test Plan', duration: 1, discountedPrice: 13000 });\n              setShowSuccessModal(true);\n            }}\n            style={{\n              position: 'fixed',\n              top: '10px',\n              right: '10px',\n              background: '#52c41a',\n              color: 'white',\n              border: 'none',\n              padding: '8px 16px',\n              borderRadius: '4px',\n              cursor: 'pointer',\n              zIndex: 9999\n            }}\n          >\n            🧪 Test Success Modal\n          </button>\n          <h1 className=\"page-title\">\n            <FaCrown className=\"title-icon\" />\n            Subscription Management\n          </h1>\n          <p className=\"page-subtitle\">Manage your subscription and access premium features</p>\n        </motion.div>\n\n        {/* Current Subscription Status */}\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.6, delay: 0.2 }}\n          className=\"current-subscription\"\n        >\n          <h2 className=\"section-title\">Current Subscription</h2>\n          \n          {subscriptionStatus === 'active' && (\n            <div className=\"subscription-card active\">\n              <div className=\"subscription-status\">\n                <FaCheckCircle className=\"status-icon active\" />\n                <span className=\"status-text\">Active Subscription</span>\n              </div>\n              <div className=\"subscription-details\">\n                <div className=\"detail-item\">\n                  <FaCrown className=\"detail-icon\" />\n                  <span>Plan: {subscriptionData?.activePlan?.title || 'Premium Plan'}</span>\n                </div>\n                <div className=\"detail-item\">\n                  <FaCalendarAlt className=\"detail-icon\" />\n                  <span>Expires: {formatDate(subscriptionData?.endDate)}</span>\n                </div>\n                <div className=\"detail-item\">\n                  <FaCheckCircle className=\"detail-icon\" />\n                  <span>Days Remaining: {getDaysRemaining()}</span>\n                </div>\n              </div>\n            </div>\n          )}\n\n          {subscriptionStatus === 'expired' && (\n            <div className=\"subscription-card expired\">\n              <div className=\"subscription-status\">\n                <FaTimesCircle className=\"status-icon expired\" />\n                <span className=\"status-text\">Subscription Expired</span>\n              </div>\n              <div className=\"subscription-details\">\n                <div className=\"detail-item\">\n                  <FaCalendarAlt className=\"detail-icon\" />\n                  <span>Expired: {formatDate(subscriptionData?.endDate)}</span>\n                </div>\n                <p className=\"renewal-message\">\n                  Your subscription has expired. Choose a new plan below to continue accessing premium features.\n                </p>\n              </div>\n            </div>\n          )}\n\n          {subscriptionStatus === 'none' && (\n            <div className=\"subscription-card none\">\n              <div className=\"subscription-status\">\n                <FaUser className=\"status-icon none\" />\n                <span className=\"status-text\">Free Account</span>\n              </div>\n              <div className=\"subscription-details\">\n                <p className=\"upgrade-message\">\n                  You're currently using a free account. Upgrade to a premium plan to unlock all features.\n                </p>\n              </div>\n            </div>\n          )}\n        </motion.div>\n\n        {/* Available Plans */}\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.6, delay: 0.4 }}\n          className=\"available-plans\"\n        >\n          <h2 className=\"section-title\">\n            {subscriptionStatus === 'active'\n              ? '🚀 Upgrade Your Plan'\n              : subscriptionStatus === 'expired'\n                ? '🔄 Renew Your Subscription'\n                : '🎯 Choose Your Plan'\n            }\n          </h2>\n\n          {/* Temporary Test Buttons */}\n          <div style={{ display: 'flex', gap: '10px', justifyContent: 'center', marginBottom: '20px' }}>\n            <button\n              onClick={testProcessingModal}\n              style={{\n                background: '#ff6b6b',\n                color: 'white',\n                border: 'none',\n                padding: '8px 16px',\n                borderRadius: '6px',\n                cursor: 'pointer',\n                fontSize: '12px'\n              }}\n            >\n              🧪 Test Processing Modal\n            </button>\n            <button\n              onClick={testSuccessModal}\n              style={{\n                background: '#51cf66',\n                color: 'white',\n                border: 'none',\n                padding: '8px 16px',\n                borderRadius: '6px',\n                cursor: 'pointer',\n                fontSize: '12px'\n              }}\n            >\n              🧪 Test Success Modal\n            </button>\n          </div>\n          <p className=\"section-subtitle\">\n            {subscriptionStatus === 'active'\n              ? 'Upgrade to a longer plan for better value and extended access'\n              : subscriptionStatus === 'expired'\n                ? 'Your subscription has expired. Renew now to continue accessing premium features'\n                : 'Select a subscription plan to unlock all premium features and start your learning journey'\n            }\n          </p>\n          \n          {loading ? (\n            <div className=\"loading-state\">\n              <div className=\"spinner\"></div>\n              <p>Loading plans...</p>\n            </div>\n          ) : plans.length === 0 ? (\n            <div className=\"no-plans-state\">\n              <div className=\"no-plans-icon\">📋</div>\n              <h3>No Plans Available</h3>\n              <p>Plans are currently being loaded. Please refresh the page or try again later.</p>\n              <button className=\"refresh-btn\" onClick={fetchPlans}>\n                🔄 Refresh Plans\n              </button>\n            </div>\n          ) : (\n            <div className=\"plans-grid\">\n              {plans.map((plan) => (\n                <motion.div\n                  key={plan._id}\n                  whileHover={{ scale: 1.02 }}\n                  whileTap={{ scale: 0.98 }}\n                  className=\"plan-card\"\n                >\n                  <div className=\"plan-header\">\n                    <h3 className=\"plan-title\">{plan.title}</h3>\n                    {plan.title?.toLowerCase().includes('standard') && (\n                      <span className=\"plan-badge\">🔥 Popular</span>\n                    )}\n                  </div>\n                  \n                  <div className=\"plan-pricing\">\n                    <div className=\"price-display\">\n                      <div className=\"current-price\">\n                        <span className=\"currency\">TZS</span>\n                        {plan.discountedPrice?.toLocaleString()}\n                      </div>\n                      {plan.actualPrice > plan.discountedPrice && (\n                        <>\n                          <span className=\"original-price\">{plan.actualPrice?.toLocaleString()} TZS</span>\n                          <span className=\"discount-badge\">\n                            {Math.round(((plan.actualPrice - plan.discountedPrice) / plan.actualPrice) * 100)}% OFF\n                          </span>\n                        </>\n                      )}\n                    </div>\n                    <div className=\"plan-duration\">\n                      <span className=\"duration-highlight\">{plan.duration}</span> month{plan.duration > 1 ? 's' : ''} access\n                    </div>\n                  </div>\n\n                  <div className=\"plan-features\">\n                    {plan.features?.slice(0, 5).map((feature, index) => (\n                      <div key={index} className=\"feature-item\">\n                        <FaCheckCircle className=\"feature-icon\" />\n                        <span>{feature}</span>\n                      </div>\n                    ))}\n                  </div>\n\n                  <button\n                    className=\"select-plan-btn\"\n                    onClick={() => handlePlanSelect(plan)}\n                    disabled={paymentLoading === plan._id}\n                    style={{\n                      background: 'linear-gradient(135deg, #3b82f6, #1d4ed8)',\n                      color: 'white',\n                      border: 'none',\n                      borderRadius: '12px',\n                      padding: '1rem 1.5rem',\n                      fontSize: '1rem',\n                      fontWeight: '600',\n                      cursor: paymentLoading === plan._id ? 'not-allowed' : 'pointer',\n                      transition: 'all 0.3s ease',\n                      display: 'flex',\n                      alignItems: 'center',\n                      justifyContent: 'center',\n                      gap: '0.5rem',\n                      width: '100%',\n                      opacity: paymentLoading === plan._id ? 0.6 : 1\n                    }}\n                    onMouseEnter={(e) => {\n                      if (paymentLoading !== plan._id) {\n                        e.target.style.background = 'linear-gradient(135deg, #1d4ed8, #1e40af)';\n                        e.target.style.transform = 'translateY(-2px)';\n                        e.target.style.boxShadow = '0 8px 25px rgba(59, 130, 246, 0.4)';\n                      }\n                    }}\n                    onMouseLeave={(e) => {\n                      if (paymentLoading !== plan._id) {\n                        e.target.style.background = 'linear-gradient(135deg, #3b82f6, #1d4ed8)';\n                        e.target.style.transform = 'translateY(0)';\n                        e.target.style.boxShadow = '0 4px 15px rgba(59, 130, 246, 0.3)';\n                      }\n                    }}\n                  >\n                    <FaCreditCard className=\"btn-icon\" />\n                    {paymentLoading === plan._id\n                      ? 'Processing...'\n                      : subscriptionStatus === 'active'\n                        ? 'Click to Upgrade'\n                        : subscriptionStatus === 'expired'\n                          ? 'Click to Renew'\n                          : 'Click to Pay'\n                    }\n                  </button>\n                </motion.div>\n              ))}\n            </div>\n          )}\n        </motion.div>\n\n        {/* Phone Number Warning */}\n        {(!user.phoneNumber || !/^(06|07)\\d{8}$/.test(user.phoneNumber)) && (\n          <motion.div\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.6, delay: 0.6 }}\n            className=\"phone-warning\"\n          >\n            <div className=\"warning-content\">\n              <FaTimesCircle className=\"warning-icon\" />\n              <div>\n                <h4>Phone Number Required</h4>\n                <p>Please update your phone number in your profile to subscribe to a plan.</p>\n                <button \n                  className=\"update-phone-btn\"\n                  onClick={() => window.location.href = '/profile'}\n                >\n                  Update Phone Number\n                </button>\n              </div>\n            </div>\n          </motion.div>\n        )}\n\n        {/* Professional Payment Processing Modal */}\n        {showProcessingModal && (\n          <div\n            className=\"payment-modal-overlay\"\n            onClick={(e) => {\n              // Allow clicking through overlay but prevent closing modal accidentally\n              if (e.target === e.currentTarget) {\n                // Don't close modal, just allow background interaction\n                e.stopPropagation();\n              }\n            }}\n          >\n            <div className=\"payment-modal-container processing\">\n              {/* Close Button */}\n              <button\n                className=\"modal-close-btn\"\n                onClick={handleCloseProcessingModal}\n                aria-label=\"Close modal\"\n              >\n                <svg width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\">\n                  <path d=\"M18 6L6 18M6 6L18 18\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\"/>\n                </svg>\n              </button>\n\n              {/* Header */}\n              <div className=\"modal-header processing\">\n                <div className=\"processing-icon\">\n                  <div className=\"spinner\"></div>\n                  <svg className=\"payment-icon\" width=\"32\" height=\"32\" viewBox=\"0 0 24 24\" fill=\"none\">\n                    <path d=\"M2 12C2 8.229 2 6.343 3.172 5.172C4.343 4 6.229 4 10 4H14C17.771 4 19.657 4 20.828 5.172C22 6.343 22 8.229 22 12C22 15.771 22 17.657 20.828 18.828C19.657 20 17.771 20 14 20H10C6.229 20 4.343 20 3.172 18.828C2 17.657 2 15.771 2 12Z\" stroke=\"currentColor\" strokeWidth=\"1.5\"/>\n                    <path d=\"M10 16H6\" stroke=\"currentColor\" strokeWidth=\"1.5\" strokeLinecap=\"round\"/>\n                    <path d=\"M14 16H12.5\" stroke=\"currentColor\" strokeWidth=\"1.5\" strokeLinecap=\"round\"/>\n                    <path d=\"M2 10L22 10\" stroke=\"currentColor\" strokeWidth=\"1.5\" strokeLinecap=\"round\"/>\n                  </svg>\n                </div>\n                <h2>Processing Payment</h2>\n                <p>Secure transaction in progress</p>\n              </div>\n\n              {/* Content */}\n              <div className=\"modal-content\">\n                {/* Status */}\n                <div className=\"status-card\">\n                  <div className=\"status-indicator processing\"></div>\n                  <p className=\"status-text\">{paymentStatus}</p>\n                </div>\n\n                {/* Plan Info */}\n                <div className=\"plan-info-card\">\n                  <h3>{selectedPlan?.title}</h3>\n                  <div className=\"plan-details\">\n                    <div className=\"detail-row\">\n                      <span>Amount</span>\n                      <strong>{selectedPlan?.discountedPrice?.toLocaleString()} TZS</strong>\n                    </div>\n                    <div className=\"detail-row\">\n                      <span>Duration</span>\n                      <strong>{selectedPlan?.duration} month{selectedPlan?.duration > 1 ? 's' : ''}</strong>\n                    </div>\n                  </div>\n                </div>\n\n                {/* Phone Instructions */}\n                <div className=\"instruction-card\">\n                  <div className=\"instruction-header\">\n                    <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\">\n                      <path d=\"M16.5562 12.9062L16.1007 13.359C16.1007 13.359 15.0181 14.4355 12.0631 11.4972C9.10812 8.55901 10.1907 7.48257 10.1907 7.48257L10.4775 7.19738C11.1841 6.49484 11.2507 5.36691 10.6342 4.54348L9.37326 2.85908C8.61028 1.83992 7.13596 1.70529 6.26145 2.57483L4.69185 4.13552C4.25823 4.56668 3.96765 5.12559 4.00289 5.74561C4.09304 7.33182 4.81071 10.7447 8.81536 14.7266C13.0621 18.9492 17.0468 19.117 18.6763 18.9651C19.1917 18.9171 19.6399 18.6546 20.0011 18.2954L21.4217 16.883C22.3806 15.9295 22.1102 14.2949 20.8833 13.628L18.9728 12.5894C18.1672 12.1515 17.1858 12.2801 16.5562 12.9062Z\" fill=\"currentColor\"/>\n                    </svg>\n                    <span>Check Your Phone</span>\n                  </div>\n                  <div className=\"phone-number\">{user?.phoneNumber}</div>\n                  <div className=\"instruction-steps\">\n                    <div className=\"step\">1. You'll receive an SMS with payment instructions</div>\n                    <div className=\"step\">2. Follow the SMS steps to confirm payment</div>\n                    <div className=\"step\">3. Complete the mobile money transaction</div>\n                  </div>\n                </div>\n\n                {/* Try Again */}\n                {showTryAgain && (\n                  <div className=\"try-again-card\">\n                    <p>Taking longer than expected?</p>\n                    <button className=\"try-again-btn\" onClick={handleTryAgain}>\n                      <svg width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\">\n                        <path d=\"M4 12a8 8 0 018-8V2.5\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\"/>\n                        <path d=\"M12 4L9 7L12 10\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\n                      </svg>\n                      Try Again\n                    </button>\n                  </div>\n                )}\n              </div>\n            </div>\n          </div>\n        )}\n\n\n\n        {/* Professional Success Modal */}\n        {showSuccessModal && (\n          <div\n            className=\"payment-modal-overlay\"\n            onClick={(e) => {\n              // Allow clicking through overlay but keep modal open\n              if (e.target === e.currentTarget) {\n                e.stopPropagation();\n              }\n            }}\n          >\n            <div className=\"payment-modal-container success\">\n              {/* Close Button */}\n              <button\n                className=\"modal-close-btn\"\n                onClick={() => {\n                  setAutoNavigateCountdown(null);\n                  setShowSuccessModal(false);\n                }}\n                aria-label=\"Close modal\"\n              >\n                <svg width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\">\n                  <path d=\"M18 6L6 18M6 6L18 18\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\"/>\n                </svg>\n              </button>\n\n              {/* Header */}\n              <div className=\"modal-header success\">\n                <div className=\"success-icon\">\n                  <svg width=\"48\" height=\"48\" viewBox=\"0 0 24 24\" fill=\"none\">\n                    <path d=\"M22 12C22 17.5228 17.5228 22 12 22C6.47715 22 2 17.5228 2 12C2 6.47715 6.47715 2 12 2C17.5228 2 22 6.47715 22 12Z\" fill=\"#22c55e\" fillOpacity=\"0.2\"/>\n                    <path d=\"M16 9L10.5 14.5L8 12\" stroke=\"#22c55e\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\n                    <path d=\"M22 12C22 17.5228 17.5228 22 12 22C6.47715 22 2 17.5228 2 12C2 6.47715 6.47715 2 12 2C17.5228 2 22 6.47715 22 12Z\" stroke=\"#22c55e\" strokeWidth=\"2\"/>\n                  </svg>\n                </div>\n                <h2>Payment Successful!</h2>\n                <p>Welcome to {selectedPlan?.title}!</p>\n              </div>\n              {/* Content */}\n              <div className=\"modal-content\">\n                {/* Auto-Navigation Notice */}\n                {autoNavigateCountdown && (\n                  <div className=\"countdown-card\">\n                    <div className=\"countdown-icon\">\n                      <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\">\n                        <path d=\"M12 2V6M12 18V22M4.93 4.93L7.76 7.76M16.24 16.24L19.07 19.07M2 12H6M18 12H22M4.93 19.07L7.76 16.24M16.24 7.76L19.07 4.93\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\"/>\n                        <circle cx=\"12\" cy=\"12\" r=\"3\" stroke=\"currentColor\" strokeWidth=\"2\"/>\n                      </svg>\n                    </div>\n                    <p>Redirecting to Hub in {autoNavigateCountdown} seconds...</p>\n                  </div>\n                )}\n\n                {/* Plan Summary */}\n                <div className=\"plan-summary-card\">\n                  <h3>Subscription Activated</h3>\n                  <div className=\"plan-details\">\n                    <div className=\"detail-row\">\n                      <span>Plan</span>\n                      <strong>{selectedPlan?.title}</strong>\n                    </div>\n                    <div className=\"detail-row\">\n                      <span>Duration</span>\n                      <strong>{selectedPlan?.duration} month{selectedPlan?.duration > 1 ? 's' : ''}</strong>\n                    </div>\n                    <div className=\"detail-row\">\n                      <span>Amount Paid</span>\n                      <strong>{selectedPlan?.discountedPrice?.toLocaleString()} TZS</strong>\n                    </div>\n                    <div className=\"detail-row status\">\n                      <span>Status</span>\n                      <div className=\"status-badge\">\n                        <svg width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\">\n                          <path d=\"M9 12L11 14L15 10\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\n                          <circle cx=\"12\" cy=\"12\" r=\"9\" stroke=\"currentColor\" strokeWidth=\"2\"/>\n                        </svg>\n                        Active\n                      </div>\n                    </div>\n                  </div>\n                </div>\n\n                {/* Features Unlocked */}\n                <div className=\"features-card\">\n                  <h3>🚀 Premium Features Unlocked</h3>\n                  <div className=\"features-grid\">\n                    <div className=\"feature-item\">\n                      <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\">\n                        <path d=\"M9 12L11 14L15 10\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\n                        <circle cx=\"12\" cy=\"12\" r=\"9\" stroke=\"currentColor\" strokeWidth=\"2\"/>\n                      </svg>\n                      <span>Unlimited Quizzes</span>\n                    </div>\n                    <div className=\"feature-item\">\n                      <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\">\n                        <path d=\"M9 12L11 14L15 10\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\n                        <circle cx=\"12\" cy=\"12\" r=\"9\" stroke=\"currentColor\" strokeWidth=\"2\"/>\n                      </svg>\n                      <span>AI Assistant</span>\n                    </div>\n                    <div className=\"feature-item\">\n                      <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\">\n                        <path d=\"M9 12L11 14L15 10\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\n                        <circle cx=\"12\" cy=\"12\" r=\"9\" stroke=\"currentColor\" strokeWidth=\"2\"/>\n                      </svg>\n                      <span>Study Materials</span>\n                    </div>\n                    <div className=\"feature-item\">\n                      <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\">\n                        <path d=\"M9 12L11 14L15 10\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\n                        <circle cx=\"12\" cy=\"12\" r=\"9\" stroke=\"currentColor\" strokeWidth=\"2\"/>\n                      </svg>\n                      <span>Progress Tracking</span>\n                    </div>\n                    <div className=\"feature-item\">\n                      <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\">\n                        <path d=\"M9 12L11 14L15 10\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\n                        <circle cx=\"12\" cy=\"12\" r=\"9\" stroke=\"currentColor\" strokeWidth=\"2\"/>\n                      </svg>\n                      <span>Learning Videos</span>\n                    </div>\n                    <div className=\"feature-item\">\n                      <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\">\n                        <path d=\"M9 12L11 14L15 10\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\n                        <circle cx=\"12\" cy=\"12\" r=\"9\" stroke=\"currentColor\" strokeWidth=\"2\"/>\n                      </svg>\n                      <span>Forum Access</span>\n                    </div>\n                  </div>\n                </div>\n\n                {/* Action Buttons */}\n                <div className=\"modal-actions\">\n                  <button\n                    className=\"primary-btn\"\n                    onClick={() => {\n                      setAutoNavigateCountdown(null);\n                      setShowSuccessModal(false);\n                      window.location.href = '/user/hub';\n                    }}\n                  >\n                    <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\">\n                      <path d=\"M3 9L12 2L21 9V20C21 20.5304 20.7893 21.0391 20.4142 21.4142C20.0391 21.7893 19.5304 22 19 22H5C4.46957 22 3.96086 21.7893 3.58579 21.4142C3.21071 21.0391 3 20.5304 3 20V9Z\" stroke=\"currentColor\" strokeWidth=\"2\"/>\n                      <polyline points=\"9,22 9,12 15,12 15,22\" stroke=\"currentColor\" strokeWidth=\"2\"/>\n                    </svg>\n                    Continue to Hub {autoNavigateCountdown ? `(${autoNavigateCountdown}s)` : ''}\n                  </button>\n                  <button\n                    className=\"secondary-btn\"\n                    onClick={() => {\n                      setAutoNavigateCountdown(null);\n                      setShowSuccessModal(false);\n                    }}\n                  >\n                    Close\n                  </button>\n                </div>\n              </div>\n            </div>\n          </div>\n        )}\n\n\n\n        {/* Upgrade Restriction Modal */}\n        <UpgradeRestrictionModal\n          visible={showUpgradeRestriction}\n          onClose={() => setShowUpgradeRestriction(false)}\n          currentPlan={plans.find(p => p._id === subscriptionData?.activePlan) || subscriptionData?.plan}\n          subscription={subscriptionData}\n          user={user}\n        />\n\n        {/* Subscription Expired Modal */}\n        <SubscriptionExpiredModal\n          visible={showExpiredModal}\n          onClose={() => setShowExpiredModal(false)}\n          onRenew={handleRenewSubscription}\n          subscription={subscriptionData}\n          user={user}\n          plans={plans}\n        />\n      </div>\n    </div>\n  );\n};\n\nexport default Subscription;\n", "// This icon file is generated automatically.\nvar CalendarOutlined = { \"icon\": { \"tag\": \"svg\", \"attrs\": { \"viewBox\": \"64 64 896 896\", \"focusable\": \"false\" }, \"children\": [{ \"tag\": \"path\", \"attrs\": { \"d\": \"M880 184H712v-64c0-4.4-3.6-8-8-8h-56c-4.4 0-8 3.6-8 8v64H384v-64c0-4.4-3.6-8-8-8h-56c-4.4 0-8 3.6-8 8v64H144c-17.7 0-32 14.3-32 32v664c0 17.7 14.3 32 32 32h736c17.7 0 32-14.3 32-32V216c0-17.7-14.3-32-32-32zm-40 656H184V460h656v380zM184 392V256h128v48c0 4.4 3.6 8 8 8h56c4.4 0 8-3.6 8-8v-48h256v48c0 4.4 3.6 8 8 8h56c4.4 0 8-3.6 8-8v-48h128v136H184z\" } }] }, \"name\": \"calendar\", \"theme\": \"outlined\" };\nexport default CalendarOutlined;\n", "import _extends from \"@babel/runtime/helpers/esm/extends\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react';\nimport CalendarOutlinedSvg from \"@ant-design/icons-svg/es/asn/CalendarOutlined\";\nimport AntdIcon from \"../components/AntdIcon\";\nvar CalendarOutlined = function CalendarOutlined(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {\n    ref: ref,\n    icon: CalendarOutlinedSvg\n  }));\n};\nif (process.env.NODE_ENV !== 'production') {\n  CalendarOutlined.displayName = 'CalendarOutlined';\n}\nexport default /*#__PURE__*/React.forwardRef(CalendarOutlined);", "// This icon file is generated automatically.\nvar ClockCircleOutlined = { \"icon\": { \"tag\": \"svg\", \"attrs\": { \"viewBox\": \"64 64 896 896\", \"focusable\": \"false\" }, \"children\": [{ \"tag\": \"path\", \"attrs\": { \"d\": \"M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372z\" } }, { \"tag\": \"path\", \"attrs\": { \"d\": \"M686.7 638.6L544.1 535.5V288c0-4.4-3.6-8-8-8H488c-4.4 0-8 3.6-8 8v275.4c0 2.6 1.2 5 3.3 6.5l165.4 120.6c3.6 2.6 8.6 1.8 11.2-1.7l28.6-39c2.6-3.7 1.8-8.7-1.8-11.2z\" } }] }, \"name\": \"clock-circle\", \"theme\": \"outlined\" };\nexport default ClockCircleOutlined;\n", "import _extends from \"@babel/runtime/helpers/esm/extends\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react';\nimport ClockCircleOutlinedSvg from \"@ant-design/icons-svg/es/asn/ClockCircleOutlined\";\nimport AntdIcon from \"../components/AntdIcon\";\nvar ClockCircleOutlined = function ClockCircleOutlined(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {\n    ref: ref,\n    icon: ClockCircleOutlinedSvg\n  }));\n};\nif (process.env.NODE_ENV !== 'production') {\n  ClockCircleOutlined.displayName = 'ClockCircleOutlined';\n}\nexport default /*#__PURE__*/React.forwardRef(ClockCircleOutlined);", "// This icon file is generated automatically.\nvar CrownOutlined = { \"icon\": { \"tag\": \"svg\", \"attrs\": { \"viewBox\": \"64 64 896 896\", \"focusable\": \"false\" }, \"children\": [{ \"tag\": \"path\", \"attrs\": { \"d\": \"M899.6 276.5L705 396.4 518.4 147.5a8.06 8.06 0 00-12.9 0L319 396.4 124.3 276.5c-5.7-3.5-13.1 1.2-12.2 7.9L188.5 865c1.1 7.9 7.9 14 16 14h615.1c8 0 14.9-6 15.9-14l76.4-580.6c.8-6.7-6.5-11.4-12.3-7.9zm-126 534.1H250.3l-53.8-409.4 139.8 86.1L512 252.9l175.7 234.4 139.8-86.1-53.9 409.4zM512 509c-62.1 0-112.6 50.5-112.6 112.6S449.9 734.2 512 734.2s112.6-50.5 112.6-112.6S574.1 509 512 509zm0 160.9c-26.6 0-48.2-21.6-48.2-48.3 0-26.6 21.6-48.3 48.2-48.3s48.2 21.6 48.2 48.3c0 26.6-21.6 48.3-48.2 48.3z\" } }] }, \"name\": \"crown\", \"theme\": \"outlined\" };\nexport default CrownOutlined;\n", "import _extends from \"@babel/runtime/helpers/esm/extends\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react';\nimport CrownOutlinedSvg from \"@ant-design/icons-svg/es/asn/CrownOutlined\";\nimport AntdIcon from \"../components/AntdIcon\";\nvar CrownOutlined = function CrownOutlined(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {\n    ref: ref,\n    icon: CrownOutlinedSvg\n  }));\n};\nif (process.env.NODE_ENV !== 'production') {\n  CrownOutlined.displayName = 'CrownOutlined';\n}\nexport default /*#__PURE__*/React.forwardRef(CrownOutlined);"], "names": ["default", "axiosInstance", "require", "getPlans", "async", "get", "payload", "data", "error", "response", "CheckCircleOutlined", "props", "ref", "React", "AntdIcon", "_extends", "icon", "CheckCircleOutlinedSvg", "_ref", "_currentPlan$features", "visible", "onClose", "currentPlan", "subscription", "user", "daysRemaining", "calculateDaysRemaining", "endDate", "diffTime", "Date", "diffDays", "Math", "ceil", "max", "progress", "calculateProgress", "startDate", "totalDays", "usedDays", "min", "planTitle", "title", "toLocaleDateString", "_jsx", "Modal", "open", "onCancel", "footer", "width", "centered", "className", "maskStyle", "backgroundColor", "children", "_jsxs", "CrownOutlined", "Progress", "percent", "strokeColor", "trailColor", "strokeWidth", "showInfo", "CalendarOutlined", "ClockCircleOutlined", "concat", "features", "slice", "map", "feature", "index", "<PERSON><PERSON>", "type", "size", "onClick", "ExclamationCircleOutlined", "ExclamationCircleOutlinedSvg", "RocketOutlined", "RocketOutlinedSvg", "StarOutlined", "StarOutlinedSvg", "onRenew", "plans", "<PERSON><PERSON><PERSON>", "setSelectedPlan", "useState", "showPlans", "setShowPlans", "expiredDays", "calculateExpiredDays", "availablePlans", "length", "_id", "discountedPrice", "actualPrice", "duration", "closable", "plan", "_plan$title", "_plan$discountedPrice", "_plan$actualPrice", "_plan$features", "handlePlanSelect", "toLowerCase", "includes", "toLocaleString", "round", "handleRenewSubscription", "disabled", "handleShowPlans", "Subscription", "_subscriptionData$act", "_selectedPlan$discoun", "_selectedPlan$discoun2", "setPlans", "loading", "setLoading", "paymentLoading", "setPaymentLoading", "showProcessingModal", "setShowProcessingModal", "useEffect", "console", "log", "showSuccessModal", "setShowSuccessModal", "paymentStatus", "setPaymentStatus", "showUpgradeRestriction", "setShowUpgradeRestriction", "showExpiredModal", "setShowExpiredModal", "processingStartTime", "setProcessingStartTime", "showTryAgain", "setShowTryAgain", "autoNavigateCountdown", "setAutoNavigateCountdown", "useSelector", "state", "subscriptionData", "samplePlans", "useDispatch", "discountPercentage", "status", "fetchPlans", "checkCurrentSubscription", "document", "body", "style", "overflow", "position", "height", "detectScrollableContent", "querySelectorAll", "for<PERSON>ach", "content", "scrollHeight", "clientHeight", "classList", "add", "remove", "setTimeout", "window", "addEventListener", "removeEventListener", "isSubscriptionExpired", "success", "Array", "isArray", "warn", "message", "info", "warning", "checkPaymentStatus", "today", "setHours", "phoneNumber", "test", "_user$name", "now", "Promise", "resolve", "paymentData", "userId", "userPhone", "userEmail", "email", "name", "replace", "addPayment", "Error", "_response$data", "order_id", "marginTop", "orderIdToCheck", "checkPaymentConfirmation", "orderId", "handleVisibilityChange", "isPolling", "attempts", "maxAttempts", "pollPaymentStatus", "statusResponse", "fontSize", "countdownInterval", "setInterval", "prev", "clearInterval", "location", "href", "hidden", "formatDate", "dateString", "year", "month", "day", "subscriptionStatus", "getSubscriptionStatus", "motion", "div", "initial", "opacity", "y", "animate", "transition", "top", "right", "background", "color", "border", "padding", "borderRadius", "cursor", "zIndex", "FaCrown", "delay", "FaCheckCircle", "activePlan", "FaCalendarAlt", "getDaysRemaining", "FaTimesCircle", "FaUser", "display", "gap", "justifyContent", "marginBottom", "testProcessingModal", "testSuccessModal", "whileHover", "scale", "whileTap", "_Fragment", "fontWeight", "alignItems", "onMouseEnter", "e", "target", "transform", "boxShadow", "onMouseLeave", "FaCreditCard", "currentTarget", "stopPropagation", "handleCloseProcessingModal", "viewBox", "fill", "d", "stroke", "strokeLinecap", "handleTryAgain", "strokeLinejoin", "fillOpacity", "cx", "cy", "r", "points", "UpgradeRestrictionModal", "find", "p", "SubscriptionExpiredModal", "CalendarOutlinedSvg", "ClockCircleOutlinedSvg", "CrownOutlinedSvg"], "sourceRoot": ""}