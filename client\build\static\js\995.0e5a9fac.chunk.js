"use strict";(self.webpackChunkclient=self.webpackChunkclient||[]).push([[995],{272:(e,t,a)=>{a.d(t,{$s:()=>d,I1:()=>n,Ss:()=>i,cq:()=>r,dM:()=>s,uH:()=>l});const{default:o}=a(3371),r=async e=>{try{return(await o.post("/api/reports/add-report",e)).data}catch(t){return t.response.data}},n=async e=>{try{return(await o.post("/api/reports/get-all-reports",e)).data}catch(t){return t.response.data}},i=async e=>{try{return(await o.post("/api/reports/get-all-reports-by-user",e)).data}catch(t){return t.response.data}},l=async e=>{try{return(await o.get("/api/reports/get-all-reports-for-ranking")).data}catch(t){return t.response.data}},s=async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};try{const t=new URLSearchParams;e.limit&&t.append("limit",e.limit),e.classFilter&&t.append("classFilter",e.classFilter),e.levelFilter&&t.append("levelFilter",e.levelFilter),e.seasonFilter&&t.append("seasonFilter",e.seasonFilter),e.includeInactive&&t.append("includeInactive",e.includeInactive);return(await o.get("/api/quiz/xp-leaderboard?".concat(t.toString()))).data}catch(t){return t.response.data}},d=async function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:5;try{return(await o.get("/api/quiz/user-ranking/".concat(e,"?context=").concat(t))).data}catch(a){return a.response.data}}},995:(e,t,a)=>{a.r(t),a.d(t,{default:()=>p});var o=a(1413),r=a(2791),n=a(6042),i=a(9434),l=a(7689),s=a(7027),d=a(5526),c=a(272),x=a(8262),g=a(2048),m=(a(9242),a(184));const p=()=>{const e=(0,i.v9)((e=>e.users||{})).user||null,t=(()=>{try{const e=localStorage.getItem("user");return e?JSON.parse(e):null}catch(e){return null}})(),a=(()=>{try{const e=localStorage.getItem("token");if(e){return JSON.parse(atob(e.split(".")[1]))}return null}catch(e){return null}})(),p=e||t||a,[u,h]=(0,r.useState)(null);console.log("\ud83d\udd0d User Data Sources:",{redux:e,localStorage:t,token:a,final:p}),p&&!u&&console.log("\ud83d\udd0d Loading user data for:",p.userId);const b=(0,l.s0)(),[f,y]=(0,r.useState)([]),[v,w]=(0,r.useState)(!0),[j,k]=(0,r.useState)(null),[N,F]=(0,r.useState)("global"),[S,C]=(0,r.useState)(!0),[_,P]=(0,r.useState)(0),[I,z]=(0,r.useState)(""),[E,D]=(0,r.useState)(null),[X,T]=(0,r.useState)([]),[O,A]=(0,r.useState)(!1),[L,B]=(0,r.useState)(null),[U,W]=(0,r.useState)({}),[R,M]=(0,r.useState)(!1),[H,G]=(0,r.useState)(!1),Z=(0,r.useRef)({}),Y=((0,r.useRef)(null),(0,r.useRef)(null),(0,r.useRef)(null)),Q=(0,r.useRef)(null),q=["\ud83d\ude80 Every expert was once a beginner. Keep climbing!","\u2b50 Your potential is endless. Show them what you're made of!","\ud83d\udd25 Champions are made in the moments when nobody's watching.","\ud83d\udc8e Pressure makes diamonds. You're becoming brilliant!","\ud83c\udfaf Success is not final, failure is not fatal. Keep going!","\u26a1 The only impossible journey is the one you never begin.","\ud83c\udf1f Believe in yourself and all that you are capable of!","\ud83c\udfc6 Greatness is not about being better than others, it's about being better than yesterday.","\ud83d\udcaa Your only limit is your mind. Break through it!","\ud83c\udfa8 Paint your success with the colors of determination!"],K={mythic:{min:5e4,color:"from-purple-300 via-pink-300 via-red-300 to-orange-300",bgColor:"bg-gradient-to-br from-purple-900/50 via-pink-900/50 to-red-900/50",textColor:"#FFD700",nameColor:"#FF1493",shadowColor:"rgba(255, 20, 147, 0.9)",glow:"shadow-pink-500/90",icon:d.PWB,title:"MYTHIC",description:"Legendary Master",borderColor:"#FF1493",effect:"mythic-aura",leagueIcon:"\ud83d\udc51",promotionXP:0,relegationXP:4e4,maxUsers:10},legendary:{min:25e3,color:"from-purple-400 via-indigo-400 via-blue-400 to-cyan-400",bgColor:"bg-gradient-to-br from-purple-900/40 via-indigo-900/40 to-blue-900/40",textColor:"#8A2BE2",nameColor:"#9370DB",shadowColor:"rgba(138, 43, 226, 0.9)",glow:"shadow-purple-500/80",icon:d.ab0,title:"LEGENDARY",description:"Elite Champion",borderColor:"#8A2BE2",effect:"legendary-sparkle",leagueIcon:"\ud83d\udc8e",promotionXP:5e4,relegationXP:2e4,maxUsers:25},diamond:{min:12e3,color:"from-cyan-300 via-blue-300 via-indigo-300 to-purple-300",bgColor:"bg-gradient-to-br from-cyan-900/40 via-blue-900/40 to-indigo-900/40",textColor:"#00CED1",nameColor:"#40E0D0",shadowColor:"rgba(0, 206, 209, 0.9)",glow:"shadow-cyan-400/80",icon:d.$T2,title:"DIAMOND",description:"Expert Level",borderColor:"#00CED1",effect:"diamond-shine",leagueIcon:"\ud83d\udee1\ufe0f",promotionXP:25e3,relegationXP:8e3,maxUsers:50},platinum:{min:6e3,color:"from-slate-300 via-gray-300 via-zinc-300 to-stone-300",bgColor:"bg-gradient-to-br from-slate-800/40 via-gray-800/40 to-zinc-800/40",textColor:"#C0C0C0",nameColor:"#D3D3D3",shadowColor:"rgba(192, 192, 192, 0.9)",glow:"shadow-slate-400/80",icon:d.iy9,title:"PLATINUM",description:"Advanced",borderColor:"#C0C0C0",effect:"platinum-gleam",leagueIcon:"\ud83c\udfc6",promotionXP:12e3,relegationXP:4e3,maxUsers:100},gold:{min:3e3,color:"from-yellow-300 via-amber-300 via-orange-300 to-red-300",bgColor:"bg-gradient-to-br from-yellow-900/40 via-amber-900/40 to-orange-900/40",textColor:"#FFD700",nameColor:"#FFA500",shadowColor:"rgba(255, 215, 0, 0.9)",glow:"shadow-yellow-400/80",icon:d.gBl,title:"GOLD",description:"Skilled",borderColor:"#FFD700",effect:"gold-glow",leagueIcon:"\ud83e\udd47",promotionXP:6e3,relegationXP:2e3,maxUsers:200},silver:{min:1500,color:"from-gray-300 via-slate-300 via-zinc-300 to-gray-300",bgColor:"bg-gradient-to-br from-gray-800/40 via-slate-800/40 to-zinc-800/40",textColor:"#C0C0C0",nameColor:"#B8B8B8",shadowColor:"rgba(192, 192, 192, 0.9)",glow:"shadow-gray-400/80",icon:d.Vh6,title:"SILVER",description:"Improving",borderColor:"#C0C0C0",effect:"silver-shimmer",leagueIcon:"\ud83e\udd48",promotionXP:3e3,relegationXP:800,maxUsers:300},bronze:{min:500,color:"from-orange-300 via-amber-300 via-yellow-300 to-orange-300",bgColor:"bg-gradient-to-br from-orange-900/40 via-amber-900/40 to-yellow-900/40",textColor:"#CD7F32",nameColor:"#D2691E",shadowColor:"rgba(205, 127, 50, 0.9)",glow:"shadow-orange-400/80",icon:d.jsT,title:"BRONZE",description:"Learning",borderColor:"#CD7F32",effect:"bronze-warm",leagueIcon:"\ud83e\udd49",promotionXP:1500,relegationXP:200,maxUsers:500},rookie:{min:0,color:"from-green-300 via-emerald-300 via-teal-300 to-cyan-300",bgColor:"bg-gradient-to-br from-green-900/40 via-emerald-900/40 to-teal-900/40",textColor:"#32CD32",nameColor:"#90EE90",shadowColor:"rgba(50, 205, 50, 0.9)",glow:"shadow-green-400/80",icon:d.Wzz,title:"ROOKIE",description:"Starting Out",borderColor:"#32CD32",effect:"rookie-glow",leagueIcon:"\ud83d\ude80",promotionXP:500,relegationXP:0,maxUsers:1e3}},V=e=>{for(const[t,a]of Object.entries(K))if(e>=a.min)return(0,o.Z)({league:t},a);return(0,o.Z)({league:"rookie"},K.rookie)},J=e=>{const t={};return e.forEach((e=>{const a=V(e.totalXP);t[a.league]||(t[a.league]={config:a,users:[]}),t[a.league].users.push((0,o.Z)((0,o.Z)({},e),{},{tier:a}))})),Object.keys(t).forEach((e=>{t[e].users.sort(((e,t)=>t.totalXP-e.totalXP))})),t},$=(e,t)=>{if(!t)return null;const a=V(t.totalXP||0),o=e.filter((e=>V(e.totalXP).league===a.league)).sort(((e,t)=>t.totalXP-e.totalXP));return{league:a,users:o,userRank:o.findIndex((e=>e._id===t._id))+1,totalInLeague:o.length}},ee=e=>{var t;console.log("\ud83c\udfaf League selected:",e),B(e),A(!0),T((null===(t=U[e])||void 0===t?void 0:t.users)||[]),setTimeout((()=>{const t=document.querySelector('[data-league="'.concat(e,'"]'))||document.getElementById("league-".concat(e))||Z.current[e];t&&(t.scrollIntoView({behavior:"smooth",block:"center",inline:"nearest"}),t.style.transform="scale(1.02)",t.style.transition="all 0.3s ease",t.style.boxShadow="0 0 30px rgba(59, 130, 246, 0.5)",setTimeout((()=>{t.style.transform="scale(1)",t.style.boxShadow=""}),600))}),100)},te=()=>["mythic","legendary","diamond","platinum","gold","silver","bronze","rookie"].filter((e=>U[e]&&U[e].users.length>0)),ae=async function(){let e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];try{const n=(null===p||void 0===p?void 0:p.level)||"primary";if(["primary","secondary","advance"].forEach((e=>{e!==n&&(localStorage.removeItem("ranking_cache_".concat(e)),localStorage.removeItem("ranking_cache_time_".concat(e)))})),!e){const e=(null===p||void 0===p?void 0:p.level)||"primary",t=localStorage.getItem("ranking_cache_".concat(e)),a=localStorage.getItem("ranking_cache_time_".concat(e)),o=Date.now();if(t&&a&&o-parseInt(a)<12e4){const e=JSON.parse(t);return y(e.data||[]),k(e.userRank),void w(!1)}}w(!0),console.log("\ud83d\ude80 Fetching enhanced XP ranking data...",e?"(Force Refresh)":"");try{console.log("\ud83d\udcca Fetching XP leaderboard...");const t=await(0,c.dM)((0,o.Z)({limit:1e3,levelFilter:(null===p||void 0===p?void 0:p.level)||"all",includeInactive:!1},e&&{_t:Date.now()}));if(console.log("\u2728 XP Leaderboard response:",t),t&&t.success&&t.data){console.log("\ud83c\udfaf Using enhanced XP ranking data");const e=t.data.filter((e=>e.totalXP&&e.totalXP>0||e.totalQuizzesTaken&&e.totalQuizzesTaken>0));console.log("\ud83d\udd0d First 3 users profile data:",e.slice(0,3).map((e=>({_id:e._id,name:e.name,profileImage:e.profileImage,profilePicture:e.profilePicture,hasProfileData:!(!e.profileImage&&!e.profilePicture)}))));const a=e.map(((e,t)=>({_id:e._id,name:e.name||"Anonymous Champion",email:e.email||"",class:e.class||"",level:e.level||"",profilePicture:e.profileImage||e.profilePicture||"",profileImage:e.profileImage||e.profilePicture||"",totalXP:e.totalXP||0,totalQuizzesTaken:e.totalQuizzesTaken||0,averageScore:e.averageScore||0,currentStreak:e.currentStreak||0,bestStreak:e.bestStreak||0,subscriptionStatus:e.subscriptionStatus||"free",rank:t+1,tier:V(e.totalXP||0),isRealUser:!0,rankingScore:e.rankingScore||0,currentLevel:e.currentLevel||1,xpToNextLevel:e.xpToNextLevel||100,lifetimeXP:e.lifetimeXP||0,seasonXP:e.seasonXP||0,achievements:e.achievements||[],dataSource:"enhanced_xp"})));console.log("\ud83c\udfc6 Top 3 transformed users:",a.slice(0,3).map((e=>({_id:e._id,name:e.name,profileImage:e.profileImage,profilePicture:e.profilePicture,hasProfileData:!(!e.profileImage&&!e.profilePicture)})))),y(a);const o=a.findIndex((e=>e._id===(null===p||void 0===p?void 0:p._id)));if(k(o>=0?o+1:null),p){const e=$(a,p);D(e),T((null===e||void 0===e?void 0:e.users)||[])}const r=J(a);W(r);const n=(null===p||void 0===p?void 0:p.level)||"primary",i={data:a,userRank:o>=0?o+1:null};return localStorage.setItem("ranking_cache_".concat(n),JSON.stringify(i)),localStorage.setItem("ranking_cache_time_".concat(n),Date.now().toString()),void w(!1)}}catch(t){console.log("\u26a0\ufe0f XP leaderboard failed, trying fallback:",t)}let i,l;console.log("\ud83d\udd04 Falling back to legacy ranking system...");try{console.log("\ud83d\udcca Fetching legacy ranking reports..."),i=await(0,c.uH)(),console.log("\ud83d\udc65 Fetching all users..."),l=await(0,x.AW)()}catch(a){console.log("\u26a1 Error fetching legacy data:",a);try{l=await(0,x.AW)()}catch(r){console.log("\u274c Failed to fetch users:",r)}}let d=[];if(l&&l.success&&l.data){console.log("\ud83d\udd04 Processing legacy user data...");const e={};i&&i.success&&i.data&&i.data.forEach((t=>{var a;const o=(null===(a=t.user)||void 0===a?void 0:a._id)||t.userId;o&&(e[o]=t.reports||[])})),d=l.data.filter((e=>{if(!e||!e._id)return!1;if(!e.isAdmin&&null!==p&&void 0!==p&&p.level){const t=p.level.toLowerCase(),a=(e.level||"primary").toLowerCase();if("primary"===t)return"primary"===a;if("secondary"===t)return"secondary"===a;if("advance"===t)return"advance"===a}return!0})).map(((t,a)=>{const o=e[t._id]||[];let r=o.length||t.totalQuizzesTaken||0,n=o.reduce(((e,t)=>e+(t.score||0)),0),i=r>0?Math.round(n/r):t.averageScore||0;if(!o.length&&t.totalPoints){const e=Math.max(1,Math.floor(t.totalPoints/100)),a=Math.min(95,Math.max(60,60+t.totalPoints/e/10));r=e,i=Math.round(a),n=Math.round(i*r),console.log("\ud83d\udcca Estimated stats for ".concat(t.name,": ").concat(e," quizzes, ").concat(a,"% avg from ").concat(t.totalPoints," points"))}let l=t.totalXP||0;l||(t.totalPoints?l=Math.floor(t.totalPoints+25*r+(i>80?15*r:0)+(i>90?10*r:0)):r>0&&(l=Math.floor(i*r*8+40*r+(i>80?20*r:0))));let s=t.currentStreak||0,d=t.bestStreak||0;if(o.length>0){let e=0;o.forEach((t=>{t.score>=60?(e++,d=Math.max(d,e)):e=0})),s=e}else if(t.totalPoints&&!s){const e=r>0?t.totalPoints/r:0;e>80&&(s=Math.min(r,Math.floor(e/20)),d=Math.max(s,Math.floor(e/15)))}return{_id:t._id,name:t.name||"Anonymous Champion",email:t.email||"",class:t.class||"",level:t.level||"",profilePicture:t.profileImage||t.profilePicture||"",profileImage:t.profileImage||t.profilePicture||"",totalXP:l,totalQuizzesTaken:r,averageScore:i,currentStreak:s,bestStreak:d,subscriptionStatus:t.subscriptionStatus||"free",rank:a+1,tier:V(l),isRealUser:!0,originalPoints:t.totalPoints||0,hasReports:o.length>0,dataSource:o.length>0?"reports":t.totalPoints?"legacy_points":"estimated"}})),d.sort(((e,t)=>t.totalXP-e.totalXP)),d.forEach(((e,t)=>{e.rank=t+1})),y(d);const t=(null===p||void 0===p?void 0:p.level)||"primary",a={data:d,userRank:null};let o=-1;if(p&&(o=d.findIndex((e=>e._id===p._id)),-1===o&&(o=d.findIndex((e=>String(e._id)===String(p._id)))),-1===o&&p.name&&(o=d.findIndex((e=>e.name===p.name)))),k(o>=0?o+1:null),a.userRank=o>=0?o+1:null,localStorage.setItem("ranking_cache_".concat(t),JSON.stringify(a)),localStorage.setItem("ranking_cache_time_".concat(t),Date.now().toString()),p){const e=$(d,p);D(e),T((null===e||void 0===e?void 0:e.users)||[])}const r=J(d);W(r);const n={reports:d.filter((e=>"reports"===e.dataSource)).length,legacy_points:d.filter((e=>"legacy_points"===e.dataSource)).length,estimated:d.filter((e=>"estimated"===e.dataSource)).length};console.log("\ud83c\udf89 Amazing ranking data loaded!",d.length,"real champions"),console.log("\ud83d\udcca Data sources:",n),console.log("\ud83c\udfc6 Top 5 champions:",d.slice(0,5).map((e=>({name:e.name,xp:e.totalXP,quizzes:e.totalQuizzesTaken,avg:e.averageScore,source:e.dataSource}))))}else console.log("\u26a0\ufe0f No user data available"),y([]),k(null),s.ZP.warning("No ranking data available. Please check your connection.")}catch(a){console.error("\ud83d\udca5 Error fetching ranking data:",a),s.ZP.error("Failed to load the leaderboard. But champions never give up!")}finally{w(!1)}};(0,r.useEffect)((()=>{if(!u&&null!==p&&void 0!==p&&p.userId&&f.length>0){console.log("\ud83d\udd0d Trying to find user in ranking data...");const e=f.find((e=>String(e._id)===String(p.userId)));if(e){console.log("\u2705 Found user in ranking data:",e);const t=(0,o.Z)((0,o.Z)({},e),{},{profilePicture:e.profileImage||e.profilePicture||"",profileImage:e.profileImage||e.profilePicture||""});h(t)}else console.log("\u274c User not found in ranking data either")}}),[f,p,u]),(0,r.useEffect)((()=>{ae(),(async()=>{if(null!==p&&void 0!==p&&p.userId)try{console.log("\ud83d\udd0d Fetching full user data for userId:",p.userId);const e=await(0,x.AW)();if(console.log("\ud83d\udccb getAllUsers response:",e),e.success){console.log("\ud83d\udcca Total users found:",e.data.length),console.log("\ud83d\udd0d Looking for userId:",p.userId),console.log("\ud83d\udcdd First 5 user IDs:",e.data.slice(0,5).map((e=>({id:e._id,name:e.name}))));const t=e.data.find((e=>String(e._id)===String(p.userId)));if(t){console.log("\u2705 Found full user data:",t);const e=(0,o.Z)((0,o.Z)({},t),{},{profilePicture:t.profileImage||t.profilePicture||"",profileImage:t.profileImage||t.profilePicture||""});h(e)}else{console.log("\u274c User not found in users list"),console.log("\ud83d\udd0d Trying alternative search methods...");const t=e.data.find((e=>e._id===p.userId||e.id===p.userId||String(e._id).includes(p.userId)||String(p.userId).includes(e._id)));if(t){console.log("\u2705 Found user with alternative method:",t);const e=(0,o.Z)((0,o.Z)({},t),{},{profilePicture:t.profileImage||t.profilePicture||"",profileImage:t.profileImage||t.profilePicture||""});h(e)}else console.log("\u274c User not found with any method")}}else console.log("\u274c getAllUsers failed:",e)}catch(e){console.error("\u274c Error fetching user data:",e)}else console.log("\u274c No userId available:",p)})();const e=q[Math.floor(Math.random()*q.length)];z(e);const t=setInterval((()=>{P((e=>(e+1)%4))}),3e3),a=()=>{console.log("\ud83c\udfaf Window focused - refreshing ranking data..."),ae(!0)},r=e=>{console.log("\ud83d\ude80 Real-time ranking update triggered:",e.detail),localStorage.removeItem("rankingCache"),localStorage.removeItem("userRankingPosition"),localStorage.removeItem("leaderboardData");setTimeout((()=>{!async function(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:3;for(let r=0;r<t;r++)try{var a;if(console.log("\ud83d\udd04 Refreshing ranking data (attempt ".concat(r+1,"/").concat(t,")")),await ae(!0),null!==(a=e.detail)&&void 0!==a&&a.newTotalXP&&p){const t=f.find((e=>String(e._id)===String(p._id)));if(t&&t.totalXP>=e.detail.newTotalXP){console.log("\u2705 XP update confirmed in ranking data");break}}r<t-1&&await new Promise((e=>setTimeout(e,1500)))}catch(o){console.error("\u274c Ranking refresh attempt ".concat(r+1," failed:"),o),r<t-1&&await new Promise((e=>setTimeout(e,1500)))}}()}),1e3)};return window.addEventListener("focus",a),window.addEventListener("rankingUpdate",r),()=>{clearInterval(t),window.removeEventListener("focus",a),window.removeEventListener("rankingUpdate",r)}}),[]),(0,r.useEffect)((()=>{if(p&&U&&Object.keys(U).length>0&&!L)for(const[e,t]of Object.entries(U)){if(t.users.find((e=>String(e._id)===String(p._id)))){console.log("\ud83c\udfaf Auto-selecting user league:",e),B(e),A(!0),T(t.users);break}}}),[p,U,L]);const oe=f.slice(0,3),re=(f.slice(3),(()=>{if(null===p||void 0===p||!p._id)return null;if(oe.some((e=>String(e._id)===String(p._id)))){return{type:"podium",position:oe.findIndex((e=>String(e._id)===String(p._id)))+1,league:"Champion Podium",leagueKey:"podium"}}for(const[t,a]of Object.entries(U)){var e;if(null===(e=a.users)||void 0===e?void 0:e.find((e=>String(e._id)===String(p._id)))){return{type:"league",position:a.users.findIndex((e=>String(e._id)===String(p._id)))+1,league:a.title,leagueKey:t,totalUsers:a.users.length}}}return null})()),ne=e=>p&&String(e)===String(p._id),ie=e=>ne(e)&&!R;(0,r.useEffect)((()=>{M(!1),G(!1)}),[null===p||void 0===p?void 0:p._id,L]),(0,r.useEffect)((()=>{if(console.log("\ud83d\udd04 Auto-scroll check:",{userId:null===p||void 0===p?void 0:p._id,autoScrollCompleted:H,rankingDataLength:f.length}),null===p||void 0===p||!p._id||H||0===f.length)return void console.log("\u274c Auto-scroll skipped:",{hasUser:!(null===p||void 0===p||!p._id),completed:H,hasData:f.length>0});const e=setTimeout((()=>{console.log("\ud83c\udfaf Starting auto-scroll for user:",p._id);const e=f.find((e=>String(e._id)===String(p._id)));if(!e)return console.log("\u274c User not found in ranking data"),void G(!0);console.log("\u2705 User found in ranking at position:",e.rank);const t=e.rank<=3;if(console.log("\ud83c\udfc6 Is user in podium?",t),t){console.log("\ud83d\udccd Scrolling to podium section...");const e=document.querySelector('[data-section="podium"]');console.log("\ud83c\udfaa Podium section found:",!!e),e?setTimeout((()=>{e.scrollIntoView({behavior:"smooth",block:"center",inline:"nearest"}),console.log("\u2705 Scrolled to podium"),setTimeout((()=>{M(!0),G(!0),console.log("\u2705 Auto-scroll completed")}),1e3)}),500):G(!0)}else{console.log("\ud83d\udccd Looking for user element with ID:",p._id);const e=document.querySelector('[data-user-id="'.concat(p._id,'"]'));console.log("\ud83c\udfaf User element found:",!!e),e?setTimeout((()=>{e.scrollIntoView({behavior:"smooth",block:"center",inline:"nearest"}),console.log("\u2705 Scrolled to user position"),setTimeout((()=>{M(!0),G(!0),console.log("\u2705 Auto-scroll completed")}),1e3)}),500):(console.log("\u274c User element not found in DOM"),G(!0))}}),2e3);return()=>clearTimeout(e)}),[null===p||void 0===p?void 0:p._id,f,H]);const le=()=>(0,m.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900",children:(0,m.jsxs)("div",{className:"container mx-auto px-4 py-8",children:[(0,m.jsxs)("div",{className:"text-center mb-12",children:[(0,m.jsx)("div",{className:"h-12 bg-white/10 rounded-lg w-96 mx-auto mb-4 animate-pulse"}),(0,m.jsx)("div",{className:"h-6 bg-white/5 rounded w-64 mx-auto animate-pulse"})]}),(0,m.jsx)("div",{className:"flex justify-center items-end mb-16 space-x-8",children:[2,1,3].map((e=>(0,m.jsxs)("div",{className:"text-center ".concat(1===e?"order-2":2===e?"order-1":"order-3"),children:[(0,m.jsx)("div",{className:"w-20 h-20 bg-gradient-to-br from-yellow-400/20 to-yellow-600/20 rounded-full mx-auto mb-4 animate-pulse"}),(0,m.jsx)("div",{className:"h-4 bg-white/10 rounded w-16 mx-auto mb-2 animate-pulse"}),(0,m.jsx)("div",{className:"h-3 bg-white/5 rounded w-12 mx-auto animate-pulse"})]},e)))}),(0,m.jsx)("div",{className:"space-y-4 max-w-4xl mx-auto",children:[...Array(8)].map(((e,t)=>(0,m.jsx)("div",{className:"bg-white/5 rounded-xl p-4 animate-pulse",children:(0,m.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,m.jsx)("div",{className:"w-12 h-12 bg-white/10 rounded-full"}),(0,m.jsxs)("div",{className:"flex-1",children:[(0,m.jsx)("div",{className:"h-4 bg-white/10 rounded w-32 mb-2"}),(0,m.jsx)("div",{className:"h-3 bg-white/5 rounded w-24"})]}),(0,m.jsx)("div",{className:"h-6 bg-white/10 rounded w-16"})]})},t)))})]})});return v&&0===f.length?(0,m.jsx)(le,{}):(0,m.jsxs)(m.Fragment,{children:[(0,m.jsx)("style",{children:'\n        /* Dark background for better color visibility */\n        body {\n          background: linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #334155 100%) !important;\n          min-height: 100vh;\n          color: #ffffff !important;\n        }\n\n        .ranking-page-container {\n          background: linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #334155 100%);\n          min-height: 100vh;\n          color: #ffffff;\n        }\n\n        /* Fix black text visibility - Enhanced */\n        .ranking-page-container * {\n          color: inherit;\n        }\n\n        .ranking-page-container .text-black,\n        .ranking-page-container .text-gray-900,\n        .ranking-page-container h1,\n        .ranking-page-container h2,\n        .ranking-page-container h3,\n        .ranking-page-container h4,\n        .ranking-page-container h5,\n        .ranking-page-container h6,\n        .ranking-page-container p,\n        .ranking-page-container span,\n        .ranking-page-container div {\n          color: #ffffff !important;\n        }\n\n        .ranking-page-container [style*="color: #000000"],\n        .ranking-page-container [style*="color: black"],\n        .ranking-page-container [style*="color:#000000"],\n        .ranking-page-container [style*="color:black"],\n        .ranking-page-container [style*="color: #1f2937"],\n        .ranking-page-container [style*="color:#1f2937"] {\n          color: #ffffff !important;\n        }\n\n        /* Force white text for names and content */\n        .ranking-page-container .font-bold,\n        .ranking-page-container .font-black,\n        .ranking-page-container .font-semibold,\n        .ranking-page-container .font-medium {\n          color: #ffffff !important;\n        }\n\n\n        /* Enhanced hover effects for ranking cards */\n        .ranking-card {\n          transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\n        }\n\n        .ranking-card:hover {\n          transform: translateY(-2px) scale(1.01);\n        }\n\n        /* Smooth animations for league badges */\n        .league-badge {\n          transition: all 0.2s ease-in-out;\n        }\n\n        .league-badge:hover {\n          transform: scale(1.05);\n        }\n\n        /* Gradient text animations */\n        @keyframes gradientShift {\n          0%, 100% { background-position: 0% 50%; }\n          50% { background-position: 100% 50%; }\n        }\n\n        .animated-gradient {\n          background-size: 200% 200%;\n          animation: gradientShift 3s ease infinite;\n        }\n\n        /* League-specific animations */\n        .mythic-aura {\n          animation: mythicPulse 2s ease-in-out infinite alternate;\n        }\n\n        .legendary-sparkle {\n          animation: legendarySparkle 3s ease-in-out infinite;\n        }\n\n        .diamond-shine {\n          animation: diamondShine 2.5s ease-in-out infinite;\n        }\n\n        .platinum-gleam {\n          animation: platinumGleam 3s ease-in-out infinite;\n        }\n\n        .gold-glow {\n          animation: goldGlow 2s ease-in-out infinite alternate;\n        }\n\n        .silver-shimmer {\n          animation: silverShimmer 2.5s ease-in-out infinite;\n        }\n\n        .bronze-warm {\n          animation: bronzeWarm 3s ease-in-out infinite;\n        }\n\n        .rookie-glow {\n          animation: rookieGlow 2s ease-in-out infinite alternate;\n        }\n\n        @keyframes mythicPulse {\n          0% { box-shadow: 0 0 20px rgba(255, 20, 147, 0.5); }\n          100% { box-shadow: 0 0 40px rgba(255, 20, 147, 0.8), 0 0 60px rgba(138, 43, 226, 0.6); }\n        }\n\n        @keyframes legendarySparkle {\n          0%, 100% { filter: brightness(1) hue-rotate(0deg); }\n          50% { filter: brightness(1.2) hue-rotate(10deg); }\n        }\n\n        @keyframes diamondShine {\n          0%, 100% { filter: brightness(1) saturate(1); }\n          50% { filter: brightness(1.3) saturate(1.2); }\n        }\n\n        @keyframes platinumGleam {\n          0%, 100% { filter: brightness(1); }\n          50% { filter: brightness(1.1) contrast(1.1); }\n        }\n\n        @keyframes goldGlow {\n          0% { filter: brightness(1) drop-shadow(0 0 10px #FFD700); }\n          100% { filter: brightness(1.2) drop-shadow(0 0 20px #FFD700); }\n        }\n\n        @keyframes silverShimmer {\n          0%, 100% { filter: brightness(1); }\n          50% { filter: brightness(1.15) contrast(1.05); }\n        }\n\n        @keyframes bronzeWarm {\n          0%, 100% { filter: brightness(1) hue-rotate(0deg); }\n          50% { filter: brightness(1.1) hue-rotate(5deg); }\n        }\n\n        @keyframes rookieGlow {\n          0% { filter: brightness(1) drop-shadow(0 0 5px #32CD32); }\n          100% { filter: brightness(1.15) drop-shadow(0 0 15px #32CD32); }\n        }\n\n        /* Horizontal podium animations */\n        .podium-animation {\n          animation: podiumFloat 4s ease-in-out infinite;\n        }\n\n        @keyframes podiumFloat {\n          0%, 100% { transform: translateY(0px); }\n          50% { transform: translateY(-5px); }\n        }\n      '}),(0,m.jsxs)("div",{className:"ranking-page-container ranking-page min-h-screen bg-gradient-to-br from-slate-900 via-gray-900 to-black relative overflow-hidden",onClick:()=>{R||(M(!0),console.log("\ud83d\udc46 User clicked - highlighting disabled"))},children:[!R&&p&&(0,m.jsx)(n.E.div,{initial:{opacity:0,y:-20},animate:{opacity:1,y:0},exit:{opacity:0,y:-20},className:"fixed top-4 left-1/2 transform -translate-x-1/2 z-50 bg-yellow-500/90 backdrop-blur-sm text-black px-4 py-2 rounded-lg shadow-lg text-sm font-medium",children:"\ud83c\udfaf Finding your position... Click anywhere to stop highlighting"}),(0,m.jsxs)("div",{className:"absolute inset-0 overflow-hidden",children:[(0,m.jsx)("div",{className:"absolute -top-40 -right-40 w-80 h-80 bg-purple-600 rounded-full mix-blend-multiply filter blur-xl opacity-15 animate-blob"}),(0,m.jsx)("div",{className:"absolute -bottom-40 -left-40 w-80 h-80 bg-blue-600 rounded-full mix-blend-multiply filter blur-xl opacity-15 animate-blob animation-delay-2000"}),(0,m.jsx)("div",{className:"absolute top-40 left-40 w-80 h-80 bg-indigo-600 rounded-full mix-blend-multiply filter blur-xl opacity-15 animate-blob animation-delay-4000"}),(0,m.jsx)("div",{className:"absolute top-1/2 right-1/3 w-60 h-60 bg-cyan-600 rounded-full mix-blend-multiply filter blur-xl opacity-10 animate-blob animation-delay-6000"})]}),(0,m.jsx)("div",{className:"absolute inset-0 overflow-hidden pointer-events-none",children:[...Array(20)].map(((e,t)=>(0,m.jsx)(n.E.div,{className:"absolute w-2 h-2 bg-white rounded-full opacity-20",animate:{y:[0,-100,0],x:[0,100*Math.random()-50,0],opacity:[.2,.8,.2]},transition:{duration:3+2*Math.random(),repeat:1/0,delay:2*Math.random()},style:{left:"".concat(100*Math.random(),"%"),top:"".concat(100*Math.random(),"%")}},t)))}),(0,m.jsxs)("div",{className:"relative z-10",children:[(0,m.jsx)(n.E.div,{initial:{opacity:0,y:-20},animate:{opacity:1,y:0},transition:{duration:.8},className:"px-2 sm:px-4 md:px-6 lg:px-8 py-2 sm:py-4 md:py-6 lg:py-8",style:{padding:window.innerWidth<=768?"8px":window.innerWidth<=1024?"16px":"32px"},children:(0,m.jsx)("div",{className:"max-w-7xl mx-auto",children:(0,m.jsx)("div",{className:"bg-white/5 backdrop-blur-lg rounded-xl sm:rounded-2xl md:rounded-3xl p-3 sm:p-4 md:p-6 lg:p-8 border border-white/10",children:(0,m.jsxs)("div",{className:"flex flex-col sm:flex-row gap-2 sm:gap-3 md:gap-4 lg:gap-6 items-center justify-center",children:[(0,m.jsxs)(n.E.button,{whileHover:{scale:1.05},whileTap:{scale:.95},onClick:()=>b("/user/hub"),className:"flex items-center gap-2 md:gap-3 px-4 md:px-6 py-3 md:py-4 bg-gradient-to-r from-blue-600 to-indigo-600 text-white rounded-xl font-bold shadow-lg hover:shadow-xl transition-all duration-300 w-full sm:w-auto",style:{fontSize:window.innerWidth<768?"1rem":"1.1rem"},children:[(0,m.jsx)(d.diY,{className:"w-5 h-5 md:w-6 md:h-6"}),(0,m.jsx)("span",{children:"Hub"})]}),re&&(0,m.jsxs)(n.E.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:"flex items-center gap-2 md:gap-3 px-4 md:px-6 py-3 md:py-4 bg-gradient-to-r from-blue-500 to-purple-600 text-white rounded-xl font-bold shadow-lg",style:{background:"podium"===re.type?"linear-gradient(135deg, #FFD700, #FFA500)":"linear-gradient(135deg, #3B82F6, #8B5CF6)",color:"podium"===re.type?"#1F2937":"#FFFFFF",boxShadow:"0 4px 15px rgba(59, 130, 246, 0.3)",fontSize:window.innerWidth<768?"0.9rem":"1rem"},children:[(0,m.jsx)(d.gBl,{className:"w-5 h-5 md:w-6 md:h-6"}),(0,m.jsx)("span",{children:"podium"===re.type?"\ud83c\udfc6 Podium #".concat(re.position):"".concat(re.league," #").concat(re.position)})]}),u&&(0,m.jsx)(n.E.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:"bg-gradient-to-br from-blue-600/20 via-purple-600/20 to-indigo-600/20 backdrop-blur-lg rounded-2xl p-4 border border-blue-400/30 shadow-2xl max-w-sm",children:(0,m.jsxs)("div",{className:"flex items-center gap-4",children:[(0,m.jsxs)("div",{className:"flex-shrink-0 relative",children:[(0,m.jsx)(g.Z,{user:u,size:"xl",showOnlineStatus:!1,style:{border:"3px solid #facc15",boxShadow:"0 10px 25px rgba(0,0,0,0.15)"}}),u.isOnline&&(0,m.jsx)("div",{style:{position:"absolute",bottom:"4px",right:"4px",width:"16px",height:"16px",backgroundColor:"#22c55e",borderRadius:"50%",border:"3px solid #ffffff",boxShadow:"0 2px 8px rgba(34, 197, 94, 0.6)",zIndex:10},title:"Online"})]}),(0,m.jsxs)("div",{className:"flex-grow",children:[(0,m.jsx)("h3",{className:"text-lg font-bold text-white mb-2 truncate",children:u.name||u.username||"User"}),(0,m.jsxs)("div",{className:"grid grid-cols-2 gap-2 text-sm",children:[(0,m.jsxs)("div",{className:"bg-green-500/20 rounded-lg p-2 text-center",children:[(0,m.jsx)("div",{className:"text-green-300 text-xs",children:"Total XP"}),(0,m.jsx)("div",{className:"text-white font-bold",children:(u.totalXP||u.xp||u.points||u.totalPoints||0).toLocaleString()})]}),(0,m.jsxs)("div",{className:"bg-purple-500/20 rounded-lg p-2 text-center",children:[(0,m.jsx)("div",{className:"text-purple-300 text-xs",children:"Rank"}),(0,m.jsx)("div",{className:"text-white font-bold",children:(()=>{const e=f.find((e=>String(e._id)===String(u._id)));return e?"#".concat(e.rank):j?"#".concat(j):"N/A"})()})]}),(0,m.jsxs)("div",{className:"bg-blue-500/20 rounded-lg p-2 text-center",children:[(0,m.jsx)("div",{className:"text-blue-300 text-xs",children:"League"}),(0,m.jsx)("div",{className:"text-white font-bold text-xs",children:(()=>{const e=u.totalXP||u.xp||u.points||u.totalPoints||0;for(const[a,o]of Object.entries(U)){var t;if(null===(t=o.users)||void 0===t?void 0:t.find((e=>String(e._id)===String(u._id)))){const t=V(e);return"".concat(t.leagueIcon," ").concat(a.toUpperCase())}}if(e>0){const t=V(e);return"".concat(t.leagueIcon," ").concat(t.league.toUpperCase())}return"\ud83d\udd30 Unranked"})()})]}),(0,m.jsxs)("div",{className:"bg-orange-500/20 rounded-lg p-2 text-center",children:[(0,m.jsx)("div",{className:"text-orange-300 text-xs",children:"Quizzes"}),(0,m.jsx)("div",{className:"text-white font-bold",children:u.quizzesCompleted||u.totalQuizzesTaken||u.quizzesTaken||u.totalQuizzes||0})]})]}),(0,m.jsxs)("div",{className:"grid grid-cols-3 gap-2 mt-2 text-xs",children:[(0,m.jsxs)("div",{className:"bg-yellow-500/20 rounded-lg p-1.5 text-center",children:[(0,m.jsx)("div",{className:"text-yellow-300 text-xs",children:"Level"}),(0,m.jsx)("div",{className:"text-white font-bold",children:u.currentLevel||u.level||1})]}),(0,m.jsxs)("div",{className:"bg-red-500/20 rounded-lg p-1.5 text-center",children:[(0,m.jsx)("div",{className:"text-red-300 text-xs",children:"Streak"}),(0,m.jsx)("div",{className:"text-white font-bold",children:u.currentStreak||u.streak||0})]}),(0,m.jsxs)("div",{className:"bg-cyan-500/20 rounded-lg p-1.5 text-center",children:[(0,m.jsx)("div",{className:"text-cyan-300 text-xs",children:"Avg Score"}),(0,m.jsxs)("div",{className:"text-white font-bold",children:[(()=>{const e=u.averageScore||u.avgScore||0;return Math.round(e)})(),"%"]})]})]}),(()=>{for(const[t,a]of Object.entries(U)){var e;const t=null===(e=a.users)||void 0===e?void 0:e.findIndex((e=>String(e._id)===String(u._id)));if(-1!==t&&void 0!==t)return(0,m.jsx)("div",{className:"mt-2 text-center",children:(0,m.jsxs)("div",{className:"bg-gradient-to-r from-yellow-400/20 to-orange-400/20 rounded-lg p-1.5",children:[(0,m.jsx)("div",{className:"text-yellow-300 text-xs",children:"League Position"}),(0,m.jsxs)("div",{className:"text-white font-bold text-sm",children:["#",t+1," of ",a.users.length]})]})})}return null})()]})]})}),(0,m.jsxs)("div",{className:"flex flex-col items-center gap-4 bg-white/5 backdrop-blur-lg rounded-2xl p-6 border border-white/10 max-w-4xl mx-auto",children:[(0,m.jsx)(n.E.h3,{className:"text-2xl md:text-3xl font-black mb-2",style:{background:"linear-gradient(45deg, #FFD700, #FFA500, #FF6B35)",WebkitBackgroundClip:"text",WebkitTextFillColor:"transparent",textShadow:"2px 2px 4px rgba(0,0,0,0.8)",filter:"drop-shadow(0 0 10px #FFD700)"},animate:{scale:[1,1.02,1]},transition:{duration:3,repeat:1/0},children:"\ud83c\udfc6 LEAGUES \ud83c\udfc6"}),(0,m.jsx)("div",{className:"flex flex-wrap items-center justify-center gap-3 md:gap-4",children:te().map((e=>{var t;const a=K[e],o=L===e,r=(null===(t=U[e])||void 0===t?void 0:t.users.length)||0;return(0,m.jsxs)(n.E.div,{className:"flex flex-col items-center gap-2",whileHover:{scale:1.05},children:[(0,m.jsxs)(n.E.button,{whileHover:{scale:1.1,y:-3},whileTap:{scale:.95},onClick:()=>ee(e),className:"relative flex items-center justify-center w-16 h-16 md:w-20 md:h-20 rounded-2xl transition-all duration-300 ".concat(o?"ring-4 ring-yellow-400 ring-opacity-100 shadow-2xl":"hover:ring-2 hover:ring-white/30"),style:{background:o?"linear-gradient(135deg, ".concat(a.borderColor,"80, ").concat(a.textColor,"50, ").concat(a.borderColor,"80)"):"linear-gradient(135deg, ".concat(a.borderColor,"60, ").concat(a.textColor,"30)"),border:"3px solid ".concat(o?"#FFD700":a.borderColor+"80"),boxShadow:o?"0 0 30px ".concat(a.shadowColor,"80, 0 0 60px #FFD70080, 0 6px 30px ").concat(a.shadowColor,"80"):"0 4px 15px ".concat(a.shadowColor,"40"),transform:o?"scale(1.1)":"scale(1)",filter:o?"brightness(1.3) saturate(1.2)":"brightness(1)"},animate:o?{boxShadow:["0 0 30px ".concat(a.shadowColor,"80, 0 0 60px #FFD70080"),"0 0 40px ".concat(a.shadowColor,"100, 0 0 80px #FFD700A0"),"0 0 30px ".concat(a.shadowColor,"80, 0 0 60px #FFD70080")],scale:[1.1,1.15,1.1]}:{},transition:{duration:2,repeat:o?1/0:0,ease:"easeInOut"},title:"Click to view ".concat(a.title," League (").concat(r," users)"),children:[(0,m.jsx)("span",{className:"text-3xl md:text-4xl",children:a.leagueIcon}),o&&(0,m.jsx)(n.E.div,{initial:{scale:0,rotate:-360,opacity:0},animate:{scale:[1,1.3,1],rotate:[0,360,720],opacity:1,boxShadow:["0 0 15px rgba(255, 215, 0, 1), 0 0 30px rgba(255, 215, 0, 0.8)","0 0 25px rgba(255, 215, 0, 1), 0 0 50px rgba(255, 215, 0, 1)","0 0 15px rgba(255, 215, 0, 1), 0 0 30px rgba(255, 215, 0, 0.8)"]},transition:{scale:{duration:2,repeat:1/0,ease:"easeInOut"},rotate:{duration:4,repeat:1/0,ease:"linear"},boxShadow:{duration:1.5,repeat:1/0,ease:"easeInOut"},opacity:{duration:.3}},className:"absolute -top-3 -right-3 w-8 h-8 bg-gradient-to-r from-yellow-400 via-orange-400 to-yellow-500 rounded-full flex items-center justify-center border-3 border-white shadow-lg",style:{background:"linear-gradient(45deg, #FFD700, #FFA500, #FFD700)",border:"3px solid white",zIndex:10},children:(0,m.jsx)(n.E.span,{className:"text-sm font-black text-gray-900",animate:{scale:[1,1.2,1],rotate:[0,-10,10,0]},transition:{duration:1,repeat:1/0,ease:"easeInOut"},children:"\u2713"})}),(0,m.jsx)("div",{className:"absolute -bottom-2 -right-2 w-6 h-6 rounded-full flex items-center justify-center text-xs font-bold border-2 border-white",style:{background:a.borderColor,color:"#FFFFFF",fontSize:"11px"},children:r})]}),(0,m.jsx)(n.E.div,{className:"text-center",whileHover:{scale:1.05},children:(0,m.jsx)("div",{className:"text-xs md:text-sm font-bold px-2 py-1 rounded-lg",style:{color:a.nameColor,textShadow:"1px 1px 2px ".concat(a.shadowColor),background:"".concat(a.borderColor,"20"),border:"1px solid ".concat(a.borderColor,"40")},children:a.title})})]},e)}))}),(0,m.jsx)("p",{className:"text-white/70 text-sm text-center mt-2",children:"Click any league to view its members and scroll to their section"})]}),(0,m.jsxs)(n.E.button,{whileHover:{scale:1.05,rotate:180},whileTap:{scale:.95},onClick:ae,disabled:v,className:"flex items-center gap-3 px-6 py-3 md:py-4 bg-gradient-to-r from-purple-600 to-pink-600 text-white rounded-xl font-medium shadow-lg hover:shadow-xl transition-all duration-300 disabled:opacity-50 w-full sm:w-auto",style:{fontSize:window.innerWidth<768?"1rem":"1.1rem"},children:[(0,m.jsx)(d.T8D,{className:"w-5 h-5 md:w-6 md:h-6 ".concat(v?"animate-spin":"")}),(0,m.jsx)("span",{children:"Refresh"})]})]})})})}),!1,(0,m.jsx)(n.E.div,{initial:{opacity:0,y:-50},animate:{opacity:1,y:0},transition:{duration:1,ease:"easeOut"},className:"relative overflow-hidden mb-8",children:(0,m.jsxs)("div",{className:"bg-gradient-to-br from-blue-600 via-indigo-500 via-purple-500 via-cyan-500 to-teal-500 relative",children:[(0,m.jsx)("div",{className:"absolute inset-0 bg-gradient-to-t from-black/40 via-black/20 to-transparent"}),(0,m.jsx)("div",{className:"absolute inset-0 bg-gradient-to-r from-transparent via-white/5 to-transparent"}),(0,m.jsx)("div",{className:"relative z-10 px-3 sm:px-4 md:px-6 lg:px-8 py-6 sm:py-8 md:py-12 lg:py-20",children:(0,m.jsxs)("div",{className:"max-w-7xl mx-auto text-center",children:[(0,m.jsx)(n.E.div,{animate:{scale:[1,1.02,1],rotateY:[0,5,0]},transition:{duration:4,repeat:1/0,ease:"easeInOut"},className:"mb-6 md:mb-8",children:(0,m.jsxs)("h1",{className:"text-2xl sm:text-3xl md:text-4xl lg:text-5xl xl:text-6xl font-black mb-2 md:mb-4 tracking-tight",children:[(0,m.jsx)(n.E.span,{animate:{backgroundPosition:["0% 50%","100% 50%","0% 50%"]},transition:{duration:4,repeat:1/0,ease:"linear"},className:"bg-gradient-to-r from-yellow-300 via-pink-300 via-cyan-300 via-purple-300 to-yellow-300 bg-clip-text text-transparent bg-400%",style:{backgroundSize:"400% 400%",WebkitBackgroundClip:"text",WebkitTextFillColor:"transparent",filter:"drop-shadow(2px 2px 4px rgba(0,0,0,0.8))"},children:"HALL OF"}),(0,m.jsx)("br",{}),(0,m.jsx)(n.E.span,{animate:{textShadow:["0 0 20px rgba(255,215,0,0.8), 0 0 40px rgba(255,215,0,0.6)","0 0 30px rgba(255,215,0,1), 0 0 60px rgba(255,215,0,0.8)","0 0 20px rgba(255,215,0,0.8), 0 0 40px rgba(255,215,0,0.6)"]},transition:{duration:2.5,repeat:1/0,ease:"easeInOut"},style:{color:"#FFD700",fontWeight:"900",textShadow:"3px 3px 6px rgba(0,0,0,0.9)"},children:"CHAMPIONS"})]})}),(0,m.jsx)(n.E.p,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.5,duration:.8},className:"text-sm sm:text-base md:text-lg lg:text-xl font-semibold mb-4 md:mb-6 max-w-4xl mx-auto leading-relaxed px-2",style:{color:"#F3F4F6",textShadow:"2px 2px 4px rgba(0,0,0,0.8)",background:"linear-gradient(45deg, #F3F4F6, #E5E7EB)",WebkitBackgroundClip:"text",WebkitTextFillColor:"transparent"},children:"\u2728 Where legends are born and greatness is celebrated \u2728"}),(0,m.jsx)(n.E.div,{initial:{opacity:0,scale:.9},animate:{opacity:1,scale:1},transition:{delay:.8,duration:.8},className:"mb-6 md:mb-8",children:(0,m.jsx)("p",{className:"text-sm sm:text-base md:text-lg font-medium text-yellow-200 bg-black/20 backdrop-blur-sm rounded-xl px-4 py-3 max-w-3xl mx-auto border border-yellow-400/30",style:{textShadow:"2px 2px 4px rgba(0,0,0,0.8)",fontStyle:"italic"},children:I})}),(0,m.jsx)(n.E.div,{initial:{opacity:0,y:30},animate:{opacity:1,y:0},transition:{delay:1,duration:.8},className:"grid grid-cols-2 sm:grid-cols-4 gap-3 sm:gap-4 md:gap-6 max-w-4xl mx-auto",children:[{icon:d.HLl,value:f.length,label:"Champions",bgGradient:"from-blue-600/20 via-indigo-600/20 to-purple-600/20",iconColor:"#60A5FA",borderColor:"#3B82F6"},{icon:d.gBl,value:oe.length,label:"Top Performers",bgGradient:"from-yellow-600/20 via-orange-600/20 to-red-600/20",iconColor:"#FBBF24",borderColor:"#F59E0B"},{icon:d.p8m,value:f.filter((e=>e.currentStreak>0)).length,label:"Active Streaks",bgGradient:"from-red-600/20 via-pink-600/20 to-rose-600/20",iconColor:"#F87171",borderColor:"#EF4444"},{icon:d.jsT,value:f.reduce(((e,t)=>e+(t.totalXP||0)),0).toLocaleString(),label:"Total XP",bgGradient:"from-green-600/20 via-emerald-600/20 to-teal-600/20",iconColor:"#34D399",borderColor:"#10B981"}].map(((e,t)=>(0,m.jsxs)(n.E.div,{initial:{opacity:0,scale:.8},animate:{opacity:1,scale:1},transition:{delay:1.2+.1*t,duration:.6},whileHover:{scale:1.05,y:-5},className:"bg-gradient-to-br ".concat(e.bgGradient," backdrop-blur-lg rounded-xl p-3 md:p-4 text-center relative overflow-hidden"),style:{border:"2px solid ".concat(e.borderColor,"40"),boxShadow:"0 8px 32px ".concat(e.borderColor,"20")},children:[(0,m.jsx)("div",{className:"absolute inset-0 bg-gradient-to-br from-white/5 to-transparent"}),(0,m.jsx)(e.icon,{className:"w-6 h-6 md:w-8 md:h-8 mx-auto mb-2 relative z-10",style:{color:e.iconColor,filter:"drop-shadow(0 2px 4px rgba(0,0,0,0.5))"}}),(0,m.jsx)("div",{className:"text-lg sm:text-xl md:text-2xl lg:text-3xl font-black mb-1 relative z-10",style:{color:e.iconColor,textShadow:"3px 3px 6px rgba(0,0,0,0.9)",filter:"drop-shadow(0 0 10px currentColor)",fontSize:"clamp(1rem, 4vw, 2.5rem)"},children:e.value}),(0,m.jsx)("div",{className:"text-xs sm:text-sm font-bold relative z-10",style:{color:"#FFFFFF",textShadow:"2px 2px 4px rgba(0,0,0,0.9)",fontSize:"clamp(0.75rem, 2vw, 1rem)"},children:e.label})]},t)))})]})})]})}),v&&(0,m.jsxs)(n.E.div,{initial:{opacity:0},animate:{opacity:1},className:"flex flex-col items-center justify-center py-20",children:[(0,m.jsx)(n.E.div,{animate:{rotate:360},transition:{duration:2,repeat:1/0,ease:"linear"},className:"w-16 h-16 border-4 border-purple-500 border-t-transparent rounded-full mb-4"}),(0,m.jsx)("p",{className:"text-white/80 text-lg font-medium",children:"Loading champions..."})]}),!v&&(0,m.jsx)(n.E.div,{initial:{opacity:0,y:30},animate:{opacity:1,y:0},transition:{delay:.3,duration:.8},className:"px-4 sm:px-6 md:px-8 lg:px-12 pb-20 md:pb-24 lg:pb-32",children:(0,m.jsxs)("div",{className:"max-w-7xl mx-auto",children:[oe.length>0&&(0,m.jsxs)(n.E.div,{initial:{opacity:0,scale:.9},animate:{opacity:1,scale:1},transition:{delay:.5,duration:.8},className:"mb-12",children:[(0,m.jsx)("h2",{className:"text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-black text-center mb-6 md:mb-8 lg:mb-12 px-4",style:{background:"linear-gradient(45deg, #FFD700, #FFA500, #FF6B35)",WebkitBackgroundClip:"text",WebkitTextFillColor:"transparent",textShadow:"3px 3px 6px rgba(0,0,0,0.8)",filter:"drop-shadow(0 0 15px #FFD700)"},children:"\ud83c\udfc6 CHAMPIONS PODIUM \ud83c\udfc6"}),(0,m.jsxs)("div",{className:"flex items-end justify-center max-w-5xl mx-auto mb-4 md:mb-8",style:{gap:window.innerWidth<=768?"8px":window.innerWidth<=1024?"16px":"32px",padding:window.innerWidth<=768?"8px":"16px 24px"},children:[oe[1]&&(0,m.jsxs)(n.E.div,{ref:p&&String(oe[1]._id)===String(p._id)?Y:null,"data-user-id":oe[1]._id,"data-user-rank":2,initial:{opacity:0,x:-100,y:50},animate:{opacity:1,x:0,y:0,scale:[1,1.02,1],rotateY:[0,5,0]},transition:{delay:.8,duration:1.2,scale:{duration:4,repeat:1/0,ease:"easeInOut"},rotateY:{duration:6,repeat:1/0,ease:"easeInOut"}},whileHover:{scale:1.05,y:-10},className:"relative order-1 ".concat(ie(oe[1]._id)?"ring-8 ring-yellow-400 ring-opacity-100":""),style:{height:window.innerWidth<=768?"200px":"280px",transform:ie(oe[1]._id)?"scale(1.08)":"scale(1)",filter:ie(oe[1]._id)?"brightness(1.3) saturate(1.2) drop-shadow(0 0 30px rgba(255, 215, 0, 1))":"none",transition:"all 0.3s ease",border:ie(oe[1]._id)?"4px solid #FFD700":"none",borderRadius:ne(oe[1]._id)?"20px":"0px",background:ne(oe[1]._id)?"linear-gradient(45deg, rgba(255, 215, 0, 0.1), rgba(255, 165, 0, 0.1))":"transparent"},children:[(0,m.jsx)("div",{className:"absolute bottom-0 w-full h-20 bg-gradient-to-t from-gray-400 to-gray-300 rounded-t-lg border-2 border-gray-500 flex items-center justify-center z-10",children:(0,m.jsx)("span",{className:"text-2xl font-black text-gray-800 relative z-20",children:"2nd"})}),(0,m.jsx)("div",{className:"relative bg-gradient-to-br ".concat(oe[1].tier.color," p-1 rounded-xl ").concat(oe[1].tier.glow," shadow-xl mb-20"),style:{boxShadow:"0 6px 20px ".concat(oe[1].tier.shadowColor,"50"),width:"200px"},children:(0,m.jsxs)("div",{className:"".concat(oe[1].tier.bgColor," backdrop-blur-lg rounded-xl p-4 text-center relative overflow-hidden"),children:[(0,m.jsx)("div",{className:"absolute inset-0 bg-gradient-to-br from-white/5 to-transparent"}),(0,m.jsx)("div",{className:"absolute -top-3 left-1/2 transform -translate-x-1/2 w-10 h-10 bg-gradient-to-br from-gray-300 to-gray-500 rounded-full flex items-center justify-center font-black text-lg shadow-lg relative z-20",style:{color:"#1f2937",border:"2px solid #FFFFFF"},children:"\ud83e\udd48"}),(0,m.jsxs)("div",{className:"relative mx-auto mb-3 ".concat(p&&oe[1]._id===p._id?"ring-1 ring-yellow-400 ring-opacity-80":""),children:[(0,m.jsx)(g.Z,{user:oe[1],size:"md",showOnlineStatus:!0,style:{width:"40px",height:"40px"}}),console.log("\ud83e\udd48 Second place user:",oe[1])]}),(0,m.jsx)("h3",{className:"text-sm font-bold mb-2 truncate",style:{color:oe[1].tier.nameColor},children:oe[1].name}),(0,m.jsxs)("div",{className:"text-lg font-black mb-2",style:{color:oe[1].tier.textColor},children:[oe[1].totalXP.toLocaleString()," XP"]}),(0,m.jsxs)("div",{className:"flex justify-center gap-3 text-xs",children:[(0,m.jsxs)("span",{style:{color:oe[1].tier.textColor},children:["\ud83e\udde0 ",oe[1].totalQuizzesTaken]}),(0,m.jsxs)("span",{style:{color:oe[1].tier.textColor},children:["\ud83d\udd25 ",oe[1].currentStreak]})]})]})})]},"second-".concat(oe[1]._id)),oe[0]&&(0,m.jsxs)(n.E.div,{ref:p&&String(oe[0]._id)===String(p._id)?Y:null,"data-user-id":oe[0]._id,"data-user-rank":1,initial:{opacity:0,y:-100,scale:.8},animate:{opacity:1,y:0,scale:1,rotateY:[0,10,-10,0],y:[0,-10,0]},transition:{delay:.5,duration:1.5,rotateY:{duration:8,repeat:1/0,ease:"easeInOut"},y:{duration:4,repeat:1/0,ease:"easeInOut"}},whileHover:{scale:1.08,y:-15},className:"relative order-2 z-10 ".concat(ie(oe[0]._id)?"ring-8 ring-yellow-400 ring-opacity-100":""),style:{height:window.innerWidth<=768?"240px":"320px",transform:ie(oe[0]._id)?"scale(1.08)":"scale(1)",filter:ie(oe[0]._id)?"brightness(1.3) saturate(1.2) drop-shadow(0 0 30px rgba(255, 215, 0, 1))":"none",transition:"all 0.3s ease",border:ie(oe[0]._id)?"4px solid #FFD700":"none",borderRadius:ne(oe[0]._id)?"20px":"0px",background:ne(oe[0]._id)?"linear-gradient(45deg, rgba(255, 215, 0, 0.1), rgba(255, 165, 0, 0.1))":"transparent"},"data-section":"podium",children:[(0,m.jsx)("div",{className:"absolute bottom-0 w-full h-32 bg-gradient-to-t from-yellow-500 to-yellow-300 rounded-t-lg border-2 border-yellow-600 flex items-center justify-center z-10",children:(0,m.jsx)("span",{className:"text-3xl font-black text-yellow-900 relative z-20",children:"1st"})}),(0,m.jsx)(n.E.div,{animate:{rotate:[0,10,-10,0],y:[0,-5,0]},transition:{duration:3,repeat:1/0},className:"absolute -top-16 left-1/2 transform -translate-x-1/2 z-30",children:(0,m.jsx)(d.PWB,{className:"w-16 h-16 text-yellow-400 drop-shadow-lg"})}),(0,m.jsx)("div",{className:"relative bg-gradient-to-br ".concat(oe[0].tier.color," p-1.5 rounded-2xl ").concat(oe[0].tier.glow," shadow-2xl mb-32 transform scale-110"),style:{boxShadow:"0 8px 32px ".concat(oe[0].tier.shadowColor,"60, 0 0 0 1px rgba(255,255,255,0.1)"),width:"240px"},children:(0,m.jsxs)("div",{className:"".concat(oe[0].tier.bgColor," backdrop-blur-lg rounded-xl p-6 text-center relative overflow-hidden"),style:{background:"".concat(oe[0].tier.bgColor,", radial-gradient(circle at center, rgba(255,255,255,0.1) 0%, transparent 70%)")},children:[(0,m.jsx)("div",{className:"absolute inset-0 bg-gradient-to-br from-white/10 to-transparent rounded-xl"}),(0,m.jsx)("div",{className:"absolute -top-4 left-1/2 transform -translate-x-1/2 w-12 h-12 bg-gradient-to-br from-yellow-300 to-yellow-500 rounded-full flex items-center justify-center font-black text-xl shadow-lg relative z-20",style:{color:"#1f2937",textShadow:"1px 1px 2px rgba(0,0,0,0.3)",border:"2px solid #FFFFFF"},children:"\ud83d\udc51"}),(0,m.jsxs)("div",{className:"relative mx-auto mb-4 ".concat(p&&oe[0]._id===p._id?"ring-1 ring-yellow-400 ring-opacity-80":""),children:[(0,m.jsxs)("div",{className:"relative",children:[(0,m.jsx)(g.Z,{user:oe[0],size:"lg",showOnlineStatus:!1,style:{width:"48px",height:"48px"}}),oe[0].isOnline&&(0,m.jsx)("div",{style:{position:"absolute",bottom:"-2px",right:"-2px",width:"14px",height:"14px",backgroundColor:"#22c55e",borderRadius:"50%",border:"2px solid #ffffff",boxShadow:"0 2px 8px rgba(34, 197, 94, 0.6)",zIndex:10},title:"Online"})]}),console.log("\ud83e\udd47 First place user:",oe[0]),p&&oe[0]._id===p._id&&(0,m.jsx)("div",{className:"absolute -bottom-2 -right-2 rounded-full p-2 animate-pulse",style:{background:"linear-gradient(45deg, #fbbf24, #f59e0b)",boxShadow:"0 2px 8px rgba(0,0,0,0.3)"},children:(0,m.jsx)(d.jsT,{className:"w-6 h-6 text-gray-900"})})]}),(0,m.jsxs)("div",{className:"flex items-center justify-center gap-2 mb-2",children:[(0,m.jsx)("h3",{className:"text-lg font-black truncate",style:{color:oe[0].tier.nameColor,textShadow:"2px 2px 4px ".concat(oe[0].tier.shadowColor),filter:"drop-shadow(0 0 8px currentColor)"},children:oe[0].name}),ne(oe[0]._id)&&(0,m.jsx)("span",{className:"px-2 py-1 rounded-full text-xs font-black animate-pulse",style:{background:"linear-gradient(45deg, #FFD700, #FFA500)",color:"#1f2937",boxShadow:"0 2px 8px rgba(255,215,0,0.8)",border:"1px solid #FFFFFF",fontSize:"10px"},children:"\ud83c\udfaf YOU"})]}),(0,m.jsxs)("div",{className:"inline-flex items-center gap-2 px-4 py-2 bg-gradient-to-r ".concat(oe[0].tier.color," rounded-full text-sm font-black mb-3 relative z-10"),style:{background:"linear-gradient(135deg, ".concat(oe[0].tier.borderColor,", ").concat(oe[0].tier.textColor,")"),color:"#FFFFFF",textShadow:"2px 2px 4px rgba(0,0,0,0.8)",boxShadow:"0 4px 15px ".concat(oe[0].tier.shadowColor,"60"),border:"2px solid rgba(255,255,255,0.2)"},children:[oe[0].tier.icon&&r.createElement(oe[0].tier.icon,{className:"w-4 h-4",style:{color:"#FFFFFF"}}),(0,m.jsx)("span",{style:{color:"#FFFFFF"},children:oe[0].tier.title})]}),(0,m.jsxs)("div",{className:"space-y-2 relative z-10",children:[(0,m.jsxs)("div",{className:"text-xl font-black",style:{color:oe[0].tier.nameColor,textShadow:"2px 2px 4px ".concat(oe[0].tier.shadowColor),filter:"drop-shadow(0 0 8px currentColor)"},children:[oe[0].totalXP.toLocaleString()," XP"]}),(0,m.jsxs)("div",{className:"flex justify-center gap-4 text-sm",children:[(0,m.jsxs)("div",{className:"text-center",children:[(0,m.jsxs)("div",{className:"flex items-center gap-1 justify-center",children:[(0,m.jsx)(d.Kkf,{className:"w-4 h-4",style:{color:oe[0].tier.textColor}}),(0,m.jsx)("span",{className:"font-bold",style:{color:oe[0].tier.textColor},children:oe[0].totalQuizzesTaken})]}),(0,m.jsx)("div",{className:"text-xs opacity-80",style:{color:oe[0].tier.textColor},children:"Quizzes"})]}),(0,m.jsxs)("div",{className:"text-center",children:[(0,m.jsxs)("div",{className:"flex items-center gap-1 justify-center",children:[(0,m.jsx)(d.p8m,{className:"w-4 h-4",style:{color:"#FF6B35"}}),(0,m.jsx)("span",{className:"font-bold",style:{color:oe[0].tier.textColor},children:oe[0].currentStreak})]}),(0,m.jsx)("div",{className:"text-xs opacity-80",style:{color:oe[0].tier.textColor},children:"Streak"})]})]})]})]})})]},"first-".concat(oe[0]._id)),oe[2]&&(0,m.jsxs)(n.E.div,{ref:p&&String(oe[2]._id)===String(p._id)?Y:null,"data-user-id":oe[2]._id,"data-user-rank":3,initial:{opacity:0,x:100,y:50},animate:{opacity:1,x:0,y:0,scale:[1,1.02,1],rotateY:[0,-5,0]},transition:{delay:1,duration:1.2,scale:{duration:4,repeat:1/0,ease:"easeInOut"},rotateY:{duration:6,repeat:1/0,ease:"easeInOut"}},whileHover:{scale:1.05,y:-10},className:"relative order-3 ".concat(ie(oe[2]._id)?"ring-8 ring-yellow-400 ring-opacity-100":""),style:{height:window.innerWidth<=768?"200px":"280px",transform:ie(oe[2]._id)?"scale(1.08)":"scale(1)",filter:ie(oe[2]._id)?"brightness(1.3) saturate(1.2) drop-shadow(0 0 30px rgba(255, 215, 0, 1))":"none",transition:"all 0.3s ease",border:ie(oe[2]._id)?"4px solid #FFD700":"none",borderRadius:ne(oe[2]._id)?"20px":"0px",background:ne(oe[2]._id)?"linear-gradient(45deg, rgba(255, 215, 0, 0.1), rgba(255, 165, 0, 0.1))":"transparent"},children:[(0,m.jsx)("div",{className:"absolute bottom-0 w-full h-16 bg-gradient-to-t from-amber-600 to-amber-400 rounded-t-lg border-2 border-amber-700 flex items-center justify-center z-10",children:(0,m.jsx)("span",{className:"text-xl font-black text-amber-900 relative z-20",children:"3rd"})}),(0,m.jsx)("div",{className:"relative bg-gradient-to-br ".concat(oe[2].tier.color," p-1 rounded-xl ").concat(oe[2].tier.glow," shadow-xl mb-16"),style:{boxShadow:"0 6px 20px ".concat(oe[2].tier.shadowColor,"50"),width:"200px"},children:(0,m.jsxs)("div",{className:"".concat(oe[2].tier.bgColor," backdrop-blur-lg rounded-xl p-4 text-center relative overflow-hidden"),children:[(0,m.jsx)("div",{className:"absolute inset-0 bg-gradient-to-br from-white/5 to-transparent"}),(0,m.jsx)("div",{className:"absolute -top-3 left-1/2 transform -translate-x-1/2 w-10 h-10 bg-gradient-to-br from-amber-600 to-amber-800 rounded-full flex items-center justify-center font-black text-lg shadow-lg relative z-20",style:{color:"#1f2937",border:"2px solid #FFFFFF"},children:"\ud83e\udd49"}),(0,m.jsx)("div",{className:"relative mx-auto mb-3 ".concat(p&&oe[2]._id===p._id?"ring-1 ring-yellow-400 ring-opacity-80":""),children:(0,m.jsx)(g.Z,{user:oe[2],size:"md",showOnlineStatus:!0,style:{width:"40px",height:"40px"}})}),(0,m.jsx)("h3",{className:"text-sm font-bold mb-2 truncate",style:{color:oe[2].tier.nameColor},children:oe[2].name}),(0,m.jsxs)("div",{className:"text-lg font-black mb-2",style:{color:oe[2].tier.textColor},children:[oe[2].totalXP.toLocaleString()," XP"]}),(0,m.jsxs)("div",{className:"flex justify-center gap-3 text-xs",children:[(0,m.jsxs)("span",{style:{color:oe[2].tier.textColor},children:["\ud83e\udde0 ",oe[2].totalQuizzesTaken]}),(0,m.jsxs)("span",{style:{color:oe[2].tier.textColor},children:["\ud83d\udd25 ",oe[2].currentStreak]})]})]})})]},"third-".concat(oe[2]._id))]})]}),L?X.length>0&&(0,m.jsxs)(n.E.div,{initial:{opacity:0,y:30},animate:{opacity:1,y:0},transition:{delay:1,duration:.8},className:"mt-16 main-ranking-section",children:[(0,m.jsxs)("div",{className:"text-center mb-8 md:mb-12",children:[(0,m.jsxs)(n.E.h2,{className:"text-2xl sm:text-3xl md:text-4xl font-black mb-3",style:{background:"linear-gradient(45deg, ".concat(K[L].borderColor,", ").concat(K[L].textColor,")"),WebkitBackgroundClip:"text",WebkitTextFillColor:"transparent",textShadow:"2px 2px 4px rgba(0,0,0,0.8)",filter:"drop-shadow(0 0 12px ".concat(K[L].borderColor,")")},animate:{scale:[1,1.01,1]},transition:{duration:4,repeat:1/0},children:[K[L].leagueIcon," ",K[L].title," LEAGUE ",K[L].leagueIcon]}),(0,m.jsxs)("p",{className:"text-white/70 text-sm md:text-base font-medium",children:[X.length," champions in this league"]}),(0,m.jsx)(n.E.button,{whileHover:{scale:1.05},whileTap:{scale:.95},onClick:()=>B(null),className:"mt-4 px-6 py-2 bg-gradient-to-r from-gray-600 to-gray-700 text-white rounded-lg font-medium shadow-lg hover:shadow-xl transition-all duration-300",children:"\u2190 Back to All Leagues"})]}),(0,m.jsx)("div",{className:"max-w-6xl mx-auto px-4",children:(0,m.jsx)("div",{className:"grid gap-3 md:gap-4",children:X.map(((e,t)=>{const a=t+1,o=p&&String(e._id)===String(p._id);return(0,m.jsx)(n.E.div,{ref:o?Q:null,"data-user-id":e._id,"data-user-rank":a,initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.1+.05*t,duration:.4},whileHover:{scale:1.01,y:-2},className:"ranking-card group relative ".concat(ie(e._id)?"ring-8 ring-yellow-400 ring-opacity-100":""),style:{transform:ie(e._id)?"scale(1.05)":"scale(1)",filter:ie(e._id)?"brightness(1.25) saturate(1.3) drop-shadow(0 0 25px rgba(255, 215, 0, 1))":"none",transition:"all 0.3s ease",border:ie(e._id)?"4px solid #FFD700":"none",borderRadius:ie(e._id)?"16px":"0px",background:o?"linear-gradient(135deg, rgba(255, 215, 0, 0.15), rgba(255, 165, 0, 0.15))":"transparent",position:"relative",zIndex:o?10:1},children:(0,m.jsx)("div",{className:"bg-gradient-to-r ".concat(e.tier.color," p-0.5 rounded-2xl ").concat(e.tier.glow," transition-all duration-300 group-hover:scale-[1.01]"),style:{boxShadow:"0 4px 20px ".concat(e.tier.shadowColor,"40")},children:(0,m.jsxs)("div",{className:"".concat(e.tier.bgColor," backdrop-blur-xl rounded-2xl p-4 flex items-center gap-4 relative overflow-hidden"),style:{border:"1px solid ".concat(e.tier.borderColor,"30")},children:[(0,m.jsx)("div",{className:"absolute inset-0 bg-gradient-to-r from-white/3 to-transparent rounded-2xl"}),(0,m.jsxs)("div",{className:"flex items-center gap-3 flex-shrink-0",children:[(0,m.jsx)("div",{className:"relative",children:(0,m.jsxs)("div",{className:"w-10 h-10 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-full flex items-center justify-center font-black text-sm shadow-lg relative z-10",style:{color:"#FFFFFF",textShadow:"1px 1px 2px rgba(0,0,0,0.8)",border:"2px solid rgba(255,255,255,0.2)",boxShadow:"0 2px 8px rgba(0,0,0,0.3)"},children:["#",a]})}),(0,m.jsxs)("div",{className:"relative",children:[(0,m.jsx)(g.Z,{user:e,size:"sm",showOnlineStatus:!1,style:{width:"32px",height:"32px"}}),e.isOnline&&(0,m.jsx)("div",{style:{position:"absolute",bottom:"-2px",right:"-2px",width:"10px",height:"10px",backgroundColor:"#22c55e",borderRadius:"50%",border:"2px solid #ffffff",boxShadow:"0 2px 8px rgba(34, 197, 94, 0.6)",zIndex:10},title:"Online"}),o&&(0,m.jsx)("div",{className:"absolute -top-1 -right-1 w-4 h-4 rounded-full flex items-center justify-center animate-pulse",style:{background:"linear-gradient(45deg, #FFD700, #FFA500)",boxShadow:"0 2px 6px rgba(255,215,0,0.6)"},children:(0,m.jsx)(d.jsT,{className:"w-2.5 h-2.5 text-gray-900"})})]})]}),(0,m.jsx)("div",{className:"flex-1 min-w-0 px-2",children:(0,m.jsxs)("div",{className:"space-y-1",children:[(0,m.jsxs)("div",{className:"flex items-center gap-2 mb-1",children:[(0,m.jsx)("h3",{className:"text-base md:text-lg font-bold truncate",style:{color:e.tier.nameColor,textShadow:"1px 1px 2px ".concat(e.tier.shadowColor),filter:"drop-shadow(0 0 4px currentColor)"},children:e.name}),o&&(0,m.jsx)("span",{className:"px-3 py-1 rounded-full text-sm font-black animate-pulse",style:{background:"linear-gradient(45deg, #FFD700, #FFA500, #FFD700)",color:"#1f2937",boxShadow:"0 4px 12px rgba(255,215,0,0.8), 0 0 20px rgba(255,215,0,0.6)",border:"2px solid #FFFFFF",textShadow:"1px 1px 2px rgba(0,0,0,0.3)",fontSize:"12px",fontWeight:"900"},children:"\ud83c\udfaf YOU"})]}),(0,m.jsxs)("div",{className:"text-xs text-white/70 mt-0.5",children:[e.level," \u2022 Class ",e.class]})]})}),(0,m.jsxs)("div",{className:"flex flex-col items-end gap-1 flex-shrink-0",children:[(0,m.jsxs)("div",{className:"text-lg md:text-xl font-black mb-2",style:{color:e.tier.nameColor,textShadow:"1px 1px 2px ".concat(e.tier.shadowColor),filter:"drop-shadow(0 0 6px currentColor)"},children:[e.totalXP.toLocaleString()," XP"]}),(0,m.jsxs)("div",{className:"flex items-center gap-3 text-xs",children:[(0,m.jsxs)("div",{className:"flex items-center gap-1 px-2 py-1 rounded-md",style:{backgroundColor:"".concat(e.tier.borderColor,"20"),color:e.tier.textColor},children:[(0,m.jsx)(d.Kkf,{className:"w-3 h-3"}),(0,m.jsx)("span",{className:"font-medium",children:e.totalQuizzesTaken})]}),(0,m.jsxs)("div",{className:"flex items-center gap-1 px-2 py-1 rounded-md",style:{backgroundColor:"#FF6B3520",color:"#FF6B35"},children:[(0,m.jsx)(d.p8m,{className:"w-3 h-3"}),(0,m.jsx)("span",{className:"font-medium",children:e.currentStreak})]})]})]})]})})},e._id)}))})})]}):Object.keys(U).length>0&&(0,m.jsxs)(n.E.div,{initial:{opacity:0,y:30},animate:{opacity:1,y:0},transition:{delay:1,duration:.8},className:"mt-16 main-ranking-section",id:"grouped-leagues-section",children:[(0,m.jsxs)("div",{className:"text-center mb-8 md:mb-12",children:[(0,m.jsx)(n.E.h2,{className:"text-2xl sm:text-3xl md:text-4xl font-black mb-3",style:{background:"linear-gradient(45deg, #8B5CF6, #06B6D4, #10B981)",WebkitBackgroundClip:"text",WebkitTextFillColor:"transparent",textShadow:"2px 2px 4px rgba(0,0,0,0.8)",filter:"drop-shadow(0 0 12px #8B5CF6)"},animate:{scale:[1,1.01,1]},transition:{duration:4,repeat:1/0},children:"\ud83c\udfc6 LEAGUE RANKINGS \ud83c\udfc6"}),(0,m.jsx)("p",{className:"text-white/70 text-sm md:text-base font-medium",children:"Click on any league icon above to see its members"})]}),(0,m.jsx)("div",{className:"max-w-6xl mx-auto px-4 space-y-8",children:te().map((e=>{const t=K[e],a=U[e],o=a.users.slice(0,3);return(0,m.jsxs)(n.E.div,{ref:t=>Z.current[e]=t,initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.2,duration:.6},className:"bg-gradient-to-r from-white/5 to-white/10 backdrop-blur-lg rounded-2xl p-6 border border-white/10",id:"league-".concat(e),"data-league":e,children:[(0,m.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,m.jsxs)("div",{className:"flex items-center gap-4",children:[(0,m.jsx)("div",{className:"w-16 h-16 rounded-xl flex items-center justify-center text-3xl",style:{background:"linear-gradient(135deg, ".concat(t.borderColor,"40, ").concat(t.textColor,"20)"),border:"2px solid ".concat(t.borderColor,"60"),boxShadow:"0 4px 20px ".concat(t.shadowColor,"40")},children:t.leagueIcon}),(0,m.jsxs)("div",{children:[(0,m.jsxs)("h3",{className:"text-2xl font-black mb-1",style:{color:t.nameColor,textShadow:"2px 2px 4px ".concat(t.shadowColor),filter:"drop-shadow(0 0 8px currentColor)"},children:[t.title," LEAGUE"]}),(0,m.jsxs)("p",{className:"text-white/70 text-sm",children:[a.users.length," champions \u2022 ",t.description]})]})]}),(0,m.jsxs)(n.E.button,{whileHover:{scale:1.05},whileTap:{scale:.95},onClick:()=>ee(e),className:"px-4 py-2 bg-gradient-to-r from-blue-600 to-indigo-600 text-white rounded-lg font-medium shadow-lg hover:shadow-xl transition-all duration-300",children:["View All (",a.users.length,")"]})]}),(0,m.jsx)("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4",children:o.map(((e,a)=>{const o=p&&e._id===p._id,r=a+1;return(0,m.jsx)(n.E.div,{"data-user-id":e._id,"data-user-rank":r,initial:{opacity:0,scale:.9},animate:{opacity:1,scale:1},transition:{delay:.3+.1*a,duration:.4},whileHover:{scale:1.02,y:-2},className:"relative ".concat(ie(e._id)?"ring-2 ring-yellow-400/60":""),children:(0,m.jsx)("div",{className:"bg-gradient-to-br ".concat(e.tier.color," p-0.5 rounded-xl ").concat(e.tier.glow," shadow-lg"),style:{boxShadow:"0 4px 15px ".concat(e.tier.shadowColor,"30")},children:(0,m.jsxs)("div",{className:"".concat(e.tier.bgColor," backdrop-blur-lg rounded-xl p-4 text-center relative overflow-hidden"),children:[(0,m.jsx)("div",{className:"absolute inset-0 bg-gradient-to-br from-white/5 to-transparent"}),(0,m.jsxs)("div",{className:"absolute -top-2 -right-2 w-6 h-6 rounded-full flex items-center justify-center font-black text-xs",style:{background:t.borderColor,color:"#FFFFFF",border:"2px solid #FFFFFF"},children:["#",r]}),(0,m.jsxs)("div",{className:"relative mx-auto mb-3 ".concat(o?"ring-1 ring-yellow-400 ring-opacity-80":""),children:[(0,m.jsx)(g.Z,{user:e,size:"md",showOnlineStatus:!0,style:{width:"40px",height:"40px"}}),o&&(0,m.jsx)("div",{className:"absolute -bottom-1 -right-1 w-4 h-4 rounded-full flex items-center justify-center animate-pulse",style:{background:"linear-gradient(45deg, #FFD700, #FFA500)",boxShadow:"0 2px 6px rgba(255,215,0,0.6)"},children:(0,m.jsx)(d.jsT,{className:"w-2.5 h-2.5 text-gray-900"})})]}),(0,m.jsxs)("h4",{className:"text-sm font-bold mb-2 truncate",style:{color:e.tier.nameColor},children:[e.name,o&&(0,m.jsx)("span",{className:"ml-1 text-xs text-yellow-400",children:"\ud83d\udc51"})]}),(0,m.jsxs)("div",{className:"text-lg font-black mb-2",style:{color:e.tier.textColor},children:[e.totalXP.toLocaleString()," XP"]}),(0,m.jsxs)("div",{className:"flex justify-center gap-3 text-xs",children:[(0,m.jsxs)("span",{style:{color:e.tier.textColor},children:["\ud83e\udde0 ",e.totalQuizzesTaken]}),(0,m.jsxs)("span",{style:{color:e.tier.textColor},children:["\ud83d\udd25 ",e.currentStreak]})]})]})})},e._id)}))}),a.users.length>3&&(0,m.jsx)("div",{className:"text-center mt-4",children:(0,m.jsxs)("p",{className:"text-white/60 text-sm",children:["+",a.users.length-3," more champions in this league"]})})]},e)}))})]}),0===f.length&&!v&&(0,m.jsxs)(n.E.div,{initial:{opacity:0,scale:.9},animate:{opacity:1,scale:1},className:"text-center py-20",children:[(0,m.jsx)(d.gBl,{className:"w-24 h-24 text-white/30 mx-auto mb-6"}),(0,m.jsx)("h3",{className:"text-2xl font-bold mb-4",style:{color:"#ffffff",textShadow:"2px 2px 4px rgba(0,0,0,0.8)",fontWeight:"800"},children:"No Champions Yet"}),(0,m.jsx)("p",{className:"text-lg",style:{color:"#e5e7eb",textShadow:"1px 1px 2px rgba(0,0,0,0.8)",fontWeight:"600"},children:"Be the first to take a quiz and claim your spot in the Hall of Champions!"})]})]})})]})]})]})}}}]);
//# sourceMappingURL=995.0e5a9fac.chunk.js.map