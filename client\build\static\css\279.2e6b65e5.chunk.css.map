{"version": 3, "file": "static/css/279.2e6b65e5.chunk.css", "mappings": "AAGA,iBACE,GAAK,SAAY,CACjB,IAAM,UAAc,CACpB,GAAO,SAAY,CACrB,CAEA,mBACE,GAAK,2BAA8B,CACnC,GAAO,0BAA6B,CACtC,CAEA,iBACE,MAAW,uBAA4B,CACvC,IAAM,2BAA8B,CACtC,CAEA,gBACE,MAAW,6BAA+C,CAC1D,IAAM,6BAA+C,CACvD,CAEA,qBACE,MAAW,uBAA0B,CACrC,oBAA0B,0BAA6B,CACvD,gBAAqB,yBAA4B,CACnD,CAEA,qBACE,GAEE,4BAA0C,CAD1C,kBAEF,CACA,IAEE,+BAA2C,CAD3C,qBAEF,CACA,GAEE,4BAAwC,CADxC,kBAEF,CACF,CAEA,oBACE,MAEE,aAAc,CADd,8BAEF,CACA,IAEE,aAAc,CADd,iDAEF,CACF,CAEA,sBACE,kBACE,uBACF,CACA,QACE,gCACF,CACA,IACE,+BACF,CACA,IACE,+BACF,CACF,CAEA,gBACE,OAEE,SAAU,CADV,kBAEF,CACF,CAEA,iBACE,MACE,SACF,CACA,IACE,UACF,CACF,CAEA,gBACE,GACE,uBACF,CACF,CAGA,iBACE,+BACF,CAGA,kBAEE,eAAgB,CADhB,iBAEF,CAEA,yBAWE,wCAAyC,CAJzC,qLAGkF,CATlF,UAAW,CAKX,WAAY,CAFZ,SAAU,CAQV,mBAAoB,CAVpB,iBAAkB,CAClB,QAAS,CAET,UAQF,CAEA,eAME,6DAGF,CAEA,8BANE,QAAS,CAFT,MAAO,CAKP,mBAAoB,CAPpB,cAAe,CAGf,OAAQ,CAFR,KAAM,CAKN,YAgBF,CAZA,eAWE,gCAAkC,CALlC,yJAMF,CAGA,kBACE,mCACF,CAEA,iBACE,0CACF,CAEA,mBACE,mCACF,CAEA,sBACE,iBACF,CAEA,6BAUE,2CAA4C,CAH5C,uDAAoF,CACpF,qBAAsB,CAFtB,YAAa,CALb,UAAW,CAGX,UAAW,CAFX,iBAAkB,CAGlB,WAAY,CAFZ,SAAU,CAMV,UAEF,CAGA,aAEE,kBAAmB,CAWnB,kCAA2B,CAA3B,0BAA2B,CAN3B,gBAAiB,CAFjB,kBAAmB,CACnB,qDAA0E,CAL1E,YAAa,CASb,cAAe,CADf,eAAgB,CANhB,QAAS,CACT,iBAAkB,CAQlB,iBAAkB,CADlB,iCAA2C,CAG3C,uBAAwB,CANxB,0CAOF,CAEA,oBACE,kDAA6D,CAC7D,sBAAsC,CAEtC,qDAA+E,CAD/E,UAEF,CAEA,0BAEE,qDAAgF,CADhF,0BAEF,CAEA,qBACE,kDAA6D,CAC7D,sBAAqC,CAErC,qDAA8E,CAD9E,UAEF,CAEA,2BAEE,qDAA+E,CAD/E,0BAEF,CAEA,sBAIE,gDAAmD,CAHnD,kDAA6D,CAC7D,sBAAsC,CAGtC,iDAA+E,CAF/E,UAGF,CAEA,iBACE,MAAW,qCAAwC,CACnD,IAAM,2CAA8C,CACpD,IAAM,yCAA4C,CACpD,CAEA,kBAGE,4CAAiD,CADjD,WAAY,CADZ,UAGF,CAGA,oBAKE,iDAAsD,CAFtD,0BAAyC,CACzC,kBAAmB,CAFnB,OAAQ,CADR,iBAKF,CAGA,0BACE,eACF,CAEA,uBAEE,kBAAmB,CADnB,YAAa,CAEb,6BAA8B,CAC9B,iBACF,CAEA,sBAGE,aAAc,CAFd,cAAe,CACf,eAEF,CAEA,2BAGE,aAAc,CAFd,cAAe,CACf,eAEF,CAEA,oBAGE,wBAAyB,CACzB,oBAAqB,CACrB,oCAA8C,CAH9C,WAAY,CAIZ,eAAgB,CALhB,UAMF,CAEA,qBAEE,iDAAuD,CACvD,oBAAqB,CAErB,8BAA6C,CAJ7C,WAAY,CAGZ,6BAEF,CAGA,qBAUE,kCAA2B,CAA3B,0BAA2B,CAT3B,+CAA6D,CAM7D,sBAA0C,CAL1C,kBAAmB,CACnB,8EAGwC,CAExC,eAAgB,CAChB,iBAEF,CAEA,4BAOE,mDAAsF,CANtF,UAAW,CAKX,UAAW,CAFX,MAAO,CAFP,iBAAkB,CAGlB,OAAQ,CAFR,KAKF,CAEA,uBACE,kDAA6D,CAE7D,UAAY,CAEZ,eAAgB,CAHhB,iBAAkB,CAElB,iBAEF,CAEA,8BAOE,sDAAiF,CADjF,QAAS,CALT,UAAW,CAGX,MAAO,CAIP,mBAAoB,CANpB,iBAAkB,CAGlB,OAAQ,CAFR,KAMF,CAEA,uBAEE,kBAAmB,CADnB,YAAa,CAEb,QAAS,CACT,kBACF,CAEA,6BAME,kBAAmB,CAHnB,gBAAoC,CACpC,kBAAmB,CACnB,YAAa,CAGb,cAAe,CACf,eAAiB,CAPjB,WAAY,CAKZ,sBAAuB,CANvB,UASF,CAEA,sBACE,cAAe,CACf,eAAgB,CAEhB,kBACF,CAGA,cAQE,+CAA6D,CAH7D,wBAAyB,CADzB,kBAAmB,CAKnB,cAAe,CAFf,kBAAmB,CAInB,eAAgB,CAThB,iBAAkB,CAQlB,iBAAkB,CAPlB,eAAgB,CAGhB,0CAMF,CAEA,qBAOE,uEAMC,CAZD,UAAW,CAKX,WAAY,CAFZ,UAAW,CAFX,iBAAkB,CAClB,KAAM,CAWN,wBAA0B,CAT1B,UAUF,CAEA,2BACE,SACF,CAEA,6BAQE,yCAA0C,CAD1C,mEAAkG,CADlG,QAAS,CALT,UAAW,CAGX,MAAO,CAKP,mBAAoB,CAPpB,iBAAkB,CAGlB,OAAQ,CAFR,KAOF,CAEA,oBAEE,kDAA6D,CAD7D,oBAAqB,CAErB,oDAEoC,CACpC,sCACF,CAEA,uBAEE,kDAA6D,CAD7D,oBAAqB,CAGrB,6EAGwC,CAJxC,aAAc,CAKd,sCACF,CAEA,sBAEE,kBAAmB,CADnB,YAAa,CAEb,QACF,CAEA,qBAME,kBAAmB,CAFnB,wBAAyB,CADzB,iBAAkB,CAOlB,aAAc,CALd,YAAa,CAIb,cAAe,CADf,eAAiB,CANjB,WAAY,CAKZ,sBAAuB,CAIvB,uBAAyB,CAVzB,UAWF,CAEA,4CAEE,kBAAmB,CADnB,oBAAqB,CAErB,UACF,CAEA,mBACE,QAAO,CACP,cAAe,CACf,eAAgB,CAChB,eACF,CAGA,mBAKE,4BAA6B,CAF7B,6BAA8B,CAC9B,gBAEF,CAEA,8BANE,kBAAmB,CADnB,YAkBF,CAXA,WAUE,WAAY,CALZ,kBAAmB,CAInB,cAAe,CAFf,cAAe,CADf,eAAgB,CAHhB,OAAQ,CACR,iBAAkB,CAIlB,uBAGF,CAEA,iBACE,qBACF,CAEA,kBACE,oBACF,CAEA,qBACE,kBAAmB,CACnB,aACF,CAEA,2BACE,kBAAmB,CACnB,+BACF,CAEA,8BACE,kBAAmB,CACnB,aAAc,CACd,kBAAmB,CACnB,cACF,CAEA,mBACE,iDAAuD,CAEvD,+BAA6C,CAD7C,UAEF,CAEA,yBACE,iDAAuD,CACvD,gCACF,CAEA,mBACE,iDAAuD,CAEvD,+BAA6C,CAD7C,UAEF,CAEA,yBACE,iDAAuD,CACvD,gCACF,CAGA,eAME,iCAAkC,CAFlC,0BAA2B,CAC3B,iBAAkB,CADlB,qBAA2B,CAF3B,WAAY,CADZ,UAMF,CAGA,aAOE,kBAAmB,CAJnB,wBAAyB,CACzB,kBAAmB,CAEnB,eAAgB,CAJhB,iBAAkB,CAMlB,uBACF,CAEA,mBAGE,eAAiB,CADjB,oBAAqB,CAErB,8BAA4C,CAH5C,YAIF,CAGA,mBACE,eAAiB,CAGjB,wBAAyB,CAFzB,kBAAmB,CACnB,gCAA2C,CAG3C,kBAAmB,CADnB,eAEF,CAEA,qBACE,iDAAuD,CAGvD,UAAY,CAFZ,iBAAkB,CAClB,iBAEF,CAEA,oBACE,cAAe,CACf,eAAiB,CACjB,iBAAkB,CAClB,iCACF,CAEA,oBAEE,eAA+B,CAD/B,cAEF,CAEA,sBACE,iBACF,CAEA,kBAGE,aAAS,CAFT,YAAa,CAEb,QAAS,CADT,wDAA2D,CAE3D,kBACF,CAEA,iBAGE,kBAAmB,CAEnB,wBAAyB,CADzB,kBAAmB,CAFnB,YAAa,CADb,iBAKF,CAEA,iBAOE,kBAAmB,CAFnB,kBAAmB,CACnB,YAAa,CAJb,WAAY,CAMZ,sBAAuB,CALvB,kBAAmB,CACnB,YAAa,CAHb,UAQF,CAEA,kBAGE,aAAc,CAFd,cAAe,CACf,eAAiB,CAEjB,iBACF,CAEA,kBAEE,aAAc,CADd,cAAe,CAEf,eACF,CAGA,kCACE,kBAAmB,CACnB,aACF,CAEA,mCACE,kBAAmB,CACnB,aACF,CAEA,iCACE,kBAAmB,CACnB,aACF,CAEA,oCACE,kBAAmB,CACnB,aACF,CAKA,iBAIE,aAAc,CAFd,cAAe,CACf,YAGF,CAEA,+BAHE,qBAAsB,CAJtB,UAYF,CALA,cAEE,kBAAmB,CADnB,iBAIF,CAEA,eAGE,qBAAsB,CAFtB,iBAAkB,CAClB,UAEF,CAEA,qBAIE,qBAAsB,CAHtB,aAAc,CAEd,cAAe,CADf,UAGF,CAEA,uBACE,iBACF,CAEA,sBACE,cAAe,CACf,eAAgB,CAChB,kBACF,CAEA,cAKE,qBAAsB,CADtB,cAAe,CADf,kBAAmB,CADnB,iBAAkB,CADlB,UAKF,CAEA,qBAIE,aAAc,CADd,cAAe,CADf,WAAY,CADZ,UAIF,CAEA,mBAGE,oBAAqB,CAFrB,cAAe,CACf,eAEF,CAEA,WAKE,qBAAsB,CAHtB,cAAe,CAEf,sBAAuB,CAHvB,iBAAkB,CAElB,UAGF,CAEA,mBACE,qBAAsB,CACtB,QAAS,CACT,UACF,CAEA,aAEE,cAAe,CACf,OAAQ,CAFR,gBAGF,CAEA,kBAEE,WAAY,CADZ,UAEF,CAEA,oBACE,UACF,CAEA,aAIE,qBAAsB,CAFtB,cAAe,CADf,iBAAkB,CAElB,UAEF,CAGA,yBACE,iBAGE,aAAc,CAFd,eAAgB,CAChB,YAEF,CAEA,cAEE,kBAAmB,CADnB,iBAEF,CAEA,eACE,iBACF,CAEA,qBACE,cACF,CAEA,uBACE,iBACF,CAEA,sBACE,cAAe,CACf,kBACF,CAEA,cAGE,cAAe,CADf,kBAAmB,CADnB,iBAGF,CAEA,qBAGE,cAAe,CADf,WAAY,CADZ,UAGF,CAEA,mBACE,cACF,CAEA,mBACE,kBAAmB,CAEnB,QAAS,CADT,6BAEF,CAEA,WAEE,cAAe,CAEf,eAAgB,CAHhB,iBAAkB,CAElB,UAEF,CAEA,aAEE,cAAe,CACf,OAAQ,CAFR,iBAGF,CAEA,kBAEE,WAAY,CADZ,UAEF,CAEA,oBACE,UACF,CAEA,kBAEE,QAAS,CADT,mCAEF,CAEA,aAEE,cAAe,CADf,iBAEF,CACF,CAGA,0BACE,iBAGE,aAAc,CAFd,eAAgB,CAChB,YAEF,CAEA,cAEE,kBAAmB,CADnB,iBAEF,CAEA,eACE,iBACF,CAEA,qBACE,cACF,CAEA,uBACE,iBACF,CAEA,sBACE,cAAe,CACf,kBACF,CAEA,cAGE,cAAe,CADf,kBAAmB,CADnB,iBAGF,CAEA,qBAGE,cAAe,CADf,WAAY,CADZ,UAGF,CAMA,8BAHE,cAOF,CAJA,WAGE,eAAgB,CAFhB,iBAGF,CAEA,aAEE,cAAe,CACf,QAAS,CAFT,iBAGF,CAEA,kBAEE,WAAY,CADZ,UAEF,CAEA,oBACE,WACF,CAEA,kBAEE,QAAS,CADT,mCAEF,CAEA,aAEE,cAAe,CADf,iBAEF,CACF,CAGA,0BACE,iBAGE,aAAc,CAFd,gBAAiB,CACjB,YAEF,CAEA,cAEE,kBAAmB,CADnB,iBAEF,CAEA,eACE,iBACF,CAEA,uBACE,iBACF,CAEA,sBACE,cAAe,CACf,kBACF,CAEA,cAGE,cAAe,CADf,kBAAmB,CADnB,iBAGF,CAEA,qBAGE,cAAe,CADf,WAAY,CADZ,UAGF,CAMA,8BAHE,cAOF,CAJA,WAGE,eAAgB,CAFhB,iBAGF,CAEA,aAEE,cAAe,CACf,QAAS,CAFT,iBAGF,CAEA,kBAEE,WAAY,CADZ,UAEF,CAEA,oBACE,WACF,CAEA,kBAEE,QAAS,CADT,mCAEF,CAEA,aAEE,cAAe,CADf,iBAEF,CACF,CAGA,yBACE,aAEE,cAAe,CACf,OAAQ,CAFR,iBAGF,CAEA,kBAEE,WAAY,CADZ,UAEF,CAEA,uBACE,iBACF,CAEA,6BAGE,cAAe,CADf,WAAY,CADZ,UAGF,CAEA,sBACE,cAAe,CACf,eACF,CAEA,cAEE,kBAAmB,CADnB,iBAEF,CAEA,qBAGE,cAAe,CADf,WAAY,CADZ,UAGF,CAEA,mBACE,cAAe,CACf,eACF,CAEA,WAEE,cAAe,CACf,OAAQ,CAFR,iBAGF,CAEA,mBACE,qBAAsB,CACtB,QACF,CAEA,8BAEE,sBAAuB,CADvB,UAEF,CAEA,0BACE,eACF,CAEA,iDAEE,cACF,CAEA,oBACE,UACF,CAEA,oBACE,cACF,CAEA,sBACE,iBACF,CAEA,kBAEE,QAAS,CADT,yBAEF,CAEA,iBACE,YACF,CAEA,kBACE,cACF,CAEA,kBACE,cACF,CAEA,aAEE,cAAe,CADf,iBAEF,CACF,CAGA,8CACE,cACE,eAAgB,CAChB,iBAAkB,CAClB,yBACF,CAEA,WAEE,iBAEF,CAEA,wBALE,eAAgB,CAEhB,yBAMF,CAEA,aACE,iBACF,CACF,CAGA,qDACE,aACE,wBACF,CACF,CAGA,kEACE,uCAEE,yCAA0C,CAC1C,2BACF,CACF,CAGA,qDACE,cAEE,iBAAkB,CADlB,gBAEF,CAEA,iBACE,WACF,CAEA,eACE,gBACF,CAEA,uBACE,iBACF,CAEA,sBACE,cAAe,CACf,kBACF,CAEA,cAEE,iBAAkB,CADlB,iBAEF,CAEA,mBACE,OACF,CACF", "sources": ["components/trial/TrialQuiz.css"], "sourcesContent": ["/* Trial Quiz Enhanced Styling - Premium Experience */\n\n/* Premium Animations */\n@keyframes flash {\n  0% { opacity: 0; }\n  50% { opacity: 0.4; }\n  100% { opacity: 0; }\n}\n\n@keyframes shimmer {\n  0% { transform: translateX(-100%); }\n  100% { transform: translateX(100%); }\n}\n\n@keyframes float {\n  0%, 100% { transform: translateY(0px); }\n  50% { transform: translateY(-10px); }\n}\n\n@keyframes glow {\n  0%, 100% { box-shadow: 0 0 20px rgba(102, 126, 234, 0.3); }\n  50% { box-shadow: 0 0 40px rgba(102, 126, 234, 0.6); }\n}\n\n@keyframes failShake {\n  0%, 100% { transform: translateX(0); }\n  10%, 30%, 50%, 70%, 90% { transform: translateX(-8px); }\n  20%, 40%, 60%, 80% { transform: translateX(8px); }\n}\n\n@keyframes failPulse {\n  0% {\n    transform: scale(1);\n    box-shadow: 0 0 0 0 rgba(239, 68, 68, 0.7);\n  }\n  70% {\n    transform: scale(1.05);\n    box-shadow: 0 0 0 20px rgba(239, 68, 68, 0);\n  }\n  100% {\n    transform: scale(1);\n    box-shadow: 0 0 0 0 rgba(239, 68, 68, 0);\n  }\n}\n\n@keyframes failGlow {\n  0%, 100% {\n    text-shadow: 0 0 10px rgba(239, 68, 68, 0.5);\n    color: #ef4444;\n  }\n  50% {\n    text-shadow: 0 0 20px rgba(239, 68, 68, 0.8), 0 0 30px rgba(239, 68, 68, 0.6);\n    color: #dc2626;\n  }\n}\n\n@keyframes failBounce {\n  0%, 20%, 53%, 80%, 100% {\n    transform: translate3d(0,0,0);\n  }\n  40%, 43% {\n    transform: translate3d(0, -15px, 0);\n  }\n  70% {\n    transform: translate3d(0, -7px, 0);\n  }\n  90% {\n    transform: translate3d(0, -2px, 0);\n  }\n}\n\n@keyframes ping {\n  75%, 100% {\n    transform: scale(2);\n    opacity: 0;\n  }\n}\n\n@keyframes pulse {\n  0%, 100% {\n    opacity: 1;\n  }\n  50% {\n    opacity: .5;\n  }\n}\n\n@keyframes spin {\n  to {\n    transform: rotate(360deg);\n  }\n}\n\n/* Flash Animation Classes */\n.flash-animation {\n  animation: flash 0.5s ease-in-out;\n}\n\n/* Premium Background Effects */\n.trial-background {\n  position: relative;\n  overflow: hidden;\n}\n\n.trial-background::before {\n  content: '';\n  position: absolute;\n  top: -50%;\n  left: -50%;\n  width: 200%;\n  height: 200%;\n  background:\n    radial-gradient(circle at 20% 80%, rgba(102, 126, 234, 0.1) 0%, transparent 50%),\n    radial-gradient(circle at 80% 20%, rgba(118, 75, 162, 0.1) 0%, transparent 50%),\n    radial-gradient(circle at 40% 40%, rgba(59, 130, 246, 0.05) 0%, transparent 50%);\n  animation: float 20s ease-in-out infinite;\n  pointer-events: none;\n}\n\n.flash-success {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: radial-gradient(circle, rgba(34, 197, 94, 0.4) 0%, rgba(34, 197, 94, 0.1) 100%);\n  z-index: 9999;\n  pointer-events: none;\n}\n\n.flash-failure {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background:\n    radial-gradient(circle at 50% 50%, rgba(239, 68, 68, 0.6) 0%, rgba(239, 68, 68, 0.3) 30%, rgba(239, 68, 68, 0.1) 60%, transparent 100%),\n    linear-gradient(45deg, rgba(220, 38, 38, 0.1) 0%, rgba(239, 68, 68, 0.2) 50%, rgba(220, 38, 38, 0.1) 100%);\n  z-index: 9999;\n  pointer-events: none;\n  animation: failPulse 0.6s ease-out;\n}\n\n/* Fail Animation Classes */\n.trial-fail-shake {\n  animation: failShake 0.8s ease-in-out;\n}\n\n.trial-fail-glow {\n  animation: failGlow 2s ease-in-out infinite;\n}\n\n.trial-fail-bounce {\n  animation: failBounce 1s ease-in-out;\n}\n\n.trial-fail-container {\n  position: relative;\n}\n\n.trial-fail-container::before {\n  content: '';\n  position: absolute;\n  top: -10px;\n  left: -10px;\n  right: -10px;\n  bottom: -10px;\n  background: linear-gradient(45deg, transparent, rgba(239, 68, 68, 0.1), transparent);\n  border-radius: inherit;\n  z-index: -1;\n  animation: failPulse 2s ease-in-out infinite;\n}\n\n/* Enhanced Timer Styling */\n.trial-timer {\n  display: flex;\n  align-items: center;\n  gap: 12px;\n  padding: 16px 32px;\n  border-radius: 16px;\n  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15), 0 5px 15px rgba(0, 0, 0, 0.1);\n  border: 3px solid;\n  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);\n  font-weight: 800;\n  font-size: 20px;\n  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);\n  position: relative;\n  backdrop-filter: blur(10px);\n  transform: translateY(0);\n}\n\n.trial-timer.normal {\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  border-color: rgba(102, 126, 234, 0.6);\n  color: white;\n  box-shadow: 0 15px 35px rgba(102, 126, 234, 0.4), 0 5px 15px rgba(0, 0, 0, 0.1);\n}\n\n.trial-timer.normal:hover {\n  transform: translateY(-2px);\n  box-shadow: 0 20px 40px rgba(102, 126, 234, 0.5), 0 8px 20px rgba(0, 0, 0, 0.15);\n}\n\n.trial-timer.warning {\n  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);\n  border-color: rgba(245, 87, 108, 0.6);\n  color: white;\n  box-shadow: 0 15px 35px rgba(245, 87, 108, 0.4), 0 5px 15px rgba(0, 0, 0, 0.1);\n}\n\n.trial-timer.warning:hover {\n  transform: translateY(-2px);\n  box-shadow: 0 20px 40px rgba(245, 87, 108, 0.5), 0 8px 20px rgba(0, 0, 0, 0.15);\n}\n\n.trial-timer.critical {\n  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);\n  border-color: rgba(255, 107, 107, 0.8);\n  color: white;\n  animation: pulse 1.5s infinite, shake 0.5s infinite;\n  box-shadow: 0 15px 35px rgba(255, 107, 107, 0.6), 0 5px 15px rgba(0, 0, 0, 0.2);\n}\n\n@keyframes shake {\n  0%, 100% { transform: translateX(0) translateY(0); }\n  25% { transform: translateX(-2px) translateY(-1px); }\n  75% { transform: translateX(2px) translateY(1px); }\n}\n\n.trial-timer-icon {\n  width: 24px;\n  height: 24px;\n  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));\n}\n\n/* Warning Ring Animation */\n.timer-warning-ring {\n  position: absolute;\n  inset: 0;\n  border: 2px solid rgba(253, 224, 71, 0.5);\n  border-radius: 12px;\n  animation: ping 2s cubic-bezier(0, 0, 0.2, 1) infinite;\n}\n\n/* Progress Bar Styling */\n.trial-progress-container {\n  margin-top: 16px;\n}\n\n.trial-progress-header {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  margin-bottom: 8px;\n}\n\n.trial-progress-label {\n  font-size: 14px;\n  font-weight: 500;\n  color: #374151;\n}\n\n.trial-progress-percentage {\n  font-size: 14px;\n  font-weight: 500;\n  color: #2563eb;\n}\n\n.trial-progress-bar {\n  width: 100%;\n  height: 12px;\n  background-color: #e5e7eb;\n  border-radius: 9999px;\n  box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1);\n  overflow: hidden;\n}\n\n.trial-progress-fill {\n  height: 100%;\n  background: linear-gradient(to right, #3b82f6, #4f46e5);\n  border-radius: 9999px;\n  transition: width 0.5s ease-out;\n  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);\n}\n\n/* Question Card Styling */\n.trial-question-card {\n  background: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%);\n  border-radius: 32px;\n  box-shadow:\n    0 32px 64px rgba(0, 0, 0, 0.12),\n    0 16px 32px rgba(0, 0, 0, 0.08),\n    inset 0 1px 0 rgba(255, 255, 255, 0.9);\n  border: 2px solid rgba(255, 255, 255, 0.8);\n  overflow: hidden;\n  position: relative;\n  backdrop-filter: blur(20px);\n}\n\n.trial-question-card::before {\n  content: '';\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  height: 1px;\n  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.8), transparent);\n}\n\n.trial-question-header {\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  padding: 40px 48px;\n  color: white;\n  position: relative;\n  overflow: hidden;\n}\n\n.trial-question-header::before {\n  content: '';\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, transparent 50%);\n  pointer-events: none;\n}\n\n.trial-question-number {\n  display: flex;\n  align-items: center;\n  gap: 12px;\n  margin-bottom: 16px;\n}\n\n.trial-question-number-badge {\n  width: 48px;\n  height: 48px;\n  background: rgba(255, 255, 255, 0.2);\n  border-radius: 12px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 20px;\n  font-weight: bold;\n}\n\n.trial-question-title {\n  font-size: 24px;\n  font-weight: 600;\n  line-height: 1.4;\n  margin-bottom: 24px;\n}\n\n/* Option Styling */\n.trial-option {\n  width: 100%;\n  padding: 24px 28px;\n  text-align: left;\n  border-radius: 20px;\n  border: 3px solid #e2e8f0;\n  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);\n  margin-bottom: 16px;\n  background: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%);\n  cursor: pointer;\n  position: relative;\n  overflow: hidden;\n}\n\n.trial-option::before {\n  content: '';\n  position: absolute;\n  top: 0;\n  left: -100%;\n  width: 100%;\n  height: 100%;\n  background: linear-gradient(90deg,\n    transparent,\n    rgba(102, 126, 234, 0.2),\n    rgba(255, 255, 255, 0.4),\n    rgba(102, 126, 234, 0.2),\n    transparent\n  );\n  transition: left 0.6s ease;\n}\n\n.trial-option:hover::before {\n  left: 100%;\n}\n\n.trial-option.selected::after {\n  content: '';\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: linear-gradient(45deg, transparent 30%, rgba(102, 126, 234, 0.1) 50%, transparent 70%);\n  animation: shimmer 2s ease-in-out infinite;\n  pointer-events: none;\n}\n\n.trial-option:hover {\n  border-color: #667eea;\n  background: linear-gradient(145deg, #f0f4ff 0%, #e0e7ff 100%);\n  box-shadow:\n    0 12px 24px rgba(102, 126, 234, 0.15),\n    0 4px 8px rgba(102, 126, 234, 0.1);\n  transform: translateY(-3px) scale(1.02);\n}\n\n.trial-option.selected {\n  border-color: #667eea;\n  background: linear-gradient(145deg, #e0e7ff 0%, #c7d2fe 100%);\n  color: #3730a3;\n  box-shadow:\n    0 16px 32px rgba(102, 126, 234, 0.25),\n    0 8px 16px rgba(102, 126, 234, 0.15),\n    inset 0 1px 0 rgba(255, 255, 255, 0.5);\n  transform: translateY(-2px) scale(1.03);\n}\n\n.trial-option-content {\n  display: flex;\n  align-items: center;\n  gap: 16px;\n}\n\n.trial-option-letter {\n  width: 48px;\n  height: 48px;\n  border-radius: 50%;\n  border: 2px solid #d1d5db;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-weight: bold;\n  font-size: 18px;\n  color: #6b7280;\n  transition: all 0.3s ease;\n}\n\n.trial-option.selected .trial-option-letter {\n  border-color: #2563eb;\n  background: #2563eb;\n  color: white;\n}\n\n.trial-option-text {\n  flex: 1;\n  font-size: 16px;\n  font-weight: 500;\n  line-height: 1.5;\n}\n\n/* Navigation Buttons */\n.trial-nav-buttons {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  padding-top: 32px;\n  border-top: 1px solid #e5e7eb;\n}\n\n.trial-btn {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  padding: 12px 24px;\n  border-radius: 12px;\n  font-weight: 600;\n  font-size: 16px;\n  transition: all 0.3s ease;\n  cursor: pointer;\n  border: none;\n}\n\n.trial-btn:hover {\n  transform: scale(1.02);\n}\n\n.trial-btn:active {\n  transform: scale(0.98);\n}\n\n.trial-btn-secondary {\n  background: #f3f4f6;\n  color: #374151;\n}\n\n.trial-btn-secondary:hover {\n  background: #e5e7eb;\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\n}\n\n.trial-btn-secondary:disabled {\n  background: #f9fafb;\n  color: #9ca3af;\n  cursor: not-allowed;\n  transform: none;\n}\n\n.trial-btn-primary {\n  background: linear-gradient(to right, #2563eb, #1d4ed8);\n  color: white;\n  box-shadow: 0 8px 25px rgba(37, 99, 235, 0.3);\n}\n\n.trial-btn-primary:hover {\n  background: linear-gradient(to right, #1d4ed8, #1e40af);\n  box-shadow: 0 12px 35px rgba(37, 99, 235, 0.4);\n}\n\n.trial-btn-success {\n  background: linear-gradient(to right, #059669, #047857);\n  color: white;\n  box-shadow: 0 8px 25px rgba(5, 150, 105, 0.3);\n}\n\n.trial-btn-success:hover {\n  background: linear-gradient(to right, #047857, #065f46);\n  box-shadow: 0 12px 35px rgba(5, 150, 105, 0.4);\n}\n\n/* Loading Spinner */\n.trial-spinner {\n  width: 20px;\n  height: 20px;\n  border: 2px solid rgba(255, 255, 255, 0.3);\n  border-top: 2px solid white;\n  border-radius: 50%;\n  animation: spin 1s linear infinite;\n}\n\n/* Input Styling */\n.trial-input {\n  width: 100%;\n  padding: 20px 24px;\n  border: 2px solid #d1d5db;\n  border-radius: 16px;\n  font-size: 16px;\n  font-weight: 500;\n  background: #f9fafb;\n  transition: all 0.3s ease;\n}\n\n.trial-input:focus {\n  outline: none;\n  border-color: #2563eb;\n  background: white;\n  box-shadow: 0 0 0 4px rgba(37, 99, 235, 0.1);\n}\n\n/* Result Page Styling */\n.trial-result-card {\n  background: white;\n  border-radius: 24px;\n  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);\n  border: 1px solid #f3f4f6;\n  overflow: hidden;\n  margin-bottom: 32px;\n}\n\n.trial-result-header {\n  background: linear-gradient(to right, #2563eb, #1d4ed8);\n  padding: 32px 40px;\n  text-align: center;\n  color: white;\n}\n\n.trial-result-score {\n  font-size: 72px;\n  font-weight: bold;\n  margin-bottom: 8px;\n  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);\n}\n\n.trial-result-label {\n  font-size: 20px;\n  color: rgba(255, 255, 255, 0.9);\n}\n\n.trial-result-content {\n  padding: 32px 40px;\n}\n\n.trial-stats-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n  gap: 24px;\n  margin-bottom: 32px;\n}\n\n.trial-stat-card {\n  text-align: center;\n  padding: 24px;\n  background: #f8fafc;\n  border-radius: 16px;\n  border: 1px solid #e2e8f0;\n}\n\n.trial-stat-icon {\n  width: 48px;\n  height: 48px;\n  margin: 0 auto 16px;\n  padding: 12px;\n  border-radius: 12px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.trial-stat-value {\n  font-size: 24px;\n  font-weight: bold;\n  color: #1f2937;\n  margin-bottom: 4px;\n}\n\n.trial-stat-label {\n  font-size: 14px;\n  color: #6b7280;\n  font-weight: 500;\n}\n\n/* Color variants for stat cards */\n.trial-stat-blue .trial-stat-icon {\n  background: #dbeafe;\n  color: #2563eb;\n}\n\n.trial-stat-green .trial-stat-icon {\n  background: #dcfce7;\n  color: #16a34a;\n}\n\n.trial-stat-red .trial-stat-icon {\n  background: #fee2e2;\n  color: #dc2626;\n}\n\n.trial-stat-purple .trial-stat-icon {\n  background: #f3e8ff;\n  color: #9333ea;\n}\n\n/* Enhanced Responsive Design - Mobile First */\n\n/* Base Mobile Styles (320px - 767px) */\n.trial-container {\n  width: 100%;\n  max-width: 100%;\n  padding: 12px;\n  margin: 0 auto;\n  box-sizing: border-box;\n}\n\n.trial-header {\n  padding: 12px 16px;\n  margin-bottom: 12px;\n  width: 100%;\n  box-sizing: border-box;\n}\n\n.trial-content {\n  padding: 16px 12px;\n  width: 100%;\n  box-sizing: border-box;\n}\n\n.trial-question-card {\n  margin: 0 auto;\n  width: 100%;\n  max-width: 100%;\n  box-sizing: border-box;\n}\n\n.trial-question-header {\n  padding: 16px 12px;\n}\n\n.trial-question-title {\n  font-size: 16px;\n  line-height: 1.4;\n  margin-bottom: 16px;\n}\n\n.trial-option {\n  width: 100%;\n  padding: 14px 16px;\n  margin-bottom: 10px;\n  font-size: 14px;\n  box-sizing: border-box;\n}\n\n.trial-option-letter {\n  width: 32px;\n  height: 32px;\n  font-size: 14px;\n  flex-shrink: 0;\n}\n\n.trial-option-text {\n  font-size: 14px;\n  line-height: 1.4;\n  word-wrap: break-word;\n}\n\n.trial-btn {\n  padding: 12px 16px;\n  font-size: 14px;\n  width: 100%;\n  justify-content: center;\n  box-sizing: border-box;\n}\n\n.trial-nav-buttons {\n  flex-direction: column;\n  gap: 10px;\n  width: 100%;\n}\n\n.trial-timer {\n  padding: 8px 12px;\n  font-size: 14px;\n  gap: 6px;\n}\n\n.trial-timer-icon {\n  width: 16px;\n  height: 16px;\n}\n\n.trial-progress-bar {\n  height: 6px;\n}\n\n.trial-input {\n  padding: 14px 16px;\n  font-size: 16px; /* Prevent zoom on iOS */\n  width: 100%;\n  box-sizing: border-box;\n}\n\n/* Tablet Styles (768px - 1023px) */\n@media (min-width: 768px) {\n  .trial-container {\n    max-width: 720px;\n    padding: 20px;\n    margin: 0 auto;\n  }\n\n  .trial-header {\n    padding: 20px 24px;\n    margin-bottom: 20px;\n  }\n\n  .trial-content {\n    padding: 24px 20px;\n  }\n\n  .trial-question-card {\n    max-width: 100%;\n  }\n\n  .trial-question-header {\n    padding: 24px 28px;\n  }\n\n  .trial-question-title {\n    font-size: 20px;\n    margin-bottom: 20px;\n  }\n\n  .trial-option {\n    padding: 18px 22px;\n    margin-bottom: 12px;\n    font-size: 15px;\n  }\n\n  .trial-option-letter {\n    width: 40px;\n    height: 40px;\n    font-size: 16px;\n  }\n\n  .trial-option-text {\n    font-size: 15px;\n  }\n\n  .trial-nav-buttons {\n    flex-direction: row;\n    justify-content: space-between;\n    gap: 16px;\n  }\n\n  .trial-btn {\n    padding: 14px 20px;\n    font-size: 15px;\n    width: auto;\n    min-width: 120px;\n  }\n\n  .trial-timer {\n    padding: 12px 20px;\n    font-size: 16px;\n    gap: 8px;\n  }\n\n  .trial-timer-icon {\n    width: 20px;\n    height: 20px;\n  }\n\n  .trial-progress-bar {\n    height: 8px;\n  }\n\n  .trial-stats-grid {\n    grid-template-columns: repeat(2, 1fr);\n    gap: 16px;\n  }\n\n  .trial-input {\n    padding: 16px 20px;\n    font-size: 16px;\n  }\n}\n\n/* Laptop Styles (1024px - 1279px) */\n@media (min-width: 1024px) {\n  .trial-container {\n    max-width: 960px;\n    padding: 28px;\n    margin: 0 auto;\n  }\n\n  .trial-header {\n    padding: 28px 36px;\n    margin-bottom: 28px;\n  }\n\n  .trial-content {\n    padding: 36px 32px;\n  }\n\n  .trial-question-card {\n    max-width: 100%;\n  }\n\n  .trial-question-header {\n    padding: 32px 40px;\n  }\n\n  .trial-question-title {\n    font-size: 24px;\n    margin-bottom: 24px;\n  }\n\n  .trial-option {\n    padding: 20px 24px;\n    margin-bottom: 14px;\n    font-size: 16px;\n  }\n\n  .trial-option-letter {\n    width: 44px;\n    height: 44px;\n    font-size: 18px;\n  }\n\n  .trial-option-text {\n    font-size: 16px;\n  }\n\n  .trial-btn {\n    padding: 16px 24px;\n    font-size: 16px;\n    min-width: 140px;\n  }\n\n  .trial-timer {\n    padding: 14px 24px;\n    font-size: 18px;\n    gap: 10px;\n  }\n\n  .trial-timer-icon {\n    width: 22px;\n    height: 22px;\n  }\n\n  .trial-progress-bar {\n    height: 10px;\n  }\n\n  .trial-stats-grid {\n    grid-template-columns: repeat(3, 1fr);\n    gap: 20px;\n  }\n\n  .trial-input {\n    padding: 18px 22px;\n    font-size: 16px;\n  }\n}\n\n/* Desktop Styles (1280px+) */\n@media (min-width: 1280px) {\n  .trial-container {\n    max-width: 1200px;\n    padding: 32px;\n    margin: 0 auto;\n  }\n\n  .trial-header {\n    padding: 32px 48px;\n    margin-bottom: 32px;\n  }\n\n  .trial-content {\n    padding: 48px 40px;\n  }\n\n  .trial-question-header {\n    padding: 40px 48px;\n  }\n\n  .trial-question-title {\n    font-size: 28px;\n    margin-bottom: 28px;\n  }\n\n  .trial-option {\n    padding: 24px 28px;\n    margin-bottom: 16px;\n    font-size: 17px;\n  }\n\n  .trial-option-letter {\n    width: 48px;\n    height: 48px;\n    font-size: 20px;\n  }\n\n  .trial-option-text {\n    font-size: 17px;\n  }\n\n  .trial-btn {\n    padding: 18px 28px;\n    font-size: 17px;\n    min-width: 160px;\n  }\n\n  .trial-timer {\n    padding: 16px 28px;\n    font-size: 20px;\n    gap: 12px;\n  }\n\n  .trial-timer-icon {\n    width: 24px;\n    height: 24px;\n  }\n\n  .trial-progress-bar {\n    height: 12px;\n  }\n\n  .trial-stats-grid {\n    grid-template-columns: repeat(4, 1fr);\n    gap: 24px;\n  }\n\n  .trial-input {\n    padding: 20px 24px;\n    font-size: 17px;\n  }\n}\n\n/* Mobile Specific Adjustments */\n@media (max-width: 767px) {\n  .trial-timer {\n    padding: 10px 16px;\n    font-size: 16px;\n    gap: 8px;\n  }\n\n  .trial-timer-icon {\n    width: 18px;\n    height: 18px;\n  }\n\n  .trial-question-header {\n    padding: 20px 16px;\n  }\n\n  .trial-question-number-badge {\n    width: 36px;\n    height: 36px;\n    font-size: 16px;\n  }\n\n  .trial-question-title {\n    font-size: 18px;\n    line-height: 1.4;\n  }\n\n  .trial-option {\n    padding: 16px 18px;\n    margin-bottom: 12px;\n  }\n\n  .trial-option-letter {\n    width: 36px;\n    height: 36px;\n    font-size: 14px;\n  }\n\n  .trial-option-text {\n    font-size: 14px;\n    line-height: 1.4;\n  }\n\n  .trial-btn {\n    padding: 12px 16px;\n    font-size: 14px;\n    gap: 6px;\n  }\n\n  .trial-nav-buttons {\n    flex-direction: column;\n    gap: 12px;\n  }\n\n  .trial-nav-buttons .trial-btn {\n    width: 100%;\n    justify-content: center;\n  }\n\n  .trial-progress-container {\n    margin-top: 12px;\n  }\n\n  .trial-progress-label,\n  .trial-progress-percentage {\n    font-size: 12px;\n  }\n\n  .trial-progress-bar {\n    height: 8px;\n  }\n\n  .trial-result-score {\n    font-size: 48px;\n  }\n\n  .trial-result-content {\n    padding: 20px 16px;\n  }\n\n  .trial-stats-grid {\n    grid-template-columns: 1fr;\n    gap: 12px;\n  }\n\n  .trial-stat-card {\n    padding: 16px;\n  }\n\n  .trial-stat-value {\n    font-size: 18px;\n  }\n\n  .trial-stat-label {\n    font-size: 12px;\n  }\n\n  .trial-input {\n    padding: 16px 20px;\n    font-size: 16px;\n  }\n}\n\n/* Touch-friendly adjustments for mobile */\n@media (max-width: 767px) and (pointer: coarse) {\n  .trial-option {\n    min-height: 56px;\n    padding: 16px 18px;\n    touch-action: manipulation;\n  }\n\n  .trial-btn {\n    min-height: 44px;\n    padding: 12px 18px;\n    touch-action: manipulation;\n  }\n\n  .trial-input {\n    min-height: 44px;\n    touch-action: manipulation;\n  }\n\n  .trial-timer {\n    touch-action: none;\n  }\n}\n\n/* Prevent zoom on input focus for iOS */\n@media screen and (-webkit-min-device-pixel-ratio: 0) {\n  .trial-input {\n    font-size: 16px !important;\n  }\n}\n\n/* High DPI displays */\n@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {\n  .trial-option-letter,\n  .trial-timer-icon {\n    image-rendering: -webkit-optimize-contrast;\n    image-rendering: crisp-edges;\n  }\n}\n\n/* Landscape orientation on mobile */\n@media (max-width: 767px) and (orientation: landscape) {\n  .trial-header {\n    padding: 8px 16px;\n    margin-bottom: 8px;\n  }\n\n  .trial-container {\n    padding: 8px;\n  }\n\n  .trial-content {\n    padding: 12px 8px;\n  }\n\n  .trial-question-header {\n    padding: 12px 16px;\n  }\n\n  .trial-question-title {\n    font-size: 14px;\n    margin-bottom: 12px;\n  }\n\n  .trial-option {\n    padding: 10px 14px;\n    margin-bottom: 8px;\n  }\n\n  .trial-nav-buttons {\n    gap: 8px;\n  }\n}\n"], "names": [], "sourceRoot": ""}