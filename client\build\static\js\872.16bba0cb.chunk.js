"use strict";(self.webpackChunkclient=self.webpackChunkclient||[]).push([[872],{3372:(e,t,o)=>{o.d(t,{Bn:()=>s,Ds:()=>c,Km:()=>r,ZB:()=>i,_K:()=>l,xL:()=>a});const{default:n}=o(3371),a=async e=>{try{return(await n.post("/api/forum/add-question",e)).data}catch(t){return t.response.data}},s=async e=>{try{return(await n.post("/api/forum/add-reply",e)).data}catch(t){return t.response.data}},i=async e=>{let{page:t,limit:o}=e;try{return(await n.get("/api/forum/get-all-questions?page=".concat(t,"&limit=").concat(o))).data}catch(a){return a.response.data}},r=async e=>{try{return(await n.delete("/api/forum/delete-question/".concat(e))).data}catch(t){return t.response.data}},c=async(e,t)=>{try{return(await n.put("/api/forum/update-question/".concat(t),e)).data}catch(o){return o.response.data}},l=async(e,t)=>{try{return(await n.put("/api/forum/update-reply-status/".concat(t),e)).data}catch(o){return o.response.data}}},640:(e,t,o)=>{o.d(t,{Z:()=>s});var n=o(2791),a=o(184);const s=function(e){let{title:t}=e;const[o,s]=(0,n.useState)(!1);return(0,n.useEffect)((()=>{window.innerWidth<768&&s(!0)}),[]),(0,a.jsx)("div",{className:"mt-2",children:(0,a.jsx)("h1",{className:o?"text-lg":"",children:t})})}},2872:(e,t,o)=>{o.r(t),o.d(t,{default:()=>X});var n=o(1413),a=o(2791),s=o(7027),i=o(6473),r=o(1694),c=o.n(r),l=o(8568),d=o(4466),u=o(1113),m=o(1929),g=o(2666),b=o(7521),p=o(6356),f=o(5564),h=o(9922);const x=new g.E4("antStatusProcessing",{"0%":{transform:"scale(0.8)",opacity:.5},"100%":{transform:"scale(2.4)",opacity:0}}),v=new g.E4("antZoomBadgeIn",{"0%":{transform:"scale(0) translate(50%, -50%)",opacity:0},"100%":{transform:"scale(1) translate(50%, -50%)"}}),y=new g.E4("antZoomBadgeOut",{"0%":{transform:"scale(1) translate(50%, -50%)"},"100%":{transform:"scale(0) translate(50%, -50%)",opacity:0}}),j=new g.E4("antNoWrapperZoomBadgeIn",{"0%":{transform:"scale(0)",opacity:0},"100%":{transform:"scale(1)"}}),N=new g.E4("antNoWrapperZoomBadgeOut",{"0%":{transform:"scale(1)"},"100%":{transform:"scale(0)",opacity:0}}),w=new g.E4("antBadgeLoadingCircle",{"0%":{transformOrigin:"50%"},"100%":{transform:"translate(50%, -50%) rotate(360deg)",transformOrigin:"50%"}}),S=e=>{const{componentCls:t,iconCls:o,antCls:n,badgeFontHeight:a,badgeShadowSize:s,badgeHeightSm:i,motionDurationSlow:r,badgeStatusSize:c,marginXS:l,badgeRibbonOffset:d}=e,u="".concat(n,"-scroll-number"),m="".concat(n,"-ribbon"),g="".concat(n,"-ribbon-wrapper"),f=(0,p.Z)(e,((e,o)=>{let{darkColor:n}=o;return{["&".concat(t," ").concat(t,"-color-").concat(e)]:{background:n,["&:not(".concat(t,"-count)")]:{color:n}}}})),h=(0,p.Z)(e,((e,t)=>{let{darkColor:o}=t;return{["&".concat(m,"-color-").concat(e)]:{background:o,color:o}}}));return{[t]:Object.assign(Object.assign(Object.assign(Object.assign({},(0,b.Wf)(e)),{position:"relative",display:"inline-block",width:"fit-content",lineHeight:1,["".concat(t,"-count")]:{zIndex:e.badgeZIndex,minWidth:e.badgeHeight,height:e.badgeHeight,color:e.badgeTextColor,fontWeight:e.badgeFontWeight,fontSize:e.badgeFontSize,lineHeight:"".concat(e.badgeHeight,"px"),whiteSpace:"nowrap",textAlign:"center",background:e.badgeColor,borderRadius:e.badgeHeight/2,boxShadow:"0 0 0 ".concat(s,"px ").concat(e.badgeShadowColor),transition:"background ".concat(e.motionDurationMid),a:{color:e.badgeTextColor},"a:hover":{color:e.badgeTextColor},"a:hover &":{background:e.badgeColorHover}},["".concat(t,"-count-sm")]:{minWidth:i,height:i,fontSize:e.badgeFontSizeSm,lineHeight:"".concat(i,"px"),borderRadius:i/2},["".concat(t,"-multiple-words")]:{padding:"0 ".concat(e.paddingXS,"px")},["".concat(t,"-dot")]:{zIndex:e.badgeZIndex,width:e.badgeDotSize,minWidth:e.badgeDotSize,height:e.badgeDotSize,background:e.badgeColor,borderRadius:"100%",boxShadow:"0 0 0 ".concat(s,"px ").concat(e.badgeShadowColor)},["".concat(t,"-dot").concat(u)]:{transition:"background ".concat(r)},["".concat(t,"-count, ").concat(t,"-dot, ").concat(u,"-custom-component")]:{position:"absolute",top:0,insetInlineEnd:0,transform:"translate(50%, -50%)",transformOrigin:"100% 0%",["&".concat(o,"-spin")]:{animationName:w,animationDuration:"1s",animationIterationCount:"infinite",animationTimingFunction:"linear"}},["&".concat(t,"-status")]:{lineHeight:"inherit",verticalAlign:"baseline",["".concat(t,"-status-dot")]:{position:"relative",top:-1,display:"inline-block",width:c,height:c,verticalAlign:"middle",borderRadius:"50%"},["".concat(t,"-status-success")]:{backgroundColor:e.colorSuccess},["".concat(t,"-status-processing")]:{overflow:"visible",color:e.colorPrimary,backgroundColor:e.colorPrimary,"&::after":{position:"absolute",top:0,insetInlineStart:0,width:"100%",height:"100%",borderWidth:s,borderStyle:"solid",borderColor:"inherit",borderRadius:"50%",animationName:x,animationDuration:e.badgeProcessingDuration,animationIterationCount:"infinite",animationTimingFunction:"ease-in-out",content:'""'}},["".concat(t,"-status-default")]:{backgroundColor:e.colorTextPlaceholder},["".concat(t,"-status-error")]:{backgroundColor:e.colorError},["".concat(t,"-status-warning")]:{backgroundColor:e.colorWarning},["".concat(t,"-status-text")]:{marginInlineStart:l,color:e.colorText,fontSize:e.fontSize}}}),f),{["".concat(t,"-zoom-appear, ").concat(t,"-zoom-enter")]:{animationName:v,animationDuration:e.motionDurationSlow,animationTimingFunction:e.motionEaseOutBack,animationFillMode:"both"},["".concat(t,"-zoom-leave")]:{animationName:y,animationDuration:e.motionDurationSlow,animationTimingFunction:e.motionEaseOutBack,animationFillMode:"both"},["&".concat(t,"-not-a-wrapper")]:{["".concat(t,"-zoom-appear, ").concat(t,"-zoom-enter")]:{animationName:j,animationDuration:e.motionDurationSlow,animationTimingFunction:e.motionEaseOutBack},["".concat(t,"-zoom-leave")]:{animationName:N,animationDuration:e.motionDurationSlow,animationTimingFunction:e.motionEaseOutBack},["&:not(".concat(t,"-status)")]:{verticalAlign:"middle"},["".concat(u,"-custom-component, ").concat(t,"-count")]:{transform:"none"},["".concat(u,"-custom-component, ").concat(u)]:{position:"relative",top:"auto",display:"block",transformOrigin:"50% 50%"}},["".concat(u)]:{overflow:"hidden",["".concat(u,"-only")]:{position:"relative",display:"inline-block",height:e.badgeHeight,transition:"all ".concat(e.motionDurationSlow," ").concat(e.motionEaseOutBack),WebkitTransformStyle:"preserve-3d",WebkitBackfaceVisibility:"hidden",["> p".concat(u,"-only-unit")]:{height:e.badgeHeight,margin:0,WebkitTransformStyle:"preserve-3d",WebkitBackfaceVisibility:"hidden"}},["".concat(u,"-symbol")]:{verticalAlign:"top"}},"&-rtl":{direction:"rtl",["".concat(t,"-count, ").concat(t,"-dot, ").concat(u,"-custom-component")]:{transform:"translate(-50%, -50%)"}}}),["".concat(g)]:{position:"relative"},["".concat(m)]:Object.assign(Object.assign(Object.assign(Object.assign({},(0,b.Wf)(e)),{position:"absolute",top:l,padding:"0 ".concat(e.paddingXS,"px"),color:e.colorPrimary,lineHeight:"".concat(a,"px"),whiteSpace:"nowrap",backgroundColor:e.colorPrimary,borderRadius:e.borderRadiusSM,["".concat(m,"-text")]:{color:e.colorTextLightSolid},["".concat(m,"-corner")]:{position:"absolute",top:"100%",width:d,height:d,color:"currentcolor",border:"".concat(d/2,"px solid"),transform:e.badgeRibbonCornerTransform,transformOrigin:"top",filter:e.badgeRibbonCornerFilter}}),h),{["&".concat(m,"-placement-end")]:{insetInlineEnd:-d,borderEndEndRadius:0,["".concat(m,"-corner")]:{insetInlineEnd:0,borderInlineEndColor:"transparent",borderBlockEndColor:"transparent"}},["&".concat(m,"-placement-start")]:{insetInlineStart:-d,borderEndStartRadius:0,["".concat(m,"-corner")]:{insetInlineStart:0,borderBlockEndColor:"transparent",borderInlineStartColor:"transparent"}},"&-rtl":{direction:"rtl"}})}},O=(0,f.Z)("Badge",(e=>{const{fontSize:t,lineHeight:o,fontSizeSM:n,lineWidth:a,marginXS:s,colorBorderBg:i}=e,r=Math.round(t*o),c=a,l=r-2*c,d=e.colorBgContainer,u=n,m=e.colorError,g=e.colorErrorHover,b=t,p=n/2,f=n,x=n/2,v=(0,h.TS)(e,{badgeFontHeight:r,badgeShadowSize:c,badgeZIndex:"auto",badgeHeight:l,badgeTextColor:d,badgeFontWeight:"normal",badgeFontSize:u,badgeColor:m,badgeColorHover:g,badgeShadowColor:i,badgeHeightSm:b,badgeDotSize:p,badgeFontSizeSm:f,badgeStatusSize:x,badgeProcessingDuration:"1.2s",badgeRibbonOffset:s,badgeRibbonCornerTransform:"scaleY(0.75)",badgeRibbonCornerFilter:"brightness(75%)"});return[S(v)]}));const C=e=>{const{className:t,prefixCls:o,style:n,color:s,children:i,text:r,placement:l="end"}=e,{getPrefixCls:u,direction:g}=a.useContext(m.E_),b=u("ribbon",o),p=(0,d.o2)(s,!1),f=c()(b,"".concat(b,"-placement-").concat(l),{["".concat(b,"-rtl")]:"rtl"===g,["".concat(b,"-color-").concat(s)]:p},t),[h,x]=O(b),v={},y={};return s&&!p&&(v.background=s,y.color=s),h(a.createElement("div",{className:c()("".concat(b,"-wrapper"),x)},i,a.createElement("div",{className:c()(f,x),style:Object.assign(Object.assign({},v),n)},a.createElement("span",{className:"".concat(b,"-text")},r),a.createElement("div",{className:"".concat(b,"-corner"),style:y}))))};function E(e){let t,{prefixCls:o,value:n,current:s,offset:i=0}=e;return i&&(t={position:"absolute",top:"".concat(i,"00%"),left:0}),a.createElement("span",{style:t,className:c()("".concat(o,"-only-unit"),{current:s})},n)}function k(e,t,o){let n=e,a=0;for(;(n+10)%10!==t;)n+=o,a+=o;return a}function R(e){const{prefixCls:t,count:o,value:n}=e,s=Number(n),i=Math.abs(o),[r,c]=a.useState(s),[l,d]=a.useState(i),u=()=>{c(s),d(i)};let m,g;if(a.useEffect((()=>{const e=setTimeout((()=>{u()}),1e3);return()=>{clearTimeout(e)}}),[s]),r===s||Number.isNaN(s)||Number.isNaN(r))m=[a.createElement(E,Object.assign({},e,{key:s,current:!0}))],g={transition:"none"};else{m=[];const t=s+10,o=[];for(let e=s;e<=t;e+=1)o.push(e);const n=o.findIndex((e=>e%10===r));m=o.map(((t,o)=>{const s=t%10;return a.createElement(E,Object.assign({},e,{key:t,value:s,offset:o-n,current:o===n}))}));g={transform:"translateY(".concat(-k(r,s,l<i?1:-1),"00%)")}}return a.createElement("span",{className:"".concat(t,"-only"),style:g,onTransitionEnd:u},m)}var Z=function(e,t){var o={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&t.indexOf(n)<0&&(o[n]=e[n]);if(null!=e&&"function"===typeof Object.getOwnPropertySymbols){var a=0;for(n=Object.getOwnPropertySymbols(e);a<n.length;a++)t.indexOf(n[a])<0&&Object.prototype.propertyIsEnumerable.call(e,n[a])&&(o[n[a]]=e[n[a]])}return o};const P=a.forwardRef(((e,t)=>{const{prefixCls:o,count:n,className:s,motionClassName:i,style:r,title:l,show:d,component:g="sup",children:b}=e,p=Z(e,["prefixCls","count","className","motionClassName","style","title","show","component","children"]),{getPrefixCls:f}=a.useContext(m.E_),h=f("scroll-number",o),x=Object.assign(Object.assign({},p),{"data-show":d,style:r,className:c()(h,s,i),title:l});let v=n;if(n&&Number(n)%1===0){const e=String(n).split("");v=e.map(((t,o)=>a.createElement(R,{prefixCls:h,count:Number(n),value:t,key:e.length-o})))}return r&&r.borderColor&&(x.style=Object.assign(Object.assign({},r),{boxShadow:"0 0 0 1px ".concat(r.borderColor," inset")})),b?(0,u.Tm)(b,(e=>({className:c()("".concat(h,"-custom-component"),null===e||void 0===e?void 0:e.className,i)}))):a.createElement(g,Object.assign({},x,{ref:t}),v)})),z=P;var I=function(e,t){var o={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&t.indexOf(n)<0&&(o[n]=e[n]);if(null!=e&&"function"===typeof Object.getOwnPropertySymbols){var a=0;for(n=Object.getOwnPropertySymbols(e);a<n.length;a++)t.indexOf(n[a])<0&&Object.prototype.propertyIsEnumerable.call(e,n[a])&&(o[n[a]]=e[n[a]])}return o};const D=(e,t)=>{var o,n,s,i,r;const{prefixCls:g,scrollNumberPrefixCls:b,children:p,status:f,text:h,color:x,count:v=null,overflowCount:y=99,dot:j=!1,size:N="default",title:w,offset:S,style:C,className:E,rootClassName:k,classNames:R,styles:Z,showZero:P=!1}=e,D=I(e,["prefixCls","scrollNumberPrefixCls","children","status","text","color","count","overflowCount","dot","size","title","offset","style","className","rootClassName","classNames","styles","showZero"]),{getPrefixCls:T,direction:H,badge:A}=a.useContext(m.E_),B=T("badge",g),[F,W]=O(B),V=v>y?"".concat(y,"+"):v,_="0"===V||0===V,M=(null!==f&&void 0!==f||null!==x&&void 0!==x)&&(null===v||_&&!P),q=j&&!_,Q=q?"":V,L=(0,a.useMemo)((()=>(null===Q||void 0===Q||""===Q||_&&!P)&&!q),[Q,_,P,q]),X=(0,a.useRef)(v);L||(X.current=v);const K=X.current,Y=(0,a.useRef)(Q);L||(Y.current=Q);const U=Y.current,G=(0,a.useRef)(q);L||(G.current=q);const J=(0,a.useMemo)((()=>{if(!S)return Object.assign(Object.assign({},null===A||void 0===A?void 0:A.style),C);const e={marginTop:S[1]};return"rtl"===H?e.left=parseInt(S[0],10):e.right=-parseInt(S[0],10),Object.assign(Object.assign(Object.assign({},e),null===A||void 0===A?void 0:A.style),C)}),[H,S,C,null===A||void 0===A?void 0:A.style]),$=null!==w&&void 0!==w?w:"string"===typeof K||"number"===typeof K?K:void 0,ee=L||!h?null:a.createElement("span",{className:"".concat(B,"-status-text")},h),te=K&&"object"===typeof K?(0,u.Tm)(K,(e=>({style:Object.assign(Object.assign({},J),e.style)}))):void 0,oe=(0,d.o2)(x,!1),ne=c()(null===R||void 0===R?void 0:R.indicator,null===(o=null===A||void 0===A?void 0:A.classNames)||void 0===o?void 0:o.indicator,{["".concat(B,"-status-dot")]:M,["".concat(B,"-status-").concat(f)]:!!f,["".concat(B,"-color-").concat(x)]:oe}),ae={};x&&!oe&&(ae.color=x,ae.background=x);const se=c()(B,{["".concat(B,"-status")]:M,["".concat(B,"-not-a-wrapper")]:!p,["".concat(B,"-rtl")]:"rtl"===H},E,k,null===A||void 0===A?void 0:A.className,null===(n=null===A||void 0===A?void 0:A.classNames)||void 0===n?void 0:n.root,null===R||void 0===R?void 0:R.root,W);if(!p&&M){const e=J.color;return F(a.createElement("span",Object.assign({},D,{className:se,style:Object.assign(Object.assign(Object.assign({},null===Z||void 0===Z?void 0:Z.root),null===(s=null===A||void 0===A?void 0:A.styles)||void 0===s?void 0:s.root),J)}),a.createElement("span",{className:ne,style:Object.assign(Object.assign(Object.assign({},null===Z||void 0===Z?void 0:Z.indicator),null===(i=null===A||void 0===A?void 0:A.styles)||void 0===i?void 0:i.indicator),ae)}),h&&a.createElement("span",{style:{color:e},className:"".concat(B,"-status-text")},h)))}return F(a.createElement("span",Object.assign({ref:t},D,{className:se,style:Object.assign(Object.assign({},null===(r=null===A||void 0===A?void 0:A.styles)||void 0===r?void 0:r.root),null===Z||void 0===Z?void 0:Z.root)}),p,a.createElement(l.ZP,{visible:!L,motionName:"".concat(B,"-zoom"),motionAppear:!1,motionDeadline:1e3},(e=>{let{className:t,ref:o}=e;var n,s;const i=T("scroll-number",b),r=G.current,l=c()(null===R||void 0===R?void 0:R.indicator,null===(n=null===A||void 0===A?void 0:A.classNames)||void 0===n?void 0:n.indicator,{["".concat(B,"-dot")]:r,["".concat(B,"-count")]:!r,["".concat(B,"-count-sm")]:"small"===N,["".concat(B,"-multiple-words")]:!r&&U&&U.toString().length>1,["".concat(B,"-status-").concat(f)]:!!f,["".concat(B,"-color-").concat(x)]:oe});let d=Object.assign(Object.assign(Object.assign({},null===Z||void 0===Z?void 0:Z.indicator),null===(s=null===A||void 0===A?void 0:A.styles)||void 0===s?void 0:s.indicator),J);return x&&!oe&&(d=d||{},d.background=x),a.createElement(z,{prefixCls:i,show:!L,motionClassName:t,className:l,count:U,title:$,style:d,key:"scrollNumber",ref:o},te)})),ee))},T=a.forwardRef(D);T.Ribbon=C;const H=T;var A=o(7309),B=o(2879),F=o(640),W=o(9434),V=o(8247),_=o(2048),M=o(3372),q=o(2202),Q=o(1909),L=o(184);const X=()=>{const[e,t]=(0,a.useState)([]),[o,r]=(0,a.useState)(!1),[c,l]=(0,a.useState)({}),[d,u]=(0,a.useState)({totalQuestions:0,totalReplies:0,pendingReplies:0,verifiedReplies:0}),m=(0,W.I0)(),g=async()=>{r(!0),m((0,V.YC)());try{const e=await(0,M.ZB)();e.success?(t(e.data),b(e.data)):s.ZP.error(e.message)}catch(e){s.ZP.error(e.message)}finally{r(!1),m((0,V.Ir)())}},b=e=>{let t=0,o=0,n=0;e.forEach((e=>{t+=e.replies.length,e.replies.forEach((e=>{e.user.isAdmin||(e.isVerified?n++:o++)}))})),u({totalQuestions:e.length,totalReplies:t,pendingReplies:o,verifiedReplies:n})};(0,a.useEffect)((()=>{g()}),[]);const p=e=>{let{title:t,value:o,icon:n,color:a,bgColor:s}=e;return(0,L.jsxs)(i.Z,{className:"text-center",children:[(0,L.jsx)("div",{className:"inline-flex items-center justify-center w-12 h-12 ".concat(s," rounded-lg mb-3"),children:(0,L.jsx)(n,{className:"w-6 h-6 ".concat(a)})}),(0,L.jsx)("h3",{className:"text-2xl font-bold text-gray-900",children:o}),(0,L.jsx)("p",{className:"text-gray-600",children:t})]})};return(0,L.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50",children:[(0,L.jsx)(F.Z,{title:"Forum Management"}),(0,L.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,L.jsxs)("div",{className:"mb-8",children:[(0,L.jsx)("h1",{className:"text-3xl font-bold text-gray-900 mb-2",children:"Forum Management"}),(0,L.jsx)("p",{className:"text-gray-600",children:"Manage community questions and verify user replies"})]}),(0,L.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8",children:[(0,L.jsx)(p,{title:"Total Questions",value:d.totalQuestions,icon:Q.a9O,color:"text-blue-600",bgColor:"bg-blue-100"}),(0,L.jsx)(p,{title:"Total Replies",value:d.totalReplies,icon:Q.a9O,color:"text-green-600",bgColor:"bg-green-100"}),(0,L.jsx)(p,{title:"Pending Approval",value:d.pendingReplies,icon:Q.FmG,color:"text-orange-600",bgColor:"bg-orange-100"}),(0,L.jsx)(p,{title:"Verified Replies",value:d.verifiedReplies,icon:Q.vr,color:"text-green-600",bgColor:"bg-green-100"})]}),(0,L.jsx)("div",{className:"space-y-6",children:e.map((e=>(0,L.jsx)(i.Z,{className:"shadow-lg",children:(0,L.jsxs)("div",{className:"p-6",children:[(0,L.jsxs)("div",{className:"flex items-start justify-between mb-4",children:[(0,L.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,L.jsx)(_.Z,{user:e.user,size:"sm",showOnlineStatus:!1}),(0,L.jsxs)("div",{children:[(0,L.jsx)("h4",{className:"font-semibold text-gray-900",children:e.user.name}),(0,L.jsx)("p",{className:"text-sm text-gray-500",children:new Date(e.createdAt).toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric",hour:"2-digit",minute:"2-digit"})})]})]}),(0,L.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,L.jsx)(H,{count:e.replies.length,showZero:!0,children:(0,L.jsxs)(A.ZP,{icon:(0,L.jsx)(q.dSq,{}),onClick:()=>{return t=e._id,void l((e=>(0,n.Z)((0,n.Z)({},e),{},{[t]:!e[t]})));var t},type:c[e._id]?"primary":"default",children:[c[e._id]?"Hide":"View"," Replies"]})}),(0,L.jsx)(A.ZP,{icon:(0,L.jsx)(q.Xm5,{}),danger:!0,onClick:()=>(async e=>{try{const t=await(0,M.Km)({questionId:e});t.success?(s.ZP.success("Question deleted successfully"),g()):s.ZP.error(t.message)}catch(t){s.ZP.error(t.message)}})(e._id),children:"Delete"})]})]}),(0,L.jsx)("h3",{className:"text-xl font-bold text-gray-900 mb-2",children:e.title}),(0,L.jsx)("p",{className:"text-gray-700 mb-4",children:e.body}),c[e._id]&&(0,L.jsxs)("div",{className:"mt-6 space-y-4 bg-gray-50 rounded-lg p-4",children:[(0,L.jsxs)("h4",{className:"text-lg font-semibold text-gray-800 mb-4",children:["Replies (",e.replies.length,")"]}),e.replies.map((t=>(0,L.jsx)("div",{className:"bg-white rounded-lg p-4 border-l-4 ".concat(t.user.isAdmin?"border-purple-500 bg-purple-50":t.isVerified?"border-green-500 bg-green-50":"border-orange-500 bg-orange-50"),children:(0,L.jsxs)("div",{className:"flex items-start justify-between",children:[(0,L.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,L.jsx)(_.Z,{user:t.user,size:"xs",showOnlineStatus:!1}),(0,L.jsxs)("div",{className:"flex-1",children:[(0,L.jsxs)("div",{className:"flex items-center space-x-2 mb-2",children:[(0,L.jsx)("h5",{className:"font-semibold text-gray-900",children:t.user.name}),t.user.isAdmin&&(0,L.jsx)(H,{color:"purple",text:"Admin"}),t.isVerified&&!t.user.isAdmin&&(0,L.jsx)(H,{color:"green",text:"Verified"}),!t.isVerified&&!t.user.isAdmin&&(0,L.jsx)(H,{color:"orange",text:"Pending"})]}),(0,L.jsx)("p",{className:"text-sm mb-2 ".concat(t.isVerified&&!t.user.isAdmin?"text-green-800 font-medium":t.user.isAdmin?"text-purple-800 font-medium":"text-gray-700"),children:t.text}),(0,L.jsx)("p",{className:"text-xs text-gray-500",children:(()=>{try{const e=new Date(t.createdAt);return isNaN(e.getTime())?"Invalid date":e.toLocaleDateString("en-US",{month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"})}catch(e){return"Invalid date"}})()})]})]}),!t.user.isAdmin&&(0,L.jsx)("div",{className:"flex space-x-2",children:(0,L.jsx)(B.Z,{title:t.isVerified?"Disapprove Reply":"Approve Reply",children:(0,L.jsx)(A.ZP,{size:"small",type:t.isVerified?"danger":"primary",icon:t.isVerified?(0,L.jsx)(q.aHS,{}):(0,L.jsx)(q.l_A,{}),onClick:()=>(async(e,t,o)=>{try{const n=await(0,M._K)({questionId:e,replyId:t,isVerified:o});n.success?(s.ZP.success(o?"Reply approved successfully":"Reply disapproved successfully"),g()):s.ZP.error(n.message)}catch(n){s.ZP.error(n.message)}})(e._id,t._id,!t.isVerified),children:t.isVerified?"Disapprove":"Approve"})})})]})},t._id)))]})]})},e._id)))}),0===e.length&&!o&&(0,L.jsxs)("div",{className:"text-center py-12",children:[(0,L.jsx)(Q.a9O,{className:"w-16 h-16 text-gray-400 mx-auto mb-4"}),(0,L.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"No questions found"}),(0,L.jsx)("p",{className:"text-gray-500",children:"Questions will appear here when users post them."})]})]})]})}}}]);
//# sourceMappingURL=872.16bba0cb.chunk.js.map