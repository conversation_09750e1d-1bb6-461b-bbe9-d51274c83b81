{"version": 3, "file": "static/css/92.09775eed.chunk.css", "mappings": "AACA,uBAEE,iFAAkG,CAElG,+BAAiC,CADjC,qFAAkG,CAElG,yBAA2B,CAJ3B,0BAA4B,CAM5B,iBAAkB,CADlB,iBAEF,CAEA,8BAOE,qLAGiF,CAJjF,QAAS,CALT,UAAW,CAGX,MAAO,CAOP,mBAAoB,CATpB,iBAAkB,CAGlB,OAAQ,CAFR,KAAM,CASN,SACF,CAEA,yBACE,iBAAkB,CAClB,SACF,CAGA,eACE,wEAAqF,CAMrF,wCAA0C,CAH1C,0EAEoD,CAJpD,oBAAuB,CAMvB,sCAAwC,CAExC,eAAgB,CAPhB,gCAAkC,CAMlC,iBAEF,CAEA,sBAOE,sDAAgG,CADhG,QAAS,CALT,UAAW,CAGX,MAAO,CAIP,mBAAoB,CANpB,iBAAkB,CAGlB,OAAQ,CAFR,KAAM,CAMN,SACF,CAEA,iBACE,iBAAkB,CAClB,SACF,CAEA,gBAIE,6BAA8B,CAF9B,aAAc,CADd,gBAKF,CAEA,6BAHE,kBAAmB,CAFnB,YASF,CAJA,aAGE,UACF,CAEA,aAEE,aAAc,CADd,cAAe,CAEf,UACF,CAEA,gBAIE,UAAY,CAHZ,gBAAiB,CACjB,eAAgB,CAChB,gBAEF,CAEA,eAGE,aAAc,CAFd,gBAAiB,CAGjB,UACF,CAEA,aAME,kCAA2B,CAA3B,0BAA2B,CAL3B,gBAAoC,CAMpC,0BAA0C,CAJ1C,kBAAmB,CAEnB,eAAiB,CADjB,eAAgB,CAFhB,qBAMF,CAEA,iBAIE,UAAY,CAHZ,gBAAiB,CACjB,eAAgB,CAGhB,sBAAwB,CAFxB,QAGF,CAEA,iBAIE,aAAc,CAHd,gBAAiB,CAEjB,eAAgB,CADhB,UAGF,CAEA,mBAKE,kCAA2B,CAA3B,0BAA2B,CAD3B,oBAAqC,CAErC,sBAA0C,CAC1C,kBAAmB,CACnB,qBAAuB,CAPvB,iBAAkB,CAElB,UAAW,CADX,QAOF,CAEA,YAIE,UAAY,CAFZ,iBAAmB,CADnB,eAAgB,CAEhB,mBAEF,CAGA,oBAGE,kBAAmB,CADnB,wBAAyB,CAGzB,YAAa,CACb,iBAAkB,CAClB,SAEF,CAEA,kBAIE,+BAAgC,CAFhC,kBAAmB,CACnB,qBAEF,CAEA,qBAGE,aAAc,CAEd,sBACF,CAEA,oBAEE,aAAc,CAEd,eACF,CAGA,eAKE,kBAAmB,CACnB,kBAAmB,CAJnB,SAAW,CACX,kBAAmB,CAInB,eAAgB,CAHhB,aAIF,CAEA,SAME,iBAAkB,CASlB,eAAgB,CAXhB,mBAAoB,CAQpB,kBAIF,CAEA,eACE,kBAAmB,CACnB,aACF,CAEA,gBACE,kBAAmB,CAEnB,8BAA6C,CAD7C,UAEF,CAEA,UACE,cACF,CAGA,gBACE,oBACF,CAEA,eAME,+BAAgC,CAFhC,kBAAmB,CACnB,qBAEF,CAQA,YAGE,SACF,CAEA,eACE,kBAAmB,CAGnB,kBAAmB,CAFnB,UAAY,CAIZ,eAAgB,CAHhB,kBAIF,CAEA,kBACE,kBAAmB,CACnB,wBAAyB,CAKzB,aACF,CAEA,wBACE,kBAAmB,CACnB,oBACF,CA+CA,uBAEE,kBAAmB,CAMnB,gCAA0C,CAJ1C,eAAgB,CAGhB,eAAgB,CAJhB,YAMF,CAEA,cAIE,oBAAqB,CACrB,mBAEF,CASA,WACE,kBAAmB,CACnB,wBAAyB,CAKzB,aACF,CAEA,iBACE,kBAAmB,CACnB,oBACF,CAQA,cAIE,YASF,CAcA,wBAIE,4BAA6B,CAH7B,YAAa,CACb,sBAAuB,CAGvB,eAAgB,CAFhB,gBAGF,CAEA,oBAWE,kBAAmB,CAVnB,kDAA6D,CAE7D,WAAY,CAEZ,iBAAkB,CAHlB,UAAY,CAMZ,cAAe,CAEf,YAAa,CAHb,cAAe,CADf,eAAgB,CAMhB,SAAW,CAEX,sBAAuB,CADvB,eAAgB,CAThB,iBAAkB,CAKlB,uBAMF,CAEA,yCAGE,kDAA6D,CAD7D,+BAA8C,CAD9C,0BAGF,CAEA,6BAEE,kBAAmB,CADnB,UAEF,CAYA,kBAEE,kBAAmB,CAInB,4BAA6B,CAL7B,YAAa,CAEb,QAAS,CACT,sBAAuB,CAGvB,eAAgB,CAFhB,gBAGF,CAEA,YACE,kBAAmB,CAEnB,wBAAyB,CAEzB,iBAAkB,CAHlB,aAAc,CAMd,cAAe,CADf,iBAAmB,CADnB,eAAgB,CAFhB,kBAAoB,CAKpB,uBACF,CAEA,kBAGE,kBAAmB,CAFnB,oBAAqB,CACrB,aAEF,CAEA,mBACE,kBAAmB,CAEnB,oBAAqB,CADrB,UAEF,CAEA,aACE,aAAc,CACd,iBAAmB,CACnB,eACF,CAGA,oBACE,eAAiB,CAIjB,wBAAyB,CAHzB,kBAAmB,CACnB,oDAAyE,CACzE,kBAAmB,CAEnB,eACF,CAEA,kBAGE,kDAA6D,CAC7D,UAAY,CAHZ,sBAAuB,CACvB,iBAGF,CAEA,qBACE,iBAAkB,CAClB,eAAgB,CAChB,gBACF,CAEA,oBACE,cAAe,CAEf,QAAS,CADT,UAEF,CAGA,eAEE,kBAAmB,CACnB,+BAAgC,CAFhC,YAGF,CAEA,SAUE,kBAAmB,CANnB,gBAAuB,CASvB,WAAoC,CAApC,6BAAoC,CARpC,aAAc,CAEd,cAAe,CAEf,YAAa,CARb,QAAO,CAKP,eAAgB,CAMhB,SAAW,CADX,sBAAuB,CATvB,oBAAqB,CAMrB,uBAMF,CAEA,eACE,kBAAmB,CACnB,aACF,CAEA,gBACE,eAAiB,CAEjB,2BAA4B,CAD5B,aAEF,CAEA,UACE,gBACF,CAGA,gBAEE,eAAiB,CADjB,YAEF,CAEA,eAIE,oBAGF,CA0CA,kBACE,YACF,CAEA,eAEE,kBAAmB,CADnB,YAAa,CAIb,cAAe,CACf,QAAS,CAHT,6BAA8B,CAC9B,kBAGF,CAEA,iBAGE,aAAc,CAFd,gBAAiB,CACjB,eAAgB,CAEhB,gBACF,CAEA,gBACE,aAAc,CAEd,cAAe,CADf,QAEF,CAEA,YAEE,kBAAmB,CAEnB,kBAAmB,CAGnB,wBAAyB,CADzB,iBAAkB,CALlB,YAAa,CAEb,UAAY,CAEZ,mBAGF,CAEA,eAEE,aAAc,CACd,iBAAmB,CAFnB,eAGF,CAEA,kBASE,kBAAmB,CARnB,kBAAmB,CAEnB,WAAY,CAEZ,iBAAkB,CAHlB,UAAY,CAIZ,cAAe,CAEf,YAAa,CAEb,sBAAuB,CANvB,aAAe,CAGf,uBAIF,CAEA,wBACE,kBAAmB,CACnB,0BACF,CAEA,iBAEE,qBAAsB,CACtB,UACF,CAEA,gBACE,YAAa,CACb,qBAAsB,CACtB,QACF,CAEA,cAGE,cACF,CAEA,iBACE,YAAa,CACb,cAAe,CACf,UACF,CAEA,aAGE,eAAiB,CADjB,wBAAyB,CAGzB,iBAAkB,CADlB,aAAc,CAEd,cAAe,CAGf,iBAAmB,CADnB,eAAgB,CAPhB,sBAAwB,CAMxB,uBAGF,CAEA,mBAGE,kBAAmB,CAFnB,oBAAqB,CACrB,aAEF,CAEA,oBACE,kBAAmB,CAEnB,oBAAqB,CADrB,UAEF,CAGA,yBAQE,kBAAmB,CAFnB,oBAA8B,CAD9B,QAAS,CAET,YAAa,CAEb,sBAAuB,CANvB,MAAO,CAFP,cAAe,CAGf,OAAQ,CAFR,KAAM,CAQN,YACF,CAEA,uBACE,eAAiB,CACjB,kBAAmB,CACnB,sDAA2E,CAG3E,eAAgB,CAFhB,eAAgB,CAGhB,eAAgB,CAFhB,SAGF,CAEA,cAEE,kBAAmB,CAGnB,kBAAmB,CACnB,+BAAgC,CALhC,YAAa,CAEb,6BAA8B,CAC9B,mBAGF,CAEA,iBAGE,aAAc,CAFd,iBAAkB,CAClB,eAAgB,CAEhB,QACF,CAEA,WASE,kBAAmB,CANnB,WAAY,CAEZ,iBAAkB,CAClB,cAAe,CAEf,YAAa,CAEb,sBAAuB,CANvB,aAAe,CAGf,uBAIF,CAEA,iBAEE,0BACF,CAEA,eAEE,YAAa,CACb,qBAAsB,CACtB,UAAY,CACZ,gBAAiB,CACjB,eAAgB,CALhB,mBAMF,CAEA,cAEE,kBAAmB,CAInB,eAAiB,CADjB,wBAAyB,CAGzB,iBAAkB,CADlB,aAAc,CANd,YAAa,CAUb,eAAgB,CARhB,QAAS,CACT,oBAAqB,CAMrB,uBAGF,CAEA,oBAGE,kBAAmB,CAFnB,oBAAqB,CACrB,aAEF,CAEA,qBACE,kBAAmB,CAEnB,oBACF,CAGA,mBACE,mBACF,CAEA,qBACE,YAAa,CACb,qBAAsB,CACtB,UACF,CAGA,kBACE,eAAiB,CAGjB,wBAAyB,CAFzB,kBAAmB,CACnB,oDAAyE,CAEzE,eACF,CAEA,gBAEE,kBAAmB,CACnB,+BAAgC,CAIhC,cAAe,CADf,6BAA8B,CAL9B,wBAQF,CAEA,+BANE,kBAAmB,CADnB,YAAa,CAIb,QAOF,CAEA,cAEE,aAAc,CADd,gBAEF,CAEA,kBAGE,aAAc,CAFd,gBAAiB,CACjB,eAAgB,CAEhB,QACF,CAEA,eACE,kBAAmB,CAGnB,kBAAmB,CAFnB,UAAY,CAGZ,iBAAmB,CACnB,eAAgB,CAChB,cAAe,CAJf,qBAAwB,CAKxB,iBACF,CAEA,kBAEE,kBAAmB,CADnB,YAAa,CAEb,QACF,CAEA,oCACE,iBAAkB,CAClB,WACF,CAEA,kCAEE,kBAAmB,CADnB,YAEF,CAEA,+BAIE,eAAiB,CAFjB,wBAAyB,CACzB,iBAAkB,CAElB,aAAc,CAEd,cAAe,CADf,iBAAmB,CAGnB,eAAgB,CARhB,oBAAuB,CAOvB,uBAEF,CAEA,qCAEE,oBAAqB,CACrB,8BAA6C,CAF7C,YAGF,CAGA,kBAEE,kBAAmB,CAInB,kBAAmB,CAEnB,wBAAyB,CADzB,iBAAkB,CANlB,YAAa,CAEb,QAAS,CACT,kBAAmB,CACnB,cAIF,CAEA,kBAEE,QAAO,CACP,eACF,CAEA,gBAEE,kBAAmB,CADnB,YAAa,CAEb,UACF,CAEA,YAEE,aAAc,CACd,iBAAmB,CAFnB,eAAgB,CAGhB,kBACF,CAEA,aAIE,eAAiB,CAFjB,wBAAyB,CACzB,iBAAkB,CAElB,aAAc,CAId,eAAgB,CARhB,sBASF,CAEA,mBAEE,oBAAqB,CACrB,8BACF,CAGA,gBACE,YAAa,CACb,qBAAsB,CACtB,QACF,CAEA,eAEE,kBAAmB,CAGnB,kBAAmB,CACnB,wBAAyB,CACzB,iBAAkB,CANlB,YAAa,CAEb,6BAA8B,CAC9B,cAAe,CAIf,uBACF,CAEA,qBAEE,kBAAmB,CADnB,oBAAqB,CAGrB,+BAA8C,CAD9C,0BAEF,CAEA,WACE,QACF,CAEA,aAEE,kBAAmB,CADnB,YAAa,CAEb,QAAS,CACT,mBACF,CAEA,YAGE,aAAc,CAFd,kBAAmB,CACnB,eAAgB,CAGhB,eAAgB,CADhB,QAEF,CAEA,WACE,kBAAmB,CAGnB,kBAAmB,CAFnB,UAAY,CAGZ,gBAAkB,CAClB,eAAgB,CAHhB,qBAAwB,CAIxB,kBACF,CAEA,WAGE,kBAAmB,CAFnB,YAAa,CACb,QAEF,CAEA,WAIE,iBAKF,CAEA,cACE,aAAc,CACd,iBAAmB,CACnB,eACF,CAEA,cAGE,kBAAmB,CAFnB,YAAa,CACb,UAEF,CAEA,YAIE,oBAAsB,CAQtB,kBACF,CAEA,oBACE,kBAAmB,CACnB,UACF,CAEA,gCACE,kBAAmB,CACnB,0BACF,CAcA,WACE,kBAAmB,CAEnB,wBAAyB,CADzB,aAEF,CAEA,iBACE,kBAAmB,CACnB,oBACF,CAEA,kBACE,aAAc,CAEd,iBAAmB,CADnB,iBAEF,CAGA,YAEE,mBAAoB,CADpB,qBAEF,CAEA,uBAEE,kBAAmB,CADnB,YAAa,CAEb,6BAA8B,CAC9B,eACF,CAEA,cAGE,4BAA6B,CAF7B,iBAAkB,CAClB,kBAEF,CAEA,qBACE,iBAAkB,CAClB,kBACF,CAGA,aAGE,aACF,CAQA,gBAIE,aAAc,CADd,eAEF,CAEA,eAGE,aAAc,CACd,eAAgB,CAFhB,QAGF,CAGA,oBAIE,eAAiB,CAGjB,wBAAyB,CAFzB,kBAAmB,CACnB,oDAAyE,CAHzE,aAAc,CADd,iBAAkB,CADlB,iBAOF,CAEA,oBACE,kBACF,CAEA,gCAEE,aAAc,CADd,cAAe,CAEf,oBACF,CAEA,uBAIE,aAAc,CAHd,iBAAkB,CAClB,eAAgB,CAChB,eAEF,CAEA,sBAGE,aAAc,CAFd,kBAAmB,CAGnB,eAAgB,CAFhB,gBAGF,CAEA,YAEE,uBAAyB,CADzB,iBAEF,CAEA,mBACE,eAAiB,CAKjB,wBAAyB,CAJzB,kBAAmB,CACnB,oDAAyE,CACzE,kBAAmB,CACnB,eAEF,CAEA,kBACE,kDAA6D,CAC7D,UAAY,CAIZ,6BAA8B,CAH9B,mBAIF,CAEA,+BAJE,kBAAmB,CADnB,YASF,CAJA,aAGE,QACF,CAEA,eAEE,aAAc,CADd,iBAEF,CAEA,gBAIE,UAAY,CAHZ,gBAAiB,CACjB,eAAgB,CAChB,iBAEF,CAEA,eAGE,aAAc,CAFd,eAAiB,CAGjB,eAAgB,CAFhB,QAGF,CAEA,eACE,oBAAqC,CAErC,kBAAmB,CACnB,iBAAmB,CACnB,eAAgB,CAHhB,kBAIF,CAGA,gBAEE,yDAA4D,CAE5D,YACF,CAEA,eACE,kBAAmB,CAKnB,YAAa,CACb,qBAAsB,CAHtB,uBAIF,CAEA,qBACE,oBAAqB,CAErB,+BACF,CAEA,cAEE,WAAY,CADZ,cAEF,CAEA,aAKE,QAAS,CAJT,kBAKF,CAEA,gBAGE,aAAc,CAGd,WAAY,CAFZ,QAGF,CAEA,eACE,kBAAmB,CAGnB,kBAAmB,CAFnB,UAAY,CAKZ,kBACF,CAEA,sBACE,YAAa,CACb,QAAS,CACT,kBACF,CAEA,WACE,kBAAmB,CAGnB,iBAAkB,CAFlB,aAAc,CAGd,gBAAkB,CAClB,eAAgB,CAEhB,mBAAqB,CALrB,oBAAuB,CAIvB,wBAEF,CAEA,gBACE,aAAc,CACd,iBAAmB,CACnB,eACF,CAEA,oBAIE,kBAAmB,CAFnB,iBAAkB,CADlB,kBAAmB,CAEnB,eAEF,CAEA,wBAEE,YAAa,CACb,gBAAiB,CACjB,6BAA+B,CAH/B,UAIF,CAEA,8BACE,qBACF,CAEA,cAEE,eAAiB,CACjB,4BAA6B,CAF7B,mBAKF,CAEA,YAYE,QAAO,CACP,sBAAuB,CATvB,uBAAyB,CAMzB,oBAIF,CAEA,UACE,kBAAmB,CACnB,UACF,CAEA,gBACE,kBAAmB,CACnB,0BACF,CAEA,cACE,kBAAmB,CAEnB,wBAAyB,CADzB,aAEF,CAEA,oBACE,kBAAmB,CACnB,oBAAqB,CACrB,0BACF,CAEA,oBAKE,kBAAmB,CACnB,iBAAkB,CALlB,aAAc,CAMd,iBAAmB,CALnB,iBAAkB,CAClB,cAAgB,CAChB,iBAIF,CAGA,YAGE,aAAS,CAFT,YAAa,CAEb,QAAS,CADT,yDAA4D,CAE5D,YACF,CAEA,YACE,kBAAmB,CAGnB,sBAA6B,CAF7B,kBAAmB,CACnB,eAAgB,CAGhB,iBAAkB,CADlB,uBAEF,CAEA,mBAOE,iDAAoD,CANpD,UAAW,CAKX,UAAW,CAFX,MAAO,CAFP,iBAAkB,CAGlB,OAAQ,CAFR,KAKF,CAEA,kBACE,oBAAqB,CAErB,gCAAgD,CADhD,0BAEF,CAEA,cAEE,eACF,CAEA,aAGE,UAAW,CAFX,gBAAiB,CACjB,eAAgB,CAGhB,eAAgB,CADhB,QAEF,CAEA,eACE,iBACF,CAOA,iBAIE,cAAe,CAFf,YAIF,CAMA,cAME,oBAA8B,CAK9B,cAGF,CAMA,WACE,cAGF,CAEA,WAGE,gBACF,CAEA,WACE,aAAc,CACd,uBACF,CAEA,cAEE,eAAiB,CADjB,cAEF,CAEA,eAEE,kBAAmB,CACnB,kBAAmB,CAFnB,UAGF,CAEA,WACE,UACF,CAGA,iBAEE,kBAAmB,CADnB,YAAa,CAEb,sBAAuB,CACvB,aACF,CAEA,gBACE,UAAW,CACX,eAAiB,CACjB,iBACF,CAGA,sBAGE,aAAc,CADd,iBAAkB,CADlB,iBAGF,CAEA,oBACE,oBACF,CAEA,YAEE,aAAc,CACd,kBACF,CAEA,yBAIE,aAAc,CAHd,gBAAiB,CACjB,eAAgB,CAChB,eAEF,CAEA,wBAGE,aAAc,CAFd,cAAe,CAGf,eAAgB,CAFhB,gBAGF,CAEA,YAEE,aAAc,CADd,eAEF,CAEA,sBAGE,kBAAmB,CAGnB,wBAAyB,CAFzB,kBAAmB,CACnB,WAAY,CAHZ,iBAAkB,CADlB,iBAMF,CAEA,yBACE,aAAc,CACd,iBAAkB,CAElB,eAAgB,CADhB,eAEF,CAEA,wBACE,aAAc,CAEd,eAAgB,CADhB,QAEF,CAGA,yBACE,cACE,iBACF,CAMA,8BACE,cACF,CAEA,aAGE,qBAAsB,CADtB,eAAgB,CADhB,eAGF,CAEA,gBACE,sBAAuB,CACvB,cACF,CAEA,aAEE,QAAS,CADT,yBAEF,CAEA,iBACE,mBACF,CAQA,0BAEE,QAAS,CADT,yBAA0B,CAE1B,YACF,CAEA,gBACE,cACF,CAEA,mBACE,gBACF,CAEA,cACE,gBACF,CACF,CAEA,yBACE,cACE,qBAAsB,CAEtB,SAAW,CADX,iBAEF,CAEA,iBACE,gBACF,CAMA,2BACE,YACF,CAEA,cACE,qBACF,CAEA,YACE,sBACF,CACF,CAGA,oBACE,GACE,SAAU,CACV,0BACF,CACA,GACE,SAAU,CACV,uBACF,CACF,CAMA,0BACE,+BACF,CAGA,iBAGE,6BAA8B,CAF9B,qEAAyE,CACzE,yBAEF,CAEA,mBACE,GACE,2BACF,CACA,GACE,0BACF,CACF,CAGA,yDAGE,2BAA2C,CAC3C,kBACF,CAGA,wBACE,kBAAmB,CACnB,+BAAgC,CAEhC,YAAa,CACb,qBAAsB,CACtB,UAAW,CAHX,mBAIF,CAEA,kBAGE,eAAgB,CAFhB,iBAAkB,CAClB,UAEF,CAEA,iBAEE,kBAAmB,CADnB,YAAa,CAEb,QACF,CAEA,aAKE,aAAc,CAHd,SAKF,CAEA,cAME,eAAiB,CAHjB,wBAAyB,CADzB,iCAMF,CAEA,oBAEE,oBAAqB,CACrB,8BACF,CAIA,cAEE,kBAEF,CAEA,cAEE,aAAc,CAEd,kBACF,CAEA,eAIE,eAAiB,CAFjB,wBAAyB,CACzB,iBAAkB,CAElB,aAAc,CAEd,cAAe,CADf,iBAAmB,CAGnB,eAAgB,CARhB,sBAAyB,CAOzB,uBAEF,CAEA,qBAEE,oBAAqB,CACrB,8BAA6C,CAF7C,YAGF,CAKA,qBACE,wBAAyB,CAGzB,eAAgB,CADhB,iBAAkB,CADlB,uBAGF,CAEA,4BAOE,iDAAoD,CANpD,UAAW,CAKX,UAAW,CAFX,MAAO,CAIP,SAAU,CANV,iBAAkB,CAGlB,OAAQ,CAFR,KAAM,CAMN,2BACF,CAEA,kCACE,SACF,CAEA,2BACE,oBAAqB,CAErB,gCAAgD,CADhD,0BAEF,CAGA,yBACE,gBACE,kBACF,CAEA,kBACE,cACF,CAEA,eAEE,mBAAoB,CADpB,qBAAsB,CAEtB,UACF,CAEA,cACE,iBACF,CAMA,6BACE,sBACF,CAEA,uBACE,SACF,CAMA,6BACE,mBACF,CAEA,mBACE,mBACF,CAEA,qBACE,QACF,CAEA,gBAEE,mBAAoB,CADpB,qBAAsB,CAEtB,UAAW,CACX,cACF,CAEA,eACE,sBACF,CAEA,kBACE,qBAAsB,CACtB,QACF,CAEA,oCACE,UACF,CAEA,kCACE,sBACF,CAEA,eAEE,mBAAoB,CADpB,qBAAsB,CAEtB,QACF,CAEA,aAEE,sBAAuB,CADvB,qBAAsB,CAEtB,SACF,CAEA,cACE,sBACF,CAEA,oBACE,iBACF,CAEA,gCACE,cACF,CAEA,uBACE,gBACF,CAEA,sBACE,cACF,CACF,CAEA,yBACE,cACE,cACF,CAEA,0BACE,eACF,CAEA,cAEE,iBAAmB,CADnB,eAEF,CAMA,sBACE,cACF,CAEA,YAEE,eAAiB,CADjB,oBAEF,CAEA,aACE,iBACF,CAEA,YACE,cACF,CAEA,gBACE,iBACF,CACF,CAGA,UACE,eAAiB,CACjB,+BAAgC,CAChC,8BACF,CAEA,gBAQE,uBAAwB,CALxB,YAAa,CAEb,KAAM,CADN,sBAAuB,CAFvB,aAAc,CADd,gBAAiB,CAKjB,eAAgB,CAChB,oBAEF,CAEA,mCACE,YACF,CAEA,SAEE,kBAAmB,CAGnB,gBAAuB,CAOvB,WAAoC,CAApC,6BAAoC,CALpC,aAAc,CAGd,cAAe,CATf,YAAa,CAOb,cAAe,CACf,eAAgB,CANhB,UAAY,CACZ,oBAAqB,CAUrB,iBAAkB,CAHlB,uBAAyB,CAEzB,kBAEF,CAOA,+BAHE,kBAAmB,CADnB,aAQF,CAJA,gBAEE,2BAEF,CAEA,mBACE,iBACF,CAGA,iBACE,eAAiB,CACjB,+BAAgC,CAChC,gBACF,CAEA,mBAME,UAAW,CAJX,aAAc,CADd,gBAAiB,CAEjB,cAIF,CAEA,iCALE,YAAa,CACb,qBAQF,CAJA,cAGE,UACF,CAEA,cAOE,kBAAmB,CAJnB,aAAc,CAGd,YAAa,CAGb,cAAe,CARf,iBAAmB,CACnB,eAAgB,CAMhB,SAAW,CAHX,oBAAsB,CADtB,wBAMF,CAEA,uBAIE,kBAAmB,CAGnB,wBAAyB,CADzB,kBAAmB,CAKnB,8BAA6C,CAR7C,UAAY,CAFZ,iBAAmB,CACnB,eAAgB,CAOhB,qBAAsB,CACtB,iBAAmB,CALnB,sBAAyB,CAGzB,mBAIF,CAGA,gBACE,iBACF,CAEA,mBAEE,kBAAmB,CAGnB,kBAAmB,CACnB,wBAAyB,CACzB,iBAAkB,CAClB,aAAc,CAEd,cAAe,CATf,YAAa,CAQb,eAAgB,CANhB,6BAA8B,CAS9B,eAAgB,CARhB,mBAAqB,CAOrB,uBAEF,CAEA,yBAEE,kBAAmB,CADnB,oBAEF,CAEA,SACE,6BACF,CAEA,cACE,wBACF,CAEA,gBAKE,eAAiB,CACjB,wBAAyB,CACzB,iBAAkB,CAClB,gCAA0C,CAL1C,MAAO,CAOP,gBAAiB,CACjB,eAAgB,CAVhB,iBAAkB,CAGlB,OAAQ,CAFR,QAAS,CAOT,UAGF,CAEA,cAIE,gBAAuB,CACvB,WAAY,CAEZ,aAAc,CACd,cAAe,CAPf,aAAc,CAEd,mBAAqB,CAGrB,eAAgB,CAGhB,oCAAsC,CAPtC,UAQF,CAEA,oBACE,kBACF,CAEA,qBACE,kBAAmB,CACnB,UACF,CAEA,2BACE,kBAAmB,CACnB,wBAAyB,CACzB,aAAc,CACd,eACF,CAEA,iCACE,kBAAmB,CACnB,oBACF,CAEA,kCACE,kBAAmB,CAEnB,oBAAqB,CADrB,UAEF,CAEA,eAIE,kBAAmB,CAInB,wBAAyB,CAFzB,iBAAkB,CAHlB,aAAc,CAFd,iBAAmB,CACnB,eAAgB,CAQhB,qBAAuB,CAHvB,iBAAmB,CAFnB,uBAA0B,CAI1B,wBAEF,CAEA,qFAEE,gBAAoC,CAEpC,sBAAsC,CADtC,UAEF,CAEA,kCAEE,kBAAmB,CACnB,oBAAqB,CAFrB,aAGF,CAGA,eACE,YAAa,CACb,cAAe,CACf,UACF,CAEA,cAEE,kBAAmB,CACnB,wBAAyB,CACzB,kBAAmB,CACnB,aAAc,CAGd,cAAe,CAFf,iBAAmB,CACnB,eAAgB,CANhB,kBAAoB,CAQpB,uBACF,CAEA,oBACE,oBAAqB,CACrB,aACF,CAEA,qBACE,kBAAmB,CACnB,oBAAqB,CACrB,UACF,CAGA,uBAGE,oBAAqB,CAGrB,4BAA6B,CAL7B,YAAa,CACb,QAAS,CAET,eAAgB,CAChB,gBAEF,CAGA,gBACE,YAAa,CAGb,QAAO,CAFP,qBAAsB,CACtB,UAEF,CAGA,cACE,YAAa,CACb,qBAAsB,CAEtB,aAAc,CADd,UAEF,CAEA,YAGE,eAAgB,CAFhB,iBAAkB,CAClB,UAEF,CAEA,aAKE,aAAc,CACd,iBAAmB,CAJnB,WAAa,CADb,iBAAkB,CAElB,OAAQ,CACR,0BAGF,CAEA,cAME,kBAAmB,CAHnB,wBAAyB,CACzB,iBAAkB,CAClB,iBAAmB,CAHnB,mCAAuC,CAKvC,uBAAyB,CANzB,UAOF,CAEA,oBAGE,eAAiB,CADjB,oBAAqB,CAErB,8BAA6C,CAH7C,YAIF,CAEA,eAEE,eAAgB,CADhB,WAEF,CAEA,aAKE,kBAAmB,CAFnB,wBAAyB,CACzB,iBAAkB,CAElB,aAAc,CAEd,cAAe,CADf,iBAAmB,CALnB,cAAgB,CAOhB,uBAAyB,CARzB,UASF,CAEA,mBAGE,eAAiB,CADjB,oBAAqB,CAErB,8BAA6C,CAH7C,YAIF,CAGA,aACE,uBACE,yBACF,CAEA,eACE,yBAA2B,CAC3B,oBACF,CAOA,gCAHE,qBAAsB,CADtB,yBAQF,CAJA,eAGE,kBAAmB,CAAnB,uBACF,CAEA,2BAEE,sBACF,CACF,CAGA,mBAEE,aAAc,CADd,gBAAiB,CAEjB,YACF,CAEA,eAGE,kBAAmB,CAGnB,aAAc,CALd,YAAa,CACb,qBAAsB,CAEtB,sBAAuB,CACvB,iBAEF,CAEA,iBAIE,wBAA6B,CAA7B,wBAA6B,CAG7B,kBACF,CAQA,gBAGE,eAAW,CAFX,YAAa,CAEb,UAAW,CADX,yDAEF,CAEA,eACE,eAAiB,CACjB,wBAAyB,CACzB,kBAAmB,CAGnB,8BAAyC,CAFzC,eAAgB,CAChB,uBAEF,CAEA,qBAGE,oBAAqB,CADrB,+BAAyC,CADzC,0BAGF,CAEA,aAGE,sBAAuB,CAEvB,+BAAgC,CAJhC,YAAa,CACb,6BAA8B,CAE9B,2BAEF,CAEA,cAIE,SACF,CAEA,0BAJE,oBAAqB,CAFrB,YAAa,CACb,qBAUF,CALA,YAGE,UAEF,CAEA,WAEE,kBAAmB,CACnB,eAAiB,CACjB,eAAgB,CAGhB,eAAgB,CANhB,qBAAwB,CAKxB,iBAAkB,CADlB,kBAGF,CAEA,sBACE,kBAAmB,CAEnB,wBAAyB,CADzB,aAEF,CAEA,wBACE,kBAAmB,CAEnB,wBAAyB,CADzB,aAEF,CAEA,eAEE,kBAAmB,CADnB,YAAa,CAEb,SACF,CAEA,WAEE,aAAc,CADd,cAEF,CAEA,YAEE,eAAgB,CAGhB,oBAAsB,CADtB,wBAEF,CAEA,2BALE,aAAc,CAFd,gBAcF,CAPA,eACE,kBAAmB,CAGnB,kBAAmB,CAEnB,eAAgB,CAHhB,qBAIF,CAEA,cACE,eACF,CAEA,gBAGE,aAAc,CAFd,kBAAmB,CACnB,eAAgB,CAGhB,eAAgB,CADhB,iBAEF,CAEA,eACE,YAAa,CACb,cAAe,CACf,UAAY,CACZ,kBACF,CAEA,kBACE,kBAAmB,CACnB,aAKF,CAEA,kCALE,kBAAmB,CACnB,gBAAkB,CAClB,eAAgB,CAHhB,qBAaF,CAPA,gBACE,kBAAmB,CACnB,aAKF,CAEA,cAEE,kBAAmB,CAEnB,UAAY,CAHZ,oBAKF,CAEA,0BAHE,kBAAmB,CAFnB,YAgBF,CAXA,YAKE,WAAY,CACZ,iBAAkB,CAGlB,cAAe,CAFf,iBAAmB,CACnB,eAAgB,CALhB,SAAW,CACX,kBAAoB,CAMpB,uBACF,CAEA,oBACE,kBAAmB,CACnB,UACF,CAEA,0BACE,kBAAmB,CACnB,0BACF,CAEA,sBACE,kBAAmB,CAEnB,wBAAyB,CADzB,aAEF,CAEA,4BACE,kBAAmB,CACnB,aACF,CAEA,aACE,aAAc,CACd,iBAAmB,CACnB,iBACF,CAGA,aAGE,kBAAmB,CAInB,aAAc,CANd,YAAa,CACb,qBAAsB,CAEtB,sBAAuB,CACvB,iBAAkB,CAClB,iBAEF,CAEA,YAEE,aAAc,CADd,cAAe,CAEf,oBACF,CAEA,gBAGE,aAAc,CAFd,gBAAiB,CACjB,eAAgB,CAEhB,iBACF,CAEA,eACE,cAAe,CACf,gBAAoB,CACpB,eACF,CAGA,aAGE,kBAAmB,CAInB,aAAc,CANd,YAAa,CACb,qBAAsB,CAEtB,sBAAuB,CACvB,iBAAkB,CAClB,iBAEF,CAEA,YAEE,aAAc,CADd,cAAe,CAEf,oBACF,CAEA,gBAGE,aAAc,CAFd,gBAAiB,CACjB,eAAgB,CAEhB,iBACF,CAEA,eAIE,aAAc,CAHd,cAAe,CACf,iBAAoB,CACpB,eAEF,CAEA,WACE,kBAAmB,CAEnB,WAAY,CAEZ,iBAAkB,CAHlB,UAAY,CAMZ,cAAe,CAFf,cAAe,CACf,eAAgB,CAHhB,qBAAuB,CAKvB,uBACF,CAEA,iBACE,kBAAmB,CACnB,0BACF,CAEA,YACE,uBAAyB,CACzB,2BACF,CAGA,2BAGE,kBAAmB,CAEnB,cAAe,CADf,kBAAmB,CAFnB,eAAgB,CADhB,iBAKF,CAEA,iBAKE,aAAc,CAHd,YAAa,CACb,gBAAiB,CACjB,6BAA+B,CAH/B,UAKF,CAEA,uBACE,qBACF,CAEA,cASE,kBAAmB,CAHnB,gBAA8B,CAD9B,QAAS,CAET,YAAa,CACb,qBAAsB,CAEtB,sBAAuB,CAPvB,MAAO,CAQP,SAAU,CAVV,iBAAkB,CAGlB,OAAQ,CAFR,KAAM,CAUN,2BACF,CAEA,+CACE,SACF,CAGA,cAIE,eAAgB,CADhB,iBAAkB,CAIlB,aAAc,CALd,YAAa,CAIb,kBAAmB,CADnB,YAAa,CAGb,iBAAkB,CAPlB,UAAW,CAQX,SACF,CAEA,oBACE,yBAA0B,CAC1B,kBACF,CAGA,eAME,oBAA8B,CAQ9B,gBAAiB,CADjB,eAAgB,CAFhB,YAAa,CACb,uBAAyB,CAFzB,YAKF,CAEA,wBAEE,oBAA+B,CAD/B,SAEF,CAEA,aAQE,4BAA0C,CAI1C,YAAa,CACb,qBAAsB,CAFtB,WAAY,CAPZ,eAAgB,CAChB,gBAAiB,CAEjB,eAAgB,CAGhB,iBAAkB,CADlB,uBAMF,CAEA,sBAKE,eAAgB,CAHhB,YAAa,CAEb,gBAAiB,CADjB,eAAgB,CAFhB,WAKF,CAEA,cAME,kDAA6D,CAJ7D,4BAA6B,CAD7B,iBAAkB,CAMlB,iBACF,CAEA,eAEE,UAAW,CACX,gBAAiB,CACjB,eAAgB,CAChB,eAAgB,CAJhB,cAKF,CAEA,YACE,YAAa,CACb,QAAS,CACT,cACF,CAEA,4BAIE,kBAAmB,CAFnB,gBAAkB,CAGlB,eAAgB,CAFhB,eAGF,CAEA,eACE,kBAAmB,CACnB,aACF,CAEA,aACE,kBAAmB,CACnB,aACF,CAEA,cAGE,kBAAmB,CAFnB,gBAAkB,CAGlB,eAAgB,CAFhB,eAGF,CAEA,wBACE,kBAAmB,CACnB,aACF,CAEA,0BACE,kBAAmB,CACnB,aACF,CAEA,yBAGE,2BAA4B,CAF5B,kBAAmB,CACnB,aAEF,CAEA,iBACE,MAAW,SAAY,CACvB,IAAM,UAAc,CACtB,CAEA,gBAGE,kBAAmB,CAFnB,YAAa,CACb,QAEF,CAEA,kBAIE,eAAiB,CAFjB,qBAAsB,CACtB,iBAAkB,CAGlB,UAAW,CACX,cAAe,CAFf,gBAAkB,CAJlB,gBAAiB,CAOjB,uBACF,CAEA,wBACE,oBACF,CAEA,wBAEE,oBAAqB,CACrB,8BAA6C,CAF7C,YAGF,CAEA,aAUE,kBAAmB,CATnB,eAAgB,CAChB,WAAY,CAKZ,iBAAkB,CAHlB,UAAW,CACX,cAAe,CAIf,YAAa,CANb,gBAAiB,CAQjB,sBAAuB,CALvB,WAAY,CAEZ,uBAIF,CAEA,mBACE,oBAA8B,CAC9B,UAAW,CACX,qBACF,CAEA,kBACE,aACF,CAEA,oBACE,aACF,CAEA,iBACE,aACF,CAEA,iBACE,eAAgB,CAGhB,iBAAkB,CAElB,gBAAiB,CADjB,gBAAiB,CAFjB,YAIF,CAEA,uCAGE,eAAgB,CADhB,6BAA8B,CAD9B,SAGF,CAEA,cAIE,iBAAkB,CAFlB,YAGF,CAEA,oCAEE,eAAgB,CADhB,yBAEF,CAEA,oCAEE,eAAgB,CADhB,mCAEF,CAGA,eAGE,kBAAmB,CAGnB,eAAgB,CAEhB,iBAAkB,CADlB,UAAY,CANZ,YAAa,CACb,qBAAsB,CAGtB,WAAY,CADZ,sBAKF,CAEA,qCAEE,eAAgB,CADhB,WAEF,CAEA,iBAME,iCAAkC,CAFlC,0BAA0B,CAC1B,iBAAkB,CADlB,qBAA0B,CAF1B,WAAY,CAKZ,kBAAmB,CANnB,UAOF,CAOA,iBAEE,cAAe,CADf,QAAS,CAET,UACF,CAGA,yBAGE,iBAAkB,CAElB,cAAe,CAHf,YAAa,CAEb,eAAgB,CAHhB,iBAKF,CAEA,yBAEE,WAAY,CACZ,gBAAiB,CACjB,6BAA+B,CAH/B,UAIF,CAEA,wDACE,qBACF,CAEA,uBASE,kBAAmB,CAHnB,gBAA8B,CAD9B,QAAS,CAET,YAAa,CACb,qBAAsB,CAEtB,sBAAuB,CAPvB,MAAO,CAFP,iBAAkB,CAGlB,OAAQ,CAFR,KAAM,CASN,2BACF,CAEA,wBAME,kBAAmB,CALnB,oBAAoC,CACpC,WAAY,CACZ,kBAAmB,CAUnB,+BAAyC,CAHzC,UAAW,CACX,cAAe,CANf,YAAa,CAGb,gBAAiB,CACjB,eAAgB,CAFhB,QAAS,CAHT,iBAAkB,CAQlB,uBAEF,CAEA,8BACE,eAAiB,CAEjB,2BAAyC,CADzC,qBAEF,CAEA,mBAEE,aAAc,CADd,cAEF,CAGA,uCAIE,UAAY,CAFZ,eAAgB,CAChB,iBAEF,CAEA,cAGE,oBAAoC,CACpC,iBAAkB,CAFlB,UAAW,CAIX,gBAAiB,CADjB,eAAgB,CAJhB,WAMF,CAEA,eAEE,iDAAoD,CACpD,iBAAkB,CAFlB,WAAY,CAGZ,yBACF,CAEA,iDAEE,eAAiB,CACjB,UACF,CAGA,cAIE,oBAAoC,CAGpC,sBAA0C,CAD1C,iBAAkB,CALlB,eAAgB,CAEhB,eAAgB,CAEhB,YAAa,CAHb,eAMF,CAEA,gBAIE,UAAc,CAFd,eAAiB,CACjB,eAAgB,CAFhB,eAIF,CAEA,iBAGE,oBAAqB,CAFrB,QAAS,CACT,iBAEF,CAEA,iBACE,gBAAkB,CAGlB,eAAgB,CAFhB,iBAAkB,CAClB,UAEF,CAEA,iBACE,YAAa,CAIb,cAAe,CAHf,QAAS,CAET,sBAAuB,CADvB,aAGF,CAEA,qCAEE,WAAY,CACZ,iBAAkB,CAGlB,cAAe,CAFf,eAAiB,CACjB,eAAgB,CAGhB,eAAgB,CAPhB,iBAAkB,CAMlB,uBAEF,CAEA,0BACE,kBAAmB,CAInB,8BAA4C,CAH5C,UAAY,CACZ,cAAe,CACf,iBAEF,CAEA,gCACE,kBAAmB,CAEnB,+BAA6C,CAD7C,0BAEF,CAEA,mBACE,gBAAoC,CAEpC,0BAA0C,CAD1C,UAEF,CAEA,yBACE,oBAAoC,CACpC,0BACF,CAGA,kBAEE,kBAAmB,CAGnB,kDAAqD,CACrD,kBAAmB,CACnB,UAAY,CANZ,YAAa,CAEb,sBAAuB,CACvB,gBAAiB,CAKjB,YAAa,CADb,iBAEF,CAEA,oBACE,eACF,CAEA,oBACE,oBACF,CAEA,SAME,iCAAkC,CAFlC,0BAA2B,CAC3B,iBAAkB,CADlB,qBAA2B,CAF3B,WAAY,CAKZ,aAAc,CANd,UAOF,CAOA,uBACE,gBAAiB,CAEjB,eAAgB,CADhB,kBAEF,CAEA,sBAGE,eAAgB,CAFhB,oBAAsB,CACtB,UAEF,CAEA,oBACE,eACF,CAEA,aACE,gBAAoC,CACpC,0BAA0C,CAG1C,iBAAkB,CAFlB,UAAY,CAGZ,cAAe,CACf,eAAgB,CAEhB,kBAAmB,CALnB,qBAAuB,CAIvB,uBAEF,CAEA,kCACE,oBAAoC,CACpC,sBAAsC,CACtC,0BACF,CAEA,sBAEE,kBAAmB,CADnB,UAEF,CAEA,iBACE,eAAiB,CAEjB,QAAS,CADT,UAEF,CAGA,aAEE,kBAAmB,CAGnB,kBAAmB,CAGnB,sBAAuB,CADvB,iBAAkB,CADlB,UAAW,CALX,YAAa,CAGb,YAAa,CADb,sBAMF,CAEA,eAEE,cAAe,CADf,QAEF,CAGA,oBAEE,kBAAmB,CAGnB,iDAAqD,CAErD,iBAAkB,CADlB,UAAY,CALZ,YAAa,CAEb,sBAAuB,CACvB,gBAAiB,CAIjB,YAAa,CACb,iBACF,CAEA,yBAEE,gBAAiB,CACjB,eAAgB,CAFhB,eAGF,CAEA,wBAEE,cAAe,CACf,eAAgB,CAFhB,aAGF,CAEA,sBACE,eACF,CAEA,oBAEE,gBAAoC,CAQpC,0BAA0C,CAJ1C,kBAAmB,CAHnB,UAAY,CAFZ,oBAAqB,CAOrB,cAAe,CADf,eAAgB,CAFhB,iBAAkB,CADlB,oBAAqB,CAKrB,uBAEF,CAEA,0BACE,oBAAoC,CAEpC,2BAAyC,CADzC,0BAEF,CAEA,aAEE,eAAiB,CAEjB,iBAAkB,CAHlB,eAAgB,CAEhB,UAEF,CAOA,cAEE,kBAAmB,CACnB,yBAA0B,CAF1B,iBAGF,CAEA,mBACE,kBACF,CAEA,qBAEE,UAAW,CACX,gBAAkB,CAClB,eAAgB,CAHhB,QAIF,CAEA,eACE,YAAa,CACb,QAAS,CACT,wBACF,CAEA,2BAEE,kBAAmB,CAInB,iBAAkB,CALlB,YAAa,CAIb,eAAiB,CAFjB,OAAQ,CACR,iBAAkB,CAGlB,uBACF,CAEA,iCAEE,+BAA0C,CAD1C,0BAEF,CAGA,yBACE,eACE,YACF,CAEA,aAGE,eAAgB,CADhB,cAAe,CADf,UAGF,CAEA,cACE,YACF,CAEA,eACE,gBACF,CAEA,YACE,qBAAsB,CACtB,OACF,CAEA,gBACE,OACF,CAEA,aAEE,cAAe,CADf,WAEF,CAEA,iBACE,YACF,CAEA,4BAEE,WACF,CAEA,cACE,YACF,CAEA,eACE,sBACF,CACF,CAEA,yBACE,eACE,WACF,CAEA,aACE,eACF,CAEA,cACE,YACF,CAEA,eACE,cACF,CAEA,iBACE,YACF,CAEA,4BAEE,WACF,CACF,CAEA,WAEE,UAAY,CADZ,cAAe,CAEf,mBACF,CAEA,WACE,UAAY,CAEZ,cAAe,CADf,eAEF,CAEA,gBAOE,kBAAmB,CALnB,kDAA6D,CAC7D,WAAY,CACZ,UAAY,CAEZ,YAAa,CADb,eAAgB,CAIhB,SAAW,CADX,sBAAuB,CAPvB,UASF,CAEA,sBACE,kDAA6D,CAC7D,0BACF,CAGA,eAQE,kBAAmB,CAFnB,gBAA8B,CAD9B,QAAS,CAET,YAAa,CAEb,sBAAuB,CANvB,MAAO,CAQP,YAAa,CAVb,cAAe,CAGf,OAAQ,CAFR,KAAM,CAQN,YAEF,CAEA,aACE,eAAiB,CACjB,kBAAmB,CAKnB,gCAA2C,CAD3C,eAAgB,CAFhB,eAAgB,CADhB,eAAgB,CAEhB,UAGF,CAEA,cAGE,kBAAmB,CAGnB,kBAAmB,CADnB,+BAAgC,CAJhC,YAAa,CACb,6BAA8B,CAE9B,cAGF,CAEA,iBAGE,aAAc,CAFd,iBAAkB,CAClB,eAAgB,CAEhB,QACF,CAEA,iBAEE,kBAAmB,CAInB,kBAAmB,CACnB,WAAY,CACZ,iBAAkB,CAClB,aAAc,CACd,cAAe,CATf,YAAa,CAIb,WAAY,CAFZ,sBAAuB,CAQvB,uBAAyB,CAPzB,UAQF,CAEA,uBACE,kBAAmB,CACnB,aACF,CAEA,iBAGE,QAAS,CACT,qBAAsB,CAHtB,iBAAkB,CAClB,UAGF,CAEA,cAME,WAAY,CADZ,WAAY,CAFZ,MAAO,CAFP,iBAAkB,CAClB,KAAM,CAEN,UAGF,CAGA,yBACE,eACE,mBACF,CAEA,gBACE,qBAAsB,CACtB,QAAS,CACT,iBACF,CAEA,gBACE,cACF,CAEA,gBACE,cACF,CAEA,SAEE,iBAAmB,CADnB,mBAEF,CAEA,mBACE,cACF,CAEA,uBAGE,mBAAoB,CAFpB,qBAAsB,CACtB,QAEF,CAEA,8BAEE,SACF,CAEA,YACE,cACF,CAEA,eAEE,cAAe,CADf,UAEF,CAEA,mBACE,YACF,CAEA,gBAEE,QAAS,CADT,yBAEF,CAEA,eACE,sBACF,CAEA,eACE,YACF,CAEA,aACE,eACF,CAEA,cACE,YACF,CAEA,iBACE,cACF,CACF,CAEA,yBACE,gBACE,iBACF,CAEA,SACE,mBACF,CAEA,mBACE,cACF,CAEA,eACE,cACF,CAEA,cACE,qBAAsB,CACtB,SACF,CAEA,YAEE,sBAAuB,CADvB,UAEF,CACF,CAGA,gBACE,GAAK,sBAAyB,CAC9B,GAAO,uBAA2B,CACpC,CAGA,gCAEE,0BAAwC,CADxC,0BAEF,CAEA,iCACE,uBACF", "sources": ["pages/user/StudyMaterial/index.css"], "sourcesContent": ["/* Modern Study Material Styles */\r\n.study-material-modern {\r\n  min-height: 100vh !important;\r\n  background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 25%, #f8fafc 50%, #ffffff 100%) !important;\r\n  font-family: 'Inter', '<PERSON>o', 'Nunito', -apple-system, BlinkMacSystemFont, sans-serif !important;\r\n  color: var(--gray-800) !important;\r\n  line-height: 1.6 !important;\r\n  position: relative;\r\n  overflow-x: hidden;\r\n}\r\n\r\n.study-material-modern::before {\r\n  content: '';\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  background:\r\n    radial-gradient(circle at 20% 80%, rgba(0, 123, 255, 0.08) 0%, transparent 50%),\r\n    radial-gradient(circle at 80% 20%, rgba(0, 86, 210, 0.06) 0%, transparent 50%),\r\n    radial-gradient(circle at 40% 40%, rgba(0, 212, 255, 0.04) 0%, transparent 50%);\r\n  pointer-events: none;\r\n  z-index: 0;\r\n}\r\n\r\n.study-material-modern > * {\r\n  position: relative;\r\n  z-index: 1;\r\n}\r\n\r\n/* Modern Header */\r\n.modern-header {\r\n  background: linear-gradient(135deg, #007BFF 0%, #0056D2 50%, #4338CA 100%) !important;\r\n  color: white !important;\r\n  padding: var(--space-6) !important;\r\n  box-shadow:\r\n    0 20px 25px -5px rgba(0, 123, 255, 0.25),\r\n    0 10px 10px -5px rgba(0, 123, 255, 0.1) !important;\r\n  border-radius: var(--radius-xl) !important;\r\n  margin-bottom: var(--space-4) !important;\r\n  position: relative;\r\n  overflow: hidden;\r\n}\r\n\r\n.modern-header::before {\r\n  content: '';\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);\r\n  pointer-events: none;\r\n  z-index: 0;\r\n}\r\n\r\n.modern-header > * {\r\n  position: relative;\r\n  z-index: 1;\r\n}\r\n\r\n.header-content {\r\n  max-width: 1200px;\r\n  margin: 0 auto;\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n\r\n.header-main {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 1.5rem;\r\n}\r\n\r\n.header-icon {\r\n  font-size: 3rem;\r\n  color: #e2e8f0;\r\n  opacity: 0.9;\r\n}\r\n\r\n.header-text h1 {\r\n  font-size: 2.5rem;\r\n  font-weight: 700;\r\n  margin: 0 0 0.5rem 0;\r\n  color: white;\r\n}\r\n\r\n.header-text p {\r\n  font-size: 1.1rem;\r\n  margin: 0;\r\n  color: #e2e8f0;\r\n  opacity: 0.9;\r\n}\r\n\r\n.level-badge {\r\n  background: rgba(255, 255, 255, 0.2);\r\n  padding: 0.75rem 1.5rem;\r\n  border-radius: 50px;\r\n  font-weight: 600;\r\n  font-size: 0.9rem;\r\n  backdrop-filter: blur(10px);\r\n  border: 1px solid rgba(255, 255, 255, 0.3);\r\n}\r\n\r\n.header-title h1 {\r\n  font-size: 2.5rem;\r\n  font-weight: 600;\r\n  margin: 0;\r\n  color: white;\r\n  letter-spacing: -0.025em;\r\n}\r\n\r\n.header-subtitle {\r\n  font-size: 1.1rem;\r\n  opacity: 0.9;\r\n  font-weight: 400;\r\n  color: #e2e8f0;\r\n}\r\n\r\n.school-type-badge {\r\n  position: absolute;\r\n  top: 2rem;\r\n  right: 2rem;\r\n  background: rgba(255, 255, 255, 0.15);\r\n  backdrop-filter: blur(10px);\r\n  border: 1px solid rgba(255, 255, 255, 0.2);\r\n  border-radius: 25px;\r\n  padding: 0.5rem 1.25rem;\r\n}\r\n\r\n.badge-text {\r\n  font-weight: 500;\r\n  font-size: 0.875rem;\r\n  letter-spacing: 0.5px;\r\n  color: white;\r\n}\r\n\r\n/* Simplified Material Selection */\r\n.material-selection {\r\n  background: white;\r\n  margin: -1.5rem 2rem 2rem;\r\n  border-radius: 16px;\r\n  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05), 0 10px 15px rgba(0, 0, 0, 0.1);\r\n  padding: 2rem;\r\n  position: relative;\r\n  z-index: 3;\r\n  border: 1px solid #e2e8f0;\r\n}\r\n\r\n.selection-header {\r\n  text-align: center;\r\n  margin-bottom: 2rem;\r\n  padding-bottom: 1.5rem;\r\n  border-bottom: 1px solid #e2e8f0;\r\n}\r\n\r\n.selection-header h2 {\r\n  font-size: 1.75rem;\r\n  font-weight: 600;\r\n  color: #2d3748;\r\n  margin: 0 0 0.5rem 0;\r\n  letter-spacing: -0.025em;\r\n}\r\n\r\n.selection-header p {\r\n  font-size: 1rem;\r\n  color: #718096;\r\n  margin: 0;\r\n  font-weight: 400;\r\n}\r\n\r\n/* Material Type Tabs */\r\n.material-tabs {\r\n  display: flex;\r\n  gap: 0.5rem;\r\n  margin-bottom: 2rem;\r\n  padding: 0.5rem;\r\n  background: #f7fafc;\r\n  border-radius: 12px;\r\n  overflow-x: auto;\r\n}\r\n\r\n.tab-btn {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 0.5rem;\r\n  padding: 1rem 1.5rem;\r\n  border: none;\r\n  border-radius: 8px;\r\n  background: transparent;\r\n  color: #4a5568;\r\n  font-weight: 500;\r\n  cursor: pointer;\r\n  transition: all 0.2s ease;\r\n  white-space: nowrap;\r\n  flex: 1;\r\n  justify-content: center;\r\n  min-width: 120px;\r\n}\r\n\r\n.tab-btn:hover {\r\n  background: #edf2f7;\r\n  color: #2d3748;\r\n}\r\n\r\n.tab-btn.active {\r\n  background: #3182ce;\r\n  color: white;\r\n  box-shadow: 0 2px 4px rgba(49, 130, 206, 0.2);\r\n}\r\n\r\n.tab-icon {\r\n  font-size: 1rem;\r\n}\r\n\r\n/* Subject Filter */\r\n.subject-filter {\r\n  margin-bottom: 1.5rem;\r\n}\r\n\r\n.filter-header {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: space-between;\r\n  margin-bottom: 1rem;\r\n  padding-bottom: 0.75rem;\r\n  border-bottom: 1px solid #e2e8f0;\r\n}\r\n\r\n.filter-label {\r\n  font-weight: 600;\r\n  color: #2d3748;\r\n  font-size: 1rem;\r\n}\r\n\r\n.class-info {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 0.5rem;\r\n}\r\n\r\n.current-class {\r\n  background: #3182ce;\r\n  color: white;\r\n  padding: 0.5rem 1rem;\r\n  border-radius: 20px;\r\n  font-size: 0.875rem;\r\n  font-weight: 500;\r\n}\r\n\r\n.change-class-btn {\r\n  background: #f7fafc;\r\n  border: 1px solid #e2e8f0;\r\n  border-radius: 6px;\r\n  padding: 0.5rem;\r\n  cursor: pointer;\r\n  transition: all 0.2s ease;\r\n  color: #4a5568;\r\n}\r\n\r\n.change-class-btn:hover {\r\n  background: #edf2f7;\r\n  border-color: #cbd5e0;\r\n}\r\n\r\n/* Subject Buttons */\r\n.subject-buttons {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  gap: 0.75rem;\r\n}\r\n\r\n.subject-btn {\r\n  padding: 0.75rem 1.25rem;\r\n  border: 2px solid #e2e8f0;\r\n  border-radius: 8px;\r\n  background: white;\r\n  color: #4a5568;\r\n  font-weight: 500;\r\n  cursor: pointer;\r\n  transition: all 0.2s ease;\r\n  font-size: 0.875rem;\r\n}\r\n\r\n.subject-btn:hover {\r\n  border-color: #3182ce;\r\n  color: #2b6cb0;\r\n  background: #ebf8ff;\r\n}\r\n\r\n.subject-btn.active {\r\n  background: #3182ce;\r\n  color: white;\r\n  border-color: #3182ce;\r\n}\r\n\r\n/* Class Selection Modal */\r\n.class-selection-overlay {\r\n  position: fixed;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  background: rgba(0, 0, 0, 0.5);\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  z-index: 1000;\r\n}\r\n\r\n.class-selection-modal {\r\n  background: white;\r\n  border-radius: 16px;\r\n  padding: 2rem;\r\n  max-width: 400px;\r\n  width: 90%;\r\n  max-height: 80vh;\r\n  overflow-y: auto;\r\n  box-shadow: 0 20px 25px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.modal-header {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: space-between;\r\n  margin-bottom: 1.5rem;\r\n  padding-bottom: 1rem;\r\n  border-bottom: 1px solid #e2e8f0;\r\n}\r\n\r\n.modal-header h3 {\r\n  font-size: 1.25rem;\r\n  font-weight: 600;\r\n  color: #2d3748;\r\n  margin: 0;\r\n}\r\n\r\n.close-btn {\r\n  background: #f7fafc;\r\n  border: 1px solid #e2e8f0;\r\n  border-radius: 6px;\r\n  padding: 0.5rem;\r\n  cursor: pointer;\r\n  transition: all 0.2s ease;\r\n  color: #4a5568;\r\n}\r\n\r\n.close-btn:hover {\r\n  background: #edf2f7;\r\n  border-color: #cbd5e0;\r\n}\r\n\r\n.class-options {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 0.75rem;\r\n}\r\n\r\n.class-option {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 1rem;\r\n  padding: 1rem;\r\n  border: 2px solid #e2e8f0;\r\n  border-radius: 8px;\r\n  background: white;\r\n  color: #4a5568;\r\n  font-weight: 500;\r\n  cursor: pointer;\r\n  transition: all 0.2s ease;\r\n  text-align: left;\r\n}\r\n\r\n.class-option:hover {\r\n  border-color: #3182ce;\r\n  background: #ebf8ff;\r\n}\r\n\r\n.class-option.active {\r\n  background: #3182ce;\r\n  color: white;\r\n  border-color: #3182ce;\r\n}\r\n\r\n/* Show Materials Section */\r\n.show-materials-section {\r\n  display: flex;\r\n  justify-content: center;\r\n  padding: 1.5rem 0;\r\n  border-top: 1px solid #e2e8f0;\r\n  margin-top: 1rem;\r\n}\r\n\r\n.show-materials-btn {\r\n  background: linear-gradient(135deg, #2b6cb0 0%, #3182ce 100%);\r\n  color: white;\r\n  border: none;\r\n  padding: 1rem 2rem;\r\n  border-radius: 8px;\r\n  font-weight: 600;\r\n  font-size: 1rem;\r\n  cursor: pointer;\r\n  transition: all 0.2s ease;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 0.5rem;\r\n  min-width: 180px;\r\n  justify-content: center;\r\n}\r\n\r\n.show-materials-btn:hover:not(:disabled) {\r\n  transform: translateY(-1px);\r\n  box-shadow: 0 4px 12px rgba(43, 108, 176, 0.3);\r\n  background: linear-gradient(135deg, #2c5282 0%, #2b6cb0 100%);\r\n}\r\n\r\n.show-materials-btn:disabled {\r\n  opacity: 0.7;\r\n  cursor: not-allowed;\r\n}\r\n\r\n.spinner {\r\n  animation: spin 1s linear infinite;\r\n}\r\n\r\n@keyframes spin {\r\n  from { transform: rotate(0deg); }\r\n  to { transform: rotate(360deg); }\r\n}\r\n\r\n/* Advanced Options */\r\n.advanced-options {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 1rem;\r\n  justify-content: center;\r\n  padding-top: 1rem;\r\n  border-top: 1px solid #e2e8f0;\r\n  margin-top: 1rem;\r\n}\r\n\r\n.option-btn {\r\n  background: #f7fafc;\r\n  color: #4a5568;\r\n  border: 2px solid #e2e8f0;\r\n  padding: 0.5rem 1rem;\r\n  border-radius: 6px;\r\n  font-weight: 500;\r\n  font-size: 0.875rem;\r\n  cursor: pointer;\r\n  transition: all 0.2s ease;\r\n}\r\n\r\n.option-btn:hover {\r\n  border-color: #3182ce;\r\n  color: #2b6cb0;\r\n  background: #ebf8ff;\r\n}\r\n\r\n.option-btn.active {\r\n  background: #3182ce;\r\n  color: white;\r\n  border-color: #3182ce;\r\n}\r\n\r\n.option-info {\r\n  color: #718096;\r\n  font-size: 0.875rem;\r\n  font-weight: 400;\r\n}\r\n\r\n/* Material Selection Section */\r\n.material-selection {\r\n  background: white;\r\n  border-radius: 12px;\r\n  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05), 0 10px 15px rgba(0, 0, 0, 0.1);\r\n  margin: 0 2rem 2rem;\r\n  border: 1px solid #e2e8f0;\r\n  overflow: hidden;\r\n}\r\n\r\n.selection-header {\r\n  padding: 2rem 2rem 1rem;\r\n  text-align: center;\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n  color: white;\r\n}\r\n\r\n.selection-header h2 {\r\n  font-size: 1.75rem;\r\n  font-weight: 600;\r\n  margin: 0 0 0.5rem 0;\r\n}\r\n\r\n.selection-header p {\r\n  font-size: 1rem;\r\n  opacity: 0.9;\r\n  margin: 0;\r\n}\r\n\r\n/* Material Type Tabs */\r\n.material-tabs {\r\n  display: flex;\r\n  background: #f8fafb;\r\n  border-bottom: 1px solid #e2e8f0;\r\n}\r\n\r\n.tab-btn {\r\n  flex: 1;\r\n  padding: 1.25rem 1rem;\r\n  border: none;\r\n  background: transparent;\r\n  color: #4a5568;\r\n  font-weight: 500;\r\n  cursor: pointer;\r\n  transition: all 0.2s ease;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  gap: 0.5rem;\r\n  border-bottom: 3px solid transparent;\r\n}\r\n\r\n.tab-btn:hover {\r\n  background: #ebf8ff;\r\n  color: #2b6cb0;\r\n}\r\n\r\n.tab-btn.active {\r\n  background: white;\r\n  color: #3182ce;\r\n  border-bottom-color: #3182ce;\r\n}\r\n\r\n.tab-icon {\r\n  font-size: 1.1rem;\r\n}\r\n\r\n/* Subject Filter */\r\n.subject-filter {\r\n  padding: 2rem;\r\n  background: white;\r\n}\r\n\r\n.filter-header {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: space-between;\r\n  margin-bottom: 1.5rem;\r\n  flex-wrap: wrap;\r\n  gap: 1rem;\r\n}\r\n\r\n.filter-label {\r\n  font-weight: 600;\r\n  color: #2d3748;\r\n  font-size: 1rem;\r\n}\r\n\r\n.class-info {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 0.75rem;\r\n  background: #f8fafb;\r\n  padding: 0.75rem 1rem;\r\n  border-radius: 8px;\r\n  border: 1px solid #e2e8f0;\r\n}\r\n\r\n.current-class {\r\n  font-weight: 600;\r\n  color: #2d3748;\r\n  font-size: 0.875rem;\r\n}\r\n\r\n.change-class-btn {\r\n  background: #3182ce;\r\n  color: white;\r\n  border: none;\r\n  padding: 0.5rem;\r\n  border-radius: 6px;\r\n  cursor: pointer;\r\n  transition: all 0.2s ease;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.change-class-btn:hover {\r\n  background: #2c5282;\r\n  transform: translateY(-1px);\r\n}\r\n\r\n.filter-container {\r\n  padding: 2rem;\r\n}\r\n\r\n.filter-header {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: space-between;\r\n  margin-bottom: 2rem;\r\n  flex-wrap: wrap;\r\n  gap: 1rem;\r\n}\r\n\r\n.filter-title h2 {\r\n  font-size: 1.5rem;\r\n  font-weight: 600;\r\n  color: #2d3748;\r\n  margin: 0 0 0.5rem 0;\r\n}\r\n\r\n.filter-title p {\r\n  color: #718096;\r\n  margin: 0;\r\n  font-size: 1rem;\r\n}\r\n\r\n.class-info {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 0.75rem;\r\n  background: #f8fafb;\r\n  padding: 0.75rem 1rem;\r\n  border-radius: 8px;\r\n  border: 1px solid #e2e8f0;\r\n}\r\n\r\n.current-class {\r\n  font-weight: 600;\r\n  color: #2d3748;\r\n  font-size: 0.875rem;\r\n}\r\n\r\n.change-class-btn {\r\n  background: #3182ce;\r\n  color: white;\r\n  border: none;\r\n  padding: 0.5rem;\r\n  border-radius: 6px;\r\n  cursor: pointer;\r\n  transition: all 0.2s ease;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.change-class-btn:hover {\r\n  background: #2c5282;\r\n  transform: translateY(-1px);\r\n}\r\n\r\n.filter-controls {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 1.5rem;\r\n}\r\n\r\n.subject-filter {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 1rem;\r\n}\r\n\r\n.filter-label {\r\n  font-weight: 600;\r\n  color: #2d3748;\r\n  font-size: 1rem;\r\n}\r\n\r\n.subject-buttons {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  gap: 0.75rem;\r\n}\r\n\r\n.subject-btn {\r\n  padding: 0.75rem 1.25rem;\r\n  border: 2px solid #e2e8f0;\r\n  background: white;\r\n  color: #4a5568;\r\n  border-radius: 8px;\r\n  cursor: pointer;\r\n  transition: all 0.2s ease;\r\n  font-weight: 500;\r\n  font-size: 0.875rem;\r\n}\r\n\r\n.subject-btn:hover {\r\n  border-color: #3182ce;\r\n  color: #2b6cb0;\r\n  background: #ebf8ff;\r\n}\r\n\r\n.subject-btn.active {\r\n  background: #3182ce;\r\n  color: white;\r\n  border-color: #3182ce;\r\n}\r\n\r\n/* Class Selection Modal */\r\n.class-selection-overlay {\r\n  position: fixed;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  background: rgba(0, 0, 0, 0.5);\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  z-index: 1000;\r\n}\r\n\r\n.class-selection-modal {\r\n  background: white;\r\n  border-radius: 12px;\r\n  box-shadow: 0 20px 25px rgba(0, 0, 0, 0.1), 0 10px 10px rgba(0, 0, 0, 0.04);\r\n  max-width: 500px;\r\n  width: 90%;\r\n  max-height: 80vh;\r\n  overflow: hidden;\r\n}\r\n\r\n.modal-header {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: space-between;\r\n  padding: 1.5rem 2rem;\r\n  background: #f8fafb;\r\n  border-bottom: 1px solid #e2e8f0;\r\n}\r\n\r\n.modal-header h3 {\r\n  font-size: 1.25rem;\r\n  font-weight: 600;\r\n  color: #2d3748;\r\n  margin: 0;\r\n}\r\n\r\n.close-btn {\r\n  background: #fed7d7;\r\n  color: #c53030;\r\n  border: none;\r\n  padding: 0.5rem;\r\n  border-radius: 6px;\r\n  cursor: pointer;\r\n  transition: all 0.2s ease;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.close-btn:hover {\r\n  background: #fbb6ce;\r\n  transform: translateY(-1px);\r\n}\r\n\r\n.class-options {\r\n  padding: 1.5rem 2rem;\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 0.75rem;\r\n  max-height: 400px;\r\n  overflow-y: auto;\r\n}\r\n\r\n.class-option {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 1rem;\r\n  padding: 1rem 1.25rem;\r\n  border: 2px solid #e2e8f0;\r\n  background: white;\r\n  color: #4a5568;\r\n  border-radius: 8px;\r\n  cursor: pointer;\r\n  transition: all 0.2s ease;\r\n  font-weight: 500;\r\n  text-align: left;\r\n}\r\n\r\n.class-option:hover {\r\n  border-color: #3182ce;\r\n  color: #2b6cb0;\r\n  background: #ebf8ff;\r\n}\r\n\r\n.class-option.active {\r\n  background: #3182ce;\r\n  color: white;\r\n  border-color: #3182ce;\r\n}\r\n\r\n/* Professional Materials Display Section */\r\n.materials-display {\r\n  padding: 0 2rem 4rem;\r\n}\r\n\r\n.materials-container {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 1.5rem;\r\n}\r\n\r\n/* Material Section */\r\n.material-section {\r\n  background: white;\r\n  border-radius: 12px;\r\n  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05), 0 10px 15px rgba(0, 0, 0, 0.1);\r\n  border: 1px solid #e2e8f0;\r\n  overflow: hidden;\r\n}\r\n\r\n.section-header {\r\n  padding: 2rem 2rem 1.5rem;\r\n  background: #f8fafb;\r\n  border-bottom: 1px solid #e2e8f0;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: space-between;\r\n  flex-wrap: wrap;\r\n  gap: 1rem;\r\n}\r\n\r\n.section-title {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 1rem;\r\n}\r\n\r\n.section-icon {\r\n  font-size: 1.5rem;\r\n  color: #3182ce;\r\n}\r\n\r\n.section-title h2 {\r\n  font-size: 1.5rem;\r\n  font-weight: 600;\r\n  color: #2d3748;\r\n  margin: 0;\r\n}\r\n\r\n.section-count {\r\n  background: #3182ce;\r\n  color: white;\r\n  padding: 0.25rem 0.75rem;\r\n  border-radius: 12px;\r\n  font-size: 0.875rem;\r\n  font-weight: 500;\r\n  min-width: 2rem;\r\n  text-align: center;\r\n}\r\n\r\n.section-controls {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 1rem;\r\n}\r\n\r\n.section-controls .search-container {\r\n  position: relative;\r\n  width: 250px;\r\n}\r\n\r\n.section-controls .sort-container {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.section-controls .sort-select {\r\n  padding: 0.5rem 0.75rem;\r\n  border: 2px solid #e2e8f0;\r\n  border-radius: 6px;\r\n  background: white;\r\n  color: #4a5568;\r\n  font-size: 0.875rem;\r\n  cursor: pointer;\r\n  transition: all 0.2s ease;\r\n  min-width: 140px;\r\n}\r\n\r\n.section-controls .sort-select:focus {\r\n  outline: none;\r\n  border-color: #3182ce;\r\n  box-shadow: 0 0 0 3px rgba(49, 130, 206, 0.1);\r\n}\r\n\r\n/* Content Controls */\r\n.content-controls {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 2rem;\r\n  margin-bottom: 2rem;\r\n  padding: 1.5rem;\r\n  background: #f8fafb;\r\n  border-radius: 8px;\r\n  border: 1px solid #e2e8f0;\r\n}\r\n\r\n.search-container {\r\n  position: relative;\r\n  flex: 1;\r\n  max-width: 400px;\r\n}\r\n\r\n.sort-container {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 0.75rem;\r\n}\r\n\r\n.sort-label {\r\n  font-weight: 600;\r\n  color: #2d3748;\r\n  font-size: 0.875rem;\r\n  white-space: nowrap;\r\n}\r\n\r\n.sort-select {\r\n  padding: 0.625rem 0.75rem;\r\n  border: 2px solid #e2e8f0;\r\n  border-radius: 6px;\r\n  background: white;\r\n  color: #4a5568;\r\n  font-size: 0.875rem;\r\n  cursor: pointer;\r\n  transition: all 0.2s ease;\r\n  min-width: 140px;\r\n}\r\n\r\n.sort-select:focus {\r\n  outline: none;\r\n  border-color: #3182ce;\r\n  box-shadow: 0 0 0 3px rgba(49, 130, 206, 0.1);\r\n}\r\n\r\n/* Materials List */\r\n.materials-list {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 1rem;\r\n}\r\n\r\n.material-item {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: space-between;\r\n  padding: 1.5rem;\r\n  background: #f8fafb;\r\n  border: 1px solid #e2e8f0;\r\n  border-radius: 8px;\r\n  transition: all 0.2s ease;\r\n}\r\n\r\n.material-item:hover {\r\n  border-color: #3182ce;\r\n  background: #ebf8ff;\r\n  transform: translateY(-1px);\r\n  box-shadow: 0 4px 12px rgba(49, 130, 206, 0.1);\r\n}\r\n\r\n.item-info {\r\n  flex: 1;\r\n}\r\n\r\n.item-header {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 1rem;\r\n  margin-bottom: 0.5rem;\r\n}\r\n\r\n.item-title {\r\n  font-size: 1.125rem;\r\n  font-weight: 600;\r\n  color: #2d3748;\r\n  margin: 0;\r\n  line-height: 1.4;\r\n}\r\n\r\n.item-year {\r\n  background: #3182ce;\r\n  color: white;\r\n  padding: 0.25rem 0.75rem;\r\n  border-radius: 12px;\r\n  font-size: 0.75rem;\r\n  font-weight: 500;\r\n  white-space: nowrap;\r\n}\r\n\r\n.item-meta {\r\n  display: flex;\r\n  gap: 1rem;\r\n  align-items: center;\r\n}\r\n\r\n.file-type {\r\n  background: #edf2f7;\r\n  color: #4a5568;\r\n  padding: 0.25rem 0.5rem;\r\n  border-radius: 4px;\r\n  font-size: 0.75rem;\r\n  font-weight: 500;\r\n  text-transform: uppercase;\r\n  letter-spacing: 0.5px;\r\n}\r\n\r\n.item-subject {\r\n  color: #718096;\r\n  font-size: 0.875rem;\r\n  font-weight: 400;\r\n}\r\n\r\n.item-actions {\r\n  display: flex;\r\n  gap: 0.75rem;\r\n  align-items: center;\r\n}\r\n\r\n.action-btn {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 0.5rem;\r\n  padding: 0.625rem 1rem;\r\n  border: none;\r\n  border-radius: 6px;\r\n  font-weight: 500;\r\n  cursor: pointer;\r\n  transition: all 0.2s ease;\r\n  text-decoration: none;\r\n  font-size: 0.875rem;\r\n  white-space: nowrap;\r\n}\r\n\r\n.view-btn, .play-btn {\r\n  background: #3182ce;\r\n  color: white;\r\n}\r\n\r\n.view-btn:hover, .play-btn:hover {\r\n  background: #2c5282;\r\n  transform: translateY(-1px);\r\n}\r\n\r\n.download-btn {\r\n  background: #f7fafc;\r\n  color: #4a5568;\r\n  border: 1px solid #e2e8f0;\r\n}\r\n\r\n.download-btn:hover {\r\n  background: #edf2f7;\r\n  border-color: #cbd5e0;\r\n  transform: translateY(-1px);\r\n}\r\n\r\n.close-btn {\r\n  background: #fed7d7;\r\n  color: #c53030;\r\n  border: 1px solid #feb2b2;\r\n}\r\n\r\n.close-btn:hover {\r\n  background: #fbb6ce;\r\n  border-color: #f687b3;\r\n}\r\n\r\n.unavailable-text {\r\n  color: #a0aec0;\r\n  font-style: italic;\r\n  font-size: 0.875rem;\r\n}\r\n\r\n/* Video Items */\r\n.video-item {\r\n  flex-direction: column;\r\n  align-items: stretch;\r\n}\r\n\r\n.video-item .item-info {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: space-between;\r\n  margin-bottom: 0;\r\n}\r\n\r\n.video-player {\r\n  margin-top: 1.5rem;\r\n  padding-top: 1.5rem;\r\n  border-top: 1px solid #e2e8f0;\r\n}\r\n\r\n.video-player iframe {\r\n  border-radius: 8px;\r\n  margin-bottom: 1rem;\r\n}\r\n\r\n/* Empty States */\r\n.empty-state {\r\n  text-align: center;\r\n  padding: 4rem 2rem;\r\n  color: #718096;\r\n}\r\n\r\n.empty-icon {\r\n  font-size: 4rem;\r\n  color: #cbd5e0;\r\n  margin-bottom: 1.5rem;\r\n}\r\n\r\n.empty-state h3 {\r\n  font-size: 1.5rem;\r\n  font-weight: 600;\r\n  margin: 0 0 1rem 0;\r\n  color: #2d3748;\r\n}\r\n\r\n.empty-state p {\r\n  font-size: 1rem;\r\n  margin: 0;\r\n  color: #718096;\r\n  line-height: 1.5;\r\n}\r\n\r\n/* No Materials State */\r\n.no-materials-state {\r\n  text-align: center;\r\n  padding: 6rem 2rem;\r\n  color: #718096;\r\n  background: white;\r\n  border-radius: 12px;\r\n  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05), 0 10px 15px rgba(0, 0, 0, 0.1);\r\n  border: 1px solid #e2e8f0;\r\n}\r\n\r\n.empty-illustration {\r\n  margin-bottom: 2rem;\r\n}\r\n\r\n.no-materials-state .empty-icon {\r\n  font-size: 5rem;\r\n  color: #cbd5e0;\r\n  margin-bottom: 1.5rem;\r\n}\r\n\r\n.no-materials-state h3 {\r\n  font-size: 1.75rem;\r\n  font-weight: 600;\r\n  margin: 0 0 1rem 0;\r\n  color: #2d3748;\r\n}\r\n\r\n.no-materials-state p {\r\n  font-size: 1.125rem;\r\n  margin: 0 0 0.5rem 0;\r\n  color: #718096;\r\n  line-height: 1.6;\r\n}\r\n\r\n.suggestion {\r\n  font-style: italic;\r\n  color: #a0aec0 !important;\r\n}\r\n\r\n.materials-section {\r\n  background: white;\r\n  border-radius: 12px;\r\n  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05), 0 10px 15px rgba(0, 0, 0, 0.1);\r\n  margin-bottom: 2rem;\r\n  overflow: hidden;\r\n  border: 1px solid #e2e8f0;\r\n}\r\n\r\n.materials-header {\r\n  background: linear-gradient(135deg, #2b6cb0 0%, #3182ce 100%);\r\n  color: white;\r\n  padding: 1.5rem 2rem;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: space-between;\r\n}\r\n\r\n.header-left {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 1rem;\r\n}\r\n\r\n.material-icon {\r\n  font-size: 1.75rem;\r\n  color: #90cdf4;\r\n}\r\n\r\n.header-text h2 {\r\n  font-size: 1.5rem;\r\n  font-weight: 600;\r\n  margin: 0 0 0.25rem 0;\r\n  color: white;\r\n}\r\n\r\n.header-text p {\r\n  font-size: 0.9rem;\r\n  margin: 0;\r\n  color: #bee3f8;\r\n  font-weight: 400;\r\n}\r\n\r\n.results-count {\r\n  background: rgba(255, 255, 255, 0.15);\r\n  padding: 0.5rem 1rem;\r\n  border-radius: 20px;\r\n  font-size: 0.875rem;\r\n  font-weight: 500;\r\n}\r\n\r\n/* Materials Grid */\r\n.materials-grid {\r\n  display: grid;\r\n  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));\r\n  gap: 1.5rem;\r\n  padding: 2rem;\r\n}\r\n\r\n.material-card {\r\n  background: #f7fafc;\r\n  border-radius: 12px;\r\n  border: 1px solid #e2e8f0;\r\n  transition: all 0.2s ease;\r\n  overflow: hidden;\r\n  display: flex;\r\n  flex-direction: column;\r\n}\r\n\r\n.material-card:hover {\r\n  border-color: #3182ce;\r\n  transform: translateY(-2px);\r\n  box-shadow: 0 8px 25px rgba(49, 130, 206, 0.15);\r\n}\r\n\r\n.card-content {\r\n  padding: 1.5rem;\r\n  flex-grow: 1;\r\n}\r\n\r\n.card-header {\r\n  margin-bottom: 1rem;\r\n  display: flex;\r\n  align-items: flex-start;\r\n  justify-content: space-between;\r\n  gap: 1rem;\r\n}\r\n\r\n.material-title {\r\n  font-size: 1.125rem;\r\n  font-weight: 600;\r\n  color: #2d3748;\r\n  margin: 0;\r\n  line-height: 1.4;\r\n  flex-grow: 1;\r\n}\r\n\r\n.material-year {\r\n  background: #3182ce;\r\n  color: white;\r\n  padding: 0.25rem 0.75rem;\r\n  border-radius: 12px;\r\n  font-size: 0.75rem;\r\n  font-weight: 500;\r\n  white-space: nowrap;\r\n}\r\n\r\n.material-description {\r\n  display: flex;\r\n  gap: 1rem;\r\n  margin-bottom: 1rem;\r\n}\r\n\r\n.file-type {\r\n  background: #edf2f7;\r\n  color: #4a5568;\r\n  padding: 0.25rem 0.5rem;\r\n  border-radius: 6px;\r\n  font-size: 0.75rem;\r\n  font-weight: 500;\r\n  text-transform: uppercase;\r\n  letter-spacing: 0.5px;\r\n}\r\n\r\n.material-topic {\r\n  color: #718096;\r\n  font-size: 0.875rem;\r\n  font-weight: 400;\r\n}\r\n\r\n.material-thumbnail {\r\n  margin-bottom: 1rem;\r\n  border-radius: 8px;\r\n  overflow: hidden;\r\n  background: #edf2f7;\r\n}\r\n\r\n.material-thumbnail img {\r\n  width: 100%;\r\n  height: 160px;\r\n  object-fit: cover;\r\n  transition: transform 0.2s ease;\r\n}\r\n\r\n.material-thumbnail:hover img {\r\n  transform: scale(1.02);\r\n}\r\n\r\n.card-actions {\r\n  padding: 1rem 1.5rem;\r\n  background: white;\r\n  border-top: 1px solid #e2e8f0;\r\n  display: flex;\r\n  gap: 0.75rem;\r\n}\r\n\r\n.action-btn {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 0.5rem;\r\n  padding: 0.625rem 1.25rem;\r\n  border: none;\r\n  border-radius: 6px;\r\n  font-weight: 500;\r\n  cursor: pointer;\r\n  transition: all 0.2s ease;\r\n  text-decoration: none;\r\n  font-size: 0.875rem;\r\n  flex: 1;\r\n  justify-content: center;\r\n}\r\n\r\n.open-btn {\r\n  background: #3182ce;\r\n  color: white;\r\n}\r\n\r\n.open-btn:hover {\r\n  background: #2c5282;\r\n  transform: translateY(-1px);\r\n}\r\n\r\n.download-btn {\r\n  background: #f7fafc;\r\n  color: #4a5568;\r\n  border: 1px solid #e2e8f0;\r\n}\r\n\r\n.download-btn:hover {\r\n  background: #edf2f7;\r\n  border-color: #cbd5e0;\r\n  transform: translateY(-1px);\r\n}\r\n\r\n.unavailable-notice {\r\n  color: #a0aec0;\r\n  font-style: italic;\r\n  padding: 0.75rem;\r\n  text-align: center;\r\n  background: #f7fafc;\r\n  border-radius: 6px;\r\n  font-size: 0.875rem;\r\n}\r\n\r\n/* Video Specific Styles */\r\n.video-grid {\r\n  display: grid;\r\n  grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));\r\n  gap: 2rem;\r\n  padding: 2rem;\r\n}\r\n\r\n.video-card {\r\n  background: #f8f9ff;\r\n  border-radius: 15px;\r\n  overflow: hidden;\r\n  border: 2px solid transparent;\r\n  transition: all 0.3s ease;\r\n  position: relative;\r\n}\r\n\r\n.video-card::before {\r\n  content: '';\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  height: 4px;\r\n  background: linear-gradient(90deg, #ff6b6b, #ee5a24);\r\n}\r\n\r\n.video-card:hover {\r\n  border-color: #ff6b6b;\r\n  transform: translateY(-5px);\r\n  box-shadow: 0 15px 35px rgba(255, 107, 107, 0.2);\r\n}\r\n\r\n.video-header {\r\n  padding: 1.5rem;\r\n  background: white;\r\n}\r\n\r\n.video-title {\r\n  font-size: 1.2rem;\r\n  font-weight: 600;\r\n  color: #333;\r\n  margin: 0;\r\n  line-height: 1.4;\r\n}\r\n\r\n.video-preview {\r\n  position: relative;\r\n}\r\n\r\n.video-thumbnail-container {\r\n  position: relative;\r\n  overflow: hidden;\r\n}\r\n\r\n.video-thumbnail {\r\n  width: 100%;\r\n  height: 250px;\r\n  object-fit: cover;\r\n  cursor: pointer;\r\n  transition: transform 0.3s ease;\r\n}\r\n\r\n.video-thumbnail:hover {\r\n  transform: scale(1.05);\r\n}\r\n\r\n.play-overlay {\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  background: rgba(0, 0, 0, 0.5);\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  justify-content: center;\r\n  cursor: pointer;\r\n  opacity: 0;\r\n  transition: opacity 0.3s ease;\r\n}\r\n\r\n.video-thumbnail-container:hover .play-overlay {\r\n  opacity: 1;\r\n}\r\n\r\n.play-icon {\r\n  font-size: 4rem;\r\n  color: white;\r\n  margin-bottom: 0.5rem;\r\n}\r\n\r\n.play-text {\r\n  color: white;\r\n  font-weight: 600;\r\n  font-size: 1.1rem;\r\n}\r\n\r\n.video-btn {\r\n  margin: 1.5rem;\r\n  width: calc(100% - 3rem);\r\n}\r\n\r\n.video-player {\r\n  padding: 1.5rem;\r\n  background: white;\r\n}\r\n\r\n.video-element {\r\n  width: 100%;\r\n  border-radius: 10px;\r\n  margin-bottom: 1rem;\r\n}\r\n\r\n.close-btn {\r\n  width: 100%;\r\n}\r\n\r\n/* Video Info Text */\r\n.video-info-text {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  padding: 0.5rem;\r\n}\r\n\r\n.video-duration {\r\n  color: #666;\r\n  font-size: 0.9rem;\r\n  font-style: italic;\r\n}\r\n\r\n/* Academic Empty States */\r\n.empty-state-academic {\r\n  text-align: center;\r\n  padding: 4rem 2rem;\r\n  color: #718096;\r\n}\r\n\r\n.empty-illustration {\r\n  margin-bottom: 1.5rem;\r\n}\r\n\r\n.empty-icon {\r\n  font-size: 4rem;\r\n  color: #cbd5e0;\r\n  margin-bottom: 1rem;\r\n}\r\n\r\n.empty-state-academic h3 {\r\n  font-size: 1.5rem;\r\n  font-weight: 600;\r\n  margin: 0 0 1rem 0;\r\n  color: #2d3748;\r\n}\r\n\r\n.empty-state-academic p {\r\n  font-size: 1rem;\r\n  margin: 0 0 0.5rem 0;\r\n  color: #718096;\r\n  line-height: 1.5;\r\n}\r\n\r\n.suggestion {\r\n  font-weight: 500;\r\n  color: #4a5568;\r\n}\r\n\r\n.error-state-academic {\r\n  text-align: center;\r\n  padding: 3rem 2rem;\r\n  background: #fef5e7;\r\n  border-radius: 12px;\r\n  margin: 2rem;\r\n  border: 1px solid #f6e05e;\r\n}\r\n\r\n.error-state-academic h3 {\r\n  color: #d69e2e;\r\n  font-size: 1.25rem;\r\n  margin: 0 0 1rem 0;\r\n  font-weight: 600;\r\n}\r\n\r\n.error-state-academic p {\r\n  color: #b7791f;\r\n  margin: 0;\r\n  font-weight: 400;\r\n}\r\n\r\n/* Responsive Design */\r\n@media (max-width: 768px) {\r\n  .study-header {\r\n    padding: 2rem 1rem;\r\n  }\r\n\r\n  .header-title h1 {\r\n    font-size: 2rem;\r\n  }\r\n\r\n  .header-icon {\r\n    font-size: 2rem;\r\n  }\r\n\r\n  .level-badge {\r\n    position: static;\r\n    margin-top: 1rem;\r\n    align-self: flex-start;\r\n  }\r\n\r\n  .filter-section {\r\n    margin: -1rem 1rem 1rem;\r\n    padding: 1.5rem;\r\n  }\r\n\r\n  .filter-grid {\r\n    grid-template-columns: 1fr;\r\n    gap: 1rem;\r\n  }\r\n\r\n  .content-display {\r\n    padding: 0 1rem 2rem;\r\n  }\r\n\r\n  .content-grid {\r\n    grid-template-columns: 1fr;\r\n    gap: 1rem;\r\n    padding: 1rem;\r\n  }\r\n\r\n  .video-grid {\r\n    grid-template-columns: 1fr;\r\n    gap: 1rem;\r\n    padding: 1rem;\r\n  }\r\n\r\n  .section-header {\r\n    padding: 1.5rem;\r\n  }\r\n\r\n  .section-header h2 {\r\n    font-size: 1.4rem;\r\n  }\r\n\r\n  .section-icon {\r\n    font-size: 1.5rem;\r\n  }\r\n}\r\n\r\n@media (max-width: 480px) {\r\n  .header-title {\r\n    flex-direction: column;\r\n    text-align: center;\r\n    gap: 0.5rem;\r\n  }\r\n\r\n  .header-title h1 {\r\n    font-size: 1.8rem;\r\n  }\r\n\r\n  .filter-card {\r\n    padding: 1rem;\r\n  }\r\n\r\n  .content-card {\r\n    padding: 1rem;\r\n  }\r\n\r\n  .card-actions {\r\n    flex-direction: column;\r\n  }\r\n\r\n  .action-btn {\r\n    justify-content: center;\r\n  }\r\n}\r\n\r\n/* Animation Classes */\r\n@keyframes fadeInUp {\r\n  from {\r\n    opacity: 0;\r\n    transform: translateY(30px);\r\n  }\r\n  to {\r\n    opacity: 1;\r\n    transform: translateY(0);\r\n  }\r\n}\r\n\r\n.content-card {\r\n  animation: fadeInUp 0.6s ease-out;\r\n}\r\n\r\n.video-card {\r\n  animation: fadeInUp 0.6s ease-out;\r\n}\r\n\r\n/* Loading States */\r\n.loading-shimmer {\r\n  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);\r\n  background-size: 200% 100%;\r\n  animation: shimmer 2s infinite;\r\n}\r\n\r\n@keyframes shimmer {\r\n  0% {\r\n    background-position: -200% 0;\r\n  }\r\n  100% {\r\n    background-position: 200% 0;\r\n  }\r\n}\r\n\r\n/* Accessibility */\r\n.action-btn:focus,\r\n.modern-select:focus,\r\n.toggle-btn:focus {\r\n  outline: 3px solid rgba(102, 126, 234, 0.5);\r\n  outline-offset: 2px;\r\n}\r\n\r\n/* Simplified Past Papers Styles */\r\n.papers-controls-simple {\r\n  background: #f8fafb;\r\n  border-bottom: 1px solid #e2e8f0;\r\n  padding: 1.5rem 2rem;\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 1.5rem;\r\n}\r\n\r\n.search-container {\r\n  position: relative;\r\n  width: 100%;\r\n  max-width: 500px;\r\n}\r\n\r\n.filter-controls {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 1rem;\r\n}\r\n\r\n.search-icon {\r\n  position: absolute;\r\n  left: 1rem;\r\n  top: 50%;\r\n  transform: translateY(-50%);\r\n  color: #718096;\r\n  font-size: 0.875rem;\r\n}\r\n\r\n.search-input {\r\n  width: 100%;\r\n  padding: 0.75rem 1rem 0.75rem 2.5rem;\r\n  border: 2px solid #e2e8f0;\r\n  border-radius: 8px;\r\n  font-size: 0.875rem;\r\n  background: white;\r\n  transition: all 0.2s ease;\r\n}\r\n\r\n.search-input:focus {\r\n  outline: none;\r\n  border-color: #3182ce;\r\n  box-shadow: 0 0 0 3px rgba(49, 130, 206, 0.1);\r\n}\r\n\r\n\r\n\r\n.filter-group {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 0.75rem;\r\n}\r\n\r\n.filter-label {\r\n  font-weight: 600;\r\n  color: #2d3748;\r\n  font-size: 0.875rem;\r\n  white-space: nowrap;\r\n}\r\n\r\n.filter-select {\r\n  padding: 0.625rem 0.75rem;\r\n  border: 2px solid #e2e8f0;\r\n  border-radius: 6px;\r\n  background: white;\r\n  color: #4a5568;\r\n  font-size: 0.875rem;\r\n  cursor: pointer;\r\n  transition: all 0.2s ease;\r\n  min-width: 140px;\r\n}\r\n\r\n.filter-select:focus {\r\n  outline: none;\r\n  border-color: #3182ce;\r\n  box-shadow: 0 0 0 3px rgba(49, 130, 206, 0.1);\r\n}\r\n\r\n\r\n\r\n/* Enhanced Paper Cards */\r\n.paper-card-enhanced {\r\n  border: 2px solid #e2e8f0;\r\n  transition: all 0.3s ease;\r\n  position: relative;\r\n  overflow: hidden;\r\n}\r\n\r\n.paper-card-enhanced::before {\r\n  content: '';\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  height: 4px;\r\n  background: linear-gradient(90deg, #3182ce, #2b6cb0);\r\n  opacity: 0;\r\n  transition: opacity 0.3s ease;\r\n}\r\n\r\n.paper-card-enhanced:hover::before {\r\n  opacity: 1;\r\n}\r\n\r\n.paper-card-enhanced:hover {\r\n  border-color: #3182ce;\r\n  transform: translateY(-4px);\r\n  box-shadow: 0 12px 28px rgba(49, 130, 206, 0.15);\r\n}\r\n\r\n/* Responsive Design for Professional Layout */\r\n@media (max-width: 768px) {\r\n  .filter-section {\r\n    margin: 0 1rem 2rem;\r\n  }\r\n\r\n  .filter-container {\r\n    padding: 1.5rem;\r\n  }\r\n\r\n  .filter-header {\r\n    flex-direction: column;\r\n    align-items: stretch;\r\n    gap: 1.5rem;\r\n  }\r\n\r\n  .filter-title {\r\n    text-align: center;\r\n  }\r\n\r\n  .class-info {\r\n    justify-content: center;\r\n  }\r\n\r\n  .subject-buttons {\r\n    justify-content: center;\r\n  }\r\n\r\n  .class-selection-modal {\r\n    width: 95%;\r\n  }\r\n\r\n  .modal-header {\r\n    padding: 1rem 1.5rem;\r\n  }\r\n\r\n  .class-options {\r\n    padding: 1rem 1.5rem;\r\n  }\r\n\r\n  .materials-display {\r\n    padding: 0 1rem 2rem;\r\n  }\r\n\r\n  .materials-container {\r\n    gap: 1rem;\r\n  }\r\n\r\n  .section-header {\r\n    flex-direction: column;\r\n    align-items: stretch;\r\n    gap: 1.5rem;\r\n    padding: 1.5rem;\r\n  }\r\n\r\n  .section-title {\r\n    justify-content: center;\r\n  }\r\n\r\n  .section-controls {\r\n    flex-direction: column;\r\n    gap: 1rem;\r\n  }\r\n\r\n  .section-controls .search-container {\r\n    width: 100%;\r\n  }\r\n\r\n  .section-controls .sort-container {\r\n    justify-content: center;\r\n  }\r\n\r\n  .material-item {\r\n    flex-direction: column;\r\n    align-items: stretch;\r\n    gap: 1rem;\r\n  }\r\n\r\n  .item-header {\r\n    flex-direction: column;\r\n    align-items: flex-start;\r\n    gap: 0.5rem;\r\n  }\r\n\r\n  .item-actions {\r\n    justify-content: center;\r\n  }\r\n\r\n  .no-materials-state {\r\n    padding: 4rem 1rem;\r\n  }\r\n\r\n  .no-materials-state .empty-icon {\r\n    font-size: 4rem;\r\n  }\r\n\r\n  .no-materials-state h3 {\r\n    font-size: 1.5rem;\r\n  }\r\n\r\n  .no-materials-state p {\r\n    font-size: 1rem;\r\n  }\r\n}\r\n\r\n@media (max-width: 480px) {\r\n  .search-input {\r\n    font-size: 1rem; /* Prevent zoom on iOS */\r\n  }\r\n\r\n  .filter-label, .sort-label {\r\n    font-size: 0.8rem;\r\n  }\r\n\r\n  .material-tab {\r\n    padding: 0.875rem;\r\n    font-size: 0.875rem;\r\n  }\r\n\r\n  .tab-icon {\r\n    font-size: 1rem;\r\n  }\r\n\r\n  .item-title {\r\n    font-size: 1rem;\r\n  }\r\n\r\n  .action-btn {\r\n    padding: 0.5rem 0.75rem;\r\n    font-size: 0.8rem;\r\n  }\r\n\r\n  .empty-state {\r\n    padding: 3rem 1rem;\r\n  }\r\n\r\n  .empty-icon {\r\n    font-size: 3rem;\r\n  }\r\n\r\n  .empty-state h3 {\r\n    font-size: 1.25rem;\r\n  }\r\n}\r\n\r\n/* Modern Navigation Tabs */\r\n.nav-tabs {\r\n  background: white;\r\n  border-bottom: 1px solid #e2e8f0;\r\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);\r\n}\r\n\r\n.tabs-container {\r\n  max-width: 1200px;\r\n  margin: 0 auto;\r\n  display: flex;\r\n  justify-content: center;\r\n  gap: 0;\r\n  overflow-x: auto;\r\n  scrollbar-width: none;\r\n  -ms-overflow-style: none;\r\n}\r\n\r\n.tabs-container::-webkit-scrollbar {\r\n  display: none;\r\n}\r\n\r\n.nav-tab {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 0.75rem;\r\n  padding: 1.25rem 2rem;\r\n  background: transparent;\r\n  border: none;\r\n  color: #64748b;\r\n  font-size: 1rem;\r\n  font-weight: 600;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n  border-bottom: 3px solid transparent;\r\n  white-space: nowrap;\r\n  position: relative;\r\n}\r\n\r\n.nav-tab:hover {\r\n  color: #3b82f6;\r\n  background: #f8fafc;\r\n}\r\n\r\n.nav-tab.active {\r\n  color: #3b82f6;\r\n  border-bottom-color: #3b82f6;\r\n  background: #f8fafc;\r\n}\r\n\r\n.nav-tab .tab-icon {\r\n  font-size: 1.25rem;\r\n}\r\n\r\n/* Modern Filters Section */\r\n.filters-section {\r\n  background: white;\r\n  border-bottom: 1px solid #e2e8f0;\r\n  padding: 1.5rem 0;\r\n}\r\n\r\n.filters-container {\r\n  max-width: 1200px;\r\n  margin: 0 auto;\r\n  padding: 0 2rem;\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 1.5rem;\r\n}\r\n\r\n.filter-group {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 0.75rem;\r\n}\r\n\r\n.filter-label {\r\n  font-size: 0.875rem;\r\n  font-weight: 600;\r\n  color: #374151;\r\n  text-transform: uppercase;\r\n  letter-spacing: 0.05em;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 0.5rem;\r\n  flex-wrap: wrap;\r\n}\r\n\r\n.current-class-display {\r\n  font-size: 0.875rem;\r\n  font-weight: 600;\r\n  color: white;\r\n  background: #3b82f6;\r\n  padding: 0.375rem 0.75rem;\r\n  border-radius: 50px;\r\n  border: 1px solid #3b82f6;\r\n  text-transform: none;\r\n  letter-spacing: normal;\r\n  margin-left: 0.5rem;\r\n  box-shadow: 0 2px 4px rgba(59, 130, 246, 0.1);\r\n}\r\n\r\n/* Class Selector */\r\n.class-selector {\r\n  position: relative;\r\n}\r\n\r\n.class-display-btn {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: space-between;\r\n  padding: 0.75rem 1rem;\r\n  background: #f8fafc;\r\n  border: 1px solid #d1d5db;\r\n  border-radius: 8px;\r\n  color: #374151;\r\n  font-weight: 500;\r\n  cursor: pointer;\r\n  transition: all 0.2s ease;\r\n  min-width: 200px;\r\n}\r\n\r\n.class-display-btn:hover {\r\n  border-color: #3b82f6;\r\n  background: #f1f5f9;\r\n}\r\n\r\n.chevron {\r\n  transition: transform 0.2s ease;\r\n}\r\n\r\n.chevron.open {\r\n  transform: rotate(180deg);\r\n}\r\n\r\n.class-dropdown {\r\n  position: absolute;\r\n  top: 100%;\r\n  left: 0;\r\n  right: 0;\r\n  background: white;\r\n  border: 1px solid #d1d5db;\r\n  border-radius: 8px;\r\n  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);\r\n  z-index: 10;\r\n  max-height: 200px;\r\n  overflow-y: auto;\r\n}\r\n\r\n.class-option {\r\n  display: block;\r\n  width: 100%;\r\n  padding: 0.75rem 1rem;\r\n  background: transparent;\r\n  border: none;\r\n  text-align: left;\r\n  color: #374151;\r\n  cursor: pointer;\r\n  transition: background-color 0.2s ease;\r\n}\r\n\r\n.class-option:hover {\r\n  background: #f8fafc;\r\n}\r\n\r\n.class-option.active {\r\n  background: #3b82f6;\r\n  color: white;\r\n}\r\n\r\n.class-option.user-current {\r\n  background: #f0f9ff;\r\n  border: 2px solid #0ea5e9;\r\n  color: #0c4a6e;\r\n  font-weight: 600;\r\n}\r\n\r\n.class-option.user-current:hover {\r\n  background: #e0f2fe;\r\n  border-color: #0284c7;\r\n}\r\n\r\n.class-option.user-current.active {\r\n  background: #0ea5e9;\r\n  color: white;\r\n  border-color: #0ea5e9;\r\n}\r\n\r\n.current-badge {\r\n  font-size: 0.625rem;\r\n  font-weight: 600;\r\n  color: #0ea5e9;\r\n  background: #f0f9ff;\r\n  padding: 0.125rem 0.375rem;\r\n  border-radius: 8px;\r\n  margin-left: 0.5rem;\r\n  border: 1px solid #bae6fd;\r\n  text-transform: uppercase;\r\n  letter-spacing: 0.025em;\r\n}\r\n\r\n.class-option.active .current-badge,\r\n.class-option.user-current.active .current-badge {\r\n  background: rgba(255, 255, 255, 0.2);\r\n  color: white;\r\n  border-color: rgba(255, 255, 255, 0.3);\r\n}\r\n\r\n.class-display-btn .current-badge {\r\n  color: #059669;\r\n  background: #d1fae5;\r\n  border-color: #a7f3d0;\r\n}\r\n\r\n/* Subject Pills */\r\n.subject-pills {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  gap: 0.75rem;\r\n}\r\n\r\n.subject-pill {\r\n  padding: 0.5rem 1rem;\r\n  background: #f1f5f9;\r\n  border: 1px solid #d1d5db;\r\n  border-radius: 50px;\r\n  color: #374151;\r\n  font-size: 0.875rem;\r\n  font-weight: 500;\r\n  cursor: pointer;\r\n  transition: all 0.2s ease;\r\n}\r\n\r\n.subject-pill:hover {\r\n  border-color: #3b82f6;\r\n  color: #3b82f6;\r\n}\r\n\r\n.subject-pill.active {\r\n  background: #3b82f6;\r\n  border-color: #3b82f6;\r\n  color: white;\r\n}\r\n\r\n/* Search and Sort Container */\r\n.search-sort-container {\r\n  display: flex;\r\n  gap: 2rem;\r\n  align-items: flex-end;\r\n  margin-top: 2rem; /* Add more space above search bar */\r\n  padding-top: 1rem; /* Additional padding for better separation */\r\n  border-top: 1px solid #e2e8f0; /* Subtle separator line */\r\n}\r\n\r\n/* Search Section */\r\n.search-section {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 0.75rem;\r\n  flex: 1; /* Take up remaining space */\r\n}\r\n\r\n/* Sort Section */\r\n.sort-section {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 0.75rem;\r\n  flex-shrink: 0; /* Don't shrink */\r\n}\r\n\r\n.search-box {\r\n  position: relative;\r\n  width: 100%;\r\n  max-width: 400px; /* Reasonable maximum width for side-by-side layout */\r\n}\r\n\r\n.search-icon {\r\n  position: absolute;\r\n  left: 0.75rem;\r\n  top: 50%;\r\n  transform: translateY(-50%);\r\n  color: #9ca3af;\r\n  font-size: 0.875rem;\r\n}\r\n\r\n.search-input {\r\n  width: 100%;\r\n  padding: 0.75rem 0.75rem 0.75rem 2.5rem;\r\n  border: 1px solid #d1d5db;\r\n  border-radius: 8px;\r\n  font-size: 0.875rem;\r\n  background: #f8fafc;\r\n  transition: all 0.2s ease;\r\n}\r\n\r\n.search-input:focus {\r\n  outline: none;\r\n  border-color: #3b82f6;\r\n  background: white;\r\n  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);\r\n}\r\n\r\n.sort-selector {\r\n  width: 200px; /* Fixed width for consistent layout */\r\n  min-width: 200px; /* Ensure minimum width */\r\n}\r\n\r\n.sort-select {\r\n  width: 100%;\r\n  padding: 0.75rem;\r\n  border: 1px solid #d1d5db;\r\n  border-radius: 8px;\r\n  background: #f8fafc;\r\n  color: #374151;\r\n  font-size: 0.875rem;\r\n  cursor: pointer;\r\n  transition: all 0.2s ease;\r\n}\r\n\r\n.sort-select:focus {\r\n  outline: none;\r\n  border-color: #3b82f6;\r\n  background: white;\r\n  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);\r\n}\r\n\r\n/* Print Styles */\r\n@media print {\r\n  .study-material-modern {\r\n    background: white !important;\r\n  }\r\n\r\n  .modern-header {\r\n    background: #333 !important;\r\n    color: white !important;\r\n  }\r\n\r\n  .filters-section {\r\n    box-shadow: none !important;\r\n    border: 1px solid #ddd;\r\n  }\r\n\r\n  .material-card {\r\n    box-shadow: none !important;\r\n    border: 1px solid #ddd;\r\n    break-inside: avoid;\r\n  }\r\n\r\n  .nav-tabs,\r\n  .filters-section {\r\n    display: none !important;\r\n  }\r\n}\r\n\r\n/* Modern Materials Section */\r\n.materials-section {\r\n  max-width: 1200px;\r\n  margin: 0 auto;\r\n  padding: 2rem;\r\n}\r\n\r\n.loading-state {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  justify-content: center;\r\n  padding: 4rem 2rem;\r\n  color: #64748b;\r\n}\r\n\r\n.loading-spinner {\r\n  width: 40px;\r\n  height: 40px;\r\n  border: 3px solid #e2e8f0;\r\n  border-top: 3px solid #3b82f6;\r\n  border-radius: 50%;\r\n  animation: spin 1s linear infinite;\r\n  margin-bottom: 1rem;\r\n}\r\n\r\n@keyframes spin {\r\n  0% { transform: rotate(0deg); }\r\n  100% { transform: rotate(360deg); }\r\n}\r\n\r\n/* Materials Grid */\r\n.materials-grid {\r\n  display: grid;\r\n  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));\r\n  gap: 1.5rem;\r\n}\r\n\r\n.material-card {\r\n  background: white;\r\n  border: 1px solid #e2e8f0;\r\n  border-radius: 12px;\r\n  overflow: hidden;\r\n  transition: all 0.3s ease;\r\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);\r\n}\r\n\r\n.material-card:hover {\r\n  transform: translateY(-2px);\r\n  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);\r\n  border-color: #3b82f6;\r\n}\r\n\r\n.card-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: flex-start;\r\n  padding: 1rem 1.25rem 0.75rem;\r\n  border-bottom: 1px solid #f1f5f9;\r\n}\r\n\r\n.header-right {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: flex-end;\r\n  gap: 0.5rem;\r\n}\r\n\r\n.class-tags {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 0.25rem;\r\n  align-items: flex-end;\r\n}\r\n\r\n.class-tag {\r\n  padding: 0.25rem 0.75rem;\r\n  border-radius: 12px;\r\n  font-size: 0.7rem;\r\n  font-weight: 500;\r\n  white-space: nowrap;\r\n  text-align: center;\r\n  line-height: 1.2;\r\n}\r\n\r\n.class-tag.core-class {\r\n  background: #dbeafe;\r\n  color: #1e40af;\r\n  border: 1px solid #bfdbfe;\r\n}\r\n\r\n.class-tag.shared-class {\r\n  background: #fef3c7;\r\n  color: #d97706;\r\n  border: 1px solid #fde68a;\r\n}\r\n\r\n.material-type {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 0.5rem;\r\n}\r\n\r\n.type-icon {\r\n  font-size: 1rem;\r\n  color: #3b82f6;\r\n}\r\n\r\n.type-label {\r\n  font-size: 0.75rem;\r\n  font-weight: 600;\r\n  color: #64748b;\r\n  text-transform: uppercase;\r\n  letter-spacing: 0.05em;\r\n}\r\n\r\n.material-year {\r\n  background: #f1f5f9;\r\n  color: #64748b;\r\n  padding: 0.25rem 0.75rem;\r\n  border-radius: 50px;\r\n  font-size: 0.75rem;\r\n  font-weight: 500;\r\n}\r\n\r\n.card-content {\r\n  padding: 1.25rem;\r\n}\r\n\r\n.material-title {\r\n  font-size: 1.125rem;\r\n  font-weight: 600;\r\n  color: #1e293b;\r\n  margin: 0 0 0.75rem 0;\r\n  line-height: 1.4;\r\n}\r\n\r\n.material-meta {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  gap: 0.75rem;\r\n  margin-bottom: 1rem;\r\n}\r\n\r\n.material-subject {\r\n  background: #dbeafe;\r\n  color: #1e40af;\r\n  padding: 0.25rem 0.75rem;\r\n  border-radius: 50px;\r\n  font-size: 0.75rem;\r\n  font-weight: 500;\r\n}\r\n\r\n.material-class {\r\n  background: #f3e8ff;\r\n  color: #7c3aed;\r\n  padding: 0.25rem 0.75rem;\r\n  border-radius: 50px;\r\n  font-size: 0.75rem;\r\n  font-weight: 500;\r\n}\r\n\r\n.card-actions {\r\n  padding: 1rem 1.25rem;\r\n  background: #f8fafc;\r\n  display: flex;\r\n  gap: 0.75rem;\r\n  align-items: center;\r\n}\r\n\r\n.action-btn {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 0.5rem;\r\n  padding: 0.5rem 1rem;\r\n  border: none;\r\n  border-radius: 6px;\r\n  font-size: 0.875rem;\r\n  font-weight: 500;\r\n  cursor: pointer;\r\n  transition: all 0.2s ease;\r\n}\r\n\r\n.action-btn.primary {\r\n  background: #3b82f6;\r\n  color: white;\r\n}\r\n\r\n.action-btn.primary:hover {\r\n  background: #2563eb;\r\n  transform: translateY(-1px);\r\n}\r\n\r\n.action-btn.secondary {\r\n  background: #f1f5f9;\r\n  color: #64748b;\r\n  border: 1px solid #d1d5db;\r\n}\r\n\r\n.action-btn.secondary:hover {\r\n  background: #e2e8f0;\r\n  color: #374151;\r\n}\r\n\r\n.unavailable {\r\n  color: #9ca3af;\r\n  font-size: 0.875rem;\r\n  font-style: italic;\r\n}\r\n\r\n/* Empty State */\r\n.empty-state {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  justify-content: center;\r\n  padding: 4rem 2rem;\r\n  text-align: center;\r\n  color: #64748b;\r\n}\r\n\r\n.empty-icon {\r\n  font-size: 4rem;\r\n  color: #d1d5db;\r\n  margin-bottom: 1.5rem;\r\n}\r\n\r\n.empty-state h3 {\r\n  font-size: 1.5rem;\r\n  font-weight: 600;\r\n  color: #374151;\r\n  margin: 0 0 0.75rem 0;\r\n}\r\n\r\n.empty-state p {\r\n  font-size: 1rem;\r\n  margin: 0 0 0.5rem 0;\r\n  max-width: 400px;\r\n}\r\n\r\n/* Error State */\r\n.error-state {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  justify-content: center;\r\n  padding: 4rem 2rem;\r\n  text-align: center;\r\n  color: #dc2626;\r\n}\r\n\r\n.error-icon {\r\n  font-size: 4rem;\r\n  color: #fca5a5;\r\n  margin-bottom: 1.5rem;\r\n}\r\n\r\n.error-state h3 {\r\n  font-size: 1.5rem;\r\n  font-weight: 600;\r\n  color: #dc2626;\r\n  margin: 0 0 0.75rem 0;\r\n}\r\n\r\n.error-state p {\r\n  font-size: 1rem;\r\n  margin: 0 0 1.5rem 0;\r\n  max-width: 400px;\r\n  color: #6b7280;\r\n}\r\n\r\n.retry-btn {\r\n  background: #dc2626;\r\n  color: white;\r\n  border: none;\r\n  padding: 0.75rem 1.5rem;\r\n  border-radius: 8px;\r\n  font-size: 1rem;\r\n  font-weight: 500;\r\n  cursor: pointer;\r\n  transition: all 0.2s ease;\r\n}\r\n\r\n.retry-btn:hover {\r\n  background: #b91c1c;\r\n  transform: translateY(-1px);\r\n}\r\n\r\n.suggestion {\r\n  color: #9ca3af !important;\r\n  font-size: 0.875rem !important;\r\n}\r\n\r\n/* Video Thumbnail Styles */\r\n.video-thumbnail-container {\r\n  position: relative;\r\n  overflow: hidden;\r\n  border-radius: 12px;\r\n  margin-bottom: 1rem;\r\n  cursor: pointer;\r\n}\r\n\r\n.video-thumbnail {\r\n  width: 100%;\r\n  height: 200px;\r\n  object-fit: cover;\r\n  transition: transform 0.3s ease;\r\n  display: block;\r\n}\r\n\r\n.video-thumbnail:hover {\r\n  transform: scale(1.05);\r\n}\r\n\r\n.play-overlay {\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  background: rgba(0, 0, 0, 0.6);\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  justify-content: center;\r\n  opacity: 0;\r\n  transition: opacity 0.3s ease;\r\n}\r\n\r\n.video-thumbnail-container:hover .play-overlay {\r\n  opacity: 1;\r\n}\r\n\r\n/* HTML5 Video Player Styles */\r\n.video-player {\r\n  width: 100%;\r\n  height: 420px;\r\n  border-radius: 8px;\r\n  background: #000;\r\n  outline: none;\r\n  object-fit: contain;\r\n  display: block;\r\n  position: relative;\r\n  z-index: 1;\r\n}\r\n\r\n.video-player:focus {\r\n  outline: 2px solid #007bff;\r\n  outline-offset: 2px;\r\n}\r\n\r\n/* Enhanced Video Modal Styles */\r\n.video-overlay {\r\n  position: fixed;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  background: rgba(0, 0, 0, 0.9);\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  z-index: 9999;\r\n  padding: 20px;\r\n  transition: all 0.3s ease;\r\n  overflow-y: auto;\r\n  min-height: 100vh;\r\n}\r\n\r\n.video-overlay.expanded {\r\n  padding: 0;\r\n  background: rgba(0, 0, 0, 0.95);\r\n}\r\n\r\n.video-modal {\r\n  background: white;\r\n  border-radius: 12px;\r\n  width: 100%;\r\n  max-width: 800px;\r\n  min-height: 500px;\r\n  max-height: 90vh;\r\n  overflow-y: auto;\r\n  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.4);\r\n  transition: all 0.3s ease;\r\n  position: relative;\r\n  margin: auto;\r\n  display: flex;\r\n  flex-direction: column;\r\n\r\n}\r\n\r\n.video-modal.expanded {\r\n  width: 100vw;\r\n  height: 100vh;\r\n  max-width: 100vw;\r\n  max-height: 100vh;\r\n  border-radius: 0;\r\n}\r\n\r\n.video-header {\r\n  padding: 20px 25px;\r\n  border-bottom: 1px solid #eee;\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);\r\n  position: relative;\r\n}\r\n\r\n.video-info h3 {\r\n  margin: 0 0 5px 0;\r\n  color: #333;\r\n  font-size: 1.3rem;\r\n  font-weight: 600;\r\n  line-height: 1.3;\r\n}\r\n\r\n.video-meta {\r\n  display: flex;\r\n  gap: 15px;\r\n  margin-top: 5px;\r\n}\r\n\r\n.video-subject,\r\n.video-class {\r\n  font-size: 0.85rem;\r\n  padding: 4px 8px;\r\n  border-radius: 12px;\r\n  font-weight: 500;\r\n}\r\n\r\n.video-subject {\r\n  background: #e3f2fd;\r\n  color: #1976d2;\r\n}\r\n\r\n.video-class {\r\n  background: #f3e5f5;\r\n  color: #7b1fa2;\r\n}\r\n\r\n.video-format {\r\n  font-size: 0.75rem;\r\n  padding: 3px 6px;\r\n  border-radius: 10px;\r\n  font-weight: 600;\r\n}\r\n\r\n.video-format.supported {\r\n  background: #e8f5e8;\r\n  color: #2e7d32;\r\n}\r\n\r\n.video-format.unsupported {\r\n  background: #ffebee;\r\n  color: #c62828;\r\n}\r\n\r\n.video-format.processing {\r\n  background: #e3f2fd;\r\n  color: #1976d2;\r\n  animation: pulse 2s infinite;\r\n}\r\n\r\n@keyframes pulse {\r\n  0%, 100% { opacity: 1; }\r\n  50% { opacity: 0.7; }\r\n}\r\n\r\n.video-controls {\r\n  display: flex;\r\n  gap: 10px;\r\n  align-items: center;\r\n}\r\n\r\n.quality-selector {\r\n  padding: 6px 10px;\r\n  border: 1px solid #ddd;\r\n  border-radius: 4px;\r\n  background: white;\r\n  font-size: 0.85rem;\r\n  color: #666;\r\n  cursor: pointer;\r\n  transition: all 0.2s ease;\r\n}\r\n\r\n.quality-selector:hover {\r\n  border-color: #007bff;\r\n}\r\n\r\n.quality-selector:focus {\r\n  outline: none;\r\n  border-color: #007bff;\r\n  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);\r\n}\r\n\r\n.control-btn {\r\n  background: none;\r\n  border: none;\r\n  font-size: 1.2rem;\r\n  color: #666;\r\n  cursor: pointer;\r\n  padding: 8px;\r\n  border-radius: 6px;\r\n  transition: all 0.2s ease;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.control-btn:hover {\r\n  background: rgba(0, 0, 0, 0.1);\r\n  color: #333;\r\n  transform: scale(1.05);\r\n}\r\n\r\n.expand-btn:hover {\r\n  color: #28a745;\r\n}\r\n\r\n.collapse-btn:hover {\r\n  color: #ffc107;\r\n}\r\n\r\n.close-btn:hover {\r\n  color: #dc3545;\r\n}\r\n\r\n.video-container {\r\n  background: #000;\r\n  position: relative;\r\n  padding: 15px;\r\n  border-radius: 8px;\r\n  overflow: visible;\r\n  min-height: 450px;\r\n}\r\n\r\n.video-modal.expanded .video-container {\r\n  padding: 0;\r\n  min-height: calc(100vh - 80px);\r\n  border-radius: 0;\r\n}\r\n\r\n.video-iframe {\r\n  width: 100%;\r\n  height: 420px;\r\n  border: none;\r\n  border-radius: 8px;\r\n}\r\n\r\n.video-modal.expanded .video-iframe {\r\n  height: calc(100vh - 80px);\r\n  border-radius: 0;\r\n}\r\n\r\n.video-modal.expanded .video-player {\r\n  height: calc(100vh - 80px) !important;\r\n  border-radius: 0;\r\n}\r\n\r\n/* Video Loading State */\r\n.video-loading {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  justify-content: center;\r\n  height: 100%;\r\n  background: #000;\r\n  color: white;\r\n  border-radius: 8px;\r\n}\r\n\r\n.video-modal.expanded .video-loading {\r\n  height: 100%;\r\n  border-radius: 0;\r\n}\r\n\r\n.loading-spinner {\r\n  width: 40px;\r\n  height: 40px;\r\n  border: 3px solid rgba(255, 255, 255, 0.3);\r\n  border-top: 3px solid #fff;\r\n  border-radius: 50%;\r\n  animation: spin 1s linear infinite;\r\n  margin-bottom: 15px;\r\n}\r\n\r\n@keyframes spin {\r\n  0% { transform: rotate(0deg); }\r\n  100% { transform: rotate(360deg); }\r\n}\r\n\r\n.video-loading p {\r\n  margin: 0;\r\n  font-size: 1rem;\r\n  opacity: 0.8;\r\n}\r\n\r\n/* Video Preview Container */\r\n.video-preview-container {\r\n  position: relative;\r\n  height: 400px;\r\n  border-radius: 8px;\r\n  overflow: hidden;\r\n  cursor: pointer;\r\n}\r\n\r\n.video-preview-thumbnail {\r\n  width: 100%;\r\n  height: 100%;\r\n  object-fit: cover;\r\n  transition: transform 0.3s ease;\r\n}\r\n\r\n.video-preview-container:hover .video-preview-thumbnail {\r\n  transform: scale(1.05);\r\n}\r\n\r\n.video-preview-overlay {\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  background: rgba(0, 0, 0, 0.6);\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  justify-content: center;\r\n  transition: opacity 0.3s ease;\r\n}\r\n\r\n.video-preview-play-btn {\r\n  background: rgba(255, 255, 255, 0.9);\r\n  border: none;\r\n  border-radius: 50px;\r\n  padding: 20px 30px;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 12px;\r\n  font-size: 1.1rem;\r\n  font-weight: 600;\r\n  color: #333;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);\r\n}\r\n\r\n.video-preview-play-btn:hover {\r\n  background: white;\r\n  transform: scale(1.05);\r\n  box-shadow: 0 6px 25px rgba(0, 0, 0, 0.4);\r\n}\r\n\r\n.preview-play-icon {\r\n  font-size: 2rem;\r\n  color: #007bff;\r\n}\r\n\r\n/* Progress Bar Styles */\r\n.video-load-progress,\r\n.loading-progress {\r\n  margin-top: 20px;\r\n  text-align: center;\r\n  color: white;\r\n}\r\n\r\n.progress-bar {\r\n  width: 200px;\r\n  height: 6px;\r\n  background: rgba(255, 255, 255, 0.3);\r\n  border-radius: 3px;\r\n  overflow: hidden;\r\n  margin: 10px auto;\r\n}\r\n\r\n.progress-fill {\r\n  height: 100%;\r\n  background: linear-gradient(90deg, #007bff, #28a745);\r\n  border-radius: 3px;\r\n  transition: width 0.3s ease;\r\n}\r\n\r\n.video-load-progress span,\r\n.loading-progress span {\r\n  font-size: 0.9rem;\r\n  opacity: 0.9;\r\n}\r\n\r\n/* Loading Tips */\r\n.loading-tips {\r\n  margin-top: 30px;\r\n  text-align: left;\r\n  max-width: 300px;\r\n  background: rgba(255, 255, 255, 0.1);\r\n  padding: 15px;\r\n  border-radius: 8px;\r\n  border: 1px solid rgba(255, 255, 255, 0.2);\r\n}\r\n\r\n.loading-tips p {\r\n  margin: 0 0 10px 0;\r\n  font-size: 0.9rem;\r\n  font-weight: 600;\r\n  color: #ffd700;\r\n}\r\n\r\n.loading-tips ul {\r\n  margin: 0;\r\n  padding-left: 20px;\r\n  list-style-type: disc;\r\n}\r\n\r\n.loading-tips li {\r\n  font-size: 0.85rem;\r\n  margin-bottom: 5px;\r\n  opacity: 0.9;\r\n  line-height: 1.4;\r\n}\r\n\r\n.loading-actions {\r\n  display: flex;\r\n  gap: 10px;\r\n  margin: 15px 0;\r\n  justify-content: center;\r\n  flex-wrap: wrap;\r\n}\r\n\r\n.skip-loading-btn, .retry-loading-btn {\r\n  padding: 10px 20px;\r\n  border: none;\r\n  border-radius: 6px;\r\n  font-size: 0.9rem;\r\n  font-weight: 600;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n  min-width: 140px;\r\n}\r\n\r\n.skip-loading-btn.primary {\r\n  background: #27ae60;\r\n  color: white;\r\n  font-size: 1rem;\r\n  padding: 12px 24px;\r\n  box-shadow: 0 2px 8px rgba(39, 174, 96, 0.3);\r\n}\r\n\r\n.skip-loading-btn.primary:hover {\r\n  background: #219a52;\r\n  transform: translateY(-1px);\r\n  box-shadow: 0 4px 12px rgba(39, 174, 96, 0.4);\r\n}\r\n\r\n.retry-loading-btn {\r\n  background: rgba(255, 255, 255, 0.2);\r\n  color: white;\r\n  border: 1px solid rgba(255, 255, 255, 0.3);\r\n}\r\n\r\n.retry-loading-btn:hover {\r\n  background: rgba(255, 255, 255, 0.3);\r\n  transform: translateY(-1px);\r\n}\r\n\r\n/* Video Processing State */\r\n.video-processing {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  min-height: 400px;\r\n  background: linear-gradient(135deg, #3498db, #2980b9);\r\n  border-radius: 12px;\r\n  color: white;\r\n  text-align: center;\r\n  padding: 2rem;\r\n}\r\n\r\n.processing-content {\r\n  max-width: 400px;\r\n}\r\n\r\n.processing-spinner {\r\n  margin-bottom: 1.5rem;\r\n}\r\n\r\n.spinner {\r\n  width: 60px;\r\n  height: 60px;\r\n  border: 4px solid rgba(255, 255, 255, 0.3);\r\n  border-top: 4px solid white;\r\n  border-radius: 50%;\r\n  animation: spin 1s linear infinite;\r\n  margin: 0 auto;\r\n}\r\n\r\n@keyframes spin {\r\n  0% { transform: rotate(0deg); }\r\n  100% { transform: rotate(360deg); }\r\n}\r\n\r\n.processing-content h3 {\r\n  font-size: 1.5rem;\r\n  margin-bottom: 1rem;\r\n  font-weight: 600;\r\n}\r\n\r\n.processing-content p {\r\n  margin-bottom: 0.75rem;\r\n  opacity: 0.9;\r\n  line-height: 1.5;\r\n}\r\n\r\n.processing-actions {\r\n  margin-top: 2rem;\r\n}\r\n\r\n.refresh-btn {\r\n  background: rgba(255, 255, 255, 0.2);\r\n  border: 2px solid rgba(255, 255, 255, 0.3);\r\n  color: white;\r\n  padding: 0.75rem 1.5rem;\r\n  border-radius: 8px;\r\n  cursor: pointer;\r\n  font-weight: 500;\r\n  transition: all 0.3s ease;\r\n  margin-bottom: 1rem;\r\n}\r\n\r\n.refresh-btn:hover:not(:disabled) {\r\n  background: rgba(255, 255, 255, 0.3);\r\n  border-color: rgba(255, 255, 255, 0.5);\r\n  transform: translateY(-2px);\r\n}\r\n\r\n.refresh-btn:disabled {\r\n  opacity: 0.6;\r\n  cursor: not-allowed;\r\n}\r\n\r\n.processing-note {\r\n  font-size: 0.9rem;\r\n  opacity: 0.8;\r\n  margin: 0;\r\n}\r\n\r\n/* Video Error State */\r\n.video-error {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  height: 400px;\r\n  background: #f8f9fa;\r\n  color: #666;\r\n  border-radius: 8px;\r\n  border: 2px dashed #ddd;\r\n}\r\n\r\n.video-error p {\r\n  margin: 0;\r\n  font-size: 1rem;\r\n}\r\n\r\n/* Video Format Error */\r\n.video-format-error {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  min-height: 400px;\r\n  background: linear-gradient(135deg, #ff6b6b, #ffa500);\r\n  color: white;\r\n  border-radius: 8px;\r\n  padding: 30px;\r\n  text-align: center;\r\n}\r\n\r\n.format-error-content h3 {\r\n  margin: 0 0 15px 0;\r\n  font-size: 1.5rem;\r\n  font-weight: 600;\r\n}\r\n\r\n.format-error-content p {\r\n  margin: 10px 0;\r\n  font-size: 1rem;\r\n  line-height: 1.5;\r\n}\r\n\r\n.format-error-actions {\r\n  margin-top: 25px;\r\n}\r\n\r\n.download-video-btn {\r\n  display: inline-block;\r\n  background: rgba(255, 255, 255, 0.2);\r\n  color: white;\r\n  text-decoration: none;\r\n  padding: 12px 24px;\r\n  border-radius: 25px;\r\n  font-weight: 600;\r\n  font-size: 1rem;\r\n  transition: all 0.3s ease;\r\n  border: 2px solid rgba(255, 255, 255, 0.3);\r\n}\r\n\r\n.download-video-btn:hover {\r\n  background: rgba(255, 255, 255, 0.3);\r\n  transform: translateY(-2px);\r\n  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);\r\n}\r\n\r\n.format-note {\r\n  margin-top: 15px;\r\n  font-size: 0.9rem;\r\n  opacity: 0.9;\r\n  font-style: italic;\r\n}\r\n\r\n\r\n\r\n\r\n\r\n/* Video Footer */\r\n.video-footer {\r\n  padding: 20px 25px;\r\n  background: #f8f9fa;\r\n  border-top: 1px solid #eee;\r\n}\r\n\r\n.video-description {\r\n  margin-bottom: 15px;\r\n}\r\n\r\n.video-description p {\r\n  margin: 0;\r\n  color: #666;\r\n  font-size: 0.95rem;\r\n  line-height: 1.5;\r\n}\r\n\r\n.video-actions {\r\n  display: flex;\r\n  gap: 10px;\r\n  justify-content: flex-end;\r\n}\r\n\r\n.video-actions .action-btn {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n  padding: 10px 16px;\r\n  font-size: 0.9rem;\r\n  border-radius: 6px;\r\n  transition: all 0.2s ease;\r\n}\r\n\r\n.video-actions .action-btn:hover {\r\n  transform: translateY(-1px);\r\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\r\n}\r\n\r\n/* Responsive Design for Video Modal */\r\n@media (max-width: 768px) {\r\n  .video-overlay {\r\n    padding: 10px;\r\n  }\r\n\r\n  .video-modal {\r\n    width: 100%;\r\n    max-width: 100%;\r\n    max-height: 90vh;\r\n  }\r\n\r\n  .video-header {\r\n    padding: 15px;\r\n  }\r\n\r\n  .video-info h3 {\r\n    font-size: 1.1rem;\r\n  }\r\n\r\n  .video-meta {\r\n    flex-direction: column;\r\n    gap: 5px;\r\n  }\r\n\r\n  .video-controls {\r\n    gap: 5px;\r\n  }\r\n\r\n  .control-btn {\r\n    padding: 6px;\r\n    font-size: 1rem;\r\n  }\r\n\r\n  .video-container {\r\n    height: 250px;\r\n  }\r\n\r\n  .video-iframe,\r\n  .video-player {\r\n    height: 100%;\r\n  }\r\n\r\n  .video-footer {\r\n    padding: 15px;\r\n  }\r\n\r\n  .video-actions {\r\n    justify-content: center;\r\n  }\r\n}\r\n\r\n@media (max-width: 480px) {\r\n  .video-overlay {\r\n    padding: 5px;\r\n  }\r\n\r\n  .video-modal {\r\n    max-height: 95vh;\r\n  }\r\n\r\n  .video-header {\r\n    padding: 12px;\r\n  }\r\n\r\n  .video-info h3 {\r\n    font-size: 1rem;\r\n  }\r\n\r\n  .video-container {\r\n    height: 200px;\r\n  }\r\n\r\n  .video-iframe,\r\n  .video-player {\r\n    height: 100%;\r\n  }\r\n}\r\n\r\n.play-icon {\r\n  font-size: 3rem;\r\n  color: white;\r\n  margin-bottom: 0.5rem;\r\n}\r\n\r\n.play-text {\r\n  color: white;\r\n  font-weight: 600;\r\n  font-size: 1rem;\r\n}\r\n\r\n.video-play-btn {\r\n  width: 100%;\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n  border: none;\r\n  color: white;\r\n  font-weight: 600;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  gap: 0.5rem;\r\n}\r\n\r\n.video-play-btn:hover {\r\n  background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);\r\n  transform: translateY(-2px);\r\n}\r\n\r\n/* Video Overlay */\r\n.video-overlay {\r\n  position: fixed;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  background: rgba(0, 0, 0, 0.8);\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  z-index: 1000;\r\n  padding: 2rem;\r\n}\r\n\r\n.video-modal {\r\n  background: white;\r\n  border-radius: 12px;\r\n  overflow: hidden;\r\n  max-width: 900px;\r\n  width: 100%;\r\n  max-height: 90vh;\r\n  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.25);\r\n}\r\n\r\n.video-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding: 1.5rem;\r\n  border-bottom: 1px solid #e2e8f0;\r\n  background: #f8fafc;\r\n}\r\n\r\n.video-header h3 {\r\n  font-size: 1.25rem;\r\n  font-weight: 600;\r\n  color: #1e293b;\r\n  margin: 0;\r\n}\r\n\r\n.close-video-btn {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  width: 32px;\r\n  height: 32px;\r\n  background: #f1f5f9;\r\n  border: none;\r\n  border-radius: 50%;\r\n  color: #64748b;\r\n  cursor: pointer;\r\n  transition: all 0.2s ease;\r\n}\r\n\r\n.close-video-btn:hover {\r\n  background: #e2e8f0;\r\n  color: #374151;\r\n}\r\n\r\n.video-container {\r\n  position: relative;\r\n  width: 100%;\r\n  height: 0;\r\n  padding-bottom: 56.25%; /* 16:9 aspect ratio */\r\n}\r\n\r\n.video-iframe {\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  width: 100%;\r\n  height: 100%;\r\n  border: none;\r\n}\r\n\r\n/* Responsive Design */\r\n@media (max-width: 768px) {\r\n  .modern-header {\r\n    padding: 1.5rem 1rem;\r\n  }\r\n\r\n  .header-content {\r\n    flex-direction: column;\r\n    gap: 1rem;\r\n    text-align: center;\r\n  }\r\n\r\n  .header-text h1 {\r\n    font-size: 2rem;\r\n  }\r\n\r\n  .tabs-container {\r\n    padding: 0 1rem;\r\n  }\r\n\r\n  .nav-tab {\r\n    padding: 1rem 1.5rem;\r\n    font-size: 0.875rem;\r\n  }\r\n\r\n  .filters-container {\r\n    padding: 0 1rem;\r\n  }\r\n\r\n  .search-sort-container {\r\n    flex-direction: column;\r\n    gap: 1rem;\r\n    align-items: stretch;\r\n  }\r\n\r\n  .search-section,\r\n  .sort-section {\r\n    flex: none; /* Reset flex properties on mobile */\r\n  }\r\n\r\n  .search-box {\r\n    max-width: none; /* Remove max-width on mobile */\r\n  }\r\n\r\n  .sort-selector {\r\n    width: 100%; /* Full width on mobile */\r\n    min-width: auto; /* Remove min-width on mobile */\r\n  }\r\n\r\n  .materials-section {\r\n    padding: 1rem;\r\n  }\r\n\r\n  .materials-grid {\r\n    grid-template-columns: 1fr;\r\n    gap: 1rem;\r\n  }\r\n\r\n  .subject-pills {\r\n    justify-content: center;\r\n  }\r\n\r\n  .video-overlay {\r\n    padding: 1rem;\r\n  }\r\n\r\n  .video-modal {\r\n    max-height: 95vh;\r\n  }\r\n\r\n  .video-header {\r\n    padding: 1rem;\r\n  }\r\n\r\n  .video-header h3 {\r\n    font-size: 1rem;\r\n  }\r\n}\r\n\r\n@media (max-width: 480px) {\r\n  .header-text h1 {\r\n    font-size: 1.75rem;\r\n  }\r\n\r\n  .nav-tab {\r\n    padding: 0.75rem 1rem;\r\n  }\r\n\r\n  .nav-tab .tab-icon {\r\n    font-size: 1rem;\r\n  }\r\n\r\n  .material-card {\r\n    margin: 0 0.5rem;\r\n  }\r\n\r\n  .card-actions {\r\n    flex-direction: column;\r\n    gap: 0.5rem;\r\n  }\r\n\r\n  .action-btn {\r\n    width: 100%;\r\n    justify-content: center;\r\n  }\r\n}\r\n\r\n/* Subtitle Controls Animations */\r\n@keyframes spin {\r\n  0% { transform: rotate(0deg); }\r\n  100% { transform: rotate(360deg); }\r\n}\r\n\r\n/* Subtitle button hover effects */\r\n.subtitle-controls button:hover {\r\n  transform: translateY(-1px);\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);\r\n}\r\n\r\n.subtitle-controls button:active {\r\n  transform: translateY(0);\r\n}\r\n  "], "names": [], "sourceRoot": ""}