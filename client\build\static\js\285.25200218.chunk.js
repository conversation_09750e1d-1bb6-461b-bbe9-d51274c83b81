"use strict";(self.webpackChunkclient=self.webpackChunkclient||[]).push([[285],{5033:(e,n,t)=>{t.d(n,{Z:()=>u});var r=t(7462),o=t(2791);const i={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M176 511a56 56 0 10112 0 56 56 0 10-112 0zm280 0a56 56 0 10112 0 56 56 0 10-112 0zm280 0a56 56 0 10112 0 56 56 0 10-112 0z"}}]},name:"ellipsis",theme:"outlined"};var l=t(4291),a=function(e,n){return o.createElement(l.Z,(0,r.Z)({},e,{ref:n,icon:i}))};const u=o.forwardRef(a)},353:(e,n,t)=>{t.d(n,{Z:()=>E});var r=t(7462),o=t(4942),i=t(9439),l=t(4925),a=t(273),u=t(1694),c=t.n(u),s=t(8834),f=t(2791),d=t(1354),v=t(5314),p=d.Z.ESC,m=d.Z.TAB;const b=(0,f.forwardRef)((function(e,n){var t=e.overlay,r=e.arrow,o=e.prefixCls,i=(0,f.useMemo)((function(){return"function"===typeof t?t():t}),[t]),l=(0,s.sQ)(n,null===i||void 0===i?void 0:i.ref);return f.createElement(f.Fragment,null,r&&f.createElement("div",{className:"".concat(o,"-arrow")}),f.cloneElement(i,{ref:(0,s.Yr)(i)?l:void 0}))}));var h={adjustX:1,adjustY:1},y=[0,0];const g={topLeft:{points:["bl","tl"],overflow:h,offset:[0,-4],targetOffset:y},top:{points:["bc","tc"],overflow:h,offset:[0,-4],targetOffset:y},topRight:{points:["br","tr"],overflow:h,offset:[0,-4],targetOffset:y},bottomLeft:{points:["tl","bl"],overflow:h,offset:[0,4],targetOffset:y},bottom:{points:["tc","bc"],overflow:h,offset:[0,4],targetOffset:y},bottomRight:{points:["tr","br"],overflow:h,offset:[0,4],targetOffset:y}};var Z=["arrow","prefixCls","transitionName","animation","align","placement","placements","getPopupContainer","showAction","hideAction","overlayClassName","overlayStyle","visible","trigger","autoFocus","overlay","children","onVisibleChange"];function C(e,n){var t,u=e.arrow,d=void 0!==u&&u,h=e.prefixCls,y=void 0===h?"rc-dropdown":h,C=e.transitionName,E=e.animation,w=e.align,M=e.placement,k=void 0===M?"bottomLeft":M,R=e.placements,N=void 0===R?g:R,P=e.getPopupContainer,x=e.showAction,I=e.hideAction,S=e.overlayClassName,K=e.overlayStyle,O=e.visible,A=e.trigger,T=void 0===A?["hover"]:A,L=e.autoFocus,D=e.overlay,V=e.children,_=e.onVisibleChange,z=(0,l.Z)(e,Z),F=f.useState(),j=(0,i.Z)(F,2),B=j[0],W=j[1],H="visible"in e?O:B,q=f.useRef(null),Y=f.useRef(null),X=f.useRef(null);f.useImperativeHandle(n,(function(){return q.current}));var G=function(e){W(e),null===_||void 0===_||_(e)};!function(e){var n=e.visible,t=e.triggerRef,r=e.onVisibleChange,o=e.autoFocus,i=e.overlayRef,l=f.useRef(!1),a=function(){var e,o;n&&(null===(e=t.current)||void 0===e||null===(o=e.focus)||void 0===o||o.call(e),null===r||void 0===r||r(!1))},u=function(){var e;return!(null===(e=i.current)||void 0===e||!e.focus)&&(i.current.focus(),l.current=!0,!0)},c=function(e){switch(e.keyCode){case p:a();break;case m:var n=!1;l.current||(n=u()),n?e.preventDefault():a()}};f.useEffect((function(){return n?(window.addEventListener("keydown",c),o&&(0,v.Z)(u,3),function(){window.removeEventListener("keydown",c),l.current=!1}):function(){l.current=!1}}),[n])}({visible:H,triggerRef:X,onVisibleChange:G,autoFocus:L,overlayRef:Y});var Q=function(){return f.createElement(b,{ref:Y,overlay:D,prefixCls:y,arrow:d})},U=f.cloneElement(V,{className:c()(null===(t=V.props)||void 0===t?void 0:t.className,H&&function(){var n=e.openClassName;return void 0!==n?n:"".concat(y,"-open")}()),ref:(0,s.Yr)(V)?(0,s.sQ)(X,V.ref):void 0}),J=I;return J||-1===T.indexOf("contextMenu")||(J=["click"]),f.createElement(a.Z,(0,r.Z)({builtinPlacements:N},z,{prefixCls:y,ref:q,popupClassName:c()(S,(0,o.Z)({},"".concat(y,"-show-arrow"),d)),popupStyle:K,action:T,showAction:x,hideAction:J,popupPlacement:k,popupAlign:w,popupTransitionName:C,popupAnimation:E,popupVisible:H,stretch:function(){var n=e.minOverlayWidthMatchTrigger,t=e.alignPoint;return"minOverlayWidthMatchTrigger"in e?n:!t}()?"minWidth":"",popup:"function"===typeof D?Q:Q(),onPopupVisibleChange:G,onPopupClick:function(n){var t=e.onOverlayClick;W(!1),t&&t(n)},getPopupContainer:P}),U)}const E=f.forwardRef(C)},2894:(e,n,t)=>{t.d(n,{iz:()=>Be,ck:()=>ye,BW:()=>je,sN:()=>ye,Wd:()=>De,ZP:()=>Qe,Xl:()=>P});var r=t(7462),o=t(4942),i=t(1413),l=t(3433),a=t(9439),u=t(4925),c=t(1694),s=t.n(c),f=t(7289),d=t(5179),v=t(632),p=t(2791),m=t(4164),b=t(2034),h=p.createContext(null);function y(e,n){return void 0===e?null:"".concat(e,"-").concat(n)}function g(e){return y(p.useContext(h),e)}var Z=t(1534),C=["children","locked"],E=p.createContext(null);function w(e){var n=e.children,t=e.locked,r=(0,u.Z)(e,C),o=p.useContext(E),l=(0,Z.Z)((function(){return function(e,n){var t=(0,i.Z)({},e);return Object.keys(n).forEach((function(e){var r=n[e];void 0!==r&&(t[e]=r)})),t}(o,r)}),[o,r],(function(e,n){return!t&&(e[0]!==n[0]||!(0,b.Z)(e[1],n[1],!0))}));return p.createElement(E.Provider,{value:l},n)}var M=[],k=p.createContext(null);function R(){return p.useContext(k)}var N=p.createContext(M);function P(e){var n=p.useContext(N);return p.useMemo((function(){return void 0!==e?[].concat((0,l.Z)(n),[e]):n}),[n,e])}var x=p.createContext(null);const I=p.createContext({});var S=t(1354),K=t(5314),O=t(2386);function A(e){var n=arguments.length>1&&void 0!==arguments[1]&&arguments[1];if((0,O.Z)(e)){var t=e.nodeName.toLowerCase(),r=["input","select","textarea","button"].includes(t)||e.isContentEditable||"a"===t&&!!e.getAttribute("href"),o=e.getAttribute("tabindex"),i=Number(o),l=null;return o&&!Number.isNaN(i)?l=i:r&&null===l&&(l=0),r&&e.disabled&&(l=null),null!==l&&(l>=0||n&&l<0)}return!1}function T(e){var n=arguments.length>1&&void 0!==arguments[1]&&arguments[1],t=(0,l.Z)(e.querySelectorAll("*")).filter((function(e){return A(e,n)}));return A(e,n)&&t.unshift(e),t}var L=S.Z.LEFT,D=S.Z.RIGHT,V=S.Z.UP,_=S.Z.DOWN,z=S.Z.ENTER,F=S.Z.ESC,j=S.Z.HOME,B=S.Z.END,W=[V,_,L,D];function H(e,n){return T(e,!0).filter((function(e){return n.has(e)}))}function q(e,n,t){var r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:1;if(!e)return null;var o=H(e,n),i=o.length,l=o.findIndex((function(e){return t===e}));return r<0?-1===l?l=i-1:l-=1:r>0&&(l+=1),o[l=(l+i)%i]}function Y(e,n,t,r,i,l,a,u,c,s){var f=p.useRef(),d=p.useRef();d.current=n;var v=function(){K.Z.cancel(f.current)};return p.useEffect((function(){return function(){v()}}),[]),function(p){var m=p.which;if([].concat(W,[z,F,j,B]).includes(m)){var b,h,g,Z=function(){return b=new Set,h=new Map,g=new Map,l().forEach((function(e){var n=document.querySelector("[data-menu-id='".concat(y(r,e),"']"));n&&(b.add(n),g.set(n,e),h.set(e,n))})),b};Z();var C=function(e,n){for(var t=e||document.activeElement;t;){if(n.has(t))return t;t=t.parentElement}return null}(h.get(n),b),E=g.get(C),w=function(e,n,t,r){var i,l,a,u,c="prev",s="next",f="children",d="parent";if("inline"===e&&r===z)return{inlineTrigger:!0};var v=(i={},(0,o.Z)(i,V,c),(0,o.Z)(i,_,s),i),p=(l={},(0,o.Z)(l,L,t?s:c),(0,o.Z)(l,D,t?c:s),(0,o.Z)(l,_,f),(0,o.Z)(l,z,f),l),m=(a={},(0,o.Z)(a,V,c),(0,o.Z)(a,_,s),(0,o.Z)(a,z,f),(0,o.Z)(a,F,d),(0,o.Z)(a,L,t?f:d),(0,o.Z)(a,D,t?d:f),a);switch(null===(u={inline:v,horizontal:p,vertical:m,inlineSub:v,horizontalSub:m,verticalSub:m}["".concat(e).concat(n?"":"Sub")])||void 0===u?void 0:u[r]){case c:return{offset:-1,sibling:!0};case s:return{offset:1,sibling:!0};case d:return{offset:-1,sibling:!1};case f:return{offset:1,sibling:!1};default:return null}}(e,1===a(E,!0).length,t,m);if(!w&&m!==j&&m!==B)return;(W.includes(m)||[j,B].includes(m))&&p.preventDefault();var M=function(e){if(e){var n=e,t=e.querySelector("a");null!==t&&void 0!==t&&t.getAttribute("href")&&(n=t);var r=g.get(e);u(r),v(),f.current=(0,K.Z)((function(){d.current===r&&n.focus()}))}};if([j,B].includes(m)||w.sibling||!C){var k,R,N=H(k=C&&"inline"!==e?function(e){for(var n=e;n;){if(n.getAttribute("data-menu-list"))return n;n=n.parentElement}return null}(C):i.current,b);R=m===j?N[0]:m===B?N[N.length-1]:q(k,b,C,w.offset),M(R)}else if(w.inlineTrigger)c(E);else if(w.offset>0)c(E,!0),v(),f.current=(0,K.Z)((function(){Z();var e=C.getAttribute("aria-controls"),n=q(document.getElementById(e),b);M(n)}),5);else if(w.offset<0){var P=a(E,!0),x=P[P.length-2],I=h.get(x);c(x,!1),M(I)}}null===s||void 0===s||s(p)}}var X="__RC_UTIL_PATH_SPLIT__",G=function(e){return e.join(X)},Q="rc-menu-more";function U(){var e=p.useState({}),n=(0,a.Z)(e,2)[1],t=(0,p.useRef)(new Map),r=(0,p.useRef)(new Map),o=p.useState([]),i=(0,a.Z)(o,2),u=i[0],c=i[1],s=(0,p.useRef)(0),f=(0,p.useRef)(!1),d=(0,p.useCallback)((function(e,o){var i=G(o);r.current.set(i,e),t.current.set(e,i),s.current+=1;var l,a=s.current;l=function(){a===s.current&&(f.current||n({}))},Promise.resolve().then(l)}),[]),v=(0,p.useCallback)((function(e,n){var o=G(n);r.current.delete(o),t.current.delete(e)}),[]),m=(0,p.useCallback)((function(e){c(e)}),[]),b=(0,p.useCallback)((function(e,n){var r=t.current.get(e)||"",o=r.split(X);return n&&u.includes(o[0])&&o.unshift(Q),o}),[u]),h=(0,p.useCallback)((function(e,n){return e.some((function(e){return b(e,!0).includes(n)}))}),[b]),y=(0,p.useCallback)((function(e){var n="".concat(t.current.get(e)).concat(X),o=new Set;return(0,l.Z)(r.current.keys()).forEach((function(e){e.startsWith(n)&&o.add(r.current.get(e))})),o}),[]);return p.useEffect((function(){return function(){f.current=!0}}),[]),{registerPath:d,unregisterPath:v,refreshOverflowKeys:m,isSubPathKey:h,getKeyPath:b,getKeys:function(){var e=(0,l.Z)(t.current.keys());return u.length&&e.push(Q),e},getSubPathKeys:y}}function J(e){var n=p.useRef(e);n.current=e;var t=p.useCallback((function(){for(var e,t=arguments.length,r=new Array(t),o=0;o<t;o++)r[o]=arguments[o];return null===(e=n.current)||void 0===e?void 0:e.call.apply(e,[n].concat(r))}),[]);return e?t:void 0}var $=Math.random().toFixed(5).toString().slice(2),ee=0;var ne=t(5671),te=t(3144),re=t(9340),oe=t(8557),ie=t(1818),le=t(8834);function ae(e,n,t,r){var o=p.useContext(E),i=o.activeKey,l=o.onActive,a=o.onInactive,u={active:i===e};return n||(u.onMouseEnter=function(n){null===t||void 0===t||t({key:e,domEvent:n}),l(e)},u.onMouseLeave=function(n){null===r||void 0===r||r({key:e,domEvent:n}),a(e)}),u}function ue(e){var n=p.useContext(E),t=n.mode,r=n.rtl,o=n.inlineIndent;if("inline"!==t)return null;return r?{paddingRight:e*o}:{paddingLeft:e*o}}function ce(e){var n=e.icon,t=e.props,r=e.children;return("function"===typeof n?p.createElement(n,(0,i.Z)({},t)):n)||r||null}var se=["item"];function fe(e){var n=e.item,t=(0,u.Z)(e,se);return Object.defineProperty(t,"item",{get:function(){return(0,v.ZP)(!1,"`info.item` is deprecated since we will move to function component that not provides React Node instance in future."),n}}),t}var de=["title","attribute","elementRef"],ve=["style","className","eventKey","warnKey","disabled","itemIcon","children","role","onMouseEnter","onMouseLeave","onClick","onKeyDown","onFocus"],pe=["active"],me=function(e){(0,re.Z)(t,e);var n=(0,oe.Z)(t);function t(){return(0,ne.Z)(this,t),n.apply(this,arguments)}return(0,te.Z)(t,[{key:"render",value:function(){var e=this.props,n=e.title,t=e.attribute,o=e.elementRef,i=(0,u.Z)(e,de),l=(0,ie.Z)(i,["eventKey","popupClassName","popupOffset","onTitleClick"]);return(0,v.ZP)(!t,"`attribute` of Menu.Item is deprecated. Please pass attribute directly."),p.createElement(f.Z.Item,(0,r.Z)({},t,{title:"string"===typeof n?n:void 0},l,{ref:o}))}}]),t}(p.Component),be=p.forwardRef((function(e,n){var t,a=e.style,c=e.className,f=e.eventKey,d=(e.warnKey,e.disabled),v=e.itemIcon,m=e.children,b=e.role,h=e.onMouseEnter,y=e.onMouseLeave,Z=e.onClick,C=e.onKeyDown,w=e.onFocus,M=(0,u.Z)(e,ve),k=g(f),R=p.useContext(E),N=R.prefixCls,x=R.onItemClick,K=R.disabled,O=R.overflowDisabled,A=R.itemIcon,T=R.selectedKeys,L=R.onActive,D=p.useContext(I)._internalRenderMenuItem,V="".concat(N,"-item"),_=p.useRef(),z=p.useRef(),F=K||d,j=(0,le.x1)(n,z),B=P(f);var W=function(e){return{key:f,keyPath:(0,l.Z)(B).reverse(),item:_.current,domEvent:e}},H=v||A,q=ae(f,F,h,y),Y=q.active,X=(0,u.Z)(q,pe),G=T.includes(f),Q=ue(B.length),U={};"option"===e.role&&(U["aria-selected"]=G);var J=p.createElement(me,(0,r.Z)({ref:_,elementRef:j,role:null===b?"none":b||"menuitem",tabIndex:d?null:-1,"data-menu-id":O&&k?null:k},M,X,U,{component:"li","aria-disabled":d,style:(0,i.Z)((0,i.Z)({},Q),a),className:s()(V,(t={},(0,o.Z)(t,"".concat(V,"-active"),Y),(0,o.Z)(t,"".concat(V,"-selected"),G),(0,o.Z)(t,"".concat(V,"-disabled"),F),t),c),onClick:function(e){if(!F){var n=W(e);null===Z||void 0===Z||Z(fe(n)),x(n)}},onKeyDown:function(e){if(null===C||void 0===C||C(e),e.which===S.Z.ENTER){var n=W(e);null===Z||void 0===Z||Z(fe(n)),x(n)}},onFocus:function(e){L(f),null===w||void 0===w||w(e)}}),m,p.createElement(ce,{props:(0,i.Z)((0,i.Z)({},e),{},{isSelected:G}),icon:H}));return D&&(J=D(J,e,{selected:G})),J}));function he(e,n){var t=e.eventKey,o=R(),i=P(t);return p.useEffect((function(){if(o)return o.registerPath(t,i),function(){o.unregisterPath(t,i)}}),[i]),o?null:p.createElement(be,(0,r.Z)({},e,{ref:n}))}const ye=p.forwardRef(he);var ge=["className","children"],Ze=function(e,n){var t=e.className,o=e.children,i=(0,u.Z)(e,ge),l=p.useContext(E),a=l.prefixCls,c=l.mode,f=l.rtl;return p.createElement("ul",(0,r.Z)({className:s()(a,f&&"".concat(a,"-rtl"),"".concat(a,"-sub"),"".concat(a,"-").concat("inline"===c?"inline":"vertical"),t),role:"menu"},i,{"data-menu-list":!0,ref:n}),o)},Ce=p.forwardRef(Ze);Ce.displayName="SubMenuList";const Ee=Ce;var we=t(5501);function Me(e,n){return(0,we.Z)(e).map((function(e,t){if(p.isValidElement(e)){var r,o,i=e.key,a=null!==(r=null===(o=e.props)||void 0===o?void 0:o.eventKey)&&void 0!==r?r:i;(null===a||void 0===a)&&(a="tmp_key-".concat([].concat((0,l.Z)(n),[t]).join("-")));var u={key:a,eventKey:a};return p.cloneElement(e,u)}return e}))}var ke=t(273),Re={adjustX:1,adjustY:1},Ne={topLeft:{points:["bl","tl"],overflow:Re},topRight:{points:["br","tr"],overflow:Re},bottomLeft:{points:["tl","bl"],overflow:Re},bottomRight:{points:["tr","br"],overflow:Re},leftTop:{points:["tr","tl"],overflow:Re},leftBottom:{points:["br","bl"],overflow:Re},rightTop:{points:["tl","tr"],overflow:Re},rightBottom:{points:["bl","br"],overflow:Re}},Pe={topLeft:{points:["bl","tl"],overflow:Re},topRight:{points:["br","tr"],overflow:Re},bottomLeft:{points:["tl","bl"],overflow:Re},bottomRight:{points:["tr","br"],overflow:Re},rightTop:{points:["tr","tl"],overflow:Re},rightBottom:{points:["br","bl"],overflow:Re},leftTop:{points:["tl","tr"],overflow:Re},leftBottom:{points:["bl","br"],overflow:Re}};function xe(e,n,t){return n||(t?t[e]||t.other:void 0)}var Ie={horizontal:"bottomLeft",vertical:"rightTop","vertical-left":"rightTop","vertical-right":"leftTop"};function Se(e){var n=e.prefixCls,t=e.visible,r=e.children,l=e.popup,u=e.popupClassName,c=e.popupOffset,f=e.disabled,d=e.mode,v=e.onVisibleChange,m=p.useContext(E),b=m.getPopupContainer,h=m.rtl,y=m.subMenuOpenDelay,g=m.subMenuCloseDelay,Z=m.builtinPlacements,C=m.triggerSubMenuAction,w=m.forceSubMenuRender,M=m.rootClassName,k=m.motion,R=m.defaultMotions,N=p.useState(!1),P=(0,a.Z)(N,2),x=P[0],I=P[1],S=h?(0,i.Z)((0,i.Z)({},Pe),Z):(0,i.Z)((0,i.Z)({},Ne),Z),O=Ie[d],A=xe(d,k,R),T=p.useRef(A);"inline"!==d&&(T.current=A);var L=(0,i.Z)((0,i.Z)({},T.current),{},{leavedClassName:"".concat(n,"-hidden"),removeOnLeave:!1,motionAppear:!0}),D=p.useRef();return p.useEffect((function(){return D.current=(0,K.Z)((function(){I(t)})),function(){K.Z.cancel(D.current)}}),[t]),p.createElement(ke.Z,{prefixCls:n,popupClassName:s()("".concat(n,"-popup"),(0,o.Z)({},"".concat(n,"-rtl"),h),u,M),stretch:"horizontal"===d?"minWidth":null,getPopupContainer:b,builtinPlacements:S,popupPlacement:O,popupVisible:x,popup:l,popupAlign:c&&{offset:c},action:f?[]:[C],mouseEnterDelay:y,mouseLeaveDelay:g,onPopupVisibleChange:v,forceRender:w,popupMotion:L},r)}var Ke=t(8568);function Oe(e){var n=e.id,t=e.open,o=e.keyPath,l=e.children,u="inline",c=p.useContext(E),s=c.prefixCls,f=c.forceSubMenuRender,d=c.motion,v=c.defaultMotions,m=c.mode,b=p.useRef(!1);b.current=m===u;var h=p.useState(!b.current),y=(0,a.Z)(h,2),g=y[0],Z=y[1],C=!!b.current&&t;p.useEffect((function(){b.current&&Z(!1)}),[m]);var M=(0,i.Z)({},xe(u,d,v));o.length>1&&(M.motionAppear=!1);var k=M.onVisibleChanged;return M.onVisibleChanged=function(e){return b.current||e||Z(!0),null===k||void 0===k?void 0:k(e)},g?null:p.createElement(w,{mode:u,locked:!b.current},p.createElement(Ke.ZP,(0,r.Z)({visible:C},M,{forceRender:f,removeOnLeave:!1,leavedClassName:"".concat(s,"-hidden")}),(function(e){var t=e.className,r=e.style;return p.createElement(Ee,{id:n,className:t,style:r},l)})))}var Ae=["style","className","title","eventKey","warnKey","disabled","internalPopupClose","children","itemIcon","expandIcon","popupClassName","popupOffset","onClick","onMouseEnter","onMouseLeave","onTitleClick","onTitleMouseEnter","onTitleMouseLeave"],Te=["active"],Le=function(e){var n,t=e.style,l=e.className,c=e.title,d=e.eventKey,v=(e.warnKey,e.disabled),m=e.internalPopupClose,b=e.children,h=e.itemIcon,y=e.expandIcon,Z=e.popupClassName,C=e.popupOffset,M=e.onClick,k=e.onMouseEnter,R=e.onMouseLeave,N=e.onTitleClick,S=e.onTitleMouseEnter,K=e.onTitleMouseLeave,O=(0,u.Z)(e,Ae),A=g(d),T=p.useContext(E),L=T.prefixCls,D=T.mode,V=T.openKeys,_=T.disabled,z=T.overflowDisabled,F=T.activeKey,j=T.selectedKeys,B=T.itemIcon,W=T.expandIcon,H=T.onItemClick,q=T.onOpenChange,Y=T.onActive,X=p.useContext(I)._internalRenderSubMenuItem,G=p.useContext(x).isSubPathKey,Q=P(),U="".concat(L,"-submenu"),$=_||v,ee=p.useRef(),ne=p.useRef();var te=h||B,re=y||W,oe=V.includes(d),ie=!z&&oe,le=G(j,d),se=ae(d,$,S,K),de=se.active,ve=(0,u.Z)(se,Te),pe=p.useState(!1),me=(0,a.Z)(pe,2),be=me[0],he=me[1],ye=function(e){$||he(e)},ge=p.useMemo((function(){return de||"inline"!==D&&(be||G([F],d))}),[D,de,F,be,d,G]),Ze=ue(Q.length),Ce=J((function(e){null===M||void 0===M||M(fe(e)),H(e)})),we=A&&"".concat(A,"-popup"),Me=p.createElement("div",(0,r.Z)({role:"menuitem",style:Ze,className:"".concat(U,"-title"),tabIndex:$?null:-1,ref:ee,title:"string"===typeof c?c:null,"data-menu-id":z&&A?null:A,"aria-expanded":ie,"aria-haspopup":!0,"aria-controls":we,"aria-disabled":$,onClick:function(e){$||(null===N||void 0===N||N({key:d,domEvent:e}),"inline"===D&&q(d,!oe))},onFocus:function(){Y(d)}},ve),c,p.createElement(ce,{icon:"horizontal"!==D?re:null,props:(0,i.Z)((0,i.Z)({},e),{},{isOpen:ie,isSubMenu:!0})},p.createElement("i",{className:"".concat(U,"-arrow")}))),ke=p.useRef(D);if("inline"!==D&&Q.length>1?ke.current="vertical":ke.current=D,!z){var Re=ke.current;Me=p.createElement(Se,{mode:Re,prefixCls:U,visible:!m&&ie&&"inline"!==D,popupClassName:Z,popupOffset:C,popup:p.createElement(w,{mode:"horizontal"===Re?"vertical":Re},p.createElement(Ee,{id:we,ref:ne},b)),disabled:$,onVisibleChange:function(e){"inline"!==D&&q(d,e)}},Me)}var Ne=p.createElement(f.Z.Item,(0,r.Z)({role:"none"},O,{component:"li",style:t,className:s()(U,"".concat(U,"-").concat(D),l,(n={},(0,o.Z)(n,"".concat(U,"-open"),ie),(0,o.Z)(n,"".concat(U,"-active"),ge),(0,o.Z)(n,"".concat(U,"-selected"),le),(0,o.Z)(n,"".concat(U,"-disabled"),$),n)),onMouseEnter:function(e){ye(!0),null===k||void 0===k||k({key:d,domEvent:e})},onMouseLeave:function(e){ye(!1),null===R||void 0===R||R({key:d,domEvent:e})}}),Me,!z&&p.createElement(Oe,{id:we,open:ie,keyPath:Q},b));return X&&(Ne=X(Ne,e,{selected:le,active:ge,open:ie,disabled:$})),p.createElement(w,{onItemClick:Ce,mode:"horizontal"===D?"vertical":D,itemIcon:te,expandIcon:re},Ne)};function De(e){var n,t=e.eventKey,r=e.children,o=P(t),i=Me(r,o),l=R();return p.useEffect((function(){if(l)return l.registerPath(t,o),function(){l.unregisterPath(t,o)}}),[o]),n=l?i:p.createElement(Le,e,i),p.createElement(N.Provider,{value:o},n)}var Ve=t(1002),_e=["className","title","eventKey","children"],ze=["children"],Fe=function(e){var n=e.className,t=e.title,o=(e.eventKey,e.children),i=(0,u.Z)(e,_e),l=p.useContext(E).prefixCls,a="".concat(l,"-item-group");return p.createElement("li",(0,r.Z)({role:"presentation"},i,{onClick:function(e){return e.stopPropagation()},className:s()(a,n)}),p.createElement("div",{role:"presentation",className:"".concat(a,"-title"),title:"string"===typeof t?t:void 0},t),p.createElement("ul",{role:"group",className:"".concat(a,"-list")},o))};function je(e){var n=e.children,t=(0,u.Z)(e,ze),r=Me(n,P(t.eventKey));return R()?r:p.createElement(Fe,(0,ie.Z)(t,["warnKey"]),r)}function Be(e){var n=e.className,t=e.style,r=p.useContext(E).prefixCls;return R()?null:p.createElement("li",{className:s()("".concat(r,"-item-divider"),n),style:t})}var We=["label","children","key","type"];function He(e){return(e||[]).map((function(e,n){if(e&&"object"===(0,Ve.Z)(e)){var t=e,o=t.label,i=t.children,l=t.key,a=t.type,c=(0,u.Z)(t,We),s=null!==l&&void 0!==l?l:"tmp-".concat(n);return i||"group"===a?"group"===a?p.createElement(je,(0,r.Z)({key:s},c,{title:o}),He(i)):p.createElement(De,(0,r.Z)({key:s},c,{title:o}),He(i)):"divider"===a?p.createElement(Be,(0,r.Z)({key:s},c)):p.createElement(ye,(0,r.Z)({key:s},c),o)}return null})).filter((function(e){return e}))}function qe(e,n,t){var r=e;return n&&(r=He(n)),Me(r,t)}var Ye=["prefixCls","rootClassName","style","className","tabIndex","items","children","direction","id","mode","inlineCollapsed","disabled","disabledOverflow","subMenuOpenDelay","subMenuCloseDelay","forceSubMenuRender","defaultOpenKeys","openKeys","activeKey","defaultActiveFirst","selectable","multiple","defaultSelectedKeys","selectedKeys","onSelect","onDeselect","inlineIndent","motion","defaultMotions","triggerSubMenuAction","builtinPlacements","itemIcon","expandIcon","overflowedIndicator","overflowedIndicatorPopupClassName","getPopupContainer","onClick","onOpenChange","onKeyDown","openAnimation","openTransitionName","_internalRenderMenuItem","_internalRenderSubMenuItem"],Xe=[];var Ge=p.forwardRef((function(e,n){var t,c,v=e,g=v.prefixCls,Z=void 0===g?"rc-menu":g,C=v.rootClassName,E=v.style,M=v.className,R=v.tabIndex,N=void 0===R?0:R,P=v.items,S=v.children,K=v.direction,O=v.id,A=v.mode,T=void 0===A?"vertical":A,L=v.inlineCollapsed,D=v.disabled,V=v.disabledOverflow,_=v.subMenuOpenDelay,z=void 0===_?.1:_,F=v.subMenuCloseDelay,j=void 0===F?.1:F,B=v.forceSubMenuRender,W=v.defaultOpenKeys,H=v.openKeys,q=v.activeKey,X=v.defaultActiveFirst,G=v.selectable,ne=void 0===G||G,te=v.multiple,re=void 0!==te&&te,oe=v.defaultSelectedKeys,ie=v.selectedKeys,le=v.onSelect,ae=v.onDeselect,ue=v.inlineIndent,ce=void 0===ue?24:ue,se=v.motion,de=v.defaultMotions,ve=v.triggerSubMenuAction,pe=void 0===ve?"hover":ve,me=v.builtinPlacements,be=v.itemIcon,he=v.expandIcon,ge=v.overflowedIndicator,Ze=void 0===ge?"...":ge,Ce=v.overflowedIndicatorPopupClassName,Ee=v.getPopupContainer,we=v.onClick,Me=v.onOpenChange,ke=v.onKeyDown,Re=(v.openAnimation,v.openTransitionName,v._internalRenderMenuItem),Ne=v._internalRenderSubMenuItem,Pe=(0,u.Z)(v,Ye),xe=p.useMemo((function(){return qe(S,P,Xe)}),[S,P]),Ie=p.useState(!1),Se=(0,a.Z)(Ie,2),Ke=Se[0],Oe=Se[1],Ae=p.useRef(),Te=function(e){var n=(0,d.Z)(e,{value:e}),t=(0,a.Z)(n,2),r=t[0],o=t[1];return p.useEffect((function(){ee+=1;var e="".concat($,"-").concat(ee);o("rc-menu-uuid-".concat(e))}),[]),r}(O),Le="rtl"===K;var Ve=(0,d.Z)(W,{value:H,postState:function(e){return e||Xe}}),_e=(0,a.Z)(Ve,2),ze=_e[0],Fe=_e[1],je=function(e){function n(){Fe(e),null===Me||void 0===Me||Me(e)}arguments.length>1&&void 0!==arguments[1]&&arguments[1]?(0,m.flushSync)(n):n()},Be=p.useState(ze),We=(0,a.Z)(Be,2),He=We[0],Ge=We[1],Qe=p.useRef(!1),Ue=p.useMemo((function(){return"inline"!==T&&"vertical"!==T||!L?[T,!1]:["vertical",L]}),[T,L]),Je=(0,a.Z)(Ue,2),$e=Je[0],en=Je[1],nn="inline"===$e,tn=p.useState($e),rn=(0,a.Z)(tn,2),on=rn[0],ln=rn[1],an=p.useState(en),un=(0,a.Z)(an,2),cn=un[0],sn=un[1];p.useEffect((function(){ln($e),sn(en),Qe.current&&(nn?Fe(He):je(Xe))}),[$e,en]);var fn=p.useState(0),dn=(0,a.Z)(fn,2),vn=dn[0],pn=dn[1],mn=vn>=xe.length-1||"horizontal"!==on||V;p.useEffect((function(){nn&&Ge(ze)}),[ze]),p.useEffect((function(){return Qe.current=!0,function(){Qe.current=!1}}),[]);var bn=U(),hn=bn.registerPath,yn=bn.unregisterPath,gn=bn.refreshOverflowKeys,Zn=bn.isSubPathKey,Cn=bn.getKeyPath,En=bn.getKeys,wn=bn.getSubPathKeys,Mn=p.useMemo((function(){return{registerPath:hn,unregisterPath:yn}}),[hn,yn]),kn=p.useMemo((function(){return{isSubPathKey:Zn}}),[Zn]);p.useEffect((function(){gn(mn?Xe:xe.slice(vn+1).map((function(e){return e.key})))}),[vn,mn]);var Rn=(0,d.Z)(q||X&&(null===(t=xe[0])||void 0===t?void 0:t.key),{value:q}),Nn=(0,a.Z)(Rn,2),Pn=Nn[0],xn=Nn[1],In=J((function(e){xn(e)})),Sn=J((function(){xn(void 0)}));(0,p.useImperativeHandle)(n,(function(){return{list:Ae.current,focus:function(e){var n,t,r,o,i=null!==Pn&&void 0!==Pn?Pn:null===(n=xe.find((function(e){return!e.props.disabled})))||void 0===n?void 0:n.key;i&&(null===(t=Ae.current)||void 0===t||null===(r=t.querySelector("li[data-menu-id='".concat(y(Te,i),"']")))||void 0===r||null===(o=r.focus)||void 0===o||o.call(r,e))}}}));var Kn=(0,d.Z)(oe||[],{value:ie,postState:function(e){return Array.isArray(e)?e:null===e||void 0===e?Xe:[e]}}),On=(0,a.Z)(Kn,2),An=On[0],Tn=On[1],Ln=J((function(e){null===we||void 0===we||we(fe(e)),function(e){if(ne){var n,t=e.key,r=An.includes(t);n=re?r?An.filter((function(e){return e!==t})):[].concat((0,l.Z)(An),[t]):[t],Tn(n);var o=(0,i.Z)((0,i.Z)({},e),{},{selectedKeys:n});r?null===ae||void 0===ae||ae(o):null===le||void 0===le||le(o)}!re&&ze.length&&"inline"!==on&&je(Xe)}(e)})),Dn=J((function(e,n){var t=ze.filter((function(n){return n!==e}));if(n)t.push(e);else if("inline"!==on){var r=wn(e);t=t.filter((function(e){return!r.has(e)}))}(0,b.Z)(ze,t,!0)||je(t,!0)})),Vn=Y(on,Pn,Le,Te,Ae,En,Cn,xn,(function(e,n){var t=null!==n&&void 0!==n?n:!ze.includes(e);Dn(e,t)}),ke);p.useEffect((function(){Oe(!0)}),[]);var _n=p.useMemo((function(){return{_internalRenderMenuItem:Re,_internalRenderSubMenuItem:Ne}}),[Re,Ne]),zn="horizontal"!==on||V?xe:xe.map((function(e,n){return p.createElement(w,{key:e.key,overflowDisabled:n>vn},e)})),Fn=p.createElement(f.Z,(0,r.Z)({id:O,ref:Ae,prefixCls:"".concat(Z,"-overflow"),component:"ul",itemComponent:ye,className:s()(Z,"".concat(Z,"-root"),"".concat(Z,"-").concat(on),M,(c={},(0,o.Z)(c,"".concat(Z,"-inline-collapsed"),cn),(0,o.Z)(c,"".concat(Z,"-rtl"),Le),c),C),dir:K,style:E,role:"menu",tabIndex:N,data:zn,renderRawItem:function(e){return e},renderRawRest:function(e){var n=e.length,t=n?xe.slice(-n):null;return p.createElement(De,{eventKey:Q,title:Ze,disabled:mn,internalPopupClose:0===n,popupClassName:Ce},t)},maxCount:"horizontal"!==on||V?f.Z.INVALIDATE:f.Z.RESPONSIVE,ssr:"full","data-menu-list":!0,onVisibleChange:function(e){pn(e)},onKeyDown:Vn},Pe));return p.createElement(I.Provider,{value:_n},p.createElement(h.Provider,{value:Te},p.createElement(w,{prefixCls:Z,rootClassName:C,mode:on,openKeys:ze,rtl:Le,disabled:D,motion:Ke?se:null,defaultMotions:Ke?de:null,activeKey:Pn,onActive:In,onInactive:Sn,selectedKeys:An,inlineIndent:ce,subMenuOpenDelay:z,subMenuCloseDelay:j,forceSubMenuRender:B,builtinPlacements:me,triggerSubMenuAction:pe,getPopupContainer:Ee,itemIcon:be,expandIcon:he,onItemClick:Ln,onOpenChange:Dn},p.createElement(x.Provider,{value:kn},Fn),p.createElement("div",{style:{display:"none"},"aria-hidden":!0},p.createElement(k.Provider,{value:Mn},xe)))))}));Ge.Item=ye,Ge.SubMenu=De,Ge.ItemGroup=je,Ge.Divider=Be;const Qe=Ge}}]);
//# sourceMappingURL=285.25200218.chunk.js.map