{"ast": null, "code": "import _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport * as React from 'react';\nfunction getUseId() {\n  // We need fully clone React function here to avoid webpack warning React 17 do not export `useId`\n  var fullClone = _objectSpread({}, React);\n  return fullClone.useId;\n}\nvar uuid = 0;\n\n/** @private Note only worked in develop env. Not work in production. */\nexport function resetUuid() {\n  if (process.env.NODE_ENV !== 'production') {\n    uuid = 0;\n  }\n}\nexport default function useId(id) {\n  // Inner id for accessibility usage. Only work in client side\n  var _React$useState = React.useState('ssr-id'),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    innerId = _React$useState2[0],\n    setInnerId = _React$useState2[1];\n  var useOriginId = getUseId();\n  var reactNativeId = useOriginId === null || useOriginId === void 0 ? void 0 : useOriginId();\n  React.useEffect(function () {\n    if (!useOriginId) {\n      var nextId = uuid;\n      uuid += 1;\n      setInnerId(\"rc_unique_\".concat(nextId));\n    }\n  }, []);\n\n  // Developer passed id is single source of truth\n  if (id) {\n    return id;\n  }\n\n  // Test env always return mock id\n  if (process.env.NODE_ENV === 'test') {\n    return 'test-id';\n  }\n\n  // Return react native id or inner id\n  return reactNativeId || innerId;\n}", "map": {"version": 3, "names": ["_slicedToArray", "_objectSpread", "React", "getUseId", "fullClone", "useId", "uuid", "resetUuid", "process", "env", "NODE_ENV", "id", "_React$useState", "useState", "_React$useState2", "innerId", "setInnerId", "useOriginId", "reactNativeId", "useEffect", "nextId", "concat"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/node_modules/rc-util/es/hooks/useId.js"], "sourcesContent": ["import _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport * as React from 'react';\nfunction getUseId() {\n  // We need fully clone React function here to avoid webpack warning React 17 do not export `useId`\n  var fullClone = _objectSpread({}, React);\n  return fullClone.useId;\n}\nvar uuid = 0;\n\n/** @private Note only worked in develop env. Not work in production. */\nexport function resetUuid() {\n  if (process.env.NODE_ENV !== 'production') {\n    uuid = 0;\n  }\n}\nexport default function useId(id) {\n  // Inner id for accessibility usage. Only work in client side\n  var _React$useState = React.useState('ssr-id'),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    innerId = _React$useState2[0],\n    setInnerId = _React$useState2[1];\n  var useOriginId = getUseId();\n  var reactNativeId = useOriginId === null || useOriginId === void 0 ? void 0 : useOriginId();\n  React.useEffect(function () {\n    if (!useOriginId) {\n      var nextId = uuid;\n      uuid += 1;\n      setInnerId(\"rc_unique_\".concat(nextId));\n    }\n  }, []);\n\n  // Developer passed id is single source of truth\n  if (id) {\n    return id;\n  }\n\n  // Test env always return mock id\n  if (process.env.NODE_ENV === 'test') {\n    return 'test-id';\n  }\n\n  // Return react native id or inner id\n  return reactNativeId || innerId;\n}"], "mappings": "AAAA,OAAOA,cAAc,MAAM,0CAA0C;AACrE,OAAOC,aAAa,MAAM,0CAA0C;AACpE,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,QAAQA,CAAA,EAAG;EAClB;EACA,IAAIC,SAAS,GAAGH,aAAa,CAAC,CAAC,CAAC,EAAEC,KAAK,CAAC;EACxC,OAAOE,SAAS,CAACC,KAAK;AACxB;AACA,IAAIC,IAAI,GAAG,CAAC;;AAEZ;AACA,OAAO,SAASC,SAASA,CAAA,EAAG;EAC1B,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzCJ,IAAI,GAAG,CAAC;EACV;AACF;AACA,eAAe,SAASD,KAAKA,CAACM,EAAE,EAAE;EAChC;EACA,IAAIC,eAAe,GAAGV,KAAK,CAACW,QAAQ,CAAC,QAAQ,CAAC;IAC5CC,gBAAgB,GAAGd,cAAc,CAACY,eAAe,EAAE,CAAC,CAAC;IACrDG,OAAO,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IAC7BE,UAAU,GAAGF,gBAAgB,CAAC,CAAC,CAAC;EAClC,IAAIG,WAAW,GAAGd,QAAQ,CAAC,CAAC;EAC5B,IAAIe,aAAa,GAAGD,WAAW,KAAK,IAAI,IAAIA,WAAW,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,WAAW,CAAC,CAAC;EAC3Ff,KAAK,CAACiB,SAAS,CAAC,YAAY;IAC1B,IAAI,CAACF,WAAW,EAAE;MAChB,IAAIG,MAAM,GAAGd,IAAI;MACjBA,IAAI,IAAI,CAAC;MACTU,UAAU,CAAC,YAAY,CAACK,MAAM,CAACD,MAAM,CAAC,CAAC;IACzC;EACF,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,IAAIT,EAAE,EAAE;IACN,OAAOA,EAAE;EACX;;EAEA;EACA,IAAIH,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,MAAM,EAAE;IACnC,OAAO,SAAS;EAClB;;EAEA;EACA,OAAOQ,aAAa,IAAIH,OAAO;AACjC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}