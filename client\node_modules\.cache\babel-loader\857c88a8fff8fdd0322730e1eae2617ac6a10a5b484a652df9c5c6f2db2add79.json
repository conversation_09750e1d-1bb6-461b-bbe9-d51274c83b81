{"ast": null, "code": "import _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport * as React from 'react';\nimport { useRef, useLayoutEffect } from 'react';\nimport classNames from 'classnames';\nimport { scrollTo, waitElementReady } from \"../../utils/uiUtil\";\nimport PanelContext from \"../../PanelContext\";\nfunction TimeUnitColumn(props) {\n  var prefixCls = props.prefixCls,\n    units = props.units,\n    onSelect = props.onSelect,\n    value = props.value,\n    active = props.active,\n    hideDisabledOptions = props.hideDisabledOptions,\n    info = props.info,\n    type = props.type;\n  var cellPrefixCls = \"\".concat(prefixCls, \"-cell\");\n  var _React$useContext = React.useContext(PanelContext),\n    open = _React$useContext.open;\n  var ulRef = useRef(null);\n  var liRefs = useRef(new Map());\n  var scrollRef = useRef();\n\n  // `useLayoutEffect` here to avoid blink by duration is 0\n  useLayoutEffect(function () {\n    var li = liRefs.current.get(value);\n    if (li && open !== false) {\n      scrollTo(ulRef.current, li.offsetTop, 120);\n    }\n  }, [value]);\n  useLayoutEffect(function () {\n    if (open) {\n      var li = liRefs.current.get(value);\n      if (li) {\n        scrollRef.current = waitElementReady(li, function () {\n          scrollTo(ulRef.current, li.offsetTop, 0);\n        });\n      }\n    }\n    return function () {\n      var _scrollRef$current;\n      (_scrollRef$current = scrollRef.current) === null || _scrollRef$current === void 0 ? void 0 : _scrollRef$current.call(scrollRef);\n    };\n  }, [open]);\n  return /*#__PURE__*/React.createElement(\"ul\", {\n    className: classNames(\"\".concat(prefixCls, \"-column\"), _defineProperty({}, \"\".concat(prefixCls, \"-column-active\"), active)),\n    ref: ulRef,\n    style: {\n      position: 'relative'\n    }\n  }, units.map(function (unit) {\n    var _classNames2;\n    if (hideDisabledOptions && unit.disabled) {\n      return null;\n    }\n    return /*#__PURE__*/React.createElement(\"li\", {\n      key: unit.value,\n      ref: function ref(element) {\n        liRefs.current.set(unit.value, element);\n      },\n      className: classNames(cellPrefixCls, (_classNames2 = {}, _defineProperty(_classNames2, \"\".concat(cellPrefixCls, \"-disabled\"), unit.disabled), _defineProperty(_classNames2, \"\".concat(cellPrefixCls, \"-selected\"), value === unit.value), _classNames2)),\n      onClick: function onClick() {\n        if (unit.disabled) {\n          return;\n        }\n        onSelect(unit.value);\n      }\n    }, info.cellRender ? info.cellRender(unit.value, {\n      today: info.today,\n      locale: info.locale,\n      originNode: /*#__PURE__*/React.createElement(\"div\", {\n        className: \"\".concat(cellPrefixCls, \"-inner\")\n      }, unit.label),\n      type: 'time',\n      subType: type\n    }) : /*#__PURE__*/React.createElement(\"div\", {\n      className: \"\".concat(cellPrefixCls, \"-inner\")\n    }, unit.label));\n  }));\n}\nexport default TimeUnitColumn;", "map": {"version": 3, "names": ["_defineProperty", "React", "useRef", "useLayoutEffect", "classNames", "scrollTo", "waitE<PERSON><PERSON><PERSON><PERSON>", "PanelContext", "TimeUnitColumn", "props", "prefixCls", "units", "onSelect", "value", "active", "hideDisabledOptions", "info", "type", "cellPrefixCls", "concat", "_React$useContext", "useContext", "open", "ulRef", "liRefs", "Map", "scrollRef", "li", "current", "get", "offsetTop", "_scrollRef$current", "call", "createElement", "className", "ref", "style", "position", "map", "unit", "_classNames2", "disabled", "key", "element", "set", "onClick", "cellRender", "today", "locale", "originNode", "label", "subType"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/node_modules/rc-picker/es/panels/TimePanel/TimeUnitColumn.js"], "sourcesContent": ["import _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport * as React from 'react';\nimport { useRef, useLayoutEffect } from 'react';\nimport classNames from 'classnames';\nimport { scrollTo, waitElementReady } from \"../../utils/uiUtil\";\nimport PanelContext from \"../../PanelContext\";\nfunction TimeUnitColumn(props) {\n  var prefixCls = props.prefixCls,\n    units = props.units,\n    onSelect = props.onSelect,\n    value = props.value,\n    active = props.active,\n    hideDisabledOptions = props.hideDisabledOptions,\n    info = props.info,\n    type = props.type;\n  var cellPrefixCls = \"\".concat(prefixCls, \"-cell\");\n  var _React$useContext = React.useContext(PanelContext),\n    open = _React$useContext.open;\n  var ulRef = useRef(null);\n  var liRefs = useRef(new Map());\n  var scrollRef = useRef();\n\n  // `useLayoutEffect` here to avoid blink by duration is 0\n  useLayoutEffect(function () {\n    var li = liRefs.current.get(value);\n    if (li && open !== false) {\n      scrollTo(ulRef.current, li.offsetTop, 120);\n    }\n  }, [value]);\n  useLayoutEffect(function () {\n    if (open) {\n      var li = liRefs.current.get(value);\n      if (li) {\n        scrollRef.current = waitElementReady(li, function () {\n          scrollTo(ulRef.current, li.offsetTop, 0);\n        });\n      }\n    }\n    return function () {\n      var _scrollRef$current;\n      (_scrollRef$current = scrollRef.current) === null || _scrollRef$current === void 0 ? void 0 : _scrollRef$current.call(scrollRef);\n    };\n  }, [open]);\n  return /*#__PURE__*/React.createElement(\"ul\", {\n    className: classNames(\"\".concat(prefixCls, \"-column\"), _defineProperty({}, \"\".concat(prefixCls, \"-column-active\"), active)),\n    ref: ulRef,\n    style: {\n      position: 'relative'\n    }\n  }, units.map(function (unit) {\n    var _classNames2;\n    if (hideDisabledOptions && unit.disabled) {\n      return null;\n    }\n    return /*#__PURE__*/React.createElement(\"li\", {\n      key: unit.value,\n      ref: function ref(element) {\n        liRefs.current.set(unit.value, element);\n      },\n      className: classNames(cellPrefixCls, (_classNames2 = {}, _defineProperty(_classNames2, \"\".concat(cellPrefixCls, \"-disabled\"), unit.disabled), _defineProperty(_classNames2, \"\".concat(cellPrefixCls, \"-selected\"), value === unit.value), _classNames2)),\n      onClick: function onClick() {\n        if (unit.disabled) {\n          return;\n        }\n        onSelect(unit.value);\n      }\n    }, info.cellRender ? info.cellRender(unit.value, {\n      today: info.today,\n      locale: info.locale,\n      originNode: /*#__PURE__*/React.createElement(\"div\", {\n        className: \"\".concat(cellPrefixCls, \"-inner\")\n      }, unit.label),\n      type: 'time',\n      subType: type\n    }) : /*#__PURE__*/React.createElement(\"div\", {\n      className: \"\".concat(cellPrefixCls, \"-inner\")\n    }, unit.label));\n  }));\n}\nexport default TimeUnitColumn;"], "mappings": "AAAA,OAAOA,eAAe,MAAM,2CAA2C;AACvE,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,MAAM,EAAEC,eAAe,QAAQ,OAAO;AAC/C,OAAOC,UAAU,MAAM,YAAY;AACnC,SAASC,QAAQ,EAAEC,gBAAgB,QAAQ,oBAAoB;AAC/D,OAAOC,YAAY,MAAM,oBAAoB;AAC7C,SAASC,cAAcA,CAACC,KAAK,EAAE;EAC7B,IAAIC,SAAS,GAAGD,KAAK,CAACC,SAAS;IAC7BC,KAAK,GAAGF,KAAK,CAACE,KAAK;IACnBC,QAAQ,GAAGH,KAAK,CAACG,QAAQ;IACzBC,KAAK,GAAGJ,KAAK,CAACI,KAAK;IACnBC,MAAM,GAAGL,KAAK,CAACK,MAAM;IACrBC,mBAAmB,GAAGN,KAAK,CAACM,mBAAmB;IAC/CC,IAAI,GAAGP,KAAK,CAACO,IAAI;IACjBC,IAAI,GAAGR,KAAK,CAACQ,IAAI;EACnB,IAAIC,aAAa,GAAG,EAAE,CAACC,MAAM,CAACT,SAAS,EAAE,OAAO,CAAC;EACjD,IAAIU,iBAAiB,GAAGnB,KAAK,CAACoB,UAAU,CAACd,YAAY,CAAC;IACpDe,IAAI,GAAGF,iBAAiB,CAACE,IAAI;EAC/B,IAAIC,KAAK,GAAGrB,MAAM,CAAC,IAAI,CAAC;EACxB,IAAIsB,MAAM,GAAGtB,MAAM,CAAC,IAAIuB,GAAG,CAAC,CAAC,CAAC;EAC9B,IAAIC,SAAS,GAAGxB,MAAM,CAAC,CAAC;;EAExB;EACAC,eAAe,CAAC,YAAY;IAC1B,IAAIwB,EAAE,GAAGH,MAAM,CAACI,OAAO,CAACC,GAAG,CAAChB,KAAK,CAAC;IAClC,IAAIc,EAAE,IAAIL,IAAI,KAAK,KAAK,EAAE;MACxBjB,QAAQ,CAACkB,KAAK,CAACK,OAAO,EAAED,EAAE,CAACG,SAAS,EAAE,GAAG,CAAC;IAC5C;EACF,CAAC,EAAE,CAACjB,KAAK,CAAC,CAAC;EACXV,eAAe,CAAC,YAAY;IAC1B,IAAImB,IAAI,EAAE;MACR,IAAIK,EAAE,GAAGH,MAAM,CAACI,OAAO,CAACC,GAAG,CAAChB,KAAK,CAAC;MAClC,IAAIc,EAAE,EAAE;QACND,SAAS,CAACE,OAAO,GAAGtB,gBAAgB,CAACqB,EAAE,EAAE,YAAY;UACnDtB,QAAQ,CAACkB,KAAK,CAACK,OAAO,EAAED,EAAE,CAACG,SAAS,EAAE,CAAC,CAAC;QAC1C,CAAC,CAAC;MACJ;IACF;IACA,OAAO,YAAY;MACjB,IAAIC,kBAAkB;MACtB,CAACA,kBAAkB,GAAGL,SAAS,CAACE,OAAO,MAAM,IAAI,IAAIG,kBAAkB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,kBAAkB,CAACC,IAAI,CAACN,SAAS,CAAC;IAClI,CAAC;EACH,CAAC,EAAE,CAACJ,IAAI,CAAC,CAAC;EACV,OAAO,aAAarB,KAAK,CAACgC,aAAa,CAAC,IAAI,EAAE;IAC5CC,SAAS,EAAE9B,UAAU,CAAC,EAAE,CAACe,MAAM,CAACT,SAAS,EAAE,SAAS,CAAC,EAAEV,eAAe,CAAC,CAAC,CAAC,EAAE,EAAE,CAACmB,MAAM,CAACT,SAAS,EAAE,gBAAgB,CAAC,EAAEI,MAAM,CAAC,CAAC;IAC3HqB,GAAG,EAAEZ,KAAK;IACVa,KAAK,EAAE;MACLC,QAAQ,EAAE;IACZ;EACF,CAAC,EAAE1B,KAAK,CAAC2B,GAAG,CAAC,UAAUC,IAAI,EAAE;IAC3B,IAAIC,YAAY;IAChB,IAAIzB,mBAAmB,IAAIwB,IAAI,CAACE,QAAQ,EAAE;MACxC,OAAO,IAAI;IACb;IACA,OAAO,aAAaxC,KAAK,CAACgC,aAAa,CAAC,IAAI,EAAE;MAC5CS,GAAG,EAAEH,IAAI,CAAC1B,KAAK;MACfsB,GAAG,EAAE,SAASA,GAAGA,CAACQ,OAAO,EAAE;QACzBnB,MAAM,CAACI,OAAO,CAACgB,GAAG,CAACL,IAAI,CAAC1B,KAAK,EAAE8B,OAAO,CAAC;MACzC,CAAC;MACDT,SAAS,EAAE9B,UAAU,CAACc,aAAa,GAAGsB,YAAY,GAAG,CAAC,CAAC,EAAExC,eAAe,CAACwC,YAAY,EAAE,EAAE,CAACrB,MAAM,CAACD,aAAa,EAAE,WAAW,CAAC,EAAEqB,IAAI,CAACE,QAAQ,CAAC,EAAEzC,eAAe,CAACwC,YAAY,EAAE,EAAE,CAACrB,MAAM,CAACD,aAAa,EAAE,WAAW,CAAC,EAAEL,KAAK,KAAK0B,IAAI,CAAC1B,KAAK,CAAC,EAAE2B,YAAY,CAAC,CAAC;MACxPK,OAAO,EAAE,SAASA,OAAOA,CAAA,EAAG;QAC1B,IAAIN,IAAI,CAACE,QAAQ,EAAE;UACjB;QACF;QACA7B,QAAQ,CAAC2B,IAAI,CAAC1B,KAAK,CAAC;MACtB;IACF,CAAC,EAAEG,IAAI,CAAC8B,UAAU,GAAG9B,IAAI,CAAC8B,UAAU,CAACP,IAAI,CAAC1B,KAAK,EAAE;MAC/CkC,KAAK,EAAE/B,IAAI,CAAC+B,KAAK;MACjBC,MAAM,EAAEhC,IAAI,CAACgC,MAAM;MACnBC,UAAU,EAAE,aAAahD,KAAK,CAACgC,aAAa,CAAC,KAAK,EAAE;QAClDC,SAAS,EAAE,EAAE,CAACf,MAAM,CAACD,aAAa,EAAE,QAAQ;MAC9C,CAAC,EAAEqB,IAAI,CAACW,KAAK,CAAC;MACdjC,IAAI,EAAE,MAAM;MACZkC,OAAO,EAAElC;IACX,CAAC,CAAC,GAAG,aAAahB,KAAK,CAACgC,aAAa,CAAC,KAAK,EAAE;MAC3CC,SAAS,EAAE,EAAE,CAACf,MAAM,CAACD,aAAa,EAAE,QAAQ;IAC9C,CAAC,EAAEqB,IAAI,CAACW,KAAK,CAAC,CAAC;EACjB,CAAC,CAAC,CAAC;AACL;AACA,eAAe1C,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}