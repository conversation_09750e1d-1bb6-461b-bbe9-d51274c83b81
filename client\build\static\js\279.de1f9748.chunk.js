"use strict";(self.webpackChunkclient=self.webpackChunkclient||[]).push([[279],{2279:(e,t,s)=>{s.r(t),s.d(t,{default:()=>y});var i=s(2791),a=s(7689),l=s(6042),n=s(1413),r=s(5526),o=s(7027),c=s(3371);var d=s(184);const x=e=>{var t,s,a,x,m,u,p,h,g,b,y,v,j,f,N,w;let{trialUserInfo:T,onQuizSelected:k,onBack:S}=e;const[C,A]=(0,i.useState)(!0),[z,E]=(0,i.useState)(null),[q,R]=(0,i.useState)(null);(0,i.useEffect)((()=>{Q()}),[T]);const Q=async()=>{try{A(!0),R(null);const e=await(async e=>{try{return(await c.default.post("/api/trial/get-trial-quiz",e)).data}catch(q){var t;return(null===(t=q.response)||void 0===t?void 0:t.data)||{success:!1,message:"Network error"}}})({level:T.level,class:T.class});e.success?(E(e.data),console.log("\u2705 Trial quiz loaded:",e.data)):(R(e.message||"Failed to load trial quiz"),o.ZP.error(e.message||"Failed to load trial quiz"))}catch(q){console.error("\u274c Error fetching trial quiz:",q),R("Something went wrong. Please try again."),o.ZP.error("Something went wrong. Please try again.")}finally{A(!1)}},W={hidden:{opacity:0,y:20},visible:{opacity:1,y:0,transition:{duration:.6,ease:"easeOut"}}};return C?(0,d.jsx)(l.E.div,{className:"min-h-screen bg-gradient-to-br from-blue-50 via-white to-blue-50 flex items-center justify-center p-4",variants:W,initial:"hidden",animate:"visible",children:(0,d.jsxs)("div",{className:"text-center",children:[(0,d.jsx)("div",{className:"w-16 h-16 mx-auto mb-4 bg-blue-100 rounded-full flex items-center justify-center",children:(0,d.jsx)(r.cIg,{className:"w-8 h-8 text-blue-600 animate-spin"})}),(0,d.jsx)("h2",{className:"text-xl font-semibold text-gray-800 mb-2",children:"Finding Your Perfect Trial Quiz"}),(0,d.jsx)("p",{className:"text-gray-600",children:"We're selecting a quiz that matches your level and class..."})]})}):q?(0,d.jsx)(l.E.div,{className:"min-h-screen bg-gradient-to-br from-blue-50 via-white to-blue-50 flex items-center justify-center p-4",variants:W,initial:"hidden",animate:"visible",children:(0,d.jsxs)("div",{className:"max-w-md w-full bg-white rounded-2xl shadow-xl p-8 text-center",children:[(0,d.jsx)("div",{className:"w-16 h-16 mx-auto mb-4 bg-red-100 rounded-full flex items-center justify-center",children:(0,d.jsx)(r.pBF,{className:"w-8 h-8 text-red-600"})}),(0,d.jsx)("h2",{className:"text-xl font-semibold text-gray-800 mb-2",children:"Quiz Not Available"}),(0,d.jsx)("p",{className:"text-gray-600 mb-6",children:q}),(0,d.jsxs)("div",{className:"space-y-3",children:[(0,d.jsx)("button",{onClick:Q,className:"w-full py-3 px-6 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors font-medium",children:"Try Again"}),(0,d.jsx)("button",{onClick:S,className:"w-full py-3 px-6 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors font-medium",children:"Go Back"})]})]})}):(0,d.jsx)(l.E.div,{className:"min-h-screen bg-gradient-to-br from-blue-50 via-white to-blue-50 flex items-center justify-center p-4",variants:W,initial:"hidden",animate:"visible",children:(0,d.jsxs)("div",{className:"max-w-2xl w-full mx-auto",children:[(0,d.jsxs)(l.E.div,{className:"text-center mb-6 sm:mb-8",initial:{opacity:0,y:-20},animate:{opacity:1,y:0},transition:{duration:.6},children:[(0,d.jsxs)("h1",{className:"text-2xl sm:text-3xl md:text-4xl font-bold text-gray-800 mb-2",children:["Welcome, ",T.name,"! \ud83d\udc4b"]}),(0,d.jsxs)("p",{className:"text-base sm:text-lg text-gray-600 px-4",children:["We've found the perfect quiz for your ",(0,d.jsxs)("span",{className:"font-semibold text-blue-600",children:[T.level," ",T.class]})," level"]})]}),(0,d.jsxs)(l.E.div,{className:"bg-white rounded-2xl shadow-xl overflow-hidden",variants:{hidden:{opacity:0,scale:.9},visible:{opacity:1,scale:1,transition:{duration:.5,delay:.2}}},initial:"hidden",animate:"visible",children:[(0,d.jsx)("div",{className:"bg-gradient-to-r from-blue-600 to-blue-700 px-4 sm:px-6 py-6 sm:py-8 text-white",children:(0,d.jsxs)("div",{className:"flex items-center space-x-3 sm:space-x-4",children:[(0,d.jsx)("div",{className:"p-2 sm:p-3 bg-white/20 rounded-xl",children:(0,d.jsx)(r.Kkf,{className:"w-6 h-6 sm:w-8 sm:h-8"})}),(0,d.jsxs)("div",{className:"min-w-0 flex-1",children:[(0,d.jsx)("h2",{className:"text-lg sm:text-2xl font-bold truncate",children:null===z||void 0===z||null===(t=z.exam)||void 0===t?void 0:t.name}),(0,d.jsxs)("p",{className:"text-blue-100 text-sm sm:text-base",children:[null===z||void 0===z||null===(s=z.exam)||void 0===s?void 0:s.subject," \u2022 ",T.level.charAt(0).toUpperCase()+T.level.slice(1)," Level"]})]})]})}),(0,d.jsxs)("div",{className:"p-4 sm:p-6",children:[(0,d.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-3 gap-4 sm:gap-6 mb-6 sm:mb-8",children:[(0,d.jsxs)("div",{className:"text-center p-4 bg-blue-50 rounded-xl",children:[(0,d.jsx)(r.pBF,{className:"w-8 h-8 text-blue-600 mx-auto mb-2"}),(0,d.jsx)("div",{className:"text-2xl font-bold text-gray-800",children:(null===z||void 0===z||null===(a=z.trialInfo)||void 0===a?void 0:a.trialQuestionCount)||(null===z||void 0===z||null===(x=z.exam)||void 0===x||null===(m=x.questions)||void 0===m?void 0:m.length)||0}),(0,d.jsx)("div",{className:"text-sm text-gray-600",children:"Questions"})]}),(0,d.jsxs)("div",{className:"text-center p-4 bg-green-50 rounded-xl",children:[(0,d.jsx)(r.rfE,{className:"w-8 h-8 text-green-600 mx-auto mb-2"}),(0,d.jsx)("div",{className:"text-2xl font-bold text-gray-800",children:(null===z||void 0===z||null===(u=z.trialInfo)||void 0===u?void 0:u.trialDuration)||(null===z||void 0===z||null===(p=z.exam)||void 0===p?void 0:p.duration)||0}),(0,d.jsx)("div",{className:"text-sm text-gray-600",children:"Minutes"})]}),(0,d.jsxs)("div",{className:"text-center p-4 bg-purple-50 rounded-xl",children:[(0,d.jsx)(r.Kkf,{className:"w-8 h-8 text-purple-600 mx-auto mb-2"}),(0,d.jsx)("div",{className:"text-2xl font-bold text-gray-800",children:(null===z||void 0===z||null===(h=z.exam)||void 0===h?void 0:h.passingMarks)||Math.ceil(.6*((null===z||void 0===z||null===(g=z.exam)||void 0===g||null===(b=g.questions)||void 0===b?void 0:b.length)||0))}),(0,d.jsx)("div",{className:"text-sm text-gray-600",children:"Pass Mark"})]})]}),(0,d.jsx)("div",{className:"bg-gray-50 border border-gray-200 rounded-xl p-4 mb-6",children:(0,d.jsxs)("div",{className:"flex flex-wrap items-center justify-center gap-4 text-sm text-gray-700",children:[(0,d.jsxs)("div",{className:"flex items-center",children:[(0,d.jsx)(r.rfE,{className:"w-4 h-4 mr-2 text-blue-600"}),(0,d.jsxs)("span",{children:[(0,d.jsx)("strong",{children:(null===z||void 0===z||null===(y=z.trialInfo)||void 0===y?void 0:y.trialDuration)||(null===z||void 0===z||null===(v=z.exam)||void 0===v?void 0:v.duration)||0})," minutes"]})]}),(0,d.jsxs)("div",{className:"flex items-center",children:[(0,d.jsx)(r.pBF,{className:"w-4 h-4 mr-2 text-green-600"}),(0,d.jsxs)("span",{children:[(0,d.jsx)("strong",{children:(null===z||void 0===z||null===(j=z.trialInfo)||void 0===j?void 0:j.trialQuestionCount)||5})," questions"]})]}),(0,d.jsxs)("div",{className:"flex items-center",children:[(0,d.jsx)(r.Kkf,{className:"w-4 h-4 mr-2 text-purple-600"}),(0,d.jsxs)("span",{children:["Pass mark: ",(0,d.jsx)("strong",{children:(null===z||void 0===z||null===(f=z.exam)||void 0===f?void 0:f.passingMarks)||Math.ceil(.6*((null===z||void 0===z||null===(N=z.exam)||void 0===N||null===(w=N.questions)||void 0===w?void 0:w.length)||0))})]})]}),(0,d.jsxs)("div",{className:"flex items-center",children:[(0,d.jsx)(r.CR0,{className:"w-4 h-4 mr-2 text-orange-600"}),(0,d.jsx)("span",{children:"Click answers to select"})]})]})}),(0,d.jsxs)("div",{className:"flex flex-col sm:flex-row gap-3",children:[(0,d.jsx)("button",{onClick:S,className:"flex-1 py-3 px-6 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors font-medium",children:"Go Back"}),(0,d.jsxs)(l.E.button,{onClick:()=>{z&&z.exam&&k((0,n.Z)((0,n.Z)({},z),{},{trialUserInfo:T}))},className:"flex-1 py-3 px-6 bg-gradient-to-r from-blue-600 to-blue-700 text-white rounded-lg hover:from-blue-700 hover:to-blue-800 transition-all font-medium flex items-center justify-center space-x-2 shadow-lg hover:shadow-xl",whileHover:{scale:1.02},whileTap:{scale:.98},children:[(0,d.jsx)("span",{children:"Start Trial Quiz"}),(0,d.jsx)(r.CR0,{className:"w-5 h-5"})]})]})]})]}),(0,d.jsx)(l.E.div,{className:"text-center mt-6 text-sm text-gray-500",initial:{opacity:0},animate:{opacity:1},transition:{delay:.8},children:"After completing this trial, you'll be invited to register for full access to our platform"})]})})},m=e=>{let{quizData:t,onComplete:s,onBack:a}=e;const{exam:x,trialUserInfo:m}=t,u=x.questions||[],[p,h]=(0,i.useState)(0),[g,b]=(0,i.useState)({}),[y,v]=(0,i.useState)(x.duration||180),[j,f]=(0,i.useState)(!1),[N]=(0,i.useState)(Date.now());(0,i.useEffect)((()=>{if(y<=0)return void T();const e=setInterval((()=>{v((e=>e-1))}),1e3);return()=>clearInterval(e)}),[y]);const w=(e,t)=>{S(),b((s=>(0,n.Z)((0,n.Z)({},s),{},{[e]:t})))},T=(0,i.useCallback)((async()=>{if(!j){C(),f(!0);try{const e=Math.round((Date.now()-N)/1e3),t=await(async e=>{try{return(await c.default.post("/api/trial/submit-trial-result",e)).data}catch(s){var t;return(null===(t=s.response)||void 0===t?void 0:t.data)||{success:!1,message:"Network error"}}})({examId:x._id,answers:g,timeSpent:e,trialUserInfo:m});t.success?s(t.data):(o.ZP.error(t.message||"Failed to submit quiz"),f(!1))}catch(e){console.error("\u274c Error submitting trial quiz:",e),o.ZP.error("Something went wrong. Please try again."),f(!1)}}}),[x._id,g,m,N,s,j]),k=()=>{try{const e=new(window.AudioContext||window.webkitAudioContext),t=e.createOscillator(),s=e.createGain();t.connect(s),s.connect(e.destination),t.frequency.setValueAtTime(800,e.currentTime),t.frequency.exponentialRampToValueAtTime(400,e.currentTime+.1),s.gain.setValueAtTime(.1,e.currentTime),s.gain.exponentialRampToValueAtTime(.01,e.currentTime+.1),t.start(e.currentTime),t.stop(e.currentTime+.1)}catch(e){console.log("Navigation sound not available")}},S=()=>{try{const e=new(window.AudioContext||window.webkitAudioContext),t=e.createOscillator(),s=e.createGain();t.connect(s),s.connect(e.destination),t.frequency.setValueAtTime(523,e.currentTime),t.frequency.setValueAtTime(659,e.currentTime+.1),t.frequency.setValueAtTime(784,e.currentTime+.2),s.gain.setValueAtTime(.15,e.currentTime),s.gain.exponentialRampToValueAtTime(.01,e.currentTime+.3),t.start(e.currentTime),t.stop(e.currentTime+.3)}catch(e){console.log("Success sound not available")}},C=()=>{try{const e=new(window.AudioContext||window.webkitAudioContext),t=e.createOscillator(),s=e.createGain();t.connect(s),s.connect(e.destination),t.frequency.setValueAtTime(440,e.currentTime),t.frequency.setValueAtTime(554,e.currentTime+.15),t.frequency.setValueAtTime(659,e.currentTime+.3),t.frequency.setValueAtTime(880,e.currentTime+.45),s.gain.setValueAtTime(.2,e.currentTime),s.gain.exponentialRampToValueAtTime(.01,e.currentTime+.6),t.start(e.currentTime),t.stop(e.currentTime+.6)}catch(e){console.log("Submit sound not available")}};if(0===u.length)return(0,d.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 via-white to-blue-50 flex items-center justify-center p-4",children:(0,d.jsxs)("div",{className:"text-center",children:[(0,d.jsx)("h2",{className:"text-xl font-semibold text-gray-800 mb-2",children:"No Questions Available"}),(0,d.jsx)("p",{className:"text-gray-600 mb-4",children:"This quiz doesn't have any questions."}),(0,d.jsx)("button",{onClick:a,className:"py-2 px-4 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:"Go Back"})]})});const A=u[p],z=p===u.length-1,E=Object.keys(g).length,q=(null===A||void 0===A?void 0:A.type)||(null===A||void 0===A?void 0:A.answerType)||"mcq",R="mcq"===q||"Options"===q||"multiple-choice"===q,Q="fill"===q||"Fill in the Blank"===q||"Free Text"===q,W="diagram"===q||"Diagram"===q||(null===A||void 0===A?void 0:A.image);let B=[];return R&&(B=Array.isArray(null===A||void 0===A?void 0:A.options)?A.options:"object"===typeof(null===A||void 0===A?void 0:A.options)&&null!==(null===A||void 0===A?void 0:A.options)?Object.values(A.options):[null===A||void 0===A?void 0:A.optionA,null===A||void 0===A?void 0:A.optionB,null===A||void 0===A?void 0:A.optionC,null===A||void 0===A?void 0:A.optionD].filter(Boolean)),(0,d.jsxs)("div",{className:"trial-background",style:{minHeight:"100vh",background:"linear-gradient(135deg, #eff6ff 0%, #e0e7ff 100%)",width:"100%",overflow:"hidden"},children:[(0,d.jsx)("div",{className:"trial-header",style:{background:"white",boxShadow:"0 10px 25px rgba(0, 0, 0, 0.1)",borderBottom:"2px solid #e0e7ff",width:"100%",position:"sticky",top:0,zIndex:100},children:(0,d.jsxs)("div",{className:"trial-container",children:[(0,d.jsxs)("div",{style:{display:"flex",alignItems:"center",justifyContent:"space-between",marginBottom:"clamp(12px, 3vw, 20px)",flexWrap:"wrap",gap:"clamp(8px, 2vw, 16px)",width:"100%"},children:[(0,d.jsxs)("div",{style:{display:"flex",alignItems:"center",gap:"clamp(8px, 2vw, 16px)",minWidth:0,flex:1,maxWidth:"100%"},children:[(0,d.jsx)("button",{onClick:a,style:{padding:"clamp(8px, 2vw, 12px)",background:"rgba(59, 130, 246, 0.1)",border:"none",borderRadius:"clamp(8px, 2vw, 12px)",cursor:"pointer",transition:"all 0.3s ease",display:"flex",alignItems:"center",justifyContent:"center",minWidth:"40px",minHeight:"40px"},onMouseEnter:e=>e.target.style.background="rgba(59, 130, 246, 0.2)",onMouseLeave:e=>e.target.style.background="rgba(59, 130, 246, 0.1)",children:(0,d.jsx)(r.F4Y,{style:{width:"clamp(18px, 4vw, 24px)",height:"clamp(18px, 4vw, 24px)",color:"#2563eb"}})}),(0,d.jsxs)("div",{style:{minWidth:0,flex:1,overflow:"hidden"},children:[(0,d.jsx)("h1",{style:{fontSize:"clamp(16px, 4vw, 24px)",fontWeight:"bold",background:"linear-gradient(135deg, #2563eb 0%, #4f46e5 100%)",WebkitBackgroundClip:"text",WebkitTextFillColor:"transparent",margin:0,overflow:"hidden",textOverflow:"ellipsis",whiteSpace:"nowrap",lineHeight:1.2},children:x.name}),(0,d.jsxs)("div",{className:"flex items-center space-x-2 mt-1",children:[(0,d.jsx)("span",{className:"text-sm sm:text-base text-gray-600",children:x.subject}),(0,d.jsx)("span",{className:"text-gray-400",children:"\u2022"}),(0,d.jsxs)("span",{className:"text-sm sm:text-base font-medium text-blue-600",children:["Question ",p+1," of ",u.length]})]})]})]}),(0,d.jsxs)("div",{style:{display:"flex",alignItems:"center",gap:"clamp(8px, 2vw, 16px)",flexShrink:0,flexWrap:"wrap",justifyContent:"flex-end"},children:[(0,d.jsxs)("div",{style:{position:"relative"},children:[(0,d.jsxs)("div",{style:{display:"flex",alignItems:"center",gap:"8px",textShadow:"1px 1px 2px rgba(0,0,0,0.5)",boxShadow:y<=60?"0 0 20px rgba(239, 68, 68, 0.6), 0 4px 15px rgba(0,0,0,0.3)":"0 0 15px rgba(34, 197, 94, 0.4), 0 4px 15px rgba(0,0,0,0.3)",background:y<=60?"linear-gradient(to right, #ef4444, #dc2626)":"linear-gradient(to right, #22c55e, #16a34a)",color:"white",padding:"10px 16px",borderRadius:"12px",border:y<=60?"2px solid #fca5a5":"2px solid #86efac",animation:y<=60?"pulse 1s infinite":"none"},children:[(0,d.jsx)(r.rfE,{style:{width:"20px",height:"20px",filter:"drop-shadow(0 2px 4px rgba(0,0,0,0.3))",animation:y<=60?"bounce 1s infinite":"none"}}),(0,d.jsx)("span",{style:{fontFamily:"Monaco, Menlo, monospace",fontWeight:"900",fontSize:"18px",animation:y<=60?"pulse 1s infinite":"none"},children:(e=>{const t=Math.floor(e/60),s=e%60;return"".concat(t,":").concat(s.toString().padStart(2,"0"))})(y)})]}),y<=60&&(0,d.jsx)("div",{className:"timer-warning-ring"})]}),(0,d.jsx)("div",{className:"hidden sm:flex items-center space-x-2 px-3 py-2 bg-gray-100 rounded-xl",children:(0,d.jsxs)("span",{className:"text-sm font-medium text-gray-700",children:[E,"/",u.length," answered"]})})]})]}),(0,d.jsxs)("div",{className:"trial-progress-container",children:[(0,d.jsxs)("div",{className:"trial-progress-header",children:[(0,d.jsxs)("span",{className:"trial-progress-label",children:["Progress: ",p+1," of ",u.length]}),(0,d.jsxs)("span",{className:"trial-progress-percentage",children:[Math.round((p+1)/u.length*100),"%"]})]}),(0,d.jsx)("div",{className:"trial-progress-bar",children:(0,d.jsx)("div",{className:"trial-progress-fill",style:{width:"".concat((p+1)/u.length*100,"%")}})})]}),(0,d.jsx)("div",{className:"sm:hidden mt-3 flex items-center justify-center",children:(0,d.jsx)("div",{className:"flex items-center space-x-2 px-3 py-2 bg-gray-100 rounded-lg",children:(0,d.jsxs)("span",{className:"text-sm font-medium text-gray-700",children:[E,"/",u.length," answered"]})})})]})}),(0,d.jsx)("div",{className:"trial-container",style:{paddingTop:"clamp(16px, 4vw, 32px)",paddingBottom:"clamp(16px, 4vw, 32px)"},children:(0,d.jsxs)(l.E.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},exit:{opacity:0,y:-20},transition:{duration:.4,ease:"easeOut"},className:"trial-question-card",children:[(0,d.jsxs)("div",{className:"trial-question-header",children:[(0,d.jsxs)("div",{className:"trial-question-number",children:[(0,d.jsx)("div",{className:"trial-question-number-badge",children:(0,d.jsx)("span",{children:p+1})}),(0,d.jsxs)("div",{children:[(0,d.jsx)("div",{style:{color:"rgba(255, 255, 255, 0.9)",fontSize:"14px"},children:"Question"}),(0,d.jsxs)("div",{style:{color:"white",fontWeight:"500",fontSize:"18px"},children:[p+1," of ",u.length]})]})]}),(0,d.jsx)("h2",{className:"trial-question-title",children:A.name})]}),(0,d.jsxs)("div",{className:"trial-content",style:{padding:"clamp(16px, 4vw, 40px)",width:"100%",boxSizing:"border-box"},children:[A.image&&(0,d.jsx)("div",{style:{marginBottom:"32px",textAlign:"center"},children:(0,d.jsx)("img",{src:A.image,alt:"Question",style:{maxWidth:"100%",maxHeight:"400px",borderRadius:"16px",boxShadow:"0 8px 25px rgba(0, 0, 0, 0.1)"}})}),(0,d.jsx)("div",{className:"mb-10 sm:mb-12",children:R&&B.length>0?(0,d.jsxs)("div",{className:"space-y-4 sm:space-y-5",children:[(0,d.jsx)("h3",{className:"text-lg sm:text-xl font-semibold text-gray-800 mb-6",children:"Choose your answer:"}),B.map(((e,t)=>{const s=String.fromCharCode(65+t),i=g[A._id]===s;return(0,d.jsx)(l.E.button,{onClick:()=>w(A._id,s),className:"trial-option ".concat(i?"selected":""),whileHover:{scale:i?1.01:1.005},whileTap:{scale:.995},children:(0,d.jsxs)("div",{className:"trial-option-content",children:[(0,d.jsx)("div",{className:"trial-option-letter",children:i?(0,d.jsx)(r.e6w,{style:{width:"24px",height:"24px"}}):s}),(0,d.jsx)("span",{className:"trial-option-text",children:e})]})},t)}))]}):Q?(0,d.jsxs)("div",{children:[(0,d.jsx)("h3",{style:{fontSize:"20px",fontWeight:"600",color:"#374151",marginBottom:"24px"},children:"\ud83d\udcad Type your answer:"}),(0,d.jsxs)("div",{style:{position:"relative"},children:[(0,d.jsx)("input",{type:"text",value:g[A._id]||"",onChange:e=>w(A._id,e.target.value),placeholder:"Type your answer here...",className:"trial-input",style:{paddingRight:"60px"}}),(0,d.jsx)("div",{style:{position:"absolute",right:"20px",top:"50%",transform:"translateY(-50%)",fontSize:"24px",color:"#9ca3af"},children:"\u270f\ufe0f"})]}),(0,d.jsx)("div",{style:{marginTop:"12px",fontSize:"14px",color:"#6b7280",fontStyle:"italic"},children:"\ud83d\udca1 Tip: Be specific and clear in your answer"})]}):W?(0,d.jsxs)("div",{children:[(0,d.jsx)("h3",{style:{fontSize:"20px",fontWeight:"600",color:"#374151",marginBottom:"24px"},children:"\ud83d\udd0d Study the diagram and answer:"}),(0,d.jsxs)("div",{style:{position:"relative"},children:[(0,d.jsx)("input",{type:"text",value:g[A._id]||"",onChange:e=>w(A._id,e.target.value),placeholder:"Analyze the diagram and type your answer...",className:"trial-input",style:{paddingRight:"60px"}}),(0,d.jsx)("div",{style:{position:"absolute",right:"20px",top:"50%",transform:"translateY(-50%)",fontSize:"24px",color:"#9ca3af"},children:"\ud83d\udcca"})]}),(0,d.jsx)("div",{style:{marginTop:"12px",fontSize:"14px",color:"#6b7280",fontStyle:"italic"},children:"\ud83c\udfaf Tip: Look carefully at all parts of the diagram"})]}):(0,d.jsxs)("div",{children:[(0,d.jsx)("h3",{style:{fontSize:"20px",fontWeight:"600",color:"#374151",marginBottom:"24px"},children:"\ud83d\udcdd Provide your answer:"}),(0,d.jsx)("div",{style:{position:"relative"},children:(0,d.jsx)("input",{type:"text",value:g[A._id]||"",onChange:e=>w(A._id,e.target.value),placeholder:"Type your answer here...",className:"trial-input"})})]})}),(0,d.jsxs)("div",{className:"trial-nav-buttons",children:[(0,d.jsxs)(l.E.button,{onClick:()=>{p>0&&(k(),h(p-1))},disabled:0===p,className:"trial-btn trial-btn-secondary ".concat(""),whileHover:p>0?{scale:1.02}:{},whileTap:p>0?{scale:.98}:{},children:[(0,d.jsx)(r.F4Y,{style:{width:"20px",height:"20px"}}),(0,d.jsx)("span",{children:"Previous"})]}),z?(0,d.jsxs)(l.E.button,{onClick:T,disabled:j,className:"trial-btn ".concat(j?"trial-btn-secondary":"trial-btn-success"),whileHover:j?{}:{scale:1.02},whileTap:j?{}:{scale:.98},children:[j?(0,d.jsx)("div",{className:"trial-spinner"}):(0,d.jsx)(r.e6w,{style:{width:"20px",height:"20px"}}),(0,d.jsx)("span",{children:j?"Submitting...":"Submit Quiz"})]}):(0,d.jsxs)(l.E.button,{onClick:()=>{p<u.length-1&&(k(),h(p+1))},className:"trial-btn trial-btn-primary",whileHover:{scale:1.02},whileTap:{scale:.98},children:[(0,d.jsx)("span",{children:"Next Question"}),(0,d.jsx)(r.CR0,{style:{width:"20px",height:"20px"}})]})]})]})]},p)}),(0,d.jsx)("div",{style:{position:"fixed",bottom:"clamp(12px, 3vw, 20px)",right:"clamp(12px, 3vw, 20px)",background:"#2563eb",color:"white",padding:"clamp(6px, 1.5vw, 8px) clamp(12px, 3vw, 16px)",borderRadius:"20px",fontSize:"clamp(10px, 2.5vw, 12px)",fontWeight:"500",boxShadow:"0 4px 12px rgba(37, 99, 235, 0.3)",zIndex:1e3,userSelect:"none",pointerEvents:"none"},children:"Trial Mode"})]})};var u=s(1087),p=s(6584);const h=e=>{var t;let{result:s,onTryAnother:a,onRegister:n}=e;const[o,c]=(0,i.useState)(!1),[x,m]=(0,i.useState)(!1),[h,g]=(0,i.useState)(!1),[b,y]=(0,i.useState)(!1);(0,i.useEffect)((()=>{s.percentage>=60?setTimeout((()=>{(0,p.Z)({particleCount:100,spread:70,origin:{y:.6}});try{const e=new Audio("/sounds/clap.mp3");e.volume=.3,e.play().catch((()=>{}))}catch(e){console.log("Sound not available")}g(!0),setTimeout((()=>g(!1)),500)}),1e3):setTimeout((()=>{g(!0),y(!0);try{const e=new(window.AudioContext||window.webkitAudioContext),t=e.createOscillator(),s=e.createGain();t.connect(s),s.connect(e.destination),t.frequency.setValueAtTime(400,e.currentTime),t.frequency.exponentialRampToValueAtTime(200,e.currentTime+.5),t.frequency.exponentialRampToValueAtTime(100,e.currentTime+1),s.gain.setValueAtTime(.15,e.currentTime),s.gain.exponentialRampToValueAtTime(.01,e.currentTime+1),t.start(e.currentTime),t.stop(e.currentTime+1)}catch(e){console.log("Failure sound not available")}setTimeout((()=>{g(!1),y(!1)}),800)}),1e3)}),[s.percentage]);const v=(j=s.percentage)>=90?{message:"Outstanding Performance! \ud83c\udf1f",color:"text-purple-600",bg:"bg-purple-50",gradient:"from-purple-500 to-purple-600"}:j>=80?{message:"Excellent Work! \ud83c\udf89",color:"text-green-600",bg:"bg-green-50",gradient:"from-green-500 to-green-600"}:j>=70?{message:"Great Job! \ud83d\udc4f",color:"text-blue-600",bg:"bg-blue-50",gradient:"from-blue-500 to-blue-600"}:j>=60?{message:"Well Done! \u2728",color:"text-emerald-600",bg:"bg-emerald-50",gradient:"from-emerald-500 to-emerald-600"}:j>=40?{message:"Good Effort! \ud83d\udcaa",color:"text-yellow-600",bg:"bg-yellow-50",gradient:"from-yellow-500 to-yellow-600"}:{message:"Keep Practicing! \ud83d\udcda",color:"text-orange-600",bg:"bg-orange-50",gradient:"from-orange-500 to-orange-600"};var j;const f=s.percentage>=60,N=[{icon:r.NQR,title:"Study Materials",description:"Access comprehensive study materials, notes, and resources"},{icon:r.Kkf,title:"AI Assistant",description:"Get personalized explanations and study recommendations"},{icon:r.hWk,title:"Ranking System",description:"Compete with other students and track your progress"},{icon:r.YsT,title:"Forum Access",description:"Ask questions and help other students in our community"},{icon:r.HLl,title:"Unlimited Quizzes",description:"Take as many quizzes as you want across all subjects"},{icon:r.jsT,title:"Progress Tracking",description:"Detailed analytics and performance insights"}];return(0,d.jsxs)("div",{className:"trial-background",style:{minHeight:"100vh",background:"linear-gradient(135deg, #eff6ff 0%, #ffffff 50%, #eff6ff 100%)",position:"relative"},children:[h&&(0,d.jsx)("div",{className:"flash-animation ".concat(s.percentage>=60?"flash-success":"flash-failure")}),(0,d.jsxs)("div",{className:"trial-container",style:{position:"relative",zIndex:10,padding:"clamp(12px, 3vw, 32px)",width:"100%",maxWidth:"100%",boxSizing:"border-box"},children:[(0,d.jsxs)(l.E.div,{initial:{opacity:0,y:-30},animate:{opacity:1,y:0},transition:{duration:.8,ease:"easeOut"},className:"text-center mb-8 sm:mb-12",children:[(0,d.jsx)(l.E.div,{initial:{scale:0},animate:{scale:1},transition:{duration:.6,delay:.3,type:"spring",stiffness:200},className:"inline-flex items-center justify-center rounded-full bg-gradient-to-r ".concat(v.gradient," mb-6 shadow-lg ").concat(b?"trial-fail-shake":""),style:{width:"clamp(64px, 15vw, 112px)",height:"clamp(64px, 15vw, 112px)"},onAnimationComplete:()=>m(!0),children:(0,d.jsx)(r.gBl,{className:"text-white",style:{width:"clamp(32px, 8vw, 56px)",height:"clamp(32px, 8vw, 56px)"}})}),(0,d.jsx)(l.E.h1,{initial:{opacity:0},animate:{opacity:1},transition:{duration:.6,delay:.6},className:"font-bold text-gray-900 mb-4 ".concat(b?"trial-fail-glow":""),style:{fontSize:"clamp(24px, 6vw, 48px)"},children:"Quiz Complete! \ud83c\udf89"}),(0,d.jsx)(l.E.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6,delay:.8},className:"inline-block px-6 py-3 rounded-full ".concat(v.bg," border-2 border-").concat(v.color.split("-")[1],"-200"),children:(0,d.jsx)("p",{className:"text-xl sm:text-2xl font-bold ".concat(v.color),children:v.message})})]}),(0,d.jsxs)(l.E.div,{initial:{opacity:0,scale:.8},animate:{opacity:1,scale:1},transition:{duration:.6,delay:1},className:"bg-white rounded-3xl shadow-2xl border border-gray-100 overflow-hidden mb-8 sm:mb-12",children:[(0,d.jsx)("div",{className:"bg-gradient-to-r ".concat(v.gradient," px-6 sm:px-8 lg:px-10 py-6 sm:py-8"),children:(0,d.jsxs)("div",{className:"text-center",children:[(0,d.jsxs)(l.E.div,{initial:{scale:0},animate:{scale:1},transition:{duration:.8,delay:1.2,type:"spring"},className:"font-bold text-white mb-2 ".concat(b?"trial-fail-bounce":""),style:{fontSize:"clamp(48px, 12vw, 96px)"},children:[s.percentage,"%"]}),(0,d.jsx)("div",{className:"text-white/90 text-lg sm:text-xl",children:"Your Score"})]})}),(0,d.jsxs)("div",{className:"px-6 sm:px-8 lg:px-10 py-8 sm:py-10",children:[(0,d.jsx)("div",{className:"grid grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6 mb-8",children:[{label:"Total Questions",value:s.totalQuestions,icon:r.NQR,color:"blue",delay:1.4},{label:"Correct Answers",value:s.correctAnswers,icon:r.e6w,color:"green",delay:1.6},{label:"Wrong Answers",value:s.wrongAnswers,icon:r.lhV,color:"red",delay:1.8},{label:"Time Taken",value:(e=>{const t=Math.floor(e/60),s=e%60;return"".concat(t,"m ").concat(s,"s")})(s.timeSpent),icon:r.rfE,color:"purple",delay:2}].map(((e,t)=>(0,d.jsxs)(l.E.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.5,delay:e.delay},className:"p-4 sm:p-6 bg-".concat(e.color,"-50 rounded-2xl border border-").concat(e.color,"-100 text-center"),children:[(0,d.jsx)("div",{className:"w-12 h-12 mx-auto mb-3 bg-".concat(e.color,"-100 rounded-xl flex items-center justify-center"),children:(0,d.jsx)(e.icon,{className:"w-6 h-6 text-".concat(e.color,"-600")})}),(0,d.jsx)("div",{className:"text-2xl sm:text-3xl font-bold text-".concat(e.color,"-600 mb-1"),children:e.value}),(0,d.jsx)("div",{className:"text-sm text-gray-600 font-medium",children:e.label})]},t)))}),(0,d.jsx)(l.E.div,{initial:{opacity:0,scale:.9},animate:{opacity:1,scale:1},transition:{duration:.5,delay:2.2},className:"text-center mb-8",children:(0,d.jsxs)("div",{className:"inline-flex items-center space-x-3 px-6 py-4 rounded-2xl text-lg font-semibold ".concat(f?"bg-green-100 text-green-700 border-2 border-green-200":"bg-red-100 text-red-700 border-2 border-red-200"),children:[f?(0,d.jsx)(r.e6w,{className:"w-6 h-6"}):(0,d.jsx)(r.lhV,{className:"w-6 h-6"}),(0,d.jsx)("span",{children:f?"\ud83c\udf89 Congratulations! You Passed!":"\ud83d\udcda Keep Studying! You Can Do Better!"})]})}),(0,d.jsx)(l.E.div,{initial:{opacity:0},animate:{opacity:1},transition:{duration:.5,delay:2.4},className:"text-center",children:(0,d.jsxs)("button",{onClick:()=>c(!o),className:"inline-flex items-center space-x-2 px-6 py-3 bg-gray-100 hover:bg-gray-200 text-gray-700 rounded-xl font-medium transition-all duration-200",children:[(0,d.jsx)(r.hWk,{className:"w-5 h-5"}),(0,d.jsx)("span",{children:o?"Hide Question Summary":"View Question Summary"})]})})]})]}),(0,d.jsx)(l.E.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6,delay:2.6},className:"text-center mb-8",children:(0,d.jsx)(u.rU,{to:"/register",children:(0,d.jsxs)(l.E.button,{className:"px-12 py-4 bg-gradient-to-r from-blue-600 to-blue-700 text-white rounded-2xl font-bold text-xl hover:from-blue-700 hover:to-blue-800 transition-all shadow-xl hover:shadow-2xl flex items-center justify-center space-x-3 mx-auto",whileHover:{scale:1.05,y:-2},whileTap:{scale:.98},children:[(0,d.jsx)("span",{children:"Register Now"}),(0,d.jsx)(r.CR0,{className:"w-6 h-6"})]})})}),(0,d.jsxs)(l.E.div,{initial:{opacity:0,y:30},animate:{opacity:1,y:0},transition:{duration:.8,delay:2.8},className:"bg-white rounded-3xl shadow-xl border border-gray-100 overflow-hidden mb-8",children:[(0,d.jsxs)("div",{className:"bg-gradient-to-r from-blue-600 to-purple-600 px-6 sm:px-8 py-6 text-white text-center",children:[(0,d.jsx)("h3",{className:"text-2xl sm:text-3xl font-bold mb-2",children:"\ud83d\udd13 Unlock These Amazing Features"}),(0,d.jsx)("p",{className:"text-blue-100 text-lg",children:"Join thousands of students already excelling with BrainWave"})]}),(0,d.jsxs)("div",{className:"p-6 sm:p-8",children:[(0,d.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:N.map(((e,t)=>(0,d.jsxs)(l.E.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.5,delay:3+.1*t},className:"group bg-gradient-to-br from-blue-50 to-purple-50 rounded-2xl p-6 border border-blue-100 hover:shadow-lg transition-all duration-300",children:[(0,d.jsxs)("div",{className:"flex items-center space-x-4 mb-4",children:[(0,d.jsx)("div",{className:"w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-500 rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300",children:(0,d.jsx)(e.icon,{className:"w-6 h-6 text-white"})}),(0,d.jsx)("h4",{className:"text-lg font-bold text-gray-800",children:e.title})]}),(0,d.jsx)("p",{className:"text-gray-600 leading-relaxed",children:e.description}),(0,d.jsxs)("div",{className:"mt-4 flex items-center text-blue-600 font-medium",children:[(0,d.jsx)(r.jsT,{className:"w-5 h-5 mr-2"}),(0,d.jsx)("span",{className:"text-sm",children:"Premium Feature"})]})]},t)))}),(0,d.jsxs)(l.E.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6,delay:3.4},className:"mt-8 bg-gradient-to-r from-purple-50 to-blue-50 rounded-2xl p-6 border border-purple-200",children:[(0,d.jsxs)("h4",{className:"text-xl font-bold text-gray-800 mb-6 text-center flex items-center justify-center",children:[(0,d.jsx)(r.Kkf,{className:"w-6 h-6 mr-2 text-purple-600"}),"Advanced Quiz Features & Maximum Control"]}),(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,d.jsxs)(l.E.div,{initial:{opacity:0,x:-20},animate:{opacity:1,x:0},transition:{duration:.5,delay:3.6},className:"bg-white rounded-xl p-5 shadow-sm border border-purple-100",children:[(0,d.jsxs)("div",{className:"flex items-center mb-3",children:[(0,d.jsx)("div",{className:"w-10 h-10 bg-gradient-to-r from-purple-500 to-blue-500 rounded-lg flex items-center justify-center mr-3",children:(0,d.jsx)(r.NQR,{className:"w-5 h-5 text-white"})}),(0,d.jsx)("h5",{className:"font-bold text-gray-800",children:"Multiple Subject Selection"})]}),(0,d.jsxs)("ul",{className:"text-sm text-gray-600 space-y-2",children:[(0,d.jsxs)("li",{className:"flex items-start",children:[(0,d.jsx)("span",{className:"text-purple-500 mr-2",children:"\u2022"}),(0,d.jsxs)("span",{children:["Choose from ",(0,d.jsx)("strong",{children:"15+ subjects"})," across all levels"]})]}),(0,d.jsxs)("li",{className:"flex items-start",children:[(0,d.jsx)("span",{className:"text-purple-500 mr-2",children:"\u2022"}),(0,d.jsx)("span",{children:"Mix and match subjects in custom quizzes"})]}),(0,d.jsxs)("li",{className:"flex items-start",children:[(0,d.jsx)("span",{className:"text-purple-500 mr-2",children:"\u2022"}),(0,d.jsx)("span",{children:"Subject-specific performance tracking"})]}),(0,d.jsxs)("li",{className:"flex items-start",children:[(0,d.jsx)("span",{className:"text-purple-500 mr-2",children:"\u2022"}),(0,d.jsx)("span",{children:"Cross-subject comparison analytics"})]})]})]}),(0,d.jsxs)(l.E.div,{initial:{opacity:0,x:20},animate:{opacity:1,x:0},transition:{duration:.5,delay:3.8},className:"bg-white rounded-xl p-5 shadow-sm border border-blue-100",children:[(0,d.jsxs)("div",{className:"flex items-center mb-3",children:[(0,d.jsx)("div",{className:"w-10 h-10 bg-gradient-to-r from-blue-500 to-green-500 rounded-lg flex items-center justify-center mr-3",children:(0,d.jsx)(r.sDK,{className:"w-5 h-5 text-white"})}),(0,d.jsx)("h5",{className:"font-bold text-gray-800",children:"Maximum Quiz Control"})]}),(0,d.jsxs)("ul",{className:"text-sm text-gray-600 space-y-2",children:[(0,d.jsxs)("li",{className:"flex items-start",children:[(0,d.jsx)("span",{className:"text-blue-500 mr-2",children:"\u2022"}),(0,d.jsxs)("span",{children:["Set custom ",(0,d.jsx)("strong",{children:"time limits"})," (5-180 minutes)"]})]}),(0,d.jsxs)("li",{className:"flex items-start",children:[(0,d.jsx)("span",{className:"text-blue-500 mr-2",children:"\u2022"}),(0,d.jsx)("span",{children:"Choose question count (5-100 questions)"})]}),(0,d.jsxs)("li",{className:"flex items-start",children:[(0,d.jsx)("span",{className:"text-blue-500 mr-2",children:"\u2022"}),(0,d.jsx)("span",{children:"Select difficulty levels (Easy, Medium, Hard)"})]}),(0,d.jsxs)("li",{className:"flex items-start",children:[(0,d.jsx)("span",{className:"text-blue-500 mr-2",children:"\u2022"}),(0,d.jsx)("span",{children:"Pause and resume quiz sessions"})]})]})]})]}),(0,d.jsxs)(l.E.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.5,delay:4},className:"mt-6 bg-gradient-to-r from-green-100 to-blue-100 rounded-xl p-5",children:[(0,d.jsx)("h5",{className:"font-bold text-gray-800 mb-4 text-center",children:"\ud83d\ude80 Advanced Quiz Features"}),(0,d.jsx)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4",children:[{icon:"\u23f1\ufe0f",title:"Smart Timer",desc:"Adaptive timing"},{icon:"\ud83c\udfaf",title:"Targeted Practice",desc:"Weak area focus"},{icon:"\ud83d\udcca",title:"Live Analytics",desc:"Real-time insights"},{icon:"\ud83c\udfc6",title:"Achievement System",desc:"Unlock rewards"}].map(((e,t)=>(0,d.jsxs)(l.E.div,{initial:{opacity:0,scale:.9},animate:{opacity:1,scale:1},transition:{duration:.3,delay:4.2+.1*t},className:"text-center p-3 bg-white rounded-lg shadow-sm",children:[(0,d.jsx)("div",{className:"text-xl mb-1",children:e.icon}),(0,d.jsx)("div",{className:"font-semibold text-xs text-gray-800",children:e.title}),(0,d.jsx)("div",{className:"text-xs text-gray-600",children:e.desc})]},t)))})]})]}),(0,d.jsxs)(l.E.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6,delay:3.6},className:"mt-8 bg-gradient-to-r from-green-50 to-blue-50 rounded-2xl p-6 border border-green-200",children:[(0,d.jsx)("h4",{className:"text-xl font-bold text-gray-800 mb-4 text-center",children:"\ud83c\udfaf Why Students Choose BrainWave"}),(0,d.jsx)("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4",children:[{icon:"\ud83d\ude80",title:"Instant Access",desc:"Start learning immediately"},{icon:"\ud83d\udcf1",title:"Mobile Friendly",desc:"Study anywhere, anytime"},{icon:"\ud83c\udf93",title:"Expert Content",desc:"Created by top educators"},{icon:"\ud83c\udfc6",title:"Proven Results",desc:"98% success rate"}].map(((e,t)=>(0,d.jsxs)(l.E.div,{initial:{opacity:0,scale:.9},animate:{opacity:1,scale:1},transition:{duration:.4,delay:3.8+.1*t},className:"text-center p-4 bg-white rounded-xl shadow-sm",children:[(0,d.jsx)("div",{className:"text-2xl mb-2",children:e.icon}),(0,d.jsx)("div",{className:"font-semibold text-gray-800 mb-1",children:e.title}),(0,d.jsx)("div",{className:"text-sm text-gray-600",children:e.desc})]},t)))})]}),(0,d.jsxs)(l.E.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6,delay:4.2},className:"text-center mt-8 p-6 bg-gradient-to-r from-blue-600 to-purple-600 rounded-2xl text-white",children:[(0,d.jsx)("h4",{className:"text-xl font-bold mb-2",children:"Ready to Excel? \ud83c\udf1f"}),(0,d.jsx)("p",{className:"text-blue-100 mb-4",children:"Join BrainWave today and unlock your full potential!"}),(0,d.jsxs)("div",{className:"flex flex-col sm:flex-row gap-3 justify-center items-center",children:[(0,d.jsx)(u.rU,{to:"/register",children:(0,d.jsxs)(l.E.button,{className:"px-8 py-3 bg-white text-blue-600 rounded-xl font-bold hover:bg-blue-50 transition-all shadow-lg flex items-center space-x-2",whileHover:{scale:1.05},whileTap:{scale:.98},children:[(0,d.jsx)("span",{children:"Create Free Account"}),(0,d.jsx)(r.CR0,{className:"w-5 h-5"})]})}),(0,d.jsx)("div",{className:"text-blue-200 text-sm",children:"\u2728 No credit card required \u2022 Start immediately"})]})]})]})]}),o&&(0,d.jsxs)(l.E.div,{initial:{opacity:0,height:0},animate:{opacity:1,height:"auto"},transition:{duration:.3},className:"bg-white rounded-2xl shadow-xl p-6 mb-8",children:[(0,d.jsx)("h3",{className:"text-xl font-bold text-gray-800 mb-4",children:"Question Review"}),(0,d.jsx)("div",{className:"space-y-4",children:null===(t=s.questionResults)||void 0===t?void 0:t.map(((e,t)=>(0,d.jsx)("div",{className:"p-4 rounded-lg border-2 ".concat(e.isCorrect?"border-green-200 bg-green-50":"border-red-200 bg-red-50"),children:(0,d.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,d.jsx)("div",{className:"w-8 h-8 rounded-full flex items-center justify-center ".concat(e.isCorrect?"bg-green-500 text-white":"bg-red-500 text-white"),children:e.isCorrect?(0,d.jsx)(r.e6w,{className:"w-4 h-4"}):(0,d.jsx)(r.lhV,{className:"w-4 h-4"})}),(0,d.jsxs)("div",{className:"flex-1",children:[(0,d.jsx)("p",{className:"font-medium text-gray-800 mb-2",children:e.question}),(0,d.jsxs)("div",{className:"text-sm space-y-1",children:[(0,d.jsxs)("p",{children:[(0,d.jsx)("span",{className:"text-gray-600",children:"Your answer:"}),(0,d.jsx)("span",{className:"ml-2 font-medium ".concat(e.isCorrect?"text-green-600":"text-red-600"),children:e.userAnswer||"Not answered"})]}),!e.isCorrect&&(0,d.jsxs)("p",{children:[(0,d.jsx)("span",{className:"text-gray-600",children:"Correct answer:"}),(0,d.jsx)("span",{className:"ml-2 font-medium text-green-600",children:e.correctAnswer})]})]})]})]})},t)))})]}),(0,d.jsxs)(l.E.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.5,delay:.6},className:"flex flex-col sm:flex-row gap-4 justify-center",children:[(0,d.jsx)("button",{onClick:a,className:"px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors font-medium",children:"Try Another Quiz"}),(0,d.jsx)(u.rU,{to:"/",children:(0,d.jsx)("button",{className:"px-6 py-3 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors font-medium",children:"Back to Home"})})]}),(0,d.jsx)("div",{className:"text-center mt-8",children:(0,d.jsx)("span",{className:"inline-block bg-yellow-100 text-yellow-800 px-4 py-2 rounded-full text-sm font-medium",children:"\ud83c\udfaf Trial Mode - Register for unlimited access"})})]})]})};var g=s(3791);const b=e=>{let{isOpen:t,onClose:s,trialResult:i}=e;const a=[{icon:r.$KY,title:"Unlimited Quizzes",description:"Access thousands of quizzes across all subjects and levels",color:"text-blue-600",bg:"bg-blue-50"},{icon:r.NQR,title:"Study Materials",description:"Comprehensive notes, videos, and resources for all subjects",color:"text-green-600",bg:"bg-green-50"},{icon:r.Kkf,title:"AI Assistant",description:"Get personalized explanations and study recommendations",color:"text-purple-600",bg:"bg-purple-50"},{icon:r.hWk,title:"Ranking System",description:"Compete with students nationwide and track your progress",color:"text-orange-600",bg:"bg-orange-50"},{icon:r.YsT,title:"Forum Access",description:"Ask questions and help other students in our community",color:"text-indigo-600",bg:"bg-indigo-50"},{icon:r.gBl,title:"Achievements",description:"Earn badges and rewards for your learning milestones",color:"text-yellow-600",bg:"bg-yellow-50"}];return t?(0,d.jsx)(g.M,{children:(0,d.jsxs)(l.E.div,{className:"fixed inset-0 z-50 flex items-center justify-center p-4",variants:{hidden:{opacity:0},visible:{opacity:1},exit:{opacity:0}},initial:"hidden",animate:"visible",exit:"exit",children:[(0,d.jsx)(l.E.div,{className:"absolute inset-0 bg-black/60 backdrop-blur-sm",onClick:s}),(0,d.jsxs)(l.E.div,{className:"relative w-full max-w-4xl max-h-[90vh] mx-4 bg-white rounded-2xl shadow-2xl overflow-hidden",variants:{hidden:{opacity:0,scale:.8,y:50},visible:{opacity:1,scale:1,y:0,transition:{type:"spring",damping:25,stiffness:300}},exit:{opacity:0,scale:.8,y:50,transition:{duration:.2}}},initial:"hidden",animate:"visible",exit:"exit",children:[(0,d.jsxs)("div",{className:"bg-gradient-to-r from-blue-600 to-purple-600 px-4 sm:px-6 py-4 sm:py-6 text-white relative overflow-hidden",children:[(0,d.jsx)("div",{className:"absolute inset-0 bg-gradient-to-r from-blue-600/90 to-purple-600/90"}),(0,d.jsxs)("div",{className:"relative z-10",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,d.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,d.jsx)("div",{className:"p-2 bg-white/20 rounded-lg",children:(0,d.jsx)(r.jsT,{className:"w-6 h-6"})}),(0,d.jsxs)("div",{children:[(0,d.jsx)("h2",{className:"text-xl sm:text-2xl font-bold",children:"Congratulations! \ud83c\udf89"}),(0,d.jsx)("p",{className:"text-blue-100 text-sm sm:text-base",children:"You've completed your trial quiz"})]})]}),(0,d.jsx)("button",{onClick:s,className:"p-2 hover:bg-white/20 rounded-lg transition-colors",children:(0,d.jsx)(r.lhV,{className:"w-5 h-5"})})]}),i&&(0,d.jsx)("div",{className:"bg-white/10 backdrop-blur-sm rounded-xl p-4",children:(0,d.jsxs)("div",{className:"grid grid-cols-3 gap-4 text-center",children:[(0,d.jsxs)("div",{children:[(0,d.jsxs)("div",{className:"text-2xl font-bold",children:[i.percentage,"%"]}),(0,d.jsx)("div",{className:"text-sm text-blue-100",children:"Score"})]}),(0,d.jsxs)("div",{children:[(0,d.jsxs)("div",{className:"text-2xl font-bold",children:[i.correctAnswers,"/",i.totalQuestions]}),(0,d.jsx)("div",{className:"text-sm text-blue-100",children:"Correct"})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("div",{className:"text-2xl font-bold",children:i.passed?"PASSED":"FAILED"}),(0,d.jsx)("div",{className:"text-sm text-blue-100",children:"Result"})]})]})})]})]}),(0,d.jsxs)("div",{className:"p-4 sm:p-6 overflow-y-auto max-h-[60vh]",children:[(0,d.jsxs)("div",{className:"text-center mb-8",children:[(0,d.jsx)("div",{className:"w-16 h-16 mx-auto mb-4 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full flex items-center justify-center",children:(0,d.jsx)(r.PnF,{className:"w-8 h-8 text-white"})}),(0,d.jsx)("h3",{className:"text-2xl font-bold text-gray-800 mb-2",children:"Ready to Unlock Your Full Potential?"}),(0,d.jsx)("p",{className:"text-gray-600 text-lg",children:"Join thousands of students who are already excelling with BrainWave"})]}),(0,d.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-8",children:a.map(((e,t)=>(0,d.jsxs)(l.E.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.3,delay:.1*t},className:"p-4 rounded-xl border-2 border-gray-100 hover:border-blue-200 transition-all ".concat(e.bg),children:[(0,d.jsx)(e.icon,{className:"w-8 h-8 ".concat(e.color," mb-3")}),(0,d.jsx)("h4",{className:"font-semibold text-gray-800 mb-2",children:e.title}),(0,d.jsx)("p",{className:"text-sm text-gray-600",children:e.description})]},t)))}),(0,d.jsxs)("div",{className:"bg-gray-50 rounded-xl p-6 mb-8",children:[(0,d.jsxs)("h4",{className:"font-bold text-gray-800 mb-4 flex items-center",children:[(0,d.jsx)(r.e6w,{className:"w-5 h-5 text-green-600 mr-2"}),"What You'll Get With Full Access:"]}),(0,d.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-3",children:["Track your progress across all subjects","Get detailed performance analytics","Access past papers and exam preparation","Join study groups and discussions","Receive personalized study plans","Get instant feedback and explanations"].map(((e,t)=>(0,d.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,d.jsx)(r.e6w,{className:"w-4 h-4 text-green-600 flex-shrink-0"}),(0,d.jsx)("span",{className:"text-sm text-gray-700",children:e})]},t)))})]}),(0,d.jsx)("div",{className:"bg-blue-50 rounded-xl p-6 mb-8",children:(0,d.jsxs)("div",{className:"flex items-center justify-center space-x-8 text-center",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("div",{className:"text-2xl font-bold text-blue-600",children:"10,000+"}),(0,d.jsx)("div",{className:"text-sm text-gray-600",children:"Active Students"})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("div",{className:"text-2xl font-bold text-blue-600",children:"50,000+"}),(0,d.jsx)("div",{className:"text-sm text-gray-600",children:"Quizzes Completed"})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("div",{className:"text-2xl font-bold text-blue-600",children:"95%"}),(0,d.jsx)("div",{className:"text-sm text-gray-600",children:"Success Rate"})]})]})}),(0,d.jsxs)("div",{className:"space-y-4",children:[(0,d.jsx)(u.rU,{to:"/register",className:"block",children:(0,d.jsxs)(l.E.button,{className:"w-full py-4 px-6 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-xl font-bold text-lg hover:from-blue-700 hover:to-purple-700 transition-all shadow-lg hover:shadow-xl flex items-center justify-center space-x-2",whileHover:{scale:1.02},whileTap:{scale:.98},children:[(0,d.jsx)("span",{children:"Create Free Account"}),(0,d.jsx)(r.CR0,{className:"w-5 h-5"})]})}),(0,d.jsx)(u.rU,{to:"/login",className:"block",children:(0,d.jsx)("button",{className:"w-full py-3 px-6 border-2 border-gray-300 text-gray-700 rounded-xl font-medium hover:bg-gray-50 transition-all",children:"Already have an account? Sign In"})}),(0,d.jsx)("div",{className:"text-center",children:(0,d.jsx)("button",{onClick:s,className:"text-gray-500 hover:text-gray-700 text-sm font-medium",children:"Maybe later"})})]}),(0,d.jsx)("div",{className:"mt-6 text-center text-xs text-gray-500",children:(0,d.jsx)("p",{children:"\u2705 Free to join \u2022 \u2705 No credit card required \u2022 \u2705 Cancel anytime"})})]})]})]})}):null},y=()=>{const e=(0,a.TH)(),t=(0,a.s0)(),[s,n]=(0,i.useState)("selection"),[r,o]=(0,i.useState)(null),[c,u]=(0,i.useState)(null),[p,g]=(0,i.useState)(null),[y,v]=(0,i.useState)(!1);(0,i.useEffect)((()=>{var s;null!==(s=e.state)&&void 0!==s&&s.trialUserInfo?o(e.state.trialUserInfo):t("/")}),[e.state,t]);const j=()=>{"playing"===s?(n("selection"),u(null)):"selection"===s&&t("/")};return r?(0,d.jsxs)("div",{className:"trial-page",children:[(0,d.jsx)("div",{className:"fixed top-2 sm:top-4 left-1/2 transform -translate-x-1/2 z-40 px-4",children:(0,d.jsx)("div",{className:"bg-white/90 backdrop-blur-sm rounded-full px-3 sm:px-6 py-2 shadow-lg border max-w-sm sm:max-w-none overflow-hidden",children:(0,d.jsxs)("div",{className:"flex items-center space-x-2 sm:space-x-4",children:[(0,d.jsxs)("div",{className:"flex items-center space-x-1 sm:space-x-2 ".concat("selection"===s?"text-blue-600":"text-gray-400"),children:[(0,d.jsx)("div",{className:"w-2 h-2 sm:w-3 sm:h-3 rounded-full ".concat("selection"===s?"bg-blue-600":"bg-gray-300")}),(0,d.jsx)("span",{className:"text-xs sm:text-sm font-medium hidden sm:inline",children:"Select Quiz"}),(0,d.jsx)("span",{className:"text-xs sm:text-sm font-medium sm:hidden",children:"Select"})]}),(0,d.jsx)("div",{className:"w-4 sm:w-8 h-px bg-gray-300"}),(0,d.jsxs)("div",{className:"flex items-center space-x-1 sm:space-x-2 ".concat("playing"===s?"text-blue-600":"text-gray-400"),children:[(0,d.jsx)("div",{className:"w-2 h-2 sm:w-3 sm:h-3 rounded-full ".concat("playing"===s?"bg-blue-600":"bg-gray-300")}),(0,d.jsx)("span",{className:"text-xs sm:text-sm font-medium hidden sm:inline",children:"Take Quiz"}),(0,d.jsx)("span",{className:"text-xs sm:text-sm font-medium sm:hidden",children:"Quiz"})]}),(0,d.jsx)("div",{className:"w-4 sm:w-8 h-px bg-gray-300"}),(0,d.jsxs)("div",{className:"flex items-center space-x-1 sm:space-x-2 ".concat("result"===s?"text-blue-600":"text-gray-400"),children:[(0,d.jsx)("div",{className:"w-2 h-2 sm:w-3 sm:h-3 rounded-full ".concat("result"===s?"bg-blue-600":"bg-gray-300")}),(0,d.jsx)("span",{className:"text-xs sm:text-sm font-medium",children:"Results"})]})]})})}),(0,d.jsxs)(l.E.div,{initial:{opacity:0,x:20},animate:{opacity:1,x:0},exit:{opacity:0,x:-20},transition:{duration:.3},children:["selection"===s&&(0,d.jsx)(x,{trialUserInfo:r,onQuizSelected:e=>{u(e),n("playing")},onBack:j}),"playing"===s&&c&&(0,d.jsx)(m,{quizData:c,onComplete:e=>{g(e),n("result"),setTimeout((()=>{v(!0)}),3e3)},onBack:j}),"result"===s&&p&&(0,d.jsx)(h,{result:p,onTryAnother:()=>{n("selection"),u(null),g(null),v(!1)},onRegister:()=>{t("/register",{state:{trialCompleted:!0,trialResult:p,trialUserInfo:r}})}})]},s),(0,d.jsx)(b,{isOpen:y,onClose:()=>v(!1),trialResult:p})]}):(0,d.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 via-white to-blue-50 flex items-center justify-center",children:(0,d.jsxs)("div",{className:"text-center",children:[(0,d.jsx)("div",{className:"w-16 h-16 mx-auto mb-4 bg-blue-100 rounded-full flex items-center justify-center",children:(0,d.jsx)("div",{className:"w-6 h-6 border-2 border-blue-600 border-t-transparent rounded-full animate-spin"})}),(0,d.jsx)("p",{className:"text-gray-600",children:"Loading trial experience..."})]})})}}}]);
//# sourceMappingURL=279.de1f9748.chunk.js.map