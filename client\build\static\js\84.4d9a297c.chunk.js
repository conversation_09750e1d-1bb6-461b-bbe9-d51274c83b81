"use strict";(self.webpackChunkclient=self.webpackChunkclient||[]).push([[84],{4084:(e,s,t)=>{t.r(s),t.d(s,{default:()=>x});var r=t(2791),n=t(3655),a=t(7027),o=t(6473),c=t(1046),l=t(7309),i=t(3371);const d=async function(e){let s=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;try{const t=s?"?class=".concat(s):"";return(await i.default.get("/api/syllabus/subjects/".concat(e).concat(t))).data}catch(t){return t.response.data}},u=async function(e){let s=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;try{console.log("\ud83d\udd0d Fetching subjects for level: ".concat(e,", class: ").concat(s));const t=s?"?class=".concat(s):"";console.log("\ud83d\udcda Trying syllabus endpoint: /api/syllabus/subjects/".concat(e).concat(t));const r=await i.default.get("/api/syllabus/subjects/".concat(e).concat(t));if(console.log("\ud83d\udcda Syllabus response:",r.data),r.data.success&&r.data.data.length>0)return console.log("\u2705 Using syllabus-based subjects for ".concat(e,":"),r.data.data),r.data;console.log("\ud83d\udcd6 No syllabus subjects found, falling back to hardcoded subjects for ".concat(e));const n=await i.default.get("/api/ai-questions/subjects/".concat(e));return console.log("\ud83d\udcd6 Fallback response:",n.data),n.data}catch(r){var t;console.error("\u274c Error fetching subjects:",r),console.error("Error details:",null===(t=r.response)||void 0===t?void 0:t.data);try{console.log("\ud83d\udd04 Trying fallback due to error...");const s=await i.default.get("/api/ai-questions/subjects/".concat(e));return console.log("\ud83d\udd04 Fallback response:",s.data),s.data}catch(n){return console.error("\u274c Fallback also failed:",n),{success:!1,message:"Failed to fetch subjects",data:[]}}}};var g=t(184);const{Text:h,Title:j}=n.default,x=()=>{const[e,s]=(0,r.useState)({}),[t,n]=(0,r.useState)({}),[x,b]=(0,r.useState)(!1);(0,r.useEffect)((()=>{p()}),[]);const p=()=>{const e=localStorage.getItem("token"),t=localStorage.getItem("user");let r={};if(e)try{const s=JSON.parse(atob(e.split(".")[1]));r={userId:s.userId,exp:s.exp,iat:s.iat,isExpired:s.exp<Date.now()/1e3}}catch(n){r={error:"Invalid token format"}}s({hasToken:!!e,hasUser:!!t,token:e?"".concat(e.substring(0,20),"..."):null,user:t?JSON.parse(t):null,tokenInfo:r})};return(0,g.jsxs)("div",{style:{padding:"20px",maxWidth:"800px",margin:"0 auto"},children:[(0,g.jsx)(j,{level:2,children:"\ud83d\udd0d Authentication & API Debug"}),(0,g.jsxs)(o.Z,{title:"\ud83d\udd10 Authentication Status",style:{marginBottom:"20px"},children:[(0,g.jsxs)(c.Z,{direction:"vertical",style:{width:"100%"},children:[(0,g.jsxs)(h,{children:[(0,g.jsx)("strong",{children:"Has Token:"})," ",e.hasToken?"\u2705 Yes":"\u274c No"]}),(0,g.jsxs)(h,{children:[(0,g.jsx)("strong",{children:"Has User:"})," ",e.hasUser?"\u2705 Yes":"\u274c No"]}),e.token&&(0,g.jsxs)(h,{children:[(0,g.jsx)("strong",{children:"Token Preview:"})," ",e.token]}),e.user&&(0,g.jsxs)("div",{children:[(0,g.jsxs)(h,{children:[(0,g.jsx)("strong",{children:"User:"})," ",e.user.name," (",e.user.email,")"]}),(0,g.jsx)("br",{}),(0,g.jsxs)(h,{children:[(0,g.jsx)("strong",{children:"Is Admin:"})," ",e.user.isAdmin?"\u2705 Yes":"\u274c No"]})]}),e.tokenInfo&&(0,g.jsxs)("div",{children:[(0,g.jsx)(h,{children:(0,g.jsx)("strong",{children:"Token Info:"})}),(0,g.jsx)("br",{}),(0,g.jsxs)(h,{children:["User ID: ",e.tokenInfo.userId]}),(0,g.jsx)("br",{}),(0,g.jsxs)(h,{children:["Expires: ",e.tokenInfo.exp?new Date(1e3*e.tokenInfo.exp).toLocaleString():"N/A"]}),(0,g.jsx)("br",{}),(0,g.jsxs)(h,{children:["Is Expired: ",e.tokenInfo.isExpired?"\u274c Yes":"\u2705 No"]}),e.tokenInfo.error&&(0,g.jsxs)(g.Fragment,{children:[(0,g.jsx)("br",{}),(0,g.jsxs)(h,{type:"danger",children:["Error: ",e.tokenInfo.error]})]})]})]}),(0,g.jsx)("div",{style:{marginTop:"15px"},children:(0,g.jsxs)(c.Z,{children:[(0,g.jsx)(l.ZP,{onClick:()=>{p(),a.ZP.info("Authentication info refreshed")},children:"Refresh"}),(0,g.jsx)(l.ZP,{onClick:()=>{localStorage.removeItem("token"),localStorage.removeItem("user"),a.ZP.info("Authentication cleared"),p()},danger:!0,children:"Clear Auth"})]})})]}),(0,g.jsx)(o.Z,{title:"\ud83e\uddea API Test Results",style:{marginBottom:"20px"},children:(0,g.jsxs)(c.Z,{direction:"vertical",style:{width:"100%"},children:[(0,g.jsx)(l.ZP,{type:"primary",onClick:async()=>{b(!0);const e={};try{var s;console.log("\ud83e\uddea Testing getAllSyllabuses...");const t=await async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};try{const s=new URLSearchParams;return Object.keys(e).forEach((t=>{e[t]&&s.append(t,e[t])})),(await i.default.get("/api/syllabus?".concat(s.toString()))).data}catch(s){return s.response.data}}();e.getAllSyllabuses={success:t.success,dataLength:(null===(s=t.data)||void 0===s?void 0:s.length)||0,message:t.message,error:t.success?null:t.message},console.log("\ud83d\udcda Syllabus response:",t)}catch(l){var t;e.getAllSyllabuses={success:!1,error:l.message,status:null===(t=l.response)||void 0===t?void 0:t.status},console.error("\u274c Syllabus error:",l)}try{var r;console.log("\ud83e\uddea Testing getAvailableSubjects...");const s=await d("primary");e.getAvailableSubjects={success:s.success,dataLength:(null===(r=s.data)||void 0===r?void 0:r.length)||0,data:s.data,message:s.message,error:s.success?null:s.message},console.log("\ud83d\udcd6 Subjects response:",s)}catch(l){var a;e.getAvailableSubjects={success:!1,error:l.message,status:null===(a=l.response)||void 0===a?void 0:a.status},console.error("\u274c Subjects error:",l)}try{var o;console.log("\ud83e\uddea Testing getSubjectsForLevel (AI)...");const s=await u("primary");e.getSubjectsForLevel={success:s.success,dataLength:(null===(o=s.data)||void 0===o?void 0:o.length)||0,data:s.data,message:s.message,error:s.success?null:s.message},console.log("\ud83e\udd16 AI Subjects response:",s)}catch(l){var c;e.getSubjectsForLevel={success:!1,error:l.message,status:null===(c=l.response)||void 0===c?void 0:c.status},console.error("\u274c AI Subjects error:",l)}n(e),b(!1)},loading:x,disabled:!e.hasToken,children:"Test API Endpoints"}),Object.keys(t).length>0&&(0,g.jsxs)("div",{children:[(0,g.jsx)(j,{level:4,children:"Test Results:"}),Object.entries(t).map((e=>{let[s,t]=e;return(0,g.jsxs)(o.Z,{size:"small",title:s,style:{marginBottom:"10px"},children:[(0,g.jsxs)(h,{children:[(0,g.jsx)("strong",{children:"Success:"})," ",t.success?"\u2705 Yes":"\u274c No"]}),(0,g.jsx)("br",{}),void 0!==t.dataLength&&(0,g.jsxs)(g.Fragment,{children:[(0,g.jsxs)(h,{children:[(0,g.jsx)("strong",{children:"Data Length:"})," ",t.dataLength]}),(0,g.jsx)("br",{})]}),t.data&&(0,g.jsxs)(g.Fragment,{children:[(0,g.jsxs)(h,{children:[(0,g.jsx)("strong",{children:"Data:"})," ",JSON.stringify(t.data)]}),(0,g.jsx)("br",{})]}),t.message&&(0,g.jsxs)(g.Fragment,{children:[(0,g.jsxs)(h,{children:[(0,g.jsx)("strong",{children:"Message:"})," ",t.message]}),(0,g.jsx)("br",{})]}),t.error&&(0,g.jsxs)(g.Fragment,{children:[(0,g.jsxs)(h,{type:"danger",children:[(0,g.jsx)("strong",{children:"Error:"})," ",t.error]}),(0,g.jsx)("br",{})]}),t.status&&(0,g.jsxs)(g.Fragment,{children:[(0,g.jsxs)(h,{children:[(0,g.jsx)("strong",{children:"Status:"})," ",t.status]}),(0,g.jsx)("br",{})]})]},s)}))]}),(0,g.jsx)("div",{style:{marginTop:"15px"},children:(0,g.jsxs)(c.Z,{children:[(0,g.jsx)(l.ZP,{onClick:async()=>{try{const e=await u("primary");console.log("Quick test - Primary subjects:",e),a.ZP.info("Primary subjects: ".concat(JSON.stringify(e.data)))}catch(e){console.error("Quick test error:",e),a.ZP.error("Error: ".concat(e.message))}},children:"Quick Test: Primary Subjects"}),(0,g.jsx)(l.ZP,{onClick:async()=>{try{const e=await d("primary");console.log("Quick test - Available subjects:",e),a.ZP.info("Available subjects: ".concat(JSON.stringify(e.data)))}catch(e){console.error("Quick test error:",e),a.ZP.error("Error: ".concat(e.message))}},children:"Quick Test: Syllabus Subjects"})]})})]})}),(0,g.jsx)(o.Z,{title:"\ud83d\udccb Debug Instructions",children:(0,g.jsxs)(c.Z,{direction:"vertical",children:[(0,g.jsx)(h,{children:"1. Check if you have a valid authentication token"}),(0,g.jsx)(h,{children:"2. Verify the token is not expired"}),(0,g.jsx)(h,{children:"3. Test API endpoints to see specific error messages"}),(0,g.jsx)(h,{children:"4. Check browser console for detailed error logs"}),(0,g.jsx)(h,{children:"5. If token is invalid, try logging out and logging back in"})]})})]})}}}]);
//# sourceMappingURL=84.4d9a297c.chunk.js.map