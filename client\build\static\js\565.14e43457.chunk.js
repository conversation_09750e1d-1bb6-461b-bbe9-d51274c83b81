"use strict";(self.webpackChunkclient=self.webpackChunkclient||[]).push([[565],{2713:(e,s,a)=>{a.d(s,{A4:()=>j,AL:()=>c,FM:()=>p,HH:()=>d,Hh:()=>y,KB:()=>m,MC:()=>x,Qk:()=>u,SL:()=>r,TA:()=>o,X9:()=>n,cN:()=>i,f_:()=>v,o$:()=>l,u8:()=>h});const{default:t}=a(3371),l=async e=>{try{return await t.post("/api/study/get-study-content",e)}catch(s){return s.response}},n=async()=>{try{return(await t.get("/api/study/videos-subtitle-status")).data}catch(s){var e;return(null===(e=s.response)||void 0===e?void 0:e.data)||{success:!1,message:"Failed to fetch videos"}}},i=async function(e){let s=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;try{const a=e instanceof FormData?{headers:{"Content-Type":"multipart/form-data"},timeout:6e5,onUploadProgress:s?e=>{const a=Math.round(100*e.loaded/e.total);s(a,e.loaded,e.total)}:void 0}:{timeout:6e4};return await t.post("/api/study/add-video",e,a)}catch(a){return a.response}},c=async e=>{try{return await t.post("/api/study/add-note",e,{headers:{"Content-Type":"multipart/form-data"}})}catch(s){return s.response}},r=async e=>{try{return await t.post("/api/study/add-past-paper",e,{headers:{"Content-Type":"multipart/form-data"}})}catch(s){return s.response}},o=async e=>{try{return await t.post("/api/study/add-book",e,{headers:{"Content-Type":"multipart/form-data"}})}catch(s){return s.response}},d=async(e,s)=>{try{let a={headers:{"Content-Type":"application/json"}};s instanceof FormData&&(a.headers["Content-Type"]="multipart/form-data");return await t.put("/api/study/update-video/".concat(e),s,a)}catch(a){return a.response}},u=async(e,s)=>{try{return await t.put("/api/study/update-note/".concat(e),s,{headers:{"Content-Type":"multipart/form-data"}})}catch(a){return a.response}},m=async(e,s)=>{try{return await t.put("/api/study/update-past-paper/".concat(e),s,{headers:{"Content-Type":"multipart/form-data"}})}catch(a){return a.response}},h=async(e,s)=>{try{return await t.put("/api/study/update-book/".concat(e),s,{headers:{"Content-Type":"multipart/form-data"}})}catch(a){return a.response}},p=async e=>{try{return await t.delete("/api/study/delete-video/".concat(e))}catch(s){return s.response}},v=async e=>{try{return await t.delete("/api/study/delete-note/".concat(e))}catch(s){return s.response}},j=async e=>{try{return await t.delete("/api/study/delete-past-paper/".concat(e))}catch(s){return s.response}},x=async e=>{try{return await t.delete("/api/study/delete-book/".concat(e))}catch(s){return s.response}},y=async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};try{const s=new URLSearchParams;e.materialType&&s.append("materialType",e.materialType),e.level&&s.append("level",e.level),e.className&&s.append("className",e.className),e.subject&&s.append("subject",e.subject);return await t.get("/api/study/admin/all-materials?".concat(s.toString()))}catch(s){return s.response}}},1304:(e,s,a)=>{a.d(s,{Lo:()=>l,iq:()=>t,vp:()=>n});const t=["Mathematics","Science and Technology","Geography","Kiswahili","SocialStudies","English","Religion","Arithmetic","Sport and Art","Health and Environment","Civic and Moral","French","Historia ya Tanzania"],l=["Physics","Mathematics","Biology","Chemistry","Geography","Civics","History","Computer","English language","Kiswahili","Commerce","Economics","Advanced Mathematics","Basic Applied Mathematics","Accountancy","Agriculture"],n=["Physics","Mathematics","Biology","Chemistry","Geography","Civics","History","Computer","English language","Kiswahili","Commerce","Economics","Advanced Mathematics","Basic Applied Mathematics","Accountancy","Agriculture"]},1565:(e,s,a)=>{a.r(s),a.d(s,{default:()=>m});var t=a(1413),l=a(2791),n=a(2713),i=a(9434),c=a(8247),r=a(2202),o=a(5526),d=a(1304),u=a(184);const m=function(){const{user:e}=(0,i.v9)((e=>e.user)),s=(0,i.I0)(),[a,m]=(0,l.useState)([]),[h,p]=(0,l.useState)(!1),[v,j]=(0,l.useState)(null),[x,y]=(0,l.useState)((null===e||void 0===e?void 0:e.level)||"primary"),[g,N]=(0,l.useState)((null===e||void 0===e?void 0:e.class)||"all"),[b,f]=(0,l.useState)("all"),[C,w]=(0,l.useState)(""),[S,k]=(0,l.useState)("newest"),[A,T]=(0,l.useState)(null),[U,D]=(0,l.useState)([]),[L,F]=(0,l.useState)(!1),[M,E]=(0,l.useState)(null),[I,R]=(0,l.useState)(null),[V,G]=(0,l.useState)([]),[H,P]=(0,l.useState)(""),[B,z]=(0,l.useState)(null),[O,Y]=(0,l.useState)(""),q=(0,l.useMemo)((()=>"primary"===x?["1","2","3","4","5","6","7"]:"secondary"===x?["1","2","3","4"]:"advance"===x?["5","6"]:[]),[x]),K=(0,l.useMemo)((()=>"primary"===x?d.iq:"secondary"===x?d.Lo:"advance"===x?d.vp:[]),[x]),Z=(0,l.useCallback)((async()=>{try{var e;p(!0),j(null),s((0,c.YC)());const t={level:x,className:"",subject:"",content:"videos"},l=await(0,n.o$)(t);var a;if(null!==l&&void 0!==l&&null!==(e=l.data)&&void 0!==e&&e.success)m(l.data.data||[]);else j((null===l||void 0===l||null===(a=l.data)||void 0===a?void 0:a.message)||"Failed to fetch videos"),m([])}catch(v){console.error("\u274c Error fetching videos:",v),j("Failed to load videos. Please try again."),m([])}finally{p(!1),s((0,c.Ir)())}}),[x,s]),W=(0,l.useMemo)((()=>{let e=a;if(e=e.filter((e=>e.level===x)),"all"!==g&&(e=e.filter((e=>(e.className||e.class)===g))),"all"!==b&&(e=e.filter((e=>e.subject===b))),C.trim()){const s=C.toLowerCase();e=e.filter((e=>{var a,t,l;return(null===(a=e.title)||void 0===a?void 0:a.toLowerCase().includes(s))||(null===(t=e.subject)||void 0===t?void 0:t.toLowerCase().includes(s))||(null===(l=e.topic)||void 0===l?void 0:l.toLowerCase().includes(s))}))}const s=[...e].sort(((e,s)=>{switch(S){case"newest":return new Date(s.createdAt||0)-new Date(e.createdAt||0);case"oldest":return new Date(e.createdAt||0)-new Date(s.createdAt||0);case"title":return(e.title||"").localeCompare(s.title||"");case"subject":return(e.subject||"").localeCompare(s.subject||"");default:return 0}}));return console.log("\u2705 Final filtered videos:",s.length),s.length>0&&console.log("\ud83d\udcf9 Sample filtered video:",s[0]),s}),[a,C,S,x,g,b]),_=()=>{D([]),T(null),F(!1),E(null),I&&I.pause()},X=()=>{F(!L)},$=async e=>{if(!e)return e;if(e.includes("amazonaws.com")||e.includes("s3."))try{const s=await fetch("http://localhost:5000/api/study/video-signed-url?videoUrl=".concat(encodeURIComponent(e)),{method:"GET",headers:{"Content-Type":"application/json"}});if(!s.ok)throw new Error("HTTP error! status: ".concat(s.status));const a=await s.json();return a.success&&a.signedUrl?(console.log("\u2705 Got signed URL for S3 video"),a.signedUrl):(console.warn("\u26a0\ufe0f Invalid response from signed URL endpoint:",a),e)}catch(v){return console.error("\u274c Error getting signed URL:",v),e}return e},Q=e=>{if(e.thumbnail)return e.thumbnail;if(e.videoID&&!e.videoID.includes("amazonaws.com")){let s=e.videoID;if(s.includes("youtube.com")||s.includes("youtu.be")){const e=s.match(/(?:youtube\.com\/watch\?v=|youtu\.be\/)([^&\n?#]+)/);s=e?e[1]:s}return"https://img.youtube.com/vi/".concat(s,"/maxresdefault.jpg")}return"/api/placeholder/400/225"};(0,l.useEffect)((()=>{Z()}),[Z]),(0,l.useEffect)((()=>{null!==e&&void 0!==e&&e.level&&y(e.level),null!==e&&void 0!==e&&e.class&&N(e.class)}),[e]);const J=()=>{if(H.trim()){const s={id:Date.now(),text:H,author:(null===e||void 0===e?void 0:e.name)||"Anonymous",timestamp:(new Date).toISOString(),replies:[]};G([...V,s]),P("")}};return(0,u.jsxs)("div",{className:"video-lessons-container",children:[(0,u.jsx)("div",{className:"video-lessons-header",children:(0,u.jsxs)("div",{className:"header-content",children:[(0,u.jsxs)("div",{className:"header-main",children:[(0,u.jsx)("div",{className:"header-icon",children:(0,u.jsx)(o.mL_,{})}),(0,u.jsxs)("div",{className:"header-text",children:[(0,u.jsx)("h1",{children:"Video Lessons"}),(0,u.jsx)("p",{children:"Watch educational videos to enhance your learning"})]})]}),(0,u.jsxs)("div",{className:"level-display",children:[(0,u.jsxs)("div",{className:"current-level",children:[(0,u.jsx)("span",{className:"level-label",children:"Level:"}),(0,u.jsx)("span",{className:"level-value",children:x.charAt(0).toUpperCase()+x.slice(1)})]}),(0,u.jsxs)("div",{className:"current-class",children:[(0,u.jsx)("span",{className:"class-label",children:"Your Class:"}),(0,u.jsx)("span",{className:"class-value",children:"primary"===(null===e||void 0===e?void 0:e.level)?"Class ".concat((null===e||void 0===e?void 0:e.class)||"N/A"):"secondary"===(null===e||void 0===e?void 0:e.level)||"advance"===(null===e||void 0===e?void 0:e.level)?"Form ".concat((null===e||void 0===e?void 0:e.class)||"N/A"):"Not Set"})]})]})]})}),(0,u.jsxs)("div",{className:"video-lessons-content",children:[(0,u.jsxs)("div",{className:"video-controls",children:[(0,u.jsxs)("div",{className:"controls-row",children:[(0,u.jsxs)("div",{className:"control-group",children:[(0,u.jsxs)("label",{className:"control-label",children:[(0,u.jsx)(o.a9n,{}),"Filter by Class"]}),(0,u.jsxs)("select",{value:g,onChange:e=>N(e.target.value),className:"control-select class-select",children:[(0,u.jsx)("option",{value:"all",children:"All Classes"}),q.map((e=>(0,u.jsx)("option",{value:e,children:"primary"===x?"Class ".concat(e):"Form ".concat(e)},e)))]})]}),(0,u.jsxs)("div",{className:"control-group",children:[(0,u.jsxs)("label",{className:"control-label",children:[(0,u.jsx)(o.a9n,{}),"Subject"]}),(0,u.jsxs)("select",{value:b,onChange:e=>f(e.target.value),className:"control-select subject-select",children:[(0,u.jsx)("option",{value:"all",children:"All Subjects"}),K.map((e=>(0,u.jsx)("option",{value:e,children:e},e)))]})]}),(0,u.jsxs)("div",{className:"control-group",children:[(0,u.jsxs)("label",{className:"control-label",children:[(0,u.jsx)(o.U2W,{}),"Sort"]}),(0,u.jsxs)("select",{value:S,onChange:e=>k(e.target.value),className:"control-select sort-select",children:[(0,u.jsx)("option",{value:"newest",children:"Newest First"}),(0,u.jsx)("option",{value:"oldest",children:"Oldest First"}),(0,u.jsx)("option",{value:"title",children:"Title A-Z"}),(0,u.jsx)("option",{value:"subject",children:"Subject A-Z"})]})]})]}),(0,u.jsxs)("div",{className:"search-row",children:[(0,u.jsxs)("div",{className:"search-container",children:[(0,u.jsx)(o.adB,{className:"search-icon"}),(0,u.jsx)("input",{type:"text",placeholder:"Search videos by title, subject, or topic...",value:C,onChange:e=>w(e.target.value),className:"search-input"}),C&&(0,u.jsxs)("button",{onClick:()=>{w("")},className:"clear-search-btn",children:[(0,u.jsx)(o.lhV,{}),"Clear Search"]})]}),(0,u.jsxs)("button",{onClick:()=>{Z()},className:"refresh-btn",children:[(0,u.jsx)(o.HXz,{}),"Refresh All"]})]})]}),h?(0,u.jsxs)("div",{className:"loading-state",children:[(0,u.jsx)("div",{className:"loading-spinner"}),(0,u.jsx)("p",{children:"Loading videos..."})]}):v?(0,u.jsxs)("div",{className:"error-state",children:[(0,u.jsx)(o.bS7,{className:"error-icon"}),(0,u.jsx)("h3",{children:"Error Loading Videos"}),(0,u.jsx)("p",{children:v}),(0,u.jsx)("button",{onClick:Z,className:"retry-btn",children:"Try Again"})]}):W.length>0?(0,u.jsx)("div",{className:"videos-grid",children:W.map(((e,s)=>(0,u.jsxs)("div",{className:"video-card",onClick:()=>(async e=>{const s=W[e];if(T(e),D([e]),F(!1),E(null),null!==s&&void 0!==s&&s.videoUrl&&(s.videoUrl.includes("amazonaws.com")||s.videoUrl.includes("s3.")))try{const e=await $(s.videoUrl);s.signedVideoUrl=e}catch(v){console.warn("Failed to get signed URL, using original URL"),s.signedVideoUrl=s.videoUrl}})(s),children:[(0,u.jsxs)("div",{className:"video-card-thumbnail",children:[(0,u.jsx)("img",{src:Q(e),alt:e.title,className:"thumbnail-image",onError:s=>{if(e.videoID&&!e.videoID.includes("amazonaws.com")){let a=e.videoID;if(a.includes("youtube.com")||a.includes("youtu.be")){const e=a.match(/(?:youtube\.com\/watch\?v=|youtu\.be\/)([^&\n?#]+)/);a=e?e[1]:a}const t=["https://img.youtube.com/vi/".concat(a,"/hqdefault.jpg"),"https://img.youtube.com/vi/".concat(a,"/mqdefault.jpg"),"https://img.youtube.com/vi/".concat(a,"/default.jpg"),"/api/placeholder/320/180"],l=s.target.src,n=t.findIndex((e=>l.includes(e.split("/").pop())));n<t.length-1&&(s.target.src=t[n+1])}else s.target.src="/api/placeholder/320/180"}}),(0,u.jsx)("div",{className:"play-overlay",children:(0,u.jsx)(r.Gzj,{className:"play-icon"})}),(0,u.jsx)("div",{className:"video-duration",children:e.duration||"Video"}),e.subtitles&&e.subtitles.length>0&&(0,u.jsxs)("div",{className:"subtitle-badge",children:[(0,u.jsx)(o.GCM,{}),"CC"]})]}),(0,u.jsxs)("div",{className:"video-card-content",children:[(0,u.jsx)("h3",{className:"video-title",children:e.title}),(0,u.jsxs)("div",{className:"video-meta",children:[(0,u.jsx)("span",{className:"video-subject",children:e.subject}),(0,u.jsx)("span",{className:"video-class",children:"primary"===x?"Class ".concat(e.className||e.class):"Form ".concat(e.className||e.class)})]}),(0,u.jsxs)("div",{className:"video-tags",children:[e.topic&&(0,u.jsx)("span",{className:"topic-tag",children:e.topic}),e.sharedFromClass&&e.sharedFromClass!==(e.className||e.class)&&(0,u.jsxs)("span",{className:"shared-tag",children:["Shared from ","primary"===x?"Class ".concat(e.sharedFromClass):"Form ".concat(e.sharedFromClass)]})]})]})]},s)))}):(0,u.jsxs)("div",{className:"empty-state",children:[(0,u.jsx)(r.nGB,{className:"empty-icon"}),(0,u.jsx)("h3",{children:"No Videos Found"}),(0,u.jsx)("p",{children:"No video lessons are available for your current selection."}),(0,u.jsx)("p",{className:"suggestion",children:"Try selecting a different class or subject."})]})]}),U.length>0&&null!==A&&(0,u.jsx)("div",{className:"video-overlay ".concat(L?"expanded":""),onClick:e=>{e.target===e.currentTarget&&_()},children:(0,u.jsx)("div",{className:"video-modal ".concat(L?"expanded":""),children:(()=>{const s=W[A];return s?(0,u.jsxs)("div",{className:"video-content",children:[(0,u.jsxs)("div",{className:"video-header",children:[(0,u.jsxs)("div",{className:"video-info",children:[(0,u.jsx)("h3",{className:"video-title",children:s.title}),(0,u.jsxs)("div",{className:"video-meta",children:[(0,u.jsx)("span",{className:"video-subject",children:s.subject}),(0,u.jsxs)("span",{className:"video-class",children:["Class ",s.className]}),s.level&&(0,u.jsx)("span",{className:"video-level",children:s.level})]})]}),(0,u.jsxs)("div",{className:"video-controls",children:[(0,u.jsx)("button",{className:"control-btn expand-btn",onClick:X,title:L?"Exit fullscreen":"Enter fullscreen",children:L?(0,u.jsx)(r.Y7H,{}):(0,u.jsx)(r.a1O,{})}),(0,u.jsx)("button",{className:"control-btn close-btn",onClick:_,title:"Close video",children:(0,u.jsx)(r.aHS,{})})]})]}),(0,u.jsx)("div",{className:"video-container",children:s.videoUrl?(0,u.jsxs)("div",{style:{padding:"15px",background:"#000",borderRadius:"8px"},children:[(0,u.jsxs)("video",{ref:e=>R(e),controls:!0,autoPlay:!0,playsInline:!0,preload:"metadata",width:"100%",height:"400",poster:Q(s),style:{width:"100%",height:"400px",backgroundColor:"#000"},onError:e=>{E("Failed to load video: ".concat(s.title,". Please try refreshing the page."))},onCanPlay:()=>{E(null)},onLoadStart:()=>{console.log("\ud83c\udfac Video loading started")},crossOrigin:"anonymous",children:[(0,u.jsx)("source",{src:s.signedVideoUrl||s.videoUrl,type:"video/mp4"}),s.subtitles&&s.subtitles.length>0&&s.subtitles.map(((e,s)=>(0,u.jsx)("track",{kind:"subtitles",src:e.url,srcLang:e.language,label:e.languageName,default:e.isDefault||0===s},"".concat(e.language,"-").concat(s)))),"Your browser does not support the video tag."]}),s.subtitles&&s.subtitles.length>0&&(0,u.jsxs)("div",{className:"subtitle-indicator",children:[(0,u.jsx)(o.GCM,{className:"subtitle-icon"}),(0,u.jsxs)("span",{children:["Subtitles available in ",s.subtitles.length," language(s)"]})]}),M&&(0,u.jsx)("div",{className:"video-error-overlay",children:(0,u.jsxs)("div",{className:"error-content",children:[(0,u.jsx)(o.bS7,{className:"error-icon"}),(0,u.jsx)("p",{children:M}),(0,u.jsx)("button",{onClick:()=>E(null),className:"dismiss-error-btn",children:"Dismiss"})]})})]}):s.videoID?(0,u.jsx)("iframe",{src:"https://www.youtube.com/embed/".concat(s.videoID,"?autoplay=1&rel=0"),title:s.title,frameBorder:"0",allowFullScreen:!0,className:"video-iframe",onLoad:()=>console.log("\u2705 YouTube iframe loaded")}):(0,u.jsxs)("div",{className:"video-error",children:[(0,u.jsx)("div",{className:"error-icon",children:"\u26a0\ufe0f"}),(0,u.jsx)("h3",{children:"Video Unavailable"}),(0,u.jsx)("p",{children:M||"This video cannot be played at the moment."}),(0,u.jsx)("div",{className:"error-actions",children:(0,u.jsx)("a",{href:s.signedVideoUrl||s.videoUrl,target:"_blank",rel:"noopener noreferrer",className:"external-link-btn",children:"\ud83d\udcf1 Open in New Tab"})})]})}),!L&&(0,u.jsxs)("div",{className:"comments-section",children:[(0,u.jsxs)("h4",{className:"comments-title",children:[(0,u.jsx)(o.GCM,{}),"Discussion (",V.length,")"]}),(0,u.jsx)("div",{className:"add-comment",children:(0,u.jsxs)("div",{className:"comment-input-container",children:[(0,u.jsx)("textarea",{value:H,onChange:e=>P(e.target.value),placeholder:"Share your thoughts about this video...",className:"comment-input",rows:"3"}),(0,u.jsx)("button",{onClick:J,className:"comment-submit-btn",disabled:!H.trim(),children:"Post Comment"})]})}),(0,u.jsx)("div",{className:"comments-list",children:0===V.length?(0,u.jsxs)("div",{className:"no-comments",children:[(0,u.jsx)(o.GCM,{}),(0,u.jsx)("p",{children:"No comments yet. Be the first to share your thoughts!"})]}):V.map((s=>(0,u.jsxs)("div",{className:"comment",children:[(0,u.jsxs)("div",{className:"comment-header",children:[(0,u.jsx)("span",{className:"comment-author",children:s.author}),(0,u.jsx)("span",{className:"comment-time",children:new Date(s.timestamp).toLocaleDateString()})]}),(0,u.jsx)("div",{className:"comment-text",children:s.text}),(0,u.jsx)("div",{className:"comment-actions",children:(0,u.jsx)("button",{onClick:()=>z(s.id),className:"reply-btn",children:"Reply"})}),B===s.id&&(0,u.jsxs)("div",{className:"reply-input-container",children:[(0,u.jsx)("textarea",{value:O,onChange:e=>Y(e.target.value),placeholder:"Write a reply...",className:"reply-input",rows:"2"}),(0,u.jsxs)("div",{className:"reply-actions",children:[(0,u.jsx)("button",{onClick:()=>(s=>{if(O.trim()){const a={id:Date.now(),text:O,author:(null===e||void 0===e?void 0:e.name)||"Anonymous",timestamp:(new Date).toISOString()};G(V.map((e=>e.id===s?(0,t.Z)((0,t.Z)({},e),{},{replies:[...e.replies,a]}):e))),Y(""),z(null)}})(s.id),className:"reply-submit-btn",disabled:!O.trim(),children:"Reply"}),(0,u.jsx)("button",{onClick:()=>{z(null),Y("")},className:"reply-cancel-btn",children:"Cancel"})]})]}),s.replies.length>0&&(0,u.jsx)("div",{className:"replies",children:s.replies.map((e=>(0,u.jsxs)("div",{className:"reply",children:[(0,u.jsxs)("div",{className:"reply-header",children:[(0,u.jsx)("span",{className:"reply-author",children:e.author}),(0,u.jsx)("span",{className:"reply-time",children:new Date(e.timestamp).toLocaleDateString()})]}),(0,u.jsx)("div",{className:"reply-text",children:e.text})]},e.id)))})]},s.id)))})]})]}):(0,u.jsx)("div",{children:"Video not found"})})()})})]})}}}]);
//# sourceMappingURL=565.14e43457.chunk.js.map