{"version": 3, "file": "static/css/657.5b558e65.chunk.css", "mappings": "AAAA,qBAEE,aAAc,CADd,gBAAiB,CAEjB,iBACF,CAEA,4BAGE,sBAAuB,CAFvB,YAAa,CAIb,QAAS,CAHT,6BAA8B,CAE9B,kBAEF,CAEA,YAEE,kBAAmB,CAInB,aAAc,CALd,YAAa,CAGb,cAAe,CACf,eAAgB,CAFhB,UAAY,CAIZ,gBACF,CAEA,YACE,aACF,CAEA,kBACE,aAAc,CACd,cAAe,CACf,QACF,CAEA,yBAEE,kBAAmB,CACnB,mCAA6C,CAF7C,eAGF,CAEA,wCACE,+BAAgC,CAChC,mBACF,CAEA,8CAEE,aAAc,CADd,eAEF,CAEA,wCACE,SACF,CAEA,wCAEE,+BAAgC,CADhC,cAEF,CAEA,mDACE,kBACF,CAEA,mBAEE,aAAc,CACd,iBAAmB,CAFnB,gBAGF,CAEA,WACE,mBAAqB,CACrB,qBACF,CAEA,mBAGE,UACF,CAEA,+BALE,YAAa,CACb,qBAQF,CAJA,YAGE,SACF,CAEA,kBAEE,aAAc,CACd,iBAAmB,CAFnB,eAGF,CAEA,6FAIE,wBAAyB,CADzB,iBAAkB,CAElB,cACF,CAEA,6HAGE,oBAAqB,CACrB,8BACF,CAEA,oCACE,aACF,CAGA,kBACE,+BAAgC,CAChC,0BACF,CAEA,iBAEE,aAAc,CACd,iBAAkB,CAFlB,eAGF,CAEA,gBACE,cACF,CAEA,kBACE,4BAA6B,CAC7B,mBACF,CAGA,iBAQE,kBAAmB,CAPnB,wBAAyB,CACzB,oBAAqB,CACrB,iBAAkB,CAIlB,YAAa,CAHb,eAAgB,CAKhB,SAAW,CAJX,WAAY,CACZ,qBAIF,CAEA,uBACE,wBAAyB,CACzB,oBACF,CAEA,cAKE,kBAAmB,CAJnB,iBAAkB,CAGlB,YAAa,CAEb,SAAW,CAJX,WAAY,CACZ,kBAIF,CAGA,SAKE,WAAY,CAJZ,iBAAkB,CAElB,gBAAkB,CADlB,eAAgB,CAEhB,oBAEF,CAGA,0BAGE,aAAc,CADd,eAAgB,CADhB,mBAGF,CAEA,gCACE,aAAc,CACd,eACF,CAEA,sBACE,gBACF,CAGA,qBAEE,aAAc,CADd,iBAAkB,CAElB,iBACF,CAGA,yBACE,qBACE,kBACF,CAEA,4BAEE,mBAAoB,CADpB,qBAAsB,CAEtB,QACF,CAEA,YACE,gBACF,CAEA,WACE,WAAY,CACZ,4BACF,CAEA,mBACE,qBAAsB,CACtB,SACF,CACF,CAGA,oBACE,gBACF,CAGA,kDAGE,yBAA0B,CAC1B,kBACF,CAGA,aACE,YACF,CAGA,qBACE,iBAAkB,CAClB,qCACF,CAEA,iBACE,iBAAkB,CAClB,cACF,CAEA,iCACE,wBAAyB,CACzB,aACF,CAEA,+BACE,wBACF", "sources": ["pages/admin/Notifications/AdminNotifications.css"], "sourcesContent": [".admin-notifications {\n  max-width: 1200px;\n  margin: 0 auto;\n  padding: 2rem 1rem;\n}\n\n.admin-notifications-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: flex-start;\n  margin-bottom: 2rem;\n  gap: 2rem;\n}\n\n.page-title {\n  display: flex;\n  align-items: center;\n  gap: 0.75rem;\n  font-size: 2rem;\n  font-weight: 700;\n  color: #1f2937;\n  margin: 0 0 0.5rem 0;\n}\n\n.title-icon {\n  color: #3b82f6;\n}\n\n.page-description {\n  color: #6b7280;\n  font-size: 1rem;\n  margin: 0;\n}\n\n.sent-notifications-card {\n  margin-top: 1rem;\n  border-radius: 12px;\n  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);\n}\n\n.sent-notifications-card .ant-card-head {\n  border-bottom: 1px solid #e5e7eb;\n  padding: 1rem 1.5rem;\n}\n\n.sent-notifications-card .ant-card-head-title {\n  font-weight: 600;\n  color: #1f2937;\n}\n\n.sent-notifications-card .ant-card-body {\n  padding: 0;\n}\n\n.sent-notifications-card .ant-list-item {\n  padding: 1.5rem;\n  border-bottom: 1px solid #f3f4f6;\n}\n\n.sent-notifications-card .ant-list-item:last-child {\n  border-bottom: none;\n}\n\n.notification-meta {\n  margin-top: 0.5rem;\n  color: #6b7280;\n  font-size: 0.875rem;\n}\n\n.meta-icon {\n  margin-right: 0.25rem;\n  vertical-align: middle;\n}\n\n.notification-form {\n  display: flex;\n  flex-direction: column;\n  gap: 1.5rem;\n}\n\n.form-group {\n  display: flex;\n  flex-direction: column;\n  gap: 0.5rem;\n}\n\n.form-group label {\n  font-weight: 600;\n  color: #374151;\n  font-size: 0.875rem;\n}\n\n.form-group .ant-input,\n.form-group .ant-input-affix-wrapper,\n.form-group .ant-select-selector {\n  border-radius: 8px;\n  border: 1px solid #d1d5db;\n  padding: 0.75rem;\n}\n\n.form-group .ant-input:focus,\n.form-group .ant-input-affix-wrapper:focus,\n.form-group .ant-select-focused .ant-select-selector {\n  border-color: #3b82f6;\n  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);\n}\n\n.form-group .ant-input::placeholder {\n  color: #9ca3af;\n}\n\n/* Modal customization */\n.ant-modal-header {\n  border-bottom: 1px solid #e5e7eb;\n  padding: 1.5rem 1.5rem 1rem 1.5rem;\n}\n\n.ant-modal-title {\n  font-weight: 600;\n  color: #1f2937;\n  font-size: 1.25rem;\n}\n\n.ant-modal-body {\n  padding: 1.5rem;\n}\n\n.ant-modal-footer {\n  border-top: 1px solid #e5e7eb;\n  padding: 1rem 1.5rem;\n}\n\n/* Button customization */\n.ant-btn-primary {\n  background-color: #3b82f6;\n  border-color: #3b82f6;\n  border-radius: 8px;\n  font-weight: 500;\n  height: auto;\n  padding: 0.75rem 1.5rem;\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n}\n\n.ant-btn-primary:hover {\n  background-color: #2563eb;\n  border-color: #2563eb;\n}\n\n.ant-btn-text {\n  border-radius: 6px;\n  height: auto;\n  padding: 0.5rem 1rem;\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n}\n\n/* Tag customization */\n.ant-tag {\n  border-radius: 6px;\n  font-weight: 500;\n  font-size: 0.75rem;\n  padding: 0.25rem 0.5rem;\n  border: none;\n}\n\n/* List customization */\n.ant-list-item-meta-title {\n  margin-bottom: 0.5rem;\n  font-weight: 600;\n  color: #1f2937;\n}\n\n.ant-list-item-meta-description {\n  color: #4b5563;\n  line-height: 1.5;\n}\n\n.ant-list-item-action {\n  margin-left: 1rem;\n}\n\n/* Empty state */\n.ant-list-empty-text {\n  padding: 3rem 1rem;\n  color: #9ca3af;\n  text-align: center;\n}\n\n/* Responsive design */\n@media (max-width: 768px) {\n  .admin-notifications {\n    padding: 1rem 0.5rem;\n  }\n  \n  .admin-notifications-header {\n    flex-direction: column;\n    align-items: stretch;\n    gap: 1rem;\n  }\n  \n  .page-title {\n    font-size: 1.5rem;\n  }\n  \n  .ant-modal {\n    margin: 1rem;\n    max-width: calc(100vw - 2rem);\n  }\n  \n  .notification-meta {\n    flex-direction: column;\n    gap: 0.5rem;\n  }\n}\n\n/* Loading states */\n.ant-spin-container {\n  min-height: 200px;\n}\n\n/* Focus states for accessibility */\n.ant-btn:focus,\n.ant-input:focus,\n.ant-select:focus {\n  outline: 2px solid #3b82f6;\n  outline-offset: 2px;\n}\n\n/* Success/Error message styling */\n.ant-message {\n  z-index: 9999;\n}\n\n/* Select dropdown styling */\n.ant-select-dropdown {\n  border-radius: 8px;\n  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);\n}\n\n.ant-select-item {\n  border-radius: 4px;\n  margin: 2px 4px;\n}\n\n.ant-select-item-option-selected {\n  background-color: #eff6ff;\n  color: #1d4ed8;\n}\n\n.ant-select-item-option-active {\n  background-color: #f3f4f6;\n}\n"], "names": [], "sourceRoot": ""}