{"version": 3, "file": "static/js/510.44e94fe3.chunk.js", "mappings": "6JAAA,MAAQA,QAASC,GAAkBC,EAAQ,MAG9BC,EAAYC,UACrB,IAEI,aADuBH,EAAcI,KAAK,0BAA2BC,IACrDC,IACpB,CAAE,MAAOC,GACL,OAAOA,EAAMC,SAASF,IAC1B,GAISG,EAAgBN,UACzB,IAEI,aADuBH,EAAcI,KAAK,+BAAgCM,IAC1DJ,IACpB,CAAE,MAAOC,GACL,OAAOA,EAAMC,SAASF,IAC1B,GAISK,EAAsBR,UAC/B,IAEI,aADuBH,EAAcI,KAAK,uCAAwCC,IAClEC,IACpB,CAAE,MAAOC,GACL,OAAOA,EAAMC,SAASF,IAC1B,GAISM,EAA0BT,UACnC,IAEI,aADuBH,EAAca,IAAI,6CACzBP,IACpB,CAAE,MAAOC,GACL,OAAOA,EAAMC,SAASF,IAC1B,GAaSQ,EAAmBX,iBAAyB,IAAlBY,EAAOC,UAAAC,OAAA,QAAAC,IAAAF,UAAA,GAAAA,UAAA,GAAG,CAAC,EAC9C,IACI,MAAMG,EAAS,IAAIC,gBACfL,EAAQM,OAAOF,EAAOG,OAAO,QAASP,EAAQM,OAC9CN,EAAQQ,aAAaJ,EAAOG,OAAO,cAAeP,EAAQQ,aAC1DR,EAAQS,aAAaL,EAAOG,OAAO,cAAeP,EAAQS,aAC1DT,EAAQU,cAAcN,EAAOG,OAAO,eAAgBP,EAAQU,cAC5DV,EAAQW,iBAAiBP,EAAOG,OAAO,kBAAmBP,EAAQW,iBAGtE,aADuB1B,EAAca,IAAI,4BAADc,OAA6BR,EAAOS,cAC5DtB,IACpB,CAAE,MAAOC,GACL,OAAOA,EAAMC,SAASF,IAC1B,CACJ,EAEauB,EAAiB1B,eAAO2B,GAAyB,IAAjBC,EAAOf,UAAAC,OAAA,QAAAC,IAAAF,UAAA,GAAAA,UAAA,GAAG,EACnD,IAEI,aADuBhB,EAAca,IAAI,0BAADc,OAA2BG,EAAM,aAAAH,OAAYI,KACrEzB,IACpB,CAAE,MAAOC,GACL,OAAOA,EAAMC,SAASF,IAC1B,CACJ,C,2ICnDA,MAwdM0B,EAAWC,IAAmD,IAADC,EAAA,IAAjD,KAAEC,EAAI,WAAEC,EAAU,QAAEC,EAAO,OAAEC,EAAM,MAAEC,GAAON,EAgC5D,OAAKE,GAAwB,kBAATA,GASlBK,EAAAA,EAAAA,MAAA,OACEC,UAAU,oIACVC,MAAO,CACLC,WAAY,iEACZC,OAAQR,EACoB,SAAvBA,EAAWS,QAAqB,oBAAsB,oBACvD,oBACJC,UAAWV,EACiB,SAAvBA,EAAWS,QACR,qCACA,oCACJ,qCACJE,UAAWC,OAAOC,YAAc,IAAM,QAAU,QAChDC,OAAQ,QACRC,SAAA,EAGFC,EAAAA,EAAAA,KAAA,OAAKX,UAAU,mBAAkBU,UAC/BC,EAAAA,EAAAA,KAAA,MACEX,UAAU,8BACVC,MAAO,CACLW,MAAO,UACPC,WAAY,4BACZC,WAAY,MACZC,SAAUR,OAAOC,YAAc,IAAM,OAAS,QAC9CE,SAEoB,kBAAdhB,EAAKsB,KAAoBtB,EAAKsB,KAAO,qBAKjDL,EAAAA,EAAAA,KAAA,OAAKX,UAAU,mBAAkBU,SAC9Bf,GACCI,EAAAA,EAAAA,MAAA,OAAKC,UAAU,yCAAwCU,SAAA,EACrDC,EAAAA,EAAAA,KAAA,OACEX,UAAU,gEACVC,MAAO,CACLgB,gBAAwC,SAAvBtB,EAAWS,QAAqB,UAAY,UAC7DW,SAAUR,OAAOC,YAAc,IAAM,MAAQ,QAC7CE,SAEsB,SAAvBf,EAAWS,QAAqB,gBAAa,mBAEhDL,EAAAA,EAAAA,MAAA,OACEC,UAAU,iEACVC,MAAO,CACLgB,gBAAiB,UACjBL,MAAO,UACPG,SAAUR,OAAOC,YAAc,IAAM,MAAQ,QAC7CE,SAAA,CAEgC,kBAA1Bf,EAAWuB,WAA0BvB,EAAWuB,WAAa,EAAE,WAI3EP,EAAAA,EAAAA,KAAA,OACEX,UAAU,gEACVC,MAAO,CACLgB,gBAAiB,UACjBF,SAAUR,OAAOC,YAAc,IAAM,MAAQ,QAC7CE,SACH,kCAMLC,EAAAA,EAAAA,KAAA,OAAKX,UAAU,mBAAkBU,UAC/BX,EAAAA,EAAAA,MAAA,OAAKC,UAAU,SAAQU,SAAA,EAGrBX,EAAAA,EAAAA,MAAA,OAAKC,UAAU,iCAAgCU,SAAA,EAC7CX,EAAAA,EAAAA,MAAA,OACEC,UAAU,gEACVC,MAAO,CACLC,WAAY,8CACZiB,YAAa,WACbT,SAAA,EAEFC,EAAAA,EAAAA,KAACS,EAAAA,IAAc,CAACpB,UAAU,UAAUC,MAAO,CAAEW,MAAO,cACpDD,EAAAA,EAAAA,KAAA,QACEX,UAAU,YACVC,MAAO,CACLW,MAAO,UACPG,SAAUR,OAAOC,YAAc,IAAM,OAAS,QAC9CE,SAEDW,MAAMC,QAAQ5B,EAAK6B,WAAa7B,EAAK6B,UAAU/C,OAAS,QAG7DuB,EAAAA,EAAAA,MAAA,OACEC,UAAU,gEACVC,MAAO,CACLC,WAAY,8CACZiB,YAAa,WACbT,SAAA,EAEFC,EAAAA,EAAAA,KAACa,EAAAA,IAAO,CAACxB,UAAU,UAAUC,MAAO,CAAEW,MAAO,cAC7CD,EAAAA,EAAAA,KAAA,QACEX,UAAU,YACVC,MAAO,CACLW,MAAO,UACPG,SAAUR,OAAOC,YAAc,IAAM,OAAS,QAC9CE,SACH,cAQLX,EAAAA,EAAAA,MAAA,OAAKC,UAAU,wDAAuDU,SAAA,EAEpEX,EAAAA,EAAAA,MAAA,QACEC,UAAU,qEACVC,MAAO,CACLC,WAAY,8CACZa,SAAUR,OAAOC,YAAc,IAAM,MAAQ,QAC7CE,SAAA,CACH,eACIhB,EAAK+B,OAAS,cAInB1B,EAAAA,EAAAA,MAAA,QACEC,UAAU,qEACVC,MAAO,CACLC,WAAY,8CACZa,SAAUR,OAAOC,YAAc,IAAM,MAAQ,QAC7CE,SAAA,CACH,eAC0B,kBAAfhB,EAAKgC,OAA4C,kBAAfhC,EAAKgC,MAC/B,YAAfhC,EAAK+B,MAAmB,SAAAvC,OAAYQ,EAAKgC,OAAUhC,EAAKgC,MAAS,UAItE3B,EAAAA,EAAAA,MAAA,QACEC,UAAU,qEACVC,MAAO,CACLC,WAAY,8CACZa,SAAUR,OAAOC,YAAc,IAAM,MAAQ,QAC7CE,SAAA,CACH,eACIhB,EAAKiC,UAAY,cAItB5B,EAAAA,EAAAA,MAAA,QACEC,UAAU,qEACVC,MAAO,CACLC,WAAYR,EAAKkC,OAAwB,YAAflC,EAAKkC,OAAsC,KAAflC,EAAKkC,MACvD,8CACA,8CACJb,SAAUR,OAAOC,YAAc,IAAM,MAAQ,QAC7CE,SAAA,CACH,eACIhB,EAAKkC,OAAS,qBAQxBjC,GAAoC,kBAAfA,IACpBI,EAAAA,EAAAA,MAAA,OACEC,UAAU,uCACVC,MAAO,CACLC,WAAmC,SAAvBP,EAAWS,QACnB,qDACA,qDACJe,YAAoC,SAAvBxB,EAAWS,QAAqB,UAAY,WACzDM,SAAA,EAEFX,EAAAA,EAAAA,MAAA,OAAKC,UAAU,yCAAwCU,SAAA,EACrDX,EAAAA,EAAAA,MAAA,OAAKC,UAAU,0BAAyBU,SAAA,CACd,SAAvBf,EAAWS,SACVO,EAAAA,EAAAA,KAAA,OACEX,UAAU,2FACVC,MAAO,CACLC,WAAY,8CACZiB,YAAa,WACbT,UAEFC,EAAAA,EAAAA,KAACkB,EAAAA,IAAO,CAAC7B,UAAU,oBAAoBC,MAAO,CAAEW,MAAO,gBAGzDD,EAAAA,EAAAA,KAAA,OACEX,UAAU,2FACVC,MAAO,CACLC,WAAY,8CACZiB,YAAa,WACbT,UAEFC,EAAAA,EAAAA,KAACmB,EAAAA,IAAG,CAAC9B,UAAU,oBAAoBC,MAAO,CAAEW,MAAO,gBAGvDb,EAAAA,EAAAA,MAAA,OAAAW,SAAA,EACEC,EAAAA,EAAAA,KAAA,QAAMX,UAAU,oBAAoBC,MAAO,CAAEW,MAAO,WAAYF,SAAC,8BACjEC,EAAAA,EAAAA,KAAA,OAAKX,UAAU,UAAUC,MAAO,CAAEW,MAAO,WAAYF,SAClD,IAAIqB,KAAKpC,EAAWqC,aAAerC,EAAWsC,WAAaF,KAAKG,OAAOC,8BAI9EpC,EAAAA,EAAAA,MAAA,QACEC,UAAU,+BACVC,MAAO,CACLW,MAA8B,SAAvBjB,EAAWS,QAAqB,UAAY,WACnDM,SAAA,CAEgC,kBAA1Bf,EAAWuB,WAA0BvB,EAAWuB,WAAa,EAAE,WAK3EnB,EAAAA,EAAAA,MAAA,OAAKC,UAAU,sCAAqCU,SAAA,EAElDX,EAAAA,EAAAA,MAAA,OACEC,UAAU,gEACVC,MAAO,CACLC,WAAY,8CACZiB,YAAa,WACbT,SAAA,EAEFX,EAAAA,EAAAA,MAAA,OAAKC,UAAU,0BAAyBU,SAAA,EACtCC,EAAAA,EAAAA,KAACkB,EAAAA,IAAO,CAAC7B,UAAU,UAAUC,MAAO,CAAEW,MAAO,cAC7CD,EAAAA,EAAAA,KAAA,QAAMX,UAAU,oBAAoBC,MAAO,CAAEW,MAAO,WAAYF,SACxB,kBAA9Bf,EAAWyC,eAA8BzC,EAAWyC,eAAiB,QAGjFrC,EAAAA,EAAAA,MAAA,OAAKC,UAAU,0BAAyBU,SAAA,EACtCC,EAAAA,EAAAA,KAACmB,EAAAA,IAAG,CAAC9B,UAAU,UAAUC,MAAO,CAAEW,MAAO,cACzCD,EAAAA,EAAAA,KAAA,QAAMX,UAAU,oBAAoBC,MAAO,CAAEW,MAAO,WAAYF,WAC9C,QAAdjB,EAAAC,EAAK6B,iBAAS,IAAA9B,OAAA,EAAdA,EAAgBjB,SAAU,IAA2C,kBAA9BmB,EAAWyC,eAA8BzC,EAAWyC,eAAiB,YAMpHrC,EAAAA,EAAAA,MAAA,OACEC,UAAU,gEACVC,MAAO,CACLC,WAAY,qDACZiB,YAAa,WACbT,SAAA,EAEFC,EAAAA,EAAAA,KAAA,QAAMX,UAAU,UAASU,SAAC,YAC1BC,EAAAA,EAAAA,KAAA,QAAMX,UAAU,oBAAoBC,MAAO,CAAEW,MAAO,WAAYF,SAC7Df,EAAW0C,UAAY1C,EAAW2C,QAAU,OAKhD3C,EAAW4C,WAAa5C,EAAW4C,UAAY,IAC9CxC,EAAAA,EAAAA,MAAA,OACEC,UAAU,gEACVC,MAAO,CACLC,WAAY,qDACZiB,YAAa,WACbT,SAAA,EAEFC,EAAAA,EAAAA,KAACa,EAAAA,IAAO,CAACxB,UAAU,UAAUC,MAAO,CAAEW,MAAO,cAC7CD,EAAAA,EAAAA,KAAA,QAAMX,UAAU,oBAAoBC,MAAO,CAAEW,MAAO,WAAYF,SAzShD8B,KAE5B,IAAKA,GAAmC,IAAlBA,EAAqB,MAAO,KAElD,IAAIC,EAAeD,EAQnB,GAL6B,kBAAlBA,IACTC,EAAeC,SAASF,EAAe,KAIrCG,MAAMF,IAAiBA,EAAe,EAAG,MAAO,KAEpD,MAAMG,EAAUC,KAAKC,MAAML,EAAe,IACpCM,EAAUN,EAAe,GAE/B,OAAIG,EAAU,EACN,GAAN1D,OAAU0D,EAAO,MAAA1D,OAAK6D,EAAO,KAEzB,GAAN7D,OAAUsD,EAAa,MAsRRQ,CAAqBrD,EAAW4C,uBAS7C5B,EAAAA,EAAAA,KAAA,OAAKX,UAAU,YAEfD,EAAAA,EAAAA,MAAA,OAAKC,UAAU,kBAAiBU,SAAA,EAE9BX,EAAAA,EAAAA,MAAA,UACEkD,QAASA,IAAMrD,EAAQF,GACvBM,UAAU,0LACVC,MAAO,CACLC,WAAYP,EACR,8CACA,8CACJoB,SAAU,OACVT,UAAW,QACXI,SAAA,EAEFC,EAAAA,EAAAA,KAACuC,EAAAA,IAAY,CAAClD,UAAU,YACvBL,EAAa,2BAAmB,6BAIlCA,IACCgB,EAAAA,EAAAA,KAAA,UACEsC,QAASA,IAAMpD,EAAOH,GACtBM,UAAU,4IACVC,MAAO,CACLC,WAAY,8CACZa,SAAU,OACVT,UAAW,QAEb6C,MAAM,eAAczC,UAEpBC,EAAAA,EAAAA,KAACyC,EAAAA,IAAQ,CAACpD,UAAU,qBAxT1BW,EAAAA,EAAAA,KAAA,OAAKX,UAAU,4DAA2DU,UACxEC,EAAAA,EAAAA,KAAA,KAAGX,UAAU,gBAAeU,SAAC,uBA2T3B,EAIV,EA1zBa2C,KACX,MAAOC,EAAOC,IAAYC,EAAAA,EAAAA,UAAS,KAC5BC,EAAeC,IAAoBF,EAAAA,EAAAA,UAAS,KAC5CG,EAAYC,IAAiBJ,EAAAA,EAAAA,WAAS,IAEpCK,aAAaC,QAAQ,qBAAuB,MAE9CC,EAAeC,IAAoBR,EAAAA,EAAAA,WAAS,IAE1CK,aAAaC,QAAQ,wBAA0B,MAEjDG,EAAaC,IAAkBV,EAAAA,EAAAA,UAAS,CAAC,IACzCW,EAASC,IAAcZ,EAAAA,EAAAA,WAAS,IAChCa,EAAaC,IAAkBd,EAAAA,EAAAA,UAAS,MACzCe,GAAWC,EAAAA,EAAAA,MACXC,GAAWC,EAAAA,EAAAA,OACX,KAAEC,IAASC,EAAAA,EAAAA,KAAaC,GAAUA,EAAMF,QAe9CG,EAAAA,EAAAA,YAAU,KAERC,SAASC,KAAKC,UAAUC,IAAI,oBAGrB,KACLH,SAASC,KAAKC,UAAUE,OAAO,mBAAmB,IAInD,KAGHL,EAAAA,EAAAA,YAAU,KACHH,GACHS,GACF,GACC,CAACT,IAGJ,MAYMS,EAAwBA,KAC5BxB,EAAc,IACdI,EAAiB,IACjBH,aAAawB,WAAW,oBACxBxB,aAAawB,WAAW,sBAAsB,EAG1CC,GAAiBC,EAAAA,EAAAA,cAAY7H,UACjC,IACE,GAAS,OAAJiH,QAAI,IAAJA,IAAAA,EAAMa,IAAK,OAEhB,MAAMzH,QAAiBG,EAAAA,EAAAA,IAAoB,CAAEmB,OAAQsF,EAAKa,MAE1D,GAAIzH,EAAS0H,QAAS,CACpB,MAAMC,EAAa,CAAC,EACpB3H,EAASF,KAAK8H,SAAQC,IAAW,IAADC,EAC9B,MAAMC,EAAoB,QAAdD,EAAGD,EAAOG,YAAI,IAAAF,OAAA,EAAXA,EAAaL,IAC5B,IAAKM,IAAWF,EAAOI,OAAQ,OAG/B,MAAMA,EAASJ,EAAOI,SAEjBN,EAAWI,IAAW,IAAI/D,KAAK6D,EAAO3D,WAAa,IAAIF,KAAK2D,EAAWI,GAAQ7D,cAClFyD,EAAWI,GAAU,CACnB1F,QAAS4F,EAAO5F,QAChBc,WAAY8E,EAAO9E,WACnBkB,eAAgB4D,EAAO5D,eACvB6D,aAAcD,EAAOC,aACrBC,eAAgBF,EAAOE,eACvBC,cAAeH,EAAOG,cACtBC,WAAYJ,EAAOI,WACnBC,MAAOL,EAAOK,MACd/D,OAAQ0D,EAAO1D,OACfD,SAAU2D,EAAO3D,UAAY2D,EAAO1D,QAAU0D,EAAOM,UAAY,EACjE/D,UAAWqD,EAAOrD,UAClBP,YAAa4D,EAAO3D,WAExB,IAEFiC,EAAewB,EACjB,CACF,CAAE,MAAO5H,GACPyI,QAAQzI,MAAM,+BAAgCA,EAChD,IACC,CAAK,OAAJ6G,QAAI,IAAJA,OAAI,EAAJA,EAAMa,MAGJgB,GAAWjB,EAAAA,EAAAA,cAAY7H,UACzB,IAEE,IAAKiH,EAEH,YADA4B,QAAQE,IAAI,4CAKd,MAAMC,GAAgB,OAAJ/B,QAAI,IAAJA,OAAI,EAAJA,EAAMlD,QAAS,UAC3BkF,EAAQ,oBAAAzH,OAAuBwH,GAC/BE,EAAY,yBAAA1H,OAA4BwH,GAG5B,CAAC,UAAW,YAAa,WACjCf,SAAQlE,IACZA,IAAUiF,IACZ7C,aAAawB,WAAW,oBAADnG,OAAqBuC,IAC5CoC,aAAawB,WAAW,yBAADnG,OAA0BuC,IACnD,IAIF,MAAMoF,EAAchD,aAAaC,QAAQ6C,GACnCG,EAAYjD,aAAaC,QAAQ8C,GACjC1E,EAAMH,KAAKG,MAGjB,GAAI2E,GAAeC,GAAc5E,EAAMQ,SAASoE,GAAc,IAAQ,CACpE,MAAMC,EAASC,KAAKC,MAAMJ,GAK1B,OAJAtD,EAASwD,GACTzC,EAAe,IAAIvC,KAAKW,SAASoE,KACjC1C,GAAW,QACXmC,QAAQE,IAAI,uCAADvH,OAA8BwH,EAAS,WAAWK,EAAOvI,OAEtE,CAEAiG,GAASyC,EAAAA,EAAAA,OACT,MAAMnJ,QAAiBoJ,EAAAA,EAAAA,MAGvB,GAFA1C,GAAS2C,EAAAA,EAAAA,OAELrJ,EAAS0H,QAAS,CACpBc,QAAQE,IAAI,sBAAuB1I,EAASF,KAAKW,QACjD+H,QAAQE,IAAI,cAAmB,OAAJ9B,QAAI,IAAJA,OAAI,EAAJA,EAAMlD,OAGjC,MAAM4F,EAAiBtJ,EAASF,KAAKyJ,QAAOvB,MACrCA,EAAKtE,OAAUkD,GAASA,EAAKlD,QAC3BsE,EAAKtE,MAAM8F,gBAAkB5C,EAAKlD,MAAM8F,gBAGjDhB,QAAQE,IAAI,oCAAqCY,EAAe7I,QAChE,MAAMgJ,EAAcH,EAAeI,MAAK,CAACC,EAAGC,IAAM,IAAI5F,KAAK4F,EAAE1F,WAAa,IAAIF,KAAK2F,EAAEzF,aACrFsB,EAASiE,GACTlD,EAAe,IAAIvC,MAGnB8B,aAAa+D,QAAQjB,EAAUK,KAAKa,UAAUL,IAC9C3D,aAAa+D,QAAQhB,EAAc7E,KAAKG,MAAM/C,YAGtC,OAAJwF,QAAI,IAAJA,GAAAA,EAAMjD,OACRsC,EAAiB8D,OAAOnD,EAAKjD,OAEjC,MACEqG,EAAAA,GAAQjK,MAAMC,EAASgK,QAE3B,CAAE,MAAOjK,GACP2G,GAAS2C,EAAAA,EAAAA,OACTW,EAAAA,GAAQjK,MAAMA,EAAMiK,QACtB,CAAC,QACC3D,GAAW,EACb,IACD,CAACK,EAAUE,KAEdG,EAAAA,EAAAA,YAAU,KArKU,CAAC,UAAW,YAAa,WACjCa,SAAQlE,IAChBoC,aAAawB,WAAW,oBAADnG,OAAqBuC,IAC5CoC,aAAawB,WAAW,yBAADnG,OAA0BuC,GAAQ,IAG3DoC,aAAawB,WAAW,oBACxBxB,aAAawB,WAAW,yBAkKxBmB,IACAlB,GAAgB,GACf,CAACkB,EAAUlB,KAGdR,EAAAA,EAAAA,YAAU,KAER,MAAMkD,EAAsBA,KAC1BzB,QAAQE,IAAI,wEACZnB,GAAgB,EAIZ2C,EAAgBA,KACpB1B,QAAQE,IAAI,mEACR9B,GACFW,GACF,EAII4C,EAAoBA,KACxB3B,QAAQE,IAAI,0EACZnB,GAAgB,EAOlB,OAJA/E,OAAO4H,iBAAiB,gBAAiBH,GACzCzH,OAAO4H,iBAAiB,QAASD,GACjC3H,OAAO4H,iBAAiB,iBAAkBF,GAEnC,KACL1H,OAAO6H,oBAAoB,gBAAiBJ,GAC5CzH,OAAO6H,oBAAoB,QAASF,GACpC3H,OAAO6H,oBAAoB,iBAAkBH,EAAc,CAC5D,GACA,KAIHnD,EAAAA,EAAAA,YAAU,KACRyB,QAAQE,IAAI,mBAAoB,CAAEnD,MAAOA,EAAM9E,OAAQmF,aAAYI,kBACnE,IAAIsE,EAAW/E,EACf,GAAIK,EAAY,CACd,MAAM2E,EAAc3E,EAAW4D,cAC/Bc,EAAWA,EAASf,QAAOvB,GAEA,CACvBA,EAAK/E,KACL+E,EAAKwC,QACLxC,EAAKnE,MACLmE,EAAKyC,YACLzC,EAAKpE,SACLoE,EAAKtE,UAEDsE,EAAKxE,WAAa,IAAIkH,KAAIC,GAAKA,EAAEC,mBACjC5C,EAAKxE,WAAa,IAAIkH,KAAIC,GAAKA,EAAEH,cACjCxC,EAAKxE,WAAa,IAAIkH,KAAIC,GAAKA,EAAE9G,SAGfgH,MAAKC,GAC3BA,GAASA,EAAM1J,WAAWoI,cAAcuB,SAASR,MAGvD,CACIvE,IACFsE,EAAWA,EAASf,QAAOvB,GAAQ+B,OAAO/B,EAAKrE,SAAWoG,OAAO/D,MAEnEsE,EAASZ,MAAK,CAACC,EAAGC,IAAM,IAAI5F,KAAK4F,EAAE1F,WAAa,IAAIF,KAAK2F,EAAEzF,aAC3DsE,QAAQE,IAAI,yBAA0B4B,EAAS7J,QAC/CkF,EAAiB2E,EAAS,GACzB,CAAC/E,EAAOK,EAAYI,IAEvB,MAAMgF,EAAmB,IAAI,IAAIC,IAAI1F,EAAMmF,KAAIQ,GAAKA,EAAEvH,QAAO4F,OAAO4B,WAAWzB,OAEzE0B,EAAmBzJ,IACvB,IAAKA,IAASA,EAAK8F,IAEjB,YADAuC,EAAAA,GAAQjK,MAAM,4CAKM,oBACHsL,KAAK1J,EAAK8F,MAK7B6D,EAAAA,EAAAA,kBAAgB,KACd9E,EAAS,SAADrF,OAAUQ,EAAK8F,IAAG,SAAQ,IALlCuC,EAAAA,GAAQjK,MAAM,4CAMd,EAuBJ,OAAIqG,GAEAxD,EAAAA,EAAAA,KAAA,OAAKX,UAAU,6FAA4FU,UACzGX,EAAAA,EAAAA,MAAA,OAAKC,UAAU,cAAaU,SAAA,EAC1BC,EAAAA,EAAAA,KAAA,OAAKX,UAAU,iFACfW,EAAAA,EAAAA,KAAA,KAAGX,UAAU,gBAAeU,SAAC,6BAOnCX,EAAAA,EAAAA,MAAAuJ,EAAAA,SAAA,CAAA5I,SAAA,EAEEC,EAAAA,EAAAA,KAAA,SAAO4I,KAAG,EAACC,QAAM,EAAA9I,SAAA,iqCAyCjBC,EAAAA,EAAAA,KAAA,OAAKX,UAAU,qFAAoFU,UACjGX,EAAAA,EAAAA,MAAA,OAAKC,UAAU,mEAAkEU,SAAA,EAIjFC,EAAAA,EAAAA,KAAA,OAAKX,UAAU,6CAA4CU,UACzDX,EAAAA,EAAAA,MAAA,OAAKC,UAAU,2CAA0CU,SAAA,EAEvDX,EAAAA,EAAAA,MAAA,OAAKC,UAAU,4FAA2FU,SAAA,EACxGX,EAAAA,EAAAA,MAAA,OAAKC,UAAU,gDAA+CU,SAAA,EAC5DX,EAAAA,EAAAA,MAAA,OAAKC,UAAU,0BAAyBU,SAAA,EACtCC,EAAAA,EAAAA,KAAA,OAAKX,UAAU,0CACfD,EAAAA,EAAAA,MAAA,QAAMC,UAAU,cAAaU,SAAA,CAAC,WAAY,OAAJiE,QAAI,IAAJA,OAAI,EAAJA,EAAMlD,QAAS,oBAEvD1B,EAAAA,EAAAA,MAAA,OAAKC,UAAU,0BAAyBU,SAAA,EACtCC,EAAAA,EAAAA,KAAA,OAAKX,UAAU,2CACfD,EAAAA,EAAAA,MAAA,QAAAW,SAAA,CAAO+C,EAAcjF,OAAO,8BAG/B6F,IACCtE,EAAAA,EAAAA,MAAA,OAAKC,UAAU,6DAA4DU,SAAA,EACzEC,EAAAA,EAAAA,KAAA,OAAKX,UAAU,sCACfD,EAAAA,EAAAA,MAAA,QAAAW,SAAA,CAAM,YAAU2D,EAAYoF,+BAMlC1J,EAAAA,EAAAA,MAAA,OAAKC,UAAU,2CAA0CU,SAAA,EACvDX,EAAAA,EAAAA,MAAA,OAAKC,UAAU,kBAAiBU,SAAA,EAC9BC,EAAAA,EAAAA,KAAA,OAAKX,UAAU,uEAAsEU,UACnFC,EAAAA,EAAAA,KAAC+I,EAAAA,IAAQ,CAAC1J,UAAU,6BAEtBW,EAAAA,EAAAA,KAAA,SACEgJ,KAAK,OACLC,YAAY,+CACZC,MAAOlG,EACPmG,SAAWb,IAAMc,OAtVLF,EAsVwBZ,EAAEe,OAAOH,MArV3DjG,EAAciG,QACdhG,aAAa+D,QAAQ,mBAAoBiC,GAFfA,KAsVwC,EACpD7J,UAAU,iMAGdW,EAAAA,EAAAA,KAAA,OAAKX,UAAU,eAAcU,UAC3BX,EAAAA,EAAAA,MAAA,OAAKC,UAAU,WAAUU,SAAA,EACvBC,EAAAA,EAAAA,KAAA,OAAKX,UAAU,uEAAsEU,UACnFC,EAAAA,EAAAA,KAACsJ,EAAAA,IAAQ,CAACjK,UAAU,6BAEtBD,EAAAA,EAAAA,MAAA,UACE8J,MAAO9F,EACP+F,SAAWb,IAAMiB,OA3VRL,EA2V0BZ,EAAEe,OAAOH,MA1V5D7F,EAAiB6F,QACjBhG,aAAa+D,QAAQ,sBAAuBiC,GAFnBA,KA2V0C,EACnD7J,UAAU,2MAA0MU,SAAA,EAEpNC,EAAAA,EAAAA,KAAA,UAAQkJ,MAAM,GAAEnJ,SAAC,gBAChBqI,EAAiBN,KAAKzI,IACrBW,EAAAA,EAAAA,KAAA,UAAwBkJ,MAAO7J,EAAUU,SACtB,aAAZ,OAAJiE,QAAI,IAAJA,OAAI,EAAJA,EAAMlD,OAAmB,SAAAvC,OAAYc,GAAcA,GADzCA,cASnB2D,GAAcI,KACdhE,EAAAA,EAAAA,MAAA,UACEkD,QAASmC,EACTpF,UAAU,iOACVmD,MAAM,+BAA8BzC,SAAA,EAEpCC,EAAAA,EAAAA,KAACmB,EAAAA,IAAG,CAAC9B,UAAU,aACfW,EAAAA,EAAAA,KAAA,QAAMX,UAAU,kCAAiCU,SAAC,sBAQ5DC,EAAAA,EAAAA,KAAA,OAAKX,UAAU,cAAcC,MAAO,CAAEkK,UAAW,UAAWzJ,SAGhC,IAAzB+C,EAAcjF,QACbmC,EAAAA,EAAAA,KAAA,OAAKX,UAAU,4BAA2BU,UACxCX,EAAAA,EAAAA,MAAA,OAAKC,UAAU,6DAA4DU,SAAA,EACzEC,EAAAA,EAAAA,KAACyJ,EAAAA,IAAQ,CAACpK,UAAU,0DACpBW,EAAAA,EAAAA,KAAA,MAAIX,UAAU,sDAAqDU,SAAC,sBACpEC,EAAAA,EAAAA,KAAA,KAAGX,UAAU,qCAAoCU,SAC9CiD,GAAcI,EACX,gDACA,iEAMVpD,EAAAA,EAAAA,KAAA,OAAKX,UAAU,YAAWU,SACvB+C,EAAcgF,KAAI,CAAC/I,EAAMI,KACxBa,EAAAA,EAAAA,KAACpB,EAAQ,CAEPG,KAAMA,EACNC,WAAYsE,EAAYvE,EAAK8F,KAC7B6E,aAAa,EACbzK,QAASuJ,EACTtJ,OAAQA,IA/KAH,KACtB,IAAKA,IAASA,EAAK8F,IAEjB,YADAuC,EAAAA,GAAQjK,MAAM,4CAIGmG,EAAYvE,EAAK8F,MAKpC6D,EAAAA,EAAAA,kBAAgB,KACd9E,EAAS,SAADrF,OAAUQ,EAAK8F,IAAG,WAAU,IAJpCuC,EAAAA,GAAQuC,KAAK,uDAKb,EAkK0BC,CAAe7K,GAC7BI,MAAOA,GANFJ,EAAK8F,kBAcrB,C", "sources": ["apicalls/reports.js", "pages/user/Quiz/index.js"], "sourcesContent": ["const { default: axiosInstance } = require(\".\");\r\n\r\n// add report\r\nexport const addReport = async (payload) => {\r\n    try {\r\n        const response = await axiosInstance.post(\"/api/reports/add-report\", payload);\r\n        return response.data;\r\n    } catch (error) {\r\n        return error.response.data;\r\n    }\r\n}\r\n\r\n// get all reports\r\nexport const getAllReports = async (filters) => {\r\n    try {\r\n        const response = await axiosInstance.post(\"/api/reports/get-all-reports\", filters);\r\n        return response.data;\r\n    } catch (error) {\r\n        return error.response.data;\r\n    }\r\n}\r\n\r\n// get all reports by user\r\nexport const getAllReportsByUser = async (payload) => {\r\n    try {\r\n        const response = await axiosInstance.post(\"/api/reports/get-all-reports-by-user\", payload);\r\n        return response.data;\r\n    } catch (error) {\r\n        return error.response.data;\r\n    }\r\n}\r\n\r\n// get all reports for ranking (legacy)\r\nexport const getAllReportsForRanking = async (filters) => {\r\n    try {\r\n        const response = await axiosInstance.get(\"/api/reports/get-all-reports-for-ranking\");\r\n        return response.data;\r\n    } catch (error) {\r\n        return error.response.data;\r\n    }\r\n}\r\n\r\n// Enhanced XP-based ranking endpoints\r\nexport const getEnhancedLeaderboard = async (level = 'all', limit = 1000) => {\r\n    try {\r\n        const response = await axiosInstance.get(`/api/quiz/enhanced-leaderboard?level=${level}&limit=${limit}`);\r\n        return response.data;\r\n    } catch (error) {\r\n        return error.response.data;\r\n    }\r\n}\r\n\r\nexport const getXPLeaderboard = async (options = {}) => {\r\n    try {\r\n        const params = new URLSearchParams();\r\n        if (options.limit) params.append('limit', options.limit);\r\n        if (options.classFilter) params.append('classFilter', options.classFilter);\r\n        if (options.levelFilter) params.append('levelFilter', options.levelFilter);\r\n        if (options.seasonFilter) params.append('seasonFilter', options.seasonFilter);\r\n        if (options.includeInactive) params.append('includeInactive', options.includeInactive);\r\n\r\n        const response = await axiosInstance.get(`/api/quiz/xp-leaderboard?${params.toString()}`);\r\n        return response.data;\r\n    } catch (error) {\r\n        return error.response.data;\r\n    }\r\n}\r\n\r\nexport const getUserRanking = async (userId, context = 5) => {\r\n    try {\r\n        const response = await axiosInstance.get(`/api/quiz/user-ranking/${userId}?context=${context}`);\r\n        return response.data;\r\n    } catch (error) {\r\n        return error.response.data;\r\n    }\r\n}\r\n\r\nexport const getClassRankings = async (className, limit = 50) => {\r\n    try {\r\n        const response = await axiosInstance.get(`/api/quiz/class-rankings/${className}?limit=${limit}`);\r\n        return response.data;\r\n    } catch (error) {\r\n        return error.response.data;\r\n    }\r\n}\r\n\r\n// XP Dashboard endpoints\r\nexport const getXPDashboard = async () => {\r\n    try {\r\n        const response = await axiosInstance.get(\"/api/xp-dashboard/dashboard\");\r\n        return response.data;\r\n    } catch (error) {\r\n        return error.response.data;\r\n    }\r\n}\r\n\r\nexport const getClassLeaderboard = async () => {\r\n    try {\r\n        const response = await axiosInstance.get(\"/api/xp-dashboard/class-leaderboard\");\r\n        return response.data;\r\n    } catch (error) {\r\n        return error.response.data;\r\n    }\r\n}\r\n", "import React, { useState, useEffect, useCallback, startTransition } from 'react';\r\nimport { useNavigate } from 'react-router-dom';\r\nimport { useDispatch, useSelector } from 'react-redux';\r\nimport { message } from 'antd';\r\nimport {\r\n  Tb<PERSON><PERSON>ch,\r\n  Tb<PERSON><PERSON><PERSON>,\r\n  TbClock,\r\n  TbQuestionMark,\r\n  TbTrophy,\r\n  TbPlayerPlay,\r\n  TbBrain,\r\n  TbTarget,\r\n  TbCheck,\r\n  TbX,\r\n  TbStar,\r\n  TbHome,\r\n  TbBolt\r\n} from 'react-icons/tb';\r\nimport { getAllExams } from '../../../apicalls/exams';\r\nimport { getAllReportsByUser } from '../../../apicalls/reports';\r\nimport { HideLoading, ShowLoading } from '../../../redux/loaderSlice';\r\nimport './animations.css';\r\n\r\nconst Quiz = () => {\r\n  const [exams, setExams] = useState([]);\r\n  const [filteredExams, setFilteredExams] = useState([]);\r\n  const [searchTerm, setSearchTerm] = useState(() => {\r\n    // Restore search term from localStorage\r\n    return localStorage.getItem('quiz-search-term') || '';\r\n  });\r\n  const [selectedClass, setSelectedClass] = useState(() => {\r\n    // Restore selected class from localStorage\r\n    return localStorage.getItem('quiz-selected-class') || '';\r\n  });\r\n  const [userResults, setUserResults] = useState({});\r\n  const [loading, setLoading] = useState(true);\r\n  const [lastRefresh, setLastRefresh] = useState(null);\r\n  const navigate = useNavigate();\r\n  const dispatch = useDispatch();\r\n  const { user } = useSelector((state) => state.user);\r\n\r\n  // Function to clear all quiz caches\r\n  const clearAllQuizCaches = () => {\r\n    const allLevels = ['primary', 'secondary', 'advance'];\r\n    allLevels.forEach(level => {\r\n      localStorage.removeItem(`user_exams_cache_${level}`);\r\n      localStorage.removeItem(`user_exams_cache_time_${level}`);\r\n    });\r\n    // Also clear old cache keys for backward compatibility\r\n    localStorage.removeItem('user_exams_cache');\r\n    localStorage.removeItem('user_exams_cache_time');\r\n  };\r\n\r\n  // Remove white space on component mount and handle cleanup\r\n  useEffect(() => {\r\n    // Add class to body to remove spacing\r\n    document.body.classList.add('quiz-page-active');\r\n\r\n    // Cleanup on unmount (when user navigates away or logs out)\r\n    return () => {\r\n      document.body.classList.remove('quiz-page-active');\r\n      // Note: We don't clear localStorage here to maintain search persistence\r\n      // Search is only cleared on manual refresh or explicit clear action\r\n    };\r\n  }, []);\r\n\r\n  // Clear search when user changes (logout scenario)\r\n  useEffect(() => {\r\n    if (!user) {\r\n      clearSearchAndFilters();\r\n    }\r\n  }, [user]);\r\n\r\n  // Handle search term change with localStorage persistence\r\n  const handleSearchChange = (value) => {\r\n    setSearchTerm(value);\r\n    localStorage.setItem('quiz-search-term', value);\r\n  };\r\n\r\n  // Handle class selection change with localStorage persistence\r\n  const handleClassChange = (value) => {\r\n    setSelectedClass(value);\r\n    localStorage.setItem('quiz-selected-class', value);\r\n  };\r\n\r\n  // Clear search and filters (for manual refresh)\r\n  const clearSearchAndFilters = () => {\r\n    setSearchTerm('');\r\n    setSelectedClass('');\r\n    localStorage.removeItem('quiz-search-term');\r\n    localStorage.removeItem('quiz-selected-class');\r\n  };\r\n\r\n  const getUserResults = useCallback(async () => {\r\n    try {\r\n      if (!user?._id) return;\r\n\r\n      const response = await getAllReportsByUser({ userId: user._id });\r\n\r\n      if (response.success) {\r\n        const resultsMap = {};\r\n        response.data.forEach(report => {\r\n          const examId = report.exam?._id;\r\n          if (!examId || !report.result) return;\r\n\r\n          // Extract data from the result object\r\n          const result = report.result;\r\n\r\n          if (!resultsMap[examId] || new Date(report.createdAt) > new Date(resultsMap[examId].createdAt)) {\r\n            resultsMap[examId] = {\r\n              verdict: result.verdict,\r\n              percentage: result.percentage,\r\n              correctAnswers: result.correctAnswers,\r\n              wrongAnswers: result.wrongAnswers,\r\n              totalQuestions: result.totalQuestions,\r\n              obtainedMarks: result.obtainedMarks,\r\n              totalMarks: result.totalMarks,\r\n              score: result.score,\r\n              points: result.points,\r\n              xpEarned: result.xpEarned || result.points || result.xpGained || 0,\r\n              timeTaken: report.timeTaken,\r\n              completedAt: report.createdAt,\r\n            };\r\n          }\r\n        });\r\n        setUserResults(resultsMap);\r\n      }\r\n    } catch (error) {\r\n      console.error('Error fetching user results:', error);\r\n    }\r\n  }, [user?._id]);\r\n\r\n  // Define getExams function to load exams once\r\n  const getExams = useCallback(async () => {\r\n      try {\r\n        // Safety check: ensure user exists before proceeding\r\n        if (!user) {\r\n          console.log(\"User not loaded yet, skipping exam fetch\");\r\n          return;\r\n        }\r\n\r\n        // Level-specific cache to prevent cross-level contamination\r\n        const userLevel = user?.level || 'primary';\r\n        const cacheKey = `user_exams_cache_${userLevel}`;\r\n        const cacheTimeKey = `user_exams_cache_time_${userLevel}`;\r\n\r\n        // Clear caches for other levels\r\n        const allLevels = ['primary', 'secondary', 'advance'];\r\n        allLevels.forEach(level => {\r\n          if (level !== userLevel) {\r\n            localStorage.removeItem(`user_exams_cache_${level}`);\r\n            localStorage.removeItem(`user_exams_cache_time_${level}`);\r\n          }\r\n        });\r\n\r\n        // Check level-specific cache first\r\n        const cachedExams = localStorage.getItem(cacheKey);\r\n        const cacheTime = localStorage.getItem(cacheTimeKey);\r\n        const now = Date.now();\r\n\r\n        // Use cache if less than 10 minutes old (increased cache time)\r\n        if (cachedExams && cacheTime && (now - parseInt(cacheTime)) < 600000) {\r\n          const cached = JSON.parse(cachedExams);\r\n          setExams(cached);\r\n          setLastRefresh(new Date(parseInt(cacheTime)));\r\n          setLoading(false);\r\n          console.log(`📋 Using cached exams for ${userLevel} level:`, cached.length);\r\n          return;\r\n        }\r\n\r\n        dispatch(ShowLoading());\r\n        const response = await getAllExams();\r\n        dispatch(HideLoading());\r\n\r\n        if (response.success) {\r\n          console.log('Raw exams from API:', response.data.length);\r\n          console.log('User level:', user?.level);\r\n\r\n          // Filter exams by user's level with proper null checks\r\n          const userLevelExams = response.data.filter(exam => {\r\n            if (!exam.level || !user || !user.level) return false;\r\n            return exam.level.toLowerCase() === user.level.toLowerCase();\r\n          });\r\n\r\n          console.log('User level exams after filtering:', userLevelExams.length);\r\n          const sortedExams = userLevelExams.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));\r\n          setExams(sortedExams);\r\n          setLastRefresh(new Date());\r\n\r\n          // Cache the exams data with level-specific key\r\n          localStorage.setItem(cacheKey, JSON.stringify(sortedExams));\r\n          localStorage.setItem(cacheTimeKey, Date.now().toString());\r\n\r\n          // Set default class filter to user's class\r\n          if (user?.class) {\r\n            setSelectedClass(String(user.class));\r\n          }\r\n        } else {\r\n          message.error(response.message);\r\n        }\r\n      } catch (error) {\r\n        dispatch(HideLoading());\r\n        message.error(error.message);\r\n      } finally {\r\n        setLoading(false);\r\n      }\r\n  }, [dispatch, user]);\r\n\r\n  useEffect(() => {\r\n    // Clear ALL caches when component mounts to ensure fresh data\r\n    clearAllQuizCaches();\r\n\r\n    getExams(); // Initial load only\r\n    getUserResults();\r\n  }, [getExams, getUserResults]);\r\n\r\n  // Real-time updates for quiz completion and new exams\r\n  useEffect(() => {\r\n    // Listen for real-time updates from quiz completion\r\n    const handleRankingUpdate = () => {\r\n      console.log('🔄 Quiz listing - refreshing data after quiz completion...');\r\n      getUserResults(); // Refresh user results to show updated XP\r\n    };\r\n\r\n    // Listen for new exam creation events\r\n    const handleNewExam = () => {\r\n      console.log('🆕 New exam created - refreshing user results only...');\r\n      if (user) {\r\n        getUserResults(); // Only refresh user results, keep filters intact\r\n      }\r\n    };\r\n\r\n    // Listen for window focus to refresh user results only (not exams)\r\n    const handleWindowFocus = () => {\r\n      console.log('🎯 Quiz listing - window focused, refreshing user results...');\r\n      getUserResults(); // Only refresh user results, keep filters intact\r\n    };\r\n\r\n    window.addEventListener('rankingUpdate', handleRankingUpdate);\r\n    window.addEventListener('focus', handleWindowFocus);\r\n    window.addEventListener('newExamCreated', handleNewExam);\r\n\r\n    return () => {\r\n      window.removeEventListener('rankingUpdate', handleRankingUpdate);\r\n      window.removeEventListener('focus', handleWindowFocus);\r\n      window.removeEventListener('newExamCreated', handleNewExam);\r\n    };\r\n  }, []);\r\n\r\n\r\n\r\n  useEffect(() => {\r\n    console.log('Filtering exams:', { exams: exams.length, searchTerm, selectedClass });\r\n    let filtered = exams;\r\n    if (searchTerm) {\r\n      const searchLower = searchTerm.toLowerCase();\r\n      filtered = filtered.filter(exam => {\r\n        // Search in multiple fields for comprehensive results\r\n        const searchableFields = [\r\n          exam.name,\r\n          exam.subject,\r\n          exam.topic,\r\n          exam.description,\r\n          exam.category,\r\n          exam.level,\r\n          // Search in questions if available\r\n          ...(exam.questions || []).map(q => q.questionText),\r\n          ...(exam.questions || []).map(q => q.subject),\r\n          ...(exam.questions || []).map(q => q.topic)\r\n        ];\r\n\r\n        return searchableFields.some(field =>\r\n          field && field.toString().toLowerCase().includes(searchLower)\r\n        );\r\n      });\r\n    }\r\n    if (selectedClass) {\r\n      filtered = filtered.filter(exam => String(exam.class) === String(selectedClass));\r\n    }\r\n    filtered.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));\r\n    console.log('Filtered exams result:', filtered.length);\r\n    setFilteredExams(filtered);\r\n  }, [exams, searchTerm, selectedClass]);\r\n\r\n  const availableClasses = [...new Set(exams.map(e => e.class).filter(Boolean))].sort();\r\n\r\n  const handleQuizStart = (quiz) => {\r\n    if (!quiz || !quiz._id) {\r\n      message.error('Invalid quiz selected. Please try again.');\r\n      return;\r\n    }\r\n\r\n    // Validate MongoDB ObjectId format (24 character hex string)\r\n    const objectIdRegex = /^[0-9a-fA-F]{24}$/;\r\n    if (!objectIdRegex.test(quiz._id)) {\r\n      message.error('Invalid quiz ID format. Please try again.');\r\n      return;\r\n    }\r\n\r\n    startTransition(() => {\r\n      navigate(`/quiz/${quiz._id}/play`);\r\n    });\r\n  };\r\n\r\n\r\n\r\n  const handleQuizView = (quiz) => {\r\n    if (!quiz || !quiz._id) {\r\n      message.error('Invalid quiz selected. Please try again.');\r\n      return;\r\n    }\r\n    // Check if user has attempted this quiz\r\n    const userResult = userResults[quiz._id];\r\n    if (!userResult) {\r\n      message.info('You need to attempt this quiz first to view results.');\r\n      return;\r\n    }\r\n    startTransition(() => {\r\n      navigate(`/quiz/${quiz._id}/result`);\r\n    });\r\n  };\r\n\r\n\r\n\r\n  if (loading) {\r\n    return (\r\n      <div className=\"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center\">\r\n        <div className=\"text-center\">\r\n          <div className=\"animate-spin rounded-full h-16 w-16 border-b-2 border-blue-600 mx-auto mb-4\"></div>\r\n          <p className=\"text-gray-600\">Loading quizzes...</p>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <>\r\n      {/* Custom CSS for compact header and optimized layout */}\r\n      <style jsx global>{`\r\n        /* Completely remove all white space from ProtectedRoute main wrapper */\r\n        body.quiz-page-active main {\r\n          padding: 0 !important;\r\n          margin: 0 !important;\r\n        }\r\n\r\n        /* Remove all spacing from layout containers */\r\n        body.quiz-page-active .safe-content-animation {\r\n          padding: 0 !important;\r\n          margin: 0 !important;\r\n        }\r\n\r\n        /* Remove any inherited spacing from all parent containers */\r\n        body.quiz-page-active main > div,\r\n        body.quiz-page-active main * {\r\n          margin-top: 0 !important;\r\n        }\r\n\r\n        /* Ensure Quiz container starts immediately */\r\n        .quiz-container-immediate {\r\n          margin-top: 0 !important;\r\n          padding-top: 0 !important;\r\n        }\r\n        .quiz-grid {\r\n          gap: 1rem !important;\r\n          margin-top: 1rem !important;\r\n        }\r\n        @media (min-width: 640px) {\r\n          .quiz-grid {\r\n            gap: 1.25rem !important;\r\n            margin-top: 1rem !important;\r\n          }\r\n        }\r\n        @media (min-width: 1024px) {\r\n          .quiz-grid {\r\n            gap: 1.5rem !important;\r\n          }\r\n        }\r\n      `}</style>\r\n\r\n      <div className=\"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 quiz-container-immediate\">\r\n        <div className=\"container mx-auto px-3 sm:px-4 lg:px-8 pt-3 pb-3 sm:pb-4 lg:pb-6\">\r\n\r\n\r\n        {/* Compact Search and Filter with User Level */}\r\n        <div className=\"max-w-4xl mx-auto mb-3 sm:mb-4 opacity-100\">\r\n          <div className=\"bg-white rounded-xl shadow-md p-3 sm:p-4\">\r\n            {/* User Level and Quiz Count */}\r\n            <div className=\"flex flex-col sm:flex-row items-center justify-between mb-3 pb-3 border-b border-gray-100\">\r\n              <div className=\"flex items-center gap-4 text-sm text-gray-600\">\r\n                <div className=\"flex items-center gap-2\">\r\n                  <div className=\"w-2.5 h-2.5 bg-blue-500 rounded-full\"></div>\r\n                  <span className=\"font-medium\">Level: {user?.level || 'All Levels'}</span>\r\n                </div>\r\n                <div className=\"flex items-center gap-2\">\r\n                  <div className=\"w-2.5 h-2.5 bg-green-500 rounded-full\"></div>\r\n                  <span>{filteredExams.length} Available Quizzes</span>\r\n                </div>\r\n              </div>\r\n              {lastRefresh && (\r\n                <div className=\"flex items-center gap-2 text-xs text-gray-400 mt-2 sm:mt-0\">\r\n                  <div className=\"w-2 h-2 bg-gray-400 rounded-full\"></div>\r\n                  <span>Updated: {lastRefresh.toLocaleTimeString()}</span>\r\n                </div>\r\n              )}\r\n            </div>\r\n\r\n            {/* Search, Filter, and Refresh Controls */}\r\n            <div className=\"flex flex-col sm:flex-row gap-2 sm:gap-3\">\r\n              <div className=\"flex-1 relative\">\r\n                <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\r\n                  <TbSearch className=\"h-4 w-4 text-gray-400\" />\r\n                </div>\r\n                <input\r\n                  type=\"text\"\r\n                  placeholder=\"Search quizzes by subject, topic, or name...\"\r\n                  value={searchTerm}\r\n                  onChange={(e) => handleSearchChange(e.target.value)}\r\n                  className=\"block w-full pl-9 pr-3 py-2 border border-gray-300 rounded-lg bg-gray-50 focus:bg-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all text-sm\"\r\n                />\r\n              </div>\r\n              <div className=\"w-36 sm:w-40\">\r\n                <div className=\"relative\">\r\n                  <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\r\n                    <TbFilter className=\"h-4 w-4 text-gray-400\" />\r\n                  </div>\r\n                  <select\r\n                    value={selectedClass}\r\n                    onChange={(e) => handleClassChange(e.target.value)}\r\n                    className=\"block w-full pl-9 pr-8 py-2 border border-gray-300 rounded-lg bg-gray-50 focus:bg-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all appearance-none text-sm\"\r\n                  >\r\n                    <option value=\"\">All Classes</option>\r\n                    {availableClasses.map((className) => (\r\n                      <option key={className} value={className}>\r\n                        {user?.level === 'primary' ? `Class ${className}` : className}\r\n                      </option>\r\n                    ))}\r\n                  </select>\r\n                </div>\r\n              </div>\r\n\r\n              {/* Clear Filters Button */}\r\n              {(searchTerm || selectedClass) && (\r\n                <button\r\n                  onClick={clearSearchAndFilters}\r\n                  className=\"flex items-center justify-center px-3 py-2 bg-gradient-to-r from-gray-500 to-gray-600 text-white rounded-lg hover:from-gray-600 hover:to-gray-700 focus:outline-none focus:ring-2 focus:ring-gray-500 transition-all shadow-md\"\r\n                  title=\"Clear all search and filters\"\r\n                >\r\n                  <TbX className=\"h-4 w-4\" />\r\n                  <span className=\"ml-1.5 hidden sm:inline text-sm\">Clear</span>\r\n                </button>\r\n              )}\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Quiz Grid */}\r\n        <div className=\"opacity-100\" style={{ marginTop: '0.5rem' }}>\r\n\r\n\r\n          {filteredExams.length === 0 ? (\r\n            <div className=\"text-center py-8 sm:py-12\">\r\n              <div className=\"bg-white rounded-2xl shadow-lg p-6 sm:p-8 max-w-md mx-auto\">\r\n                <TbTarget className=\"w-12 h-12 sm:w-16 sm:h-16 text-gray-400 mx-auto mb-4\" />\r\n                <h3 className=\"text-lg sm:text-xl font-semibold text-gray-900 mb-2\">No Quizzes Found</h3>\r\n                <p className=\"text-gray-600 text-sm sm:text-base\">\r\n                  {searchTerm || selectedClass\r\n                    ? \"Try adjusting your search or filter criteria.\"\r\n                    : \"No quizzes are available for your level at the moment.\"\r\n                  }\r\n                </p>\r\n              </div>\r\n            </div>\r\n          ) : (\r\n            <div className=\"quiz-grid\">\r\n              {filteredExams.map((quiz, index) => (\r\n                <QuizCard\r\n                  key={quiz._id}\r\n                  quiz={quiz}\r\n                  userResult={userResults[quiz._id]}\r\n                  showResults={true}\r\n                  onStart={handleQuizStart}\r\n                  onView={() => handleQuizView(quiz)}\r\n                  index={index}\r\n                />\r\n              ))}\r\n            </div>\r\n          )}\r\n        </div>\r\n      </div>\r\n    </div>\r\n    </>\r\n  );\r\n};\r\n\r\n// Simple QuizCard component without Framer Motion\r\nconst QuizCard = ({ quiz, userResult, onStart, onView, index }) => {\r\n\r\n  const formatTime = (seconds) => {\r\n    if (!seconds) return 'N/A';\r\n    const minutes = Math.round(seconds / 60);\r\n    return `${minutes} min`;\r\n  };\r\n\r\n  const formatCompletionTime = (timeInSeconds) => {\r\n    // Handle different possible time formats\r\n    if (!timeInSeconds && timeInSeconds !== 0) return '0s';\r\n\r\n    let totalSeconds = timeInSeconds;\r\n\r\n    // If it's a string, try to parse it\r\n    if (typeof timeInSeconds === 'string') {\r\n      totalSeconds = parseInt(timeInSeconds, 10);\r\n    }\r\n\r\n    // If it's still not a valid number, return 0s\r\n    if (isNaN(totalSeconds) || totalSeconds < 0) return '0s';\r\n\r\n    const minutes = Math.floor(totalSeconds / 60);\r\n    const seconds = totalSeconds % 60;\r\n\r\n    if (minutes > 0) {\r\n      return `${minutes}m ${seconds}s`;\r\n    }\r\n    return `${timeInSeconds}s`;\r\n  };\r\n\r\n  // Safety checks for quiz object\r\n  if (!quiz || typeof quiz !== 'object') {\r\n    return (\r\n      <div className=\"bg-white rounded-2xl shadow-lg p-6 border border-gray-100\">\r\n        <p className=\"text-gray-500\">Invalid quiz data</p>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div\r\n      className=\"rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 p-3 transform hover:scale-105 opacity-100 relative flex flex-col\"\r\n      style={{\r\n        background: 'linear-gradient(135deg, #ffffff 0%, #f8fafc 50%, #f1f5f9 100%)',\r\n        border: userResult\r\n          ? (userResult.verdict === 'Pass' ? '2px solid #10b981' : '2px solid #ef4444')\r\n          : '2px solid #3b82f6',\r\n        boxShadow: userResult\r\n          ? (userResult.verdict === 'Pass'\r\n              ? '0 8px 20px rgba(16, 185, 129, 0.3)'\r\n              : '0 8px 20px rgba(239, 68, 68, 0.3)')\r\n          : '0 8px 20px rgba(59, 130, 246, 0.3)',\r\n        minHeight: window.innerWidth <= 768 ? '240px' : '320px',\r\n        height: 'auto'\r\n      }}\r\n    >\r\n      {/* Quiz Title - At Top */}\r\n      <div className=\"mb-2 text-center\">\r\n        <h3\r\n          className=\"font-bold mb-1 line-clamp-2\"\r\n          style={{\r\n            color: '#1f2937',\r\n            textShadow: '0 1px 2px rgba(0,0,0,0.1)',\r\n            lineHeight: '1.1',\r\n            fontSize: window.innerWidth <= 768 ? '14px' : '16px'\r\n          }}\r\n        >\r\n          {typeof quiz.name === 'string' ? quiz.name : 'Untitled Quiz'}\r\n        </h3>\r\n      </div>\r\n\r\n      {/* Status Tags - Centered */}\r\n      <div className=\"mb-2 text-center\">\r\n        {userResult ? (\r\n          <div className=\"flex items-center justify-center gap-1\">\r\n            <div\r\n              className=\"px-2 py-1 rounded-full text-xs font-bold text-white shadow-md\"\r\n              style={{\r\n                backgroundColor: userResult.verdict === 'Pass' ? '#10b981' : '#ef4444',\r\n                fontSize: window.innerWidth <= 768 ? '9px' : '10px'\r\n              }}\r\n            >\r\n              {userResult.verdict === 'Pass' ? '✅ PASSED' : '❌ FAILED'}\r\n            </div>\r\n            <div\r\n              className=\"px-2 py-1 rounded-full text-xs font-bold text-center shadow-md\"\r\n              style={{\r\n                backgroundColor: '#ffffff',\r\n                color: '#1f2937',\r\n                fontSize: window.innerWidth <= 768 ? '9px' : '10px'\r\n              }}\r\n            >\r\n              {typeof userResult.percentage === 'number' ? userResult.percentage : 0}%\r\n            </div>\r\n          </div>\r\n        ) : (\r\n          <div\r\n            className=\"px-2 py-1 rounded-full text-xs font-bold text-white shadow-md\"\r\n            style={{\r\n              backgroundColor: '#3b82f6',\r\n              fontSize: window.innerWidth <= 768 ? '9px' : '10px'\r\n            }}\r\n          >\r\n            🆕 NOT ATTEMPTED\r\n          </div>\r\n        )}\r\n      </div>\r\n\r\n      <div className=\"text-center mb-6\">\r\n        <div className=\"flex-1\">\r\n\r\n          {/* Questions and Duration - Horizontal */}\r\n          <div className=\"flex gap-1 mb-2 justify-center\">\r\n            <div\r\n              className=\"flex items-center gap-1 rounded-lg py-1 px-2 border shadow-sm\"\r\n              style={{\r\n                background: 'linear-gradient(to right, #eff6ff, #e0e7ff)',\r\n                borderColor: '#bfdbfe'\r\n              }}\r\n            >\r\n              <TbQuestionMark className=\"w-3 h-3\" style={{ color: '#2563eb' }} />\r\n              <span\r\n                className=\"font-bold\"\r\n                style={{\r\n                  color: '#1e40af',\r\n                  fontSize: window.innerWidth <= 768 ? '11px' : '12px'\r\n                }}\r\n              >\r\n                {Array.isArray(quiz.questions) ? quiz.questions.length : 0}\r\n              </span>\r\n            </div>\r\n            <div\r\n              className=\"flex items-center gap-1 rounded-lg py-1 px-2 border shadow-sm\"\r\n              style={{\r\n                background: 'linear-gradient(to right, #fdf4ff, #fce7f3)',\r\n                borderColor: '#e9d5ff'\r\n              }}\r\n            >\r\n              <TbClock className=\"w-3 h-3\" style={{ color: '#9333ea' }} />\r\n              <span\r\n                className=\"font-bold\"\r\n                style={{\r\n                  color: '#7c3aed',\r\n                  fontSize: window.innerWidth <= 768 ? '11px' : '12px'\r\n                }}\r\n              >\r\n                3m\r\n              </span>\r\n            </div>\r\n          </div>\r\n\r\n\r\n\r\n          <div className=\"flex items-center justify-center gap-1 flex-wrap mb-2\">\r\n            {/* Level Tag */}\r\n            <span\r\n              className=\"inline-block px-1 py-1 font-bold rounded-full text-white shadow-sm\"\r\n              style={{\r\n                background: 'linear-gradient(to right, #8b5cf6, #7c3aed)',\r\n                fontSize: window.innerWidth <= 768 ? '8px' : '10px'\r\n              }}\r\n            >\r\n              🎯{quiz.level || 'Primary'}\r\n            </span>\r\n\r\n            {/* Class Tag */}\r\n            <span\r\n              className=\"inline-block px-1 py-1 font-bold rounded-full text-white shadow-sm\"\r\n              style={{\r\n                background: 'linear-gradient(to right, #4ade80, #3b82f6)',\r\n                fontSize: window.innerWidth <= 768 ? '8px' : '10px'\r\n              }}\r\n            >\r\n              📖{typeof quiz.class === 'string' || typeof quiz.class === 'number' ?\r\n                (quiz.level === 'primary' ? `Class ${quiz.class}` : quiz.class) : 'N/A'}\r\n            </span>\r\n\r\n            {/* Category Tag */}\r\n            <span\r\n              className=\"inline-block px-1 py-1 font-bold rounded-full text-white shadow-sm\"\r\n              style={{\r\n                background: 'linear-gradient(to right, #f97316, #ea580c)',\r\n                fontSize: window.innerWidth <= 768 ? '8px' : '10px'\r\n              }}\r\n            >\r\n              📂{quiz.category || 'General'}\r\n            </span>\r\n\r\n            {/* Topic Tag - Show actual topic or \"General\" if not defined */}\r\n            <span\r\n              className=\"inline-block px-1 py-1 font-bold rounded-full text-white shadow-sm\"\r\n              style={{\r\n                background: quiz.topic && quiz.topic !== 'General' && quiz.topic !== ''\r\n                  ? 'linear-gradient(to right, #10b981, #059669)'\r\n                  : 'linear-gradient(to right, #6b7280, #4b5563)',\r\n                fontSize: window.innerWidth <= 768 ? '8px' : '10px'\r\n              }}\r\n            >\r\n              📚{quiz.topic || 'General'}\r\n            </span>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n\r\n\r\n      {userResult && typeof userResult === 'object' && (\r\n        <div\r\n          className=\"mb-2 p-2 rounded-lg border shadow-md\"\r\n          style={{\r\n            background: userResult.verdict === 'Pass'\r\n              ? 'linear-gradient(to bottom right, #f0fdf4, #ecfdf5)'\r\n              : 'linear-gradient(to bottom right, #fef2f2, #fdf2f8)',\r\n            borderColor: userResult.verdict === 'Pass' ? '#86efac' : '#fca5a5'\r\n          }}\r\n        >\r\n          <div className=\"flex items-center justify-between mb-3\">\r\n            <div className=\"flex items-center gap-3\">\r\n              {userResult.verdict === 'Pass' ? (\r\n                <div\r\n                  className=\"w-10 h-10 rounded-full flex items-center justify-center shadow-lg border-2 animate-pulse\"\r\n                  style={{\r\n                    background: 'linear-gradient(to right, #10b981, #059669)',\r\n                    borderColor: '#86efac'\r\n                  }}\r\n                >\r\n                  <TbCheck className=\"w-6 h-6 font-bold\" style={{ color: '#ffffff' }} />\r\n                </div>\r\n              ) : (\r\n                <div\r\n                  className=\"w-10 h-10 rounded-full flex items-center justify-center shadow-lg border-2 animate-pulse\"\r\n                  style={{\r\n                    background: 'linear-gradient(to right, #ef4444, #dc2626)',\r\n                    borderColor: '#fca5a5'\r\n                  }}\r\n                >\r\n                  <TbX className=\"w-6 h-6 font-bold\" style={{ color: '#ffffff' }} />\r\n                </div>\r\n              )}\r\n              <div>\r\n                <span className=\"text-lg font-bold\" style={{ color: '#1f2937' }}>🏆 Last Result</span>\r\n                <div className=\"text-sm\" style={{ color: '#6b7280' }}>\r\n                  {new Date(userResult.completedAt || userResult.createdAt || Date.now()).toLocaleDateString()}\r\n                </div>\r\n              </div>\r\n            </div>\r\n            <span\r\n              className=\"text-3xl font-bold shadow-lg\"\r\n              style={{\r\n                color: userResult.verdict === 'Pass' ? '#059669' : '#dc2626'\r\n              }}\r\n            >\r\n              {typeof userResult.percentage === 'number' ? userResult.percentage : 0}%\r\n            </span>\r\n          </div>\r\n\r\n          {/* Horizontal Layout for Results */}\r\n          <div className=\"flex gap-1 justify-center flex-wrap\">\r\n            {/* Correct/Wrong - Horizontal */}\r\n            <div\r\n              className=\"flex items-center gap-1 rounded-lg py-1 px-2 border shadow-md\"\r\n              style={{\r\n                background: 'linear-gradient(to right, #dcfce7, #fecaca)',\r\n                borderColor: '#86efac'\r\n              }}\r\n            >\r\n              <div className=\"flex items-center gap-1\">\r\n                <TbCheck className=\"w-3 h-3\" style={{ color: '#16a34a' }} />\r\n                <span className=\"text-sm font-bold\" style={{ color: '#15803d' }}>\r\n                  {typeof userResult.correctAnswers === 'number' ? userResult.correctAnswers : 0}\r\n                </span>\r\n              </div>\r\n              <div className=\"flex items-center gap-1\">\r\n                <TbX className=\"w-3 h-3\" style={{ color: '#dc2626' }} />\r\n                <span className=\"text-sm font-bold\" style={{ color: '#b91c1c' }}>\r\n                  {(quiz.questions?.length || 0) - (typeof userResult.correctAnswers === 'number' ? userResult.correctAnswers : 0)}\r\n                </span>\r\n              </div>\r\n            </div>\r\n\r\n            {/* XP */}\r\n            <div\r\n              className=\"flex items-center gap-1 rounded-lg py-1 px-2 border shadow-md\"\r\n              style={{\r\n                background: 'linear-gradient(to bottom right, #fef3c7, #fed7aa)',\r\n                borderColor: '#fde047'\r\n              }}\r\n            >\r\n              <span className=\"text-sm\">⭐</span>\r\n              <span className=\"text-sm font-bold\" style={{ color: '#92400e' }}>\r\n                {userResult.xpEarned || userResult.points || 0}\r\n              </span>\r\n            </div>\r\n\r\n            {/* Time - Horizontal if available */}\r\n            {userResult.timeTaken && userResult.timeTaken > 0 && (\r\n              <div\r\n                className=\"flex items-center gap-1 rounded-lg py-1 px-2 border shadow-md\"\r\n                style={{\r\n                  background: 'linear-gradient(to bottom right, #e9d5ff, #f3e8ff)',\r\n                  borderColor: '#c4b5fd'\r\n                }}\r\n              >\r\n                <TbClock className=\"w-3 h-3\" style={{ color: '#9333ea' }} />\r\n                <span className=\"text-sm font-bold\" style={{ color: '#7c3aed' }}>\r\n                  {formatCompletionTime(userResult.timeTaken)}\r\n                </span>\r\n              </div>\r\n            )}\r\n          </div>\r\n        </div>\r\n      )}\r\n\r\n      {/* Spacer to push buttons to bottom */}\r\n      <div className=\"flex-1\"></div>\r\n\r\n      <div className=\"flex gap-2 mt-3\">\r\n        {/* Main Action Button - Bigger for retake */}\r\n        <button\r\n          onClick={() => onStart(quiz)}\r\n          className=\"flex-1 flex items-center justify-center gap-1 px-3 py-2 rounded-lg font-bold transition-all duration-200 shadow-md hover:shadow-lg transform hover:scale-105 active:scale-95 text-white\"\r\n          style={{\r\n            background: userResult\r\n              ? 'linear-gradient(to right, #f97316, #ef4444)'\r\n              : 'linear-gradient(to right, #3b82f6, #8b5cf6)',\r\n            fontSize: '13px',\r\n            minHeight: '36px'\r\n          }}\r\n        >\r\n          <TbPlayerPlay className=\"w-3 h-3\" />\r\n          {userResult ? '🔄 Retake Quiz' : '🚀 Start Quiz'}\r\n        </button>\r\n\r\n        {/* Small Trophy Button - Only show when there are results */}\r\n        {userResult && (\r\n          <button\r\n            onClick={() => onView(quiz)}\r\n            className=\"px-3 py-2 rounded-lg transition-all duration-200 font-bold transform hover:scale-105 active:scale-95 shadow-md hover:shadow-lg text-white\"\r\n            style={{\r\n              background: 'linear-gradient(to right, #fbbf24, #f97316)',\r\n              fontSize: '13px',\r\n              minHeight: '36px'\r\n            }}\r\n            title=\"View Results\"\r\n          >\r\n            <TbTrophy className=\"w-3 h-3\" />\r\n          </button>\r\n        )}\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default Quiz;\r\n"], "names": ["default", "axiosInstance", "require", "addReport", "async", "post", "payload", "data", "error", "response", "getAllReports", "filters", "getAllReportsByUser", "getAllReportsForRanking", "get", "getXPLeaderboard", "options", "arguments", "length", "undefined", "params", "URLSearchParams", "limit", "append", "classFilter", "levelFilter", "seasonFilter", "includeInactive", "concat", "toString", "getUserRanking", "userId", "context", "QuizCard", "_ref", "_quiz$questions", "quiz", "userResult", "onStart", "onView", "index", "_jsxs", "className", "style", "background", "border", "verdict", "boxShadow", "minHeight", "window", "innerWidth", "height", "children", "_jsx", "color", "textShadow", "lineHeight", "fontSize", "name", "backgroundColor", "percentage", "borderColor", "TbQuestionMark", "Array", "isArray", "questions", "TbClock", "level", "class", "category", "topic", "TbCheck", "TbX", "Date", "completedAt", "createdAt", "now", "toLocaleDateString", "correctAnswers", "xpEarned", "points", "timeTaken", "timeInSeconds", "totalSeconds", "parseInt", "isNaN", "minutes", "Math", "floor", "seconds", "formatCompletionTime", "onClick", "TbPlayerPlay", "title", "TbTrophy", "Quiz", "exams", "setExams", "useState", "filteredExams", "setFilteredExams", "searchTerm", "setSearchTerm", "localStorage", "getItem", "selectedClass", "setSelectedClass", "userResults", "setUserResults", "loading", "setLoading", "lastRefresh", "setLastRefresh", "navigate", "useNavigate", "dispatch", "useDispatch", "user", "useSelector", "state", "useEffect", "document", "body", "classList", "add", "remove", "clearSearchAndFilters", "removeItem", "getUserResults", "useCallback", "_id", "success", "resultsMap", "for<PERSON>ach", "report", "_report$exam", "examId", "exam", "result", "wrongAnswers", "totalQuestions", "obtainedMarks", "totalMarks", "score", "xpGained", "console", "getExams", "log", "userLevel", "cache<PERSON>ey", "cacheTimeKey", "cachedExams", "cacheTime", "cached", "JSON", "parse", "ShowLoading", "getAllExams", "HideLoading", "userLevelExams", "filter", "toLowerCase", "sortedExams", "sort", "a", "b", "setItem", "stringify", "String", "message", "handleRankingUpdate", "handleNewExam", "handleWindowFocus", "addEventListener", "removeEventListener", "filtered", "searchLower", "subject", "description", "map", "q", "questionText", "some", "field", "includes", "availableClasses", "Set", "e", "Boolean", "handleQuizStart", "test", "startTransition", "_Fragment", "jsx", "global", "toLocaleTimeString", "TbSearch", "type", "placeholder", "value", "onChange", "handleSearchChange", "target", "Tb<PERSON><PERSON>er", "handleClassChange", "marginTop", "TbTarget", "showResults", "info", "handleQuizView"], "sourceRoot": ""}