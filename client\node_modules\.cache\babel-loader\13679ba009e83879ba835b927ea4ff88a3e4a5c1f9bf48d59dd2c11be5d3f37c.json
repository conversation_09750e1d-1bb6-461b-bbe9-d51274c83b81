{"ast": null, "code": "function getError(option, xhr) {\n  var msg = \"cannot \".concat(option.method, \" \").concat(option.action, \" \").concat(xhr.status, \"'\");\n  var err = new Error(msg);\n  err.status = xhr.status;\n  err.method = option.method;\n  err.url = option.action;\n  return err;\n}\nfunction getBody(xhr) {\n  var text = xhr.responseText || xhr.response;\n  if (!text) {\n    return text;\n  }\n  try {\n    return JSON.parse(text);\n  } catch (e) {\n    return text;\n  }\n}\nexport default function upload(option) {\n  // eslint-disable-next-line no-undef\n  var xhr = new XMLHttpRequest();\n  if (option.onProgress && xhr.upload) {\n    xhr.upload.onprogress = function progress(e) {\n      if (e.total > 0) {\n        e.percent = e.loaded / e.total * 100;\n      }\n      option.onProgress(e);\n    };\n  } // eslint-disable-next-line no-undef\n\n  var formData = new FormData();\n  if (option.data) {\n    Object.keys(option.data).forEach(function (key) {\n      var value = option.data[key]; // support key-value array data\n\n      if (Array.isArray(value)) {\n        value.forEach(function (item) {\n          // { list: [ 11, 22 ] }\n          // formData.append('list[]', 11);\n          formData.append(\"\".concat(key, \"[]\"), item);\n        });\n        return;\n      }\n      formData.append(key, value);\n    });\n  } // eslint-disable-next-line no-undef\n\n  if (option.file instanceof Blob) {\n    formData.append(option.filename, option.file, option.file.name);\n  } else {\n    formData.append(option.filename, option.file);\n  }\n  xhr.onerror = function error(e) {\n    option.onError(e);\n  };\n  xhr.onload = function onload() {\n    // allow success when 2xx status\n    // see https://github.com/react-component/upload/issues/34\n    if (xhr.status < 200 || xhr.status >= 300) {\n      return option.onError(getError(option, xhr), getBody(xhr));\n    }\n    return option.onSuccess(getBody(xhr), xhr);\n  };\n  xhr.open(option.method, option.action, true); // Has to be after `.open()`. See https://github.com/enyo/dropzone/issues/179\n\n  if (option.withCredentials && 'withCredentials' in xhr) {\n    xhr.withCredentials = true;\n  }\n  var headers = option.headers || {}; // when set headers['X-Requested-With'] = null , can close default XHR header\n  // see https://github.com/react-component/upload/issues/33\n\n  if (headers['X-Requested-With'] !== null) {\n    xhr.setRequestHeader('X-Requested-With', 'XMLHttpRequest');\n  }\n  Object.keys(headers).forEach(function (h) {\n    if (headers[h] !== null) {\n      xhr.setRequestHeader(h, headers[h]);\n    }\n  });\n  xhr.send(formData);\n  return {\n    abort: function abort() {\n      xhr.abort();\n    }\n  };\n}", "map": {"version": 3, "names": ["getError", "option", "xhr", "msg", "concat", "method", "action", "status", "err", "Error", "url", "getBody", "text", "responseText", "response", "JSON", "parse", "e", "upload", "XMLHttpRequest", "onProgress", "onprogress", "progress", "total", "percent", "loaded", "formData", "FormData", "data", "Object", "keys", "for<PERSON>ach", "key", "value", "Array", "isArray", "item", "append", "file", "Blob", "filename", "name", "onerror", "error", "onError", "onload", "onSuccess", "open", "withCredentials", "headers", "setRequestHeader", "h", "send", "abort"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/node_modules/rc-upload/es/request.js"], "sourcesContent": ["function getError(option, xhr) {\n  var msg = \"cannot \".concat(option.method, \" \").concat(option.action, \" \").concat(xhr.status, \"'\");\n  var err = new Error(msg);\n  err.status = xhr.status;\n  err.method = option.method;\n  err.url = option.action;\n  return err;\n}\n\nfunction getBody(xhr) {\n  var text = xhr.responseText || xhr.response;\n\n  if (!text) {\n    return text;\n  }\n\n  try {\n    return JSON.parse(text);\n  } catch (e) {\n    return text;\n  }\n}\n\nexport default function upload(option) {\n  // eslint-disable-next-line no-undef\n  var xhr = new XMLHttpRequest();\n\n  if (option.onProgress && xhr.upload) {\n    xhr.upload.onprogress = function progress(e) {\n      if (e.total > 0) {\n        e.percent = e.loaded / e.total * 100;\n      }\n\n      option.onProgress(e);\n    };\n  } // eslint-disable-next-line no-undef\n\n\n  var formData = new FormData();\n\n  if (option.data) {\n    Object.keys(option.data).forEach(function (key) {\n      var value = option.data[key]; // support key-value array data\n\n      if (Array.isArray(value)) {\n        value.forEach(function (item) {\n          // { list: [ 11, 22 ] }\n          // formData.append('list[]', 11);\n          formData.append(\"\".concat(key, \"[]\"), item);\n        });\n        return;\n      }\n\n      formData.append(key, value);\n    });\n  } // eslint-disable-next-line no-undef\n\n\n  if (option.file instanceof Blob) {\n    formData.append(option.filename, option.file, option.file.name);\n  } else {\n    formData.append(option.filename, option.file);\n  }\n\n  xhr.onerror = function error(e) {\n    option.onError(e);\n  };\n\n  xhr.onload = function onload() {\n    // allow success when 2xx status\n    // see https://github.com/react-component/upload/issues/34\n    if (xhr.status < 200 || xhr.status >= 300) {\n      return option.onError(getError(option, xhr), getBody(xhr));\n    }\n\n    return option.onSuccess(getBody(xhr), xhr);\n  };\n\n  xhr.open(option.method, option.action, true); // Has to be after `.open()`. See https://github.com/enyo/dropzone/issues/179\n\n  if (option.withCredentials && 'withCredentials' in xhr) {\n    xhr.withCredentials = true;\n  }\n\n  var headers = option.headers || {}; // when set headers['X-Requested-With'] = null , can close default XHR header\n  // see https://github.com/react-component/upload/issues/33\n\n  if (headers['X-Requested-With'] !== null) {\n    xhr.setRequestHeader('X-Requested-With', 'XMLHttpRequest');\n  }\n\n  Object.keys(headers).forEach(function (h) {\n    if (headers[h] !== null) {\n      xhr.setRequestHeader(h, headers[h]);\n    }\n  });\n  xhr.send(formData);\n  return {\n    abort: function abort() {\n      xhr.abort();\n    }\n  };\n}"], "mappings": "AAAA,SAASA,QAAQA,CAACC,MAAM,EAAEC,GAAG,EAAE;EAC7B,IAAIC,GAAG,GAAG,SAAS,CAACC,MAAM,CAACH,MAAM,CAACI,MAAM,EAAE,GAAG,CAAC,CAACD,MAAM,CAACH,MAAM,CAACK,MAAM,EAAE,GAAG,CAAC,CAACF,MAAM,CAACF,GAAG,CAACK,MAAM,EAAE,GAAG,CAAC;EACjG,IAAIC,GAAG,GAAG,IAAIC,KAAK,CAACN,GAAG,CAAC;EACxBK,GAAG,CAACD,MAAM,GAAGL,GAAG,CAACK,MAAM;EACvBC,GAAG,CAACH,MAAM,GAAGJ,MAAM,CAACI,MAAM;EAC1BG,GAAG,CAACE,GAAG,GAAGT,MAAM,CAACK,MAAM;EACvB,OAAOE,GAAG;AACZ;AAEA,SAASG,OAAOA,CAACT,GAAG,EAAE;EACpB,IAAIU,IAAI,GAAGV,GAAG,CAACW,YAAY,IAAIX,GAAG,CAACY,QAAQ;EAE3C,IAAI,CAACF,IAAI,EAAE;IACT,OAAOA,IAAI;EACb;EAEA,IAAI;IACF,OAAOG,IAAI,CAACC,KAAK,CAACJ,IAAI,CAAC;EACzB,CAAC,CAAC,OAAOK,CAAC,EAAE;IACV,OAAOL,IAAI;EACb;AACF;AAEA,eAAe,SAASM,MAAMA,CAACjB,MAAM,EAAE;EACrC;EACA,IAAIC,GAAG,GAAG,IAAIiB,cAAc,CAAC,CAAC;EAE9B,IAAIlB,MAAM,CAACmB,UAAU,IAAIlB,GAAG,CAACgB,MAAM,EAAE;IACnChB,GAAG,CAACgB,MAAM,CAACG,UAAU,GAAG,SAASC,QAAQA,CAACL,CAAC,EAAE;MAC3C,IAAIA,CAAC,CAACM,KAAK,GAAG,CAAC,EAAE;QACfN,CAAC,CAACO,OAAO,GAAGP,CAAC,CAACQ,MAAM,GAAGR,CAAC,CAACM,KAAK,GAAG,GAAG;MACtC;MAEAtB,MAAM,CAACmB,UAAU,CAACH,CAAC,CAAC;IACtB,CAAC;EACH,CAAC,CAAC;;EAGF,IAAIS,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;EAE7B,IAAI1B,MAAM,CAAC2B,IAAI,EAAE;IACfC,MAAM,CAACC,IAAI,CAAC7B,MAAM,CAAC2B,IAAI,CAAC,CAACG,OAAO,CAAC,UAAUC,GAAG,EAAE;MAC9C,IAAIC,KAAK,GAAGhC,MAAM,CAAC2B,IAAI,CAACI,GAAG,CAAC,CAAC,CAAC;;MAE9B,IAAIE,KAAK,CAACC,OAAO,CAACF,KAAK,CAAC,EAAE;QACxBA,KAAK,CAACF,OAAO,CAAC,UAAUK,IAAI,EAAE;UAC5B;UACA;UACAV,QAAQ,CAACW,MAAM,CAAC,EAAE,CAACjC,MAAM,CAAC4B,GAAG,EAAE,IAAI,CAAC,EAAEI,IAAI,CAAC;QAC7C,CAAC,CAAC;QACF;MACF;MAEAV,QAAQ,CAACW,MAAM,CAACL,GAAG,EAAEC,KAAK,CAAC;IAC7B,CAAC,CAAC;EACJ,CAAC,CAAC;;EAGF,IAAIhC,MAAM,CAACqC,IAAI,YAAYC,IAAI,EAAE;IAC/Bb,QAAQ,CAACW,MAAM,CAACpC,MAAM,CAACuC,QAAQ,EAAEvC,MAAM,CAACqC,IAAI,EAAErC,MAAM,CAACqC,IAAI,CAACG,IAAI,CAAC;EACjE,CAAC,MAAM;IACLf,QAAQ,CAACW,MAAM,CAACpC,MAAM,CAACuC,QAAQ,EAAEvC,MAAM,CAACqC,IAAI,CAAC;EAC/C;EAEApC,GAAG,CAACwC,OAAO,GAAG,SAASC,KAAKA,CAAC1B,CAAC,EAAE;IAC9BhB,MAAM,CAAC2C,OAAO,CAAC3B,CAAC,CAAC;EACnB,CAAC;EAEDf,GAAG,CAAC2C,MAAM,GAAG,SAASA,MAAMA,CAAA,EAAG;IAC7B;IACA;IACA,IAAI3C,GAAG,CAACK,MAAM,GAAG,GAAG,IAAIL,GAAG,CAACK,MAAM,IAAI,GAAG,EAAE;MACzC,OAAON,MAAM,CAAC2C,OAAO,CAAC5C,QAAQ,CAACC,MAAM,EAAEC,GAAG,CAAC,EAAES,OAAO,CAACT,GAAG,CAAC,CAAC;IAC5D;IAEA,OAAOD,MAAM,CAAC6C,SAAS,CAACnC,OAAO,CAACT,GAAG,CAAC,EAAEA,GAAG,CAAC;EAC5C,CAAC;EAEDA,GAAG,CAAC6C,IAAI,CAAC9C,MAAM,CAACI,MAAM,EAAEJ,MAAM,CAACK,MAAM,EAAE,IAAI,CAAC,CAAC,CAAC;;EAE9C,IAAIL,MAAM,CAAC+C,eAAe,IAAI,iBAAiB,IAAI9C,GAAG,EAAE;IACtDA,GAAG,CAAC8C,eAAe,GAAG,IAAI;EAC5B;EAEA,IAAIC,OAAO,GAAGhD,MAAM,CAACgD,OAAO,IAAI,CAAC,CAAC,CAAC,CAAC;EACpC;;EAEA,IAAIA,OAAO,CAAC,kBAAkB,CAAC,KAAK,IAAI,EAAE;IACxC/C,GAAG,CAACgD,gBAAgB,CAAC,kBAAkB,EAAE,gBAAgB,CAAC;EAC5D;EAEArB,MAAM,CAACC,IAAI,CAACmB,OAAO,CAAC,CAAClB,OAAO,CAAC,UAAUoB,CAAC,EAAE;IACxC,IAAIF,OAAO,CAACE,CAAC,CAAC,KAAK,IAAI,EAAE;MACvBjD,GAAG,CAACgD,gBAAgB,CAACC,CAAC,EAAEF,OAAO,CAACE,CAAC,CAAC,CAAC;IACrC;EACF,CAAC,CAAC;EACFjD,GAAG,CAACkD,IAAI,CAAC1B,QAAQ,CAAC;EAClB,OAAO;IACL2B,KAAK,EAAE,SAASA,KAAKA,CAAA,EAAG;MACtBnD,GAAG,CAACmD,KAAK,CAAC,CAAC;IACb;EACF,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}