{"ast": null, "code": "import _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport * as React from 'react';\n/**\n * Trigger only when component unmount\n */\nexport default function useUnmount(triggerStart, triggerEnd) {\n  var _React$useState = React.useState(false),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    firstMount = _React$useState2[0],\n    setFirstMount = _React$useState2[1];\n  React.useLayoutEffect(function () {\n    if (firstMount) {\n      triggerStart();\n      return function () {\n        triggerEnd();\n      };\n    }\n  }, [firstMount]);\n  React.useLayoutEffect(function () {\n    setFirstMount(true);\n    return function () {\n      setFirstMount(false);\n    };\n  }, []);\n}", "map": {"version": 3, "names": ["_slicedToArray", "React", "useUnmount", "triggerStart", "triggerEnd", "_React$useState", "useState", "_React$useState2", "firstMount", "set<PERSON><PERSON><PERSON>Mount", "useLayoutEffect"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/node_modules/rc-tree/es/useUnmount.js"], "sourcesContent": ["import _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport * as React from 'react';\n/**\n * Trigger only when component unmount\n */\nexport default function useUnmount(triggerStart, triggerEnd) {\n  var _React$useState = React.useState(false),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    firstMount = _React$useState2[0],\n    setFirstMount = _React$useState2[1];\n  React.useLayoutEffect(function () {\n    if (firstMount) {\n      triggerStart();\n      return function () {\n        triggerEnd();\n      };\n    }\n  }, [firstMount]);\n  React.useLayoutEffect(function () {\n    setFirstMount(true);\n    return function () {\n      setFirstMount(false);\n    };\n  }, []);\n}"], "mappings": "AAAA,OAAOA,cAAc,MAAM,0CAA0C;AACrE,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B;AACA;AACA;AACA,eAAe,SAASC,UAAUA,CAACC,YAAY,EAAEC,UAAU,EAAE;EAC3D,IAAIC,eAAe,GAAGJ,KAAK,CAACK,QAAQ,CAAC,KAAK,CAAC;IACzCC,gBAAgB,GAAGP,cAAc,CAACK,eAAe,EAAE,CAAC,CAAC;IACrDG,UAAU,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IAChCE,aAAa,GAAGF,gBAAgB,CAAC,CAAC,CAAC;EACrCN,KAAK,CAACS,eAAe,CAAC,YAAY;IAChC,IAAIF,UAAU,EAAE;MACdL,YAAY,CAAC,CAAC;MACd,OAAO,YAAY;QACjBC,UAAU,CAAC,CAAC;MACd,CAAC;IACH;EACF,CAAC,EAAE,CAACI,UAAU,CAAC,CAAC;EAChBP,KAAK,CAACS,eAAe,CAAC,YAAY;IAChCD,aAAa,CAAC,IAAI,CAAC;IACnB,OAAO,YAAY;MACjBA,aAAa,CAAC,KAAK,CAAC;IACtB,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;AACR", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}