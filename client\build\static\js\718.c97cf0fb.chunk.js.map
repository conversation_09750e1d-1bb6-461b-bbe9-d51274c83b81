{"version": 3, "file": "static/js/718.c97cf0fb.chunk.js", "mappings": "6JAAA,MAAQA,QAASC,GAAkBC,EAAQ,MAG9BC,EAAYC,UACrB,IAEI,aADuBH,EAAcI,KAAK,0BAA2BC,IACrDC,IACpB,CAAE,MAAOC,GACL,OAAOA,EAAMC,SAASF,IAC1B,GAISG,EAAgBN,UACzB,IAEI,aADuBH,EAAcI,KAAK,+BAAgCM,IAC1DJ,IACpB,CAAE,MAAOC,GACL,OAAOA,EAAMC,SAASF,IAC1B,GAISK,EAAsBR,UAC/B,IAEI,aADuBH,EAAcI,KAAK,uCAAwCC,IAClEC,IACpB,CAAE,MAAOC,GACL,OAAOA,EAAMC,SAASF,IAC1B,GAISM,EAA0BT,UACnC,IAEI,aADuBH,EAAca,IAAI,6CACzBP,IACpB,CAAE,MAAOC,GACL,OAAOA,EAAMC,SAASF,IAC1B,GAaSQ,EAAmBX,iBAAyB,IAAlBY,EAAOC,UAAAC,OAAA,QAAAC,IAAAF,UAAA,GAAAA,UAAA,GAAG,CAAC,EAC9C,IACI,MAAMG,EAAS,IAAIC,gBACfL,EAAQM,OAAOF,EAAOG,OAAO,QAASP,EAAQM,OAC9CN,EAAQQ,aAAaJ,EAAOG,OAAO,cAAeP,EAAQQ,aAC1DR,EAAQS,aAAaL,EAAOG,OAAO,cAAeP,EAAQS,aAC1DT,EAAQU,cAAcN,EAAOG,OAAO,eAAgBP,EAAQU,cAC5DV,EAAQW,iBAAiBP,EAAOG,OAAO,kBAAmBP,EAAQW,iBAGtE,aADuB1B,EAAca,IAAI,4BAADc,OAA6BR,EAAOS,cAC5DtB,IACpB,CAAE,MAAOC,GACL,OAAOA,EAAMC,SAASF,IAC1B,CACJ,EAEauB,EAAiB1B,eAAO2B,GAAyB,IAAjBC,EAAOf,UAAAC,OAAA,QAAAC,IAAAF,UAAA,GAAAA,UAAA,GAAG,EACnD,IAEI,aADuBhB,EAAca,IAAI,0BAADc,OAA2BG,EAAM,aAAAH,OAAYI,KACrEzB,IACpB,CAAE,MAAOC,GACL,OAAOA,EAAMC,SAASF,IAC1B,CACJ,C,wDC3DA,QAdA,SAAkB0B,GAAa,IAAZ,MAAEC,GAAOD,EAC1B,MAAOE,EAAUC,IAAeC,EAAAA,EAAAA,WAAS,GAMzC,OALAC,EAAAA,EAAAA,YAAU,KACJC,OAAOC,WAAa,KACtBJ,GAAY,EACd,GACC,KAEDK,EAAAA,EAAAA,KAAA,OAAKC,UAAU,OAAMC,UACnBF,EAAAA,EAAAA,KAAA,MAAIC,UAAWP,EAAW,UAAY,GAAGQ,SAAET,KAGjD,C,mOCbA,MACA,EADyCU,EAAAA,cAAoB,CAAC,GCK9D,EANyBX,IACvB,IAAI,SACFU,GACEV,EACJ,OAAOU,CAAQ,ECFjB,SAASE,EAASC,GAChB,YAAe3B,IAAR2B,GAA6B,OAARA,CAC9B,CACA,MA6CA,EA7CaC,IACX,MAAM,cACJC,EAAa,UACbC,EAAS,KACTC,EAAI,UACJR,EAAS,MACTS,EAAK,WACLC,EAAU,aACVC,EAAY,SACZC,EAAQ,MACRC,EAAK,QACLC,EAAO,MACPC,GACEV,EACEW,EAAYT,EAClB,OAAIK,EACkBV,EAAAA,cAAoBc,EAAW,CACjDhB,UAAWiB,IAAW,CACpB,CAAC,GAAD/B,OAAIoB,EAAa,gBAAgBH,EAASU,GAC1C,CAAC,GAAD3B,OAAIoB,EAAa,kBAAkBH,EAASW,IAC3Cd,GACHS,MAAOA,EACPS,QAASV,GACRL,EAASU,IAAuBX,EAAAA,cAAoB,OAAQ,CAC7DO,MAAOC,GACNG,GAAQV,EAASW,IAAyBZ,EAAAA,cAAoB,OAAQ,CACvEO,MAAOE,GACNG,IAEeZ,EAAAA,cAAoBc,EAAW,CACjDhB,UAAWiB,IAAW,GAAD/B,OAAIoB,EAAa,SAASN,GAC/CS,MAAOA,EACPS,QAASV,GACKN,EAAAA,cAAoB,MAAO,CACzCF,UAAW,GAAFd,OAAKoB,EAAa,qBACzBO,GAAmB,IAAVA,IAA6BX,EAAAA,cAAoB,OAAQ,CACpEF,UAAWiB,IAAW,GAAD/B,OAAIoB,EAAa,eAAe,CACnD,CAAC,GAADpB,OAAIoB,EAAa,oBAAoBS,IAEvCN,MAAOC,GACNG,IAASC,GAAuB,IAAZA,IAA+BZ,EAAAA,cAAoB,OAAQ,CAChFF,UAAWiB,IAAW,GAAD/B,OAAIoB,EAAa,kBACtCG,MAAOE,GACNG,IAAU,EC7Cf,SAASK,EAAYC,EAAO7B,EAAM8B,GAChC,IAAI,MACFN,EAAK,UACLO,EAAS,SACTV,GACErB,GACA,UACFgB,EAAS,KACTgB,EAAI,UACJC,EAAS,YACTC,EACAf,WAAYgB,EACZf,aAAcgB,GACZN,EACJ,OAAOD,EAAMQ,KAAI,CAACC,EAAOC,KACvB,IAAI,MACFjB,EAAK,SACLZ,EACAqB,UAAWhB,EAAgBgB,EAAS,UACpCtB,EAAS,MACTS,EAAK,WACLC,EAAU,aACVC,EAAY,KACZH,EAAO,EAAC,IACRuB,GACEF,EACJ,MAAyB,kBAAdtB,EACWL,EAAAA,cAAoB8B,EAAM,CAC5CD,IAAK,GAAF7C,OAAKqC,EAAI,KAAArC,OAAI6C,GAAOD,GACvB9B,UAAWA,EACXS,MAAOA,EACPC,WAAYuB,OAAOC,OAAOD,OAAOC,OAAO,CAAC,EAAGR,GAAiBhB,GAC7DC,aAAcsB,OAAOC,OAAOD,OAAOC,OAAO,CAAC,EAAGP,GAAmBhB,GACjEH,KAAMA,EACNO,MAAOA,EACPR,UAAWA,EACXD,cAAeA,EACfM,SAAUA,EACVC,MAAOW,EAAYX,EAAQ,KAC3BC,QAASW,EAAcxB,EAAW,OAG/B,CAAcC,EAAAA,cAAoB8B,EAAM,CAC7CD,IAAK,SAAF7C,OAAW6C,GAAOD,GACrB9B,UAAWA,EACXS,MAAOwB,OAAOC,OAAOD,OAAOC,OAAOD,OAAOC,OAAO,CAAC,EAAGR,GAAiBjB,GAAQC,GAC9EF,KAAM,EACNO,MAAOA,EACPR,UAAWA,EAAU,GACrBD,cAAeA,EACfM,SAAUA,EACVC,MAAOA,IACQX,EAAAA,cAAoB8B,EAAM,CACzCD,IAAK,WAAF7C,OAAa6C,GAAOD,GACvB9B,UAAWA,EACXS,MAAOwB,OAAOC,OAAOD,OAAOC,OAAOD,OAAOC,OAAO,CAAC,EAAGP,GAAmBlB,GAAQE,GAChFH,KAAa,EAAPA,EAAW,EACjBD,UAAWA,EAAU,GACrBD,cAAeA,EACfM,SAAUA,EACVE,QAASb,IACR,GAEP,CACA,MAoCA,EApCYI,IACV,MAAM8B,EAAcjC,EAAAA,WAAiBkC,IAC/B,UACJd,EAAS,SACTe,EAAQ,IACRC,EAAG,MACHR,EAAK,SACLlB,GACEP,EACJ,OAAIgC,EACkBnC,EAAAA,cAAoBA,EAAAA,SAAgB,KAAmBA,EAAAA,cAAoB,KAAM,CACnG6B,IAAK,SAAF7C,OAAW4C,GACd9B,UAAW,GAAFd,OAAKoC,EAAS,SACtBH,EAAYmB,EAAKjC,EAAO4B,OAAOC,OAAO,CACvC3B,UAAW,KACXgB,KAAM,QACNC,WAAW,GACVW,KAA6BjC,EAAAA,cAAoB,KAAM,CACxD6B,IAAK,WAAF7C,OAAa4C,GAChB9B,UAAW,GAAFd,OAAKoC,EAAS,SACtBH,EAAYmB,EAAKjC,EAAO4B,OAAOC,OAAO,CACvC3B,UAAW,KACXgB,KAAM,UACNE,aAAa,GACZU,MAEejC,EAAAA,cAAoB,KAAM,CAC5C6B,IAAKD,EACL9B,UAAW,GAAFd,OAAKoC,EAAS,SACtBH,EAAYmB,EAAKjC,EAAO4B,OAAOC,OAAO,CACvC3B,UAAWK,EAAW,CAAC,KAAM,MAAQ,KACrCW,KAAM,OACNC,WAAW,EACXC,aAAa,GACZU,IAAc,E,cClGnB,SAASI,EAAcC,EAASC,EAAYjC,GAC1C,IAAIkC,EAAQF,EAOZ,YANa/D,IAAT+B,GAAsBA,EAAOiC,KAC/BC,EAAQT,OAAOC,OAAOD,OAAOC,OAAO,CAAC,EAAGM,GAAU,CAChDhC,KAAMiC,KAIHC,CACT,CAIA,SAASC,EAAYC,EAAUC,GAC7B,MAAMC,EAAO,GACb,IAAIC,EAAS,GACTN,EAAaI,EAoBjB,OAnBAD,EAASI,QAAOC,GAAKA,IAAGC,SAAQ,CAACV,EAASV,KACxC,MAAMtB,EAAmB,OAAZgC,QAAgC,IAAZA,OAAqB,EAASA,EAAQhC,KACjE2C,EAAa3C,GAAQ,EAE3B,GAAIsB,IAAUc,EAASpE,OAAS,EAG9B,OAFAuE,EAAOK,KAAKb,EAAcC,EAASC,EAAYjC,SAC/CsC,EAAKM,KAAKL,GAGRI,EAAaV,GACfA,GAAcU,EACdJ,EAAOK,KAAKZ,KAEZO,EAAOK,KAAKb,EAAcC,EAASC,EAAYU,IAC/CL,EAAKM,KAAKL,GACVN,EAAaI,EACbE,EAAS,GACX,IAEKD,CACT,CACA,MASA,EATeO,CAACR,EAAczB,EAAOnB,KACtBqD,EAAAA,EAAAA,UAAQ,KACnB,OAAIC,MAAMC,QAAQpC,GACTuB,EAAYvB,EAAOyB,GAErBF,GAhCiBc,EAgCexD,GAhCDyD,EAAAA,EAAAA,GAAQD,GAAY7B,KAAI+B,GAAiB,OAATA,QAA0B,IAATA,OAAkB,EAASA,EAAKtD,SAgCrEwC,GAhC1BY,KAgCuC,GAC9D,CAACrC,EAAOnB,EAAU4C,I,kCC7CvB,MAAMe,EAAmBC,IACvB,MAAM,aACJC,EAAY,QACZC,GACEF,EACJ,MAAO,CACL,CAAC,IAAD3E,OAAK4E,EAAY,cAAc,CAC7B,CAAC,KAAD5E,OAAM4E,EAAY,UAAU,CAC1BE,OAAQ,GAAF9E,OAAK2E,EAAMI,UAAS,OAAA/E,OAAM2E,EAAMK,SAAQ,KAAAhF,OAAI2E,EAAMM,YACxD,UAAW,CACTC,YAAa,OACbC,eAAgB,YAElB,CAAC,GAADnF,OAAI4E,EAAY,SAAS,CACvBQ,aAAc,GAAFpF,OAAK2E,EAAMI,UAAS,OAAA/E,OAAM2E,EAAMK,SAAQ,KAAAhF,OAAI2E,EAAMM,YAC9D,eAAgB,CACdG,aAAc,QAEhB,CAAC,KAADpF,OAAM4E,EAAY,mBAAA5E,OAAkB4E,EAAY,kBAAkB,CAChES,QAAS,GAAFrF,OAAK2E,EAAMU,QAAO,OAAArF,OAAM2E,EAAMW,UAAS,MAC9CC,gBAAiB,GAAFvF,OAAK2E,EAAMI,UAAS,OAAA/E,OAAM2E,EAAMK,SAAQ,KAAAhF,OAAI2E,EAAMM,YACjE,eAAgB,CACdM,gBAAiB,SAGrB,CAAC,KAADvF,OAAM4E,EAAY,gBAAgB,CAChCY,MAAOb,EAAMc,mBACbC,gBAAiBb,EACjB,WAAY,CACVc,QAAS,WAKjB,CAAC,IAAD3F,OAAK4E,EAAY,YAAY,CAC3B,CAAC,GAAD5E,OAAI4E,EAAY,SAAS,CACvB,CAAC,KAAD5E,OAAM4E,EAAY,mBAAA5E,OAAkB4E,EAAY,kBAAkB,CAChES,QAAS,GAAFrF,OAAK2E,EAAMiB,UAAS,OAAA5F,OAAM2E,EAAMW,UAAS,SAItD,CAAC,IAADtF,OAAK4E,EAAY,WAAW,CAC1B,CAAC,GAAD5E,OAAI4E,EAAY,SAAS,CACvB,CAAC,KAAD5E,OAAM4E,EAAY,mBAAA5E,OAAkB4E,EAAY,kBAAkB,CAChES,QAAS,GAAFrF,OAAK2E,EAAMkB,UAAS,OAAA7F,OAAM2E,EAAMU,QAAO,UAKvD,EAEGS,EAAuBnB,IAC3B,MAAM,aACJC,EAAY,WACZmB,EAAU,kBACVC,EAAiB,iBACjBC,EAAgB,gBAChBC,EAAe,kBACfC,GACExB,EACJ,MAAO,CACL,CAACC,GAAe7B,OAAOC,OAAOD,OAAOC,OAAOD,OAAOC,OAAO,CAAC,GAAGoD,EAAAA,EAAAA,IAAezB,IAASD,EAAiBC,IAAS,CAC9G,QAAW,CACT0B,UAAW,OAEb,CAAC,GAADrG,OAAI4E,EAAY,YAAY,CAC1Be,QAAS,OACTW,WAAY,SACZC,aAAcJ,GAEhB,CAAC,GAADnG,OAAI4E,EAAY,WAAW7B,OAAOC,OAAOD,OAAOC,OAAO,CAAC,EAAGwD,EAAAA,IAAe,CACxEC,KAAM,OACNjB,MAAOb,EAAM+B,UACbC,WAAYhC,EAAMiC,iBAClBC,SAAUlC,EAAMmC,WAChBC,WAAYpC,EAAMqC,eAEpB,CAAC,GAADhH,OAAI4E,EAAY,WAAW,CACzBqC,kBAAmB,OACnBzB,MAAOO,EACPc,SAAUlC,EAAMkC,UAElB,CAAC,GAAD7G,OAAI4E,EAAY,UAAU,CACxBsC,MAAO,OACPC,aAAcxC,EAAMyC,eACpBC,MAAO,CACLH,MAAO,OACPhC,YAAa,UAGjB,CAAC,GAADlF,OAAI4E,EAAY,SAAS,CACvB,aAAc,CACZ0C,cAAetB,GAEjB,eAAgB,CACdZ,aAAc,SAGlB,CAAC,GAADpF,OAAI4E,EAAY,gBAAgB,CAC9BY,MAAOb,EAAM4C,kBACbZ,WAAY,SACZE,SAAUlC,EAAMkC,SAChBE,WAAYpC,EAAMoC,WAClBS,UAAW,QACX,WAAY,CACV5F,QAAS,MACT6F,SAAU,WACVC,KAAM,GACNC,aAAc,GAAF3H,OAAKkG,EAAe,OAAAlG,OAAMiG,EAAgB,OAExD,CAAC,IAADjG,OAAK4E,EAAY,0BAA0B,CACzChD,QAAS,OAGb,CAAC,GAAD5B,OAAI4E,EAAY,mBAAmB,CACjC,WAAY,CACVgD,OAAQ,EACRhG,QAAS,OAGb,CAAC,GAAD5B,OAAI4E,EAAY,kBAAkB,CAChCe,QAAS,aACTc,KAAM,EACNjB,MAAOb,EAAM+B,UACbG,SAAUlC,EAAMkC,SAChBE,WAAYpC,EAAMoC,WAClBc,UAAW,aACXC,aAAc,cAEhB,CAAC,GAAD9H,OAAI4E,EAAY,UAAU,CACxB0C,cAAe,EACfS,cAAe,MACf,cAAe,CACbpC,QAAS,OACT,CAAC,GAAD3F,OAAI4E,EAAY,gBAAgB,CAC9Be,QAAS,cACTW,WAAY,YAEd,CAAC,GAADtG,OAAI4E,EAAY,kBAAkB,CAChCe,QAAS,cACTW,WAAY,cAIlB,WAAY,CACV,CAAC,GAADtG,OAAI4E,EAAY,SAAS,CACvB,aAAc,CACZ0C,cAAe3C,EAAMiB,aAI3B,UAAW,CACT,CAAC,GAAD5F,OAAI4E,EAAY,SAAS,CACvB,aAAc,CACZ0C,cAAe3C,EAAMkB,eAK9B,EAGH,GAAemC,EAAAA,EAAAA,GAAsB,gBAAgBrD,IACnD,MAAMsD,GAAmBC,EAAAA,EAAAA,IAAWvD,EAAO,CAAC,GAC5C,MAAO,CAACmB,EAAqBmC,GAAkB,IAC9CtD,IAAS,CACVE,QAASF,EAAMwD,eACfhC,kBAAmBxB,EAAMyD,WAAazD,EAAM0D,aAC5CrC,kBAAmBrB,EAAMU,QACzBY,iBAAkBtB,EAAM2D,SACxBpC,gBAAiBvB,EAAM4D,UAAY,EACnCxC,WAAYpB,EAAM+B,cC3KpB,IAAI8B,EAAgC,SAAUC,EAAGC,GAC/C,IAAIC,EAAI,CAAC,EACT,IAAK,IAAIC,KAAKH,EAAO1F,OAAO8F,UAAUC,eAAeC,KAAKN,EAAGG,IAAMF,EAAEM,QAAQJ,GAAK,IAAGD,EAAEC,GAAKH,EAAEG,IAC9F,GAAS,MAALH,GAAqD,oBAAjC1F,OAAOkG,sBAA2C,KAAIC,EAAI,EAAb,IAAgBN,EAAI7F,OAAOkG,sBAAsBR,GAAIS,EAAIN,EAAEtJ,OAAQ4J,IAClIR,EAAEM,QAAQJ,EAAEM,IAAM,GAAKnG,OAAO8F,UAAUM,qBAAqBJ,KAAKN,EAAGG,EAAEM,MAAKP,EAAEC,EAAEM,IAAMT,EAAEG,EAAEM,IADuB,CAGvH,OAAOP,CACT,EAYA,MAAMS,EAAqB,CACzBC,IAAK,EACLC,GAAI,EACJC,GAAI,EACJC,GAAI,EACJC,GAAI,EACJC,GAAI,GAgBN,MAAMC,EAAexI,IACnB,MACIiB,UAAWwH,EAAkB,MAC7BtJ,EAAK,MACLuJ,EAAK,OACLC,EAASV,EAAkB,MAC3BvH,GAAQ,EAAI,SACZH,EAAQ,OACRqI,EAAM,SACNhJ,EAAQ,UACRD,EAAS,cACTkJ,EAAa,MACbzI,EACA0I,KAAMC,EAAa,WACnB1I,EAAU,aACVC,EAAY,MACZS,GACEf,EACJgJ,EAAY3B,EAAOrH,EAAO,CAAC,YAAa,QAAS,QAAS,SAAU,QAAS,WAAY,SAAU,WAAY,YAAa,gBAAiB,QAAS,OAAQ,aAAc,eAAgB,WACxL,aACJiJ,EAAY,UACZ/D,EAAS,aACTgE,GACErJ,EAAAA,WAAiBsJ,EAAAA,IACflI,EAAYgI,EAAa,eAAgBR,IACxCW,EAASC,GAAcxJ,EAAAA,SAAe,CAAC,GACxC2C,EAxCR,SAAmBmG,EAAQS,GACzB,GAAsB,kBAAXT,EACT,OAAOA,EAET,GAAsB,kBAAXA,EACT,IAAK,IAAIZ,EAAI,EAAGA,EAAIuB,EAAAA,EAAgBnL,OAAQ4J,IAAK,CAC/C,MAAMwB,EAAaD,EAAAA,EAAgBvB,GACnC,GAAIqB,EAAQG,SAAsCnL,IAAvBuK,EAAOY,GAChC,OAAOZ,EAAOY,IAAetB,EAAmBsB,EAEpD,CAEF,OAAO,CACT,CA2BuBC,CAAUb,EAAQS,GACjCK,GAAaC,EAAAA,EAAAA,GAAQX,GACrBtG,EAAOO,EAAOR,EAAczB,EAAOnB,IAClC+J,EAASC,GAAUC,EAAS5I,GAC7B6I,GAAqBC,EAAAA,EAAAA,KAE3BlK,EAAAA,WAAgB,KACd,MAAM2D,EAAQsG,EAAmBE,WAAUC,IACnB,kBAAXtB,GAGXU,EAAWY,EAAW,IAExB,MAAO,KACLH,EAAmBI,YAAY1G,EAAM,CACtC,GACA,IAEH,MAAM2G,EAAetK,EAAAA,SAAc,KAAM,CACvCQ,aACAC,kBACE,CAACD,EAAYC,IACjB,OAAOqJ,EAAsB9J,EAAAA,cAAoBkC,EAAoBqI,SAAU,CAC7EC,MAAOF,GACOtK,EAAAA,cAAoB,MAAO+B,OAAOC,OAAO,CACvDlC,UAAWiB,IAAWK,EAA4B,OAAjBiI,QAA0C,IAAjBA,OAA0B,EAASA,EAAavJ,UAAW,CACnH,CAAC,GAADd,OAAIoC,EAAS,KAAApC,OAAI4K,IAAeA,GAA6B,YAAfA,EAC9C,CAAC,GAAD5K,OAAIoC,EAAS,gBAAgBV,EAC7B,CAAC,GAAD1B,OAAIoC,EAAS,SAAuB,QAAdiE,GACrBvF,EAAWkJ,EAAee,GAC7BxJ,MAAOwB,OAAOC,OAAOD,OAAOC,OAAO,CAAC,EAAoB,OAAjBqH,QAA0C,IAAjBA,OAA0B,EAASA,EAAa9I,OAAQA,IACvH4I,IAAa7J,GAASuJ,IAAuB7I,EAAAA,cAAoB,MAAO,CACzEF,UAAW,GAAFd,OAAKoC,EAAS,YACtB9B,GAAsBU,EAAAA,cAAoB,MAAO,CAClDF,UAAW,GAAFd,OAAKoC,EAAS,WACtB9B,GAAQuJ,GAAsB7I,EAAAA,cAAoB,MAAO,CAC1DF,UAAW,GAAFd,OAAKoC,EAAS,WACtByH,IAAsB7I,EAAAA,cAAoB,MAAO,CAClDF,UAAW,GAAFd,OAAKoC,EAAS,UACTpB,EAAAA,cAAoB,QAAS,KAAmBA,EAAAA,cAAoB,QAAS,KAAM4C,EAAKlB,KAAI,CAACU,EAAKR,IAAuB5B,EAAAA,cAAoByK,EAAK,CAChK5I,IAAKD,EACLA,MAAOA,EACPf,MAAOA,EACPO,UAAWA,EACXe,SAAqB,aAAX4G,EACVrI,SAAUA,EACV0B,IAAKA,WACE,EAMXuG,EAAa+B,KAAOC,EACpB,U,iFClGA,MAAM,OAAEC,GAAWC,EAAAA,SACb,YAAEC,GAAgBC,EAAAA,QAiqBxB,QA/pBA,WAAwB,IAADC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EACrB,MAAOC,EAAaC,IAAkB5M,EAAAA,EAAAA,UAAS,KACxC6M,EAAcC,IAAmB9M,EAAAA,EAAAA,UAAS,KAC1C+M,EAAeC,IAAoBhN,EAAAA,EAAAA,UAAS,QAC5CiN,EAAeC,IAAoBlN,EAAAA,EAAAA,UAAS,QAC5CmN,EAAWC,IAAgBpN,EAAAA,EAAAA,UAAS,OACpCqN,EAAUC,IAAetN,EAAAA,EAAAA,UAAS,UAClCuN,GAAOC,KAAYxN,EAAAA,EAAAA,UAAS,CACjCyN,WAAY,EACZC,YAAa,EACbC,aAAc,EACdC,OAAQ,EACRC,UAAW,KAEN/N,GAAUC,KAAeC,EAAAA,EAAAA,UAASE,OAAOC,WAAa,MACtD2N,GAAUC,KAAe/N,EAAAA,EAAAA,UAASE,OAAOC,YAAc,KAAOD,OAAOC,WAAa,OAClF6N,GAAgBC,KAAqBjO,EAAAA,EAAAA,WAAS,IAC9CkO,GAAgBC,KAAqBnO,EAAAA,EAAAA,UAAS,MAC/CoO,IAAWC,EAAAA,EAAAA,MAEXC,GAAkBpQ,IACtB,IAAKA,GAAwB,IAAhBA,EAAKW,OAQhB,YAPA2O,GAAS,CACPC,WAAY,EACZC,YAAa,EACbC,aAAc,EACdC,OAAQ,EACRC,UAAW,IAKf,MAAMJ,EAAavP,EAAKW,OAClB6O,EAAcxP,EAAKmF,QAAOkL,IAAM,IAAAC,EAAA,MAA+B,UAAd,QAAbA,EAAAD,EAAOE,cAAM,IAAAD,OAAA,EAAbA,EAAeE,QAAkB,IAAE7P,OACvE8P,EAASzQ,EAAK+D,KAAIsM,IAAW,IAADK,EAAAC,EAAAC,EAGhC,QAF8B,QAAbF,EAAAL,EAAOE,cAAM,IAAAG,GAAgB,QAAhBC,EAAbD,EAAeG,sBAAc,IAAAF,OAAhB,EAAbA,EAA+BhQ,SAAU,KACjC,QAAXiQ,EAAAP,EAAOS,YAAI,IAAAF,OAAA,EAAXA,EAAaG,aAAc,GACb,GAAG,IAG3BtB,EAAegB,EAAOO,QAAO,CAACC,EAAKC,IAAUD,EAAMC,GAAO,GAAK3B,EAC/DI,EAAYwB,KAAKC,OAAOX,GAG9B,IAAIY,EAAgB,EAChBC,EAAY,EAChB,IAAK,IAAI/G,EAAIvK,EAAKW,OAAS,EAAG4J,GAAK,EAAGA,IAAK,CAAC,IAADgH,EACT,UAAd,QAAdA,EAAAvR,EAAKuK,GAAGgG,cAAM,IAAAgB,OAAA,EAAdA,EAAgBf,UAClBa,IACAC,EAAYH,KAAKC,IAAIE,EAAWD,IAEhCA,EAAgB,CAEpB,CAEA/B,GAAS,CACPC,aACAC,cACAC,aAAc0B,KAAKK,MAAM/B,GACzBC,OAAQ4B,EACR3B,UAAWwB,KAAKK,MAAM7B,IACtB,GA6CJ5N,EAAAA,EAAAA,YAAU,KA1CMlC,WACd,IACEqQ,IAASuB,EAAAA,EAAAA,OACT,MAAMvR,QAAiBG,EAAAA,EAAAA,MACnBH,EAASwR,SACXhD,EAAexO,EAASF,MACxB4O,EAAgB1O,EAASF,MACzBoQ,GAAelQ,EAASF,OAExB2R,EAAAA,GAAQ1R,MAAMC,EAASyR,SAEzBzB,IAAS0B,EAAAA,EAAAA,MACX,CAAE,MAAO3R,GACPiQ,IAAS0B,EAAAA,EAAAA,OACTD,EAAAA,GAAQ1R,MAAMA,EAAM0R,QACtB,GA4BAE,EAAS,GACR,KAEH9P,EAAAA,EAAAA,YAAU,KA5BW+P,MACnB,IAAIC,EAAW,IAAItD,GAEG,QAAlBI,IACFkD,EAAWA,EAAS5M,QAAOkL,IAAM,IAAA2B,EAAAC,EAAA,OACpB,QADoBD,EAC/B3B,EAAOS,YAAI,IAAAkB,GAAS,QAATC,EAAXD,EAAaE,eAAO,IAAAD,OAAT,EAAXA,EAAsBE,cAAcC,SAASvD,EAAcsD,cAAc,KAIvD,QAAlBpD,IACFgD,EAAWA,EAAS5M,QAAOkL,IAAM,IAAAgC,EAAA,OAAiB,QAAbA,EAAAhC,EAAOE,cAAM,IAAA8B,OAAA,EAAbA,EAAe7B,WAAYzB,CAAa,KAG3EE,GAAkC,IAArBA,EAAUtO,SACzBoR,EAAWA,EAAS5M,QAAOkL,GACNiC,IAAOjC,EAAOkC,WACfC,UAAUvD,EAAU,GAAIA,EAAU,GAAI,MAAO,SAInEL,EAAgBmD,GAChB3B,GAAe2B,EAAS,EAQxBD,EAAc,GACb,CAACjD,EAAeE,EAAeE,EAAWR,KAE7C1M,EAAAA,EAAAA,YAAU,KACR,MAAM0Q,EAAeA,KACnB5Q,GAAYG,OAAOC,WAAa,KAChC4N,GAAY7N,OAAOC,YAAc,KAAOD,OAAOC,WAAa,KAAK,EAInE,OADAD,OAAO0Q,iBAAiB,SAAUD,GAC3B,IAAMzQ,OAAO2Q,oBAAoB,SAAUF,EAAa,GAC9D,IAEH,MAOMG,GAAkBpC,GACH,SAAZA,GACLtO,EAAAA,EAAAA,KAAC2Q,EAAAA,IAAO,CAAC1Q,UAAU,4BACnBD,EAAAA,EAAAA,KAAC4Q,EAAAA,IAAG,CAAC3Q,UAAU,yBAQb4Q,GAAqBC,IACzB/C,GAAkB+C,GAClBjD,IAAkB,EAAK,EAGnBkD,GAAmBA,KACvBlD,IAAkB,GAClBE,GAAkB,KAAK,EA6InBiD,GA1IuBC,MAC3B,MAAMvR,EAAWI,OAAOC,WAAa,IAC/B2N,EAAW5N,OAAOC,YAAc,KAAOD,OAAOC,WAAa,KAE3DmR,EAAc,CAClB,CACEzR,MAAO,OACP0R,UAAW,WACXnP,IAAK,WACLoP,OAAQA,CAACC,EAAMP,KAAM,IAAAQ,EAAAC,EAAA,OACnBC,EAAAA,EAAAA,MAAA,OAAKvR,UAAU,UAASC,SAAA,EACtBF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,4DAA2DC,UAC5D,QAAXoR,EAAAR,EAAOlC,YAAI,IAAA0C,OAAA,EAAXA,EAAaG,OAAQ,kBAExBzR,EAAAA,EAAAA,KAAA,OAAKC,UAAU,4CAA2CC,UAC5C,QAAXqR,EAAAT,EAAOlC,YAAI,IAAA2C,OAAA,EAAXA,EAAavB,UAAW,YAE1BtQ,IACC8R,EAAAA,EAAAA,MAAA,OAAKvR,UAAU,yCAAwCC,SAAA,EACrDF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,wBAAuBC,SACnCkQ,IAAOU,EAAOT,WAAWqB,OAAO,mBAEnC1R,EAAAA,EAAAA,KAAC2R,EAAAA,GAAM,CACLnQ,KAAK,OACL4H,KAAK,QACLwI,MAAM5R,EAAAA,EAAAA,KAAC6R,EAAAA,IAAK,IACZC,QAASA,IAAMjB,GAAkBC,GACjC7Q,UAAU,2BAA0BC,SACrC,cAKD,EAERmG,MAAO3G,EAAW,IAAMgO,EAAW,IAAM,IACzCqE,UAAU,GAEZ,CACEtS,MAAO,QACP0R,UAAW,QACXnP,IAAK,QACLoP,OAAQA,CAACC,EAAMP,KAAY,IAADkB,EAAAC,EAAAC,EACxB,MAAMC,GAAwB,QAAbH,EAAAlB,EAAOzC,cAAM,IAAA2D,GAAgB,QAAhBC,EAAbD,EAAerD,sBAAc,IAAAsD,OAAhB,EAAbA,EAA+BxT,SAAU,EACpD2T,GAAmB,QAAXF,EAAApB,EAAOlC,YAAI,IAAAsD,OAAA,EAAXA,EAAarD,aAAc,EACnCwD,EAAapD,KAAKK,MAAO6C,EAAWC,EAAS,KAEnD,OACEZ,EAAAA,EAAAA,MAAA,OAAKvR,UAAU,cAAaC,SAAA,EAC1BsR,EAAAA,EAAAA,MAAA,OAAKvR,UAAU,+CAA8CC,SAAA,CAC1DiS,EAAS,IAAEC,MAEdpS,EAAAA,EAAAA,KAACsS,EAAAA,EAAQ,CACPC,QAASF,EACTjJ,KAAK,QACLoJ,YAAaH,GAAc,GAAK,UAAY,UAC5CI,UAAU,EACVxS,UAAU,UAEZuR,EAAAA,EAAAA,MAAA,OAAKvR,UAAS,kCAAAd,QAvFH6P,EAuFqDqD,EAtFtErD,GAAS,GAAW,iBACpBA,GAAS,GAAW,gBACpBA,GAAS,GAAW,kBACjB,iBAmFiF9O,SAAA,CAC3EmS,EAAW,UAxFHrD,KA0FP,EAGV3I,MAAO3G,EAAW,GAAK,IACvBgT,OAAQA,CAACC,EAAGC,KAAO,IAADC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAGhB,OAFejE,KAAKK,QAAgB,QAARuD,EAAAF,EAAEtE,cAAM,IAAAwE,GAAgB,QAAhBC,EAARD,EAAUlE,sBAAc,IAAAmE,OAAhB,EAARA,EAA0BrU,SAAU,KAAY,QAANsU,EAAAJ,EAAE/D,YAAI,IAAAmE,OAAA,EAANA,EAAQlE,aAAc,GAAM,KACnFI,KAAKK,QAAgB,QAAR0D,EAAAJ,EAAEvE,cAAM,IAAA2E,GAAgB,QAAhBC,EAARD,EAAUrE,sBAAc,IAAAsE,OAAhB,EAARA,EAA0BxU,SAAU,KAAY,QAANyU,EAAAN,EAAEhE,YAAI,IAAAsE,OAAA,EAANA,EAAQrE,aAAc,GAAM,IAC5E,GAG1B,CACEpP,MAAO,SACP0R,UAAW,UACXnP,IAAK,UACLoP,OAAQA,CAACC,EAAMP,KAAY,IAADqC,EACxB,MAAM7E,EAAuB,QAAhB6E,EAAGrC,EAAOzC,cAAM,IAAA8E,OAAA,EAAbA,EAAe7E,QACzB8E,EAAuB,SAAZ9E,EAEjB,OACEtO,EAAAA,EAAAA,KAACqT,EAAAA,EAAG,CACFzB,KAAOlS,EAAqC,KAA1BgR,GAAepC,GACjC3J,MAAOyO,EAAW,UAAY,QAC9BnT,UAAU,iCAAgCC,SAEzCR,EAAY0T,EAAW,IAAM,IAAQ9E,GAAW,OAC7C,EAGVjI,MAAO3G,EAAW,GAAK,IACvBxB,QAAUwB,OAGNhB,EAHiB,CACnB,CAAE2S,KAAM,OAAQ1G,MAAO,QACvB,CAAE0G,KAAM,OAAQ1G,MAAO,SAEzB2I,SAAW5T,OAAiEhB,EAAtD,CAACiM,EAAOmG,KAAM,IAAAyC,EAAA,OAAkB,QAAbA,EAAAzC,EAAOzC,cAAM,IAAAkF,OAAA,EAAbA,EAAejF,WAAY3D,CAAK,IAwC7E,OAnCKjL,GACHwR,EAAYsC,OAAO,EAAG,EAAG,CACvB/T,MAAO,OACP0R,UAAW,YACXnP,IAAK,OACLoP,OAASqC,IACPjC,EAAAA,EAAAA,MAAA,OAAKvR,UAAU,UAASC,SAAA,EACtBF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,cAAaC,SAAEkQ,IAAOqD,GAAM/B,OAAO,mBAClD1R,EAAAA,EAAAA,KAAA,OAAKC,UAAU,gBAAeC,SAAEkQ,IAAOqD,GAAM/B,OAAO,cAGxDrL,MAAOqH,EAAW,IAAM,MAKvBhO,GACHwR,EAAY7N,KAAK,CACf5D,MAAO,UACPuC,IAAK,UACLoP,OAAQA,CAACC,EAAMP,KACb9Q,EAAAA,EAAAA,KAAC2R,EAAAA,GAAM,CACLnQ,KAAK,UACL4H,KAAK,QACLwI,MAAM5R,EAAAA,EAAAA,KAAC6R,EAAAA,IAAK,IACZC,QAASA,IAAMjB,GAAkBC,GACjC7Q,UAAU,gCAA+BC,SAExCwN,EAAW,GAAK,SAGrBrH,MAAOqH,EAAW,GAAK,KAIpBwD,CAAW,EAGJD,GAEhB,OACEO,EAAAA,EAAAA,MAAA,OAAKvR,UAAU,qEAAoEC,SAAA,EACjFF,EAAAA,EAAAA,KAAC0T,EAAAA,EAAS,CAACjU,MAAM,yBAEjB+R,EAAAA,EAAAA,MAAA,OAAKvR,UAAU,sEAAqEC,SAAA,EAElFsR,EAAAA,EAAAA,MAACmC,EAAAA,EAAOC,IAAG,CACTC,QAAS,CAAEC,QAAS,EAAGC,EAAG,IAC1BC,QAAS,CAAEF,QAAS,EAAGC,EAAG,GAC1B9T,UAAU,qCAAoCC,SAAA,EAE9CF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,kLAAiLC,UAC9LF,EAAAA,EAAAA,KAACiU,EAAAA,IAAU,CAAChU,UAAU,sDAExBuR,EAAAA,EAAAA,MAAA,MAAIvR,UAAU,6EAA4EC,SAAA,CAAC,SACpFF,EAAAA,EAAAA,KAAA,QAAMC,UAAU,6EAA4EC,SAAC,gBAAkB,eAEtHF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,mFAAkFC,SAAC,uFAMlGsR,EAAAA,EAAAA,MAACmC,EAAAA,EAAOC,IAAG,CACTC,QAAS,CAAEC,QAAS,EAAGC,EAAG,IAC1BC,QAAS,CAAEF,QAAS,EAAGC,EAAG,GAC1BG,WAAY,CAAEC,MAAO,IACrBlU,UAAU,sFAAqFC,SAAA,EAE/FF,EAAAA,EAAAA,KAACoU,EAAAA,EAAI,CAACnU,UAAU,8GAA6GC,UAC3HsR,EAAAA,EAAAA,MAAA,OAAKvR,UAAU,wCAAuCC,SAAA,EACpDF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,iHAAgHC,UAC7HF,EAAAA,EAAAA,KAACqU,EAAAA,IAAQ,CAACpU,UAAU,sDAEtBD,EAAAA,EAAAA,KAACsU,EAAAA,EAAS,CACR7U,MAAM,cACNkL,MAAOwC,GAAME,WACbkH,WAAY,CACV5P,MAAO,UACPmB,WAAY,QAEd7F,UAAU,+BAKhBD,EAAAA,EAAAA,KAACoU,EAAAA,EAAI,CAACnU,UAAU,gHAA+GC,UAC7HsR,EAAAA,EAAAA,MAAA,OAAKvR,UAAU,wCAAuCC,SAAA,EACpDF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,kHAAiHC,UAC9HF,EAAAA,EAAAA,KAAC2Q,EAAAA,IAAO,CAAC1Q,UAAU,sDAErBD,EAAAA,EAAAA,KAACsU,EAAAA,EAAS,CACR7U,MAAM,SACNkL,MAAOwC,GAAMG,YACbiH,WAAY,CACV5P,MAAO,UACPmB,WAAY,QAEd7F,UAAU,+BAKhBD,EAAAA,EAAAA,KAACoU,EAAAA,EAAI,CAACnU,UAAU,kHAAiHC,UAC/HsR,EAAAA,EAAAA,MAAA,OAAKvR,UAAU,wCAAuCC,SAAA,EACpDF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,mHAAkHC,UAC/HF,EAAAA,EAAAA,KAACwU,EAAAA,IAAY,CAACvU,UAAU,sDAE1BD,EAAAA,EAAAA,KAACsU,EAAAA,EAAS,CACR7U,MAAM,gBACNkL,MAAOwC,GAAMI,aACbkH,OAAO,IACPF,WAAY,CACV5P,MAAO,UACPmB,WAAY,QAEd7F,UAAU,+BAKhBD,EAAAA,EAAAA,KAACoU,EAAAA,EAAI,CAACnU,UAAU,kHAAiHC,UAC/HsR,EAAAA,EAAAA,MAAA,OAAKvR,UAAU,wCAAuCC,SAAA,EACpDF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,mHAAkHC,UAC/HF,EAAAA,EAAAA,KAAC0U,EAAAA,IAAQ,CAACzU,UAAU,sDAEtBD,EAAAA,EAAAA,KAACsU,EAAAA,EAAS,CACR7U,MAAM,aACNkL,MAAOwC,GAAMM,UACbgH,OAAO,IACPF,WAAY,CACV5P,MAAO,UACPmB,WAAY,QAEd7F,UAAU,+BAKhBD,EAAAA,EAAAA,KAACoU,EAAAA,EAAI,CAACnU,UAAU,0IAAyIC,UACvJsR,EAAAA,EAAAA,MAAA,OAAKvR,UAAU,wCAAuCC,SAAA,EACpDF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,iHAAgHC,UAC7HF,EAAAA,EAAAA,KAAC2U,EAAAA,IAAO,CAAC1U,UAAU,sDAErBD,EAAAA,EAAAA,KAACsU,EAAAA,EAAS,CACR7U,MAAM,cACNkL,MAAOwC,GAAMK,OACb+G,WAAY,CACV5P,MAAO,UACPmB,WAAY,QAEd7F,UAAU,kCASlBD,EAAAA,EAAAA,KAAC2T,EAAAA,EAAOC,IAAG,CACTC,QAAS,CAAEC,QAAS,EAAGC,EAAG,IAC1BC,QAAS,CAAEF,QAAS,EAAGC,EAAG,GAC1BG,WAAY,CAAEC,MAAO,IACrBlU,UAAU,8FAA6FC,UAEvGsR,EAAAA,EAAAA,MAAA,OAAKvR,UAAU,sBAAqBC,SAAA,EAClCsR,EAAAA,EAAAA,MAAA,OAAKvR,UAAU,0BAAyBC,SAAA,EACtCF,EAAAA,EAAAA,KAAC4U,EAAAA,IAAQ,CAAC3U,UAAU,2BACpBD,EAAAA,EAAAA,KAAA,MAAIC,UAAU,sCAAqCC,SAAC,uBAItDsR,EAAAA,EAAAA,MAAA,OAAKvR,UAAU,uDAAsDC,SAAA,EAEnEsR,EAAAA,EAAAA,MAAA,OAAKvR,UAAU,YAAWC,SAAA,EACxBF,EAAAA,EAAAA,KAAA,SAAOC,UAAU,oCAAmCC,SAAC,aACrDsR,EAAAA,EAAAA,MAACxG,EAAAA,QAAM,CACL6J,YAAY,eACZlK,MAAOgC,EACPmI,SAAUlI,EACV3M,UAAU,SACVmJ,KAAK,QAAOlJ,SAAA,EAEZF,EAAAA,EAAAA,KAAC+K,EAAM,CAACJ,MAAM,MAAKzK,SAAC,iBA3SV6U,MACxB,MAAMC,EAAWzI,EAAY1K,KAAIsM,IAAM,IAAA8G,EAAA,OAAe,QAAfA,EAAI9G,EAAOS,YAAI,IAAAqG,OAAA,EAAXA,EAAajF,OAAO,IAAE/M,OAAOiS,SACxE,MAAO,IAAI,IAAIC,IAAIH,GAAU,EA0SdD,GAAoBlT,KAAImO,IACvBhQ,EAAAA,EAAAA,KAAC+K,EAAM,CAAeJ,MAAOqF,EAAQ9P,SAAE8P,GAA1BA,YAMnBwB,EAAAA,EAAAA,MAAA,OAAKvR,UAAU,YAAWC,SAAA,EACxBF,EAAAA,EAAAA,KAAA,SAAOC,UAAU,oCAAmCC,SAAC,YACrDsR,EAAAA,EAAAA,MAACxG,EAAAA,QAAM,CACL6J,YAAY,cACZlK,MAAOkC,EACPiI,SAAUhI,EACV7M,UAAU,SACVmJ,KAAK,QAAOlJ,SAAA,EAEZF,EAAAA,EAAAA,KAAC+K,EAAM,CAACJ,MAAM,MAAKzK,SAAC,iBACpBF,EAAAA,EAAAA,KAAC+K,EAAM,CAACJ,MAAM,OAAMzK,SAAC,YACrBF,EAAAA,EAAAA,KAAC+K,EAAM,CAACJ,MAAM,OAAMzK,SAAC,kBAKzBsR,EAAAA,EAAAA,MAAA,OAAKvR,UAAU,wCAAuCC,SAAA,EACpDF,EAAAA,EAAAA,KAAA,SAAOC,UAAU,oCAAmCC,SAAC,gBACrDF,EAAAA,EAAAA,KAACiL,EAAW,CACVN,MAAOoC,EACP+H,SAAU9H,EACV/M,UAAU,SACVmJ,KAAK,QACLyL,YAAa,CAAC,OAAQ,MACtBnD,OAAO,aACP0D,YAAU,QAKd5D,EAAAA,EAAAA,MAAA,OAAKvR,UAAU,YAAWC,SAAA,EACxBF,EAAAA,EAAAA,KAAA,SAAOC,UAAU,8CAA6CC,SAAC,aAC/DF,EAAAA,EAAAA,KAAC2R,EAAAA,GAAM,CACLG,QAASA,KACPlF,EAAiB,OACjBE,EAAiB,OACjBE,EAAa,KAAK,EAEpB5D,KAAK,QACLnJ,UAAU,SACV2R,MAAM5R,EAAAA,EAAAA,KAAC4Q,EAAAA,IAAG,IAAI1Q,SACf,qBAOe,QAAlByM,GAA6C,QAAlBE,GAA2BE,KACtDyE,EAAAA,EAAAA,MAAA,OAAKvR,UAAU,qDAAoDC,SAAA,EACjEF,EAAAA,EAAAA,KAAA,QAAMC,UAAU,oCAAmCC,SAAC,oBACjC,QAAlByM,IACC3M,EAAAA,EAAAA,KAACqT,EAAAA,EAAG,CACFgC,UAAQ,EACRC,QAASA,IAAM1I,EAAiB,OAChC3M,UAAU,2CAA0CC,SAEnDyM,IAGc,QAAlBE,IACC7M,EAAAA,EAAAA,KAACqT,EAAAA,EAAG,CACFgC,UAAQ,EACRC,QAASA,IAAMxI,EAAiB,OAChC7M,UAA6B,SAAlB4M,EAA2B,8CAAgD,wCAAwC3M,SAE7H2M,IAGJE,IACCyE,EAAAA,EAAAA,MAAC6B,EAAAA,EAAG,CACFgC,UAAQ,EACRC,QAASA,IAAMtI,EAAa,MAC5B/M,UAAU,iDAAgDC,SAAA,CAEzD6M,EAAU,GAAG2E,OAAO,YAAY,MAAI3E,EAAU,GAAG2E,OAAO,wBASrE1R,EAAAA,EAAAA,KAAC2T,EAAAA,EAAOC,IAAG,CACTC,QAAS,CAAEC,QAAS,EAAGC,EAAG,IAC1BC,QAAS,CAAEF,QAAS,EAAGC,EAAG,GAC1BG,WAAY,CAAEC,MAAO,IACrBlU,UAAU,sFAAqFC,UAE/FF,EAAAA,EAAAA,KAACuV,EAAAA,EAAK,CACJvE,QAASA,GACTwE,WAAY/I,EACZgJ,OAAS3E,GAAWA,EAAO4E,IAC3BC,WAAY,CACVC,SAAU9V,OAAOC,WAAa,IAAM,EAAI,GACxC8V,gBAAiB/V,OAAOC,YAAc,IACtC+V,gBAAiBhW,OAAOC,YAAc,IACtCgW,UAAWA,CAAC3D,EAAO4D,IACjBlW,OAAOC,YAAc,IAAG,GAAAZ,OACjB6W,EAAM,GAAE,KAAA7W,OAAI6W,EAAM,GAAE,QAAA7W,OAAOiT,EAAK,eAAAjT,OAChC6W,EAAM,GAAE,KAAA7W,OAAI6W,EAAM,GAAE,OAAA7W,OAAMiT,GACnCnS,UAAW,4BACXgW,OAAQnW,OAAOC,WAAa,KAE9BmW,OAAQ,CAAEC,EAAGrW,OAAOC,WAAa,IAAM,IAAM,KAC7CE,UAAU,eACVmJ,KAAMtJ,OAAOC,WAAa,IAAM,SAAW,QAC3CqW,OAAQ,CACNC,WACErW,EAAAA,EAAAA,KAACsW,EAAAA,EAAK,CACJC,MAAOD,EAAAA,EAAME,uBACbC,aACEjF,EAAAA,EAAAA,MAAA,OAAKvR,UAAU,OAAMC,SAAA,EACnBF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,sDAAqDC,SAAC,2BACpEF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,0CAAyCC,SAAC,uFAUrEF,EAAAA,EAAAA,KAAC0W,EAAAA,EAAK,CACJjX,OACE+R,EAAAA,EAAAA,MAAA,OAAKvR,UAAU,0BAAyBC,SAAA,EACtCF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,oEAAmEC,UAChFF,EAAAA,EAAAA,KAAC6R,EAAAA,IAAK,CAAC5R,UAAU,0BAEnBD,EAAAA,EAAAA,KAAA,QAAMC,UAAU,wBAAuBC,SAAC,oBAG5CyW,KAAM/I,GACNgJ,SAAU7F,GACV8F,OAAQ,EACN7W,EAAAA,EAAAA,KAAC2R,EAAAA,GAAM,CAAaG,QAASf,GAAkB3H,KAAK,QAAOlJ,SAAC,SAAhD,UAIdmG,MAAO3G,GAAW,MAAQgO,GAAW,IAAM,IAC3CzN,UAAU,qBAAoBC,SAE7B4N,KACC0D,EAAAA,EAAAA,MAAA,OAAKvR,UAAU,YAAWC,SAAA,EAExBsR,EAAAA,EAAAA,MAAA,OAAKvR,UAAU,4BAA2BC,SAAA,EACxCF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,2CAA0CC,SAAC,sBACzDsR,EAAAA,EAAAA,MAAC1I,EAAY,CAACG,OAAQvJ,GAAW,EAAI,EAAG0J,KAAK,QAAOlJ,SAAA,EAClDF,EAAAA,EAAAA,KAAC8I,EAAa+B,KAAI,CAAC/J,MAAM,YAAYL,KAAMf,GAAW,EAAI,EAAEQ,UAC1DF,EAAAA,EAAAA,KAAA,QAAMC,UAAU,cAAaC,UAAqB,QAAnBiL,EAAA2C,GAAec,YAAI,IAAAzD,OAAA,EAAnBA,EAAqBsG,OAAQ,WAE9DzR,EAAAA,EAAAA,KAAC8I,EAAa+B,KAAI,CAAC/J,MAAM,UAASZ,UACZ,QAAnBkL,EAAA0C,GAAec,YAAI,IAAAxD,OAAA,EAAnBA,EAAqB4E,UAAW,aAEnChQ,EAAAA,EAAAA,KAAC8I,EAAa+B,KAAI,CAAC/J,MAAM,aAAYZ,SAClCkQ,IAAOtC,GAAeuC,WAAWqB,OAAO,+BAE3C1R,EAAAA,EAAAA,KAAC8I,EAAa+B,KAAI,CAAC/J,MAAM,kBAAiBZ,UACpB,QAAnBmL,EAAAyC,GAAec,YAAI,IAAAvD,OAAA,EAAnBA,EAAqBwD,aAAc,KAEtC7O,EAAAA,EAAAA,KAAC8I,EAAa+B,KAAI,CAAC/J,MAAM,gBAAeZ,UAClB,QAAnBoL,EAAAwC,GAAec,YAAI,IAAAtD,OAAA,EAAnBA,EAAqBwL,eAAgB,WAM5CtF,EAAAA,EAAAA,MAAA,OAAKvR,UAAU,4BAA2BC,SAAA,EACxCF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,2CAA0CC,SAAC,yBACzDsR,EAAAA,EAAAA,MAAA,OAAKvR,UAAU,wCAAuCC,SAAA,EACpDsR,EAAAA,EAAAA,MAAA,OAAKvR,UAAU,sCAAqCC,SAAA,EAClDF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,mCAAkCC,UACzB,QAArBqL,EAAAuC,GAAeO,cAAM,IAAA9C,GAAgB,QAAhBC,EAArBD,EAAuBoD,sBAAc,IAAAnD,OAAhB,EAArBA,EAAuC/M,SAAU,KAEpDuB,EAAAA,EAAAA,KAAA,OAAKC,UAAU,wBAAuBC,SAAC,wBAEzCsR,EAAAA,EAAAA,MAAA,OAAKvR,UAAU,sCAAqCC,SAAA,EAClDF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,mCAAkCC,WAC1B,QAAnBuL,EAAAqC,GAAec,YAAI,IAAAnD,OAAA,EAAnBA,EAAqBoD,aAAc,KAA2B,QAArBnD,EAAAoC,GAAeO,cAAM,IAAA3C,GAAgB,QAAhBC,EAArBD,EAAuBiD,sBAAc,IAAAhD,OAAhB,EAArBA,EAAuClN,SAAU,MAE9FuB,EAAAA,EAAAA,KAAA,OAAKC,UAAU,wBAAuBC,SAAC,sBAEzCsR,EAAAA,EAAAA,MAAA,OAAKvR,UAAU,sCAAqCC,SAAA,EAClDsR,EAAAA,EAAAA,MAAA,OAAKvR,UAAS,sBAAAd,OACZ8P,KAAKK,QAA6B,QAArB1D,EAAAkC,GAAeO,cAAM,IAAAzC,GAAgB,QAAhBC,EAArBD,EAAuB+C,sBAAc,IAAA9C,OAAhB,EAArBA,EAAuCpN,SAAU,KAAyB,QAAnBqN,EAAAgC,GAAec,YAAI,IAAA9C,OAAA,EAAnBA,EAAqB+C,aAAc,GAAM,MAAQ,GACjH,iBACA,gBACH3O,SAAA,CACA+O,KAAKK,QAA6B,QAArBvD,EAAA+B,GAAeO,cAAM,IAAAtC,GAAgB,QAAhBC,EAArBD,EAAuB4C,sBAAc,IAAA3C,OAAhB,EAArBA,EAAuCvN,SAAU,KAAyB,QAAnBwN,EAAA6B,GAAec,YAAI,IAAA3C,OAAA,EAAnBA,EAAqB4C,aAAc,GAAM,KAAK,QAErH7O,EAAAA,EAAAA,KAAA,OAAKC,UAAU,wBAAuBC,SAAC,oBAM7CsR,EAAAA,EAAAA,MAAA,OAAKvR,UAAS,kBAAAd,OACuB,UAAd,QAArB+M,EAAA4B,GAAeO,cAAM,IAAAnC,OAAA,EAArBA,EAAuBoC,SACnB,sCACA,mCACHpO,SAAA,EACDsR,EAAAA,EAAAA,MAAA,OAAKvR,UAAU,yCAAwCC,SAAA,CACpDwQ,GAAoC,QAAtBvE,EAAC2B,GAAeO,cAAM,IAAAlC,OAAA,EAArBA,EAAuBmC,UACvCtO,EAAAA,EAAAA,KAAA,QAAMC,UAAS,yBAAAd,OACsB,UAAd,QAArBiN,EAAA0B,GAAeO,cAAM,IAAAjC,OAAA,EAArBA,EAAuBkC,SAAqB,iBAAmB,gBAC9DpO,SACmC,UAAd,QAArBmM,EAAAyB,GAAeO,cAAM,IAAAhC,OAAA,EAArBA,EAAuBiC,SAAqB,8BAAgC,uCAGjFtO,EAAAA,EAAAA,KAAA,OAAKC,UAAU,mBAAkBC,UAC/BF,EAAAA,EAAAA,KAAA,QAAMC,UAAU,wBAAuBC,SACD,UAAd,QAArBoM,EAAAwB,GAAeO,cAAM,IAAA/B,OAAA,EAArBA,EAAuBgC,SACpB,kCACA,4EAUxB,C", "sources": ["apicalls/reports.js", "components/PageTitle.js", "../node_modules/antd/es/descriptions/DescriptionsContext.js", "../node_modules/antd/es/descriptions/Item.js", "../node_modules/antd/es/descriptions/Cell.js", "../node_modules/antd/es/descriptions/Row.js", "../node_modules/antd/es/descriptions/hooks/useRow.js", "../node_modules/antd/es/descriptions/style/index.js", "../node_modules/antd/es/descriptions/index.js", "pages/user/UserReports/index.js"], "sourcesContent": ["const { default: axiosInstance } = require(\".\");\r\n\r\n// add report\r\nexport const addReport = async (payload) => {\r\n    try {\r\n        const response = await axiosInstance.post(\"/api/reports/add-report\", payload);\r\n        return response.data;\r\n    } catch (error) {\r\n        return error.response.data;\r\n    }\r\n}\r\n\r\n// get all reports\r\nexport const getAllReports = async (filters) => {\r\n    try {\r\n        const response = await axiosInstance.post(\"/api/reports/get-all-reports\", filters);\r\n        return response.data;\r\n    } catch (error) {\r\n        return error.response.data;\r\n    }\r\n}\r\n\r\n// get all reports by user\r\nexport const getAllReportsByUser = async (payload) => {\r\n    try {\r\n        const response = await axiosInstance.post(\"/api/reports/get-all-reports-by-user\", payload);\r\n        return response.data;\r\n    } catch (error) {\r\n        return error.response.data;\r\n    }\r\n}\r\n\r\n// get all reports for ranking (legacy)\r\nexport const getAllReportsForRanking = async (filters) => {\r\n    try {\r\n        const response = await axiosInstance.get(\"/api/reports/get-all-reports-for-ranking\");\r\n        return response.data;\r\n    } catch (error) {\r\n        return error.response.data;\r\n    }\r\n}\r\n\r\n// Enhanced XP-based ranking endpoints\r\nexport const getEnhancedLeaderboard = async (level = 'all', limit = 1000) => {\r\n    try {\r\n        const response = await axiosInstance.get(`/api/quiz/enhanced-leaderboard?level=${level}&limit=${limit}`);\r\n        return response.data;\r\n    } catch (error) {\r\n        return error.response.data;\r\n    }\r\n}\r\n\r\nexport const getXPLeaderboard = async (options = {}) => {\r\n    try {\r\n        const params = new URLSearchParams();\r\n        if (options.limit) params.append('limit', options.limit);\r\n        if (options.classFilter) params.append('classFilter', options.classFilter);\r\n        if (options.levelFilter) params.append('levelFilter', options.levelFilter);\r\n        if (options.seasonFilter) params.append('seasonFilter', options.seasonFilter);\r\n        if (options.includeInactive) params.append('includeInactive', options.includeInactive);\r\n\r\n        const response = await axiosInstance.get(`/api/quiz/xp-leaderboard?${params.toString()}`);\r\n        return response.data;\r\n    } catch (error) {\r\n        return error.response.data;\r\n    }\r\n}\r\n\r\nexport const getUserRanking = async (userId, context = 5) => {\r\n    try {\r\n        const response = await axiosInstance.get(`/api/quiz/user-ranking/${userId}?context=${context}`);\r\n        return response.data;\r\n    } catch (error) {\r\n        return error.response.data;\r\n    }\r\n}\r\n\r\nexport const getClassRankings = async (className, limit = 50) => {\r\n    try {\r\n        const response = await axiosInstance.get(`/api/quiz/class-rankings/${className}?limit=${limit}`);\r\n        return response.data;\r\n    } catch (error) {\r\n        return error.response.data;\r\n    }\r\n}\r\n\r\n// XP Dashboard endpoints\r\nexport const getXPDashboard = async () => {\r\n    try {\r\n        const response = await axiosInstance.get(\"/api/xp-dashboard/dashboard\");\r\n        return response.data;\r\n    } catch (error) {\r\n        return error.response.data;\r\n    }\r\n}\r\n\r\nexport const getClassLeaderboard = async () => {\r\n    try {\r\n        const response = await axiosInstance.get(\"/api/xp-dashboard/class-leaderboard\");\r\n        return response.data;\r\n    } catch (error) {\r\n        return error.response.data;\r\n    }\r\n}\r\n", "import React, { useState, useEffect } from 'react'\r\n\r\nfunction PageTitle({ title }) {\r\n  const [isMobile, setIsMobile] = useState(false);\r\n  useEffect(() => {\r\n    if (window.innerWidth < 768) {\r\n      setIsMobile(true);\r\n    }\r\n  }, []);\r\n  return (\r\n    <div className='mt-2'>\r\n      <h1 className={isMobile ? 'text-lg' : ''}>{title}</h1>\r\n    </div>\r\n  )\r\n}\r\n\r\nexport default PageTitle", "import React from 'react';\nconst DescriptionsContext = /*#__PURE__*/React.createContext({});\nexport default DescriptionsContext;", "const DescriptionsItem = _ref => {\n  let {\n    children\n  } = _ref;\n  return children;\n};\nexport default DescriptionsItem;", "import classNames from 'classnames';\nimport * as React from 'react';\nfunction notEmpty(val) {\n  return val !== undefined && val !== null;\n}\nconst Cell = props => {\n  const {\n    itemPrefixCls,\n    component,\n    span,\n    className,\n    style,\n    labelStyle,\n    contentStyle,\n    bordered,\n    label,\n    content,\n    colon\n  } = props;\n  const Component = component;\n  if (bordered) {\n    return /*#__PURE__*/React.createElement(Component, {\n      className: classNames({\n        [`${itemPrefixCls}-item-label`]: notEmpty(label),\n        [`${itemPrefixCls}-item-content`]: notEmpty(content)\n      }, className),\n      style: style,\n      colSpan: span\n    }, notEmpty(label) && /*#__PURE__*/React.createElement(\"span\", {\n      style: labelStyle\n    }, label), notEmpty(content) && /*#__PURE__*/React.createElement(\"span\", {\n      style: contentStyle\n    }, content));\n  }\n  return /*#__PURE__*/React.createElement(Component, {\n    className: classNames(`${itemPrefixCls}-item`, className),\n    style: style,\n    colSpan: span\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: `${itemPrefixCls}-item-container`\n  }, (label || label === 0) && /*#__PURE__*/React.createElement(\"span\", {\n    className: classNames(`${itemPrefixCls}-item-label`, {\n      [`${itemPrefixCls}-item-no-colon`]: !colon\n    }),\n    style: labelStyle\n  }, label), (content || content === 0) && /*#__PURE__*/React.createElement(\"span\", {\n    className: classNames(`${itemPrefixCls}-item-content`),\n    style: contentStyle\n  }, content)));\n};\nexport default Cell;", "import * as React from 'react';\nimport Cell from './Cell';\nimport DescriptionsContext from './DescriptionsContext';\nfunction renderCells(items, _ref, _ref2) {\n  let {\n    colon,\n    prefixCls,\n    bordered\n  } = _ref;\n  let {\n    component,\n    type,\n    showLabel,\n    showContent,\n    labelStyle: rootLabelStyle,\n    contentStyle: rootContentStyle\n  } = _ref2;\n  return items.map((_ref3, index) => {\n    let {\n      label,\n      children,\n      prefixCls: itemPrefixCls = prefixCls,\n      className,\n      style,\n      labelStyle,\n      contentStyle,\n      span = 1,\n      key\n    } = _ref3;\n    if (typeof component === 'string') {\n      return /*#__PURE__*/React.createElement(Cell, {\n        key: `${type}-${key || index}`,\n        className: className,\n        style: style,\n        labelStyle: Object.assign(Object.assign({}, rootLabelStyle), labelStyle),\n        contentStyle: Object.assign(Object.assign({}, rootContentStyle), contentStyle),\n        span: span,\n        colon: colon,\n        component: component,\n        itemPrefixCls: itemPrefixCls,\n        bordered: bordered,\n        label: showLabel ? label : null,\n        content: showContent ? children : null\n      });\n    }\n    return [/*#__PURE__*/React.createElement(Cell, {\n      key: `label-${key || index}`,\n      className: className,\n      style: Object.assign(Object.assign(Object.assign({}, rootLabelStyle), style), labelStyle),\n      span: 1,\n      colon: colon,\n      component: component[0],\n      itemPrefixCls: itemPrefixCls,\n      bordered: bordered,\n      label: label\n    }), /*#__PURE__*/React.createElement(Cell, {\n      key: `content-${key || index}`,\n      className: className,\n      style: Object.assign(Object.assign(Object.assign({}, rootContentStyle), style), contentStyle),\n      span: span * 2 - 1,\n      component: component[1],\n      itemPrefixCls: itemPrefixCls,\n      bordered: bordered,\n      content: children\n    })];\n  });\n}\nconst Row = props => {\n  const descContext = React.useContext(DescriptionsContext);\n  const {\n    prefixCls,\n    vertical,\n    row,\n    index,\n    bordered\n  } = props;\n  if (vertical) {\n    return /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(\"tr\", {\n      key: `label-${index}`,\n      className: `${prefixCls}-row`\n    }, renderCells(row, props, Object.assign({\n      component: 'th',\n      type: 'label',\n      showLabel: true\n    }, descContext))), /*#__PURE__*/React.createElement(\"tr\", {\n      key: `content-${index}`,\n      className: `${prefixCls}-row`\n    }, renderCells(row, props, Object.assign({\n      component: 'td',\n      type: 'content',\n      showContent: true\n    }, descContext))));\n  }\n  return /*#__PURE__*/React.createElement(\"tr\", {\n    key: index,\n    className: `${prefixCls}-row`\n  }, renderCells(row, props, Object.assign({\n    component: bordered ? ['th', 'td'] : 'td',\n    type: 'item',\n    showLabel: true,\n    showContent: true\n  }, descContext)));\n};\nexport default Row;", "import toArray from \"rc-util/es/Children/toArray\";\nimport { useMemo } from 'react';\nimport warning from '../../_util/warning';\nfunction getFilledItem(rowItem, rowRestCol, span) {\n  let clone = rowItem;\n  if (span === undefined || span > rowRestCol) {\n    clone = Object.assign(Object.assign({}, rowItem), {\n      span: rowRestCol\n    });\n    process.env.NODE_ENV !== \"production\" ? warning(span === undefined, 'Descriptions', 'Sum of column `span` in a line not match `column` of Descriptions.') : void 0;\n  }\n  return clone;\n}\n// Convert children into items\nconst transChildren2Items = childNodes => toArray(childNodes).map(node => node === null || node === void 0 ? void 0 : node.props);\n// Calculate the sum of span in a row\nfunction getCalcRows(rowItems, mergedColumn) {\n  const rows = [];\n  let tmpRow = [];\n  let rowRestCol = mergedColumn;\n  rowItems.filter(n => n).forEach((rowItem, index) => {\n    const span = rowItem === null || rowItem === void 0 ? void 0 : rowItem.span;\n    const mergedSpan = span || 1;\n    // Additional handle last one\n    if (index === rowItems.length - 1) {\n      tmpRow.push(getFilledItem(rowItem, rowRestCol, span));\n      rows.push(tmpRow);\n      return;\n    }\n    if (mergedSpan < rowRestCol) {\n      rowRestCol -= mergedSpan;\n      tmpRow.push(rowItem);\n    } else {\n      tmpRow.push(getFilledItem(rowItem, rowRestCol, mergedSpan));\n      rows.push(tmpRow);\n      rowRestCol = mergedColumn;\n      tmpRow = [];\n    }\n  });\n  return rows;\n}\nconst useRow = (mergedColumn, items, children) => {\n  const rows = useMemo(() => {\n    if (Array.isArray(items)) {\n      return getCalcRows(items, mergedColumn);\n    }\n    return getCalcRows(transChildren2Items(children), mergedColumn);\n  }, [items, children, mergedColumn]);\n  return rows;\n};\nexport default useRow;", "import { resetComponent, textEllipsis } from '../../style';\nimport { genComponentStyleHook, mergeToken } from '../../theme/internal';\nconst genBorderedStyle = token => {\n  const {\n    componentCls,\n    labelBg\n  } = token;\n  return {\n    [`&${componentCls}-bordered`]: {\n      [`> ${componentCls}-view`]: {\n        border: `${token.lineWidth}px ${token.lineType} ${token.colorSplit}`,\n        '> table': {\n          tableLayout: 'auto',\n          borderCollapse: 'collapse'\n        },\n        [`${componentCls}-row`]: {\n          borderBottom: `${token.lineWidth}px ${token.lineType} ${token.colorSplit}`,\n          '&:last-child': {\n            borderBottom: 'none'\n          },\n          [`> ${componentCls}-item-label, > ${componentCls}-item-content`]: {\n            padding: `${token.padding}px ${token.paddingLG}px`,\n            borderInlineEnd: `${token.lineWidth}px ${token.lineType} ${token.colorSplit}`,\n            '&:last-child': {\n              borderInlineEnd: 'none'\n            }\n          },\n          [`> ${componentCls}-item-label`]: {\n            color: token.colorTextSecondary,\n            backgroundColor: labelBg,\n            '&::after': {\n              display: 'none'\n            }\n          }\n        }\n      },\n      [`&${componentCls}-middle`]: {\n        [`${componentCls}-row`]: {\n          [`> ${componentCls}-item-label, > ${componentCls}-item-content`]: {\n            padding: `${token.paddingSM}px ${token.paddingLG}px`\n          }\n        }\n      },\n      [`&${componentCls}-small`]: {\n        [`${componentCls}-row`]: {\n          [`> ${componentCls}-item-label, > ${componentCls}-item-content`]: {\n            padding: `${token.paddingXS}px ${token.padding}px`\n          }\n        }\n      }\n    }\n  };\n};\nconst genDescriptionStyles = token => {\n  const {\n    componentCls,\n    extraColor,\n    itemPaddingBottom,\n    colonMarginRight,\n    colonMarginLeft,\n    titleMarginBottom\n  } = token;\n  return {\n    [componentCls]: Object.assign(Object.assign(Object.assign({}, resetComponent(token)), genBorderedStyle(token)), {\n      [`&-rtl`]: {\n        direction: 'rtl'\n      },\n      [`${componentCls}-header`]: {\n        display: 'flex',\n        alignItems: 'center',\n        marginBottom: titleMarginBottom\n      },\n      [`${componentCls}-title`]: Object.assign(Object.assign({}, textEllipsis), {\n        flex: 'auto',\n        color: token.colorText,\n        fontWeight: token.fontWeightStrong,\n        fontSize: token.fontSizeLG,\n        lineHeight: token.lineHeightLG\n      }),\n      [`${componentCls}-extra`]: {\n        marginInlineStart: 'auto',\n        color: extraColor,\n        fontSize: token.fontSize\n      },\n      [`${componentCls}-view`]: {\n        width: '100%',\n        borderRadius: token.borderRadiusLG,\n        table: {\n          width: '100%',\n          tableLayout: 'fixed'\n        }\n      },\n      [`${componentCls}-row`]: {\n        '> th, > td': {\n          paddingBottom: itemPaddingBottom\n        },\n        '&:last-child': {\n          borderBottom: 'none'\n        }\n      },\n      [`${componentCls}-item-label`]: {\n        color: token.colorTextTertiary,\n        fontWeight: 'normal',\n        fontSize: token.fontSize,\n        lineHeight: token.lineHeight,\n        textAlign: `start`,\n        '&::after': {\n          content: '\":\"',\n          position: 'relative',\n          top: -0.5,\n          marginInline: `${colonMarginLeft}px ${colonMarginRight}px`\n        },\n        [`&${componentCls}-item-no-colon::after`]: {\n          content: '\"\"'\n        }\n      },\n      [`${componentCls}-item-no-label`]: {\n        '&::after': {\n          margin: 0,\n          content: '\"\"'\n        }\n      },\n      [`${componentCls}-item-content`]: {\n        display: 'table-cell',\n        flex: 1,\n        color: token.colorText,\n        fontSize: token.fontSize,\n        lineHeight: token.lineHeight,\n        wordBreak: 'break-word',\n        overflowWrap: 'break-word'\n      },\n      [`${componentCls}-item`]: {\n        paddingBottom: 0,\n        verticalAlign: 'top',\n        '&-container': {\n          display: 'flex',\n          [`${componentCls}-item-label`]: {\n            display: 'inline-flex',\n            alignItems: 'baseline'\n          },\n          [`${componentCls}-item-content`]: {\n            display: 'inline-flex',\n            alignItems: 'baseline'\n          }\n        }\n      },\n      '&-middle': {\n        [`${componentCls}-row`]: {\n          '> th, > td': {\n            paddingBottom: token.paddingSM\n          }\n        }\n      },\n      '&-small': {\n        [`${componentCls}-row`]: {\n          '> th, > td': {\n            paddingBottom: token.paddingXS\n          }\n        }\n      }\n    })\n  };\n};\n// ============================== Export ==============================\nexport default genComponentStyleHook('Descriptions', token => {\n  const descriptionToken = mergeToken(token, {});\n  return [genDescriptionStyles(descriptionToken)];\n}, token => ({\n  labelBg: token.colorFillAlter,\n  titleMarginBottom: token.fontSizeSM * token.lineHeightSM,\n  itemPaddingBottom: token.padding,\n  colonMarginRight: token.marginXS,\n  colonMarginLeft: token.marginXXS / 2,\n  extraColor: token.colorText\n}));", "'use client';\n\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\n/* eslint-disable react/no-array-index-key */\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport useResponsiveObserver, { responsiveArray } from '../_util/responsiveObserver';\nimport { ConfigContext } from '../config-provider';\nimport useSize from '../config-provider/hooks/useSize';\nimport DescriptionsContext from './DescriptionsContext';\nimport DescriptionsItem from './Item';\nimport Row from './Row';\nimport useRow from './hooks/useRow';\nimport useStyle from './style';\nconst DEFAULT_COLUMN_MAP = {\n  xxl: 3,\n  xl: 3,\n  lg: 3,\n  md: 3,\n  sm: 2,\n  xs: 1\n};\nfunction getColumn(column, screens) {\n  if (typeof column === 'number') {\n    return column;\n  }\n  if (typeof column === 'object') {\n    for (let i = 0; i < responsiveArray.length; i++) {\n      const breakpoint = responsiveArray[i];\n      if (screens[breakpoint] && column[breakpoint] !== undefined) {\n        return column[breakpoint] || DEFAULT_COLUMN_MAP[breakpoint];\n      }\n    }\n  }\n  return 3;\n}\nconst Descriptions = props => {\n  const {\n      prefixCls: customizePrefixCls,\n      title,\n      extra,\n      column = DEFAULT_COLUMN_MAP,\n      colon = true,\n      bordered,\n      layout,\n      children,\n      className,\n      rootClassName,\n      style,\n      size: customizeSize,\n      labelStyle,\n      contentStyle,\n      items\n    } = props,\n    restProps = __rest(props, [\"prefixCls\", \"title\", \"extra\", \"column\", \"colon\", \"bordered\", \"layout\", \"children\", \"className\", \"rootClassName\", \"style\", \"size\", \"labelStyle\", \"contentStyle\", \"items\"]);\n  const {\n    getPrefixCls,\n    direction,\n    descriptions\n  } = React.useContext(ConfigContext);\n  const prefixCls = getPrefixCls('descriptions', customizePrefixCls);\n  const [screens, setScreens] = React.useState({});\n  const mergedColumn = getColumn(column, screens);\n  const mergedSize = useSize(customizeSize);\n  const rows = useRow(mergedColumn, items, children);\n  const [wrapSSR, hashId] = useStyle(prefixCls);\n  const responsiveObserver = useResponsiveObserver();\n  // Responsive\n  React.useEffect(() => {\n    const token = responsiveObserver.subscribe(newScreens => {\n      if (typeof column !== 'object') {\n        return;\n      }\n      setScreens(newScreens);\n    });\n    return () => {\n      responsiveObserver.unsubscribe(token);\n    };\n  }, []);\n  // ======================== Render ========================\n  const contextValue = React.useMemo(() => ({\n    labelStyle,\n    contentStyle\n  }), [labelStyle, contentStyle]);\n  return wrapSSR( /*#__PURE__*/React.createElement(DescriptionsContext.Provider, {\n    value: contextValue\n  }, /*#__PURE__*/React.createElement(\"div\", Object.assign({\n    className: classNames(prefixCls, descriptions === null || descriptions === void 0 ? void 0 : descriptions.className, {\n      [`${prefixCls}-${mergedSize}`]: mergedSize && mergedSize !== 'default',\n      [`${prefixCls}-bordered`]: !!bordered,\n      [`${prefixCls}-rtl`]: direction === 'rtl'\n    }, className, rootClassName, hashId),\n    style: Object.assign(Object.assign({}, descriptions === null || descriptions === void 0 ? void 0 : descriptions.style), style)\n  }, restProps), (title || extra) && /*#__PURE__*/React.createElement(\"div\", {\n    className: `${prefixCls}-header`\n  }, title && /*#__PURE__*/React.createElement(\"div\", {\n    className: `${prefixCls}-title`\n  }, title), extra && /*#__PURE__*/React.createElement(\"div\", {\n    className: `${prefixCls}-extra`\n  }, extra)), /*#__PURE__*/React.createElement(\"div\", {\n    className: `${prefixCls}-view`\n  }, /*#__PURE__*/React.createElement(\"table\", null, /*#__PURE__*/React.createElement(\"tbody\", null, rows.map((row, index) => /*#__PURE__*/React.createElement(Row, {\n    key: index,\n    index: index,\n    colon: colon,\n    prefixCls: prefixCls,\n    vertical: layout === 'vertical',\n    bordered: bordered,\n    row: row\n  }))))))));\n};\nif (process.env.NODE_ENV !== 'production') {\n  Descriptions.displayName = 'Descriptions';\n}\nexport { DescriptionsContext };\nDescriptions.Item = DescriptionsItem;\nexport default Descriptions;", "import React, { useState, useEffect } from \"react\";\r\nimport './index.css';\r\nimport PageTitle from \"../../../components/PageTitle\";\r\nimport { message, Card, Progress, Statistic, Select, DatePicker, Button, Empty, Table, Tag, Modal, Descriptions } from \"antd\";\r\nimport { useDispatch } from \"react-redux\";\r\nimport { HideLoading, ShowLoading } from \"../../../redux/loaderSlice\";\r\nimport { getAllReportsByUser } from \"../../../apicalls/reports\";\r\nimport { motion } from \"framer-motion\";\r\nimport {\r\n  TbTrophy,\r\n  TbTarget,\r\n  TbTrendingUp,\r\n  TbCalendar,\r\n  TbClock,\r\n  TbAward,\r\n  TbChartBar,\r\n  TbDownload,\r\n  TbFilter,\r\n  TbEye,\r\n  TbCheck,\r\n  TbX,\r\n  TbFlame\r\n} from \"react-icons/tb\";\r\nimport moment from \"moment\";\r\n\r\nconst { Option } = Select;\r\nconst { RangePicker } = DatePicker;\r\n\r\nfunction UserReports() {\r\n  const [reportsData, setReportsData] = useState([]);\r\n  const [filteredData, setFilteredData] = useState([]);\r\n  const [filterSubject, setFilterSubject] = useState('all');\r\n  const [filterVerdict, setFilterVerdict] = useState('all');\r\n  const [dateRange, setDateRange] = useState(null);\r\n  const [viewMode, setViewMode] = useState('cards'); // 'cards' or 'table'\r\n  const [stats, setStats] = useState({\r\n    totalExams: 0,\r\n    passedExams: 0,\r\n    averageScore: 0,\r\n    streak: 0,\r\n    bestScore: 0\r\n  });\r\n  const [isMobile, setIsMobile] = useState(window.innerWidth < 768);\r\n  const [isTablet, setIsTablet] = useState(window.innerWidth >= 768 && window.innerWidth < 1024);\r\n  const [isModalVisible, setIsModalVisible] = useState(false);\r\n  const [selectedReport, setSelectedReport] = useState(null);\r\n  const dispatch = useDispatch();\r\n\r\n  const calculateStats = (data) => {\r\n    if (!data || data.length === 0) {\r\n      setStats({\r\n        totalExams: 0,\r\n        passedExams: 0,\r\n        averageScore: 0,\r\n        streak: 0,\r\n        bestScore: 0\r\n      });\r\n      return;\r\n    }\r\n\r\n    const totalExams = data.length;\r\n    const passedExams = data.filter(report => report.result?.verdict === 'Pass').length;\r\n    const scores = data.map(report => {\r\n      const obtained = report.result?.correctAnswers?.length || 0;\r\n      const total = report.exam?.totalMarks || 1;\r\n      return (obtained / total) * 100;\r\n    });\r\n\r\n    const averageScore = scores.reduce((sum, score) => sum + score, 0) / totalExams;\r\n    const bestScore = Math.max(...scores);\r\n\r\n    // Calculate streak (consecutive passes)\r\n    let currentStreak = 0;\r\n    let maxStreak = 0;\r\n    for (let i = data.length - 1; i >= 0; i--) {\r\n      if (data[i].result?.verdict === 'Pass') {\r\n        currentStreak++;\r\n        maxStreak = Math.max(maxStreak, currentStreak);\r\n      } else {\r\n        currentStreak = 0;\r\n      }\r\n    }\r\n\r\n    setStats({\r\n      totalExams,\r\n      passedExams,\r\n      averageScore: Math.round(averageScore),\r\n      streak: maxStreak,\r\n      bestScore: Math.round(bestScore)\r\n    });\r\n  };\r\n\r\n  const getData = async () => {\r\n    try {\r\n      dispatch(ShowLoading());\r\n      const response = await getAllReportsByUser();\r\n      if (response.success) {\r\n        setReportsData(response.data);\r\n        setFilteredData(response.data);\r\n        calculateStats(response.data);\r\n      } else {\r\n        message.error(response.message);\r\n      }\r\n      dispatch(HideLoading());\r\n    } catch (error) {\r\n      dispatch(HideLoading());\r\n      message.error(error.message);\r\n    }\r\n  };\r\n\r\n  const applyFilters = () => {\r\n    let filtered = [...reportsData];\r\n\r\n    if (filterSubject !== 'all') {\r\n      filtered = filtered.filter(report =>\r\n        report.exam?.subject?.toLowerCase().includes(filterSubject.toLowerCase())\r\n      );\r\n    }\r\n\r\n    if (filterVerdict !== 'all') {\r\n      filtered = filtered.filter(report => report.result?.verdict === filterVerdict);\r\n    }\r\n\r\n    if (dateRange && dateRange.length === 2) {\r\n      filtered = filtered.filter(report => {\r\n        const reportDate = moment(report.createdAt);\r\n        return reportDate.isBetween(dateRange[0], dateRange[1], 'day', '[]');\r\n      });\r\n    }\r\n\r\n    setFilteredData(filtered);\r\n    calculateStats(filtered);\r\n  };\r\n\r\n  useEffect(() => {\r\n    getData();\r\n  }, []);\r\n\r\n  useEffect(() => {\r\n    applyFilters();\r\n  }, [filterSubject, filterVerdict, dateRange, reportsData]);\r\n\r\n  useEffect(() => {\r\n    const handleResize = () => {\r\n      setIsMobile(window.innerWidth < 768);\r\n      setIsTablet(window.innerWidth >= 768 && window.innerWidth < 1024);\r\n    };\r\n\r\n    window.addEventListener('resize', handleResize);\r\n    return () => window.removeEventListener('resize', handleResize);\r\n  }, []);\r\n\r\n  const getScoreColor = (score) => {\r\n    if (score >= 80) return 'text-green-600';\r\n    if (score >= 60) return 'text-blue-600';\r\n    if (score >= 40) return 'text-orange-600';\r\n    return 'text-red-600';\r\n  };\r\n\r\n  const getVerdictIcon = (verdict) => {\r\n    return verdict === 'Pass' ?\r\n      <TbCheck className=\"w-5 h-5 text-green-600\" /> :\r\n      <TbX className=\"w-5 h-5 text-red-600\" />;\r\n  };\r\n\r\n  const getUniqueSubjects = () => {\r\n    const subjects = reportsData.map(report => report.exam?.subject).filter(Boolean);\r\n    return [...new Set(subjects)];\r\n  };\r\n\r\n  const handleViewDetails = (record) => {\r\n    setSelectedReport(record);\r\n    setIsModalVisible(true);\r\n  };\r\n\r\n  const handleCloseModal = () => {\r\n    setIsModalVisible(false);\r\n    setSelectedReport(null);\r\n  };\r\n\r\n  const getResponsiveColumns = () => {\r\n    const isMobile = window.innerWidth < 768;\r\n    const isTablet = window.innerWidth >= 768 && window.innerWidth < 1024;\r\n\r\n    const baseColumns = [\r\n      {\r\n        title: 'Exam',\r\n        dataIndex: 'examName',\r\n        key: 'examName',\r\n        render: (text, record) => (\r\n          <div className=\"min-w-0\">\r\n            <div className=\"font-semibold text-gray-900 text-sm sm:text-base truncate\">\r\n              {record.exam?.name || 'Unnamed Exam'}\r\n            </div>\r\n            <div className=\"text-xs sm:text-sm text-gray-500 truncate\">\r\n              {record.exam?.subject || 'General'}\r\n            </div>\r\n            {isMobile && (\r\n              <div className=\"flex items-center justify-between mt-2\">\r\n                <div className=\"text-xs text-gray-400\">\r\n                  {moment(record.createdAt).format(\"MMM DD, YYYY\")}\r\n                </div>\r\n                <Button\r\n                  type=\"link\"\r\n                  size=\"small\"\r\n                  icon={<TbEye />}\r\n                  onClick={() => handleViewDetails(record)}\r\n                  className=\"text-blue-500 p-0 h-auto\"\r\n                >\r\n                  View\r\n                </Button>\r\n              </div>\r\n            )}\r\n          </div>\r\n        ),\r\n        width: isMobile ? 180 : isTablet ? 200 : 250,\r\n        ellipsis: true,\r\n      },\r\n      {\r\n        title: 'Score',\r\n        dataIndex: 'score',\r\n        key: 'score',\r\n        render: (text, record) => {\r\n          const obtained = record.result?.correctAnswers?.length || 0;\r\n          const total = record.exam?.totalMarks || 1;\r\n          const percentage = Math.round((obtained / total) * 100);\r\n\r\n          return (\r\n            <div className=\"text-center\">\r\n              <div className=\"text-sm sm:text-base font-bold text-gray-900\">\r\n                {obtained}/{total}\r\n              </div>\r\n              <Progress\r\n                percent={percentage}\r\n                size=\"small\"\r\n                strokeColor={percentage >= 60 ? '#10b981' : '#ef4444'}\r\n                showInfo={false}\r\n                className=\"mb-1\"\r\n              />\r\n              <div className={`text-xs sm:text-sm font-medium ${getScoreColor(percentage)}`}>\r\n                {percentage}%\r\n              </div>\r\n            </div>\r\n          );\r\n        },\r\n        width: isMobile ? 80 : 120,\r\n        sorter: (a, b) => {\r\n          const scoreA = Math.round(((a.result?.correctAnswers?.length || 0) / (a.exam?.totalMarks || 1)) * 100);\r\n          const scoreB = Math.round(((b.result?.correctAnswers?.length || 0) / (b.exam?.totalMarks || 1)) * 100);\r\n          return scoreA - scoreB;\r\n        },\r\n      },\r\n      {\r\n        title: 'Result',\r\n        dataIndex: 'verdict',\r\n        key: 'verdict',\r\n        render: (text, record) => {\r\n          const verdict = record.result?.verdict;\r\n          const isPassed = verdict === 'Pass';\r\n\r\n          return (\r\n            <Tag\r\n              icon={!isMobile ? getVerdictIcon(verdict) : null}\r\n              color={isPassed ? 'success' : 'error'}\r\n              className=\"font-medium text-xs sm:text-sm\"\r\n            >\r\n              {isMobile ? (isPassed ? 'P' : 'F') : (verdict || 'N/A')}\r\n            </Tag>\r\n          );\r\n        },\r\n        width: isMobile ? 50 : 100,\r\n        filters: !isMobile ? [\r\n          { text: 'Pass', value: 'Pass' },\r\n          { text: 'Fail', value: 'Fail' },\r\n        ] : undefined,\r\n        onFilter: !isMobile ? (value, record) => record.result?.verdict === value : undefined,\r\n      },\r\n    ];\r\n\r\n    // Add date column for tablet and desktop\r\n    if (!isMobile) {\r\n      baseColumns.splice(1, 0, {\r\n        title: 'Date',\r\n        dataIndex: 'createdAt',\r\n        key: 'date',\r\n        render: (date) => (\r\n          <div className=\"text-sm\">\r\n            <div className=\"font-medium\">{moment(date).format(\"MMM DD, YYYY\")}</div>\r\n            <div className=\"text-gray-500\">{moment(date).format(\"HH:mm\")}</div>\r\n          </div>\r\n        ),\r\n        width: isTablet ? 100 : 120,\r\n      });\r\n    }\r\n\r\n    // Add actions column for desktop\r\n    if (!isMobile) {\r\n      baseColumns.push({\r\n        title: 'Actions',\r\n        key: 'actions',\r\n        render: (text, record) => (\r\n          <Button\r\n            type=\"primary\"\r\n            size=\"small\"\r\n            icon={<TbEye />}\r\n            onClick={() => handleViewDetails(record)}\r\n            className=\"bg-blue-500 hover:bg-blue-600\"\r\n          >\r\n            {isTablet ? '' : 'View'}\r\n          </Button>\r\n        ),\r\n        width: isTablet ? 60 : 80,\r\n      });\r\n    }\r\n\r\n    return baseColumns;\r\n  };\r\n\r\n  const columns = getResponsiveColumns();\r\n\r\n  return (\r\n    <div className=\"min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50\">\r\n      <PageTitle title=\"Performance Reports\" />\r\n\r\n      <div className=\"max-w-7xl mx-auto px-3 sm:px-4 md:px-6 lg:px-8 py-4 sm:py-6 lg:py-8\">\r\n        {/* Header Section */}\r\n        <motion.div\r\n          initial={{ opacity: 0, y: 20 }}\r\n          animate={{ opacity: 1, y: 0 }}\r\n          className=\"text-center mb-8 sm:mb-10 lg:mb-12\"\r\n        >\r\n          <div className=\"inline-flex items-center justify-center w-12 h-12 sm:w-14 sm:h-14 lg:w-16 lg:h-16 bg-gradient-to-r from-blue-600 to-indigo-600 rounded-xl sm:rounded-2xl mb-4 sm:mb-6 shadow-lg\">\r\n            <TbChartBar className=\"w-6 h-6 sm:w-7 sm:h-7 lg:w-8 lg:h-8 text-white\" />\r\n          </div>\r\n          <h1 className=\"text-2xl sm:text-3xl lg:text-4xl font-bold text-gray-900 mb-3 sm:mb-4 px-4\">\r\n            Your <span className=\"text-transparent bg-clip-text bg-gradient-to-r from-blue-600 to-indigo-600\">Performance</span> Journey\r\n          </h1>\r\n          <p className=\"text-sm sm:text-base lg:text-lg text-gray-600 max-w-xl sm:max-w-2xl mx-auto px-4\">\r\n            Track your progress, analyze your performance, and celebrate your achievements\r\n          </p>\r\n        </motion.div>\r\n\r\n        {/* Stats Cards */}\r\n        <motion.div\r\n          initial={{ opacity: 0, y: 20 }}\r\n          animate={{ opacity: 1, y: 0 }}\r\n          transition={{ delay: 0.1 }}\r\n          className=\"grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-5 gap-3 sm:gap-4 lg:gap-6 mb-6 sm:mb-8\"\r\n        >\r\n          <Card className=\"text-center hover:shadow-lg transition-all duration-300 border-0 bg-gradient-to-br from-blue-50 to-blue-100\">\r\n            <div className=\"flex flex-col items-center p-2 sm:p-4\">\r\n              <div className=\"w-8 h-8 sm:w-10 sm:h-10 lg:w-12 lg:h-12 bg-blue-500 rounded-full flex items-center justify-center mb-2 sm:mb-3\">\r\n                <TbTarget className=\"w-4 h-4 sm:w-5 sm:h-5 lg:w-6 lg:h-6 text-white\" />\r\n              </div>\r\n              <Statistic\r\n                title=\"Total Exams\"\r\n                value={stats.totalExams}\r\n                valueStyle={{\r\n                  color: '#1e40af',\r\n                  fontWeight: 'bold'\r\n                }}\r\n                className=\"responsive-statistic\"\r\n              />\r\n            </div>\r\n          </Card>\r\n\r\n          <Card className=\"text-center hover:shadow-lg transition-all duration-300 border-0 bg-gradient-to-br from-green-50 to-green-100\">\r\n            <div className=\"flex flex-col items-center p-2 sm:p-4\">\r\n              <div className=\"w-8 h-8 sm:w-10 sm:h-10 lg:w-12 lg:h-12 bg-green-500 rounded-full flex items-center justify-center mb-2 sm:mb-3\">\r\n                <TbCheck className=\"w-4 h-4 sm:w-5 sm:h-5 lg:w-6 lg:h-6 text-white\" />\r\n              </div>\r\n              <Statistic\r\n                title=\"Passed\"\r\n                value={stats.passedExams}\r\n                valueStyle={{\r\n                  color: '#059669',\r\n                  fontWeight: 'bold'\r\n                }}\r\n                className=\"responsive-statistic\"\r\n              />\r\n            </div>\r\n          </Card>\r\n\r\n          <Card className=\"text-center hover:shadow-lg transition-all duration-300 border-0 bg-gradient-to-br from-purple-50 to-purple-100\">\r\n            <div className=\"flex flex-col items-center p-2 sm:p-4\">\r\n              <div className=\"w-8 h-8 sm:w-10 sm:h-10 lg:w-12 lg:h-12 bg-purple-500 rounded-full flex items-center justify-center mb-2 sm:mb-3\">\r\n                <TbTrendingUp className=\"w-4 h-4 sm:w-5 sm:h-5 lg:w-6 lg:h-6 text-white\" />\r\n              </div>\r\n              <Statistic\r\n                title=\"Average Score\"\r\n                value={stats.averageScore}\r\n                suffix=\"%\"\r\n                valueStyle={{\r\n                  color: '#7c3aed',\r\n                  fontWeight: 'bold'\r\n                }}\r\n                className=\"responsive-statistic\"\r\n              />\r\n            </div>\r\n          </Card>\r\n\r\n          <Card className=\"text-center hover:shadow-lg transition-all duration-300 border-0 bg-gradient-to-br from-orange-50 to-orange-100\">\r\n            <div className=\"flex flex-col items-center p-2 sm:p-4\">\r\n              <div className=\"w-8 h-8 sm:w-10 sm:h-10 lg:w-12 lg:h-12 bg-orange-500 rounded-full flex items-center justify-center mb-2 sm:mb-3\">\r\n                <TbTrophy className=\"w-4 h-4 sm:w-5 sm:h-5 lg:w-6 lg:h-6 text-white\" />\r\n              </div>\r\n              <Statistic\r\n                title=\"Best Score\"\r\n                value={stats.bestScore}\r\n                suffix=\"%\"\r\n                valueStyle={{\r\n                  color: '#ea580c',\r\n                  fontWeight: 'bold'\r\n                }}\r\n                className=\"responsive-statistic\"\r\n              />\r\n            </div>\r\n          </Card>\r\n\r\n          <Card className=\"text-center hover:shadow-lg transition-all duration-300 border-0 bg-gradient-to-br from-pink-50 to-pink-100 sm:col-span-3 lg:col-span-1\">\r\n            <div className=\"flex flex-col items-center p-2 sm:p-4\">\r\n              <div className=\"w-8 h-8 sm:w-10 sm:h-10 lg:w-12 lg:h-12 bg-pink-500 rounded-full flex items-center justify-center mb-2 sm:mb-3\">\r\n                <TbFlame className=\"w-4 h-4 sm:w-5 sm:h-5 lg:w-6 lg:h-6 text-white\" />\r\n              </div>\r\n              <Statistic\r\n                title=\"Best Streak\"\r\n                value={stats.streak}\r\n                valueStyle={{\r\n                  color: '#db2777',\r\n                  fontWeight: 'bold'\r\n                }}\r\n                className=\"responsive-statistic\"\r\n              />\r\n            </div>\r\n          </Card>\r\n\r\n\r\n        </motion.div>\r\n\r\n        {/* Filters Section */}\r\n        <motion.div\r\n          initial={{ opacity: 0, y: 20 }}\r\n          animate={{ opacity: 1, y: 0 }}\r\n          transition={{ delay: 0.2 }}\r\n          className=\"bg-white rounded-xl sm:rounded-2xl shadow-lg p-4 sm:p-6 mb-6 sm:mb-8 border border-gray-100\"\r\n        >\r\n          <div className=\"flex flex-col gap-4\">\r\n            <div className=\"flex items-center gap-2\">\r\n              <TbFilter className=\"w-5 h-5 text-gray-600\" />\r\n              <h3 className=\"text-lg font-semibold text-gray-900\">Filter Results</h3>\r\n            </div>\r\n\r\n            {/* Filter Controls */}\r\n            <div className=\"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4\">\r\n              {/* Subject Filter */}\r\n              <div className=\"space-y-2\">\r\n                <label className=\"text-sm font-medium text-gray-700\">Subject</label>\r\n                <Select\r\n                  placeholder=\"All Subjects\"\r\n                  value={filterSubject}\r\n                  onChange={setFilterSubject}\r\n                  className=\"w-full\"\r\n                  size=\"large\"\r\n                >\r\n                  <Option value=\"all\">All Subjects</Option>\r\n                  {getUniqueSubjects().map(subject => (\r\n                    <Option key={subject} value={subject}>{subject}</Option>\r\n                  ))}\r\n                </Select>\r\n              </div>\r\n\r\n              {/* Result Filter */}\r\n              <div className=\"space-y-2\">\r\n                <label className=\"text-sm font-medium text-gray-700\">Result</label>\r\n                <Select\r\n                  placeholder=\"All Results\"\r\n                  value={filterVerdict}\r\n                  onChange={setFilterVerdict}\r\n                  className=\"w-full\"\r\n                  size=\"large\"\r\n                >\r\n                  <Option value=\"all\">All Results</Option>\r\n                  <Option value=\"Pass\">Passed</Option>\r\n                  <Option value=\"Fail\">Failed</Option>\r\n                </Select>\r\n              </div>\r\n\r\n              {/* Date Range Filter */}\r\n              <div className=\"space-y-2 sm:col-span-2 lg:col-span-1\">\r\n                <label className=\"text-sm font-medium text-gray-700\">Date Range</label>\r\n                <RangePicker\r\n                  value={dateRange}\r\n                  onChange={setDateRange}\r\n                  className=\"w-full\"\r\n                  size=\"large\"\r\n                  placeholder={['From', 'To']}\r\n                  format=\"DD/MM/YYYY\"\r\n                  allowClear\r\n                />\r\n              </div>\r\n\r\n              {/* Clear Button */}\r\n              <div className=\"space-y-2\">\r\n                <label className=\"text-sm font-medium text-gray-700 opacity-0\">Actions</label>\r\n                <Button\r\n                  onClick={() => {\r\n                    setFilterSubject('all');\r\n                    setFilterVerdict('all');\r\n                    setDateRange(null);\r\n                  }}\r\n                  size=\"large\"\r\n                  className=\"w-full\"\r\n                  icon={<TbX />}\r\n                >\r\n                  Clear All\r\n                </Button>\r\n              </div>\r\n            </div>\r\n\r\n            {/* Active Filters Display */}\r\n            {(filterSubject !== 'all' || filterVerdict !== 'all' || dateRange) && (\r\n              <div className=\"flex flex-wrap gap-2 pt-3 border-t border-gray-100\">\r\n                <span className=\"text-sm font-medium text-gray-700\">Active filters:</span>\r\n                {filterSubject !== 'all' && (\r\n                  <Tag\r\n                    closable\r\n                    onClose={() => setFilterSubject('all')}\r\n                    className=\"bg-blue-50 border-blue-200 text-blue-700\"\r\n                  >\r\n                    {filterSubject}\r\n                  </Tag>\r\n                )}\r\n                {filterVerdict !== 'all' && (\r\n                  <Tag\r\n                    closable\r\n                    onClose={() => setFilterVerdict('all')}\r\n                    className={filterVerdict === 'Pass' ? 'bg-green-50 border-green-200 text-green-700' : 'bg-red-50 border-red-200 text-red-700'}\r\n                  >\r\n                    {filterVerdict}\r\n                  </Tag>\r\n                )}\r\n                {dateRange && (\r\n                  <Tag\r\n                    closable\r\n                    onClose={() => setDateRange(null)}\r\n                    className=\"bg-purple-50 border-purple-200 text-purple-700\"\r\n                  >\r\n                    {dateRange[0].format('DD/MM/YY')} - {dateRange[1].format('DD/MM/YY')}\r\n                  </Tag>\r\n                )}\r\n              </div>\r\n            )}\r\n          </div>\r\n        </motion.div>\r\n\r\n        {/* Exam Results Table */}\r\n        <motion.div\r\n          initial={{ opacity: 0, y: 20 }}\r\n          animate={{ opacity: 1, y: 0 }}\r\n          transition={{ delay: 0.3 }}\r\n          className=\"bg-white rounded-xl sm:rounded-2xl shadow-lg overflow-hidden border border-gray-100\"\r\n        >\r\n          <Table\r\n            columns={columns}\r\n            dataSource={filteredData}\r\n            rowKey={(record) => record._id}\r\n            pagination={{\r\n              pageSize: window.innerWidth < 768 ? 5 : 10,\r\n              showSizeChanger: window.innerWidth >= 768,\r\n              showQuickJumper: window.innerWidth >= 768,\r\n              showTotal: (total, range) =>\r\n                window.innerWidth >= 640\r\n                  ? `${range[0]}-${range[1]} of ${total} results`\r\n                  : `${range[0]}-${range[1]} / ${total}`,\r\n              className: \"px-3 sm:px-6 py-2 sm:py-4\",\r\n              simple: window.innerWidth < 640\r\n            }}\r\n            scroll={{ x: window.innerWidth < 768 ? 600 : 800 }}\r\n            className=\"modern-table\"\r\n            size={window.innerWidth < 768 ? \"middle\" : \"large\"}\r\n            locale={{\r\n              emptyText: (\r\n                <Empty\r\n                  image={Empty.PRESENTED_IMAGE_SIMPLE}\r\n                  description={\r\n                    <div className=\"py-8\">\r\n                      <h3 className=\"text-base sm:text-lg font-medium text-gray-900 mb-2\">No exam results found</h3>\r\n                      <p className=\"text-sm sm:text-base text-gray-500 px-4\">Try adjusting your filters or take some exams to see your results here.</p>\r\n                    </div>\r\n                  }\r\n                />\r\n              )\r\n            }}\r\n          />\r\n        </motion.div>\r\n\r\n        {/* Details Modal */}\r\n        <Modal\r\n          title={\r\n            <div className=\"flex items-center gap-3\">\r\n              <div className=\"w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center\">\r\n                <TbEye className=\"w-4 h-4 text-white\" />\r\n              </div>\r\n              <span className=\"text-lg font-semibold\">Exam Details</span>\r\n            </div>\r\n          }\r\n          open={isModalVisible}\r\n          onCancel={handleCloseModal}\r\n          footer={[\r\n            <Button key=\"close\" onClick={handleCloseModal} size=\"large\">\r\n              Close\r\n            </Button>\r\n          ]}\r\n          width={isMobile ? '95%' : isTablet ? 600 : 700}\r\n          className=\"exam-details-modal\"\r\n        >\r\n          {selectedReport && (\r\n            <div className=\"space-y-6\">\r\n              {/* Exam Information */}\r\n              <div className=\"bg-gray-50 rounded-lg p-4\">\r\n                <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">Exam Information</h3>\r\n                <Descriptions column={isMobile ? 1 : 2} size=\"small\">\r\n                  <Descriptions.Item label=\"Exam Name\" span={isMobile ? 1 : 2}>\r\n                    <span className=\"font-medium\">{selectedReport.exam?.name || 'N/A'}</span>\r\n                  </Descriptions.Item>\r\n                  <Descriptions.Item label=\"Subject\">\r\n                    {selectedReport.exam?.subject || 'General'}\r\n                  </Descriptions.Item>\r\n                  <Descriptions.Item label=\"Date Taken\">\r\n                    {moment(selectedReport.createdAt).format(\"MMMM DD, YYYY [at] HH:mm\")}\r\n                  </Descriptions.Item>\r\n                  <Descriptions.Item label=\"Total Questions\">\r\n                    {selectedReport.exam?.totalMarks || 0}\r\n                  </Descriptions.Item>\r\n                  <Descriptions.Item label=\"Passing Marks\">\r\n                    {selectedReport.exam?.passingMarks || 0}\r\n                  </Descriptions.Item>\r\n                </Descriptions>\r\n              </div>\r\n\r\n              {/* Performance Summary */}\r\n              <div className=\"bg-blue-50 rounded-lg p-4\">\r\n                <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">Performance Summary</h3>\r\n                <div className=\"grid grid-cols-1 sm:grid-cols-3 gap-4\">\r\n                  <div className=\"text-center p-3 bg-white rounded-lg\">\r\n                    <div className=\"text-2xl font-bold text-blue-600\">\r\n                      {selectedReport.result?.correctAnswers?.length || 0}\r\n                    </div>\r\n                    <div className=\"text-sm text-gray-600\">Correct Answers</div>\r\n                  </div>\r\n                  <div className=\"text-center p-3 bg-white rounded-lg\">\r\n                    <div className=\"text-2xl font-bold text-gray-600\">\r\n                      {(selectedReport.exam?.totalMarks || 0) - (selectedReport.result?.correctAnswers?.length || 0)}\r\n                    </div>\r\n                    <div className=\"text-sm text-gray-600\">Wrong Answers</div>\r\n                  </div>\r\n                  <div className=\"text-center p-3 bg-white rounded-lg\">\r\n                    <div className={`text-2xl font-bold ${\r\n                      Math.round(((selectedReport.result?.correctAnswers?.length || 0) / (selectedReport.exam?.totalMarks || 1)) * 100) >= 60\r\n                        ? 'text-green-600'\r\n                        : 'text-red-600'\r\n                    }`}>\r\n                      {Math.round(((selectedReport.result?.correctAnswers?.length || 0) / (selectedReport.exam?.totalMarks || 1)) * 100)}%\r\n                    </div>\r\n                    <div className=\"text-sm text-gray-600\">Score</div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n\r\n              {/* Result Status */}\r\n              <div className={`rounded-lg p-4 ${\r\n                selectedReport.result?.verdict === 'Pass'\r\n                  ? 'bg-green-50 border border-green-200'\r\n                  : 'bg-red-50 border border-red-200'\r\n              }`}>\r\n                <div className=\"flex items-center justify-center gap-3\">\r\n                  {getVerdictIcon(selectedReport.result?.verdict)}\r\n                  <span className={`text-xl font-semibold ${\r\n                    selectedReport.result?.verdict === 'Pass' ? 'text-green-700' : 'text-red-700'\r\n                  }`}>\r\n                    {selectedReport.result?.verdict === 'Pass' ? 'Congratulations! You Passed' : 'Keep Trying! You Can Do Better'}\r\n                  </span>\r\n                </div>\r\n                <div className=\"text-center mt-2\">\r\n                  <span className=\"text-sm text-gray-600\">\r\n                    {selectedReport.result?.verdict === 'Pass'\r\n                      ? 'Great job on passing this exam!'\r\n                      : 'Review the material and try again to improve your score.'}\r\n                  </span>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          )}\r\n        </Modal>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n\r\nexport default UserReports;\r\n"], "names": ["default", "axiosInstance", "require", "addReport", "async", "post", "payload", "data", "error", "response", "getAllReports", "filters", "getAllReportsByUser", "getAllReportsForRanking", "get", "getXPLeaderboard", "options", "arguments", "length", "undefined", "params", "URLSearchParams", "limit", "append", "classFilter", "levelFilter", "seasonFilter", "includeInactive", "concat", "toString", "getUserRanking", "userId", "context", "_ref", "title", "isMobile", "setIsMobile", "useState", "useEffect", "window", "innerWidth", "_jsx", "className", "children", "React", "notEmpty", "val", "props", "itemPrefixCls", "component", "span", "style", "labelStyle", "contentStyle", "bordered", "label", "content", "colon", "Component", "classNames", "colSpan", "renderCells", "items", "_ref2", "prefixCls", "type", "showLabel", "showContent", "rootLabelStyle", "rootContentStyle", "map", "_ref3", "index", "key", "Cell", "Object", "assign", "descContext", "DescriptionsContext", "vertical", "row", "getFilledItem", "rowItem", "rowRestCol", "clone", "getCalcRows", "rowItems", "mergedColumn", "rows", "tmpRow", "filter", "n", "for<PERSON>ach", "mergedSpan", "push", "useRow", "useMemo", "Array", "isArray", "childNodes", "toArray", "node", "genBorderedStyle", "token", "componentCls", "labelBg", "border", "lineWidth", "lineType", "colorSplit", "tableLayout", "borderCollapse", "borderBottom", "padding", "paddingLG", "borderInlineEnd", "color", "colorTextSecondary", "backgroundColor", "display", "paddingSM", "paddingXS", "genDescriptionStyles", "extraColor", "itemPaddingBottom", "colonMarginRight", "colonMarginLeft", "titleMarginBottom", "resetComponent", "direction", "alignItems", "marginBottom", "textEllipsis", "flex", "colorText", "fontWeight", "fontWeightStrong", "fontSize", "fontSizeLG", "lineHeight", "lineHeightLG", "marginInlineStart", "width", "borderRadius", "borderRadiusLG", "table", "paddingBottom", "colorTextTertiary", "textAlign", "position", "top", "marginInline", "margin", "wordBreak", "overflowWrap", "verticalAlign", "genComponentStyleHook", "descriptionToken", "mergeToken", "colorFillAlter", "fontSizeSM", "lineHeightSM", "marginXS", "marginXXS", "__rest", "s", "e", "t", "p", "prototype", "hasOwnProperty", "call", "indexOf", "getOwnPropertySymbols", "i", "propertyIsEnumerable", "DEFAULT_COLUMN_MAP", "xxl", "xl", "lg", "md", "sm", "xs", "Descriptions", "customizePrefixCls", "extra", "column", "layout", "rootClassName", "size", "customizeSize", "restProps", "getPrefixCls", "descriptions", "ConfigContext", "screens", "setScreens", "responsiveArray", "breakpoint", "getColumn", "mergedSize", "useSize", "wrapSSR", "hashId", "useStyle", "responsiveObserver", "useResponsiveObserver", "subscribe", "newScreens", "unsubscribe", "contextValue", "Provider", "value", "Row", "<PERSON><PERSON>", "DescriptionsItem", "Option", "Select", "RangePicker", "DatePicker", "_selectedReport$exam", "_selectedReport$exam2", "_selectedReport$exam3", "_selectedReport$exam4", "_selectedReport$resul", "_selectedReport$resul2", "_selectedReport$exam5", "_selectedReport$resul3", "_selectedReport$resul4", "_selectedReport$resul5", "_selectedReport$resul6", "_selectedReport$exam6", "_selectedReport$resul7", "_selectedReport$resul8", "_selectedReport$exam7", "_selectedReport$resul9", "_selectedReport$resul10", "_selectedReport$resul11", "_selectedReport$resul12", "_selectedReport$resul13", "reportsData", "setReportsData", "filteredData", "setFilteredData", "filterSubject", "setFilterSubject", "filterVerdict", "setFilterVerdict", "date<PERSON><PERSON><PERSON>", "setDateRange", "viewMode", "setViewMode", "stats", "setStats", "totalExams", "passedExams", "averageScore", "streak", "bestScore", "isTablet", "setIsTablet", "isModalVisible", "setIsModalVisible", "selectedReport", "setSelectedReport", "dispatch", "useDispatch", "calculateStats", "report", "_report$result", "result", "verdict", "scores", "_report$result2", "_report$result2$corre", "_report$exam", "correctAnswers", "exam", "totalMarks", "reduce", "sum", "score", "Math", "max", "currentStreak", "maxStreak", "_data$i$result", "round", "ShowLoading", "success", "message", "HideLoading", "getData", "applyFilters", "filtered", "_report$exam2", "_report$exam2$subject", "subject", "toLowerCase", "includes", "_report$result3", "moment", "createdAt", "isBetween", "handleResize", "addEventListener", "removeEventListener", "getVerdictIcon", "TbCheck", "TbX", "handleViewDetails", "record", "handleCloseModal", "columns", "getResponsiveColumns", "baseColumns", "dataIndex", "render", "text", "_record$exam", "_record$exam2", "_jsxs", "name", "format", "<PERSON><PERSON>", "icon", "TbEye", "onClick", "ellipsis", "_record$result", "_record$result$correc", "_record$exam3", "obtained", "total", "percentage", "Progress", "percent", "strokeColor", "showInfo", "sorter", "a", "b", "_a$result", "_a$result$correctAnsw", "_a$exam", "_b$result", "_b$result$correctAnsw", "_b$exam", "_record$result2", "isPassed", "Tag", "onFilter", "_record$result3", "splice", "date", "Page<PERSON><PERSON>le", "motion", "div", "initial", "opacity", "y", "animate", "TbChartBar", "transition", "delay", "Card", "TbTarget", "Statistic", "valueStyle", "TbTrendingUp", "suffix", "TbTrophy", "TbFlame", "Tb<PERSON><PERSON>er", "placeholder", "onChange", "getUniqueSubjects", "subjects", "_report$exam3", "Boolean", "Set", "allowClear", "closable", "onClose", "Table", "dataSource", "<PERSON><PERSON><PERSON>", "_id", "pagination", "pageSize", "showSizeChanger", "showQuickJumper", "showTotal", "range", "simple", "scroll", "x", "locale", "emptyText", "Empty", "image", "PRESENTED_IMAGE_SIMPLE", "description", "Modal", "open", "onCancel", "footer", "passingMarks"], "sourceRoot": ""}