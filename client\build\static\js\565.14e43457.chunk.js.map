{"version": 3, "file": "static/js/565.14e43457.chunk.js", "mappings": "+OAAA,MAAQA,QAASC,GAAkBC,EAAQ,MAG9BC,EAAmBC,UAC5B,IAEI,aADuBH,EAAcI,KAAK,+BAAiCC,EAE/E,CAAE,MAAOC,GACL,OAAOA,EAAMC,QACjB,GAcSC,EAAeL,UACxB,IAEI,aADuBH,EAAcS,IAAI,sCACzBC,IACpB,CAAE,MAAOJ,GAAQ,IAADK,EACZ,OAAqB,QAAdA,EAAAL,EAAMC,gBAAQ,IAAAI,OAAA,EAAdA,EAAgBD,OAAQ,CAAEE,SAAS,EAAOC,QAAS,yBAC9D,GAMSC,EAAWX,eAAOY,GAAwC,IAA7BC,EAAgBC,UAAAC,OAAA,QAAAC,IAAAF,UAAA,GAAAA,UAAA,GAAG,KACzD,IACI,MACMG,EADaL,aAAqBM,SACZ,CACxBC,QAAS,CACL,eAAgB,uBAEpBC,QAAS,IACTP,iBAAkBA,EAAoBQ,IAClC,MAAMC,EAAmBC,KAAKC,MAA8B,IAAvBH,EAAcI,OAAgBJ,EAAcK,OAEjFb,EAAiBS,EAAkBD,EAAcI,OAAQJ,EAAcK,MAAM,OAC7EV,GACJ,CACAI,QAAS,KAIb,aADuBvB,EAAcI,KAAK,uBAAwBW,EAAWK,EAEjF,CAAE,MAAOd,GACL,OAAOA,EAAMC,QACjB,CACJ,EAGauB,EAAU3B,UACnB,IAMI,aALuBH,EAAcI,KAAK,sBAAuB2B,EAAU,CACvET,QAAS,CACL,eAAgB,wBAI5B,CAAE,MAAOhB,GACL,OAAOA,EAAMC,QACjB,GAISyB,EAAe7B,UACxB,IAMI,aALuBH,EAAcI,KAAK,4BAA6B2B,EAAU,CAC7ET,QAAS,CACL,eAAgB,wBAI5B,CAAE,MAAOhB,GACL,OAAOA,EAAMC,QACjB,GAIS0B,EAAU9B,UACnB,IAMI,aALuBH,EAAcI,KAAK,sBAAuB2B,EAAU,CACvET,QAAS,CACL,eAAgB,wBAI5B,CAAE,MAAOhB,GACL,OAAOA,EAAMC,QACjB,GAMS2B,EAAc/B,MAAOgC,EAAIpB,KAClC,IACI,IAAIK,EAAS,CACTE,QAAS,CACL,eAAgB,qBAKpBP,aAAqBM,WACrBD,EAAOE,QAAQ,gBAAkB,uBAIrC,aADuBtB,EAAcoC,IAAI,2BAADC,OAA4BF,GAAMpB,EAAWK,EAEzF,CAAE,MAAOd,GACL,OAAOA,EAAMC,QACjB,GAIS+B,EAAanC,MAAOgC,EAAIJ,KACjC,IAMI,aALuB/B,EAAcoC,IAAI,0BAADC,OAA2BF,GAAMJ,EAAU,CAC/ET,QAAS,CACL,eAAgB,wBAI5B,CAAE,MAAOhB,GACL,OAAOA,EAAMC,QACjB,GAISgC,EAAkBpC,MAAOgC,EAAIJ,KACtC,IAMI,aALuB/B,EAAcoC,IAAI,gCAADC,OAAiCF,GAAMJ,EAAU,CACrFT,QAAS,CACL,eAAgB,wBAI5B,CAAE,MAAOhB,GACL,OAAOA,EAAMC,QACjB,GAISiC,EAAarC,MAAOgC,EAAIJ,KACjC,IAMI,aALuB/B,EAAcoC,IAAI,0BAADC,OAA2BF,GAAMJ,EAAU,CAC/ET,QAAS,CACL,eAAgB,wBAI5B,CAAE,MAAOhB,GACL,OAAOA,EAAMC,QACjB,GAMSkC,EAActC,UACvB,IAEI,aADuBH,EAAc0C,OAAO,2BAADL,OAA4BF,GAE3E,CAAE,MAAO7B,GACL,OAAOA,EAAMC,QACjB,GAISoC,EAAaxC,UACtB,IAEI,aADuBH,EAAc0C,OAAO,0BAADL,OAA2BF,GAE1E,CAAE,MAAO7B,GACL,OAAOA,EAAMC,QACjB,GAISqC,EAAkBzC,UAC3B,IAEI,aADuBH,EAAc0C,OAAO,gCAADL,OAAiCF,GAEhF,CAAE,MAAO7B,GACL,OAAOA,EAAMC,QACjB,GAISsC,EAAa1C,UACtB,IAEI,aADuBH,EAAc0C,OAAO,0BAADL,OAA2BF,GAE1E,CAAE,MAAO7B,GACL,OAAOA,EAAMC,QACjB,GAISuC,EAAuB3C,iBAAyB,IAAlBE,EAAOY,UAAAC,OAAA,QAAAC,IAAAF,UAAA,GAAAA,UAAA,GAAG,CAAC,EAClD,IACI,MAAM8B,EAAS,IAAIC,gBACf3C,EAAQ4C,cAAcF,EAAOG,OAAO,eAAgB7C,EAAQ4C,cAC5D5C,EAAQ8C,OAAOJ,EAAOG,OAAO,QAAS7C,EAAQ8C,OAC9C9C,EAAQ+C,WAAWL,EAAOG,OAAO,YAAa7C,EAAQ+C,WACtD/C,EAAQgD,SAASN,EAAOG,OAAO,UAAW7C,EAAQgD,SAGtD,aADuBrD,EAAcS,IAAI,kCAAD4B,OAAmCU,EAAOO,YAEtF,CAAE,MAAOhD,GACL,OAAOA,EAAMC,QACjB,CACJ,C,qDC9NO,MAAMgD,EAAkB,CAC7B,cACA,yBACA,YACA,YACA,gBACA,UACA,WACA,aACA,gBACA,yBACA,kBACA,SACA,wBAGWC,EAAoB,CAC/B,UACA,cACA,UACA,YACA,YACA,SACA,UACA,WACA,mBACA,YACA,WACA,YACA,uBACA,4BACA,cACA,eAGWC,EAAkB,CAC7B,UACA,cACA,UACA,YACA,YACA,SACA,UACA,WACA,mBACA,YACA,WACA,YACA,uBACA,4BACA,cACA,c,4ICuuBF,QApvBA,WACE,MAAM,KAAEC,IAASC,EAAAA,EAAAA,KAAaC,GAAUA,EAAMF,OACxCG,GAAWC,EAAAA,EAAAA,OAGVC,EAAQC,IAAaC,EAAAA,EAAAA,UAAS,KAC9BC,EAASC,IAAcF,EAAAA,EAAAA,WAAS,IAChC3D,EAAO8D,IAAYH,EAAAA,EAAAA,UAAS,OAC5BI,EAAeC,IAAoBL,EAAAA,EAAAA,WAAa,OAAJP,QAAI,IAAJA,OAAI,EAAJA,EAAMP,QAAS,YAC3DoB,EAAeC,IAAoBP,EAAAA,EAAAA,WAAa,OAAJP,QAAI,IAAJA,OAAI,EAAJA,EAAMe,QAAS,QAC3DC,EAAiBC,IAAsBV,EAAAA,EAAAA,UAAS,QAChDW,EAAYC,IAAiBZ,EAAAA,EAAAA,UAAS,KACtCa,EAAQC,IAAad,EAAAA,EAAAA,UAAS,WAG9Be,EAAmBC,IAAwBhB,EAAAA,EAAAA,UAAS,OACpDiB,EAAkBC,IAAuBlB,EAAAA,EAAAA,UAAS,KAClDmB,EAAiBC,IAAsBpB,EAAAA,EAAAA,WAAS,IAChDqB,EAAYC,IAAiBtB,EAAAA,EAAAA,UAAS,OACtCuB,EAAUC,IAAexB,EAAAA,EAAAA,UAAS,OAGlCyB,EAAUC,IAAe1B,EAAAA,EAAAA,UAAS,KAClC2B,EAAYC,IAAiB5B,EAAAA,EAAAA,UAAS,KACtC6B,EAAYC,IAAiB9B,EAAAA,EAAAA,UAAS,OACtC+B,EAAWC,IAAgBhC,EAAAA,EAAAA,UAAS,IAGrCiC,GAAmBC,EAAAA,EAAAA,UAAQ,IACT,YAAlB9B,EAAoC,CAAC,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,KACjD,cAAlBA,EAAsC,CAAC,IAAK,IAAK,IAAK,KACpC,YAAlBA,EAAoC,CAAC,IAAK,KACvC,IACN,CAACA,IAGE+B,GAAoBD,EAAAA,EAAAA,UAAQ,IACV,YAAlB9B,EAAoCd,EAAAA,GAClB,cAAlBc,EAAsCb,EAAAA,GACpB,YAAlBa,EAAoCZ,EAAAA,GACjC,IACN,CAACY,IAGEgC,GAAcC,EAAAA,EAAAA,cAAYnG,UAC9B,IAAK,IAADoG,EACFpC,GAAW,GACXC,EAAS,MACTP,GAAS2C,EAAAA,EAAAA,OAET,MAAMnG,EAAU,CACd8C,MAAOkB,EACPjB,UAAW,GACXC,QAAS,GACToD,QAAS,UAGLlG,QAAiBL,EAAAA,EAAAA,IAAiBG,GAIhC,IAADqG,EAFP,GAAY,OAARnG,QAAQ,IAARA,GAAc,QAANgG,EAARhG,EAAUG,YAAI,IAAA6F,GAAdA,EAAgB3F,QAClBoD,EAAUzD,EAASG,KAAKA,MAAQ,SAEhC0D,GAAiB,OAAR7D,QAAQ,IAARA,GAAc,QAANmG,EAARnG,EAAUG,YAAI,IAAAgG,OAAN,EAARA,EAAgB7F,UAAW,0BACpCmD,EAAU,GAEd,CAAE,MAAO1D,GACPqG,QAAQrG,MAAM,gCAA4BA,GAC1C8D,EAAS,4CACTJ,EAAU,GACZ,CAAC,QACCG,GAAW,GACXN,GAAS+C,EAAAA,EAAAA,MACX,IACC,CAACvC,EAAeR,IAGbgD,GAA0BV,EAAAA,EAAAA,UAAQ,KAGtC,IAAIW,EAAW/C,EAoBf,GAjBA+C,EAAWA,EAASC,QAAOC,GAASA,EAAM7D,QAAUkB,IAG9B,QAAlBE,IACFuC,EAAWA,EAASC,QAAOC,IAENA,EAAM5D,WAAa4D,EAAMvC,SACtBF,KAKF,QAApBG,IACFoC,EAAWA,EAASC,QAAOC,GAASA,EAAM3D,UAAYqB,KAIpDE,EAAWqC,OAAQ,CACrB,MAAMC,EAActC,EAAWuC,cAC/BL,EAAWA,EAASC,QAAOC,IAAK,IAAAI,EAAAC,EAAAC,EAAA,OACnB,QAAXF,EAAAJ,EAAMO,aAAK,IAAAH,OAAA,EAAXA,EAAaD,cAAcK,SAASN,MACvB,QADmCG,EAChDL,EAAM3D,eAAO,IAAAgE,OAAA,EAAbA,EAAeF,cAAcK,SAASN,MAC3B,QADuCI,EAClDN,EAAMS,aAAK,IAAAH,OAAA,EAAXA,EAAaH,cAAcK,SAASN,GAAY,GAEpD,CAGA,MAAMQ,EAAS,IAAIZ,GAAUa,MAAK,CAACC,EAAGC,KACpC,OAAQ/C,GACN,IAAK,SACH,OAAO,IAAIgD,KAAKD,EAAEE,WAAa,GAAK,IAAID,KAAKF,EAAEG,WAAa,GAC9D,IAAK,SACH,OAAO,IAAID,KAAKF,EAAEG,WAAa,GAAK,IAAID,KAAKD,EAAEE,WAAa,GAC9D,IAAK,QACH,OAAQH,EAAEL,OAAS,IAAIS,cAAcH,EAAEN,OAAS,IAClD,IAAK,UACH,OAAQK,EAAEvE,SAAW,IAAI2E,cAAcH,EAAExE,SAAW,IACtD,QACE,OAAO,EACX,IAQF,OALAsD,QAAQsB,IAAI,gCAA4BP,EAAOxG,QAC3CwG,EAAOxG,OAAS,GAClByF,QAAQsB,IAAI,sCAA6BP,EAAO,IAG3CA,CAAM,GACZ,CAAC3D,EAAQa,EAAYE,EAAQT,EAAeE,EAAeG,IAuBxDwD,EAAkBA,KACtB/C,EAAoB,IACpBF,EAAqB,MACrBI,GAAmB,GACnBE,EAAc,MACVC,GACFA,EAAS2C,OACX,EAGIC,EAAuBA,KAC3B/C,GAAoBD,EAAgB,EAIhCiD,EAAoBlI,UACxB,IAAKmI,EAAU,OAAOA,EAGtB,GAAIA,EAASd,SAAS,kBAAoBc,EAASd,SAAS,OAC1D,IACE,MAAMjH,QAAiBgI,MAAM,6DAADlG,OAA8DmG,mBAAmBF,IAAa,CACxHG,OAAQ,MACRnH,QAAS,CACP,eAAgB,sBAIpB,IAAKf,EAASmI,GACZ,MAAM,IAAIC,MAAM,uBAADtG,OAAwB9B,EAASqI,SAGlD,MAAMlI,QAAaH,EAASsI,OAE5B,OAAInI,EAAKE,SAAWF,EAAKoI,WACvBnC,QAAQsB,IAAI,sCACLvH,EAAKoI,YAEZnC,QAAQoC,KAAK,0DAAiDrI,GACvD4H,EAEX,CAAE,MAAOhI,GAEP,OADAqG,QAAQrG,MAAM,mCAA+BA,GACtCgI,CACT,CAGF,OAAOA,CAAQ,EAIXU,EAAmBhC,IACvB,GAAIA,EAAMiC,UACR,OAAOjC,EAAMiC,UAGf,GAAIjC,EAAMkC,UAAYlC,EAAMkC,QAAQ1B,SAAS,iBAAkB,CAC7D,IAAI2B,EAAUnC,EAAMkC,QACpB,GAAIC,EAAQ3B,SAAS,gBAAkB2B,EAAQ3B,SAAS,YAAa,CACnE,MAAM4B,EAAQD,EAAQC,MAAM,sDAC5BD,EAAUC,EAAQA,EAAM,GAAKD,CAC/B,CACA,MAAM,8BAAN9G,OAAqC8G,EAAO,qBAC9C,CAEA,MAAO,0BAA0B,GAInCE,EAAAA,EAAAA,YAAU,KACRhD,GAAa,GACZ,CAACA,KAEJgD,EAAAA,EAAAA,YAAU,KACA,OAAJ3F,QAAI,IAAJA,GAAAA,EAAMP,OACRmB,EAAiBZ,EAAKP,OAEhB,OAAJO,QAAI,IAAJA,GAAAA,EAAMe,OACRD,EAAiBd,EAAKe,MACxB,GACC,CAACf,IAGJ,MAiBM4F,EAAmBA,KACvB,GAAI1D,EAAWqB,OAAQ,CACrB,MAAMsC,EAAU,CACdpH,GAAI2F,KAAK0B,MACTC,KAAM7D,EACN8D,QAAY,OAAJhG,QAAI,IAAJA,OAAI,EAAJA,EAAMiG,OAAQ,YACtBC,WAAW,IAAI9B,MAAO+B,cACtBC,QAAS,IAEXnE,EAAY,IAAID,EAAU6D,IAC1B1D,EAAc,GAChB,GAsBF,OACEkE,EAAAA,EAAAA,MAAA,OAAK3G,UAAU,0BAAyB4G,SAAA,EAEtCC,EAAAA,EAAAA,KAAA,OAAK7G,UAAU,uBAAsB4G,UACnCD,EAAAA,EAAAA,MAAA,OAAK3G,UAAU,iBAAgB4G,SAAA,EAC7BD,EAAAA,EAAAA,MAAA,OAAK3G,UAAU,cAAa4G,SAAA,EAC1BC,EAAAA,EAAAA,KAAA,OAAK7G,UAAU,cAAa4G,UAC1BC,EAAAA,EAAAA,KAACC,EAAAA,IAAO,OAEVH,EAAAA,EAAAA,MAAA,OAAK3G,UAAU,cAAa4G,SAAA,EAC1BC,EAAAA,EAAAA,KAAA,MAAAD,SAAI,mBACJC,EAAAA,EAAAA,KAAA,KAAAD,SAAG,6DAKPD,EAAAA,EAAAA,MAAA,OAAK3G,UAAU,gBAAe4G,SAAA,EAC5BD,EAAAA,EAAAA,MAAA,OAAK3G,UAAU,gBAAe4G,SAAA,EAC5BC,EAAAA,EAAAA,KAAA,QAAM7G,UAAU,cAAa4G,SAAC,YAC9BC,EAAAA,EAAAA,KAAA,QAAM7G,UAAU,cAAa4G,SAAE3F,EAAc8F,OAAO,GAAGC,cAAgB/F,EAAcgG,MAAM,SAE7FN,EAAAA,EAAAA,MAAA,OAAK3G,UAAU,gBAAe4G,SAAA,EAC5BC,EAAAA,EAAAA,KAAA,QAAM7G,UAAU,cAAa4G,SAAC,iBAC9BC,EAAAA,EAAAA,KAAA,QAAM7G,UAAU,cAAa4G,SACV,aAAZ,OAAJtG,QAAI,IAAJA,OAAI,EAAJA,EAAMP,OAAmB,SAAAd,QAAgB,OAAJqB,QAAI,IAAJA,OAAI,EAAJA,EAAMe,QAAS,OACpC,eAAZ,OAAJf,QAAI,IAAJA,OAAI,EAAJA,EAAMP,QACU,aAAZ,OAAJO,QAAI,IAAJA,OAAI,EAAJA,EAAMP,OADqB,QAAAd,QAAe,OAAJqB,QAAI,IAAJA,OAAI,EAAJA,EAAMe,QAAS,OAErD,wBAOXsF,EAAAA,EAAAA,MAAA,OAAK3G,UAAU,wBAAuB4G,SAAA,EAEpCD,EAAAA,EAAAA,MAAA,OAAK3G,UAAU,iBAAgB4G,SAAA,EAC7BD,EAAAA,EAAAA,MAAA,OAAK3G,UAAU,eAAc4G,SAAA,EAE3BD,EAAAA,EAAAA,MAAA,OAAK3G,UAAU,gBAAe4G,SAAA,EAC5BD,EAAAA,EAAAA,MAAA,SAAO3G,UAAU,gBAAe4G,SAAA,EAC9BC,EAAAA,EAAAA,KAACK,EAAAA,IAAQ,IAAG,sBAGdP,EAAAA,EAAAA,MAAA,UACEQ,MAAOhG,EACPiG,SAAWC,GAAMjG,EAAiBiG,EAAEC,OAAOH,OAC3CnH,UAAU,8BAA6B4G,SAAA,EAEvCC,EAAAA,EAAAA,KAAA,UAAQM,MAAM,MAAKP,SAAC,gBACnB9D,EAAiByE,KAAKC,IACrBX,EAAAA,EAAAA,KAAA,UAAkBM,MAAOK,EAAIZ,SACR,YAAlB3F,EAA2B,SAAAhC,OAAYuI,GAAG,QAAAvI,OAAauI,IAD7CA,YAQnBb,EAAAA,EAAAA,MAAA,OAAK3G,UAAU,gBAAe4G,SAAA,EAC5BD,EAAAA,EAAAA,MAAA,SAAO3G,UAAU,gBAAe4G,SAAA,EAC9BC,EAAAA,EAAAA,KAACK,EAAAA,IAAQ,IAAG,cAGdP,EAAAA,EAAAA,MAAA,UACEQ,MAAO7F,EACP8F,SAAWC,GAAM9F,EAAmB8F,EAAEC,OAAOH,OAC7CnH,UAAU,gCAA+B4G,SAAA,EAEzCC,EAAAA,EAAAA,KAAA,UAAQM,MAAM,MAAKP,SAAC,iBACnB5D,EAAkBuE,KAAKtH,IACtB4G,EAAAA,EAAAA,KAAA,UAAsBM,MAAOlH,EAAQ2G,SAClC3G,GADUA,YAQnB0G,EAAAA,EAAAA,MAAA,OAAK3G,UAAU,gBAAe4G,SAAA,EAC5BD,EAAAA,EAAAA,MAAA,SAAO3G,UAAU,gBAAe4G,SAAA,EAC9BC,EAAAA,EAAAA,KAACY,EAAAA,IAAe,IAAG,WAGrBd,EAAAA,EAAAA,MAAA,UACEQ,MAAOzF,EACP0F,SAAWC,GAAM1F,EAAU0F,EAAEC,OAAOH,OACpCnH,UAAU,6BAA4B4G,SAAA,EAEtCC,EAAAA,EAAAA,KAAA,UAAQM,MAAM,SAAQP,SAAC,kBACvBC,EAAAA,EAAAA,KAAA,UAAQM,MAAM,SAAQP,SAAC,kBACvBC,EAAAA,EAAAA,KAAA,UAAQM,MAAM,QAAOP,SAAC,eACtBC,EAAAA,EAAAA,KAAA,UAAQM,MAAM,UAASP,SAAC,0BAM9BD,EAAAA,EAAAA,MAAA,OAAK3G,UAAU,aAAY4G,SAAA,EACzBD,EAAAA,EAAAA,MAAA,OAAK3G,UAAU,mBAAkB4G,SAAA,EAC/BC,EAAAA,EAAAA,KAACa,EAAAA,IAAQ,CAAC1H,UAAU,iBACpB6G,EAAAA,EAAAA,KAAA,SACEc,KAAK,OACLC,YAAY,+CACZT,MAAO3F,EACP4F,SAAWC,GAAM5F,EAAc4F,EAAEC,OAAOH,OACxCnH,UAAU,iBAEXwB,IACCmF,EAAAA,EAAAA,MAAA,UAAQkB,QA/JIC,KACxBrG,EAAc,GAAG,EA8J+BzB,UAAU,mBAAkB4G,SAAA,EAC9DC,EAAAA,EAAAA,KAACkB,EAAAA,IAAG,IAAG,sBAMbpB,EAAAA,EAAAA,MAAA,UAAQkB,QAlKIG,KAEpB/E,GAAa,EAgK2BjD,UAAU,cAAa4G,SAAA,EACrDC,EAAAA,EAAAA,KAACoB,EAAAA,IAAU,IAAG,uBAOnBnH,GACC6F,EAAAA,EAAAA,MAAA,OAAK3G,UAAU,gBAAe4G,SAAA,EAC5BC,EAAAA,EAAAA,KAAA,OAAK7G,UAAU,qBACf6G,EAAAA,EAAAA,KAAA,KAAAD,SAAG,yBAEH1J,GACFyJ,EAAAA,EAAAA,MAAA,OAAK3G,UAAU,cAAa4G,SAAA,EAC1BC,EAAAA,EAAAA,KAACqB,EAAAA,IAAe,CAAClI,UAAU,gBAC3B6G,EAAAA,EAAAA,KAAA,MAAAD,SAAI,0BACJC,EAAAA,EAAAA,KAAA,KAAAD,SAAI1J,KACJ2J,EAAAA,EAAAA,KAAA,UAAQgB,QAAS5E,EAAajD,UAAU,YAAW4G,SAAC,iBAIpDnD,EAAwB3F,OAAS,GACnC+I,EAAAA,EAAAA,KAAA,OAAK7G,UAAU,cAAa4G,SACzBnD,EAAwB8D,KAAI,CAAC3D,EAAOuE,KACnCxB,EAAAA,EAAAA,MAAA,OAAiB3G,UAAU,aAAa6H,QAASA,IAtSrC9K,WACtB,MAAM6G,EAAQH,EAAwB0E,GAQtC,GANAtG,EAAqBsG,GACrBpG,EAAoB,CAACoG,IACrBlG,GAAmB,GACnBE,EAAc,MAGL,OAALyB,QAAK,IAALA,GAAAA,EAAOsB,WAAatB,EAAMsB,SAASd,SAAS,kBAAoBR,EAAMsB,SAASd,SAAS,QAC1F,IACE,MAAMsB,QAAkBT,EAAkBrB,EAAMsB,UAChDtB,EAAMwE,eAAiB1C,CACzB,CAAE,MAAOxI,GACPqG,QAAQoC,KAAK,gDACb/B,EAAMwE,eAAiBxE,EAAMsB,QAC/B,CACF,EAqRiEmD,CAAgBF,GAAOvB,SAAA,EAC5ED,EAAAA,EAAAA,MAAA,OAAK3G,UAAU,uBAAsB4G,SAAA,EACnCC,EAAAA,EAAAA,KAAA,OACEyB,IAAK1C,EAAgBhC,GACrB2E,IAAK3E,EAAMO,MACXnE,UAAU,kBACVwI,QAAUnB,IAER,GAAIzD,EAAMkC,UAAYlC,EAAMkC,QAAQ1B,SAAS,iBAAkB,CAE7D,IAAI2B,EAAUnC,EAAMkC,QACpB,GAAIC,EAAQ3B,SAAS,gBAAkB2B,EAAQ3B,SAAS,YAAa,CACnE,MAAM4B,EAAQD,EAAQC,MAAM,sDAC5BD,EAAUC,EAAQA,EAAM,GAAKD,CAC/B,CAEA,MAAM0C,EAAY,CAAC,8BAADxJ,OACc8G,EAAO,gDAAA9G,OACP8G,EAAO,gDAAA9G,OACP8G,EAAO,gBACrC,4BAGI2C,EAAarB,EAAEC,OAAOgB,IACtBK,EAAeF,EAAUG,WAAUC,GAAOH,EAAWtE,SAASyE,EAAIC,MAAM,KAAKC,SAE/EJ,EAAeF,EAAU3K,OAAS,IACpCuJ,EAAEC,OAAOgB,IAAMG,EAAUE,EAAe,GAE5C,MACEtB,EAAEC,OAAOgB,IAAM,0BACjB,KAGJzB,EAAAA,EAAAA,KAAA,OAAK7G,UAAU,eAAc4G,UAC3BC,EAAAA,EAAAA,KAACmC,EAAAA,IAAY,CAAChJ,UAAU,iBAE1B6G,EAAAA,EAAAA,KAAA,OAAK7G,UAAU,iBAAgB4G,SAC5BhD,EAAMqF,UAAY,UAEpBrF,EAAMsF,WAAatF,EAAMsF,UAAUpL,OAAS,IAC3C6I,EAAAA,EAAAA,MAAA,OAAK3G,UAAU,iBAAgB4G,SAAA,EAC7BC,EAAAA,EAAAA,KAACsC,EAAAA,IAAY,IAAG,YAMtBxC,EAAAA,EAAAA,MAAA,OAAK3G,UAAU,qBAAoB4G,SAAA,EACjCC,EAAAA,EAAAA,KAAA,MAAI7G,UAAU,cAAa4G,SAAEhD,EAAMO,SACnCwC,EAAAA,EAAAA,MAAA,OAAK3G,UAAU,aAAY4G,SAAA,EACzBC,EAAAA,EAAAA,KAAA,QAAM7G,UAAU,gBAAe4G,SAAEhD,EAAM3D,WACvC4G,EAAAA,EAAAA,KAAA,QAAM7G,UAAU,cAAa4G,SACR,YAAlB3F,EAA2B,SAAAhC,OAAY2E,EAAM5D,WAAa4D,EAAMvC,OAAK,QAAApC,OAAa2E,EAAM5D,WAAa4D,EAAMvC,aAGhHsF,EAAAA,EAAAA,MAAA,OAAK3G,UAAU,aAAY4G,SAAA,CACxBhD,EAAMS,QAASwC,EAAAA,EAAAA,KAAA,QAAM7G,UAAU,YAAW4G,SAAEhD,EAAMS,QAClDT,EAAMwF,iBAAmBxF,EAAMwF,mBAAqBxF,EAAM5D,WAAa4D,EAAMvC,SAC5EsF,EAAAA,EAAAA,MAAA,QAAM3G,UAAU,aAAY4G,SAAA,CAAC,eACI,YAAlB3F,EAA2B,SAAAhC,OAAY2E,EAAMwF,iBAAe,QAAAnK,OAAa2E,EAAMwF,4BA5D5FjB,QAqEdxB,EAAAA,EAAAA,MAAA,OAAK3G,UAAU,cAAa4G,SAAA,EAC1BC,EAAAA,EAAAA,KAACwC,EAAAA,IAAe,CAACrJ,UAAU,gBAC3B6G,EAAAA,EAAAA,KAAA,MAAAD,SAAI,qBACJC,EAAAA,EAAAA,KAAA,KAAAD,SAAG,gEACHC,EAAAA,EAAAA,KAAA,KAAG7G,UAAU,aAAY4G,SAAC,sDAM/B9E,EAAiBhE,OAAS,GAA2B,OAAtB8D,IAC9BiF,EAAAA,EAAAA,KAAA,OAAK7G,UAAS,iBAAAf,OAAmB+C,EAAkB,WAAa,IAAM6F,QAAUR,IAC1EA,EAAEC,SAAWD,EAAEiC,eAAexE,GAAiB,EACnD8B,UACAC,EAAAA,EAAAA,KAAA,OAAK7G,UAAS,eAAAf,OAAiB+C,EAAkB,WAAa,IAAK4E,SAChE,MACC,MAAMhD,EAAQH,EAAwB7B,GACtC,OAAKgC,GAGH+C,EAAAA,EAAAA,MAAA,OAAK3G,UAAU,gBAAe4G,SAAA,EAC5BD,EAAAA,EAAAA,MAAA,OAAK3G,UAAU,eAAc4G,SAAA,EAC3BD,EAAAA,EAAAA,MAAA,OAAK3G,UAAU,aAAY4G,SAAA,EACzBC,EAAAA,EAAAA,KAAA,MAAI7G,UAAU,cAAa4G,SAAEhD,EAAMO,SACnCwC,EAAAA,EAAAA,MAAA,OAAK3G,UAAU,aAAY4G,SAAA,EACzBC,EAAAA,EAAAA,KAAA,QAAM7G,UAAU,gBAAe4G,SAAEhD,EAAM3D,WACvC0G,EAAAA,EAAAA,MAAA,QAAM3G,UAAU,cAAa4G,SAAA,CAAC,SAAOhD,EAAM5D,aAC1C4D,EAAM7D,QAAS8G,EAAAA,EAAAA,KAAA,QAAM7G,UAAU,cAAa4G,SAAEhD,EAAM7D,eAGzD4G,EAAAA,EAAAA,MAAA,OAAK3G,UAAU,iBAAgB4G,SAAA,EAC7BC,EAAAA,EAAAA,KAAA,UACE7G,UAAU,yBACV6H,QAAS7C,EACTb,MAAOnC,EAAkB,kBAAoB,mBAAmB4E,SAE/D5E,GAAkB6E,EAAAA,EAAAA,KAAC0C,EAAAA,IAAU,KAAM1C,EAAAA,EAAAA,KAAC2C,EAAAA,IAAQ,OAE/C3C,EAAAA,EAAAA,KAAA,UACE7G,UAAU,wBACV6H,QAAS/C,EACTX,MAAM,cAAayC,UAEnBC,EAAAA,EAAAA,KAAC4C,EAAAA,IAAO,aAId5C,EAAAA,EAAAA,KAAA,OAAK7G,UAAU,kBAAiB4G,SAC7BhD,EAAMsB,UACLyB,EAAAA,EAAAA,MAAA,OAAK+C,MAAO,CAAEC,QAAS,OAAQC,WAAY,OAAQC,aAAc,OAAQjD,SAAA,EACrED,EAAAA,EAAAA,MAAA,SACEmD,IAAMA,GAAQzH,EAAYyH,GAC1BC,UAAQ,EACRC,UAAQ,EACRC,aAAW,EACXC,QAAQ,WACRC,MAAM,OACNC,OAAO,MACPC,OAAQzE,EAAgBhC,GACxB8F,MAAO,CACLS,MAAO,OACPC,OAAQ,QACRE,gBAAiB,QAEnB9B,QAAUnB,IACRlF,EAAc,yBAADlD,OAA0B2E,EAAMO,MAAK,qCAAoC,EAExFoG,UAAWA,KACTpI,EAAc,KAAK,EAErBqI,YAAaA,KACXjH,QAAQsB,IAAI,qCAA2B,EAEzC4F,YAAY,YAAW7D,SAAA,EAGvBC,EAAAA,EAAAA,KAAA,UAAQyB,IAAK1E,EAAMwE,gBAAkBxE,EAAMsB,SAAUyC,KAAK,cAGzD/D,EAAMsF,WAAatF,EAAMsF,UAAUpL,OAAS,GAAK8F,EAAMsF,UAAU3B,KAAI,CAACmD,EAAUvC,KAC/EtB,EAAAA,EAAAA,KAAA,SAEE8D,KAAK,YACLrC,IAAKoC,EAAS7B,IACd+B,QAASF,EAASG,SAClBC,MAAOJ,EAASK,aAChBpO,QAAS+N,EAASM,WAAuB,IAAV7C,GAAY,GAAAlJ,OALnCyL,EAASG,SAAQ,KAAA5L,OAAIkJ,MAO9B,kDAMJvE,EAAMsF,WAAatF,EAAMsF,UAAUpL,OAAS,IAC3C6I,EAAAA,EAAAA,MAAA,OAAK3G,UAAU,qBAAoB4G,SAAA,EACjCC,EAAAA,EAAAA,KAACsC,EAAAA,IAAY,CAACnJ,UAAU,mBACxB2G,EAAAA,EAAAA,MAAA,QAAAC,SAAA,CAAM,0BAAwBhD,EAAMsF,UAAUpL,OAAO,qBAKxDoE,IACC2E,EAAAA,EAAAA,KAAA,OAAK7G,UAAU,sBAAqB4G,UAClCD,EAAAA,EAAAA,MAAA,OAAK3G,UAAU,gBAAe4G,SAAA,EAC5BC,EAAAA,EAAAA,KAACqB,EAAAA,IAAe,CAAClI,UAAU,gBAC3B6G,EAAAA,EAAAA,KAAA,KAAAD,SAAI1E,KACJ2E,EAAAA,EAAAA,KAAA,UAAQgB,QAASA,IAAM1F,EAAc,MAAOnC,UAAU,oBAAmB4G,SAAC,oBAOlFhD,EAAMkC,SAERe,EAAAA,EAAAA,KAAA,UACEyB,IAAG,iCAAArJ,OAAmC2E,EAAMkC,QAAO,qBACnD3B,MAAOP,EAAMO,MACb8G,YAAY,IACZC,iBAAe,EACflL,UAAU,eACVmL,OAAQA,IAAM5H,QAAQsB,IAAI,mCAG5B8B,EAAAA,EAAAA,MAAA,OAAK3G,UAAU,cAAa4G,SAAA,EAC1BC,EAAAA,EAAAA,KAAA,OAAK7G,UAAU,aAAY4G,SAAC,kBAC5BC,EAAAA,EAAAA,KAAA,MAAAD,SAAI,uBACJC,EAAAA,EAAAA,KAAA,KAAAD,SAAI1E,GAAc,gDAClB2E,EAAAA,EAAAA,KAAA,OAAK7G,UAAU,gBAAe4G,UAC5BC,EAAAA,EAAAA,KAAA,KACEuE,KAAMxH,EAAMwE,gBAAkBxE,EAAMsB,SACpCoC,OAAO,SACP+D,IAAI,sBACJrL,UAAU,oBAAmB4G,SAC9B,yCASP5E,IACA2E,EAAAA,EAAAA,MAAA,OAAK3G,UAAU,mBAAkB4G,SAAA,EAC/BD,EAAAA,EAAAA,MAAA,MAAI3G,UAAU,iBAAgB4G,SAAA,EAC5BC,EAAAA,EAAAA,KAACsC,EAAAA,IAAY,IAAG,eACH7G,EAASxE,OAAO,QAI/B+I,EAAAA,EAAAA,KAAA,OAAK7G,UAAU,cAAa4G,UAC1BD,EAAAA,EAAAA,MAAA,OAAK3G,UAAU,0BAAyB4G,SAAA,EACtCC,EAAAA,EAAAA,KAAA,YACEM,MAAO3E,EACP4E,SAAWC,GAAM5E,EAAc4E,EAAEC,OAAOH,OACxCS,YAAY,0CACZ5H,UAAU,gBACVsL,KAAK,OAEPzE,EAAAA,EAAAA,KAAA,UACEgB,QAAS3B,EACTlG,UAAU,qBACVuL,UAAW/I,EAAWqB,OAAO+C,SAC9B,uBAOLC,EAAAA,EAAAA,KAAA,OAAK7G,UAAU,gBAAe4G,SACP,IAApBtE,EAASxE,QACR6I,EAAAA,EAAAA,MAAA,OAAK3G,UAAU,cAAa4G,SAAA,EAC1BC,EAAAA,EAAAA,KAACsC,EAAAA,IAAY,KACbtC,EAAAA,EAAAA,KAAA,KAAAD,SAAG,6DAGLtE,EAASiF,KAAKpB,IACZQ,EAAAA,EAAAA,MAAA,OAAsB3G,UAAU,UAAS4G,SAAA,EACvCD,EAAAA,EAAAA,MAAA,OAAK3G,UAAU,iBAAgB4G,SAAA,EAC7BC,EAAAA,EAAAA,KAAA,QAAM7G,UAAU,iBAAgB4G,SAAET,EAAQG,UAC1CO,EAAAA,EAAAA,KAAA,QAAM7G,UAAU,eAAc4G,SAC3B,IAAIlC,KAAKyB,EAAQK,WAAWgF,2BAGjC3E,EAAAA,EAAAA,KAAA,OAAK7G,UAAU,eAAc4G,SAAET,EAAQE,QACvCQ,EAAAA,EAAAA,KAAA,OAAK7G,UAAU,kBAAiB4G,UAC9BC,EAAAA,EAAAA,KAAA,UACEgB,QAASA,IAAMlF,EAAcwD,EAAQpH,IACrCiB,UAAU,YAAW4G,SACtB,YAMFlE,IAAeyD,EAAQpH,KACtB4H,EAAAA,EAAAA,MAAA,OAAK3G,UAAU,wBAAuB4G,SAAA,EACpCC,EAAAA,EAAAA,KAAA,YACEM,MAAOvE,EACPwE,SAAWC,GAAMxE,EAAawE,EAAEC,OAAOH,OACvCS,YAAY,mBACZ5H,UAAU,cACVsL,KAAK,OAEP3E,EAAAA,EAAAA,MAAA,OAAK3G,UAAU,gBAAe4G,SAAA,EAC5BC,EAAAA,EAAAA,KAAA,UACEgB,QAASA,IAtbrB4D,KACtB,GAAI7I,EAAUiB,OAAQ,CACpB,MAAM6H,EAAQ,CACZ3M,GAAI2F,KAAK0B,MACTC,KAAMzD,EACN0D,QAAY,OAAJhG,QAAI,IAAJA,OAAI,EAAJA,EAAMiG,OAAQ,YACtBC,WAAW,IAAI9B,MAAO+B,eAGxBlE,EAAYD,EAASiF,KAAIpB,GACvBA,EAAQpH,KAAO0M,GAASE,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACfxF,GAAO,IAAEO,QAAS,IAAIP,EAAQO,QAASgF,KAC5CvF,KAENtD,EAAa,IACbF,EAAc,KAChB,GAsaiDiJ,CAAezF,EAAQpH,IACtCiB,UAAU,mBACVuL,UAAW3I,EAAUiB,OAAO+C,SAC7B,WAGDC,EAAAA,EAAAA,KAAA,UACEgB,QAASA,KACPlF,EAAc,MACdE,EAAa,GAAG,EAElB7C,UAAU,mBAAkB4G,SAC7B,iBAQNT,EAAQO,QAAQ5I,OAAS,IACxB+I,EAAAA,EAAAA,KAAA,OAAK7G,UAAU,UAAS4G,SACrBT,EAAQO,QAAQa,KAAKmE,IACpB/E,EAAAA,EAAAA,MAAA,OAAoB3G,UAAU,QAAO4G,SAAA,EACnCD,EAAAA,EAAAA,MAAA,OAAK3G,UAAU,eAAc4G,SAAA,EAC3BC,EAAAA,EAAAA,KAAA,QAAM7G,UAAU,eAAc4G,SAAE8E,EAAMpF,UACtCO,EAAAA,EAAAA,KAAA,QAAM7G,UAAU,aAAY4G,SACzB,IAAIlC,KAAKgH,EAAMlF,WAAWgF,2BAG/B3E,EAAAA,EAAAA,KAAA,OAAK7G,UAAU,aAAY4G,SAAE8E,EAAMrF,SAP3BqF,EAAM3M,UApDdoH,EAAQpH,eAnKb8H,EAAAA,EAAAA,KAAA,OAAAD,SAAK,mBA2OzB,EA7OA,SAmPb,C", "sources": ["apicalls/study.js", "data/Subjects.jsx", "pages/user/VideoLessons/index.js"], "sourcesContent": ["const { default: axiosInstance } = require(\".\");\r\n\r\n// get study materials\r\nexport const getStudyMaterial = async (filters) => {\r\n    try {\r\n        const response = await axiosInstance.post(\"/api/study/get-study-content\" , filters);\r\n        return response;\r\n    } catch (error) {\r\n        return error.response;\r\n    }\r\n}\r\n\r\n// get available classes for user's level\r\nexport const getAvailableClasses = async () => {\r\n    try {\r\n        const response = await axiosInstance.post(\"/api/study/get-available-classes\");\r\n        return response;\r\n    } catch (error) {\r\n        return error.response;\r\n    }\r\n}\r\n\r\n// get all videos for admin management\r\nexport const getAllVideos = async () => {\r\n    try {\r\n        const response = await axiosInstance.get(\"/api/study/videos-subtitle-status\");\r\n        return response.data;\r\n    } catch (error) {\r\n        return error.response?.data || { success: false, message: \"Failed to fetch videos\" };\r\n    }\r\n}\r\n\r\n// Add study material functions\r\n\r\n// Add video (supports both JSON data and FormData)\r\nexport const addVideo = async (videoData, onUploadProgress = null) => {\r\n    try {\r\n        const isFormData = videoData instanceof FormData;\r\n        const config = isFormData ? {\r\n            headers: {\r\n                'Content-Type': 'multipart/form-data',\r\n            },\r\n            timeout: 600000, // 10 minutes timeout for large files\r\n            onUploadProgress: onUploadProgress ? (progressEvent) => {\r\n                const percentCompleted = Math.round((progressEvent.loaded * 100) / progressEvent.total);\r\n                // Pass additional information for better progress tracking\r\n                onUploadProgress(percentCompleted, progressEvent.loaded, progressEvent.total);\r\n            } : undefined,\r\n        } : {\r\n            timeout: 60000, // 1 minute for YouTube videos\r\n        };\r\n\r\n        const response = await axiosInstance.post(\"/api/study/add-video\", videoData, config);\r\n        return response;\r\n    } catch (error) {\r\n        return error.response;\r\n    }\r\n}\r\n\r\n// Add note\r\nexport const addNote = async (formData) => {\r\n    try {\r\n        const response = await axiosInstance.post(\"/api/study/add-note\", formData, {\r\n            headers: {\r\n                'Content-Type': 'multipart/form-data',\r\n            },\r\n        });\r\n        return response;\r\n    } catch (error) {\r\n        return error.response;\r\n    }\r\n}\r\n\r\n// Add past paper\r\nexport const addPastPaper = async (formData) => {\r\n    try {\r\n        const response = await axiosInstance.post(\"/api/study/add-past-paper\", formData, {\r\n            headers: {\r\n                'Content-Type': 'multipart/form-data',\r\n            },\r\n        });\r\n        return response;\r\n    } catch (error) {\r\n        return error.response;\r\n    }\r\n}\r\n\r\n// Add book\r\nexport const addBook = async (formData) => {\r\n    try {\r\n        const response = await axiosInstance.post(\"/api/study/add-book\", formData, {\r\n            headers: {\r\n                'Content-Type': 'multipart/form-data',\r\n            },\r\n        });\r\n        return response;\r\n    } catch (error) {\r\n        return error.response;\r\n    }\r\n}\r\n\r\n// Update study material functions\r\n\r\n// Update video\r\nexport const updateVideo = async (id, videoData) => {\r\n    try {\r\n        let config = {\r\n            headers: {\r\n                'Content-Type': 'application/json',\r\n            },\r\n        };\r\n\r\n        // If videoData is FormData (contains file uploads), change content type\r\n        if (videoData instanceof FormData) {\r\n            config.headers['Content-Type'] = 'multipart/form-data';\r\n        }\r\n\r\n        const response = await axiosInstance.put(`/api/study/update-video/${id}`, videoData, config);\r\n        return response;\r\n    } catch (error) {\r\n        return error.response;\r\n    }\r\n}\r\n\r\n// Update note\r\nexport const updateNote = async (id, formData) => {\r\n    try {\r\n        const response = await axiosInstance.put(`/api/study/update-note/${id}`, formData, {\r\n            headers: {\r\n                'Content-Type': 'multipart/form-data',\r\n            },\r\n        });\r\n        return response;\r\n    } catch (error) {\r\n        return error.response;\r\n    }\r\n}\r\n\r\n// Update past paper\r\nexport const updatePastPaper = async (id, formData) => {\r\n    try {\r\n        const response = await axiosInstance.put(`/api/study/update-past-paper/${id}`, formData, {\r\n            headers: {\r\n                'Content-Type': 'multipart/form-data',\r\n            },\r\n        });\r\n        return response;\r\n    } catch (error) {\r\n        return error.response;\r\n    }\r\n}\r\n\r\n// Update book\r\nexport const updateBook = async (id, formData) => {\r\n    try {\r\n        const response = await axiosInstance.put(`/api/study/update-book/${id}`, formData, {\r\n            headers: {\r\n                'Content-Type': 'multipart/form-data',\r\n            },\r\n        });\r\n        return response;\r\n    } catch (error) {\r\n        return error.response;\r\n    }\r\n}\r\n\r\n// Delete study material functions\r\n\r\n// Delete video\r\nexport const deleteVideo = async (id) => {\r\n    try {\r\n        const response = await axiosInstance.delete(`/api/study/delete-video/${id}`);\r\n        return response;\r\n    } catch (error) {\r\n        return error.response;\r\n    }\r\n}\r\n\r\n// Delete note\r\nexport const deleteNote = async (id) => {\r\n    try {\r\n        const response = await axiosInstance.delete(`/api/study/delete-note/${id}`);\r\n        return response;\r\n    } catch (error) {\r\n        return error.response;\r\n    }\r\n}\r\n\r\n// Delete past paper\r\nexport const deletePastPaper = async (id) => {\r\n    try {\r\n        const response = await axiosInstance.delete(`/api/study/delete-past-paper/${id}`);\r\n        return response;\r\n    } catch (error) {\r\n        return error.response;\r\n    }\r\n}\r\n\r\n// Delete book\r\nexport const deleteBook = async (id) => {\r\n    try {\r\n        const response = await axiosInstance.delete(`/api/study/delete-book/${id}`);\r\n        return response;\r\n    } catch (error) {\r\n        return error.response;\r\n    }\r\n}\r\n\r\n// Get all study materials for admin management\r\nexport const getAllStudyMaterials = async (filters = {}) => {\r\n    try {\r\n        const params = new URLSearchParams();\r\n        if (filters.materialType) params.append('materialType', filters.materialType);\r\n        if (filters.level) params.append('level', filters.level);\r\n        if (filters.className) params.append('className', filters.className);\r\n        if (filters.subject) params.append('subject', filters.subject);\r\n\r\n        const response = await axiosInstance.get(`/api/study/admin/all-materials?${params.toString()}`);\r\n        return response;\r\n    } catch (error) {\r\n        return error.response;\r\n    }\r\n}\r\n", "export const primarySubjects = [\r\n  \"Mathematics\",\r\n  \"Science and Technology\",\r\n  \"Geography\",\r\n  \"Kiswahili\",\r\n  \"SocialStudies\",\r\n  \"English\",\r\n  \"Religion\",\r\n  \"Arithmetic\",\r\n  \"Sport and Art\",\r\n  \"Health and Environment\",\r\n  \"Civic and Moral\",\r\n  \"French\",\r\n  \"Historia ya Tanzania\",\r\n];\r\n\r\nexport const secondarySubjects = [\r\n  \"Physics\",\r\n  \"Mathematics\",\r\n  \"Biology\",\r\n  \"Chemistry\",\r\n  \"Geography\",\r\n  \"Civics\",\r\n  \"History\",\r\n  \"Computer\",\r\n  \"English language\",\r\n  \"Kiswahili\",\r\n  \"Commerce\",\r\n  \"Economics\",\r\n  \"Advanced Mathematics\",\r\n  \"Basic Applied Mathematics\",\r\n  \"Accountancy\",\r\n  \"Agriculture\",\r\n];\r\n\r\nexport const advanceSubjects = [\r\n  \"Physics\",\r\n  \"Mathematics\",\r\n  \"Biology\",\r\n  \"Chemistry\",\r\n  \"Geography\",\r\n  \"Civics\",\r\n  \"History\",\r\n  \"Computer\",\r\n  \"English language\",\r\n  \"Kiswahili\",\r\n  \"Commerce\",\r\n  \"Economics\",\r\n  \"Advanced Mathematics\",\r\n  \"Basic Applied Mathematics\",\r\n  \"Accountancy\",\r\n  \"Agriculture\",\r\n];", "import React, { useState, useEffect, useCallback, useMemo } from \"react\";\nimport \"./index.css\";\nimport { motion, AnimatePresence } from \"framer-motion\";\nimport { getStudyMaterial } from \"../../../apicalls/study\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { HideLoading, ShowLoading } from \"../../../redux/loaderSlice\";\n\nimport {\n  FaPlayCircle,\n  FaVideo,\n  FaGraduationCap,\n  FaDownload,\n  FaEye,\n  FaTimes,\n  FaChevronDown,\n  FaSearch,\n  FaExpand,\n  FaCompress,\n} from \"react-icons/fa\";\nimport {\n  TbVideo,\n  TbSchool,\n  TbSearch,\n  TbFilter,\n  TbSortAscending,\n  TbDownload,\n  TbEye,\n  TbCalendar,\n  TbUser,\n  TbChevronDown as TbChevronDownIcon,\n  TbChevronUp,\n  TbX,\n  Tb<PERSON>lert<PERSON>riangle,\n  TbIn<PERSON><PERSON><PERSON><PERSON>,\n  Tb<PERSON><PERSON><PERSON>,\n} from \"react-icons/tb\";\nimport { primarySubjects, secondarySubjects, advanceSubjects } from \"../../../data/Subjects.jsx\";\n\nfunction VideoLessons() {\n  const { user } = useSelector((state) => state.user);\n  const dispatch = useDispatch();\n\n  // State management\n  const [videos, setVideos] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState(null);\n  const [selectedLevel, setSelectedLevel] = useState(user?.level || \"primary\");\n  const [selectedClass, setSelectedClass] = useState(user?.class || \"all\");\n  const [selectedSubject, setSelectedSubject] = useState(\"all\");\n  const [searchTerm, setSearchTerm] = useState(\"\");\n  const [sortBy, setSortBy] = useState(\"newest\");\n\n  // Video player state\n  const [currentVideoIndex, setCurrentVideoIndex] = useState(null);\n  const [showVideoIndices, setShowVideoIndices] = useState([]);\n  const [isVideoExpanded, setIsVideoExpanded] = useState(false);\n  const [videoError, setVideoError] = useState(null);\n  const [videoRef, setVideoRef] = useState(null);\n\n  // Comments state\n  const [comments, setComments] = useState([]);\n  const [newComment, setNewComment] = useState(\"\");\n  const [replyingTo, setReplyingTo] = useState(null);\n  const [replyText, setReplyText] = useState(\"\");\n\n  // Available classes based on level\n  const availableClasses = useMemo(() => {\n    if (selectedLevel === \"primary\") return [\"1\", \"2\", \"3\", \"4\", \"5\", \"6\", \"7\"];\n    if (selectedLevel === \"secondary\") return [\"1\", \"2\", \"3\", \"4\"];\n    if (selectedLevel === \"advance\") return [\"5\", \"6\"];\n    return [];\n  }, [selectedLevel]);\n\n  // Available subjects based on level\n  const availableSubjects = useMemo(() => {\n    if (selectedLevel === \"primary\") return primarySubjects;\n    if (selectedLevel === \"secondary\") return secondarySubjects;\n    if (selectedLevel === \"advance\") return advanceSubjects;\n    return [];\n  }, [selectedLevel]);\n\n  // Fetch videos\n  const fetchVideos = useCallback(async () => {\n    try {\n      setLoading(true);\n      setError(null);\n      dispatch(ShowLoading());\n\n      const filters = {\n        level: selectedLevel,\n        className: \"\", // Get all classes for the level\n        subject: \"\", // Get all subjects for the level\n        content: \"videos\"\n      };\n\n      const response = await getStudyMaterial(filters);\n\n      if (response?.data?.success) {\n        setVideos(response.data.data || []);\n      } else {\n        setError(response?.data?.message || \"Failed to fetch videos\");\n        setVideos([]);\n      }\n    } catch (error) {\n      console.error(\"❌ Error fetching videos:\", error);\n      setError(\"Failed to load videos. Please try again.\");\n      setVideos([]);\n    } finally {\n      setLoading(false);\n      dispatch(HideLoading());\n    }\n  }, [selectedLevel, dispatch]);\n\n  // Filter and sort videos\n  const filteredAndSortedVideos = useMemo(() => {\n\n\n    let filtered = videos;\n\n    // Apply level filter\n    filtered = filtered.filter(video => video.level === selectedLevel);\n\n    // Apply class filter\n    if (selectedClass !== \"all\") {\n      filtered = filtered.filter(video => {\n        // Check both className and class fields for compatibility\n        const videoClass = video.className || video.class;\n        return videoClass === selectedClass;\n      });\n    }\n\n    // Apply subject filter\n    if (selectedSubject !== \"all\") {\n      filtered = filtered.filter(video => video.subject === selectedSubject);\n    }\n\n    // Apply search filter\n    if (searchTerm.trim()) {\n      const searchLower = searchTerm.toLowerCase();\n      filtered = filtered.filter(video =>\n        video.title?.toLowerCase().includes(searchLower) ||\n        video.subject?.toLowerCase().includes(searchLower) ||\n        video.topic?.toLowerCase().includes(searchLower)\n      );\n    }\n\n    // Apply sorting\n    const sorted = [...filtered].sort((a, b) => {\n      switch (sortBy) {\n        case \"newest\":\n          return new Date(b.createdAt || 0) - new Date(a.createdAt || 0);\n        case \"oldest\":\n          return new Date(a.createdAt || 0) - new Date(b.createdAt || 0);\n        case \"title\":\n          return (a.title || \"\").localeCompare(b.title || \"\");\n        case \"subject\":\n          return (a.subject || \"\").localeCompare(b.subject || \"\");\n        default:\n          return 0;\n      }\n    });\n\n    console.log('✅ Final filtered videos:', sorted.length);\n    if (sorted.length > 0) {\n      console.log('📹 Sample filtered video:', sorted[0]);\n    }\n\n    return sorted;\n  }, [videos, searchTerm, sortBy, selectedLevel, selectedClass, selectedSubject]);\n\n  // Video handlers\n  const handleShowVideo = async (index) => {\n    const video = filteredAndSortedVideos[index];\n\n    setCurrentVideoIndex(index);\n    setShowVideoIndices([index]);\n    setIsVideoExpanded(false);\n    setVideoError(null);\n\n    // Get signed URL for S3 videos if needed\n    if (video?.videoUrl && (video.videoUrl.includes('amazonaws.com') || video.videoUrl.includes('s3.'))) {\n      try {\n        const signedUrl = await getSignedVideoUrl(video.videoUrl);\n        video.signedVideoUrl = signedUrl;\n      } catch (error) {\n        console.warn('Failed to get signed URL, using original URL');\n        video.signedVideoUrl = video.videoUrl;\n      }\n    }\n  };\n\n  const handleHideVideo = () => {\n    setShowVideoIndices([]);\n    setCurrentVideoIndex(null);\n    setIsVideoExpanded(false);\n    setVideoError(null);\n    if (videoRef) {\n      videoRef.pause();\n    }\n  };\n\n  const toggleVideoExpansion = () => {\n    setIsVideoExpanded(!isVideoExpanded);\n  };\n\n  // Get signed URL for S3 videos to ensure access\n  const getSignedVideoUrl = async (videoUrl) => {\n    if (!videoUrl) return videoUrl;\n\n    // For AWS S3 URLs, get signed URL from backend\n    if (videoUrl.includes('amazonaws.com') || videoUrl.includes('s3.')) {\n      try {\n        const response = await fetch(`http://localhost:5000/api/study/video-signed-url?videoUrl=${encodeURIComponent(videoUrl)}`, {\n          method: 'GET',\n          headers: {\n            'Content-Type': 'application/json',\n          }\n        });\n\n        if (!response.ok) {\n          throw new Error(`HTTP error! status: ${response.status}`);\n        }\n\n        const data = await response.json();\n\n        if (data.success && data.signedUrl) {\n          console.log('✅ Got signed URL for S3 video');\n          return data.signedUrl;\n        } else {\n          console.warn('⚠️ Invalid response from signed URL endpoint:', data);\n          return videoUrl;\n        }\n      } catch (error) {\n        console.error('❌ Error getting signed URL:', error);\n        return videoUrl;\n      }\n    }\n\n    return videoUrl;\n  };\n\n  // Get thumbnail URL\n  const getThumbnailUrl = (video) => {\n    if (video.thumbnail) {\n      return video.thumbnail;\n    }\n    \n    if (video.videoID && !video.videoID.includes('amazonaws.com')) {\n      let videoId = video.videoID;\n      if (videoId.includes('youtube.com') || videoId.includes('youtu.be')) {\n        const match = videoId.match(/(?:youtube\\.com\\/watch\\?v=|youtu\\.be\\/)([^&\\n?#]+)/);\n        videoId = match ? match[1] : videoId;\n      }\n      return `https://img.youtube.com/vi/${videoId}/maxresdefault.jpg`;\n    }\n    \n    return '/api/placeholder/400/225';\n  };\n\n  // Effects\n  useEffect(() => {\n    fetchVideos();\n  }, [fetchVideos]);\n\n  useEffect(() => {\n    if (user?.level) {\n      setSelectedLevel(user.level);\n    }\n    if (user?.class) {\n      setSelectedClass(user.class);\n    }\n  }, [user]);\n\n  // Clear search and refresh\n  const handleClearSearch = () => {\n    setSearchTerm(\"\");\n  };\n\n  const handleRefresh = () => {\n    // Only refresh data, don't clear filters or search\n    fetchVideos();\n  };\n\n  const handleClearAll = () => {\n    setSearchTerm(\"\");\n    setSelectedSubject(\"all\");\n    setSelectedClass(\"all\");\n    fetchVideos();\n  };\n\n  // Comment functions\n  const handleAddComment = () => {\n    if (newComment.trim()) {\n      const comment = {\n        id: Date.now(),\n        text: newComment,\n        author: user?.name || \"Anonymous\",\n        timestamp: new Date().toISOString(),\n        replies: []\n      };\n      setComments([...comments, comment]);\n      setNewComment(\"\");\n    }\n  };\n\n  const handleAddReply = (commentId) => {\n    if (replyText.trim()) {\n      const reply = {\n        id: Date.now(),\n        text: replyText,\n        author: user?.name || \"Anonymous\",\n        timestamp: new Date().toISOString()\n      };\n\n      setComments(comments.map(comment =>\n        comment.id === commentId\n          ? { ...comment, replies: [...comment.replies, reply] }\n          : comment\n      ));\n      setReplyText(\"\");\n      setReplyingTo(null);\n    }\n  };\n\n  return (\n    <div className=\"video-lessons-container\">\n      {/* Enhanced Header with Level Display */}\n      <div className=\"video-lessons-header\">\n        <div className=\"header-content\">\n          <div className=\"header-main\">\n            <div className=\"header-icon\">\n              <TbVideo />\n            </div>\n            <div className=\"header-text\">\n              <h1>Video Lessons</h1>\n              <p>Watch educational videos to enhance your learning</p>\n            </div>\n          </div>\n\n          {/* Level and Class Display */}\n          <div className=\"level-display\">\n            <div className=\"current-level\">\n              <span className=\"level-label\">Level:</span>\n              <span className=\"level-value\">{selectedLevel.charAt(0).toUpperCase() + selectedLevel.slice(1)}</span>\n            </div>\n            <div className=\"current-class\">\n              <span className=\"class-label\">Your Class:</span>\n              <span className=\"class-value\">\n                {user?.level === 'primary' ? `Class ${user?.class || 'N/A'}` :\n                 user?.level === 'secondary' ? `Form ${user?.class || 'N/A'}` :\n                 user?.level === 'advance' ? `Form ${user?.class || 'N/A'}` :\n                 'Not Set'}\n              </span>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <div className=\"video-lessons-content\">\n        {/* Enhanced Filters and Controls */}\n        <div className=\"video-controls\">\n          <div className=\"controls-row\">\n            {/* Class Filter */}\n            <div className=\"control-group\">\n              <label className=\"control-label\">\n                <TbFilter />\n                Filter by Class\n              </label>\n              <select\n                value={selectedClass}\n                onChange={(e) => setSelectedClass(e.target.value)}\n                className=\"control-select class-select\"\n              >\n                <option value=\"all\">All Classes</option>\n                {availableClasses.map((cls) => (\n                  <option key={cls} value={cls}>\n                    {selectedLevel === 'primary' ? `Class ${cls}` : `Form ${cls}`}\n                  </option>\n                ))}\n              </select>\n            </div>\n\n            {/* Subject Filter */}\n            <div className=\"control-group\">\n              <label className=\"control-label\">\n                <TbFilter />\n                Subject\n              </label>\n              <select\n                value={selectedSubject}\n                onChange={(e) => setSelectedSubject(e.target.value)}\n                className=\"control-select subject-select\"\n              >\n                <option value=\"all\">All Subjects</option>\n                {availableSubjects.map((subject) => (\n                  <option key={subject} value={subject}>\n                    {subject}\n                  </option>\n                ))}\n              </select>\n            </div>\n\n            {/* Sort */}\n            <div className=\"control-group\">\n              <label className=\"control-label\">\n                <TbSortAscending />\n                Sort\n              </label>\n              <select\n                value={sortBy}\n                onChange={(e) => setSortBy(e.target.value)}\n                className=\"control-select sort-select\"\n              >\n                <option value=\"newest\">Newest First</option>\n                <option value=\"oldest\">Oldest First</option>\n                <option value=\"title\">Title A-Z</option>\n                <option value=\"subject\">Subject A-Z</option>\n              </select>\n            </div>\n          </div>\n\n          {/* Search Row */}\n          <div className=\"search-row\">\n            <div className=\"search-container\">\n              <TbSearch className=\"search-icon\" />\n              <input\n                type=\"text\"\n                placeholder=\"Search videos by title, subject, or topic...\"\n                value={searchTerm}\n                onChange={(e) => setSearchTerm(e.target.value)}\n                className=\"search-input\"\n              />\n              {searchTerm && (\n                <button onClick={handleClearSearch} className=\"clear-search-btn\">\n                  <TbX />\n                  Clear Search\n                </button>\n              )}\n            </div>\n\n            <button onClick={handleRefresh} className=\"refresh-btn\">\n              <TbDownload />\n              Refresh All\n            </button>\n          </div>\n        </div>\n\n        {/* Loading State */}\n        {loading ? (\n          <div className=\"loading-state\">\n            <div className=\"loading-spinner\"></div>\n            <p>Loading videos...</p>\n          </div>\n        ) : error ? (\n          <div className=\"error-state\">\n            <TbAlertTriangle className=\"error-icon\" />\n            <h3>Error Loading Videos</h3>\n            <p>{error}</p>\n            <button onClick={fetchVideos} className=\"retry-btn\">\n              Try Again\n            </button>\n          </div>\n        ) : filteredAndSortedVideos.length > 0 ? (\n          <div className=\"videos-grid\">\n            {filteredAndSortedVideos.map((video, index) => (\n              <div key={index} className=\"video-card\" onClick={() => handleShowVideo(index)}>\n                <div className=\"video-card-thumbnail\">\n                  <img\n                    src={getThumbnailUrl(video)}\n                    alt={video.title}\n                    className=\"thumbnail-image\"\n                    onError={(e) => {\n                      // Fallback logic for failed thumbnails\n                      if (video.videoID && !video.videoID.includes('amazonaws.com')) {\n                        // For YouTube videos, try different quality thumbnails\n                        let videoId = video.videoID;\n                        if (videoId.includes('youtube.com') || videoId.includes('youtu.be')) {\n                          const match = videoId.match(/(?:youtube\\.com\\/watch\\?v=|youtu\\.be\\/)([^&\\n?#]+)/);\n                          videoId = match ? match[1] : videoId;\n                        }\n\n                        const fallbacks = [\n                          `https://img.youtube.com/vi/${videoId}/hqdefault.jpg`,\n                          `https://img.youtube.com/vi/${videoId}/mqdefault.jpg`,\n                          `https://img.youtube.com/vi/${videoId}/default.jpg`,\n                          '/api/placeholder/320/180'\n                        ];\n\n                        const currentSrc = e.target.src;\n                        const currentIndex = fallbacks.findIndex(url => currentSrc.includes(url.split('/').pop()));\n\n                        if (currentIndex < fallbacks.length - 1) {\n                          e.target.src = fallbacks[currentIndex + 1];\n                        }\n                      } else {\n                        e.target.src = '/api/placeholder/320/180';\n                      }\n                    }}\n                  />\n                  <div className=\"play-overlay\">\n                    <FaPlayCircle className=\"play-icon\" />\n                  </div>\n                  <div className=\"video-duration\">\n                    {video.duration || \"Video\"}\n                  </div>\n                  {video.subtitles && video.subtitles.length > 0 && (\n                    <div className=\"subtitle-badge\">\n                      <TbInfoCircle />\n                      CC\n                    </div>\n                  )}\n                </div>\n\n                <div className=\"video-card-content\">\n                  <h3 className=\"video-title\">{video.title}</h3>\n                  <div className=\"video-meta\">\n                    <span className=\"video-subject\">{video.subject}</span>\n                    <span className=\"video-class\">\n                      {selectedLevel === 'primary' ? `Class ${video.className || video.class}` : `Form ${video.className || video.class}`}\n                    </span>\n                  </div>\n                  <div className=\"video-tags\">\n                    {video.topic && <span className=\"topic-tag\">{video.topic}</span>}\n                    {video.sharedFromClass && video.sharedFromClass !== (video.className || video.class) && (\n                      <span className=\"shared-tag\">\n                        Shared from {selectedLevel === 'primary' ? `Class ${video.sharedFromClass}` : `Form ${video.sharedFromClass}`}\n                      </span>\n                    )}\n                  </div>\n                </div>\n              </div>\n            ))}\n          </div>\n        ) : (\n          <div className=\"empty-state\">\n            <FaGraduationCap className=\"empty-icon\" />\n            <h3>No Videos Found</h3>\n            <p>No video lessons are available for your current selection.</p>\n            <p className=\"suggestion\">Try selecting a different class or subject.</p>\n          </div>\n        )}\n      </div>\n\n      {/* Enhanced Video Display */}\n      {showVideoIndices.length > 0 && currentVideoIndex !== null && (\n        <div className={`video-overlay ${isVideoExpanded ? 'expanded' : ''}`} onClick={(e) => {\n          if (e.target === e.currentTarget) handleHideVideo();\n        }}>\n          <div className={`video-modal ${isVideoExpanded ? 'expanded' : ''}`}>\n            {(() => {\n              const video = filteredAndSortedVideos[currentVideoIndex];\n              if (!video) return <div>Video not found</div>;\n\n              return (\n                <div className=\"video-content\">\n                  <div className=\"video-header\">\n                    <div className=\"video-info\">\n                      <h3 className=\"video-title\">{video.title}</h3>\n                      <div className=\"video-meta\">\n                        <span className=\"video-subject\">{video.subject}</span>\n                        <span className=\"video-class\">Class {video.className}</span>\n                        {video.level && <span className=\"video-level\">{video.level}</span>}\n                      </div>\n                    </div>\n                    <div className=\"video-controls\">\n                      <button\n                        className=\"control-btn expand-btn\"\n                        onClick={toggleVideoExpansion}\n                        title={isVideoExpanded ? \"Exit fullscreen\" : \"Enter fullscreen\"}\n                      >\n                        {isVideoExpanded ? <FaCompress /> : <FaExpand />}\n                      </button>\n                      <button\n                        className=\"control-btn close-btn\"\n                        onClick={handleHideVideo}\n                        title=\"Close video\"\n                      >\n                        <FaTimes />\n                      </button>\n                    </div>\n                  </div>\n                  <div className=\"video-container\">\n                    {video.videoUrl ? (\n                      <div style={{ padding: '15px', background: '#000', borderRadius: '8px' }}>\n                          <video\n                            ref={(ref) => setVideoRef(ref)}\n                            controls\n                            autoPlay\n                            playsInline\n                            preload=\"metadata\"\n                            width=\"100%\"\n                            height=\"400\"\n                            poster={getThumbnailUrl(video)}\n                            style={{\n                              width: '100%',\n                              height: '400px',\n                              backgroundColor: '#000'\n                            }}\n                            onError={(e) => {\n                              setVideoError(`Failed to load video: ${video.title}. Please try refreshing the page.`);\n                            }}\n                            onCanPlay={() => {\n                              setVideoError(null);\n                            }}\n                            onLoadStart={() => {\n                              console.log('🎬 Video loading started');\n                            }}\n                            crossOrigin=\"anonymous\"\n                          >\n                            {/* Use signed URL if available, otherwise use original URL */}\n                            <source src={video.signedVideoUrl || video.videoUrl} type=\"video/mp4\" />\n\n                            {/* Add subtitle tracks if available */}\n                            {video.subtitles && video.subtitles.length > 0 && video.subtitles.map((subtitle, index) => (\n                              <track\n                                key={`${subtitle.language}-${index}`}\n                                kind=\"subtitles\"\n                                src={subtitle.url}\n                                srcLang={subtitle.language}\n                                label={subtitle.languageName}\n                                default={subtitle.isDefault || index === 0}\n                              />\n                            ))}\n\n                            Your browser does not support the video tag.\n                          </video>\n\n                          {/* Subtitle indicator */}\n                          {video.subtitles && video.subtitles.length > 0 && (\n                            <div className=\"subtitle-indicator\">\n                              <TbInfoCircle className=\"subtitle-icon\" />\n                              <span>Subtitles available in {video.subtitles.length} language(s)</span>\n                            </div>\n                          )}\n\n                          {/* Video error display */}\n                          {videoError && (\n                            <div className=\"video-error-overlay\">\n                              <div className=\"error-content\">\n                                <TbAlertTriangle className=\"error-icon\" />\n                                <p>{videoError}</p>\n                                <button onClick={() => setVideoError(null)} className=\"dismiss-error-btn\">\n                                  Dismiss\n                                </button>\n                              </div>\n                            </div>\n                          )}\n                        </div>\n                    ) : video.videoID ? (\n                      // Fallback to YouTube embed if no videoUrl\n                      <iframe\n                        src={`https://www.youtube.com/embed/${video.videoID}?autoplay=1&rel=0`}\n                        title={video.title}\n                        frameBorder=\"0\"\n                        allowFullScreen\n                        className=\"video-iframe\"\n                        onLoad={() => console.log('✅ YouTube iframe loaded')}\n                      ></iframe>\n                    ) : (\n                      <div className=\"video-error\">\n                        <div className=\"error-icon\">⚠️</div>\n                        <h3>Video Unavailable</h3>\n                        <p>{videoError || \"This video cannot be played at the moment.\"}</p>\n                        <div className=\"error-actions\">\n                          <a\n                            href={video.signedVideoUrl || video.videoUrl}\n                            target=\"_blank\"\n                            rel=\"noopener noreferrer\"\n                            className=\"external-link-btn\"\n                          >\n                            📱 Open in New Tab\n                          </a>\n                        </div>\n                      </div>\n                    )}\n                  </div>\n\n                  {/* Comments Section */}\n                  {!isVideoExpanded && (\n                    <div className=\"comments-section\">\n                      <h4 className=\"comments-title\">\n                        <TbInfoCircle />\n                        Discussion ({comments.length})\n                      </h4>\n\n                      {/* Add Comment */}\n                      <div className=\"add-comment\">\n                        <div className=\"comment-input-container\">\n                          <textarea\n                            value={newComment}\n                            onChange={(e) => setNewComment(e.target.value)}\n                            placeholder=\"Share your thoughts about this video...\"\n                            className=\"comment-input\"\n                            rows=\"3\"\n                          />\n                          <button\n                            onClick={handleAddComment}\n                            className=\"comment-submit-btn\"\n                            disabled={!newComment.trim()}\n                          >\n                            Post Comment\n                          </button>\n                        </div>\n                      </div>\n\n                      {/* Comments List */}\n                      <div className=\"comments-list\">\n                        {comments.length === 0 ? (\n                          <div className=\"no-comments\">\n                            <TbInfoCircle />\n                            <p>No comments yet. Be the first to share your thoughts!</p>\n                          </div>\n                        ) : (\n                          comments.map((comment) => (\n                            <div key={comment.id} className=\"comment\">\n                              <div className=\"comment-header\">\n                                <span className=\"comment-author\">{comment.author}</span>\n                                <span className=\"comment-time\">\n                                  {new Date(comment.timestamp).toLocaleDateString()}\n                                </span>\n                              </div>\n                              <div className=\"comment-text\">{comment.text}</div>\n                              <div className=\"comment-actions\">\n                                <button\n                                  onClick={() => setReplyingTo(comment.id)}\n                                  className=\"reply-btn\"\n                                >\n                                  Reply\n                                </button>\n                              </div>\n\n                              {/* Reply Input */}\n                              {replyingTo === comment.id && (\n                                <div className=\"reply-input-container\">\n                                  <textarea\n                                    value={replyText}\n                                    onChange={(e) => setReplyText(e.target.value)}\n                                    placeholder=\"Write a reply...\"\n                                    className=\"reply-input\"\n                                    rows=\"2\"\n                                  />\n                                  <div className=\"reply-actions\">\n                                    <button\n                                      onClick={() => handleAddReply(comment.id)}\n                                      className=\"reply-submit-btn\"\n                                      disabled={!replyText.trim()}\n                                    >\n                                      Reply\n                                    </button>\n                                    <button\n                                      onClick={() => {\n                                        setReplyingTo(null);\n                                        setReplyText(\"\");\n                                      }}\n                                      className=\"reply-cancel-btn\"\n                                    >\n                                      Cancel\n                                    </button>\n                                  </div>\n                                </div>\n                              )}\n\n                              {/* Replies */}\n                              {comment.replies.length > 0 && (\n                                <div className=\"replies\">\n                                  {comment.replies.map((reply) => (\n                                    <div key={reply.id} className=\"reply\">\n                                      <div className=\"reply-header\">\n                                        <span className=\"reply-author\">{reply.author}</span>\n                                        <span className=\"reply-time\">\n                                          {new Date(reply.timestamp).toLocaleDateString()}\n                                        </span>\n                                      </div>\n                                      <div className=\"reply-text\">{reply.text}</div>\n                                    </div>\n                                  ))}\n                                </div>\n                              )}\n                            </div>\n                          ))\n                        )}\n                      </div>\n                    </div>\n                  )}\n                </div>\n              );\n            })()}\n          </div>\n        </div>\n      )}\n    </div>\n  );\n}\n\nexport default VideoLessons;\n"], "names": ["default", "axiosInstance", "require", "getStudyMaterial", "async", "post", "filters", "error", "response", "getAllVideos", "get", "data", "_error$response", "success", "message", "addVideo", "videoData", "onUploadProgress", "arguments", "length", "undefined", "config", "FormData", "headers", "timeout", "progressEvent", "percentCompleted", "Math", "round", "loaded", "total", "addNote", "formData", "addPastPaper", "addBook", "updateVideo", "id", "put", "concat", "updateNote", "updatePastPaper", "updateBook", "deleteVideo", "delete", "deleteNote", "deletePastPaper", "deleteBook", "getAllStudyMaterials", "params", "URLSearchParams", "materialType", "append", "level", "className", "subject", "toString", "primarySubjects", "secondarySubjects", "advanceSubjects", "user", "useSelector", "state", "dispatch", "useDispatch", "videos", "setVideos", "useState", "loading", "setLoading", "setError", "selectedLevel", "setSelectedLevel", "selectedClass", "setSelectedClass", "class", "selectedSubject", "setSelectedSubject", "searchTerm", "setSearchTerm", "sortBy", "setSortBy", "currentVideoIndex", "setCurrentVideoIndex", "showVideoIndices", "setShowVideoIndices", "isVideoExpanded", "setIsVideoExpanded", "videoError", "setVideoError", "videoRef", "setVideoRef", "comments", "setComments", "newComment", "setNewComment", "replyingTo", "setReplyingTo", "replyText", "setReplyText", "availableClasses", "useMemo", "availableSubjects", "fetchVideos", "useCallback", "_response$data", "ShowLoading", "content", "_response$data2", "console", "HideLoading", "filteredAndSortedVideos", "filtered", "filter", "video", "trim", "searchLower", "toLowerCase", "_video$title", "_video$subject", "_video$topic", "title", "includes", "topic", "sorted", "sort", "a", "b", "Date", "createdAt", "localeCompare", "log", "handleHideVideo", "pause", "toggleVideoExpansion", "getSignedVideoUrl", "videoUrl", "fetch", "encodeURIComponent", "method", "ok", "Error", "status", "json", "signedUrl", "warn", "getThumbnailUrl", "thumbnail", "videoID", "videoId", "match", "useEffect", "handleAddComment", "comment", "now", "text", "author", "name", "timestamp", "toISOString", "replies", "_jsxs", "children", "_jsx", "TbVideo", "char<PERSON>t", "toUpperCase", "slice", "Tb<PERSON><PERSON>er", "value", "onChange", "e", "target", "map", "cls", "TbSortAscending", "TbSearch", "type", "placeholder", "onClick", "handleClearSearch", "TbX", "handleRefresh", "TbDownload", "TbAlertTriangle", "index", "signedVideoUrl", "handleShowVideo", "src", "alt", "onError", "fallbacks", "currentSrc", "currentIndex", "findIndex", "url", "split", "pop", "FaPlayCircle", "duration", "subtitles", "TbInfoCircle", "sharedFromClass", "FaGraduationCap", "currentTarget", "FaCompress", "FaExpand", "FaTimes", "style", "padding", "background", "borderRadius", "ref", "controls", "autoPlay", "playsInline", "preload", "width", "height", "poster", "backgroundColor", "onCanPlay", "onLoadStart", "crossOrigin", "subtitle", "kind", "srcLang", "language", "label", "languageName", "isDefault", "frameBorder", "allowFullScreen", "onLoad", "href", "rel", "rows", "disabled", "toLocaleDateString", "commentId", "reply", "_objectSpread", "handleAddReply"], "sourceRoot": ""}