{"version": 3, "file": "static/js/295.e0c4580e.chunk.js", "mappings": "sIAgBA,QAdA,SAAkBA,GAAa,IAAZ,MAAEC,GAAOD,EAC1B,MAAOE,EAAUC,IAAeC,EAAAA,EAAAA,WAAS,GAMzC,OALAC,EAAAA,EAAAA,YAAU,KACJC,OAAOC,WAAa,KACtBJ,GAAY,EACd,GACC,KAEDK,EAAAA,EAAAA,KAAA,OAAKC,UAAU,OAAMC,UACnBF,EAAAA,EAAAA,KAAA,MAAIC,UAAWP,EAAW,UAAY,GAAGQ,SAAET,KAGjD,C,qDCdO,MAAMU,EAAkB,CAC7B,cACA,yBACA,YACA,YACA,gBACA,UACA,WACA,aACA,gBACA,yBACA,kBACA,SACA,wBAGWC,EAAoB,CAC/B,UACA,cACA,UACA,YACA,YACA,SACA,UACA,WACA,mBACA,YACA,WACA,YACA,uBACA,4BACA,cACA,eAGWC,EAAkB,CAC7B,UACA,cACA,UACA,YACA,YACA,SACA,UACA,WACA,mBACA,YACA,WACA,YACA,uBACA,4BACA,cACA,c,iFChDF,Q,QAAkB,ECAlB,Q,QAAkB,E,qGCqUlB,QAlUA,SAAwBb,GAOpB,IAADc,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAA,IAPsB,yBACvBC,EAAwB,4BACxBC,EAA2B,YAC3BC,EAAW,OACXC,EAAM,iBACNC,EAAgB,oBAChBC,GACD3B,EACC,MAAM4B,GAAWC,EAAAA,EAAAA,OACVC,EAAcC,IAAmB3B,EAAAA,EAAAA,WAAS,IAC3B,OAAhBsB,QAAgB,IAAhBA,GAAAA,EAAkBM,KACbN,EAAiBM,KAEW,aAAjB,OAAhBN,QAAgB,IAAhBA,OAAgB,EAAhBA,EAAkBO,YACG,OAAhBP,QAAgB,IAAhBA,GAAAA,EAAkBQ,OAAyB,OAAhBR,QAAgB,IAAhBA,GAAAA,EAAkBS,SAAW,QAAU,MAEtC,uBAAjB,OAAhBT,QAAgB,IAAhBA,OAAgB,EAAhBA,EAAkBO,aAAuE,eAAjB,OAAhBP,QAAgB,IAAhBA,OAAgB,EAAhBA,EAAkBO,YACrE,OAGW,OAAhBP,QAAgB,IAAhBA,GAAAA,EAAkBU,cACmB,mBAAnB,OAAhBV,QAAgB,IAAhBA,OAAgB,EAAhBA,EAAkBI,cAAyC,QACxB,gBAAnB,OAAhBJ,QAAgB,IAAhBA,OAAgB,EAAhBA,EAAkBI,cAAsC,OACrD,MAEF,SAEFO,EAAWC,IAAgBlC,EAAAA,EAAAA,UAAS,MA4E3C,OACEI,EAAAA,EAAAA,KAAC+B,EAAAA,EAAK,CACJtC,MAAOyB,EAAmB,gBAAkB,eAC5Cc,KAAMlB,EACNmB,QAAQ,EACRC,SAAUA,KACRnB,GAA4B,GAC5BI,EAAoB,MACpBW,EAAa,KAAK,EAClB5B,UAEFiC,EAAAA,EAAAA,MAACC,EAAAA,EAAI,CACHC,SAtFWC,UACf,IACElB,GAASmB,EAAAA,EAAAA,OAGT,MAAMC,EAAW,IAAIC,SAsCrB,IAAIC,EAnCJF,EAASG,OAAO,OAAQC,EAAOC,MAC/BL,EAASG,OAAO,OAAQrB,GACxBkB,EAASG,OAAO,OAAQ1B,GACxBuB,EAASG,OAAO,QAASC,EAAOE,OAAS,WACzCN,EAASG,OAAO,aAAcC,EAAOG,YAAc,WAG9B,QAAjBzB,EACFkB,EAASG,OAAO,aAAc,WACJ,SAAjBrB,EACTkB,EAASG,OAAO,aAAc,qBACJ,UAAjBrB,GACTkB,EAASG,OAAO,aAAc,WAIhCH,EAASG,OAAO,gBAAiBC,EAAOI,eAGnB,QAAjB1B,GAA2C,UAAjBA,IAC5BkB,EAASG,OAAO,aAAcC,EAAOK,GACrCT,EAASG,OAAO,aAAcC,EAAOM,GACrCV,EAASG,OAAO,aAAcC,EAAOO,GACrCX,EAASG,OAAO,aAAcC,EAAOQ,GAErCZ,EAASG,OAAO,gBAAiBC,EAAOI,gBAItCnB,EACFW,EAASG,OAAO,QAASd,GACA,OAAhBX,QAAgB,IAAhBA,GAAAA,EAAkBQ,OAC3Bc,EAASG,OAAO,QAASzB,EAAiBQ,OAIxCR,GAEFsB,EAASG,OAAO,aAAczB,EAAiBmC,KAC/CX,QAAiBY,EAAAA,EAAAA,IAAiBd,IAElCE,QAAiBa,EAAAA,EAAAA,IAAkBf,GAGjCE,EAASc,SACXC,EAAAA,GAAQD,QAAQd,EAASe,SACzBzC,IACAD,GAA4B,GAC5Be,EAAa,OAEb2B,EAAAA,GAAQC,MAAMhB,EAASe,SAGzBtC,EAAoB,MACpBC,GAASuC,EAAAA,EAAAA,MACX,CAAE,MAAOD,GACPtC,GAASuC,EAAAA,EAAAA,OACTF,EAAAA,GAAQC,MAAMA,EAAMD,QACtB,GAqBIG,OAAO,WACPC,cAAe,CACbhB,KAAsB,OAAhB3B,QAAgB,IAAhBA,OAAgB,EAAhBA,EAAkB2B,KACxBG,eAA+B,OAAhB9B,QAAgB,IAAhBA,OAAgB,EAAhBA,EAAkB8B,iBAAiC,OAAhB9B,QAAgB,IAAhBA,OAAgB,EAAhBA,EAAkB4C,eACpEhB,OAAuB,OAAhB5B,QAAgB,IAAhBA,OAAgB,EAAhBA,EAAkB4B,QAAS,UAClCC,YAA4B,OAAhB7B,QAAgB,IAAhBA,OAAgB,EAAhBA,EAAkB6B,aAAc,UAC5CE,GAAmB,OAAhB/B,QAAgB,IAAhBA,GAAyB,QAATZ,EAAhBY,EAAkB6C,eAAO,IAAAzD,OAAT,EAAhBA,EAA2B2C,KAAqB,OAAhB/B,QAAgB,IAAhBA,GAAyB,QAATX,EAAhBW,EAAkB6C,eAAO,IAAAxD,OAAT,EAAhBA,EAA2ByD,IAAK,GACnEd,GAAmB,OAAhBhC,QAAgB,IAAhBA,GAAyB,QAATV,EAAhBU,EAAkB6C,eAAO,IAAAvD,OAAT,EAAhBA,EAA2B0C,KAAqB,OAAhBhC,QAAgB,IAAhBA,GAAyB,QAATT,EAAhBS,EAAkB6C,eAAO,IAAAtD,OAAT,EAAhBA,EAA2BwD,IAAK,GACnEd,GAAmB,OAAhBjC,QAAgB,IAAhBA,GAAyB,QAATR,EAAhBQ,EAAkB6C,eAAO,IAAArD,OAAT,EAAhBA,EAA2ByC,KAAqB,OAAhBjC,QAAgB,IAAhBA,GAAyB,QAATP,EAAhBO,EAAkB6C,eAAO,IAAApD,OAAT,EAAhBA,EAA2BuD,IAAK,GACnEd,GAAmB,OAAhBlC,QAAgB,IAAhBA,GAAyB,QAATN,EAAhBM,EAAkB6C,eAAO,IAAAnD,OAAT,EAAhBA,EAA2BwC,KAAqB,OAAhBlC,QAAgB,IAAhBA,GAAyB,QAATL,EAAhBK,EAAkB6C,eAAO,IAAAlD,OAAT,EAAhBA,EAA2BsD,IAAK,IACnEjE,SAAA,EAGe,OAAhBgB,QAAgB,IAAhBA,OAAgB,EAAhBA,EAAkBU,iBACjBO,EAAAA,EAAAA,MAAA,OAAKlC,UAAU,wDAAuDC,SAAA,EACpEiC,EAAAA,EAAAA,MAAA,OAAKlC,UAAU,0BAAyBC,SAAA,EACtCF,EAAAA,EAAAA,KAAA,QAAMC,UAAU,wBAAuBC,SAAC,kBACxCF,EAAAA,EAAAA,KAAA,QAAMC,UAAU,8BAA6BC,SAAC,8BAEhDF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,6BAA4BC,SAAC,6FAO3B,UAAjBoB,GAA+D,mBAAnB,OAAhBJ,QAAgB,IAAhBA,OAAgB,EAAhBA,EAAkBI,kBAC9B,OAAhBJ,QAAgB,IAAhBA,GAAAA,EAAkBQ,UAA0B,OAAhBR,QAAgB,IAAhBA,GAAAA,EAAkBS,YAAaE,IAC3DM,EAAAA,EAAAA,MAAA,OAAKlC,UAAU,0DAAyDC,SAAA,EACtEiC,EAAAA,EAAAA,MAAA,OAAKlC,UAAU,0BAAyBC,SAAA,EACtCF,EAAAA,EAAAA,KAAA,QAAMC,UAAU,yBAAwBC,SAAC,kBACzCF,EAAAA,EAAAA,KAAA,QAAMC,UAAU,+BAA8BC,SAAC,uBAEjDF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,8BAA6BC,SAAC,0GAK/CF,EAAAA,EAAAA,KAACoC,EAAAA,EAAKgC,KAAI,CAACvB,KAAK,OAAOwB,MAAM,WAAUnE,UACrCF,EAAAA,EAAAA,KAAA,SAAOwB,KAAK,YAIdxB,EAAAA,EAAAA,KAACoC,EAAAA,EAAKgC,KAAI,CAACvB,KAAK,eAAewB,MAAM,gBAAenE,UAClDiC,EAAAA,EAAAA,MAAA,OAAKlC,UAAU,YAAWC,SAAA,EACxBiC,EAAAA,EAAAA,MAAA,UACEmC,MAAOhD,EACPiD,SAAWC,GAAMjD,EAAgBiD,EAAEC,OAAOH,OAC1CrE,UAAU,+CAA8CC,SAAA,EAExDF,EAAAA,EAAAA,KAAA,UAAQsE,MAAM,MAAKpE,SAAC,2BACpBF,EAAAA,EAAAA,KAAA,UAAQsE,MAAM,OAAMpE,SAAC,uBACrBF,EAAAA,EAAAA,KAAA,UAAQsE,MAAM,QAAOpE,SAAC,6BAIP,OAAhBgB,QAAgB,IAAhBA,OAAgB,EAAhBA,EAAkBU,iBACjBO,EAAAA,EAAAA,MAAA,OAAKlC,UAAU,+CAA8CC,SAAA,CAAC,iBACzDF,EAAAA,EAAAA,KAAA,UAAAE,SAAQ,SAAa,2FAKV,UAAjBoB,GAA+D,mBAAnB,OAAhBJ,QAAgB,IAAhBA,OAAgB,EAAhBA,EAAkBI,eAAqE,aAAjB,OAAhBJ,QAAgB,IAAhBA,OAAgB,EAAhBA,EAAkBO,cACnGzB,EAAAA,EAAAA,KAAA,OAAKC,UAAU,iDAAgDC,SAAC,wFAQtEiC,EAAAA,EAAAA,MAAA,OAAKlC,UAAU,aAAYC,SAAA,EACzBF,EAAAA,EAAAA,KAACoC,EAAAA,EAAKgC,KAAI,CAACvB,KAAK,QAAQwB,MAAM,QAAOnE,UACnCF,EAAAA,EAAAA,KAAA,SAAOwB,KAAK,OAAOkD,YAAY,kCAEjC1E,EAAAA,EAAAA,KAACoC,EAAAA,EAAKgC,KAAI,CAACvB,KAAK,aAAawB,MAAM,cAAanE,UAC9CF,EAAAA,EAAAA,KAAA,SAAOwB,KAAK,OAAOkD,YAAY,sCAKjB,UAAjBpD,IACCa,EAAAA,EAAAA,MAACC,EAAAA,EAAKgC,KAAI,CAACvB,KAAK,QAAQwB,MAAM,iBAAgBnE,SAAA,EAC5CF,EAAAA,EAAAA,KAAA,SACEwB,KAAK,OACLmD,OAAO,UACPJ,SAAWC,GAAM1C,EAAa0C,EAAEC,OAAOG,MAAM,OAE/C5E,EAAAA,EAAAA,KAAA,SAAAE,SAAO,0CAKXF,EAAAA,EAAAA,KAACoC,EAAAA,EAAKgC,KAAI,CAACvB,KAAK,gBAAgBwB,MAAM,iBAAgBnE,UACpDF,EAAAA,EAAAA,KAAA,SACEwB,KAAK,OACLkD,YACmB,QAAjBpD,GAA2C,UAAjBA,EACtB,2CACA,gCAMS,QAAjBA,GAA2C,UAAjBA,KAC1Ba,EAAAA,EAAAA,MAAA0C,EAAAA,SAAA,CAAA3E,SAAA,EACEiC,EAAAA,EAAAA,MAAA,OAAKlC,UAAU,aAAYC,SAAA,EACzBF,EAAAA,EAAAA,KAACoC,EAAAA,EAAKgC,KAAI,CAACvB,KAAK,IAAIwB,MAAM,WAAUnE,UAClCF,EAAAA,EAAAA,KAAA,SAAOwB,KAAK,YAEdxB,EAAAA,EAAAA,KAACoC,EAAAA,EAAKgC,KAAI,CAACvB,KAAK,IAAIwB,MAAM,WAAUnE,UAClCF,EAAAA,EAAAA,KAAA,SAAOwB,KAAK,eAGhBW,EAAAA,EAAAA,MAAA,OAAKlC,UAAU,aAAYC,SAAA,EACzBF,EAAAA,EAAAA,KAACoC,EAAAA,EAAKgC,KAAI,CAACvB,KAAK,IAAIwB,MAAM,WAAUnE,UAClCF,EAAAA,EAAAA,KAAA,SAAOwB,KAAK,YAEdxB,EAAAA,EAAAA,KAACoC,EAAAA,EAAKgC,KAAI,CAACvB,KAAK,IAAIwB,MAAM,WAAUnE,UAClCF,EAAAA,EAAAA,KAAA,SAAOwB,KAAK,kBAOpBxB,EAAAA,EAAAA,KAACoC,EAAAA,EAAKgC,KAAI,CACRvB,KAAK,QACLwB,OACElC,EAAAA,EAAAA,MAAA,OAAKlC,UAAU,0BAAyBC,SAAA,EACtCF,EAAAA,EAAAA,KAAA,QAAAE,SAAM,oBACa,UAAjBoB,GAA+D,mBAAnB,OAAhBJ,QAAgB,IAAhBA,OAAgB,EAAhBA,EAAkBI,iBAC9CtB,EAAAA,EAAAA,KAAA,QAAMC,UAAU,eAAcC,SAAC,OAEhB,OAAhBgB,QAAgB,IAAhBA,OAAgB,EAAhBA,EAAkBU,kBAAkC,OAAhBV,QAAgB,IAAhBA,GAAAA,EAAkBQ,UAA0B,OAAhBR,QAAgB,IAAhBA,GAAAA,EAAkBS,YACjF3B,EAAAA,EAAAA,KAAA,QAAMC,UAAU,wBAAuBC,SAAC,mCAG7CA,UAEDiC,EAAAA,EAAAA,MAAA,OAAKlC,UAAU,YAAWC,SAAA,EAExBiC,EAAAA,EAAAA,MAAA,OAAKlC,UAAU,gGAA+FC,SAAA,EAC5GF,EAAAA,EAAAA,KAAA,SACEwB,KAAK,OACLmD,OAAO,UACPJ,SAtKaC,IACzB,MAAMM,EAAON,EAAEC,OAAOG,MAAM,GAC5B9C,EAAagD,GAAQ,KAAK,EAqKd7E,UAAU,YAEZD,EAAAA,EAAAA,KAAA,KAAGC,UAAU,6BAA4BC,SACrB,UAAjBoB,GAA+D,mBAAnB,OAAhBJ,QAAgB,IAAhBA,OAAgB,EAAhBA,EAAkBI,cAC3C,gDACA,kCAKPO,IACC7B,EAAAA,EAAAA,KAAA,OAAKC,UAAU,qDAAoDC,UACjEiC,EAAAA,EAAAA,MAAA,OAAKlC,UAAU,0BAAyBC,SAAA,EACtCF,EAAAA,EAAAA,KAAA,QAAMC,UAAU,iBAAgBC,SAAC,YACjCF,EAAAA,EAAAA,KAAA,QAAMC,UAAU,6BAA4BC,SAAC,yBAC7CF,EAAAA,EAAAA,KAAA,QAAMC,UAAU,iBAAgBC,SAAE2B,EAAUgB,cAMhC,OAAhB3B,QAAgB,IAAhBA,OAAgB,EAAhBA,EAAkBQ,SAAyB,OAAhBR,QAAgB,IAAhBA,OAAgB,EAAhBA,EAAkBS,aAAcE,IAC3DM,EAAAA,EAAAA,MAAA,OAAKlC,UAAU,YAAWC,SAAA,EACxBF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,oCAAmCC,SAAC,oBACjDF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,mCAAkCC,UAC/CF,EAAAA,EAAAA,KAAA,OACE+E,IAAK7D,EAAiBQ,OAASR,EAAiBS,SAChDqD,IAAI,mBACJ/E,UAAU,0DAGdD,EAAAA,EAAAA,KAAA,KAAGC,UAAU,wBAAuBC,SAAC,0DAO7CiC,EAAAA,EAAAA,MAAA,OAAKlC,UAAU,8BAA6BC,SAAA,EAC1CF,EAAAA,EAAAA,KAAA,UACEC,UAAU,uBACVuB,KAAK,SACLyD,QAASA,KACPlE,GAA4B,GAC5BI,EAAoB,MACpBW,EAAa,KAAK,EAClB5B,SACH,YAGDF,EAAAA,EAAAA,KAAA,UAAQC,UAAU,wBAAuBC,SAAC,gBAKpD,E,cCtTA,MAAM,QAAEgF,GAAYC,EAAAA,QA2apB,QAzaA,WAAwB,IAADC,EACrB,MAAMhE,GAAWC,EAAAA,EAAAA,MACXgE,GAAWC,EAAAA,EAAAA,OACVC,EAAUC,IAAe5F,EAAAA,EAAAA,UAAS,OAClC6F,EAAOC,IAAY9F,EAAAA,EAAAA,UAAS,KAC5BkB,EAA0BC,IAA+BnB,EAAAA,EAAAA,WAAS,IAClEsB,EAAkBC,IAAuBvB,EAAAA,EAAAA,UAAS,OAClD+F,EAAYC,IAAiBhG,EAAAA,EAAAA,UAAS,IACvCiG,GAASC,EAAAA,EAAAA,MAqDTC,EAAczD,UAClB,IAAK,IAAD0D,EAAAC,EACF7E,GAASmB,EAAAA,EAAAA,OAGT,MAAM2D,EAAOC,KAAKC,MAAMC,aAAaC,QAAQ,SAEvC5D,QAAiB6D,EAAAA,EAAAA,IAAY,CACjCtF,OAAQ4E,EAAOW,GACfC,OAAY,OAAJP,QAAI,IAAJA,OAAI,EAAJA,EAAM7C,MAGhBuC,EAAsB,OAARlD,QAAQ,IAARA,GAAc,QAANsD,EAARtD,EAAUgE,YAAI,IAAAV,OAAN,EAARA,EAAgBW,OAC9BjB,EAAiB,OAARhD,QAAQ,IAARA,GAAc,QAANuD,EAARvD,EAAUgE,YAAI,IAAAT,OAAN,EAARA,EAAgBR,OACzBrE,GAASuC,EAAAA,EAAAA,OACLjB,EAASc,QACXgC,EAAY9C,EAASgE,MAErBjD,EAAAA,GAAQC,MAAMhB,EAASe,QAE3B,CAAE,MAAOC,GACPtC,GAASuC,EAAAA,EAAAA,OACTF,EAAAA,GAAQC,MAAMA,EAAMD,QACtB,IAGF5D,EAAAA,EAAAA,YAAU,KACJgG,EAAOW,IACTT,GACF,GACC,IAEH,MAoBMa,EAAmB,CACvB,CACEnH,MAAO,WACPoH,UAAW,QAEb,CACEpH,MAAO,UACPoH,UAAW,UACXC,OAAQA,CAACC,EAAMC,IACH,OAANA,QAAM,IAANA,GAAAA,EAAQjD,SAAqC,kBAAnBiD,EAAOjD,SAAwBkD,OAAOC,KAAKF,EAAOjD,SAASoD,OAAS,EACzFF,OAAOC,KAAKF,EAAOjD,SAASqD,KAAKC,IACtClF,EAAAA,EAAAA,MAAA,OAAAjC,SAAA,CACGmH,EAAI,KAAGL,EAAOjD,QAAQsD,KADfA,MAKLrH,EAAAA,EAAAA,KAAA,OAAAE,SAAK,6CAIlB,CACET,MAAO,iBACPoH,UAAW,gBACXC,OAAQA,CAACC,EAAMC,KAEb,MAAMhE,EAAgBgE,EAAOhE,eAAiBgE,EAAOlD,cAErD,MAA0B,cAAtBkD,EAAOvF,YAA8C,SAAhBuF,EAAOxF,MAAmC,SAAhBwF,EAAOxF,MACjExB,EAAAA,EAAAA,KAAA,OAAAE,SAAM8C,KAGXb,EAAAA,EAAAA,MAAA,OAAAjC,SAAA,CACG8C,EAAc,KAAGgE,EAAOjD,SAAWiD,EAAOjD,QAAQf,GAAiBgE,EAAOjD,QAAQf,GAAiBA,IAG1G,GAGJ,CACEvD,MAAO,SACPoH,UAAW,SACXC,OAAQA,CAACC,EAAMC,KACb7E,EAAAA,EAAAA,MAAA,OAAKlC,UAAU,0BAAyBC,SAAA,CAC/B,OAAN8G,QAAM,IAANA,GAAAA,EAAQpF,eACP5B,EAAAA,EAAAA,KAAA,QAAMC,UAAU,gDAA+CC,SAAC,qBAIhEF,EAAAA,EAAAA,KAAA,QAAMC,UAAU,wBAAuBC,SAAC,aAElC,OAAN8G,QAAM,IAANA,OAAM,EAANA,EAAQtF,SAAe,OAANsF,QAAM,IAANA,OAAM,EAANA,EAAQrF,aACzB3B,EAAAA,EAAAA,KAAA,QAAMP,MAAM,YAAWS,SAAC,2BAKhC,CACET,MAAO,SACPoH,UAAW,SACXC,OAAQA,CAACC,EAAMC,KACb7E,EAAAA,EAAAA,MAAA,OAAKlC,UAAU,0BAAyBC,SAAA,EAEtCF,EAAAA,EAAAA,KAAA,KACEC,UAAU,kEACVR,MAAM,gBACNwF,QAASA,KACP9D,EAAoB6F,GACpBjG,GAA4B,EAAK,KAK9B,OAANiG,QAAM,IAANA,OAAM,EAANA,EAAQpF,kBAAwB,OAANoF,QAAM,IAANA,GAAAA,EAAQtF,UAAgB,OAANsF,QAAM,IAANA,GAAAA,EAAQrF,YACnD3B,EAAAA,EAAAA,KAAA,KACEC,UAAU,uEACVR,MAAM,2BACNwF,QAASA,KACP9D,EAAoB6F,GACpBjG,GAA4B,EAAK,KAMhC,OAANiG,QAAM,IAANA,OAAM,EAANA,EAAQpF,iBACP5B,EAAAA,EAAAA,KAAA,QACEC,UAAU,wBACVR,MAAM,wBAAuBS,SAC9B,mBAMK,OAAN8G,QAAM,IAANA,OAAM,EAANA,EAAQtF,SAAe,OAANsF,QAAM,IAANA,OAAM,EAANA,EAAQrF,aACzB3B,EAAAA,EAAAA,KAAA,QACEC,UAAU,yBACVR,MAAM,YAAWS,SAClB,wBAMHF,EAAAA,EAAAA,KAAA,KACEC,UAAU,oEACVR,MAAM,kBACNwF,QAASA,KA/HI3C,WACrB,IACElB,GAASmB,EAAAA,EAAAA,OACT,MAAMG,QAAiB4E,EAAAA,EAAAA,IAAmB,CACxCC,aACAtG,OAAQ4E,EAAOW,KAEjBpF,GAASuC,EAAAA,EAAAA,OACLjB,EAASc,SACXC,EAAAA,GAAQD,QAAQd,EAASe,SACzBsC,KAEAtC,EAAAA,GAAQC,MAAMhB,EAASe,QAE3B,CAAE,MAAOC,GACPtC,GAASuC,EAAAA,EAAAA,OACTF,EAAAA,GAAQC,MAAMA,EAAMD,QACtB,GA+GU+D,CAAeR,EAAO3D,IAAI,SAiBtC,OAJAoE,QAAQC,IAAI/B,EAAY,eAKtBxD,EAAAA,EAAAA,MAAA,OAAAjC,SAAA,EACEF,EAAAA,EAAAA,KAAC2H,EAAAA,EAAS,CAAClI,MAAOoG,EAAOW,GAAK,YAAc,cAC5CxG,EAAAA,EAAAA,KAAA,OAAKC,UAAU,aAEbsF,IAAaM,EAAOW,MACpBxG,EAAAA,EAAAA,KAACoC,EAAAA,EAAI,CAACwB,OAAO,WAAWvB,SAxObC,UACf,IAEE,IAAII,EAUJ,GAXAtB,GAASmB,EAAAA,EAAAA,OAIPG,EADEmD,EAAOW,SACQoB,EAAAA,EAAAA,KAAYC,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAC,CAAC,EAC1BjF,GAAM,IACT3B,OAAQ4E,EAAOW,YAGAsB,EAAAA,EAAAA,IAAQlF,GAEvBF,EAASc,QAAS,CAIpB,GAHAC,EAAAA,GAAQD,QAAQd,EAASe,UAGpBoC,EAAOW,GAAI,CAAC,IAADuB,EAAAC,EACdlI,OAAOmI,cAAc,IAAIC,YAAY,iBAAkB,CACrDC,OAAQ,CACNC,SAAUxF,EAAOC,KACjB4C,MAAO7C,EAAO6C,MACd4C,UAAWC,KAAKC,UAKpB,MAAMC,GAAyB,QAAbT,EAAArF,EAASgE,YAAI,IAAAqB,OAAA,EAAbA,EAAe1E,OAAoB,QAAjB2E,EAAItF,EAASgE,YAAI,IAAAsB,OAAA,EAAbA,EAAexB,IACvD,GAAIgC,EAGF,OAFApH,GAASuC,EAAAA,EAAAA,YACT0B,EAAS,qBAADoD,OAAsBD,GAGlC,CAGI3C,EAAOW,IACTT,GAEJ,MACEtC,EAAAA,GAAQC,MAAMhB,EAASe,SAEzBrC,GAASuC,EAAAA,EAAAA,MACX,CAAE,MAAOD,GACPtC,GAASuC,EAAAA,EAAAA,OACTF,EAAAA,GAAQC,MAAMA,EAAMD,QACtB,GA0LgDI,cAAe0B,EAASrF,UAClEiC,EAAAA,EAAAA,MAACgD,EAAAA,QAAI,CAACuD,iBAAiB,IAAGxI,SAAA,EACxBiC,EAAAA,EAAAA,MAAC+C,EAAO,CAACyD,IAAI,eAAczI,SAAA,EACzBiC,EAAAA,EAAAA,MAACyG,EAAG,CAACC,OAAQ,CAAC,GAAI,IAAI3I,SAAA,EACpBF,EAAAA,EAAAA,KAAC8I,EAAG,CAACC,KAAM,EAAE7I,UACXF,EAAAA,EAAAA,KAACoC,EAAAA,EAAKgC,KAAI,CAACC,MAAM,YAAYxB,KAAK,OAAM3C,UACtCF,EAAAA,EAAAA,KAAA,SAAOwB,KAAK,cAGhBxB,EAAAA,EAAAA,KAAC8I,EAAG,CAACC,KAAM,EAAE7I,UACXF,EAAAA,EAAAA,KAACoC,EAAAA,EAAKgC,KAAI,CAACC,MAAM,QAAQxB,KAAK,QAAO3C,UACnCF,EAAAA,EAAAA,KAAA,SAAOwB,KAAK,OAAOkD,YAAY,wDAGnC1E,EAAAA,EAAAA,KAAC8I,EAAG,CAACC,KAAM,EAAE7I,UACXF,EAAAA,EAAAA,KAACoC,EAAAA,EAAKgC,KAAI,CAACC,MAAM,0BAA0BxB,KAAK,WAAU3C,UACxDF,EAAAA,EAAAA,KAAA,SAAOwB,KAAK,gBAMhBxB,EAAAA,EAAAA,KAAC8I,EAAG,CAACC,KAAM,EAAE7I,UACXF,EAAAA,EAAAA,KAACoC,EAAAA,EAAKgC,KAAI,CAACvB,KAAK,QAAQwB,MAAM,QAAQ2E,aAAa,GAAE9I,UACnDiC,EAAAA,EAAAA,MAAA,UAAQmC,MAAOmB,EAAOlB,SAvCbC,IACzBkB,EAASlB,EAAEC,OAAOH,OAClBsB,EAAc,GAAG,EAqCiD1F,SAAA,EAChDF,EAAAA,EAAAA,KAAA,UAAQsE,MAAM,GAAG2E,UAAQ,EAAA/I,SAAE,kBAG3BF,EAAAA,EAAAA,KAAA,UAAQsE,MAAM,UAASpE,SAAC,aACxBF,EAAAA,EAAAA,KAAA,UAAQsE,MAAM,YAAWpE,SAAC,eAC1BF,EAAAA,EAAAA,KAAA,UAAQsE,MAAM,UAASpE,SAAC,oBAK9BF,EAAAA,EAAAA,KAAC8I,EAAG,CAACC,KAAM,EAAE7I,UACXF,EAAAA,EAAAA,KAACoC,EAAAA,EAAKgC,KAAI,CAACC,MAAM,WAAWxB,KAAK,WAAU3C,UACzCiC,EAAAA,EAAAA,MAAA,UAAQU,KAAK,GAAG2D,GAAG,GAAEtG,SAAA,EACnBF,EAAAA,EAAAA,KAAA,UAAQsE,MAAM,GAAEpE,SAAC,oBACQ,YAAxBuF,EAAMyD,gBACLlJ,EAAAA,EAAAA,KAAA6E,EAAAA,SAAA,CAAA3E,SACGC,EAAAA,GAAgBiH,KAAI,CAAC+B,EAASC,KAC7BpJ,EAAAA,EAAAA,KAAA,UAAoBsE,MAAO6E,EAAQjJ,SAChCiJ,GADUC,OAMM,cAAxB3D,EAAMyD,gBACLlJ,EAAAA,EAAAA,KAAA6E,EAAAA,SAAA,CAAA3E,SACGE,EAAAA,GAAkBgH,KAAI,CAAC+B,EAASC,KAC/BpJ,EAAAA,EAAAA,KAAA,UAAoBsE,MAAO6E,EAAQjJ,SAChCiJ,GADUC,OAMM,YAAxB3D,EAAMyD,gBACLlJ,EAAAA,EAAAA,KAAA6E,EAAAA,SAAA,CAAA3E,SACGG,EAAAA,GAAgB+G,KAAI,CAAC+B,EAASC,KAC7BpJ,EAAAA,EAAAA,KAAA,UAAoBsE,MAAO6E,EAAQjJ,SAChCiJ,GADUC,eAUzBpJ,EAAAA,EAAAA,KAAC8I,EAAG,CAACC,KAAM,EAAE7I,UAEXF,EAAAA,EAAAA,KAACoC,EAAAA,EAAKgC,KAAI,CAACvB,KAAK,QAAQwB,MAAM,QAAQ2E,aAAa,GAAGK,UAAQ,EAAAnJ,UAC5DiC,EAAAA,EAAAA,MAAA,UAAQmC,MAAOqB,EAAYpB,SAAWC,GAAMoB,EAAcpB,EAAEC,OAAOH,OAAOpE,SAAA,EACxEF,EAAAA,EAAAA,KAAA,UAAQsE,MAAM,GAAEpE,SAAG,iBAGM,YAAxBuF,EAAMyD,gBACL/G,EAAAA,EAAAA,MAAA0C,EAAAA,SAAA,CAAA3E,SAAA,EACEF,EAAAA,EAAAA,KAAA,UAAQsE,MAAM,IAAGpE,SAAC,OAClBF,EAAAA,EAAAA,KAAA,UAAQsE,MAAM,IAAGpE,SAAC,OAClBF,EAAAA,EAAAA,KAAA,UAAQsE,MAAM,IAAGpE,SAAC,OAClBF,EAAAA,EAAAA,KAAA,UAAQsE,MAAM,IAAGpE,SAAC,OAClBF,EAAAA,EAAAA,KAAA,UAAQsE,MAAM,IAAGpE,SAAC,OAClBF,EAAAA,EAAAA,KAAA,UAAQsE,MAAM,IAAGpE,SAAC,OAClBF,EAAAA,EAAAA,KAAA,UAAQsE,MAAM,IAAGpE,SAAC,SAGG,cAAxBuF,EAAMyD,gBACL/G,EAAAA,EAAAA,MAAA0C,EAAAA,SAAA,CAAA3E,SAAA,EACEF,EAAAA,EAAAA,KAAA,UAAQsE,MAAM,SAAQpE,SAAC,YACvBF,EAAAA,EAAAA,KAAA,UAAQsE,MAAM,SAAQpE,SAAC,YACvBF,EAAAA,EAAAA,KAAA,UAAQsE,MAAM,SAAQpE,SAAC,YACvBF,EAAAA,EAAAA,KAAA,UAAQsE,MAAM,SAAQpE,SAAC,cAGF,YAAxBuF,EAAMyD,gBACL/G,EAAAA,EAAAA,MAAA0C,EAAAA,SAAA,CAAA3E,SAAA,EACEF,EAAAA,EAAAA,KAAA,UAAQsE,MAAM,SAAQpE,SAAC,YACvBF,EAAAA,EAAAA,KAAA,UAAQsE,MAAM,SAAQpE,SAAC,sBAMjCF,EAAAA,EAAAA,KAAC8I,EAAG,CAACC,KAAM,EAAE7I,UACXF,EAAAA,EAAAA,KAACoC,EAAAA,EAAKgC,KAAI,CAACC,MAAM,cAAcxB,KAAK,aAAY3C,UAC9CF,EAAAA,EAAAA,KAAA,SAAOwB,KAAK,gBAGhBxB,EAAAA,EAAAA,KAAC8I,EAAG,CAACC,KAAM,EAAE7I,UACXF,EAAAA,EAAAA,KAACoC,EAAAA,EAAKgC,KAAI,CAACC,MAAM,gBAAgBxB,KAAK,eAAc3C,UAClDF,EAAAA,EAAAA,KAAA,SAAOwB,KAAK,mBAIlBW,EAAAA,EAAAA,MAAA,OAAKlC,UAAU,yBAAwBC,SAAA,EACrCF,EAAAA,EAAAA,KAAA,UACEC,UAAU,uBACVuB,KAAK,SACLyD,QAASA,IAAMI,EAAS,gBAAgBnF,SACzC,YAGDF,EAAAA,EAAAA,KAAA,UAAQC,UAAU,wBAAwBuB,KAAK,SAAQtB,SAAC,cA1H5B,KA+H/B2F,EAAOW,KACNrE,EAAAA,EAAAA,MAAC+C,EAAO,CAACyD,IAAI,YAAWzI,SAAA,EACtBiC,EAAAA,EAAAA,MAAA,OAAKlC,UAAU,yCAAwCC,SAAA,EACrDiC,EAAAA,EAAAA,MAAA,OAAAjC,SAAA,EACEF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,wBAAuBC,SAAC,oBACtCF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,gBAAeC,SAAC,+CAE/BF,EAAAA,EAAAA,KAAA,UACEC,UAAU,wBACVuB,KAAK,SACLyD,QAASA,IAAMlE,GAA4B,GAAMb,SAClD,qBAKHF,EAAAA,EAAAA,KAACsJ,EAAAA,EAAK,CACJC,QAAS3C,EACT4C,YAAoB,OAARjE,QAAQ,IAARA,OAAQ,EAARA,EAAUkE,YAAa,GACnCC,WAAY,CACVC,SAAU,GACVC,iBAAiB,EACjBC,iBAAiB,GAEnBC,OAAQ,CACNC,UAA2C,KAAxB,OAARxE,QAAQ,IAARA,GAAmB,QAAXH,EAARG,EAAUkE,iBAAS,IAAArE,OAAX,EAARA,EAAqB+B,QAC9B,iEACA,4BA1BqB,UAmCpCrG,IACCd,EAAAA,EAAAA,KAACgK,EAAe,CACdjJ,4BAA6BA,EAC7BD,yBAA0BA,EAC1BG,OAAQ4E,EAAOW,GACfxF,YAAa+E,EACb7E,iBAAkBA,EAClBC,oBAAqBA,MAO/B,C", "sources": ["components/PageTitle.js", "data/Subjects.jsx", "../node_modules/antd/es/row/index.js", "../node_modules/antd/es/col/index.js", "pages/admin/Exams/AddEditQuestion.js", "pages/admin/Exams/AddEditExam.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react'\r\n\r\nfunction PageTitle({ title }) {\r\n  const [isMobile, setIsMobile] = useState(false);\r\n  useEffect(() => {\r\n    if (window.innerWidth < 768) {\r\n      setIsMobile(true);\r\n    }\r\n  }, []);\r\n  return (\r\n    <div className='mt-2'>\r\n      <h1 className={isMobile ? 'text-lg' : ''}>{title}</h1>\r\n    </div>\r\n  )\r\n}\r\n\r\nexport default PageTitle", "export const primarySubjects = [\r\n  \"Mathematics\",\r\n  \"Science and Technology\",\r\n  \"Geography\",\r\n  \"Kiswahili\",\r\n  \"SocialStudies\",\r\n  \"English\",\r\n  \"Religion\",\r\n  \"Arithmetic\",\r\n  \"Sport and Art\",\r\n  \"Health and Environment\",\r\n  \"Civic and Moral\",\r\n  \"French\",\r\n  \"Historia ya Tanzania\",\r\n];\r\n\r\nexport const secondarySubjects = [\r\n  \"Physics\",\r\n  \"Mathematics\",\r\n  \"Biology\",\r\n  \"Chemistry\",\r\n  \"Geography\",\r\n  \"Civics\",\r\n  \"History\",\r\n  \"Computer\",\r\n  \"English language\",\r\n  \"Kiswahili\",\r\n  \"Commerce\",\r\n  \"Economics\",\r\n  \"Advanced Mathematics\",\r\n  \"Basic Applied Mathematics\",\r\n  \"Accountancy\",\r\n  \"Agriculture\",\r\n];\r\n\r\nexport const advanceSubjects = [\r\n  \"Physics\",\r\n  \"Mathematics\",\r\n  \"Biology\",\r\n  \"Chemistry\",\r\n  \"Geography\",\r\n  \"Civics\",\r\n  \"History\",\r\n  \"Computer\",\r\n  \"English language\",\r\n  \"Kiswahili\",\r\n  \"Commerce\",\r\n  \"Economics\",\r\n  \"Advanced Mathematics\",\r\n  \"Basic Applied Mathematics\",\r\n  \"Accountancy\",\r\n  \"Agriculture\",\r\n];", "'use client';\n\nimport { Row } from '../grid';\nexport default Row;", "'use client';\n\nimport { Col } from '../grid';\nexport default Col;", "import { Form, message, Modal } from \"antd\";\r\nimport React, { useState } from \"react\";\r\nimport { useDispatch } from \"react-redux\";\r\nimport { addQuestionToExam, editQuestionById } from \"../../../apicalls/exams\";\r\nimport { HideLoading, ShowLoading } from \"../../../redux/loaderSlice\";\r\n\r\nfunction AddEditQuestion({\r\n  showAddEditQuestionModal,\r\n  setShowAddEditQuestionModal,\r\n  refreshData,\r\n  examId,\r\n  selectedQuestion,\r\n  setSelectedQuestion,\r\n}) {\r\n  const dispatch = useDispatch();\r\n  const [questionType, setQuestionType] = useState(() => {\r\n    if (selectedQuestion?.type) {\r\n      return selectedQuestion.type;\r\n    }\r\n    if (selectedQuestion?.answerType === \"Options\") {\r\n      return selectedQuestion?.image || selectedQuestion?.imageUrl ? \"image\" : \"mcq\";\r\n    }\r\n    if (selectedQuestion?.answerType === \"Fill in the Blank\" || selectedQuestion?.answerType === \"Free Text\") {\r\n      return \"fill\";\r\n    }\r\n    // Default for AI-generated questions\r\n    if (selectedQuestion?.isAIGenerated) {\r\n      if (selectedQuestion?.questionType === \"picture_based\") return \"image\";\r\n      if (selectedQuestion?.questionType === \"fill_blank\") return \"fill\";\r\n      return \"mcq\";\r\n    }\r\n    return \"mcq\";\r\n  });\r\n  const [imageFile, setImageFile] = useState(null);\r\n\r\n  const onFinish = async (values) => {\r\n    try {\r\n      dispatch(ShowLoading());\r\n\r\n      // Prepare form data for file upload\r\n      const formData = new FormData();\r\n\r\n      // Append question details\r\n      formData.append('name', values.name);\r\n      formData.append('type', questionType);\r\n      formData.append('exam', examId);\r\n      formData.append('topic', values.topic || 'General');\r\n      formData.append('classLevel', values.classLevel || 'General');\r\n\r\n      // Set legacy answerType for backward compatibility\r\n      if (questionType === \"mcq\") {\r\n        formData.append('answerType', 'Options');\r\n      } else if (questionType === \"fill\") {\r\n        formData.append('answerType', 'Fill in the Blank');\r\n      } else if (questionType === \"image\") {\r\n        formData.append('answerType', 'Options'); // Image questions can have MCQ options\r\n      }\r\n\r\n      // Append correct answer - unified for all types\r\n      formData.append('correctAnswer', values.correctAnswer);\r\n\r\n      // Append options for MCQ and image questions\r\n      if (questionType === \"mcq\" || questionType === \"image\") {\r\n        formData.append('options[A]', values.A);\r\n        formData.append('options[B]', values.B);\r\n        formData.append('options[C]', values.C);\r\n        formData.append('options[D]', values.D);\r\n        // Legacy field for backward compatibility\r\n        formData.append('correctOption', values.correctAnswer);\r\n      }\r\n\r\n      // Append image if selected\r\n      if (imageFile) {\r\n        formData.append(\"image\", imageFile);\r\n      } else if (selectedQuestion?.image) {\r\n        formData.append(\"image\", selectedQuestion.image); // Retain existing image if editing\r\n      }\r\n\r\n      let response;\r\n      if (selectedQuestion) {\r\n        // For editing, include question ID\r\n        formData.append('questionId', selectedQuestion._id);\r\n        response = await editQuestionById(formData);\r\n      } else {\r\n        response = await addQuestionToExam(formData);\r\n      }\r\n\r\n      if (response.success) {\r\n        message.success(response.message);\r\n        refreshData();\r\n        setShowAddEditQuestionModal(false);\r\n        setImageFile(null);\r\n      } else {\r\n        message.error(response.message);\r\n      }\r\n\r\n      setSelectedQuestion(null);\r\n      dispatch(HideLoading());\r\n    } catch (error) {\r\n      dispatch(HideLoading());\r\n      message.error(error.message);\r\n    }\r\n  };\r\n\r\n  const handleImageChange = (e) => {\r\n    const file = e.target.files[0];\r\n    setImageFile(file || null);\r\n  };\r\n\r\n  return (\r\n    <Modal\r\n      title={selectedQuestion ? \"Edit Question\" : \"Add Question\"}\r\n      open={showAddEditQuestionModal}\r\n      footer={false}\r\n      onCancel={() => {\r\n        setShowAddEditQuestionModal(false);\r\n        setSelectedQuestion(null);\r\n        setImageFile(null);\r\n      }}\r\n    >\r\n      <Form\r\n        onFinish={onFinish}\r\n        layout=\"vertical\"\r\n        initialValues={{\r\n          name: selectedQuestion?.name,\r\n          correctAnswer: selectedQuestion?.correctAnswer || selectedQuestion?.correctOption,\r\n          topic: selectedQuestion?.topic || 'General',\r\n          classLevel: selectedQuestion?.classLevel || 'General',\r\n          A: selectedQuestion?.options?.A || selectedQuestion?.options?.a || '',\r\n          B: selectedQuestion?.options?.B || selectedQuestion?.options?.b || '',\r\n          C: selectedQuestion?.options?.C || selectedQuestion?.options?.c || '',\r\n          D: selectedQuestion?.options?.D || selectedQuestion?.options?.d || '',\r\n        }}\r\n      >\r\n        {/* AI-Generated Question Indicator */}\r\n        {selectedQuestion?.isAIGenerated && (\r\n          <div className=\"mb-4 p-3 bg-blue-50 border border-blue-200 rounded-lg\">\r\n            <div className=\"flex items-center gap-2\">\r\n              <span className=\"text-blue-600 text-lg\">🤖</span>\r\n              <span className=\"text-blue-800 font-semibold\">AI-Generated Question</span>\r\n            </div>\r\n            <p className=\"text-blue-700 text-sm mt-1\">\r\n              This question was generated by AI. You can edit all fields and add images as needed.\r\n            </p>\r\n          </div>\r\n        )}\r\n\r\n        {/* Missing Image Alert for Image-based Questions */}\r\n        {(questionType === \"image\" || selectedQuestion?.questionType === \"picture_based\") &&\r\n         !selectedQuestion?.image && !selectedQuestion?.imageUrl && !imageFile && (\r\n          <div className=\"mb-4 p-3 bg-amber-50 border border-amber-200 rounded-lg\">\r\n            <div className=\"flex items-center gap-2\">\r\n              <span className=\"text-amber-600 text-lg\">⚠️</span>\r\n              <span className=\"text-amber-800 font-semibold\">Image Required</span>\r\n            </div>\r\n            <p className=\"text-amber-700 text-sm mt-1\">\r\n              This is an image-based question but no image is currently attached. Please upload an image below.\r\n            </p>\r\n          </div>\r\n        )}\r\n        <Form.Item name=\"name\" label=\"Question\">\r\n          <input type=\"text\" />\r\n        </Form.Item>\r\n\r\n        {/* Question Type Selection */}\r\n        <Form.Item name=\"questionType\" label=\"Question Type\">\r\n          <div className=\"space-y-2\">\r\n            <select\r\n              value={questionType}\r\n              onChange={(e) => setQuestionType(e.target.value)}\r\n              className=\"w-full p-2 border border-gray-300 rounded-md\"\r\n            >\r\n              <option value=\"mcq\">Multiple Choice (MCQ)</option>\r\n              <option value=\"fill\">Fill in the Blank</option>\r\n              <option value=\"image\">Image-based Question</option>\r\n            </select>\r\n\r\n            {/* Helpful hints for AI-generated questions */}\r\n            {selectedQuestion?.isAIGenerated && (\r\n              <div className=\"text-sm text-blue-600 bg-blue-50 p-2 rounded\">\r\n                💡 <strong>Tip:</strong> Change to \"Image-based Question\" to add visual content to this AI-generated question\r\n              </div>\r\n            )}\r\n\r\n            {/* Type change notification */}\r\n            {questionType === \"image\" && selectedQuestion?.questionType !== \"picture_based\" && selectedQuestion?.answerType !== \"Options\" && (\r\n              <div className=\"text-sm text-green-600 bg-green-50 p-2 rounded\">\r\n                ✓ Converting to image-based question. You can now upload an image below.\r\n              </div>\r\n            )}\r\n          </div>\r\n        </Form.Item>\r\n\r\n        {/* Additional Fields */}\r\n        <div className=\"flex gap-3\">\r\n          <Form.Item name=\"topic\" label=\"Topic\">\r\n            <input type=\"text\" placeholder=\"e.g., Mathematics, Science\" />\r\n          </Form.Item>\r\n          <Form.Item name=\"classLevel\" label=\"Class Level\">\r\n            <input type=\"text\" placeholder=\"e.g., Primary 1, Secondary 2\" />\r\n          </Form.Item>\r\n        </div>\r\n\r\n        {/* Image Upload for Image-based Questions */}\r\n        {questionType === \"image\" && (\r\n          <Form.Item name=\"image\" label=\"Question Image\">\r\n            <input\r\n              type=\"file\"\r\n              accept=\"image/*\"\r\n              onChange={(e) => setImageFile(e.target.files[0])}\r\n            />\r\n            <small>Upload an image for this question</small>\r\n          </Form.Item>\r\n        )}\r\n\r\n        {/* Correct Answer - Universal Field */}\r\n        <Form.Item name=\"correctAnswer\" label=\"Correct Answer\">\r\n          <input\r\n            type=\"text\"\r\n            placeholder={\r\n              questionType === \"mcq\" || questionType === \"image\"\r\n                ? \"Enter the correct option (A, B, C, or D)\"\r\n                : \"Enter the correct answer\"\r\n            }\r\n          />\r\n        </Form.Item>\r\n\r\n        {/* Options for MCQ and Image Questions */}\r\n        {(questionType === \"mcq\" || questionType === \"image\") && (\r\n          <>\r\n            <div className=\"flex gap-3\">\r\n              <Form.Item name=\"A\" label=\"Option A\">\r\n                <input type=\"text\" />\r\n              </Form.Item>\r\n              <Form.Item name=\"B\" label=\"Option B\">\r\n                <input type=\"text\" />\r\n              </Form.Item>\r\n            </div>\r\n            <div className=\"flex gap-3\">\r\n              <Form.Item name=\"C\" label=\"Option C\">\r\n                <input type=\"text\" />\r\n              </Form.Item>\r\n              <Form.Item name=\"D\" label=\"Option D\">\r\n                <input type=\"text\" />\r\n              </Form.Item>\r\n            </div>\r\n          </>\r\n        )}\r\n\r\n        {/* Enhanced Image Upload Section */}\r\n        <Form.Item\r\n          name=\"image\"\r\n          label={\r\n            <div className=\"flex items-center gap-2\">\r\n              <span>Question Image</span>\r\n              {(questionType === \"image\" || selectedQuestion?.questionType === \"picture_based\") && (\r\n                <span className=\"text-red-500\">*</span>\r\n              )}\r\n              {selectedQuestion?.isAIGenerated && !selectedQuestion?.image && !selectedQuestion?.imageUrl && (\r\n                <span className=\"text-blue-600 text-sm\">(Add image for AI question)</span>\r\n              )}\r\n            </div>\r\n          }\r\n        >\r\n          <div className=\"space-y-3\">\r\n            {/* File Upload */}\r\n            <div className=\"border-2 border-dashed border-gray-300 rounded-lg p-4 hover:border-blue-400 transition-colors\">\r\n              <input\r\n                type=\"file\"\r\n                accept=\"image/*\"\r\n                onChange={handleImageChange}\r\n                className=\"w-full\"\r\n              />\r\n              <p className=\"text-sm text-gray-500 mt-2\">\r\n                {questionType === \"image\" || selectedQuestion?.questionType === \"picture_based\"\r\n                  ? \"Upload an image for this image-based question\"\r\n                  : \"Upload an image (optional)\"}\r\n              </p>\r\n            </div>\r\n\r\n            {/* Selected File Preview */}\r\n            {imageFile && (\r\n              <div className=\"p-3 bg-green-50 border border-green-200 rounded-lg\">\r\n                <div className=\"flex items-center gap-2\">\r\n                  <span className=\"text-green-600\">✓</span>\r\n                  <span className=\"text-green-800 font-medium\">New image selected:</span>\r\n                  <span className=\"text-green-700\">{imageFile.name}</span>\r\n                </div>\r\n              </div>\r\n            )}\r\n\r\n            {/* Current Image Display */}\r\n            {(selectedQuestion?.image || selectedQuestion?.imageUrl) && !imageFile && (\r\n              <div className=\"space-y-2\">\r\n                <p className=\"text-sm font-medium text-gray-700\">Current image:</p>\r\n                <div className=\"border rounded-lg p-2 bg-gray-50\">\r\n                  <img\r\n                    src={selectedQuestion.image || selectedQuestion.imageUrl}\r\n                    alt=\"Current question\"\r\n                    className=\"max-w-[300px] max-h-[200px] object-contain rounded\"\r\n                  />\r\n                </div>\r\n                <p className=\"text-sm text-gray-500\">Upload a new image above to replace this one</p>\r\n              </div>\r\n            )}\r\n          </div>\r\n        </Form.Item>\r\n\r\n        {/* Buttons */}\r\n        <div className=\"flex justify-end mt-2 gap-3\">\r\n          <button\r\n            className=\"primary-outlined-btn\"\r\n            type=\"button\"\r\n            onClick={() => {\r\n              setShowAddEditQuestionModal(false);\r\n              setSelectedQuestion(null);\r\n              setImageFile(null);\r\n            }}\r\n          >\r\n            Cancel\r\n          </button>\r\n          <button className=\"primary-contained-btn\">Save</button>\r\n        </div>\r\n      </Form>\r\n    </Modal>\r\n  );\r\n}\r\n\r\nexport default AddEditQuestion;", "import { Col, Form, message, Row, Select, Table } from \"antd\";\r\nimport React, { useEffect, useState } from \"react\";\r\nimport {\r\n  addExam,\r\n  deleteQuestionById,\r\n  editExamById,\r\n  getExamById,\r\n} from \"../../../apicalls/exams\";\r\nimport PageTitle from \"../../../components/PageTitle\";\r\nimport { useNavigate, useParams } from \"react-router-dom\";\r\n\r\nimport { useDispatch } from \"react-redux\";\r\nimport { HideLoading, ShowLoading } from \"../../../redux/loaderSlice\";\r\nimport { Tabs } from \"antd\";\r\nimport AddEditQuestion from \"./AddEditQuestion\";\r\nimport { primarySubjects, secondarySubjects, advanceSubjects } from \"../../../data/Subjects\";\r\nconst { TabPane } = Tabs;\r\n\r\nfunction AddEditExam() {\r\n  const dispatch = useDispatch();\r\n  const navigate = useNavigate();\r\n  const [examData, setExamData] = useState(null);\r\n  const [level, setLevel] = useState('');\r\n  const [showAddEditQuestionModal, setShowAddEditQuestionModal] = useState(false);\r\n  const [selectedQuestion, setSelectedQuestion] = useState(null);\r\n  const [classValue, setClassValue] = useState('');\r\n  const params = useParams();\r\n\r\n\r\n\r\n  const onFinish = async (values) => {\r\n    try {\r\n      dispatch(ShowLoading());\r\n      let response;\r\n\r\n      if (params.id) {\r\n        response = await editExamById({\r\n          ...values,\r\n          examId: params.id,\r\n        });\r\n      } else {\r\n        response = await addExam(values);\r\n      }\r\n      if (response.success) {\r\n        message.success(response.message);\r\n\r\n        // Dispatch event to notify other components about new exam creation\r\n        if (!params.id) { // Only for new exams, not edits\r\n          window.dispatchEvent(new CustomEvent('newExamCreated', {\r\n            detail: {\r\n              examName: values.name,\r\n              level: values.level,\r\n              timestamp: Date.now()\r\n            }\r\n          }));\r\n\r\n          // For new exams, navigate to edit mode so user can add questions\r\n          const newExamId = response.data?._id || response.data?.id;\r\n          if (newExamId) {\r\n            dispatch(HideLoading()); // Hide loading before navigation\r\n            navigate(`/admin/exams/edit/${newExamId}`);\r\n            return; // Don't continue with the rest of the function\r\n          }\r\n        }\r\n\r\n        // For edits, stay on the same page and refresh data\r\n        if (params.id) {\r\n          getExamData(); // Refresh the exam data\r\n        }\r\n      } else {\r\n        message.error(response.message);\r\n      }\r\n      dispatch(HideLoading());\r\n    } catch (error) {\r\n      dispatch(HideLoading());\r\n      message.error(error.message);\r\n    }\r\n  };\r\n\r\n  const getExamData = async () => {\r\n    try {\r\n      dispatch(ShowLoading());\r\n\r\n      // Get user data from localStorage for the API call\r\n      const user = JSON.parse(localStorage.getItem(\"user\"));\r\n\r\n      const response = await getExamById({\r\n        examId: params.id,\r\n        userId: user?._id, // Add userId for backend validation\r\n      });\r\n\r\n      setClassValue(response?.data?.class);\r\n      setLevel(response?.data?.level);\r\n      dispatch(HideLoading());\r\n      if (response.success) {\r\n        setExamData(response.data);\r\n      } else {\r\n        message.error(response.message);\r\n      }\r\n    } catch (error) {\r\n      dispatch(HideLoading());\r\n      message.error(error.message);\r\n    }\r\n  };\r\n\r\n  useEffect(() => {\r\n    if (params.id) {\r\n      getExamData();\r\n    }\r\n  }, []);\r\n\r\n  const deleteQuestion = async (questionId) => {\r\n    try {\r\n      dispatch(ShowLoading());\r\n      const response = await deleteQuestionById({\r\n        questionId,\r\n        examId: params.id\r\n      });\r\n      dispatch(HideLoading());\r\n      if (response.success) {\r\n        message.success(response.message);\r\n        getExamData();\r\n      } else {\r\n        message.error(response.message);\r\n      }\r\n    } catch (error) {\r\n      dispatch(HideLoading());\r\n      message.error(error.message);\r\n    }\r\n  };\r\n\r\n  const questionsColumns = [\r\n    {\r\n      title: \"Question\",\r\n      dataIndex: \"name\",\r\n    },\r\n    {\r\n      title: \"Options\",\r\n      dataIndex: \"options\",\r\n      render: (text, record) => {\r\n        if (record?.options && typeof record.options === 'object' && Object.keys(record.options).length > 0) {\r\n          return Object.keys(record.options).map((key) => (\r\n            <div key={key}>\r\n              {key}: {record.options[key]}\r\n            </div>\r\n          ));\r\n        } else {\r\n          return <div>No options available for this question.</div>;\r\n        }\r\n      },\r\n    },\r\n    {\r\n      title: \"Correct Answer\",\r\n      dataIndex: \"correctAnswer\",\r\n      render: (text, record) => {\r\n        // Handle both old (correctOption) and new (correctAnswer) formats\r\n        const correctAnswer = record.correctAnswer || record.correctOption;\r\n\r\n        if (record.answerType === \"Free Text\" || record.type === \"fill\" || record.type === \"text\") {\r\n          return <div>{correctAnswer}</div>;\r\n        } else {\r\n          return (\r\n            <div>\r\n              {correctAnswer}: {record.options && record.options[correctAnswer] ? record.options[correctAnswer] : correctAnswer}\r\n            </div>\r\n          );\r\n        }\r\n      },\r\n    },\r\n    {\r\n      title: \"Source\",\r\n      dataIndex: \"source\",\r\n      render: (text, record) => (\r\n        <div className=\"flex items-center gap-1\">\r\n          {record?.isAIGenerated ? (\r\n            <span className=\"flex items-center gap-1 text-blue-600 text-sm\">\r\n              🤖 AI\r\n            </span>\r\n          ) : (\r\n            <span className=\"text-gray-600 text-sm\">Manual</span>\r\n          )}\r\n          {(record?.image || record?.imageUrl) && (\r\n            <span title=\"Has Image\">🖼️</span>\r\n          )}\r\n        </div>\r\n      ),\r\n    },\r\n    {\r\n      title: \"Action\",\r\n      dataIndex: \"action\",\r\n      render: (text, record) => (\r\n        <div className=\"flex gap-2 items-center\">\r\n          {/* Edit Button */}\r\n          <i\r\n            className=\"ri-pencil-line cursor-pointer text-blue-600 hover:text-blue-800\"\r\n            title=\"Edit Question\"\r\n            onClick={() => {\r\n              setSelectedQuestion(record);\r\n              setShowAddEditQuestionModal(true);\r\n            }}\r\n          ></i>\r\n\r\n          {/* Add Image Button for AI-generated questions without images */}\r\n          {record?.isAIGenerated && !record?.image && !record?.imageUrl && (\r\n            <i\r\n              className=\"ri-image-add-line cursor-pointer text-green-600 hover:text-green-800\"\r\n              title=\"Add Image to AI Question\"\r\n              onClick={() => {\r\n                setSelectedQuestion(record);\r\n                setShowAddEditQuestionModal(true);\r\n              }}\r\n            ></i>\r\n          )}\r\n\r\n          {/* AI Generated Indicator */}\r\n          {record?.isAIGenerated && (\r\n            <span\r\n              className=\"text-blue-500 text-sm\"\r\n              title=\"AI Generated Question\"\r\n            >\r\n              🤖\r\n            </span>\r\n          )}\r\n\r\n          {/* Image Indicator */}\r\n          {(record?.image || record?.imageUrl) && (\r\n            <span\r\n              className=\"text-green-500 text-sm\"\r\n              title=\"Has Image\"\r\n            >\r\n              🖼️\r\n            </span>\r\n          )}\r\n\r\n          {/* Delete Button */}\r\n          <i\r\n            className=\"ri-delete-bin-line cursor-pointer text-red-600 hover:text-red-800\"\r\n            title=\"Delete Question\"\r\n            onClick={() => {\r\n              deleteQuestion(record._id);\r\n            }}\r\n          ></i>\r\n        </div>\r\n      ),\r\n    },\r\n  ];\r\n\r\n  const handleLevelChange = (e) => {\r\n    setLevel(e.target.value);\r\n    setClassValue(\"\"); // Reset class\r\n  };\r\n\r\n  console.log(classValue, \"classValue\")\r\n\r\n\r\n\r\n  return (\r\n    <div>\r\n      <PageTitle title={params.id ? \"Edit Exam\" : \"Add Exam\"} />\r\n      <div className=\"divider\"></div>\r\n\r\n      {(examData || !params.id) && (\r\n        <Form layout=\"vertical\" onFinish={onFinish} initialValues={examData}>\r\n          <Tabs defaultActiveKey=\"1\">\r\n            <TabPane tab=\"Exam Details\" key=\"1\">\r\n              <Row gutter={[10, 10]}>\r\n                <Col span={8}>\r\n                  <Form.Item label=\"Exam Name\" name=\"name\">\r\n                    <input type=\"text\" />\r\n                  </Form.Item>\r\n                </Col>\r\n                <Col span={8}>\r\n                  <Form.Item label=\"Topic\" name=\"topic\">\r\n                    <input type=\"text\" placeholder=\"Enter quiz topic (e.g., Algebra, Cell Biology)\" />\r\n                  </Form.Item>\r\n                </Col>\r\n                <Col span={8}>\r\n                  <Form.Item label=\"Exam Duration (Seconds)\" name=\"duration\">\r\n                    <input type=\"number\" />\r\n                  </Form.Item>\r\n                </Col>\r\n\r\n\r\n\r\n                <Col span={8}>\r\n                  <Form.Item name=\"level\" label=\"Level\" initialValue=\"\">\r\n                    <select value={level} onChange={handleLevelChange}   >\r\n                      <option value=\"\" disabled >\r\n                        Select Level\r\n                      </option>\r\n                      <option value=\"Primary\">Primary</option>\r\n                      <option value=\"Secondary\">Secondary</option>\r\n                      <option value=\"Advance\">Advance</option>\r\n                    </select>\r\n                  </Form.Item>\r\n                </Col>\r\n\r\n                <Col span={8}>\r\n                  <Form.Item label=\"Category\" name=\"category\">\r\n                    <select name=\"\" id=\"\">\r\n                      <option value=\"\">Select Category</option>\r\n                      {level.toLowerCase() === \"primary\" && (\r\n                        <>\r\n                          {primarySubjects.map((subject, index) => (\r\n                            <option key={index} value={subject}>\r\n                              {subject}\r\n                            </option>\r\n                          ))}\r\n                        </>\r\n                      )}\r\n                      {level.toLowerCase() === \"secondary\" && (\r\n                        <>\r\n                          {secondarySubjects.map((subject, index) => (\r\n                            <option key={index} value={subject}>\r\n                              {subject}\r\n                            </option>\r\n                          ))}\r\n                        </>\r\n                      )}\r\n                      {level.toLowerCase() === \"advance\" && (\r\n                        <>\r\n                          {advanceSubjects.map((subject, index) => (\r\n                            <option key={index} value={subject}>\r\n                              {subject}\r\n                            </option>\r\n                          ))}\r\n                        </>\r\n                      )}\r\n                    </select>\r\n                  </Form.Item>\r\n                </Col>\r\n\r\n                <Col span={8}>\r\n\r\n                  <Form.Item name=\"class\" label=\"Class\" initialValue=\"\" required>\r\n                    <select value={classValue} onChange={(e) => setClassValue(e.target.value)}>\r\n                      <option value=\"\"  >\r\n                        Select Class\r\n                      </option>\r\n                      {level.toLowerCase() === \"primary\" && (\r\n                        <>\r\n                          <option value=\"1\">1</option>\r\n                          <option value=\"2\">2</option>\r\n                          <option value=\"3\">3</option>\r\n                          <option value=\"4\">4</option>\r\n                          <option value=\"5\">5</option>\r\n                          <option value=\"6\">6</option>\r\n                          <option value=\"7\">7</option>\r\n                        </>\r\n                      )}\r\n                      {level.toLowerCase() === \"secondary\" && (\r\n                        <>\r\n                          <option value=\"Form-1\">Form-1</option>\r\n                          <option value=\"Form-2\">Form-2</option>\r\n                          <option value=\"Form-3\">Form-3</option>\r\n                          <option value=\"Form-4\">Form-4</option>\r\n                        </>\r\n                      )}\r\n                      {level.toLowerCase() === \"advance\" && (\r\n                        <>\r\n                          <option value=\"Form-5\">Form-5</option>\r\n                          <option value=\"Form-6\">Form-6</option>\r\n                        </>\r\n                      )}\r\n                    </select>\r\n                  </Form.Item>\r\n                </Col>\r\n                <Col span={8}>\r\n                  <Form.Item label=\"Total Marks\" name=\"totalMarks\">\r\n                    <input type=\"number\" />\r\n                  </Form.Item>\r\n                </Col>\r\n                <Col span={8}>\r\n                  <Form.Item label=\"Passing Marks\" name=\"passingMarks\">\r\n                    <input type=\"number\" />\r\n                  </Form.Item>\r\n                </Col>\r\n              </Row>\r\n              <div className=\"flex justify-end gap-2\">\r\n                <button\r\n                  className=\"primary-outlined-btn\"\r\n                  type=\"button\"\r\n                  onClick={() => navigate(\"/admin/exams\")}\r\n                >\r\n                  Cancel\r\n                </button>\r\n                <button className=\"primary-contained-btn\" type=\"submit\">\r\n                  Save\r\n                </button>\r\n              </div>\r\n            </TabPane>\r\n            {params.id && (\r\n              <TabPane tab=\"Questions\" key=\"2\">\r\n                <div className=\"flex justify-between items-center mb-4\">\r\n                  <div>\r\n                    <h3 className=\"text-lg font-semibold\">Exam Questions</h3>\r\n                    <p className=\"text-gray-600\">Add and manage questions for this exam</p>\r\n                  </div>\r\n                  <button\r\n                    className=\"primary-contained-btn\"\r\n                    type=\"button\"\r\n                    onClick={() => setShowAddEditQuestionModal(true)}\r\n                  >\r\n                    Add Question\r\n                  </button>\r\n                </div>\r\n\r\n                <Table\r\n                  columns={questionsColumns}\r\n                  dataSource={examData?.questions || []}\r\n                  pagination={{\r\n                    pageSize: 10,\r\n                    showSizeChanger: true,\r\n                    showQuickJumper: true,\r\n                  }}\r\n                  locale={{\r\n                    emptyText: examData?.questions?.length === 0 ?\r\n                      'No questions added yet. Click \"Add Question\" to add questions.' :\r\n                      'Loading questions...'\r\n                  }}\r\n                />\r\n              </TabPane>\r\n            )}\r\n          </Tabs>\r\n        </Form>\r\n      )}\r\n\r\n      {showAddEditQuestionModal && (\r\n        <AddEditQuestion\r\n          setShowAddEditQuestionModal={setShowAddEditQuestionModal}\r\n          showAddEditQuestionModal={showAddEditQuestionModal}\r\n          examId={params.id}\r\n          refreshData={getExamData}\r\n          selectedQuestion={selectedQuestion}\r\n          setSelectedQuestion={setSelectedQuestion}\r\n        />\r\n      )}\r\n\r\n\r\n    </div>\r\n  );\r\n}\r\n\r\nexport default AddEditExam;"], "names": ["_ref", "title", "isMobile", "setIsMobile", "useState", "useEffect", "window", "innerWidth", "_jsx", "className", "children", "primarySubjects", "secondarySubjects", "advanceSubjects", "_selectedQuestion$opt", "_selectedQuestion$opt2", "_selectedQuestion$opt3", "_selectedQuestion$opt4", "_selectedQuestion$opt5", "_selectedQuestion$opt6", "_selectedQuestion$opt7", "_selectedQuestion$opt8", "showAddEditQuestionModal", "setShowAddEditQuestionModal", "refreshData", "examId", "selectedQuestion", "setSelectedQuestion", "dispatch", "useDispatch", "questionType", "setQuestionType", "type", "answerType", "image", "imageUrl", "isAIGenerated", "imageFile", "setImageFile", "Modal", "open", "footer", "onCancel", "_jsxs", "Form", "onFinish", "async", "ShowLoading", "formData", "FormData", "response", "append", "values", "name", "topic", "classLevel", "<PERSON><PERSON><PERSON><PERSON>", "A", "B", "C", "D", "_id", "editQuestionById", "addQuestionToExam", "success", "message", "error", "HideLoading", "layout", "initialValues", "correctOption", "options", "a", "b", "c", "d", "<PERSON><PERSON>", "label", "value", "onChange", "e", "target", "placeholder", "accept", "files", "_Fragment", "file", "src", "alt", "onClick", "TabPane", "Tabs", "_examData$questions", "navigate", "useNavigate", "examData", "setExamData", "level", "setLevel", "classValue", "setClassValue", "params", "useParams", "getExamData", "_response$data3", "_response$data4", "user", "JSON", "parse", "localStorage", "getItem", "getExamById", "id", "userId", "data", "class", "questionsColumns", "dataIndex", "render", "text", "record", "Object", "keys", "length", "map", "key", "deleteQuestionById", "questionId", "deleteQuestion", "console", "log", "Page<PERSON><PERSON>le", "editExamById", "_objectSpread", "addExam", "_response$data", "_response$data2", "dispatchEvent", "CustomEvent", "detail", "examName", "timestamp", "Date", "now", "newExamId", "concat", "defaultActiveKey", "tab", "Row", "gutter", "Col", "span", "initialValue", "disabled", "toLowerCase", "subject", "index", "required", "Table", "columns", "dataSource", "questions", "pagination", "pageSize", "showSizeChanger", "showQuickJumper", "locale", "emptyText", "AddEditQuestion"], "sourceRoot": ""}