{"ast": null, "code": "import { useContext } from '@rc-component/context';\nimport * as React from 'react';\nimport Cell from \"../Cell\";\nimport TableContext from \"../context/TableContext\";\nimport devRenderTimes from \"../hooks/useRenderTimes\";\nfunction ExpandedRow(props) {\n  if (process.env.NODE_ENV !== 'production') {\n    devRenderTimes(props);\n  }\n  var prefixCls = props.prefixCls,\n    children = props.children,\n    Component = props.component,\n    cellComponent = props.cellComponent,\n    className = props.className,\n    expanded = props.expanded,\n    colSpan = props.colSpan,\n    isEmpty = props.isEmpty;\n  var _useContext = useContext(TableContext, ['scrollbarSize', 'fixHeader', 'fixColumn', 'componentWidth', 'horizonScroll']),\n    scrollbarSize = _useContext.scrollbarSize,\n    fixHeader = _useContext.fixHeader,\n    fixColumn = _useContext.fixColumn,\n    componentWidth = _useContext.componentWidth,\n    horizonScroll = _useContext.horizonScroll;\n\n  // Cache render node\n  var contentNode = children;\n  if (isEmpty ? horizonScroll : fixColumn) {\n    contentNode = /*#__PURE__*/React.createElement(\"div\", {\n      style: {\n        width: componentWidth - (fixHeader ? scrollbarSize : 0),\n        position: 'sticky',\n        left: 0,\n        overflow: 'hidden'\n      },\n      className: \"\".concat(prefixCls, \"-expanded-row-fixed\")\n    }, componentWidth !== 0 && contentNode);\n  }\n  return /*#__PURE__*/React.createElement(Component, {\n    className: className,\n    style: {\n      display: expanded ? null : 'none'\n    }\n  }, /*#__PURE__*/React.createElement(Cell, {\n    component: cellComponent,\n    prefixCls: prefixCls,\n    colSpan: colSpan\n  }, contentNode));\n}\nexport default ExpandedRow;", "map": {"version": 3, "names": ["useContext", "React", "Cell", "TableContext", "devRenderTimes", "ExpandedRow", "props", "process", "env", "NODE_ENV", "prefixCls", "children", "Component", "component", "cellComponent", "className", "expanded", "colSpan", "isEmpty", "_useContext", "scrollbarSize", "fixHeader", "fixColumn", "componentWidth", "horizonScroll", "contentNode", "createElement", "style", "width", "position", "left", "overflow", "concat", "display"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/node_modules/rc-table/es/Body/ExpandedRow.js"], "sourcesContent": ["import { useContext } from '@rc-component/context';\nimport * as React from 'react';\nimport Cell from \"../Cell\";\nimport TableContext from \"../context/TableContext\";\nimport devRenderTimes from \"../hooks/useRenderTimes\";\nfunction ExpandedRow(props) {\n  if (process.env.NODE_ENV !== 'production') {\n    devRenderTimes(props);\n  }\n  var prefixCls = props.prefixCls,\n    children = props.children,\n    Component = props.component,\n    cellComponent = props.cellComponent,\n    className = props.className,\n    expanded = props.expanded,\n    colSpan = props.colSpan,\n    isEmpty = props.isEmpty;\n  var _useContext = useContext(TableContext, ['scrollbarSize', 'fixHeader', 'fixColumn', 'componentWidth', 'horizonScroll']),\n    scrollbarSize = _useContext.scrollbarSize,\n    fixHeader = _useContext.fixHeader,\n    fixColumn = _useContext.fixColumn,\n    componentWidth = _useContext.componentWidth,\n    horizonScroll = _useContext.horizonScroll;\n\n  // Cache render node\n  var contentNode = children;\n  if (isEmpty ? horizonScroll : fixColumn) {\n    contentNode = /*#__PURE__*/React.createElement(\"div\", {\n      style: {\n        width: componentWidth - (fixHeader ? scrollbarSize : 0),\n        position: 'sticky',\n        left: 0,\n        overflow: 'hidden'\n      },\n      className: \"\".concat(prefixCls, \"-expanded-row-fixed\")\n    }, componentWidth !== 0 && contentNode);\n  }\n  return /*#__PURE__*/React.createElement(Component, {\n    className: className,\n    style: {\n      display: expanded ? null : 'none'\n    }\n  }, /*#__PURE__*/React.createElement(Cell, {\n    component: cellComponent,\n    prefixCls: prefixCls,\n    colSpan: colSpan\n  }, contentNode));\n}\nexport default ExpandedRow;"], "mappings": "AAAA,SAASA,UAAU,QAAQ,uBAAuB;AAClD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,IAAI,MAAM,SAAS;AAC1B,OAAOC,YAAY,MAAM,yBAAyB;AAClD,OAAOC,cAAc,MAAM,yBAAyB;AACpD,SAASC,WAAWA,CAACC,KAAK,EAAE;EAC1B,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzCL,cAAc,CAACE,KAAK,CAAC;EACvB;EACA,IAAII,SAAS,GAAGJ,KAAK,CAACI,SAAS;IAC7BC,QAAQ,GAAGL,KAAK,CAACK,QAAQ;IACzBC,SAAS,GAAGN,KAAK,CAACO,SAAS;IAC3BC,aAAa,GAAGR,KAAK,CAACQ,aAAa;IACnCC,SAAS,GAAGT,KAAK,CAACS,SAAS;IAC3BC,QAAQ,GAAGV,KAAK,CAACU,QAAQ;IACzBC,OAAO,GAAGX,KAAK,CAACW,OAAO;IACvBC,OAAO,GAAGZ,KAAK,CAACY,OAAO;EACzB,IAAIC,WAAW,GAAGnB,UAAU,CAACG,YAAY,EAAE,CAAC,eAAe,EAAE,WAAW,EAAE,WAAW,EAAE,gBAAgB,EAAE,eAAe,CAAC,CAAC;IACxHiB,aAAa,GAAGD,WAAW,CAACC,aAAa;IACzCC,SAAS,GAAGF,WAAW,CAACE,SAAS;IACjCC,SAAS,GAAGH,WAAW,CAACG,SAAS;IACjCC,cAAc,GAAGJ,WAAW,CAACI,cAAc;IAC3CC,aAAa,GAAGL,WAAW,CAACK,aAAa;;EAE3C;EACA,IAAIC,WAAW,GAAGd,QAAQ;EAC1B,IAAIO,OAAO,GAAGM,aAAa,GAAGF,SAAS,EAAE;IACvCG,WAAW,GAAG,aAAaxB,KAAK,CAACyB,aAAa,CAAC,KAAK,EAAE;MACpDC,KAAK,EAAE;QACLC,KAAK,EAAEL,cAAc,IAAIF,SAAS,GAAGD,aAAa,GAAG,CAAC,CAAC;QACvDS,QAAQ,EAAE,QAAQ;QAClBC,IAAI,EAAE,CAAC;QACPC,QAAQ,EAAE;MACZ,CAAC;MACDhB,SAAS,EAAE,EAAE,CAACiB,MAAM,CAACtB,SAAS,EAAE,qBAAqB;IACvD,CAAC,EAAEa,cAAc,KAAK,CAAC,IAAIE,WAAW,CAAC;EACzC;EACA,OAAO,aAAaxB,KAAK,CAACyB,aAAa,CAACd,SAAS,EAAE;IACjDG,SAAS,EAAEA,SAAS;IACpBY,KAAK,EAAE;MACLM,OAAO,EAAEjB,QAAQ,GAAG,IAAI,GAAG;IAC7B;EACF,CAAC,EAAE,aAAaf,KAAK,CAACyB,aAAa,CAACxB,IAAI,EAAE;IACxCW,SAAS,EAAEC,aAAa;IACxBJ,SAAS,EAAEA,SAAS;IACpBO,OAAO,EAAEA;EACX,CAAC,EAAEQ,WAAW,CAAC,CAAC;AAClB;AACA,eAAepB,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}