{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nvar _excluded = [\"component\"],\n  _excluded2 = [\"className\"],\n  _excluded3 = [\"className\"];\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport Item from './Item';\nimport { OverflowContext } from './context';\nvar InternalRawItem = function InternalRawItem(props, ref) {\n  var context = React.useContext(OverflowContext);\n  // Render directly when context not provided\n  if (!context) {\n    var _props$component = props.component,\n      Component = _props$component === void 0 ? 'div' : _props$component,\n      _restProps = _objectWithoutProperties(props, _excluded);\n    return /*#__PURE__*/React.createElement(Component, _extends({}, _restProps, {\n      ref: ref\n    }));\n  }\n  var contextClassName = context.className,\n    restContext = _objectWithoutProperties(context, _excluded2);\n  var className = props.className,\n    restProps = _objectWithoutProperties(props, _excluded3);\n  // Do not pass context to sub item to avoid multiple measure\n  return /*#__PURE__*/React.createElement(OverflowContext.Provider, {\n    value: null\n  }, /*#__PURE__*/React.createElement(Item, _extends({\n    ref: ref,\n    className: classNames(contextClassName, className)\n  }, restContext, restProps)));\n};\nvar RawItem = /*#__PURE__*/React.forwardRef(InternalRawItem);\nRawItem.displayName = 'RawItem';\nexport default RawItem;", "map": {"version": 3, "names": ["_extends", "_objectWithoutProperties", "_excluded", "_excluded2", "_excluded3", "React", "classNames", "<PERSON><PERSON>", "OverflowContext", "InternalRawItem", "props", "ref", "context", "useContext", "_props$component", "component", "Component", "_restProps", "createElement", "contextClassName", "className", "restContext", "restProps", "Provider", "value", "RawItem", "forwardRef", "displayName"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/node_modules/rc-overflow/es/RawItem.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nvar _excluded = [\"component\"],\n  _excluded2 = [\"className\"],\n  _excluded3 = [\"className\"];\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport Item from './Item';\nimport { OverflowContext } from './context';\nvar InternalRawItem = function InternalRawItem(props, ref) {\n  var context = React.useContext(OverflowContext);\n  // Render directly when context not provided\n  if (!context) {\n    var _props$component = props.component,\n      Component = _props$component === void 0 ? 'div' : _props$component,\n      _restProps = _objectWithoutProperties(props, _excluded);\n    return /*#__PURE__*/React.createElement(Component, _extends({}, _restProps, {\n      ref: ref\n    }));\n  }\n  var contextClassName = context.className,\n    restContext = _objectWithoutProperties(context, _excluded2);\n  var className = props.className,\n    restProps = _objectWithoutProperties(props, _excluded3);\n  // Do not pass context to sub item to avoid multiple measure\n  return /*#__PURE__*/React.createElement(OverflowContext.Provider, {\n    value: null\n  }, /*#__PURE__*/React.createElement(Item, _extends({\n    ref: ref,\n    className: classNames(contextClassName, className)\n  }, restContext, restProps)));\n};\nvar RawItem = /*#__PURE__*/React.forwardRef(InternalRawItem);\nRawItem.displayName = 'RawItem';\nexport default RawItem;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,wBAAwB,MAAM,oDAAoD;AACzF,IAAIC,SAAS,GAAG,CAAC,WAAW,CAAC;EAC3BC,UAAU,GAAG,CAAC,WAAW,CAAC;EAC1BC,UAAU,GAAG,CAAC,WAAW,CAAC;AAC5B,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAOC,IAAI,MAAM,QAAQ;AACzB,SAASC,eAAe,QAAQ,WAAW;AAC3C,IAAIC,eAAe,GAAG,SAASA,eAAeA,CAACC,KAAK,EAAEC,GAAG,EAAE;EACzD,IAAIC,OAAO,GAAGP,KAAK,CAACQ,UAAU,CAACL,eAAe,CAAC;EAC/C;EACA,IAAI,CAACI,OAAO,EAAE;IACZ,IAAIE,gBAAgB,GAAGJ,KAAK,CAACK,SAAS;MACpCC,SAAS,GAAGF,gBAAgB,KAAK,KAAK,CAAC,GAAG,KAAK,GAAGA,gBAAgB;MAClEG,UAAU,GAAGhB,wBAAwB,CAACS,KAAK,EAAER,SAAS,CAAC;IACzD,OAAO,aAAaG,KAAK,CAACa,aAAa,CAACF,SAAS,EAAEhB,QAAQ,CAAC,CAAC,CAAC,EAAEiB,UAAU,EAAE;MAC1EN,GAAG,EAAEA;IACP,CAAC,CAAC,CAAC;EACL;EACA,IAAIQ,gBAAgB,GAAGP,OAAO,CAACQ,SAAS;IACtCC,WAAW,GAAGpB,wBAAwB,CAACW,OAAO,EAAET,UAAU,CAAC;EAC7D,IAAIiB,SAAS,GAAGV,KAAK,CAACU,SAAS;IAC7BE,SAAS,GAAGrB,wBAAwB,CAACS,KAAK,EAAEN,UAAU,CAAC;EACzD;EACA,OAAO,aAAaC,KAAK,CAACa,aAAa,CAACV,eAAe,CAACe,QAAQ,EAAE;IAChEC,KAAK,EAAE;EACT,CAAC,EAAE,aAAanB,KAAK,CAACa,aAAa,CAACX,IAAI,EAAEP,QAAQ,CAAC;IACjDW,GAAG,EAAEA,GAAG;IACRS,SAAS,EAAEd,UAAU,CAACa,gBAAgB,EAAEC,SAAS;EACnD,CAAC,EAAEC,WAAW,EAAEC,SAAS,CAAC,CAAC,CAAC;AAC9B,CAAC;AACD,IAAIG,OAAO,GAAG,aAAapB,KAAK,CAACqB,UAAU,CAACjB,eAAe,CAAC;AAC5DgB,OAAO,CAACE,WAAW,GAAG,SAAS;AAC/B,eAAeF,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}