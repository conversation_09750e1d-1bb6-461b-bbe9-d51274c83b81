{"ast": null, "code": "import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { composeRef } from \"rc-util/es/ref\";\nimport { warning } from \"rc-util/es/warning\";\nvar Input = function Input(_ref, ref) {\n  var _inputNode2, _inputNode2$props;\n  var prefixCls = _ref.prefixCls,\n    id = _ref.id,\n    inputElement = _ref.inputElement,\n    disabled = _ref.disabled,\n    tabIndex = _ref.tabIndex,\n    autoFocus = _ref.autoFocus,\n    autoComplete = _ref.autoComplete,\n    editable = _ref.editable,\n    activeDescendantId = _ref.activeDescendantId,\n    value = _ref.value,\n    maxLength = _ref.maxLength,\n    _onKeyDown = _ref.onKeyDown,\n    _onMouseDown = _ref.onMouseDown,\n    _onChange = _ref.onChange,\n    onPaste = _ref.onPaste,\n    _onCompositionStart = _ref.onCompositionStart,\n    _onCompositionEnd = _ref.onCompositionEnd,\n    open = _ref.open,\n    attrs = _ref.attrs;\n  var inputNode = inputElement || /*#__PURE__*/React.createElement(\"input\", null);\n  var _inputNode = inputNode,\n    originRef = _inputNode.ref,\n    originProps = _inputNode.props;\n  var onOriginKeyDown = originProps.onKeyDown,\n    onOriginChange = originProps.onChange,\n    onOriginMouseDown = originProps.onMouseDown,\n    onOriginCompositionStart = originProps.onCompositionStart,\n    onOriginCompositionEnd = originProps.onCompositionEnd,\n    style = originProps.style;\n  warning(!('maxLength' in inputNode.props), \"Passing 'maxLength' to input element directly may not work because input in BaseSelect is controlled.\");\n  inputNode = /*#__PURE__*/React.cloneElement(inputNode, _objectSpread(_objectSpread(_objectSpread({\n    type: 'search'\n  }, originProps), {}, {\n    // Override over origin props\n    id: id,\n    ref: composeRef(ref, originRef),\n    disabled: disabled,\n    tabIndex: tabIndex,\n    autoComplete: autoComplete || 'off',\n    autoFocus: autoFocus,\n    className: classNames(\"\".concat(prefixCls, \"-selection-search-input\"), (_inputNode2 = inputNode) === null || _inputNode2 === void 0 ? void 0 : (_inputNode2$props = _inputNode2.props) === null || _inputNode2$props === void 0 ? void 0 : _inputNode2$props.className),\n    role: 'combobox',\n    'aria-label': 'Search',\n    'aria-expanded': open,\n    'aria-haspopup': 'listbox',\n    'aria-owns': \"\".concat(id, \"_list\"),\n    'aria-autocomplete': 'list',\n    'aria-controls': \"\".concat(id, \"_list\"),\n    'aria-activedescendant': open ? activeDescendantId : undefined\n  }, attrs), {}, {\n    value: editable ? value : '',\n    maxLength: maxLength,\n    readOnly: !editable,\n    unselectable: !editable ? 'on' : null,\n    style: _objectSpread(_objectSpread({}, style), {}, {\n      opacity: editable ? null : 0\n    }),\n    onKeyDown: function onKeyDown(event) {\n      _onKeyDown(event);\n      if (onOriginKeyDown) {\n        onOriginKeyDown(event);\n      }\n    },\n    onMouseDown: function onMouseDown(event) {\n      _onMouseDown(event);\n      if (onOriginMouseDown) {\n        onOriginMouseDown(event);\n      }\n    },\n    onChange: function onChange(event) {\n      _onChange(event);\n      if (onOriginChange) {\n        onOriginChange(event);\n      }\n    },\n    onCompositionStart: function onCompositionStart(event) {\n      _onCompositionStart(event);\n      if (onOriginCompositionStart) {\n        onOriginCompositionStart(event);\n      }\n    },\n    onCompositionEnd: function onCompositionEnd(event) {\n      _onCompositionEnd(event);\n      if (onOriginCompositionEnd) {\n        onOriginCompositionEnd(event);\n      }\n    },\n    onPaste: onPaste\n  }));\n  return inputNode;\n};\nvar RefInput = /*#__PURE__*/React.forwardRef(Input);\nRefInput.displayName = 'Input';\nexport default RefInput;", "map": {"version": 3, "names": ["_objectSpread", "React", "classNames", "composeRef", "warning", "Input", "_ref", "ref", "_inputNode2", "_inputNode2$props", "prefixCls", "id", "inputElement", "disabled", "tabIndex", "autoFocus", "autoComplete", "editable", "activeDescendantId", "value", "max<PERSON><PERSON><PERSON>", "_onKeyDown", "onKeyDown", "_onMouseDown", "onMouseDown", "_onChange", "onChange", "onPaste", "_onCompositionStart", "onCompositionStart", "_onCompositionEnd", "onCompositionEnd", "open", "attrs", "inputNode", "createElement", "_inputNode", "originRef", "originProps", "props", "onOriginKeyDown", "onOriginChange", "onOriginMouseDown", "onOriginCompositionStart", "onOriginCompositionEnd", "style", "cloneElement", "type", "className", "concat", "role", "undefined", "readOnly", "unselectable", "opacity", "event", "RefInput", "forwardRef", "displayName"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/node_modules/rc-select/es/Selector/Input.js"], "sourcesContent": ["import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { composeRef } from \"rc-util/es/ref\";\nimport { warning } from \"rc-util/es/warning\";\nvar Input = function Input(_ref, ref) {\n  var _inputNode2, _inputNode2$props;\n  var prefixCls = _ref.prefixCls,\n    id = _ref.id,\n    inputElement = _ref.inputElement,\n    disabled = _ref.disabled,\n    tabIndex = _ref.tabIndex,\n    autoFocus = _ref.autoFocus,\n    autoComplete = _ref.autoComplete,\n    editable = _ref.editable,\n    activeDescendantId = _ref.activeDescendantId,\n    value = _ref.value,\n    maxLength = _ref.maxLength,\n    _onKeyDown = _ref.onKeyDown,\n    _onMouseDown = _ref.onMouseDown,\n    _onChange = _ref.onChange,\n    onPaste = _ref.onPaste,\n    _onCompositionStart = _ref.onCompositionStart,\n    _onCompositionEnd = _ref.onCompositionEnd,\n    open = _ref.open,\n    attrs = _ref.attrs;\n  var inputNode = inputElement || /*#__PURE__*/React.createElement(\"input\", null);\n  var _inputNode = inputNode,\n    originRef = _inputNode.ref,\n    originProps = _inputNode.props;\n  var onOriginKeyDown = originProps.onKeyDown,\n    onOriginChange = originProps.onChange,\n    onOriginMouseDown = originProps.onMouseDown,\n    onOriginCompositionStart = originProps.onCompositionStart,\n    onOriginCompositionEnd = originProps.onCompositionEnd,\n    style = originProps.style;\n  warning(!('maxLength' in inputNode.props), \"Passing 'maxLength' to input element directly may not work because input in BaseSelect is controlled.\");\n  inputNode = /*#__PURE__*/React.cloneElement(inputNode, _objectSpread(_objectSpread(_objectSpread({\n    type: 'search'\n  }, originProps), {}, {\n    // Override over origin props\n    id: id,\n    ref: composeRef(ref, originRef),\n    disabled: disabled,\n    tabIndex: tabIndex,\n    autoComplete: autoComplete || 'off',\n    autoFocus: autoFocus,\n    className: classNames(\"\".concat(prefixCls, \"-selection-search-input\"), (_inputNode2 = inputNode) === null || _inputNode2 === void 0 ? void 0 : (_inputNode2$props = _inputNode2.props) === null || _inputNode2$props === void 0 ? void 0 : _inputNode2$props.className),\n    role: 'combobox',\n    'aria-label': 'Search',\n    'aria-expanded': open,\n    'aria-haspopup': 'listbox',\n    'aria-owns': \"\".concat(id, \"_list\"),\n    'aria-autocomplete': 'list',\n    'aria-controls': \"\".concat(id, \"_list\"),\n    'aria-activedescendant': open ? activeDescendantId : undefined\n  }, attrs), {}, {\n    value: editable ? value : '',\n    maxLength: maxLength,\n    readOnly: !editable,\n    unselectable: !editable ? 'on' : null,\n    style: _objectSpread(_objectSpread({}, style), {}, {\n      opacity: editable ? null : 0\n    }),\n    onKeyDown: function onKeyDown(event) {\n      _onKeyDown(event);\n      if (onOriginKeyDown) {\n        onOriginKeyDown(event);\n      }\n    },\n    onMouseDown: function onMouseDown(event) {\n      _onMouseDown(event);\n      if (onOriginMouseDown) {\n        onOriginMouseDown(event);\n      }\n    },\n    onChange: function onChange(event) {\n      _onChange(event);\n      if (onOriginChange) {\n        onOriginChange(event);\n      }\n    },\n    onCompositionStart: function onCompositionStart(event) {\n      _onCompositionStart(event);\n      if (onOriginCompositionStart) {\n        onOriginCompositionStart(event);\n      }\n    },\n    onCompositionEnd: function onCompositionEnd(event) {\n      _onCompositionEnd(event);\n      if (onOriginCompositionEnd) {\n        onOriginCompositionEnd(event);\n      }\n    },\n    onPaste: onPaste\n  }));\n  return inputNode;\n};\nvar RefInput = /*#__PURE__*/React.forwardRef(Input);\nRefInput.displayName = 'Input';\nexport default RefInput;"], "mappings": "AAAA,OAAOA,aAAa,MAAM,0CAA0C;AACpE,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,UAAU,MAAM,YAAY;AACnC,SAASC,UAAU,QAAQ,gBAAgB;AAC3C,SAASC,OAAO,QAAQ,oBAAoB;AAC5C,IAAIC,KAAK,GAAG,SAASA,KAAKA,CAACC,IAAI,EAAEC,GAAG,EAAE;EACpC,IAAIC,WAAW,EAAEC,iBAAiB;EAClC,IAAIC,SAAS,GAAGJ,IAAI,CAACI,SAAS;IAC5BC,EAAE,GAAGL,IAAI,CAACK,EAAE;IACZC,YAAY,GAAGN,IAAI,CAACM,YAAY;IAChCC,QAAQ,GAAGP,IAAI,CAACO,QAAQ;IACxBC,QAAQ,GAAGR,IAAI,CAACQ,QAAQ;IACxBC,SAAS,GAAGT,IAAI,CAACS,SAAS;IAC1BC,YAAY,GAAGV,IAAI,CAACU,YAAY;IAChCC,QAAQ,GAAGX,IAAI,CAACW,QAAQ;IACxBC,kBAAkB,GAAGZ,IAAI,CAACY,kBAAkB;IAC5CC,KAAK,GAAGb,IAAI,CAACa,KAAK;IAClBC,SAAS,GAAGd,IAAI,CAACc,SAAS;IAC1BC,UAAU,GAAGf,IAAI,CAACgB,SAAS;IAC3BC,YAAY,GAAGjB,IAAI,CAACkB,WAAW;IAC/BC,SAAS,GAAGnB,IAAI,CAACoB,QAAQ;IACzBC,OAAO,GAAGrB,IAAI,CAACqB,OAAO;IACtBC,mBAAmB,GAAGtB,IAAI,CAACuB,kBAAkB;IAC7CC,iBAAiB,GAAGxB,IAAI,CAACyB,gBAAgB;IACzCC,IAAI,GAAG1B,IAAI,CAAC0B,IAAI;IAChBC,KAAK,GAAG3B,IAAI,CAAC2B,KAAK;EACpB,IAAIC,SAAS,GAAGtB,YAAY,IAAI,aAAaX,KAAK,CAACkC,aAAa,CAAC,OAAO,EAAE,IAAI,CAAC;EAC/E,IAAIC,UAAU,GAAGF,SAAS;IACxBG,SAAS,GAAGD,UAAU,CAAC7B,GAAG;IAC1B+B,WAAW,GAAGF,UAAU,CAACG,KAAK;EAChC,IAAIC,eAAe,GAAGF,WAAW,CAAChB,SAAS;IACzCmB,cAAc,GAAGH,WAAW,CAACZ,QAAQ;IACrCgB,iBAAiB,GAAGJ,WAAW,CAACd,WAAW;IAC3CmB,wBAAwB,GAAGL,WAAW,CAACT,kBAAkB;IACzDe,sBAAsB,GAAGN,WAAW,CAACP,gBAAgB;IACrDc,KAAK,GAAGP,WAAW,CAACO,KAAK;EAC3BzC,OAAO,CAAC,EAAE,WAAW,IAAI8B,SAAS,CAACK,KAAK,CAAC,EAAE,uGAAuG,CAAC;EACnJL,SAAS,GAAG,aAAajC,KAAK,CAAC6C,YAAY,CAACZ,SAAS,EAAElC,aAAa,CAACA,aAAa,CAACA,aAAa,CAAC;IAC/F+C,IAAI,EAAE;EACR,CAAC,EAAET,WAAW,CAAC,EAAE,CAAC,CAAC,EAAE;IACnB;IACA3B,EAAE,EAAEA,EAAE;IACNJ,GAAG,EAAEJ,UAAU,CAACI,GAAG,EAAE8B,SAAS,CAAC;IAC/BxB,QAAQ,EAAEA,QAAQ;IAClBC,QAAQ,EAAEA,QAAQ;IAClBE,YAAY,EAAEA,YAAY,IAAI,KAAK;IACnCD,SAAS,EAAEA,SAAS;IACpBiC,SAAS,EAAE9C,UAAU,CAAC,EAAE,CAAC+C,MAAM,CAACvC,SAAS,EAAE,yBAAyB,CAAC,EAAE,CAACF,WAAW,GAAG0B,SAAS,MAAM,IAAI,IAAI1B,WAAW,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAG,CAACC,iBAAiB,GAAGD,WAAW,CAAC+B,KAAK,MAAM,IAAI,IAAI9B,iBAAiB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,iBAAiB,CAACuC,SAAS,CAAC;IACvQE,IAAI,EAAE,UAAU;IAChB,YAAY,EAAE,QAAQ;IACtB,eAAe,EAAElB,IAAI;IACrB,eAAe,EAAE,SAAS;IAC1B,WAAW,EAAE,EAAE,CAACiB,MAAM,CAACtC,EAAE,EAAE,OAAO,CAAC;IACnC,mBAAmB,EAAE,MAAM;IAC3B,eAAe,EAAE,EAAE,CAACsC,MAAM,CAACtC,EAAE,EAAE,OAAO,CAAC;IACvC,uBAAuB,EAAEqB,IAAI,GAAGd,kBAAkB,GAAGiC;EACvD,CAAC,EAAElB,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE;IACbd,KAAK,EAAEF,QAAQ,GAAGE,KAAK,GAAG,EAAE;IAC5BC,SAAS,EAAEA,SAAS;IACpBgC,QAAQ,EAAE,CAACnC,QAAQ;IACnBoC,YAAY,EAAE,CAACpC,QAAQ,GAAG,IAAI,GAAG,IAAI;IACrC4B,KAAK,EAAE7C,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE6C,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE;MACjDS,OAAO,EAAErC,QAAQ,GAAG,IAAI,GAAG;IAC7B,CAAC,CAAC;IACFK,SAAS,EAAE,SAASA,SAASA,CAACiC,KAAK,EAAE;MACnClC,UAAU,CAACkC,KAAK,CAAC;MACjB,IAAIf,eAAe,EAAE;QACnBA,eAAe,CAACe,KAAK,CAAC;MACxB;IACF,CAAC;IACD/B,WAAW,EAAE,SAASA,WAAWA,CAAC+B,KAAK,EAAE;MACvChC,YAAY,CAACgC,KAAK,CAAC;MACnB,IAAIb,iBAAiB,EAAE;QACrBA,iBAAiB,CAACa,KAAK,CAAC;MAC1B;IACF,CAAC;IACD7B,QAAQ,EAAE,SAASA,QAAQA,CAAC6B,KAAK,EAAE;MACjC9B,SAAS,CAAC8B,KAAK,CAAC;MAChB,IAAId,cAAc,EAAE;QAClBA,cAAc,CAACc,KAAK,CAAC;MACvB;IACF,CAAC;IACD1B,kBAAkB,EAAE,SAASA,kBAAkBA,CAAC0B,KAAK,EAAE;MACrD3B,mBAAmB,CAAC2B,KAAK,CAAC;MAC1B,IAAIZ,wBAAwB,EAAE;QAC5BA,wBAAwB,CAACY,KAAK,CAAC;MACjC;IACF,CAAC;IACDxB,gBAAgB,EAAE,SAASA,gBAAgBA,CAACwB,KAAK,EAAE;MACjDzB,iBAAiB,CAACyB,KAAK,CAAC;MACxB,IAAIX,sBAAsB,EAAE;QAC1BA,sBAAsB,CAACW,KAAK,CAAC;MAC/B;IACF,CAAC;IACD5B,OAAO,EAAEA;EACX,CAAC,CAAC,CAAC;EACH,OAAOO,SAAS;AAClB,CAAC;AACD,IAAIsB,QAAQ,GAAG,aAAavD,KAAK,CAACwD,UAAU,CAACpD,KAAK,CAAC;AACnDmD,QAAQ,CAACE,WAAW,GAAG,OAAO;AAC9B,eAAeF,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}