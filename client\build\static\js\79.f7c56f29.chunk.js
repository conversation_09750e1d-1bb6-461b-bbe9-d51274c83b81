"use strict";(self.webpackChunkclient=self.webpackChunkclient||[]).push([[79],{3372:(e,s,t)=>{t.d(s,{Bn:()=>i,Ds:()=>o,Km:()=>n,ZB:()=>l,_K:()=>m,xL:()=>r});const{default:a}=t(3371),r=async e=>{try{return(await a.post("/api/forum/add-question",e)).data}catch(s){return s.response.data}},i=async e=>{try{return(await a.post("/api/forum/add-reply",e)).data}catch(s){return s.response.data}},l=async e=>{let{page:s,limit:t}=e;try{return(await a.get("/api/forum/get-all-questions?page=".concat(s,"&limit=").concat(t))).data}catch(r){return r.response.data}},n=async e=>{try{return(await a.delete("/api/forum/delete-question/".concat(e))).data}catch(s){return s.response.data}},o=async(e,s)=>{try{return(await a.put("/api/forum/update-question/".concat(s),e)).data}catch(t){return t.response.data}},m=async(e,s)=>{try{return(await a.put("/api/forum/update-reply-status/".concat(s),e)).data}catch(t){return t.response.data}}},640:(e,s,t)=>{t.d(s,{Z:()=>i});var a=t(2791),r=t(184);const i=function(e){let{title:s}=e;const[t,i]=(0,a.useState)(!1);return(0,a.useEffect)((()=>{window.innerWidth<768&&i(!0)}),[]),(0,r.jsx)("div",{className:"mt-2",children:(0,r.jsx)("h1",{className:t?"text-lg":"",children:s})})}},4079:(e,s,t)=>{t.r(s),t.d(s,{default:()=>f});var a=t(1413),r=t(2791),i=t(8262),l=t(7846),n=t(7027),o=t(9389),m=t(7309),c=t(1632),d=(t(640),t(9434)),x=t(8247),u=t(2048),h=(t(9242),t(3372)),p=(t(1641),t(2202)),g=t(1909),b=t(184);const f=()=>{const[e,s]=(0,r.useState)(!1),[t,f]=(0,r.useState)(""),[y,j]=(0,r.useState)([]),[w,v]=(0,r.useState)({}),[N,Z]=(0,r.useState)(!1),[_,S]=(0,r.useState)(null),[P,I]=(0,r.useState)(null),[k]=l.Z.useForm(),[q,C]=(0,r.useState)(!0),A=()=>(0,b.jsxs)("div",{className:"forum-container",children:[(0,b.jsxs)("div",{className:"forum-header",children:[(0,b.jsx)("div",{className:"h-8 bg-gray-200 rounded w-48 mb-4 animate-pulse"}),(0,b.jsx)("div",{className:"h-10 bg-blue-100 rounded w-32 animate-pulse"})]}),(0,b.jsx)("div",{className:"forum-content",children:[...Array(5)].map(((e,s)=>(0,b.jsx)("div",{className:"question-card mb-4 p-4 border rounded-lg",children:(0,b.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,b.jsx)("div",{className:"w-10 h-10 bg-gray-200 rounded-full animate-pulse"}),(0,b.jsxs)("div",{className:"flex-1",children:[(0,b.jsx)("div",{className:"h-4 bg-gray-200 rounded w-3/4 mb-2 animate-pulse"}),(0,b.jsx)("div",{className:"h-3 bg-gray-100 rounded w-1/2 mb-3 animate-pulse"}),(0,b.jsx)("div",{className:"h-3 bg-gray-100 rounded w-full mb-2 animate-pulse"}),(0,b.jsx)("div",{className:"h-3 bg-gray-100 rounded w-2/3 animate-pulse"})]})]})},s)))})]}),[F]=l.Z.useForm(),T=(0,d.I0)(),[V,D]=(0,r.useState)({}),[Q,O]=(0,r.useState)(1),[R,z]=(0,r.useState)(1),[E]=(0,r.useState)(10),[U,L]=(0,r.useState)(0),B=()=>{["primary","secondary","advance"].forEach((e=>{for(let s=1;s<=20;s++)localStorage.removeItem("forum_questions_".concat(e,"_").concat(s,"_").concat(E)),localStorage.removeItem("forum_questions_".concat(e,"_").concat(s,"_").concat(E,"_time"))}))},K=async e=>{try{const s=(null===t||void 0===t?void 0:t.level)||"primary";["primary","secondary","advance"].forEach((e=>{if(e!==s)for(let s=1;s<=10;s++)localStorage.removeItem("forum_questions_".concat(e,"_").concat(s,"_").concat(E)),localStorage.removeItem("forum_questions_".concat(e,"_").concat(s,"_").concat(E,"_time"))}));const a="forum_questions_".concat(s,"_").concat(e,"_").concat(E),r=localStorage.getItem(a),i=localStorage.getItem("".concat(a,"_time")),l=Date.now();if(r&&i&&l-parseInt(i)<3e5){const e=JSON.parse(r);return j(e.questions),L(e.totalQuestions),void z(e.totalPages)}T((0,x.YC)());const o=await(0,h.ZB)({page:e,limit:E});o.success?(console.log(o.data),j(o.data),L(o.totalQuestions),z(o.totalPages),localStorage.setItem(a,JSON.stringify({questions:o.data,totalQuestions:o.totalQuestions,totalPages:o.totalPages})),localStorage.setItem("".concat(a,"_time"),l.toString())):n.ZP.error(o.message)}catch(s){n.ZP.error(s.message)}finally{T((0,x.Ir)())}};(0,r.useEffect)((()=>{B(),K(Q).finally((()=>{C(!1)}))}),[Q,E]);(0,r.useEffect)((()=>{localStorage.getItem("token")&&(async()=>{T((0,x.YC)());try{const e=await(0,i.bG)();e.success?e.data.isAdmin?(s(!0),f(e.data),await K()):(s(!1),f(e.data),await K()):n.ZP.error(e.message)}catch(e){n.ZP.error(e.message)}})()}),[]);const W=async e=>{try{const s={questionId:_,text:e.text},t=await(0,h.Bn)(s);t.success?(n.ZP.success(t.message),S(null),k.resetFields(),B(),await K()):n.ZP.error(t.message)}catch(s){n.ZP.error(s.message)}};(0,r.useEffect)((()=>{_&&!V[_]&&D((e=>(0,a.Z)((0,a.Z)({},e),{},{[_]:r.createRef()})))}),[_,V]),(0,r.useEffect)((()=>{_&&V[_]&&V[_].current.scrollIntoView({behavior:"smooth"})}),[_,V]);const Y=async e=>{try{const s=await(0,h.Ds)(e,P._id);s.success?(n.ZP.success(s.message),I(null),B(),await K()):n.ZP.error(s.message)}catch(s){n.ZP.error(s.message)}},H=()=>{I("")};(0,r.useEffect)((()=>{P?F.setFieldsValue({title:P.title,body:P.body}):F.resetFields()}),[P]);return q&&0===y.length?(0,b.jsx)(A,{}):(0,b.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50",children:(0,b.jsxs)("div",{className:"Forum max-w-7xl mx-auto px-3 sm:px-4 md:px-6 lg:px-8 py-4 sm:py-6 lg:py-8",children:[(0,b.jsxs)("div",{className:"text-center mb-8 sm:mb-10 lg:mb-12",children:[(0,b.jsx)("div",{className:"inline-flex items-center justify-center w-12 h-12 sm:w-14 sm:h-14 lg:w-16 lg:h-16 bg-gradient-to-r from-blue-600 to-indigo-600 rounded-xl sm:rounded-2xl mb-4 sm:mb-6 shadow-lg",children:(0,b.jsx)("i",{className:"ri-discuss-line text-lg sm:text-xl lg:text-2xl text-white"})}),(0,b.jsxs)("h1",{className:"text-2xl sm:text-3xl lg:text-4xl font-bold text-gray-900 mb-3 sm:mb-4 px-4",children:["Community ",(0,b.jsx)("span",{className:"text-transparent bg-clip-text bg-gradient-to-r from-blue-600 to-indigo-600",children:"Forum"})]}),(0,b.jsx)("p",{className:"text-sm sm:text-base lg:text-lg text-gray-600 max-w-xl sm:max-w-2xl mx-auto mb-6 sm:mb-8 px-4",children:"Connect with fellow learners, ask questions, share knowledge, and grow together in our vibrant learning community."}),(0,b.jsxs)("button",{onClick:()=>Z(!0),className:"inline-flex items-center px-6 py-3 sm:px-8 sm:py-4 bg-gradient-to-r from-blue-600 to-indigo-600 text-white font-semibold rounded-lg sm:rounded-xl shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300 text-sm sm:text-base",children:[(0,b.jsx)("i",{className:"ri-add-line text-lg sm:text-xl mr-2"}),"Ask a Question"]})]}),N&&(0,b.jsxs)("div",{className:"bg-white rounded-xl sm:rounded-2xl shadow-xl p-4 sm:p-6 lg:p-8 mb-6 sm:mb-8 border border-gray-100",children:[(0,b.jsxs)("div",{className:"flex items-center mb-4 sm:mb-6",children:[(0,b.jsx)("div",{className:"w-8 h-8 sm:w-10 sm:h-10 bg-gradient-to-r from-blue-600 to-indigo-600 rounded-lg flex items-center justify-center mr-3 sm:mr-4",children:(0,b.jsx)("i",{className:"ri-question-line text-white text-sm sm:text-lg"})}),(0,b.jsx)("h2",{className:"text-xl sm:text-2xl font-bold text-gray-900",children:"Ask a Question"})]}),(0,b.jsxs)(l.Z,{form:k,onFinish:async e=>{try{const s=await(0,h.xL)(e);s.success?(n.ZP.success(s.message),Z(!1),k.resetFields(),B(),await K()):n.ZP.error(s.message)}catch(s){n.ZP.error(s.message)}},layout:"vertical",className:"modern-form",children:[(0,b.jsx)(l.Z.Item,{name:"title",label:"Question Title",rules:[{required:!0,message:"Please enter a descriptive title"}],children:(0,b.jsx)(o.default,{placeholder:"What would you like to know?",className:"h-12 text-lg"})}),(0,b.jsx)(l.Z.Item,{name:"body",label:"Question Details",rules:[{required:!0,message:"Please provide more details about your question"}],children:(0,b.jsx)(o.default.TextArea,{rows:6,placeholder:"Describe your question in detail. The more information you provide, the better answers you'll receive.",className:"text-base"})}),(0,b.jsx)(l.Z.Item,{className:"mb-0",children:(0,b.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,b.jsxs)(m.ZP,{type:"primary",htmlType:"submit",className:"h-12 px-8 bg-gradient-to-r from-blue-600 to-indigo-600 border-none rounded-lg font-semibold text-base",children:[(0,b.jsx)("i",{className:"ri-send-plane-line mr-2"}),"Post Question"]}),(0,b.jsx)(m.ZP,{onClick:()=>{Z(!1),k.resetFields()},className:"h-12 px-6 border-gray-300 rounded-lg font-semibold text-base hover:bg-gray-50",children:"Cancel"})]})})]})]}),0===y.length&&(0,b.jsxs)("div",{className:"text-center py-12",children:[(0,b.jsx)("div",{className:"inline-flex items-center justify-center w-16 h-16 bg-gray-100 rounded-full mb-4",children:(0,b.jsx)("i",{className:"ri-loader-4-line text-2xl text-gray-400 animate-spin"})}),(0,b.jsx)("p",{className:"text-gray-500",children:"Loading discussions..."})]}),(0,b.jsx)("div",{className:"space-y-4 sm:space-y-6",children:y.filter((e=>e&&e.user)).map((s=>{var r,i;return(0,b.jsxs)("div",{className:"bg-white rounded-xl sm:rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 overflow-hidden border border-gray-100",children:[(0,b.jsx)("div",{className:"p-4 sm:p-6 border-b border-gray-100",children:(0,b.jsxs)("div",{className:"flex items-start justify-between",children:[(0,b.jsxs)("div",{className:"flex items-center space-x-3 sm:space-x-4",children:[(0,b.jsxs)("div",{className:"relative",children:[(0,b.jsx)(u.Z,{user:s.user,size:"sm",showOnlineStatus:!1,style:{width:"32px",height:"32px"}}),s.user&&s.user.isOnline&&(0,b.jsx)("div",{style:{position:"absolute",bottom:"-2px",right:"-2px",width:"12px",height:"12px",backgroundColor:"#22c55e",borderRadius:"50%",border:"2px solid #ffffff",boxShadow:"0 2px 8px rgba(34, 197, 94, 0.6)",zIndex:10},title:"Online"})]}),(0,b.jsxs)("div",{className:"min-w-0 flex-1",children:[(0,b.jsx)("h4",{className:"font-semibold text-gray-900 text-sm sm:text-base truncate",children:(null===(r=s.user)||void 0===r?void 0:r.name)||"Unknown User"}),(0,b.jsx)("p",{className:"text-xs sm:text-sm text-gray-500",children:new Date(s.createdAt).toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"})})]})]}),(0,b.jsx)("div",{className:"flex items-center space-x-1 sm:space-x-2 ml-2",children:(t._id===(null===(i=s.user)||void 0===i?void 0:i._id)||t.isAdmin)&&(0,b.jsxs)(b.Fragment,{children:[(0,b.jsx)("button",{onClick:()=>(e=>{I(e)})(s),className:"flex items-center px-2 py-2 sm:px-3 text-blue-600 hover:bg-blue-50 rounded-lg transition-colors duration-200",children:(0,b.jsx)(p.KHI,{className:"w-3 h-3 sm:w-4 sm:h-4"})}),(0,b.jsx)("button",{onClick:()=>(async e=>{try{if(!window.confirm("Are you sure you want to delete this question?"))return;const s=await(0,h.Km)(e._id);s.success?(n.ZP.success(s.message),B(),await K()):n.ZP.error(s.message)}catch(s){n.ZP.error(s.message)}})(s),className:"flex items-center px-2 py-2 sm:px-3 text-red-600 hover:bg-red-50 rounded-lg transition-colors duration-200",children:(0,b.jsx)(g.ZkW,{className:"w-3 h-3 sm:w-4 sm:h-4"})})]})})]})}),(0,b.jsxs)("div",{className:"p-4 sm:p-6",children:[(0,b.jsx)("h3",{className:"text-lg sm:text-xl font-bold text-gray-900 mb-3 leading-tight",children:s.title}),(0,b.jsx)("p",{className:"text-gray-700 leading-relaxed mb-6",children:s.body}),(0,b.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center sm:justify-between pt-3 sm:pt-4 border-t border-gray-100 gap-3 sm:gap-0",children:[(0,b.jsxs)("div",{className:"flex items-center space-x-2 sm:space-x-4 overflow-x-auto",children:[(0,b.jsxs)("button",{onClick:()=>{return e=s._id,void v((s=>(0,a.Z)((0,a.Z)({},s),{},{[e]:!s[e]})));var e},className:"flex items-center px-3 py-2 sm:px-4 text-gray-600 hover:text-blue-600 hover:bg-blue-50 rounded-lg transition-colors duration-200 text-sm sm:text-base whitespace-nowrap",children:[(0,b.jsx)("i",{className:"ri-eye-line mr-1 sm:mr-2 text-sm sm:text-base"}),w[s._id]?"Hide Replies":"View Replies"]}),(0,b.jsxs)("button",{onClick:()=>{return e=s._id,void S(e);var e},className:"flex items-center px-3 py-2 sm:px-4 text-gray-600 hover:text-green-600 hover:bg-green-50 rounded-lg transition-colors duration-200 text-sm sm:text-base whitespace-nowrap",children:[(0,b.jsx)("i",{className:"ri-reply-line mr-1 sm:mr-2 text-sm sm:text-base"}),"Reply"]})]}),(0,b.jsxs)("div",{className:"flex items-center px-2 py-1.5 sm:px-3 sm:py-2 bg-gray-50 rounded-lg self-start sm:self-auto",children:[(0,b.jsx)(g.a9O,{className:"w-3 h-3 sm:w-4 sm:h-4 text-gray-500 mr-1 sm:mr-2"}),(0,b.jsx)("span",{className:"text-xs sm:text-sm font-medium text-gray-700",children:s.replies.length})]})]})]}),P&&P._id===s._id&&(0,b.jsxs)(l.Z,{form:F,onFinish:Y,layout:"vertical",initialValues:{title:P.title,body:P.body},children:[(0,b.jsx)(l.Z.Item,{name:"title",label:"Title",rules:[{required:!0,message:"Please enter the title"}],children:(0,b.jsx)(o.default,{style:{padding:"18px 12px"}})}),(0,b.jsx)(l.Z.Item,{name:"body",label:"Body",rules:[{required:!0,message:"Please enter the body"}],children:(0,b.jsx)(o.default.TextArea,{})}),(0,b.jsxs)(l.Z.Item,{children:[(0,b.jsx)(m.ZP,{type:"primary",htmlType:"submit",children:"Update Question"}),(0,b.jsx)(m.ZP,{onClick:H,style:{marginLeft:10},children:"Cancel"})]})]}),w[s._id]&&(0,b.jsxs)("div",{className:"mt-4 sm:mt-6 space-y-3 sm:space-y-4 bg-gray-50 rounded-lg sm:rounded-xl p-3 sm:p-4",children:[(0,b.jsxs)("h4",{className:"text-base sm:text-lg font-semibold text-gray-800 mb-3 sm:mb-4 flex items-center",children:[(0,b.jsx)("i",{className:"ri-chat-3-line mr-2 text-blue-600 text-sm sm:text-base"}),"Replies (",s.replies.filter((e=>e&&e.user)).length,")"]}),s.replies.filter((e=>e&&e.user)).map(((t,a)=>{var r,i,l,o,m,c,d;return(0,b.jsx)("div",{className:"bg-white rounded-lg p-3 sm:p-4 shadow-sm border-l-4 ".concat(null!==(r=t.user)&&void 0!==r&&r.isAdmin?"border-purple-500 bg-purple-50":t.isVerified?"border-green-500 bg-green-50":"border-gray-300"),children:(0,b.jsxs)("div",{className:"flex items-start space-x-2 sm:space-x-3",children:[(0,b.jsxs)("div",{className:"flex-shrink-0 relative",children:[(0,b.jsx)(u.Z,{user:t.user,size:"xs",showOnlineStatus:!1,style:{width:"20px",height:"20px"},className:"sm:w-6 sm:h-6"}),t.user&&t.user.isOnline&&(0,b.jsx)("div",{style:{position:"absolute",bottom:"-1px",right:"-1px",width:"6px",height:"6px",backgroundColor:"#22c55e",borderRadius:"50%",border:"1px solid #ffffff",boxShadow:"0 1px 4px rgba(34, 197, 94, 0.6)",zIndex:10},className:"sm:w-2 sm:h-2",title:"Online"})]}),(0,b.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,b.jsxs)("div",{className:"flex items-start sm:items-center justify-between mb-2 gap-2",children:[(0,b.jsxs)("div",{className:"flex items-center space-x-1 sm:space-x-2 min-w-0 flex-1",children:[(0,b.jsx)("h5",{className:"font-semibold text-gray-900 text-sm sm:text-base truncate",children:(null===(i=t.user)||void 0===i?void 0:i.name)||"Unknown User"}),(null===(l=t.user)||void 0===l?void 0:l.isAdmin)&&(0,b.jsx)("span",{className:"px-1.5 py-0.5 sm:px-2 sm:py-1 text-xs font-medium bg-purple-100 text-purple-800 rounded-full whitespace-nowrap",children:"Admin"}),t.isVerified&&!(null!==(o=t.user)&&void 0!==o&&o.isAdmin)&&(0,b.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,b.jsx)(p.l_A,{className:"w-3 h-3 sm:w-4 sm:h-4 text-green-600"}),(0,b.jsx)("span",{className:"px-1.5 py-0.5 sm:px-2 sm:py-1 text-xs font-medium bg-green-100 text-green-800 rounded-full whitespace-nowrap",children:"Verified"})]})]}),(0,b.jsx)("span",{className:"text-xs sm:text-sm text-gray-500 whitespace-nowrap",children:(()=>{try{const e=new Date(t.createdAt);return isNaN(e.getTime())?"Invalid date":e.toLocaleDateString("en-US",{month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"})}catch(e){return"Invalid date"}})()})]}),(0,b.jsx)("div",{className:"leading-relaxed mb-3 text-sm sm:text-base ".concat(!t.isVerified||null!==(m=t.user)&&void 0!==m&&m.isAdmin?null!==(c=t.user)&&void 0!==c&&c.isAdmin?"text-purple-800 font-medium":"text-gray-700":"text-green-800 font-medium"),children:t.text}),e&&!(null!==(d=t.user)&&void 0!==d&&d.isAdmin)&&(0,b.jsx)("div",{className:"flex justify-end",children:(0,b.jsx)("button",{onClick:()=>(async(e,s,t)=>{try{const a=await(0,h._K)({replyId:s,status:t},e);a.success?(n.ZP.success(a.message),B(),await K()):n.ZP.error(a.message)}catch(a){n.ZP.error(a.message)}})(s._id,t._id,!t.isVerified),className:"px-3 py-1.5 sm:px-4 sm:py-2 text-xs sm:text-sm font-medium rounded-lg transition-colors duration-200 ".concat(t.isVerified?"bg-red-100 text-red-700 hover:bg-red-200":"bg-green-100 text-green-700 hover:bg-green-200"),children:t.isVerified?"Disapprove":"Approve"})})]})]})},t._id)}))]}),(0,b.jsx)("div",{ref:V[s._id],className:"mt-4 sm:mt-6",children:_===s._id&&(0,b.jsx)("div",{className:"bg-white rounded-lg sm:rounded-xl p-4 sm:p-6 border border-gray-200",children:(0,b.jsxs)(l.Z,{form:k,onFinish:W,layout:"vertical",children:[(0,b.jsx)(l.Z.Item,{name:"text",label:(0,b.jsx)("span",{className:"text-sm sm:text-base font-medium",children:"Your Reply"}),rules:[{required:!0,message:"Please enter your reply"}],children:(0,b.jsx)(o.default.TextArea,{rows:3,className:"text-sm sm:text-base",placeholder:"Write your reply here..."})}),(0,b.jsx)(l.Z.Item,{className:"mb-0",children:(0,b.jsxs)("div",{className:"flex flex-col sm:flex-row gap-2 sm:gap-3",children:[(0,b.jsx)(m.ZP,{type:"primary",htmlType:"submit",className:"w-full sm:w-auto",size:"large",children:"Submit Reply"}),(0,b.jsx)(m.ZP,{onClick:()=>S(null),className:"w-full sm:w-auto",size:"large",children:"Cancel"})]})})]})})})]},s._id)}))}),(0,b.jsx)(c.Z,{current:Q,total:U,pageSize:E,onChange:e=>{O(e)},style:{marginTop:"20px",textAlign:"center"},showSizeChanger:!1})]})})}},1641:(e,s,t)=>{e.exports=t.p+"static/media/person.b330f873e0d44db684c9.png"}}]);
//# sourceMappingURL=79.f7c56f29.chunk.js.map