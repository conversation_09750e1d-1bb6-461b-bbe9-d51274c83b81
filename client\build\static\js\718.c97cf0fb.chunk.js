"use strict";(self.webpackChunkclient=self.webpackChunkclient||[]).push([[718],{272:(e,t,l)=>{l.d(t,{$s:()=>c,I1:()=>n,Ss:()=>r,cq:()=>s,dM:()=>i,uH:()=>o});const{default:a}=l(3371),s=async e=>{try{return(await a.post("/api/reports/add-report",e)).data}catch(t){return t.response.data}},n=async e=>{try{return(await a.post("/api/reports/get-all-reports",e)).data}catch(t){return t.response.data}},r=async e=>{try{return(await a.post("/api/reports/get-all-reports-by-user",e)).data}catch(t){return t.response.data}},o=async e=>{try{return(await a.get("/api/reports/get-all-reports-for-ranking")).data}catch(t){return t.response.data}},i=async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};try{const t=new URLSearchParams;e.limit&&t.append("limit",e.limit),e.classFilter&&t.append("classFilter",e.classFilter),e.levelFilter&&t.append("levelFilter",e.levelFilter),e.seasonFilter&&t.append("seasonFilter",e.seasonFilter),e.includeInactive&&t.append("includeInactive",e.includeInactive);return(await a.get("/api/quiz/xp-leaderboard?".concat(t.toString()))).data}catch(t){return t.response.data}},c=async function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:5;try{return(await a.get("/api/quiz/user-ranking/".concat(e,"?context=").concat(t))).data}catch(l){return l.response.data}}},640:(e,t,l)=>{l.d(t,{Z:()=>n});var a=l(2791),s=l(184);const n=function(e){let{title:t}=e;const[l,n]=(0,a.useState)(!1);return(0,a.useEffect)((()=>{window.innerWidth<768&&n(!0)}),[]),(0,s.jsx)("div",{className:"mt-2",children:(0,s.jsx)("h1",{className:l?"text-lg":"",children:t})})}},6718:(e,t,l)=>{l.r(t),l.d(t,{default:()=>K});var a=l(2791),s=l(640),n=l(5725),r=l(8839),o=l(7027),i=l(7309),c=l(5273),d=l(2339),m=l(6473),x=l(1429),u=l(222),h=l(4664),g=l(6275),p=l(1694),v=l.n(p),b=l(635),f=l(1929),y=l(4107);const w=a.createContext({}),j=e=>{let{children:t}=e;return t};function N(e){return void 0!==e&&null!==e}const S=e=>{const{itemPrefixCls:t,component:l,span:s,className:n,style:r,labelStyle:o,contentStyle:i,bordered:c,label:d,content:m,colon:x}=e,u=l;return c?a.createElement(u,{className:v()({["".concat(t,"-item-label")]:N(d),["".concat(t,"-item-content")]:N(m)},n),style:r,colSpan:s},N(d)&&a.createElement("span",{style:o},d),N(m)&&a.createElement("span",{style:i},m)):a.createElement(u,{className:v()("".concat(t,"-item"),n),style:r,colSpan:s},a.createElement("div",{className:"".concat(t,"-item-container")},(d||0===d)&&a.createElement("span",{className:v()("".concat(t,"-item-label"),{["".concat(t,"-item-no-colon")]:!x}),style:o},d),(m||0===m)&&a.createElement("span",{className:v()("".concat(t,"-item-content")),style:i},m)))};function E(e,t,l){let{colon:s,prefixCls:n,bordered:r}=t,{component:o,type:i,showLabel:c,showContent:d,labelStyle:m,contentStyle:x}=l;return e.map(((e,t)=>{let{label:l,children:u,prefixCls:h=n,className:g,style:p,labelStyle:v,contentStyle:b,span:f=1,key:y}=e;return"string"===typeof o?a.createElement(S,{key:"".concat(i,"-").concat(y||t),className:g,style:p,labelStyle:Object.assign(Object.assign({},m),v),contentStyle:Object.assign(Object.assign({},x),b),span:f,colon:s,component:o,itemPrefixCls:h,bordered:r,label:c?l:null,content:d?u:null}):[a.createElement(S,{key:"label-".concat(y||t),className:g,style:Object.assign(Object.assign(Object.assign({},m),p),v),span:1,colon:s,component:o[0],itemPrefixCls:h,bordered:r,label:l}),a.createElement(S,{key:"content-".concat(y||t),className:g,style:Object.assign(Object.assign(Object.assign({},x),p),b),span:2*f-1,component:o[1],itemPrefixCls:h,bordered:r,content:u})]}))}const k=e=>{const t=a.useContext(w),{prefixCls:l,vertical:s,row:n,index:r,bordered:o}=e;return s?a.createElement(a.Fragment,null,a.createElement("tr",{key:"label-".concat(r),className:"".concat(l,"-row")},E(n,e,Object.assign({component:"th",type:"label",showLabel:!0},t))),a.createElement("tr",{key:"content-".concat(r),className:"".concat(l,"-row")},E(n,e,Object.assign({component:"td",type:"content",showContent:!0},t)))):a.createElement("tr",{key:r,className:"".concat(l,"-row")},E(n,e,Object.assign({component:o?["th","td"]:"td",type:"item",showLabel:!0,showContent:!0},t)))};var C=l(5501);function M(e,t,l){let a=e;return(void 0===l||l>t)&&(a=Object.assign(Object.assign({},e),{span:t})),a}function P(e,t){const l=[];let a=[],s=t;return e.filter((e=>e)).forEach(((n,r)=>{const o=null===n||void 0===n?void 0:n.span,i=o||1;if(r===e.length-1)return a.push(M(n,s,o)),void l.push(a);i<s?(s-=i,a.push(n)):(a.push(M(n,s,i)),l.push(a),s=t,a=[])})),l}const O=(e,t,l)=>(0,a.useMemo)((()=>{return Array.isArray(t)?P(t,e):P((a=l,(0,C.Z)(a).map((e=>null===e||void 0===e?void 0:e.props))),e);var a}),[t,l,e]);var W=l(7521),A=l(5564),Z=l(9922);const I=e=>{const{componentCls:t,labelBg:l}=e;return{["&".concat(t,"-bordered")]:{["> ".concat(t,"-view")]:{border:"".concat(e.lineWidth,"px ").concat(e.lineType," ").concat(e.colorSplit),"> table":{tableLayout:"auto",borderCollapse:"collapse"},["".concat(t,"-row")]:{borderBottom:"".concat(e.lineWidth,"px ").concat(e.lineType," ").concat(e.colorSplit),"&:last-child":{borderBottom:"none"},["> ".concat(t,"-item-label, > ").concat(t,"-item-content")]:{padding:"".concat(e.padding,"px ").concat(e.paddingLG,"px"),borderInlineEnd:"".concat(e.lineWidth,"px ").concat(e.lineType," ").concat(e.colorSplit),"&:last-child":{borderInlineEnd:"none"}},["> ".concat(t,"-item-label")]:{color:e.colorTextSecondary,backgroundColor:l,"&::after":{display:"none"}}}},["&".concat(t,"-middle")]:{["".concat(t,"-row")]:{["> ".concat(t,"-item-label, > ").concat(t,"-item-content")]:{padding:"".concat(e.paddingSM,"px ").concat(e.paddingLG,"px")}}},["&".concat(t,"-small")]:{["".concat(t,"-row")]:{["> ".concat(t,"-item-label, > ").concat(t,"-item-content")]:{padding:"".concat(e.paddingXS,"px ").concat(e.padding,"px")}}}}}},z=e=>{const{componentCls:t,extraColor:l,itemPaddingBottom:a,colonMarginRight:s,colonMarginLeft:n,titleMarginBottom:r}=e;return{[t]:Object.assign(Object.assign(Object.assign({},(0,W.Wf)(e)),I(e)),{"&-rtl":{direction:"rtl"},["".concat(t,"-header")]:{display:"flex",alignItems:"center",marginBottom:r},["".concat(t,"-title")]:Object.assign(Object.assign({},W.vS),{flex:"auto",color:e.colorText,fontWeight:e.fontWeightStrong,fontSize:e.fontSizeLG,lineHeight:e.lineHeightLG}),["".concat(t,"-extra")]:{marginInlineStart:"auto",color:l,fontSize:e.fontSize},["".concat(t,"-view")]:{width:"100%",borderRadius:e.borderRadiusLG,table:{width:"100%",tableLayout:"fixed"}},["".concat(t,"-row")]:{"> th, > td":{paddingBottom:a},"&:last-child":{borderBottom:"none"}},["".concat(t,"-item-label")]:{color:e.colorTextTertiary,fontWeight:"normal",fontSize:e.fontSize,lineHeight:e.lineHeight,textAlign:"start","&::after":{content:'":"',position:"relative",top:-.5,marginInline:"".concat(n,"px ").concat(s,"px")},["&".concat(t,"-item-no-colon::after")]:{content:'""'}},["".concat(t,"-item-no-label")]:{"&::after":{margin:0,content:'""'}},["".concat(t,"-item-content")]:{display:"table-cell",flex:1,color:e.colorText,fontSize:e.fontSize,lineHeight:e.lineHeight,wordBreak:"break-word",overflowWrap:"break-word"},["".concat(t,"-item")]:{paddingBottom:0,verticalAlign:"top","&-container":{display:"flex",["".concat(t,"-item-label")]:{display:"inline-flex",alignItems:"baseline"},["".concat(t,"-item-content")]:{display:"inline-flex",alignItems:"baseline"}}},"&-middle":{["".concat(t,"-row")]:{"> th, > td":{paddingBottom:e.paddingSM}}},"&-small":{["".concat(t,"-row")]:{"> th, > td":{paddingBottom:e.paddingXS}}}})}},Y=(0,A.Z)("Descriptions",(e=>{const t=(0,Z.TS)(e,{});return[z(t)]}),(e=>({labelBg:e.colorFillAlter,titleMarginBottom:e.fontSizeSM*e.lineHeightSM,itemPaddingBottom:e.padding,colonMarginRight:e.marginXS,colonMarginLeft:e.marginXXS/2,extraColor:e.colorText})));var B=function(e,t){var l={};for(var a in e)Object.prototype.hasOwnProperty.call(e,a)&&t.indexOf(a)<0&&(l[a]=e[a]);if(null!=e&&"function"===typeof Object.getOwnPropertySymbols){var s=0;for(a=Object.getOwnPropertySymbols(e);s<a.length;s++)t.indexOf(a[s])<0&&Object.prototype.propertyIsEnumerable.call(e,a[s])&&(l[a[s]]=e[a[s]])}return l};const T={xxl:3,xl:3,lg:3,md:3,sm:2,xs:1};const D=e=>{const{prefixCls:t,title:l,extra:s,column:n=T,colon:r=!0,bordered:o,layout:i,children:c,className:d,rootClassName:m,style:x,size:u,labelStyle:h,contentStyle:g,items:p}=e,j=B(e,["prefixCls","title","extra","column","colon","bordered","layout","children","className","rootClassName","style","size","labelStyle","contentStyle","items"]),{getPrefixCls:N,direction:S,descriptions:E}=a.useContext(f.E_),C=N("descriptions",t),[M,P]=a.useState({}),W=function(e,t){if("number"===typeof e)return e;if("object"===typeof e)for(let l=0;l<b.c.length;l++){const a=b.c[l];if(t[a]&&void 0!==e[a])return e[a]||T[a]}return 3}(n,M),A=(0,y.Z)(u),Z=O(W,p,c),[I,z]=Y(C),D=(0,b.Z)();a.useEffect((()=>{const e=D.subscribe((e=>{"object"===typeof n&&P(e)}));return()=>{D.unsubscribe(e)}}),[]);const F=a.useMemo((()=>({labelStyle:h,contentStyle:g})),[h,g]);return I(a.createElement(w.Provider,{value:F},a.createElement("div",Object.assign({className:v()(C,null===E||void 0===E?void 0:E.className,{["".concat(C,"-").concat(A)]:A&&"default"!==A,["".concat(C,"-bordered")]:!!o,["".concat(C,"-rtl")]:"rtl"===S},d,m,z),style:Object.assign(Object.assign({},null===E||void 0===E?void 0:E.style),x)},j),(l||s)&&a.createElement("div",{className:"".concat(C,"-header")},l&&a.createElement("div",{className:"".concat(C,"-title")},l),s&&a.createElement("div",{className:"".concat(C,"-extra")},s)),a.createElement("div",{className:"".concat(C,"-view")},a.createElement("table",null,a.createElement("tbody",null,Z.map(((e,t)=>a.createElement(k,{key:t,index:t,colon:r,prefixCls:C,vertical:"vertical"===i,bordered:o,row:e})))))))))};D.Item=j;const F=D;var L=l(9434),R=l(8247),H=l(272),G=l(6042),Q=l(5526),X=l(2426),V=l.n(X),_=l(184);const{Option:q}=n.default,{RangePicker:J}=r.default;const K=function(){var e,t,l,r,p,v,b,f,y,w,j,N,S,E,k,C,M,P,O,W;const[A,Z]=(0,a.useState)([]),[I,z]=(0,a.useState)([]),[Y,B]=(0,a.useState)("all"),[T,D]=(0,a.useState)("all"),[X,K]=(0,a.useState)(null),[U,$]=(0,a.useState)("cards"),[ee,te]=(0,a.useState)({totalExams:0,passedExams:0,averageScore:0,streak:0,bestScore:0}),[le,ae]=(0,a.useState)(window.innerWidth<768),[se,ne]=(0,a.useState)(window.innerWidth>=768&&window.innerWidth<1024),[re,oe]=(0,a.useState)(!1),[ie,ce]=(0,a.useState)(null),de=(0,L.I0)(),me=e=>{if(!e||0===e.length)return void te({totalExams:0,passedExams:0,averageScore:0,streak:0,bestScore:0});const t=e.length,l=e.filter((e=>{var t;return"Pass"===(null===(t=e.result)||void 0===t?void 0:t.verdict)})).length,a=e.map((e=>{var t,l,a;return((null===(t=e.result)||void 0===t||null===(l=t.correctAnswers)||void 0===l?void 0:l.length)||0)/((null===(a=e.exam)||void 0===a?void 0:a.totalMarks)||1)*100})),s=a.reduce(((e,t)=>e+t),0)/t,n=Math.max(...a);let r=0,o=0;for(let c=e.length-1;c>=0;c--){var i;"Pass"===(null===(i=e[c].result)||void 0===i?void 0:i.verdict)?(r++,o=Math.max(o,r)):r=0}te({totalExams:t,passedExams:l,averageScore:Math.round(s),streak:o,bestScore:Math.round(n)})};(0,a.useEffect)((()=>{(async()=>{try{de((0,R.YC)());const e=await(0,H.Ss)();e.success?(Z(e.data),z(e.data),me(e.data)):o.ZP.error(e.message),de((0,R.Ir)())}catch(e){de((0,R.Ir)()),o.ZP.error(e.message)}})()}),[]),(0,a.useEffect)((()=>{(()=>{let e=[...A];"all"!==Y&&(e=e.filter((e=>{var t,l;return null===(t=e.exam)||void 0===t||null===(l=t.subject)||void 0===l?void 0:l.toLowerCase().includes(Y.toLowerCase())}))),"all"!==T&&(e=e.filter((e=>{var t;return(null===(t=e.result)||void 0===t?void 0:t.verdict)===T}))),X&&2===X.length&&(e=e.filter((e=>V()(e.createdAt).isBetween(X[0],X[1],"day","[]")))),z(e),me(e)})()}),[Y,T,X,A]),(0,a.useEffect)((()=>{const e=()=>{ae(window.innerWidth<768),ne(window.innerWidth>=768&&window.innerWidth<1024)};return window.addEventListener("resize",e),()=>window.removeEventListener("resize",e)}),[]);const xe=e=>"Pass"===e?(0,_.jsx)(Q.e6w,{className:"w-5 h-5 text-green-600"}):(0,_.jsx)(Q.lhV,{className:"w-5 h-5 text-red-600"}),ue=e=>{ce(e),oe(!0)},he=()=>{oe(!1),ce(null)},ge=(()=>{const e=window.innerWidth<768,t=window.innerWidth>=768&&window.innerWidth<1024,l=[{title:"Exam",dataIndex:"examName",key:"examName",render:(t,l)=>{var a,s;return(0,_.jsxs)("div",{className:"min-w-0",children:[(0,_.jsx)("div",{className:"font-semibold text-gray-900 text-sm sm:text-base truncate",children:(null===(a=l.exam)||void 0===a?void 0:a.name)||"Unnamed Exam"}),(0,_.jsx)("div",{className:"text-xs sm:text-sm text-gray-500 truncate",children:(null===(s=l.exam)||void 0===s?void 0:s.subject)||"General"}),e&&(0,_.jsxs)("div",{className:"flex items-center justify-between mt-2",children:[(0,_.jsx)("div",{className:"text-xs text-gray-400",children:V()(l.createdAt).format("MMM DD, YYYY")}),(0,_.jsx)(i.ZP,{type:"link",size:"small",icon:(0,_.jsx)(Q.f7Q,{}),onClick:()=>ue(l),className:"text-blue-500 p-0 h-auto",children:"View"})]})]})},width:e?180:t?200:250,ellipsis:!0},{title:"Score",dataIndex:"score",key:"score",render:(e,t)=>{var l,a,s;const n=(null===(l=t.result)||void 0===l||null===(a=l.correctAnswers)||void 0===a?void 0:a.length)||0,r=(null===(s=t.exam)||void 0===s?void 0:s.totalMarks)||1,o=Math.round(n/r*100);return(0,_.jsxs)("div",{className:"text-center",children:[(0,_.jsxs)("div",{className:"text-sm sm:text-base font-bold text-gray-900",children:[n,"/",r]}),(0,_.jsx)(c.Z,{percent:o,size:"small",strokeColor:o>=60?"#10b981":"#ef4444",showInfo:!1,className:"mb-1"}),(0,_.jsxs)("div",{className:"text-xs sm:text-sm font-medium ".concat((i=o,i>=80?"text-green-600":i>=60?"text-blue-600":i>=40?"text-orange-600":"text-red-600")),children:[o,"%"]})]});var i},width:e?80:120,sorter:(e,t)=>{var l,a,s,n,r,o;return Math.round(((null===(l=e.result)||void 0===l||null===(a=l.correctAnswers)||void 0===a?void 0:a.length)||0)/((null===(s=e.exam)||void 0===s?void 0:s.totalMarks)||1)*100)-Math.round(((null===(n=t.result)||void 0===n||null===(r=n.correctAnswers)||void 0===r?void 0:r.length)||0)/((null===(o=t.exam)||void 0===o?void 0:o.totalMarks)||1)*100)}},{title:"Result",dataIndex:"verdict",key:"verdict",render:(t,l)=>{var a;const s=null===(a=l.result)||void 0===a?void 0:a.verdict,n="Pass"===s;return(0,_.jsx)(d.Z,{icon:e?null:xe(s),color:n?"success":"error",className:"font-medium text-xs sm:text-sm",children:e?n?"P":"F":s||"N/A"})},width:e?50:100,filters:e?void 0:[{text:"Pass",value:"Pass"},{text:"Fail",value:"Fail"}],onFilter:e?void 0:(e,t)=>{var l;return(null===(l=t.result)||void 0===l?void 0:l.verdict)===e}}];return e||l.splice(1,0,{title:"Date",dataIndex:"createdAt",key:"date",render:e=>(0,_.jsxs)("div",{className:"text-sm",children:[(0,_.jsx)("div",{className:"font-medium",children:V()(e).format("MMM DD, YYYY")}),(0,_.jsx)("div",{className:"text-gray-500",children:V()(e).format("HH:mm")})]}),width:t?100:120}),e||l.push({title:"Actions",key:"actions",render:(e,l)=>(0,_.jsx)(i.ZP,{type:"primary",size:"small",icon:(0,_.jsx)(Q.f7Q,{}),onClick:()=>ue(l),className:"bg-blue-500 hover:bg-blue-600",children:t?"":"View"}),width:t?60:80}),l})();return(0,_.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50",children:[(0,_.jsx)(s.Z,{title:"Performance Reports"}),(0,_.jsxs)("div",{className:"max-w-7xl mx-auto px-3 sm:px-4 md:px-6 lg:px-8 py-4 sm:py-6 lg:py-8",children:[(0,_.jsxs)(G.E.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:"text-center mb-8 sm:mb-10 lg:mb-12",children:[(0,_.jsx)("div",{className:"inline-flex items-center justify-center w-12 h-12 sm:w-14 sm:h-14 lg:w-16 lg:h-16 bg-gradient-to-r from-blue-600 to-indigo-600 rounded-xl sm:rounded-2xl mb-4 sm:mb-6 shadow-lg",children:(0,_.jsx)(Q.hWk,{className:"w-6 h-6 sm:w-7 sm:h-7 lg:w-8 lg:h-8 text-white"})}),(0,_.jsxs)("h1",{className:"text-2xl sm:text-3xl lg:text-4xl font-bold text-gray-900 mb-3 sm:mb-4 px-4",children:["Your ",(0,_.jsx)("span",{className:"text-transparent bg-clip-text bg-gradient-to-r from-blue-600 to-indigo-600",children:"Performance"})," Journey"]}),(0,_.jsx)("p",{className:"text-sm sm:text-base lg:text-lg text-gray-600 max-w-xl sm:max-w-2xl mx-auto px-4",children:"Track your progress, analyze your performance, and celebrate your achievements"})]}),(0,_.jsxs)(G.E.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.1},className:"grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-5 gap-3 sm:gap-4 lg:gap-6 mb-6 sm:mb-8",children:[(0,_.jsx)(m.Z,{className:"text-center hover:shadow-lg transition-all duration-300 border-0 bg-gradient-to-br from-blue-50 to-blue-100",children:(0,_.jsxs)("div",{className:"flex flex-col items-center p-2 sm:p-4",children:[(0,_.jsx)("div",{className:"w-8 h-8 sm:w-10 sm:h-10 lg:w-12 lg:h-12 bg-blue-500 rounded-full flex items-center justify-center mb-2 sm:mb-3",children:(0,_.jsx)(Q.Chd,{className:"w-4 h-4 sm:w-5 sm:h-5 lg:w-6 lg:h-6 text-white"})}),(0,_.jsx)(x.Z,{title:"Total Exams",value:ee.totalExams,valueStyle:{color:"#1e40af",fontWeight:"bold"},className:"responsive-statistic"})]})}),(0,_.jsx)(m.Z,{className:"text-center hover:shadow-lg transition-all duration-300 border-0 bg-gradient-to-br from-green-50 to-green-100",children:(0,_.jsxs)("div",{className:"flex flex-col items-center p-2 sm:p-4",children:[(0,_.jsx)("div",{className:"w-8 h-8 sm:w-10 sm:h-10 lg:w-12 lg:h-12 bg-green-500 rounded-full flex items-center justify-center mb-2 sm:mb-3",children:(0,_.jsx)(Q.e6w,{className:"w-4 h-4 sm:w-5 sm:h-5 lg:w-6 lg:h-6 text-white"})}),(0,_.jsx)(x.Z,{title:"Passed",value:ee.passedExams,valueStyle:{color:"#059669",fontWeight:"bold"},className:"responsive-statistic"})]})}),(0,_.jsx)(m.Z,{className:"text-center hover:shadow-lg transition-all duration-300 border-0 bg-gradient-to-br from-purple-50 to-purple-100",children:(0,_.jsxs)("div",{className:"flex flex-col items-center p-2 sm:p-4",children:[(0,_.jsx)("div",{className:"w-8 h-8 sm:w-10 sm:h-10 lg:w-12 lg:h-12 bg-purple-500 rounded-full flex items-center justify-center mb-2 sm:mb-3",children:(0,_.jsx)(Q.ehl,{className:"w-4 h-4 sm:w-5 sm:h-5 lg:w-6 lg:h-6 text-white"})}),(0,_.jsx)(x.Z,{title:"Average Score",value:ee.averageScore,suffix:"%",valueStyle:{color:"#7c3aed",fontWeight:"bold"},className:"responsive-statistic"})]})}),(0,_.jsx)(m.Z,{className:"text-center hover:shadow-lg transition-all duration-300 border-0 bg-gradient-to-br from-orange-50 to-orange-100",children:(0,_.jsxs)("div",{className:"flex flex-col items-center p-2 sm:p-4",children:[(0,_.jsx)("div",{className:"w-8 h-8 sm:w-10 sm:h-10 lg:w-12 lg:h-12 bg-orange-500 rounded-full flex items-center justify-center mb-2 sm:mb-3",children:(0,_.jsx)(Q.gBl,{className:"w-4 h-4 sm:w-5 sm:h-5 lg:w-6 lg:h-6 text-white"})}),(0,_.jsx)(x.Z,{title:"Best Score",value:ee.bestScore,suffix:"%",valueStyle:{color:"#ea580c",fontWeight:"bold"},className:"responsive-statistic"})]})}),(0,_.jsx)(m.Z,{className:"text-center hover:shadow-lg transition-all duration-300 border-0 bg-gradient-to-br from-pink-50 to-pink-100 sm:col-span-3 lg:col-span-1",children:(0,_.jsxs)("div",{className:"flex flex-col items-center p-2 sm:p-4",children:[(0,_.jsx)("div",{className:"w-8 h-8 sm:w-10 sm:h-10 lg:w-12 lg:h-12 bg-pink-500 rounded-full flex items-center justify-center mb-2 sm:mb-3",children:(0,_.jsx)(Q.p8m,{className:"w-4 h-4 sm:w-5 sm:h-5 lg:w-6 lg:h-6 text-white"})}),(0,_.jsx)(x.Z,{title:"Best Streak",value:ee.streak,valueStyle:{color:"#db2777",fontWeight:"bold"},className:"responsive-statistic"})]})})]}),(0,_.jsx)(G.E.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.2},className:"bg-white rounded-xl sm:rounded-2xl shadow-lg p-4 sm:p-6 mb-6 sm:mb-8 border border-gray-100",children:(0,_.jsxs)("div",{className:"flex flex-col gap-4",children:[(0,_.jsxs)("div",{className:"flex items-center gap-2",children:[(0,_.jsx)(Q.a9n,{className:"w-5 h-5 text-gray-600"}),(0,_.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"Filter Results"})]}),(0,_.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4",children:[(0,_.jsxs)("div",{className:"space-y-2",children:[(0,_.jsx)("label",{className:"text-sm font-medium text-gray-700",children:"Subject"}),(0,_.jsxs)(n.default,{placeholder:"All Subjects",value:Y,onChange:B,className:"w-full",size:"large",children:[(0,_.jsx)(q,{value:"all",children:"All Subjects"}),(()=>{const e=A.map((e=>{var t;return null===(t=e.exam)||void 0===t?void 0:t.subject})).filter(Boolean);return[...new Set(e)]})().map((e=>(0,_.jsx)(q,{value:e,children:e},e)))]})]}),(0,_.jsxs)("div",{className:"space-y-2",children:[(0,_.jsx)("label",{className:"text-sm font-medium text-gray-700",children:"Result"}),(0,_.jsxs)(n.default,{placeholder:"All Results",value:T,onChange:D,className:"w-full",size:"large",children:[(0,_.jsx)(q,{value:"all",children:"All Results"}),(0,_.jsx)(q,{value:"Pass",children:"Passed"}),(0,_.jsx)(q,{value:"Fail",children:"Failed"})]})]}),(0,_.jsxs)("div",{className:"space-y-2 sm:col-span-2 lg:col-span-1",children:[(0,_.jsx)("label",{className:"text-sm font-medium text-gray-700",children:"Date Range"}),(0,_.jsx)(J,{value:X,onChange:K,className:"w-full",size:"large",placeholder:["From","To"],format:"DD/MM/YYYY",allowClear:!0})]}),(0,_.jsxs)("div",{className:"space-y-2",children:[(0,_.jsx)("label",{className:"text-sm font-medium text-gray-700 opacity-0",children:"Actions"}),(0,_.jsx)(i.ZP,{onClick:()=>{B("all"),D("all"),K(null)},size:"large",className:"w-full",icon:(0,_.jsx)(Q.lhV,{}),children:"Clear All"})]})]}),("all"!==Y||"all"!==T||X)&&(0,_.jsxs)("div",{className:"flex flex-wrap gap-2 pt-3 border-t border-gray-100",children:[(0,_.jsx)("span",{className:"text-sm font-medium text-gray-700",children:"Active filters:"}),"all"!==Y&&(0,_.jsx)(d.Z,{closable:!0,onClose:()=>B("all"),className:"bg-blue-50 border-blue-200 text-blue-700",children:Y}),"all"!==T&&(0,_.jsx)(d.Z,{closable:!0,onClose:()=>D("all"),className:"Pass"===T?"bg-green-50 border-green-200 text-green-700":"bg-red-50 border-red-200 text-red-700",children:T}),X&&(0,_.jsxs)(d.Z,{closable:!0,onClose:()=>K(null),className:"bg-purple-50 border-purple-200 text-purple-700",children:[X[0].format("DD/MM/YY")," - ",X[1].format("DD/MM/YY")]})]})]})}),(0,_.jsx)(G.E.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.3},className:"bg-white rounded-xl sm:rounded-2xl shadow-lg overflow-hidden border border-gray-100",children:(0,_.jsx)(u.Z,{columns:ge,dataSource:I,rowKey:e=>e._id,pagination:{pageSize:window.innerWidth<768?5:10,showSizeChanger:window.innerWidth>=768,showQuickJumper:window.innerWidth>=768,showTotal:(e,t)=>window.innerWidth>=640?"".concat(t[0],"-").concat(t[1]," of ").concat(e," results"):"".concat(t[0],"-").concat(t[1]," / ").concat(e),className:"px-3 sm:px-6 py-2 sm:py-4",simple:window.innerWidth<640},scroll:{x:window.innerWidth<768?600:800},className:"modern-table",size:window.innerWidth<768?"middle":"large",locale:{emptyText:(0,_.jsx)(h.Z,{image:h.Z.PRESENTED_IMAGE_SIMPLE,description:(0,_.jsxs)("div",{className:"py-8",children:[(0,_.jsx)("h3",{className:"text-base sm:text-lg font-medium text-gray-900 mb-2",children:"No exam results found"}),(0,_.jsx)("p",{className:"text-sm sm:text-base text-gray-500 px-4",children:"Try adjusting your filters or take some exams to see your results here."})]})})}})}),(0,_.jsx)(g.Z,{title:(0,_.jsxs)("div",{className:"flex items-center gap-3",children:[(0,_.jsx)("div",{className:"w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center",children:(0,_.jsx)(Q.f7Q,{className:"w-4 h-4 text-white"})}),(0,_.jsx)("span",{className:"text-lg font-semibold",children:"Exam Details"})]}),open:re,onCancel:he,footer:[(0,_.jsx)(i.ZP,{onClick:he,size:"large",children:"Close"},"close")],width:le?"95%":se?600:700,className:"exam-details-modal",children:ie&&(0,_.jsxs)("div",{className:"space-y-6",children:[(0,_.jsxs)("div",{className:"bg-gray-50 rounded-lg p-4",children:[(0,_.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Exam Information"}),(0,_.jsxs)(F,{column:le?1:2,size:"small",children:[(0,_.jsx)(F.Item,{label:"Exam Name",span:le?1:2,children:(0,_.jsx)("span",{className:"font-medium",children:(null===(e=ie.exam)||void 0===e?void 0:e.name)||"N/A"})}),(0,_.jsx)(F.Item,{label:"Subject",children:(null===(t=ie.exam)||void 0===t?void 0:t.subject)||"General"}),(0,_.jsx)(F.Item,{label:"Date Taken",children:V()(ie.createdAt).format("MMMM DD, YYYY [at] HH:mm")}),(0,_.jsx)(F.Item,{label:"Total Questions",children:(null===(l=ie.exam)||void 0===l?void 0:l.totalMarks)||0}),(0,_.jsx)(F.Item,{label:"Passing Marks",children:(null===(r=ie.exam)||void 0===r?void 0:r.passingMarks)||0})]})]}),(0,_.jsxs)("div",{className:"bg-blue-50 rounded-lg p-4",children:[(0,_.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Performance Summary"}),(0,_.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-3 gap-4",children:[(0,_.jsxs)("div",{className:"text-center p-3 bg-white rounded-lg",children:[(0,_.jsx)("div",{className:"text-2xl font-bold text-blue-600",children:(null===(p=ie.result)||void 0===p||null===(v=p.correctAnswers)||void 0===v?void 0:v.length)||0}),(0,_.jsx)("div",{className:"text-sm text-gray-600",children:"Correct Answers"})]}),(0,_.jsxs)("div",{className:"text-center p-3 bg-white rounded-lg",children:[(0,_.jsx)("div",{className:"text-2xl font-bold text-gray-600",children:((null===(b=ie.exam)||void 0===b?void 0:b.totalMarks)||0)-((null===(f=ie.result)||void 0===f||null===(y=f.correctAnswers)||void 0===y?void 0:y.length)||0)}),(0,_.jsx)("div",{className:"text-sm text-gray-600",children:"Wrong Answers"})]}),(0,_.jsxs)("div",{className:"text-center p-3 bg-white rounded-lg",children:[(0,_.jsxs)("div",{className:"text-2xl font-bold ".concat(Math.round(((null===(w=ie.result)||void 0===w||null===(j=w.correctAnswers)||void 0===j?void 0:j.length)||0)/((null===(N=ie.exam)||void 0===N?void 0:N.totalMarks)||1)*100)>=60?"text-green-600":"text-red-600"),children:[Math.round(((null===(S=ie.result)||void 0===S||null===(E=S.correctAnswers)||void 0===E?void 0:E.length)||0)/((null===(k=ie.exam)||void 0===k?void 0:k.totalMarks)||1)*100),"%"]}),(0,_.jsx)("div",{className:"text-sm text-gray-600",children:"Score"})]})]})]}),(0,_.jsxs)("div",{className:"rounded-lg p-4 ".concat("Pass"===(null===(C=ie.result)||void 0===C?void 0:C.verdict)?"bg-green-50 border border-green-200":"bg-red-50 border border-red-200"),children:[(0,_.jsxs)("div",{className:"flex items-center justify-center gap-3",children:[xe(null===(M=ie.result)||void 0===M?void 0:M.verdict),(0,_.jsx)("span",{className:"text-xl font-semibold ".concat("Pass"===(null===(P=ie.result)||void 0===P?void 0:P.verdict)?"text-green-700":"text-red-700"),children:"Pass"===(null===(O=ie.result)||void 0===O?void 0:O.verdict)?"Congratulations! You Passed":"Keep Trying! You Can Do Better"})]}),(0,_.jsx)("div",{className:"text-center mt-2",children:(0,_.jsx)("span",{className:"text-sm text-gray-600",children:"Pass"===(null===(W=ie.result)||void 0===W?void 0:W.verdict)?"Great job on passing this exam!":"Review the material and try again to improve your score."})})]})]})})]})]})}}}]);
//# sourceMappingURL=718.c97cf0fb.chunk.js.map