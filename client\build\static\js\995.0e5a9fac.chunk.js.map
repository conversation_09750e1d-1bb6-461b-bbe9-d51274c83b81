{"version": 3, "file": "static/js/995.0e5a9fac.chunk.js", "mappings": "6JAAA,MAAQA,QAASC,GAAkBC,EAAQ,MAG9BC,EAAYC,UACrB,IAEI,aADuBH,EAAcI,KAAK,0BAA2BC,IACrDC,IACpB,CAAE,MAAOC,GACL,OAAOA,EAAMC,SAASF,IAC1B,GAISG,EAAgBN,UACzB,IAEI,aADuBH,EAAcI,KAAK,+BAAgCM,IAC1DJ,IACpB,CAAE,MAAOC,GACL,OAAOA,EAAMC,SAASF,IAC1B,GAISK,EAAsBR,UAC/B,IAEI,aADuBH,EAAcI,KAAK,uCAAwCC,IAClEC,IACpB,CAAE,MAAOC,GACL,OAAOA,EAAMC,SAASF,IAC1B,GAISM,EAA0BT,UACnC,IAEI,aADuBH,EAAca,IAAI,6CACzBP,IACpB,CAAE,MAAOC,GACL,OAAOA,EAAMC,SAASF,IAC1B,GAaSQ,EAAmBX,iBAAyB,IAAlBY,EAAOC,UAAAC,OAAA,QAAAC,IAAAF,UAAA,GAAAA,UAAA,GAAG,CAAC,EAC9C,IACI,MAAMG,EAAS,IAAIC,gBACfL,EAAQM,OAAOF,EAAOG,OAAO,QAASP,EAAQM,OAC9CN,EAAQQ,aAAaJ,EAAOG,OAAO,cAAeP,EAAQQ,aAC1DR,EAAQS,aAAaL,EAAOG,OAAO,cAAeP,EAAQS,aAC1DT,EAAQU,cAAcN,EAAOG,OAAO,eAAgBP,EAAQU,cAC5DV,EAAQW,iBAAiBP,EAAOG,OAAO,kBAAmBP,EAAQW,iBAGtE,aADuB1B,EAAca,IAAI,4BAADc,OAA6BR,EAAOS,cAC5DtB,IACpB,CAAE,MAAOC,GACL,OAAOA,EAAMC,SAASF,IAC1B,CACJ,EAEauB,EAAiB1B,eAAO2B,GAAyB,IAAjBC,EAAOf,UAAAC,OAAA,QAAAC,IAAAF,UAAA,GAAAA,UAAA,GAAG,EACnD,IAEI,aADuBhB,EAAca,IAAI,0BAADc,OAA2BG,EAAM,aAAAH,OAAYI,KACrEzB,IACpB,CAAE,MAAOC,GACL,OAAOA,EAAMC,SAASF,IAC1B,CACJ,C,wKClDA,MA02FA,EA12F2B0B,KACzB,MACMC,GADYC,EAAAA,EAAAA,KAAaC,GAAUA,EAAMC,OAAS,CAAC,IAC7BC,MAAQ,KAG9BC,EAAmB,MACvB,IACE,MAAMC,EAAWC,aAAaC,QAAQ,QACtC,OAAOF,EAAWG,KAAKC,MAAMJ,GAAY,IAC3C,CAAE,MAAAK,GACA,OAAO,IACT,CACD,EAPwB,GASnBC,EAAY,MAChB,IACE,MAAMC,EAAQN,aAAaC,QAAQ,SACnC,GAAIK,EAAO,CAET,OADgBJ,KAAKC,MAAMI,KAAKD,EAAME,MAAM,KAAK,IAEnD,CACA,OAAO,IACT,CAAE,MAAAC,GACA,OAAO,IACT,CACD,EAXiB,GAcZZ,EAAOJ,GAAaK,GAAoBO,GAGvCK,EAAcC,IAAmBC,EAAAA,EAAAA,UAAS,MAGjDC,QAAQC,IAAI,kCAAyB,CACnCC,MAAOtB,EACPO,aAAcF,EACdQ,MAAOD,EACPW,MAAOnB,IAILA,IAASa,GACXG,QAAQC,IAAI,sCAA6BjB,EAAKP,QAEhD,MAAM2B,GAAWC,EAAAA,EAAAA,OACVC,EAAaC,IAAkBR,EAAAA,EAAAA,UAAS,KACxCS,EAASC,IAAcV,EAAAA,EAAAA,WAAS,IAChCW,EAAiBC,IAAsBZ,EAAAA,EAAAA,UAAS,OAChDa,EAAUC,IAAed,EAAAA,EAAAA,UAAS,WAClCe,EAAWC,IAAgBhB,EAAAA,EAAAA,WAAS,IACpCiB,EAAgBC,IAAqBlB,EAAAA,EAAAA,UAAS,IAC9CmB,EAAmBC,IAAwBpB,EAAAA,EAAAA,UAAS,KAEpDqB,EAAmBC,IAAwBtB,EAAAA,EAAAA,UAAS,OACpDuB,EAAaC,IAAkBxB,EAAAA,EAAAA,UAAS,KACxCyB,EAAgBC,IAAqB1B,EAAAA,EAAAA,WAAS,IAC9C2B,EAAgBC,IAAqB5B,EAAAA,EAAAA,UAAS,OAC9C6B,EAAcC,IAAmB9B,EAAAA,EAAAA,UAAS,CAAC,IAC3C+B,EAAkBC,IAAuBhC,EAAAA,EAAAA,WAAS,IAClDiC,EAAqBC,IAA0BlC,EAAAA,EAAAA,WAAS,GAGzDmC,GAAaC,EAAAA,EAAAA,QAAO,CAAC,GAGrBC,IAFYD,EAAAA,EAAAA,QAAO,OACFA,EAAAA,EAAAA,QAAO,OACRA,EAAAA,EAAAA,QAAO,OACvBE,GAAcF,EAAAA,EAAAA,QAAO,MAGrBG,EAAqB,CACzB,gEACA,mEACA,yEACA,mEACA,uEACA,iEACA,oEACA,wGACA,+DACA,qEAIIC,EAAe,CACnBC,OAAQ,CACNC,IAAK,IACLC,MAAO,yDACPC,QAAS,qEACTC,UAAW,UACXC,UAAW,UACXC,YAAa,0BACbC,KAAM,qBACNC,KAAMC,EAAAA,IACNC,MAAO,SACPC,YAAa,mBACbC,YAAa,UACbC,OAAQ,cACRC,WAAY,eACZC,YAAa,EACbC,aAAc,IACdC,SAAU,IAEZC,UAAW,CACTjB,IAAK,KACLC,MAAO,0DACPC,QAAS,wEACTC,UAAW,UACXC,UAAW,UACXC,YAAa,0BACbC,KAAM,uBACNC,KAAMW,EAAAA,IACNT,MAAO,YACPC,YAAa,iBACbC,YAAa,UACbC,OAAQ,oBACRC,WAAY,eACZC,YAAa,IACbC,aAAc,IACdC,SAAU,IAEZG,QAAS,CACPnB,IAAK,KACLC,MAAO,0DACPC,QAAS,sEACTC,UAAW,UACXC,UAAW,UACXC,YAAa,yBACbC,KAAM,qBACNC,KAAMa,EAAAA,IACNX,MAAO,UACPC,YAAa,eACbC,YAAa,UACbC,OAAQ,gBACRC,WAAY,qBACZC,YAAa,KACbC,aAAc,IACdC,SAAU,IAEZK,SAAU,CACRrB,IAAK,IACLC,MAAO,wDACPC,QAAS,qEACTC,UAAW,UACXC,UAAW,UACXC,YAAa,2BACbC,KAAM,sBACNC,KAAMe,EAAAA,IACNb,MAAO,WACPC,YAAa,WACbC,YAAa,UACbC,OAAQ,iBACRC,WAAY,eACZC,YAAa,KACbC,aAAc,IACdC,SAAU,KAEZO,KAAM,CACJvB,IAAK,IACLC,MAAO,0DACPC,QAAS,yEACTC,UAAW,UACXC,UAAW,UACXC,YAAa,yBACbC,KAAM,uBACNC,KAAMiB,EAAAA,IACNf,MAAO,OACPC,YAAa,UACbC,YAAa,UACbC,OAAQ,YACRC,WAAY,eACZC,YAAa,IACbC,aAAc,IACdC,SAAU,KAEZS,OAAQ,CACNzB,IAAK,KACLC,MAAO,uDACPC,QAAS,qEACTC,UAAW,UACXC,UAAW,UACXC,YAAa,2BACbC,KAAM,qBACNC,KAAMmB,EAAAA,IACNjB,MAAO,SACPC,YAAa,YACbC,YAAa,UACbC,OAAQ,iBACRC,WAAY,eACZC,YAAa,IACbC,aAAc,IACdC,SAAU,KAEZW,OAAQ,CACN3B,IAAK,IACLC,MAAO,6DACPC,QAAS,yEACTC,UAAW,UACXC,UAAW,UACXC,YAAa,0BACbC,KAAM,uBACNC,KAAMqB,EAAAA,IACNnB,MAAO,SACPC,YAAa,WACbC,YAAa,UACbC,OAAQ,cACRC,WAAY,eACZC,YAAa,KACbC,aAAc,IACdC,SAAU,KAEZa,OAAQ,CACN7B,IAAK,EACLC,MAAO,0DACPC,QAAS,wEACTC,UAAW,UACXC,UAAW,UACXC,YAAa,yBACbC,KAAM,sBACNC,KAAMuB,EAAAA,IACNrB,MAAO,SACPC,YAAa,eACbC,YAAa,UACbC,OAAQ,cACRC,WAAY,eACZC,YAAa,IACbC,aAAc,EACdC,SAAU,MAKRe,EAAiBC,IACrB,IAAK,MAAOC,EAAQC,KAAWC,OAAOC,QAAQtC,GAC5C,GAAIkC,GAAME,EAAOlC,IAAK,OAAAqC,EAAAA,EAAAA,GAAA,CAASJ,UAAWC,GAE5C,OAAAG,EAAAA,EAAAA,GAAA,CAASJ,OAAQ,UAAanC,EAAa+B,OAAM,EAI7CS,EAAsBhG,IAC1B,MAAMiG,EAAU,CAAC,EAqBjB,OAnBAjG,EAAMkG,SAAQjG,IACZ,MAAMkG,EAAaV,EAAcxF,EAAKmG,SACjCH,EAAQE,EAAWR,UACtBM,EAAQE,EAAWR,QAAU,CAC3BC,OAAQO,EACRnG,MAAO,KAGXiG,EAAQE,EAAWR,QAAQ3F,MAAMqG,MAAIN,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAC,CAAC,EAClC9F,GAAI,IACPqG,KAAMH,IACN,IAIJN,OAAOU,KAAKN,GAASC,SAAQM,IAC3BP,EAAQO,GAAWxG,MAAMyG,MAAK,CAACC,EAAGC,IAAMA,EAAEP,QAAUM,EAAEN,SAAQ,IAGzDH,CAAO,EAIVW,EAA2BA,CAACC,EAAUC,KAC1C,IAAKA,EAAa,OAAO,KAEzB,MAAMX,EAAaV,EAAcqB,EAAYV,SAAW,GAClD7D,EAAcsE,EAASE,QAAO9G,GACnBwF,EAAcxF,EAAKmG,SACpBT,SAAWQ,EAAWR,SACnCc,MAAK,CAACC,EAAGC,IAAMA,EAAEP,QAAUM,EAAEN,UAEhC,MAAO,CACLT,OAAQQ,EACRnG,MAAOuC,EACPyE,SAAUzE,EAAY0E,WAAUC,GAAKA,EAAEC,MAAQL,EAAYK,MAAO,EAClEC,cAAe7E,EAAY1D,OAC5B,EAIGwI,GAAsBb,IAAe,IAADc,EACxCrG,QAAQC,IAAI,gCAAuBsF,GAGnC5D,EAAkB4D,GAClB9D,GAAkB,GAClBF,GAAsC,QAAvB8E,EAAAzE,EAAa2D,UAAU,IAAAc,OAAA,EAAvBA,EAAyBtH,QAAS,IAGjDuH,YAAW,KACT,MAAMC,EAAgBC,SAASC,cAAc,iBAADnI,OAAkBiH,EAAS,QAClDiB,SAASE,eAAe,UAADpI,OAAWiH,KAClCrD,EAAWyE,QAAQpB,GAEpCgB,IACFA,EAAcK,eAAe,CAC3BC,SAAU,SACVC,MAAO,SACPC,OAAQ,YAIVR,EAAcS,MAAMC,UAAY,cAChCV,EAAcS,MAAME,WAAa,gBACjCX,EAAcS,MAAMG,UAAY,mCAEhCb,YAAW,KACTC,EAAcS,MAAMC,UAAY,WAChCV,EAAcS,MAAMG,UAAY,EAAE,GACjC,KACL,GACC,IAAI,EAIHC,GAAoBA,IACJ,CAAC,SAAU,YAAa,UAAW,WAAY,OAAQ,SAAU,SAAU,UAC5EtB,QAAOpB,GAAU9C,EAAa8C,IAAW9C,EAAa8C,GAAQ3F,MAAMnB,OAAS,IAM5FyJ,GAAmBvK,iBAAiC,IAA1BwK,EAAY3J,UAAAC,OAAA,QAAAC,IAAAF,UAAA,IAAAA,UAAA,GAC1C,IAEE,MAAM4J,GAAmB,OAAJvI,QAAI,IAAJA,OAAI,EAAJA,EAAMwI,QAAS,UASpC,GARkB,CAAC,UAAW,YAAa,WACjCvC,SAAQuC,IACZA,IAAUD,IACZpI,aAAasI,WAAW,iBAADnJ,OAAkBkJ,IACzCrI,aAAasI,WAAW,sBAADnJ,OAAuBkJ,IAChD,KAGGF,EAAc,CACjB,MAAMI,GAAgB,OAAJ1I,QAAI,IAAJA,OAAI,EAAJA,EAAMwI,QAAS,UAC3BG,EAAgBxI,aAAaC,QAAQ,iBAADd,OAAkBoJ,IACtDE,EAAYzI,aAAaC,QAAQ,sBAADd,OAAuBoJ,IACvDG,EAAMC,KAAKD,MAGjB,GAAIF,GAAiBC,GAAcC,EAAME,SAASH,GAAc,KAAQ,CACtE,MAAMI,EAAS3I,KAAKC,MAAMqI,GAI1B,OAHApH,EAAeyH,EAAO/K,MAAQ,IAC9B0D,EAAmBqH,EAAOjC,eAC1BtF,GAAW,EAEb,CACF,CAEAA,GAAW,GACXT,QAAQC,IAAI,oDAA2CqH,EAAe,kBAAoB,IAG1F,IACEtH,QAAQC,IAAI,2CACZ,MAAMgI,QAA8BxK,EAAAA,EAAAA,KAAgBqH,EAAAA,EAAAA,GAAC,CACnD9G,MAAO,IACPG,aAAiB,OAAJa,QAAI,IAAJA,OAAI,EAAJA,EAAMwI,QAAS,MAC5BnJ,iBAAiB,GAEbiJ,GAAgB,CAAEY,GAAIJ,KAAKD,SAKjC,GAFA7H,QAAQC,IAAI,kCAA8BgI,GAEtCA,GAAyBA,EAAsBE,SAAWF,EAAsBhL,KAAM,CACxF+C,QAAQC,IAAI,+CAGZ,MAAMmI,EAAeH,EAAsBhL,KAAK6I,QAAO5G,GACpDA,EAASiG,SAAWjG,EAASiG,QAAU,GACvCjG,EAASmJ,mBAAqBnJ,EAASmJ,kBAAoB,IAI9DrI,QAAQC,IAAI,2CAAkCmI,EAAaE,MAAM,EAAG,GAAGC,KAAItC,IAAC,CAC1EC,IAAKD,EAAEC,IACPsC,KAAMvC,EAAEuC,KACRC,aAAcxC,EAAEwC,aAChBC,eAAgBzC,EAAEyC,eAClBC,kBAAmB1C,EAAEwC,eAAgBxC,EAAEyC,qBAGzC,MAAME,EAAkBR,EAAaG,KAAI,CAACrJ,EAAU2J,KAAK,CACvD3C,IAAKhH,EAASgH,IACdsC,KAAMtJ,EAASsJ,MAAQ,qBACvBM,MAAO5J,EAAS4J,OAAS,GACzBC,MAAO7J,EAAS6J,OAAS,GACzBvB,MAAOtI,EAASsI,OAAS,GACzBkB,eAAgBxJ,EAASuJ,cAAgBvJ,EAASwJ,gBAAkB,GACpED,aAAcvJ,EAASuJ,cAAgBvJ,EAASwJ,gBAAkB,GAClEvD,QAASjG,EAASiG,SAAW,EAC7BkD,kBAAmBnJ,EAASmJ,mBAAqB,EACjDW,aAAc9J,EAAS8J,cAAgB,EACvCC,cAAe/J,EAAS+J,eAAiB,EACzCC,WAAYhK,EAASgK,YAAc,EACnCC,mBAAoBjK,EAASiK,oBAAsB,OACnDC,KAAMP,EAAQ,EACdxD,KAAMb,EAActF,EAASiG,SAAW,GACxCkE,YAAY,EACZC,aAAcpK,EAASoK,cAAgB,EAEvC/B,aAAcrI,EAASqI,cAAgB,EACvCgC,cAAerK,EAASqK,eAAiB,IACzCC,WAAYtK,EAASsK,YAAc,EACnCC,SAAUvK,EAASuK,UAAY,EAC/BC,aAAcxK,EAASwK,cAAgB,GACvCC,WAAY,kBAId3J,QAAQC,IAAI,wCAA+B2I,EAAgBN,MAAM,EAAG,GAAGC,KAAItC,IAAC,CAC1EC,IAAKD,EAAEC,IACPsC,KAAMvC,EAAEuC,KACRC,aAAcxC,EAAEwC,aAChBC,eAAgBzC,EAAEyC,eAClBC,kBAAmB1C,EAAEwC,eAAgBxC,EAAEyC,qBAGzCnI,EAAeqI,GAGf,MAAMgB,EAAgBhB,EAAgB5C,WAAU6D,GAAQA,EAAK3D,OAAY,OAAJlH,QAAI,IAAJA,OAAI,EAAJA,EAAMkH,OAI3E,GAHAvF,EAAmBiJ,GAAiB,EAAIA,EAAgB,EAAI,MAGxD5K,EAAM,CACR,MAAM8K,EAAiBnE,EAAyBiD,EAAiB5J,GACjEqC,EAAqByI,GACrBvI,GAA6B,OAAduI,QAAc,IAAdA,OAAc,EAAdA,EAAgB/K,QAAS,GAC1C,CAGA,MAAMgL,EAAUhF,EAAmB6D,GACnC/G,EAAgBkI,GAGhB,MAAMrC,GAAgB,OAAJ1I,QAAI,IAAJA,OAAI,EAAJA,EAAMwI,QAAS,UAC3BwC,EAAY,CAChB/M,KAAM2L,EACN7C,SAAU6D,GAAiB,EAAIA,EAAgB,EAAI,MAMrD,OAJAzK,aAAa8K,QAAQ,iBAAD3L,OAAkBoJ,GAAarI,KAAK6K,UAAUF,IAClE7K,aAAa8K,QAAQ,sBAAD3L,OAAuBoJ,GAAaI,KAAKD,MAAMtJ,iBAEnEkC,GAAW,EAEb,CACF,CAAE,MAAO0J,GACPnK,QAAQC,IAAI,uDAA8CkK,EAC5D,CAKA,IAAIC,EAAiBC,EAFrBrK,QAAQC,IAAI,yDAIZ,IACED,QAAQC,IAAI,mDACZmK,QAAwB7M,EAAAA,EAAAA,MACxByC,QAAQC,IAAI,sCACZoK,QAAsBC,EAAAA,EAAAA,KACxB,CAAE,MAAOpN,GACP8C,QAAQC,IAAI,qCAAiC/C,GAC7C,IACEmN,QAAsBC,EAAAA,EAAAA,KACxB,CAAE,MAAOC,GACPvK,QAAQC,IAAI,gCAA4BsK,EAC1C,CACF,CAEA,IAAI3B,EAAkB,GAEtB,GAAIyB,GAAiBA,EAAclC,SAAWkC,EAAcpN,KAAM,CAChE+C,QAAQC,IAAI,+CAGZ,MAAMuK,EAAiB,CAAC,EACpBJ,GAAmBA,EAAgBjC,SAAWiC,EAAgBnN,MAChEmN,EAAgBnN,KAAKgI,SAAQ4E,IAAS,IAADY,EACnC,MAAMhM,GAAkB,QAATgM,EAAAZ,EAAK7K,YAAI,IAAAyL,OAAA,EAATA,EAAWvE,MAAO2D,EAAKpL,OAClCA,IACF+L,EAAe/L,GAAUoL,EAAKa,SAAW,GAC3C,IAIJ9B,EAAkByB,EAAcpN,KAC7B6I,QAAO5G,IAEN,IAAKA,IAAaA,EAASgH,IAAK,OAAO,EAGvC,IAAKhH,EAASyL,SAAe,OAAJ3L,QAAI,IAAJA,GAAAA,EAAMwI,MAAO,CACpC,MAAME,EAAY1I,EAAKwI,MAAMoD,cACvBC,GAAa3L,EAASsI,OAAS,WAAWoD,cAEhD,GAAkB,YAAdlD,EAEF,MAAqB,YAAdmD,EACF,GAAkB,cAAdnD,EAET,MAAqB,cAAdmD,EACF,GAAkB,YAAdnD,EAET,MAAqB,YAAdmD,CAEX,CAEA,OAAO,CAAI,IAEZtC,KAAI,CAACrJ,EAAU2J,KAEd,MAAMiC,EAAcN,EAAetL,EAASgH,MAAQ,GAGpD,IAAI6E,EAAeD,EAAYlN,QAAUsB,EAASmJ,mBAAqB,EACnE2C,EAAaF,EAAYG,QAAO,CAACC,EAAKC,IAAWD,GAAOC,EAAOC,OAAS,IAAI,GAC5EpC,EAAe+B,EAAe,EAAIM,KAAKC,MAAMN,EAAaD,GAAgB7L,EAAS8J,cAAgB,EAGvG,IAAK8B,EAAYlN,QAAUsB,EAASqM,YAAa,CAE/C,MAAMC,EAAmBH,KAAKI,IAAI,EAAGJ,KAAKK,MAAMxM,EAASqM,YAAc,MACjEI,EAAmBN,KAAK5I,IAAI,GAAI4I,KAAKI,IAAI,GAAI,GAAMvM,EAASqM,YAAcC,EAAmB,KAEnGT,EAAeS,EACfxC,EAAeqC,KAAKC,MAAMK,GAC1BX,EAAaK,KAAKC,MAAMtC,EAAe+B,GAEvC/K,QAAQC,IAAI,oCAAD3B,OAA2BY,EAASsJ,KAAI,MAAAlK,OAAKkN,EAAgB,cAAAlN,OAAaqN,EAAgB,eAAArN,OAAcY,EAASqM,YAAW,WACzI,CAGA,IAAIpG,EAAUjG,EAASiG,SAAW,EAE7BA,IAECjG,EAASqM,YAEXpG,EAAUkG,KAAKK,MACbxM,EAASqM,YACO,GAAfR,GACA/B,EAAe,GAAoB,GAAf+B,EAAoB,IACxC/B,EAAe,GAAoB,GAAf+B,EAAoB,IAElCA,EAAe,IAExB5F,EAAUkG,KAAKK,MACZ1C,EAAe+B,EAAe,EACf,GAAfA,GACA/B,EAAe,GAAoB,GAAf+B,EAAoB,MAM/C,IAAI9B,EAAgB/J,EAAS+J,eAAiB,EAC1CC,EAAahK,EAASgK,YAAc,EAExC,GAAI4B,EAAYlN,OAAS,EAAG,CAE1B,IAAIgO,EAAa,EACjBd,EAAY7F,SAAQkG,IACdA,EAAOC,OAAS,IAClBQ,IACA1C,EAAamC,KAAKI,IAAIvC,EAAY0C,IAElCA,EAAa,CACf,IAEF3C,EAAgB2C,CAClB,MAAO,GAAI1M,EAASqM,cAAgBtC,EAAe,CAEjD,MAAM4C,EAAgBd,EAAe,EAAI7L,EAASqM,YAAcR,EAAe,EAC3Ec,EAAgB,KAClB5C,EAAgBoC,KAAK5I,IAAIsI,EAAcM,KAAKK,MAAMG,EAAgB,KAClE3C,EAAamC,KAAKI,IAAIxC,EAAeoC,KAAKK,MAAMG,EAAgB,KAEpE,CAEA,MAAO,CACL3F,IAAKhH,EAASgH,IACdsC,KAAMtJ,EAASsJ,MAAQ,qBACvBM,MAAO5J,EAAS4J,OAAS,GACzBC,MAAO7J,EAAS6J,OAAS,GACzBvB,MAAOtI,EAASsI,OAAS,GACzBkB,eAAgBxJ,EAASuJ,cAAgBvJ,EAASwJ,gBAAkB,GACpED,aAAcvJ,EAASuJ,cAAgBvJ,EAASwJ,gBAAkB,GAClEvD,QAASA,EACTkD,kBAAmB0C,EACnB/B,aAAcA,EACdC,cAAeA,EACfC,WAAYA,EACZC,mBAAoBjK,EAASiK,oBAAsB,OACnDC,KAAMP,EAAQ,EACdxD,KAAMb,EAAcW,GACpBkE,YAAY,EAEZyC,eAAgB5M,EAASqM,aAAe,EACxCQ,WAAYjB,EAAYlN,OAAS,EACjC+L,WAAYmB,EAAYlN,OAAS,EAAI,UAAYsB,EAASqM,YAAc,gBAAkB,YAC3F,IAIL3C,EAAgBpD,MAAK,CAACC,EAAGC,IAAMA,EAAEP,QAAUM,EAAEN,UAG7CyD,EAAgB3D,SAAQ,CAACjG,EAAM6J,KAC7B7J,EAAKoK,KAAOP,EAAQ,CAAC,IAGvBtI,EAAeqI,GAGf,MAAMlB,GAAgB,OAAJ1I,QAAI,IAAJA,OAAI,EAAJA,EAAMwI,QAAS,UAC3BwC,EAAY,CAChB/M,KAAM2L,EACN7C,SAAU,MAIZ,IAAIA,GAAY,EAwBhB,GAvBI/G,IAEF+G,EAAW6C,EAAgB5C,WAAU6D,GAAQA,EAAK3D,MAAQlH,EAAKkH,OAG7C,IAAdH,IACFA,EAAW6C,EAAgB5C,WAAU6D,GAAQmC,OAAOnC,EAAK3D,OAAS8F,OAAOhN,EAAKkH,SAI9D,IAAdH,GAAmB/G,EAAKwJ,OAC1BzC,EAAW6C,EAAgB5C,WAAU6D,GAAQA,EAAKrB,OAASxJ,EAAKwJ,SAIpE7H,EAAmBoF,GAAY,EAAIA,EAAW,EAAI,MAGlDiE,EAAUjE,SAAWA,GAAY,EAAIA,EAAW,EAAI,KACpD5G,aAAa8K,QAAQ,iBAAD3L,OAAkBoJ,GAAarI,KAAK6K,UAAUF,IAClE7K,aAAa8K,QAAQ,sBAAD3L,OAAuBoJ,GAAaI,KAAKD,MAAMtJ,YAG/DS,EAAM,CACR,MAAM8K,EAAiBnE,EAAyBiD,EAAiB5J,GACjEqC,EAAqByI,GACrBvI,GAA6B,OAAduI,QAAc,IAAdA,OAAc,EAAdA,EAAgB/K,QAAS,GAC1C,CAGA,MAAMgL,EAAUhF,EAAmB6D,GACnC/G,EAAgBkI,GAqBhB,MAAMkC,EAAc,CAClBvB,QAAS9B,EAAgB9C,QAAOG,GAAsB,YAAjBA,EAAE0D,aAA0B/L,OACjEsO,cAAetD,EAAgB9C,QAAOG,GAAsB,kBAAjBA,EAAE0D,aAAgC/L,OAC7EuO,UAAWvD,EAAgB9C,QAAOG,GAAsB,cAAjBA,EAAE0D,aAA4B/L,QAGvEoC,QAAQC,IAAI,4CAAmC2I,EAAgBhL,OAAQ,kBACvEoC,QAAQC,IAAI,6BAAoBgM,GAChCjM,QAAQC,IAAI,gCAAuB2I,EAAgBN,MAAM,EAAG,GAAGC,KAAItC,IAAC,CAClEuC,KAAMvC,EAAEuC,KACR/D,GAAIwB,EAAEd,QACNiH,QAASnG,EAAEoC,kBACXgE,IAAKpG,EAAE+C,aACPsD,OAAQrG,EAAE0D,eAEd,MACE3J,QAAQC,IAAI,uCACZM,EAAe,IACfI,EAAmB,MACnB4L,EAAAA,GAAQC,QAAQ,2DAEpB,CAAE,MAAOtP,GACP8C,QAAQ9C,MAAM,4CAAmCA,GACjDqP,EAAAA,GAAQrP,MAAM,+DAChB,CAAC,QACCuD,GAAW,EACb,CACF,GA+DAgM,EAAAA,EAAAA,YAAU,KACR,IAAK5M,GAAoB,OAAJb,QAAI,IAAJA,GAAAA,EAAMP,QAAU6B,EAAY1C,OAAS,EAAG,CAC3DoC,QAAQC,IAAI,uDACZ,MAAMyM,EAAgBpM,EAAYqM,MAAK1G,GAAK+F,OAAO/F,EAAEC,OAAS8F,OAAOhN,EAAKP,UAC1E,GAAIiO,EAAe,CACjB1M,QAAQC,IAAI,qCAAiCyM,GAE7C,MAAME,GAAmB9H,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACpB4H,GAAa,IAChBhE,eAAgBgE,EAAcjE,cAAgBiE,EAAchE,gBAAkB,GAC9ED,aAAciE,EAAcjE,cAAgBiE,EAAchE,gBAAkB,KAE9E5I,EAAgB8M,EAClB,MACE5M,QAAQC,IAAI,+CAEhB,IACC,CAACK,EAAatB,EAAMa,KAGvB4M,EAAAA,EAAAA,YAAU,KACRpF,KAjFwBvK,WACxB,GAAS,OAAJkC,QAAI,IAAJA,GAAAA,EAAMP,OAKX,IACEuB,QAAQC,IAAI,mDAA0CjB,EAAKP,QAC3D,MAAMtB,QAAiBmN,EAAAA,EAAAA,MAGvB,GAFAtK,QAAQC,IAAI,qCAA4B9C,GAEpCA,EAASgL,QAAS,CACpBnI,QAAQC,IAAI,kCAAyB9C,EAASF,KAAKW,QACnDoC,QAAQC,IAAI,mCAA0BjB,EAAKP,QAC3CuB,QAAQC,IAAI,iCAAwB9C,EAASF,KAAKqL,MAAM,EAAG,GAAGC,KAAItC,IAAC,CAAO4G,GAAI5G,EAAEC,IAAKsC,KAAMvC,EAAEuC,UAE7F,MAAMtJ,EAAW/B,EAASF,KAAK0P,MAAK1G,GAAK+F,OAAO/F,EAAEC,OAAS8F,OAAOhN,EAAKP,UACvE,GAAIS,EAAU,CACZc,QAAQC,IAAI,+BAA2Bf,GAEvC,MAAM0N,GAAmB9H,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACpB5F,GAAQ,IACXwJ,eAAgBxJ,EAASuJ,cAAgBvJ,EAASwJ,gBAAkB,GACpED,aAAcvJ,EAASuJ,cAAgBvJ,EAASwJ,gBAAkB,KAEpE5I,EAAgB8M,EAClB,KAAO,CACL5M,QAAQC,IAAI,uCACZD,QAAQC,IAAI,qDAGZ,MAAM6M,EAAc3P,EAASF,KAAK0P,MAAK1G,GACrCA,EAAEC,MAAQlH,EAAKP,QACfwH,EAAE4G,KAAO7N,EAAKP,QACduN,OAAO/F,EAAEC,KAAK6G,SAAS/N,EAAKP,SAC5BuN,OAAOhN,EAAKP,QAAQsO,SAAS9G,EAAEC,OAGjC,GAAI4G,EAAa,CACf9M,QAAQC,IAAI,6CAAyC6M,GAErD,MAAMF,GAAmB9H,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACpBgI,GAAW,IACdpE,eAAgBoE,EAAYrE,cAAgBqE,EAAYpE,gBAAkB,GAC1ED,aAAcqE,EAAYrE,cAAgBqE,EAAYpE,gBAAkB,KAE1E5I,EAAgB8M,EAClB,MACE5M,QAAQC,IAAI,wCAEhB,CACF,MACED,QAAQC,IAAI,6BAAyB9C,EAEzC,CAAE,MAAOD,GACP8C,QAAQ9C,MAAM,mCAA+BA,EAC/C,MAtDE8C,QAAQC,IAAI,8BAA0BjB,EAsDxC,EA0BAgO,GAGA,MAAMC,EAAc3K,EAAmB+I,KAAKK,MAAML,KAAK6B,SAAW5K,EAAmB1E,SACrFuD,EAAqB8L,GAGrB,MAAME,EAAiBC,aAAY,KACjCnM,GAAkBoM,IAASA,EAAO,GAAK,GAAE,GACxC,KASGC,EAAoBA,KACxBtN,QAAQC,IAAI,4DACZoH,IAAiB,EAAK,EAIlBkG,EAAuBC,IAC3BxN,QAAQC,IAAI,mDAA0CuN,EAAMC,QAG5DtO,aAAasI,WAAW,gBACxBtI,aAAasI,WAAW,uBACxBtI,aAAasI,WAAW,mBAgCxBnB,YAAW,MA7BcxJ,iBAAyB,IAAlB4Q,EAAQ/P,UAAAC,OAAA,QAAAC,IAAAF,UAAA,GAAAA,UAAA,GAAG,EACzC,IAAK,IAAIgQ,EAAI,EAAGA,EAAID,EAAUC,IAC5B,IAAK,IAADC,EAKF,GAJA5N,QAAQC,IAAI,iDAAD3B,OAAwCqP,EAAI,EAAC,KAAArP,OAAIoP,EAAQ,YAC9DrG,IAAiB,GAGP,QAAZuG,EAAAJ,EAAMC,cAAM,IAAAG,GAAZA,EAAcC,YAAc7O,EAAM,CACpC,MAAM8O,EAAcxN,EAAYqM,MAAK1G,GAAK+F,OAAO/F,EAAEC,OAAS8F,OAAOhN,EAAKkH,OACxE,GAAI4H,GAAeA,EAAY3I,SAAWqI,EAAMC,OAAOI,WAAY,CACjE7N,QAAQC,IAAI,8CACZ,KACF,CACF,CAGI0N,EAAID,EAAW,SACX,IAAIK,SAAQC,GAAW1H,WAAW0H,EAAS,OAErD,CAAE,MAAO9Q,GACP8C,QAAQ9C,MAAM,kCAADoB,OAA8BqP,EAAI,EAAC,YAAYzQ,GACxDyQ,EAAID,EAAW,SACX,IAAIK,SAAQC,GAAW1H,WAAW0H,EAAS,OAErD,CAEJ,CAIEC,EAAkB,GACjB,IAAK,EAMV,OAHAC,OAAOC,iBAAiB,QAASb,GACjCY,OAAOC,iBAAiB,gBAAiBZ,GAElC,KACLa,cAAcjB,GAEde,OAAOG,oBAAoB,QAASf,GACpCY,OAAOG,oBAAoB,gBAAiBd,EAAoB,CACjE,GACA,KAGHd,EAAAA,EAAAA,YAAU,KACR,GAAIzN,GAAQ4C,GAAgBgD,OAAOU,KAAK1D,GAAchE,OAAS,IAAM8D,EAEnE,IAAK,MAAO6D,EAAW+I,KAAe1J,OAAOC,QAAQjD,GAAe,CAElE,GADqB0M,EAAWvP,MAAM4N,MAAK1G,GAAK+F,OAAO/F,EAAEC,OAAS8F,OAAOhN,EAAKkH,OAC5D,CAChBlG,QAAQC,IAAI,2CAAkCsF,GAC9C5D,EAAkB4D,GAClB9D,GAAkB,GAClBF,EAAe+M,EAAWvP,OAC1B,KACF,CACF,CACF,GACC,CAACC,EAAM4C,EAAcF,IAGxB,MAAM6M,GAAgBjO,EAAYgI,MAAM,EAAG,GAuCrCkG,IAtCkBlO,EAAYgI,MAAM,GAKhBmG,MACxB,GAAS,OAAJzP,QAAI,IAAJA,IAAAA,EAAMkH,IAAK,OAAO,KAIvB,GADmBqI,GAAcG,MAAKC,GAAa3C,OAAO2C,EAAUzI,OAAS8F,OAAOhN,EAAKkH,OACzE,CAEd,MAAO,CACL0I,KAAM,SACNC,SAHqBN,GAAcvI,WAAU2I,GAAa3C,OAAO2C,EAAUzI,OAAS8F,OAAOhN,EAAKkH,OAAQ,EAIxGxB,OAAQ,kBACRa,UAAW,SAEf,CAGA,IAAK,MAAOA,EAAW+I,KAAe1J,OAAOC,QAAQjD,GAAe,CAAC,IAADkN,EAElE,GADqC,QAAnBA,EAAGR,EAAWvP,aAAK,IAAA+P,OAAA,EAAhBA,EAAkBnC,MAAK1G,GAAK+F,OAAO/F,EAAEC,OAAS8F,OAAOhN,EAAKkH,OAC7D,CAEhB,MAAO,CACL0I,KAAM,SACNC,SAHeP,EAAWvP,MAAMiH,WAAUC,GAAK+F,OAAO/F,EAAEC,OAAS8F,OAAOhN,EAAKkH,OAAQ,EAIrFxB,OAAQ4J,EAAWpL,MACnBqC,UAAWA,EACXwJ,WAAYT,EAAWvP,MAAMnB,OAEjC,CACF,CAEA,OAAO,IAAI,EAGU6Q,IAGjBO,GAAiBvQ,GACdO,GAAQgN,OAAOvN,KAAYuN,OAAOhN,EAAKkH,KAI1C+I,GAAuBxQ,GACpBuQ,GAAcvQ,KAAYqD,GAYnC2K,EAAAA,EAAAA,YAAU,KACR1K,GAAoB,GACpBE,GAAuB,EAAM,GAC5B,CAAK,OAAJjD,QAAI,IAAJA,OAAI,EAAJA,EAAMkH,IAAKxE,KAGf+K,EAAAA,EAAAA,YAAU,KAQR,GAPAzM,QAAQC,IAAI,kCAAyB,CACnCxB,OAAY,OAAJO,QAAI,IAAJA,OAAI,EAAJA,EAAMkH,IACdlE,sBACAkN,kBAAmB5O,EAAY1C,SAIxB,OAAJoB,QAAI,IAAJA,IAAAA,EAAMkH,KAAOlE,GAA8C,IAAvB1B,EAAY1C,OAMnD,YALAoC,QAAQC,IAAI,8BAA0B,CACpCkP,UAAe,OAAJnQ,QAAI,IAAJA,IAAAA,EAAMkH,KACjBkJ,UAAWpN,EACXqN,QAAS/O,EAAY1C,OAAS,IAKlC,MAoEM0R,EAAQhJ,YApEOiJ,KACnBvP,QAAQC,IAAI,8CAAqCjB,EAAKkH,KAGtD,MAAMwG,EAAgBpM,EAAYqM,MAAK1G,GAAK+F,OAAO/F,EAAEC,OAAS8F,OAAOhN,EAAKkH,OAC1E,IAAKwG,EAGH,OAFA1M,QAAQC,IAAI,8CACZgC,GAAuB,GAIzBjC,QAAQC,IAAI,4CAAwCyM,EAActD,MAGlE,MAAMoG,EAAa9C,EAActD,MAAQ,EAGzC,GAFApJ,QAAQC,IAAI,kCAAyBuP,GAEjCA,EAAY,CAEdxP,QAAQC,IAAI,+CACZ,MAAMwP,EAAgBjJ,SAASC,cAAc,2BAC7CzG,QAAQC,IAAI,uCAA8BwP,GACtCA,EACFnJ,YAAW,KACTmJ,EAAc7I,eAAe,CAC3BC,SAAU,SACVC,MAAO,SACPC,OAAQ,YAEV/G,QAAQC,IAAI,6BAEZqG,YAAW,KACTvE,GAAoB,GACpBE,GAAuB,GACvBjC,QAAQC,IAAI,+BAA0B,GACrC,IAAK,GACP,KAEHgC,GAAuB,EAE3B,KAAO,CAELjC,QAAQC,IAAI,iDAAwCjB,EAAKkH,KACzD,MAAMwJ,EAAclJ,SAASC,cAAc,kBAADnI,OAAmBU,EAAKkH,IAAG,OACrElG,QAAQC,IAAI,qCAA4ByP,GACpCA,EACFpJ,YAAW,KACToJ,EAAY9I,eAAe,CACzBC,SAAU,SACVC,MAAO,SACPC,OAAQ,YAEV/G,QAAQC,IAAI,oCAEZqG,YAAW,KACTvE,GAAoB,GACpBE,GAAuB,GACvBjC,QAAQC,IAAI,+BAA0B,GACrC,IAAK,GACP,MAEHD,QAAQC,IAAI,wCACZgC,GAAuB,GAE3B,IAIqC,KACvC,MAAO,IAAM0N,aAAaL,EAAM,GAC/B,CAAK,OAAJtQ,QAAI,IAAJA,OAAI,EAAJA,EAAMkH,IAAK5F,EAAa0B,IAG5B,MA+CM4N,GAAkBA,KACtBC,EAAAA,EAAAA,KAAA,OAAKC,UAAU,4EAA2EC,UACxFC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,8BAA6BC,SAAA,EAE1CC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,oBAAmBC,SAAA,EAChCF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,iEACfD,EAAAA,EAAAA,KAAA,OAAKC,UAAU,0DAIjBD,EAAAA,EAAAA,KAAA,OAAKC,UAAU,gDAA+CC,SAC3D,CAAC,EAAG,EAAG,GAAGxH,KAAKsG,IACdmB,EAAAA,EAAAA,MAAA,OAAoBF,UAAS,eAAAxR,OAA8B,IAAbuQ,EAAiB,UAAyB,IAAbA,EAAiB,UAAY,WAAYkB,SAAA,EAClHF,EAAAA,EAAAA,KAAA,OAAKC,UAAS,6GACdD,EAAAA,EAAAA,KAAA,OAAKC,UAAU,6DACfD,EAAAA,EAAAA,KAAA,OAAKC,UAAU,wDAHPjB,QASdgB,EAAAA,EAAAA,KAAA,OAAKC,UAAU,8BAA6BC,SACzC,IAAIE,MAAM,IAAI1H,KAAI,CAAC2H,EAAGvC,KACrBkC,EAAAA,EAAAA,KAAA,OAAaC,UAAU,0CAAyCC,UAC9DC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,8BAA6BC,SAAA,EAC1CF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,wCACfE,EAAAA,EAAAA,MAAA,OAAKF,UAAU,SAAQC,SAAA,EACrBF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,uCACfD,EAAAA,EAAAA,KAAA,OAAKC,UAAU,oCAEjBD,EAAAA,EAAAA,KAAA,OAAKC,UAAU,qCAPTnC,YAiBpB,OAAInN,GAAkC,IAAvBF,EAAY1C,QAClBiS,EAAAA,EAAAA,KAACD,GAAe,KAIvBI,EAAAA,EAAAA,MAAAG,EAAAA,SAAA,CAAAJ,SAAA,EACEF,EAAAA,EAAAA,KAAA,SAAAE,SAAA,4gKAmKAC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,mIAAmIM,QAxW9HC,KACjBvO,IACHC,GAAoB,GACpB/B,QAAQC,IAAI,qDACd,EAoW6K8P,SAAA,EAGzKjO,GAAoB9C,IACpB6Q,EAAAA,EAAAA,KAACS,EAAAA,EAAOC,IAAG,CACTC,QAAS,CAAEC,QAAS,EAAGC,GAAI,IAC3BC,QAAS,CAAEF,QAAS,EAAGC,EAAG,GAC1BE,KAAM,CAAEH,QAAS,EAAGC,GAAI,IACxBZ,UAAU,uJAAsJC,SACjK,+EAMHC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,mCAAkCC,SAAA,EAC/CF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,+HACfD,EAAAA,EAAAA,KAAA,OAAKC,UAAU,oJACfD,EAAAA,EAAAA,KAAA,OAAKC,UAAU,iJACfD,EAAAA,EAAAA,KAAA,OAAKC,UAAU,qJAIjBD,EAAAA,EAAAA,KAAA,OAAKC,UAAU,uDAAsDC,SAClE,IAAIE,MAAM,KAAK1H,KAAI,CAAC2H,EAAGvC,KACtBkC,EAAAA,EAAAA,KAACS,EAAAA,EAAOC,IAAG,CAETT,UAAU,oDACVa,QAAS,CACPD,EAAG,CAAC,GAAI,IAAK,GACbG,EAAG,CAAC,EAAmB,IAAhBxF,KAAK6B,SAAiB,GAAI,GACjCuD,QAAS,CAAC,GAAK,GAAK,KAEtBvJ,WAAY,CACV4J,SAAU,EAAoB,EAAhBzF,KAAK6B,SACnB6D,OAAQC,IACRC,MAAuB,EAAhB5F,KAAK6B,UAEdlG,MAAO,CACLkK,KAAK,GAAD5S,OAAqB,IAAhB+M,KAAK6B,SAAc,KAC5BiE,IAAI,GAAD7S,OAAqB,IAAhB+M,KAAK6B,SAAc,OAdxBS,QAoBXqC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,gBAAeC,SAAA,EAE5BF,EAAAA,EAAAA,KAACS,EAAAA,EAAOC,IAAG,CACTC,QAAS,CAAEC,QAAS,EAAGC,GAAI,IAC3BC,QAAS,CAAEF,QAAS,EAAGC,EAAG,GAC1BxJ,WAAY,CAAE4J,SAAU,IACxBhB,UAAU,4DACV9I,MAAO,CACLoK,QAASlD,OAAOmD,YAAc,IAAM,MAAQnD,OAAOmD,YAAc,KAAO,OAAS,QACjFtB,UAEFF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,oBAAmBC,UAChCF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,uHAAsHC,UACnIC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,yFAAwFC,SAAA,EAGrGC,EAAAA,EAAAA,MAACM,EAAAA,EAAOgB,OAAM,CACZC,WAAY,CAAEC,MAAO,MACrBC,SAAU,CAAED,MAAO,KACnBpB,QAASA,IAAMhQ,EAAS,aACxB0P,UAAU,iNACV9I,MAAO,CACL0K,SAAUxD,OAAOmD,WAAa,IAAM,OAAS,UAC7CtB,SAAA,EAEFF,EAAAA,EAAAA,KAAC8B,EAAAA,IAAM,CAAC7B,UAAU,2BAClBD,EAAAA,EAAAA,KAAA,QAAAE,SAAM,WAIPvB,KACCwB,EAAAA,EAAAA,MAACM,EAAAA,EAAOC,IAAG,CACTC,QAAS,CAAEC,QAAS,EAAGC,EAAG,IAC1BC,QAAS,CAAEF,QAAS,EAAGC,EAAG,GAC1BZ,UAAU,oJACV9I,MAAO,CACL4K,WAAoC,WAAxBpD,GAAeI,KACvB,4CACA,4CACJlM,MAA+B,WAAxB8L,GAAeI,KAAoB,UAAY,UACtDzH,UAAW,qCACXuK,SAAUxD,OAAOmD,WAAa,IAAM,SAAW,QAC/CtB,SAAA,EAEFF,EAAAA,EAAAA,KAAC5L,EAAAA,IAAQ,CAAC6L,UAAU,2BACpBD,EAAAA,EAAAA,KAAA,QAAAE,SAC2B,WAAxBvB,GAAeI,KAAiB,wBAAAtQ,OACfkQ,GAAeK,UAAQ,GAAAvQ,OAClCkQ,GAAe9J,OAAM,MAAApG,OAAKkQ,GAAeK,eAMrDhP,IACCgQ,EAAAA,EAAAA,KAACS,EAAAA,EAAOC,IAAG,CACTC,QAAS,CAAEC,QAAS,EAAGC,EAAG,IAC1BC,QAAS,CAAEF,QAAS,EAAGC,EAAG,GAC1BZ,UAAU,uJAAsJC,UAEhKC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,0BAAyBC,SAAA,EAEtCC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,yBAAwBC,SAAA,EACrCF,EAAAA,EAAAA,KAACgC,EAAAA,EAAc,CACb7S,KAAMa,EACNiS,KAAK,KACLC,kBAAkB,EAClB/K,MAAO,CACLgL,OAAQ,oBACR7K,UAAW,kCAIdtH,EAAaoS,WACZpC,EAAAA,EAAAA,KAAA,OACE7I,MAAO,CACL6H,SAAU,WACVqD,OAAQ,MACRC,MAAO,MACPC,MAAO,OACPC,OAAQ,OACRC,gBAAiB,UACjBC,aAAc,MACdP,OAAQ,oBACR7K,UAAW,mCACXqL,OAAQ,IAEVtP,MAAM,eAMZ8M,EAAAA,EAAAA,MAAA,OAAKF,UAAU,YAAWC,SAAA,EACxBF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,6CAA4CC,SACvDlQ,EAAa2I,MAAQ3I,EAAa4S,UAAY,UAIjDzC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,iCAAgCC,SAAA,EAC7CC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,6CAA4CC,SAAA,EACzDF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,yBAAwBC,SAAC,cACxCF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,uBAAsBC,UAGtBlQ,EAAasF,SAAWtF,EAAa4E,IAAM5E,EAAa6S,QAAU7S,EAAa0L,aAAe,GAC/FoH,uBAKhB3C,EAAAA,EAAAA,MAAA,OAAKF,UAAU,8CAA6CC,SAAA,EAC1DF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,0BAAyBC,SAAC,UACzCF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,uBAAsBC,SAClC,MAEC,MAAMrD,EAAgBpM,EAAYqM,MAAK1G,GAAK+F,OAAO/F,EAAEC,OAAS8F,OAAOnM,EAAaqG,OAClF,OAAOwG,EAAa,IAAApO,OAAOoO,EAActD,MAAU1I,EAAe,IAAApC,OAAOoC,GAAoB,KAC9F,EAJA,SAQLsP,EAAAA,EAAAA,MAAA,OAAKF,UAAU,4CAA2CC,SAAA,EACxDF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,wBAAuBC,SAAC,YACvCF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,+BAA8BC,SAC1C,MAEC,MAAM6C,EAAS/S,EAAasF,SAAWtF,EAAa4E,IAAM5E,EAAa6S,QAAU7S,EAAa0L,aAAe,EAC7G,IAAK,MAAOhG,EAAW+I,KAAe1J,OAAOC,QAAQjD,GAAe,CAAC,IAADiR,EAElE,GADqC,QAAnBA,EAAGvE,EAAWvP,aAAK,IAAA8T,OAAA,EAAhBA,EAAkBlG,MAAK1G,GAAK+F,OAAO/F,EAAEC,OAAS8F,OAAOnM,EAAaqG,OACrE,CAChB,MAAM4M,EAAatO,EAAcoO,GACjC,MAAM,GAANtU,OAAUwU,EAAWxP,WAAU,KAAAhF,OAAIiH,EAAUwN,cAC/C,CACF,CAEA,GAAIH,EAAS,EAAG,CACd,MAAME,EAAatO,EAAcoO,GACjC,MAAM,GAANtU,OAAUwU,EAAWxP,WAAU,KAAAhF,OAAIwU,EAAWpO,OAAOqO,cACvD,CACA,MAAO,uBACR,EAhBA,SAoBL/C,EAAAA,EAAAA,MAAA,OAAKF,UAAU,8CAA6CC,SAAA,EAC1DF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,0BAAyBC,SAAC,aACzCF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,uBAAsBC,SAG1BlQ,EAAamT,kBAAoBnT,EAAawI,mBAAqBxI,EAAaoT,cAAgBpT,EAAakL,cAAgB,WAO5IiF,EAAAA,EAAAA,MAAA,OAAKF,UAAU,sCAAqCC,SAAA,EAClDC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,gDAA+CC,SAAA,EAC5DF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,0BAAyBC,SAAC,WACzCF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,uBAAsBC,SAClClQ,EAAa0H,cAAgB1H,EAAa2H,OAAS,QAIxDwI,EAAAA,EAAAA,MAAA,OAAKF,UAAU,6CAA4CC,SAAA,EACzDF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,uBAAsBC,SAAC,YACtCF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,uBAAsBC,SAClClQ,EAAaoJ,eAAiBpJ,EAAaqT,QAAU,QAI1DlD,EAAAA,EAAAA,MAAA,OAAKF,UAAU,8CAA6CC,SAAA,EAC1DF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,wBAAuBC,SAAC,eACvCC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,uBAAsBC,SAAA,CAClC,MACC,MAAMoD,EAAWtT,EAAamJ,cAAgBnJ,EAAasT,UAAY,EACvE,OAAO9H,KAAKC,MAAM6H,EACnB,EAHA,GAGI,aAMV,MAEC,IAAK,MAAO5N,EAAW+I,KAAe1J,OAAOC,QAAQjD,GAAe,CAAC,IAADwR,EAClE,MAAMC,EAA4B,QAAnBD,EAAG9E,EAAWvP,aAAK,IAAAqU,OAAA,EAAhBA,EAAkBpN,WAAUC,GAAK+F,OAAO/F,EAAEC,OAAS8F,OAAOnM,EAAaqG,OACzF,IAAmB,IAAfmN,QAAkCxV,IAAdwV,EACtB,OACExD,EAAAA,EAAAA,KAAA,OAAKC,UAAU,mBAAkBC,UAC/BC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,wEAAuEC,SAAA,EACpFF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,0BAAyBC,SAAC,qBACzCC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,+BAA8BC,SAAA,CAAC,IAC1CsD,EAAY,EAAE,OAAK/E,EAAWvP,MAAMnB,cAMlD,CACA,OAAO,IACR,EAlBA,YAmCToS,EAAAA,EAAAA,MAAA,OAAKF,UAAU,wHAAuHC,SAAA,EAEpIF,EAAAA,EAAAA,KAACS,EAAAA,EAAOgD,GAAE,CACRxD,UAAU,uCACV9I,MAAO,CACL4K,WAAY,oDACZ2B,qBAAsB,OACtBC,oBAAqB,cACrBC,WAAY,8BACZ3N,OAAQ,iCAEV6K,QAAS,CAAEa,MAAO,CAAC,EAAG,KAAM,IAC5BtK,WAAY,CAAE4J,SAAU,EAAGC,OAAQC,KAAWjB,SAC/C,uCAKDF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,4DAA2DC,SACvE3I,KAAoBmB,KAAKhD,IAAe,IAADmO,EACtC,MAAMhP,EAASnC,EAAagD,GACtBoO,EAAajS,IAAmB6D,EAChCqO,GAAmC,QAAvBF,EAAA9R,EAAa2D,UAAU,IAAAmO,OAAA,EAAvBA,EAAyB3U,MAAMnB,SAAU,EAE3D,OACEoS,EAAAA,EAAAA,MAACM,EAAAA,EAAOC,IAAG,CAETT,UAAU,mCACVyB,WAAY,CAAEC,MAAO,MAAOzB,SAAA,EAE5BC,EAAAA,EAAAA,MAACM,EAAAA,EAAOgB,OAAM,CACZC,WAAY,CAAEC,MAAO,IAAKd,GAAI,GAC9Be,SAAU,CAAED,MAAO,KACnBpB,QAASA,IAAMhK,GAAmBb,GAClCuK,UAAS,+GAAAxR,OACPqV,EACI,qDACA,oCAEN3M,MAAO,CACL4K,WAAY+B,EAAU,2BAAArV,OACSoG,EAAOtB,YAAW,QAAA9E,OAAOoG,EAAO9B,UAAS,QAAAtE,OAAOoG,EAAOtB,YAAW,kCAAA9E,OAClEoG,EAAOtB,YAAW,QAAA9E,OAAOoG,EAAO9B,UAAS,OACxEoP,OAAO,aAAD1T,OAAeqV,EAAa,UAAYjP,EAAOtB,YAAc,MACnE+D,UAAWwM,EAAU,YAAArV,OACLoG,EAAO5B,YAAW,uCAAAxE,OAAsCoG,EAAO5B,YAAW,oBAAAxE,OACxEoG,EAAO5B,YAAW,MACpCmE,UAAW0M,EAAa,aAAe,WACvC7N,OAAQ6N,EAAa,gCAAkC,iBAEzDhD,QAASgD,EAAa,CACpBxM,UAAW,CAAC,YAAD7I,OACGoG,EAAO5B,YAAW,sCAAAxE,OAClBoG,EAAO5B,YAAW,uCAAAxE,OAClBoG,EAAO5B,YAAW,2BAEhC0O,MAAO,CAAC,IAAK,KAAM,MACjB,CAAC,EACLtK,WAAY,CACV4J,SAAU,EACVC,OAAQ4C,EAAa3C,IAAW,EAChC6C,KAAM,aAER3Q,MAAK,iBAAA5E,OAAmBoG,EAAOxB,MAAK,aAAA5E,OAAYsV,EAAS,WAAU7D,SAAA,EAEnEF,EAAAA,EAAAA,KAAA,QAAMC,UAAU,uBAAsBC,SAAErL,EAAOpB,aAC9CqQ,IACC9D,EAAAA,EAAAA,KAACS,EAAAA,EAAOC,IAAG,CACTC,QAAS,CAAEgB,MAAO,EAAGsC,QAAS,IAAKrD,QAAS,GAC5CE,QAAS,CACPa,MAAO,CAAC,EAAG,IAAK,GAChBsC,OAAQ,CAAC,EAAG,IAAK,KACjBrD,QAAS,EACTtJ,UAAW,CACT,iEACA,+DACA,mEAGJD,WAAY,CACVsK,MAAO,CAAEV,SAAU,EAAGC,OAAQC,IAAU6C,KAAM,aAC9CC,OAAQ,CAAEhD,SAAU,EAAGC,OAAQC,IAAU6C,KAAM,UAC/C1M,UAAW,CAAE2J,SAAU,IAAKC,OAAQC,IAAU6C,KAAM,aACpDpD,QAAS,CAAEK,SAAU,KAEvBhB,UAAU,+KACV9I,MAAO,CACL4K,WAAY,oDACZI,OAAQ,kBACRQ,OAAQ,IACRzC,UAEFF,EAAAA,EAAAA,KAACS,EAAAA,EAAOyD,KAAI,CACVjE,UAAU,mCACVa,QAAS,CACPa,MAAO,CAAC,EAAG,IAAK,GAChBsC,OAAQ,CAAC,GAAI,GAAI,GAAI,IAEvB5M,WAAY,CACV4J,SAAU,EACVC,OAAQC,IACR6C,KAAM,aACN9D,SACH,cAKLF,EAAAA,EAAAA,KAAA,OACEC,UAAU,4HACV9I,MAAO,CACL4K,WAAYlN,EAAOtB,YACnBV,MAAO,UACPgP,SAAU,QACV3B,SAED6D,QAKL/D,EAAAA,EAAAA,KAACS,EAAAA,EAAOC,IAAG,CACTT,UAAU,cACVyB,WAAY,CAAEC,MAAO,MAAOzB,UAE5BF,EAAAA,EAAAA,KAAA,OACEC,UAAU,oDACV9I,MAAO,CACLtE,MAAOgC,EAAO7B,UACd4Q,WAAW,eAADnV,OAAiBoG,EAAO5B,aAClC8O,WAAW,GAADtT,OAAKoG,EAAOtB,YAAW,MACjC4O,OAAO,aAAD1T,OAAeoG,EAAOtB,YAAW,OACvC2M,SAEDrL,EAAOxB,YA5GPqC,EA+GM,OAKnBsK,EAAAA,EAAAA,KAAA,KAAGC,UAAU,yCAAwCC,SAAC,yEAQxDC,EAAAA,EAAAA,MAACM,EAAAA,EAAOgB,OAAM,CACZC,WAAY,CAAEC,MAAO,KAAMsC,OAAQ,KACnCrC,SAAU,CAAED,MAAO,KACnBpB,QAAS/I,GACT2M,SAAUxT,EACVsP,UAAU,sNACV9I,MAAO,CACL0K,SAAUxD,OAAOmD,WAAa,IAAM,OAAS,UAC7CtB,SAAA,EAEFF,EAAAA,EAAAA,KAACoE,EAAAA,IAAS,CAACnE,UAAS,yBAAAxR,OAA2BkC,EAAU,eAAiB,OAC1EqP,EAAAA,EAAAA,KAAA,QAAAE,SAAM,yBAQf,GA0BDF,EAAAA,EAAAA,KAACS,EAAAA,EAAOC,IAAG,CACTC,QAAS,CAAEC,QAAS,EAAGC,GAAI,IAC3BC,QAAS,CAAEF,QAAS,EAAGC,EAAG,GAC1BxJ,WAAY,CAAE4J,SAAU,EAAG+C,KAAM,WACjC/D,UAAU,gCAA+BC,UAGzCC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,kGAAiGC,SAAA,EAC9GF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,iFACfD,EAAAA,EAAAA,KAAA,OAAKC,UAAU,mFAGfD,EAAAA,EAAAA,KAAA,OAAKC,UAAU,4EAA2EC,UACxFC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,gCAA+BC,SAAA,EAG5CF,EAAAA,EAAAA,KAACS,EAAAA,EAAOC,IAAG,CACTI,QAAS,CACPa,MAAO,CAAC,EAAG,KAAM,GACjB0C,QAAS,CAAC,EAAG,EAAG,IAElBhN,WAAY,CACV4J,SAAU,EACVC,OAAQC,IACR6C,KAAM,aAER/D,UAAU,eAAcC,UAExBC,EAAAA,EAAAA,MAAA,MAAIF,UAAU,kGAAiGC,SAAA,EAC7GF,EAAAA,EAAAA,KAACS,EAAAA,EAAOyD,KAAI,CACVpD,QAAS,CACPwD,mBAAoB,CAAC,SAAU,WAAY,WAE7CjN,WAAY,CACV4J,SAAU,EACVC,OAAQC,IACR6C,KAAM,UAER/D,UAAU,gIACV9I,MAAO,CACLoN,eAAgB,YAChBb,qBAAsB,OACtBC,oBAAqB,cACrB1N,OAAQ,4CACRiK,SACH,aAGDF,EAAAA,EAAAA,KAAA,UACAA,EAAAA,EAAAA,KAACS,EAAAA,EAAOyD,KAAI,CACVpD,QAAS,CACP8C,WAAY,CACV,6DACA,2DACA,+DAGJvM,WAAY,CACV4J,SAAU,IACVC,OAAQC,IACR6C,KAAM,aAER7M,MAAO,CACLtE,MAAO,UACP2R,WAAY,MACZZ,WAAY,+BACZ1D,SACH,oBAOLF,EAAAA,EAAAA,KAACS,EAAAA,EAAOgE,EAAC,CACP9D,QAAS,CAAEC,QAAS,EAAGC,EAAG,IAC1BC,QAAS,CAAEF,QAAS,EAAGC,EAAG,GAC1BxJ,WAAY,CAAE+J,MAAO,GAAKH,SAAU,IACpChB,UAAU,+GACV9I,MAAO,CACLtE,MAAO,UACP+Q,WAAY,8BACZ7B,WAAY,2CACZ2B,qBAAsB,OACtBC,oBAAqB,eACrBzD,SACH,sEAKDF,EAAAA,EAAAA,KAACS,EAAAA,EAAOC,IAAG,CACTC,QAAS,CAAEC,QAAS,EAAGe,MAAO,IAC9Bb,QAAS,CAAEF,QAAS,EAAGe,MAAO,GAC9BtK,WAAY,CAAE+J,MAAO,GAAKH,SAAU,IACpChB,UAAU,eAAcC,UAExBF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,8JACV9I,MAAO,CACLyM,WAAY,8BACZc,UAAW,UACXxE,SACF7O,OAKL2O,EAAAA,EAAAA,KAACS,EAAAA,EAAOC,IAAG,CACTC,QAAS,CAAEC,QAAS,EAAGC,EAAG,IAC1BC,QAAS,CAAEF,QAAS,EAAGC,EAAG,GAC1BxJ,WAAY,CAAE+J,MAAO,EAAGH,SAAU,IAClChB,UAAU,4EAA2EC,SAEpF,CACC,CACE/M,KAAMwR,EAAAA,IACNC,MAAOnU,EAAY1C,OACnB8W,MAAO,YACPC,WAAY,sDACZC,UAAW,UACXxR,YAAa,WAEf,CACEJ,KAAMiB,EAAAA,IACNwQ,MAAOlG,GAAc3Q,OACrB8W,MAAO,iBACPC,WAAY,qDACZC,UAAW,UACXxR,YAAa,WAEf,CACEJ,KAAM6R,EAAAA,IACNJ,MAAOnU,EAAYwF,QAAOG,GAAKA,EAAEgD,cAAgB,IAAGrL,OACpD8W,MAAO,iBACPC,WAAY,iDACZC,UAAW,UACXxR,YAAa,WAEf,CACEJ,KAAMqB,EAAAA,IACNoQ,MAAOnU,EAAY2K,QAAO,CAACC,EAAKjF,IAAMiF,GAAOjF,EAAEd,SAAW,IAAI,GAAGwN,iBACjE+B,MAAO,WACPC,WAAY,sDACZC,UAAW,UACXxR,YAAa,YAEfmF,KAAI,CAACuM,EAAMjM,KACXmH,EAAAA,EAAAA,MAACM,EAAAA,EAAOC,IAAG,CAETC,QAAS,CAAEC,QAAS,EAAGe,MAAO,IAC9Bb,QAAS,CAAEF,QAAS,EAAGe,MAAO,GAC9BtK,WAAY,CAAE+J,MAAO,IAAc,GAARpI,EAAaiI,SAAU,IAClDS,WAAY,CAAEC,MAAO,KAAMd,GAAI,GAC/BZ,UAAS,qBAAAxR,OAAuBwW,EAAKH,WAAU,gFAC/C3N,MAAO,CACLgL,OAAO,aAAD1T,OAAewW,EAAK1R,YAAW,MACrC+D,UAAU,cAAD7I,OAAgBwW,EAAK1R,YAAW,OACzC2M,SAAA,EAEFF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,oEACfD,EAAAA,EAAAA,KAACiF,EAAK9R,KAAI,CACR8M,UAAU,mDACV9I,MAAO,CAAEtE,MAAOoS,EAAKF,UAAW9O,OAAQ,6CAE1C+J,EAAAA,EAAAA,KAAA,OACEC,UAAU,2EACV9I,MAAO,CACLtE,MAAOoS,EAAKF,UACZnB,WAAW,8BACX3N,OAAQ,qCACR4L,SAAU,4BACV3B,SAED+E,EAAKL,SAER5E,EAAAA,EAAAA,KAAA,OACEC,UAAU,6CACV9I,MAAO,CACLtE,MAAO,UACP+Q,WAAY,8BACZ/B,SAAU,6BACV3B,SAED+E,EAAKJ,UAnCH7L,iBA8ClBrI,IACCwP,EAAAA,EAAAA,MAACM,EAAAA,EAAOC,IAAG,CACTC,QAAS,CAAEC,QAAS,GACpBE,QAAS,CAAEF,QAAS,GACpBX,UAAU,kDAAiDC,SAAA,EAE3DF,EAAAA,EAAAA,KAACS,EAAAA,EAAOC,IAAG,CACTI,QAAS,CAAEmD,OAAQ,KACnB5M,WAAY,CAAE4J,SAAU,EAAGC,OAAQC,IAAU6C,KAAM,UACnD/D,UAAU,iFAEZD,EAAAA,EAAAA,KAAA,KAAGC,UAAU,oCAAmCC,SAAC,6BAKnDvP,IACAqP,EAAAA,EAAAA,KAACS,EAAAA,EAAOC,IAAG,CACTC,QAAS,CAAEC,QAAS,EAAGC,EAAG,IAC1BC,QAAS,CAAEF,QAAS,EAAGC,EAAG,GAC1BxJ,WAAY,CAAE+J,MAAO,GAAKH,SAAU,IACpChB,UAAU,wDAAuDC,UAEjEC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,oBAAmBC,SAAA,CAG/BxB,GAAc3Q,OAAS,IACtBoS,EAAAA,EAAAA,MAACM,EAAAA,EAAOC,IAAG,CACTC,QAAS,CAAEC,QAAS,EAAGe,MAAO,IAC9Bb,QAAS,CAAEF,QAAS,EAAGe,MAAO,GAC9BtK,WAAY,CAAE+J,MAAO,GAAKH,SAAU,IACpChB,UAAU,QAAOC,SAAA,EAEjBF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,iGAAiG9I,MAAO,CACpH4K,WAAY,oDACZ2B,qBAAsB,OACtBC,oBAAqB,cACrBC,WAAY,8BACZ3N,OAAQ,iCACRiK,SAAC,gDAKHC,EAAAA,EAAAA,MAAA,OACEF,UAAU,+DACV9I,MAAO,CACL+N,IAAK7G,OAAOmD,YAAc,IAAM,MAAQnD,OAAOmD,YAAc,KAAO,OAAS,OAC7ED,QAASlD,OAAOmD,YAAc,IAAM,MAAQ,aAC5CtB,SAAA,CAGDxB,GAAc,KACbyB,EAAAA,EAAAA,MAACM,EAAAA,EAAOC,IAAG,CAETyE,IAAKhW,GAAQgN,OAAOuC,GAAc,GAAGrI,OAAS8F,OAAOhN,EAAKkH,KAAO9D,EAAgB,KACjF,eAAcmM,GAAc,GAAGrI,IAC/B,iBAAgB,EAChBsK,QAAS,CAAEC,QAAS,EAAGI,GAAI,IAAKH,EAAG,IACnCC,QAAS,CACPF,QAAS,EACTI,EAAG,EACHH,EAAG,EACHc,MAAO,CAAC,EAAG,KAAM,GACjB0C,QAAS,CAAC,EAAG,EAAG,IAElBhN,WAAY,CACV+J,MAAO,GACPH,SAAU,IACVU,MAAO,CAAEV,SAAU,EAAGC,OAAQC,IAAU6C,KAAM,aAC9CK,QAAS,CAAEpD,SAAU,EAAGC,OAAQC,IAAU6C,KAAM,cAElDtC,WAAY,CAAEC,MAAO,KAAMd,GAAI,IAC/BZ,UAAS,oBAAAxR,OACP2Q,GAAoBV,GAAc,GAAGrI,KACjC,0CACA,IAENc,MAAO,CACLqL,OAAQnE,OAAOmD,YAAc,IAAM,QAAU,QAC7CpK,UAAWgI,GAAoBV,GAAc,GAAGrI,KAAO,cAAgB,WACvEJ,OAAQmJ,GAAoBV,GAAc,GAAGrI,KACzC,2EACA,OACJgB,WAAY,gBACZ8K,OAAQ/C,GAAoBV,GAAc,GAAGrI,KAAO,oBAAsB,OAC1EqM,aAAcvD,GAAcT,GAAc,GAAGrI,KAAO,OAAS,MAC7D0L,WAAY5C,GAAcT,GAAc,GAAGrI,KAAO,yEAA2E,eAC7H6J,SAAA,EAGFF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,uJAAsJC,UACnKF,EAAAA,EAAAA,KAAA,QAAMC,UAAU,kDAAiDC,SAAC,WAIpEF,EAAAA,EAAAA,KAAA,OACEC,UAAS,8BAAAxR,OAAgCiQ,GAAc,GAAGlJ,KAAK3C,MAAK,oBAAApE,OAAmBiQ,GAAc,GAAGlJ,KAAKtC,KAAI,oBACjHiE,MAAO,CACLG,UAAU,cAAD7I,OAAgBiQ,GAAc,GAAGlJ,KAAKvC,YAAW,MAC1DsP,MAAO,SACPrC,UAEFC,EAAAA,EAAAA,MAAA,OACEF,UAAS,GAAAxR,OAAKiQ,GAAc,GAAGlJ,KAAK1C,QAAO,yEAAwEoN,SAAA,EAEnHF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,oEAGfD,EAAAA,EAAAA,KAAA,OACEC,UAAU,qMACV9I,MAAO,CACLtE,MAAO,UACPsP,OAAQ,qBACRjC,SACH,kBAKDC,EAAAA,EAAAA,MAAA,OAAKF,UAAS,yBAAAxR,OAA2BU,GAAQuP,GAAc,GAAGrI,MAAQlH,EAAKkH,IAAM,yCAA2C,IAAK6J,SAAA,EAEnIF,EAAAA,EAAAA,KAACgC,EAAAA,EAAc,CACb7S,KAAMuP,GAAc,GACpBuD,KAAK,KACLC,kBAAkB,EAClB/K,MAAO,CACLoL,MAAO,OACPC,OAAQ,UAIXrS,QAAQC,IAAI,kCAAyBsO,GAAc,QAItDsB,EAAAA,EAAAA,KAAA,MACEC,UAAU,kCACV9I,MAAO,CAAEtE,MAAO6L,GAAc,GAAGlJ,KAAKxC,WAAYkN,SAEjDxB,GAAc,GAAG/F,QAGpBwH,EAAAA,EAAAA,MAAA,OAAKF,UAAU,0BAA0B9I,MAAO,CAAEtE,MAAO6L,GAAc,GAAGlJ,KAAKzC,WAAYmN,SAAA,CACxFxB,GAAc,GAAGpJ,QAAQwN,iBAAiB,UAG7C3C,EAAAA,EAAAA,MAAA,OAAKF,UAAU,oCAAmCC,SAAA,EAChDC,EAAAA,EAAAA,MAAA,QAAMhJ,MAAO,CAAEtE,MAAO6L,GAAc,GAAGlJ,KAAKzC,WAAYmN,SAAA,CAAC,gBACnDxB,GAAc,GAAGlG,sBAEvB2H,EAAAA,EAAAA,MAAA,QAAMhJ,MAAO,CAAEtE,MAAO6L,GAAc,GAAGlJ,KAAKzC,WAAYmN,SAAA,CAAC,gBACnDxB,GAAc,GAAGtF,2BAIvB,UAAA3K,OAtGSiQ,GAAc,GAAGrI,MA2GnCqI,GAAc,KACbyB,EAAAA,EAAAA,MAACM,EAAAA,EAAOC,IAAG,CAETyE,IAAKhW,GAAQgN,OAAOuC,GAAc,GAAGrI,OAAS8F,OAAOhN,EAAKkH,KAAO9D,EAAgB,KACjF,eAAcmM,GAAc,GAAGrI,IAC/B,iBAAgB,EAChBsK,QAAS,CAAEC,QAAS,EAAGC,GAAI,IAAKc,MAAO,IACvCb,QAAS,CACPF,QAAS,EACTC,EAAG,EACHc,MAAO,EACP0C,QAAS,CAAC,EAAG,IAAK,GAAI,GACtBxD,EAAG,CAAC,GAAI,GAAI,IAEdxJ,WAAY,CACV+J,MAAO,GACPH,SAAU,IACVoD,QAAS,CAAEpD,SAAU,EAAGC,OAAQC,IAAU6C,KAAM,aAChDnD,EAAG,CAAEI,SAAU,EAAGC,OAAQC,IAAU6C,KAAM,cAE5CtC,WAAY,CAAEC,MAAO,KAAMd,GAAI,IAC/BZ,UAAS,yBAAAxR,OACP2Q,GAAoBV,GAAc,GAAGrI,KACjC,0CACA,IAENc,MAAO,CACLqL,OAAQnE,OAAOmD,YAAc,IAAM,QAAU,QAC7CpK,UAAWgI,GAAoBV,GAAc,GAAGrI,KAAO,cAAgB,WACvEJ,OAAQmJ,GAAoBV,GAAc,GAAGrI,KACzC,2EACA,OACJgB,WAAY,gBACZ8K,OAAQ/C,GAAoBV,GAAc,GAAGrI,KAAO,oBAAsB,OAC1EqM,aAAcvD,GAAcT,GAAc,GAAGrI,KAAO,OAAS,MAC7D0L,WAAY5C,GAAcT,GAAc,GAAGrI,KAAO,yEAA2E,eAE/H,eAAa,SAAQ6J,SAAA,EAIrBF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,6JAA4JC,UACzKF,EAAAA,EAAAA,KAAA,QAAMC,UAAU,oDAAmDC,SAAC,WAItEF,EAAAA,EAAAA,KAACS,EAAAA,EAAOC,IAAG,CACTI,QAAS,CAAEmD,OAAQ,CAAC,EAAG,IAAK,GAAI,GAAIpD,EAAG,CAAC,GAAI,EAAG,IAC/CxJ,WAAY,CAAE4J,SAAU,EAAGC,OAAQC,KACnClB,UAAU,4DAA2DC,UAErEF,EAAAA,EAAAA,KAAC5M,EAAAA,IAAO,CAAC6M,UAAU,gDAIrBD,EAAAA,EAAAA,KAAA,OACEC,UAAS,8BAAAxR,OAAgCiQ,GAAc,GAAGlJ,KAAK3C,MAAK,uBAAApE,OAAsBiQ,GAAc,GAAGlJ,KAAKtC,KAAI,yCACpHiE,MAAO,CACLG,UAAU,cAAD7I,OAAgBiQ,GAAc,GAAGlJ,KAAKvC,YAAW,uCAC1DsP,MAAO,SACPrC,UAEFC,EAAAA,EAAAA,MAAA,OACEF,UAAS,GAAAxR,OAAKiQ,GAAc,GAAGlJ,KAAK1C,QAAO,yEAC3CqE,MAAO,CACL4K,WAAW,GAADtT,OAAKiQ,GAAc,GAAGlJ,KAAK1C,QAAO,mFAC5CoN,SAAA,EAEFF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,gFAGfD,EAAAA,EAAAA,KAAA,OACEC,UAAU,yMACV9I,MAAO,CACLtE,MAAO,UACP+Q,WAAY,8BACZzB,OAAQ,qBACRjC,SACH,kBAKDC,EAAAA,EAAAA,MAAA,OAAKF,UAAS,yBAAAxR,OAA2BU,GAAQuP,GAAc,GAAGrI,MAAQlH,EAAKkH,IAAM,yCAA2C,IAAK6J,SAAA,EAEnIC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,WAAUC,SAAA,EACvBF,EAAAA,EAAAA,KAACgC,EAAAA,EAAc,CACb7S,KAAMuP,GAAc,GACpBuD,KAAK,KACLC,kBAAkB,EAClB/K,MAAO,CACLoL,MAAO,OACPC,OAAQ,UAIX9D,GAAc,GAAG0D,WAChBpC,EAAAA,EAAAA,KAAA,OACE7I,MAAO,CACL6H,SAAU,WACVqD,OAAQ,OACRC,MAAO,OACPC,MAAO,OACPC,OAAQ,OACRC,gBAAiB,UACjBC,aAAc,MACdP,OAAQ,oBACR7K,UAAW,mCACXqL,OAAQ,IAEVtP,MAAM,cAKXlD,QAAQC,IAAI,iCAAwBsO,GAAc,IAClDvP,GAAQuP,GAAc,GAAGrI,MAAQlH,EAAKkH,MACrC2J,EAAAA,EAAAA,KAAA,OACEC,UAAU,6DACV9I,MAAO,CACL4K,WAAY,2CACZzK,UAAW,6BACX4I,UAEFF,EAAAA,EAAAA,KAACxL,EAAAA,IAAM,CAACyL,UAAU,gCAMxBE,EAAAA,EAAAA,MAAA,OAAKF,UAAU,8CAA6CC,SAAA,EAC1DF,EAAAA,EAAAA,KAAA,MACEC,UAAU,8BACV9I,MAAO,CACLtE,MAAO6L,GAAc,GAAGlJ,KAAKxC,UAC7B4Q,WAAW,eAADnV,OAAiBiQ,GAAc,GAAGlJ,KAAKvC,aACjDgD,OAAQ,qCACRiK,SAEDxB,GAAc,GAAG/F,OAEnBwG,GAAcT,GAAc,GAAGrI,OAC9B2J,EAAAA,EAAAA,KAAA,QACEC,UAAU,0DACV9I,MAAO,CACL4K,WAAY,2CACZlP,MAAO,UACPyE,UAAW,gCACX6K,OAAQ,oBACRN,SAAU,QACV3B,SACH,yBAMLC,EAAAA,EAAAA,MAAA,OACEF,UAAS,6DAAAxR,OAA+DiQ,GAAc,GAAGlJ,KAAK3C,MAAK,uDACnGsE,MAAO,CACL4K,WAAW,2BAADtT,OAA6BiQ,GAAc,GAAGlJ,KAAKjC,YAAW,MAAA9E,OAAKiQ,GAAc,GAAGlJ,KAAKzC,UAAS,KAC5GF,MAAO,UACP+Q,WAAY,8BACZtM,UAAU,cAAD7I,OAAgBiQ,GAAc,GAAGlJ,KAAKvC,YAAW,MAC1DkP,OAAQ,mCACRjC,SAAA,CAEDxB,GAAc,GAAGlJ,KAAKrC,MAAQiS,EAAAA,cAAoB1G,GAAc,GAAGlJ,KAAKrC,KAAM,CAC7E8M,UAAW,UACX9I,MAAO,CAAEtE,MAAO,cAElBmN,EAAAA,EAAAA,KAAA,QAAM7I,MAAO,CAAEtE,MAAO,WAAYqN,SAAExB,GAAc,GAAGlJ,KAAKnC,YAI5D8M,EAAAA,EAAAA,MAAA,OAAKF,UAAU,0BAAyBC,SAAA,EACtCC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,qBAAqB9I,MAAO,CACzCtE,MAAO6L,GAAc,GAAGlJ,KAAKxC,UAC7B4Q,WAAW,eAADnV,OAAiBiQ,GAAc,GAAGlJ,KAAKvC,aACjDgD,OAAQ,qCACRiK,SAAA,CACCxB,GAAc,GAAGpJ,QAAQwN,iBAAiB,UAG7C3C,EAAAA,EAAAA,MAAA,OAAKF,UAAU,oCAAmCC,SAAA,EAChDC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,cAAaC,SAAA,EAC1BC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,yCAAwCC,SAAA,EACrDF,EAAAA,EAAAA,KAACqF,EAAAA,IAAO,CAACpF,UAAU,UAAU9I,MAAO,CAAEtE,MAAO6L,GAAc,GAAGlJ,KAAKzC,cACnEiN,EAAAA,EAAAA,KAAA,QAAMC,UAAU,YAAY9I,MAAO,CAAEtE,MAAO6L,GAAc,GAAGlJ,KAAKzC,WAAYmN,SAC3ExB,GAAc,GAAGlG,wBAGtBwH,EAAAA,EAAAA,KAAA,OAAKC,UAAU,qBAAqB9I,MAAO,CAAEtE,MAAO6L,GAAc,GAAGlJ,KAAKzC,WAAYmN,SAAC,gBAEzFC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,cAAaC,SAAA,EAC1BC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,yCAAwCC,SAAA,EACrDF,EAAAA,EAAAA,KAACgF,EAAAA,IAAO,CAAC/E,UAAU,UAAU9I,MAAO,CAAEtE,MAAO,cAC7CmN,EAAAA,EAAAA,KAAA,QAAMC,UAAU,YAAY9I,MAAO,CAAEtE,MAAO6L,GAAc,GAAGlJ,KAAKzC,WAAYmN,SAC3ExB,GAAc,GAAGtF,oBAGtB4G,EAAAA,EAAAA,KAAA,OAAKC,UAAU,qBAAqB9I,MAAO,CAAEtE,MAAO6L,GAAc,GAAGlJ,KAAKzC,WAAYmN,SAAC,2BAK3F,SAAAzR,OA5MQiQ,GAAc,GAAGrI,MAiNlCqI,GAAc,KACbyB,EAAAA,EAAAA,MAACM,EAAAA,EAAOC,IAAG,CAETyE,IAAKhW,GAAQgN,OAAOuC,GAAc,GAAGrI,OAAS8F,OAAOhN,EAAKkH,KAAO9D,EAAgB,KACjF,eAAcmM,GAAc,GAAGrI,IAC/B,iBAAgB,EAChBsK,QAAS,CAAEC,QAAS,EAAGI,EAAG,IAAKH,EAAG,IAClCC,QAAS,CACPF,QAAS,EACTI,EAAG,EACHH,EAAG,EACHc,MAAO,CAAC,EAAG,KAAM,GACjB0C,QAAS,CAAC,GAAI,EAAG,IAEnBhN,WAAY,CACV+J,MAAO,EACPH,SAAU,IACVU,MAAO,CAAEV,SAAU,EAAGC,OAAQC,IAAU6C,KAAM,aAC9CK,QAAS,CAAEpD,SAAU,EAAGC,OAAQC,IAAU6C,KAAM,cAElDtC,WAAY,CAAEC,MAAO,KAAMd,GAAI,IAC/BZ,UAAS,oBAAAxR,OACP2Q,GAAoBV,GAAc,GAAGrI,KACjC,0CACA,IAENc,MAAO,CACLqL,OAAQnE,OAAOmD,YAAc,IAAM,QAAU,QAC7CpK,UAAWgI,GAAoBV,GAAc,GAAGrI,KAAO,cAAgB,WACvEJ,OAAQmJ,GAAoBV,GAAc,GAAGrI,KACzC,2EACA,OACJgB,WAAY,gBACZ8K,OAAQ/C,GAAoBV,GAAc,GAAGrI,KAAO,oBAAsB,OAC1EqM,aAAcvD,GAAcT,GAAc,GAAGrI,KAAO,OAAS,MAC7D0L,WAAY5C,GAAcT,GAAc,GAAGrI,KAAO,yEAA2E,eAC7H6J,SAAA,EAGFF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,0JAAyJC,UACtKF,EAAAA,EAAAA,KAAA,QAAMC,UAAU,kDAAiDC,SAAC,WAIpEF,EAAAA,EAAAA,KAAA,OACEC,UAAS,8BAAAxR,OAAgCiQ,GAAc,GAAGlJ,KAAK3C,MAAK,oBAAApE,OAAmBiQ,GAAc,GAAGlJ,KAAKtC,KAAI,oBACjHiE,MAAO,CACLG,UAAU,cAAD7I,OAAgBiQ,GAAc,GAAGlJ,KAAKvC,YAAW,MAC1DsP,MAAO,SACPrC,UAEFC,EAAAA,EAAAA,MAAA,OACEF,UAAS,GAAAxR,OAAKiQ,GAAc,GAAGlJ,KAAK1C,QAAO,yEAAwEoN,SAAA,EAEnHF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,oEAGfD,EAAAA,EAAAA,KAAA,OACEC,UAAU,uMACV9I,MAAO,CACLtE,MAAO,UACPsP,OAAQ,qBACRjC,SACH,kBAKDF,EAAAA,EAAAA,KAAA,OAAKC,UAAS,yBAAAxR,OAA2BU,GAAQuP,GAAc,GAAGrI,MAAQlH,EAAKkH,IAAM,yCAA2C,IAAK6J,UACnIF,EAAAA,EAAAA,KAACgC,EAAAA,EAAc,CACb7S,KAAMuP,GAAc,GACpBuD,KAAK,KACLC,kBAAkB,EAClB/K,MAAO,CACLoL,MAAO,OACPC,OAAQ,aAMdxC,EAAAA,EAAAA,KAAA,MACEC,UAAU,kCACV9I,MAAO,CAAEtE,MAAO6L,GAAc,GAAGlJ,KAAKxC,WAAYkN,SAEjDxB,GAAc,GAAG/F,QAGpBwH,EAAAA,EAAAA,MAAA,OAAKF,UAAU,0BAA0B9I,MAAO,CAAEtE,MAAO6L,GAAc,GAAGlJ,KAAKzC,WAAYmN,SAAA,CACxFxB,GAAc,GAAGpJ,QAAQwN,iBAAiB,UAG7C3C,EAAAA,EAAAA,MAAA,OAAKF,UAAU,oCAAmCC,SAAA,EAChDC,EAAAA,EAAAA,MAAA,QAAMhJ,MAAO,CAAEtE,MAAO6L,GAAc,GAAGlJ,KAAKzC,WAAYmN,SAAA,CAAC,gBACnDxB,GAAc,GAAGlG,sBAEvB2H,EAAAA,EAAAA,MAAA,QAAMhJ,MAAO,CAAEtE,MAAO6L,GAAc,GAAGlJ,KAAKzC,WAAYmN,SAAA,CAAC,gBACnDxB,GAAc,GAAGtF,2BAIvB,SAAA3K,OAnGQiQ,GAAc,GAAGrI,YAmHxCxE,EAECJ,EAAY1D,OAAS,IACnBoS,EAAAA,EAAAA,MAACM,EAAAA,EAAOC,IAAG,CACTC,QAAS,CAAEC,QAAS,EAAGC,EAAG,IAC1BC,QAAS,CAAEF,QAAS,EAAGC,EAAG,GAC1BxJ,WAAY,CAAE+J,MAAO,EAAGH,SAAU,IAClChB,UAAU,6BAA4BC,SAAA,EAGtCC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,4BAA2BC,SAAA,EACxCC,EAAAA,EAAAA,MAACM,EAAAA,EAAO6E,GAAE,CACRrF,UAAU,mDACV9I,MAAO,CACL4K,WAAW,0BAADtT,OAA4BiE,EAAab,GAAgB0B,YAAW,MAAA9E,OAAKiE,EAAab,GAAgBkB,UAAS,KACzH2Q,qBAAsB,OACtBC,oBAAqB,cACrBC,WAAY,8BACZ3N,OAAO,wBAADxH,OAA0BiE,EAAab,GAAgB0B,YAAW,MAE1EuN,QAAS,CAAEa,MAAO,CAAC,EAAG,KAAM,IAC5BtK,WAAY,CAAE4J,SAAU,EAAGC,OAAQC,KAAWjB,SAAA,CAE7CxN,EAAab,GAAgB4B,WAAW,IAAEf,EAAab,GAAgBwB,MAAM,WAASX,EAAab,GAAgB4B,eAEtH0M,EAAAA,EAAAA,MAAA,KAAGF,UAAU,iDAAgDC,SAAA,CAC1DzO,EAAY1D,OAAO,gCAEtBiS,EAAAA,EAAAA,KAACS,EAAAA,EAAOgB,OAAM,CACZC,WAAY,CAAEC,MAAO,MACrBC,SAAU,CAAED,MAAO,KACnBpB,QAASA,IAAMzO,EAAkB,MACjCmO,UAAU,oJAAmJC,SAC9J,mCAMHF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,yBAAwBC,UACrCF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,sBAAqBC,SACjCzO,EAAYiH,KAAI,CAAC6M,EAAUvM,KAC1B,MAAMwM,EAAaxM,EAAQ,EACrBmG,EAAgBhQ,GAAQgN,OAAOoJ,EAASlP,OAAS8F,OAAOhN,EAAKkH,KAEnE,OACE2J,EAAAA,EAAAA,KAACS,EAAAA,EAAOC,IAAG,CAETyE,IAAKhG,EAAgB3M,EAAc,KACnC,eAAc+S,EAASlP,IACvB,iBAAgBmP,EAChB7E,QAAS,CAAEC,QAAS,EAAGC,EAAG,IAC1BC,QAAS,CAAEF,QAAS,EAAGC,EAAG,GAC1BxJ,WAAY,CAAE+J,MAAO,GAAc,IAARpI,EAAciI,SAAU,IACnDS,WAAY,CAAEC,MAAO,KAAMd,GAAI,GAC/BZ,UAAS,+BAAAxR,OACP2Q,GAAoBmG,EAASlP,KACzB,0CACA,IAENc,MAAO,CACLC,UAAWgI,GAAoBmG,EAASlP,KAAO,cAAgB,WAC/DJ,OAAQmJ,GAAoBmG,EAASlP,KACjC,4EACA,OACJgB,WAAY,gBACZ8K,OAAQ/C,GAAoBmG,EAASlP,KAAO,oBAAsB,OAClEqM,aAActD,GAAoBmG,EAASlP,KAAO,OAAS,MAC3D0L,WAAY5C,EAAgB,4EAA8E,cAC1GH,SAAU,WACV2D,OAAQxD,EAAgB,GAAK,GAC7Be,UAGFF,EAAAA,EAAAA,KAAA,OACEC,UAAS,oBAAAxR,OAAsB8W,EAAS/P,KAAK3C,MAAK,uBAAApE,OAAsB8W,EAAS/P,KAAKtC,KAAI,yDAC1FiE,MAAO,CACLG,UAAU,cAAD7I,OAAgB8W,EAAS/P,KAAKvC,YAAW,OAClDiN,UAEFC,EAAAA,EAAAA,MAAA,OACEF,UAAS,GAAAxR,OAAK8W,EAAS/P,KAAK1C,QAAO,sFACnCqE,MAAO,CACLgL,OAAO,aAAD1T,OAAe8W,EAAS/P,KAAKjC,YAAW,OAC9C2M,SAAA,EAGFF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,+EAGfE,EAAAA,EAAAA,MAAA,OAAKF,UAAU,wCAAuCC,SAAA,EAEpDF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,WAAUC,UACvBC,EAAAA,EAAAA,MAAA,OACEF,UAAU,mJACV9I,MAAO,CACLtE,MAAO,UACP+Q,WAAY,8BACZzB,OAAQ,kCACR7K,UAAW,6BACX4I,SAAA,CACH,IACGsF,QAKNrF,EAAAA,EAAAA,MAAA,OAAKF,UAAU,WAAUC,SAAA,EACvBF,EAAAA,EAAAA,KAACgC,EAAAA,EAAc,CACb7S,KAAMoW,EACNtD,KAAK,KACLC,kBAAkB,EAClB/K,MAAO,CACLoL,MAAO,OACPC,OAAQ,UAIX+C,EAASnD,WACRpC,EAAAA,EAAAA,KAAA,OACE7I,MAAO,CACL6H,SAAU,WACVqD,OAAQ,OACRC,MAAO,OACPC,MAAO,OACPC,OAAQ,OACRC,gBAAiB,UACjBC,aAAc,MACdP,OAAQ,oBACR7K,UAAW,mCACXqL,OAAQ,IAEVtP,MAAM,WAIT8L,IACCa,EAAAA,EAAAA,KAAA,OACEC,UAAU,+FACV9I,MAAO,CACL4K,WAAY,2CACZzK,UAAW,iCACX4I,UAEFF,EAAAA,EAAAA,KAACxL,EAAAA,IAAM,CAACyL,UAAU,uCAO1BD,EAAAA,EAAAA,KAAA,OAAKC,UAAU,sBAAqBC,UAClCC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,YAAWC,SAAA,EAExBC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,+BAA8BC,SAAA,EAC3CF,EAAAA,EAAAA,KAAA,MACEC,UAAU,0CACV9I,MAAO,CACLtE,MAAO0S,EAAS/P,KAAKxC,UACrB4Q,WAAW,eAADnV,OAAiB8W,EAAS/P,KAAKvC,aACzCgD,OAAQ,qCACRiK,SAEDqF,EAAS5M,OAEXwG,IACCa,EAAAA,EAAAA,KAAA,QACEC,UAAU,0DACV9I,MAAO,CACL4K,WAAY,oDACZlP,MAAO,UACPyE,UAAW,+DACX6K,OAAQ,oBACRyB,WAAY,8BACZ/B,SAAU,OACV2C,WAAY,OACZtE,SACH,yBAOLC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,+BAA8BC,SAAA,CAC1CqF,EAAS5N,MAAM,iBAAU4N,EAASrM,eAMzCiH,EAAAA,EAAAA,MAAA,OAAKF,UAAU,8CAA6CC,SAAA,EAE1DC,EAAAA,EAAAA,MAAA,OACEF,UAAU,qCACV9I,MAAO,CACLtE,MAAO0S,EAAS/P,KAAKxC,UACrB4Q,WAAW,eAADnV,OAAiB8W,EAAS/P,KAAKvC,aACzCgD,OAAQ,qCACRiK,SAAA,CAEDqF,EAASjQ,QAAQwN,iBAAiB,UAIrC3C,EAAAA,EAAAA,MAAA,OAAKF,UAAU,kCAAiCC,SAAA,EAC9CC,EAAAA,EAAAA,MAAA,OACEF,UAAU,+CACV9I,MAAO,CACLsL,gBAAgB,GAADhU,OAAK8W,EAAS/P,KAAKjC,YAAW,MAC7CV,MAAO0S,EAAS/P,KAAKzC,WACrBmN,SAAA,EAEFF,EAAAA,EAAAA,KAACqF,EAAAA,IAAO,CAACpF,UAAU,aACnBD,EAAAA,EAAAA,KAAA,QAAMC,UAAU,cAAaC,SAAEqF,EAAS/M,wBAE1C2H,EAAAA,EAAAA,MAAA,OACEF,UAAU,+CACV9I,MAAO,CACLsL,gBAAiB,YACjB5P,MAAO,WACPqN,SAAA,EAEFF,EAAAA,EAAAA,KAACgF,EAAAA,IAAO,CAAC/E,UAAU,aACnBD,EAAAA,EAAAA,KAAA,QAAMC,UAAU,cAAaC,SAAEqF,EAASnM,+BAjL7CmM,EAASlP,IAuLH,WASzBtB,OAAOU,KAAK1D,GAAchE,OAAS,IACjCoS,EAAAA,EAAAA,MAACM,EAAAA,EAAOC,IAAG,CACTC,QAAS,CAAEC,QAAS,EAAGC,EAAG,IAC1BC,QAAS,CAAEF,QAAS,EAAGC,EAAG,GAC1BxJ,WAAY,CAAE+J,MAAO,EAAGH,SAAU,IAClChB,UAAU,6BACVjD,GAAG,0BAAyBkD,SAAA,EAG5BC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,4BAA2BC,SAAA,EACxCF,EAAAA,EAAAA,KAACS,EAAAA,EAAO6E,GAAE,CACRrF,UAAU,mDACV9I,MAAO,CACL4K,WAAY,oDACZ2B,qBAAsB,OACtBC,oBAAqB,cACrBC,WAAY,8BACZ3N,OAAQ,iCAEV6K,QAAS,CAAEa,MAAO,CAAC,EAAG,KAAM,IAC5BtK,WAAY,CAAE4J,SAAU,EAAGC,OAAQC,KAAWjB,SAC/C,+CAGDF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,iDAAgDC,SAAC,0DAMhEF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,mCAAkCC,SAC9C3I,KAAoBmB,KAAKhD,IACxB,MAAMb,EAASnC,EAAagD,GACtB+I,EAAa1M,EAAa2D,GAC1B+P,EAAWhH,EAAWvP,MAAMuJ,MAAM,EAAG,GAE3C,OACE0H,EAAAA,EAAAA,MAACM,EAAAA,EAAOC,IAAG,CAETyE,IAAMO,GAAQrT,EAAWyE,QAAQpB,GAAagQ,EAC9C/E,QAAS,CAAEC,QAAS,EAAGC,EAAG,IAC1BC,QAAS,CAAEF,QAAS,EAAGC,EAAG,GAC1BxJ,WAAY,CAAE+J,MAAO,GAAKH,SAAU,IACpChB,UAAU,oGACVjD,GAAE,UAAAvO,OAAYiH,GACd,cAAaA,EAAUwK,SAAA,EAGvBC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,yCAAwCC,SAAA,EACrDC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,0BAAyBC,SAAA,EACtCF,EAAAA,EAAAA,KAAA,OACEC,UAAU,iEACV9I,MAAO,CACL4K,WAAW,2BAADtT,OAA6BoG,EAAOtB,YAAW,QAAA9E,OAAOoG,EAAO9B,UAAS,OAChFoP,OAAO,aAAD1T,OAAeoG,EAAOtB,YAAW,MACvC+D,UAAU,cAAD7I,OAAgBoG,EAAO5B,YAAW,OAC3CiN,SAEDrL,EAAOpB,cAEV0M,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEC,EAAAA,EAAAA,MAAA,MACEF,UAAU,2BACV9I,MAAO,CACLtE,MAAOgC,EAAO7B,UACd4Q,WAAW,eAADnV,OAAiBoG,EAAO5B,aAClCgD,OAAQ,qCACRiK,SAAA,CAEDrL,EAAOxB,MAAM,cAEhB8M,EAAAA,EAAAA,MAAA,KAAGF,UAAU,wBAAuBC,SAAA,CACjCzB,EAAWvP,MAAMnB,OAAO,qBAAc8G,EAAOvB,sBAIpD6M,EAAAA,EAAAA,MAACM,EAAAA,EAAOgB,OAAM,CACZC,WAAY,CAAEC,MAAO,MACrBC,SAAU,CAAED,MAAO,KACnBpB,QAASA,IAAMhK,GAAmBb,GAClCuK,UAAU,iJAAgJC,SAAA,CAC3J,aACYzB,EAAWvP,MAAMnB,OAAO,WAKvCiS,EAAAA,EAAAA,KAAA,OAAKC,UAAU,uDAAsDC,SAClEuF,EAAS/M,KAAI,CAAC6M,EAAUvM,KACvB,MAAMmG,EAAgBhQ,GAAQoW,EAASlP,MAAQlH,EAAKkH,IAC9CsP,EAAa3M,EAAQ,EAE3B,OACEgH,EAAAA,EAAAA,KAACS,EAAAA,EAAOC,IAAG,CAET,eAAc6E,EAASlP,IACvB,iBAAgBsP,EAChBhF,QAAS,CAAEC,QAAS,EAAGe,MAAO,IAC9Bb,QAAS,CAAEF,QAAS,EAAGe,MAAO,GAC9BtK,WAAY,CAAE+J,MAAO,GAAc,GAARpI,EAAaiI,SAAU,IAClDS,WAAY,CAAEC,MAAO,KAAMd,GAAI,GAC/BZ,UAAS,YAAAxR,OACP2Q,GAAoBmG,EAASlP,KACzB,4BACA,IACH6J,UAEHF,EAAAA,EAAAA,KAAA,OACEC,UAAS,qBAAAxR,OAAuB8W,EAAS/P,KAAK3C,MAAK,sBAAApE,OAAqB8W,EAAS/P,KAAKtC,KAAI,cAC1FiE,MAAO,CACLG,UAAU,cAAD7I,OAAgB8W,EAAS/P,KAAKvC,YAAW,OAClDiN,UAEFC,EAAAA,EAAAA,MAAA,OACEF,UAAS,GAAAxR,OAAK8W,EAAS/P,KAAK1C,QAAO,yEAAwEoN,SAAA,EAE3GF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,oEAGfE,EAAAA,EAAAA,MAAA,OACEF,UAAU,oGACV9I,MAAO,CACL4K,WAAYlN,EAAOtB,YACnBV,MAAO,UACPsP,OAAQ,qBACRjC,SAAA,CACH,IACGyF,MAIJxF,EAAAA,EAAAA,MAAA,OAAKF,UAAS,yBAAAxR,OACZ0Q,EACI,yCACA,IACHe,SAAA,EACDF,EAAAA,EAAAA,KAACgC,EAAAA,EAAc,CACb7S,KAAMoW,EACNtD,KAAK,KACLC,kBAAkB,EAClB/K,MAAO,CACLoL,MAAO,OACPC,OAAQ,UAGXrD,IACCa,EAAAA,EAAAA,KAAA,OACEC,UAAU,kGACV9I,MAAO,CACL4K,WAAY,2CACZzK,UAAW,iCACX4I,UAEFF,EAAAA,EAAAA,KAACxL,EAAAA,IAAM,CAACyL,UAAU,oCAMxBE,EAAAA,EAAAA,MAAA,MACEF,UAAU,kCACV9I,MAAO,CAAEtE,MAAO0S,EAAS/P,KAAKxC,WAAYkN,SAAA,CAEzCqF,EAAS5M,KACTwG,IACCa,EAAAA,EAAAA,KAAA,QAAMC,UAAU,+BAA8BC,SAAC,qBAInDC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,0BAA0B9I,MAAO,CAAEtE,MAAO0S,EAAS/P,KAAKzC,WAAYmN,SAAA,CAChFqF,EAASjQ,QAAQwN,iBAAiB,UAGrC3C,EAAAA,EAAAA,MAAA,OAAKF,UAAU,oCAAmCC,SAAA,EAChDC,EAAAA,EAAAA,MAAA,QAAMhJ,MAAO,CAAEtE,MAAO0S,EAAS/P,KAAKzC,WAAYmN,SAAA,CAAC,gBAC3CqF,EAAS/M,sBAEf2H,EAAAA,EAAAA,MAAA,QAAMhJ,MAAO,CAAEtE,MAAO0S,EAAS/P,KAAKzC,WAAYmN,SAAA,CAAC,gBAC3CqF,EAASnM,0BApFhBmM,EAASlP,IAyFH,MAMlBoI,EAAWvP,MAAMnB,OAAS,IACzBiS,EAAAA,EAAAA,KAAA,OAAKC,UAAU,mBAAkBC,UAC/BC,EAAAA,EAAAA,MAAA,KAAGF,UAAU,wBAAuBC,SAAA,CAAC,IACjCzB,EAAWvP,MAAMnB,OAAS,EAAE,wCA1J/B2H,EA8JM,SAYD,IAAvBjF,EAAY1C,SAAiB4C,IAC5BwP,EAAAA,EAAAA,MAACM,EAAAA,EAAOC,IAAG,CACTC,QAAS,CAAEC,QAAS,EAAGe,MAAO,IAC9Bb,QAAS,CAAEF,QAAS,EAAGe,MAAO,GAC9B1B,UAAU,oBAAmBC,SAAA,EAE7BF,EAAAA,EAAAA,KAAC5L,EAAAA,IAAQ,CAAC6L,UAAU,0CACpBD,EAAAA,EAAAA,KAAA,MAAIC,UAAU,0BAA0B9I,MAAO,CAC7CtE,MAAO,UACP+Q,WAAY,8BACZY,WAAY,OACZtE,SAAC,sBACHF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,UAAU9I,MAAO,CAC5BtE,MAAO,UACP+Q,WAAY,8BACZY,WAAY,OACZtE,SAAC,8FAUd,C", "sources": ["apicalls/reports.js", "pages/user/Ranking/index.js"], "sourcesContent": ["const { default: axiosInstance } = require(\".\");\r\n\r\n// add report\r\nexport const addReport = async (payload) => {\r\n    try {\r\n        const response = await axiosInstance.post(\"/api/reports/add-report\", payload);\r\n        return response.data;\r\n    } catch (error) {\r\n        return error.response.data;\r\n    }\r\n}\r\n\r\n// get all reports\r\nexport const getAllReports = async (filters) => {\r\n    try {\r\n        const response = await axiosInstance.post(\"/api/reports/get-all-reports\", filters);\r\n        return response.data;\r\n    } catch (error) {\r\n        return error.response.data;\r\n    }\r\n}\r\n\r\n// get all reports by user\r\nexport const getAllReportsByUser = async (payload) => {\r\n    try {\r\n        const response = await axiosInstance.post(\"/api/reports/get-all-reports-by-user\", payload);\r\n        return response.data;\r\n    } catch (error) {\r\n        return error.response.data;\r\n    }\r\n}\r\n\r\n// get all reports for ranking (legacy)\r\nexport const getAllReportsForRanking = async (filters) => {\r\n    try {\r\n        const response = await axiosInstance.get(\"/api/reports/get-all-reports-for-ranking\");\r\n        return response.data;\r\n    } catch (error) {\r\n        return error.response.data;\r\n    }\r\n}\r\n\r\n// Enhanced XP-based ranking endpoints\r\nexport const getEnhancedLeaderboard = async (level = 'all', limit = 1000) => {\r\n    try {\r\n        const response = await axiosInstance.get(`/api/quiz/enhanced-leaderboard?level=${level}&limit=${limit}`);\r\n        return response.data;\r\n    } catch (error) {\r\n        return error.response.data;\r\n    }\r\n}\r\n\r\nexport const getXPLeaderboard = async (options = {}) => {\r\n    try {\r\n        const params = new URLSearchParams();\r\n        if (options.limit) params.append('limit', options.limit);\r\n        if (options.classFilter) params.append('classFilter', options.classFilter);\r\n        if (options.levelFilter) params.append('levelFilter', options.levelFilter);\r\n        if (options.seasonFilter) params.append('seasonFilter', options.seasonFilter);\r\n        if (options.includeInactive) params.append('includeInactive', options.includeInactive);\r\n\r\n        const response = await axiosInstance.get(`/api/quiz/xp-leaderboard?${params.toString()}`);\r\n        return response.data;\r\n    } catch (error) {\r\n        return error.response.data;\r\n    }\r\n}\r\n\r\nexport const getUserRanking = async (userId, context = 5) => {\r\n    try {\r\n        const response = await axiosInstance.get(`/api/quiz/user-ranking/${userId}?context=${context}`);\r\n        return response.data;\r\n    } catch (error) {\r\n        return error.response.data;\r\n    }\r\n}\r\n\r\nexport const getClassRankings = async (className, limit = 50) => {\r\n    try {\r\n        const response = await axiosInstance.get(`/api/quiz/class-rankings/${className}?limit=${limit}`);\r\n        return response.data;\r\n    } catch (error) {\r\n        return error.response.data;\r\n    }\r\n}\r\n\r\n// XP Dashboard endpoints\r\nexport const getXPDashboard = async () => {\r\n    try {\r\n        const response = await axiosInstance.get(\"/api/xp-dashboard/dashboard\");\r\n        return response.data;\r\n    } catch (error) {\r\n        return error.response.data;\r\n    }\r\n}\r\n\r\nexport const getClassLeaderboard = async () => {\r\n    try {\r\n        const response = await axiosInstance.get(\"/api/xp-dashboard/class-leaderboard\");\r\n        return response.data;\r\n    } catch (error) {\r\n        return error.response.data;\r\n    }\r\n}\r\n", "import React, { useState, useEffect, useRef } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { useSelector } from 'react-redux';\nimport { useNavigate } from 'react-router-dom';\nimport { message } from 'antd';\nimport {\n  TbTrophy,\n  TbCrown,\n  TbStar,\n  TbFlame,\n  TbBrain,\n  TbHome,\n  TbRefresh,\n  TbMedal,\n  TbRocket,\n  TbDiamond,\n  TbAward,\n  TbShield,\n  TbUsers\n} from 'react-icons/tb';\nimport { getAllReportsForRanking, getXPLeaderboard, getUserRanking } from '../../../apicalls/reports';\nimport { getAllUsers } from '../../../apicalls/users';\nimport ProfilePicture from '../../../components/common/ProfilePicture';\nimport OnlineStatusIndicator from '../../../components/common/OnlineStatusIndicator';\n\nconst AmazingRankingPage = () => {\n  const userState = useSelector((state) => state.users || {});\n  const reduxUser = userState.user || null;\n\n  // Try multiple sources for user data\n  const localStorageUser = (() => {\n    try {\n      const userData = localStorage.getItem('user');\n      return userData ? JSON.parse(userData) : null;\n    } catch {\n      return null;\n    }\n  })();\n\n  const tokenUser = (() => {\n    try {\n      const token = localStorage.getItem('token');\n      if (token) {\n        const payload = JSON.parse(atob(token.split('.')[1]));\n        return payload;\n      }\n      return null;\n    } catch {\n      return null;\n    }\n  })();\n\n  // Use the first available user data\n  const user = reduxUser || localStorageUser || tokenUser;\n\n  // State for full user data\n  const [fullUserData, setFullUserData] = useState(null);\n\n  // Debug: Log all user sources\n  console.log('🔍 User Data Sources:', {\n    redux: reduxUser,\n    localStorage: localStorageUser,\n    token: tokenUser,\n    final: user\n  });\n\n  // Debug: Log user data structure for migrated users (simplified)\n  if (user && !fullUserData) {\n    console.log('🔍 Loading user data for:', user.userId);\n  }\n  const navigate = useNavigate();\n  const [rankingData, setRankingData] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [currentUserRank, setCurrentUserRank] = useState(null);\n  const [viewMode, setViewMode] = useState('global');\n  const [showStats, setShowStats] = useState(true);\n  const [animationPhase, setAnimationPhase] = useState(0);\n  const [motivationalQuote, setMotivationalQuote] = useState('');\n\n  const [currentUserLeague, setCurrentUserLeague] = useState(null);\n  const [leagueUsers, setLeagueUsers] = useState([]);\n  const [showLeagueView, setShowLeagueView] = useState(false);\n  const [selectedLeague, setSelectedLeague] = useState(null);\n  const [leagueGroups, setLeagueGroups] = useState({});\n  const [userHasBeenShown, setUserHasBeenShown] = useState(false);\n  const [autoScrollCompleted, setAutoScrollCompleted] = useState(false);\n\n  // Refs for league sections\n  const leagueRefs = useRef({});\n  const headerRef = useRef(null);\n  const currentUserRef = useRef(null);\n  const podiumUserRef = useRef(null);\n  const listUserRef = useRef(null);\n\n  // Motivational quotes for different performance levels\n  const motivationalQuotes = [\n    \"🚀 Every expert was once a beginner. Keep climbing!\",\n    \"⭐ Your potential is endless. Show them what you're made of!\",\n    \"🔥 Champions are made in the moments when nobody's watching.\",\n    \"💎 Pressure makes diamonds. You're becoming brilliant!\",\n    \"🎯 Success is not final, failure is not fatal. Keep going!\",\n    \"⚡ The only impossible journey is the one you never begin.\",\n    \"🌟 Believe in yourself and all that you are capable of!\",\n    \"🏆 Greatness is not about being better than others, it's about being better than yesterday.\",\n    \"💪 Your only limit is your mind. Break through it!\",\n    \"🎨 Paint your success with the colors of determination!\"\n  ];\n\n  // Enhanced League System with Duolingo-style progression\n  const leagueSystem = {\n    mythic: {\n      min: 50000,\n      color: 'from-purple-300 via-pink-300 via-red-300 to-orange-300',\n      bgColor: 'bg-gradient-to-br from-purple-900/50 via-pink-900/50 to-red-900/50',\n      textColor: '#FFD700',\n      nameColor: '#FF1493',\n      shadowColor: 'rgba(255, 20, 147, 0.9)',\n      glow: 'shadow-pink-500/90',\n      icon: TbCrown,\n      title: 'MYTHIC',\n      description: 'Legendary Master',\n      borderColor: '#FF1493',\n      effect: 'mythic-aura',\n      leagueIcon: '👑',\n      promotionXP: 0, // Max league\n      relegationXP: 40000,\n      maxUsers: 10\n    },\n    legendary: {\n      min: 25000,\n      color: 'from-purple-400 via-indigo-400 via-blue-400 to-cyan-400',\n      bgColor: 'bg-gradient-to-br from-purple-900/40 via-indigo-900/40 to-blue-900/40',\n      textColor: '#8A2BE2',\n      nameColor: '#9370DB',\n      shadowColor: 'rgba(138, 43, 226, 0.9)',\n      glow: 'shadow-purple-500/80',\n      icon: TbDiamond,\n      title: 'LEGENDARY',\n      description: 'Elite Champion',\n      borderColor: '#8A2BE2',\n      effect: 'legendary-sparkle',\n      leagueIcon: '💎',\n      promotionXP: 50000,\n      relegationXP: 20000,\n      maxUsers: 25\n    },\n    diamond: {\n      min: 12000,\n      color: 'from-cyan-300 via-blue-300 via-indigo-300 to-purple-300',\n      bgColor: 'bg-gradient-to-br from-cyan-900/40 via-blue-900/40 to-indigo-900/40',\n      textColor: '#00CED1',\n      nameColor: '#40E0D0',\n      shadowColor: 'rgba(0, 206, 209, 0.9)',\n      glow: 'shadow-cyan-400/80',\n      icon: TbShield,\n      title: 'DIAMOND',\n      description: 'Expert Level',\n      borderColor: '#00CED1',\n      effect: 'diamond-shine',\n      leagueIcon: '🛡️',\n      promotionXP: 25000,\n      relegationXP: 8000,\n      maxUsers: 50\n    },\n    platinum: {\n      min: 6000,\n      color: 'from-slate-300 via-gray-300 via-zinc-300 to-stone-300',\n      bgColor: 'bg-gradient-to-br from-slate-800/40 via-gray-800/40 to-zinc-800/40',\n      textColor: '#C0C0C0',\n      nameColor: '#D3D3D3',\n      shadowColor: 'rgba(192, 192, 192, 0.9)',\n      glow: 'shadow-slate-400/80',\n      icon: TbAward,\n      title: 'PLATINUM',\n      description: 'Advanced',\n      borderColor: '#C0C0C0',\n      effect: 'platinum-gleam',\n      leagueIcon: '🏆',\n      promotionXP: 12000,\n      relegationXP: 4000,\n      maxUsers: 100\n    },\n    gold: {\n      min: 3000,\n      color: 'from-yellow-300 via-amber-300 via-orange-300 to-red-300',\n      bgColor: 'bg-gradient-to-br from-yellow-900/40 via-amber-900/40 to-orange-900/40',\n      textColor: '#FFD700',\n      nameColor: '#FFA500',\n      shadowColor: 'rgba(255, 215, 0, 0.9)',\n      glow: 'shadow-yellow-400/80',\n      icon: TbTrophy,\n      title: 'GOLD',\n      description: 'Skilled',\n      borderColor: '#FFD700',\n      effect: 'gold-glow',\n      leagueIcon: '🥇',\n      promotionXP: 6000,\n      relegationXP: 2000,\n      maxUsers: 200\n    },\n    silver: {\n      min: 1500,\n      color: 'from-gray-300 via-slate-300 via-zinc-300 to-gray-300',\n      bgColor: 'bg-gradient-to-br from-gray-800/40 via-slate-800/40 to-zinc-800/40',\n      textColor: '#C0C0C0',\n      nameColor: '#B8B8B8',\n      shadowColor: 'rgba(192, 192, 192, 0.9)',\n      glow: 'shadow-gray-400/80',\n      icon: TbMedal,\n      title: 'SILVER',\n      description: 'Improving',\n      borderColor: '#C0C0C0',\n      effect: 'silver-shimmer',\n      leagueIcon: '🥈',\n      promotionXP: 3000,\n      relegationXP: 800,\n      maxUsers: 300\n    },\n    bronze: {\n      min: 500,\n      color: 'from-orange-300 via-amber-300 via-yellow-300 to-orange-300',\n      bgColor: 'bg-gradient-to-br from-orange-900/40 via-amber-900/40 to-yellow-900/40',\n      textColor: '#CD7F32',\n      nameColor: '#D2691E',\n      shadowColor: 'rgba(205, 127, 50, 0.9)',\n      glow: 'shadow-orange-400/80',\n      icon: TbStar,\n      title: 'BRONZE',\n      description: 'Learning',\n      borderColor: '#CD7F32',\n      effect: 'bronze-warm',\n      leagueIcon: '🥉',\n      promotionXP: 1500,\n      relegationXP: 200,\n      maxUsers: 500\n    },\n    rookie: {\n      min: 0,\n      color: 'from-green-300 via-emerald-300 via-teal-300 to-cyan-300',\n      bgColor: 'bg-gradient-to-br from-green-900/40 via-emerald-900/40 to-teal-900/40',\n      textColor: '#32CD32',\n      nameColor: '#90EE90',\n      shadowColor: 'rgba(50, 205, 50, 0.9)',\n      glow: 'shadow-green-400/80',\n      icon: TbRocket,\n      title: 'ROOKIE',\n      description: 'Starting Out',\n      borderColor: '#32CD32',\n      effect: 'rookie-glow',\n      leagueIcon: '🚀',\n      promotionXP: 500,\n      relegationXP: 0, // Can't be relegated from rookie\n      maxUsers: 1000\n    }\n  };\n\n  // Get user's league based on XP with enhanced progression\n  const getUserLeague = (xp) => {\n    for (const [league, config] of Object.entries(leagueSystem)) {\n      if (xp >= config.min) return { league, ...config };\n    }\n    return { league: 'rookie', ...leagueSystem.rookie };\n  };\n\n  // Group users by their leagues for better organization\n  const groupUsersByLeague = (users) => {\n    const leagues = {};\n\n    users.forEach(user => {\n      const userLeague = getUserLeague(user.totalXP);\n      if (!leagues[userLeague.league]) {\n        leagues[userLeague.league] = {\n          config: userLeague,\n          users: []\n        };\n      }\n      leagues[userLeague.league].users.push({\n        ...user,\n        tier: userLeague // Update to use league instead of tier\n      });\n    });\n\n    // Sort users within each league by XP\n    Object.keys(leagues).forEach(leagueKey => {\n      leagues[leagueKey].users.sort((a, b) => b.totalXP - a.totalXP);\n    });\n\n    return leagues;\n  };\n\n  // Get current user's league and friends in the same league\n  const getCurrentUserLeagueData = (allUsers, currentUser) => {\n    if (!currentUser) return null;\n\n    const userLeague = getUserLeague(currentUser.totalXP || 0);\n    const leagueUsers = allUsers.filter(user => {\n      const league = getUserLeague(user.totalXP);\n      return league.league === userLeague.league;\n    }).sort((a, b) => b.totalXP - a.totalXP);\n\n    return {\n      league: userLeague,\n      users: leagueUsers,\n      userRank: leagueUsers.findIndex(u => u._id === currentUser._id) + 1,\n      totalInLeague: leagueUsers.length\n    };\n  };\n\n  // Handle league selection with unique visual effect\n  const handleLeagueSelect = (leagueKey) => {\n    console.log('🎯 League selected:', leagueKey);\n\n    // Set selected league with unique visual effect\n    setSelectedLeague(leagueKey);\n    setShowLeagueView(true);\n    setLeagueUsers(leagueGroups[leagueKey]?.users || []);\n\n    // Scroll to league section with smooth animation\n    setTimeout(() => {\n      const leagueElement = document.querySelector(`[data-league=\"${leagueKey}\"]`) ||\n                           document.getElementById(`league-${leagueKey}`) ||\n                           leagueRefs.current[leagueKey];\n\n      if (leagueElement) {\n        leagueElement.scrollIntoView({\n          behavior: 'smooth',\n          block: 'center',\n          inline: 'nearest'\n        });\n\n        // Add unique visual effect - pulse animation\n        leagueElement.style.transform = 'scale(1.02)';\n        leagueElement.style.transition = 'all 0.3s ease';\n        leagueElement.style.boxShadow = '0 0 30px rgba(59, 130, 246, 0.5)';\n\n        setTimeout(() => {\n          leagueElement.style.transform = 'scale(1)';\n          leagueElement.style.boxShadow = '';\n        }, 600);\n      }\n    }, 100);\n  };\n\n  // Get ordered league keys from best to worst\n  const getOrderedLeagues = () => {\n    const leagueOrder = ['mythic', 'legendary', 'diamond', 'platinum', 'gold', 'silver', 'bronze', 'rookie'];\n    return leagueOrder.filter(league => leagueGroups[league] && leagueGroups[league].users.length > 0);\n  };\n\n\n\n  // Fetch ranking data using enhanced XP system\n  const fetchRankingData = async (forceRefresh = false) => {\n    try {\n      // Clear old caches for other levels to prevent contamination\n      const currentLevel = user?.level || 'primary';\n      const allLevels = ['primary', 'secondary', 'advance'];\n      allLevels.forEach(level => {\n        if (level !== currentLevel) {\n          localStorage.removeItem(`ranking_cache_${level}`);\n          localStorage.removeItem(`ranking_cache_time_${level}`);\n        }\n      });\n      // Check cache first (unless force refresh)\n      if (!forceRefresh) {\n        const userLevel = user?.level || 'primary';\n        const cachedRanking = localStorage.getItem(`ranking_cache_${userLevel}`);\n        const cacheTime = localStorage.getItem(`ranking_cache_time_${userLevel}`);\n        const now = Date.now();\n\n        // Use cache if less than 2 minutes old and for the same level\n        if (cachedRanking && cacheTime && (now - parseInt(cacheTime)) < 120000) {\n          const cached = JSON.parse(cachedRanking);\n          setRankingData(cached.data || []);\n          setCurrentUserRank(cached.userRank);\n          setLoading(false);\n          return;\n        }\n      }\n\n      setLoading(true);\n      console.log('🚀 Fetching enhanced XP ranking data...', forceRefresh ? '(Force Refresh)' : '');\n\n      // Try the new XP-based leaderboard first\n      try {\n        console.log('📊 Fetching XP leaderboard...');\n        const xpLeaderboardResponse = await getXPLeaderboard({\n          limit: 1000,\n          levelFilter: user?.level || 'all',\n          includeInactive: false,\n          // Add timestamp for cache busting when force refreshing\n          ...(forceRefresh && { _t: Date.now() })\n        });\n\n        console.log('✨ XP Leaderboard response:', xpLeaderboardResponse);\n\n        if (xpLeaderboardResponse && xpLeaderboardResponse.success && xpLeaderboardResponse.data) {\n          console.log('🎯 Using enhanced XP ranking data');\n\n          // Filter to only include users who have actually taken quizzes and earned XP\n          const filteredData = xpLeaderboardResponse.data.filter(userData =>\n            (userData.totalXP && userData.totalXP > 0) ||\n            (userData.totalQuizzesTaken && userData.totalQuizzesTaken > 0)\n          );\n\n          // Debug: Check first few users' profile data\n          console.log('🔍 First 3 users profile data:', filteredData.slice(0, 3).map(u => ({\n            _id: u._id,\n            name: u.name,\n            profileImage: u.profileImage,\n            profilePicture: u.profilePicture,\n            hasProfileData: !!(u.profileImage || u.profilePicture)\n          })));\n\n          const transformedData = filteredData.map((userData, index) => ({\n            _id: userData._id,\n            name: userData.name || 'Anonymous Champion',\n            email: userData.email || '',\n            class: userData.class || '',\n            level: userData.level || '',\n            profilePicture: userData.profileImage || userData.profilePicture || '',\n            profileImage: userData.profileImage || userData.profilePicture || '',\n            totalXP: userData.totalXP || 0,\n            totalQuizzesTaken: userData.totalQuizzesTaken || 0,\n            averageScore: userData.averageScore || 0,\n            currentStreak: userData.currentStreak || 0,\n            bestStreak: userData.bestStreak || 0,\n            subscriptionStatus: userData.subscriptionStatus || 'free',\n            rank: index + 1,\n            tier: getUserLeague(userData.totalXP || 0),\n            isRealUser: true,\n            rankingScore: userData.rankingScore || 0,\n            // Enhanced XP data\n            currentLevel: userData.currentLevel || 1,\n            xpToNextLevel: userData.xpToNextLevel || 100,\n            lifetimeXP: userData.lifetimeXP || 0,\n            seasonXP: userData.seasonXP || 0,\n            achievements: userData.achievements || [],\n            dataSource: 'enhanced_xp'\n          }));\n\n          // Debug: Check final transformed data for top 3 users\n          console.log('🏆 Top 3 transformed users:', transformedData.slice(0, 3).map(u => ({\n            _id: u._id,\n            name: u.name,\n            profileImage: u.profileImage,\n            profilePicture: u.profilePicture,\n            hasProfileData: !!(u.profileImage || u.profilePicture)\n          })));\n\n          setRankingData(transformedData);\n\n          // Find current user's rank\n          const userRankIndex = transformedData.findIndex(item => item._id === user?._id);\n          setCurrentUserRank(userRankIndex >= 0 ? userRankIndex + 1 : null);\n\n          // Set up league data for current user\n          if (user) {\n            const userLeagueData = getCurrentUserLeagueData(transformedData, user);\n            setCurrentUserLeague(userLeagueData);\n            setLeagueUsers(userLeagueData?.users || []);\n          }\n\n          // Group all users by their leagues\n          const grouped = groupUsersByLeague(transformedData);\n          setLeagueGroups(grouped);\n\n          // Cache the results with level-specific key\n          const userLevel = user?.level || 'primary';\n          const cacheData = {\n            data: transformedData,\n            userRank: userRankIndex >= 0 ? userRankIndex + 1 : null\n          };\n          localStorage.setItem(`ranking_cache_${userLevel}`, JSON.stringify(cacheData));\n          localStorage.setItem(`ranking_cache_time_${userLevel}`, Date.now().toString());\n\n          setLoading(false);\n          return;\n        }\n      } catch (xpError) {\n        console.log('⚠️ XP leaderboard failed, trying fallback:', xpError);\n      }\n\n      // Fallback to legacy system if XP leaderboard fails\n      console.log('🔄 Falling back to legacy ranking system...');\n\n      let rankingResponse, usersResponse;\n\n      try {\n        console.log('📊 Fetching legacy ranking reports...');\n        rankingResponse = await getAllReportsForRanking();\n        console.log('👥 Fetching all users...');\n        usersResponse = await getAllUsers();\n      } catch (error) {\n        console.log('⚡ Error fetching legacy data:', error);\n        try {\n          usersResponse = await getAllUsers();\n        } catch (userError) {\n          console.log('❌ Failed to fetch users:', userError);\n        }\n      }\n\n      let transformedData = [];\n\n      if (usersResponse && usersResponse.success && usersResponse.data) {\n        console.log('🔄 Processing legacy user data...');\n\n        // Create a map of user reports for quick lookup\n        const userReportsMap = {};\n        if (rankingResponse && rankingResponse.success && rankingResponse.data) {\n          rankingResponse.data.forEach(item => {\n            const userId = item.user?._id || item.userId;\n            if (userId) {\n              userReportsMap[userId] = item.reports || [];\n            }\n          });\n        }\n\n        transformedData = usersResponse.data\n          .filter(userData => {\n            // Filter out invalid users\n            if (!userData || !userData._id) return false;\n\n            // Apply level filtering for non-admin users\n            if (!userData.isAdmin && user?.level) {\n              const userLevel = user.level.toLowerCase();\n              const dataLevel = (userData.level || 'primary').toLowerCase();\n\n              if (userLevel === 'primary') {\n                // Primary users should only see primary users\n                return dataLevel === 'primary';\n              } else if (userLevel === 'secondary') {\n                // Secondary users should only see secondary users\n                return dataLevel === 'secondary';\n              } else if (userLevel === 'advance') {\n                // Advance users should only see advance users\n                return dataLevel === 'advance';\n              }\n            }\n\n            return true; // Include admins and when no level filtering needed\n          })\n          .map((userData, index) => {\n            // Get reports for this user\n            const userReports = userReportsMap[userData._id] || [];\n\n            // Use existing user data or calculate from reports\n            let totalQuizzes = userReports.length || userData.totalQuizzesTaken || 0;\n            let totalScore = userReports.reduce((sum, report) => sum + (report.score || 0), 0);\n            let averageScore = totalQuizzes > 0 ? Math.round(totalScore / totalQuizzes) : userData.averageScore || 0;\n\n            // For existing users with old data, make intelligent assumptions\n            if (!userReports.length && userData.totalPoints) {\n              // Assume higher points = more exams and better performance\n              const estimatedQuizzes = Math.max(1, Math.floor(userData.totalPoints / 100)); // Assume ~100 points per quiz\n              const estimatedAverage = Math.min(95, Math.max(60, 60 + (userData.totalPoints / estimatedQuizzes / 10))); // Scale average based on points\n\n              totalQuizzes = estimatedQuizzes;\n              averageScore = Math.round(estimatedAverage);\n              totalScore = Math.round(averageScore * totalQuizzes);\n\n              console.log(`📊 Estimated stats for ${userData.name}: ${estimatedQuizzes} quizzes, ${estimatedAverage}% avg from ${userData.totalPoints} points`);\n            }\n\n            // Calculate XP based on performance (enhanced calculation)\n            let totalXP = userData.totalXP || 0;\n\n            if (!totalXP) {\n              // Calculate XP from available data\n              if (userData.totalPoints) {\n                // Use existing points as base XP with bonuses\n                totalXP = Math.floor(\n                  userData.totalPoints + // Base points\n                  (totalQuizzes * 25) + // Participation bonus\n                  (averageScore > 80 ? totalQuizzes * 15 : 0) + // Excellence bonus\n                  (averageScore > 90 ? totalQuizzes * 10 : 0) // Mastery bonus\n                );\n              } else if (totalQuizzes > 0) {\n                // Calculate from quiz performance\n                totalXP = Math.floor(\n                  (averageScore * totalQuizzes * 8) + // Base XP from scores\n                  (totalQuizzes * 40) + // Participation bonus\n                  (averageScore > 80 ? totalQuizzes * 20 : 0) // Excellence bonus\n                );\n              }\n            }\n\n            // Calculate streaks (enhanced logic)\n            let currentStreak = userData.currentStreak || 0;\n            let bestStreak = userData.bestStreak || 0;\n\n            if (userReports.length > 0) {\n              // Calculate from actual reports\n              let tempStreak = 0;\n              userReports.forEach(report => {\n                if (report.score >= 60) { // Passing score\n                  tempStreak++;\n                  bestStreak = Math.max(bestStreak, tempStreak);\n                } else {\n                  tempStreak = 0;\n                }\n              });\n              currentStreak = tempStreak;\n            } else if (userData.totalPoints && !currentStreak) {\n              // Estimate streaks from points (higher points = likely better streaks)\n              const pointsPerQuiz = totalQuizzes > 0 ? userData.totalPoints / totalQuizzes : 0;\n              if (pointsPerQuiz > 80) {\n                currentStreak = Math.min(totalQuizzes, Math.floor(pointsPerQuiz / 20)); // Estimate current streak\n                bestStreak = Math.max(currentStreak, Math.floor(pointsPerQuiz / 15)); // Estimate best streak\n              }\n            }\n\n            return {\n              _id: userData._id,\n              name: userData.name || 'Anonymous Champion',\n              email: userData.email || '',\n              class: userData.class || '',\n              level: userData.level || '',\n              profilePicture: userData.profileImage || userData.profilePicture || '',\n              profileImage: userData.profileImage || userData.profilePicture || '',\n              totalXP: totalXP,\n              totalQuizzesTaken: totalQuizzes,\n              averageScore: averageScore,\n              currentStreak: currentStreak,\n              bestStreak: bestStreak,\n              subscriptionStatus: userData.subscriptionStatus || 'free',\n              rank: index + 1,\n              tier: getUserLeague(totalXP),\n              isRealUser: true,\n              // Additional tracking fields for future updates\n              originalPoints: userData.totalPoints || 0,\n              hasReports: userReports.length > 0,\n              dataSource: userReports.length > 0 ? 'reports' : userData.totalPoints ? 'legacy_points' : 'estimated'\n            };\n          });\n\n        // Sort by XP descending\n        transformedData.sort((a, b) => b.totalXP - a.totalXP);\n        \n        // Update ranks after sorting\n        transformedData.forEach((user, index) => {\n          user.rank = index + 1;\n        });\n\n        setRankingData(transformedData);\n\n        // Cache the fallback results with level-specific key\n        const userLevel = user?.level || 'primary';\n        const cacheData = {\n          data: transformedData,\n          userRank: null // Will be set below after finding user rank\n        };\n\n        // Find current user's rank with multiple matching strategies\n        let userRank = -1;\n        if (user) {\n          // Try exact ID match first\n          userRank = transformedData.findIndex(item => item._id === user._id);\n\n          // If not found, try string comparison (in case of type differences)\n          if (userRank === -1) {\n            userRank = transformedData.findIndex(item => String(item._id) === String(user._id));\n          }\n\n          // If still not found, try matching by name (as fallback)\n          if (userRank === -1 && user.name) {\n            userRank = transformedData.findIndex(item => item.name === user.name);\n          }\n        }\n\n        setCurrentUserRank(userRank >= 0 ? userRank + 1 : null);\n\n        // Update cache with final user rank\n        cacheData.userRank = userRank >= 0 ? userRank + 1 : null;\n        localStorage.setItem(`ranking_cache_${userLevel}`, JSON.stringify(cacheData));\n        localStorage.setItem(`ranking_cache_time_${userLevel}`, Date.now().toString());\n\n        // Set up league data for current user\n        if (user) {\n          const userLeagueData = getCurrentUserLeagueData(transformedData, user);\n          setCurrentUserLeague(userLeagueData);\n          setLeagueUsers(userLeagueData?.users || []);\n        }\n\n        // Group all users by their leagues\n        const grouped = groupUsersByLeague(transformedData);\n        setLeagueGroups(grouped);\n\n        // Enhanced debug logging for user ranking (development only)\n        if (process.env.NODE_ENV === 'development') {\n          console.log('🔍 Enhanced User ranking debug:', {\n            currentUser: user?.name,\n            userId: user?._id,\n            userIdType: typeof user?._id,\n            isAdmin: user?.role === 'admin' || user?.isAdmin,\n            userXP: user?.totalXP,\n            userRankIndex: userRank,\n            userRankPosition: userRank >= 0 ? userRank + 1 : null,\n            totalRankedUsers: transformedData.length,\n            firstFewUserIds: transformedData.slice(0, 5).map(u => ({ id: u._id, type: typeof u._id, name: u.name })),\n            exactMatch: transformedData.find(item => item._id === user?._id),\n            stringMatch: transformedData.find(item => String(item._id) === String(user?._id)),\n            nameMatch: transformedData.find(item => item.name === user?.name)\n          });\n        }\n\n        // Log data sources for transparency\n        const dataSources = {\n          reports: transformedData.filter(u => u.dataSource === 'reports').length,\n          legacy_points: transformedData.filter(u => u.dataSource === 'legacy_points').length,\n          estimated: transformedData.filter(u => u.dataSource === 'estimated').length\n        };\n\n        console.log('🎉 Amazing ranking data loaded!', transformedData.length, 'real champions');\n        console.log('📊 Data sources:', dataSources);\n        console.log('🏆 Top 5 champions:', transformedData.slice(0, 5).map(u => ({\n          name: u.name,\n          xp: u.totalXP,\n          quizzes: u.totalQuizzesTaken,\n          avg: u.averageScore,\n          source: u.dataSource\n        })));\n      } else {\n        console.log('⚠️ No user data available');\n        setRankingData([]);\n        setCurrentUserRank(null);\n        message.warning('No ranking data available. Please check your connection.');\n      }\n    } catch (error) {\n      console.error('💥 Error fetching ranking data:', error);\n      message.error('Failed to load the leaderboard. But champions never give up!');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Fetch full user data\n  const fetchFullUserData = async () => {\n    if (!user?.userId) {\n      console.log('❌ No userId available:', user);\n      return;\n    }\n\n    try {\n      console.log('🔍 Fetching full user data for userId:', user.userId);\n      const response = await getAllUsers();\n      console.log('📋 getAllUsers response:', response);\n\n      if (response.success) {\n        console.log('📊 Total users found:', response.data.length);\n        console.log('🔍 Looking for userId:', user.userId);\n        console.log('📝 First 5 user IDs:', response.data.slice(0, 5).map(u => ({ id: u._id, name: u.name })));\n\n        const userData = response.data.find(u => String(u._id) === String(user.userId));\n        if (userData) {\n          console.log('✅ Found full user data:', userData);\n          // Ensure profile picture properties are set\n          const userDataWithProfile = {\n            ...userData,\n            profilePicture: userData.profileImage || userData.profilePicture || '',\n            profileImage: userData.profileImage || userData.profilePicture || ''\n          };\n          setFullUserData(userDataWithProfile);\n        } else {\n          console.log('❌ User not found in users list');\n          console.log('🔍 Trying alternative search methods...');\n\n          // Try different ID formats\n          const userDataAlt = response.data.find(u =>\n            u._id === user.userId ||\n            u.id === user.userId ||\n            String(u._id).includes(user.userId) ||\n            String(user.userId).includes(u._id)\n          );\n\n          if (userDataAlt) {\n            console.log('✅ Found user with alternative method:', userDataAlt);\n            // Ensure profile picture properties are set\n            const userDataWithProfile = {\n              ...userDataAlt,\n              profilePicture: userDataAlt.profileImage || userDataAlt.profilePicture || '',\n              profileImage: userDataAlt.profileImage || userDataAlt.profilePicture || ''\n            };\n            setFullUserData(userDataWithProfile);\n          } else {\n            console.log('❌ User not found with any method');\n          }\n        }\n      } else {\n        console.log('❌ getAllUsers failed:', response);\n      }\n    } catch (error) {\n      console.error('❌ Error fetching user data:', error);\n    }\n  };\n\n  // Try to find user in ranking data as fallback\n  useEffect(() => {\n    if (!fullUserData && user?.userId && rankingData.length > 0) {\n      console.log('🔍 Trying to find user in ranking data...');\n      const userInRanking = rankingData.find(u => String(u._id) === String(user.userId));\n      if (userInRanking) {\n        console.log('✅ Found user in ranking data:', userInRanking);\n        // Ensure profile picture properties are set\n        const userDataWithProfile = {\n          ...userInRanking,\n          profilePicture: userInRanking.profileImage || userInRanking.profilePicture || '',\n          profileImage: userInRanking.profileImage || userInRanking.profilePicture || ''\n        };\n        setFullUserData(userDataWithProfile);\n      } else {\n        console.log('❌ User not found in ranking data either');\n      }\n    }\n  }, [rankingData, user, fullUserData]);\n\n  // Initialize component\n  useEffect(() => {\n    fetchRankingData();\n    fetchFullUserData(); // Fetch full user data\n\n    // Set random motivational quote\n    const randomQuote = motivationalQuotes[Math.floor(Math.random() * motivationalQuotes.length)];\n    setMotivationalQuote(randomQuote);\n\n    // Start animation sequence\n    const animationTimer = setInterval(() => {\n      setAnimationPhase(prev => (prev + 1) % 4);\n    }, 3000);\n\n    // Auto-refresh disabled to prevent interference with Find Me functionality\n    // const refreshTimer = setInterval(() => {\n    //   console.log('🔄 Auto-refreshing ranking data...');\n    //   fetchRankingData();\n    // }, 30000);\n\n    // Refresh when user comes back from quiz (window focus)\n    const handleWindowFocus = () => {\n      console.log('🎯 Window focused - refreshing ranking data...');\n      fetchRankingData(true); // Force refresh when returning from quiz\n    };\n\n    // Listen for real-time ranking updates from quiz completion\n    const handleRankingUpdate = (event) => {\n      console.log('🚀 Real-time ranking update triggered:', event.detail);\n\n      // Clear any cached data to ensure fresh fetch\n      localStorage.removeItem('rankingCache');\n      localStorage.removeItem('userRankingPosition');\n      localStorage.removeItem('leaderboardData');\n\n      // Immediate refresh after quiz completion with multiple attempts\n      const refreshWithRetry = async (attempts = 3) => {\n        for (let i = 0; i < attempts; i++) {\n          try {\n            console.log(`🔄 Refreshing ranking data (attempt ${i + 1}/${attempts})`);\n            await fetchRankingData(true); // Force refresh to bypass cache\n\n            // Verify the XP was updated by checking if user's XP matches the event data\n            if (event.detail?.newTotalXP && user) {\n              const updatedUser = rankingData.find(u => String(u._id) === String(user._id));\n              if (updatedUser && updatedUser.totalXP >= event.detail.newTotalXP) {\n                console.log('✅ XP update confirmed in ranking data');\n                break;\n              }\n            }\n\n            // Wait before retry\n            if (i < attempts - 1) {\n              await new Promise(resolve => setTimeout(resolve, 1500));\n            }\n          } catch (error) {\n            console.error(`❌ Ranking refresh attempt ${i + 1} failed:`, error);\n            if (i < attempts - 1) {\n              await new Promise(resolve => setTimeout(resolve, 1500));\n            }\n          }\n        }\n      };\n\n      // Start refresh with delay to ensure server processing\n      setTimeout(() => {\n        refreshWithRetry();\n      }, 1000);\n    };\n\n    window.addEventListener('focus', handleWindowFocus);\n    window.addEventListener('rankingUpdate', handleRankingUpdate);\n\n    return () => {\n      clearInterval(animationTimer);\n      // clearInterval(refreshTimer); // Commented out since refreshTimer is disabled\n      window.removeEventListener('focus', handleWindowFocus);\n      window.removeEventListener('rankingUpdate', handleRankingUpdate);\n    };\n  }, []);\n\n  // Auto-select user's current league when data loads\n  useEffect(() => {\n    if (user && leagueGroups && Object.keys(leagueGroups).length > 0 && !selectedLeague) {\n      // Find user's current league\n      for (const [leagueKey, leagueData] of Object.entries(leagueGroups)) {\n        const userInLeague = leagueData.users.find(u => String(u._id) === String(user._id));\n        if (userInLeague) {\n          console.log('🎯 Auto-selecting user league:', leagueKey);\n          setSelectedLeague(leagueKey);\n          setShowLeagueView(true);\n          setLeagueUsers(leagueData.users);\n          break;\n        }\n      }\n    }\n  }, [user, leagueGroups, selectedLeague]);\n\n  // Get top performers for special display (no filtering)\n  const topPerformers = rankingData.slice(0, 3);\n  const otherPerformers = rankingData.slice(3);\n\n\n\n  // Get user's current league information\n  const getUserLeagueInfo = () => {\n    if (!user?._id) return null;\n\n    // Check if user is in top 3 (podium)\n    const isInPodium = topPerformers.some(performer => String(performer._id) === String(user._id));\n    if (isInPodium) {\n      const podiumPosition = topPerformers.findIndex(performer => String(performer._id) === String(user._id)) + 1;\n      return {\n        type: 'podium',\n        position: podiumPosition,\n        league: 'Champion Podium',\n        leagueKey: 'podium'\n      };\n    }\n\n    // Find user's league\n    for (const [leagueKey, leagueData] of Object.entries(leagueGroups)) {\n      const userInLeague = leagueData.users?.find(u => String(u._id) === String(user._id));\n      if (userInLeague) {\n        const position = leagueData.users.findIndex(u => String(u._id) === String(user._id)) + 1;\n        return {\n          type: 'league',\n          position: position,\n          league: leagueData.title,\n          leagueKey: leagueKey,\n          totalUsers: leagueData.users.length\n        };\n      }\n    }\n\n    return null;\n  };\n\n  const userLeagueInfo = getUserLeagueInfo();\n\n  // Helper function to check if a user is the current user\n  const isCurrentUser = (userId) => {\n    return user && String(userId) === String(user._id);\n  };\n\n  // Helper function to check if user should be highlighted (only before they've been shown)\n  const shouldHighlightUser = (userId) => {\n    return isCurrentUser(userId) && !userHasBeenShown;\n  };\n\n  // Allow users to click anywhere to disable highlighting\n  const handlePageClick = () => {\n    if (!userHasBeenShown) {\n      setUserHasBeenShown(true);\n      console.log('👆 User clicked - highlighting disabled');\n    }\n  };\n\n  // Reset highlighting when user or league changes\n  useEffect(() => {\n    setUserHasBeenShown(false);\n    setAutoScrollCompleted(false); // Reset auto-scroll state\n  }, [user?._id, selectedLeague]);\n\n  // Auto-scroll to user position ONLY on first visit\n  useEffect(() => {\n    console.log('🔄 Auto-scroll check:', {\n      userId: user?._id,\n      autoScrollCompleted,\n      rankingDataLength: rankingData.length\n    });\n\n    // Only scroll if user exists, hasn't been scrolled yet, and we have data\n    if (!user?._id || autoScrollCompleted || rankingData.length === 0) {\n      console.log('❌ Auto-scroll skipped:', {\n        hasUser: !!user?._id,\n        completed: autoScrollCompleted,\n        hasData: rankingData.length > 0\n      });\n      return;\n    }\n\n    const scrollToUser = () => {\n      console.log('🎯 Starting auto-scroll for user:', user._id);\n\n      // First, try to find user in any ranking data\n      const userInRanking = rankingData.find(u => String(u._id) === String(user._id));\n      if (!userInRanking) {\n        console.log('❌ User not found in ranking data');\n        setAutoScrollCompleted(true); // Mark as completed even if not found\n        return;\n      }\n\n      console.log('✅ User found in ranking at position:', userInRanking.rank);\n\n      // Check if user is in top 3 (podium)\n      const isInPodium = userInRanking.rank <= 3;\n      console.log('🏆 Is user in podium?', isInPodium);\n\n      if (isInPodium) {\n        // Scroll to podium section\n        console.log('📍 Scrolling to podium section...');\n        const podiumSection = document.querySelector('[data-section=\"podium\"]');\n        console.log('🎪 Podium section found:', !!podiumSection);\n        if (podiumSection) {\n          setTimeout(() => {\n            podiumSection.scrollIntoView({\n              behavior: 'smooth',\n              block: 'center',\n              inline: 'nearest'\n            });\n            console.log('✅ Scrolled to podium');\n            // Mark as completed after scroll\n            setTimeout(() => {\n              setUserHasBeenShown(true);\n              setAutoScrollCompleted(true);\n              console.log('✅ Auto-scroll completed');\n            }, 1000);\n          }, 500);\n        } else {\n          setAutoScrollCompleted(true);\n        }\n      } else {\n        // Look for user element in the ranking list\n        console.log('📍 Looking for user element with ID:', user._id);\n        const userElement = document.querySelector(`[data-user-id=\"${user._id}\"]`);\n        console.log('🎯 User element found:', !!userElement);\n        if (userElement) {\n          setTimeout(() => {\n            userElement.scrollIntoView({\n              behavior: 'smooth',\n              block: 'center',\n              inline: 'nearest'\n            });\n            console.log('✅ Scrolled to user position');\n            // Mark as completed after scroll\n            setTimeout(() => {\n              setUserHasBeenShown(true);\n              setAutoScrollCompleted(true);\n              console.log('✅ Auto-scroll completed');\n            }, 1000);\n          }, 500);\n        } else {\n          console.log('❌ User element not found in DOM');\n          setAutoScrollCompleted(true);\n        }\n      }\n    };\n\n    // Delay to ensure DOM is ready, but not too long\n    const timer = setTimeout(scrollToUser, 2000);\n    return () => clearTimeout(timer);\n  }, [user?._id, rankingData, autoScrollCompleted]);\n\n  // Get subscription status badge - simplified to only ACTIVATED and EXPIRED\n  const getSubscriptionBadge = (subscriptionStatus, subscriptionEndDate, subscriptionPlan, activePlanTitle, userIndex = 0) => {\n    const now = new Date();\n    const endDate = subscriptionEndDate ? new Date(subscriptionEndDate) : null;\n\n    console.log('Subscription Debug:', {\n      subscriptionStatus,\n      subscriptionEndDate,\n      subscriptionPlan,\n      activePlanTitle,\n      endDate,\n      now,\n      isActive: endDate && endDate > now,\n      userIndex\n    });\n\n    // Check if user has an active subscription\n    if (subscriptionStatus === 'active' || subscriptionStatus === 'premium') {\n      // Check if subscription is still valid (not expired)\n      if (!endDate || endDate > now) {\n        // User has active plan - show ACTIVATED\n        return {\n          text: 'ACTIVATED',\n          color: '#10B981', // Green\n          bgColor: 'rgba(16, 185, 129, 0.2)',\n          borderColor: '#10B981'\n        };\n      } else {\n        // Subscription status is active but end date has passed - show EXPIRED\n        return {\n          text: 'EXPIRED',\n          color: '#EF4444', // Red\n          bgColor: 'rgba(239, 68, 68, 0.2)',\n          borderColor: '#EF4444'\n        };\n      }\n    } else {\n      // No active subscription - show EXPIRED\n      return {\n        text: 'EXPIRED',\n        color: '#EF4444', // Red\n        bgColor: 'rgba(239, 68, 68, 0.2)',\n        borderColor: '#EF4444'\n      };\n    }\n  };\n\n  // Skeleton loading component\n  const RankingSkeleton = () => (\n    <div className=\"min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900\">\n      <div className=\"container mx-auto px-4 py-8\">\n        {/* Header Skeleton */}\n        <div className=\"text-center mb-12\">\n          <div className=\"h-12 bg-white/10 rounded-lg w-96 mx-auto mb-4 animate-pulse\"></div>\n          <div className=\"h-6 bg-white/5 rounded w-64 mx-auto animate-pulse\"></div>\n        </div>\n\n        {/* Podium Skeleton */}\n        <div className=\"flex justify-center items-end mb-16 space-x-8\">\n          {[2, 1, 3].map((position) => (\n            <div key={position} className={`text-center ${position === 1 ? 'order-2' : position === 2 ? 'order-1' : 'order-3'}`}>\n              <div className={`w-20 h-20 bg-gradient-to-br from-yellow-400/20 to-yellow-600/20 rounded-full mx-auto mb-4 animate-pulse`}></div>\n              <div className=\"h-4 bg-white/10 rounded w-16 mx-auto mb-2 animate-pulse\"></div>\n              <div className=\"h-3 bg-white/5 rounded w-12 mx-auto animate-pulse\"></div>\n            </div>\n          ))}\n        </div>\n\n        {/* List Skeleton */}\n        <div className=\"space-y-4 max-w-4xl mx-auto\">\n          {[...Array(8)].map((_, i) => (\n            <div key={i} className=\"bg-white/5 rounded-xl p-4 animate-pulse\">\n              <div className=\"flex items-center space-x-4\">\n                <div className=\"w-12 h-12 bg-white/10 rounded-full\"></div>\n                <div className=\"flex-1\">\n                  <div className=\"h-4 bg-white/10 rounded w-32 mb-2\"></div>\n                  <div className=\"h-3 bg-white/5 rounded w-24\"></div>\n                </div>\n                <div className=\"h-6 bg-white/10 rounded w-16\"></div>\n              </div>\n            </div>\n          ))}\n        </div>\n      </div>\n    </div>\n  );\n\n  // Show skeleton only on initial load\n  if (loading && rankingData.length === 0) {\n    return <RankingSkeleton />;\n  }\n\n  return (\n    <>\n      <style>{`\n        /* Dark background for better color visibility */\n        body {\n          background: linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #334155 100%) !important;\n          min-height: 100vh;\n          color: #ffffff !important;\n        }\n\n        .ranking-page-container {\n          background: linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #334155 100%);\n          min-height: 100vh;\n          color: #ffffff;\n        }\n\n        /* Fix black text visibility - Enhanced */\n        .ranking-page-container * {\n          color: inherit;\n        }\n\n        .ranking-page-container .text-black,\n        .ranking-page-container .text-gray-900,\n        .ranking-page-container h1,\n        .ranking-page-container h2,\n        .ranking-page-container h3,\n        .ranking-page-container h4,\n        .ranking-page-container h5,\n        .ranking-page-container h6,\n        .ranking-page-container p,\n        .ranking-page-container span,\n        .ranking-page-container div {\n          color: #ffffff !important;\n        }\n\n        .ranking-page-container [style*=\"color: #000000\"],\n        .ranking-page-container [style*=\"color: black\"],\n        .ranking-page-container [style*=\"color:#000000\"],\n        .ranking-page-container [style*=\"color:black\"],\n        .ranking-page-container [style*=\"color: #1f2937\"],\n        .ranking-page-container [style*=\"color:#1f2937\"] {\n          color: #ffffff !important;\n        }\n\n        /* Force white text for names and content */\n        .ranking-page-container .font-bold,\n        .ranking-page-container .font-black,\n        .ranking-page-container .font-semibold,\n        .ranking-page-container .font-medium {\n          color: #ffffff !important;\n        }\n\n\n        /* Enhanced hover effects for ranking cards */\n        .ranking-card {\n          transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\n        }\n\n        .ranking-card:hover {\n          transform: translateY(-2px) scale(1.01);\n        }\n\n        /* Smooth animations for league badges */\n        .league-badge {\n          transition: all 0.2s ease-in-out;\n        }\n\n        .league-badge:hover {\n          transform: scale(1.05);\n        }\n\n        /* Gradient text animations */\n        @keyframes gradientShift {\n          0%, 100% { background-position: 0% 50%; }\n          50% { background-position: 100% 50%; }\n        }\n\n        .animated-gradient {\n          background-size: 200% 200%;\n          animation: gradientShift 3s ease infinite;\n        }\n\n        /* League-specific animations */\n        .mythic-aura {\n          animation: mythicPulse 2s ease-in-out infinite alternate;\n        }\n\n        .legendary-sparkle {\n          animation: legendarySparkle 3s ease-in-out infinite;\n        }\n\n        .diamond-shine {\n          animation: diamondShine 2.5s ease-in-out infinite;\n        }\n\n        .platinum-gleam {\n          animation: platinumGleam 3s ease-in-out infinite;\n        }\n\n        .gold-glow {\n          animation: goldGlow 2s ease-in-out infinite alternate;\n        }\n\n        .silver-shimmer {\n          animation: silverShimmer 2.5s ease-in-out infinite;\n        }\n\n        .bronze-warm {\n          animation: bronzeWarm 3s ease-in-out infinite;\n        }\n\n        .rookie-glow {\n          animation: rookieGlow 2s ease-in-out infinite alternate;\n        }\n\n        @keyframes mythicPulse {\n          0% { box-shadow: 0 0 20px rgba(255, 20, 147, 0.5); }\n          100% { box-shadow: 0 0 40px rgba(255, 20, 147, 0.8), 0 0 60px rgba(138, 43, 226, 0.6); }\n        }\n\n        @keyframes legendarySparkle {\n          0%, 100% { filter: brightness(1) hue-rotate(0deg); }\n          50% { filter: brightness(1.2) hue-rotate(10deg); }\n        }\n\n        @keyframes diamondShine {\n          0%, 100% { filter: brightness(1) saturate(1); }\n          50% { filter: brightness(1.3) saturate(1.2); }\n        }\n\n        @keyframes platinumGleam {\n          0%, 100% { filter: brightness(1); }\n          50% { filter: brightness(1.1) contrast(1.1); }\n        }\n\n        @keyframes goldGlow {\n          0% { filter: brightness(1) drop-shadow(0 0 10px #FFD700); }\n          100% { filter: brightness(1.2) drop-shadow(0 0 20px #FFD700); }\n        }\n\n        @keyframes silverShimmer {\n          0%, 100% { filter: brightness(1); }\n          50% { filter: brightness(1.15) contrast(1.05); }\n        }\n\n        @keyframes bronzeWarm {\n          0%, 100% { filter: brightness(1) hue-rotate(0deg); }\n          50% { filter: brightness(1.1) hue-rotate(5deg); }\n        }\n\n        @keyframes rookieGlow {\n          0% { filter: brightness(1) drop-shadow(0 0 5px #32CD32); }\n          100% { filter: brightness(1.15) drop-shadow(0 0 15px #32CD32); }\n        }\n\n        /* Horizontal podium animations */\n        .podium-animation {\n          animation: podiumFloat 4s ease-in-out infinite;\n        }\n\n        @keyframes podiumFloat {\n          0%, 100% { transform: translateY(0px); }\n          50% { transform: translateY(-5px); }\n        }\n      `}</style>\n      <div className=\"ranking-page-container ranking-page min-h-screen bg-gradient-to-br from-slate-900 via-gray-900 to-black relative overflow-hidden\" onClick={handlePageClick}>\n\n      {/* Highlighting Notification */}\n      {!userHasBeenShown && user && (\n        <motion.div\n          initial={{ opacity: 0, y: -20 }}\n          animate={{ opacity: 1, y: 0 }}\n          exit={{ opacity: 0, y: -20 }}\n          className=\"fixed top-4 left-1/2 transform -translate-x-1/2 z-50 bg-yellow-500/90 backdrop-blur-sm text-black px-4 py-2 rounded-lg shadow-lg text-sm font-medium\"\n        >\n          🎯 Finding your position... Click anywhere to stop highlighting\n        </motion.div>\n      )}\n\n      {/* Animated Background Elements - Darker Theme */}\n      <div className=\"absolute inset-0 overflow-hidden\">\n        <div className=\"absolute -top-40 -right-40 w-80 h-80 bg-purple-600 rounded-full mix-blend-multiply filter blur-xl opacity-15 animate-blob\"></div>\n        <div className=\"absolute -bottom-40 -left-40 w-80 h-80 bg-blue-600 rounded-full mix-blend-multiply filter blur-xl opacity-15 animate-blob animation-delay-2000\"></div>\n        <div className=\"absolute top-40 left-40 w-80 h-80 bg-indigo-600 rounded-full mix-blend-multiply filter blur-xl opacity-15 animate-blob animation-delay-4000\"></div>\n        <div className=\"absolute top-1/2 right-1/3 w-60 h-60 bg-cyan-600 rounded-full mix-blend-multiply filter blur-xl opacity-10 animate-blob animation-delay-6000\"></div>\n      </div>\n\n      {/* Floating Particles */}\n      <div className=\"absolute inset-0 overflow-hidden pointer-events-none\">\n        {[...Array(20)].map((_, i) => (\n          <motion.div\n            key={i}\n            className=\"absolute w-2 h-2 bg-white rounded-full opacity-20\"\n            animate={{\n              y: [0, -100, 0],\n              x: [0, Math.random() * 100 - 50, 0],\n              opacity: [0.2, 0.8, 0.2]\n            }}\n            transition={{\n              duration: 3 + Math.random() * 2,\n              repeat: Infinity,\n              delay: Math.random() * 2\n            }}\n            style={{\n              left: `${Math.random() * 100}%`,\n              top: `${Math.random() * 100}%`\n            }}\n          />\n        ))}\n      </div>\n\n      <div className=\"relative z-10\">\n        {/* TOP CONTROLS */}\n        <motion.div\n          initial={{ opacity: 0, y: -20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8 }}\n          className=\"px-2 sm:px-4 md:px-6 lg:px-8 py-2 sm:py-4 md:py-6 lg:py-8\"\n          style={{\n            padding: window.innerWidth <= 768 ? '8px' : window.innerWidth <= 1024 ? '16px' : '32px'\n          }}\n        >\n          <div className=\"max-w-7xl mx-auto\">\n            <div className=\"bg-white/5 backdrop-blur-lg rounded-xl sm:rounded-2xl md:rounded-3xl p-3 sm:p-4 md:p-6 lg:p-8 border border-white/10\">\n              <div className=\"flex flex-col sm:flex-row gap-2 sm:gap-3 md:gap-4 lg:gap-6 items-center justify-center\">\n\n                {/* Hub Button */}\n                <motion.button\n                  whileHover={{ scale: 1.05 }}\n                  whileTap={{ scale: 0.95 }}\n                  onClick={() => navigate('/user/hub')}\n                  className=\"flex items-center gap-2 md:gap-3 px-4 md:px-6 py-3 md:py-4 bg-gradient-to-r from-blue-600 to-indigo-600 text-white rounded-xl font-bold shadow-lg hover:shadow-xl transition-all duration-300 w-full sm:w-auto\"\n                  style={{\n                    fontSize: window.innerWidth < 768 ? '1rem' : '1.1rem'\n                  }}\n                >\n                  <TbHome className=\"w-5 h-5 md:w-6 md:h-6\" />\n                  <span>Hub</span>\n                </motion.button>\n\n                {/* User League Info Display */}\n                {userLeagueInfo && (\n                  <motion.div\n                    initial={{ opacity: 0, y: 20 }}\n                    animate={{ opacity: 1, y: 0 }}\n                    className=\"flex items-center gap-2 md:gap-3 px-4 md:px-6 py-3 md:py-4 bg-gradient-to-r from-blue-500 to-purple-600 text-white rounded-xl font-bold shadow-lg\"\n                    style={{\n                      background: userLeagueInfo.type === 'podium'\n                        ? 'linear-gradient(135deg, #FFD700, #FFA500)'\n                        : 'linear-gradient(135deg, #3B82F6, #8B5CF6)',\n                      color: userLeagueInfo.type === 'podium' ? '#1F2937' : '#FFFFFF',\n                      boxShadow: '0 4px 15px rgba(59, 130, 246, 0.3)',\n                      fontSize: window.innerWidth < 768 ? '0.9rem' : '1rem'\n                    }}\n                  >\n                    <TbTrophy className=\"w-5 h-5 md:w-6 md:h-6\" />\n                    <span>\n                      {userLeagueInfo.type === 'podium'\n                        ? `🏆 Podium #${userLeagueInfo.position}`\n                        : `${userLeagueInfo.league} #${userLeagueInfo.position}`}\n                    </span>\n                  </motion.div>\n                )}\n\n                {/* User Profile Window */}\n                {fullUserData && (\n                  <motion.div\n                    initial={{ opacity: 0, y: 20 }}\n                    animate={{ opacity: 1, y: 0 }}\n                    className=\"bg-gradient-to-br from-blue-600/20 via-purple-600/20 to-indigo-600/20 backdrop-blur-lg rounded-2xl p-4 border border-blue-400/30 shadow-2xl max-w-sm\"\n                  >\n                    <div className=\"flex items-center gap-4\">\n                      {/* Profile Picture with Online Status */}\n                      <div className=\"flex-shrink-0 relative\">\n                        <ProfilePicture\n                          user={fullUserData}\n                          size=\"xl\"\n                          showOnlineStatus={false}\n                          style={{\n                            border: '3px solid #facc15',\n                            boxShadow: '0 10px 25px rgba(0,0,0,0.15)'\n                          }}\n                        />\n                        {/* Only show online dot if user is actually online */}\n                        {fullUserData.isOnline && (\n                          <div\n                            style={{\n                              position: 'absolute',\n                              bottom: '4px',\n                              right: '4px',\n                              width: '16px',\n                              height: '16px',\n                              backgroundColor: '#22c55e',\n                              borderRadius: '50%',\n                              border: '3px solid #ffffff',\n                              boxShadow: '0 2px 8px rgba(34, 197, 94, 0.6)',\n                              zIndex: 10\n                            }}\n                            title=\"Online\"\n                          />\n                        )}\n                      </div>\n\n                      {/* User Details */}\n                      <div className=\"flex-grow\">\n                        <h3 className=\"text-lg font-bold text-white mb-2 truncate\">\n                          {fullUserData.name || fullUserData.username || 'User'}\n                        </h3>\n\n                        {/* Stats Grid */}\n                        <div className=\"grid grid-cols-2 gap-2 text-sm\">\n                          <div className=\"bg-green-500/20 rounded-lg p-2 text-center\">\n                            <div className=\"text-green-300 text-xs\">Total XP</div>\n                            <div className=\"text-white font-bold\">\n                              {(() => {\n                                // Try multiple XP field names for migrated users\n                                const xp = fullUserData.totalXP || fullUserData.xp || fullUserData.points || fullUserData.totalPoints || 0;\n                                return xp.toLocaleString();\n                              })()}\n                            </div>\n                          </div>\n\n                          <div className=\"bg-purple-500/20 rounded-lg p-2 text-center\">\n                            <div className=\"text-purple-300 text-xs\">Rank</div>\n                            <div className=\"text-white font-bold\">\n                              {(() => {\n                                // Try to find user in ranking data\n                                const userInRanking = rankingData.find(u => String(u._id) === String(fullUserData._id));\n                                return userInRanking ? `#${userInRanking.rank}` : (currentUserRank ? `#${currentUserRank}` : 'N/A');\n                              })()}\n                            </div>\n                          </div>\n\n                          <div className=\"bg-blue-500/20 rounded-lg p-2 text-center\">\n                            <div className=\"text-blue-300 text-xs\">League</div>\n                            <div className=\"text-white font-bold text-xs\">\n                              {(() => {\n                                // Find user's league with icon - try multiple XP sources\n                                const userXP = fullUserData.totalXP || fullUserData.xp || fullUserData.points || fullUserData.totalPoints || 0;\n                                for (const [leagueKey, leagueData] of Object.entries(leagueGroups)) {\n                                  const userInLeague = leagueData.users?.find(u => String(u._id) === String(fullUserData._id));\n                                  if (userInLeague) {\n                                    const leagueInfo = getUserLeague(userXP);\n                                    return `${leagueInfo.leagueIcon} ${leagueKey.toUpperCase()}`;\n                                  }\n                                }\n                                // Fallback: calculate league from XP even if not in league data\n                                if (userXP > 0) {\n                                  const leagueInfo = getUserLeague(userXP);\n                                  return `${leagueInfo.leagueIcon} ${leagueInfo.league.toUpperCase()}`;\n                                }\n                                return '🔰 Unranked';\n                              })()}\n                            </div>\n                          </div>\n\n                          <div className=\"bg-orange-500/20 rounded-lg p-2 text-center\">\n                            <div className=\"text-orange-300 text-xs\">Quizzes</div>\n                            <div className=\"text-white font-bold\">\n                              {(() => {\n                                // Try multiple quiz count field names\n                                return fullUserData.quizzesCompleted || fullUserData.totalQuizzesTaken || fullUserData.quizzesTaken || fullUserData.totalQuizzes || 0;\n                              })()}\n                            </div>\n                          </div>\n                        </div>\n\n                        {/* Additional Stats Row */}\n                        <div className=\"grid grid-cols-3 gap-2 mt-2 text-xs\">\n                          <div className=\"bg-yellow-500/20 rounded-lg p-1.5 text-center\">\n                            <div className=\"text-yellow-300 text-xs\">Level</div>\n                            <div className=\"text-white font-bold\">\n                              {fullUserData.currentLevel || fullUserData.level || 1}\n                            </div>\n                          </div>\n\n                          <div className=\"bg-red-500/20 rounded-lg p-1.5 text-center\">\n                            <div className=\"text-red-300 text-xs\">Streak</div>\n                            <div className=\"text-white font-bold\">\n                              {fullUserData.currentStreak || fullUserData.streak || 0}\n                            </div>\n                          </div>\n\n                          <div className=\"bg-cyan-500/20 rounded-lg p-1.5 text-center\">\n                            <div className=\"text-cyan-300 text-xs\">Avg Score</div>\n                            <div className=\"text-white font-bold\">\n                              {(() => {\n                                const avgScore = fullUserData.averageScore || fullUserData.avgScore || 0;\n                                return Math.round(avgScore);\n                              })()}%\n                            </div>\n                          </div>\n                        </div>\n\n                        {/* League Position */}\n                        {(() => {\n                          // Find user's position in their league\n                          for (const [leagueKey, leagueData] of Object.entries(leagueGroups)) {\n                            const userIndex = leagueData.users?.findIndex(u => String(u._id) === String(fullUserData._id));\n                            if (userIndex !== -1 && userIndex !== undefined) {\n                              return (\n                                <div className=\"mt-2 text-center\">\n                                  <div className=\"bg-gradient-to-r from-yellow-400/20 to-orange-400/20 rounded-lg p-1.5\">\n                                    <div className=\"text-yellow-300 text-xs\">League Position</div>\n                                    <div className=\"text-white font-bold text-sm\">\n                                      #{userIndex + 1} of {leagueData.users.length}\n                                    </div>\n                                  </div>\n                                </div>\n                              );\n                            }\n                          }\n                          return null;\n                        })()}\n                      </div>\n                    </div>\n                  </motion.div>\n                )}\n\n\n\n\n\n\n\n\n\n\n\n                {/* League Selection Section */}\n                <div className=\"flex flex-col items-center gap-4 bg-white/5 backdrop-blur-lg rounded-2xl p-6 border border-white/10 max-w-4xl mx-auto\">\n                  {/* LEAGUES Title */}\n                  <motion.h3\n                    className=\"text-2xl md:text-3xl font-black mb-2\"\n                    style={{\n                      background: 'linear-gradient(45deg, #FFD700, #FFA500, #FF6B35)',\n                      WebkitBackgroundClip: 'text',\n                      WebkitTextFillColor: 'transparent',\n                      textShadow: '2px 2px 4px rgba(0,0,0,0.8)',\n                      filter: 'drop-shadow(0 0 10px #FFD700)'\n                    }}\n                    animate={{ scale: [1, 1.02, 1] }}\n                    transition={{ duration: 3, repeat: Infinity }}\n                  >\n                    🏆 LEAGUES 🏆\n                  </motion.h3>\n\n                  {/* League Icons */}\n                  <div className=\"flex flex-wrap items-center justify-center gap-3 md:gap-4\">\n                    {getOrderedLeagues().map((leagueKey) => {\n                      const league = leagueSystem[leagueKey];\n                      const isSelected = selectedLeague === leagueKey;\n                      const userCount = leagueGroups[leagueKey]?.users.length || 0;\n\n                      return (\n                        <motion.div\n                          key={leagueKey}\n                          className=\"flex flex-col items-center gap-2\"\n                          whileHover={{ scale: 1.05 }}\n                        >\n                          <motion.button\n                            whileHover={{ scale: 1.1, y: -3 }}\n                            whileTap={{ scale: 0.95 }}\n                            onClick={() => handleLeagueSelect(leagueKey)}\n                            className={`relative flex items-center justify-center w-16 h-16 md:w-20 md:h-20 rounded-2xl transition-all duration-300 ${\n                              isSelected\n                                ? 'ring-4 ring-yellow-400 ring-opacity-100 shadow-2xl'\n                                : 'hover:ring-2 hover:ring-white/30'\n                            }`}\n                            style={{\n                              background: isSelected\n                                ? `linear-gradient(135deg, ${league.borderColor}80, ${league.textColor}50, ${league.borderColor}80)`\n                                : `linear-gradient(135deg, ${league.borderColor}60, ${league.textColor}30)`,\n                              border: `3px solid ${isSelected ? '#FFD700' : league.borderColor + '80'}`,\n                              boxShadow: isSelected\n                                ? `0 0 30px ${league.shadowColor}80, 0 0 60px #FFD70080, 0 6px 30px ${league.shadowColor}80`\n                                : `0 4px 15px ${league.shadowColor}40`,\n                              transform: isSelected ? 'scale(1.1)' : 'scale(1)',\n                              filter: isSelected ? 'brightness(1.3) saturate(1.2)' : 'brightness(1)'\n                            }}\n                            animate={isSelected ? {\n                              boxShadow: [\n                                `0 0 30px ${league.shadowColor}80, 0 0 60px #FFD70080`,\n                                `0 0 40px ${league.shadowColor}100, 0 0 80px #FFD700A0`,\n                                `0 0 30px ${league.shadowColor}80, 0 0 60px #FFD70080`\n                              ],\n                              scale: [1.1, 1.15, 1.1]\n                            } : {}}\n                            transition={{\n                              duration: 2,\n                              repeat: isSelected ? Infinity : 0,\n                              ease: \"easeInOut\"\n                            }}\n                            title={`Click to view ${league.title} League (${userCount} users)`}\n                          >\n                            <span className=\"text-3xl md:text-4xl\">{league.leagueIcon}</span>\n                            {isSelected && (\n                              <motion.div\n                                initial={{ scale: 0, rotate: -360, opacity: 0 }}\n                                animate={{\n                                  scale: [1, 1.3, 1],\n                                  rotate: [0, 360, 720],\n                                  opacity: 1,\n                                  boxShadow: [\n                                    '0 0 15px rgba(255, 215, 0, 1), 0 0 30px rgba(255, 215, 0, 0.8)',\n                                    '0 0 25px rgba(255, 215, 0, 1), 0 0 50px rgba(255, 215, 0, 1)',\n                                    '0 0 15px rgba(255, 215, 0, 1), 0 0 30px rgba(255, 215, 0, 0.8)'\n                                  ]\n                                }}\n                                transition={{\n                                  scale: { duration: 2, repeat: Infinity, ease: \"easeInOut\" },\n                                  rotate: { duration: 4, repeat: Infinity, ease: \"linear\" },\n                                  boxShadow: { duration: 1.5, repeat: Infinity, ease: \"easeInOut\" },\n                                  opacity: { duration: 0.3 }\n                                }}\n                                className=\"absolute -top-3 -right-3 w-8 h-8 bg-gradient-to-r from-yellow-400 via-orange-400 to-yellow-500 rounded-full flex items-center justify-center border-3 border-white shadow-lg\"\n                                style={{\n                                  background: 'linear-gradient(45deg, #FFD700, #FFA500, #FFD700)',\n                                  border: '3px solid white',\n                                  zIndex: 10\n                                }}\n                              >\n                                <motion.span\n                                  className=\"text-sm font-black text-gray-900\"\n                                  animate={{\n                                    scale: [1, 1.2, 1],\n                                    rotate: [0, -10, 10, 0]\n                                  }}\n                                  transition={{\n                                    duration: 1,\n                                    repeat: Infinity,\n                                    ease: \"easeInOut\"\n                                  }}\n                                >\n                                  ✓\n                                </motion.span>\n                              </motion.div>\n                            )}\n                            <div\n                              className=\"absolute -bottom-2 -right-2 w-6 h-6 rounded-full flex items-center justify-center text-xs font-bold border-2 border-white\"\n                              style={{\n                                background: league.borderColor,\n                                color: '#FFFFFF',\n                                fontSize: '11px'\n                              }}\n                            >\n                              {userCount}\n                            </div>\n                          </motion.button>\n\n                          {/* League Name */}\n                          <motion.div\n                            className=\"text-center\"\n                            whileHover={{ scale: 1.05 }}\n                          >\n                            <div\n                              className=\"text-xs md:text-sm font-bold px-2 py-1 rounded-lg\"\n                              style={{\n                                color: league.nameColor,\n                                textShadow: `1px 1px 2px ${league.shadowColor}`,\n                                background: `${league.borderColor}20`,\n                                border: `1px solid ${league.borderColor}40`\n                              }}\n                            >\n                              {league.title}\n                            </div>\n                          </motion.div>\n                        </motion.div>\n                      );\n                    })}\n                  </div>\n\n                  <p className=\"text-white/70 text-sm text-center mt-2\">\n                    Click any league to view its members and scroll to their section\n                  </p>\n                </div>\n\n\n\n                {/* Refresh Button */}\n                <motion.button\n                  whileHover={{ scale: 1.05, rotate: 180 }}\n                  whileTap={{ scale: 0.95 }}\n                  onClick={fetchRankingData}\n                  disabled={loading}\n                  className=\"flex items-center gap-3 px-6 py-3 md:py-4 bg-gradient-to-r from-purple-600 to-pink-600 text-white rounded-xl font-medium shadow-lg hover:shadow-xl transition-all duration-300 disabled:opacity-50 w-full sm:w-auto\"\n                  style={{\n                    fontSize: window.innerWidth < 768 ? '1rem' : '1.1rem'\n                  }}\n                >\n                  <TbRefresh className={`w-5 h-5 md:w-6 md:h-6 ${loading ? 'animate-spin' : ''}`} />\n                  <span>Refresh</span>\n                </motion.button>\n              </div>\n            </div>\n          </div>\n        </motion.div>\n\n        {/* Admin Notice - DISABLED FOR TESTING */}\n        {false && (user?.role === 'admin' || user?.isAdmin) && (\n          <motion.div\n            initial={{ opacity: 0, y: -20 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.6 }}\n            className=\"px-3 sm:px-4 md:px-6 lg:px-8 mb-6\"\n          >\n            <div className=\"max-w-7xl mx-auto\">\n              <div className=\"bg-gradient-to-r from-purple-500/20 to-blue-500/20 backdrop-blur-lg rounded-xl p-4 border border-purple-300/30\">\n                <div className=\"flex items-center gap-3\">\n                  <div className=\"w-8 h-8 bg-purple-500 rounded-full flex items-center justify-center\">\n                    <span className=\"text-white font-bold text-sm\">👑</span>\n                  </div>\n                  <div>\n                    <h3 className=\"font-bold text-white\">Admin View</h3>\n                    <p className=\"text-sm text-white/80\">\n                      You're viewing as an admin. Admin accounts are excluded from student rankings.\n                    </p>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </motion.div>\n        )}\n\n        {/* SPECTACULAR RANKING HEADER */}\n        <motion.div\n          initial={{ opacity: 0, y: -50 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ duration: 1, ease: \"easeOut\" }}\n          className=\"relative overflow-hidden mb-8\"\n        >\n          {/* Header Background with Modern Gradient */}\n          <div className=\"bg-gradient-to-br from-blue-600 via-indigo-500 via-purple-500 via-cyan-500 to-teal-500 relative\">\n            <div className=\"absolute inset-0 bg-gradient-to-t from-black/40 via-black/20 to-transparent\"></div>\n            <div className=\"absolute inset-0 bg-gradient-to-r from-transparent via-white/5 to-transparent\"></div>\n\n            {/* Animated Header Content */}\n            <div className=\"relative z-10 px-3 sm:px-4 md:px-6 lg:px-8 py-6 sm:py-8 md:py-12 lg:py-20\">\n              <div className=\"max-w-7xl mx-auto text-center\">\n\n                {/* Main Title with Epic Animation */}\n                <motion.div\n                  animate={{\n                    scale: [1, 1.02, 1],\n                    rotateY: [0, 5, 0]\n                  }}\n                  transition={{\n                    duration: 4,\n                    repeat: Infinity,\n                    ease: \"easeInOut\"\n                  }}\n                  className=\"mb-6 md:mb-8\"\n                >\n                  <h1 className=\"text-2xl sm:text-3xl md:text-4xl lg:text-5xl xl:text-6xl font-black mb-2 md:mb-4 tracking-tight\">\n                    <motion.span\n                      animate={{\n                        backgroundPosition: ['0% 50%', '100% 50%', '0% 50%']\n                      }}\n                      transition={{\n                        duration: 4,\n                        repeat: Infinity,\n                        ease: \"linear\"\n                      }}\n                      className=\"bg-gradient-to-r from-yellow-300 via-pink-300 via-cyan-300 via-purple-300 to-yellow-300 bg-clip-text text-transparent bg-400%\"\n                      style={{\n                        backgroundSize: '400% 400%',\n                        WebkitBackgroundClip: 'text',\n                        WebkitTextFillColor: 'transparent',\n                        filter: 'drop-shadow(2px 2px 4px rgba(0,0,0,0.8))'\n                      }}\n                    >\n                      HALL OF\n                    </motion.span>\n                    <br />\n                    <motion.span\n                      animate={{\n                        textShadow: [\n                          '0 0 20px rgba(255,215,0,0.8), 0 0 40px rgba(255,215,0,0.6)',\n                          '0 0 30px rgba(255,215,0,1), 0 0 60px rgba(255,215,0,0.8)',\n                          '0 0 20px rgba(255,215,0,0.8), 0 0 40px rgba(255,215,0,0.6)'\n                        ]\n                      }}\n                      transition={{\n                        duration: 2.5,\n                        repeat: Infinity,\n                        ease: \"easeInOut\"\n                      }}\n                      style={{\n                        color: '#FFD700',\n                        fontWeight: '900',\n                        textShadow: '3px 3px 6px rgba(0,0,0,0.9)'\n                      }}\n                    >\n                      CHAMPIONS\n                    </motion.span>\n                  </h1>\n                </motion.div>\n\n                {/* Epic Subtitle */}\n                <motion.p\n                  initial={{ opacity: 0, y: 20 }}\n                  animate={{ opacity: 1, y: 0 }}\n                  transition={{ delay: 0.5, duration: 0.8 }}\n                  className=\"text-sm sm:text-base md:text-lg lg:text-xl font-semibold mb-4 md:mb-6 max-w-4xl mx-auto leading-relaxed px-2\"\n                  style={{\n                    color: '#F3F4F6',\n                    textShadow: '2px 2px 4px rgba(0,0,0,0.8)',\n                    background: 'linear-gradient(45deg, #F3F4F6, #E5E7EB)',\n                    WebkitBackgroundClip: 'text',\n                    WebkitTextFillColor: 'transparent'\n                  }}\n                >\n                  ✨ Where legends are born and greatness is celebrated ✨\n                </motion.p>\n\n                {/* Motivational Quote */}\n                <motion.div\n                  initial={{ opacity: 0, scale: 0.9 }}\n                  animate={{ opacity: 1, scale: 1 }}\n                  transition={{ delay: 0.8, duration: 0.8 }}\n                  className=\"mb-6 md:mb-8\"\n                >\n                  <p className=\"text-sm sm:text-base md:text-lg font-medium text-yellow-200 bg-black/20 backdrop-blur-sm rounded-xl px-4 py-3 max-w-3xl mx-auto border border-yellow-400/30\"\n                     style={{\n                       textShadow: '2px 2px 4px rgba(0,0,0,0.8)',\n                       fontStyle: 'italic'\n                     }}>\n                    {motivationalQuote}\n                  </p>\n                </motion.div>\n\n                {/* Enhanced Stats Grid */}\n                <motion.div\n                  initial={{ opacity: 0, y: 30 }}\n                  animate={{ opacity: 1, y: 0 }}\n                  transition={{ delay: 1, duration: 0.8 }}\n                  className=\"grid grid-cols-2 sm:grid-cols-4 gap-3 sm:gap-4 md:gap-6 max-w-4xl mx-auto\"\n                >\n                  {[\n                    {\n                      icon: TbUsers,\n                      value: rankingData.length,\n                      label: 'Champions',\n                      bgGradient: 'from-blue-600/20 via-indigo-600/20 to-purple-600/20',\n                      iconColor: '#60A5FA',\n                      borderColor: '#3B82F6'\n                    },\n                    {\n                      icon: TbTrophy,\n                      value: topPerformers.length,\n                      label: 'Top Performers',\n                      bgGradient: 'from-yellow-600/20 via-orange-600/20 to-red-600/20',\n                      iconColor: '#FBBF24',\n                      borderColor: '#F59E0B'\n                    },\n                    {\n                      icon: TbFlame,\n                      value: rankingData.filter(u => u.currentStreak > 0).length,\n                      label: 'Active Streaks',\n                      bgGradient: 'from-red-600/20 via-pink-600/20 to-rose-600/20',\n                      iconColor: '#F87171',\n                      borderColor: '#EF4444'\n                    },\n                    {\n                      icon: TbStar,\n                      value: rankingData.reduce((sum, u) => sum + (u.totalXP || 0), 0).toLocaleString(),\n                      label: 'Total XP',\n                      bgGradient: 'from-green-600/20 via-emerald-600/20 to-teal-600/20',\n                      iconColor: '#34D399',\n                      borderColor: '#10B981'\n                    }\n                  ].map((stat, index) => (\n                    <motion.div\n                      key={index}\n                      initial={{ opacity: 0, scale: 0.8 }}\n                      animate={{ opacity: 1, scale: 1 }}\n                      transition={{ delay: 1.2 + index * 0.1, duration: 0.6 }}\n                      whileHover={{ scale: 1.05, y: -5 }}\n                      className={`bg-gradient-to-br ${stat.bgGradient} backdrop-blur-lg rounded-xl p-3 md:p-4 text-center relative overflow-hidden`}\n                      style={{\n                        border: `2px solid ${stat.borderColor}40`,\n                        boxShadow: `0 8px 32px ${stat.borderColor}20`\n                      }}\n                    >\n                      <div className=\"absolute inset-0 bg-gradient-to-br from-white/5 to-transparent\"></div>\n                      <stat.icon\n                        className=\"w-6 h-6 md:w-8 md:h-8 mx-auto mb-2 relative z-10\"\n                        style={{ color: stat.iconColor, filter: 'drop-shadow(0 2px 4px rgba(0,0,0,0.5))' }}\n                      />\n                      <div\n                        className=\"text-lg sm:text-xl md:text-2xl lg:text-3xl font-black mb-1 relative z-10\"\n                        style={{\n                          color: stat.iconColor,\n                          textShadow: `3px 3px 6px rgba(0,0,0,0.9)`,\n                          filter: 'drop-shadow(0 0 10px currentColor)',\n                          fontSize: 'clamp(1rem, 4vw, 2.5rem)'\n                        }}\n                      >\n                        {stat.value}\n                      </div>\n                      <div\n                        className=\"text-xs sm:text-sm font-bold relative z-10\"\n                        style={{\n                          color: '#FFFFFF',\n                          textShadow: '2px 2px 4px rgba(0,0,0,0.9)',\n                          fontSize: 'clamp(0.75rem, 2vw, 1rem)'\n                        }}\n                      >\n                        {stat.label}\n                      </div>\n                    </motion.div>\n                  ))}\n                </motion.div>\n              </div>\n            </div>\n          </div>\n        </motion.div>\n\n        {/* LOADING STATE */}\n        {loading && (\n          <motion.div\n            initial={{ opacity: 0 }}\n            animate={{ opacity: 1 }}\n            className=\"flex flex-col items-center justify-center py-20\"\n          >\n            <motion.div\n              animate={{ rotate: 360 }}\n              transition={{ duration: 2, repeat: Infinity, ease: \"linear\" }}\n              className=\"w-16 h-16 border-4 border-purple-500 border-t-transparent rounded-full mb-4\"\n            />\n            <p className=\"text-white/80 text-lg font-medium\">Loading champions...</p>\n          </motion.div>\n        )}\n\n        {/* EPIC LEADERBOARD */}\n        {!loading && (\n          <motion.div\n            initial={{ opacity: 0, y: 30 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ delay: 0.3, duration: 0.8 }}\n            className=\"px-4 sm:px-6 md:px-8 lg:px-12 pb-20 md:pb-24 lg:pb-32\"\n          >\n            <div className=\"max-w-7xl mx-auto\">\n\n              {/* TOP 3 PODIUM */}\n              {topPerformers.length > 0 && (\n                <motion.div\n                  initial={{ opacity: 0, scale: 0.9 }}\n                  animate={{ opacity: 1, scale: 1 }}\n                  transition={{ delay: 0.5, duration: 0.8 }}\n                  className=\"mb-12\"\n                >\n                  <h2 className=\"text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-black text-center mb-6 md:mb-8 lg:mb-12 px-4\" style={{\n                    background: 'linear-gradient(45deg, #FFD700, #FFA500, #FF6B35)',\n                    WebkitBackgroundClip: 'text',\n                    WebkitTextFillColor: 'transparent',\n                    textShadow: '3px 3px 6px rgba(0,0,0,0.8)',\n                    filter: 'drop-shadow(0 0 15px #FFD700)'\n                  }}>\n                    🏆 CHAMPIONS PODIUM 🏆\n                  </h2>\n\n                  {/* Horizontal Podium Layout with Moving Animations */}\n                  <div\n                    className=\"flex items-end justify-center max-w-5xl mx-auto mb-4 md:mb-8\"\n                    style={{\n                      gap: window.innerWidth <= 768 ? '8px' : window.innerWidth <= 1024 ? '16px' : '32px',\n                      padding: window.innerWidth <= 768 ? '8px' : '16px 24px'\n                    }}\n                  >\n                    {/* Second Place - Left */}\n                    {topPerformers[1] && (\n                      <motion.div\n                        key={`second-${topPerformers[1]._id}`}\n                        ref={user && String(topPerformers[1]._id) === String(user._id) ? podiumUserRef : null}\n                        data-user-id={topPerformers[1]._id}\n                        data-user-rank={2}\n                        initial={{ opacity: 0, x: -100, y: 50 }}\n                        animate={{\n                          opacity: 1,\n                          x: 0,\n                          y: 0,\n                          scale: [1, 1.02, 1],\n                          rotateY: [0, 5, 0]\n                        }}\n                        transition={{\n                          delay: 0.8,\n                          duration: 1.2,\n                          scale: { duration: 4, repeat: Infinity, ease: \"easeInOut\" },\n                          rotateY: { duration: 6, repeat: Infinity, ease: \"easeInOut\" }\n                        }}\n                        whileHover={{ scale: 1.05, y: -10 }}\n                        className={`relative order-1 ${\n                          shouldHighlightUser(topPerformers[1]._id)\n                            ? 'ring-8 ring-yellow-400 ring-opacity-100'\n                            : ''\n                        }`}\n                        style={{\n                          height: window.innerWidth <= 768 ? '200px' : '280px',\n                          transform: shouldHighlightUser(topPerformers[1]._id) ? 'scale(1.08)' : 'scale(1)',\n                          filter: shouldHighlightUser(topPerformers[1]._id)\n                            ? 'brightness(1.3) saturate(1.2) drop-shadow(0 0 30px rgba(255, 215, 0, 1))'\n                            : 'none',\n                          transition: 'all 0.3s ease',\n                          border: shouldHighlightUser(topPerformers[1]._id) ? '4px solid #FFD700' : 'none',\n                          borderRadius: isCurrentUser(topPerformers[1]._id) ? '20px' : '0px',\n                          background: isCurrentUser(topPerformers[1]._id) ? 'linear-gradient(45deg, rgba(255, 215, 0, 0.1), rgba(255, 165, 0, 0.1))' : 'transparent'\n                        }}\n                      >\n                        {/* Second Place Podium Base */}\n                        <div className=\"absolute bottom-0 w-full h-20 bg-gradient-to-t from-gray-400 to-gray-300 rounded-t-lg border-2 border-gray-500 flex items-center justify-center z-10\">\n                          <span className=\"text-2xl font-black text-gray-800 relative z-20\">2nd</span>\n                        </div>\n\n                        {/* Second Place Champion Card */}\n                        <div\n                          className={`relative bg-gradient-to-br ${topPerformers[1].tier.color} p-1 rounded-xl ${topPerformers[1].tier.glow} shadow-xl mb-20`}\n                          style={{\n                            boxShadow: `0 6px 20px ${topPerformers[1].tier.shadowColor}50`,\n                            width: '200px'\n                          }}\n                        >\n                          <div\n                            className={`${topPerformers[1].tier.bgColor} backdrop-blur-lg rounded-xl p-4 text-center relative overflow-hidden`}\n                          >\n                            <div className=\"absolute inset-0 bg-gradient-to-br from-white/5 to-transparent\"></div>\n\n                            {/* Silver Medal */}\n                            <div\n                              className=\"absolute -top-3 left-1/2 transform -translate-x-1/2 w-10 h-10 bg-gradient-to-br from-gray-300 to-gray-500 rounded-full flex items-center justify-center font-black text-lg shadow-lg relative z-20\"\n                              style={{\n                                color: '#1f2937',\n                                border: '2px solid #FFFFFF'\n                              }}\n                            >\n                              🥈\n                            </div>\n\n                            {/* Profile Picture */}\n                            <div className={`relative mx-auto mb-3 ${user && topPerformers[1]._id === user._id ? 'ring-1 ring-yellow-400 ring-opacity-80' : ''}`}>\n\n                              <ProfilePicture\n                                user={topPerformers[1]}\n                                size=\"md\"\n                                showOnlineStatus={true}\n                                style={{\n                                  width: '40px',\n                                  height: '40px'\n                                }}\n                              />\n                              {/* Debug: Show user data */}\n                              {console.log('🥈 Second place user:', topPerformers[1])}\n                            </div>\n\n                            {/* Name and Stats */}\n                            <h3\n                              className=\"text-sm font-bold mb-2 truncate\"\n                              style={{ color: topPerformers[1].tier.nameColor }}\n                            >\n                              {topPerformers[1].name}\n                            </h3>\n\n                            <div className=\"text-lg font-black mb-2\" style={{ color: topPerformers[1].tier.textColor }}>\n                              {topPerformers[1].totalXP.toLocaleString()} XP\n                            </div>\n\n                            <div className=\"flex justify-center gap-3 text-xs\">\n                              <span style={{ color: topPerformers[1].tier.textColor }}>\n                                🧠 {topPerformers[1].totalQuizzesTaken}\n                              </span>\n                              <span style={{ color: topPerformers[1].tier.textColor }}>\n                                🔥 {topPerformers[1].currentStreak}\n                              </span>\n                            </div>\n                          </div>\n                        </div>\n                      </motion.div>\n                    )}\n\n                    {/* First Place - Center and Elevated */}\n                    {topPerformers[0] && (\n                      <motion.div\n                        key={`first-${topPerformers[0]._id}`}\n                        ref={user && String(topPerformers[0]._id) === String(user._id) ? podiumUserRef : null}\n                        data-user-id={topPerformers[0]._id}\n                        data-user-rank={1}\n                        initial={{ opacity: 0, y: -100, scale: 0.8 }}\n                        animate={{\n                          opacity: 1,\n                          y: 0,\n                          scale: 1,\n                          rotateY: [0, 10, -10, 0],\n                          y: [0, -10, 0]\n                        }}\n                        transition={{\n                          delay: 0.5,\n                          duration: 1.5,\n                          rotateY: { duration: 8, repeat: Infinity, ease: \"easeInOut\" },\n                          y: { duration: 4, repeat: Infinity, ease: \"easeInOut\" }\n                        }}\n                        whileHover={{ scale: 1.08, y: -15 }}\n                        className={`relative order-2 z-10 ${\n                          shouldHighlightUser(topPerformers[0]._id)\n                            ? 'ring-8 ring-yellow-400 ring-opacity-100'\n                            : ''\n                        }`}\n                        style={{\n                          height: window.innerWidth <= 768 ? '240px' : '320px',\n                          transform: shouldHighlightUser(topPerformers[0]._id) ? 'scale(1.08)' : 'scale(1)',\n                          filter: shouldHighlightUser(topPerformers[0]._id)\n                            ? 'brightness(1.3) saturate(1.2) drop-shadow(0 0 30px rgba(255, 215, 0, 1))'\n                            : 'none',\n                          transition: 'all 0.3s ease',\n                          border: shouldHighlightUser(topPerformers[0]._id) ? '4px solid #FFD700' : 'none',\n                          borderRadius: isCurrentUser(topPerformers[0]._id) ? '20px' : '0px',\n                          background: isCurrentUser(topPerformers[0]._id) ? 'linear-gradient(45deg, rgba(255, 215, 0, 0.1), rgba(255, 165, 0, 0.1))' : 'transparent'\n                        }}\n                        data-section=\"podium\"\n\n                      >\n                        {/* First Place Podium Base - Tallest */}\n                        <div className=\"absolute bottom-0 w-full h-32 bg-gradient-to-t from-yellow-500 to-yellow-300 rounded-t-lg border-2 border-yellow-600 flex items-center justify-center z-10\">\n                          <span className=\"text-3xl font-black text-yellow-900 relative z-20\">1st</span>\n                        </div>\n\n                        {/* Crown Animation */}\n                        <motion.div\n                          animate={{ rotate: [0, 10, -10, 0], y: [0, -5, 0] }}\n                          transition={{ duration: 3, repeat: Infinity }}\n                          className=\"absolute -top-16 left-1/2 transform -translate-x-1/2 z-30\"\n                        >\n                          <TbCrown className=\"w-16 h-16 text-yellow-400 drop-shadow-lg\" />\n                        </motion.div>\n\n                        {/* First Place Champion Card */}\n                        <div\n                          className={`relative bg-gradient-to-br ${topPerformers[0].tier.color} p-1.5 rounded-2xl ${topPerformers[0].tier.glow} shadow-2xl mb-32 transform scale-110`}\n                          style={{\n                            boxShadow: `0 8px 32px ${topPerformers[0].tier.shadowColor}60, 0 0 0 1px rgba(255,255,255,0.1)`,\n                            width: '240px'\n                          }}\n                        >\n                          <div\n                            className={`${topPerformers[0].tier.bgColor} backdrop-blur-lg rounded-xl p-6 text-center relative overflow-hidden`}\n                            style={{\n                              background: `${topPerformers[0].tier.bgColor}, radial-gradient(circle at center, rgba(255,255,255,0.1) 0%, transparent 70%)`\n                            }}\n                          >\n                            <div className=\"absolute inset-0 bg-gradient-to-br from-white/10 to-transparent rounded-xl\"></div>\n\n                            {/* Gold Medal */}\n                            <div\n                              className=\"absolute -top-4 left-1/2 transform -translate-x-1/2 w-12 h-12 bg-gradient-to-br from-yellow-300 to-yellow-500 rounded-full flex items-center justify-center font-black text-xl shadow-lg relative z-20\"\n                              style={{\n                                color: '#1f2937',\n                                textShadow: '1px 1px 2px rgba(0,0,0,0.3)',\n                                border: '2px solid #FFFFFF'\n                              }}\n                            >\n                              👑\n                            </div>\n\n                            {/* Profile Picture */}\n                            <div className={`relative mx-auto mb-4 ${user && topPerformers[0]._id === user._id ? 'ring-1 ring-yellow-400 ring-opacity-80' : ''}`}>\n\n                              <div className=\"relative\">\n                                <ProfilePicture\n                                  user={topPerformers[0]}\n                                  size=\"lg\"\n                                  showOnlineStatus={false}\n                                  style={{\n                                    width: '48px',\n                                    height: '48px'\n                                  }}\n                                />\n                                {/* Only show online dot if user is actually online */}\n                                {topPerformers[0].isOnline && (\n                                  <div\n                                    style={{\n                                      position: 'absolute',\n                                      bottom: '-2px',\n                                      right: '-2px',\n                                      width: '14px',\n                                      height: '14px',\n                                      backgroundColor: '#22c55e',\n                                      borderRadius: '50%',\n                                      border: '2px solid #ffffff',\n                                      boxShadow: '0 2px 8px rgba(34, 197, 94, 0.6)',\n                                      zIndex: 10\n                                    }}\n                                    title=\"Online\"\n                                  />\n                                )}\n                              </div>\n                              {/* Debug: Show user data */}\n                              {console.log('🥇 First place user:', topPerformers[0])}\n                              {user && topPerformers[0]._id === user._id && (\n                                <div\n                                  className=\"absolute -bottom-2 -right-2 rounded-full p-2 animate-pulse\"\n                                  style={{\n                                    background: 'linear-gradient(45deg, #fbbf24, #f59e0b)',\n                                    boxShadow: '0 2px 8px rgba(0,0,0,0.3)'\n                                  }}\n                                >\n                                  <TbStar className=\"w-6 h-6 text-gray-900\" />\n                                </div>\n                              )}\n                            </div>\n\n                            {/* Champion Info */}\n                            <div className=\"flex items-center justify-center gap-2 mb-2\">\n                              <h3\n                                className=\"text-lg font-black truncate\"\n                                style={{\n                                  color: topPerformers[0].tier.nameColor,\n                                  textShadow: `2px 2px 4px ${topPerformers[0].tier.shadowColor}`,\n                                  filter: 'drop-shadow(0 0 8px currentColor)'\n                                }}\n                              >\n                                {topPerformers[0].name}\n                              </h3>\n                              {isCurrentUser(topPerformers[0]._id) && (\n                                <span\n                                  className=\"px-2 py-1 rounded-full text-xs font-black animate-pulse\"\n                                  style={{\n                                    background: 'linear-gradient(45deg, #FFD700, #FFA500)',\n                                    color: '#1f2937',\n                                    boxShadow: '0 2px 8px rgba(255,215,0,0.8)',\n                                    border: '1px solid #FFFFFF',\n                                    fontSize: '10px'\n                                  }}\n                                >\n                                  🎯 YOU\n                                </span>\n                              )}\n                            </div>\n\n                            <div\n                              className={`inline-flex items-center gap-2 px-4 py-2 bg-gradient-to-r ${topPerformers[0].tier.color} rounded-full text-sm font-black mb-3 relative z-10`}\n                              style={{\n                                background: `linear-gradient(135deg, ${topPerformers[0].tier.borderColor}, ${topPerformers[0].tier.textColor})`,\n                                color: '#FFFFFF',\n                                textShadow: '2px 2px 4px rgba(0,0,0,0.8)',\n                                boxShadow: `0 4px 15px ${topPerformers[0].tier.shadowColor}60`,\n                                border: '2px solid rgba(255,255,255,0.2)'\n                              }}\n                            >\n                              {topPerformers[0].tier.icon && React.createElement(topPerformers[0].tier.icon, {\n                                className: \"w-4 h-4\",\n                                style: { color: '#FFFFFF' }\n                              })}\n                              <span style={{ color: '#FFFFFF' }}>{topPerformers[0].tier.title}</span>\n                            </div>\n\n                            {/* Enhanced Stats */}\n                            <div className=\"space-y-2 relative z-10\">\n                              <div className=\"text-xl font-black\" style={{\n                                color: topPerformers[0].tier.nameColor,\n                                textShadow: `2px 2px 4px ${topPerformers[0].tier.shadowColor}`,\n                                filter: 'drop-shadow(0 0 8px currentColor)'\n                              }}>\n                                {topPerformers[0].totalXP.toLocaleString()} XP\n                              </div>\n\n                              <div className=\"flex justify-center gap-4 text-sm\">\n                                <div className=\"text-center\">\n                                  <div className=\"flex items-center gap-1 justify-center\">\n                                    <TbBrain className=\"w-4 h-4\" style={{ color: topPerformers[0].tier.textColor }} />\n                                    <span className=\"font-bold\" style={{ color: topPerformers[0].tier.textColor }}>\n                                      {topPerformers[0].totalQuizzesTaken}\n                                    </span>\n                                  </div>\n                                  <div className=\"text-xs opacity-80\" style={{ color: topPerformers[0].tier.textColor }}>Quizzes</div>\n                                </div>\n                                <div className=\"text-center\">\n                                  <div className=\"flex items-center gap-1 justify-center\">\n                                    <TbFlame className=\"w-4 h-4\" style={{ color: '#FF6B35' }} />\n                                    <span className=\"font-bold\" style={{ color: topPerformers[0].tier.textColor }}>\n                                      {topPerformers[0].currentStreak}\n                                    </span>\n                                  </div>\n                                  <div className=\"text-xs opacity-80\" style={{ color: topPerformers[0].tier.textColor }}>Streak</div>\n                                </div>\n                              </div>\n                            </div>\n                          </div>\n                        </div>\n                      </motion.div>\n                    )}\n\n                    {/* Third Place - Right */}\n                    {topPerformers[2] && (\n                      <motion.div\n                        key={`third-${topPerformers[2]._id}`}\n                        ref={user && String(topPerformers[2]._id) === String(user._id) ? podiumUserRef : null}\n                        data-user-id={topPerformers[2]._id}\n                        data-user-rank={3}\n                        initial={{ opacity: 0, x: 100, y: 50 }}\n                        animate={{\n                          opacity: 1,\n                          x: 0,\n                          y: 0,\n                          scale: [1, 1.02, 1],\n                          rotateY: [0, -5, 0]\n                        }}\n                        transition={{\n                          delay: 1.0,\n                          duration: 1.2,\n                          scale: { duration: 4, repeat: Infinity, ease: \"easeInOut\" },\n                          rotateY: { duration: 6, repeat: Infinity, ease: \"easeInOut\" }\n                        }}\n                        whileHover={{ scale: 1.05, y: -10 }}\n                        className={`relative order-3 ${\n                          shouldHighlightUser(topPerformers[2]._id)\n                            ? 'ring-8 ring-yellow-400 ring-opacity-100'\n                            : ''\n                        }`}\n                        style={{\n                          height: window.innerWidth <= 768 ? '200px' : '280px',\n                          transform: shouldHighlightUser(topPerformers[2]._id) ? 'scale(1.08)' : 'scale(1)',\n                          filter: shouldHighlightUser(topPerformers[2]._id)\n                            ? 'brightness(1.3) saturate(1.2) drop-shadow(0 0 30px rgba(255, 215, 0, 1))'\n                            : 'none',\n                          transition: 'all 0.3s ease',\n                          border: shouldHighlightUser(topPerformers[2]._id) ? '4px solid #FFD700' : 'none',\n                          borderRadius: isCurrentUser(topPerformers[2]._id) ? '20px' : '0px',\n                          background: isCurrentUser(topPerformers[2]._id) ? 'linear-gradient(45deg, rgba(255, 215, 0, 0.1), rgba(255, 165, 0, 0.1))' : 'transparent'\n                        }}\n                      >\n                        {/* Third Place Podium Base */}\n                        <div className=\"absolute bottom-0 w-full h-16 bg-gradient-to-t from-amber-600 to-amber-400 rounded-t-lg border-2 border-amber-700 flex items-center justify-center z-10\">\n                          <span className=\"text-xl font-black text-amber-900 relative z-20\">3rd</span>\n                        </div>\n\n                        {/* Third Place Champion Card */}\n                        <div\n                          className={`relative bg-gradient-to-br ${topPerformers[2].tier.color} p-1 rounded-xl ${topPerformers[2].tier.glow} shadow-xl mb-16`}\n                          style={{\n                            boxShadow: `0 6px 20px ${topPerformers[2].tier.shadowColor}50`,\n                            width: '200px'\n                          }}\n                        >\n                          <div\n                            className={`${topPerformers[2].tier.bgColor} backdrop-blur-lg rounded-xl p-4 text-center relative overflow-hidden`}\n                          >\n                            <div className=\"absolute inset-0 bg-gradient-to-br from-white/5 to-transparent\"></div>\n\n                            {/* Bronze Medal */}\n                            <div\n                              className=\"absolute -top-3 left-1/2 transform -translate-x-1/2 w-10 h-10 bg-gradient-to-br from-amber-600 to-amber-800 rounded-full flex items-center justify-center font-black text-lg shadow-lg relative z-20\"\n                              style={{\n                                color: '#1f2937',\n                                border: '2px solid #FFFFFF'\n                              }}\n                            >\n                              🥉\n                            </div>\n\n                            {/* Profile Picture */}\n                            <div className={`relative mx-auto mb-3 ${user && topPerformers[2]._id === user._id ? 'ring-1 ring-yellow-400 ring-opacity-80' : ''}`}>\n                              <ProfilePicture\n                                user={topPerformers[2]}\n                                size=\"md\"\n                                showOnlineStatus={true}\n                                style={{\n                                  width: '40px',\n                                  height: '40px'\n                                }}\n                              />\n                            </div>\n\n                            {/* Name and Stats */}\n                            <h3\n                              className=\"text-sm font-bold mb-2 truncate\"\n                              style={{ color: topPerformers[2].tier.nameColor }}\n                            >\n                              {topPerformers[2].name}\n                            </h3>\n\n                            <div className=\"text-lg font-black mb-2\" style={{ color: topPerformers[2].tier.textColor }}>\n                              {topPerformers[2].totalXP.toLocaleString()} XP\n                            </div>\n\n                            <div className=\"flex justify-center gap-3 text-xs\">\n                              <span style={{ color: topPerformers[2].tier.textColor }}>\n                                🧠 {topPerformers[2].totalQuizzesTaken}\n                              </span>\n                              <span style={{ color: topPerformers[2].tier.textColor }}>\n                                🔥 {topPerformers[2].currentStreak}\n                              </span>\n                            </div>\n                          </div>\n                        </div>\n                      </motion.div>\n                    )}\n                  </div>\n\n\n\n\n\n\n\n\n                </motion.div>\n              )}\n\n              {/* LEAGUE-BASED RANKING DISPLAY */}\n              {selectedLeague ? (\n                /* SELECTED LEAGUE VIEW */\n                leagueUsers.length > 0 && (\n                  <motion.div\n                    initial={{ opacity: 0, y: 30 }}\n                    animate={{ opacity: 1, y: 0 }}\n                    transition={{ delay: 1, duration: 0.8 }}\n                    className=\"mt-16 main-ranking-section\"\n                  >\n                    {/* Selected League Header */}\n                    <div className=\"text-center mb-8 md:mb-12\">\n                      <motion.h2\n                        className=\"text-2xl sm:text-3xl md:text-4xl font-black mb-3\"\n                        style={{\n                          background: `linear-gradient(45deg, ${leagueSystem[selectedLeague].borderColor}, ${leagueSystem[selectedLeague].textColor})`,\n                          WebkitBackgroundClip: 'text',\n                          WebkitTextFillColor: 'transparent',\n                          textShadow: '2px 2px 4px rgba(0,0,0,0.8)',\n                          filter: `drop-shadow(0 0 12px ${leagueSystem[selectedLeague].borderColor})`\n                        }}\n                        animate={{ scale: [1, 1.01, 1] }}\n                        transition={{ duration: 4, repeat: Infinity }}\n                      >\n                        {leagueSystem[selectedLeague].leagueIcon} {leagueSystem[selectedLeague].title} LEAGUE {leagueSystem[selectedLeague].leagueIcon}\n                      </motion.h2>\n                      <p className=\"text-white/70 text-sm md:text-base font-medium\">\n                        {leagueUsers.length} champions in this league\n                      </p>\n                      <motion.button\n                        whileHover={{ scale: 1.05 }}\n                        whileTap={{ scale: 0.95 }}\n                        onClick={() => setSelectedLeague(null)}\n                        className=\"mt-4 px-6 py-2 bg-gradient-to-r from-gray-600 to-gray-700 text-white rounded-lg font-medium shadow-lg hover:shadow-xl transition-all duration-300\"\n                      >\n                        ← Back to All Leagues\n                      </motion.button>\n                    </div>\n\n                    {/* Selected League Users */}\n                    <div className=\"max-w-6xl mx-auto px-4\">\n                      <div className=\"grid gap-3 md:gap-4\">\n                        {leagueUsers.map((champion, index) => {\n                          const actualRank = index + 1;\n                          const isCurrentUser = user && String(champion._id) === String(user._id);\n\n                          return (\n                            <motion.div\n                              key={champion._id}\n                              ref={isCurrentUser ? listUserRef : null}\n                              data-user-id={champion._id}\n                              data-user-rank={actualRank}\n                              initial={{ opacity: 0, y: 20 }}\n                              animate={{ opacity: 1, y: 0 }}\n                              transition={{ delay: 0.1 + index * 0.05, duration: 0.4 }}\n                              whileHover={{ scale: 1.01, y: -2 }}\n                              className={`ranking-card group relative ${\n                                shouldHighlightUser(champion._id)\n                                  ? 'ring-8 ring-yellow-400 ring-opacity-100'\n                                  : ''\n                              }`}\n                              style={{\n                                transform: shouldHighlightUser(champion._id) ? 'scale(1.05)' : 'scale(1)',\n                                filter: shouldHighlightUser(champion._id)\n                                  ? 'brightness(1.25) saturate(1.3) drop-shadow(0 0 25px rgba(255, 215, 0, 1))'\n                                  : 'none',\n                                transition: 'all 0.3s ease',\n                                border: shouldHighlightUser(champion._id) ? '4px solid #FFD700' : 'none',\n                                borderRadius: shouldHighlightUser(champion._id) ? '16px' : '0px',\n                                background: isCurrentUser ? 'linear-gradient(135deg, rgba(255, 215, 0, 0.15), rgba(255, 165, 0, 0.15))' : 'transparent',\n                                position: 'relative',\n                                zIndex: isCurrentUser ? 10 : 1\n                              }}\n                            >\n                              {/* League User Card */}\n                              <div\n                                className={`bg-gradient-to-r ${champion.tier.color} p-0.5 rounded-2xl ${champion.tier.glow} transition-all duration-300 group-hover:scale-[1.01]`}\n                                style={{\n                                  boxShadow: `0 4px 20px ${champion.tier.shadowColor}40`\n                                }}\n                              >\n                                <div\n                                  className={`${champion.tier.bgColor} backdrop-blur-xl rounded-2xl p-4 flex items-center gap-4 relative overflow-hidden`}\n                                  style={{\n                                    border: `1px solid ${champion.tier.borderColor}30`\n                                  }}\n                                >\n                                  {/* Subtle Background Gradient */}\n                                  <div className=\"absolute inset-0 bg-gradient-to-r from-white/3 to-transparent rounded-2xl\"></div>\n\n                                  {/* Left Section: Rank & Profile */}\n                                  <div className=\"flex items-center gap-3 flex-shrink-0\">\n                                    {/* Rank Badge */}\n                                    <div className=\"relative\">\n                                      <div\n                                        className=\"w-10 h-10 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-full flex items-center justify-center font-black text-sm shadow-lg relative z-10\"\n                                        style={{\n                                          color: '#FFFFFF',\n                                          textShadow: '1px 1px 2px rgba(0,0,0,0.8)',\n                                          border: '2px solid rgba(255,255,255,0.2)',\n                                          boxShadow: '0 2px 8px rgba(0,0,0,0.3)'\n                                        }}\n                                      >\n                                        #{actualRank}\n                                      </div>\n                                    </div>\n\n                                    {/* Profile Picture with Online Status */}\n                                    <div className=\"relative\">\n                                      <ProfilePicture\n                                        user={champion}\n                                        size=\"sm\"\n                                        showOnlineStatus={false}\n                                        style={{\n                                          width: '32px',\n                                          height: '32px'\n                                        }}\n                                      />\n                                      {/* Only show online dot if user is actually online */}\n                                      {champion.isOnline && (\n                                        <div\n                                          style={{\n                                            position: 'absolute',\n                                            bottom: '-2px',\n                                            right: '-2px',\n                                            width: '10px',\n                                            height: '10px',\n                                            backgroundColor: '#22c55e',\n                                            borderRadius: '50%',\n                                            border: '2px solid #ffffff',\n                                            boxShadow: '0 2px 8px rgba(34, 197, 94, 0.6)',\n                                            zIndex: 10\n                                          }}\n                                          title=\"Online\"\n                                        />\n                                      )}\n                                      {/* Current User Indicator */}\n                                      {isCurrentUser && (\n                                        <div\n                                          className=\"absolute -top-1 -right-1 w-4 h-4 rounded-full flex items-center justify-center animate-pulse\"\n                                          style={{\n                                            background: 'linear-gradient(45deg, #FFD700, #FFA500)',\n                                            boxShadow: '0 2px 6px rgba(255,215,0,0.6)'\n                                          }}\n                                        >\n                                          <TbStar className=\"w-2.5 h-2.5 text-gray-900\" />\n                                        </div>\n                                      )}\n                                    </div>\n                                  </div>\n\n                                  {/* Center Section: User Info */}\n                                  <div className=\"flex-1 min-w-0 px-2\">\n                                    <div className=\"space-y-1\">\n                                      {/* User Name */}\n                                      <div className=\"flex items-center gap-2 mb-1\">\n                                        <h3\n                                          className=\"text-base md:text-lg font-bold truncate\"\n                                          style={{\n                                            color: champion.tier.nameColor,\n                                            textShadow: `1px 1px 2px ${champion.tier.shadowColor}`,\n                                            filter: 'drop-shadow(0 0 4px currentColor)'\n                                          }}\n                                        >\n                                          {champion.name}\n                                        </h3>\n                                        {isCurrentUser && (\n                                          <span\n                                            className=\"px-3 py-1 rounded-full text-sm font-black animate-pulse\"\n                                            style={{\n                                              background: 'linear-gradient(45deg, #FFD700, #FFA500, #FFD700)',\n                                              color: '#1f2937',\n                                              boxShadow: '0 4px 12px rgba(255,215,0,0.8), 0 0 20px rgba(255,215,0,0.6)',\n                                              border: '2px solid #FFFFFF',\n                                              textShadow: '1px 1px 2px rgba(0,0,0,0.3)',\n                                              fontSize: '12px',\n                                              fontWeight: '900'\n                                            }}\n                                          >\n                                            🎯 YOU\n                                          </span>\n                                        )}\n                                      </div>\n\n                                      {/* Class Info */}\n                                      <div className=\"text-xs text-white/70 mt-0.5\">\n                                        {champion.level} • Class {champion.class}\n                                      </div>\n                                    </div>\n                                  </div>\n\n                                  {/* Right Section: Stats */}\n                                  <div className=\"flex flex-col items-end gap-1 flex-shrink-0\">\n                                    {/* XP Display */}\n                                    <div\n                                      className=\"text-lg md:text-xl font-black mb-2\"\n                                      style={{\n                                        color: champion.tier.nameColor,\n                                        textShadow: `1px 1px 2px ${champion.tier.shadowColor}`,\n                                        filter: 'drop-shadow(0 0 6px currentColor)'\n                                      }}\n                                    >\n                                      {champion.totalXP.toLocaleString()} XP\n                                    </div>\n\n                                    {/* Compact Stats */}\n                                    <div className=\"flex items-center gap-3 text-xs\">\n                                      <div\n                                        className=\"flex items-center gap-1 px-2 py-1 rounded-md\"\n                                        style={{\n                                          backgroundColor: `${champion.tier.borderColor}20`,\n                                          color: champion.tier.textColor\n                                        }}\n                                      >\n                                        <TbBrain className=\"w-3 h-3\" />\n                                        <span className=\"font-medium\">{champion.totalQuizzesTaken}</span>\n                                      </div>\n                                      <div\n                                        className=\"flex items-center gap-1 px-2 py-1 rounded-md\"\n                                        style={{\n                                          backgroundColor: '#FF6B3520',\n                                          color: '#FF6B35'\n                                        }}\n                                      >\n                                        <TbFlame className=\"w-3 h-3\" />\n                                        <span className=\"font-medium\">{champion.currentStreak}</span>\n                                      </div>\n                                    </div>\n                                  </div>\n                                </div>\n                              </div>\n                            </motion.div>\n                          );\n                        })}\n                      </div>\n                    </div>\n                  </motion.div>\n                )\n              ) : (\n                /* ALL LEAGUES GROUPED VIEW */\n                Object.keys(leagueGroups).length > 0 && (\n                  <motion.div\n                    initial={{ opacity: 0, y: 30 }}\n                    animate={{ opacity: 1, y: 0 }}\n                    transition={{ delay: 1, duration: 0.8 }}\n                    className=\"mt-16 main-ranking-section\"\n                    id=\"grouped-leagues-section\"\n                  >\n                    {/* All Leagues Header */}\n                    <div className=\"text-center mb-8 md:mb-12\">\n                      <motion.h2\n                        className=\"text-2xl sm:text-3xl md:text-4xl font-black mb-3\"\n                        style={{\n                          background: 'linear-gradient(45deg, #8B5CF6, #06B6D4, #10B981)',\n                          WebkitBackgroundClip: 'text',\n                          WebkitTextFillColor: 'transparent',\n                          textShadow: '2px 2px 4px rgba(0,0,0,0.8)',\n                          filter: 'drop-shadow(0 0 12px #8B5CF6)'\n                        }}\n                        animate={{ scale: [1, 1.01, 1] }}\n                        transition={{ duration: 4, repeat: Infinity }}\n                      >\n                        🏆 LEAGUE RANKINGS 🏆\n                      </motion.h2>\n                      <p className=\"text-white/70 text-sm md:text-base font-medium\">\n                        Click on any league icon above to see its members\n                      </p>\n                    </div>\n\n                    {/* Grouped Leagues Display */}\n                    <div className=\"max-w-6xl mx-auto px-4 space-y-8\">\n                      {getOrderedLeagues().map((leagueKey) => {\n                        const league = leagueSystem[leagueKey];\n                        const leagueData = leagueGroups[leagueKey];\n                        const topUsers = leagueData.users.slice(0, 3); // Show top 3 from each league\n\n                        return (\n                          <motion.div\n                            key={leagueKey}\n                            ref={(el) => (leagueRefs.current[leagueKey] = el)}\n                            initial={{ opacity: 0, y: 20 }}\n                            animate={{ opacity: 1, y: 0 }}\n                            transition={{ delay: 0.2, duration: 0.6 }}\n                            className=\"bg-gradient-to-r from-white/5 to-white/10 backdrop-blur-lg rounded-2xl p-6 border border-white/10\"\n                            id={`league-${leagueKey}`}\n                            data-league={leagueKey}\n                          >\n                            {/* League Header */}\n                            <div className=\"flex items-center justify-between mb-6\">\n                              <div className=\"flex items-center gap-4\">\n                                <div\n                                  className=\"w-16 h-16 rounded-xl flex items-center justify-center text-3xl\"\n                                  style={{\n                                    background: `linear-gradient(135deg, ${league.borderColor}40, ${league.textColor}20)`,\n                                    border: `2px solid ${league.borderColor}60`,\n                                    boxShadow: `0 4px 20px ${league.shadowColor}40`\n                                  }}\n                                >\n                                  {league.leagueIcon}\n                                </div>\n                                <div>\n                                  <h3\n                                    className=\"text-2xl font-black mb-1\"\n                                    style={{\n                                      color: league.nameColor,\n                                      textShadow: `2px 2px 4px ${league.shadowColor}`,\n                                      filter: 'drop-shadow(0 0 8px currentColor)'\n                                    }}\n                                  >\n                                    {league.title} LEAGUE\n                                  </h3>\n                                  <p className=\"text-white/70 text-sm\">\n                                    {leagueData.users.length} champions • {league.description}\n                                  </p>\n                                </div>\n                              </div>\n                              <motion.button\n                                whileHover={{ scale: 1.05 }}\n                                whileTap={{ scale: 0.95 }}\n                                onClick={() => handleLeagueSelect(leagueKey)}\n                                className=\"px-4 py-2 bg-gradient-to-r from-blue-600 to-indigo-600 text-white rounded-lg font-medium shadow-lg hover:shadow-xl transition-all duration-300\"\n                              >\n                                View All ({leagueData.users.length})\n                              </motion.button>\n                            </div>\n\n                            {/* Top 3 Users from League */}\n                            <div className=\"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4\">\n                              {topUsers.map((champion, index) => {\n                                const isCurrentUser = user && champion._id === user._id;\n                                const leagueRank = index + 1;\n\n                                return (\n                                  <motion.div\n                                    key={champion._id}\n                                    data-user-id={champion._id}\n                                    data-user-rank={leagueRank}\n                                    initial={{ opacity: 0, scale: 0.9 }}\n                                    animate={{ opacity: 1, scale: 1 }}\n                                    transition={{ delay: 0.3 + index * 0.1, duration: 0.4 }}\n                                    whileHover={{ scale: 1.02, y: -2 }}\n                                    className={`relative ${\n                                      shouldHighlightUser(champion._id)\n                                        ? 'ring-2 ring-yellow-400/60'\n                                        : ''\n                                    }`}\n                                  >\n                                    <div\n                                      className={`bg-gradient-to-br ${champion.tier.color} p-0.5 rounded-xl ${champion.tier.glow} shadow-lg`}\n                                      style={{\n                                        boxShadow: `0 4px 15px ${champion.tier.shadowColor}30`\n                                      }}\n                                    >\n                                      <div\n                                        className={`${champion.tier.bgColor} backdrop-blur-lg rounded-xl p-4 text-center relative overflow-hidden`}\n                                      >\n                                        <div className=\"absolute inset-0 bg-gradient-to-br from-white/5 to-transparent\"></div>\n\n                                        {/* League Rank Badge */}\n                                        <div\n                                          className=\"absolute -top-2 -right-2 w-6 h-6 rounded-full flex items-center justify-center font-black text-xs\"\n                                          style={{\n                                            background: league.borderColor,\n                                            color: '#FFFFFF',\n                                            border: '2px solid #FFFFFF'\n                                          }}\n                                        >\n                                          #{leagueRank}\n                                        </div>\n\n                                        {/* Profile Picture */}\n                                        <div className={`relative mx-auto mb-3 ${\n                                          isCurrentUser\n                                            ? 'ring-1 ring-yellow-400 ring-opacity-80'\n                                            : ''\n                                        }`}>\n                                          <ProfilePicture\n                                            user={champion}\n                                            size=\"md\"\n                                            showOnlineStatus={true}\n                                            style={{\n                                              width: '40px',\n                                              height: '40px'\n                                            }}\n                                          />\n                                          {isCurrentUser && (\n                                            <div\n                                              className=\"absolute -bottom-1 -right-1 w-4 h-4 rounded-full flex items-center justify-center animate-pulse\"\n                                              style={{\n                                                background: 'linear-gradient(45deg, #FFD700, #FFA500)',\n                                                boxShadow: '0 2px 6px rgba(255,215,0,0.6)'\n                                              }}\n                                            >\n                                              <TbStar className=\"w-2.5 h-2.5 text-gray-900\" />\n                                            </div>\n                                          )}\n                                        </div>\n\n                                        {/* Name and Stats */}\n                                        <h4\n                                          className=\"text-sm font-bold mb-2 truncate\"\n                                          style={{ color: champion.tier.nameColor }}\n                                        >\n                                          {champion.name}\n                                          {isCurrentUser && (\n                                            <span className=\"ml-1 text-xs text-yellow-400\">👑</span>\n                                          )}\n                                        </h4>\n\n                                        <div className=\"text-lg font-black mb-2\" style={{ color: champion.tier.textColor }}>\n                                          {champion.totalXP.toLocaleString()} XP\n                                        </div>\n\n                                        <div className=\"flex justify-center gap-3 text-xs\">\n                                          <span style={{ color: champion.tier.textColor }}>\n                                            🧠 {champion.totalQuizzesTaken}\n                                          </span>\n                                          <span style={{ color: champion.tier.textColor }}>\n                                            🔥 {champion.currentStreak}\n                                          </span>\n                                        </div>\n                                      </div>\n                                    </div>\n                                  </motion.div>\n                                );\n                              })}\n                            </div>\n\n                            {/* Show More Indicator */}\n                            {leagueData.users.length > 3 && (\n                              <div className=\"text-center mt-4\">\n                                <p className=\"text-white/60 text-sm\">\n                                  +{leagueData.users.length - 3} more champions in this league\n                                </p>\n                              </div>\n                            )}\n                          </motion.div>\n                        );\n                      })}\n                    </div>\n                  </motion.div>\n                )\n              )}\n\n\n\n\n              {/* EMPTY STATE */}\n              {rankingData.length === 0 && !loading && (\n                <motion.div\n                  initial={{ opacity: 0, scale: 0.9 }}\n                  animate={{ opacity: 1, scale: 1 }}\n                  className=\"text-center py-20\"\n                >\n                  <TbTrophy className=\"w-24 h-24 text-white/30 mx-auto mb-6\" />\n                  <h3 className=\"text-2xl font-bold mb-4\" style={{\n                    color: '#ffffff',\n                    textShadow: '2px 2px 4px rgba(0,0,0,0.8)',\n                    fontWeight: '800'\n                  }}>No Champions Yet</h3>\n                  <p className=\"text-lg\" style={{\n                    color: '#e5e7eb',\n                    textShadow: '1px 1px 2px rgba(0,0,0,0.8)',\n                    fontWeight: '600'\n                  }}>\n                    Be the first to take a quiz and claim your spot in the Hall of Champions!\n                  </p>\n                </motion.div>\n              )}\n            </div>\n          </motion.div>\n        )}\n      </div>\n    </div>\n    </>\n  );\n};\n\nexport default AmazingRankingPage;\n"], "names": ["default", "axiosInstance", "require", "addReport", "async", "post", "payload", "data", "error", "response", "getAllReports", "filters", "getAllReportsByUser", "getAllReportsForRanking", "get", "getXPLeaderboard", "options", "arguments", "length", "undefined", "params", "URLSearchParams", "limit", "append", "classFilter", "levelFilter", "seasonFilter", "includeInactive", "concat", "toString", "getUserRanking", "userId", "context", "AmazingRankingPage", "reduxUser", "useSelector", "state", "users", "user", "localStorageUser", "userData", "localStorage", "getItem", "JSON", "parse", "_unused", "tokenUser", "token", "atob", "split", "_unused2", "fullUserData", "setFullUserData", "useState", "console", "log", "redux", "final", "navigate", "useNavigate", "rankingData", "setRankingData", "loading", "setLoading", "currentUserRank", "setCurrentUserRank", "viewMode", "setViewMode", "showStats", "setShowStats", "animationPhase", "setAnimationPhase", "motivationalQuote", "setMotivationalQuote", "currentUserLeague", "setCurrentUserLeague", "leagueUsers", "setLeagueUsers", "showLeagueView", "setShowLeagueView", "<PERSON><PERSON><PERSON><PERSON>", "setSelectedLeague", "leagueGroups", "setLeagueGroups", "userHasBeenShown", "setUserHasBeenShown", "autoScrollCompleted", "setAutoScrollCompleted", "leagueRefs", "useRef", "podiumUserRef", "listUserRef", "motivationalQuotes", "leagueSystem", "mythic", "min", "color", "bgColor", "textColor", "nameColor", "shadowColor", "glow", "icon", "TbCrown", "title", "description", "borderColor", "effect", "leagueIcon", "promotionXP", "relegationXP", "maxUsers", "legendary", "TbDiamond", "diamond", "TbShield", "platinum", "TbAward", "gold", "TbTrophy", "silver", "TbMedal", "bronze", "TbStar", "rookie", "TbRocket", "getUserLeague", "xp", "league", "config", "Object", "entries", "_objectSpread", "groupUsersByLeague", "leagues", "for<PERSON>ach", "userLeague", "totalXP", "push", "tier", "keys", "leagueKey", "sort", "a", "b", "getCurrentUserLeagueData", "allUsers", "currentUser", "filter", "userRank", "findIndex", "u", "_id", "totalInLeague", "handleLeagueSelect", "_leagueGroups$leagueK", "setTimeout", "leagueElement", "document", "querySelector", "getElementById", "current", "scrollIntoView", "behavior", "block", "inline", "style", "transform", "transition", "boxShadow", "getOrderedLeagues", "fetchRankingData", "forceRefresh", "currentLevel", "level", "removeItem", "userLevel", "cachedRanking", "cacheTime", "now", "Date", "parseInt", "cached", "xpLeaderboardResponse", "_t", "success", "filteredData", "totalQuizzesTaken", "slice", "map", "name", "profileImage", "profilePicture", "hasProfileData", "transformedData", "index", "email", "class", "averageScore", "currentStreak", "bestStreak", "subscriptionStatus", "rank", "isRealUser", "rankingScore", "xpToNextLevel", "lifetimeXP", "seasonXP", "achievements", "dataSource", "userRankIndex", "item", "userLeagueData", "grouped", "cacheData", "setItem", "stringify", "xpError", "rankingResponse", "usersResponse", "getAllUsers", "userError", "userReportsMap", "_item$user", "reports", "isAdmin", "toLowerCase", "dataLevel", "userReports", "totalQuizzes", "totalScore", "reduce", "sum", "report", "score", "Math", "round", "totalPoints", "estimatedQuizzes", "max", "floor", "estimatedAverage", "tempStreak", "pointsPerQuiz", "originalPoints", "hasReports", "String", "dataSources", "legacy_points", "estimated", "quizzes", "avg", "source", "message", "warning", "useEffect", "userInRanking", "find", "userDataWithProfile", "id", "userDataAlt", "includes", "fetchFullUserData", "randomQuote", "random", "animationTimer", "setInterval", "prev", "handleWindowFocus", "handleRankingUpdate", "event", "detail", "attempts", "i", "_event$detail", "newTotalXP", "updatedUser", "Promise", "resolve", "refreshWithRetry", "window", "addEventListener", "clearInterval", "removeEventListener", "leagueData", "topPerformers", "userLeagueInfo", "getUserLeagueInfo", "some", "performer", "type", "position", "_leagueData$users", "totalUsers", "isCurrentUser", "shouldHighlightUser", "rankingData<PERSON>ength", "<PERSON><PERSON>ser", "completed", "hasData", "timer", "scrollToUser", "isInPodium", "podiumSection", "userElement", "clearTimeout", "RankingSkeleton", "_jsx", "className", "children", "_jsxs", "Array", "_", "_Fragment", "onClick", "handlePageClick", "motion", "div", "initial", "opacity", "y", "animate", "exit", "x", "duration", "repeat", "Infinity", "delay", "left", "top", "padding", "innerWidth", "button", "whileHover", "scale", "whileTap", "fontSize", "TbHome", "background", "ProfilePicture", "size", "showOnlineStatus", "border", "isOnline", "bottom", "right", "width", "height", "backgroundColor", "borderRadius", "zIndex", "username", "points", "toLocaleString", "userXP", "_leagueData$users2", "leagueInfo", "toUpperCase", "quizzesCompleted", "quizzesTaken", "streak", "avgScore", "_leagueData$users3", "userIndex", "h3", "WebkitBackgroundClip", "WebkitTextFillColor", "textShadow", "_leagueGroups$leagueK2", "isSelected", "userCount", "ease", "rotate", "span", "disabled", "TbRefresh", "rotateY", "backgroundPosition", "backgroundSize", "fontWeight", "p", "fontStyle", "TbUsers", "value", "label", "bgGradient", "iconColor", "TbFlame", "stat", "gap", "ref", "React", "TbBrain", "h2", "champion", "actualRank", "topUsers", "el", "leagueRank"], "sourceRoot": ""}