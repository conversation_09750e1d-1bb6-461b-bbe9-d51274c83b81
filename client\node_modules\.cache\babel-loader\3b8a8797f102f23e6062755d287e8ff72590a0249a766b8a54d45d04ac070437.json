{"ast": null, "code": "import React, { useEffect, useState } from 'react';\nimport Input from '../../input';\nimport { toHexFormat } from '../color';\nimport { generateColor } from '../util';\nconst hexReg = /(^#[\\da-f]{6}$)|(^#[\\da-f]{8}$)/i;\nconst isHexString = hex => hexReg.test(\"#\".concat(hex));\nconst ColorHexInput = _ref => {\n  let {\n    prefixCls,\n    value,\n    onChange\n  } = _ref;\n  const colorHexInputPrefixCls = \"\".concat(prefixCls, \"-hex-input\");\n  const [hexValue, setHexValue] = useState(value === null || value === void 0 ? void 0 : value.toHex());\n  // Update step value\n  useEffect(() => {\n    const hex = value === null || value === void 0 ? void 0 : value.toHex();\n    if (isHexString(hex) && value) {\n      setHexValue(toHexFormat(hex));\n    }\n  }, [value]);\n  const handleHexChange = e => {\n    const originValue = e.target.value;\n    setHexValue(toHexFormat(originValue));\n    if (isHexString(toHexFormat(originValue, true))) {\n      onChange === null || onChange === void 0 ? void 0 : onChange(generateColor(originValue));\n    }\n  };\n  return /*#__PURE__*/React.createElement(Input, {\n    className: colorHexInputPrefixCls,\n    value: hexValue === null || hexValue === void 0 ? void 0 : hexValue.toUpperCase(),\n    prefix: \"#\",\n    onChange: handleHexChange,\n    size: \"small\"\n  });\n};\nexport default ColorHexInput;", "map": {"version": 3, "names": ["React", "useEffect", "useState", "Input", "toHexFormat", "generateColor", "hexReg", "isHexString", "hex", "test", "concat", "ColorHexInput", "_ref", "prefixCls", "value", "onChange", "colorHexInputPrefixCls", "hexValue", "setHexValue", "toHex", "handleHexChange", "e", "originValue", "target", "createElement", "className", "toUpperCase", "prefix", "size"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/node_modules/antd/es/color-picker/components/ColorHexInput.js"], "sourcesContent": ["import React, { useEffect, useState } from 'react';\nimport Input from '../../input';\nimport { toHexFormat } from '../color';\nimport { generateColor } from '../util';\nconst hexReg = /(^#[\\da-f]{6}$)|(^#[\\da-f]{8}$)/i;\nconst isHexString = hex => hexReg.test(`#${hex}`);\nconst ColorHexInput = _ref => {\n  let {\n    prefixCls,\n    value,\n    onChange\n  } = _ref;\n  const colorHexInputPrefixCls = `${prefixCls}-hex-input`;\n  const [hexValue, setHexValue] = useState(value === null || value === void 0 ? void 0 : value.toHex());\n  // Update step value\n  useEffect(() => {\n    const hex = value === null || value === void 0 ? void 0 : value.toHex();\n    if (isHexString(hex) && value) {\n      setHexValue(toHexFormat(hex));\n    }\n  }, [value]);\n  const handleHexChange = e => {\n    const originValue = e.target.value;\n    setHexValue(toHexFormat(originValue));\n    if (isHexString(toHexFormat(originValue, true))) {\n      onChange === null || onChange === void 0 ? void 0 : onChange(generateColor(originValue));\n    }\n  };\n  return /*#__PURE__*/React.createElement(Input, {\n    className: colorHexInputPrefixCls,\n    value: hexValue === null || hexValue === void 0 ? void 0 : hexValue.toUpperCase(),\n    prefix: \"#\",\n    onChange: handleHexChange,\n    size: \"small\"\n  });\n};\nexport default ColorHexInput;"], "mappings": "AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,OAAOC,KAAK,MAAM,aAAa;AAC/B,SAASC,WAAW,QAAQ,UAAU;AACtC,SAASC,aAAa,QAAQ,SAAS;AACvC,MAAMC,MAAM,GAAG,kCAAkC;AACjD,MAAMC,WAAW,GAAGC,GAAG,IAAIF,MAAM,CAACG,IAAI,KAAAC,MAAA,CAAKF,GAAG,CAAE,CAAC;AACjD,MAAMG,aAAa,GAAGC,IAAI,IAAI;EAC5B,IAAI;IACFC,SAAS;IACTC,KAAK;IACLC;EACF,CAAC,GAAGH,IAAI;EACR,MAAMI,sBAAsB,MAAAN,MAAA,CAAMG,SAAS,eAAY;EACvD,MAAM,CAACI,QAAQ,EAAEC,WAAW,CAAC,GAAGhB,QAAQ,CAACY,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,KAAK,CAACK,KAAK,CAAC,CAAC,CAAC;EACrG;EACAlB,SAAS,CAAC,MAAM;IACd,MAAMO,GAAG,GAAGM,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,KAAK,CAACK,KAAK,CAAC,CAAC;IACvE,IAAIZ,WAAW,CAACC,GAAG,CAAC,IAAIM,KAAK,EAAE;MAC7BI,WAAW,CAACd,WAAW,CAACI,GAAG,CAAC,CAAC;IAC/B;EACF,CAAC,EAAE,CAACM,KAAK,CAAC,CAAC;EACX,MAAMM,eAAe,GAAGC,CAAC,IAAI;IAC3B,MAAMC,WAAW,GAAGD,CAAC,CAACE,MAAM,CAACT,KAAK;IAClCI,WAAW,CAACd,WAAW,CAACkB,WAAW,CAAC,CAAC;IACrC,IAAIf,WAAW,CAACH,WAAW,CAACkB,WAAW,EAAE,IAAI,CAAC,CAAC,EAAE;MAC/CP,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAACV,aAAa,CAACiB,WAAW,CAAC,CAAC;IAC1F;EACF,CAAC;EACD,OAAO,aAAatB,KAAK,CAACwB,aAAa,CAACrB,KAAK,EAAE;IAC7CsB,SAAS,EAAET,sBAAsB;IACjCF,KAAK,EAAEG,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAACS,WAAW,CAAC,CAAC;IACjFC,MAAM,EAAE,GAAG;IACXZ,QAAQ,EAAEK,eAAe;IACzBQ,IAAI,EAAE;EACR,CAAC,CAAC;AACJ,CAAC;AACD,eAAejB,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}