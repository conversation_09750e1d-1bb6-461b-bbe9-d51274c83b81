{"ast": null, "code": "import _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport { useEffect, useRef, useState } from 'react';\nfunction getPosition(e) {\n  var obj = 'touches' in e ? e.touches[0] : e;\n  var scrollXOffset = document.documentElement.scrollLeft || document.body.scrollLeft || window.pageXOffset;\n  var scrollYOffset = document.documentElement.scrollTop || document.body.scrollTop || window.pageYOffset;\n  return {\n    pageX: obj.pageX - scrollXOffset,\n    pageY: obj.pageY - scrollYOffset\n  };\n}\nfunction useColorDrag(props) {\n  var offset = props.offset,\n    targetRef = props.targetRef,\n    containerRef = props.containerRef,\n    direction = props.direction,\n    onDragChange = props.onDragChange,\n    onDragChangeComplete = props.onDragChangeComplete,\n    calculate = props.calculate,\n    color = props.color,\n    disabledDrag = props.disabledDrag;\n  var _useState = useState(offset || {\n      x: 0,\n      y: 0\n    }),\n    _useState2 = _slicedToArray(_useState, 2),\n    offsetValue = _useState2[0],\n    setOffsetValue = _useState2[1];\n  var mouseMoveRef = useRef(null);\n  var mouseUpRef = useRef(null);\n  var dragRef = useRef({\n    flag: false\n  });\n  useEffect(function () {\n    if (dragRef.current.flag === false) {\n      var calcOffset = calculate === null || calculate === void 0 ? void 0 : calculate(containerRef);\n      if (calcOffset) {\n        setOffsetValue(calcOffset);\n      }\n    }\n  }, [color, containerRef]);\n  useEffect(function () {\n    return function () {\n      document.removeEventListener('mousemove', mouseMoveRef.current);\n      document.removeEventListener('mouseup', mouseUpRef.current);\n      document.removeEventListener('touchmove', mouseMoveRef.current);\n      document.removeEventListener('touchend', mouseUpRef.current);\n      mouseMoveRef.current = null;\n      mouseUpRef.current = null;\n    };\n  }, []);\n  var updateOffset = function updateOffset(e) {\n    var _getPosition = getPosition(e),\n      pageX = _getPosition.pageX,\n      pageY = _getPosition.pageY;\n    var _containerRef$current = containerRef.current.getBoundingClientRect(),\n      rectX = _containerRef$current.x,\n      rectY = _containerRef$current.y,\n      width = _containerRef$current.width,\n      height = _containerRef$current.height;\n    var _targetRef$current$ge = targetRef.current.getBoundingClientRect(),\n      targetWidth = _targetRef$current$ge.width,\n      targetHeight = _targetRef$current$ge.height;\n    var centerOffsetX = targetWidth / 2;\n    var centerOffsetY = targetHeight / 2;\n    var offsetX = Math.max(0, Math.min(pageX - rectX, width)) - centerOffsetX;\n    var offsetY = Math.max(0, Math.min(pageY - rectY, height)) - centerOffsetY;\n    var calcOffset = {\n      x: offsetX,\n      y: direction === 'x' ? offsetValue.y : offsetY\n    };\n\n    // Exclusion of boundary cases\n    if (targetWidth === 0 && targetHeight === 0 || targetWidth !== targetHeight) {\n      return false;\n    }\n    setOffsetValue(calcOffset);\n    onDragChange === null || onDragChange === void 0 ? void 0 : onDragChange(calcOffset);\n  };\n  var onDragMove = function onDragMove(e) {\n    e.preventDefault();\n    updateOffset(e);\n  };\n  var onDragStop = function onDragStop(e) {\n    e.preventDefault();\n    dragRef.current.flag = false;\n    document.removeEventListener('mousemove', mouseMoveRef.current);\n    document.removeEventListener('mouseup', mouseUpRef.current);\n    document.removeEventListener('touchmove', mouseMoveRef.current);\n    document.removeEventListener('touchend', mouseUpRef.current);\n    mouseMoveRef.current = null;\n    mouseUpRef.current = null;\n    onDragChangeComplete === null || onDragChangeComplete === void 0 ? void 0 : onDragChangeComplete();\n  };\n  var onDragStart = function onDragStart(e) {\n    // https://github.com/ant-design/ant-design/issues/43529\n    document.removeEventListener('mousemove', mouseMoveRef.current);\n    document.removeEventListener('mouseup', mouseUpRef.current);\n    if (disabledDrag) {\n      return;\n    }\n    updateOffset(e);\n    dragRef.current.flag = true;\n    document.addEventListener('mousemove', onDragMove);\n    document.addEventListener('mouseup', onDragStop);\n    document.addEventListener('touchmove', onDragMove);\n    document.addEventListener('touchend', onDragStop);\n    mouseMoveRef.current = onDragMove;\n    mouseUpRef.current = onDragStop;\n  };\n  return [offsetValue, onDragStart];\n}\nexport default useColorDrag;", "map": {"version": 3, "names": ["_slicedToArray", "useEffect", "useRef", "useState", "getPosition", "e", "obj", "touches", "scrollXOffset", "document", "documentElement", "scrollLeft", "body", "window", "pageXOffset", "scrollYOffset", "scrollTop", "pageYOffset", "pageX", "pageY", "useColorDrag", "props", "offset", "targetRef", "containerRef", "direction", "onDragChange", "onDragChangeComplete", "calculate", "color", "disabledDrag", "_useState", "x", "y", "_useState2", "offsetValue", "setOffsetValue", "mouseMoveRef", "mouseUpRef", "dragRef", "flag", "current", "calcOffset", "removeEventListener", "updateOffset", "_getPosition", "_containerRef$current", "getBoundingClientRect", "rectX", "rectY", "width", "height", "_targetRef$current$ge", "targetWidth", "targetHeight", "centerOffsetX", "centerOffsetY", "offsetX", "Math", "max", "min", "offsetY", "onDragMove", "preventDefault", "onDragStop", "onDragStart", "addEventListener"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/node_modules/@rc-component/color-picker/es/hooks/useColorDrag.js"], "sourcesContent": ["import _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport { useEffect, useRef, useState } from 'react';\nfunction getPosition(e) {\n  var obj = 'touches' in e ? e.touches[0] : e;\n  var scrollXOffset = document.documentElement.scrollLeft || document.body.scrollLeft || window.pageXOffset;\n  var scrollYOffset = document.documentElement.scrollTop || document.body.scrollTop || window.pageYOffset;\n  return {\n    pageX: obj.pageX - scrollXOffset,\n    pageY: obj.pageY - scrollYOffset\n  };\n}\nfunction useColorDrag(props) {\n  var offset = props.offset,\n    targetRef = props.targetRef,\n    containerRef = props.containerRef,\n    direction = props.direction,\n    onDragChange = props.onDragChange,\n    onDragChangeComplete = props.onDragChangeComplete,\n    calculate = props.calculate,\n    color = props.color,\n    disabledDrag = props.disabledDrag;\n  var _useState = useState(offset || {\n      x: 0,\n      y: 0\n    }),\n    _useState2 = _slicedToArray(_useState, 2),\n    offsetValue = _useState2[0],\n    setOffsetValue = _useState2[1];\n  var mouseMoveRef = useRef(null);\n  var mouseUpRef = useRef(null);\n  var dragRef = useRef({\n    flag: false\n  });\n  useEffect(function () {\n    if (dragRef.current.flag === false) {\n      var calcOffset = calculate === null || calculate === void 0 ? void 0 : calculate(containerRef);\n      if (calcOffset) {\n        setOffsetValue(calcOffset);\n      }\n    }\n  }, [color, containerRef]);\n  useEffect(function () {\n    return function () {\n      document.removeEventListener('mousemove', mouseMoveRef.current);\n      document.removeEventListener('mouseup', mouseUpRef.current);\n      document.removeEventListener('touchmove', mouseMoveRef.current);\n      document.removeEventListener('touchend', mouseUpRef.current);\n      mouseMoveRef.current = null;\n      mouseUpRef.current = null;\n    };\n  }, []);\n  var updateOffset = function updateOffset(e) {\n    var _getPosition = getPosition(e),\n      pageX = _getPosition.pageX,\n      pageY = _getPosition.pageY;\n    var _containerRef$current = containerRef.current.getBoundingClientRect(),\n      rectX = _containerRef$current.x,\n      rectY = _containerRef$current.y,\n      width = _containerRef$current.width,\n      height = _containerRef$current.height;\n    var _targetRef$current$ge = targetRef.current.getBoundingClientRect(),\n      targetWidth = _targetRef$current$ge.width,\n      targetHeight = _targetRef$current$ge.height;\n    var centerOffsetX = targetWidth / 2;\n    var centerOffsetY = targetHeight / 2;\n    var offsetX = Math.max(0, Math.min(pageX - rectX, width)) - centerOffsetX;\n    var offsetY = Math.max(0, Math.min(pageY - rectY, height)) - centerOffsetY;\n    var calcOffset = {\n      x: offsetX,\n      y: direction === 'x' ? offsetValue.y : offsetY\n    };\n\n    // Exclusion of boundary cases\n    if (targetWidth === 0 && targetHeight === 0 || targetWidth !== targetHeight) {\n      return false;\n    }\n    setOffsetValue(calcOffset);\n    onDragChange === null || onDragChange === void 0 ? void 0 : onDragChange(calcOffset);\n  };\n  var onDragMove = function onDragMove(e) {\n    e.preventDefault();\n    updateOffset(e);\n  };\n  var onDragStop = function onDragStop(e) {\n    e.preventDefault();\n    dragRef.current.flag = false;\n    document.removeEventListener('mousemove', mouseMoveRef.current);\n    document.removeEventListener('mouseup', mouseUpRef.current);\n    document.removeEventListener('touchmove', mouseMoveRef.current);\n    document.removeEventListener('touchend', mouseUpRef.current);\n    mouseMoveRef.current = null;\n    mouseUpRef.current = null;\n    onDragChangeComplete === null || onDragChangeComplete === void 0 ? void 0 : onDragChangeComplete();\n  };\n  var onDragStart = function onDragStart(e) {\n    // https://github.com/ant-design/ant-design/issues/43529\n    document.removeEventListener('mousemove', mouseMoveRef.current);\n    document.removeEventListener('mouseup', mouseUpRef.current);\n    if (disabledDrag) {\n      return;\n    }\n    updateOffset(e);\n    dragRef.current.flag = true;\n    document.addEventListener('mousemove', onDragMove);\n    document.addEventListener('mouseup', onDragStop);\n    document.addEventListener('touchmove', onDragMove);\n    document.addEventListener('touchend', onDragStop);\n    mouseMoveRef.current = onDragMove;\n    mouseUpRef.current = onDragStop;\n  };\n  return [offsetValue, onDragStart];\n}\nexport default useColorDrag;"], "mappings": "AAAA,OAAOA,cAAc,MAAM,0CAA0C;AACrE,SAASC,SAAS,EAAEC,MAAM,EAAEC,QAAQ,QAAQ,OAAO;AACnD,SAASC,WAAWA,CAACC,CAAC,EAAE;EACtB,IAAIC,GAAG,GAAG,SAAS,IAAID,CAAC,GAAGA,CAAC,CAACE,OAAO,CAAC,CAAC,CAAC,GAAGF,CAAC;EAC3C,IAAIG,aAAa,GAAGC,QAAQ,CAACC,eAAe,CAACC,UAAU,IAAIF,QAAQ,CAACG,IAAI,CAACD,UAAU,IAAIE,MAAM,CAACC,WAAW;EACzG,IAAIC,aAAa,GAAGN,QAAQ,CAACC,eAAe,CAACM,SAAS,IAAIP,QAAQ,CAACG,IAAI,CAACI,SAAS,IAAIH,MAAM,CAACI,WAAW;EACvG,OAAO;IACLC,KAAK,EAAEZ,GAAG,CAACY,KAAK,GAAGV,aAAa;IAChCW,KAAK,EAAEb,GAAG,CAACa,KAAK,GAAGJ;EACrB,CAAC;AACH;AACA,SAASK,YAAYA,CAACC,KAAK,EAAE;EAC3B,IAAIC,MAAM,GAAGD,KAAK,CAACC,MAAM;IACvBC,SAAS,GAAGF,KAAK,CAACE,SAAS;IAC3BC,YAAY,GAAGH,KAAK,CAACG,YAAY;IACjCC,SAAS,GAAGJ,KAAK,CAACI,SAAS;IAC3BC,YAAY,GAAGL,KAAK,CAACK,YAAY;IACjCC,oBAAoB,GAAGN,KAAK,CAACM,oBAAoB;IACjDC,SAAS,GAAGP,KAAK,CAACO,SAAS;IAC3BC,KAAK,GAAGR,KAAK,CAACQ,KAAK;IACnBC,YAAY,GAAGT,KAAK,CAACS,YAAY;EACnC,IAAIC,SAAS,GAAG5B,QAAQ,CAACmB,MAAM,IAAI;MAC/BU,CAAC,EAAE,CAAC;MACJC,CAAC,EAAE;IACL,CAAC,CAAC;IACFC,UAAU,GAAGlC,cAAc,CAAC+B,SAAS,EAAE,CAAC,CAAC;IACzCI,WAAW,GAAGD,UAAU,CAAC,CAAC,CAAC;IAC3BE,cAAc,GAAGF,UAAU,CAAC,CAAC,CAAC;EAChC,IAAIG,YAAY,GAAGnC,MAAM,CAAC,IAAI,CAAC;EAC/B,IAAIoC,UAAU,GAAGpC,MAAM,CAAC,IAAI,CAAC;EAC7B,IAAIqC,OAAO,GAAGrC,MAAM,CAAC;IACnBsC,IAAI,EAAE;EACR,CAAC,CAAC;EACFvC,SAAS,CAAC,YAAY;IACpB,IAAIsC,OAAO,CAACE,OAAO,CAACD,IAAI,KAAK,KAAK,EAAE;MAClC,IAAIE,UAAU,GAAGd,SAAS,KAAK,IAAI,IAAIA,SAAS,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,SAAS,CAACJ,YAAY,CAAC;MAC9F,IAAIkB,UAAU,EAAE;QACdN,cAAc,CAACM,UAAU,CAAC;MAC5B;IACF;EACF,CAAC,EAAE,CAACb,KAAK,EAAEL,YAAY,CAAC,CAAC;EACzBvB,SAAS,CAAC,YAAY;IACpB,OAAO,YAAY;MACjBQ,QAAQ,CAACkC,mBAAmB,CAAC,WAAW,EAAEN,YAAY,CAACI,OAAO,CAAC;MAC/DhC,QAAQ,CAACkC,mBAAmB,CAAC,SAAS,EAAEL,UAAU,CAACG,OAAO,CAAC;MAC3DhC,QAAQ,CAACkC,mBAAmB,CAAC,WAAW,EAAEN,YAAY,CAACI,OAAO,CAAC;MAC/DhC,QAAQ,CAACkC,mBAAmB,CAAC,UAAU,EAAEL,UAAU,CAACG,OAAO,CAAC;MAC5DJ,YAAY,CAACI,OAAO,GAAG,IAAI;MAC3BH,UAAU,CAACG,OAAO,GAAG,IAAI;IAC3B,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EACN,IAAIG,YAAY,GAAG,SAASA,YAAYA,CAACvC,CAAC,EAAE;IAC1C,IAAIwC,YAAY,GAAGzC,WAAW,CAACC,CAAC,CAAC;MAC/Ba,KAAK,GAAG2B,YAAY,CAAC3B,KAAK;MAC1BC,KAAK,GAAG0B,YAAY,CAAC1B,KAAK;IAC5B,IAAI2B,qBAAqB,GAAGtB,YAAY,CAACiB,OAAO,CAACM,qBAAqB,CAAC,CAAC;MACtEC,KAAK,GAAGF,qBAAqB,CAACd,CAAC;MAC/BiB,KAAK,GAAGH,qBAAqB,CAACb,CAAC;MAC/BiB,KAAK,GAAGJ,qBAAqB,CAACI,KAAK;MACnCC,MAAM,GAAGL,qBAAqB,CAACK,MAAM;IACvC,IAAIC,qBAAqB,GAAG7B,SAAS,CAACkB,OAAO,CAACM,qBAAqB,CAAC,CAAC;MACnEM,WAAW,GAAGD,qBAAqB,CAACF,KAAK;MACzCI,YAAY,GAAGF,qBAAqB,CAACD,MAAM;IAC7C,IAAII,aAAa,GAAGF,WAAW,GAAG,CAAC;IACnC,IAAIG,aAAa,GAAGF,YAAY,GAAG,CAAC;IACpC,IAAIG,OAAO,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAED,IAAI,CAACE,GAAG,CAAC1C,KAAK,GAAG8B,KAAK,EAAEE,KAAK,CAAC,CAAC,GAAGK,aAAa;IACzE,IAAIM,OAAO,GAAGH,IAAI,CAACC,GAAG,CAAC,CAAC,EAAED,IAAI,CAACE,GAAG,CAACzC,KAAK,GAAG8B,KAAK,EAAEE,MAAM,CAAC,CAAC,GAAGK,aAAa;IAC1E,IAAId,UAAU,GAAG;MACfV,CAAC,EAAEyB,OAAO;MACVxB,CAAC,EAAER,SAAS,KAAK,GAAG,GAAGU,WAAW,CAACF,CAAC,GAAG4B;IACzC,CAAC;;IAED;IACA,IAAIR,WAAW,KAAK,CAAC,IAAIC,YAAY,KAAK,CAAC,IAAID,WAAW,KAAKC,YAAY,EAAE;MAC3E,OAAO,KAAK;IACd;IACAlB,cAAc,CAACM,UAAU,CAAC;IAC1BhB,YAAY,KAAK,IAAI,IAAIA,YAAY,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,YAAY,CAACgB,UAAU,CAAC;EACtF,CAAC;EACD,IAAIoB,UAAU,GAAG,SAASA,UAAUA,CAACzD,CAAC,EAAE;IACtCA,CAAC,CAAC0D,cAAc,CAAC,CAAC;IAClBnB,YAAY,CAACvC,CAAC,CAAC;EACjB,CAAC;EACD,IAAI2D,UAAU,GAAG,SAASA,UAAUA,CAAC3D,CAAC,EAAE;IACtCA,CAAC,CAAC0D,cAAc,CAAC,CAAC;IAClBxB,OAAO,CAACE,OAAO,CAACD,IAAI,GAAG,KAAK;IAC5B/B,QAAQ,CAACkC,mBAAmB,CAAC,WAAW,EAAEN,YAAY,CAACI,OAAO,CAAC;IAC/DhC,QAAQ,CAACkC,mBAAmB,CAAC,SAAS,EAAEL,UAAU,CAACG,OAAO,CAAC;IAC3DhC,QAAQ,CAACkC,mBAAmB,CAAC,WAAW,EAAEN,YAAY,CAACI,OAAO,CAAC;IAC/DhC,QAAQ,CAACkC,mBAAmB,CAAC,UAAU,EAAEL,UAAU,CAACG,OAAO,CAAC;IAC5DJ,YAAY,CAACI,OAAO,GAAG,IAAI;IAC3BH,UAAU,CAACG,OAAO,GAAG,IAAI;IACzBd,oBAAoB,KAAK,IAAI,IAAIA,oBAAoB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,oBAAoB,CAAC,CAAC;EACpG,CAAC;EACD,IAAIsC,WAAW,GAAG,SAASA,WAAWA,CAAC5D,CAAC,EAAE;IACxC;IACAI,QAAQ,CAACkC,mBAAmB,CAAC,WAAW,EAAEN,YAAY,CAACI,OAAO,CAAC;IAC/DhC,QAAQ,CAACkC,mBAAmB,CAAC,SAAS,EAAEL,UAAU,CAACG,OAAO,CAAC;IAC3D,IAAIX,YAAY,EAAE;MAChB;IACF;IACAc,YAAY,CAACvC,CAAC,CAAC;IACfkC,OAAO,CAACE,OAAO,CAACD,IAAI,GAAG,IAAI;IAC3B/B,QAAQ,CAACyD,gBAAgB,CAAC,WAAW,EAAEJ,UAAU,CAAC;IAClDrD,QAAQ,CAACyD,gBAAgB,CAAC,SAAS,EAAEF,UAAU,CAAC;IAChDvD,QAAQ,CAACyD,gBAAgB,CAAC,WAAW,EAAEJ,UAAU,CAAC;IAClDrD,QAAQ,CAACyD,gBAAgB,CAAC,UAAU,EAAEF,UAAU,CAAC;IACjD3B,YAAY,CAACI,OAAO,GAAGqB,UAAU;IACjCxB,UAAU,CAACG,OAAO,GAAGuB,UAAU;EACjC,CAAC;EACD,OAAO,CAAC7B,WAAW,EAAE8B,WAAW,CAAC;AACnC;AACA,eAAe7C,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}