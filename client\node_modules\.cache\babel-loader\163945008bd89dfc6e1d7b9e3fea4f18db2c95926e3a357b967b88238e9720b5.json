{"ast": null, "code": "import { useRef } from 'react';\nexport default (function (isScrollAtTop, isScrollAtBottom) {\n  // Do lock for a wheel when scrolling\n  var lockRef = useRef(false);\n  var lockTimeoutRef = useRef(null);\n  function lockScroll() {\n    clearTimeout(lockTimeoutRef.current);\n    lockRef.current = true;\n    lockTimeoutRef.current = setTimeout(function () {\n      lockRef.current = false;\n    }, 50);\n  }\n  // Pass to ref since global add is in closure\n  var scrollPingRef = useRef({\n    top: isScrollAtTop,\n    bottom: isScrollAtBottom\n  });\n  scrollPingRef.current.top = isScrollAtTop;\n  scrollPingRef.current.bottom = isScrollAtBottom;\n  return function (deltaY) {\n    var smoothOffset = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;\n    var originScroll =\n    // Pass origin wheel when on the top\n    deltaY < 0 && scrollPingRef.current.top ||\n    // Pass origin wheel when on the bottom\n    deltaY > 0 && scrollPingRef.current.bottom;\n    if (smoothOffset && originScroll) {\n      // No need lock anymore when it's smooth offset from touchMove interval\n      clearTimeout(lockTimeoutRef.current);\n      lockRef.current = false;\n    } else if (!originScroll || lockRef.current) {\n      lockScroll();\n    }\n    return !lockRef.current && originScroll;\n  };\n});", "map": {"version": 3, "names": ["useRef", "isScrollAtTop", "isScrollAtBottom", "lockRef", "lockTimeoutRef", "lockScroll", "clearTimeout", "current", "setTimeout", "scrollPingRef", "top", "bottom", "deltaY", "smoothOffset", "arguments", "length", "undefined", "originScroll"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/node_modules/rc-virtual-list/es/hooks/useOriginScroll.js"], "sourcesContent": ["import { useRef } from 'react';\nexport default (function (isScrollAtTop, isScrollAtBottom) {\n  // Do lock for a wheel when scrolling\n  var lockRef = useRef(false);\n  var lockTimeoutRef = useRef(null);\n  function lockScroll() {\n    clearTimeout(lockTimeoutRef.current);\n    lockRef.current = true;\n    lockTimeoutRef.current = setTimeout(function () {\n      lockRef.current = false;\n    }, 50);\n  }\n  // Pass to ref since global add is in closure\n  var scrollPingRef = useRef({\n    top: isScrollAtTop,\n    bottom: isScrollAtBottom\n  });\n  scrollPingRef.current.top = isScrollAtTop;\n  scrollPingRef.current.bottom = isScrollAtBottom;\n  return function (deltaY) {\n    var smoothOffset = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;\n    var originScroll =\n    // Pass origin wheel when on the top\n    deltaY < 0 && scrollPingRef.current.top ||\n    // Pass origin wheel when on the bottom\n    deltaY > 0 && scrollPingRef.current.bottom;\n    if (smoothOffset && originScroll) {\n      // No need lock anymore when it's smooth offset from touchMove interval\n      clearTimeout(lockTimeoutRef.current);\n      lockRef.current = false;\n    } else if (!originScroll || lockRef.current) {\n      lockScroll();\n    }\n    return !lockRef.current && originScroll;\n  };\n});"], "mappings": "AAAA,SAASA,MAAM,QAAQ,OAAO;AAC9B,gBAAgB,UAAUC,aAAa,EAAEC,gBAAgB,EAAE;EACzD;EACA,IAAIC,OAAO,GAAGH,MAAM,CAAC,KAAK,CAAC;EAC3B,IAAII,cAAc,GAAGJ,MAAM,CAAC,IAAI,CAAC;EACjC,SAASK,UAAUA,CAAA,EAAG;IACpBC,YAAY,CAACF,cAAc,CAACG,OAAO,CAAC;IACpCJ,OAAO,CAACI,OAAO,GAAG,IAAI;IACtBH,cAAc,CAACG,OAAO,GAAGC,UAAU,CAAC,YAAY;MAC9CL,OAAO,CAACI,OAAO,GAAG,KAAK;IACzB,CAAC,EAAE,EAAE,CAAC;EACR;EACA;EACA,IAAIE,aAAa,GAAGT,MAAM,CAAC;IACzBU,GAAG,EAAET,aAAa;IAClBU,MAAM,EAAET;EACV,CAAC,CAAC;EACFO,aAAa,CAACF,OAAO,CAACG,GAAG,GAAGT,aAAa;EACzCQ,aAAa,CAACF,OAAO,CAACI,MAAM,GAAGT,gBAAgB;EAC/C,OAAO,UAAUU,MAAM,EAAE;IACvB,IAAIC,YAAY,GAAGC,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKE,SAAS,GAAGF,SAAS,CAAC,CAAC,CAAC,GAAG,KAAK;IAC5F,IAAIG,YAAY;IAChB;IACAL,MAAM,GAAG,CAAC,IAAIH,aAAa,CAACF,OAAO,CAACG,GAAG;IACvC;IACAE,MAAM,GAAG,CAAC,IAAIH,aAAa,CAACF,OAAO,CAACI,MAAM;IAC1C,IAAIE,YAAY,IAAII,YAAY,EAAE;MAChC;MACAX,YAAY,CAACF,cAAc,CAACG,OAAO,CAAC;MACpCJ,OAAO,CAACI,OAAO,GAAG,KAAK;IACzB,CAAC,MAAM,IAAI,CAACU,YAAY,IAAId,OAAO,CAACI,OAAO,EAAE;MAC3CF,UAAU,CAAC,CAAC;IACd;IACA,OAAO,CAACF,OAAO,CAACI,OAAO,IAAIU,YAAY;EACzC,CAAC;AACH,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}