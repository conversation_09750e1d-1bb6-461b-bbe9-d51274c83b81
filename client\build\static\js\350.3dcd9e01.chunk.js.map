{"version": 3, "file": "static/js/350.3dcd9e01.chunk.js", "mappings": "uKAKA,MAuCA,EAvCaA,KACT,MAAOC,EAAUC,IAAeC,EAAAA,EAAAA,UAAS,KAClCC,EAASC,IAAcF,EAAAA,EAAAA,WAAS,GA0BvC,OAvBAG,EAAAA,EAAAA,YAAU,KAkBFC,aAAaC,QAAQ,UAjBLC,WAChB,IACI,MAAMC,QAAiBC,EAAAA,EAAAA,MACnBD,EAASE,QACLF,EAASG,KAAKT,QACdC,GAAW,IAEXA,GAAW,GACXH,EAAYQ,EAASG,OAGzBC,EAAAA,GAAQC,MAAML,EAASI,QAE/B,CAAE,MAAOC,GACLD,EAAAA,GAAQC,MAAMA,EAAMD,QACxB,GAGAE,EACJ,GACD,KAICC,EAAAA,EAAAA,MAAA,OAAKC,UAAU,GAAEC,SAAA,EACbC,EAAAA,EAAAA,KAAA,OAAAD,SAAMlB,EAASoB,QACfD,EAAAA,EAAAA,KAAA,OAAAD,SAAMlB,EAASqB,UACfF,EAAAA,EAAAA,KAAA,OAAAD,SAAMlB,EAASsB,UAEnB,C", "sources": ["pages/user/Test/index.js"], "sourcesContent": ["import React, { useEffect, useState, Suspense } from \"react\";\r\nimport './index.css'\r\nimport { getUserInfo } from \"../../../apicalls/users\";\r\n\r\nimport { message } from \"antd\";\r\nconst Test = () => {\r\n    const [userData, setUserData] = useState('');\r\n    const [isAdmin, setIsAdmin] = useState(false);\r\n\r\n\r\n    useEffect(() => {\r\n        const getUserData = async () => {\r\n            try {\r\n                const response = await getUserInfo();\r\n                if (response.success) {\r\n                    if (response.data.isAdmin) {\r\n                        setIsAdmin(true);\r\n                    } else {\r\n                        setIsAdmin(false);\r\n                        setUserData(response.data);\r\n                    }\r\n                } else {\r\n                    message.error(response.message);\r\n                }\r\n            } catch (error) {\r\n                message.error(error.message);\r\n            }\r\n        };\r\n        if (localStorage.getItem(\"token\")) {\r\n            getUserData();\r\n        }\r\n    }, []);\r\n\r\n    return (\r\n        // <Suspense fallback={<div>Loading...</div>}>\r\n        <div className=\"\">\r\n            <div>{userData.name}</div>\r\n            <div>{userData.school}</div>\r\n            <div>{userData.class}</div>\r\n        </div>\r\n        // </Suspense>\r\n    );\r\n}\r\n\r\nexport default Test;"], "names": ["Test", "userData", "setUserData", "useState", "isAdmin", "setIsAdmin", "useEffect", "localStorage", "getItem", "async", "response", "getUserInfo", "success", "data", "message", "error", "getUserData", "_jsxs", "className", "children", "_jsx", "name", "school", "class"], "sourceRoot": ""}