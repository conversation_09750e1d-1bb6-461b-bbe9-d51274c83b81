{"version": 3, "file": "static/js/84.4d9a297c.chunk.js", "mappings": "sMAGO,MA8DMA,EAAuBC,eAAOC,GAA6B,IAAtBC,EAASC,UAAAC,OAAA,QAAAC,IAAAF,UAAA,GAAAA,UAAA,GAAG,KAC5D,IACE,MAAMG,EAAcJ,EAAS,UAAAK,OAAaL,GAAc,GAExD,aADuBM,EAAAA,QAAcC,IAAI,0BAADF,OAA2BN,GAAKM,OAAGD,KAC3DI,IAClB,CAAE,MAAOC,GACP,OAAOA,EAAMC,SAASF,IACxB,CACF,ECeaG,EAAsBb,eAAOC,GAA6B,IAAtBC,EAASC,UAAAC,OAAA,QAAAC,IAAAF,UAAA,GAAAA,UAAA,GAAG,KAC3D,IACEW,QAAQC,IAAI,6CAADR,OAAoCN,EAAK,aAAAM,OAAYL,IAGhE,MAAMI,EAAcJ,EAAS,UAAAK,OAAaL,GAAc,GACxDY,QAAQC,IAAI,iEAADR,OAAwDN,GAAKM,OAAGD,IAE3E,MAAMU,QAAyBR,EAAAA,QAAcC,IAAI,0BAADF,OAA2BN,GAAKM,OAAGD,IAGnF,GAFAQ,QAAQC,IAAI,kCAAyBC,EAAiBN,MAElDM,EAAiBN,KAAKO,SAAWD,EAAiBN,KAAKA,KAAKN,OAAS,EAEvE,OADAU,QAAQC,IAAI,4CAADR,OAAwCN,EAAK,KAAKe,EAAiBN,KAAKA,MAC5EM,EAAiBN,KAI1BI,QAAQC,IAAI,mFAADR,OAA0EN,IACrF,MAAMiB,QAAyBV,EAAAA,QAAcC,IAAI,8BAADF,OAA+BN,IAE/E,OADAa,QAAQC,IAAI,kCAAyBG,EAAiBR,MAC/CQ,EAAiBR,IAC1B,CAAE,MAAOC,GAAQ,IAADQ,EACdL,QAAQH,MAAM,kCAA8BA,GAC5CG,QAAQH,MAAM,iBAAgC,QAAhBQ,EAAER,EAAMC,gBAAQ,IAAAO,OAAA,EAAdA,EAAgBT,MAGhD,IACEI,QAAQC,IAAI,gDACZ,MAAMG,QAAyBV,EAAAA,QAAcC,IAAI,8BAADF,OAA+BN,IAE/E,OADAa,QAAQC,IAAI,kCAAyBG,EAAiBR,MAC/CQ,EAAiBR,IAC1B,CAAE,MAAOU,GAEP,OADAN,QAAQH,MAAM,+BAA2BS,GAClC,CAAEH,SAAS,EAAOI,QAAS,2BAA4BX,KAAM,GACtE,CACF,CACF,E,aCvHA,MAAM,KAAEY,EAAI,MAAEC,GAAUC,EAAAA,QAsRxB,EApRkBC,KAChB,MAAOC,EAAUC,IAAeC,EAAAA,EAAAA,UAAS,CAAC,IACnCC,EAAaC,IAAkBF,EAAAA,EAAAA,UAAS,CAAC,IACzCG,EAASC,IAAcJ,EAAAA,EAAAA,WAAS,IAEvCK,EAAAA,EAAAA,YAAU,KACRC,GAAe,GACd,IAEH,MAAMA,EAAgBA,KACpB,MAAMC,EAAQC,aAAaC,QAAQ,SAC7BC,EAAOF,aAAaC,QAAQ,QAElC,IAAIE,EAAY,CAAC,EACjB,GAAIJ,EACF,IACE,MAAMK,EAAUC,KAAKC,MAAMC,KAAKR,EAAMS,MAAM,KAAK,KACjDL,EAAY,CACVM,OAAQL,EAAQK,OAChBC,IAAKN,EAAQM,IACbC,IAAKP,EAAQO,IACbC,UAAWR,EAAQM,IAAMG,KAAKC,MAAQ,IAE1C,CAAE,MAAOC,GACPZ,EAAY,CAAE5B,MAAO,uBACvB,CAGFgB,EAAY,CACVyB,WAAYjB,EACZkB,UAAWf,EACXH,MAAOA,EAAK,GAAA5B,OAAM4B,EAAMmB,UAAU,EAAG,IAAG,OAAQ,KAChDhB,KAAMA,EAAOG,KAAKC,MAAMJ,GAAQ,KAChCC,aACA,EAkFJ,OACEgB,EAAAA,EAAAA,MAAA,OAAKC,MAAO,CAAEC,QAAS,OAAQC,SAAU,QAASC,OAAQ,UAAWC,SAAA,EACnEC,EAAAA,EAAAA,KAACtC,EAAK,CAACtB,MAAO,EAAE2D,SAAC,6CAGjBL,EAAAA,EAAAA,MAACO,EAAAA,EAAI,CAACC,MAAM,qCAA2BP,MAAO,CAAEQ,aAAc,QAASJ,SAAA,EACrEL,EAAAA,EAAAA,MAACU,EAAAA,EAAK,CAACC,UAAU,WAAWV,MAAO,CAAEW,MAAO,QAASP,SAAA,EACnDL,EAAAA,EAAAA,MAACjC,EAAI,CAAAsC,SAAA,EAACC,EAAAA,EAAAA,KAAA,UAAAD,SAAQ,eAAmB,IAAElC,EAAS0B,SAAW,aAAU,gBACjEG,EAAAA,EAAAA,MAACjC,EAAI,CAAAsC,SAAA,EAACC,EAAAA,EAAAA,KAAA,UAAAD,SAAQ,cAAkB,IAAElC,EAAS2B,QAAU,aAAU,eAE9D3B,EAASS,QACRoB,EAAAA,EAAAA,MAACjC,EAAI,CAAAsC,SAAA,EAACC,EAAAA,EAAAA,KAAA,UAAAD,SAAQ,mBAAuB,IAAElC,EAASS,SAGjDT,EAASY,OACRiB,EAAAA,EAAAA,MAAA,OAAAK,SAAA,EACEL,EAAAA,EAAAA,MAACjC,EAAI,CAAAsC,SAAA,EAACC,EAAAA,EAAAA,KAAA,UAAAD,SAAQ,UAAc,IAAElC,EAASY,KAAK8B,KAAK,KAAG1C,EAASY,KAAK+B,MAAM,QACxER,EAAAA,EAAAA,KAAA,UACAN,EAAAA,EAAAA,MAACjC,EAAI,CAAAsC,SAAA,EAACC,EAAAA,EAAAA,KAAA,UAAAD,SAAQ,cAAkB,IAAElC,EAASY,KAAKgC,QAAU,aAAU,kBAIvE5C,EAASa,YACRgB,EAAAA,EAAAA,MAAA,OAAAK,SAAA,EACEC,EAAAA,EAAAA,KAACvC,EAAI,CAAAsC,UAACC,EAAAA,EAAAA,KAAA,UAAAD,SAAQ,mBACdC,EAAAA,EAAAA,KAAA,UACAN,EAAAA,EAAAA,MAACjC,EAAI,CAAAsC,SAAA,CAAC,YAAUlC,EAASa,UAAUM,WACnCgB,EAAAA,EAAAA,KAAA,UACAN,EAAAA,EAAAA,MAACjC,EAAI,CAAAsC,SAAA,CAAC,YAAUlC,EAASa,UAAUO,IAAM,IAAIG,KAA8B,IAAzBvB,EAASa,UAAUO,KAAYyB,iBAAmB,UACpGV,EAAAA,EAAAA,KAAA,UACAN,EAAAA,EAAAA,MAACjC,EAAI,CAAAsC,SAAA,CAAC,eAAalC,EAASa,UAAUS,UAAY,aAAU,eAC3DtB,EAASa,UAAU5B,QAClB4C,EAAAA,EAAAA,MAAAiB,EAAAA,SAAA,CAAAZ,SAAA,EACEC,EAAAA,EAAAA,KAAA,UACAN,EAAAA,EAAAA,MAACjC,EAAI,CAACmD,KAAK,SAAQb,SAAA,CAAC,UAAQlC,EAASa,UAAU5B,mBAOzDkD,EAAAA,EAAAA,KAAA,OAAKL,MAAO,CAAEkB,UAAW,QAASd,UAChCL,EAAAA,EAAAA,MAACU,EAAAA,EAAK,CAAAL,SAAA,EACJC,EAAAA,EAAAA,KAACc,EAAAA,GAAM,CAACC,QAhDEC,KAClB3C,IACAb,EAAAA,GAAQyD,KAAK,gCAAgC,EA8CRlB,SAAC,aAC9BC,EAAAA,EAAAA,KAACc,EAAAA,GAAM,CAACC,QAxDAG,KAChB3C,aAAa4C,WAAW,SACxB5C,aAAa4C,WAAW,QACxB3D,EAAAA,GAAQyD,KAAK,0BACb5C,GAAe,EAoDqB+C,QAAM,EAAArB,SAAC,wBAMzCC,EAAAA,EAAAA,KAACC,EAAAA,EAAI,CAACC,MAAM,gCAAsBP,MAAO,CAAEQ,aAAc,QAASJ,UAChEL,EAAAA,EAAAA,MAACU,EAAAA,EAAK,CAACC,UAAU,WAAWV,MAAO,CAAEW,MAAO,QAASP,SAAA,EACnDC,EAAAA,EAAAA,KAACc,EAAAA,GAAM,CACLF,KAAK,UACLG,QArIc5E,UACtBgC,GAAW,GACX,MAAMkD,EAAU,CAAC,EAEjB,IAAK,IAADC,EACFrE,QAAQC,IAAI,4CACZ,MAAMC,QFhCoBhB,iBAAyB,IAAlBoF,EAAOjF,UAAAC,OAAA,QAAAC,IAAAF,UAAA,GAAAA,UAAA,GAAG,CAAC,EAChD,IACE,MAAMG,EAAc,IAAI+E,gBAQxB,OAPAC,OAAOC,KAAKH,GAASI,SAAQC,IACvBL,EAAQK,IACVnF,EAAYoF,OAAOD,EAAKL,EAAQK,GAClC,WAGqBjF,EAAAA,QAAcC,IAAI,iBAADF,OAAkBD,EAAYqF,cACtDjF,IAClB,CAAE,MAAOC,GACP,OAAOA,EAAMC,SAASF,IACxB,CACF,CEkBqCkF,GAC/BV,EAAQU,iBAAmB,CACzB3E,QAASD,EAAiBC,QAC1B4E,YAAiC,QAArBV,EAAAnE,EAAiBN,YAAI,IAAAyE,OAAA,EAArBA,EAAuB/E,SAAU,EAC7CiB,QAASL,EAAiBK,QAC1BV,MAAOK,EAAiBC,QAAU,KAAOD,EAAiBK,SAE5DP,QAAQC,IAAI,kCAAyBC,EACvC,CAAE,MAAOL,GAAQ,IAADQ,EACd+D,EAAQU,iBAAmB,CACzB3E,SAAS,EACTN,MAAOA,EAAMU,QACbyE,OAAsB,QAAhB3E,EAAER,EAAMC,gBAAQ,IAAAO,OAAA,EAAdA,EAAgB2E,QAE1BhF,QAAQH,MAAM,yBAAqBA,EACrC,CAEA,IAAK,IAADoF,EACFjF,QAAQC,IAAI,gDACZ,MAAMiF,QAAyBjG,EAAqB,WACpDmF,EAAQnF,qBAAuB,CAC7BkB,QAAS+E,EAAiB/E,QAC1B4E,YAAiC,QAArBE,EAAAC,EAAiBtF,YAAI,IAAAqF,OAAA,EAArBA,EAAuB3F,SAAU,EAC7CM,KAAMsF,EAAiBtF,KACvBW,QAAS2E,EAAiB3E,QAC1BV,MAAOqF,EAAiB/E,QAAU,KAAO+E,EAAiB3E,SAE5DP,QAAQC,IAAI,kCAAyBiF,EACvC,CAAE,MAAOrF,GAAQ,IAADsF,EACdf,EAAQnF,qBAAuB,CAC7BkB,SAAS,EACTN,MAAOA,EAAMU,QACbyE,OAAsB,QAAhBG,EAAEtF,EAAMC,gBAAQ,IAAAqF,OAAA,EAAdA,EAAgBH,QAE1BhF,QAAQH,MAAM,yBAAqBA,EACrC,CAEA,IAAK,IAADuF,EACFpF,QAAQC,IAAI,oDACZ,MAAMoF,QAA2BtF,EAAoB,WACrDqE,EAAQrE,oBAAsB,CAC5BI,QAASkF,EAAmBlF,QAC5B4E,YAAmC,QAAvBK,EAAAC,EAAmBzF,YAAI,IAAAwF,OAAA,EAAvBA,EAAyB9F,SAAU,EAC/CM,KAAMyF,EAAmBzF,KACzBW,QAAS8E,EAAmB9E,QAC5BV,MAAOwF,EAAmBlF,QAAU,KAAOkF,EAAmB9E,SAEhEP,QAAQC,IAAI,qCAA4BoF,EAC1C,CAAE,MAAOxF,GAAQ,IAADyF,EACdlB,EAAQrE,oBAAsB,CAC5BI,SAAS,EACTN,MAAOA,EAAMU,QACbyE,OAAsB,QAAhBM,EAAEzF,EAAMC,gBAAQ,IAAAwF,OAAA,EAAdA,EAAgBN,QAE1BhF,QAAQH,MAAM,4BAAwBA,EACxC,CAEAmB,EAAeoD,GACflD,GAAW,EAAM,EAsETD,QAASA,EACTsE,UAAW3E,EAAS0B,SAASQ,SAC9B,uBAIA0B,OAAOC,KAAK1D,GAAazB,OAAS,IACjCmD,EAAAA,EAAAA,MAAA,OAAAK,SAAA,EACEC,EAAAA,EAAAA,KAACtC,EAAK,CAACtB,MAAO,EAAE2D,SAAC,kBAEhB0B,OAAOgB,QAAQzE,GAAa0E,KAAIC,IAAA,IAAEC,EAAUC,GAAOF,EAAA,OAClDjD,EAAAA,EAAAA,MAACO,EAAAA,EAAI,CAEH6C,KAAK,QACL5C,MAAO0C,EACPjD,MAAO,CAAEQ,aAAc,QAASJ,SAAA,EAEhCL,EAAAA,EAAAA,MAACjC,EAAI,CAAAsC,SAAA,EAACC,EAAAA,EAAAA,KAAA,UAAAD,SAAQ,aAAiB,IAAE8C,EAAOzF,QAAU,aAAU,gBAC5D4C,EAAAA,EAAAA,KAAA,cACuBxD,IAAtBqG,EAAOb,aACNtC,EAAAA,EAAAA,MAAAiB,EAAAA,SAAA,CAAAZ,SAAA,EACEL,EAAAA,EAAAA,MAACjC,EAAI,CAAAsC,SAAA,EAACC,EAAAA,EAAAA,KAAA,UAAAD,SAAQ,iBAAqB,IAAE8C,EAAOb,eAC5ChC,EAAAA,EAAAA,KAAA,YAGH6C,EAAOhG,OACN6C,EAAAA,EAAAA,MAAAiB,EAAAA,SAAA,CAAAZ,SAAA,EACEL,EAAAA,EAAAA,MAACjC,EAAI,CAAAsC,SAAA,EAACC,EAAAA,EAAAA,KAAA,UAAAD,SAAQ,UAAc,IAAEnB,KAAKmE,UAAUF,EAAOhG,UACpDmD,EAAAA,EAAAA,KAAA,YAGH6C,EAAOrF,UACNkC,EAAAA,EAAAA,MAAAiB,EAAAA,SAAA,CAAAZ,SAAA,EACEL,EAAAA,EAAAA,MAACjC,EAAI,CAAAsC,SAAA,EAACC,EAAAA,EAAAA,KAAA,UAAAD,SAAQ,aAAiB,IAAE8C,EAAOrF,YACxCwC,EAAAA,EAAAA,KAAA,YAGH6C,EAAO/F,QACN4C,EAAAA,EAAAA,MAAAiB,EAAAA,SAAA,CAAAZ,SAAA,EACEL,EAAAA,EAAAA,MAACjC,EAAI,CAACmD,KAAK,SAAQb,SAAA,EAACC,EAAAA,EAAAA,KAAA,UAAAD,SAAQ,WAAe,IAAE8C,EAAO/F,UACpDkD,EAAAA,EAAAA,KAAA,YAGH6C,EAAOZ,SACNvC,EAAAA,EAAAA,MAAAiB,EAAAA,SAAA,CAAAZ,SAAA,EACEL,EAAAA,EAAAA,MAACjC,EAAI,CAAAsC,SAAA,EAACC,EAAAA,EAAAA,KAAA,UAAAD,SAAQ,YAAgB,IAAE8C,EAAOZ,WACvCjC,EAAAA,EAAAA,KAAA,cAlCC4C,EAqCA,QAMb5C,EAAAA,EAAAA,KAAA,OAAKL,MAAO,CAAEkB,UAAW,QAASd,UAChCL,EAAAA,EAAAA,MAACU,EAAAA,EAAK,CAAAL,SAAA,EACJC,EAAAA,EAAAA,KAACc,EAAAA,GAAM,CACLC,QAAS5E,UACP,IACE,MAAMY,QAAiBC,EAAoB,WAC3CC,QAAQC,IAAI,iCAAkCH,GAC9CS,EAAAA,GAAQyD,KAAK,qBAADvE,OAAsBkC,KAAKmE,UAAUhG,EAASF,OAC5D,CAAE,MAAOC,GACPG,QAAQH,MAAM,oBAAqBA,GACnCU,EAAAA,GAAQV,MAAM,UAADJ,OAAWI,EAAMU,SAChC,GACAuC,SACH,kCAIDC,EAAAA,EAAAA,KAACc,EAAAA,GAAM,CACLC,QAAS5E,UACP,IACE,MAAMY,QAAiBb,EAAqB,WAC5Ce,QAAQC,IAAI,mCAAoCH,GAChDS,EAAAA,GAAQyD,KAAK,uBAADvE,OAAwBkC,KAAKmE,UAAUhG,EAASF,OAC9D,CAAE,MAAOC,GACPG,QAAQH,MAAM,oBAAqBA,GACnCU,EAAAA,GAAQV,MAAM,UAADJ,OAAWI,EAAMU,SAChC,GACAuC,SACH,6CASTC,EAAAA,EAAAA,KAACC,EAAAA,EAAI,CAACC,MAAM,kCAAuBH,UACjCL,EAAAA,EAAAA,MAACU,EAAAA,EAAK,CAACC,UAAU,WAAUN,SAAA,EACzBC,EAAAA,EAAAA,KAACvC,EAAI,CAAAsC,SAAC,uDACNC,EAAAA,EAAAA,KAACvC,EAAI,CAAAsC,SAAC,wCACNC,EAAAA,EAAAA,KAACvC,EAAI,CAAAsC,SAAC,0DACNC,EAAAA,EAAAA,KAACvC,EAAI,CAAAsC,SAAC,sDACNC,EAAAA,EAAAA,KAACvC,EAAI,CAAAsC,SAAC,uEAGN,C", "sources": ["apicalls/syllabus.js", "apicalls/aiQuestions.js", "components/DebugAuth.jsx"], "sourcesContent": ["import axiosInstance from \"./index\";\n\n// Upload syllabus PDF\nexport const uploadSyllabus = async (formData) => {\n  try {\n    const response = await axiosInstance.post(\"/api/syllabus/upload\", formData, {\n      headers: {\n        \"Content-Type\": \"multipart/form-data\",\n      },\n      timeout: 300000, // 5 minutes timeout for file upload\n    });\n    return response.data;\n  } catch (error) {\n    return error.response.data;\n  }\n};\n\n// Get all syllabuses\nexport const getAllSyllabuses = async (filters = {}) => {\n  try {\n    const queryParams = new URLSearchParams();\n    Object.keys(filters).forEach(key => {\n      if (filters[key]) {\n        queryParams.append(key, filters[key]);\n      }\n    });\n    \n    const response = await axiosInstance.get(`/api/syllabus?${queryParams.toString()}`);\n    return response.data;\n  } catch (error) {\n    return error.response.data;\n  }\n};\n\n// Get syllabus by ID\nexport const getSyllabusById = async (id) => {\n  try {\n    const response = await axiosInstance.get(`/api/syllabus/${id}`);\n    return response.data;\n  } catch (error) {\n    return error.response.data;\n  }\n};\n\n// Update syllabus\nexport const updateSyllabus = async (id, data) => {\n  try {\n    const response = await axiosInstance.put(`/api/syllabus/${id}`, data);\n    return response.data;\n  } catch (error) {\n    return error.response.data;\n  }\n};\n\n// Delete syllabus\nexport const deleteSyllabus = async (id) => {\n  try {\n    const response = await axiosInstance.delete(`/api/syllabus/${id}`);\n    return response.data;\n  } catch (error) {\n    return error.response.data;\n  }\n};\n\n// Get available subjects for a level\nexport const getAvailableSubjects = async (level, className = null) => {\n  try {\n    const queryParams = className ? `?class=${className}` : '';\n    const response = await axiosInstance.get(`/api/syllabus/subjects/${level}${queryParams}`);\n    return response.data;\n  } catch (error) {\n    return error.response.data;\n  }\n};\n\n// Get syllabus content for AI generation\nexport const getSyllabusForAI = async (level, className, subject) => {\n  try {\n    const response = await axiosInstance.get(`/api/syllabus/ai-content/${level}/${className}/${subject}`);\n    return response.data;\n  } catch (error) {\n    return error.response.data;\n  }\n};\n\n// Approve syllabus\nexport const approveSyllabus = async (id, approvalData) => {\n  try {\n    const response = await axiosInstance.put(`/api/syllabus/${id}`, {\n      ...approvalData,\n      approvalDate: new Date(),\n    });\n    return response.data;\n  } catch (error) {\n    return error.response.data;\n  }\n};\n\n// Get syllabus statistics\nexport const getSyllabusStats = async () => {\n  try {\n    const response = await axiosInstance.get(\"/api/syllabus/stats\");\n    return response.data;\n  } catch (error) {\n    return error.response.data;\n  }\n};\n\n// Reprocess syllabus (re-extract content)\nexport const reprocessSyllabus = async (id) => {\n  try {\n    const response = await axiosInstance.post(`/api/syllabus/${id}/reprocess`);\n    return response.data;\n  } catch (error) {\n    return error.response.data;\n  }\n};\n\n// Download syllabus file\nexport const downloadSyllabus = async (id) => {\n  try {\n    const response = await axiosInstance.get(`/api/syllabus/${id}/download`, {\n      responseType: 'blob',\n    });\n    return response;\n  } catch (error) {\n    return error.response;\n  }\n};\n\n// Search syllabuses\nexport const searchSyllabuses = async (searchTerm, filters = {}) => {\n  try {\n    const queryParams = new URLSearchParams();\n    queryParams.append('search', searchTerm);\n    \n    Object.keys(filters).forEach(key => {\n      if (filters[key]) {\n        queryParams.append(key, filters[key]);\n      }\n    });\n    \n    const response = await axiosInstance.get(`/api/syllabus/search?${queryParams.toString()}`);\n    return response.data;\n  } catch (error) {\n    return error.response.data;\n  }\n};\n\n// Get syllabus versions\nexport const getSyllabusVersions = async (level, className, subject) => {\n  try {\n    const response = await axiosInstance.get(`/api/syllabus/versions/${level}/${className}/${subject}`);\n    return response.data;\n  } catch (error) {\n    return error.response.data;\n  }\n};\n\n// Validate syllabus content\nexport const validateSyllabusContent = async (id) => {\n  try {\n    const response = await axiosInstance.post(`/api/syllabus/${id}/validate`);\n    return response.data;\n  } catch (error) {\n    return error.response.data;\n  }\n};\n\n// Get syllabus usage analytics\nexport const getSyllabusUsageAnalytics = async (id) => {\n  try {\n    const response = await axiosInstance.get(`/api/syllabus/${id}/analytics`);\n    return response.data;\n  } catch (error) {\n    return error.response.data;\n  }\n};\n\n// Bulk upload syllabuses\nexport const bulkUploadSyllabuses = async (files, metadata) => {\n  try {\n    const formData = new FormData();\n    \n    files.forEach((file, index) => {\n      formData.append(`files`, file);\n      formData.append(`metadata_${index}`, JSON.stringify(metadata[index]));\n    });\n    \n    const response = await axiosInstance.post(\"/api/syllabus/bulk-upload\", formData, {\n      headers: {\n        \"Content-Type\": \"multipart/form-data\",\n      },\n      timeout: 600000, // 10 minutes timeout for bulk upload\n    });\n    return response.data;\n  } catch (error) {\n    return error.response.data;\n  }\n};\n\n// Export syllabus data\nexport const exportSyllabusData = async (format = 'json', filters = {}) => {\n  try {\n    const queryParams = new URLSearchParams();\n    queryParams.append('format', format);\n    \n    Object.keys(filters).forEach(key => {\n      if (filters[key]) {\n        queryParams.append(key, filters[key]);\n      }\n    });\n    \n    const response = await axiosInstance.get(`/api/syllabus/export?${queryParams.toString()}`, {\n      responseType: 'blob',\n    });\n    return response;\n  } catch (error) {\n    return error.response;\n  }\n};\n\n// Get syllabus processing status\nexport const getSyllabusProcessingStatus = async (id) => {\n  try {\n    const response = await axiosInstance.get(`/api/syllabus/${id}/status`);\n    return response.data;\n  } catch (error) {\n    return error.response.data;\n  }\n};\n\n// Update syllabus tags\nexport const updateSyllabusTags = async (id, tags) => {\n  try {\n    const response = await axiosInstance.put(`/api/syllabus/${id}/tags`, { tags });\n    return response.data;\n  } catch (error) {\n    return error.response.data;\n  }\n};\n\n// Get popular syllabus topics\nexport const getPopularSyllabusTopics = async (level, subject = null) => {\n  try {\n    const queryParams = subject ? `?subject=${subject}` : '';\n    const response = await axiosInstance.get(`/api/syllabus/popular-topics/${level}${queryParams}`);\n    return response.data;\n  } catch (error) {\n    return error.response.data;\n  }\n};\n\n// Get syllabuses for AI question generation selection\nexport const getSyllabusesForAI = async (level = null, subject = null, className = null) => {\n  try {\n    const queryParams = new URLSearchParams();\n    if (level) queryParams.append('level', level);\n    if (subject) queryParams.append('subject', subject);\n    if (className) queryParams.append('class', className);\n    queryParams.append('isActive', 'true');\n\n    const response = await axiosInstance.get(`/api/syllabus?${queryParams.toString()}`);\n    return response.data;\n  } catch (error) {\n    return error.response.data;\n  }\n};\n", "import axiosInstance from \"./index\";\n\n// Generate questions using AI\nexport const generateQuestions = async (payload) => {\n  try {\n    console.log(\"🔗 Making API call to generate questions...\");\n    const response = await axiosInstance.post(\"/api/ai-questions/generate-questions\", payload, {\n      timeout: 600000, // 10 minutes timeout specifically for AI generation\n    });\n    console.log(\"✅ API call successful:\", response.data);\n    return response.data;\n  } catch (error) {\n    console.error(\"❌ API call failed:\", error);\n\n    if (error.response) {\n      console.error(\"📊 Error response:\", error.response.data);\n      return error.response.data;\n    } else if (error.request) {\n      console.error(\"📡 Network error:\", error.request);\n      return { success: false, message: \"Network error - please check your connection\" };\n    } else {\n      console.error(\"⚠️ Request setup error:\", error.message);\n      return { success: false, message: error.message };\n    }\n  }\n};\n\n// Get generation history\nexport const getGenerationHistory = async (params = {}) => {\n  try {\n    const response = await axiosInstance.get(\"/api/ai-questions/generation-history\", { params });\n    return response.data;\n  } catch (error) {\n    return error.response.data;\n  }\n};\n\n// Get specific generation details\nexport const getGenerationDetails = async (generationId) => {\n  try {\n    const response = await axiosInstance.get(`/api/ai-questions/generation/${generationId}`);\n    return response.data;\n  } catch (error) {\n    return error.response.data;\n  }\n};\n\n// Approve generated questions\nexport const approveQuestions = async (payload) => {\n  try {\n    const response = await axiosInstance.post(\"/api/ai-questions/approve-questions\", payload);\n    return response.data;\n  } catch (error) {\n    return error.response.data;\n  }\n};\n\n// Preview generated questions\nexport const previewQuestions = async (generationId) => {\n  try {\n    const response = await axiosInstance.get(`/api/ai-questions/preview/${generationId}`);\n    return response.data;\n  } catch (error) {\n    return error.response.data;\n  }\n};\n\n// Delete a specific generated question\nexport const deleteGeneratedQuestion = async (generationId, questionIndex) => {\n  try {\n    const response = await axiosInstance.delete(`/api/ai-questions/delete-question/${generationId}/${questionIndex}`);\n    return response.data;\n  } catch (error) {\n    return error.response.data;\n  }\n};\n\n// Delete entire generation\nexport const deleteGeneration = async (generationId) => {\n  try {\n    const response = await axiosInstance.delete(`/api/ai-questions/delete-generation/${generationId}`);\n    return response.data;\n  } catch (error) {\n    return error.response.data;\n  }\n};\n\n// Get available subjects for a level (now uses syllabus-based subjects)\nexport const getSubjectsForLevel = async (level, className = null) => {\n  try {\n    console.log(`🔍 Fetching subjects for level: ${level}, class: ${className}`);\n\n    // First try to get subjects from uploaded syllabuses\n    const queryParams = className ? `?class=${className}` : '';\n    console.log(`📚 Trying syllabus endpoint: /api/syllabus/subjects/${level}${queryParams}`);\n\n    const syllabusResponse = await axiosInstance.get(`/api/syllabus/subjects/${level}${queryParams}`);\n    console.log('📚 Syllabus response:', syllabusResponse.data);\n\n    if (syllabusResponse.data.success && syllabusResponse.data.data.length > 0) {\n      console.log(`✅ Using syllabus-based subjects for ${level}:`, syllabusResponse.data.data);\n      return syllabusResponse.data;\n    }\n\n    // Fallback to hardcoded subjects if no syllabuses found\n    console.log(`📖 No syllabus subjects found, falling back to hardcoded subjects for ${level}`);\n    const fallbackResponse = await axiosInstance.get(`/api/ai-questions/subjects/${level}`);\n    console.log('📖 Fallback response:', fallbackResponse.data);\n    return fallbackResponse.data;\n  } catch (error) {\n    console.error('❌ Error fetching subjects:', error);\n    console.error('Error details:', error.response?.data);\n\n    // Try fallback on error\n    try {\n      console.log(`🔄 Trying fallback due to error...`);\n      const fallbackResponse = await axiosInstance.get(`/api/ai-questions/subjects/${level}`);\n      console.log('🔄 Fallback response:', fallbackResponse.data);\n      return fallbackResponse.data;\n    } catch (fallbackError) {\n      console.error('❌ Fallback also failed:', fallbackError);\n      return { success: false, message: 'Failed to fetch subjects', data: [] };\n    }\n  }\n};\n\n// Get Tanzania syllabus topics for level, class, and subject\nexport const getSyllabusTopics = async (level, className, subject) => {\n  try {\n    // First try to get topics from uploaded syllabuses\n    const syllabusResponse = await axiosInstance.get(`/api/syllabus/ai-content/${level}/${className}/${subject}`);\n\n    if (syllabusResponse.data.success) {\n      console.log(`📚 Using syllabus-based topics for ${level} Class ${className} ${subject}`);\n      // Convert syllabus format to expected format\n      const topics = Object.keys(syllabusResponse.data.data.topics || {}).map(topicName => ({\n        topicName,\n        subtopics: syllabusResponse.data.data.topics[topicName].subtopics || [],\n        difficulty: syllabusResponse.data.data.topics[topicName].difficulty || 'medium'\n      }));\n\n      return {\n        success: true,\n        data: {\n          level,\n          class: className,\n          subject,\n          topics\n        }\n      };\n    }\n\n    // Fallback to hardcoded topics if no syllabus found\n    console.log(`📖 Falling back to hardcoded topics for ${level} Class ${className} ${subject}`);\n    const fallbackResponse = await axiosInstance.get(`/api/ai-questions/syllabus-topics/${level}/${className}/${subject}`);\n    return fallbackResponse.data;\n  } catch (error) {\n    console.error('Error fetching syllabus topics:', error);\n    return error.response?.data || { success: false, message: 'Failed to fetch topics' };\n  }\n};\n\n// Generate exam name\nexport const generateExamName = async (level, className, subjects) => {\n  try {\n    const response = await axiosInstance.post(\"/api/ai-questions/generate-exam-name\", {\n      level,\n      className,\n      subjects\n    });\n    return response.data;\n  } catch (error) {\n    return error.response.data;\n  }\n};\n", "import React, { useState, useEffect } from 'react';\nimport { Card, Button, message, Space, Typography } from 'antd';\nimport { getAllSyllabuses, getAvailableSubjects } from '../apicalls/syllabus';\nimport { getSubjectsForLevel } from '../apicalls/aiQuestions';\n\nconst { Text, Title } = Typography;\n\nconst DebugAuth = () => {\n  const [authInfo, setAuthInfo] = useState({});\n  const [testResults, setTestResults] = useState({});\n  const [loading, setLoading] = useState(false);\n\n  useEffect(() => {\n    checkAuthInfo();\n  }, []);\n\n  const checkAuthInfo = () => {\n    const token = localStorage.getItem('token');\n    const user = localStorage.getItem('user');\n    \n    let tokenInfo = {};\n    if (token) {\n      try {\n        const payload = JSON.parse(atob(token.split('.')[1]));\n        tokenInfo = {\n          userId: payload.userId,\n          exp: payload.exp,\n          iat: payload.iat,\n          isExpired: payload.exp < Date.now() / 1000\n        };\n      } catch (e) {\n        tokenInfo = { error: 'Invalid token format' };\n      }\n    }\n\n    setAuthInfo({\n      hasToken: !!token,\n      hasUser: !!user,\n      token: token ? `${token.substring(0, 20)}...` : null,\n      user: user ? JSON.parse(user) : null,\n      tokenInfo\n    });\n  };\n\n  const testSyllabusAPI = async () => {\n    setLoading(true);\n    const results = {};\n\n    try {\n      console.log('🧪 Testing getAllSyllabuses...');\n      const syllabusResponse = await getAllSyllabuses();\n      results.getAllSyllabuses = {\n        success: syllabusResponse.success,\n        dataLength: syllabusResponse.data?.length || 0,\n        message: syllabusResponse.message,\n        error: syllabusResponse.success ? null : syllabusResponse.message\n      };\n      console.log('📚 Syllabus response:', syllabusResponse);\n    } catch (error) {\n      results.getAllSyllabuses = {\n        success: false,\n        error: error.message,\n        status: error.response?.status\n      };\n      console.error('❌ Syllabus error:', error);\n    }\n\n    try {\n      console.log('🧪 Testing getAvailableSubjects...');\n      const subjectsResponse = await getAvailableSubjects('primary');\n      results.getAvailableSubjects = {\n        success: subjectsResponse.success,\n        dataLength: subjectsResponse.data?.length || 0,\n        data: subjectsResponse.data,\n        message: subjectsResponse.message,\n        error: subjectsResponse.success ? null : subjectsResponse.message\n      };\n      console.log('📖 Subjects response:', subjectsResponse);\n    } catch (error) {\n      results.getAvailableSubjects = {\n        success: false,\n        error: error.message,\n        status: error.response?.status\n      };\n      console.error('❌ Subjects error:', error);\n    }\n\n    try {\n      console.log('🧪 Testing getSubjectsForLevel (AI)...');\n      const aiSubjectsResponse = await getSubjectsForLevel('primary');\n      results.getSubjectsForLevel = {\n        success: aiSubjectsResponse.success,\n        dataLength: aiSubjectsResponse.data?.length || 0,\n        data: aiSubjectsResponse.data,\n        message: aiSubjectsResponse.message,\n        error: aiSubjectsResponse.success ? null : aiSubjectsResponse.message\n      };\n      console.log('🤖 AI Subjects response:', aiSubjectsResponse);\n    } catch (error) {\n      results.getSubjectsForLevel = {\n        success: false,\n        error: error.message,\n        status: error.response?.status\n      };\n      console.error('❌ AI Subjects error:', error);\n    }\n\n    setTestResults(results);\n    setLoading(false);\n  };\n\n  const clearAuth = () => {\n    localStorage.removeItem('token');\n    localStorage.removeItem('user');\n    message.info('Authentication cleared');\n    checkAuthInfo();\n  };\n\n  const refreshAuth = () => {\n    checkAuthInfo();\n    message.info('Authentication info refreshed');\n  };\n\n  return (\n    <div style={{ padding: '20px', maxWidth: '800px', margin: '0 auto' }}>\n      <Title level={2}>🔍 Authentication & API Debug</Title>\n      \n      {/* Authentication Info */}\n      <Card title=\"🔐 Authentication Status\" style={{ marginBottom: '20px' }}>\n        <Space direction=\"vertical\" style={{ width: '100%' }}>\n          <Text><strong>Has Token:</strong> {authInfo.hasToken ? '✅ Yes' : '❌ No'}</Text>\n          <Text><strong>Has User:</strong> {authInfo.hasUser ? '✅ Yes' : '❌ No'}</Text>\n          \n          {authInfo.token && (\n            <Text><strong>Token Preview:</strong> {authInfo.token}</Text>\n          )}\n          \n          {authInfo.user && (\n            <div>\n              <Text><strong>User:</strong> {authInfo.user.name} ({authInfo.user.email})</Text>\n              <br />\n              <Text><strong>Is Admin:</strong> {authInfo.user.isAdmin ? '✅ Yes' : '❌ No'}</Text>\n            </div>\n          )}\n          \n          {authInfo.tokenInfo && (\n            <div>\n              <Text><strong>Token Info:</strong></Text>\n              <br />\n              <Text>User ID: {authInfo.tokenInfo.userId}</Text>\n              <br />\n              <Text>Expires: {authInfo.tokenInfo.exp ? new Date(authInfo.tokenInfo.exp * 1000).toLocaleString() : 'N/A'}</Text>\n              <br />\n              <Text>Is Expired: {authInfo.tokenInfo.isExpired ? '❌ Yes' : '✅ No'}</Text>\n              {authInfo.tokenInfo.error && (\n                <>\n                  <br />\n                  <Text type=\"danger\">Error: {authInfo.tokenInfo.error}</Text>\n                </>\n              )}\n            </div>\n          )}\n        </Space>\n        \n        <div style={{ marginTop: '15px' }}>\n          <Space>\n            <Button onClick={refreshAuth}>Refresh</Button>\n            <Button onClick={clearAuth} danger>Clear Auth</Button>\n          </Space>\n        </div>\n      </Card>\n\n      {/* API Test Results */}\n      <Card title=\"🧪 API Test Results\" style={{ marginBottom: '20px' }}>\n        <Space direction=\"vertical\" style={{ width: '100%' }}>\n          <Button \n            type=\"primary\" \n            onClick={testSyllabusAPI} \n            loading={loading}\n            disabled={!authInfo.hasToken}\n          >\n            Test API Endpoints\n          </Button>\n          \n          {Object.keys(testResults).length > 0 && (\n            <div>\n              <Title level={4}>Test Results:</Title>\n\n              {Object.entries(testResults).map(([testName, result]) => (\n                <Card\n                  key={testName}\n                  size=\"small\"\n                  title={testName}\n                  style={{ marginBottom: '10px' }}\n                >\n                  <Text><strong>Success:</strong> {result.success ? '✅ Yes' : '❌ No'}</Text>\n                  <br />\n                  {result.dataLength !== undefined && (\n                    <>\n                      <Text><strong>Data Length:</strong> {result.dataLength}</Text>\n                      <br />\n                    </>\n                  )}\n                  {result.data && (\n                    <>\n                      <Text><strong>Data:</strong> {JSON.stringify(result.data)}</Text>\n                      <br />\n                    </>\n                  )}\n                  {result.message && (\n                    <>\n                      <Text><strong>Message:</strong> {result.message}</Text>\n                      <br />\n                    </>\n                  )}\n                  {result.error && (\n                    <>\n                      <Text type=\"danger\"><strong>Error:</strong> {result.error}</Text>\n                      <br />\n                    </>\n                  )}\n                  {result.status && (\n                    <>\n                      <Text><strong>Status:</strong> {result.status}</Text>\n                      <br />\n                    </>\n                  )}\n                </Card>\n              ))}\n            </div>\n          )}\n\n          {/* Quick Test Buttons */}\n          <div style={{ marginTop: '15px' }}>\n            <Space>\n              <Button\n                onClick={async () => {\n                  try {\n                    const response = await getSubjectsForLevel('primary');\n                    console.log('Quick test - Primary subjects:', response);\n                    message.info(`Primary subjects: ${JSON.stringify(response.data)}`);\n                  } catch (error) {\n                    console.error('Quick test error:', error);\n                    message.error(`Error: ${error.message}`);\n                  }\n                }}\n              >\n                Quick Test: Primary Subjects\n              </Button>\n\n              <Button\n                onClick={async () => {\n                  try {\n                    const response = await getAvailableSubjects('primary');\n                    console.log('Quick test - Available subjects:', response);\n                    message.info(`Available subjects: ${JSON.stringify(response.data)}`);\n                  } catch (error) {\n                    console.error('Quick test error:', error);\n                    message.error(`Error: ${error.message}`);\n                  }\n                }}\n              >\n                Quick Test: Syllabus Subjects\n              </Button>\n            </Space>\n          </div>\n        </Space>\n      </Card>\n\n      {/* Instructions */}\n      <Card title=\"📋 Debug Instructions\">\n        <Space direction=\"vertical\">\n          <Text>1. Check if you have a valid authentication token</Text>\n          <Text>2. Verify the token is not expired</Text>\n          <Text>3. Test API endpoints to see specific error messages</Text>\n          <Text>4. Check browser console for detailed error logs</Text>\n          <Text>5. If token is invalid, try logging out and logging back in</Text>\n        </Space>\n      </Card>\n    </div>\n  );\n};\n\nexport default DebugAuth;\n"], "names": ["getAvailableSubjects", "async", "level", "className", "arguments", "length", "undefined", "queryParams", "concat", "axiosInstance", "get", "data", "error", "response", "getSubjectsForLevel", "console", "log", "syllabusResponse", "success", "fallbackResponse", "_error$response", "fallback<PERSON><PERSON>r", "message", "Text", "Title", "Typography", "DebugAuth", "authInfo", "setAuthInfo", "useState", "testResults", "setTestResults", "loading", "setLoading", "useEffect", "checkAuthInfo", "token", "localStorage", "getItem", "user", "tokenInfo", "payload", "JSON", "parse", "atob", "split", "userId", "exp", "iat", "isExpired", "Date", "now", "e", "hasToken", "<PERSON><PERSON>ser", "substring", "_jsxs", "style", "padding", "max<PERSON><PERSON><PERSON>", "margin", "children", "_jsx", "Card", "title", "marginBottom", "Space", "direction", "width", "name", "email", "isAdmin", "toLocaleString", "_Fragment", "type", "marginTop", "<PERSON><PERSON>", "onClick", "refreshAuth", "info", "clearAuth", "removeItem", "danger", "results", "_syllabusResponse$dat", "filters", "URLSearchParams", "Object", "keys", "for<PERSON>ach", "key", "append", "toString", "getAllSyllabuses", "dataLength", "status", "_subjectsResponse$dat", "subjectsResponse", "_error$response2", "_aiSubjectsResponse$d", "aiSubjectsResponse", "_error$response3", "disabled", "entries", "map", "_ref", "testName", "result", "size", "stringify"], "sourceRoot": ""}