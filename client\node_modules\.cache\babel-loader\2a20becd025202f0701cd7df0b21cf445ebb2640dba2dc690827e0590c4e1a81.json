{"ast": null, "code": "import _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport { useEffect, useState } from 'react';\nimport { generateColor } from \"../util\";\nfunction hasValue(value) {\n  return value !== undefined;\n}\nvar useColorState = function useColorState(defaultStateValue, option) {\n  var defaultValue = option.defaultValue,\n    value = option.value;\n  var _useState = useState(function () {\n      var mergeState;\n      if (hasValue(value)) {\n        mergeState = value;\n      } else if (hasValue(defaultValue)) {\n        mergeState = defaultValue;\n      } else {\n        mergeState = defaultStateValue;\n      }\n      return generateColor(mergeState);\n    }),\n    _useState2 = _slicedToArray(_useState, 2),\n    colorValue = _useState2[0],\n    setColorValue = _useState2[1];\n  useEffect(function () {\n    if (value) {\n      setColorValue(generateColor(value));\n    }\n  }, [value]);\n  return [colorValue, setColorValue];\n};\nexport default useColorState;", "map": {"version": 3, "names": ["_slicedToArray", "useEffect", "useState", "generateColor", "hasValue", "value", "undefined", "useColorState", "defaultStateValue", "option", "defaultValue", "_useState", "mergeState", "_useState2", "colorValue", "setColorValue"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/node_modules/@rc-component/color-picker/es/hooks/useColorState.js"], "sourcesContent": ["import _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport { useEffect, useState } from 'react';\nimport { generateColor } from \"../util\";\nfunction hasValue(value) {\n  return value !== undefined;\n}\nvar useColorState = function useColorState(defaultStateValue, option) {\n  var defaultValue = option.defaultValue,\n    value = option.value;\n  var _useState = useState(function () {\n      var mergeState;\n      if (hasValue(value)) {\n        mergeState = value;\n      } else if (hasValue(defaultValue)) {\n        mergeState = defaultValue;\n      } else {\n        mergeState = defaultStateValue;\n      }\n      return generateColor(mergeState);\n    }),\n    _useState2 = _slicedToArray(_useState, 2),\n    colorValue = _useState2[0],\n    setColorValue = _useState2[1];\n  useEffect(function () {\n    if (value) {\n      setColorValue(generateColor(value));\n    }\n  }, [value]);\n  return [colorValue, setColorValue];\n};\nexport default useColorState;"], "mappings": "AAAA,OAAOA,cAAc,MAAM,0CAA0C;AACrE,SAASC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAC3C,SAASC,aAAa,QAAQ,SAAS;AACvC,SAASC,QAAQA,CAACC,KAAK,EAAE;EACvB,OAAOA,KAAK,KAAKC,SAAS;AAC5B;AACA,IAAIC,aAAa,GAAG,SAASA,aAAaA,CAACC,iBAAiB,EAAEC,MAAM,EAAE;EACpE,IAAIC,YAAY,GAAGD,MAAM,CAACC,YAAY;IACpCL,KAAK,GAAGI,MAAM,CAACJ,KAAK;EACtB,IAAIM,SAAS,GAAGT,QAAQ,CAAC,YAAY;MACjC,IAAIU,UAAU;MACd,IAAIR,QAAQ,CAACC,KAAK,CAAC,EAAE;QACnBO,UAAU,GAAGP,KAAK;MACpB,CAAC,MAAM,IAAID,QAAQ,CAACM,YAAY,CAAC,EAAE;QACjCE,UAAU,GAAGF,YAAY;MAC3B,CAAC,MAAM;QACLE,UAAU,GAAGJ,iBAAiB;MAChC;MACA,OAAOL,aAAa,CAACS,UAAU,CAAC;IAClC,CAAC,CAAC;IACFC,UAAU,GAAGb,cAAc,CAACW,SAAS,EAAE,CAAC,CAAC;IACzCG,UAAU,GAAGD,UAAU,CAAC,CAAC,CAAC;IAC1BE,aAAa,GAAGF,UAAU,CAAC,CAAC,CAAC;EAC/BZ,SAAS,CAAC,YAAY;IACpB,IAAII,KAAK,EAAE;MACTU,aAAa,CAACZ,aAAa,CAACE,KAAK,CAAC,CAAC;IACrC;EACF,CAAC,EAAE,CAACA,KAAK,CAAC,CAAC;EACX,OAAO,CAACS,UAAU,EAAEC,aAAa,CAAC;AACpC,CAAC;AACD,eAAeR,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}