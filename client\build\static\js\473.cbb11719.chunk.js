"use strict";(self.webpackChunkclient=self.webpackChunkclient||[]).push([[473],{6473:(t,e,a)=>{a.d(e,{Z:()=>B});var n=a(1694),c=a.n(n),o=a(1818),i=a(2791),r=a(1929),l=a(4107),s=a(183),d=a(5058),g=function(t,e){var a={};for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&e.indexOf(n)<0&&(a[n]=t[n]);if(null!=t&&"function"===typeof Object.getOwnPropertySymbols){var c=0;for(n=Object.getOwnPropertySymbols(t);c<n.length;c++)e.indexOf(n[c])<0&&Object.prototype.propertyIsEnumerable.call(t,n[c])&&(a[n[c]]=t[n[c]])}return a};const p=t=>{var{prefixCls:e,className:a,hoverable:n=!0}=t,o=g(t,["prefixCls","className","hoverable"]);const{getPrefixCls:l}=i.useContext(r.E_),s=l("card",e),d=c()("".concat(s,"-grid"),a,{["".concat(s,"-grid-hoverable")]:n});return i.createElement("div",Object.assign({},o,{className:d}))};var m=a(7521),b=a(5564),h=a(9922);const u=t=>{const{antCls:e,componentCls:a,headerHeight:n,cardPaddingBase:c,tabsMarginBottom:o}=t;return Object.assign(Object.assign({display:"flex",justifyContent:"center",flexDirection:"column",minHeight:n,marginBottom:-1,padding:"0 ".concat(c,"px"),color:t.colorTextHeading,fontWeight:t.fontWeightStrong,fontSize:t.headerFontSize,background:t.headerBg,borderBottom:"".concat(t.lineWidth,"px ").concat(t.lineType," ").concat(t.colorBorderSecondary),borderRadius:"".concat(t.borderRadiusLG,"px ").concat(t.borderRadiusLG,"px 0 0")},(0,m.dF)()),{"&-wrapper":{width:"100%",display:"flex",alignItems:"center"},"&-title":Object.assign(Object.assign({display:"inline-block",flex:1},m.vS),{["\n          > ".concat(a,"-typography,\n          > ").concat(a,"-typography-edit-content\n        ")]:{insetInlineStart:0,marginTop:0,marginBottom:0}}),["".concat(e,"-tabs-top")]:{clear:"both",marginBottom:o,color:t.colorText,fontWeight:"normal",fontSize:t.fontSize,"&-bar":{borderBottom:"".concat(t.lineWidth,"px ").concat(t.lineType," ").concat(t.colorBorderSecondary)}}})},f=t=>{const{cardPaddingBase:e,colorBorderSecondary:a,cardShadow:n,lineWidth:c}=t;return{width:"33.33%",padding:e,border:0,borderRadius:0,boxShadow:"\n      ".concat(c,"px 0 0 0 ").concat(a,",\n      0 ").concat(c,"px 0 0 ").concat(a,",\n      ").concat(c,"px ").concat(c,"px 0 0 ").concat(a,",\n      ").concat(c,"px 0 0 0 ").concat(a," inset,\n      0 ").concat(c,"px 0 0 ").concat(a," inset;\n    "),transition:"all ".concat(t.motionDurationMid),"&-hoverable:hover":{position:"relative",zIndex:1,boxShadow:n}}},v=t=>{const{componentCls:e,iconCls:a,actionsLiMargin:n,cardActionsIconSize:c,colorBorderSecondary:o,actionsBg:i}=t;return Object.assign(Object.assign({margin:0,padding:0,listStyle:"none",background:i,borderTop:"".concat(t.lineWidth,"px ").concat(t.lineType," ").concat(o),display:"flex",borderRadius:"0 0 ".concat(t.borderRadiusLG,"px ").concat(t.borderRadiusLG,"px ")},(0,m.dF)()),{"& > li":{margin:n,color:t.colorTextDescription,textAlign:"center","> span":{position:"relative",display:"block",minWidth:2*t.cardActionsIconSize,fontSize:t.fontSize,lineHeight:t.lineHeight,cursor:"pointer","&:hover":{color:t.colorPrimary,transition:"color ".concat(t.motionDurationMid)},["a:not(".concat(e,"-btn), > ").concat(a)]:{display:"inline-block",width:"100%",color:t.colorTextDescription,lineHeight:"".concat(t.fontSize*t.lineHeight,"px"),transition:"color ".concat(t.motionDurationMid),"&:hover":{color:t.colorPrimary}},["> ".concat(a)]:{fontSize:c,lineHeight:"".concat(c*t.lineHeight,"px")}},"&:not(:last-child)":{borderInlineEnd:"".concat(t.lineWidth,"px ").concat(t.lineType," ").concat(o)}}})},x=t=>Object.assign(Object.assign({margin:"-".concat(t.marginXXS,"px 0"),display:"flex"},(0,m.dF)()),{"&-avatar":{paddingInlineEnd:t.padding},"&-detail":{overflow:"hidden",flex:1,"> div:not(:last-child)":{marginBottom:t.marginXS}},"&-title":Object.assign({color:t.colorTextHeading,fontWeight:t.fontWeightStrong,fontSize:t.fontSizeLG},m.vS),"&-description":{color:t.colorTextDescription}}),y=t=>{const{componentCls:e,cardPaddingBase:a,colorFillAlter:n}=t;return{["".concat(e,"-head")]:{padding:"0 ".concat(a,"px"),background:n,"&-title":{fontSize:t.fontSize}},["".concat(e,"-body")]:{padding:"".concat(t.padding,"px ").concat(a,"px")}}},C=t=>{const{componentCls:e}=t;return{overflow:"hidden",["".concat(e,"-body")]:{userSelect:"none"}}},O=t=>{const{antCls:e,componentCls:a,cardShadow:n,cardHeadPadding:c,colorBorderSecondary:o,boxShadowTertiary:i,cardPaddingBase:r,extraColor:l}=t;return{[a]:Object.assign(Object.assign({},(0,m.Wf)(t)),{position:"relative",background:t.colorBgContainer,borderRadius:t.borderRadiusLG,["&:not(".concat(a,"-bordered)")]:{boxShadow:i},["".concat(a,"-head")]:u(t),["".concat(a,"-extra")]:{marginInlineStart:"auto",color:l,fontWeight:"normal",fontSize:t.fontSize},["".concat(a,"-body")]:Object.assign({padding:r,borderRadius:" 0 0 ".concat(t.borderRadiusLG,"px ").concat(t.borderRadiusLG,"px")},(0,m.dF)()),["".concat(a,"-grid")]:f(t),["".concat(a,"-cover")]:{"> *":{display:"block",width:"100%"},["img, img + ".concat(e,"-image-mask")]:{borderRadius:"".concat(t.borderRadiusLG,"px ").concat(t.borderRadiusLG,"px 0 0")}},["".concat(a,"-actions")]:v(t),["".concat(a,"-meta")]:x(t)}),["".concat(a,"-bordered")]:{border:"".concat(t.lineWidth,"px ").concat(t.lineType," ").concat(o),["".concat(a,"-cover")]:{marginTop:-1,marginInlineStart:-1,marginInlineEnd:-1}},["".concat(a,"-hoverable")]:{cursor:"pointer",transition:"box-shadow ".concat(t.motionDurationMid,", border-color ").concat(t.motionDurationMid),"&:hover":{borderColor:"transparent",boxShadow:n}},["".concat(a,"-contain-grid")]:{["".concat(a,"-body")]:{display:"flex",flexWrap:"wrap"},["&:not(".concat(a,"-loading) ").concat(a,"-body")]:{marginBlockStart:-t.lineWidth,marginInlineStart:-t.lineWidth,padding:0}},["".concat(a,"-contain-tabs")]:{["> ".concat(a,"-head")]:{["".concat(a,"-head-title, ").concat(a,"-extra")]:{paddingTop:c}}},["".concat(a,"-type-inner")]:y(t),["".concat(a,"-loading")]:C(t),["".concat(a,"-rtl")]:{direction:"rtl"}}},S=t=>{const{componentCls:e,cardPaddingSM:a,headerHeightSM:n,headerFontSizeSM:c}=t;return{["".concat(e,"-small")]:{["> ".concat(e,"-head")]:{minHeight:n,padding:"0 ".concat(a,"px"),fontSize:c,["> ".concat(e,"-head-wrapper")]:{["> ".concat(e,"-extra")]:{fontSize:t.fontSize}}},["> ".concat(e,"-body")]:{padding:a}},["".concat(e,"-small").concat(e,"-contain-tabs")]:{["> ".concat(e,"-head")]:{["".concat(e,"-head-title, ").concat(e,"-extra")]:{minHeight:n,paddingTop:0,display:"flex",alignItems:"center"}}}}},j=(0,b.Z)("Card",(t=>{const e=(0,h.TS)(t,{cardShadow:t.boxShadowCard,cardHeadPadding:t.padding,cardPaddingBase:t.paddingLG,cardActionsIconSize:t.fontSize,cardPaddingSM:12});return[O(e),S(e)]}),(t=>({headerBg:"transparent",headerFontSize:t.fontSizeLG,headerFontSizeSM:t.fontSize,headerHeight:t.fontSizeLG*t.lineHeightLG+2*t.padding,headerHeightSM:t.fontSize*t.lineHeight+2*t.paddingXS,actionsBg:t.colorBgContainer,actionsLiMargin:"".concat(t.paddingSM,"px 0"),tabsMarginBottom:-t.padding-t.lineWidth,extraColor:t.colorText})));var k=function(t,e){var a={};for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&e.indexOf(n)<0&&(a[n]=t[n]);if(null!=t&&"function"===typeof Object.getOwnPropertySymbols){var c=0;for(n=Object.getOwnPropertySymbols(t);c<n.length;c++)e.indexOf(n[c])<0&&Object.prototype.propertyIsEnumerable.call(t,n[c])&&(a[n[c]]=t[n[c]])}return a};const w=i.forwardRef(((t,e)=>{const{prefixCls:a,className:n,rootClassName:g,style:m,extra:b,headStyle:h={},bodyStyle:u={},title:f,loading:v,bordered:x=!0,size:y,type:C,cover:O,actions:S,tabList:w,children:E,activeTabKey:N,defaultActiveTabKey:z,tabBarExtraContent:B,hoverable:H,tabProps:M={}}=t,R=k(t,["prefixCls","className","rootClassName","style","extra","headStyle","bodyStyle","title","loading","bordered","size","type","cover","actions","tabList","children","activeTabKey","defaultActiveTabKey","tabBarExtraContent","hoverable","tabProps"]),{getPrefixCls:P,direction:T,card:L}=i.useContext(r.E_),I=i.useMemo((()=>{let t=!1;return i.Children.forEach(E,(e=>{e&&e.type&&e.type===p&&(t=!0)})),t}),[E]),W=P("card",a),[A,G]=j(W),F=i.createElement(s.Z,{loading:!0,active:!0,paragraph:{rows:4},title:!1},E),Z=void 0!==N,q=Object.assign(Object.assign({},M),{[Z?"activeKey":"defaultActiveKey"]:Z?N:z,tabBarExtraContent:B});let D;const _=(0,l.Z)(y),X=_&&"default"!==_?_:"large",K=w?i.createElement(d.default,Object.assign({size:X},q,{className:"".concat(W,"-head-tabs"),onChange:e=>{var a;null===(a=t.onTabChange)||void 0===a||a.call(t,e)},items:w.map((t=>{var{tab:e}=t,a=k(t,["tab"]);return Object.assign({label:e},a)}))})):null;(f||b||K)&&(D=i.createElement("div",{className:"".concat(W,"-head"),style:h},i.createElement("div",{className:"".concat(W,"-head-wrapper")},f&&i.createElement("div",{className:"".concat(W,"-head-title")},f),b&&i.createElement("div",{className:"".concat(W,"-extra")},b)),K));const V=O?i.createElement("div",{className:"".concat(W,"-cover")},O):null,J=i.createElement("div",{className:"".concat(W,"-body"),style:u},v?F:E),Q=S&&S.length?i.createElement("ul",{className:"".concat(W,"-actions")},function(t){return t.map(((e,a)=>i.createElement("li",{style:{width:"".concat(100/t.length,"%")},key:"action-".concat(a)},i.createElement("span",null,e))))}(S)):null,U=(0,o.Z)(R,["onTabChange"]),Y=c()(W,null===L||void 0===L?void 0:L.className,{["".concat(W,"-loading")]:v,["".concat(W,"-bordered")]:x,["".concat(W,"-hoverable")]:H,["".concat(W,"-contain-grid")]:I,["".concat(W,"-contain-tabs")]:w&&w.length,["".concat(W,"-").concat(_)]:_,["".concat(W,"-type-").concat(C)]:!!C,["".concat(W,"-rtl")]:"rtl"===T},n,g,G),$=Object.assign(Object.assign({},null===L||void 0===L?void 0:L.style),m);return A(i.createElement("div",Object.assign({ref:e},U,{className:Y,style:$}),D,V,J,Q))}));var E=function(t,e){var a={};for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&e.indexOf(n)<0&&(a[n]=t[n]);if(null!=t&&"function"===typeof Object.getOwnPropertySymbols){var c=0;for(n=Object.getOwnPropertySymbols(t);c<n.length;c++)e.indexOf(n[c])<0&&Object.prototype.propertyIsEnumerable.call(t,n[c])&&(a[n[c]]=t[n[c]])}return a};const N=t=>{const{prefixCls:e,className:a,avatar:n,title:o,description:l}=t,s=E(t,["prefixCls","className","avatar","title","description"]),{getPrefixCls:d}=i.useContext(r.E_),g=d("card",e),p=c()("".concat(g,"-meta"),a),m=n?i.createElement("div",{className:"".concat(g,"-meta-avatar")},n):null,b=o?i.createElement("div",{className:"".concat(g,"-meta-title")},o):null,h=l?i.createElement("div",{className:"".concat(g,"-meta-description")},l):null,u=b||h?i.createElement("div",{className:"".concat(g,"-meta-detail")},b,h):null;return i.createElement("div",Object.assign({},s,{className:p}),m,u)},z=w;z.Grid=p,z.Meta=N;const B=z},183:(t,e,a)=>{a.d(e,{Z:()=>F});var n=a(1694),c=a.n(n),o=a(2791),i=a(1929),r=a(1818);const l=t=>{const{prefixCls:e,className:a,style:n,size:i,shape:r}=t,l=c()({["".concat(e,"-lg")]:"large"===i,["".concat(e,"-sm")]:"small"===i}),s=c()({["".concat(e,"-circle")]:"circle"===r,["".concat(e,"-square")]:"square"===r,["".concat(e,"-round")]:"round"===r}),d=o.useMemo((()=>"number"===typeof i?{width:i,height:i,lineHeight:"".concat(i,"px")}:{}),[i]);return o.createElement("span",{className:c()(e,l,s,a),style:Object.assign(Object.assign({},d),n)})};var s=a(2666),d=a(5564),g=a(9922);const p=new s.E4("ant-skeleton-loading",{"0%":{backgroundPosition:"100% 50%"},"100%":{backgroundPosition:"0 50%"}}),m=t=>({height:t,lineHeight:"".concat(t,"px")}),b=t=>Object.assign({width:t},m(t)),h=t=>({background:t.skeletonLoadingBackground,backgroundSize:"400% 100%",animationName:p,animationDuration:t.skeletonLoadingMotionDuration,animationTimingFunction:"ease",animationIterationCount:"infinite"}),u=t=>Object.assign({width:5*t,minWidth:5*t},m(t)),f=t=>{const{skeletonAvatarCls:e,gradientFromColor:a,controlHeight:n,controlHeightLG:c,controlHeightSM:o}=t;return{["".concat(e)]:Object.assign({display:"inline-block",verticalAlign:"top",background:a},b(n)),["".concat(e).concat(e,"-circle")]:{borderRadius:"50%"},["".concat(e).concat(e,"-lg")]:Object.assign({},b(c)),["".concat(e).concat(e,"-sm")]:Object.assign({},b(o))}},v=t=>{const{controlHeight:e,borderRadiusSM:a,skeletonInputCls:n,controlHeightLG:c,controlHeightSM:o,gradientFromColor:i}=t;return{["".concat(n)]:Object.assign({display:"inline-block",verticalAlign:"top",background:i,borderRadius:a},u(e)),["".concat(n,"-lg")]:Object.assign({},u(c)),["".concat(n,"-sm")]:Object.assign({},u(o))}},x=t=>Object.assign({width:t},m(t)),y=t=>{const{skeletonImageCls:e,imageSizeBase:a,gradientFromColor:n,borderRadiusSM:c}=t;return{["".concat(e)]:Object.assign(Object.assign({display:"flex",alignItems:"center",justifyContent:"center",verticalAlign:"top",background:n,borderRadius:c},x(2*a)),{["".concat(e,"-path")]:{fill:"#bfbfbf"},["".concat(e,"-svg")]:Object.assign(Object.assign({},x(a)),{maxWidth:4*a,maxHeight:4*a}),["".concat(e,"-svg").concat(e,"-svg-circle")]:{borderRadius:"50%"}}),["".concat(e).concat(e,"-circle")]:{borderRadius:"50%"}}},C=(t,e,a)=>{const{skeletonButtonCls:n}=t;return{["".concat(a).concat(n,"-circle")]:{width:e,minWidth:e,borderRadius:"50%"},["".concat(a).concat(n,"-round")]:{borderRadius:e}}},O=t=>Object.assign({width:2*t,minWidth:2*t},m(t)),S=t=>{const{borderRadiusSM:e,skeletonButtonCls:a,controlHeight:n,controlHeightLG:c,controlHeightSM:o,gradientFromColor:i}=t;return Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({["".concat(a)]:Object.assign({display:"inline-block",verticalAlign:"top",background:i,borderRadius:e,width:2*n,minWidth:2*n},O(n))},C(t,n,a)),{["".concat(a,"-lg")]:Object.assign({},O(c))}),C(t,c,"".concat(a,"-lg"))),{["".concat(a,"-sm")]:Object.assign({},O(o))}),C(t,o,"".concat(a,"-sm")))},j=t=>{const{componentCls:e,skeletonAvatarCls:a,skeletonTitleCls:n,skeletonParagraphCls:c,skeletonButtonCls:o,skeletonInputCls:i,skeletonImageCls:r,controlHeight:l,controlHeightLG:s,controlHeightSM:d,gradientFromColor:g,padding:p,marginSM:m,borderRadius:u,titleHeight:x,blockRadius:C,paragraphLiHeight:O,controlHeightXS:j,paragraphMarginTop:k}=t;return{["".concat(e)]:{display:"table",width:"100%",["".concat(e,"-header")]:{display:"table-cell",paddingInlineEnd:p,verticalAlign:"top",["".concat(a)]:Object.assign({display:"inline-block",verticalAlign:"top",background:g},b(l)),["".concat(a,"-circle")]:{borderRadius:"50%"},["".concat(a,"-lg")]:Object.assign({},b(s)),["".concat(a,"-sm")]:Object.assign({},b(d))},["".concat(e,"-content")]:{display:"table-cell",width:"100%",verticalAlign:"top",["".concat(n)]:{width:"100%",height:x,background:g,borderRadius:C,["+ ".concat(c)]:{marginBlockStart:d}},["".concat(c)]:{padding:0,"> li":{width:"100%",height:O,listStyle:"none",background:g,borderRadius:C,"+ li":{marginBlockStart:j}}},["".concat(c,"> li:last-child:not(:first-child):not(:nth-child(2))")]:{width:"61%"}},["&-round ".concat(e,"-content")]:{["".concat(n,", ").concat(c," > li")]:{borderRadius:u}}},["".concat(e,"-with-avatar ").concat(e,"-content")]:{["".concat(n)]:{marginBlockStart:m,["+ ".concat(c)]:{marginBlockStart:k}}},["".concat(e).concat(e,"-element")]:Object.assign(Object.assign(Object.assign(Object.assign({display:"inline-block",width:"auto"},S(t)),f(t)),v(t)),y(t)),["".concat(e).concat(e,"-block")]:{width:"100%",["".concat(o)]:{width:"100%"},["".concat(i)]:{width:"100%"}},["".concat(e).concat(e,"-active")]:{["\n        ".concat(n,",\n        ").concat(c," > li,\n        ").concat(a,",\n        ").concat(o,",\n        ").concat(i,",\n        ").concat(r,"\n      ")]:Object.assign({},h(t))}}},k=(0,d.Z)("Skeleton",(t=>{const{componentCls:e}=t,a=(0,g.TS)(t,{skeletonAvatarCls:"".concat(e,"-avatar"),skeletonTitleCls:"".concat(e,"-title"),skeletonParagraphCls:"".concat(e,"-paragraph"),skeletonButtonCls:"".concat(e,"-button"),skeletonInputCls:"".concat(e,"-input"),skeletonImageCls:"".concat(e,"-image"),imageSizeBase:1.5*t.controlHeight,borderRadius:100,skeletonLoadingBackground:"linear-gradient(90deg, ".concat(t.gradientFromColor," 25%, ").concat(t.gradientToColor," 37%, ").concat(t.gradientFromColor," 63%)"),skeletonLoadingMotionDuration:"1.4s"});return[j(a)]}),(t=>{const{colorFillContent:e,colorFill:a}=t;return{color:e,colorGradientEnd:a,gradientFromColor:e,gradientToColor:a,titleHeight:t.controlHeight/2,blockRadius:t.borderRadiusSM,paragraphMarginTop:t.marginLG+t.marginXXS,paragraphLiHeight:t.controlHeight/2}}),{deprecatedTokens:[["color","gradientFromColor"],["colorGradientEnd","gradientToColor"]]}),w=t=>{const{prefixCls:e,className:a,rootClassName:n,active:s,shape:d="circle",size:g="default"}=t,{getPrefixCls:p}=o.useContext(i.E_),m=p("skeleton",e),[b,h]=k(m),u=(0,r.Z)(t,["prefixCls","className"]),f=c()(m,"".concat(m,"-element"),{["".concat(m,"-active")]:s},a,n,h);return b(o.createElement("div",{className:f},o.createElement(l,Object.assign({prefixCls:"".concat(m,"-avatar"),shape:d,size:g},u))))},E=t=>{const{prefixCls:e,className:a,rootClassName:n,active:s,block:d=!1,size:g="default"}=t,{getPrefixCls:p}=o.useContext(i.E_),m=p("skeleton",e),[b,h]=k(m),u=(0,r.Z)(t,["prefixCls"]),f=c()(m,"".concat(m,"-element"),{["".concat(m,"-active")]:s,["".concat(m,"-block")]:d},a,n,h);return b(o.createElement("div",{className:f},o.createElement(l,Object.assign({prefixCls:"".concat(m,"-button"),size:g},u))))},N=t=>{const{prefixCls:e,className:a,rootClassName:n,style:r,active:l}=t,{getPrefixCls:s}=o.useContext(i.E_),d=s("skeleton",e),[g,p]=k(d),m=c()(d,"".concat(d,"-element"),{["".concat(d,"-active")]:l},a,n,p);return g(o.createElement("div",{className:m},o.createElement("div",{className:c()("".concat(d,"-image"),a),style:r},o.createElement("svg",{viewBox:"0 0 1098 1024",xmlns:"http://www.w3.org/2000/svg",className:"".concat(d,"-image-svg")},o.createElement("path",{d:"M365.714286 329.142857q0 45.714286-32.036571 77.677714t-77.677714 32.036571-77.677714-32.036571-32.036571-77.677714 32.036571-77.677714 77.677714-32.036571 77.677714 32.036571 32.036571 77.677714zM950.857143 548.571429l0 256-804.571429 0 0-109.714286 182.857143-182.857143 91.428571 91.428571 292.571429-292.571429zM1005.714286 146.285714l-914.285714 0q-7.460571 0-12.873143 5.412571t-5.412571 12.873143l0 694.857143q0 7.460571 5.412571 12.873143t12.873143 5.412571l914.285714 0q7.460571 0 12.873143-5.412571t5.412571-12.873143l0-694.857143q0-7.460571-5.412571-12.873143t-12.873143-5.412571zM1097.142857 164.571429l0 694.857143q0 37.741714-26.843429 64.585143t-64.585143 26.843429l-914.285714 0q-37.741714 0-64.585143-26.843429t-26.843429-64.585143l0-694.857143q0-37.741714 26.843429-64.585143t64.585143-26.843429l914.285714 0q37.741714 0 64.585143 26.843429t26.843429 64.585143z",className:"".concat(d,"-image-path")})))))},z=t=>{const{prefixCls:e,className:a,rootClassName:n,active:s,block:d,size:g="default"}=t,{getPrefixCls:p}=o.useContext(i.E_),m=p("skeleton",e),[b,h]=k(m),u=(0,r.Z)(t,["prefixCls"]),f=c()(m,"".concat(m,"-element"),{["".concat(m,"-active")]:s,["".concat(m,"-block")]:d},a,n,h);return b(o.createElement("div",{className:f},o.createElement(l,Object.assign({prefixCls:"".concat(m,"-input"),size:g},u))))};var B=a(7462);const H={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M888 792H200V168c0-4.4-3.6-8-8-8h-56c-4.4 0-8 3.6-8 8v688c0 4.4 3.6 8 8 8h752c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zM288 604a64 64 0 10128 0 64 64 0 10-128 0zm118-224a48 48 0 1096 0 48 48 0 10-96 0zm158 228a96 96 0 10192 0 96 96 0 10-192 0zm148-314a56 56 0 10112 0 56 56 0 10-112 0z"}}]},name:"dot-chart",theme:"outlined"};var M=a(4291),R=function(t,e){return o.createElement(M.Z,(0,B.Z)({},t,{ref:e,icon:H}))};const P=o.forwardRef(R),T=t=>{const{prefixCls:e,className:a,rootClassName:n,style:r,active:l,children:s}=t,{getPrefixCls:d}=o.useContext(i.E_),g=d("skeleton",e),[p,m]=k(g),b=c()(g,"".concat(g,"-element"),{["".concat(g,"-active")]:l},m,a,n),h=null!==s&&void 0!==s?s:o.createElement(P,null);return p(o.createElement("div",{className:b},o.createElement("div",{className:c()("".concat(g,"-image"),a),style:r},h)))};var L=a(3433);const I=t=>{const e=e=>{const{width:a,rows:n=2}=t;return Array.isArray(a)?a[e]:n-1===e?a:void 0},{prefixCls:a,className:n,style:i,rows:r}=t,l=(0,L.Z)(Array(r)).map(((t,a)=>o.createElement("li",{key:a,style:{width:e(a)}})));return o.createElement("ul",{className:c()(a,n),style:i},l)},W=t=>{let{prefixCls:e,className:a,width:n,style:i}=t;return o.createElement("h3",{className:c()(e,a),style:Object.assign({width:n},i)})};function A(t){return t&&"object"===typeof t?t:{}}const G=t=>{const{prefixCls:e,loading:a,className:n,rootClassName:r,style:s,children:d,avatar:g=!1,title:p=!0,paragraph:m=!0,active:b,round:h}=t,{getPrefixCls:u,direction:f,skeleton:v}=o.useContext(i.E_),x=u("skeleton",e),[y,C]=k(x);if(a||!("loading"in t)){const t=!!g,e=!!p,a=!!m;let i,d;if(t){const t=Object.assign(Object.assign({prefixCls:"".concat(x,"-avatar")},function(t,e){return t&&!e?{size:"large",shape:"square"}:{size:"large",shape:"circle"}}(e,a)),A(g));i=o.createElement("div",{className:"".concat(x,"-header")},o.createElement(l,Object.assign({},t)))}if(e||a){let n,c;if(e){const e=Object.assign(Object.assign({prefixCls:"".concat(x,"-title")},function(t,e){return!t&&e?{width:"38%"}:t&&e?{width:"50%"}:{}}(t,a)),A(p));n=o.createElement(W,Object.assign({},e))}if(a){const a=Object.assign(Object.assign({prefixCls:"".concat(x,"-paragraph")},function(t,e){const a={};return t&&e||(a.width="61%"),a.rows=!t&&e?3:2,a}(t,e)),A(m));c=o.createElement(I,Object.assign({},a))}d=o.createElement("div",{className:"".concat(x,"-content")},n,c)}const u=c()(x,{["".concat(x,"-with-avatar")]:t,["".concat(x,"-active")]:b,["".concat(x,"-rtl")]:"rtl"===f,["".concat(x,"-round")]:h},null===v||void 0===v?void 0:v.className,n,r,C);return y(o.createElement("div",{className:u,style:Object.assign(Object.assign({},null===v||void 0===v?void 0:v.style),s)},i,d))}return"undefined"!==typeof d?d:null};G.Button=E,G.Avatar=w,G.Input=z,G.Image=N,G.Node=T;const F=G}}]);
//# sourceMappingURL=473.cbb11719.chunk.js.map