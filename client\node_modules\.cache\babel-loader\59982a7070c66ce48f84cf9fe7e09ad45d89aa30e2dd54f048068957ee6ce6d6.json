{"ast": null, "code": "import { createContext, useContext } from \"./context\";\nimport { makeImmutable, responseImmutable, useImmutableMark } from \"./Immutable\";\nexport { createContext, useContext, makeImmutable, responseImmutable, useImmutableMark };", "map": {"version": 3, "names": ["createContext", "useContext", "makeImmutable", "responseImmutable", "useImmutableMark"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/node_modules/@rc-component/context/es/index.js"], "sourcesContent": ["import { createContext, useContext } from \"./context\";\nimport { makeImmutable, responseImmutable, useImmutableMark } from \"./Immutable\";\nexport { createContext, useContext, makeImmutable, responseImmutable, useImmutableMark };"], "mappings": "AAAA,SAASA,aAAa,EAAEC,UAAU,QAAQ,WAAW;AACrD,SAASC,aAAa,EAAEC,iBAAiB,EAAEC,gBAAgB,QAAQ,aAAa;AAChF,SAASJ,aAAa,EAAEC,UAAU,EAAEC,aAAa,EAAEC,iBAAiB,EAAEC,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}