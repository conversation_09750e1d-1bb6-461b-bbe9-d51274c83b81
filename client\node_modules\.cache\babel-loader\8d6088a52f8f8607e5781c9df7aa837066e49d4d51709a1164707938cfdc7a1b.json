{"ast": null, "code": "import warning from \"rc-util/es/warning\";\nexport default (function (file, acceptedFiles) {\n  if (file && acceptedFiles) {\n    var acceptedFilesArray = Array.isArray(acceptedFiles) ? acceptedFiles : acceptedFiles.split(',');\n    var fileName = file.name || '';\n    var mimeType = file.type || '';\n    var baseMimeType = mimeType.replace(/\\/.*$/, '');\n    return acceptedFilesArray.some(function (type) {\n      var validType = type.trim(); // This is something like */*,*  allow all files\n\n      if (/^\\*(\\/\\*)?$/.test(type)) {\n        return true;\n      } // like .jpg, .png\n\n      if (validType.charAt(0) === '.') {\n        var lowerFileName = fileName.toLowerCase();\n        var lowerType = validType.toLowerCase();\n        var affixList = [lowerType];\n        if (lowerType === '.jpg' || lowerType === '.jpeg') {\n          affixList = ['.jpg', '.jpeg'];\n        }\n        return affixList.some(function (affix) {\n          return lowerFileName.endsWith(affix);\n        });\n      } // This is something like a image/* mime type\n\n      if (/\\/\\*$/.test(validType)) {\n        return baseMimeType === validType.replace(/\\/.*$/, '');\n      } // Full match\n\n      if (mimeType === validType) {\n        return true;\n      } // Invalidate type should skip\n\n      if (/^\\w+$/.test(validType)) {\n        warning(false, \"Upload takes an invalidate 'accept' type '\".concat(validType, \"'.Skip for check.\"));\n        return true;\n      }\n      return false;\n    });\n  }\n  return true;\n});", "map": {"version": 3, "names": ["warning", "file", "acceptedFiles", "acceptedFilesArray", "Array", "isArray", "split", "fileName", "name", "mimeType", "type", "baseMimeType", "replace", "some", "validType", "trim", "test", "char<PERSON>t", "lowerFileName", "toLowerCase", "lowerType", "affixList", "affix", "endsWith", "concat"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/node_modules/rc-upload/es/attr-accept.js"], "sourcesContent": ["import warning from \"rc-util/es/warning\";\nexport default (function (file, acceptedFiles) {\n  if (file && acceptedFiles) {\n    var acceptedFilesArray = Array.isArray(acceptedFiles) ? acceptedFiles : acceptedFiles.split(',');\n    var fileName = file.name || '';\n    var mimeType = file.type || '';\n    var baseMimeType = mimeType.replace(/\\/.*$/, '');\n    return acceptedFilesArray.some(function (type) {\n      var validType = type.trim(); // This is something like */*,*  allow all files\n\n      if (/^\\*(\\/\\*)?$/.test(type)) {\n        return true;\n      } // like .jpg, .png\n\n\n      if (validType.charAt(0) === '.') {\n        var lowerFileName = fileName.toLowerCase();\n        var lowerType = validType.toLowerCase();\n        var affixList = [lowerType];\n\n        if (lowerType === '.jpg' || lowerType === '.jpeg') {\n          affixList = ['.jpg', '.jpeg'];\n        }\n\n        return affixList.some(function (affix) {\n          return lowerFileName.endsWith(affix);\n        });\n      } // This is something like a image/* mime type\n\n\n      if (/\\/\\*$/.test(validType)) {\n        return baseMimeType === validType.replace(/\\/.*$/, '');\n      } // Full match\n\n\n      if (mimeType === validType) {\n        return true;\n      } // Invalidate type should skip\n\n\n      if (/^\\w+$/.test(validType)) {\n        warning(false, \"Upload takes an invalidate 'accept' type '\".concat(validType, \"'.Skip for check.\"));\n        return true;\n      }\n\n      return false;\n    });\n  }\n\n  return true;\n});"], "mappings": "AAAA,OAAOA,OAAO,MAAM,oBAAoB;AACxC,gBAAgB,UAAUC,IAAI,EAAEC,aAAa,EAAE;EAC7C,IAAID,IAAI,IAAIC,aAAa,EAAE;IACzB,IAAIC,kBAAkB,GAAGC,KAAK,CAACC,OAAO,CAACH,aAAa,CAAC,GAAGA,aAAa,GAAGA,aAAa,CAACI,KAAK,CAAC,GAAG,CAAC;IAChG,IAAIC,QAAQ,GAAGN,IAAI,CAACO,IAAI,IAAI,EAAE;IAC9B,IAAIC,QAAQ,GAAGR,IAAI,CAACS,IAAI,IAAI,EAAE;IAC9B,IAAIC,YAAY,GAAGF,QAAQ,CAACG,OAAO,CAAC,OAAO,EAAE,EAAE,CAAC;IAChD,OAAOT,kBAAkB,CAACU,IAAI,CAAC,UAAUH,IAAI,EAAE;MAC7C,IAAII,SAAS,GAAGJ,IAAI,CAACK,IAAI,CAAC,CAAC,CAAC,CAAC;;MAE7B,IAAI,aAAa,CAACC,IAAI,CAACN,IAAI,CAAC,EAAE;QAC5B,OAAO,IAAI;MACb,CAAC,CAAC;;MAGF,IAAII,SAAS,CAACG,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;QAC/B,IAAIC,aAAa,GAAGX,QAAQ,CAACY,WAAW,CAAC,CAAC;QAC1C,IAAIC,SAAS,GAAGN,SAAS,CAACK,WAAW,CAAC,CAAC;QACvC,IAAIE,SAAS,GAAG,CAACD,SAAS,CAAC;QAE3B,IAAIA,SAAS,KAAK,MAAM,IAAIA,SAAS,KAAK,OAAO,EAAE;UACjDC,SAAS,GAAG,CAAC,MAAM,EAAE,OAAO,CAAC;QAC/B;QAEA,OAAOA,SAAS,CAACR,IAAI,CAAC,UAAUS,KAAK,EAAE;UACrC,OAAOJ,aAAa,CAACK,QAAQ,CAACD,KAAK,CAAC;QACtC,CAAC,CAAC;MACJ,CAAC,CAAC;;MAGF,IAAI,OAAO,CAACN,IAAI,CAACF,SAAS,CAAC,EAAE;QAC3B,OAAOH,YAAY,KAAKG,SAAS,CAACF,OAAO,CAAC,OAAO,EAAE,EAAE,CAAC;MACxD,CAAC,CAAC;;MAGF,IAAIH,QAAQ,KAAKK,SAAS,EAAE;QAC1B,OAAO,IAAI;MACb,CAAC,CAAC;;MAGF,IAAI,OAAO,CAACE,IAAI,CAACF,SAAS,CAAC,EAAE;QAC3Bd,OAAO,CAAC,KAAK,EAAE,4CAA4C,CAACwB,MAAM,CAACV,SAAS,EAAE,mBAAmB,CAAC,CAAC;QACnG,OAAO,IAAI;MACb;MAEA,OAAO,KAAK;IACd,CAAC,CAAC;EACJ;EAEA,OAAO,IAAI;AACb,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}