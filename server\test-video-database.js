const mongoose = require('mongoose');
require('dotenv').config();

console.log('🎬 Testing Video Database Connection...\n');

// Connect to MongoDB
mongoose.connect(process.env.MONGO_URL, {
  serverSelectionTimeoutMS: 10000,
  socketTimeoutMS: 10000,
  connectTimeoutMS: 10000,
});

// Video model (matching the actual schema)
const videoSchema = new mongoose.Schema({
  className: String,
  subject: String,
  title: String,
  level: String,
  videoID: String,
  videoUrl: String,
  thumbnail: String,
  additionalClasses: [String],
  subtitles: Array,
  hasSubtitles: Boolean,
  subtitleGenerationStatus: String
});

const Videos = mongoose.model('videos', videoSchema);

async function testVideoDatabase() {
  try {
    console.log('🔗 Connecting to MongoDB...');
    await mongoose.connection.asPromise();
    console.log('✅ MongoDB Connected Successfully');
    
    // Test 1: Count total videos
    console.log('\n1️⃣ Testing Video Count...');
    const totalVideos = await Videos.countDocuments();
    console.log(`✅ Total videos in database: ${totalVideos}`);
    
    if (totalVideos === 0) {
      console.log('❌ No videos found! Database may be empty.');
      return;
    }
    
    // Test 2: Get sample videos
    console.log('\n2️⃣ Testing Video Retrieval...');
    const sampleVideos = await Videos.find().limit(5);
    console.log(`✅ Retrieved ${sampleVideos.length} sample videos:`);
    
    sampleVideos.forEach((video, index) => {
      console.log(`   ${index + 1}. ${video.title}`);
      console.log(`      Level: ${video.level}`);
      console.log(`      Class: ${video.className}`);
      console.log(`      Subject: ${video.subject}`);
      console.log(`      VideoID: ${video.videoID}`);
      console.log('');
    });
    
    // Test 3: Test level-based filtering (like the API does)
    console.log('\n3️⃣ Testing Level-Based Filtering...');
    const primaryVideos = await Videos.find({ level: 'primary' });
    const secondaryVideos = await Videos.find({ level: 'secondary' });
    const advanceVideos = await Videos.find({ level: 'advance' });
    
    console.log(`✅ Primary videos: ${primaryVideos.length}`);
    console.log(`✅ Secondary videos: ${secondaryVideos.length}`);
    console.log(`✅ Advance videos: ${advanceVideos.length}`);
    
    // Test 4: Test subject filtering
    console.log('\n4️⃣ Testing Subject Filtering...');
    const subjects = await Videos.distinct('subject');
    console.log(`✅ Available subjects: ${subjects.join(', ')}`);
    
    // Test 5: Simulate API query (primary level, all subjects)
    console.log('\n5️⃣ Simulating API Query (Primary Level)...');
    const apiQuery = await Videos.find({ 
      level: new RegExp('^primary$', 'i') 
    });
    console.log(`✅ API simulation returned ${apiQuery.length} videos`);
    
    if (apiQuery.length > 0) {
      console.log('✅ Sample API result:');
      const sample = apiQuery[0];
      console.log(`   Title: ${sample.title}`);
      console.log(`   Level: ${sample.level}`);
      console.log(`   Subject: ${sample.subject}`);
      console.log(`   VideoID: ${sample.videoID}`);
    }
    
    console.log('\n🎉 All video database tests PASSED!');
    console.log('✅ Database is ready for video lesson API');
    console.log('✅ Videos are properly stored and accessible');
    console.log('✅ Filtering by level and subject works correctly');
    
  } catch (error) {
    console.error('❌ Video database test FAILED:', error.message);
    
    if (error.message.includes('ETIMEOUT')) {
      console.error('\n🔧 Network Issue Detected. Try:');
      console.error('   1. Check internet connection');
      console.error('   2. Verify MongoDB Atlas IP whitelist');
      console.error('   3. Change DNS to Google (*******)');
    }
  } finally {
    await mongoose.disconnect();
    console.log('\n🔌 MongoDB Disconnected');
  }
}

testVideoDatabase();
