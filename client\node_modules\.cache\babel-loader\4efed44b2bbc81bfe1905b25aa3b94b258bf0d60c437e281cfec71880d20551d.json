{"ast": null, "code": "import { setTime as utilSetTime } from \"../utils/timeUtil\";\nexport default function useTimeSelection(_ref) {\n  var value = _ref.value,\n    generateConfig = _ref.generateConfig,\n    disabledMinutes = _ref.disabledMinutes,\n    disabledSeconds = _ref.disabledSeconds,\n    minutes = _ref.minutes,\n    seconds = _ref.seconds,\n    use12Hours = _ref.use12Hours;\n  var setTime = function setTime(isNewPM, newHour, newMinute, newSecond) {\n    var newDate = value || generateConfig.getNow();\n    var mergedHour = Math.max(0, newHour);\n    var mergedMinute = Math.max(0, newMinute);\n    var mergedSecond = Math.max(0, newSecond);\n    var newDisabledMinutes = disabledMinutes && disabledMinutes(mergedHour);\n    if (newDisabledMinutes !== null && newDisabledMinutes !== void 0 && newDisabledMinutes.includes(mergedMinute)) {\n      // find the first available minute in minutes\n      var availableMinute = minutes.find(function (i) {\n        return !newDisabledMinutes.includes(i.value);\n      });\n      if (availableMinute) {\n        mergedMinute = availableMinute.value;\n      } else {\n        return null;\n      }\n    }\n    var newDisabledSeconds = disabledSeconds && disabledSeconds(mergedHour, mergedMinute);\n    if (newDisabledSeconds !== null && newDisabledSeconds !== void 0 && newDisabledSeconds.includes(mergedSecond)) {\n      // find the first available second in seconds\n      var availableSecond = seconds.find(function (i) {\n        return !newDisabledSeconds.includes(i.value);\n      });\n      if (availableSecond) {\n        mergedSecond = availableSecond.value;\n      } else {\n        return null;\n      }\n    }\n    newDate = utilSetTime(generateConfig, newDate, !use12Hours || !isNewPM ? mergedHour : mergedHour + 12, mergedMinute, mergedSecond);\n    return newDate;\n  };\n  return setTime;\n}", "map": {"version": 3, "names": ["setTime", "utilSetTime", "useTimeSelection", "_ref", "value", "generateConfig", "disabledMinutes", "disabledSeconds", "minutes", "seconds", "use12Hours", "isNewPM", "newHour", "newMinute", "newSecond", "newDate", "getNow", "mergedHour", "Math", "max", "mergedMinute", "mergedSecond", "newDisabledMinutes", "includes", "availableMinute", "find", "i", "newDisabledSeconds", "availableSecond"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/node_modules/rc-picker/es/hooks/useTimeSelection.js"], "sourcesContent": ["import { setTime as utilSetTime } from \"../utils/timeUtil\";\nexport default function useTimeSelection(_ref) {\n  var value = _ref.value,\n    generateConfig = _ref.generateConfig,\n    disabledMinutes = _ref.disabledMinutes,\n    disabledSeconds = _ref.disabledSeconds,\n    minutes = _ref.minutes,\n    seconds = _ref.seconds,\n    use12Hours = _ref.use12Hours;\n  var setTime = function setTime(isNewPM, newHour, newMinute, newSecond) {\n    var newDate = value || generateConfig.getNow();\n    var mergedHour = Math.max(0, newHour);\n    var mergedMinute = Math.max(0, newMinute);\n    var mergedSecond = Math.max(0, newSecond);\n    var newDisabledMinutes = disabledMinutes && disabledMinutes(mergedHour);\n    if (newDisabledMinutes !== null && newDisabledMinutes !== void 0 && newDisabledMinutes.includes(mergedMinute)) {\n      // find the first available minute in minutes\n      var availableMinute = minutes.find(function (i) {\n        return !newDisabledMinutes.includes(i.value);\n      });\n      if (availableMinute) {\n        mergedMinute = availableMinute.value;\n      } else {\n        return null;\n      }\n    }\n    var newDisabledSeconds = disabledSeconds && disabledSeconds(mergedHour, mergedMinute);\n    if (newDisabledSeconds !== null && newDisabledSeconds !== void 0 && newDisabledSeconds.includes(mergedSecond)) {\n      // find the first available second in seconds\n      var availableSecond = seconds.find(function (i) {\n        return !newDisabledSeconds.includes(i.value);\n      });\n      if (availableSecond) {\n        mergedSecond = availableSecond.value;\n      } else {\n        return null;\n      }\n    }\n    newDate = utilSetTime(generateConfig, newDate, !use12Hours || !isNewPM ? mergedHour : mergedHour + 12, mergedMinute, mergedSecond);\n    return newDate;\n  };\n  return setTime;\n}"], "mappings": "AAAA,SAASA,OAAO,IAAIC,WAAW,QAAQ,mBAAmB;AAC1D,eAAe,SAASC,gBAAgBA,CAACC,IAAI,EAAE;EAC7C,IAAIC,KAAK,GAAGD,IAAI,CAACC,KAAK;IACpBC,cAAc,GAAGF,IAAI,CAACE,cAAc;IACpCC,eAAe,GAAGH,IAAI,CAACG,eAAe;IACtCC,eAAe,GAAGJ,IAAI,CAACI,eAAe;IACtCC,OAAO,GAAGL,IAAI,CAACK,OAAO;IACtBC,OAAO,GAAGN,IAAI,CAACM,OAAO;IACtBC,UAAU,GAAGP,IAAI,CAACO,UAAU;EAC9B,IAAIV,OAAO,GAAG,SAASA,OAAOA,CAACW,OAAO,EAAEC,OAAO,EAAEC,SAAS,EAAEC,SAAS,EAAE;IACrE,IAAIC,OAAO,GAAGX,KAAK,IAAIC,cAAc,CAACW,MAAM,CAAC,CAAC;IAC9C,IAAIC,UAAU,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEP,OAAO,CAAC;IACrC,IAAIQ,YAAY,GAAGF,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEN,SAAS,CAAC;IACzC,IAAIQ,YAAY,GAAGH,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEL,SAAS,CAAC;IACzC,IAAIQ,kBAAkB,GAAGhB,eAAe,IAAIA,eAAe,CAACW,UAAU,CAAC;IACvE,IAAIK,kBAAkB,KAAK,IAAI,IAAIA,kBAAkB,KAAK,KAAK,CAAC,IAAIA,kBAAkB,CAACC,QAAQ,CAACH,YAAY,CAAC,EAAE;MAC7G;MACA,IAAII,eAAe,GAAGhB,OAAO,CAACiB,IAAI,CAAC,UAAUC,CAAC,EAAE;QAC9C,OAAO,CAACJ,kBAAkB,CAACC,QAAQ,CAACG,CAAC,CAACtB,KAAK,CAAC;MAC9C,CAAC,CAAC;MACF,IAAIoB,eAAe,EAAE;QACnBJ,YAAY,GAAGI,eAAe,CAACpB,KAAK;MACtC,CAAC,MAAM;QACL,OAAO,IAAI;MACb;IACF;IACA,IAAIuB,kBAAkB,GAAGpB,eAAe,IAAIA,eAAe,CAACU,UAAU,EAAEG,YAAY,CAAC;IACrF,IAAIO,kBAAkB,KAAK,IAAI,IAAIA,kBAAkB,KAAK,KAAK,CAAC,IAAIA,kBAAkB,CAACJ,QAAQ,CAACF,YAAY,CAAC,EAAE;MAC7G;MACA,IAAIO,eAAe,GAAGnB,OAAO,CAACgB,IAAI,CAAC,UAAUC,CAAC,EAAE;QAC9C,OAAO,CAACC,kBAAkB,CAACJ,QAAQ,CAACG,CAAC,CAACtB,KAAK,CAAC;MAC9C,CAAC,CAAC;MACF,IAAIwB,eAAe,EAAE;QACnBP,YAAY,GAAGO,eAAe,CAACxB,KAAK;MACtC,CAAC,MAAM;QACL,OAAO,IAAI;MACb;IACF;IACAW,OAAO,GAAGd,WAAW,CAACI,cAAc,EAAEU,OAAO,EAAE,CAACL,UAAU,IAAI,CAACC,OAAO,GAAGM,UAAU,GAAGA,UAAU,GAAG,EAAE,EAAEG,YAAY,EAAEC,YAAY,CAAC;IAClI,OAAON,OAAO;EAChB,CAAC;EACD,OAAOf,OAAO;AAChB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}