{"version": 3, "file": "static/css/319.5354c6f5.chunk.css", "mappings": "AAAA,SACI,mBACJ,CAEA,oBACI,eACJ,CAEA,iBACI,YAAa,CACb,qBAAsB,CACtB,QACJ,CAEA,sBACI,eAAgB,CAEhB,gBAAiB,CADjB,SAEJ,CAEA,gBACI,yBAAkB,CAAlB,iBACJ,CAEA,sBACI,UACJ,CAEA,oBACI,gBACJ,CAEA,uBAEI,cAAiB,CADjB,SAEJ,CAEA,eAGI,wBAAoC,CAFpC,gBAAiB,CACjB,eAAgB,CAEhB,gBACJ,CAEA,sBAEI,kBAAmB,CADnB,YAAa,CAEb,QAAS,CACT,kBACJ,CAEA,kBAII,qBAAsB,CADtB,iBAAkB,CADlB,WAAY,CADZ,UAIJ,CAEA,eACI,iBACJ,CAEA,yCACI,sBACI,SACJ,CAEA,uBACI,UACJ,CAEA,eACI,gBACJ,CACJ", "sources": ["pages/user/AboutUs/index.css"], "sourcesContent": [".AboutUs {\r\n    padding-bottom: 20px;\r\n}\r\n\r\n.AboutUs .info-para {\r\n    line-height: 1.6;\r\n}\r\n\r\n.AboutUs .rating {\r\n    display: flex;\r\n    flex-direction: column;\r\n    gap: 20px;\r\n}\r\n\r\n.AboutUs .rating-text {\r\n    margin-top: 10px;\r\n    width: 50%;\r\n    padding: 5px 10px;\r\n}\r\n\r\n.AboutUs button {\r\n    width: fit-content;\r\n}\r\n\r\n.AboutUs button:hover {\r\n    opacity: 0.7;\r\n}\r\n\r\n.AboutUs .p-ratings {\r\n    padding-top: 20px;\r\n}\r\n\r\n.AboutUs .p-rating-div {\r\n    width: 40%;\r\n    padding: 10px 0px;\r\n}\r\n\r\n.AboutUs .text {\r\n    margin-left: 55px;\r\n    margin-top: 10px;\r\n    border: 1px solid rgb(186, 186, 186);\r\n    padding: 5px 10px;\r\n}\r\n\r\n.AboutUs .profile-row {\r\n    display: flex;\r\n    align-items: center;\r\n    gap: 10px;\r\n    padding-bottom: 5px;\r\n}\r\n\r\n.AboutUs .profile {\r\n    width: 50px;\r\n    height: 50px;\r\n    border-radius: 50%;\r\n    border: 1px solid grey;\r\n}\r\n\r\n.AboutUs .rate {\r\n    padding-left: 55px;\r\n}\r\n\r\n@media only screen and (max-width: 768px) {\r\n    .AboutUs .rating-text {\r\n        width: 90%;\r\n    }\r\n\r\n    .AboutUs .p-rating-div {\r\n        width: 100%;\r\n    }\r\n\r\n    .AboutUs .text {\r\n        margin-right: 5px;\r\n    }\r\n}"], "names": [], "sourceRoot": ""}