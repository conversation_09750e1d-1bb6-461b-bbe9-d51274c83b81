"use strict";(self.webpackChunkclient=self.webpackChunkclient||[]).push([[416],{6584:(e,t,n)=>{n.d(t,{Z:()=>r});var a={};!function e(t,n,a,r){var o=!!(t.Worker&&t.Blob&&t.Promise&&t.OffscreenCanvas&&t.OffscreenCanvasRenderingContext2D&&t.HTMLCanvasElement&&t.HTMLCanvasElement.prototype.transferControlToOffscreen&&t.URL&&t.URL.createObjectURL),i="function"===typeof Path2D&&"function"===typeof DOMMatrix,s=function(){if(!t.OffscreenCanvas)return!1;var e=new OffscreenCanvas(1,1),n=e.getContext("2d");n.fillRect(0,0,1,1);var a=e.transferToImageBitmap();try{n.createPattern(a,"no-repeat")}catch(r){return!1}return!0}();function c(){}function l(e){var a=n.exports.Promise,r=void 0!==a?a:t.Promise;return"function"===typeof r?new r(e):(e(c,c),null)}var f,u,h,d=(f=s,u=new Map,{transform:function(e){if(f)return e;if(u.has(e))return u.get(e);var t=new OffscreenCanvas(e.width,e.height);return t.getContext("2d").drawImage(e,0,0),u.set(e,t),t},clear:function(){u.clear()}}),m=function(){var e,t,n=Math.floor(1e3/60),a={},r=0;return"function"===typeof requestAnimationFrame&&"function"===typeof cancelAnimationFrame?(e=function(e){var t=Math.random();return a[t]=requestAnimationFrame((function o(i){r===i||r+n-1<i?(r=i,delete a[t],e()):a[t]=requestAnimationFrame(o)})),t},t=function(e){a[e]&&cancelAnimationFrame(a[e])}):(e=function(e){return setTimeout(e,n)},t=function(e){return clearTimeout(e)}),{frame:e,cancel:t}}(),p=function(){var t,n,r={};return function(){if(t)return t;if(!a&&o){var i=["var CONFETTI, SIZE = {}, module = {};","("+e.toString()+")(this, module, true, SIZE);","onmessage = function(msg) {","  if (msg.data.options) {","    CONFETTI(msg.data.options).then(function () {","      if (msg.data.callback) {","        postMessage({ callback: msg.data.callback });","      }","    });","  } else if (msg.data.reset) {","    CONFETTI && CONFETTI.reset();","  } else if (msg.data.resize) {","    SIZE.width = msg.data.resize.width;","    SIZE.height = msg.data.resize.height;","  } else if (msg.data.canvas) {","    SIZE.width = msg.data.canvas.width;","    SIZE.height = msg.data.canvas.height;","    CONFETTI = module.exports.create(msg.data.canvas);","  }","}"].join("\n");try{t=new Worker(URL.createObjectURL(new Blob([i])))}catch(s){return void 0!==typeof console&&"function"===typeof console.warn&&console.warn("\ud83c\udf8a Could not load worker",s),null}!function(e){function t(t,n){e.postMessage({options:t||{},callback:n})}e.init=function(t){var n=t.transferControlToOffscreen();e.postMessage({canvas:n},[n])},e.fire=function(a,o,i){if(n)return t(a,null),n;var s=Math.random().toString(36).slice(2);return n=l((function(o){function c(t){t.data.callback===s&&(delete r[s],e.removeEventListener("message",c),n=null,d.clear(),i(),o())}e.addEventListener("message",c),t(a,s),r[s]=c.bind(null,{data:{callback:s}})}))},e.reset=function(){for(var t in e.postMessage({reset:!0}),r)r[t](),delete r[t]}}(t)}return t}}(),g={particleCount:50,angle:90,spread:45,startVelocity:45,decay:.9,gravity:1,drift:0,ticks:200,x:.5,y:.5,shapes:["square","circle"],zIndex:100,colors:["#26ccff","#a25afd","#ff5e7e","#88ff5a","#fcff42","#ffa62d","#ff36ff"],disableForReducedMotion:!1,scalar:1};function v(e,t,n){return function(e,t){return t?t(e):e}(e&&(null!==(a=e[t])&&void 0!==a)?e[t]:g[t],n);var a}function b(e){return e<0?0:Math.floor(e)}function M(e){return parseInt(e,16)}function y(e){return e.map(w)}function w(e){var t=String(e).replace(/[^0-9a-f]/gi,"");return t.length<6&&(t=t[0]+t[0]+t[1]+t[1]+t[2]+t[2]),{r:M(t.substring(0,2)),g:M(t.substring(2,4)),b:M(t.substring(4,6))}}function x(e){e.width=document.documentElement.clientWidth,e.height=document.documentElement.clientHeight}function E(e){var t=e.getBoundingClientRect();e.width=t.width,e.height=t.height}function C(e){var t=e.angle*(Math.PI/180),n=e.spread*(Math.PI/180);return{x:e.x,y:e.y,wobble:10*Math.random(),wobbleSpeed:Math.min(.11,.1*Math.random()+.05),velocity:.5*e.startVelocity+Math.random()*e.startVelocity,angle2D:-t+(.5*n-Math.random()*n),tiltAngle:(.5*Math.random()+.25)*Math.PI,color:e.color,shape:e.shape,tick:0,totalTicks:e.ticks,decay:e.decay,drift:e.drift,random:Math.random()+2,tiltSin:0,tiltCos:0,wobbleX:0,wobbleY:0,gravity:3*e.gravity,ovalScalar:.6,scalar:e.scalar,flat:e.flat}}function P(e,t){t.x+=Math.cos(t.angle2D)*t.velocity+t.drift,t.y+=Math.sin(t.angle2D)*t.velocity+t.gravity,t.velocity*=t.decay,t.flat?(t.wobble=0,t.wobbleX=t.x+10*t.scalar,t.wobbleY=t.y+10*t.scalar,t.tiltSin=0,t.tiltCos=0,t.random=1):(t.wobble+=t.wobbleSpeed,t.wobbleX=t.x+10*t.scalar*Math.cos(t.wobble),t.wobbleY=t.y+10*t.scalar*Math.sin(t.wobble),t.tiltAngle+=.1,t.tiltSin=Math.sin(t.tiltAngle),t.tiltCos=Math.cos(t.tiltAngle),t.random=Math.random()+2);var n=t.tick++/t.totalTicks,a=t.x+t.random*t.tiltCos,r=t.y+t.random*t.tiltSin,o=t.wobbleX+t.random*t.tiltCos,s=t.wobbleY+t.random*t.tiltSin;if(e.fillStyle="rgba("+t.color.r+", "+t.color.g+", "+t.color.b+", "+(1-n)+")",e.beginPath(),i&&"path"===t.shape.type&&"string"===typeof t.shape.path&&Array.isArray(t.shape.matrix))e.fill(function(e,t,n,a,r,o,i){var s=new Path2D(e),c=new Path2D;c.addPath(s,new DOMMatrix(t));var l=new Path2D;return l.addPath(c,new DOMMatrix([Math.cos(i)*r,Math.sin(i)*r,-Math.sin(i)*o,Math.cos(i)*o,n,a])),l}(t.shape.path,t.shape.matrix,t.x,t.y,.1*Math.abs(o-a),.1*Math.abs(s-r),Math.PI/10*t.wobble));else if("bitmap"===t.shape.type){var c=Math.PI/10*t.wobble,l=.1*Math.abs(o-a),f=.1*Math.abs(s-r),u=t.shape.bitmap.width*t.scalar,h=t.shape.bitmap.height*t.scalar,m=new DOMMatrix([Math.cos(c)*l,Math.sin(c)*l,-Math.sin(c)*f,Math.cos(c)*f,t.x,t.y]);m.multiplySelf(new DOMMatrix(t.shape.matrix));var p=e.createPattern(d.transform(t.shape.bitmap),"no-repeat");p.setTransform(m),e.globalAlpha=1-n,e.fillStyle=p,e.fillRect(t.x-u/2,t.y-h/2,u,h),e.globalAlpha=1}else if("circle"===t.shape)e.ellipse?e.ellipse(t.x,t.y,Math.abs(o-a)*t.ovalScalar,Math.abs(s-r)*t.ovalScalar,Math.PI/10*t.wobble,0,2*Math.PI):function(e,t,n,a,r,o,i,s,c){e.save(),e.translate(t,n),e.rotate(o),e.scale(a,r),e.arc(0,0,1,i,s,c),e.restore()}(e,t.x,t.y,Math.abs(o-a)*t.ovalScalar,Math.abs(s-r)*t.ovalScalar,Math.PI/10*t.wobble,0,2*Math.PI);else if("star"===t.shape)for(var g=Math.PI/2*3,v=4*t.scalar,b=8*t.scalar,M=t.x,y=t.y,w=5,x=Math.PI/w;w--;)M=t.x+Math.cos(g)*b,y=t.y+Math.sin(g)*b,e.lineTo(M,y),g+=x,M=t.x+Math.cos(g)*v,y=t.y+Math.sin(g)*v,e.lineTo(M,y),g+=x;else e.moveTo(Math.floor(t.x),Math.floor(t.y)),e.lineTo(Math.floor(t.wobbleX),Math.floor(r)),e.lineTo(Math.floor(o),Math.floor(s)),e.lineTo(Math.floor(a),Math.floor(t.wobbleY));return e.closePath(),e.fill(),t.tick<t.totalTicks}function I(e,n){var i,s=!e,c=!!v(n||{},"resize"),f=!1,u=v(n,"disableForReducedMotion",Boolean),h=o&&!!v(n||{},"useWorker")?p():null,g=s?x:E,M=!(!e||!h)&&!!e.__confetti_initialized,w="function"===typeof matchMedia&&matchMedia("(prefers-reduced-motion)").matches;function I(t,n,o){for(var s,c,f=v(t,"particleCount",b),u=v(t,"angle",Number),h=v(t,"spread",Number),p=v(t,"startVelocity",Number),M=v(t,"decay",Number),w=v(t,"gravity",Number),x=v(t,"drift",Number),E=v(t,"colors",y),I=v(t,"ticks",Number),k=v(t,"shapes"),T=v(t,"scalar"),R=!!v(t,"flat"),S=function(e){var t=v(e,"origin",Object);return t.x=v(t,"x",Number),t.y=v(t,"y",Number),t}(t),O=f,B=[],A=e.width*S.x,L=e.height*S.y;O--;)B.push(C({x:A,y:L,angle:u,spread:h,startVelocity:p,color:E[O%E.length],shape:k[(s=0,c=k.length,Math.floor(Math.random()*(c-s))+s)],ticks:I,decay:M,gravity:w,drift:x,scalar:T,flat:R}));return i?i.addFettis(B):(i=function(e,t,n,o,i){var s,c,f=t.slice(),u=e.getContext("2d"),h=l((function(t){function l(){s=c=null,u.clearRect(0,0,o.width,o.height),d.clear(),i(),t()}s=m.frame((function t(){!a||o.width===r.width&&o.height===r.height||(o.width=e.width=r.width,o.height=e.height=r.height),o.width||o.height||(n(e),o.width=e.width,o.height=e.height),u.clearRect(0,0,o.width,o.height),(f=f.filter((function(e){return P(u,e)}))).length?s=m.frame(t):l()})),c=l}));return{addFettis:function(e){return f=f.concat(e),h},canvas:e,promise:h,reset:function(){s&&m.cancel(s),c&&c()}}}(e,B,g,n,o),i.promise)}function k(n){var a=u||v(n,"disableForReducedMotion",Boolean),r=v(n,"zIndex",Number);if(a&&w)return l((function(e){e()}));s&&i?e=i.canvas:s&&!e&&(e=function(e){var t=document.createElement("canvas");return t.style.position="fixed",t.style.top="0px",t.style.left="0px",t.style.pointerEvents="none",t.style.zIndex=e,t}(r),document.body.appendChild(e)),c&&!M&&g(e);var o={width:e.width,height:e.height};function d(){if(h){var t={getBoundingClientRect:function(){if(!s)return e.getBoundingClientRect()}};return g(t),void h.postMessage({resize:{width:t.width,height:t.height}})}o.width=o.height=null}function m(){i=null,c&&(f=!1,t.removeEventListener("resize",d)),s&&e&&(document.body.contains(e)&&document.body.removeChild(e),e=null,M=!1)}return h&&!M&&h.init(e),M=!0,h&&(e.__confetti_initialized=!0),c&&!f&&(f=!0,t.addEventListener("resize",d,!1)),h?h.fire(n,o,m):I(n,o,m)}return k.reset=function(){h&&h.reset(),i&&i.reset()},k}function k(){return h||(h=I(null,{useWorker:!0,resize:!0})),h}n.exports=function(){return k().apply(this,arguments)},n.exports.reset=function(){k().reset()},n.exports.create=I,n.exports.shapeFromPath=function(e){if(!i)throw new Error("path confetti are not supported in this browser");var t,n;"string"===typeof e?t=e:(t=e.path,n=e.matrix);var a=new Path2D(t),r=document.createElement("canvas").getContext("2d");if(!n){for(var o,s,c=1e3,l=c,f=c,u=0,h=0,d=0;d<c;d+=2)for(var m=0;m<c;m+=2)r.isPointInPath(a,d,m,"nonzero")&&(l=Math.min(l,d),f=Math.min(f,m),u=Math.max(u,d),h=Math.max(h,m));o=u-l,s=h-f;var p=Math.min(10/o,10/s);n=[p,0,0,p,-Math.round(o/2+l)*p,-Math.round(s/2+f)*p]}return{type:"path",path:t,matrix:n}},n.exports.shapeFromText=function(e){var t,n=1,a="#000000",r='"Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji", "EmojiOne Color", "Android Emoji", "Twemoji Mozilla", "system emoji", sans-serif';"string"===typeof e?t=e:(t=e.text,n="scalar"in e?e.scalar:n,r="fontFamily"in e?e.fontFamily:r,a="color"in e?e.color:a);var o=10*n,i=o+"px "+r,s=new OffscreenCanvas(o,o),c=s.getContext("2d");c.font=i;var l=c.measureText(t),f=Math.ceil(l.actualBoundingBoxRight+l.actualBoundingBoxLeft),u=Math.ceil(l.actualBoundingBoxAscent+l.actualBoundingBoxDescent),h=l.actualBoundingBoxLeft+2,d=l.actualBoundingBoxAscent+2;f+=4,u+=4,(c=(s=new OffscreenCanvas(f,u)).getContext("2d")).font=i,c.fillStyle=a,c.fillText(t,h,d);var m=1/n;return{type:"bitmap",bitmap:s.transferToImageBitmap(),matrix:[m,0,0,m,-f*m/2,-u*m/2]}}}(function(){return"undefined"!==typeof window?window:"undefined"!==typeof self?self:this||{}}(),a,!1);const r=a.exports;a.exports.create},3791:(e,t,n)=>{n.d(t,{M:()=>g});var a=n(2791),r=n(2199);function o(){const e=(0,a.useRef)(!1);return(0,r.L)((()=>(e.current=!0,()=>{e.current=!1})),[]),e}var i=n(8771);var s=n(131),c=n(1421);class l extends a.Component{getSnapshotBeforeUpdate(e){const t=this.props.childRef.current;if(t&&e.isPresent&&!this.props.isPresent){const e=this.props.sizeRef.current;e.height=t.offsetHeight||0,e.width=t.offsetWidth||0,e.top=t.offsetTop,e.left=t.offsetLeft}return null}componentDidUpdate(){}render(){return this.props.children}}function f(e){let{children:t,isPresent:n}=e;const r=(0,a.useId)(),o=(0,a.useRef)(null),i=(0,a.useRef)({width:0,height:0,top:0,left:0});return(0,a.useInsertionEffect)((()=>{const{width:e,height:t,top:a,left:s}=i.current;if(n||!o.current||!e||!t)return;o.current.dataset.motionPopId=r;const c=document.createElement("style");return document.head.appendChild(c),c.sheet&&c.sheet.insertRule('\n          [data-motion-pop-id="'.concat(r,'"] {\n            position: absolute !important;\n            width: ').concat(e,"px !important;\n            height: ").concat(t,"px !important;\n            top: ").concat(a,"px !important;\n            left: ").concat(s,"px !important;\n          }\n        ")),()=>{document.head.removeChild(c)}}),[n]),a.createElement(l,{isPresent:n,childRef:o,sizeRef:i},a.cloneElement(t,{ref:o}))}const u=e=>{let{children:t,initial:n,isPresent:r,onExitComplete:o,custom:i,presenceAffectsLayout:l,mode:u}=e;const d=(0,c.h)(h),m=(0,a.useId)(),p=(0,a.useMemo)((()=>({id:m,initial:n,isPresent:r,custom:i,onExitComplete:e=>{d.set(e,!0);for(const t of d.values())if(!t)return;o&&o()},register:e=>(d.set(e,!1),()=>d.delete(e))})),l?void 0:[r]);return(0,a.useMemo)((()=>{d.forEach(((e,t)=>d.set(t,!1)))}),[r]),a.useEffect((()=>{!r&&!d.size&&o&&o()}),[r]),"popLayout"===u&&(t=a.createElement(f,{isPresent:r},t)),a.createElement(s.O.Provider,{value:p},t)};function h(){return new Map}var d=n(7497);var m=n(5956);const p=e=>e.key||"";const g=e=>{let{children:t,custom:n,initial:s=!0,onExitComplete:c,exitBeforeEnter:l,presenceAffectsLayout:f=!0,mode:h="sync"}=e;(0,m.k)(!l,"Replace exitBeforeEnter with mode='wait'");const g=(0,a.useContext)(d.p).forceRender||function(){const e=o(),[t,n]=(0,a.useState)(0),r=(0,a.useCallback)((()=>{e.current&&n(t+1)}),[t]);return[(0,a.useCallback)((()=>i.Wi.postRender(r)),[r]),t]}()[0],v=o(),b=function(e){const t=[];return a.Children.forEach(e,(e=>{(0,a.isValidElement)(e)&&t.push(e)})),t}(t);let M=b;const y=(0,a.useRef)(new Map).current,w=(0,a.useRef)(M),x=(0,a.useRef)(new Map).current,E=(0,a.useRef)(!0);var C;if((0,r.L)((()=>{E.current=!1,function(e,t){e.forEach((e=>{const n=p(e);t.set(n,e)}))}(b,x),w.current=M})),C=()=>{E.current=!0,x.clear(),y.clear()},(0,a.useEffect)((()=>()=>C()),[]),E.current)return a.createElement(a.Fragment,null,M.map((e=>a.createElement(u,{key:p(e),isPresent:!0,initial:!!s&&void 0,presenceAffectsLayout:f,mode:h},e))));M=[...M];const P=w.current.map(p),I=b.map(p),k=P.length;for(let a=0;a<k;a++){const e=P[a];-1!==I.indexOf(e)||y.has(e)||y.set(e,void 0)}return"wait"===h&&y.size&&(M=[]),y.forEach(((e,t)=>{if(-1!==I.indexOf(t))return;const r=x.get(t);if(!r)return;const o=P.indexOf(t);let i=e;if(!i){const e=()=>{y.delete(t);const e=Array.from(x.keys()).filter((e=>!I.includes(e)));if(e.forEach((e=>x.delete(e))),w.current=b.filter((n=>{const a=p(n);return a===t||e.includes(a)})),!y.size){if(!1===v.current)return;g(),c&&c()}};i=a.createElement(u,{key:p(r),isPresent:!1,onExitComplete:e,custom:n,presenceAffectsLayout:f,mode:h},r),y.set(t,i)}M.splice(o,0,i)})),M=M.map((e=>{const t=e.key;return y.has(t)?e:a.createElement(u,{key:p(e),isPresent:!0,presenceAffectsLayout:f,mode:h},e)})),a.createElement(a.Fragment,null,y.size?M:M.map((e=>(0,a.cloneElement)(e))))}}}]);
//# sourceMappingURL=416.5d6e5487.chunk.js.map