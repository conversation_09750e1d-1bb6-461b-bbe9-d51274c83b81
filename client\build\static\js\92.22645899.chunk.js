(self.webpackChunkclient=self.webpackChunkclient||[]).push([[92],{2713:(e,t,a)=>{"use strict";a.d(t,{A4:()=>x,AL:()=>o,FM:()=>h,HH:()=>d,Hh:()=>v,KB:()=>m,MC:()=>g,Qk:()=>u,SL:()=>c,TA:()=>i,X9:()=>n,cN:()=>l,f_:()=>y,o$:()=>r,u8:()=>p});const{default:s}=a(3371),r=async e=>{try{return await s.post("/api/study/get-study-content",e)}catch(t){return t.response}},n=async()=>{try{return(await s.get("/api/study/videos-subtitle-status")).data}catch(t){var e;return(null===(e=t.response)||void 0===e?void 0:e.data)||{success:!1,message:"Failed to fetch videos"}}},l=async function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;try{const a=e instanceof FormData?{headers:{"Content-Type":"multipart/form-data"},timeout:6e5,onUploadProgress:t?e=>{const a=Math.round(100*e.loaded/e.total);t(a,e.loaded,e.total)}:void 0}:{timeout:6e4};return await s.post("/api/study/add-video",e,a)}catch(a){return a.response}},o=async e=>{try{return await s.post("/api/study/add-note",e,{headers:{"Content-Type":"multipart/form-data"}})}catch(t){return t.response}},c=async e=>{try{return await s.post("/api/study/add-past-paper",e,{headers:{"Content-Type":"multipart/form-data"}})}catch(t){return t.response}},i=async e=>{try{return await s.post("/api/study/add-book",e,{headers:{"Content-Type":"multipart/form-data"}})}catch(t){return t.response}},d=async(e,t)=>{try{let a={headers:{"Content-Type":"application/json"}};t instanceof FormData&&(a.headers["Content-Type"]="multipart/form-data");return await s.put("/api/study/update-video/".concat(e),t,a)}catch(a){return a.response}},u=async(e,t)=>{try{return await s.put("/api/study/update-note/".concat(e),t,{headers:{"Content-Type":"multipart/form-data"}})}catch(a){return a.response}},m=async(e,t)=>{try{return await s.put("/api/study/update-past-paper/".concat(e),t,{headers:{"Content-Type":"multipart/form-data"}})}catch(a){return a.response}},p=async(e,t)=>{try{return await s.put("/api/study/update-book/".concat(e),t,{headers:{"Content-Type":"multipart/form-data"}})}catch(a){return a.response}},h=async e=>{try{return await s.delete("/api/study/delete-video/".concat(e))}catch(t){return t.response}},y=async e=>{try{return await s.delete("/api/study/delete-note/".concat(e))}catch(t){return t.response}},x=async e=>{try{return await s.delete("/api/study/delete-past-paper/".concat(e))}catch(t){return t.response}},g=async e=>{try{return await s.delete("/api/study/delete-book/".concat(e))}catch(t){return t.response}},v=async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};try{const t=new URLSearchParams;e.materialType&&t.append("materialType",e.materialType),e.level&&t.append("level",e.level),e.className&&t.append("className",e.className),e.subject&&t.append("subject",e.subject);return await s.get("/api/study/admin/all-materials?".concat(t.toString()))}catch(t){return t.response}}},1304:(e,t,a)=>{"use strict";a.d(t,{Lo:()=>r,iq:()=>s,vp:()=>n});const s=["Mathematics","Science and Technology","Geography","Kiswahili","SocialStudies","English","Religion","Arithmetic","Sport and Art","Health and Environment","Civic and Moral","French","Historia ya Tanzania"],r=["Physics","Mathematics","Biology","Chemistry","Geography","Civics","History","Computer","English language","Kiswahili","Commerce","Economics","Advanced Mathematics","Basic Applied Mathematics","Accountancy","Agriculture"],n=["Physics","Mathematics","Biology","Chemistry","Geography","Civics","History","Computer","English language","Kiswahili","Commerce","Economics","Advanced Mathematics","Basic Applied Mathematics","Accountancy","Agriculture"]},2796:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>g});var s=a(2791),r=a(6042),n=a(3791),l=a(2713),o=a(9434),c=a(8247),i=a(3661),d=a(7948),u=a.n(d),m=a(184);u().setAppElement("#root"),i.GlobalWorkerOptions.workerSrc="https://unpkg.com/pdfjs-dist@2.15.349/build/pdf.worker.min.js";if("undefined"!==typeof document){const e=document.createElement("style");e.textContent="\n  @keyframes spin {\n    0% { transform: rotate(0deg); }\n    100% { transform: rotate(360deg); }\n  }\n",document.head.appendChild(e)}const p=e=>{let{modalIsOpen:t,closeModal:a,documentUrl:r}=e;const[n,l]=(0,s.useState)([]),[o,c]=(0,s.useState)(!1),[d,p]=(0,s.useState)(0),[h,y]=(0,s.useState)(0),x=(0,s.useRef)([]),g=(0,s.useRef)(null),v=(0,s.useRef)({}),f=async(e,t,a)=>{const s=[...a];for(let n=t;n<=e.numPages;n++)try{const t=await e.getPage(n);s.push(t),l([...s]),p(n/e.numPages*100),await new Promise((e=>setTimeout(e,50)))}catch(r){console.error("Error loading page ".concat(n,":"),r)}},j=async(e,t)=>{const a=x.current[t];if(a&&!v.current[t]&&g.current)try{v.current[t]=!0;const s=e.getViewport({scale:1}),r=g.current.clientWidth/s.width,n=e.getViewport({scale:r}),l=a.getContext("2d");a.height=n.height,a.width=n.width,a.style.width="100%",a.style.height="auto";const o={canvasContext:l,viewport:n};await e.render(o).promise,console.log("Page ".concat(t+1," rendered"))}catch(s){console.error("Error rendering page ".concat(t+1,":"),s)}finally{v.current[t]=!1}};return(0,s.useEffect)((()=>{t&&r&&(l([]),y(0),p(0),x.current=[],v.current={},(async e=>{try{c(!0),p(0);const t=await i.getDocument(e).promise;console.log("PDF loaded"),y(t.numPages);const a=Math.min(3,t.numPages),s=[];for(let e=1;e<=a;e++){const a=await t.getPage(e);s.push(a),p(e/t.numPages*100)}l(s),c(!1),t.numPages>a&&f(t,a+1,s)}catch(t){console.error("Error loading PDF:",t),c(!1)}})(r))}),[t,r]),(0,s.useEffect)((()=>{n.length>0&&g.current&&n.forEach(((e,t)=>{j(e,t)}))}),[n]),(0,s.useEffect)((()=>{const e=()=>{n.length>0&&n.forEach(((e,t)=>{j(e,t)}))};return window.addEventListener("resize",e),()=>window.removeEventListener("resize",e)}),[n]),(0,m.jsxs)(u(),{isOpen:t,onRequestClose:a,contentLabel:"Document Preview",style:{overlay:{backgroundColor:"rgba(0, 0, 0, 0.75)"},content:{top:"50%",left:"50%",right:"auto",bottom:"auto",marginRight:"-50%",transform:"translate(-50%, -50%)",width:"70%",height:"90%",padding:"20px",borderRadius:"10px",overflow:"hidden"}},children:[(0,m.jsx)("button",{onClick:a,style:{position:"absolute",top:"10px",right:"10px",background:"transparent",border:"none",fontSize:"20px",cursor:"pointer",zIndex:1},children:"X"}),(0,m.jsxs)("div",{ref:g,style:{height:"100%",overflow:"auto",padding:"10px",scrollbarWidth:"thin"},children:[o&&(0,m.jsxs)("div",{style:{display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center",height:"200px",color:"#666"},children:[(0,m.jsx)("div",{style:{width:"50px",height:"50px",border:"3px solid #f3f3f3",borderTop:"3px solid #3498db",borderRadius:"50%",animation:"spin 1s linear infinite",marginBottom:"20px"}}),(0,m.jsx)("p",{children:"Loading PDF..."}),(0,m.jsx)("div",{style:{width:"200px",height:"6px",backgroundColor:"#f3f3f3",borderRadius:"3px",overflow:"hidden",marginTop:"10px"},children:(0,m.jsx)("div",{style:{width:"".concat(d,"%"),height:"100%",backgroundColor:"#3498db",transition:"width 0.3s ease"}})}),(0,m.jsxs)("small",{style:{marginTop:"5px"},children:[Math.round(d),"% loaded"]})]}),n.map(((e,t)=>(0,m.jsx)("div",{style:{marginBottom:"10px",display:"flex",flexDirection:"column",alignItems:"center"},children:(0,m.jsx)("canvas",{ref:e=>{x.current[t]=e},style:{maxWidth:"100%",height:"auto",border:"1px solid black"}})},t))),h>n.length&&!o&&(0,m.jsxs)("div",{style:{textAlign:"center",padding:"20px",color:"#666",fontStyle:"italic"},children:["Loading remaining pages... (",n.length,"/",h,")"]})]})]})};var h=a(2202),y=a(5526),x=a(1304);const g=function(){const{user:e}=(0,o.v9)((e=>e.user)),t=(0,o.I0)(),a=(null===e||void 0===e?void 0:e.level)||"Primary",i=a.toLowerCase(),d="primary"===i?x.iq:"secondary"===i?x.Lo:x.vp;(0,s.useEffect)((()=>{console.log("\ud83d\udcda Study Materials - User Level:",a),console.log("\ud83d\udcda Study Materials - User Level (lowercase):",i),console.log("\ud83d\udcda Study Materials - Subjects List:",d),console.log("\ud83d\udcda Study Materials - User Data:",e)}),[a,i,d,e]);const u="primary"===i?["1","2","3","4","5","6","7"]:"secondary"===i?["Form-1","Form-2","Form-3","Form-4"]:["Form-5","Form-6"],[g,v]=(0,s.useState)("study-notes"),[f,j]=(0,s.useState)((null===e||void 0===e?void 0:e.class)||(null===e||void 0===e?void 0:e.className)||"all"),[b,w]=(0,s.useState)("all"),N=(null===e||void 0===e?void 0:e.class)||(null===e||void 0===e?void 0:e.className),[C,S]=(0,s.useState)([]),[k,E]=(0,s.useState)(!1),[T,P]=(0,s.useState)(null),[F,L]=(0,s.useState)(!1),[M,R]=(0,s.useState)(""),[A,U]=(0,s.useState)([]),[D,I]=(0,s.useState)(!1),[B,O]=(0,s.useState)(""),[_,H]=(0,s.useState)("newest");(0,s.useEffect)((()=>{const t=(null===e||void 0===e?void 0:e.class)||(null===e||void 0===e?void 0:e.className);t&&"all"===f&&!A.length&&j(t)}),[e,f,A.length]),(0,s.useEffect)((()=>{if(null!==e&&void 0!==e&&e.level){d.includes(b)||"all"===b||(console.log("\ud83d\udcda Resetting subject selection due to level change"),w("all"))}}),[null===e||void 0===e?void 0:e.level,d,b]);const G=(0,s.useCallback)((()=>{U(u)}),[u]),W=(0,s.useCallback)((async()=>{if(g&&"default"!==f){E(!0),P(null),t((0,c.YC)());try{const e="all"===f?"all":f.toString().replace("Form-",""),t={content:g,className:e,subject:b};a&&(t.level=a);const s=await(0,l.o$)(t);if(200===s.status&&s.data.success){const e="empty"===s.data.data?[]:s.data.data;S(e)}else S([]),P("Failed to fetch ".concat(g,". Please try again."))}catch(T){console.error("Error fetching study material:",T),S([]),P("Unable to load ".concat(g,". Please check your connection and try again."))}finally{E(!1),t((0,c.Ir)())}}}),[g,f,b,a,t]);(0,s.useEffect)((()=>{e&&a&&G()}),[e,a,G]),(0,s.useEffect)((()=>{e&&a&&g&&f&&"default"!==f&&W()}),[e,a,g,f,b,W]);const z=e=>{S([]),j(e),I(!1)},K=(0,s.useMemo)((()=>{if(!C||0===C.length)return[];let e=C;if(B.trim()){const t=B.toLowerCase();e=e.filter((e=>e.title.toLowerCase().includes(t)||e.subject.toLowerCase().includes(t)||e.year&&e.year.toLowerCase().includes(t)))}return e.sort(((e,t)=>"newest"===_?e.year&&t.year?parseInt(t.year)-parseInt(e.year):e.year&&!t.year?-1:!e.year&&t.year?1:0:"oldest"===_?e.year&&t.year?parseInt(e.year)-parseInt(t.year):e.year&&!t.year?-1:!e.year&&t.year?1:0:e.title.localeCompare(t.title))),e}),[C,B,_,g]);return(0,m.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-gray-50 to-blue-50",children:[(0,m.jsx)(r.E.div,{initial:{opacity:0,y:-20},animate:{opacity:1,y:0},className:"bg-gradient-to-r from-primary-600 to-blue-600 text-white",children:(0,m.jsx)("div",{className:"container-modern py-12",children:(0,m.jsxs)("div",{className:"flex items-center justify-between",children:[(0,m.jsxs)("div",{className:"flex items-center space-x-6",children:[(0,m.jsx)("div",{className:"w-16 h-16 bg-white/20 rounded-full flex items-center justify-center",children:(0,m.jsx)(y.T0P,{className:"w-8 h-8 text-white"})}),(0,m.jsxs)("div",{children:[(0,m.jsx)("h1",{className:"text-4xl font-bold mb-2",children:"Study Materials"}),(0,m.jsxs)("p",{className:"text-xl text-blue-100",children:["Access comprehensive learning resources for ",a," education"]})]})]}),(0,m.jsx)("div",{className:"hidden md:block",children:(0,m.jsxs)("div",{className:"bg-white/20 backdrop-blur-sm rounded-xl px-6 py-3",children:[(0,m.jsx)("div",{className:"text-sm text-blue-100 mb-1",children:"Current Level"}),(0,m.jsx)("div",{className:"text-lg font-bold",children:null===a||void 0===a?void 0:a.toUpperCase()})]})})]})})}),(0,m.jsxs)("div",{className:"container-modern py-8",children:[(0,m.jsx)("div",{className:"mb-6",children:(0,m.jsx)("div",{className:"study-tabs",children:[{key:"study-notes",label:"Notes",icon:y.RxU},{key:"past-papers",label:"Past Papers",icon:y.eH},{key:"books",label:"Books",icon:y.NQR}].map((e=>(0,m.jsxs)("button",{className:"study-tab ".concat(g===e.key?"active":""),onClick:()=>(e=>{S([]),v(e),O(""),H("newest")})(e.key),children:[(0,m.jsx)(e.icon,{}),(0,m.jsx)("span",{children:e.label})]},e.key)))})}),(0,m.jsx)(r.E.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.4},className:"mb-8",children:(0,m.jsxs)("div",{className:"card p-6",children:[(0,m.jsxs)("div",{className:"flex flex-col lg:flex-row gap-6 items-end",children:[(0,m.jsxs)("div",{className:"flex-1",children:[(0,m.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Search Materials"}),(0,m.jsx)("input",{placeholder:"Search ".concat(g.replace("-"," "),"..."),value:B,onChange:e=>O(e.target.value),className:"form-input"})]}),(0,m.jsxs)("div",{className:"w-full lg:w-64",children:[(0,m.jsxs)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:["Filter by Class",N&&(0,m.jsxs)("span",{className:"ml-2 text-xs text-primary-600 font-medium",children:["(Your class: ","primary"===i?"Class ".concat(N):"Form ".concat(N),")"]})]}),(0,m.jsxs)("div",{className:"relative",children:[(0,m.jsxs)("button",{onClick:()=>{I(!D)},className:"w-full input-modern flex items-center justify-between",children:[(0,m.jsxs)("span",{className:"flex items-center space-x-2",children:[(0,m.jsx)(y.EW2,{className:"w-4 h-4 text-gray-400"}),(0,m.jsx)("span",{children:"all"===f?"All Classes":"primary"===i?"Class ".concat(f):"Form ".concat(f)}),f===N&&(0,m.jsx)("span",{className:"badge-primary text-xs",children:"Current"})]}),(0,m.jsx)(y.YRR,{className:"w-4 h-4 text-gray-400 transition-transform ".concat(D?"rotate-180":"")})]}),(0,m.jsx)(n.M,{children:D&&(0,m.jsxs)(r.E.div,{initial:{opacity:0,y:-10},animate:{opacity:1,y:0},exit:{opacity:0,y:-10},className:"absolute top-full left-0 right-0 mt-1 bg-white border border-gray-200 rounded-lg shadow-lg z-10 max-h-60 overflow-y-auto",children:[(0,m.jsx)("button",{className:"w-full px-4 py-3 text-left hover:bg-gray-50 transition-colors ".concat("all"===f?"bg-primary-50 text-primary-700 font-medium":"text-gray-700"),onClick:()=>z("all"),children:"All Classes"}),A.map(((e,t)=>(0,m.jsxs)("button",{className:"w-full px-4 py-3 text-left hover:bg-gray-50 transition-colors flex items-center justify-between ".concat(f===e?"bg-primary-50 text-primary-700 font-medium":"text-gray-700"),onClick:()=>z(e),children:[(0,m.jsx)("span",{children:"primary"===i?"Class ".concat(e):"Form ".concat(e)}),e===N&&(0,m.jsx)("span",{className:"badge-success text-xs",children:"Your Class"})]},t)))]})})]})]}),(0,m.jsxs)("div",{className:"w-full lg:w-64",children:[(0,m.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Filter by Subject"}),(0,m.jsxs)("select",{value:b,onChange:e=>{return t=e.target.value,S([]),w(t),void O("");var t},className:"input-modern",children:[(0,m.jsx)("option",{value:"all",children:"All Subjects"}),d.map(((e,t)=>(0,m.jsx)("option",{value:e,children:e},t)))]})]}),(0,m.jsxs)("div",{className:"w-full lg:w-48",children:[(0,m.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Sort by"}),(0,m.jsxs)("select",{value:_,onChange:e=>H(e.target.value),className:"input-modern",children:[(0,m.jsx)("option",{value:"newest",children:"Newest First"}),(0,m.jsx)("option",{value:"oldest",children:"Oldest First"}),(0,m.jsx)("option",{value:"title",children:"By Title"})]})]}),(0,m.jsx)("button",{className:"btn btn-secondary",onClick:()=>{O(""),j("all"),w("all"),H("newest")},children:"Clear Filters"})]}),(B||"all"!==f||"all"!==b)&&(0,m.jsx)("div",{className:"mt-4 pt-4 border-t border-gray-100",children:(0,m.jsxs)("span",{className:"text-sm text-gray-600",children:["Showing ",K.length," of ",C.length," ",g.replace("-"," ")]})})]})}),(0,m.jsx)("div",{className:"materials-section",children:k?(0,m.jsxs)("div",{className:"loading-state",children:[(0,m.jsx)("div",{className:"loading-spinner"}),(0,m.jsx)("p",{children:"Loading materials..."})]}):T?(0,m.jsxs)("div",{className:"error-state",children:[(0,m.jsx)(h.aHS,{className:"error-icon"}),(0,m.jsx)("h3",{children:"Error Loading Materials"}),(0,m.jsx)("p",{children:T}),(0,m.jsx)("button",{className:"retry-btn",onClick:()=>{P(null),W()},children:"Try Again"})]}):K.length>0?(0,m.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",children:K.map(((e,t)=>(0,m.jsxs)("div",{className:"study-card",children:[(0,m.jsxs)("div",{className:"study-card-header",children:[(0,m.jsxs)("div",{className:"study-card-meta",children:["study-notes"===g&&(0,m.jsx)(h.mGS,{}),"past-papers"===g&&(0,m.jsx)(h.mGS,{}),"books"===g&&(0,m.jsx)(h.Mp$,{}),(0,m.jsx)("span",{children:"study-notes"===g?"Note":"past-papers"===g?"Past Paper":"Book"})]}),(0,m.jsx)("div",{className:"study-card-title",children:e.title}),e.year&&(0,m.jsx)("span",{className:"badge badge-secondary mt-2",children:e.year})]}),(0,m.jsxs)("div",{className:"card-content",children:[(0,m.jsx)("h3",{className:"material-title",children:e.title}),(0,m.jsxs)("div",{className:"material-meta",children:[(0,m.jsx)("span",{className:"material-subject",children:e.subject}),e.className&&(0,m.jsx)("span",{className:"material-class",children:"primary"===i?"Class ".concat(e.className):"Form ".concat(e.className)})]})]}),(0,m.jsx)("div",{className:"card-actions",children:e.documentUrl?(0,m.jsxs)(m.Fragment,{children:[(0,m.jsxs)("button",{className:"action-btn secondary",onClick:()=>(e=>{R(e),L(!0)})(e.documentUrl),children:[(0,m.jsx)(h.dSq,{})," View"]}),(0,m.jsxs)("button",{className:"action-btn primary",onClick:()=>(e=>{const t="".concat({NODE_ENV:"production",PUBLIC_URL:"",WDS_SOCKET_HOST:void 0,WDS_SOCKET_PATH:void 0,WDS_SOCKET_PORT:void 0,FAST_REFRESH:!0}.REACT_APP_SERVER_DOMAIN,"/api/study/document-proxy?url=").concat(encodeURIComponent(e));fetch(t,{method:"GET",headers:{Authorization:"Bearer ".concat(localStorage.getItem("token"))}}).then((e=>{if(!e.ok)throw new Error("HTTP error! status: ".concat(e.status));return e.blob()})).then((t=>{const a=window.URL.createObjectURL(t),s=document.createElement("a");s.href=a,s.download=e.split("/").pop(),document.body.appendChild(s),s.click(),document.body.removeChild(s),window.URL.revokeObjectURL(a)})).catch((t=>{console.error("Error downloading the file:",t),window.open(e,"_blank")}))})(e.documentUrl),children:[(0,m.jsx)(h.aBF,{})," Download"]})]}):(0,m.jsx)("span",{className:"unavailable",children:"Not available"})})]},t)))}):(0,m.jsxs)("div",{className:"empty-state",children:[(0,m.jsx)(h.nGB,{className:"empty-icon"}),(0,m.jsx)("h3",{children:"No Materials Found"}),(0,m.jsx)("p",{children:"No study materials are available for your current selection."}),(0,m.jsx)("p",{className:"suggestion",children:"Try selecting a different class or subject."})]})}),(0,m.jsx)(p,{modalIsOpen:F,closeModal:()=>{L(!1),R("")},documentUrl:M})]})]})}},3414:()=>{},172:()=>{},7441:()=>{},3779:()=>{},6558:()=>{},2258:()=>{}}]);
//# sourceMappingURL=92.22645899.chunk.js.map