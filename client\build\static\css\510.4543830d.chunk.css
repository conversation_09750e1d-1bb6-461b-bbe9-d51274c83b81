.quiz-grid{grid-template-columns:repeat(2,1fr)}@media (min-width:768px){.quiz-grid{grid-template-columns:repeat(3,1fr)}}@media (min-width:1024px){.quiz-grid{grid-template-columns:repeat(4,1fr)}}@media (min-width:1280px){.quiz-grid{grid-template-columns:repeat(5,1fr)}}@keyframes fadeIn{0%{opacity:0;transform:translateY(-10px)}to{opacity:1;transform:translateY(0)}}@keyframes fadeInDelay{0%{opacity:0;transform:translateY(10px)}to{opacity:1;transform:translateY(0)}}@keyframes fadeInStagger{0%{opacity:0;transform:translateY(20px)}to{opacity:1;transform:translateY(0)}}.animate-fade-in{animation:fadeIn .6s ease-out forwards}.animate-fade-in-delay{animation:fadeInDelay .6s ease-out .1s forwards}.animate-fade-in-delay-2{animation:fadeInDelay .6s ease-out .2s forwards}.animate-fade-in-stagger{animation:fadeInStagger .6s ease-out forwards}.hover-scale:hover{transform:scale(1.05)}.hover-scale:active{transform:scale(.95)}.line-clamp-2{-webkit-line-clamp:2;-webkit-box-orient:vertical;display:-webkit-box;overflow:hidden}.quiz-grid{grid-gap:1.5rem;display:grid;gap:1.5rem;grid-template-columns:repeat(auto-fill,minmax(300px,1fr))}@media (max-width:768px){.quiz-grid{gap:1rem;grid-template-columns:1fr}}*{transition:all .2s ease-in-out}button:focus,input:focus,select:focus{box-shadow:0 0 0 3px #3b82f61a;outline:none}.spinner{animation:spin 1s linear infinite;border:2px solid #f3f4f6;border-radius:50%;border-top-color:#3b82f6}@keyframes spin{0%{transform:rotate(0deg)}to{transform:rotate(1turn)}}
/*# sourceMappingURL=510.4543830d.chunk.css.map*/