"use strict";(self.webpackChunkclient=self.webpackChunkclient||[]).push([[295],{640:(e,i,s)=>{s.d(i,{Z:()=>t});var n=s(2791),a=s(184);const t=function(e){let{title:i}=e;const[s,t]=(0,n.useState)(!1);return(0,n.useEffect)((()=>{window.innerWidth<768&&t(!0)}),[]),(0,a.jsx)("div",{className:"mt-2",children:(0,a.jsx)("h1",{className:s?"text-lg":"",children:i})})}},1304:(e,i,s)=>{s.d(i,{Lo:()=>a,iq:()=>n,vp:()=>t});const n=["Mathematics","Science and Technology","Geography","Kiswahili","SocialStudies","English","Religion","Arithmetic","Sport and Art","Health and Environment","Civic and Moral","French","Historia ya Tanzania"],a=["Physics","Mathematics","Biology","Chemistry","Geography","Civics","History","Computer","English language","Kiswahili","Commerce","Economics","Advanced Mathematics","Basic Applied Mathematics","Accountancy","Agriculture"],t=["Physics","Mathematics","Biology","Chemistry","Geography","Civics","History","Computer","English language","Kiswahili","Commerce","Economics","Advanced Mathematics","Basic Applied Mathematics","Accountancy","Agriculture"]},3295:(e,i,s)=>{s.r(i),s.d(i,{default:()=>I});var n=s(1413),a=s(7027),t=s(7846);const l=s(7545).Z;const o=s(9752).Z;var d=s(222),r=s(2791),c=s(1652),m=s(640),u=s(7689),p=s(9434),x=s(8247),v=s(5058),h=s(6275),g=s(184);const j=function(e){var i,s,n,l,o,d,m,u;let{showAddEditQuestionModal:v,setShowAddEditQuestionModal:j,refreshData:b,examId:y,selectedQuestion:I,setSelectedQuestion:w}=e;const N=(0,p.I0)(),[C,f]=(0,r.useState)((()=>null!==I&&void 0!==I&&I.type?I.type:"Options"===(null===I||void 0===I?void 0:I.answerType)?null!==I&&void 0!==I&&I.image||null!==I&&void 0!==I&&I.imageUrl?"image":"mcq":"Fill in the Blank"===(null===I||void 0===I?void 0:I.answerType)||"Free Text"===(null===I||void 0===I?void 0:I.answerType)?"fill":null!==I&&void 0!==I&&I.isAIGenerated?"picture_based"===(null===I||void 0===I?void 0:I.questionType)?"image":"fill_blank"===(null===I||void 0===I?void 0:I.questionType)?"fill":"mcq":"mcq")),[A,Z]=(0,r.useState)(null);return(0,g.jsx)(h.Z,{title:I?"Edit Question":"Add Question",open:v,footer:!1,onCancel:()=>{j(!1),w(null),Z(null)},children:(0,g.jsxs)(t.Z,{onFinish:async e=>{try{N((0,x.YC)());const i=new FormData;let s;i.append("name",e.name),i.append("type",C),i.append("exam",y),i.append("topic",e.topic||"General"),i.append("classLevel",e.classLevel||"General"),"mcq"===C?i.append("answerType","Options"):"fill"===C?i.append("answerType","Fill in the Blank"):"image"===C&&i.append("answerType","Options"),i.append("correctAnswer",e.correctAnswer),"mcq"!==C&&"image"!==C||(i.append("options[A]",e.A),i.append("options[B]",e.B),i.append("options[C]",e.C),i.append("options[D]",e.D),i.append("correctOption",e.correctAnswer)),A?i.append("image",A):null!==I&&void 0!==I&&I.image&&i.append("image",I.image),I?(i.append("questionId",I._id),s=await(0,c._7)(i)):s=await(0,c.Uw)(i),s.success?(a.ZP.success(s.message),b(),j(!1),Z(null)):a.ZP.error(s.message),w(null),N((0,x.Ir)())}catch(i){N((0,x.Ir)()),a.ZP.error(i.message)}},layout:"vertical",initialValues:{name:null===I||void 0===I?void 0:I.name,correctAnswer:(null===I||void 0===I?void 0:I.correctAnswer)||(null===I||void 0===I?void 0:I.correctOption),topic:(null===I||void 0===I?void 0:I.topic)||"General",classLevel:(null===I||void 0===I?void 0:I.classLevel)||"General",A:(null===I||void 0===I||null===(i=I.options)||void 0===i?void 0:i.A)||(null===I||void 0===I||null===(s=I.options)||void 0===s?void 0:s.a)||"",B:(null===I||void 0===I||null===(n=I.options)||void 0===n?void 0:n.B)||(null===I||void 0===I||null===(l=I.options)||void 0===l?void 0:l.b)||"",C:(null===I||void 0===I||null===(o=I.options)||void 0===o?void 0:o.C)||(null===I||void 0===I||null===(d=I.options)||void 0===d?void 0:d.c)||"",D:(null===I||void 0===I||null===(m=I.options)||void 0===m?void 0:m.D)||(null===I||void 0===I||null===(u=I.options)||void 0===u?void 0:u.d)||""},children:[(null===I||void 0===I?void 0:I.isAIGenerated)&&(0,g.jsxs)("div",{className:"mb-4 p-3 bg-blue-50 border border-blue-200 rounded-lg",children:[(0,g.jsxs)("div",{className:"flex items-center gap-2",children:[(0,g.jsx)("span",{className:"text-blue-600 text-lg",children:"\ud83e\udd16"}),(0,g.jsx)("span",{className:"text-blue-800 font-semibold",children:"AI-Generated Question"})]}),(0,g.jsx)("p",{className:"text-blue-700 text-sm mt-1",children:"This question was generated by AI. You can edit all fields and add images as needed."})]}),("image"===C||"picture_based"===(null===I||void 0===I?void 0:I.questionType))&&!(null!==I&&void 0!==I&&I.image)&&!(null!==I&&void 0!==I&&I.imageUrl)&&!A&&(0,g.jsxs)("div",{className:"mb-4 p-3 bg-amber-50 border border-amber-200 rounded-lg",children:[(0,g.jsxs)("div",{className:"flex items-center gap-2",children:[(0,g.jsx)("span",{className:"text-amber-600 text-lg",children:"\u26a0\ufe0f"}),(0,g.jsx)("span",{className:"text-amber-800 font-semibold",children:"Image Required"})]}),(0,g.jsx)("p",{className:"text-amber-700 text-sm mt-1",children:"This is an image-based question but no image is currently attached. Please upload an image below."})]}),(0,g.jsx)(t.Z.Item,{name:"name",label:"Question",children:(0,g.jsx)("input",{type:"text"})}),(0,g.jsx)(t.Z.Item,{name:"questionType",label:"Question Type",children:(0,g.jsxs)("div",{className:"space-y-2",children:[(0,g.jsxs)("select",{value:C,onChange:e=>f(e.target.value),className:"w-full p-2 border border-gray-300 rounded-md",children:[(0,g.jsx)("option",{value:"mcq",children:"Multiple Choice (MCQ)"}),(0,g.jsx)("option",{value:"fill",children:"Fill in the Blank"}),(0,g.jsx)("option",{value:"image",children:"Image-based Question"})]}),(null===I||void 0===I?void 0:I.isAIGenerated)&&(0,g.jsxs)("div",{className:"text-sm text-blue-600 bg-blue-50 p-2 rounded",children:["\ud83d\udca1 ",(0,g.jsx)("strong",{children:"Tip:"}),' Change to "Image-based Question" to add visual content to this AI-generated question']}),"image"===C&&"picture_based"!==(null===I||void 0===I?void 0:I.questionType)&&"Options"!==(null===I||void 0===I?void 0:I.answerType)&&(0,g.jsx)("div",{className:"text-sm text-green-600 bg-green-50 p-2 rounded",children:"\u2713 Converting to image-based question. You can now upload an image below."})]})}),(0,g.jsxs)("div",{className:"flex gap-3",children:[(0,g.jsx)(t.Z.Item,{name:"topic",label:"Topic",children:(0,g.jsx)("input",{type:"text",placeholder:"e.g., Mathematics, Science"})}),(0,g.jsx)(t.Z.Item,{name:"classLevel",label:"Class Level",children:(0,g.jsx)("input",{type:"text",placeholder:"e.g., Primary 1, Secondary 2"})})]}),"image"===C&&(0,g.jsxs)(t.Z.Item,{name:"image",label:"Question Image",children:[(0,g.jsx)("input",{type:"file",accept:"image/*",onChange:e=>Z(e.target.files[0])}),(0,g.jsx)("small",{children:"Upload an image for this question"})]}),(0,g.jsx)(t.Z.Item,{name:"correctAnswer",label:"Correct Answer",children:(0,g.jsx)("input",{type:"text",placeholder:"mcq"===C||"image"===C?"Enter the correct option (A, B, C, or D)":"Enter the correct answer"})}),("mcq"===C||"image"===C)&&(0,g.jsxs)(g.Fragment,{children:[(0,g.jsxs)("div",{className:"flex gap-3",children:[(0,g.jsx)(t.Z.Item,{name:"A",label:"Option A",children:(0,g.jsx)("input",{type:"text"})}),(0,g.jsx)(t.Z.Item,{name:"B",label:"Option B",children:(0,g.jsx)("input",{type:"text"})})]}),(0,g.jsxs)("div",{className:"flex gap-3",children:[(0,g.jsx)(t.Z.Item,{name:"C",label:"Option C",children:(0,g.jsx)("input",{type:"text"})}),(0,g.jsx)(t.Z.Item,{name:"D",label:"Option D",children:(0,g.jsx)("input",{type:"text"})})]})]}),(0,g.jsx)(t.Z.Item,{name:"image",label:(0,g.jsxs)("div",{className:"flex items-center gap-2",children:[(0,g.jsx)("span",{children:"Question Image"}),("image"===C||"picture_based"===(null===I||void 0===I?void 0:I.questionType))&&(0,g.jsx)("span",{className:"text-red-500",children:"*"}),(null===I||void 0===I?void 0:I.isAIGenerated)&&!(null!==I&&void 0!==I&&I.image)&&!(null!==I&&void 0!==I&&I.imageUrl)&&(0,g.jsx)("span",{className:"text-blue-600 text-sm",children:"(Add image for AI question)"})]}),children:(0,g.jsxs)("div",{className:"space-y-3",children:[(0,g.jsxs)("div",{className:"border-2 border-dashed border-gray-300 rounded-lg p-4 hover:border-blue-400 transition-colors",children:[(0,g.jsx)("input",{type:"file",accept:"image/*",onChange:e=>{const i=e.target.files[0];Z(i||null)},className:"w-full"}),(0,g.jsx)("p",{className:"text-sm text-gray-500 mt-2",children:"image"===C||"picture_based"===(null===I||void 0===I?void 0:I.questionType)?"Upload an image for this image-based question":"Upload an image (optional)"})]}),A&&(0,g.jsx)("div",{className:"p-3 bg-green-50 border border-green-200 rounded-lg",children:(0,g.jsxs)("div",{className:"flex items-center gap-2",children:[(0,g.jsx)("span",{className:"text-green-600",children:"\u2713"}),(0,g.jsx)("span",{className:"text-green-800 font-medium",children:"New image selected:"}),(0,g.jsx)("span",{className:"text-green-700",children:A.name})]})}),((null===I||void 0===I?void 0:I.image)||(null===I||void 0===I?void 0:I.imageUrl))&&!A&&(0,g.jsxs)("div",{className:"space-y-2",children:[(0,g.jsx)("p",{className:"text-sm font-medium text-gray-700",children:"Current image:"}),(0,g.jsx)("div",{className:"border rounded-lg p-2 bg-gray-50",children:(0,g.jsx)("img",{src:I.image||I.imageUrl,alt:"Current question",className:"max-w-[300px] max-h-[200px] object-contain rounded"})}),(0,g.jsx)("p",{className:"text-sm text-gray-500",children:"Upload a new image above to replace this one"})]})]})}),(0,g.jsxs)("div",{className:"flex justify-end mt-2 gap-3",children:[(0,g.jsx)("button",{className:"primary-outlined-btn",type:"button",onClick:()=>{j(!1),w(null),Z(null)},children:"Cancel"}),(0,g.jsx)("button",{className:"primary-contained-btn",children:"Save"})]})]})})};var b=s(1304);const{TabPane:y}=v.default;const I=function(){var e;const i=(0,p.I0)(),s=(0,u.s0)(),[h,I]=(0,r.useState)(null),[w,N]=(0,r.useState)(""),[C,f]=(0,r.useState)(!1),[A,Z]=(0,r.useState)(null),[q,S]=(0,r.useState)(""),F=(0,u.UO)(),Q=async()=>{try{var e,s;i((0,x.YC)());const n=JSON.parse(localStorage.getItem("user")),t=await(0,c.gw)({examId:F.id,userId:null===n||void 0===n?void 0:n._id});S(null===t||void 0===t||null===(e=t.data)||void 0===e?void 0:e.class),N(null===t||void 0===t||null===(s=t.data)||void 0===s?void 0:s.level),i((0,x.Ir)()),t.success?I(t.data):a.ZP.error(t.message)}catch(n){i((0,x.Ir)()),a.ZP.error(n.message)}};(0,r.useEffect)((()=>{F.id&&Q()}),[]);const T=[{title:"Question",dataIndex:"name"},{title:"Options",dataIndex:"options",render:(e,i)=>null!==i&&void 0!==i&&i.options&&"object"===typeof i.options&&Object.keys(i.options).length>0?Object.keys(i.options).map((e=>(0,g.jsxs)("div",{children:[e,": ",i.options[e]]},e))):(0,g.jsx)("div",{children:"No options available for this question."})},{title:"Correct Answer",dataIndex:"correctAnswer",render:(e,i)=>{const s=i.correctAnswer||i.correctOption;return"Free Text"===i.answerType||"fill"===i.type||"text"===i.type?(0,g.jsx)("div",{children:s}):(0,g.jsxs)("div",{children:[s,": ",i.options&&i.options[s]?i.options[s]:s]})}},{title:"Source",dataIndex:"source",render:(e,i)=>(0,g.jsxs)("div",{className:"flex items-center gap-1",children:[null!==i&&void 0!==i&&i.isAIGenerated?(0,g.jsx)("span",{className:"flex items-center gap-1 text-blue-600 text-sm",children:"\ud83e\udd16 AI"}):(0,g.jsx)("span",{className:"text-gray-600 text-sm",children:"Manual"}),((null===i||void 0===i?void 0:i.image)||(null===i||void 0===i?void 0:i.imageUrl))&&(0,g.jsx)("span",{title:"Has Image",children:"\ud83d\uddbc\ufe0f"})]})},{title:"Action",dataIndex:"action",render:(e,s)=>(0,g.jsxs)("div",{className:"flex gap-2 items-center",children:[(0,g.jsx)("i",{className:"ri-pencil-line cursor-pointer text-blue-600 hover:text-blue-800",title:"Edit Question",onClick:()=>{Z(s),f(!0)}}),(null===s||void 0===s?void 0:s.isAIGenerated)&&!(null!==s&&void 0!==s&&s.image)&&!(null!==s&&void 0!==s&&s.imageUrl)&&(0,g.jsx)("i",{className:"ri-image-add-line cursor-pointer text-green-600 hover:text-green-800",title:"Add Image to AI Question",onClick:()=>{Z(s),f(!0)}}),(null===s||void 0===s?void 0:s.isAIGenerated)&&(0,g.jsx)("span",{className:"text-blue-500 text-sm",title:"AI Generated Question",children:"\ud83e\udd16"}),((null===s||void 0===s?void 0:s.image)||(null===s||void 0===s?void 0:s.imageUrl))&&(0,g.jsx)("span",{className:"text-green-500 text-sm",title:"Has Image",children:"\ud83d\uddbc\ufe0f"}),(0,g.jsx)("i",{className:"ri-delete-bin-line cursor-pointer text-red-600 hover:text-red-800",title:"Delete Question",onClick:()=>{(async e=>{try{i((0,x.YC)());const s=await(0,c.bl)({questionId:e,examId:F.id});i((0,x.Ir)()),s.success?(a.ZP.success(s.message),Q()):a.ZP.error(s.message)}catch(s){i((0,x.Ir)()),a.ZP.error(s.message)}})(s._id)}})]})}];return console.log(q,"classValue"),(0,g.jsxs)("div",{children:[(0,g.jsx)(m.Z,{title:F.id?"Edit Exam":"Add Exam"}),(0,g.jsx)("div",{className:"divider"}),(h||!F.id)&&(0,g.jsx)(t.Z,{layout:"vertical",onFinish:async e=>{try{let o;if(i((0,x.YC)()),o=F.id?await(0,c.k1)((0,n.Z)((0,n.Z)({},e),{},{examId:F.id})):await(0,c.rd)(e),o.success){if(a.ZP.success(o.message),!F.id){var t,l;window.dispatchEvent(new CustomEvent("newExamCreated",{detail:{examName:e.name,level:e.level,timestamp:Date.now()}}));const n=(null===(t=o.data)||void 0===t?void 0:t._id)||(null===(l=o.data)||void 0===l?void 0:l.id);if(n)return i((0,x.Ir)()),void s("/admin/exams/edit/".concat(n))}F.id&&Q()}else a.ZP.error(o.message);i((0,x.Ir)())}catch(o){i((0,x.Ir)()),a.ZP.error(o.message)}},initialValues:h,children:(0,g.jsxs)(v.default,{defaultActiveKey:"1",children:[(0,g.jsxs)(y,{tab:"Exam Details",children:[(0,g.jsxs)(l,{gutter:[10,10],children:[(0,g.jsx)(o,{span:8,children:(0,g.jsx)(t.Z.Item,{label:"Exam Name",name:"name",children:(0,g.jsx)("input",{type:"text"})})}),(0,g.jsx)(o,{span:8,children:(0,g.jsx)(t.Z.Item,{label:"Topic",name:"topic",children:(0,g.jsx)("input",{type:"text",placeholder:"Enter quiz topic (e.g., Algebra, Cell Biology)"})})}),(0,g.jsx)(o,{span:8,children:(0,g.jsx)(t.Z.Item,{label:"Exam Duration (Seconds)",name:"duration",children:(0,g.jsx)("input",{type:"number"})})}),(0,g.jsx)(o,{span:8,children:(0,g.jsx)(t.Z.Item,{name:"level",label:"Level",initialValue:"",children:(0,g.jsxs)("select",{value:w,onChange:e=>{N(e.target.value),S("")},children:[(0,g.jsx)("option",{value:"",disabled:!0,children:"Select Level"}),(0,g.jsx)("option",{value:"Primary",children:"Primary"}),(0,g.jsx)("option",{value:"Secondary",children:"Secondary"}),(0,g.jsx)("option",{value:"Advance",children:"Advance"})]})})}),(0,g.jsx)(o,{span:8,children:(0,g.jsx)(t.Z.Item,{label:"Category",name:"category",children:(0,g.jsxs)("select",{name:"",id:"",children:[(0,g.jsx)("option",{value:"",children:"Select Category"}),"primary"===w.toLowerCase()&&(0,g.jsx)(g.Fragment,{children:b.iq.map(((e,i)=>(0,g.jsx)("option",{value:e,children:e},i)))}),"secondary"===w.toLowerCase()&&(0,g.jsx)(g.Fragment,{children:b.Lo.map(((e,i)=>(0,g.jsx)("option",{value:e,children:e},i)))}),"advance"===w.toLowerCase()&&(0,g.jsx)(g.Fragment,{children:b.vp.map(((e,i)=>(0,g.jsx)("option",{value:e,children:e},i)))})]})})}),(0,g.jsx)(o,{span:8,children:(0,g.jsx)(t.Z.Item,{name:"class",label:"Class",initialValue:"",required:!0,children:(0,g.jsxs)("select",{value:q,onChange:e=>S(e.target.value),children:[(0,g.jsx)("option",{value:"",children:"Select Class"}),"primary"===w.toLowerCase()&&(0,g.jsxs)(g.Fragment,{children:[(0,g.jsx)("option",{value:"1",children:"1"}),(0,g.jsx)("option",{value:"2",children:"2"}),(0,g.jsx)("option",{value:"3",children:"3"}),(0,g.jsx)("option",{value:"4",children:"4"}),(0,g.jsx)("option",{value:"5",children:"5"}),(0,g.jsx)("option",{value:"6",children:"6"}),(0,g.jsx)("option",{value:"7",children:"7"})]}),"secondary"===w.toLowerCase()&&(0,g.jsxs)(g.Fragment,{children:[(0,g.jsx)("option",{value:"Form-1",children:"Form-1"}),(0,g.jsx)("option",{value:"Form-2",children:"Form-2"}),(0,g.jsx)("option",{value:"Form-3",children:"Form-3"}),(0,g.jsx)("option",{value:"Form-4",children:"Form-4"})]}),"advance"===w.toLowerCase()&&(0,g.jsxs)(g.Fragment,{children:[(0,g.jsx)("option",{value:"Form-5",children:"Form-5"}),(0,g.jsx)("option",{value:"Form-6",children:"Form-6"})]})]})})}),(0,g.jsx)(o,{span:8,children:(0,g.jsx)(t.Z.Item,{label:"Total Marks",name:"totalMarks",children:(0,g.jsx)("input",{type:"number"})})}),(0,g.jsx)(o,{span:8,children:(0,g.jsx)(t.Z.Item,{label:"Passing Marks",name:"passingMarks",children:(0,g.jsx)("input",{type:"number"})})})]}),(0,g.jsxs)("div",{className:"flex justify-end gap-2",children:[(0,g.jsx)("button",{className:"primary-outlined-btn",type:"button",onClick:()=>s("/admin/exams"),children:"Cancel"}),(0,g.jsx)("button",{className:"primary-contained-btn",type:"submit",children:"Save"})]})]},"1"),F.id&&(0,g.jsxs)(y,{tab:"Questions",children:[(0,g.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[(0,g.jsxs)("div",{children:[(0,g.jsx)("h3",{className:"text-lg font-semibold",children:"Exam Questions"}),(0,g.jsx)("p",{className:"text-gray-600",children:"Add and manage questions for this exam"})]}),(0,g.jsx)("button",{className:"primary-contained-btn",type:"button",onClick:()=>f(!0),children:"Add Question"})]}),(0,g.jsx)(d.Z,{columns:T,dataSource:(null===h||void 0===h?void 0:h.questions)||[],pagination:{pageSize:10,showSizeChanger:!0,showQuickJumper:!0},locale:{emptyText:0===(null===h||void 0===h||null===(e=h.questions)||void 0===e?void 0:e.length)?'No questions added yet. Click "Add Question" to add questions.':"Loading questions..."}})]},"2")]})}),C&&(0,g.jsx)(j,{setShowAddEditQuestionModal:f,showAddEditQuestionModal:C,examId:F.id,refreshData:Q,selectedQuestion:A,setSelectedQuestion:Z})]})}}}]);
//# sourceMappingURL=295.e0c4580e.chunk.js.map