{"version": 3, "file": "static/js/723.724c7a60.chunk.js", "mappings": "sMAwBA,MA0MA,EA1MYA,KACV,MAAMC,GAAWC,EAAAA,EAAAA,OACX,KAAEC,IAASC,EAAAA,EAAAA,KAAaC,GAAUA,EAAMF,QACvCG,EAAcC,IAAmBC,EAAAA,EAAAA,UAAS,GAM3CC,EAAkB,CACtB,+EACA,+EACA,yFACA,wDACA,4CACA,mDACA,8CACA,4BACA,gEACA,oFAIFC,EAAAA,EAAAA,YAAU,KACR,MAAMC,EAAWC,aAAY,KAC3BL,GAAiBM,IAAUA,EAAO,GAAKJ,EAAgBK,QAAO,GAC7D,KACH,MAAO,IAAMC,cAAcJ,EAAS,GACnC,CAACF,EAAgBK,SAGpB,MAcME,EAAkB,CACtB,CACEC,MAAO,YACPC,YAAa,sBACbC,KAAMC,EAAAA,IACNC,KAAM,aACNC,MAAO,4BACPC,WAAY,6BAEd,CACEN,MAAO,kBACPC,YAAa,wBACbC,KAAMK,EAAAA,IACNH,KAAM,uBACNC,MAAO,gCACPC,WAAY,iCAEd,CACEN,MAAO,gBACPC,YAAa,2BACbC,KAAMM,EAAAA,IACNJ,KAAM,sBACNC,MAAO,0BACPC,WAAY,2BAEd,CACEN,MAAO,UACPC,YAAa,sBACbC,KAAMO,EAAAA,GACNL,KAAM,gBACNC,MAAO,8BACPC,WAAY,+BAEd,CACEN,MAAO,UACPC,YAAa,oBACbC,KAAMQ,EAAAA,IACNN,KAAM,gBACNC,MAAO,gCACPC,WAAY,iCAEd,CACEN,MAAO,UACPC,YAAa,sBACbC,KAAMS,EAAAA,IACNP,KAAM,WACNC,MAAO,gCACPC,WAAY,iCAEd,CACEN,MAAO,QACPC,YAAa,qBACbC,KAAMU,EAAAA,IACNR,KAAM,SACNC,MAAO,4BACPC,WAAY,6BAGd,CACEN,MAAO,WACPC,YAAa,0BACbC,KAAMW,EAAAA,IACNT,KAAM,iBACNC,MAAO,4BACPC,WAAY,8BAIhB,OACEQ,EAAAA,EAAAA,KAAA,OAAKC,UAAU,gBAAeC,UAC5BC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,cAAaC,SAAA,EAG1BC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,aAAYC,SAAA,EACzBC,EAAAA,EAAAA,MAAA,MAAIF,UAAU,cAAaC,SAAA,CAAC,aACZ,OAAJ9B,QAAI,IAAJA,OAAI,EAAJA,EAAMgC,aAAiB,OAAJhC,QAAI,IAAJA,OAAI,EAAJA,EAAMiC,OAAQ,cAE7CL,EAAAA,EAAAA,KAAA,KAAGC,UAAU,eAAcC,SAAC,qCAI5BC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,YAAWC,SAAA,EACxBF,EAAAA,EAAAA,KAACM,EAAAA,IAAM,CAACC,MAAO,CAAEhB,MAAO,UAAWiB,YAAa,YAAc,IAC5D9B,EAAgBH,GAAc,KAChCyB,EAAAA,EAAAA,KAACM,EAAAA,IAAM,CAACC,MAAO,CAAEhB,MAAO,UAAWkB,WAAY,aAC/CT,EAAAA,EAAAA,KAAA,OAAKO,MAAO,CAAEG,SAAU,WAAYnB,MAAO,UAAWoB,UAAW,UAAWT,SAAC,4BAUjFC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,qBAAoBC,SAAA,EACjCF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,WAAUC,SACtBjB,EAAgB2B,KAAI,CAACC,EAAMC,KAC1B,MAAMC,EAAgBF,EAAKzB,KAC3B,OACEe,EAAAA,EAAAA,MAACa,EAAAA,EAAOC,IAAG,CAETC,QAAS,CAAEC,QAAS,EAAGC,EAAG,IAC1BC,QAAS,CAAEF,QAAS,EAAGC,EAAG,GAC1BE,WAAY,CAAEC,SAAU,GAAKC,MAAe,GAARV,GACpCb,UAAS,kBAAAwB,OAAoBZ,EAAKrB,WAAU,KAAAiC,OAAIZ,EAAKtB,OACrDmC,QAASA,IAAMxD,EAAS2C,EAAKvB,MAC7BqC,SAAU,EACVC,KAAK,SACLC,UAAYC,IACI,UAAVA,EAAEC,KAA6B,MAAVD,EAAEC,KACzB7D,EAAS2C,EAAKvB,KAChB,EAEFiB,MAAO,CACLyB,OAAQ,UACRC,YAAa,gBACb/B,SAAA,EAIFF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,gBAAeC,UAC5BF,EAAAA,EAAAA,KAACe,EAAa,OAGhBf,EAAAA,EAAAA,KAAA,MAAIC,UAAU,iBAAgBC,SAC3BW,EAAK3B,SAGRc,EAAAA,EAAAA,KAAA,KAAGC,UAAU,uBAAsBC,SAChCW,EAAK1B,gBA7BH0B,EAAK3B,MA+BC,OAKnBc,EAAAA,EAAAA,KAACgB,EAAAA,EAAOC,IAAG,CACTC,QAAS,CAAEC,QAAS,GACpBE,QAAS,CAAEF,QAAS,GACpBG,WAAY,CAAEC,SAAU,GAAKC,MAAO,IACpCvB,UAAU,wBAAuBC,UAEjCC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,qBAAoBC,SAAA,EACjCF,EAAAA,EAAAA,KAACkC,EAAAA,IAAe,CAACjC,UAAU,2CAC3BD,EAAAA,EAAAA,KAAA,QAAAE,SAAM,wCACNF,EAAAA,EAAAA,KAACmC,EAAAA,IAAQ,CAAClC,UAAU,sDAOxB,C", "sources": ["pages/user/Hub/index.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport { useSelector } from 'react-redux';\nimport { motion } from 'framer-motion';\nimport { message } from 'antd';\nimport './Hub.css';\nimport {\n  FaHome,\n  FaQuestionCircle,\n  FaBook,\n  FaChartLine,\n  FaUser,\n  FaComments,\n  FaCreditCard,\n  FaInfoCircle,\n  FaGraduationCap,\n  FaTrophy,\n  FaStar,\n  FaRocket,\n  FaRobot,\n  FaSignOutAlt,\n  FaVideo\n} from 'react-icons/fa';\n\nconst Hub = () => {\n  const navigate = useNavigate();\n  const { user } = useSelector((state) => state.user);\n  const [currentQuote, setCurrentQuote] = useState(0);\n\n\n\n\n  // Inspiring quotes that rotate\n  const inspiringQuotes = [\n    \"Education is the most powerful weapon which you can use to change the world.\",\n    \"The beautiful thing about learning is that no one can take it away from you.\",\n    \"Success is not final, failure is not fatal: it is the courage to continue that counts.\",\n    \"The only way to do great work is to love what you do.\",\n    \"Believe you can and you're halfway there.\",\n    \"Your limitation—it's only your imagination.\",\n    \"Great things never come from comfort zones.\",\n    \"Dream it. Wish it. Do it.\",\n    \"Success doesn't just find you. You have to go out and get it.\",\n    \"The harder you work for something, the greater you'll feel when you achieve it.\"\n  ];\n\n  // Rotate quotes every 4 seconds\n  useEffect(() => {\n    const interval = setInterval(() => {\n      setCurrentQuote((prev) => (prev + 1) % inspiringQuotes.length);\n    }, 4000);\n    return () => clearInterval(interval);\n  }, [inspiringQuotes.length]);\n\n  // Logout function\n  const handleLogout = () => {\n    // Clear authentication data\n    localStorage.removeItem('token');\n    localStorage.removeItem('user');\n\n    // Show success message\n    message.success('Logged out successfully!');\n\n    // Navigate to home page\n    navigate('/');\n  };\n\n\n\n  const navigationItems = [\n    {\n      title: 'Take Quiz',\n      description: 'Test your knowledge',\n      icon: FaQuestionCircle,\n      path: '/user/quiz',\n      color: 'from-blue-500 to-blue-600',\n      hoverColor: 'from-blue-600 to-blue-700'\n    },\n    {\n      title: 'Study Materials',\n      description: 'Books, notes & papers',\n      icon: FaBook,\n      path: '/user/study-material',\n      color: 'from-purple-500 to-purple-600',\n      hoverColor: 'from-purple-600 to-purple-700'\n    },\n    {\n      title: 'Video Lessons',\n      description: 'Watch educational videos',\n      icon: FaVideo,\n      path: '/user/video-lessons',\n      color: 'from-red-500 to-red-600',\n      hoverColor: 'from-red-600 to-red-700'\n    },\n    {\n      title: 'Reports',\n      description: 'Track your progress',\n      icon: FaChartLine,\n      path: '/user/reports',\n      color: 'from-green-500 to-green-600',\n      hoverColor: 'from-green-600 to-green-700'\n    },\n    {\n      title: 'Ranking',\n      description: 'See your position',\n      icon: FaTrophy,\n      path: '/user/ranking',\n      color: 'from-yellow-500 to-yellow-600',\n      hoverColor: 'from-yellow-600 to-yellow-700'\n    },\n    {\n      title: 'Profile',\n      description: 'Manage your account',\n      icon: FaUser,\n      path: '/profile',\n      color: 'from-indigo-500 to-indigo-600',\n      hoverColor: 'from-indigo-600 to-indigo-700'\n    },\n    {\n      title: 'Forum',\n      description: 'Connect with peers',\n      icon: FaComments,\n      path: '/forum',\n      color: 'from-pink-500 to-pink-600',\n      hoverColor: 'from-pink-600 to-pink-700'\n    },\n\n    {\n      title: 'About Us',\n      description: 'Learn about our mission',\n      icon: FaInfoCircle,\n      path: '/user/about-us',\n      color: 'from-cyan-500 to-cyan-600',\n      hoverColor: 'from-cyan-600 to-cyan-700'\n    }\n  ];\n\n  return (\n    <div className=\"hub-container\">\n      <div className=\"hub-content\">\n\n\n        <div className=\"hub-header\">\n          <h1 className=\"hub-welcome\">\n            Welcome, {user?.firstName || user?.name || 'Student'}\n          </h1>\n          <p className=\"hub-subtitle\">\n            Choose your learning path below\n          </p>\n\n          <div className=\"hub-quote\">\n            <FaStar style={{ color: '#f59e0b', marginRight: '0.5rem' }} />\n            \"{inspiringQuotes[currentQuote]}\"\n            <FaStar style={{ color: '#f59e0b', marginLeft: '0.5rem' }} />\n            <div style={{ fontSize: '0.875rem', color: '#6b7280', marginTop: '0.5rem' }}>\n              - BrainWave Team\n            </div>\n          </div>\n        </div>\n\n\n\n\n\n        <div className=\"hub-grid-container\">\n          <div className=\"hub-grid\">\n            {navigationItems.map((item, index) => {\n              const IconComponent = item.icon;\n              return (\n                <motion.div\n                  key={item.title}\n                  initial={{ opacity: 0, y: 30 }}\n                  animate={{ opacity: 1, y: 0 }}\n                  transition={{ duration: 0.6, delay: index * 0.1 }}\n                  className={`hub-card hover:${item.hoverColor} ${item.color}`}\n                  onClick={() => navigate(item.path)}\n                  tabIndex={0}\n                  role=\"button\"\n                  onKeyDown={(e) => {\n                    if (e.key === 'Enter' || e.key === ' ') {\n                      navigate(item.path);\n                    }\n                  }}\n                  style={{\n                    cursor: 'pointer',\n                    touchAction: 'manipulation', // Improves touch responsiveness\n                  }}\n                >\n\n\n                  <div className=\"hub-card-icon\">\n                    <IconComponent />\n                  </div>\n\n                  <h3 className=\"hub-card-title\">\n                    {item.title}\n                  </h3>\n\n                  <p className=\"hub-card-description\">\n                    {item.description}\n                  </p>\n                </motion.div>\n              );\n            })}\n          </div>\n\n          <motion.div\n            initial={{ opacity: 0 }}\n            animate={{ opacity: 1 }}\n            transition={{ duration: 0.8, delay: 0.5 }}\n            className=\"hub-bottom-decoration\"\n          >\n            <div className=\"decoration-content\">\n              <FaGraduationCap className=\"decoration-icon animate-bounce-gentle\" />\n              <span>Your learning journey starts here!</span>\n              <FaRocket className=\"decoration-icon animate-bounce-gentle\" />\n            </div>\n          </motion.div>\n        </div>\n\n\n      </div>\n    </div>\n  );\n};\n\nexport default Hub;"], "names": ["<PERSON><PERSON>", "navigate", "useNavigate", "user", "useSelector", "state", "currentQuote", "setCurrentQuote", "useState", "inspiringQuotes", "useEffect", "interval", "setInterval", "prev", "length", "clearInterval", "navigationItems", "title", "description", "icon", "FaQuestionCircle", "path", "color", "hoverColor", "FaBook", "FaVideo", "FaChartLine", "FaTrophy", "FaUser", "FaComments", "FaInfoCircle", "_jsx", "className", "children", "_jsxs", "firstName", "name", "FaStar", "style", "marginRight", "marginLeft", "fontSize", "marginTop", "map", "item", "index", "IconComponent", "motion", "div", "initial", "opacity", "y", "animate", "transition", "duration", "delay", "concat", "onClick", "tabIndex", "role", "onKeyDown", "e", "key", "cursor", "touchAction", "FaGraduationCap", "FaRocket"], "sourceRoot": ""}