{"ast": null, "code": "const genStickyStyle = token => {\n  const {\n    componentCls,\n    opacityLoading,\n    tableScrollThumbBg,\n    tableScrollThumbBgHover,\n    tableScrollThumbSize,\n    tableScrollBg,\n    zIndexTableSticky\n  } = token;\n  const tableBorder = \"\".concat(token.lineWidth, \"px \").concat(token.lineType, \" \").concat(token.tableBorderColor);\n  return {\n    [\"\".concat(componentCls, \"-wrapper\")]: {\n      [\"\".concat(componentCls, \"-sticky\")]: {\n        '&-holder': {\n          position: 'sticky',\n          zIndex: zIndexTableSticky,\n          background: token.colorBgContainer\n        },\n        '&-scroll': {\n          position: 'sticky',\n          bottom: 0,\n          height: \"\".concat(tableScrollThumbSize, \"px !important\"),\n          zIndex: zIndexTableSticky,\n          display: 'flex',\n          alignItems: 'center',\n          background: tableScrollBg,\n          borderTop: tableBorder,\n          opacity: opacityLoading,\n          '&:hover': {\n            transformOrigin: 'center bottom'\n          },\n          // fake scrollbar style of sticky\n          '&-bar': {\n            height: tableScrollThumbSize,\n            backgroundColor: tableScrollThumbBg,\n            borderRadius: 100,\n            transition: \"all \".concat(token.motionDurationSlow, \", transform none\"),\n            position: 'absolute',\n            bottom: 0,\n            '&:hover, &-active': {\n              backgroundColor: tableScrollThumbBgHover\n            }\n          }\n        }\n      }\n    }\n  };\n};\nexport default genStickyStyle;", "map": {"version": 3, "names": ["genStickyStyle", "token", "componentCls", "opacityLoading", "tableScrollThumbBg", "tableScrollThumbBgHover", "tableScrollThumbSize", "tableScrollBg", "zIndexTableSticky", "tableBorder", "concat", "lineWidth", "lineType", "tableBorderColor", "position", "zIndex", "background", "colorBgContainer", "bottom", "height", "display", "alignItems", "borderTop", "opacity", "transform<PERSON><PERSON>in", "backgroundColor", "borderRadius", "transition", "motionDurationSlow"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/node_modules/antd/es/table/style/sticky.js"], "sourcesContent": ["const genStickyStyle = token => {\n  const {\n    componentCls,\n    opacityLoading,\n    tableScrollThumbBg,\n    tableScrollThumbBgHover,\n    tableScrollThumbSize,\n    tableScrollBg,\n    zIndexTableSticky\n  } = token;\n  const tableBorder = `${token.lineWidth}px ${token.lineType} ${token.tableBorderColor}`;\n  return {\n    [`${componentCls}-wrapper`]: {\n      [`${componentCls}-sticky`]: {\n        '&-holder': {\n          position: 'sticky',\n          zIndex: zIndexTableSticky,\n          background: token.colorBgContainer\n        },\n        '&-scroll': {\n          position: 'sticky',\n          bottom: 0,\n          height: `${tableScrollThumbSize}px !important`,\n          zIndex: zIndexTableSticky,\n          display: 'flex',\n          alignItems: 'center',\n          background: tableScrollBg,\n          borderTop: tableBorder,\n          opacity: opacityLoading,\n          '&:hover': {\n            transformOrigin: 'center bottom'\n          },\n          // fake scrollbar style of sticky\n          '&-bar': {\n            height: tableScrollThumbSize,\n            backgroundColor: tableScrollThumbBg,\n            borderRadius: 100,\n            transition: `all ${token.motionDurationSlow}, transform none`,\n            position: 'absolute',\n            bottom: 0,\n            '&:hover, &-active': {\n              backgroundColor: tableScrollThumbBgHover\n            }\n          }\n        }\n      }\n    }\n  };\n};\nexport default genStickyStyle;"], "mappings": "AAAA,MAAMA,cAAc,GAAGC,KAAK,IAAI;EAC9B,MAAM;IACJC,YAAY;IACZC,cAAc;IACdC,kBAAkB;IAClBC,uBAAuB;IACvBC,oBAAoB;IACpBC,aAAa;IACbC;EACF,CAAC,GAAGP,KAAK;EACT,MAAMQ,WAAW,MAAAC,MAAA,CAAMT,KAAK,CAACU,SAAS,SAAAD,MAAA,CAAMT,KAAK,CAACW,QAAQ,OAAAF,MAAA,CAAIT,KAAK,CAACY,gBAAgB,CAAE;EACtF,OAAO;IACL,IAAAH,MAAA,CAAIR,YAAY,gBAAa;MAC3B,IAAAQ,MAAA,CAAIR,YAAY,eAAY;QAC1B,UAAU,EAAE;UACVY,QAAQ,EAAE,QAAQ;UAClBC,MAAM,EAAEP,iBAAiB;UACzBQ,UAAU,EAAEf,KAAK,CAACgB;QACpB,CAAC;QACD,UAAU,EAAE;UACVH,QAAQ,EAAE,QAAQ;UAClBI,MAAM,EAAE,CAAC;UACTC,MAAM,KAAAT,MAAA,CAAKJ,oBAAoB,kBAAe;UAC9CS,MAAM,EAAEP,iBAAiB;UACzBY,OAAO,EAAE,MAAM;UACfC,UAAU,EAAE,QAAQ;UACpBL,UAAU,EAAET,aAAa;UACzBe,SAAS,EAAEb,WAAW;UACtBc,OAAO,EAAEpB,cAAc;UACvB,SAAS,EAAE;YACTqB,eAAe,EAAE;UACnB,CAAC;UACD;UACA,OAAO,EAAE;YACPL,MAAM,EAAEb,oBAAoB;YAC5BmB,eAAe,EAAErB,kBAAkB;YACnCsB,YAAY,EAAE,GAAG;YACjBC,UAAU,SAAAjB,MAAA,CAAST,KAAK,CAAC2B,kBAAkB,qBAAkB;YAC7Dd,QAAQ,EAAE,UAAU;YACpBI,MAAM,EAAE,CAAC;YACT,mBAAmB,EAAE;cACnBO,eAAe,EAAEpB;YACnB;UACF;QACF;MACF;IACF;EACF,CAAC;AACH,CAAC;AACD,eAAeL,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}