{"version": 3, "file": "static/js/528.67596316.chunk.js", "mappings": "6JAAA,MAAQA,QAASC,GAAkBC,EAAQ,MAG9BC,EAAYC,UACrB,IAEI,aADuBH,EAAcI,KAAK,0BAA2BC,IACrDC,IACpB,CAAE,MAAOC,GACL,OAAOA,EAAMC,SAASF,IAC1B,GAISG,EAAgBN,UACzB,IAEI,aADuBH,EAAcI,KAAK,+BAAgCM,IAC1DJ,IACpB,CAAE,MAAOC,GACL,OAAOA,EAAMC,SAASF,IAC1B,GAISK,EAAsBR,UAC/B,IAEI,aADuBH,EAAcI,KAAK,uCAAwCC,IAClEC,IACpB,CAAE,MAAOC,GACL,OAAOA,EAAMC,SAASF,IAC1B,GAISM,EAA0BT,UACnC,IAEI,aADuBH,EAAca,IAAI,6CACzBP,IACpB,CAAE,MAAOC,GACL,OAAOA,EAAMC,SAASF,IAC1B,GAaSQ,EAAmBX,iBAAyB,IAAlBY,EAAOC,UAAAC,OAAA,QAAAC,IAAAF,UAAA,GAAAA,UAAA,GAAG,CAAC,EAC9C,IACI,MAAMG,EAAS,IAAIC,gBACfL,EAAQM,OAAOF,EAAOG,OAAO,QAASP,EAAQM,OAC9CN,EAAQQ,aAAaJ,EAAOG,OAAO,cAAeP,EAAQQ,aAC1DR,EAAQS,aAAaL,EAAOG,OAAO,cAAeP,EAAQS,aAC1DT,EAAQU,cAAcN,EAAOG,OAAO,eAAgBP,EAAQU,cAC5DV,EAAQW,iBAAiBP,EAAOG,OAAO,kBAAmBP,EAAQW,iBAGtE,aADuB1B,EAAca,IAAI,4BAADc,OAA6BR,EAAOS,cAC5DtB,IACpB,CAAE,MAAOC,GACL,OAAOA,EAAMC,SAASF,IAC1B,CACJ,EAEauB,EAAiB1B,eAAO2B,GAAyB,IAAjBC,EAAOf,UAAAC,OAAA,QAAAC,IAAAF,UAAA,GAAAA,UAAA,GAAG,EACnD,IAEI,aADuBhB,EAAca,IAAI,0BAADc,OAA2BG,EAAM,aAAAH,OAAYI,KACrEzB,IACpB,CAAE,MAAOC,GACL,OAAOA,EAAMC,SAASF,IAC1B,CACJ,C,iIC7DA,MAAM0B,EAAaC,IACjB,IACE,MAAMC,EAAe,IAAKC,OAAOC,cAAgBD,OAAOE,oBAElDC,EAAa,SAACC,EAAWC,GAA6B,IAAnBP,EAAIjB,UAAAC,OAAA,QAAAC,IAAAF,UAAA,GAAAA,UAAA,GAAG,OAC9C,MAAMyB,EAAaP,EAAaQ,mBAC1BC,EAAWT,EAAaU,aAE9BH,EAAWI,QAAQF,GACnBA,EAASE,QAAQX,EAAaY,aAE9BL,EAAWF,UAAUQ,eAAeR,EAAWL,EAAac,aAC5DP,EAAWR,KAAOA,EAElBU,EAASM,KAAKF,eAAe,EAAGb,EAAac,aAC7CL,EAASM,KAAKC,wBAAwB,GAAKhB,EAAac,YAAc,KACtEL,EAASM,KAAKE,6BAA6B,KAAOjB,EAAac,YAAcR,GAE7EC,EAAWW,MAAMlB,EAAac,aAC9BP,EAAWY,KAAKnB,EAAac,YAAcR,EAC7C,EAEA,OAAOP,GACL,IAAK,SAEHK,EAAW,IAAK,GAAK,UACrB,MACF,IAAK,WAEHA,EAAW,IAAK,IAAM,QACtBgB,YAAW,IAAMhB,EAAW,IAAK,GAAK,SAAS,IAC/C,MACF,IAAK,SAEHA,EAAW,IAAK,GAAK,QACrBgB,YAAW,IAAMhB,EAAW,IAAK,GAAK,SAAS,KAC/CgB,YAAW,IAAMhB,EAAW,IAAK,GAAK,SAAS,KAC/C,MACF,QACEA,EAAW,IAAK,GAAK,QAE3B,CAAE,MAAO/B,GAEPgD,QAAQC,IAAI,sBACd,GAw6BF,EAr6BiBC,KACf,MAAM,GAAEC,IAAOC,EAAAA,EAAAA,MACTC,GAAWC,EAAAA,EAAAA,OACX,KAAEC,IAASC,EAAAA,EAAAA,KAAaC,GAAUA,EAAMF,QAEvCG,EAASC,IAAcC,EAAAA,EAAAA,WAAS,IAChCC,EAAYC,IAAiBF,EAAAA,EAAAA,WAAS,IACtCG,EAAMC,IAAWJ,EAAAA,EAAAA,UAAS,OAC1BK,EAAWC,IAAgBN,EAAAA,EAAAA,UAAS,KACpCO,EAAiBC,IAAsBR,EAAAA,EAAAA,UAAS,IAChDS,EAASC,IAAcV,EAAAA,EAAAA,UAAS,KAChCW,EAAUC,IAAeZ,EAAAA,EAAAA,UAAS,IAClCa,EAAWC,IAAgBd,EAAAA,EAAAA,UAAS,OAG3Ce,EAAAA,EAAAA,YAAU,KAgEJxB,GAAMI,GA/DW3D,WACnB,IAIE,GAHA+D,GAAW,GACXX,QAAQC,IAAI,wBAAyBE,KAEhCI,IAASA,EAAKqB,OACHC,aAAaC,QAAQ,SAOjC,OALA9B,QAAQC,IAAI,wCACZ8B,EAAAA,GAAQ/E,MAAM,uCACdgF,EAAAA,EAAAA,kBAAgB,KACd3B,EAAS,SAAS,IAMxB,MAAMpD,QAAiBgF,EAAAA,EAAAA,IAAY,CAAEC,OAAQ/B,IAG7C,GAFAH,QAAQC,IAAI,qBAAsBhD,GAE9BA,EAASkF,QAAS,CACpB,IAAKlF,EAASF,KAKZ,OAJAgF,EAAAA,GAAQ/E,MAAM,4BACdgF,EAAAA,EAAAA,kBAAgB,KACd3B,EAAS,QAAQ,IAKrB,IAAKpD,EAASF,KAAKkE,WAAgD,IAAnChE,EAASF,KAAKkE,UAAUvD,OAKtD,OAJAqE,EAAAA,GAAQ/E,MAAM,6CACdgF,EAAAA,EAAAA,kBAAgB,KACd3B,EAAS,QAAQ,IAKrBW,EAAQ/D,EAASF,MACjBmE,EAAajE,EAASF,KAAKkE,WAC3BK,EAAW,IAAIc,MAAMnF,EAASF,KAAKkE,UAAUvD,QAAQ2E,KAAK,KAE1Db,EAAYvE,EAASF,KAAKkC,UAAY,KACtCyC,EAAa,IAAIY,MACjBtC,QAAQC,IAAI,4BAA6BhD,EAASF,MAClDiD,QAAQC,IAAI,2BAA4BhD,EAASF,KAAKkC,SACxD,MACEe,QAAQhD,MAAM,kBAAmBC,EAAS8E,SAC1CA,EAAAA,GAAQ/E,MAAMC,EAAS8E,SAAW,wBAClCC,EAAAA,EAAAA,kBAAgB,KACd3B,EAAS,QAAQ,GAGvB,CAAE,MAAOrD,GACPgD,QAAQhD,MAAM,sBAAuBA,GACrC+E,EAAAA,GAAQ/E,MAAM,2CACdgF,EAAAA,EAAAA,kBAAgB,KACd3B,EAAS,QAAQ,GAErB,CAAC,QACCM,GAAW,EACb,GAIA4B,EACF,GACC,CAACpC,EAAIE,EAAUE,IAGlB,MAAMiC,GAAmBC,EAAAA,EAAAA,cAAY7F,UACnCoD,QAAQC,IAAI,gEACZD,QAAQC,IAAI,4BAA6BY,GAEzC,IAEEpC,EAAU,UAGVqC,GAAc,GACdd,QAAQC,IAAI,qCACZD,QAAQC,IAAI,iDAEZ,IAAIyC,EAAcnC,EAClB,IAAKmC,IAAgBA,EAAYd,IAAK,CACpC,MAAMe,EAAad,aAAaC,QAAQ,QACxC,GAAIa,EACF,IACED,EAAcE,KAAKC,MAAMF,EAC3B,CAAE,MAAO3F,GAKP,OAJAgD,QAAQhD,MAAM,kCAAmCA,QACjDgF,EAAAA,EAAAA,kBAAgB,KACd3B,EAAS,SAAS,GAGtB,CAEJ,CAEA,IAAKqC,IAAgBA,EAAYd,IAK/B,OAJAG,EAAAA,GAAQ/E,MAAM,kDACdgF,EAAAA,EAAAA,kBAAgB,KACd3B,EAAS,SAAS,IAKtB,MAAMyC,EAAU,IAAIR,KACdS,EAAYC,KAAKC,OAAOH,EAAUrB,GAAa,KAErD,IAAIyB,EAAiB,EACrB,MAAMC,EAAgBlC,EAAUmC,KAAI,CAACC,EAAUC,KAC7C,MAAMC,EAAalC,EAAQiC,GAC3B,IAAIE,GAAY,EACZC,EAAsB,GAG1B,MAAMC,EAAeL,EAAS3E,MAAQ2E,EAASM,YAAc,MAyBrD,IAADC,EAvB4B,QAA/BF,EAAaG,eAA4C,YAAjBH,EAEtCL,EAAS7F,SAAuC,kBAArB6F,EAAS7F,QAElC6F,EAASS,eAAiBT,EAAS7F,QAAQ6F,EAASS,gBACtDL,EAAsBJ,EAAS7F,QAAQ6F,EAASS,eAChDN,EAAYD,IAAeE,GAGpBJ,EAASU,eAAiBV,EAAS7F,QAAQ6F,EAASU,gBAC3DN,EAAsBJ,EAAS7F,QAAQ6F,EAASU,eAChDP,EAAYD,IAAeE,GAGpBJ,EAASS,gBAChBL,EAAsBJ,EAASS,cAC/BN,EAAYD,IAAeE,IAI7BA,EAAsBJ,EAASS,eAAiBT,EAASU,eAAiB,GAC1EP,EAAYD,IAAeE,IAI7BA,EAAsBJ,EAASS,eAAiB,GAChDN,GAAsB,OAAVD,QAAU,IAAVA,OAAU,EAAVA,EAAYM,cAAcG,WAA8B,QAAxBJ,EAAKH,SAAmB,IAAAG,OAAA,EAAnBA,EAAqBC,cAAcG,SAKtF,OAFIR,GAAWN,IAER,CACLe,WAAYZ,EAASzB,KAAG,YAAAxD,OAAgBkF,GACxCY,aAAuC,kBAAlBb,EAASc,KAAoBd,EAASc,KAAI,YAAA/F,OAAekF,EAAQ,GACtFc,aAAcf,EAASc,MAAI,YAAA/F,OAAgBkF,EAAQ,GACnDC,WAAkC,kBAAfA,EAA0BA,EAAac,OAAOd,GAAc,IAC/EO,cAAeL,EACfD,YACAE,aAAcA,EACdlG,QAAS6F,EAAS7F,SAAW,KAC7B8G,cAAejB,EAASkB,OAASlB,EAASiB,eAAiBjB,EAASmB,UAAY,KAChFD,MAAOlB,EAASkB,OAASlB,EAASiB,eAAiBjB,EAASmB,UAAY,KACzE,IAGGC,EAAazB,KAAK0B,MAAOxB,EAAiBjC,EAAUvD,OAAU,KAE9DiH,EAAoB5D,EAAK6D,cAAgB7D,EAAK4D,mBAAqB,GACnEE,EAAUJ,GAAcE,EAAoB,OAAS,OAErDG,EAAa,CACjBC,KAAM5E,EACNI,KAAMmC,EAAYd,IAClBoD,OAAQ,CACN9B,iBACA+B,aAAchE,EAAUvD,OAASwF,EACjCuB,aACAS,MAAOT,EACPI,QAASA,EACT9B,YACAoC,UAAWpC,EACXqC,OAAyB,GAAjBlC,EACRmC,eAAgBpE,EAAUvD,SAI9B,IACEsC,QAAQC,IAAI,uCAA8B6E,GAC1C,MAAM7H,QAAiBN,EAAAA,EAAAA,IAAUmI,GAGjC,GAFA9E,QAAQC,IAAI,gCAAuBhD,IAE/BA,EAASkF,QAiCX,OANAnC,QAAQhD,MAAM,iCAA6BC,EAAS8E,cAEpDhC,YAAW,KACTe,GAAc,EAAM,GAEnB,KAhCiB,CACpBd,QAAQC,IAAI,4DAGZ,MAAMqF,EAAkB,CACtBb,aACAvB,iBACAmC,eAAgBpE,EAAUvD,OAC1BqF,YACAI,gBACAoC,OAAQtI,EAASsI,QAAU,KAC3BC,SAAUzE,EAAKoD,KACfsB,YAAa1E,EAAK2E,SAAW3E,EAAK4E,SAClChB,kBAAmBA,EACnBE,QAASA,SAIL,IAAIe,SAAQC,GAAW9F,WAAW8F,EAAS,OAEjD7F,QAAQC,IAAI,+CACZ+B,EAAAA,EAAAA,kBAAgB,KACd3B,EAAS,SAADjC,OAAU+B,EAAE,WAAW,CAC7BM,MAAO6E,GACP,GAEN,CASF,CAAE,MAAOQ,GAOP,OANA9F,QAAQhD,MAAM,sCAAkC8I,QAEhD/F,YAAW,KACTe,GAAc,EAAM,GAEnB,IAEL,CACF,CAAE,MAAO9D,GAOP,OANAgD,QAAQhD,MAAM,yBAA0BA,QAExC+C,YAAW,KACTe,GAAc,EAAM,GAEnB,IAEL,CAAC,QACCA,GAAc,EAChB,IACC,CAACW,EAAWR,EAAWI,EAASlB,EAAIE,EAAUE,KAGjDoB,EAAAA,EAAAA,YAAU,KACR,GAAIJ,GAAY,EAId,OAFAvB,QAAQC,IAAI,gDACZuC,IAIF,MAAMuD,EAAQC,aAAY,KACxBxE,GAAYyE,GAAQA,EAAO,GAAE,GAC5B,KAEH,MAAO,IAAMC,cAAcH,EAAM,GAChC,CAACxE,EAAUiB,IAGd,MAAM2D,EAAsBC,IAC1B,MAAMC,EAAa,IAAIhF,GACvBgF,EAAWlF,GAAmBiF,EAC9B9E,EAAW+E,EAAW,EAuDlBC,EAAuBA,KAC3B,IAAI9I,EAAU,GAad,OAVI4E,MAAMmE,QAAQC,EAAShJ,SACzBA,EAAUgJ,EAAShJ,QACVgJ,EAAShJ,SAAuC,kBAArBgJ,EAAShJ,QAE7CA,EAAUiJ,OAAOC,OAAOF,EAAShJ,SACxBgJ,EAASG,SAAWH,EAASI,UAEtCpJ,EAAU,CAACgJ,EAASG,QAASH,EAASI,QAASJ,EAASK,QAASL,EAASM,SAASC,OAAOC,UAGvFxJ,GAA8B,IAAnBA,EAAQE,QAmDtBuJ,EAAAA,EAAAA,KAAA,OAAKC,UAAU,YAAWC,SACvB3J,EAAQ4F,KAAI,CAACgE,EAAQ9D,KACpB,MAAM+D,EAAehD,OAAOiD,aAAa,GAAKhE,GACxCiE,EAAalG,EAAQF,KAAqBiG,EAEhD,OACEH,EAAAA,EAAAA,KAAA,UAEEO,QAASA,IAAMrB,EAAmBiB,GAClCF,UAAS,wFAAA9I,OACPmJ,EACI,qDACA,mEACHJ,UAEHM,EAAAA,EAAAA,MAAA,OAAKP,UAAU,yBAAwBC,SAAA,EACrCF,EAAAA,EAAAA,KAAA,OAAKC,UAAS,2FAAA9I,OACZmJ,EACI,yBACA,6BACHJ,SACAE,KAEHJ,EAAAA,EAAAA,KAAA,QAAMC,UAAU,yDAAwDC,SACnD,kBAAXC,EAAsBA,EAASxE,KAAK8E,UAAUN,SAjBrD9D,EAoBE,OA3EbmE,EAAAA,EAAAA,MAAA,OAAKP,UAAU,YAAWC,SAAA,EACxBM,EAAAA,EAAAA,MAAA,OAAKP,UAAU,oEAAmEC,SAAA,EAChFF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,8BAA6BC,SAAC,wCAC3CM,EAAAA,EAAAA,MAAA,WAASP,UAAU,OAAMC,SAAA,EACvBF,EAAAA,EAAAA,KAAA,WAASC,UAAU,yCAAwCC,SAAC,wBAC5DF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,0EAAyEC,SACrFvE,KAAK8E,UAAUlB,EAAU,KAAM,YAMtCS,EAAAA,EAAAA,KAAA,OAAKC,UAAU,YAAWC,SACvB,CAAC,kBAAmB,kBAAmB,kBAAmB,mBAAmB/D,KAAI,CAACgE,EAAQ9D,KACzF,MAAM+D,EAAehD,OAAOiD,aAAa,GAAKhE,GACxCiE,EAAalG,EAAQF,KAAqBiG,EAEhD,OACEH,EAAAA,EAAAA,KAAA,UAEEO,QAASA,IAAMrB,EAAmBiB,GAClCF,UAAS,wFAAA9I,OACPmJ,EACI,qDACA,mEACHJ,UAEHM,EAAAA,EAAAA,MAAA,OAAKP,UAAU,yBAAwBC,SAAA,EACrCF,EAAAA,EAAAA,KAAA,OAAKC,UAAS,2FAAA9I,OACZmJ,EACI,yBACA,6BACHJ,SACAE,KAEHJ,EAAAA,EAAAA,KAAA,QAAMC,UAAU,yDAAwDC,SACrEC,QAjBA9D,EAoBE,QAuCb,EAKJqE,EAAuBA,KAEzBF,EAAAA,EAAAA,MAAA,OAAKP,UAAU,YAAWC,SAAA,EACxBM,EAAAA,EAAAA,MAAA,OAAKP,UAAU,mDAAkDC,SAAA,EAC/DF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,yCAAwCC,SAAC,wBACtDF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,gBAAeC,SAAC,0CAE/BF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,WAAUC,UACvBF,EAAAA,EAAAA,KAAA,SACEvI,KAAK,OACLkJ,MAAOvG,EAAQF,IAAoB,GACnC0G,SAAWC,GAAM3B,EAAmB2B,EAAEC,OAAOH,OAC7CI,YAAY,2BACZd,UAAU,oHACVe,WAAS,SAQbC,EAAsBA,IACtB1B,EAAShJ,SAAW4E,MAAMmE,QAAQC,EAAShJ,UAAYgJ,EAAShJ,QAAQE,OAAS,EAC5E4I,IAEAqB,IAIX,GAAIjH,EACF,OACEuG,EAAAA,EAAAA,KAAA,OAAKC,UAAU,6FAA4FC,UACzGM,EAAAA,EAAAA,MAAA,OAAKP,UAAU,cAAaC,SAAA,EAC1BF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,iFACfD,EAAAA,EAAAA,KAAA,KAAGC,UAAU,4BAA2BC,SAAC,yBAMjD,IAAKpG,IAASE,EAAUvD,OACtB,OACEuJ,EAAAA,EAAAA,KAAA,OAAKC,UAAU,6FAA4FC,UACzGF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,0EAAyEC,UACtFM,EAAAA,EAAAA,MAAA,OAAKP,UAAU,cAAaC,SAAA,EAC1BF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,uCAAsCC,SAAC,4BACrDF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,qBAAoBC,SAAC,+CAClCF,EAAAA,EAAAA,KAAA,UACEO,QAASA,KAAMxF,EAAAA,EAAAA,kBAAgB,IAAM3B,EAAS,WAC9C6G,UAAU,uFAAsFC,SACjG,2BAUX,IAAKlG,EAAUE,GACb,OACE8F,EAAAA,EAAAA,KAAA,OAAKC,UAAU,6FAA4FC,UACzGF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,0EAAyEC,UACtFM,EAAAA,EAAAA,MAAA,OAAKP,UAAU,cAAaC,SAAA,EAC1BF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,uCAAsCC,SAAC,wBACrDF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,qBAAoBC,SAAC,0CAClCF,EAAAA,EAAAA,KAAA,UACEO,QAASA,KAAMxF,EAAAA,EAAAA,kBAAgB,IAAM3B,EAAS,WAC9C6G,UAAU,uFAAsFC,SACjG,2BASX,MAAMX,EAAWvF,EAAUE,GACrBgH,EAAiBhH,IAAoBF,EAAUvD,OAAS,EAG9D,OAAK8I,GAAgC,kBAAbA,EAsBpB3F,GAEA4G,EAAAA,EAAAA,MAAAW,EAAAA,SAAA,CAAAjB,SAAA,EACEF,EAAAA,EAAAA,KAAA,SAAAE,SAAA,wvDAyCAF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,cAAcmB,MAAO,CAClCC,SAAU,QACVC,IAAK,EACLC,KAAM,EACNC,MAAO,OACPC,OAAQ,OACRC,QAAS,OACTC,WAAY,SACZC,eAAgB,SAChBC,OAAQ,MACR3B,UACAM,EAAAA,EAAAA,MAAA,OAAKY,MAAO,CACVU,WAAY,4BACZC,aAAc,OACdC,QAASrK,OAAOsK,YAAc,IAAM,YAAc,YAClDC,UAAW,SACXC,UAAW,iEACXC,eAAgB,aAChBC,OAAQ,qCACRC,SAAU3K,OAAOsK,YAAc,IAAM,QAAU,QAC/CT,MAAO,MACPe,UAAW,0BACXrC,SAAA,EAEAM,EAAAA,EAAAA,MAAA,OAAKY,MAAO,CACVI,MAAO7J,OAAOsK,YAAc,IAAM,QAAU,QAC5CR,OAAQ9J,OAAOsK,YAAc,IAAM,QAAU,QAC7CO,OAAQ,mBACRV,WAAY,iEACZC,aAAc,MACdL,QAAS,OACTC,WAAY,SACZC,eAAgB,SAChBW,UAAW,2CACXJ,UAAW,2EACXd,SAAU,YACVnB,SAAA,EACAF,EAAAA,EAAAA,KAAA,OAAKoB,MAAO,CACVI,MAAO7J,OAAOsK,YAAc,IAAM,OAAS,OAC3CR,OAAQ9J,OAAOsK,YAAc,IAAM,OAAS,OAC5CH,WAAY,QACZC,aAAc,MACdL,QAAS,OACTC,WAAY,SACZC,eAAgB,SAChBO,UAAW,iCACXjC,UACAF,EAAAA,EAAAA,KAAA,QAAMoB,MAAO,CACXqB,SAAU9K,OAAOsK,YAAc,IAAM,OAAS,OAC9CnC,OAAQ,0CACRI,SAAC,oBAGLF,EAAAA,EAAAA,KAAA,OAAKoB,MAAO,CACVC,SAAU,WACVG,MAAO,OACPC,OAAQ,OACRY,OAAQ,qCACRN,aAAc,MACdQ,UAAW,gDAKfvC,EAAAA,EAAAA,KAAA,MAAIoB,MAAO,CACTqB,SAAU9K,OAAOsK,YAAc,IAAM,OAAS,OAC9CS,WAAY,MACZZ,WAAY,4CACZa,qBAAsB,OACtBC,oBAAqB,cACrBC,eAAgB,OAChBL,OAAQ,aACRD,UAAW,6BACXO,cAAe,UACf5C,SAAC,0BAGHF,EAAAA,EAAAA,KAAA,KAAGoB,MAAO,CACRqB,SAAU9K,OAAOsK,YAAc,IAAM,OAAS,OAC9Cc,MAAO,UACPP,OAAQ,aACRQ,WAAY,MACZN,WAAY,OACZxC,SAAC,6DAGHF,EAAAA,EAAAA,KAAA,OAAKoB,MAAO,CACVM,QAAS,OACTE,eAAgB,SAChBD,WAAY,SACZsB,IAAK,MACLC,aAAc,QACdhD,SACC,CAAC,EAAG,EAAG,EAAG,GAAG/D,KAAIgH,IAChBnD,EAAAA,EAAAA,KAAA,OAEEC,UAAU,mBACVmB,MAAO,CACLI,MAAO,OACPC,OAAQ,OACRK,WAAY,4CACZC,aAAc,MACdI,UAAW,uCAPRgB,QAcXnD,EAAAA,EAAAA,KAAA,OAAKoB,MAAO,CACVqB,SAAU9K,OAAOsK,YAAc,IAAM,OAAS,OAC9Cc,MAAO,UACPL,WAAY,MACZU,cAAe,YACfN,cAAe,OACf5C,SAAC,2BAUXM,EAAAA,EAAAA,MAAA,OAAKP,UAAU,uFAAsFC,SAAA,EAEnGF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,sEAAqEC,UAClFM,EAAAA,EAAAA,MAAA,OAAKP,UAAU,sDAAqDC,SAAA,EAElEM,EAAAA,EAAAA,MAAA,OAAKP,UAAU,8EAA6EC,SAAA,EAE1FF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,2FAA0FC,SACrGpG,EAAKoD,QAIR8C,EAAAA,EAAAA,KAAA,OAAKC,UAAU,sBAAqBC,UAClCM,EAAAA,EAAAA,MAAA,OACEP,UAAU,yGACVmB,MAAO,CACLiC,WAAY,8BACZvB,WAAYxH,GAAY,GACpB,8CACA,8CACJgJ,YAAahJ,GAAY,GAAK,UAAY,UAC1CyI,MAAO,QACPZ,UAAW7H,GAAY,GACnB,8DACA,8DACJiI,UAAWjI,GAAY,GAAK,oBAAsB,QAClD4F,SAAA,EACFF,EAAAA,EAAAA,KAACuD,EAAAA,IAAO,CACNtD,UAAU,wBACVmB,MAAO,CACLtB,OAAQ,yCACRyC,UAAWjI,GAAY,GAAK,qBAAuB,WAIvD0F,EAAAA,EAAAA,KAAA,QACEC,UAAU,uDACVmB,MAAO,CACLiC,WAAY,8BACZd,UAAWjI,GAAY,GAAK,oBAAsB,QAClD4F,SAncEsD,KAClB,MAAMC,EAAU1H,KAAKC,MAAMwH,EAAU,IAC/BE,EAAOF,EAAU,GAEvB,MAAM,GAANrM,OAAUsM,EAAO,KAAAtM,OAAIuM,EAAKtM,WAAWuM,SAAS,EAAG,KAAI,EAgctCC,CAAWtJ,WAMlBkG,EAAAA,EAAAA,MAAA,KAAGP,UAAU,2EAA0EC,SAAA,CACpFhG,EAAkB,EAAE,OAAKF,EAAUvD,cAKxC+J,EAAAA,EAAAA,MAAA,OAAKP,UAAU,eAAcC,SAAA,EAC3BM,EAAAA,EAAAA,MAAA,OAAKP,UAAU,yCAAwCC,SAAA,EACrDF,EAAAA,EAAAA,KAAA,QAAMC,UAAU,+CAA8CC,SAAC,cAG/DM,EAAAA,EAAAA,MAAA,QAAMP,UAAU,6CAA4CC,SAAA,CACzDnE,KAAK0B,OAAQvD,EAAkB,GAAKF,EAAUvD,OAAU,KAAK,WAGlEuJ,EAAAA,EAAAA,KAAA,OACEC,UAAU,mDACVmB,MAAO,CACLK,OAAQ9J,OAAOsK,YAAc,IAAM,MAAQ,OAC3C4B,gBAAiB,WACjB3D,UAEFF,EAAAA,EAAAA,KAAA,OACEoB,MAAO,CACLI,MAAM,GAADrK,QAAO+C,EAAkB,GAAKF,EAAUvD,OAAU,IAAG,KAC1DgL,OAAQ,OACRK,WAAY,8CACZC,aAAc,SACd+B,WAAY,sBACZ3B,UAAW,iDASvBnC,EAAAA,EAAAA,KAAA,OAAKC,UAAU,yCAAwCC,UACrDM,EAAAA,EAAAA,MAAA,OAAKP,UAAU,iIAAgIC,SAAA,EAE7IM,EAAAA,EAAAA,MAAA,OAAKP,UAAU,eAAcC,SAAA,EAC3BF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,4GAA2GC,SAC7F,kBAAlBX,EAASrC,KAAoBqC,EAASrC,KAAO,aAGtDqC,EAASjC,QACR0C,EAAAA,EAAAA,KAAA,OAAKC,UAAU,gDAA+CC,UAC5DF,EAAAA,EAAAA,KAAA,OACE+D,IAAKxE,EAASjC,MACd0G,IAAI,mBACJ/D,UAAU,wFACVgE,QAAUpD,IACRA,EAAEC,OAAOM,MAAMM,QAAU,OAEzB,MAAMwC,EAAWC,SAASC,cAAc,OACxCF,EAASjE,UAAY,iCACrBiE,EAASG,UAAY,gCACrBxD,EAAEC,OAAOwD,WAAWC,YAAYL,EAAS,UAQnDlE,EAAAA,EAAAA,KAAA,OACEC,UAAU,YACVmB,MAAO,CACL8B,aAAcvL,OAAOsK,YAAc,IAAM,OAAS,QAClD/B,SAxgBgBsE,MAK1B,QAJqBjF,EAAS9H,MAAQ8H,EAAS7C,YAAc,OAIxCE,eACnB,IAAK,MACL,IAAK,kBACL,IAAK,iBAaL,QAEE,OAAOyC,IAZT,IAAK,OACL,IAAK,oBACL,IAAK,YACL,IAAK,OACH,OAAOqB,IAET,IAAK,QACL,IAAK,UACH,OAAOO,IAKX,EAkfSuD,MAIHhE,EAAAA,EAAAA,MAAA,OACEP,UAAU,oBACVmB,MAAO,CACLqD,cAAe9M,OAAOsK,YAAc,IAAM,SAAW,MACrDL,eAAgBjK,OAAOsK,YAAc,IAAM,SAAW,gBACtDgB,IAAKtL,OAAOsK,YAAc,IAAM,OAAS,KACzC/B,SAAA,EAEFM,EAAAA,EAAAA,MAAA,UACED,QAviBSmE,KACfxK,EAAkB,IACpB1C,EAAU,YACV2C,EAAmBD,EAAkB,GACvC,EAoiBUyK,SAA8B,IAApBzK,EACV+F,UAAS,sEAAA9I,OACa,IAApB+C,EACI,+CACA,+CAENkH,MAAO,CACLY,QAASrK,OAAOsK,YAAc,IAAM,YAAc,YAClDQ,SAAU9K,OAAOsK,YAAc,IAAM,OAAS,OAC9CT,MAAO7J,OAAOsK,YAAc,IAAM,OAAS,OAC3CL,eAAgB,UAChB1B,SAAA,EAEFF,EAAAA,EAAAA,KAAC4E,EAAAA,IAAW,CACVxD,MAAO,CACLI,MAAO7J,OAAOsK,YAAc,IAAM,OAAS,OAC3CR,OAAQ9J,OAAOsK,YAAc,IAAM,OAAS,UAE9C,cAIHf,GACClB,EAAAA,EAAAA,KAAA,UACEO,QAAShF,EACToJ,SAAU/K,EACVqG,UAAS,sEAAA9I,OACPyC,EACI,+CACA,8CAENwH,MAAO,CACLY,QAASrK,OAAOsK,YAAc,IAAM,YAAc,YAClDQ,SAAU9K,OAAOsK,YAAc,IAAM,OAAS,OAC9CT,MAAO7J,OAAOsK,YAAc,IAAM,OAAS,OAC3CL,eAAgB,UAChB1B,SAEDtG,GACC4G,EAAAA,EAAAA,MAAAW,EAAAA,SAAA,CAAAjB,SAAA,EACEF,EAAAA,EAAAA,KAAA,OACEC,UAAU,uEACVmB,MAAO,CACLI,MAAO7J,OAAOsK,YAAc,IAAM,OAAS,OAC3CR,OAAQ9J,OAAOsK,YAAc,IAAM,OAAS,UAEzC,oBAITzB,EAAAA,EAAAA,MAAAW,EAAAA,SAAA,CAAAjB,SAAA,EACEF,EAAAA,EAAAA,KAAC6E,EAAAA,IAAO,CACNzD,MAAO,CACLI,MAAO7J,OAAOsK,YAAc,IAAM,OAAS,OAC3CR,OAAQ9J,OAAOsK,YAAc,IAAM,OAAS,UAE9C,oBAMRzB,EAAAA,EAAAA,MAAA,UACED,QA9mBGuE,KACX5K,EAAkBF,EAAUvD,OAAS,IACvCe,EAAU,YACV2C,EAAmBD,EAAkB,GACvC,EA2mBY+F,UAAU,8GACVmB,MAAO,CACLY,QAASrK,OAAOsK,YAAc,IAAM,YAAc,YAClDQ,SAAU9K,OAAOsK,YAAc,IAAM,OAAS,OAC9CT,MAAO7J,OAAOsK,YAAc,IAAM,OAAS,OAC3CL,eAAgB,UAChB1B,SAAA,CACH,QAECF,EAAAA,EAAAA,KAAC+E,EAAAA,IAAY,CACX3D,MAAO,CACLI,MAAO7J,OAAOsK,YAAc,IAAM,OAAS,OAC3CR,OAAQ9J,OAAOsK,YAAc,IAAM,OAAS,yBA9Y1DjC,EAAAA,EAAAA,KAAA,OAAKC,UAAU,6FAA4FC,UACzGF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,0EAAyEC,UACtFM,EAAAA,EAAAA,MAAA,OAAKP,UAAU,cAAaC,SAAA,EAC1BF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,uCAAsCC,SAAC,2BACrDF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,qBAAoBC,SAAC,gDAClCF,EAAAA,EAAAA,KAAA,UACEO,QAASA,KAAMxF,EAAAA,EAAAA,kBAAgB,IAAM3B,EAAS,WAC9C6G,UAAU,uFAAsFC,SACjG,0BA8YH,C", "sources": ["apicalls/reports.js", "pages/user/Quiz/QuizPlay.js"], "sourcesContent": ["const { default: axiosInstance } = require(\".\");\r\n\r\n// add report\r\nexport const addReport = async (payload) => {\r\n    try {\r\n        const response = await axiosInstance.post(\"/api/reports/add-report\", payload);\r\n        return response.data;\r\n    } catch (error) {\r\n        return error.response.data;\r\n    }\r\n}\r\n\r\n// get all reports\r\nexport const getAllReports = async (filters) => {\r\n    try {\r\n        const response = await axiosInstance.post(\"/api/reports/get-all-reports\", filters);\r\n        return response.data;\r\n    } catch (error) {\r\n        return error.response.data;\r\n    }\r\n}\r\n\r\n// get all reports by user\r\nexport const getAllReportsByUser = async (payload) => {\r\n    try {\r\n        const response = await axiosInstance.post(\"/api/reports/get-all-reports-by-user\", payload);\r\n        return response.data;\r\n    } catch (error) {\r\n        return error.response.data;\r\n    }\r\n}\r\n\r\n// get all reports for ranking (legacy)\r\nexport const getAllReportsForRanking = async (filters) => {\r\n    try {\r\n        const response = await axiosInstance.get(\"/api/reports/get-all-reports-for-ranking\");\r\n        return response.data;\r\n    } catch (error) {\r\n        return error.response.data;\r\n    }\r\n}\r\n\r\n// Enhanced XP-based ranking endpoints\r\nexport const getEnhancedLeaderboard = async (level = 'all', limit = 1000) => {\r\n    try {\r\n        const response = await axiosInstance.get(`/api/quiz/enhanced-leaderboard?level=${level}&limit=${limit}`);\r\n        return response.data;\r\n    } catch (error) {\r\n        return error.response.data;\r\n    }\r\n}\r\n\r\nexport const getXPLeaderboard = async (options = {}) => {\r\n    try {\r\n        const params = new URLSearchParams();\r\n        if (options.limit) params.append('limit', options.limit);\r\n        if (options.classFilter) params.append('classFilter', options.classFilter);\r\n        if (options.levelFilter) params.append('levelFilter', options.levelFilter);\r\n        if (options.seasonFilter) params.append('seasonFilter', options.seasonFilter);\r\n        if (options.includeInactive) params.append('includeInactive', options.includeInactive);\r\n\r\n        const response = await axiosInstance.get(`/api/quiz/xp-leaderboard?${params.toString()}`);\r\n        return response.data;\r\n    } catch (error) {\r\n        return error.response.data;\r\n    }\r\n}\r\n\r\nexport const getUserRanking = async (userId, context = 5) => {\r\n    try {\r\n        const response = await axiosInstance.get(`/api/quiz/user-ranking/${userId}?context=${context}`);\r\n        return response.data;\r\n    } catch (error) {\r\n        return error.response.data;\r\n    }\r\n}\r\n\r\nexport const getClassRankings = async (className, limit = 50) => {\r\n    try {\r\n        const response = await axiosInstance.get(`/api/quiz/class-rankings/${className}?limit=${limit}`);\r\n        return response.data;\r\n    } catch (error) {\r\n        return error.response.data;\r\n    }\r\n}\r\n\r\n// XP Dashboard endpoints\r\nexport const getXPDashboard = async () => {\r\n    try {\r\n        const response = await axiosInstance.get(\"/api/xp-dashboard/dashboard\");\r\n        return response.data;\r\n    } catch (error) {\r\n        return error.response.data;\r\n    }\r\n}\r\n\r\nexport const getClassLeaderboard = async () => {\r\n    try {\r\n        const response = await axiosInstance.get(\"/api/xp-dashboard/class-leaderboard\");\r\n        return response.data;\r\n    } catch (error) {\r\n        return error.response.data;\r\n    }\r\n}\r\n", "import React, { useState, useEffect, useCallback, startTransition } from 'react';\nimport { usePara<PERSON>, useNavigate } from 'react-router-dom';\nimport { useSelector } from 'react-redux';\nimport { message } from 'antd';\nimport { \n  Tb<PERSON><PERSON>, \n  TbArrowLeft, \n  TbArrowRight, \n  TbCheck\n} from 'react-icons/tb';\nimport { getExamById } from '../../../apicalls/exams';\nimport { addReport } from '../../../apicalls/reports';\n\n// Professional Sound System\nconst playSound = (type) => {\n  try {\n    const audioContext = new (window.AudioContext || window.webkitAudioContext)();\n\n    const createTone = (frequency, duration, type = 'sine') => {\n      const oscillator = audioContext.createOscillator();\n      const gainNode = audioContext.createGain();\n\n      oscillator.connect(gainNode);\n      gainNode.connect(audioContext.destination);\n\n      oscillator.frequency.setValueAtTime(frequency, audioContext.currentTime);\n      oscillator.type = type;\n\n      gainNode.gain.setValueAtTime(0, audioContext.currentTime);\n      gainNode.gain.linearRampToValueAtTime(0.1, audioContext.currentTime + 0.01);\n      gainNode.gain.exponentialRampToValueAtTime(0.001, audioContext.currentTime + duration);\n\n      oscillator.start(audioContext.currentTime);\n      oscillator.stop(audioContext.currentTime + duration);\n    };\n\n    switch(type) {\n      case 'select':\n        // Professional click sound\n        createTone(800, 0.1, 'square');\n        break;\n      case 'navigate':\n        // Smooth navigation sound\n        createTone(600, 0.15, 'sine');\n        setTimeout(() => createTone(800, 0.1, 'sine'), 50);\n        break;\n      case 'submit':\n        // Success sound\n        createTone(523, 0.2, 'sine'); // C\n        setTimeout(() => createTone(659, 0.2, 'sine'), 100); // E\n        setTimeout(() => createTone(784, 0.3, 'sine'), 200); // G\n        break;\n      default:\n        createTone(600, 0.1, 'sine');\n    }\n  } catch (error) {\n    // Fallback for browsers that don't support Web Audio API\n    console.log('Audio not supported');\n  }\n};\n\nconst QuizPlay = () => {\n  const { id } = useParams();\n  const navigate = useNavigate();\n  const { user } = useSelector((state) => state.user);\n  \n  const [loading, setLoading] = useState(true);\n  const [submitting, setSubmitting] = useState(false);\n  const [quiz, setQuiz] = useState(null);\n  const [questions, setQuestions] = useState([]);\n  const [currentQuestion, setCurrentQuestion] = useState(0);\n  const [answers, setAnswers] = useState([]);\n  const [timeLeft, setTimeLeft] = useState(0);\n  const [startTime, setStartTime] = useState(null);\n\n  // Load quiz data\n  useEffect(() => {\n    const loadQuizData = async () => {\n      try {\n        setLoading(true);\n        console.log('Loading quiz with ID:', id);\n        \n        if (!user || !user._id) {\n          const token = localStorage.getItem('token');\n          if (!token) {\n            console.log('No token found, redirecting to login');\n            message.error('Please login to access quizzes');\n            startTransition(() => {\n              navigate('/login');\n            });\n            return;\n          }\n        }\n\n        const response = await getExamById({ examId: id });\n        console.log('Quiz API response:', response);\n        \n        if (response.success) {\n          if (!response.data) {\n            message.error('Quiz data not found');\n            startTransition(() => {\n              navigate('/quiz');\n            });\n            return;\n          }\n          \n          if (!response.data.questions || response.data.questions.length === 0) {\n            message.error('This quiz has no questions available');\n            startTransition(() => {\n              navigate('/quiz');\n            });\n            return;\n          }\n\n          setQuiz(response.data);\n          setQuestions(response.data.questions);\n          setAnswers(new Array(response.data.questions.length).fill(''));\n          // Duration is already in seconds, no need to multiply by 60\n          setTimeLeft(response.data.duration || 180);\n          setStartTime(new Date());\n          console.log('Quiz loaded successfully:', response.data);\n          console.log('Quiz duration (seconds):', response.data.duration);\n        } else {\n          console.error('Quiz API error:', response.message);\n          message.error(response.message || 'Failed to load quiz');\n          startTransition(() => {\n            navigate('/quiz');\n          });\n        }\n      } catch (error) {\n        console.error('Quiz loading error:', error);\n        message.error('Failed to load quiz. Please try again.');\n        startTransition(() => {\n          navigate('/quiz');\n        });\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    if (id && user) {\n      loadQuizData();\n    }\n  }, [id, navigate, user]);\n\n  // Submit quiz function\n  const handleSubmitQuiz = useCallback(async () => {\n    console.log('🚀 Submit button clicked - showing loading overlay');\n    console.log('Current submitting state:', submitting);\n\n    try {\n      // Play submit sound\n      playSound('submit');\n\n      // Show loading immediately\n      setSubmitting(true);\n      console.log('✅ setSubmitting(true) called');\n      console.log('📝 Starting quiz marking process...');\n\n      let currentUser = user;\n      if (!currentUser || !currentUser._id) {\n        const storedUser = localStorage.getItem('user');\n        if (storedUser) {\n          try {\n            currentUser = JSON.parse(storedUser);\n          } catch (error) {\n            console.error('Error parsing stored user data:', error);\n            startTransition(() => {\n              navigate('/login');\n            });\n            return;\n          }\n        }\n      }\n\n      if (!currentUser || !currentUser._id) {\n        message.error('User session expired. Please login again.');\n        startTransition(() => {\n          navigate('/login');\n        });\n        return;\n      }\n\n      const endTime = new Date();\n      const timeTaken = Math.floor((endTime - startTime) / 1000);\n\n      let correctAnswers = 0;\n      const resultDetails = questions.map((question, index) => {\n        const userAnswer = answers[index];\n        let isCorrect = false;\n        let actualCorrectAnswer = '';\n\n        // Determine the correct answer based on question type\n        const questionType = question.type || question.answerType || 'mcq';\n\n        if (questionType.toLowerCase() === 'mcq' || questionType === 'Options') {\n          // For MCQ questions, check both correctAnswer and correctOption\n          if (question.options && typeof question.options === 'object') {\n            // If correctAnswer is a key (like \"B\"), get the actual text\n            if (question.correctAnswer && question.options[question.correctAnswer]) {\n              actualCorrectAnswer = question.options[question.correctAnswer];\n              isCorrect = userAnswer === actualCorrectAnswer;\n            }\n            // If correctOption is available, use it\n            else if (question.correctOption && question.options[question.correctOption]) {\n              actualCorrectAnswer = question.options[question.correctOption];\n              isCorrect = userAnswer === actualCorrectAnswer;\n            }\n            // If correctAnswer is already the full text\n            else if (question.correctAnswer) {\n              actualCorrectAnswer = question.correctAnswer;\n              isCorrect = userAnswer === actualCorrectAnswer;\n            }\n          } else {\n            // Fallback for other option formats\n            actualCorrectAnswer = question.correctAnswer || question.correctOption || '';\n            isCorrect = userAnswer === actualCorrectAnswer;\n          }\n        } else {\n          // For fill-in-the-blank and other types, direct comparison\n          actualCorrectAnswer = question.correctAnswer || '';\n          isCorrect = userAnswer?.toLowerCase().trim() === actualCorrectAnswer?.toLowerCase().trim();\n        }\n\n        if (isCorrect) correctAnswers++;\n\n        return {\n          questionId: question._id || `question_${index}`,\n          questionName: typeof question.name === 'string' ? question.name : `Question ${index + 1}`,\n          questionText: question.name || `Question ${index + 1}`,\n          userAnswer: typeof userAnswer === 'string' ? userAnswer : String(userAnswer || ''),\n          correctAnswer: actualCorrectAnswer,\n          isCorrect,\n          questionType: questionType,\n          options: question.options || null,\n          questionImage: question.image || question.questionImage || question.imageUrl || null,\n          image: question.image || question.questionImage || question.imageUrl || null\n        };\n      });\n\n      const percentage = Math.round((correctAnswers / questions.length) * 100);\n      // Use the exam's actual passing marks instead of hardcoded 60%\n      const passingPercentage = quiz.passingMarks || quiz.passingPercentage || 60;\n      const verdict = percentage >= passingPercentage ? 'Pass' : 'Fail';\n\n      const reportData = {\n        exam: id,\n        user: currentUser._id,\n        result: {\n          correctAnswers,\n          wrongAnswers: questions.length - correctAnswers,\n          percentage,\n          score: percentage,\n          verdict: verdict,\n          timeTaken,\n          timeSpent: timeTaken, // Add timeSpent for XP calculation\n          points: correctAnswers * 10,\n          totalQuestions: questions.length\n        }\n      };\n\n      try {\n        console.log('📤 Submitting quiz report:', reportData);\n        const response = await addReport(reportData);\n        console.log('📥 Server response:', response);\n\n        if (response.success) {\n          console.log('✅ Quiz submitted successfully, preparing results...');\n\n          // Include XP data in navigation state\n          const navigationState = {\n            percentage,\n            correctAnswers,\n            totalQuestions: questions.length,\n            timeTaken,\n            resultDetails,\n            xpData: response.xpData || null, // Include XP data from server response\n            quizName: quiz.name,\n            quizSubject: quiz.subject || quiz.category,\n            passingPercentage: passingPercentage, // Include actual passing marks\n            verdict: verdict // Include calculated verdict\n          };\n\n          // Brief delay to show loading screen\n          await new Promise(resolve => setTimeout(resolve, 1000));\n\n          console.log('🎯 Navigating to results page...');\n          startTransition(() => {\n            navigate(`/quiz/${id}/result`, {\n              state: navigationState\n            });\n          });\n        } else {\n          console.error('❌ Quiz submission failed:', response.message);\n          // Just log the error, don't show notification to user\n          setTimeout(() => {\n            setSubmitting(false);\n            // message.error(response.message || 'Failed to submit quiz'); // Removed notification\n          }, 1000);\n          return;\n        }\n      } catch (apiError) {\n        console.error('❌ API Error during submission:', apiError);\n        // Just log the error, don't show notification to user\n        setTimeout(() => {\n          setSubmitting(false);\n          // message.error('Network error while submitting quiz'); // Removed notification\n        }, 1000);\n        return;\n      }\n    } catch (error) {\n      console.error('Quiz submission error:', error);\n      // Just log the error, don't show notification to user\n      setTimeout(() => {\n        setSubmitting(false);\n        // message.error('Failed to submit quiz'); // Removed notification\n      }, 1000);\n      return;\n    } finally {\n      setSubmitting(false);\n    }\n  }, [startTime, questions, answers, id, navigate, user]);\n\n  // Timer countdown with auto-submit\n  useEffect(() => {\n    if (timeLeft <= 0) {\n      // Auto-submit when timer reaches 0\n      console.log('⏰ Time up! Auto-submitting quiz...');\n      handleSubmitQuiz();\n      return;\n    }\n\n    const timer = setInterval(() => {\n      setTimeLeft(prev => prev - 1);\n    }, 1000);\n\n    return () => clearInterval(timer);\n  }, [timeLeft, handleSubmitQuiz]);\n\n  // Handle answer selection\n  const handleAnswerSelect = (answer) => {\n    const newAnswers = [...answers];\n    newAnswers[currentQuestion] = answer;\n    setAnswers(newAnswers);\n  };\n\n  // Navigation functions\n  const goToNext = () => {\n    if (currentQuestion < questions.length - 1) {\n      playSound('navigate');\n      setCurrentQuestion(currentQuestion + 1);\n    }\n  };\n\n  const goToPrevious = () => {\n    if (currentQuestion > 0) {\n      playSound('navigate');\n      setCurrentQuestion(currentQuestion - 1);\n    }\n  };\n\n  // Duolingo-style time formatting (min:sec format)\n  const formatTime = (seconds) => {\n    const minutes = Math.floor(seconds / 60);\n    const secs = seconds % 60;\n\n    return `${minutes}:${secs.toString().padStart(2, '0')}`;\n  };\n\n  // Render different answer sections based on question type\n  const renderAnswerSection = () => {\n    const questionType = currentQ.type || currentQ.answerType || 'mcq';\n\n\n\n    switch (questionType.toLowerCase()) {\n      case 'mcq':\n      case 'multiple-choice':\n      case 'multiplechoice':\n        return renderMultipleChoice();\n\n      case 'fill':\n      case 'fill-in-the-blank':\n      case 'fillblank':\n      case 'text':\n        return renderFillInTheBlank();\n\n      case 'image':\n      case 'diagram':\n        return renderImageQuestion();\n\n      default:\n        // Default to multiple choice if type is unclear\n        return renderMultipleChoice();\n    }\n  };\n\n  // Render multiple choice options\n  const renderMultipleChoice = () => {\n    let options = [];\n\n    // Handle different option formats\n    if (Array.isArray(currentQ.options)) {\n      options = currentQ.options;\n    } else if (currentQ.options && typeof currentQ.options === 'object') {\n      // Handle object format like {A: \"option1\", B: \"option2\"}\n      options = Object.values(currentQ.options);\n    } else if (currentQ.option1 && currentQ.option2) {\n      // Handle individual option properties\n      options = [currentQ.option1, currentQ.option2, currentQ.option3, currentQ.option4].filter(Boolean);\n    }\n\n    if (!options || options.length === 0) {\n      // Show debug info and fallback options for testing\n      return (\n        <div className=\"space-y-4\">\n          <div className=\"text-center py-4 bg-yellow-50 border border-yellow-200 rounded-lg\">\n            <p className=\"text-yellow-800 font-medium\">No options found for this question</p>\n            <details className=\"mt-2\">\n              <summary className=\"text-sm text-yellow-600 cursor-pointer\">Show question data</summary>\n              <pre className=\"text-xs text-left mt-2 bg-yellow-100 p-2 rounded overflow-auto max-h-32\">\n                {JSON.stringify(currentQ, null, 2)}\n              </pre>\n            </details>\n          </div>\n\n          {/* Fallback test options */}\n          <div className=\"space-y-3\">\n            {['Option A (Test)', 'Option B (Test)', 'Option C (Test)', 'Option D (Test)'].map((option, index) => {\n              const optionLetter = String.fromCharCode(65 + index);\n              const isSelected = answers[currentQuestion] === option;\n\n              return (\n                <button\n                  key={index}\n                  onClick={() => handleAnswerSelect(option)}\n                  className={`w-full p-4 rounded-xl border-2 transition-all duration-200 text-left hover:shadow-md ${\n                    isSelected\n                      ? 'border-blue-500 bg-blue-50 text-blue-900 shadow-md'\n                      : 'border-gray-200 bg-white hover:border-blue-300 hover:bg-blue-50'\n                  }`}\n                >\n                  <div className=\"flex items-start gap-4\">\n                    <div className={`w-10 h-10 rounded-full flex items-center justify-center font-bold text-sm flex-shrink-0 ${\n                      isSelected\n                        ? 'bg-blue-500 text-white'\n                        : 'bg-gray-100 text-gray-600'\n                    }`}>\n                      {optionLetter}\n                    </div>\n                    <span className=\"text-lg leading-relaxed flex-1 text-left text-gray-900\">\n                      {option}\n                    </span>\n                  </div>\n                </button>\n              );\n            })}\n          </div>\n        </div>\n      );\n    }\n\n    return (\n      <div className=\"space-y-3\">\n        {options.map((option, index) => {\n          const optionLetter = String.fromCharCode(65 + index);\n          const isSelected = answers[currentQuestion] === option;\n\n          return (\n            <button\n              key={index}\n              onClick={() => handleAnswerSelect(option)}\n              className={`w-full p-4 rounded-xl border-2 transition-all duration-200 text-left hover:shadow-md ${\n                isSelected\n                  ? 'border-blue-500 bg-blue-50 text-blue-900 shadow-md'\n                  : 'border-gray-200 bg-white hover:border-blue-300 hover:bg-blue-50'\n              }`}\n            >\n              <div className=\"flex items-start gap-4\">\n                <div className={`w-10 h-10 rounded-full flex items-center justify-center font-bold text-sm flex-shrink-0 ${\n                  isSelected\n                    ? 'bg-blue-500 text-white'\n                    : 'bg-gray-100 text-gray-600'\n                }`}>\n                  {optionLetter}\n                </div>\n                <span className=\"text-lg leading-relaxed flex-1 text-left text-gray-900\">\n                  {typeof option === 'string' ? option : JSON.stringify(option)}\n                </span>\n              </div>\n            </button>\n          );\n        })}\n      </div>\n    );\n  };\n\n  // Render fill in the blank input\n  const renderFillInTheBlank = () => {\n    return (\n      <div className=\"space-y-4\">\n        <div className=\"bg-blue-50 border border-blue-200 rounded-lg p-4\">\n          <p className=\"text-blue-800 text-sm font-medium mb-2\">Fill in the blank:</p>\n          <p className=\"text-gray-700\">Type your answer in the box below</p>\n        </div>\n        <div className=\"relative\">\n          <input\n            type=\"text\"\n            value={answers[currentQuestion] || ''}\n            onChange={(e) => handleAnswerSelect(e.target.value)}\n            placeholder=\"Type your answer here...\"\n            className=\"w-full p-4 text-lg border-2 border-gray-300 rounded-xl focus:border-blue-500 focus:outline-none transition-colors\"\n            autoFocus\n          />\n        </div>\n      </div>\n    );\n  };\n\n  // Render image/diagram question (could have options or be fill-in)\n  const renderImageQuestion = () => {\n    if (currentQ.options && Array.isArray(currentQ.options) && currentQ.options.length > 0) {\n      return renderMultipleChoice();\n    } else {\n      return renderFillInTheBlank();\n    }\n  };\n\n  if (loading) {\n    return (\n      <div className=\"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center\">\n        <div className=\"text-center\">\n          <div className=\"animate-spin rounded-full h-16 w-16 border-b-2 border-blue-600 mx-auto mb-4\"></div>\n          <p className=\"text-gray-600 font-medium\">Loading quiz...</p>\n        </div>\n      </div>\n    );\n  }\n\n  if (!quiz || !questions.length) {\n    return (\n      <div className=\"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center\">\n        <div className=\"bg-white rounded-2xl shadow-xl border border-gray-200 p-8 max-w-md mx-4\">\n          <div className=\"text-center\">\n            <h2 className=\"text-xl font-bold text-gray-900 mb-4\">No Questions Available</h2>\n            <p className=\"text-gray-600 mb-6\">This quiz doesn't have any questions yet.</p>\n            <button\n              onClick={() => startTransition(() => navigate('/quiz'))}\n              className=\"mt-4 px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\"\n            >\n              Back to Quizzes\n            </button>\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  // Safety check for current question\n  if (!questions[currentQuestion]) {\n    return (\n      <div className=\"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center\">\n        <div className=\"bg-white rounded-2xl shadow-xl border border-gray-200 p-8 max-w-md mx-4\">\n          <div className=\"text-center\">\n            <h2 className=\"text-xl font-bold text-gray-900 mb-4\">Question Not Found</h2>\n            <p className=\"text-gray-600 mb-6\">Unable to load the current question.</p>\n            <button\n              onClick={() => startTransition(() => navigate('/quiz'))}\n              className=\"mt-4 px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\"\n            >\n              Back to Quizzes\n            </button>\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  const currentQ = questions[currentQuestion];\n  const isLastQuestion = currentQuestion === questions.length - 1;\n\n  // Ensure currentQ is a valid object\n  if (!currentQ || typeof currentQ !== 'object') {\n    return (\n      <div className=\"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center\">\n        <div className=\"bg-white rounded-2xl shadow-xl border border-gray-200 p-8 max-w-md mx-4\">\n          <div className=\"text-center\">\n            <h2 className=\"text-xl font-bold text-gray-900 mb-4\">Invalid Question Data</h2>\n            <p className=\"text-gray-600 mb-6\">The question data is corrupted or invalid.</p>\n            <button\n              onClick={() => startTransition(() => navigate('/quiz'))}\n              className=\"mt-4 px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\"\n            >\n              Back to Quizzes\n            </button>\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n\n\n  // Show enhanced loading screen when submitting\n  if (submitting) {\n    return (\n      <>\n        <style>{`\n          @keyframes professionalSpin {\n            0% { transform: rotate(0deg) scale(1); }\n            50% { transform: rotate(180deg) scale(1.1); }\n            100% { transform: rotate(360deg) scale(1); }\n          }\n          @keyframes elegantPulse {\n            0%, 100% { opacity: 1; transform: scale(1); }\n            50% { opacity: 0.7; transform: scale(1.05); }\n          }\n          @keyframes smoothBounce {\n            0%, 20%, 50%, 80%, 100% { transform: translateY(0) scale(1); }\n            40% { transform: translateY(-8px) scale(1.2); }\n            60% { transform: translateY(-4px) scale(1.1); }\n          }\n          @keyframes gradientShift {\n            0% { background-position: 0% 50%; }\n            50% { background-position: 100% 50%; }\n            100% { background-position: 0% 50%; }\n          }\n          @keyframes fadeInUp {\n            0% { opacity: 0; transform: translateY(20px); }\n            100% { opacity: 1; transform: translateY(0); }\n          }\n          @keyframes orbitalSpin {\n            0% { transform: rotate(0deg); }\n            100% { transform: rotate(360deg); }\n          }\n          .professional-dot {\n            animation: smoothBounce 1.6s infinite ease-in-out both;\n          }\n          .professional-dot:nth-child(1) { animation-delay: -0.32s; }\n          .professional-dot:nth-child(2) { animation-delay: -0.16s; }\n          .professional-dot:nth-child(3) { animation-delay: 0s; }\n          .professional-dot:nth-child(4) { animation-delay: 0.16s; }\n          .gradient-bg {\n            background: linear-gradient(-45deg, #667eea, #764ba2, #f093fb, #f5576c);\n            background-size: 400% 400%;\n            animation: gradientShift 4s ease infinite;\n          }\n        `}</style>\n        <div className=\"gradient-bg\" style={{\n          position: 'fixed',\n          top: 0,\n          left: 0,\n          width: '100%',\n          height: '100%',\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'center',\n          zIndex: 9999\n        }}>\n          <div style={{\n            background: 'rgba(255, 255, 255, 0.98)',\n            borderRadius: '24px',\n            padding: window.innerWidth <= 768 ? '32px 24px' : '48px 40px',\n            textAlign: 'center',\n            boxShadow: '0 32px 64px rgba(0, 0, 0, 0.2), 0 16px 32px rgba(0, 0, 0, 0.1)',\n            backdropFilter: 'blur(20px)',\n            border: '1px solid rgba(255, 255, 255, 0.3)',\n            maxWidth: window.innerWidth <= 768 ? '320px' : '450px',\n            width: '90%',\n            animation: 'fadeInUp 0.6s ease-out'\n          }}>\n            {/* Professional Animated Icon */}\n            <div style={{\n              width: window.innerWidth <= 768 ? '100px' : '120px',\n              height: window.innerWidth <= 768 ? '100px' : '120px',\n              margin: '0 auto 32px auto',\n              background: 'linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%)',\n              borderRadius: '50%',\n              display: 'flex',\n              alignItems: 'center',\n              justifyContent: 'center',\n              animation: 'professionalSpin 3s ease-in-out infinite',\n              boxShadow: '0 16px 40px rgba(102, 126, 234, 0.4), 0 8px 16px rgba(118, 75, 162, 0.3)',\n              position: 'relative'\n            }}>\n              <div style={{\n                width: window.innerWidth <= 768 ? '50px' : '60px',\n                height: window.innerWidth <= 768 ? '50px' : '60px',\n                background: 'white',\n                borderRadius: '50%',\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'center',\n                boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1)'\n              }}>\n                <span style={{\n                  fontSize: window.innerWidth <= 768 ? '24px' : '28px',\n                  filter: 'drop-shadow(0 2px 4px rgba(0,0,0,0.1))'\n                }}>🎯</span>\n              </div>\n              {/* Orbital rings */}\n              <div style={{\n                position: 'absolute',\n                width: '140%',\n                height: '140%',\n                border: '2px solid rgba(255, 255, 255, 0.3)',\n                borderRadius: '50%',\n                animation: 'orbitalSpin 4s linear infinite reverse'\n              }}></div>\n            </div>\n\n            {/* Enhanced Main Message */}\n            <h2 style={{\n              fontSize: window.innerWidth <= 768 ? '24px' : '32px',\n              fontWeight: '700',\n              background: 'linear-gradient(135deg, #667eea, #764ba2)',\n              WebkitBackgroundClip: 'text',\n              WebkitTextFillColor: 'transparent',\n              backgroundClip: 'text',\n              margin: '0 0 12px 0',\n              animation: 'elegantPulse 2.5s infinite',\n              letterSpacing: '-0.5px'\n            }}>Evaluating Your Quiz</h2>\n\n            {/* Professional Sub Message */}\n            <p style={{\n              fontSize: window.innerWidth <= 768 ? '14px' : '16px',\n              color: '#64748b',\n              margin: '0 0 32px 0',\n              lineHeight: '1.6',\n              fontWeight: '500'\n            }}>Our advanced system is carefully reviewing your answers</p>\n\n            {/* Enhanced Progress Indicator */}\n            <div style={{\n              display: 'flex',\n              justifyContent: 'center',\n              alignItems: 'center',\n              gap: '6px',\n              marginBottom: '16px'\n            }}>\n              {[1, 2, 3, 4].map(i => (\n                <div\n                  key={i}\n                  className=\"professional-dot\"\n                  style={{\n                    width: '10px',\n                    height: '10px',\n                    background: 'linear-gradient(135deg, #667eea, #764ba2)',\n                    borderRadius: '50%',\n                    boxShadow: '0 2px 8px rgba(102, 126, 234, 0.4)'\n                  }}\n                />\n              ))}\n            </div>\n\n            {/* Progress Text */}\n            <div style={{\n              fontSize: window.innerWidth <= 768 ? '12px' : '14px',\n              color: '#94a3b8',\n              fontWeight: '500',\n              textTransform: 'uppercase',\n              letterSpacing: '1px'\n            }}>\n              Processing...\n            </div>\n          </div>\n        </div>\n      </>\n    );\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 relative p-2 sm:p-4 lg:p-6\">\n      {/* Responsive Header */}\n      <div className=\"bg-white shadow-sm border-b border-gray-200 rounded-lg mb-3 sm:mb-6\">\n        <div className=\"max-w-7xl mx-auto px-3 sm:px-4 lg:px-6 py-3 sm:py-4\">\n          {/* Responsive Header Layout */}\n          <div className=\"flex flex-col sm:flex-row sm:justify-between sm:items-center gap-3 sm:gap-4\">\n            {/* Quiz Title */}\n            <h1 className=\"text-lg sm:text-xl lg:text-2xl font-bold text-gray-900 text-center sm:text-left truncate\">\n              {quiz.name}\n            </h1>\n\n            {/* Timer - Responsive */}\n            <div className=\"flex justify-center\">\n              <div\n                className=\"flex items-center gap-2 sm:gap-3 rounded-lg sm:rounded-xl shadow-lg border-2 px-3 sm:px-4 py-2 sm:py-3\"\n                style={{\n                  textShadow: '1px 1px 2px rgba(0,0,0,0.5)',\n                  background: timeLeft <= 60\n                    ? 'linear-gradient(to right, #ef4444, #dc2626)'\n                    : 'linear-gradient(to right, #22c55e, #16a34a)',\n                  borderColor: timeLeft <= 60 ? '#fca5a5' : '#86efac',\n                  color: 'white',\n                  boxShadow: timeLeft <= 60\n                    ? '0 0 20px rgba(239, 68, 68, 0.6), 0 4px 20px rgba(0,0,0,0.3)'\n                    : '0 0 15px rgba(34, 197, 94, 0.4), 0 4px 20px rgba(0,0,0,0.3)',\n                  animation: timeLeft <= 60 ? 'pulse 1s infinite' : 'none'\n                }}>\n                <TbClock\n                  className=\"w-4 h-4 sm:w-5 sm:h-5\"\n                  style={{\n                    filter: 'drop-shadow(0 2px 4px rgba(0,0,0,0.3))',\n                    animation: timeLeft <= 60 ? 'bounce 1s infinite' : 'none'\n                  }}\n                />\n\n                <span\n                  className=\"text-sm sm:text-base lg:text-lg font-mono font-black\"\n                  style={{\n                    textShadow: '2px 2px 4px rgba(0,0,0,0.7)',\n                    animation: timeLeft <= 60 ? 'pulse 1s infinite' : 'none'\n                  }}>\n                  {formatTime(timeLeft)}\n                </span>\n              </div>\n            </div>\n\n            {/* Progress */}\n            <p className=\"text-sm sm:text-base text-gray-600 font-medium text-center sm:text-right\">\n              {currentQuestion + 1} of {questions.length}\n            </p>\n          </div>\n\n          {/* Responsive Progress Bar */}\n          <div className=\"mb-3 sm:mb-4\">\n            <div className=\"flex justify-between items-center mb-2\">\n              <span className=\"text-xs sm:text-sm text-gray-600 font-medium\">\n                Progress\n              </span>\n              <span className=\"text-xs sm:text-sm text-blue-600 font-bold\">\n                {Math.round(((currentQuestion + 1) / questions.length) * 100)}%\n              </span>\n            </div>\n            <div\n              className=\"w-full rounded-full overflow-hidden shadow-inner\"\n              style={{\n                height: window.innerWidth <= 768 ? '8px' : '12px',\n                backgroundColor: '#e5e7eb'\n              }}\n            >\n              <div\n                style={{\n                  width: `${((currentQuestion + 1) / questions.length) * 100}%`,\n                  height: '100%',\n                  background: 'linear-gradient(to right, #2563eb, #1d4ed8)',\n                  borderRadius: '9999px',\n                  transition: 'width 0.5s ease-out',\n                  boxShadow: '0 2px 4px rgba(37, 99, 235, 0.4)'\n                }}\n              ></div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Responsive Main Content */}\n      <div className=\"max-w-7xl mx-auto px-2 sm:px-4 lg:px-6\">\n        <div className=\"bg-white rounded-lg sm:rounded-2xl shadow-lg sm:shadow-xl border border-gray-200 transition-all duration-300 p-4 sm:p-6 lg:p-8\">\n          {/* Responsive Question */}\n          <div className=\"mb-6 sm:mb-8\">\n            <h2 className=\"text-lg sm:text-xl lg:text-2xl xl:text-3xl font-bold text-gray-900 text-center mb-4 sm:mb-6 leading-tight\">\n              {typeof currentQ.name === 'string' ? currentQ.name : 'Question'}\n            </h2>\n\n            {currentQ.image && (\n              <div className=\"bg-gray-50 rounded-lg p-3 sm:p-4 mb-4 sm:mb-6\">\n                <img\n                  src={currentQ.image}\n                  alt=\"Question diagram\"\n                  className=\"max-w-full h-auto rounded-lg shadow-lg mx-auto block max-h-48 sm:max-h-64 lg:max-h-80\"\n                  onError={(e) => {\n                    e.target.style.display = 'none';\n                    // Show fallback message\n                    const fallback = document.createElement('div');\n                    fallback.className = 'text-center py-8 text-gray-500';\n                    fallback.innerHTML = '<p>Could not load diagram</p>';\n                    e.target.parentNode.appendChild(fallback);\n                  }}\n                />\n              </div>\n            )}\n          </div>\n\n          {/* Answer Section - Different types based on question type */}\n          <div\n            className=\"space-y-4\"\n            style={{\n              marginBottom: window.innerWidth <= 768 ? '16px' : '32px'\n            }}\n          >\n            {renderAnswerSection()}\n          </div>\n\n          {/* Navigation */}\n          <div\n            className=\"flex items-center\"\n            style={{\n              flexDirection: window.innerWidth <= 768 ? 'column' : 'row',\n              justifyContent: window.innerWidth <= 768 ? 'center' : 'space-between',\n              gap: window.innerWidth <= 768 ? '12px' : '0'\n            }}\n          >\n            <button\n              onClick={goToPrevious}\n              disabled={currentQuestion === 0}\n              className={`flex items-center gap-2 rounded-lg font-semibold transition-colors ${\n                currentQuestion === 0\n                  ? 'bg-gray-100 text-gray-400 cursor-not-allowed'\n                  : 'bg-gray-200 text-gray-700 hover:bg-gray-300'\n              }`}\n              style={{\n                padding: window.innerWidth <= 768 ? '10px 16px' : '12px 24px',\n                fontSize: window.innerWidth <= 768 ? '14px' : '16px',\n                width: window.innerWidth <= 768 ? '100%' : 'auto',\n                justifyContent: 'center'\n              }}\n            >\n              <TbArrowLeft\n                style={{\n                  width: window.innerWidth <= 768 ? '16px' : '20px',\n                  height: window.innerWidth <= 768 ? '16px' : '20px'\n                }}\n              />\n              Previous\n            </button>\n\n            {isLastQuestion ? (\n              <button\n                onClick={handleSubmitQuiz}\n                disabled={submitting}\n                className={`flex items-center gap-2 rounded-lg font-semibold transition-colors ${\n                  submitting\n                    ? 'bg-gray-400 text-gray-200 cursor-not-allowed'\n                    : 'bg-green-600 text-white hover:bg-green-700'\n                }`}\n                style={{\n                  padding: window.innerWidth <= 768 ? '10px 16px' : '12px 32px',\n                  fontSize: window.innerWidth <= 768 ? '14px' : '16px',\n                  width: window.innerWidth <= 768 ? '100%' : 'auto',\n                  justifyContent: 'center'\n                }}\n              >\n                {submitting ? (\n                  <>\n                    <div\n                      className=\"animate-spin rounded-full border-2 border-white border-t-transparent\"\n                      style={{\n                        width: window.innerWidth <= 768 ? '16px' : '20px',\n                        height: window.innerWidth <= 768 ? '16px' : '20px'\n                      }}\n                    ></div>\n                    Submitting...\n                  </>\n                ) : (\n                  <>\n                    <TbCheck\n                      style={{\n                        width: window.innerWidth <= 768 ? '16px' : '20px',\n                        height: window.innerWidth <= 768 ? '16px' : '20px'\n                      }}\n                    />\n                    Submit Quiz\n                  </>\n                )}\n              </button>\n            ) : (\n              <button\n                onClick={goToNext}\n                className=\"flex items-center gap-2 bg-blue-600 text-white rounded-lg font-semibold hover:bg-blue-700 transition-colors\"\n                style={{\n                  padding: window.innerWidth <= 768 ? '10px 16px' : '12px 24px',\n                  fontSize: window.innerWidth <= 768 ? '14px' : '16px',\n                  width: window.innerWidth <= 768 ? '100%' : 'auto',\n                  justifyContent: 'center'\n                }}\n              >\n                Next\n                <TbArrowRight\n                  style={{\n                    width: window.innerWidth <= 768 ? '16px' : '20px',\n                    height: window.innerWidth <= 768 ? '16px' : '20px'\n                  }}\n                />\n              </button>\n            )}\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default QuizPlay;\n"], "names": ["default", "axiosInstance", "require", "addReport", "async", "post", "payload", "data", "error", "response", "getAllReports", "filters", "getAllReportsByUser", "getAllReportsForRanking", "get", "getXPLeaderboard", "options", "arguments", "length", "undefined", "params", "URLSearchParams", "limit", "append", "classFilter", "levelFilter", "seasonFilter", "includeInactive", "concat", "toString", "getUserRanking", "userId", "context", "playSound", "type", "audioContext", "window", "AudioContext", "webkitAudioContext", "createTone", "frequency", "duration", "oscillator", "createOscillator", "gainNode", "createGain", "connect", "destination", "setValueAtTime", "currentTime", "gain", "linearRampToValueAtTime", "exponentialRampToValueAtTime", "start", "stop", "setTimeout", "console", "log", "QuizPlay", "id", "useParams", "navigate", "useNavigate", "user", "useSelector", "state", "loading", "setLoading", "useState", "submitting", "setSubmitting", "quiz", "setQuiz", "questions", "setQuestions", "currentQuestion", "setCurrentQuestion", "answers", "setAnswers", "timeLeft", "setTimeLeft", "startTime", "setStartTime", "useEffect", "_id", "localStorage", "getItem", "message", "startTransition", "getExamById", "examId", "success", "Array", "fill", "Date", "loadQuizData", "handleSubmitQuiz", "useCallback", "currentUser", "storedUser", "JSON", "parse", "endTime", "timeTaken", "Math", "floor", "correctAnswers", "resultDetails", "map", "question", "index", "userAnswer", "isCorrect", "actualCorrectAnswer", "questionType", "answerType", "_actualCorrectAnswer", "toLowerCase", "<PERSON><PERSON><PERSON><PERSON>", "correctOption", "trim", "questionId", "questionName", "name", "questionText", "String", "questionImage", "image", "imageUrl", "percentage", "round", "passingPercentage", "passingMarks", "verdict", "reportData", "exam", "result", "wrongAnswers", "score", "timeSpent", "points", "totalQuestions", "navigationState", "xpData", "quizName", "quizSubject", "subject", "category", "Promise", "resolve", "apiError", "timer", "setInterval", "prev", "clearInterval", "handleAnswerSelect", "answer", "newAnswers", "renderMultipleChoice", "isArray", "currentQ", "Object", "values", "option1", "option2", "option3", "option4", "filter", "Boolean", "_jsx", "className", "children", "option", "optionLetter", "fromCharCode", "isSelected", "onClick", "_jsxs", "stringify", "renderFillInTheBlank", "value", "onChange", "e", "target", "placeholder", "autoFocus", "renderImageQuestion", "isLastQuestion", "_Fragment", "style", "position", "top", "left", "width", "height", "display", "alignItems", "justifyContent", "zIndex", "background", "borderRadius", "padding", "innerWidth", "textAlign", "boxShadow", "<PERSON><PERSON>ilter", "border", "max<PERSON><PERSON><PERSON>", "animation", "margin", "fontSize", "fontWeight", "WebkitBackgroundClip", "WebkitTextFillColor", "backgroundClip", "letterSpacing", "color", "lineHeight", "gap", "marginBottom", "i", "textTransform", "textShadow", "borderColor", "TbClock", "seconds", "minutes", "secs", "padStart", "formatTime", "backgroundColor", "transition", "src", "alt", "onError", "fallback", "document", "createElement", "innerHTML", "parentNode", "append<PERSON><PERSON><PERSON>", "renderAnswerSection", "flexDirection", "goToPrevious", "disabled", "TbArrowLeft", "TbCheck", "goToNext", "TbArrowRight"], "sourceRoot": ""}