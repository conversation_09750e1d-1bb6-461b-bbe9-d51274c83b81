{"version": 3, "file": "static/js/100.b9b250dc.chunk.js", "mappings": "+OAAA,MAAQA,QAASC,GAAkBC,EAAQ,MAG9BC,EAAmBC,UAC5B,IAEI,aADuBH,EAAcI,KAAK,+BAAiCC,EAE/E,CAAE,MAAOC,GACL,OAAOA,EAAMC,QACjB,GAcSC,EAAeL,UACxB,IAEI,aADuBH,EAAcS,IAAI,sCACzBC,IACpB,CAAE,MAAOJ,GAAQ,IAADK,EACZ,OAAqB,QAAdA,EAAAL,EAAMC,gBAAQ,IAAAI,OAAA,EAAdA,EAAgBD,OAAQ,CAAEE,SAAS,EAAOC,QAAS,yBAC9D,GAMSC,EAAWX,eAAOY,GAAwC,IAA7BC,EAAgBC,UAAAC,OAAA,QAAAC,IAAAF,UAAA,GAAAA,UAAA,GAAG,KACzD,IACI,MACMG,EADaL,aAAqBM,SACZ,CACxBC,QAAS,CACL,eAAgB,uBAEpBC,QAAS,IACTP,iBAAkBA,EAAoBQ,IAClC,MAAMC,EAAmBC,KAAKC,MAA8B,IAAvBH,EAAcI,OAAgBJ,EAAcK,OAEjFb,EAAiBS,EAAkBD,EAAcI,OAAQJ,EAAcK,MAAM,OAC7EV,GACJ,CACAI,QAAS,KAIb,aADuBvB,EAAcI,KAAK,uBAAwBW,EAAWK,EAEjF,CAAE,MAAOd,GACL,OAAOA,EAAMC,QACjB,CACJ,EAGauB,EAAU3B,UACnB,IAMI,aALuBH,EAAcI,KAAK,sBAAuB2B,EAAU,CACvET,QAAS,CACL,eAAgB,wBAI5B,CAAE,MAAOhB,GACL,OAAOA,EAAMC,QACjB,GAISyB,EAAe7B,UACxB,IAMI,aALuBH,EAAcI,KAAK,4BAA6B2B,EAAU,CAC7ET,QAAS,CACL,eAAgB,wBAI5B,CAAE,MAAOhB,GACL,OAAOA,EAAMC,QACjB,GAIS0B,EAAU9B,UACnB,IAMI,aALuBH,EAAcI,KAAK,sBAAuB2B,EAAU,CACvET,QAAS,CACL,eAAgB,wBAI5B,CAAE,MAAOhB,GACL,OAAOA,EAAMC,QACjB,GAMS2B,EAAc/B,MAAOgC,EAAIpB,KAClC,IACI,IAAIK,EAAS,CACTE,QAAS,CACL,eAAgB,qBAKpBP,aAAqBM,WACrBD,EAAOE,QAAQ,gBAAkB,uBAIrC,aADuBtB,EAAcoC,IAAI,2BAADC,OAA4BF,GAAMpB,EAAWK,EAEzF,CAAE,MAAOd,GACL,OAAOA,EAAMC,QACjB,GAIS+B,EAAanC,MAAOgC,EAAIJ,KACjC,IAMI,aALuB/B,EAAcoC,IAAI,0BAADC,OAA2BF,GAAMJ,EAAU,CAC/ET,QAAS,CACL,eAAgB,wBAI5B,CAAE,MAAOhB,GACL,OAAOA,EAAMC,QACjB,GAISgC,EAAkBpC,MAAOgC,EAAIJ,KACtC,IAMI,aALuB/B,EAAcoC,IAAI,gCAADC,OAAiCF,GAAMJ,EAAU,CACrFT,QAAS,CACL,eAAgB,wBAI5B,CAAE,MAAOhB,GACL,OAAOA,EAAMC,QACjB,GAISiC,EAAarC,MAAOgC,EAAIJ,KACjC,IAMI,aALuB/B,EAAcoC,IAAI,0BAADC,OAA2BF,GAAMJ,EAAU,CAC/ET,QAAS,CACL,eAAgB,wBAI5B,CAAE,MAAOhB,GACL,OAAOA,EAAMC,QACjB,GAMSkC,EAActC,UACvB,IAEI,aADuBH,EAAc0C,OAAO,2BAADL,OAA4BF,GAE3E,CAAE,MAAO7B,GACL,OAAOA,EAAMC,QACjB,GAISoC,EAAaxC,UACtB,IAEI,aADuBH,EAAc0C,OAAO,0BAADL,OAA2BF,GAE1E,CAAE,MAAO7B,GACL,OAAOA,EAAMC,QACjB,GAISqC,EAAkBzC,UAC3B,IAEI,aADuBH,EAAc0C,OAAO,gCAADL,OAAiCF,GAEhF,CAAE,MAAO7B,GACL,OAAOA,EAAMC,QACjB,GAISsC,EAAa1C,UACtB,IAEI,aADuBH,EAAc0C,OAAO,0BAADL,OAA2BF,GAE1E,CAAE,MAAO7B,GACL,OAAOA,EAAMC,QACjB,GAISuC,EAAuB3C,iBAAyB,IAAlBE,EAAOY,UAAAC,OAAA,QAAAC,IAAAF,UAAA,GAAAA,UAAA,GAAG,CAAC,EAClD,IACI,MAAM8B,EAAS,IAAIC,gBACf3C,EAAQ4C,cAAcF,EAAOG,OAAO,eAAgB7C,EAAQ4C,cAC5D5C,EAAQ8C,OAAOJ,EAAOG,OAAO,QAAS7C,EAAQ8C,OAC9C9C,EAAQ+C,WAAWL,EAAOG,OAAO,YAAa7C,EAAQ+C,WACtD/C,EAAQgD,SAASN,EAAOG,OAAO,UAAW7C,EAAQgD,SAGtD,aADuBrD,EAAcS,IAAI,kCAAD4B,OAAmCU,EAAOO,YAEtF,CAAE,MAAOhD,GACL,OAAOA,EAAMC,QACjB,CACJ,C,wDC9MA,QAdA,SAAkBgD,GAAa,IAAZ,MAAEC,GAAOD,EAC1B,MAAOE,EAAUC,IAAeC,EAAAA,EAAAA,WAAS,GAMzC,OALAC,EAAAA,EAAAA,YAAU,KACJC,OAAOC,WAAa,KACtBJ,GAAY,EACd,GACC,KAEDK,EAAAA,EAAAA,KAAA,OAAKX,UAAU,OAAMY,UACnBD,EAAAA,EAAAA,KAAA,MAAIX,UAAWK,EAAW,UAAY,GAAGO,SAAER,KAGjD,C,qDCdO,MAAMS,EAAkB,CAC7B,cACA,yBACA,YACA,YACA,gBACA,UACA,WACA,aACA,gBACA,yBACA,kBACA,SACA,wBAGWC,EAAoB,CAC/B,UACA,cACA,UACA,YACA,YACA,SACA,UACA,WACA,mBACA,YACA,WACA,YACA,uBACA,4BACA,cACA,eAGWC,EAAkB,CAC7B,UACA,cACA,UACA,YACA,YACA,SACA,UACA,WACA,mBACA,YACA,WACA,YACA,uBACA,4BACA,cACA,c,2NCnCF,MAAM,OAAEC,GAAWC,EAAAA,QAqvBnB,QAnvBA,SAA6Bd,GAAyC,IAAxC,aAAEN,EAAY,UAAEqB,EAAS,SAAEC,GAAUhB,EACjE,MAAOiB,GAAQC,EAAAA,EAAKC,UACdC,GAAWC,EAAAA,EAAAA,OACVC,EAASC,IAAcnB,EAAAA,EAAAA,WAAS,IAChCoB,EAAUC,IAAerB,EAAAA,EAAAA,UAAS,KAClCsB,EAAeC,IAAoBvB,EAAAA,EAAAA,UAAS,KAC5CwB,EAAeC,IAAoBzB,EAAAA,EAAAA,UAAS,KAC5C0B,EAAcC,IAAmB3B,EAAAA,EAAAA,UAAS,YAC1C4B,EAAgBC,IAAqB7B,EAAAA,EAAAA,UAAS,IAC9C8B,EAAcC,IAAmB/B,EAAAA,EAAAA,UAAS,KAC1CgC,EAAaC,IAAkBjC,EAAAA,EAAAA,UAAS,IACxCkC,EAAeC,IAAoBnC,EAAAA,EAAAA,UAAS,IAC5CoC,EAAiBC,IAAsBrC,EAAAA,EAAAA,UAAS,OAChDsC,EAAYC,IAAiBvC,EAAAA,EAAAA,WAAS,IA8BtCwC,EAAmBC,IAAwBzC,EAAAA,EAAAA,UAASM,EAAAA,KACpDoC,EAAkBC,IAAuB3C,EAAAA,EAAAA,UAAS,CAAC,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,OACjF4C,EAA2BC,IAAgC7C,EAAAA,EAAAA,UAAS,IAgNrE8C,EAAc,CAClBC,aAAcA,KAAM,EACpBC,SAAU,EACVC,OAAyB,WAAjB3D,OAA4B9B,EAAY,8BAS5C0F,EAAuB,CAC3BH,aAAcA,KAAM,EACpBC,SAAU,EACVC,OAAQ,WAkBJE,EAAmBA,KACvB,OAAQ7D,GACN,IAAK,SACH,MAAO,QACT,IAAK,cACH,MAAO,aACT,IAAK,cACH,MAAO,aACT,IAAK,QACH,MAAO,OACT,QACE,MAAO,WACX,EAGF,OACEc,EAAAA,EAAAA,KAAA,OAAKX,UAAU,oBAAmBY,UAChC+C,EAAAA,EAAAA,MAAA,OAAK3D,UAAU,YAAWY,SAAA,EACxB+C,EAAAA,EAAAA,MAAA,OAAK3D,UAAU,mBAAkBY,SAAA,CAjCfgD,MACtB,OAAQ/D,GACN,IAAK,SACH,OAAOc,EAAAA,EAAAA,KAACkD,EAAAA,IAAO,IACjB,IAAK,cAML,QACE,OAAOlD,EAAAA,EAAAA,KAACmD,EAAAA,IAAS,IALnB,IAAK,cACH,OAAOnD,EAAAA,EAAAA,KAACoD,EAAAA,IAAe,IACzB,IAAK,QACH,OAAOpD,EAAAA,EAAAA,KAACqD,EAAAA,IAAM,IAGlB,EAsBOJ,IACDD,EAAAA,EAAAA,MAAA,MAAA/C,SAAA,CAAI,WAAS8C,WAGfC,EAAAA,EAAAA,MAACtC,EAAAA,EAAI,CACHD,KAAMA,EACN6C,OAAO,WACPC,SAxPanH,UACnB,IAAIoH,EACJ,IAiBE,IAAIhH,EAEJ,GAlBAuE,GAAW,GACXU,EAAkB,GAClBE,EAAgB,IAChBf,GAAS6C,EAAAA,EAAAA,OAGTD,EAAYE,YAAW,KACE,MAAnBlC,IACFT,GAAW,GACXU,EAAkB,GAClBE,EAAgB,IAChBf,GAAS+C,EAAAA,EAAAA,OACT7G,EAAAA,GAAQD,QAAQ,8EAClB,GACC,KAIkB,WAAjBqC,EAA2B,CAE7B,MAAM0E,GAAWC,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACZC,GAAM,IACTC,kBAAmBvB,IAIrB,GAAqB,YAAjBlB,EAEFK,EAAgB,2BAChBnF,QAAiBO,EAAAA,EAAAA,IAAS6G,QACrB,GAAqB,UAAjBtC,EAET,GAAIJ,EAAc/D,OAAS,GAAK+D,EAAc,GAAG8C,cAAe,CAE9D,MAAMhG,EAAW,IAAIV,SAGrB2G,OAAOC,KAAKN,GAAaO,SAAQC,SACNhH,IAArBwG,EAAYQ,IAA2C,OAArBR,EAAYQ,KAC5CC,MAAMC,QAAQV,EAAYQ,IAE5BR,EAAYQ,GAAKD,SAAQI,GAAQvG,EAASmB,OAAOiF,EAAKG,KAEtDvG,EAASmB,OAAOiF,EAAKR,EAAYQ,IAErC,IAIFpG,EAASmB,OAAO,YAAa+B,EAAc,GAAG8C,eAC9CrC,EAAgB,2CAChBnF,QAAiBO,EAAAA,EAAAA,IAASiB,EAC5B,MAEE2D,EAAgB,sBAChBnF,QAAiBO,EAAAA,EAAAA,IAAS6G,OAEvB,CAEL,MAAM5F,EAAW,IAAIV,SAGrB2G,OAAOC,KAAKN,GAAaO,SAAQC,SACNhH,IAArBwG,EAAYQ,IAA2C,OAArBR,EAAYQ,KAC5CC,MAAMC,QAAQV,EAAYQ,IAE5BR,EAAYQ,GAAKD,SAAQI,GAAQvG,EAASmB,OAAOiF,EAAKG,KAEtDvG,EAASmB,OAAOiF,EAAKR,EAAYQ,IAErC,IAIEhD,EAAcjE,OAAS,GAAKiE,EAAc,GAAG4C,gBAC/ChG,EAASmB,OAAO,QAASiC,EAAc,GAAG4C,eAC1CrC,EAAgB,4BAIdT,EAAc/D,OAAS,GAAK+D,EAAc,GAAG8C,gBAC/CQ,QAAQC,IAAI,2CAAkCvD,EAAc,GAAGwD,MAC/D1G,EAASmB,OAAO,YAAa+B,EAAc,GAAG8C,gBAIhD/B,EAAmB0C,KAAKC,OACxBpI,QAAiBO,EAAAA,EAAAA,IAASiB,GAAU,CAAC6G,EAAUhH,EAAQC,KAIrD,GAHA2D,EAAkBoD,GAGd7C,EAAiB,CACnB,MACM8C,EAAgBjH,GAAWC,EAAQ+G,EAAW,IAC9CE,EAAQD,IAFOH,KAAKC,MAAQ5C,GAAmB,KAI/CgD,GADiBlH,EAAQgH,GACWC,EAE1ClD,EAAekD,GACfhD,EAAiBiD,EACnB,CAEiB,MAAbH,EACFlD,EAAgB,wBACPkD,EAAW,GACpBlD,EAAgB,gBAADrD,OAAiBuG,EAAQ,KAC1C,GAEJ,CACF,KAAO,CAEL,MAAM7G,EAAW,IAAIV,SAmBrB,OAhBA2G,OAAOC,KAAKJ,GAAQK,SAAQC,SACNhH,IAAhB0G,EAAOM,IAAsC,OAAhBN,EAAOM,IACtCpG,EAASmB,OAAOiF,EAAKN,EAAOM,GAC9B,IAIEpD,EAAS7D,OAAS,GAAK6D,EAAS,GAAGgD,eACrChG,EAASmB,OAAO,WAAY6B,EAAS,GAAGgD,eAGrB,UAAjB9E,GAA4BgC,EAAc/D,OAAS,GAAK+D,EAAc,GAAG8C,eAC3EhG,EAASmB,OAAO,YAAa+B,EAAc,GAAG8C,eAIxC9E,GACN,IAAK,cACH1C,QAAiBuB,EAAAA,EAAAA,IAAQC,GACzB,MACF,IAAK,cACHxB,QAAiByB,EAAAA,EAAAA,IAAaD,GAC9B,MACF,IAAK,QACHxB,QAAiB0B,EAAAA,EAAAA,IAAQF,GACzB,MACF,QACE,MAAM,IAAIiH,MAAM,yBAEtB,CAEA,GAAwB,MAApBzI,EAAS0I,QAAkB1I,EAASG,KAAKE,QAC3CC,EAAAA,GAAQD,QAAQL,EAASG,KAAKG,SAC9B2D,EAAK0E,cACLlE,EAAY,IACZE,EAAiB,IACjBE,EAAiB,IACjBoB,EAA6B,IAC7BlB,EAAgB,WAChBE,EAAkB,GAClBE,EAAgB,IAChBpB,EAAUrB,OACL,CAAC,IAADkG,EACL,MAAMC,GAA4B,QAAbD,EAAA5I,EAASG,YAAI,IAAAyI,OAAA,EAAbA,EAAetI,UAAW,yBAC/CA,EAAAA,GAAQP,MAAM8I,EAChB,CACF,CAAE,MAAO9I,GAAQ,IAADK,EAAA0I,EAAAC,EAId,GAHAf,QAAQjI,MAAM,yBAA0BA,GAGrB,iBAAfA,EAAMiJ,KACR1I,EAAAA,GAAQP,MAAM,0FACT,GAA+B,OAAb,QAAdK,EAAAL,EAAMC,gBAAQ,IAAAI,OAAA,EAAdA,EAAgBsI,QACzBpI,EAAAA,GAAQP,MAAM,8DACT,GAA+B,OAAb,QAAd+I,EAAA/I,EAAMC,gBAAQ,IAAA8I,OAAA,EAAdA,EAAgBJ,QAAgB,CAAC,IAADO,EACzC3I,EAAAA,GAAQP,OAAyB,QAAnBkJ,EAAAlJ,EAAMC,SAASG,YAAI,IAAA8I,OAAA,EAAnBA,EAAqB3I,UAAW,6BAChD,MAAsC,OAAb,QAAdyI,EAAAhJ,EAAMC,gBAAQ,IAAA+I,OAAA,EAAdA,EAAgBL,QACzBpI,EAAAA,GAAQP,MAAM,yCAEdO,EAAAA,GAAQP,MAAM,sEAElB,CAAC,QACKiH,GACFkC,aAAalC,GAEfzC,GAAW,GACXU,EAAkB,GAClBE,EAAgB,IAChBf,GAAS+C,EAAAA,EAAAA,MACX,GA+DMgC,cAAe,CAAEvG,MAAO,WACxBC,UAAU,gBAAeY,SAAA,EAEzB+C,EAAAA,EAAAA,MAAA,OAAK3D,UAAU,WAAUY,SAAA,EACvBD,EAAAA,EAAAA,KAACU,EAAAA,EAAKkF,KAAI,CACRC,MAAM,QACNnB,KAAK,QACLoB,MAAO,CAAC,CAAEC,UAAU,EAAMjJ,QAAS,0BACnCuC,UAAU,iBAAgBY,UAE1B+C,EAAAA,EAAAA,MAAC1C,EAAAA,QAAM,CACL0F,YAAY,eACZC,SAtRa7G,IACzBiD,EAhC2BjD,KAC3B,OAAQA,GACN,IAAK,UACH,OAAOc,EAAAA,GACT,IAAK,YACH,OAAOC,EAAAA,GACT,IAAK,UACH,OAAOC,EAAAA,GACT,QACE,MAAO,GACX,EAsBqB8F,CAAoB9G,IACzCmD,EAnB0BnD,KAC1B,OAAQA,GACN,IAAK,UACH,MAAO,CAAC,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,KACxC,IAAK,YACH,MAAO,CAAC,IAAK,IAAK,IAAK,KACzB,IAAK,UACH,MAAO,CAAC,IAAK,KACf,QACE,MAAO,GACX,EASoB+G,CAAmB/G,IACvCqD,EAA6B,IAC7BhC,EAAK2F,eAAe,CAAE9G,aAASlC,EAAWiC,eAAWjC,GAAY,EAmRrDiJ,KAAK,QAAOpG,SAAA,EAEZD,EAAAA,EAAAA,KAACK,EAAM,CAACiG,MAAM,UAASrG,SAAC,aACxBD,EAAAA,EAAAA,KAACK,EAAM,CAACiG,MAAM,YAAWrG,SAAC,eAC1BD,EAAAA,EAAAA,KAACK,EAAM,CAACiG,MAAM,UAASrG,SAAC,kBAI5BD,EAAAA,EAAAA,KAACU,EAAAA,EAAKkF,KAAI,CACRC,MAAM,QACNnB,KAAK,YACLoB,MAAO,CAAC,CAAEC,UAAU,EAAMjJ,QAAS,0BACnCuC,UAAU,iBAAgBY,UAE1BD,EAAAA,EAAAA,KAACM,EAAAA,QAAM,CACL0F,YAAY,eACZK,KAAK,QACLJ,SA7RiBK,IAE7B,MAAMC,EAA4B/D,EAA0BgE,QAAOC,GAAOA,IAAQH,IAClF7D,EAA6B8D,EAA0B,EA0RXtG,SAE/BqC,EAAiBoE,KAAID,IACpBzG,EAAAA,EAAAA,KAACK,EAAM,CAAWiG,MAAOG,EAAIxG,SAAEwG,GAAlBA,aAMrBzG,EAAAA,EAAAA,KAACU,EAAAA,EAAKkF,KAAI,CACRC,MAAM,UACNnB,KAAK,UACLoB,MAAO,CAAC,CAAEC,UAAU,EAAMjJ,QAAS,4BAA6BmD,UAEhED,EAAAA,EAAAA,KAACM,EAAAA,QAAM,CAAC0F,YAAY,iBAAiBK,KAAK,QAAOpG,SAC9CmC,EAAkBsE,KAAIpH,IACrBU,EAAAA,EAAAA,KAACK,EAAM,CAAeiG,MAAOhH,EAAQW,SAAEX,GAA1BA,SAKD,WAAjBJ,IACC8D,EAAAA,EAAAA,MAACtC,EAAAA,EAAKkF,KAAI,CACRC,MAAM,gCACNxG,UAAU,6BAA4BY,SAAA,EAEtCD,EAAAA,EAAAA,KAAA,OAAKX,UAAU,0BAAyBY,UACtCD,EAAAA,EAAAA,KAAA,KAAAC,SAAG,oGAELD,EAAAA,EAAAA,KAACM,EAAAA,QAAM,CACLqG,KAAK,WACLX,YAAY,uCACZK,KAAK,QACLC,MAAO9D,EACPyD,SAnUyBW,IACrCnE,EAA6BmE,EAAQ,EAmUzBC,MAAO,CAAEC,MAAO,QAAS7G,SAExBqC,EACEkE,QAAOC,GAAOA,IAAQhG,EAAKsG,cAAc,eACzCL,KAAID,IACHzG,EAAAA,EAAAA,KAACK,EAAM,CAAWiG,MAAOG,EAAIxG,SAAEwG,GAAlBA,QAGnBzG,EAAAA,EAAAA,KAAA,OAAKX,UAAU,0BAAyBY,UACtCD,EAAAA,EAAAA,KAAA,SAAAC,SAAO,kGAKbD,EAAAA,EAAAA,KAACU,EAAAA,EAAKkF,KAAI,CACRC,MAAM,QACNnB,KAAK,QACLoB,MAAO,CAAC,CAAEC,UAAU,EAAMjJ,QAAS,yBAA0BmD,UAE7DD,EAAAA,EAAAA,KAAA,SACEgH,KAAK,OACLhB,YAAW,SAAA1H,OAAWyE,IAAmBkE,cAAa,UACtD5H,UAAU,kBAIK,gBAAjBH,GAAmD,UAAjBA,KAClCc,EAAAA,EAAAA,KAACU,EAAAA,EAAKkF,KAAI,CACRC,MAAM,OACNnB,KAAK,OACLoB,MAAO,CAAC,CAAEC,UAAU,EAAMjJ,QAAS,0BAA2BmD,UAE9DD,EAAAA,EAAAA,KAAA,SACEgH,KAAK,OACLhB,YAAY,0BACZ3G,UAAU,iBAKE,WAAjBH,IACC8D,EAAAA,EAAAA,MAAAkE,EAAAA,SAAA,CAAAjH,SAAA,EACED,EAAAA,EAAAA,KAACU,EAAAA,EAAKkF,KAAI,CACRC,MAAM,gBACNxG,UAAU,wBAAuBY,UAEjC+C,EAAAA,EAAAA,MAAA,OAAK3D,UAAU,yBAAwBY,SAAA,EACrC+C,EAAAA,EAAAA,MAAA,OACE3D,UAAS,iBAAAf,OAAoC,YAAjBgD,EAA6B,SAAW,IACpE6F,QAASA,IAAM5F,EAAgB,WAAWtB,SAAA,EAE1CD,EAAAA,EAAAA,KAACkD,EAAAA,IAAO,CAAC7D,UAAU,iBACnBW,EAAAA,EAAAA,KAAA,QAAAC,SAAM,mBACND,EAAAA,EAAAA,KAAA,KAAAC,SAAG,mCAEL+C,EAAAA,EAAAA,MAAA,OACE3D,UAAS,iBAAAf,OAAoC,UAAjBgD,EAA2B,SAAW,IAClE6F,QAASA,IAAM5F,EAAgB,SAAStB,SAAA,EAExCD,EAAAA,EAAAA,KAACoH,EAAAA,IAAgB,CAAC/H,UAAU,iBAC5BW,EAAAA,EAAAA,KAAA,QAAAC,SAAM,mBACND,EAAAA,EAAAA,KAAA,KAAAC,SAAG,sCAEL+C,EAAAA,EAAAA,MAAA,OACE3D,UAAS,iBAAAf,OAAoC,WAAjBgD,EAA4B,SAAW,IACnE6F,QAASA,IAAM5F,EAAgB,UAAUtB,SAAA,EAEzCD,EAAAA,EAAAA,KAACoH,EAAAA,IAAgB,CAAC/H,UAAU,iBAC5BW,EAAAA,EAAAA,KAAA,QAAAC,SAAM,uBACND,EAAAA,EAAAA,KAAA,KAAAC,SAAG,wCAKS,YAAjBqB,GACC0B,EAAAA,EAAAA,MAAAkE,EAAAA,SAAA,CAAAjH,SAAA,EACED,EAAAA,EAAAA,KAACU,EAAAA,EAAKkF,KAAI,CACRC,MAAM,qBACNnB,KAAK,UACLoB,MAAO,CAAC,CAAEC,SAA2B,YAAjBzE,EAA4BxE,QAAS,kCAAmCmD,UAE5FD,EAAAA,EAAAA,KAAA,SACEgH,KAAK,OACLhB,YAAY,6CACZ3G,UAAU,kBAIdW,EAAAA,EAAAA,KAACU,EAAAA,EAAKkF,KAAI,CACRC,MAAM,uBACNnB,KAAK,WAAUzE,UAEfD,EAAAA,EAAAA,KAAA,SACEgH,KAAK,MACLhB,YAAY,6BACZ3G,UAAU,kBAIdW,EAAAA,EAAAA,KAACU,EAAAA,EAAKkF,KAAI,CACRC,MAAM,2BACNnB,KAAK,eAAczE,UAEnBD,EAAAA,EAAAA,KAAA,SACEgH,KAAK,MACLhB,YAAY,iCACZ3G,UAAU,oBAIG,UAAjBiC,GACF0B,EAAAA,EAAAA,MAAAkE,EAAAA,SAAA,CAAAjH,SAAA,EACED,EAAAA,EAAAA,KAACU,EAAAA,EAAKkF,KAAI,CACRC,MAAM,gBACNnB,KAAK,WACLoB,MAAO,CAAC,CAAEC,SAA2B,UAAjBzE,EAA0BxE,QAAS,+BAAgCmD,UAEvFD,EAAAA,EAAAA,KAAA,SACEgH,KAAK,MACLhB,YAAY,6EACZ3G,UAAU,kBAIdW,EAAAA,EAAAA,KAACU,EAAAA,EAAKkF,KAAI,CACRC,MAAM,mCACNxG,UAAU,iBAAgBY,UAE1BD,EAAAA,EAAAA,KAACqH,EAAAA,GAAMxD,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACDf,GAAoB,IACxB9B,SAAUE,EACV+E,SAAUqB,IAAA,IAAC,SAAEtG,GAAUsG,EAAA,OAAKnG,EAAiBH,EAAS,EACtD3B,UAAU,mBACVkI,OAASC,IACPA,EAAEC,iBACFtF,GAAc,GACd,MACMuF,EADQrD,MAAMsD,KAAKH,EAAEI,aAAaC,OACfrB,QAAOsB,GAAQA,EAAKd,KAAKe,WAAW,YAC7D,GAAIL,EAAWvK,OAAS,EAAG,CACzB,MAAM2K,EAAOJ,EAAW,GAExB,GAAII,EAAKzB,KAAO,QAEd,YADAvJ,EAAAA,GAAQP,MAAM,6CAGhB4E,EAAiB,CAAC,CAChB6G,IAAK,KACLtD,KAAMoD,EAAKpD,KACXQ,OAAQ,OACRlB,cAAe8D,EACfG,IAAKC,IAAIC,gBAAgBL,MAE3BhL,EAAAA,GAAQD,QAAQ,mCAClB,MACEC,EAAAA,GAAQP,MAAM,4CAChB,EAEF6L,WAAaZ,IACXA,EAAEC,iBACFtF,GAAc,EAAK,EAErBkG,YAAcb,IACZA,EAAEC,iBACFtF,GAAc,EAAK,EAErBmG,YAAcd,IACZA,EAAEC,iBACFtF,GAAc,EAAM,EACpBlC,UAEF+C,EAAAA,EAAAA,MAAA,OAAK3D,UAAS,yCAAAf,OAA2C4D,EAAa,YAAc,IAAKjC,SAAA,EACvFD,EAAAA,EAAAA,KAACuI,EAAAA,IAAQ,CAAClJ,UAAU,iBACpBW,EAAAA,EAAAA,KAAA,KAAAC,SAAIiC,EAAa,uBAAyB,8CAC1ClC,EAAAA,EAAAA,KAAA,KAAGX,UAAU,cAAaY,SAAC,oCAC3BD,EAAAA,EAAAA,KAAA,KAAGX,UAAU,cAAaY,SAAC,kDAMnC+C,EAAAA,EAAAA,MAAAkE,EAAAA,SAAA,CAAAjH,SAAA,EACED,EAAAA,EAAAA,KAACU,EAAAA,EAAKkF,KAAI,CACRC,MAAM,oBACNxG,UAAU,iBAAgBY,UAE1BD,EAAAA,EAAAA,KAACqH,EAAAA,GAAMxD,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GAhTA,CACvBlB,aAAcA,KAAM,EACpBC,SAAU,EACVC,OAAQ,YA8S8B,IACpB7B,SAAUI,EACV6E,SAAUuC,IAAA,IAAC,SAAExH,GAAUwH,EAAA,OAAKnH,EAAiBL,EAAS,EACtD3B,UAAU,eAAcY,UAExB+C,EAAAA,EAAAA,MAAA,OAAK3D,UAAU,cAAaY,SAAA,EAC1BD,EAAAA,EAAAA,KAACoH,EAAAA,IAAgB,CAAC/H,UAAU,iBAC5BW,EAAAA,EAAAA,KAAA,KAAAC,SAAG,wCACHD,EAAAA,EAAAA,KAAA,KAAGX,UAAU,cAAaY,SAAC,8CAC3BD,EAAAA,EAAAA,KAAA,KAAGX,UAAU,cAAaY,SAAC,4DAKjCD,EAAAA,EAAAA,KAACU,EAAAA,EAAKkF,KAAI,CACRC,MAAM,qCACNxG,UAAU,iBAAgBY,UAE1BD,EAAAA,EAAAA,KAACqH,EAAAA,GAAMxD,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACDf,GAAoB,IACxB9B,SAAUE,EACV+E,SAAUwC,IAAA,IAAC,SAAEzH,GAAUyH,EAAA,OAAKtH,EAAiBH,EAAS,EACtD3B,UAAU,mBACVkI,OAASC,IACPA,EAAEC,iBACFtF,GAAc,GACd,MACMuF,EADQrD,MAAMsD,KAAKH,EAAEI,aAAaC,OACfrB,QAAOsB,GAAQA,EAAKd,KAAKe,WAAW,YAC7D,GAAIL,EAAWvK,OAAS,EAAG,CACzB,MAAM2K,EAAOJ,EAAW,GAExB,GAAII,EAAKzB,KAAO,QAEd,YADAvJ,EAAAA,GAAQP,MAAM,6CAGhB4E,EAAiB,CAAC,CAChB6G,IAAK,KACLtD,KAAMoD,EAAKpD,KACXQ,OAAQ,OACRlB,cAAe8D,EACfG,IAAKC,IAAIC,gBAAgBL,MAE3BhL,EAAAA,GAAQD,QAAQ,mCAClB,MACEC,EAAAA,GAAQP,MAAM,4CAChB,EAEF6L,WAAaZ,IACXA,EAAEC,iBACFtF,GAAc,EAAK,EAErBkG,YAAcb,IACZA,EAAEC,iBACFtF,GAAc,EAAK,EAErBmG,YAAcd,IACZA,EAAEC,iBACFtF,GAAc,EAAM,EACpBlC,UAEF+C,EAAAA,EAAAA,MAAA,OAAK3D,UAAS,yCAAAf,OAA2C4D,EAAa,YAAc,IAAKjC,SAAA,EACvFD,EAAAA,EAAAA,KAACuI,EAAAA,IAAQ,CAAClJ,UAAU,iBACpBW,EAAAA,EAAAA,KAAA,KAAAC,SAAIiC,EAAa,uBAAyB,8CAC1ClC,EAAAA,EAAAA,KAAA,KAAGX,UAAU,cAAaY,SAAC,oCAC3BD,EAAAA,EAAAA,KAAA,KAAGX,UAAU,cAAaY,SAAC,oDASvB,WAAjBf,IACCc,EAAAA,EAAAA,KAACU,EAAAA,EAAKkF,KAAI,CACRC,MAAK,UAAAvH,OAAYyE,IAAkB,aACnC1D,UAAU,iBAAgBY,UAE1BD,EAAAA,EAAAA,KAACqH,EAAAA,GAAMxD,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACDnB,GAAW,IACf1B,SAAUA,EACViF,SAAUyC,IAAA,IAAC,SAAE1H,GAAU0H,EAAA,OAAKzH,EAAYD,EAAS,EACjD3B,UAAU,kBAAiBY,UAE3B+C,EAAAA,EAAAA,MAAA,OAAK3D,UAAU,cAAaY,SAAA,EAC1BD,EAAAA,EAAAA,KAACoH,EAAAA,IAAgB,CAAC/H,UAAU,iBAC5BW,EAAAA,EAAAA,KAAA,KAAAC,SAAG,kCACHD,EAAAA,EAAAA,KAAA,KAAGX,UAAU,cAAaY,SAAC,+CAMjB,UAAjBf,IACCc,EAAAA,EAAAA,KAACU,EAAAA,EAAKkF,KAAI,CACRC,MAAM,8BACNxG,UAAU,iBAAgBY,UAE1BD,EAAAA,EAAAA,KAACqH,EAAAA,GAAMxD,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACDf,GAAoB,IACxB9B,SAAUE,EACV+E,SAAU0C,IAAA,IAAC,SAAE3H,GAAU2H,EAAA,OAAKxH,EAAiBH,EAAS,EACtD3B,UAAU,mBAAkBY,UAE5B+C,EAAAA,EAAAA,MAAA,OAAK3D,UAAU,oBAAmBY,SAAA,EAChCD,EAAAA,EAAAA,KAACuI,EAAAA,IAAQ,CAAClJ,UAAU,iBACpBW,EAAAA,EAAAA,KAAA,KAAAC,SAAG,8BAOVa,GAA4B,WAAjBQ,GAA8C,WAAjBpC,IACvC8D,EAAAA,EAAAA,MAAA,OAAK3D,UAAU,0BAAyBY,SAAA,EACtC+C,EAAAA,EAAAA,MAAA,OAAK3D,UAAU,kBAAiBY,SAAA,EAC9B+C,EAAAA,EAAAA,MAAA,OAAK3D,UAAU,gBAAeY,SAAA,EAC5BD,EAAAA,EAAAA,KAAA,QAAMX,UAAU,gBAAeY,SAAEyB,KACjCsB,EAAAA,EAAAA,MAAA,QAAM3D,UAAU,sBAAqBY,SAAA,CAAEuB,EAAe,UAEvDI,EAAc,IACboB,EAAAA,EAAAA,MAAA,OAAK3D,UAAU,eAAcY,SAAA,EAC3B+C,EAAAA,EAAAA,MAAA,QAAM3D,UAAU,eAAcY,SAAA,CAAC,iBACxB2B,EAAW,SAAkBgH,QAAQ,GAAG,WAE9C9G,EAAgB,GAAKA,EAAgB,OACpCkB,EAAAA,EAAAA,MAAA,QAAM3D,UAAU,iBAAgBY,SAAA,CAAC,gBAC3BtC,KAAKkL,KAAK/G,GAAe,wBAOvC9B,EAAAA,EAAAA,KAAA,OAAKX,UAAU,eAAcY,UAC3BD,EAAAA,EAAAA,KAAA,OACEX,UAAU,gBACVwH,MAAO,CACLC,MAAM,GAADxI,OAAKkD,EAAc,KACxBsH,WAAY,wBAKlB9I,EAAAA,EAAAA,KAAA,OAAKX,UAAU,mBAAkBY,SAC9BuB,EAAiB,KAChBwB,EAAAA,EAAAA,MAAA,OAAK3D,UAAU,iBAAgBY,SAAA,EAC7BD,EAAAA,EAAAA,KAAA,QAAAC,SAAM,oDACND,EAAAA,EAAAA,KAAA,SAAAC,SAAO,yDAGT+C,EAAAA,EAAAA,MAAA,OAAK3D,UAAU,kBAAiBY,SAAA,EAC9BD,EAAAA,EAAAA,KAAA,QAAAC,SAAM,gFACND,EAAAA,EAAAA,KAAA,SAAAC,SAAO,yDAOjB+C,EAAAA,EAAAA,MAAA,OAAK3D,UAAU,eAAcY,SAAA,EAC3BD,EAAAA,EAAAA,KAAC+I,EAAAA,GAAM,CACL/B,KAAK,UACLG,QAAS3G,EACT6F,KAAK,QACLhH,UAAU,aACV2J,SAAUlI,EAAQb,SACnB,YAGDD,EAAAA,EAAAA,KAAC+I,EAAAA,GAAM,CACL/B,KAAK,UACLiC,SAAS,SACTnI,QAASA,EACTuF,KAAK,QACLhH,UAAU,aAAYY,SAErBa,EACkB,WAAjBQ,GAA8C,WAAjBpC,EAC7B,eAAiB,YAAW,OAAAZ,OAErByE,iBAQvB,E,iHCnwBA,MAAQ/G,QAASC,GAAkBC,EAAQ,OCgBnCmE,OAAO,GAAIC,EAAAA,QAySnB,EAvSwB4I,KACtB,MAAOC,EAAQC,IAAaxJ,EAAAA,EAAAA,UAAS,KAC9BkB,EAASC,IAAcnB,EAAAA,EAAAA,WAAS,IAChCyJ,EAAoBC,IAAyB1J,EAAAA,EAAAA,WAAS,IACtD2J,EAAeC,IAAoB5J,EAAAA,EAAAA,UAAS,OAC5C6J,EAAkBC,IAAuB9J,EAAAA,EAAAA,UAAS,OAClD+J,EAAcC,IAAmBhK,EAAAA,EAAAA,UAAS,YAC1CoB,EAAUC,IAAerB,EAAAA,EAAAA,UAAS,KAClCiK,EAAqBC,IAA0BlK,EAAAA,EAAAA,UAAS,CAAC,GAE1DgB,GAAWC,EAAAA,EAAAA,MAEXkJ,EAAY,CAChB,CAAEvE,KAAM,KAAMd,KAAM,WACpB,CAAEc,KAAM,KAAMd,KAAM,WACpB,CAAEc,KAAM,KAAMd,KAAM,UACpB,CAAEc,KAAM,KAAMd,KAAM,UACpB,CAAEc,KAAM,KAAMd,KAAM,WACpB,CAAEc,KAAM,KAAMd,KAAM,cACpB,CAAEc,KAAM,KAAMd,KAAM,WACpB,CAAEc,KAAM,KAAMd,KAAM,YACpB,CAAEc,KAAM,KAAMd,KAAM,UACpB,CAAEc,KAAM,KAAMd,KAAM,WACpB,CAAEc,KAAM,KAAMd,KAAM,UACpB,CAAEc,KAAM,KAAMd,KAAM,SACpB,CAAEc,KAAM,KAAMd,KAAM,QACpB,CAAEc,KAAM,KAAMd,KAAM,aAGtB7E,EAAAA,EAAAA,YAAU,KACRmK,GAAa,GACZ,IAEH,MAAMA,EAAc5N,UAClB,IACE2E,GAAW,GACX,MAAMvE,QAAiBC,EAAAA,EAAAA,MACnBD,EAASK,QACXuM,EAAU5M,EAASG,MAEnBG,EAAAA,GAAQP,MAAM,yBAElB,CAAE,MAAOA,GACPO,EAAAA,GAAQP,MAAM,wBAChB,CAAC,QACCwE,GAAW,EACb,GAGIkJ,EAA0B7N,eAAO8N,GAA8B,IAArBC,EAAQjN,UAAAC,OAAA,QAAAC,IAAAF,UAAA,GAAAA,UAAA,GAAG,KACzD,IACE4M,GAAuBM,IAAIvG,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GAAUuG,GAAI,IAAE,CAACF,IAAU,MACtDpN,EAAAA,GAAQuN,KAAK,wDAEb,MAAM7N,QDrEqBJ,eAAO8N,GAA8B,IAArBC,EAAQjN,UAAAC,OAAA,QAAAC,IAAAF,UAAA,GAAAA,UAAA,GAAG,KAC1D,IAIE,aAHuBjB,EAAcI,KAAK,iCAADiC,OAAkC4L,GAAW,CACpFC,cAEcxN,IAClB,CAAE,MAAOJ,GACP,OAAOA,EAAMC,SAASG,IACxB,CACF,CC4D6B2N,CAAkBJ,EAASC,GAC9C3N,EAASK,SACXC,EAAAA,GAAQD,QAAQ,qCAChBmN,KAEAlN,EAAAA,GAAQP,MAAMC,EAASM,SAAW,+BAEtC,CAAE,MAAOP,GACPO,EAAAA,GAAQP,MAAM,6BAChB,CAAC,QACCuN,GAAuBM,IAAIvG,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GAAUuG,GAAI,IAAE,CAACF,IAAU,KACxD,CACF,EA+DMK,EAAU,CACd,CACE9K,MAAO,QACP+K,UAAW,QACXpG,IAAK,QACL0C,MAAO,MACP2D,OAAQA,CAAChL,EAAOiL,KACd1H,EAAAA,EAAAA,MAAA,OAAA/C,SAAA,EACED,EAAAA,EAAAA,KAAA,OAAK6G,MAAO,CAAE8D,WAAY,OAAQC,aAAc,OAAQ3K,SAAER,KAC1DuD,EAAAA,EAAAA,MAAA,OAAK6D,MAAO,CAAEgE,SAAU,OAAQC,MAAO,QAAS7K,SAAA,CAC7CyK,EAAOpL,QAAQ,iBAAUoL,EAAOrL,UAAU,WAAIqL,EAAOtL,aAK9D,CACEK,MAAO,kBACP2E,IAAK,iBACL0C,MAAO,MACP2D,OAAQA,CAACM,EAAGL,IA/CWM,KAAW,IAADC,EACnC,MAAuC,eAAnCD,EAAME,0BACDlL,EAAAA,EAAAA,KAACmL,EAAAA,EAAG,CAACL,MAAM,OAAOM,MAAMpL,EAAAA,EAAAA,KAACqL,EAAAA,EAAa,IAAIpL,SAAC,kBAEhD+K,EAAMM,eAA+B,QAAfL,EAAAD,EAAMO,iBAAS,IAAAN,OAAA,EAAfA,EAAiB9N,QAAS,GAEhD6F,EAAAA,EAAAA,MAACwI,EAAAA,EAAK,CAAAvL,SAAA,EACJ+C,EAAAA,EAAAA,MAACmI,EAAAA,EAAG,CAACL,MAAM,QAAQM,MAAMpL,EAAAA,EAAAA,KAACyL,EAAAA,EAAgB,IAAIxL,SAAA,CAC3C+K,EAAMO,UAAUpO,OAAO,YAAU6N,EAAMO,UAAUpO,OAAS,EAAI,IAAM,MAEtE6N,EAAMO,UAAU7E,KAAIgF,IACnB1I,EAAAA,EAAAA,MAACmI,EAAAA,EAAG,CAEFL,MAAOY,EAAIC,gBAAkB,OAAS,SACtCtF,KAAK,QAAOpG,SAAA,CAEXyL,EAAI/B,aAAa,IAAE+B,EAAIC,gBAAkB,SAAW,aAJhDD,EAAIvB,eAUoB,WAAnCa,EAAME,0BACDlL,EAAAA,EAAAA,KAACmL,EAAAA,EAAG,CAACL,MAAM,MAAK7K,SAAC,uBAEnBD,EAAAA,EAAAA,KAACmL,EAAAA,EAAG,CAACL,MAAM,UAAS7K,SAAC,gBAAkB,EAsBrB2L,CAAkBlB,IAE3C,CACEjL,MAAO,eACP2E,IAAK,cACL0C,MAAO,MACP2D,OAAQA,CAACM,EAAGL,IACNA,EAAOmB,UAAYnB,EAAOmB,SAASC,SAAS,kBACvC9L,EAAAA,EAAAA,KAACmL,EAAAA,EAAG,CAACL,MAAM,SAASM,MAAMpL,EAAAA,EAAAA,KAAC+L,EAAAA,EAAc,IAAI9L,SAAC,aAEnDyK,EAAOsB,UAAYtB,EAAOmB,UACrB7L,EAAAA,EAAAA,KAACmL,EAAAA,EAAG,CAACL,MAAM,MAAMM,MAAMpL,EAAAA,EAAAA,KAACiM,EAAAA,EAAkB,IAAIhM,SAAC,aAEjDD,EAAAA,EAAAA,KAACmL,EAAAA,EAAG,CAACL,MAAM,UAAS7K,SAAC,aAGhC,CACER,MAAO,UACP2E,IAAK,UACL0C,MAAO,MACP2D,OAAQA,CAACM,EAAGL,KACV1H,EAAAA,EAAAA,MAACwI,EAAAA,EAAK,CAAAvL,SAAA,CACHyK,EAAOmB,UAAYnB,EAAOmB,SAASC,SAAS,mBAC3C9L,EAAAA,EAAAA,KAACkM,EAAAA,EAAO,CAACzM,MAAM,8BAA6BQ,UAC1CD,EAAAA,EAAAA,KAAC+I,EAAAA,GAAM,CACL/B,KAAK,UACLoE,MAAMpL,EAAAA,EAAAA,KAACqL,EAAAA,EAAa,IACpBhF,KAAK,QACLvF,QAAS+I,EAAoBa,EAAOyB,KACpChF,QAASA,IAAM8C,EAAwBS,EAAOyB,KAC9CnD,SAA8C,eAApC0B,EAAOQ,yBAA0CjL,SAC5D,gBAMLD,EAAAA,EAAAA,KAACkM,EAAAA,EAAO,CAACzM,MAAM,8BAA6BQ,UAC1CD,EAAAA,EAAAA,KAAC+I,EAAAA,GAAM,CACLqC,MAAMpL,EAAAA,EAAAA,KAACoM,EAAAA,EAAc,IACrB/F,KAAK,QACLc,QAASA,KACPqC,EAAiBkB,GACjBpB,GAAsB,EAAK,EAC3BrJ,SACH,cAKHD,EAAAA,EAAAA,KAACkM,EAAAA,EAAO,CAACzM,MAAM,qBAAoBQ,UACjCD,EAAAA,EAAAA,KAAC+I,EAAAA,GAAM,CACLqC,MAAMpL,EAAAA,EAAAA,KAACqM,EAAAA,EAAc,IACrBhG,KAAK,QACLc,QAAS6C,WAQftH,EAAc,CAClBC,aAAemF,GACCA,EAAKpD,KAAKuC,cAAcqF,SAAS,SAK/CrL,EAAY,CAAC6G,KACN,IAJLhL,EAAAA,GAAQP,MAAM,uCACP,GAKXyE,WACAuL,SAAUA,KACRtL,EAAY,GAAG,GAInB,OACE+B,EAAAA,EAAAA,MAAA,OAAK3D,UAAU,mBAAkBY,SAAA,EAC/B+C,EAAAA,EAAAA,MAAA,OAAK3D,UAAU,0BAAyBY,SAAA,EACtCD,EAAAA,EAAAA,KAAA,MAAAC,SAAI,yBACJD,EAAAA,EAAAA,KAAA,KAAAC,SAAG,kFAGLD,EAAAA,EAAAA,KAACwM,EAAAA,EAAK,CACJjC,QAASA,EACTkC,WAAYtD,EACZuD,OAAO,MACP5L,QAASA,EACT6L,WAAY,CACVC,SAAU,GACVC,iBAAiB,EACjBC,iBAAiB,EACjBC,UAAYjP,GAAK,SAAAQ,OAAcR,EAAK,YAEtCkP,OAAQ,CAAEC,EAAG,QAGfjK,EAAAA,EAAAA,MAACkK,EAAAA,EAAK,CACJzN,MAAM,yBACN0N,KAAM9D,EACN+D,KAtLuBhR,UAC3B,GAAKmN,GAAqC,IAApBvI,EAAS7D,OAK/B,IACEyD,GAAS6C,EAAAA,EAAAA,OAET,MAAMzF,EAAW,IAAIV,SACrBU,EAASmB,OAAO,WAAY6B,EAAS,IACrChD,EAASmB,OAAO,WAAYsK,GAC5BzL,EAASmB,OAAO,eAAgBwK,GAChC3L,EAASmB,OAAO,YAAkC,OAArBsK,GAE7B,MAAMjN,ODtFkBJ,OAAO8N,EAASlM,KAC5C,IAIE,aAHuB/B,EAAcI,KAAK,8BAADiC,OAA+B4L,GAAWlM,EAAU,CAC3FT,QAAS,CAAE,eAAgB,0BAEbZ,IAClB,CAAE,MAAOJ,GACP,OAAOA,EAAMC,SAASG,IACxB,GC8E2B0Q,CAAe9D,EAAc4C,IAAKnO,GAErDxB,EAASK,SACXC,EAAAA,GAAQD,QAAQ,mCAChByM,GAAsB,GACtBrI,EAAY,IACZuI,EAAiB,MACjBQ,KAEAlN,EAAAA,GAAQP,MAAMC,EAASM,SAAW,4BAEtC,CAAE,MAAOP,GACPO,EAAAA,GAAQP,MAAM,2BAChB,CAAC,QACCqE,GAAS+C,EAAAA,EAAAA,MACX,MA5BE7G,EAAAA,GAAQwQ,QAAQ,mDA4BlB,EAyJI9M,SAAUA,KACR8I,GAAsB,GACtBrI,EAAY,IACZuI,EAAiB,KAAK,EAExB+D,OAAO,SACPC,WAAW,SAAQvN,SAAA,CAElBsJ,IACCvG,EAAAA,EAAAA,MAAA,OAAK6D,MAAO,CAAE+D,aAAc,QAAS3K,SAAA,EACnCD,EAAAA,EAAAA,KAAA,UAAAC,SAAQ,WAAe,IAAEsJ,EAAc9J,UAI3CuD,EAAAA,EAAAA,MAAA,OAAK6D,MAAO,CAAE+D,aAAc,QAAS3K,SAAA,EACnCD,EAAAA,EAAAA,KAAA,SAAAC,SAAO,eACPD,EAAAA,EAAAA,KAACM,EAAAA,QAAM,CACLgG,MAAOmD,EACPxD,SAAWK,IACToD,EAAoBpD,GACpB,MAAMmH,EAAO1D,EAAU2D,MAAKC,GAAKA,EAAEnI,OAASc,IAC5CsD,GAAoB,OAAJ6D,QAAI,IAAJA,OAAI,EAAJA,EAAM/I,OAAQ4B,EAAM,EAEtCO,MAAO,CAAEC,MAAO,OAAQ8G,UAAW,OAAQ3N,SAE1C8J,EAAUrD,KAAI+G,IACbzN,EAAAA,EAAAA,KAACK,EAAM,CAAiBiG,MAAOmH,EAAKjI,KAAKvF,SACtCwN,EAAK/I,MADK+I,EAAKjI,cAOxBxC,EAAAA,EAAAA,MAAA,OAAA/C,SAAA,EACED,EAAAA,EAAAA,KAAA,SAAAC,SAAO,2BACPD,EAAAA,EAAAA,KAACqH,EAAAA,GAAMxD,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GAAKnB,GAAW,IAAEmE,MAAO,CAAE+G,UAAW,OAAQ3N,UACnDD,EAAAA,EAAAA,KAAC+I,EAAAA,GAAM,CAACqC,MAAMpL,EAAAA,EAAAA,KAACoM,EAAAA,EAAc,IAAInM,SAAC,+BAIpC,E,cC5RV,MAAQI,OAAO,GAAIC,EAAAA,SACb,OAAEuN,GAAWC,EAAAA,QA0WnB,QAxWA,SAA6BtO,GAAc,IAAb,OAAEuO,GAAQvO,EACtC,MAAMoB,GAAWC,EAAAA,EAAAA,OACVmN,EAAWC,IAAgBrO,EAAAA,EAAAA,UAAS,KACpCkB,EAASC,IAAcnB,EAAAA,EAAAA,WAAS,IAChCtD,EAAS4R,IAActO,EAAAA,EAAAA,UAAS,CACrCV,aAAc,GACdE,MAAO,GACPC,UAAW,GACXC,QAAS,MAEJ6O,EAAYC,IAAiBxO,EAAAA,EAAAA,UAAS,IA+BvCyO,EAAiBjS,UACrB,IACE2E,GAAW,GACXH,GAAS6C,EAAAA,EAAAA,OAET,MAAMjH,QAAiBuC,EAAAA,EAAAA,IAAqBzC,GAEpB,MAApBE,EAAS0I,QAAkB1I,EAASG,KAAKE,QAC3CoR,EAAazR,EAASG,KAAKA,MAAQ,KAEnCG,EAAAA,GAAQP,MAAM,mCACd0R,EAAa,IAEjB,CAAE,MAAO1R,GACPiI,QAAQjI,MAAM,4BAA6BA,GAC3CO,EAAAA,GAAQP,MAAM,mCACd0R,EAAa,GACf,CAAC,QACClN,GAAW,GACXH,GAAS+C,EAAAA,EAAAA,MACX,IAGF9D,EAAAA,EAAAA,YAAU,KACRwO,GAAgB,GACf,CAAC/R,IAGJ,MAAMgS,EAAqBA,CAAClK,EAAKkC,KAC/B4H,GAAW9D,IACT,MAAMmE,GAAU1K,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GAAQuG,GAAI,IAAE,CAAChG,GAAMkC,IAQrC,MALY,UAARlC,IACFmK,EAAWlP,UAAY,GACvBkP,EAAWjP,QAAU,IAGhBiP,CAAU,GACjB,EAkDEtL,EAAmB+D,IACvB,OAAQA,GACN,IAAK,SACH,OAAOhH,EAAAA,EAAAA,KAACkD,EAAAA,IAAO,CAAC7D,UAAU,wBAC5B,IAAK,cACH,OAAOW,EAAAA,EAAAA,KAACmD,EAAAA,IAAS,CAAC9D,UAAU,uBAC9B,IAAK,cACH,OAAOW,EAAAA,EAAAA,KAACoD,EAAAA,IAAe,CAAC/D,UAAU,wBACpC,IAAK,QACH,OAAOW,EAAAA,EAAAA,KAACqD,EAAAA,IAAM,CAAChE,UAAU,uBAC3B,QACE,OAAOW,EAAAA,EAAAA,KAACmD,EAAAA,IAAS,CAAC9D,UAAU,kBAChC,EAIImP,EAAwBxH,IAC5B,OAAQA,GACN,IAAK,SACH,MAAO,QACT,IAAK,cACH,MAAO,aACT,IAAK,cACH,MAAO,aACT,IAAK,QACH,MAAO,OACT,QACE,OAAOA,EACX,EAIIyH,EAAoBT,EAAUxH,QAAOkI,GACzCA,EAASjP,MAAMwH,cAAc6E,SAASqC,EAAWlH,gBACjDyH,EAASpP,QAAQ2H,cAAc6E,SAASqC,EAAWlH,gBACnDyH,EAASrP,UAAU4H,cAAc6E,SAASqC,EAAWlH,iBAIjDsD,EAAU,CACd,CACE9K,MAAO,WACP2E,IAAK,WACL0C,MAAO,MACP2D,OAAQA,CAACM,EAAGL,KACV1K,EAAAA,EAAAA,KAAA,OAAKX,UAAU,gBAAeY,UAC5B+C,EAAAA,EAAAA,MAAA,OAAK3D,UAAU,kBAAiBY,SAAA,CAC7BgD,EAAgByH,EAAO1D,OACxBhE,EAAAA,EAAAA,MAAA,OAAK3D,UAAU,mBAAkBY,SAAA,EAC/BD,EAAAA,EAAAA,KAAA,OAAKX,UAAU,iBAAgBY,SAAEyK,EAAOjL,SACxCuD,EAAAA,EAAAA,MAAA,OAAK3D,UAAU,gBAAeY,SAAA,EAC5BD,EAAAA,EAAAA,KAACmL,EAAAA,EAAG,CAACL,MAAM,OAAM7K,SAAEuO,EAAqB9D,EAAO1D,SAC/ChE,EAAAA,EAAAA,MAAA,QAAM3D,UAAU,YAAWY,SAAA,CAAEyK,EAAOpL,QAAQ,iBAAUoL,EAAOrL,yBAOzE,CACEI,MAAO,QACP+K,UAAW,QACXpG,IAAK,QACL0C,MAAO,MACP2D,OAASrL,IACPY,EAAAA,EAAAA,KAACmL,EAAAA,EAAG,CAACL,MAAiB,YAAV1L,EAAsB,QAAoB,cAAVA,EAAwB,SAAW,SAASa,SACrFb,EAAMuP,OAAO,GAAGC,cAAgBxP,EAAMyP,MAAM,MAInD,CACEpP,MAAO,QACP+K,UAAW,YACXpG,IAAK,YACL0C,MAAO,MACP2D,OAASpL,IAAc2D,EAAAA,EAAAA,MAAA,QAAM3D,UAAU,cAAaY,SAAA,CAAC,SAAOZ,MAE9D,CACEI,MAAO,UACP+K,UAAW,UACXpG,IAAK,UACL0C,MAAO,OAET,CACErH,MAAO,OACP+K,UAAW,OACXpG,IAAK,OACL0C,MAAO,MACP2D,OAASqE,GAASA,GAAQ,KAE5B,CACErP,MAAO,UACP2E,IAAK,UACL0C,MAAO,MACP2D,OAAQA,CAACM,EAAGL,KACV1H,EAAAA,EAAAA,MAACwI,EAAAA,EAAK,CAAAvL,SAAA,EACJD,EAAAA,EAAAA,KAACkM,EAAAA,EAAO,CAACzM,MAAM,gBAAeQ,UAC5BD,EAAAA,EAAAA,KAAC+I,EAAAA,GAAM,CACL/B,KAAK,UACLoE,MAAMpL,EAAAA,EAAAA,KAAC+O,EAAAA,IAAM,IACb1I,KAAK,QACLc,QAASA,IAAM4G,EAAOrD,GAAQzK,SAC/B,YAKHD,EAAAA,EAAAA,KAACkM,EAAAA,EAAO,CAACzM,MAAM,kBAAiBQ,UAC9BD,EAAAA,EAAAA,KAAC+I,EAAAA,GAAM,CACLiG,QAAM,EACN5D,MAAMpL,EAAAA,EAAAA,KAACiP,EAAAA,IAAO,IACd5I,KAAK,QACLc,QAASA,IA9JA/K,WACnB8Q,EAAAA,EAAMgC,QAAQ,CACZzP,MAAM,UAADnB,OAAYoQ,EAAS1H,KAAKmI,QAAQ,IAAK,MAC5CC,QAAQ,oCAAD9Q,OAAsCoQ,EAASjP,MAAK,oCAC3D8N,OAAQ,SACR8B,OAAQ,SACR7B,WAAY,SACZJ,KAAMhR,UACJ,IAGE,IAAII,EACJ,OAHAoE,GAAS6C,EAAAA,EAAAA,OAGDiL,EAAS1H,MACf,IAAK,SACHxK,QAAiBkC,EAAAA,EAAAA,IAAYgQ,EAASvC,KACtC,MACF,IAAK,cACH3P,QAAiBoC,EAAAA,EAAAA,IAAW8P,EAASvC,KACrC,MACF,IAAK,cACH3P,QAAiBqC,EAAAA,EAAAA,IAAgB6P,EAASvC,KAC1C,MACF,IAAK,QACH3P,QAAiBsC,EAAAA,EAAAA,IAAW4P,EAASvC,KACrC,MACF,QACE,MAAM,IAAIlH,MAAM,yBAMZ,IAADG,EAHiB,MAApB5I,EAAS0I,QAAkB1I,EAASG,KAAKE,SAC3CC,EAAAA,GAAQD,QAAQL,EAASG,KAAKG,SAC9BuR,KAEAvR,EAAAA,GAAQP,OAAmB,QAAb6I,EAAA5I,EAASG,YAAI,IAAAyI,OAAA,EAAbA,EAAetI,UAAW,4BAE5C,CAAE,MAAOP,GACPiI,QAAQjI,MAAM,2BAA4BA,GAC1CO,EAAAA,GAAQP,MAAM,4BAChB,CAAC,QACCqE,GAAS+C,EAAAA,EAAAA,MACX,IAEF,EAoHuB2L,CAAa5E,GAAQzK,SACrC,kBASX,OACE+C,EAAAA,EAAAA,MAAA,OAAK3D,UAAU,yBAAwBY,SAAA,EACrC+C,EAAAA,EAAAA,MAAA,OAAK3D,UAAU,iBAAgBY,SAAA,EAC7BD,EAAAA,EAAAA,KAAA,MAAAC,SAAI,gCACJD,EAAAA,EAAAA,KAAA,KAAAC,SAAG,iFAILD,EAAAA,EAAAA,KAAA,OAAKX,UAAU,kBAAiBY,UAC9B+C,EAAAA,EAAAA,MAAA,OAAK3D,UAAU,cAAaY,SAAA,EAC1B+C,EAAAA,EAAAA,MAAA,OAAK3D,UAAU,eAAcY,SAAA,EAC3BD,EAAAA,EAAAA,KAAA,SAAAC,SAAO,oBACP+C,EAAAA,EAAAA,MAAC1C,EAAAA,QAAM,CACL0F,YAAY,YACZM,MAAOhK,EAAQ4C,mBAAgB9B,EAC/B6I,SAAWK,GAAUgI,EAAmB,eAAgBhI,GACxDiJ,YAAU,EACV1I,MAAO,CAAEC,MAAO,KAAM7G,SAAA,EAEtBD,EAAAA,EAAAA,KAACK,EAAM,CAACiG,MAAM,SAAQrG,SAAC,YACvBD,EAAAA,EAAAA,KAACK,EAAM,CAACiG,MAAM,cAAarG,SAAC,iBAC5BD,EAAAA,EAAAA,KAACK,EAAM,CAACiG,MAAM,cAAarG,SAAC,iBAC5BD,EAAAA,EAAAA,KAACK,EAAM,CAACiG,MAAM,QAAOrG,SAAC,iBAI1B+C,EAAAA,EAAAA,MAAA,OAAK3D,UAAU,eAAcY,SAAA,EAC3BD,EAAAA,EAAAA,KAAA,SAAAC,SAAO,YACP+C,EAAAA,EAAAA,MAAC1C,EAAAA,QAAM,CACL0F,YAAY,aACZM,MAAOhK,EAAQ8C,YAAShC,EACxB6I,SAAWK,GAAUgI,EAAmB,QAAShI,GACjDiJ,YAAU,EACV1I,MAAO,CAAEC,MAAO,KAAM7G,SAAA,EAEtBD,EAAAA,EAAAA,KAACK,EAAM,CAACiG,MAAM,UAASrG,SAAC,aACxBD,EAAAA,EAAAA,KAACK,EAAM,CAACiG,MAAM,YAAWrG,SAAC,eAC1BD,EAAAA,EAAAA,KAACK,EAAM,CAACiG,MAAM,UAASrG,SAAC,kBAI3B3D,EAAQ8C,QACP4D,EAAAA,EAAAA,MAAA,OAAK3D,UAAU,eAAcY,SAAA,EAC3BD,EAAAA,EAAAA,KAAA,SAAAC,SAAO,YACPD,EAAAA,EAAAA,KAACM,EAAAA,QAAM,CACL0F,YAAY,cACZM,MAAOhK,EAAQ+C,gBAAajC,EAC5B6I,SAAWK,GAAUgI,EAAmB,YAAahI,GACrDiJ,YAAU,EACV1I,MAAO,CAAEC,MAAO,KAAM7G,SAlRRb,KAC1B,OAAQA,GACN,IAAK,UACH,MAAO,CAAC,IAAK,IAAK,IAAK,IAAK,KAC9B,IAAK,YACH,MAAO,CAAC,IAAK,IAAK,IAAK,IAAK,KAAM,MACpC,IAAK,UACH,MAAO,CAAC,KAAM,MAChB,QACE,MAAO,GACX,EA0Qa+G,CAAmB7J,EAAQ8C,OAAOsH,KAAID,IACrCzD,EAAAA,EAAAA,MAAC3C,EAAM,CAAWiG,MAAOG,EAAIxG,SAAA,CAAC,SAAOwG,IAAxBA,UAMpBnK,EAAQ8C,QACP4D,EAAAA,EAAAA,MAAA,OAAK3D,UAAU,eAAcY,SAAA,EAC3BD,EAAAA,EAAAA,KAAA,SAAAC,SAAO,cACPD,EAAAA,EAAAA,KAACM,EAAAA,QAAM,CACL0F,YAAY,eACZM,MAAOhK,EAAQgD,cAAWlC,EAC1B6I,SAAWK,GAAUgI,EAAmB,UAAWhI,GACnDiJ,YAAU,EACV1I,MAAO,CAAEC,MAAO,KAAM7G,SAjTPb,KAC3B,OAAQA,GACN,IAAK,UACH,OAAOc,EAAAA,GACT,IAAK,YACH,OAAOC,EAAAA,GACT,IAAK,UACH,OAAOC,EAAAA,GACT,QACE,MAAO,GACX,EAySa8F,CAAoB5J,EAAQ8C,OAAOsH,KAAIpH,IACtCU,EAAAA,EAAAA,KAACK,EAAM,CAAeiG,MAAOhH,EAAQW,SAAEX,GAA1BA,WAMrB0D,EAAAA,EAAAA,MAAA,OAAK3D,UAAU,eAAcY,SAAA,EAC3BD,EAAAA,EAAAA,KAAA,SAAAC,SAAO,aACPD,EAAAA,EAAAA,KAAC6N,EAAM,CACL7H,YAAY,sBACZM,MAAO6H,EACPlI,SAAWuB,GAAM4G,EAAc5G,EAAEgI,OAAOlJ,OACxCO,MAAO,CAAEC,MAAO,KAChByI,YAAU,aAOlBvP,EAAAA,EAAAA,KAAA,OAAKX,UAAU,kBAAiBY,UAC9BD,EAAAA,EAAAA,KAACwM,EAAAA,EAAK,CACJjC,QAASA,EACTkC,WAAYgC,EACZ/B,OAAO,MACP5L,QAASA,EACT6L,WAAY,CACVC,SAAU,GACVC,iBAAiB,EACjBC,iBAAiB,EACjBC,UAAYjP,GAAK,SAAAQ,OAAcR,EAAK,eAEtCkP,OAAQ,CAAEC,EAAG,WAKvB,GC3WQ5M,OAAO,GAAIC,EAAAA,QAihBnB,QA/gBA,SAA8Bd,GAAqC,IAApC,SAAEkP,EAAQ,UAAEnO,EAAS,SAAEC,GAAUhB,EAC9D,MAAOiB,GAAQC,EAAAA,EAAKC,UACdC,GAAWC,EAAAA,EAAAA,OACVC,EAASC,IAAcnB,EAAAA,EAAAA,WAAS,IAChCoB,EAAUC,IAAerB,EAAAA,EAAAA,UAAS,KAClCsB,EAAeC,IAAoBvB,EAAAA,EAAAA,UAAS,KAC5C4C,EAA2BC,IAAgC7C,EAAAA,EAAAA,UAAS,KACpE0B,EAAcC,IAAmB3B,EAAAA,EAAAA,UAAS,WAiB3CuG,EAAsB/G,IAC1B,OAAQA,GACN,IAAK,UACH,MAAO,CAAC,IAAK,IAAK,IAAK,IAAK,KAC9B,IAAK,YACH,MAAO,CAAC,IAAK,IAAK,IAAK,IAAK,KAAM,MACpC,IAAK,UACH,MAAO,CAAC,KAAM,MAChB,QACE,MAAO,GACX,GAIFS,EAAAA,EAAAA,YAAU,KACJ6O,IACFjO,EAAK2F,eAAe,CAClBhH,MAAOsP,EAAStP,MAChBC,UAAWqP,EAASrP,UACpBC,QAASoP,EAASpP,QAClBG,MAAOiP,EAASjP,MAChBqP,KAAMJ,EAASI,KACf9C,QAAS0C,EAAS1C,QAClBH,SAAU6C,EAAS7C,SACnB4D,aAAcf,EAASgB,YAIH,WAAlBhB,EAAS1H,MAAqB0H,EAAS3K,mBACzCtB,EAA6BiM,EAAS3K,mBAIlB,WAAlB2K,EAAS1H,OACP0H,EAAS1C,UAAY0C,EAAS7C,SAChCtK,EAAgB,WACPmN,EAAS7C,UAClBtK,EAAgB,UAGtB,GACC,CAACmN,EAAUjO,IAGd,MA4GMsC,EAAmBA,KACvB,OAAgB,OAAR2L,QAAQ,IAARA,OAAQ,EAARA,EAAU1H,MAChB,IAAK,SACH,MAAO,QACT,IAAK,cACH,MAAO,aACT,IAAK,cACH,MAAO,aACT,IAAK,QACH,MAAO,OACT,QACE,MAAO,WACX,EAoBI2I,EAAsB,CAC1BhN,aAAemF,IAIb,KAHkC,oBAAdA,EAAKd,MACNc,EAAKd,KAAKe,WAAW,iBACrBD,EAAKd,KAAKe,WAAW,UAGtC,OADAjL,EAAAA,GAAQP,MAAM,wCACP,EAGT,OADgBuL,EAAKzB,KAAO,KAAO,KAAO,IAK1CpF,EAAY,CAAC6G,KACN,IAJLhL,EAAAA,GAAQP,MAAM,mCACP,EAGG,EAEdgQ,SAAUA,KACRtL,EAAY,GAAG,GAKb6B,EAAuB,CAC3BH,aAAemF,IAEb,IADgBA,EAAKd,KAAKe,WAAW,UAGnC,OADAjL,EAAAA,GAAQP,MAAM,gCACP,EAGT,OADeuL,EAAKzB,KAAO,KAAO,KAAO,GAKzClF,EAAiB,CAAC2G,KACX,IAJLhL,EAAAA,GAAQP,MAAM,mCACP,EAGG,EAEdgQ,SAAUA,KACRpL,EAAiB,GAAG,GAIxB,OAAKuN,GAKH1L,EAAAA,EAAAA,MAAA,OAAK3D,UAAU,2BAA0BY,SAAA,EACvCD,EAAAA,EAAAA,KAAA,OAAKX,UAAU,cAAaY,UAC1B+C,EAAAA,EAAAA,MAAA,OAAK3D,UAAU,iBAAgBY,SAAA,CAlEbgD,MACtB,OAAgB,OAARyL,QAAQ,IAARA,OAAQ,EAARA,EAAU1H,MAChB,IAAK,SACH,OAAOhH,EAAAA,EAAAA,KAACkD,EAAAA,IAAO,CAAC7D,UAAU,cAC5B,IAAK,cAML,QACE,OAAOW,EAAAA,EAAAA,KAACmD,EAAAA,IAAS,CAAC9D,UAAU,cAL9B,IAAK,cACH,OAAOW,EAAAA,EAAAA,KAACoD,EAAAA,IAAe,CAAC/D,UAAU,cACpC,IAAK,QACH,OAAOW,EAAAA,EAAAA,KAACqD,EAAAA,IAAM,CAAChE,UAAU,cAG7B,EAuDO4D,IACDD,EAAAA,EAAAA,MAAA,OAAA/C,SAAA,EACE+C,EAAAA,EAAAA,MAAA,MAAA/C,SAAA,CAAI,QAAM8C,QACVC,EAAAA,EAAAA,MAAA,KAAA/C,SAAA,CAAG,0BAAwByO,EAASjP,MAAM,gBAKhDuD,EAAAA,EAAAA,MAACtC,EAAAA,EAAI,CACHD,KAAMA,EACN6C,OAAO,WACPC,SArLenH,UACnB,IAIE,IAAII,EAEJ,GALAuE,GAAW,GACXH,GAAS6C,EAAAA,EAAAA,OAIa,WAAlBiL,EAAS1H,KAAmB,CAE9B,MAAMpD,GAAWC,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACZC,GAAM,IACTC,kBAAmBvB,IAGrB,GAAqB,YAAjBlB,EAEF9E,QAAiB2B,EAAAA,EAAAA,IAAYuQ,EAASvC,IAAKvI,QACtC,GAAqB,UAAjBtC,EAET,GAAIJ,EAAc/D,OAAS,GAAK+D,EAAc,GAAG8C,cAAe,CAC9D,MAAMhG,EAAW,IAAIV,SACrB2G,OAAOC,KAAKN,GAAaO,SAAQC,IACnB,sBAARA,EACFpG,EAASmB,OAAOiF,EAAKwL,KAAKC,UAAUjM,EAAYQ,KAEhDpG,EAASmB,OAAOiF,EAAKR,EAAYQ,GACnC,IAEFpG,EAASmB,OAAO,YAAa+B,EAAc,GAAG8C,eAC9CxH,QAAiB2B,EAAAA,EAAAA,IAAYuQ,EAASvC,IAAKnO,EAC7C,MACExB,QAAiB2B,EAAAA,EAAAA,IAAYuQ,EAASvC,IAAKvI,EAGjD,KAAO,CAEL,MAAM5F,EAAW,IAAIV,SAmBrB,OAjBA2G,OAAOC,KAAKJ,GAAQK,SAAQC,SACNhH,IAAhB0G,EAAOM,IAAsC,OAAhBN,EAAOM,IACtCpG,EAASmB,OAAOiF,EAAKN,EAAOM,GAC9B,IAIEpD,EAAS7D,OAAS,GAAK6D,EAAS,GAAGgD,eACrChG,EAASmB,OAAO,WAAY6B,EAAS,GAAGgD,eAIpB,UAAlB0K,EAAS1H,MAAoB9F,EAAc/D,OAAS,GAAK+D,EAAc,GAAG8C,eAC5EhG,EAASmB,OAAO,YAAa+B,EAAc,GAAG8C,eAIxC0K,EAAS1H,MACf,IAAK,cACHxK,QAAiB+B,EAAAA,EAAAA,IAAWmQ,EAASvC,IAAKnO,GAC1C,MACF,IAAK,cACHxB,QAAiBgC,EAAAA,EAAAA,IAAgBkQ,EAASvC,IAAKnO,GAC/C,MACF,IAAK,QACHxB,QAAiBiC,EAAAA,EAAAA,IAAWiQ,EAASvC,IAAKnO,GAC1C,MACF,QACE,MAAM,IAAIiH,MAAM,yBAEtB,CAEA,GAAwB,MAApBzI,EAAS0I,QAAkB1I,EAASG,KAAKE,QAC3CC,EAAAA,GAAQD,QAAQL,EAASG,KAAKG,SAC9ByD,EAAUmO,EAAS1H,UACd,CAAC,IAAD5B,EACL,MAAMC,GAA4B,QAAbD,EAAA5I,EAASG,YAAI,IAAAyI,OAAA,EAAbA,EAAetI,UAAW,4BAC/CA,EAAAA,GAAQP,MAAM8I,EAChB,CACF,CAAE,MAAO9I,GACPiI,QAAQjI,MAAM,2BAA4BA,GAC1CO,EAAAA,GAAQP,MAAM,4BAChB,CAAC,QACCwE,GAAW,GACXH,GAAS+C,EAAAA,EAAAA,MACX,GAmGItE,UAAU,gBAAeY,SAAA,EAEzB+C,EAAAA,EAAAA,MAAA,OAAK3D,UAAU,WAAUY,SAAA,EACvBD,EAAAA,EAAAA,KAACU,EAAAA,EAAKkF,KAAI,CACRC,MAAM,QACNnB,KAAK,QACLoB,MAAO,CAAC,CAAEC,UAAU,EAAMjJ,QAAS,0BACnCuC,UAAU,iBAAgBY,UAE1B+C,EAAAA,EAAAA,MAAC1C,EAAAA,QAAM,CACL0F,YAAY,eACZC,SAtNe7G,IACzBqB,EAAK2F,eAAe,CAAE/G,UAAW,GAAIC,QAAS,KAC9CmD,EAA6B,GAAG,EAqNtB4D,KAAK,QAAOpG,SAAA,EAEZD,EAAAA,EAAAA,KAACK,EAAM,CAACiG,MAAM,UAASrG,SAAC,aACxBD,EAAAA,EAAAA,KAACK,EAAM,CAACiG,MAAM,YAAWrG,SAAC,eAC1BD,EAAAA,EAAAA,KAACK,EAAM,CAACiG,MAAM,UAASrG,SAAC,kBAI5BD,EAAAA,EAAAA,KAACU,EAAAA,EAAKkF,KAAI,CACRC,MAAM,QACNnB,KAAK,YACLoB,MAAO,CAAC,CAAEC,UAAU,EAAMjJ,QAAS,0BACnCuC,UAAU,iBAAgBY,UAE1BD,EAAAA,EAAAA,KAACM,EAAAA,QAAM,CACL0F,YAAY,eACZK,KAAK,QACL2C,UAAWvI,EAAKsG,cAAc,SAAS9G,SAEtCkG,EAAmB1F,EAAKsG,cAAc,UAAUL,KAAID,IACnDzD,EAAAA,EAAAA,MAAC3C,EAAM,CAAWiG,MAAOG,EAAIxG,SAAA,CAAC,SAAOwG,IAAxBA,aAMrBzG,EAAAA,EAAAA,KAACU,EAAAA,EAAKkF,KAAI,CACRC,MAAM,UACNnB,KAAK,UACLoB,MAAO,CAAC,CAAEC,UAAU,EAAMjJ,QAAS,4BAA6BmD,UAEhED,EAAAA,EAAAA,KAACM,EAAAA,QAAM,CACL0F,YAAY,iBACZK,KAAK,QACL2C,UAAWvI,EAAKsG,cAAc,SAAS9G,SAnTpBb,KAC3B,OAAQA,GACN,IAAK,UACH,OAAOc,EAAAA,GACT,IAAK,YACH,OAAOC,EAAAA,GACT,IAAK,UACH,OAAOC,EAAAA,GACT,QACE,MAAO,GACX,EA2SS8F,CAAoBzF,EAAKsG,cAAc,UAAUL,KAAIpH,IACpDU,EAAAA,EAAAA,KAACK,EAAM,CAAeiG,MAAOhH,EAAQW,SAAEX,GAA1BA,SAMA,WAAlBoP,EAAS1H,OACRhE,EAAAA,EAAAA,MAACtC,EAAAA,EAAKkF,KAAI,CACRC,MAAM,gCACNxG,UAAU,6BAA4BY,SAAA,EAEtCD,EAAAA,EAAAA,KAACM,EAAAA,QAAM,CACLqG,KAAK,WACLX,YAAY,uDACZM,MAAO9D,EACPyD,SArQ2BW,IACrCnE,EAA6BmE,EAAQ,EAqQ3BP,KAAK,QACL2C,UAAWvI,EAAKsG,cAAc,aAAa9G,SAlQjB6P,MACpC,MAAMC,EAAetP,EAAKsG,cAAc,SAClCiJ,EAAevP,EAAKsG,cAAc,aAExC,OAAKgJ,GAAiBC,EAEf7J,EAAmB4J,GAAcvJ,QAAOC,GAAOA,IAAQuJ,IAFnB,EAEgC,EA8PhEF,GAAgCpJ,KAAID,IACnCzD,EAAAA,EAAAA,MAAC3C,EAAM,CAAWiG,MAAOG,EAAIxG,SAAA,CAAC,SAAOwG,IAAxBA,QAGjBzG,EAAAA,EAAAA,KAAA,OAAKX,UAAU,0BAAyBY,UACtCD,EAAAA,EAAAA,KAAA,SAAAC,SAAO,kGAKbD,EAAAA,EAAAA,KAACU,EAAAA,EAAKkF,KAAI,CACRC,MAAM,QACNnB,KAAK,QACLoB,MAAO,CAAC,CAAEC,UAAU,EAAMjJ,QAAS,yBAA0BmD,UAE7DD,EAAAA,EAAAA,KAAA,SACEgH,KAAK,OACLhB,YAAW,SAAA1H,OAAWyE,IAAmBkE,cAAa,UACtD5H,UAAU,kBAIM,gBAAlBqP,EAAS1H,MAA4C,UAAlB0H,EAAS1H,QAC5ChH,EAAAA,EAAAA,KAACU,EAAAA,EAAKkF,KAAI,CACRC,MAAM,OACNnB,KAAK,OACLoB,MAAO,CAAC,CAAEC,UAAU,EAAMjJ,QAAS,0BAA2BmD,UAE9DD,EAAAA,EAAAA,KAAA,SACEgH,KAAK,OACLhB,YAAY,0BACZ3G,UAAU,iBAMG,WAAlBqP,EAAS1H,OACRhE,EAAAA,EAAAA,MAAAkE,EAAAA,SAAA,CAAAjH,SAAA,EACE+C,EAAAA,EAAAA,MAAA,OAAK3D,UAAU,wBAAuBY,SAAA,EACpCD,EAAAA,EAAAA,KAAA,SAAOX,UAAU,gBAAeY,SAAC,kBACjC+C,EAAAA,EAAAA,MAAA,OAAK3D,UAAU,wBAAuBY,SAAA,EACpC+C,EAAAA,EAAAA,MAAA,OACE3D,UAAS,iBAAAf,OAAoC,YAAjBgD,EAA6B,SAAW,IACpE6F,QAASA,IAAM5F,EAAgB,WAAWtB,SAAA,EAE1CD,EAAAA,EAAAA,KAACkD,EAAAA,IAAO,CAAC7D,UAAU,iBACnBW,EAAAA,EAAAA,KAAA,QAAAC,SAAM,sBAER+C,EAAAA,EAAAA,MAAA,OACE3D,UAAS,iBAAAf,OAAoC,UAAjBgD,EAA2B,SAAW,IAClE6F,QAASA,IAAM5F,EAAgB,SAAStB,SAAA,EAExCD,EAAAA,EAAAA,KAACoH,EAAAA,IAAgB,CAAC/H,UAAU,iBAC5BW,EAAAA,EAAAA,KAAA,QAAAC,SAAM,oBAKM,YAAjBqB,IACCtB,EAAAA,EAAAA,KAACU,EAAAA,EAAKkF,KAAI,CACRC,MAAM,mBACNnB,KAAK,UACLoB,MAAO,CAAC,CAAEC,UAAU,EAAMjJ,QAAS,kCAAmCmD,UAEtED,EAAAA,EAAAA,KAAA,SACEgH,KAAK,OACLhB,YAAY,6CACZ3G,UAAU,iBAKE,UAAjBiC,IACC0B,EAAAA,EAAAA,MAAAkE,EAAAA,SAAA,CAAAjH,SAAA,EACED,EAAAA,EAAAA,KAACU,EAAAA,EAAKkF,KAAI,CACRC,MAAM,eACNnB,KAAK,WACLoB,MAAO,CAAC,CAAEC,UAAU,EAAMjJ,QAAS,8BAA+BmD,UAElED,EAAAA,EAAAA,KAAA,SACEgH,KAAK,MACLhB,YAAY,qBACZ3G,UAAU,kBAIdW,EAAAA,EAAAA,KAACU,EAAAA,EAAKkF,KAAI,CACRC,MAAM,2BACNnB,KAAK,eAAczE,UAEnBD,EAAAA,EAAAA,KAAA,SACEgH,KAAK,MACLhB,YAAY,sCACZ3G,UAAU,kBAIdW,EAAAA,EAAAA,KAACU,EAAAA,EAAKkF,KAAI,CACRC,MAAM,kCACNxG,UAAU,iBAAgBY,UAE1BD,EAAAA,EAAAA,KAACqH,EAAAA,GAAMxD,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACDf,GAAoB,IACxB9B,SAAUE,EACV+E,SAAUqB,IAAA,IAAC,SAAEtG,GAAUsG,EAAA,OAAKnG,EAAiBH,EAAS,EACtD3B,UAAU,mBAAkBY,UAE5B+C,EAAAA,EAAAA,MAAA,OAAK3D,UAAU,oBAAmBY,SAAA,EAChCD,EAAAA,EAAAA,KAACuI,EAAAA,IAAQ,CAAClJ,UAAU,iBACpBW,EAAAA,EAAAA,KAAA,KAAAC,SAAG,8CACHD,EAAAA,EAAAA,KAAA,KAAGX,UAAU,cAAaY,SAAC,oDAUtB,WAAlByO,EAAS1H,OACRhH,EAAAA,EAAAA,KAACU,EAAAA,EAAKkF,KAAI,CACRC,MAAM,iCACNxG,UAAU,iBAAgBY,UAE1BD,EAAAA,EAAAA,KAACqH,EAAAA,GAAMxD,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACD8L,GAAmB,IACvB3O,SAAUA,EACViF,SAAUuC,IAAA,IAAC,SAAExH,GAAUwH,EAAA,OAAKvH,EAAYD,EAAS,EACjD3B,UAAU,kBAAiBY,UAE3B+C,EAAAA,EAAAA,MAAA,OAAK3D,UAAU,cAAaY,SAAA,EAC1BD,EAAAA,EAAAA,KAACuI,EAAAA,IAAQ,CAAClJ,UAAU,iBACpBW,EAAAA,EAAAA,KAAA,KAAAC,SAAG,+CACHD,EAAAA,EAAAA,KAAA,KAAGX,UAAU,cAAaY,SAAC,iDAOhB,UAAlByO,EAAS1H,OACRhH,EAAAA,EAAAA,KAACU,EAAAA,EAAKkF,KAAI,CACRC,MAAM,kCACNxG,UAAU,iBAAgBY,UAE1BD,EAAAA,EAAAA,KAACqH,EAAAA,GAAMxD,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACDf,GAAoB,IACxB9B,SAAUE,EACV+E,SAAUwC,IAAA,IAAC,SAAEzH,GAAUyH,EAAA,OAAKtH,EAAiBH,EAAS,EACtD3B,UAAU,mBAAkBY,UAE5B+C,EAAAA,EAAAA,MAAA,OAAK3D,UAAU,oBAAmBY,SAAA,EAChCD,EAAAA,EAAAA,KAACuI,EAAAA,IAAQ,CAAClJ,UAAU,iBACpBW,EAAAA,EAAAA,KAAA,KAAAC,SAAG,2BACHD,EAAAA,EAAAA,KAAA,KAAGX,UAAU,cAAaY,SAAC,mDAMnC+C,EAAAA,EAAAA,MAAA,OAAK3D,UAAU,eAAcY,SAAA,EAC3BD,EAAAA,EAAAA,KAAC+I,EAAAA,GAAM,CACL/B,KAAK,UACLX,KAAK,QACLc,QAAS3G,EACTnB,UAAU,gBACV+L,MAAMpL,EAAAA,EAAAA,KAACiQ,EAAAA,IAAO,IAAIhQ,SACnB,YAGD+C,EAAAA,EAAAA,MAAC+F,EAAAA,GAAM,CACL/B,KAAK,UACLiC,SAAS,SACT5C,KAAK,QACLvF,QAASA,EACTzB,UAAU,gBACV+L,MAAMpL,EAAAA,EAAAA,KAACkQ,EAAAA,IAAM,IAAIjQ,SAAA,CAClB,UACS8C,iBA3QT/C,EAAAA,EAAAA,KAAA,OAAAC,SAAK,oCAiRhB,EC9QA,QAlQA,WAAgC,IAADkQ,EAC7B,MAAMC,GAAWC,EAAAA,EAAAA,OACVC,EAAsBC,IAA2B3Q,EAAAA,EAAAA,UAAS,KAC1D4Q,EAAaC,IAAkB7Q,EAAAA,EAAAA,WAAS,IACxC8Q,EAAqBC,IAA0B/Q,EAAAA,EAAAA,WAAS,IACxDgR,EAAqBC,IAA0BjR,EAAAA,EAAAA,WAAS,IACxDkR,EAAcC,IAAmBnR,EAAAA,EAAAA,WAAS,IAC1CoR,EAAkBC,IAAuBrR,EAAAA,EAAAA,UAAS,MAEnDsR,EAAgB,CACpB,CACE9M,IAAK,SACL3E,MAAO,SACP2L,MAAMpL,EAAAA,EAAAA,KAACkD,EAAAA,IAAO,IACdiO,YAAa,sCACbrG,MAAO,WAET,CACE1G,IAAK,cACL3E,MAAO,cACP2L,MAAMpL,EAAAA,EAAAA,KAACmD,EAAAA,IAAS,IAChBgO,YAAa,mCACbrG,MAAO,WAET,CACE1G,IAAK,cACL3E,MAAO,cACP2L,MAAMpL,EAAAA,EAAAA,KAACoD,EAAAA,IAAe,IACtB+N,YAAa,8BACbrG,MAAO,WAET,CACE1G,IAAK,QACL3E,MAAO,QACP2L,MAAMpL,EAAAA,EAAAA,KAACqD,EAAAA,IAAM,IACb8N,YAAa,2CACbrG,MAAO,YAILsG,EAAoB,CACxB,CACEhN,IAAK,mBACL3E,MAAO,mBACP2L,MAAMpL,EAAAA,EAAAA,KAACqR,EAAAA,IAAK,IACZF,YAAa,6CACbrG,MAAO,WAET,CACE1G,IAAK,YACL3E,MAAO,sBACP2L,MAAMpL,EAAAA,EAAAA,KAACsR,EAAAA,IAAkB,IACzBH,YAAa,sCACbrG,MAAO,YAiBLyG,EAAkBA,KACtBd,GAAe,GACfF,EAAwB,GAAG,EAgBvBiB,EAAsBA,KAC1BT,GAAgB,GAChBE,EAAoB,KAAK,EAmB3B,OACEjO,EAAAA,EAAAA,MAAA,OAAK3D,UAAU,wBAAuBY,SAAA,EACpC+C,EAAAA,EAAAA,MAAA,OAAK3D,UAAU,+BAA8BY,SAAA,EAE3C+C,EAAAA,EAAAA,MAACyO,EAAAA,EAAOC,OAAM,CACZC,WAAY,CAAEC,MAAO,MACrBC,SAAU,CAAED,MAAO,KACnBzK,QAASA,IAAMiJ,EAAS,oBACxB/Q,UAAU,iIAAgIY,SAAA,EAE1ID,EAAAA,EAAAA,KAAC8R,EAAAA,IAAW,CAACzS,UAAU,aACvBW,EAAAA,EAAAA,KAAA,QAAMX,UAAU,uCAAsCY,SAAC,kBAGzDD,EAAAA,EAAAA,KAAC+R,EAAAA,EAAS,CAACtS,MAAM,kCAGlBiR,GACC1N,EAAAA,EAAAA,MAAA,OAAK3D,UAAU,6BAA4BY,SAAA,EACzC+C,EAAAA,EAAAA,MAAA,OAAK3D,UAAU,cAAaY,SAAA,EAC1BD,EAAAA,EAAAA,KAAA,UACEX,UAAU,cACV8H,QAxDuB6K,KACjCrB,GAAuB,EAAM,EAuDiB1Q,SACrC,mCAGDD,EAAAA,EAAAA,KAAA,MAAAC,SAAI,4BAEND,EAAAA,EAAAA,KAACkJ,EAAe,OAEhB0H,GACF5N,EAAAA,EAAAA,MAAA,OAAK3D,UAAU,6BAA4BY,SAAA,EACzC+C,EAAAA,EAAAA,MAAA,OAAK3D,UAAU,cAAaY,SAAA,EAC1BD,EAAAA,EAAAA,KAAA,UACEX,UAAU,cACV8H,QAjEuB8K,KACjCpB,GAAuB,EAAM,EAgEiB5Q,SACrC,8BAGDD,EAAAA,EAAAA,KAAA,MAAAC,SAAI,mCAEND,EAAAA,EAAAA,KAACkS,EAAoB,CAACnE,OAnEFW,IAC1BuC,EAAoBvC,GACpBqC,GAAgB,EAAK,OAmEfD,GACF9N,EAAAA,EAAAA,MAAA,OAAK3D,UAAU,sBAAqBY,SAAA,EAClCD,EAAAA,EAAAA,KAAA,OAAKX,UAAU,cAAaY,UAC1BD,EAAAA,EAAAA,KAAA,UACEX,UAAU,cACV8H,QAASqK,EAAoBvR,SAC9B,qCAIHD,EAAAA,EAAAA,KAACmS,EAAqB,CACpBzD,SAAUsC,EACVzQ,UAjEiBrB,IACzBpC,EAAAA,GAAQD,QAAQ,GAADyB,OAAIY,EAAY,2BAC/B6R,GAAgB,GAChBE,EAAoB,KAGlB,EA4DMzQ,SAAUgR,OAGZhB,GACFxN,EAAAA,EAAAA,MAAA,OAAK3D,UAAU,qBAAoBY,SAAA,EACjC+C,EAAAA,EAAAA,MAAA,OAAK3D,UAAU,cAAaY,SAAA,EAC1BD,EAAAA,EAAAA,KAAA,UACEX,UAAU,cACV8H,QAASoK,EAAgBtR,SAC1B,8BAGD+C,EAAAA,EAAAA,MAAA,MAAA/C,SAAA,CAAI,OAC0D,QAAxDkQ,EAACe,EAAcxD,MAAK0E,GAAKA,EAAEhO,MAAQkM,WAAqB,IAAAH,OAAA,EAAvDA,EAAyD1Q,aAIlEO,EAAAA,EAAAA,KAACqS,EAAoB,CACnBnT,aAAcoR,EACd/P,UA3FiBrB,IACzBpC,EAAAA,GAAQD,QAAQ,GAADyB,OAAIY,EAAY,yBAC/BuR,GAAe,GACfF,EAAwB,GAAG,EAyFnB/P,SAAU+Q,QAIdvO,EAAAA,EAAAA,MAAA,OAAK3D,UAAU,sBAAqBY,SAAA,EAClC+C,EAAAA,EAAAA,MAAA,OAAK3D,UAAU,iBAAgBY,SAAA,EAC7BD,EAAAA,EAAAA,KAAA,MAAAC,SAAI,oCACJD,EAAAA,EAAAA,KAAA,KAAAC,SAAG,kFAGL+C,EAAAA,EAAAA,MAAA,OAAK3D,UAAU,gBAAeY,SAAA,EAC5B+C,EAAAA,EAAAA,MAAA,OAAK3D,UAAU,eAAcY,SAAA,EAC3B+C,EAAAA,EAAAA,MAAA,MAAA/C,SAAA,EACED,EAAAA,EAAAA,KAACsS,EAAAA,IAAM,CAACjT,UAAU,iBAAiB,wBAGrCW,EAAAA,EAAAA,KAAA,KAAAC,SAAG,6CACHD,EAAAA,EAAAA,KAAA,OAAKX,UAAU,sBAAqBY,SACjCiR,EAAcxK,KAAKM,IAClBhE,EAAAA,EAAAA,MAAA,OAEE3D,UAAU,qBACV8H,QAASA,KAAMoL,OAtJCrT,EAsJwB8H,EAAK5C,IArJ7DmM,EAAwBrR,QACxBuR,GAAe,GAFiBvR,KAsJkC,EAClD2H,MAAO,CAAE2L,YAAaxL,EAAK8D,OAAQ7K,SAAA,EAEnCD,EAAAA,EAAAA,KAAA,OAAKX,UAAU,YAAYwH,MAAO,CAAEiE,MAAO9D,EAAK8D,OAAQ7K,SACrD+G,EAAKoE,QAERpL,EAAAA,EAAAA,KAAA,MAAAC,SAAK+G,EAAKvH,SACVO,EAAAA,EAAAA,KAAA,KAAAC,SAAI+G,EAAKmK,eACTnO,EAAAA,EAAAA,MAAA,OAAK3D,UAAU,aAAawH,MAAO,CAAE4L,gBAAiBzL,EAAK8D,OAAQ7K,SAAA,EACjED,EAAAA,EAAAA,KAACsS,EAAAA,IAAM,KACPtP,EAAAA,EAAAA,MAAA,QAAA/C,SAAA,CAAM,OAAK+G,EAAKvH,cAZbuH,EAAK5C,aAmBlBpB,EAAAA,EAAAA,MAAA,OAAK3D,UAAU,eAAcY,SAAA,EAC3B+C,EAAAA,EAAAA,MAAA,MAAA/C,SAAA,EACED,EAAAA,EAAAA,KAAC0S,EAAAA,IAAM,CAACrT,UAAU,iBAAiB,gCAGrCW,EAAAA,EAAAA,KAAA,KAAAC,SAAG,qDACHD,EAAAA,EAAAA,KAAA,OAAKX,UAAU,0BAAyBY,SACrCmR,EAAkB1K,KAAKiM,IACtB3P,EAAAA,EAAAA,MAAA,OAEE3D,UAAU,yBACV8H,QAASA,IA7KWwL,KACrB,cAAXA,EACFhC,GAAuB,GACH,qBAAXgC,GACT9B,GAAuB,EACzB,EAwK+B+B,CAA6BD,EAAOvO,KACnDyC,MAAO,CAAE2L,YAAaG,EAAO7H,OAAQ7K,SAAA,EAErCD,EAAAA,EAAAA,KAAA,OAAKX,UAAU,YAAYwH,MAAO,CAAEiE,MAAO6H,EAAO7H,OAAQ7K,SACvD0S,EAAOvH,QAEVpL,EAAAA,EAAAA,KAAA,MAAAC,SAAK0S,EAAOlT,SACZO,EAAAA,EAAAA,KAAA,KAAAC,SAAI0S,EAAOxB,eACXnO,EAAAA,EAAAA,MAAA,OAAK3D,UAAU,gBAAgBwH,MAAO,CAAE4L,gBAAiBE,EAAO7H,OAAQ7K,SAAA,EACtED,EAAAA,EAAAA,KAACqR,EAAAA,IAAK,KACNrO,EAAAA,EAAAA,MAAA,QAAA/C,SAAA,CAAM,QAAM0S,EAAOlT,cAZhBkT,EAAOvO,oBAuBhC,C", "sources": ["apicalls/study.js", "components/PageTitle.js", "data/Subjects.jsx", "pages/admin/StudyMaterials/AddStudyMaterialForm.js", "apicalls/subtitles.js", "pages/admin/StudyMaterials/SubtitleManager.js", "pages/admin/StudyMaterials/StudyMaterialManager.js", "pages/admin/StudyMaterials/EditStudyMaterialForm.js", "pages/admin/StudyMaterials/index.js"], "sourcesContent": ["const { default: axiosInstance } = require(\".\");\r\n\r\n// get study materials\r\nexport const getStudyMaterial = async (filters) => {\r\n    try {\r\n        const response = await axiosInstance.post(\"/api/study/get-study-content\" , filters);\r\n        return response;\r\n    } catch (error) {\r\n        return error.response;\r\n    }\r\n}\r\n\r\n// get available classes for user's level\r\nexport const getAvailableClasses = async () => {\r\n    try {\r\n        const response = await axiosInstance.post(\"/api/study/get-available-classes\");\r\n        return response;\r\n    } catch (error) {\r\n        return error.response;\r\n    }\r\n}\r\n\r\n// get all videos for admin management\r\nexport const getAllVideos = async () => {\r\n    try {\r\n        const response = await axiosInstance.get(\"/api/study/videos-subtitle-status\");\r\n        return response.data;\r\n    } catch (error) {\r\n        return error.response?.data || { success: false, message: \"Failed to fetch videos\" };\r\n    }\r\n}\r\n\r\n// Add study material functions\r\n\r\n// Add video (supports both JSON data and FormData)\r\nexport const addVideo = async (videoData, onUploadProgress = null) => {\r\n    try {\r\n        const isFormData = videoData instanceof FormData;\r\n        const config = isFormData ? {\r\n            headers: {\r\n                'Content-Type': 'multipart/form-data',\r\n            },\r\n            timeout: 600000, // 10 minutes timeout for large files\r\n            onUploadProgress: onUploadProgress ? (progressEvent) => {\r\n                const percentCompleted = Math.round((progressEvent.loaded * 100) / progressEvent.total);\r\n                // Pass additional information for better progress tracking\r\n                onUploadProgress(percentCompleted, progressEvent.loaded, progressEvent.total);\r\n            } : undefined,\r\n        } : {\r\n            timeout: 60000, // 1 minute for YouTube videos\r\n        };\r\n\r\n        const response = await axiosInstance.post(\"/api/study/add-video\", videoData, config);\r\n        return response;\r\n    } catch (error) {\r\n        return error.response;\r\n    }\r\n}\r\n\r\n// Add note\r\nexport const addNote = async (formData) => {\r\n    try {\r\n        const response = await axiosInstance.post(\"/api/study/add-note\", formData, {\r\n            headers: {\r\n                'Content-Type': 'multipart/form-data',\r\n            },\r\n        });\r\n        return response;\r\n    } catch (error) {\r\n        return error.response;\r\n    }\r\n}\r\n\r\n// Add past paper\r\nexport const addPastPaper = async (formData) => {\r\n    try {\r\n        const response = await axiosInstance.post(\"/api/study/add-past-paper\", formData, {\r\n            headers: {\r\n                'Content-Type': 'multipart/form-data',\r\n            },\r\n        });\r\n        return response;\r\n    } catch (error) {\r\n        return error.response;\r\n    }\r\n}\r\n\r\n// Add book\r\nexport const addBook = async (formData) => {\r\n    try {\r\n        const response = await axiosInstance.post(\"/api/study/add-book\", formData, {\r\n            headers: {\r\n                'Content-Type': 'multipart/form-data',\r\n            },\r\n        });\r\n        return response;\r\n    } catch (error) {\r\n        return error.response;\r\n    }\r\n}\r\n\r\n// Update study material functions\r\n\r\n// Update video\r\nexport const updateVideo = async (id, videoData) => {\r\n    try {\r\n        let config = {\r\n            headers: {\r\n                'Content-Type': 'application/json',\r\n            },\r\n        };\r\n\r\n        // If videoData is FormData (contains file uploads), change content type\r\n        if (videoData instanceof FormData) {\r\n            config.headers['Content-Type'] = 'multipart/form-data';\r\n        }\r\n\r\n        const response = await axiosInstance.put(`/api/study/update-video/${id}`, videoData, config);\r\n        return response;\r\n    } catch (error) {\r\n        return error.response;\r\n    }\r\n}\r\n\r\n// Update note\r\nexport const updateNote = async (id, formData) => {\r\n    try {\r\n        const response = await axiosInstance.put(`/api/study/update-note/${id}`, formData, {\r\n            headers: {\r\n                'Content-Type': 'multipart/form-data',\r\n            },\r\n        });\r\n        return response;\r\n    } catch (error) {\r\n        return error.response;\r\n    }\r\n}\r\n\r\n// Update past paper\r\nexport const updatePastPaper = async (id, formData) => {\r\n    try {\r\n        const response = await axiosInstance.put(`/api/study/update-past-paper/${id}`, formData, {\r\n            headers: {\r\n                'Content-Type': 'multipart/form-data',\r\n            },\r\n        });\r\n        return response;\r\n    } catch (error) {\r\n        return error.response;\r\n    }\r\n}\r\n\r\n// Update book\r\nexport const updateBook = async (id, formData) => {\r\n    try {\r\n        const response = await axiosInstance.put(`/api/study/update-book/${id}`, formData, {\r\n            headers: {\r\n                'Content-Type': 'multipart/form-data',\r\n            },\r\n        });\r\n        return response;\r\n    } catch (error) {\r\n        return error.response;\r\n    }\r\n}\r\n\r\n// Delete study material functions\r\n\r\n// Delete video\r\nexport const deleteVideo = async (id) => {\r\n    try {\r\n        const response = await axiosInstance.delete(`/api/study/delete-video/${id}`);\r\n        return response;\r\n    } catch (error) {\r\n        return error.response;\r\n    }\r\n}\r\n\r\n// Delete note\r\nexport const deleteNote = async (id) => {\r\n    try {\r\n        const response = await axiosInstance.delete(`/api/study/delete-note/${id}`);\r\n        return response;\r\n    } catch (error) {\r\n        return error.response;\r\n    }\r\n}\r\n\r\n// Delete past paper\r\nexport const deletePastPaper = async (id) => {\r\n    try {\r\n        const response = await axiosInstance.delete(`/api/study/delete-past-paper/${id}`);\r\n        return response;\r\n    } catch (error) {\r\n        return error.response;\r\n    }\r\n}\r\n\r\n// Delete book\r\nexport const deleteBook = async (id) => {\r\n    try {\r\n        const response = await axiosInstance.delete(`/api/study/delete-book/${id}`);\r\n        return response;\r\n    } catch (error) {\r\n        return error.response;\r\n    }\r\n}\r\n\r\n// Get all study materials for admin management\r\nexport const getAllStudyMaterials = async (filters = {}) => {\r\n    try {\r\n        const params = new URLSearchParams();\r\n        if (filters.materialType) params.append('materialType', filters.materialType);\r\n        if (filters.level) params.append('level', filters.level);\r\n        if (filters.className) params.append('className', filters.className);\r\n        if (filters.subject) params.append('subject', filters.subject);\r\n\r\n        const response = await axiosInstance.get(`/api/study/admin/all-materials?${params.toString()}`);\r\n        return response;\r\n    } catch (error) {\r\n        return error.response;\r\n    }\r\n}\r\n", "import React, { useState, useEffect } from 'react'\r\n\r\nfunction PageTitle({ title }) {\r\n  const [isMobile, setIsMobile] = useState(false);\r\n  useEffect(() => {\r\n    if (window.innerWidth < 768) {\r\n      setIsMobile(true);\r\n    }\r\n  }, []);\r\n  return (\r\n    <div className='mt-2'>\r\n      <h1 className={isMobile ? 'text-lg' : ''}>{title}</h1>\r\n    </div>\r\n  )\r\n}\r\n\r\nexport default PageTitle", "export const primarySubjects = [\r\n  \"Mathematics\",\r\n  \"Science and Technology\",\r\n  \"Geography\",\r\n  \"Kiswahili\",\r\n  \"SocialStudies\",\r\n  \"English\",\r\n  \"Religion\",\r\n  \"Arithmetic\",\r\n  \"Sport and Art\",\r\n  \"Health and Environment\",\r\n  \"Civic and Moral\",\r\n  \"French\",\r\n  \"Historia ya Tanzania\",\r\n];\r\n\r\nexport const secondarySubjects = [\r\n  \"Physics\",\r\n  \"Mathematics\",\r\n  \"Biology\",\r\n  \"Chemistry\",\r\n  \"Geography\",\r\n  \"Civics\",\r\n  \"History\",\r\n  \"Computer\",\r\n  \"English language\",\r\n  \"Kiswahili\",\r\n  \"Commerce\",\r\n  \"Economics\",\r\n  \"Advanced Mathematics\",\r\n  \"Basic Applied Mathematics\",\r\n  \"Accountancy\",\r\n  \"Agriculture\",\r\n];\r\n\r\nexport const advanceSubjects = [\r\n  \"Physics\",\r\n  \"Mathematics\",\r\n  \"Biology\",\r\n  \"Chemistry\",\r\n  \"Geography\",\r\n  \"Civics\",\r\n  \"History\",\r\n  \"Computer\",\r\n  \"English language\",\r\n  \"Kiswahili\",\r\n  \"Commerce\",\r\n  \"Economics\",\r\n  \"Advanced Mathematics\",\r\n  \"Basic Applied Mathematics\",\r\n  \"Accountancy\",\r\n  \"Agriculture\",\r\n];", "import React, { useState } from \"react\";\nimport { Form, message, Select, Upload, Button } from \"antd\";\nimport { useDispatch } from \"react-redux\";\nimport { HideLoading, ShowLoading } from \"../../../redux/loaderSlice\";\nimport { addVideo, addNote, addPastPaper, addBook } from \"../../../apicalls/study\";\nimport { primarySubjects, secondarySubjects, advanceSubjects } from \"../../../data/Subjects\";\nimport {\n  FaUpload,\n  FaVideo,\n  FaFileAlt,\n  FaBook,\n  FaGraduationCap,\n  FaCloudUploadAlt\n} from \"react-icons/fa\";\nimport \"./AddStudyMaterialForm.css\";\n\nconst { Option } = Select;\n\nfunction AddStudyMaterialForm({ materialType, onSuccess, onCancel }) {\n  const [form] = Form.useForm();\n  const dispatch = useDispatch();\n  const [loading, setLoading] = useState(false);\n  const [fileList, setFileList] = useState([]);\n  const [thumbnailList, setThumbnailList] = useState([]);\n  const [videoFileList, setVideoFileList] = useState([]);\n  const [uploadMethod, setUploadMethod] = useState(\"youtube\"); // \"youtube\", \"upload\", or \"s3url\"\n  const [uploadProgress, setUploadProgress] = useState(0);\n  const [uploadStatus, setUploadStatus] = useState(\"\"); // \"uploading\", \"processing\", \"complete\"\n  const [uploadSpeed, setUploadSpeed] = useState(0);\n  const [estimatedTime, setEstimatedTime] = useState(0);\n  const [uploadStartTime, setUploadStartTime] = useState(null);\n  const [isDragOver, setIsDragOver] = useState(false);\n\n  // Get subjects based on level\n  const getSubjectsForLevel = (level) => {\n    switch (level) {\n      case \"primary\":\n        return primarySubjects;\n      case \"secondary\":\n        return secondarySubjects;\n      case \"advance\":\n        return advanceSubjects;\n      default:\n        return [];\n    }\n  };\n\n  // Get classes based on level\n  const getClassesForLevel = (level) => {\n    switch (level) {\n      case \"primary\":\n        return [\"1\", \"2\", \"3\", \"4\", \"5\", \"6\", \"7\"];\n      case \"secondary\":\n        return [\"1\", \"2\", \"3\", \"4\"];\n      case \"advance\":\n        return [\"5\", \"6\"];\n      default:\n        return [];\n    }\n  };\n\n  const [availableSubjects, setAvailableSubjects] = useState(primarySubjects);\n  const [availableClasses, setAvailableClasses] = useState([\"1\", \"2\", \"3\", \"4\", \"5\", \"6\", \"7\"]);\n  const [selectedAdditionalClasses, setSelectedAdditionalClasses] = useState([]);\n\n  const handleLevelChange = (level) => {\n    setAvailableSubjects(getSubjectsForLevel(level));\n    setAvailableClasses(getClassesForLevel(level));\n    setSelectedAdditionalClasses([]); // Reset additional classes when level changes\n    form.setFieldsValue({ subject: undefined, className: undefined });\n  };\n\n  const handleAdditionalClassesChange = (classes) => {\n    setSelectedAdditionalClasses(classes);\n  };\n\n  const handleCoreClassChange = (value) => {\n    // Remove the newly selected core class from additional classes if it's there\n    const filteredAdditionalClasses = selectedAdditionalClasses.filter(cls => cls !== value);\n    setSelectedAdditionalClasses(filteredAdditionalClasses);\n  };\n\n  const handleSubmit = async (values) => {\n    let timeoutId;\n    try {\n      setLoading(true);\n      setUploadProgress(0);\n      setUploadStatus(\"\");\n      dispatch(ShowLoading());\n\n      // Set a timeout to prevent infinite loading\n      timeoutId = setTimeout(() => {\n        if (uploadProgress === 100) {\n          setLoading(false);\n          setUploadProgress(0);\n          setUploadStatus(\"\");\n          dispatch(HideLoading());\n          message.success(\"Video uploaded successfully! Thumbnail generation continues in background.\");\n        }\n      }, 10000); // 10 seconds after upload completes\n\n      let response;\n\n      if (materialType === \"videos\") {\n        // Add additional classes to values for videos\n        const videoValues = {\n          ...values,\n          additionalClasses: selectedAdditionalClasses\n        };\n\n        // For videos, handle YouTube, S3 URL, and file upload methods\n        if (uploadMethod === \"youtube\") {\n          // Send JSON data for YouTube videos\n          setUploadStatus(\"Adding YouTube video...\");\n          response = await addVideo(videoValues);\n        } else if (uploadMethod === \"s3url\") {\n          // Handle S3 URL method with optional thumbnail upload\n          if (thumbnailList.length > 0 && thumbnailList[0].originFileObj) {\n            // If thumbnail is provided, create FormData to upload thumbnail\n            const formData = new FormData();\n\n            // Add form fields\n            Object.keys(videoValues).forEach(key => {\n              if (videoValues[key] !== undefined && videoValues[key] !== null) {\n                if (Array.isArray(videoValues[key])) {\n                  // Handle arrays (like additionalClasses)\n                  videoValues[key].forEach(item => formData.append(key, item));\n                } else {\n                  formData.append(key, videoValues[key]);\n                }\n              }\n            });\n\n            // Add thumbnail file\n            formData.append(\"thumbnail\", thumbnailList[0].originFileObj);\n            setUploadStatus(\"Uploading thumbnail and adding video...\");\n            response = await addVideo(formData);\n          } else {\n            // No thumbnail, send JSON data\n            setUploadStatus(\"Adding S3 video...\");\n            response = await addVideo(videoValues);\n          }\n        } else {\n          // Create FormData for video file upload\n          const formData = new FormData();\n\n          // Add form fields\n          Object.keys(videoValues).forEach(key => {\n            if (videoValues[key] !== undefined && videoValues[key] !== null) {\n              if (Array.isArray(videoValues[key])) {\n                // Handle arrays (like additionalClasses)\n                videoValues[key].forEach(item => formData.append(key, item));\n              } else {\n                formData.append(key, videoValues[key]);\n              }\n            }\n          });\n\n          // Add video file\n          if (videoFileList.length > 0 && videoFileList[0].originFileObj) {\n            formData.append(\"video\", videoFileList[0].originFileObj);\n            setUploadStatus(\"Uploading video file...\");\n          }\n\n          // Add thumbnail file if provided\n          if (thumbnailList.length > 0 && thumbnailList[0].originFileObj) {\n            console.log('📎 Adding thumbnail to upload:', thumbnailList[0].name);\n            formData.append(\"thumbnail\", thumbnailList[0].originFileObj);\n          }\n\n          // Upload with enhanced progress tracking\n          setUploadStartTime(Date.now());\n          response = await addVideo(formData, (progress, loaded, total) => {\n            setUploadProgress(progress);\n\n            // Calculate upload speed and estimated time\n            if (uploadStartTime) {\n              const elapsedTime = (Date.now() - uploadStartTime) / 1000; // seconds\n              const uploadedBytes = loaded || (total * progress / 100);\n              const speed = uploadedBytes / elapsedTime; // bytes per second\n              const remainingBytes = total - uploadedBytes;\n              const estimatedSeconds = remainingBytes / speed;\n\n              setUploadSpeed(speed);\n              setEstimatedTime(estimatedSeconds);\n            }\n\n            if (progress === 100) {\n              setUploadStatus(\"Finalizing upload...\");\n            } else if (progress > 0) {\n              setUploadStatus(`Uploading... ${progress}%`);\n            }\n          });\n        }\n      } else {\n        // For other materials, create FormData\n        const formData = new FormData();\n        \n        // Add form fields\n        Object.keys(values).forEach(key => {\n          if (values[key] !== undefined && values[key] !== null) {\n            formData.append(key, values[key]);\n          }\n        });\n\n        // Add files\n        if (fileList.length > 0 && fileList[0].originFileObj) {\n          formData.append(\"document\", fileList[0].originFileObj);\n        }\n\n        if (materialType === \"books\" && thumbnailList.length > 0 && thumbnailList[0].originFileObj) {\n          formData.append(\"thumbnail\", thumbnailList[0].originFileObj);\n        }\n\n        // Call appropriate API\n        switch (materialType) {\n          case \"study-notes\":\n            response = await addNote(formData);\n            break;\n          case \"past-papers\":\n            response = await addPastPaper(formData);\n            break;\n          case \"books\":\n            response = await addBook(formData);\n            break;\n          default:\n            throw new Error(\"Invalid material type\");\n        }\n      }\n\n      if (response.status === 201 && response.data.success) {\n        message.success(response.data.message);\n        form.resetFields();\n        setFileList([]);\n        setThumbnailList([]);\n        setVideoFileList([]);\n        setSelectedAdditionalClasses([]);\n        setUploadMethod(\"youtube\");\n        setUploadProgress(0);\n        setUploadStatus(\"\");\n        onSuccess(materialType);\n      } else {\n        const errorMessage = response.data?.message || \"Failed to add material\";\n        message.error(errorMessage);\n      }\n    } catch (error) {\n      console.error(\"Error adding material:\", error);\n\n      // Provide specific error messages based on error type\n      if (error.code === 'ECONNABORTED') {\n        message.error(\"Upload timeout. Please try with a smaller file or check your internet connection.\");\n      } else if (error.response?.status === 413) {\n        message.error(\"File too large. Please use a file smaller than 500MB.\");\n      } else if (error.response?.status === 400) {\n        message.error(error.response.data?.message || \"Invalid file or form data.\");\n      } else if (error.response?.status === 500) {\n        message.error(\"Server error. Please try again later.\");\n      } else {\n        message.error(\"Upload failed. Please check your internet connection and try again.\");\n      }\n    } finally {\n      if (timeoutId) {\n        clearTimeout(timeoutId);\n      }\n      setLoading(false);\n      setUploadProgress(0);\n      setUploadStatus(\"\");\n      dispatch(HideLoading());\n    }\n  };\n\n  const uploadProps = {\n    beforeUpload: () => false, // Prevent auto upload\n    maxCount: 1,\n    accept: materialType === \"videos\" ? undefined : \".pdf,.doc,.docx,.ppt,.pptx\",\n  };\n\n  const videoUploadProps = {\n    beforeUpload: () => false,\n    maxCount: 1,\n    accept: \"video/*\",\n  };\n\n  const thumbnailUploadProps = {\n    beforeUpload: () => false,\n    maxCount: 1,\n    accept: \"image/*\",\n  };\n\n  const getMaterialIcon = () => {\n    switch (materialType) {\n      case \"videos\":\n        return <FaVideo />;\n      case \"study-notes\":\n        return <FaFileAlt />;\n      case \"past-papers\":\n        return <FaGraduationCap />;\n      case \"books\":\n        return <FaBook />;\n      default:\n        return <FaFileAlt />;\n    }\n  };\n\n  const getMaterialTitle = () => {\n    switch (materialType) {\n      case \"videos\":\n        return \"Video\";\n      case \"study-notes\":\n        return \"Study Note\";\n      case \"past-papers\":\n        return \"Past Paper\";\n      case \"books\":\n        return \"Book\";\n      default:\n        return \"Material\";\n    }\n  };\n\n  return (\n    <div className=\"add-material-form\">\n      <div className=\"form-card\">\n        <div className=\"form-header-icon\">\n          {getMaterialIcon()}\n          <h3>Add New {getMaterialTitle()}</h3>\n        </div>\n\n        <Form\n          form={form}\n          layout=\"vertical\"\n          onFinish={handleSubmit}\n          initialValues={{ level: \"primary\" }}\n          className=\"material-form\"\n        >\n          <div className=\"form-row\">\n            <Form.Item\n              label=\"Level\"\n              name=\"level\"\n              rules={[{ required: true, message: \"Please select a level\" }]}\n              className=\"form-item-half\"\n            >\n              <Select\n                placeholder=\"Select level\"\n                onChange={handleLevelChange}\n                size=\"large\"\n              >\n                <Option value=\"primary\">Primary</Option>\n                <Option value=\"secondary\">Secondary</Option>\n                <Option value=\"advance\">Advance</Option>\n              </Select>\n            </Form.Item>\n\n            <Form.Item\n              label=\"Class\"\n              name=\"className\"\n              rules={[{ required: true, message: \"Please select a class\" }]}\n              className=\"form-item-half\"\n            >\n              <Select\n                placeholder=\"Select class\"\n                size=\"large\"\n                onChange={handleCoreClassChange}\n              >\n                {availableClasses.map(cls => (\n                  <Option key={cls} value={cls}>{cls}</Option>\n                ))}\n              </Select>\n            </Form.Item>\n          </div>\n\n          <Form.Item\n            label=\"Subject\"\n            name=\"subject\"\n            rules={[{ required: true, message: \"Please select a subject\" }]}\n          >\n            <Select placeholder=\"Select subject\" size=\"large\">\n              {availableSubjects.map(subject => (\n                <Option key={subject} value={subject}>{subject}</Option>\n              ))}\n            </Select>\n          </Form.Item>\n\n          {materialType === \"videos\" && (\n            <Form.Item\n              label=\"Additional Classes (Optional)\"\n              className=\"additional-classes-section\"\n            >\n              <div className=\"additional-classes-info\">\n                <p>Select additional classes that can access this video (besides the core class selected above)</p>\n              </div>\n              <Select\n                mode=\"multiple\"\n                placeholder=\"Select additional classes (optional)\"\n                size=\"large\"\n                value={selectedAdditionalClasses}\n                onChange={handleAdditionalClassesChange}\n                style={{ width: '100%' }}\n              >\n                {availableClasses\n                  .filter(cls => cls !== form.getFieldValue('className')) // Exclude the core class\n                  .map(cls => (\n                    <Option key={cls} value={cls}>{cls}</Option>\n                  ))}\n              </Select>\n              <div className=\"additional-classes-note\">\n                <small>Note: The video will be available to the core class and all selected additional classes</small>\n              </div>\n            </Form.Item>\n          )}\n\n          <Form.Item\n            label=\"Title\"\n            name=\"title\"\n            rules={[{ required: true, message: \"Please enter a title\" }]}\n          >\n            <input\n              type=\"text\"\n              placeholder={`Enter ${getMaterialTitle().toLowerCase()} title`}\n              className=\"form-input\"\n            />\n          </Form.Item>\n\n          {(materialType === \"past-papers\" || materialType === \"books\") && (\n            <Form.Item\n              label=\"Year\"\n              name=\"year\"\n              rules={[{ required: true, message: \"Please enter the year\" }]}\n            >\n              <input\n                type=\"text\"\n                placeholder=\"Enter year (e.g., 2023)\"\n                className=\"form-input\"\n              />\n            </Form.Item>\n          )}\n\n          {materialType === \"videos\" && (\n            <>\n              <Form.Item\n                label=\"Upload Method\"\n                className=\"upload-method-section\"\n              >\n                <div className=\"upload-method-selector\">\n                  <div\n                    className={`method-option ${uploadMethod === \"youtube\" ? \"active\" : \"\"}`}\n                    onClick={() => setUploadMethod(\"youtube\")}\n                  >\n                    <FaVideo className=\"method-icon\" />\n                    <span>YouTube Video</span>\n                    <p>Add video using YouTube ID</p>\n                  </div>\n                  <div\n                    className={`method-option ${uploadMethod === \"s3url\" ? \"active\" : \"\"}`}\n                    onClick={() => setUploadMethod(\"s3url\")}\n                  >\n                    <FaCloudUploadAlt className=\"method-icon\" />\n                    <span>S3 Object URL</span>\n                    <p>Add video using S3 bucket URL</p>\n                  </div>\n                  <div\n                    className={`method-option ${uploadMethod === \"upload\" ? \"active\" : \"\"}`}\n                    onClick={() => setUploadMethod(\"upload\")}\n                  >\n                    <FaCloudUploadAlt className=\"method-icon\" />\n                    <span>Upload Video File</span>\n                    <p>Upload video file to server</p>\n                  </div>\n                </div>\n              </Form.Item>\n\n              {uploadMethod === \"youtube\" ? (\n                <>\n                  <Form.Item\n                    label=\"Video ID (YouTube)\"\n                    name=\"videoID\"\n                    rules={[{ required: uploadMethod === \"youtube\", message: \"Please enter YouTube video ID\" }]}\n                  >\n                    <input\n                      type=\"text\"\n                      placeholder=\"Enter YouTube video ID (e.g., dQw4w9WgXcQ)\"\n                      className=\"form-input\"\n                    />\n                  </Form.Item>\n\n                  <Form.Item\n                    label=\"Video URL (Optional)\"\n                    name=\"videoUrl\"\n                  >\n                    <input\n                      type=\"url\"\n                      placeholder=\"Enter video URL (optional)\"\n                      className=\"form-input\"\n                    />\n                  </Form.Item>\n\n                  <Form.Item\n                    label=\"Thumbnail URL (Optional)\"\n                    name=\"thumbnailUrl\"\n                  >\n                    <input\n                      type=\"url\"\n                      placeholder=\"Enter thumbnail URL (optional)\"\n                      className=\"form-input\"\n                    />\n                  </Form.Item>\n                </>\n              ) : uploadMethod === \"s3url\" ? (\n                <>\n                  <Form.Item\n                    label=\"S3 Object URL\"\n                    name=\"videoUrl\"\n                    rules={[{ required: uploadMethod === \"s3url\", message: \"Please enter S3 object URL\" }]}\n                  >\n                    <input\n                      type=\"url\"\n                      placeholder=\"Enter S3 object URL (e.g., https://your-bucket.s3.amazonaws.com/video.mp4)\"\n                      className=\"form-input\"\n                    />\n                  </Form.Item>\n\n                  <Form.Item\n                    label=\"Drag & Drop Thumbnail (Optional)\"\n                    className=\"upload-section\"\n                  >\n                    <Upload\n                      {...thumbnailUploadProps}\n                      fileList={thumbnailList}\n                      onChange={({ fileList }) => setThumbnailList(fileList)}\n                      className=\"thumbnail-upload\"\n                      onDrop={(e) => {\n                        e.preventDefault();\n                        setIsDragOver(false);\n                        const files = Array.from(e.dataTransfer.files);\n                        const imageFiles = files.filter(file => file.type.startsWith('image/'));\n                        if (imageFiles.length > 0) {\n                          const file = imageFiles[0];\n                          // Validate file size (5MB limit)\n                          if (file.size > 5 * 1024 * 1024) {\n                            message.error('Thumbnail file size must be less than 5MB');\n                            return;\n                          }\n                          setThumbnailList([{\n                            uid: '-1',\n                            name: file.name,\n                            status: 'done',\n                            originFileObj: file,\n                            url: URL.createObjectURL(file)\n                          }]);\n                          message.success('Thumbnail uploaded successfully!');\n                        } else {\n                          message.error('Please drop an image file (JPG, PNG, GIF)');\n                        }\n                      }}\n                      onDragOver={(e) => {\n                        e.preventDefault();\n                        setIsDragOver(true);\n                      }}\n                      onDragEnter={(e) => {\n                        e.preventDefault();\n                        setIsDragOver(true);\n                      }}\n                      onDragLeave={(e) => {\n                        e.preventDefault();\n                        setIsDragOver(false);\n                      }}\n                    >\n                      <div className={`upload-area small thumbnail-drop-zone ${isDragOver ? 'drag-over' : ''}`}>\n                        <FaUpload className=\"upload-icon\" />\n                        <p>{isDragOver ? 'Drop thumbnail here!' : 'Drag & drop thumbnail or click to upload'}</p>\n                        <p className=\"upload-hint\">Auto-generated if not provided</p>\n                        <p className=\"upload-hint\">Supports JPG, PNG, GIF (Max: 5MB)</p>\n                      </div>\n                    </Upload>\n                  </Form.Item>\n                </>\n              ) : (\n                <>\n                  <Form.Item\n                    label=\"Upload Video File\"\n                    className=\"upload-section\"\n                  >\n                    <Upload\n                      {...videoUploadProps}\n                      fileList={videoFileList}\n                      onChange={({ fileList }) => setVideoFileList(fileList)}\n                      className=\"video-upload\"\n                    >\n                      <div className=\"upload-area\">\n                        <FaCloudUploadAlt className=\"upload-icon\" />\n                        <p>Click or drag video file to upload</p>\n                        <p className=\"upload-hint\">Supports MP4, AVI, MOV, WMV (Max: 500MB)</p>\n                        <p className=\"upload-hint\">Large files may take several minutes to upload</p>\n                      </div>\n                    </Upload>\n                  </Form.Item>\n\n                  <Form.Item\n                    label=\"Upload Custom Thumbnail (Optional)\"\n                    className=\"upload-section\"\n                  >\n                    <Upload\n                      {...thumbnailUploadProps}\n                      fileList={thumbnailList}\n                      onChange={({ fileList }) => setThumbnailList(fileList)}\n                      className=\"thumbnail-upload\"\n                      onDrop={(e) => {\n                        e.preventDefault();\n                        setIsDragOver(false);\n                        const files = Array.from(e.dataTransfer.files);\n                        const imageFiles = files.filter(file => file.type.startsWith('image/'));\n                        if (imageFiles.length > 0) {\n                          const file = imageFiles[0];\n                          // Validate file size (5MB limit)\n                          if (file.size > 5 * 1024 * 1024) {\n                            message.error('Thumbnail file size must be less than 5MB');\n                            return;\n                          }\n                          setThumbnailList([{\n                            uid: '-1',\n                            name: file.name,\n                            status: 'done',\n                            originFileObj: file,\n                            url: URL.createObjectURL(file)\n                          }]);\n                          message.success('Thumbnail uploaded successfully!');\n                        } else {\n                          message.error('Please drop an image file (JPG, PNG, GIF)');\n                        }\n                      }}\n                      onDragOver={(e) => {\n                        e.preventDefault();\n                        setIsDragOver(true);\n                      }}\n                      onDragEnter={(e) => {\n                        e.preventDefault();\n                        setIsDragOver(true);\n                      }}\n                      onDragLeave={(e) => {\n                        e.preventDefault();\n                        setIsDragOver(false);\n                      }}\n                    >\n                      <div className={`upload-area small thumbnail-drop-zone ${isDragOver ? 'drag-over' : ''}`}>\n                        <FaUpload className=\"upload-icon\" />\n                        <p>{isDragOver ? 'Drop thumbnail here!' : 'Drag & drop thumbnail or click to upload'}</p>\n                        <p className=\"upload-hint\">Auto-generated if not provided</p>\n                        <p className=\"upload-hint\">Supports JPG, PNG, GIF (Max: 5MB)</p>\n                      </div>\n                    </Upload>\n                  </Form.Item>\n                </>\n              )}\n            </>\n          )}\n\n          {materialType !== \"videos\" && (\n            <Form.Item\n              label={`Upload ${getMaterialTitle()} Document`}\n              className=\"upload-section\"\n            >\n              <Upload\n                {...uploadProps}\n                fileList={fileList}\n                onChange={({ fileList }) => setFileList(fileList)}\n                className=\"document-upload\"\n              >\n                <div className=\"upload-area\">\n                  <FaCloudUploadAlt className=\"upload-icon\" />\n                  <p>Click or drag file to upload</p>\n                  <p className=\"upload-hint\">Supports PDF, DOC, DOCX, PPT, PPTX</p>\n                </div>\n              </Upload>\n            </Form.Item>\n          )}\n\n          {materialType === \"books\" && (\n            <Form.Item\n              label=\"Upload Thumbnail (Optional)\"\n              className=\"upload-section\"\n            >\n              <Upload\n                {...thumbnailUploadProps}\n                fileList={thumbnailList}\n                onChange={({ fileList }) => setThumbnailList(fileList)}\n                className=\"thumbnail-upload\"\n              >\n                <div className=\"upload-area small\">\n                  <FaUpload className=\"upload-icon\" />\n                  <p>Upload book cover</p>\n                </div>\n              </Upload>\n            </Form.Item>\n          )}\n\n          {/* Enhanced Upload Progress Indicator */}\n          {loading && uploadMethod === \"upload\" && materialType === \"videos\" && (\n            <div className=\"upload-progress-section\">\n              <div className=\"progress-header\">\n                <div className=\"progress-info\">\n                  <span className=\"progress-text\">{uploadStatus}</span>\n                  <span className=\"progress-percentage\">{uploadProgress}%</span>\n                </div>\n                {uploadSpeed > 0 && (\n                  <div className=\"upload-stats\">\n                    <span className=\"upload-speed\">\n                      📊 {(uploadSpeed / (1024 * 1024)).toFixed(2)} MB/s\n                    </span>\n                    {estimatedTime > 0 && estimatedTime < 3600 && (\n                      <span className=\"estimated-time\">\n                        ⏱️ {Math.ceil(estimatedTime)}s remaining\n                      </span>\n                    )}\n                  </div>\n                )}\n              </div>\n\n              <div className=\"progress-bar\">\n                <div\n                  className=\"progress-fill\"\n                  style={{\n                    width: `${uploadProgress}%`,\n                    transition: 'width 0.3s ease'\n                  }}\n                ></div>\n              </div>\n\n              <div className=\"progress-details\">\n                {uploadProgress < 100 ? (\n                  <div className=\"uploading-info\">\n                    <span>📤 Uploading video file to server...</span>\n                    <small>Please keep this tab open until upload completes</small>\n                  </div>\n                ) : (\n                  <div className=\"processing-info\">\n                    <span>🎬 Upload complete! Processing video and generating thumbnail...</span>\n                    <small>This may take a few moments for large files</small>\n                  </div>\n                )}\n              </div>\n            </div>\n          )}\n\n          <div className=\"form-actions\">\n            <Button\n              type=\"default\"\n              onClick={onCancel}\n              size=\"large\"\n              className=\"cancel-btn\"\n              disabled={loading}\n            >\n              Cancel\n            </Button>\n            <Button\n              type=\"primary\"\n              htmlType=\"submit\"\n              loading={loading}\n              size=\"large\"\n              className=\"submit-btn\"\n            >\n              {loading ? (\n                uploadMethod === \"upload\" && materialType === \"videos\" ?\n                \"Uploading...\" : \"Adding...\"\n              ) : (\n                `Add ${getMaterialTitle()}`\n              )}\n            </Button>\n          </div>\n        </Form>\n      </div>\n    </div>\n  );\n}\n\nexport default AddStudyMaterialForm;\n", "const { default: axiosInstance } = require(\".\");\n\n// Generate subtitles for a video\nexport const generateSubtitles = async (videoId, language = 'en') => {\n  try {\n    const response = await axiosInstance.post(`/api/study/generate-subtitles/${videoId}`, {\n      language\n    });\n    return response.data;\n  } catch (error) {\n    return error.response.data;\n  }\n};\n\n// Upload custom subtitle file\nexport const uploadSubtitle = async (videoId, formData) => {\n  try {\n    const response = await axiosInstance.post(`/api/study/upload-subtitle/${videoId}`, formData, {\n      headers: { \"Content-Type\": \"multipart/form-data\" },\n    });\n    return response.data;\n  } catch (error) {\n    return error.response.data;\n  }\n};\n\n// Get video with subtitle information\nexport const getVideoWithSubtitles = async (videoId) => {\n  try {\n    const response = await axiosInstance.get(`/api/study/video/${videoId}`);\n    return response.data;\n  } catch (error) {\n    return error.response.data;\n  }\n};\n\n// Delete subtitle\nexport const deleteSubtitle = async (videoId, language) => {\n  try {\n    const response = await axiosInstance.delete(`/api/study/subtitle/${videoId}/${language}`);\n    return response.data;\n  } catch (error) {\n    return error.response.data;\n  }\n};\n\n// Get all videos with subtitle status\nexport const getVideosWithSubtitleStatus = async () => {\n  try {\n    const response = await axiosInstance.get('/api/study/videos-subtitle-status');\n    return response.data;\n  } catch (error) {\n    return error.response.data;\n  }\n};\n", "import React, { useState, useEffect } from 'react';\nimport { Table, Button, Upload, Select, message, Modal, Tag, Space, Tooltip } from 'antd';\nimport {\n  UploadOutlined,\n  PlayCircleOutlined,\n  ReloadOutlined,\n  RobotOutlined,\n  FileTextOutlined,\n  GlobalOutlined\n} from '@ant-design/icons';\nimport { useDispatch } from 'react-redux';\nimport { HideLoading, ShowLoading } from '../../../redux/loaderSlice';\nimport { generateSubtitles, uploadSubtitle } from '../../../apicalls/subtitles';\nimport { getAllVideos } from '../../../apicalls/study';\nimport './SubtitleManager.css';\n\nconst { Option } = Select;\n\nconst SubtitleManager = () => {\n  const [videos, setVideos] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [uploadModalVisible, setUploadModalVisible] = useState(false);\n  const [selectedVideo, setSelectedVideo] = useState(null);\n  const [selectedLanguage, setSelectedLanguage] = useState('en');\n  const [languageName, setLanguageName] = useState('English');\n  const [fileList, setFileList] = useState([]);\n  const [generatingSubtitles, setGeneratingSubtitles] = useState({});\n  \n  const dispatch = useDispatch();\n\n  const languages = [\n    { code: 'en', name: 'English' },\n    { code: 'es', name: 'Spanish' },\n    { code: 'fr', name: 'French' },\n    { code: 'de', name: 'German' },\n    { code: 'it', name: 'Italian' },\n    { code: 'pt', name: 'Portuguese' },\n    { code: 'ru', name: 'Russian' },\n    { code: 'ja', name: 'Japanese' },\n    { code: 'ko', name: 'Korean' },\n    { code: 'zh', name: 'Chinese' },\n    { code: 'ar', name: 'Arabic' },\n    { code: 'hi', name: 'Hindi' },\n    { code: 'ur', name: 'Urdu' },\n    { code: 'bn', name: 'Bengali' }\n  ];\n\n  useEffect(() => {\n    fetchVideos();\n  }, []);\n\n  const fetchVideos = async () => {\n    try {\n      setLoading(true);\n      const response = await getAllVideos();\n      if (response.success) {\n        setVideos(response.data);\n      } else {\n        message.error('Failed to fetch videos');\n      }\n    } catch (error) {\n      message.error('Error fetching videos');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleGenerateSubtitles = async (videoId, language = 'en') => {\n    try {\n      setGeneratingSubtitles(prev => ({ ...prev, [videoId]: true }));\n      message.info('Generating subtitles... This may take a few minutes.');\n      \n      const response = await generateSubtitles(videoId, language);\n      if (response.success) {\n        message.success('Subtitles generated successfully!');\n        fetchVideos(); // Refresh the list\n      } else {\n        message.error(response.message || 'Failed to generate subtitles');\n      }\n    } catch (error) {\n      message.error('Error generating subtitles');\n    } finally {\n      setGeneratingSubtitles(prev => ({ ...prev, [videoId]: false }));\n    }\n  };\n\n  const handleUploadSubtitle = async () => {\n    if (!selectedVideo || fileList.length === 0) {\n      message.warning('Please select a video and upload a subtitle file');\n      return;\n    }\n\n    try {\n      dispatch(ShowLoading());\n      \n      const formData = new FormData();\n      formData.append('subtitle', fileList[0]);\n      formData.append('language', selectedLanguage);\n      formData.append('languageName', languageName);\n      formData.append('isDefault', selectedLanguage === 'en');\n\n      const response = await uploadSubtitle(selectedVideo._id, formData);\n      \n      if (response.success) {\n        message.success('Subtitle uploaded successfully!');\n        setUploadModalVisible(false);\n        setFileList([]);\n        setSelectedVideo(null);\n        fetchVideos();\n      } else {\n        message.error(response.message || 'Failed to upload subtitle');\n      }\n    } catch (error) {\n      message.error('Error uploading subtitle');\n    } finally {\n      dispatch(HideLoading());\n    }\n  };\n\n  const getSubtitleStatus = (video) => {\n    if (video.subtitleGenerationStatus === 'processing') {\n      return <Tag color=\"blue\" icon={<RobotOutlined />}>Generating...</Tag>;\n    }\n    if (video.hasSubtitles && video.subtitles?.length > 0) {\n      return (\n        <Space>\n          <Tag color=\"green\" icon={<FileTextOutlined />}>\n            {video.subtitles.length} subtitle{video.subtitles.length > 1 ? 's' : ''}\n          </Tag>\n          {video.subtitles.map(sub => (\n            <Tag \n              key={sub.language} \n              color={sub.isAutoGenerated ? 'blue' : 'purple'}\n              size=\"small\"\n            >\n              {sub.languageName} {sub.isAutoGenerated ? '(Auto)' : '(Custom)'}\n            </Tag>\n          ))}\n        </Space>\n      );\n    }\n    if (video.subtitleGenerationStatus === 'failed') {\n      return <Tag color=\"red\">Generation Failed</Tag>;\n    }\n    return <Tag color=\"default\">No Subtitles</Tag>;\n  };\n\n  const columns = [\n    {\n      title: 'Video',\n      dataIndex: 'title',\n      key: 'title',\n      width: '25%',\n      render: (title, record) => (\n        <div>\n          <div style={{ fontWeight: 'bold', marginBottom: '4px' }}>{title}</div>\n          <div style={{ fontSize: '12px', color: '#666' }}>\n            {record.subject} • Class {record.className} • {record.level}\n          </div>\n        </div>\n      ),\n    },\n    {\n      title: 'Subtitle Status',\n      key: 'subtitleStatus',\n      width: '30%',\n      render: (_, record) => getSubtitleStatus(record),\n    },\n    {\n      title: 'Video Source',\n      key: 'videoSource',\n      width: '15%',\n      render: (_, record) => {\n        if (record.videoUrl && record.videoUrl.includes('amazonaws.com')) {\n          return <Tag color=\"orange\" icon={<GlobalOutlined />}>S3 Video</Tag>;\n        }\n        if (record.videoID && !record.videoUrl) {\n          return <Tag color=\"red\" icon={<PlayCircleOutlined />}>YouTube</Tag>;\n        }\n        return <Tag color=\"default\">Unknown</Tag>;\n      },\n    },\n    {\n      title: 'Actions',\n      key: 'actions',\n      width: '30%',\n      render: (_, record) => (\n        <Space>\n          {record.videoUrl && record.videoUrl.includes('amazonaws.com') && (\n            <Tooltip title=\"Generate subtitles using AI\">\n              <Button\n                type=\"primary\"\n                icon={<RobotOutlined />}\n                size=\"small\"\n                loading={generatingSubtitles[record._id]}\n                onClick={() => handleGenerateSubtitles(record._id)}\n                disabled={record.subtitleGenerationStatus === 'processing'}\n              >\n                Generate\n              </Button>\n            </Tooltip>\n          )}\n          \n          <Tooltip title=\"Upload custom subtitle file\">\n            <Button\n              icon={<UploadOutlined />}\n              size=\"small\"\n              onClick={() => {\n                setSelectedVideo(record);\n                setUploadModalVisible(true);\n              }}\n            >\n              Upload\n            </Button>\n          </Tooltip>\n          \n          <Tooltip title=\"Refresh video data\">\n            <Button\n              icon={<ReloadOutlined />}\n              size=\"small\"\n              onClick={fetchVideos}\n            />\n          </Tooltip>\n        </Space>\n      ),\n    },\n  ];\n\n  const uploadProps = {\n    beforeUpload: (file) => {\n      const isSRT = file.name.toLowerCase().endsWith('.srt');\n      if (!isSRT) {\n        message.error('Please upload a .srt subtitle file');\n        return false;\n      }\n      setFileList([file]);\n      return false; // Prevent automatic upload\n    },\n    fileList,\n    onRemove: () => {\n      setFileList([]);\n    },\n  };\n\n  return (\n    <div className=\"subtitle-manager\">\n      <div className=\"subtitle-manager-header\">\n        <h2>Subtitle Management</h2>\n        <p>Manage subtitles for educational videos to enhance learning accessibility</p>\n      </div>\n\n      <Table\n        columns={columns}\n        dataSource={videos}\n        rowKey=\"_id\"\n        loading={loading}\n        pagination={{\n          pageSize: 10,\n          showSizeChanger: true,\n          showQuickJumper: true,\n          showTotal: (total) => `Total ${total} videos`,\n        }}\n        scroll={{ x: 1000 }}\n      />\n\n      <Modal\n        title=\"Upload Custom Subtitle\"\n        open={uploadModalVisible}\n        onOk={handleUploadSubtitle}\n        onCancel={() => {\n          setUploadModalVisible(false);\n          setFileList([]);\n          setSelectedVideo(null);\n        }}\n        okText=\"Upload\"\n        cancelText=\"Cancel\"\n      >\n        {selectedVideo && (\n          <div style={{ marginBottom: '16px' }}>\n            <strong>Video:</strong> {selectedVideo.title}\n          </div>\n        )}\n        \n        <div style={{ marginBottom: '16px' }}>\n          <label>Language:</label>\n          <Select\n            value={selectedLanguage}\n            onChange={(value) => {\n              setSelectedLanguage(value);\n              const lang = languages.find(l => l.code === value);\n              setLanguageName(lang?.name || value);\n            }}\n            style={{ width: '100%', marginTop: '8px' }}\n          >\n            {languages.map(lang => (\n              <Option key={lang.code} value={lang.code}>\n                {lang.name}\n              </Option>\n            ))}\n          </Select>\n        </div>\n\n        <div>\n          <label>Subtitle File (.srt):</label>\n          <Upload {...uploadProps} style={{ marginTop: '8px' }}>\n            <Button icon={<UploadOutlined />}>Select SRT File</Button>\n          </Upload>\n        </div>\n      </Modal>\n    </div>\n  );\n};\n\nexport default SubtitleManager;\n", "import React, { useState, useEffect } from \"react\";\nimport { Table, Button, Space, Select, Input, message, Modal, Tag, Tooltip } from \"antd\";\nimport { useDispatch } from \"react-redux\";\nimport { HideLoading, ShowLoading } from \"../../../redux/loaderSlice\";\nimport { \n  getAllStudyMaterials, \n  deleteVideo, \n  deleteNote, \n  deletePastPaper, \n  deleteBook \n} from \"../../../apicalls/study\";\nimport { primarySubjects, secondarySubjects, advanceSubjects } from \"../../../data/Subjects\";\nimport {\n  FaVideo,\n  FaFileAlt,\n  FaBook,\n  FaGraduationCap,\n  FaEdit,\n  FaTrash,\n  FaEye,\n  FaFilter,\n  FaSearch\n} from \"react-icons/fa\";\nimport \"./StudyMaterialManager.css\";\n\nconst { Option } = Select;\nconst { Search } = Input;\n\nfunction StudyMaterialManager({ onEdit }) {\n  const dispatch = useDispatch();\n  const [materials, setMaterials] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [filters, setFilters] = useState({\n    materialType: \"\",\n    level: \"\",\n    className: \"\",\n    subject: \"\"\n  });\n  const [searchText, setSearchText] = useState(\"\");\n\n  // Get subjects based on level\n  const getSubjectsForLevel = (level) => {\n    switch (level) {\n      case \"primary\":\n        return primarySubjects;\n      case \"secondary\":\n        return secondarySubjects;\n      case \"advance\":\n        return advanceSubjects;\n      default:\n        return [];\n    }\n  };\n\n  // Get classes based on level\n  const getClassesForLevel = (level) => {\n    switch (level) {\n      case \"primary\":\n        return [\"1\", \"2\", \"3\", \"4\", \"5\"];\n      case \"secondary\":\n        return [\"6\", \"7\", \"8\", \"9\", \"10\", \"11\"];\n      case \"advance\":\n        return [\"12\", \"13\"];\n      default:\n        return [];\n    }\n  };\n\n  // Fetch materials\n  const fetchMaterials = async () => {\n    try {\n      setLoading(true);\n      dispatch(ShowLoading());\n      \n      const response = await getAllStudyMaterials(filters);\n      \n      if (response.status === 200 && response.data.success) {\n        setMaterials(response.data.data || []);\n      } else {\n        message.error(\"Failed to fetch study materials\");\n        setMaterials([]);\n      }\n    } catch (error) {\n      console.error(\"Error fetching materials:\", error);\n      message.error(\"Failed to fetch study materials\");\n      setMaterials([]);\n    } finally {\n      setLoading(false);\n      dispatch(HideLoading());\n    }\n  };\n\n  useEffect(() => {\n    fetchMaterials();\n  }, [filters]);\n\n  // Handle filter changes\n  const handleFilterChange = (key, value) => {\n    setFilters(prev => {\n      const newFilters = { ...prev, [key]: value };\n      \n      // Reset dependent filters\n      if (key === \"level\") {\n        newFilters.className = \"\";\n        newFilters.subject = \"\";\n      }\n      \n      return newFilters;\n    });\n  };\n\n  // Handle delete\n  const handleDelete = async (material) => {\n    Modal.confirm({\n      title: `Delete ${material.type.replace(\"-\", \" \")}`,\n      content: `Are you sure you want to delete \"${material.title}\"? This action cannot be undone.`,\n      okText: \"Delete\",\n      okType: \"danger\",\n      cancelText: \"Cancel\",\n      onOk: async () => {\n        try {\n          dispatch(ShowLoading());\n          \n          let response;\n          switch (material.type) {\n            case \"videos\":\n              response = await deleteVideo(material._id);\n              break;\n            case \"study-notes\":\n              response = await deleteNote(material._id);\n              break;\n            case \"past-papers\":\n              response = await deletePastPaper(material._id);\n              break;\n            case \"books\":\n              response = await deleteBook(material._id);\n              break;\n            default:\n              throw new Error(\"Invalid material type\");\n          }\n\n          if (response.status === 200 && response.data.success) {\n            message.success(response.data.message);\n            fetchMaterials(); // Refresh the list\n          } else {\n            message.error(response.data?.message || \"Failed to delete material\");\n          }\n        } catch (error) {\n          console.error(\"Error deleting material:\", error);\n          message.error(\"Failed to delete material\");\n        } finally {\n          dispatch(HideLoading());\n        }\n      }\n    });\n  };\n\n  // Get material type icon\n  const getMaterialIcon = (type) => {\n    switch (type) {\n      case \"videos\":\n        return <FaVideo className=\"material-icon video\" />;\n      case \"study-notes\":\n        return <FaFileAlt className=\"material-icon note\" />;\n      case \"past-papers\":\n        return <FaGraduationCap className=\"material-icon paper\" />;\n      case \"books\":\n        return <FaBook className=\"material-icon book\" />;\n      default:\n        return <FaFileAlt className=\"material-icon\" />;\n    }\n  };\n\n  // Get material type label\n  const getMaterialTypeLabel = (type) => {\n    switch (type) {\n      case \"videos\":\n        return \"Video\";\n      case \"study-notes\":\n        return \"Study Note\";\n      case \"past-papers\":\n        return \"Past Paper\";\n      case \"books\":\n        return \"Book\";\n      default:\n        return type;\n    }\n  };\n\n  // Filter materials based on search text\n  const filteredMaterials = materials.filter(material =>\n    material.title.toLowerCase().includes(searchText.toLowerCase()) ||\n    material.subject.toLowerCase().includes(searchText.toLowerCase()) ||\n    material.className.toLowerCase().includes(searchText.toLowerCase())\n  );\n\n  // Table columns\n  const columns = [\n    {\n      title: \"Material\",\n      key: \"material\",\n      width: \"30%\",\n      render: (_, record) => (\n        <div className=\"material-info\">\n          <div className=\"material-header\">\n            {getMaterialIcon(record.type)}\n            <div className=\"material-details\">\n              <div className=\"material-title\">{record.title}</div>\n              <div className=\"material-meta\">\n                <Tag color=\"blue\">{getMaterialTypeLabel(record.type)}</Tag>\n                <span className=\"meta-text\">{record.subject} • Class {record.className}</span>\n              </div>\n            </div>\n          </div>\n        </div>\n      ),\n    },\n    {\n      title: \"Level\",\n      dataIndex: \"level\",\n      key: \"level\",\n      width: \"10%\",\n      render: (level) => (\n        <Tag color={level === \"primary\" ? \"green\" : level === \"secondary\" ? \"orange\" : \"purple\"}>\n          {level.charAt(0).toUpperCase() + level.slice(1)}\n        </Tag>\n      ),\n    },\n    {\n      title: \"Class\",\n      dataIndex: \"className\",\n      key: \"className\",\n      width: \"10%\",\n      render: (className) => <span className=\"class-badge\">Class {className}</span>,\n    },\n    {\n      title: \"Subject\",\n      dataIndex: \"subject\",\n      key: \"subject\",\n      width: \"15%\",\n    },\n    {\n      title: \"Year\",\n      dataIndex: \"year\",\n      key: \"year\",\n      width: \"10%\",\n      render: (year) => year || \"-\",\n    },\n    {\n      title: \"Actions\",\n      key: \"actions\",\n      width: \"25%\",\n      render: (_, record) => (\n        <Space>\n          <Tooltip title=\"Edit material\">\n            <Button\n              type=\"primary\"\n              icon={<FaEdit />}\n              size=\"small\"\n              onClick={() => onEdit(record)}\n            >\n              Edit\n            </Button>\n          </Tooltip>\n          \n          <Tooltip title=\"Delete material\">\n            <Button\n              danger\n              icon={<FaTrash />}\n              size=\"small\"\n              onClick={() => handleDelete(record)}\n            >\n              Delete\n            </Button>\n          </Tooltip>\n        </Space>\n      ),\n    },\n  ];\n\n  return (\n    <div className=\"study-material-manager\">\n      <div className=\"manager-header\">\n        <h2>Study Materials Management</h2>\n        <p>Manage all uploaded study materials - edit, delete, and organize content</p>\n      </div>\n\n      {/* Filters */}\n      <div className=\"filters-section\">\n        <div className=\"filters-row\">\n          <div className=\"filter-group\">\n            <label>Material Type:</label>\n            <Select\n              placeholder=\"All Types\"\n              value={filters.materialType || undefined}\n              onChange={(value) => handleFilterChange(\"materialType\", value)}\n              allowClear\n              style={{ width: 150 }}\n            >\n              <Option value=\"videos\">Videos</Option>\n              <Option value=\"study-notes\">Study Notes</Option>\n              <Option value=\"past-papers\">Past Papers</Option>\n              <Option value=\"books\">Books</Option>\n            </Select>\n          </div>\n\n          <div className=\"filter-group\">\n            <label>Level:</label>\n            <Select\n              placeholder=\"All Levels\"\n              value={filters.level || undefined}\n              onChange={(value) => handleFilterChange(\"level\", value)}\n              allowClear\n              style={{ width: 120 }}\n            >\n              <Option value=\"primary\">Primary</Option>\n              <Option value=\"secondary\">Secondary</Option>\n              <Option value=\"advance\">Advance</Option>\n            </Select>\n          </div>\n\n          {filters.level && (\n            <div className=\"filter-group\">\n              <label>Class:</label>\n              <Select\n                placeholder=\"All Classes\"\n                value={filters.className || undefined}\n                onChange={(value) => handleFilterChange(\"className\", value)}\n                allowClear\n                style={{ width: 120 }}\n              >\n                {getClassesForLevel(filters.level).map(cls => (\n                  <Option key={cls} value={cls}>Class {cls}</Option>\n                ))}\n              </Select>\n            </div>\n          )}\n\n          {filters.level && (\n            <div className=\"filter-group\">\n              <label>Subject:</label>\n              <Select\n                placeholder=\"All Subjects\"\n                value={filters.subject || undefined}\n                onChange={(value) => handleFilterChange(\"subject\", value)}\n                allowClear\n                style={{ width: 150 }}\n              >\n                {getSubjectsForLevel(filters.level).map(subject => (\n                  <Option key={subject} value={subject}>{subject}</Option>\n                ))}\n              </Select>\n            </div>\n          )}\n\n          <div className=\"filter-group\">\n            <label>Search:</label>\n            <Search\n              placeholder=\"Search materials...\"\n              value={searchText}\n              onChange={(e) => setSearchText(e.target.value)}\n              style={{ width: 200 }}\n              allowClear\n            />\n          </div>\n        </div>\n      </div>\n\n      {/* Materials Table */}\n      <div className=\"materials-table\">\n        <Table\n          columns={columns}\n          dataSource={filteredMaterials}\n          rowKey=\"_id\"\n          loading={loading}\n          pagination={{\n            pageSize: 10,\n            showSizeChanger: true,\n            showQuickJumper: true,\n            showTotal: (total) => `Total ${total} materials`,\n          }}\n          scroll={{ x: 1000 }}\n        />\n      </div>\n    </div>\n  );\n}\n\nexport default StudyMaterialManager;\n", "import React, { useState, useEffect } from \"react\";\nimport { Form, message, Select, Upload, Button } from \"antd\";\nimport { useDispatch } from \"react-redux\";\nimport { HideLoading, ShowLoading } from \"../../../redux/loaderSlice\";\nimport { \n  updateVideo, \n  updateNote, \n  updatePastPaper, \n  updateBook \n} from \"../../../apicalls/study\";\nimport { primarySubjects, secondarySubjects, advanceSubjects } from \"../../../data/Subjects\";\nimport {\n  FaUpload,\n  FaVideo,\n  FaFileAlt,\n  FaBook,\n  FaGraduationCap,\n  FaCloudUploadAlt,\n  FaSave,\n  FaTimes\n} from \"react-icons/fa\";\nimport \"./EditStudyMaterialForm.css\";\n\nconst { Option } = Select;\n\nfunction EditStudyMaterialForm({ material, onSuccess, onCancel }) {\n  const [form] = Form.useForm();\n  const dispatch = useDispatch();\n  const [loading, setLoading] = useState(false);\n  const [fileList, setFileList] = useState([]);\n  const [thumbnailList, setThumbnailList] = useState([]);\n  const [selectedAdditionalClasses, setSelectedAdditionalClasses] = useState([]);\n  const [uploadMethod, setUploadMethod] = useState(\"youtube\");\n\n  // Get subjects based on level\n  const getSubjectsForLevel = (level) => {\n    switch (level) {\n      case \"primary\":\n        return primarySubjects;\n      case \"secondary\":\n        return secondarySubjects;\n      case \"advance\":\n        return advanceSubjects;\n      default:\n        return [];\n    }\n  };\n\n  // Get classes based on level\n  const getClassesForLevel = (level) => {\n    switch (level) {\n      case \"primary\":\n        return [\"1\", \"2\", \"3\", \"4\", \"5\"];\n      case \"secondary\":\n        return [\"6\", \"7\", \"8\", \"9\", \"10\", \"11\"];\n      case \"advance\":\n        return [\"12\", \"13\"];\n      default:\n        return [];\n    }\n  };\n\n  // Initialize form with material data\n  useEffect(() => {\n    if (material) {\n      form.setFieldsValue({\n        level: material.level,\n        className: material.className,\n        subject: material.subject,\n        title: material.title,\n        year: material.year,\n        videoID: material.videoID,\n        videoUrl: material.videoUrl,\n        thumbnailUrl: material.thumbnail\n      });\n\n      // Set additional classes for videos\n      if (material.type === \"videos\" && material.additionalClasses) {\n        setSelectedAdditionalClasses(material.additionalClasses);\n      }\n\n      // Set upload method for videos\n      if (material.type === \"videos\") {\n        if (material.videoID && !material.videoUrl) {\n          setUploadMethod(\"youtube\");\n        } else if (material.videoUrl) {\n          setUploadMethod(\"s3url\");\n        }\n      }\n    }\n  }, [material, form]);\n\n  // Handle level change\n  const handleLevelChange = (level) => {\n    form.setFieldsValue({ className: \"\", subject: \"\" });\n    setSelectedAdditionalClasses([]);\n  };\n\n  // Handle additional classes change for videos\n  const handleAdditionalClassesChange = (classes) => {\n    setSelectedAdditionalClasses(classes);\n  };\n\n  // Get available additional classes (exclude core class)\n  const getAvailableAdditionalClasses = () => {\n    const currentLevel = form.getFieldValue(\"level\");\n    const currentClass = form.getFieldValue(\"className\");\n    \n    if (!currentLevel || !currentClass) return [];\n    \n    return getClassesForLevel(currentLevel).filter(cls => cls !== currentClass);\n  };\n\n  // Handle form submission\n  const handleSubmit = async (values) => {\n    try {\n      setLoading(true);\n      dispatch(ShowLoading());\n\n      let response;\n\n      if (material.type === \"videos\") {\n        // Handle video updates\n        const videoValues = {\n          ...values,\n          additionalClasses: selectedAdditionalClasses\n        };\n\n        if (uploadMethod === \"youtube\") {\n          // Send JSON data for YouTube videos\n          response = await updateVideo(material._id, videoValues);\n        } else if (uploadMethod === \"s3url\") {\n          // Handle S3 URL method with optional thumbnail upload\n          if (thumbnailList.length > 0 && thumbnailList[0].originFileObj) {\n            const formData = new FormData();\n            Object.keys(videoValues).forEach(key => {\n              if (key === \"additionalClasses\") {\n                formData.append(key, JSON.stringify(videoValues[key]));\n              } else {\n                formData.append(key, videoValues[key]);\n              }\n            });\n            formData.append(\"thumbnail\", thumbnailList[0].originFileObj);\n            response = await updateVideo(material._id, formData);\n          } else {\n            response = await updateVideo(material._id, videoValues);\n          }\n        }\n      } else {\n        // Handle other material types (notes, past papers, books)\n        const formData = new FormData();\n        \n        Object.keys(values).forEach(key => {\n          if (values[key] !== undefined && values[key] !== null) {\n            formData.append(key, values[key]);\n          }\n        });\n\n        // Add file if uploaded\n        if (fileList.length > 0 && fileList[0].originFileObj) {\n          formData.append(\"document\", fileList[0].originFileObj);\n        }\n\n        // Add thumbnail for books\n        if (material.type === \"books\" && thumbnailList.length > 0 && thumbnailList[0].originFileObj) {\n          formData.append(\"thumbnail\", thumbnailList[0].originFileObj);\n        }\n\n        // Call appropriate API\n        switch (material.type) {\n          case \"study-notes\":\n            response = await updateNote(material._id, formData);\n            break;\n          case \"past-papers\":\n            response = await updatePastPaper(material._id, formData);\n            break;\n          case \"books\":\n            response = await updateBook(material._id, formData);\n            break;\n          default:\n            throw new Error(\"Invalid material type\");\n        }\n      }\n\n      if (response.status === 200 && response.data.success) {\n        message.success(response.data.message);\n        onSuccess(material.type);\n      } else {\n        const errorMessage = response.data?.message || \"Failed to update material\";\n        message.error(errorMessage);\n      }\n    } catch (error) {\n      console.error(\"Error updating material:\", error);\n      message.error(\"Failed to update material\");\n    } finally {\n      setLoading(false);\n      dispatch(HideLoading());\n    }\n  };\n\n  // Get material title\n  const getMaterialTitle = () => {\n    switch (material?.type) {\n      case \"videos\":\n        return \"Video\";\n      case \"study-notes\":\n        return \"Study Note\";\n      case \"past-papers\":\n        return \"Past Paper\";\n      case \"books\":\n        return \"Book\";\n      default:\n        return \"Material\";\n    }\n  };\n\n  // Get material icon\n  const getMaterialIcon = () => {\n    switch (material?.type) {\n      case \"videos\":\n        return <FaVideo className=\"form-icon\" />;\n      case \"study-notes\":\n        return <FaFileAlt className=\"form-icon\" />;\n      case \"past-papers\":\n        return <FaGraduationCap className=\"form-icon\" />;\n      case \"books\":\n        return <FaBook className=\"form-icon\" />;\n      default:\n        return <FaFileAlt className=\"form-icon\" />;\n    }\n  };\n\n  // Upload props for documents\n  const documentUploadProps = {\n    beforeUpload: (file) => {\n      const isValidType = file.type === \"application/pdf\" || \n                         file.type.startsWith(\"application/\") ||\n                         file.type.startsWith(\"text/\");\n      if (!isValidType) {\n        message.error(\"Please upload a valid document file\");\n        return false;\n      }\n      const isLt50M = file.size / 1024 / 1024 < 50;\n      if (!isLt50M) {\n        message.error(\"File must be smaller than 50MB\");\n        return false;\n      }\n      setFileList([file]);\n      return false;\n    },\n    onRemove: () => {\n      setFileList([]);\n    },\n  };\n\n  // Upload props for thumbnails\n  const thumbnailUploadProps = {\n    beforeUpload: (file) => {\n      const isImage = file.type.startsWith(\"image/\");\n      if (!isImage) {\n        message.error(\"Please upload an image file\");\n        return false;\n      }\n      const isLt5M = file.size / 1024 / 1024 < 5;\n      if (!isLt5M) {\n        message.error(\"Image must be smaller than 5MB\");\n        return false;\n      }\n      setThumbnailList([file]);\n      return false;\n    },\n    onRemove: () => {\n      setThumbnailList([]);\n    },\n  };\n\n  if (!material) {\n    return <div>No material selected for editing</div>;\n  }\n\n  return (\n    <div className=\"edit-study-material-form\">\n      <div className=\"form-header\">\n        <div className=\"header-content\">\n          {getMaterialIcon()}\n          <div>\n            <h2>Edit {getMaterialTitle()}</h2>\n            <p>Update the details of \"{material.title}\"</p>\n          </div>\n        </div>\n      </div>\n\n      <Form\n        form={form}\n        layout=\"vertical\"\n        onFinish={handleSubmit}\n        className=\"material-form\"\n      >\n        <div className=\"form-row\">\n          <Form.Item\n            label=\"Level\"\n            name=\"level\"\n            rules={[{ required: true, message: \"Please select a level\" }]}\n            className=\"form-item-half\"\n          >\n            <Select\n              placeholder=\"Select level\"\n              onChange={handleLevelChange}\n              size=\"large\"\n            >\n              <Option value=\"primary\">Primary</Option>\n              <Option value=\"secondary\">Secondary</Option>\n              <Option value=\"advance\">Advance</Option>\n            </Select>\n          </Form.Item>\n\n          <Form.Item\n            label=\"Class\"\n            name=\"className\"\n            rules={[{ required: true, message: \"Please select a class\" }]}\n            className=\"form-item-half\"\n          >\n            <Select\n              placeholder=\"Select class\"\n              size=\"large\"\n              disabled={!form.getFieldValue(\"level\")}\n            >\n              {getClassesForLevel(form.getFieldValue(\"level\")).map(cls => (\n                <Option key={cls} value={cls}>Class {cls}</Option>\n              ))}\n            </Select>\n          </Form.Item>\n        </div>\n\n        <Form.Item\n          label=\"Subject\"\n          name=\"subject\"\n          rules={[{ required: true, message: \"Please select a subject\" }]}\n        >\n          <Select\n            placeholder=\"Select subject\"\n            size=\"large\"\n            disabled={!form.getFieldValue(\"level\")}\n          >\n            {getSubjectsForLevel(form.getFieldValue(\"level\")).map(subject => (\n              <Option key={subject} value={subject}>{subject}</Option>\n            ))}\n          </Select>\n        </Form.Item>\n\n        {/* Additional Classes for Videos */}\n        {material.type === \"videos\" && (\n          <Form.Item\n            label=\"Additional Classes (Optional)\"\n            className=\"additional-classes-section\"\n          >\n            <Select\n              mode=\"multiple\"\n              placeholder=\"Select additional classes that can access this video\"\n              value={selectedAdditionalClasses}\n              onChange={handleAdditionalClassesChange}\n              size=\"large\"\n              disabled={!form.getFieldValue(\"className\")}\n            >\n              {getAvailableAdditionalClasses().map(cls => (\n                <Option key={cls} value={cls}>Class {cls}</Option>\n              ))}\n            </Select>\n            <div className=\"additional-classes-note\">\n              <small>Note: The video will be available to the core class and all selected additional classes</small>\n            </div>\n          </Form.Item>\n        )}\n\n        <Form.Item\n          label=\"Title\"\n          name=\"title\"\n          rules={[{ required: true, message: \"Please enter a title\" }]}\n        >\n          <input\n            type=\"text\"\n            placeholder={`Enter ${getMaterialTitle().toLowerCase()} title`}\n            className=\"form-input\"\n          />\n        </Form.Item>\n\n        {(material.type === \"past-papers\" || material.type === \"books\") && (\n          <Form.Item\n            label=\"Year\"\n            name=\"year\"\n            rules={[{ required: true, message: \"Please enter the year\" }]}\n          >\n            <input\n              type=\"text\"\n              placeholder=\"Enter year (e.g., 2023)\"\n              className=\"form-input\"\n            />\n          </Form.Item>\n        )}\n\n        {/* Video-specific fields */}\n        {material.type === \"videos\" && (\n          <>\n            <div className=\"upload-method-section\">\n              <label className=\"section-label\">Video Source</label>\n              <div className=\"upload-method-options\">\n                <div \n                  className={`method-option ${uploadMethod === \"youtube\" ? \"active\" : \"\"}`}\n                  onClick={() => setUploadMethod(\"youtube\")}\n                >\n                  <FaVideo className=\"method-icon\" />\n                  <span>YouTube Video</span>\n                </div>\n                <div \n                  className={`method-option ${uploadMethod === \"s3url\" ? \"active\" : \"\"}`}\n                  onClick={() => setUploadMethod(\"s3url\")}\n                >\n                  <FaCloudUploadAlt className=\"method-icon\" />\n                  <span>S3 URL</span>\n                </div>\n              </div>\n            </div>\n\n            {uploadMethod === \"youtube\" && (\n              <Form.Item\n                label=\"YouTube Video ID\"\n                name=\"videoID\"\n                rules={[{ required: true, message: \"Please enter YouTube video ID\" }]}\n              >\n                <input\n                  type=\"text\"\n                  placeholder=\"Enter YouTube video ID (e.g., dQw4w9WgXcQ)\"\n                  className=\"form-input\"\n                />\n              </Form.Item>\n            )}\n\n            {uploadMethod === \"s3url\" && (\n              <>\n                <Form.Item\n                  label=\"S3 Video URL\"\n                  name=\"videoUrl\"\n                  rules={[{ required: true, message: \"Please enter S3 video URL\" }]}\n                >\n                  <input\n                    type=\"url\"\n                    placeholder=\"Enter S3 video URL\"\n                    className=\"form-input\"\n                  />\n                </Form.Item>\n\n                <Form.Item\n                  label=\"Thumbnail URL (Optional)\"\n                  name=\"thumbnailUrl\"\n                >\n                  <input\n                    type=\"url\"\n                    placeholder=\"Enter thumbnail URL or upload below\"\n                    className=\"form-input\"\n                  />\n                </Form.Item>\n\n                <Form.Item\n                  label=\"Upload New Thumbnail (Optional)\"\n                  className=\"upload-section\"\n                >\n                  <Upload\n                    {...thumbnailUploadProps}\n                    fileList={thumbnailList}\n                    onChange={({ fileList }) => setThumbnailList(fileList)}\n                    className=\"thumbnail-upload\"\n                  >\n                    <div className=\"upload-area small\">\n                      <FaUpload className=\"upload-icon\" />\n                      <p>Drag & drop thumbnail or click to upload</p>\n                      <p className=\"upload-hint\">Supports JPG, PNG, GIF (Max: 5MB)</p>\n                    </div>\n                  </Upload>\n                </Form.Item>\n              </>\n            )}\n          </>\n        )}\n\n        {/* Document upload for non-video materials */}\n        {material.type !== \"videos\" && (\n          <Form.Item\n            label=\"Upload New Document (Optional)\"\n            className=\"upload-section\"\n          >\n            <Upload\n              {...documentUploadProps}\n              fileList={fileList}\n              onChange={({ fileList }) => setFileList(fileList)}\n              className=\"document-upload\"\n            >\n              <div className=\"upload-area\">\n                <FaUpload className=\"upload-icon\" />\n                <p>Click or drag file to upload new document</p>\n                <p className=\"upload-hint\">Leave empty to keep current document</p>\n              </div>\n            </Upload>\n          </Form.Item>\n        )}\n\n        {/* Thumbnail upload for books */}\n        {material.type === \"books\" && (\n          <Form.Item\n            label=\"Upload New Thumbnail (Optional)\"\n            className=\"upload-section\"\n          >\n            <Upload\n              {...thumbnailUploadProps}\n              fileList={thumbnailList}\n              onChange={({ fileList }) => setThumbnailList(fileList)}\n              className=\"thumbnail-upload\"\n            >\n              <div className=\"upload-area small\">\n                <FaUpload className=\"upload-icon\" />\n                <p>Upload new book cover</p>\n                <p className=\"upload-hint\">Leave empty to keep current thumbnail</p>\n              </div>\n            </Upload>\n          </Form.Item>\n        )}\n\n        <div className=\"form-actions\">\n          <Button\n            type=\"default\"\n            size=\"large\"\n            onClick={onCancel}\n            className=\"cancel-button\"\n            icon={<FaTimes />}\n          >\n            Cancel\n          </Button>\n          <Button\n            type=\"primary\"\n            htmlType=\"submit\"\n            size=\"large\"\n            loading={loading}\n            className=\"submit-button\"\n            icon={<FaSave />}\n          >\n            Update {getMaterialTitle()}\n          </Button>\n        </div>\n      </Form>\n    </div>\n  );\n}\n\nexport default EditStudyMaterialForm;\n", "import React, { useState } from \"react\";\nimport { message } from \"antd\";\nimport { motion } from \"framer-motion\";\nimport { useNavigate } from \"react-router-dom\";\nimport { TbDashboard } from \"react-icons/tb\";\nimport PageTitle from \"../../../components/PageTitle\";\nimport AddStudyMaterialForm from \"./AddStudyMaterialForm\";\nimport SubtitleManager from \"./SubtitleManager\";\nimport StudyMaterialManager from \"./StudyMaterialManager\";\nimport EditStudyMaterialForm from \"./EditStudyMaterialForm\";\nimport \"./index.css\";\nimport {\n  FaVideo,\n  FaFileAlt,\n  FaBook,\n  FaPlus,\n  FaGraduationCap,\n  FaClosedCaptioning,\n  FaCog,\n  FaList\n} from \"react-icons/fa\";\n\nfunction AdminStudyMaterials() {\n  const navigate = useNavigate();\n  const [selectedMaterialType, setSelectedMaterialType] = useState(\"\");\n  const [showAddForm, setShowAddForm] = useState(false);\n  const [showSubtitleManager, setShowSubtitleManager] = useState(false);\n  const [showMaterialManager, setShowMaterialManager] = useState(false);\n  const [showEditForm, setShowEditForm] = useState(false);\n  const [selectedMaterial, setSelectedMaterial] = useState(null);\n\n  const materialTypes = [\n    {\n      key: \"videos\",\n      title: \"Videos\",\n      icon: <FaVideo />,\n      description: \"Add educational videos for students\",\n      color: \"#e74c3c\"\n    },\n    {\n      key: \"study-notes\",\n      title: \"Study Notes\",\n      icon: <FaFileAlt />,\n      description: \"Upload study notes and documents\",\n      color: \"#3498db\"\n    },\n    {\n      key: \"past-papers\",\n      title: \"Past Papers\",\n      icon: <FaGraduationCap />,\n      description: \"Add past examination papers\",\n      color: \"#9b59b6\"\n    },\n    {\n      key: \"books\",\n      title: \"Books\",\n      icon: <FaBook />,\n      description: \"Upload textbooks and reference materials\",\n      color: \"#27ae60\"\n    }\n  ];\n\n  const managementOptions = [\n    {\n      key: \"manage-materials\",\n      title: \"Manage Materials\",\n      icon: <FaCog />,\n      description: \"Edit, delete, and organize study materials\",\n      color: \"#34495e\"\n    },\n    {\n      key: \"subtitles\",\n      title: \"Subtitle Management\",\n      icon: <FaClosedCaptioning />,\n      description: \"Manage video subtitles and captions\",\n      color: \"#f39c12\"\n    }\n  ];\n\n  const handleMaterialTypeSelect = (materialType) => {\n    setSelectedMaterialType(materialType);\n    setShowAddForm(true);\n  };\n\n  const handleManagementOptionSelect = (option) => {\n    if (option === \"subtitles\") {\n      setShowSubtitleManager(true);\n    } else if (option === \"manage-materials\") {\n      setShowMaterialManager(true);\n    }\n  };\n\n  const handleFormClose = () => {\n    setShowAddForm(false);\n    setSelectedMaterialType(\"\");\n  };\n\n  const handleSubtitleManagerClose = () => {\n    setShowSubtitleManager(false);\n  };\n\n  const handleMaterialManagerClose = () => {\n    setShowMaterialManager(false);\n  };\n\n  const handleEditMaterial = (material) => {\n    setSelectedMaterial(material);\n    setShowEditForm(true);\n  };\n\n  const handleEditFormClose = () => {\n    setShowEditForm(false);\n    setSelectedMaterial(null);\n  };\n\n  const handleFormSuccess = (materialType) => {\n    message.success(`${materialType} added successfully!`);\n    setShowAddForm(false);\n    setSelectedMaterialType(\"\");\n  };\n\n  const handleEditSuccess = (materialType) => {\n    message.success(`${materialType} updated successfully!`);\n    setShowEditForm(false);\n    setSelectedMaterial(null);\n    // Refresh the material manager if it's open\n    if (showMaterialManager) {\n      // The StudyMaterialManager component will handle its own refresh\n    }\n  };\n\n  return (\n    <div className=\"admin-study-materials\">\n      <div className=\"flex items-center gap-4 mb-4\">\n        {/* Dashboard Shortcut */}\n        <motion.button\n          whileHover={{ scale: 1.05 }}\n          whileTap={{ scale: 0.95 }}\n          onClick={() => navigate('/admin/dashboard')}\n          className=\"flex items-center gap-2 px-3 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors duration-200 shadow-md\"\n        >\n          <TbDashboard className=\"w-4 h-4\" />\n          <span className=\"hidden sm:inline text-sm font-medium\">Dashboard</span>\n        </motion.button>\n\n        <PageTitle title=\"Study Materials Management\" />\n      </div>\n      \n      {showSubtitleManager ? (\n        <div className=\"subtitle-manager-container\">\n          <div className=\"form-header\">\n            <button\n              className=\"back-button\"\n              onClick={handleSubtitleManagerClose}\n            >\n              ← Back to Material Types\n            </button>\n            <h2>Subtitle Management</h2>\n          </div>\n          <SubtitleManager />\n        </div>\n      ) : showMaterialManager ? (\n        <div className=\"material-manager-container\">\n          <div className=\"form-header\">\n            <button\n              className=\"back-button\"\n              onClick={handleMaterialManagerClose}\n            >\n              ← Back to Main Menu\n            </button>\n            <h2>Study Materials Management</h2>\n          </div>\n          <StudyMaterialManager onEdit={handleEditMaterial} />\n        </div>\n      ) : showEditForm ? (\n        <div className=\"edit-form-container\">\n          <div className=\"form-header\">\n            <button\n              className=\"back-button\"\n              onClick={handleEditFormClose}\n            >\n              ← Back to Materials List\n            </button>\n          </div>\n          <EditStudyMaterialForm\n            material={selectedMaterial}\n            onSuccess={handleEditSuccess}\n            onCancel={handleEditFormClose}\n          />\n        </div>\n      ) : showAddForm ? (\n        <div className=\"add-form-container\">\n          <div className=\"form-header\">\n            <button\n              className=\"back-button\"\n              onClick={handleFormClose}\n            >\n              ← Back to Main Menu\n            </button>\n            <h2>\n              Add {materialTypes.find(t => t.key === selectedMaterialType)?.title}\n            </h2>\n          </div>\n\n          <AddStudyMaterialForm\n            materialType={selectedMaterialType}\n            onSuccess={handleFormSuccess}\n            onCancel={handleFormClose}\n          />\n        </div>\n      ) : (\n        <div className=\"main-menu-container\">\n          <div className=\"header-section\">\n            <h2>Study Materials Administration</h2>\n            <p>Manage your educational content - add new materials or edit existing ones</p>\n          </div>\n\n          <div className=\"menu-sections\">\n            <div className=\"menu-section\">\n              <h3>\n                <FaPlus className=\"section-icon\" />\n                Add New Materials\n              </h3>\n              <p>Upload new study materials for students</p>\n              <div className=\"material-types-grid\">\n                {materialTypes.map((type) => (\n                  <div\n                    key={type.key}\n                    className=\"material-type-card\"\n                    onClick={() => handleMaterialTypeSelect(type.key)}\n                    style={{ borderColor: type.color }}\n                  >\n                    <div className=\"card-icon\" style={{ color: type.color }}>\n                      {type.icon}\n                    </div>\n                    <h4>{type.title}</h4>\n                    <p>{type.description}</p>\n                    <div className=\"add-button\" style={{ backgroundColor: type.color }}>\n                      <FaPlus />\n                      <span>Add {type.title}</span>\n                    </div>\n                  </div>\n                ))}\n              </div>\n            </div>\n\n            <div className=\"menu-section\">\n              <h3>\n                <FaList className=\"section-icon\" />\n                Manage Existing Materials\n              </h3>\n              <p>Edit, delete, and organize your study materials</p>\n              <div className=\"management-options-grid\">\n                {managementOptions.map((option) => (\n                  <div\n                    key={option.key}\n                    className=\"management-option-card\"\n                    onClick={() => handleManagementOptionSelect(option.key)}\n                    style={{ borderColor: option.color }}\n                  >\n                    <div className=\"card-icon\" style={{ color: option.color }}>\n                      {option.icon}\n                    </div>\n                    <h4>{option.title}</h4>\n                    <p>{option.description}</p>\n                    <div className=\"manage-button\" style={{ backgroundColor: option.color }}>\n                      <FaCog />\n                      <span>Open {option.title}</span>\n                    </div>\n                  </div>\n                ))}\n              </div>\n            </div>\n          </div>\n        </div>\n      )}\n    </div>\n  );\n}\n\nexport default AdminStudyMaterials;\n"], "names": ["default", "axiosInstance", "require", "getStudyMaterial", "async", "post", "filters", "error", "response", "getAllVideos", "get", "data", "_error$response", "success", "message", "addVideo", "videoData", "onUploadProgress", "arguments", "length", "undefined", "config", "FormData", "headers", "timeout", "progressEvent", "percentCompleted", "Math", "round", "loaded", "total", "addNote", "formData", "addPastPaper", "addBook", "updateVideo", "id", "put", "concat", "updateNote", "updatePastPaper", "updateBook", "deleteVideo", "delete", "deleteNote", "deletePastPaper", "deleteBook", "getAllStudyMaterials", "params", "URLSearchParams", "materialType", "append", "level", "className", "subject", "toString", "_ref", "title", "isMobile", "setIsMobile", "useState", "useEffect", "window", "innerWidth", "_jsx", "children", "primarySubjects", "secondarySubjects", "advanceSubjects", "Option", "Select", "onSuccess", "onCancel", "form", "Form", "useForm", "dispatch", "useDispatch", "loading", "setLoading", "fileList", "setFileList", "thumbnailList", "setThumbnailList", "videoFileList", "setVideoFileList", "uploadMethod", "setUploadMethod", "uploadProgress", "setUploadProgress", "uploadStatus", "setUploadStatus", "uploadSpeed", "setUploadSpeed", "estimatedTime", "setEstimatedTime", "uploadStartTime", "setUploadStartTime", "isDragOver", "setIsDragOver", "availableSubjects", "setAvailableSubjects", "availableClasses", "setAvailableClasses", "selectedAdditionalClasses", "setSelectedAdditionalClasses", "uploadProps", "beforeUpload", "maxCount", "accept", "thumbnailUploadProps", "getMaterialTitle", "_jsxs", "getMaterialIcon", "FaVideo", "FaFileAlt", "FaGraduationCap", "FaBook", "layout", "onFinish", "timeoutId", "ShowLoading", "setTimeout", "HideLoading", "videoValues", "_objectSpread", "values", "additionalClasses", "originFileObj", "Object", "keys", "for<PERSON>ach", "key", "Array", "isArray", "item", "console", "log", "name", "Date", "now", "progress", "uploadedBytes", "speed", "estimatedSeconds", "Error", "status", "resetFields", "_response$data", "errorMessage", "_error$response2", "_error$response3", "code", "_error$response$data", "clearTimeout", "initialValues", "<PERSON><PERSON>", "label", "rules", "required", "placeholder", "onChange", "getSubjectsForLevel", "getClassesForLevel", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "size", "value", "filteredAdditionalClasses", "filter", "cls", "map", "mode", "classes", "style", "width", "getFieldValue", "type", "toLowerCase", "_Fragment", "onClick", "FaCloudUploadAlt", "Upload", "_ref2", "onDrop", "e", "preventDefault", "imageFiles", "from", "dataTransfer", "files", "file", "startsWith", "uid", "url", "URL", "createObjectURL", "onDragOver", "onDragEnter", "onDragLeave", "FaUpload", "_ref3", "_ref4", "_ref5", "_ref6", "toFixed", "ceil", "transition", "<PERSON><PERSON>", "disabled", "htmlType", "SubtitleManager", "videos", "setVideos", "uploadModalVisible", "setUploadModalVisible", "<PERSON><PERSON><PERSON><PERSON>", "setSelectedVideo", "selectedLanguage", "setSelectedLanguage", "languageName", "setLanguageName", "generatingSubtitles", "setGeneratingSubtitles", "languages", "fetchVideos", "handleGenerateSubtitles", "videoId", "language", "prev", "info", "generateSubtitles", "columns", "dataIndex", "render", "record", "fontWeight", "marginBottom", "fontSize", "color", "_", "video", "_video$subtitles", "subtitleGenerationStatus", "Tag", "icon", "RobotOutlined", "hasSubtitles", "subtitles", "Space", "FileTextOutlined", "sub", "isAutoGenerated", "getSubtitleStatus", "videoUrl", "includes", "GlobalOutlined", "videoID", "PlayCircleOutlined", "<PERSON><PERSON><PERSON>", "_id", "UploadOutlined", "ReloadOutlined", "endsWith", "onRemove", "Table", "dataSource", "<PERSON><PERSON><PERSON>", "pagination", "pageSize", "showSizeChanger", "showQuickJumper", "showTotal", "scroll", "x", "Modal", "open", "onOk", "uploadSubtitle", "warning", "okText", "cancelText", "lang", "find", "l", "marginTop", "Search", "Input", "onEdit", "materials", "setMaterials", "setFilters", "searchText", "setSearchText", "fetchMaterials", "handleFilterChange", "newFilters", "getMaterialTypeLabel", "filteredMaterials", "material", "char<PERSON>t", "toUpperCase", "slice", "year", "FaEdit", "danger", "FaTrash", "confirm", "replace", "content", "okType", "handleDelete", "allowClear", "target", "thumbnailUrl", "thumbnail", "documentUploadProps", "JSON", "stringify", "getAvailableAdditionalClasses", "currentLevel", "currentClass", "FaTimes", "FaSave", "_materialTypes$find", "navigate", "useNavigate", "selectedMaterialType", "setSelectedMaterialType", "showAddForm", "setShowAddForm", "showSubtitleManager", "setShowSubtitleManager", "showMaterialManager", "setShowMaterialManager", "showEditForm", "setShowEditForm", "selectedMaterial", "setSelectedMaterial", "materialTypes", "description", "managementOptions", "FaCog", "FaClosedCaptioning", "handleFormClose", "handleEditFormClose", "motion", "button", "whileHover", "scale", "whileTap", "TbDashboard", "Page<PERSON><PERSON>le", "handleSubtitleManagerClose", "handleMaterialManagerClose", "StudyMaterialManager", "EditStudyMaterialForm", "t", "AddStudyMaterialForm", "FaPlus", "handleMaterialTypeSelect", "borderColor", "backgroundColor", "FaList", "option", "handleManagementOptionSelect"], "sourceRoot": ""}