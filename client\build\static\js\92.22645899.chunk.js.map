{"version": 3, "file": "static/js/92.22645899.chunk.js", "mappings": "8OAAA,MAAQA,QAASC,GAAkBC,EAAQ,MAG9BC,EAAmBC,UAC5B,IAEI,aADuBH,EAAcI,KAAK,+BAAiCC,EAE/E,CAAE,MAAOC,GACL,OAAOA,EAAMC,QACjB,GAcSC,EAAeL,UACxB,IAEI,aADuBH,EAAcS,IAAI,sCACzBC,IACpB,CAAE,MAAOJ,GAAQ,IAADK,EACZ,OAAqB,QAAdA,EAAAL,EAAMC,gBAAQ,IAAAI,OAAA,EAAdA,EAAgBD,OAAQ,CAAEE,SAAS,EAAOC,QAAS,yBAC9D,GAMSC,EAAWX,eAAOY,GAAwC,IAA7BC,EAAgBC,UAAAC,OAAA,QAAAC,IAAAF,UAAA,GAAAA,UAAA,GAAG,KACzD,IACI,MACMG,EADaL,aAAqBM,SACZ,CACxBC,QAAS,CACL,eAAgB,uBAEpBC,QAAS,IACTP,iBAAkBA,EAAoBQ,IAClC,MAAMC,EAAmBC,KAAKC,MAA8B,IAAvBH,EAAcI,OAAgBJ,EAAcK,OAEjFb,EAAiBS,EAAkBD,EAAcI,OAAQJ,EAAcK,MAAM,OAC7EV,GACJ,CACAI,QAAS,KAIb,aADuBvB,EAAcI,KAAK,uBAAwBW,EAAWK,EAEjF,CAAE,MAAOd,GACL,OAAOA,EAAMC,QACjB,CACJ,EAGauB,EAAU3B,UACnB,IAMI,aALuBH,EAAcI,KAAK,sBAAuB2B,EAAU,CACvET,QAAS,CACL,eAAgB,wBAI5B,CAAE,MAAOhB,GACL,OAAOA,EAAMC,QACjB,GAISyB,EAAe7B,UACxB,IAMI,aALuBH,EAAcI,KAAK,4BAA6B2B,EAAU,CAC7ET,QAAS,CACL,eAAgB,wBAI5B,CAAE,MAAOhB,GACL,OAAOA,EAAMC,QACjB,GAIS0B,EAAU9B,UACnB,IAMI,aALuBH,EAAcI,KAAK,sBAAuB2B,EAAU,CACvET,QAAS,CACL,eAAgB,wBAI5B,CAAE,MAAOhB,GACL,OAAOA,EAAMC,QACjB,GAMS2B,EAAc/B,MAAOgC,EAAIpB,KAClC,IACI,IAAIK,EAAS,CACTE,QAAS,CACL,eAAgB,qBAKpBP,aAAqBM,WACrBD,EAAOE,QAAQ,gBAAkB,uBAIrC,aADuBtB,EAAcoC,IAAI,2BAADC,OAA4BF,GAAMpB,EAAWK,EAEzF,CAAE,MAAOd,GACL,OAAOA,EAAMC,QACjB,GAIS+B,EAAanC,MAAOgC,EAAIJ,KACjC,IAMI,aALuB/B,EAAcoC,IAAI,0BAADC,OAA2BF,GAAMJ,EAAU,CAC/ET,QAAS,CACL,eAAgB,wBAI5B,CAAE,MAAOhB,GACL,OAAOA,EAAMC,QACjB,GAISgC,EAAkBpC,MAAOgC,EAAIJ,KACtC,IAMI,aALuB/B,EAAcoC,IAAI,gCAADC,OAAiCF,GAAMJ,EAAU,CACrFT,QAAS,CACL,eAAgB,wBAI5B,CAAE,MAAOhB,GACL,OAAOA,EAAMC,QACjB,GAISiC,EAAarC,MAAOgC,EAAIJ,KACjC,IAMI,aALuB/B,EAAcoC,IAAI,0BAADC,OAA2BF,GAAMJ,EAAU,CAC/ET,QAAS,CACL,eAAgB,wBAI5B,CAAE,MAAOhB,GACL,OAAOA,EAAMC,QACjB,GAMSkC,EAActC,UACvB,IAEI,aADuBH,EAAc0C,OAAO,2BAADL,OAA4BF,GAE3E,CAAE,MAAO7B,GACL,OAAOA,EAAMC,QACjB,GAISoC,EAAaxC,UACtB,IAEI,aADuBH,EAAc0C,OAAO,0BAADL,OAA2BF,GAE1E,CAAE,MAAO7B,GACL,OAAOA,EAAMC,QACjB,GAISqC,EAAkBzC,UAC3B,IAEI,aADuBH,EAAc0C,OAAO,gCAADL,OAAiCF,GAEhF,CAAE,MAAO7B,GACL,OAAOA,EAAMC,QACjB,GAISsC,EAAa1C,UACtB,IAEI,aADuBH,EAAc0C,OAAO,0BAADL,OAA2BF,GAE1E,CAAE,MAAO7B,GACL,OAAOA,EAAMC,QACjB,GAISuC,EAAuB3C,iBAAyB,IAAlBE,EAAOY,UAAAC,OAAA,QAAAC,IAAAF,UAAA,GAAAA,UAAA,GAAG,CAAC,EAClD,IACI,MAAM8B,EAAS,IAAIC,gBACf3C,EAAQ4C,cAAcF,EAAOG,OAAO,eAAgB7C,EAAQ4C,cAC5D5C,EAAQ8C,OAAOJ,EAAOG,OAAO,QAAS7C,EAAQ8C,OAC9C9C,EAAQ+C,WAAWL,EAAOG,OAAO,YAAa7C,EAAQ+C,WACtD/C,EAAQgD,SAASN,EAAOG,OAAO,UAAW7C,EAAQgD,SAGtD,aADuBrD,EAAcS,IAAI,kCAAD4B,OAAmCU,EAAOO,YAEtF,CAAE,MAAOhD,GACL,OAAOA,EAAMC,QACjB,CACJ,C,kEC9NO,MAAMgD,EAAkB,CAC7B,cACA,yBACA,YACA,YACA,gBACA,UACA,WACA,aACA,gBACA,yBACA,kBACA,SACA,wBAGWC,EAAoB,CAC/B,UACA,cACA,UACA,YACA,YACA,SACA,UACA,WACA,mBACA,YACA,WACA,YACA,uBACA,4BACA,cACA,eAGWC,EAAkB,CAC7B,UACA,cACA,UACA,YACA,YACA,SACA,UACA,WACA,mBACA,YACA,WACA,YACA,uBACA,4BACA,cACA,c,kKChDFC,IAAAA,cAAyB,SAEzBC,EAAAA,oBAA6BC,UAAS,gEAWtC,GAAwB,qBAAbC,SAA0B,CACnC,MAAMC,EAAQD,SAASE,cAAc,SACrCD,EAAME,YAVU,6GAWhBH,SAASI,KAAKC,YAAYJ,EAC5B,CAEA,MAgQA,EAhQiBK,IAA+C,IAA9C,YAAEC,EAAW,WAAEC,EAAU,YAAEC,GAAaH,EACxD,MAAOI,EAAOC,IAAYC,EAAAA,EAAAA,UAAS,KAC5BC,EAAWC,IAAgBF,EAAAA,EAAAA,WAAS,IACpCG,EAAiBC,IAAsBJ,EAAAA,EAAAA,UAAS,IAChDK,EAAYC,IAAiBN,EAAAA,EAAAA,UAAS,GACvCO,GAAaC,EAAAA,EAAAA,QAAO,IACpBC,GAAeD,EAAAA,EAAAA,QAAO,MACtBE,GAAgBF,EAAAA,EAAAA,QAAO,CAAC,GAoCxBG,EAAqBjF,MAAOkF,EAAKC,EAAWC,KAChD,MAAMC,EAAe,IAAID,GAEzB,IAAK,IAAIE,EAAIH,EAAWG,GAAKJ,EAAIK,SAAUD,IACzC,IACE,MAAME,QAAaN,EAAIO,QAAQH,GAC/BD,EAAaK,KAAKF,GAClBnB,EAAS,IAAIgB,IACbX,EAAoBY,EAAIJ,EAAIK,SAAY,WAGlC,IAAII,SAAQC,GAAWC,WAAWD,EAAS,KACnD,CAAE,MAAOzF,GACP2F,QAAQ3F,MAAM,sBAAD+B,OAAuBoD,EAAC,KAAKnF,EAC5C,CACF,EAGI4F,EAAa/F,MAAOwF,EAAMQ,KAC9B,MAAMC,EAASpB,EAAWqB,QAAQF,GAClC,GAAKC,IAAUjB,EAAckB,QAAQF,IAAWjB,EAAamB,QAE7D,IACElB,EAAckB,QAAQF,IAAS,EAE/B,MAAMG,EAAWX,EAAKY,YAAY,CAAEC,MAAO,IAErCA,EADiBtB,EAAamB,QAAQI,YACbH,EAASI,MAClCC,EAAiBhB,EAAKY,YAAY,CAAEC,UAEpCI,EAAUR,EAAOS,WAAW,MAClCT,EAAOU,OAASH,EAAeG,OAC/BV,EAAOM,MAAQC,EAAeD,MAC9BN,EAAOtC,MAAM4C,MAAQ,OACrBN,EAAOtC,MAAMgD,OAAS,OAEtB,MAAMC,EAAgB,CACpBC,cAAeJ,EACfN,SAAUK,SAGNhB,EAAKsB,OAAOF,GAAeG,QACjCjB,QAAQkB,IAAI,QAAD9E,OAAS8D,EAAQ,EAAC,aAC/B,CAAE,MAAO7F,GACP2F,QAAQ3F,MAAM,wBAAD+B,OAAyB8D,EAAQ,EAAC,KAAK7F,EACtD,CAAC,QACC6E,EAAckB,QAAQF,IAAS,CACjC,GAuCF,OApCAiB,EAAAA,EAAAA,YAAU,KACJhD,GAAeE,IACjBE,EAAS,IACTO,EAAc,GACdF,EAAmB,GACnBG,EAAWqB,QAAU,GACrBlB,EAAckB,QAAU,CAAC,EA1FXlG,WAChB,IACEwE,GAAa,GACbE,EAAmB,GAEnB,MAAMQ,QAAY1B,EAAAA,YAAqB0D,GAAKH,QAC5CjB,QAAQkB,IAAI,cAEZpC,EAAcM,EAAIK,UAGlB,MAAM4B,EAAqB5F,KAAK6F,IAAI,EAAGlC,EAAIK,UACrC8B,EAAY,GAElB,IAAK,IAAI/B,EAAI,EAAGA,GAAK6B,EAAoB7B,IAAK,CAC5C,MAAME,QAAaN,EAAIO,QAAQH,GAC/B+B,EAAU3B,KAAKF,GACfd,EAAoBY,EAAIJ,EAAIK,SAAY,IAC1C,CAEAlB,EAASgD,GACT7C,GAAa,GAGTU,EAAIK,SAAW4B,GACjBlC,EAAmBC,EAAKiC,EAAqB,EAAGE,EAGpD,CAAE,MAAOlH,GACP2F,QAAQ3F,MAAM,qBAAsBA,GACpCqE,GAAa,EACf,GA4DE8C,CAAUnD,GACZ,GAEC,CAACF,EAAaE,KAGjB8C,EAAAA,EAAAA,YAAU,KACJ7C,EAAMrD,OAAS,GAAKgE,EAAamB,SACnC9B,EAAMmD,SAAQ,CAAC/B,EAAMQ,KACnBD,EAAWP,EAAMQ,EAAM,GAE3B,GAEC,CAAC5B,KAGJ6C,EAAAA,EAAAA,YAAU,KACR,MAAMO,EAAeA,KACfpD,EAAMrD,OAAS,GACjBqD,EAAMmD,SAAQ,CAAC/B,EAAMQ,KACnBD,EAAWP,EAAMQ,EAAM,GAE3B,EAIF,OADAyB,OAAOC,iBAAiB,SAAUF,GAC3B,IAAMC,OAAOE,oBAAoB,SAAUH,EAAa,GAC9D,CAACpD,KAGFwD,EAAAA,EAAAA,MAACrE,IAAU,CACTsE,OAAQ5D,EACR6D,eAAgB5D,EAChB6D,aAAa,mBACbpE,MAAO,CACLqE,QAAS,CACPC,gBAAiB,uBAEnBC,QAAS,CACPC,IAAK,MACLC,KAAM,MACNC,MAAO,OACPC,OAAQ,OACRC,YAAa,OACbC,UAAW,wBACXjC,MAAO,MACPI,OAAQ,MACR8B,QAAS,OACTC,aAAc,OACdC,SAAU,WAEZC,SAAA,EAEFC,EAAAA,EAAAA,KAAA,UACEC,QAAS5E,EACTP,MAAO,CACLoF,SAAU,WACVZ,IAAK,OACLE,MAAO,OACPW,WAAY,cACZC,OAAQ,OACRC,SAAU,OACVC,OAAQ,UACRC,OAAQ,GACRR,SACH,OAIDhB,EAAAA,EAAAA,MAAA,OACEyB,IAAKtE,EACLpB,MAAO,CACLgD,OAAQ,OACRgC,SAAU,OACVF,QAAS,OACTa,eAAgB,QAChBV,SAAA,CAEDrE,IACCqD,EAAAA,EAAAA,MAAA,OAAKjE,MAAO,CACV4F,QAAS,OACTC,cAAe,SACfC,WAAY,SACZC,eAAgB,SAChB/C,OAAQ,QACRgD,MAAO,QACPf,SAAA,EACAC,EAAAA,EAAAA,KAAA,OAAKlF,MAAO,CACV4C,MAAO,OACPI,OAAQ,OACRsC,OAAQ,oBACRW,UAAW,oBACXlB,aAAc,MACdmB,UAAW,0BACXC,aAAc,WAEhBjB,EAAAA,EAAAA,KAAA,KAAAD,SAAG,oBACHC,EAAAA,EAAAA,KAAA,OAAKlF,MAAO,CACV4C,MAAO,QACPI,OAAQ,MACRsB,gBAAiB,UACjBS,aAAc,MACdC,SAAU,SACVoB,UAAW,QACXnB,UACAC,EAAAA,EAAAA,KAAA,OAAKlF,MAAO,CACV4C,MAAM,GAADrE,OAAKuC,EAAe,KACzBkC,OAAQ,OACRsB,gBAAiB,UACjB+B,WAAY,wBAGhBpC,EAAAA,EAAAA,MAAA,SAAOjE,MAAO,CAAEoG,UAAW,OAAQnB,SAAA,CAChCrH,KAAKC,MAAMiD,GAAiB,iBAKlCL,EAAM6F,KAAI,CAACzE,EAAMQ,KAChB6C,EAAAA,EAAAA,KAAA,OAEElF,MAAO,CACLmG,aAAc,OACdP,QAAS,OACTC,cAAe,SACfC,WAAY,UACZb,UAEFC,EAAAA,EAAAA,KAAA,UACEQ,IAAKa,IACHrF,EAAWqB,QAAQF,GAASkE,CAAO,EAErCvG,MAAO,CACLwG,SAAU,OACVxD,OAAQ,OACRsC,OAAQ,sBAfPjD,KAqBRrB,EAAaP,EAAMrD,SAAWwD,IAC7BqD,EAAAA,EAAAA,MAAA,OAAKjE,MAAO,CACVyG,UAAW,SACX3B,QAAS,OACTkB,MAAO,OACPU,UAAW,UACXzB,SAAA,CAAC,+BAC4BxE,EAAMrD,OAAO,IAAE4D,EAAW,YAIlD,E,kCC6VjB,QAtkBA,WACE,MAAM,KAAE2F,IAASC,EAAAA,EAAAA,KAAaC,GAAUA,EAAMF,OACxCG,GAAWC,EAAAA,EAAAA,MAGXC,GAAgB,OAAJL,QAAI,IAAJA,OAAI,EAAJA,EAAMtH,QAAS,UAC3B4H,EAAiBD,EAAUE,cAC3BC,EAAkC,YAAnBF,EACjBxH,EAAAA,GACmB,cAAnBwH,EACEvH,EAAAA,GACAC,EAAAA,IAGN2D,EAAAA,EAAAA,YAAU,KACRnB,QAAQkB,IAAI,6CAAoC2D,GAChD7E,QAAQkB,IAAI,yDAAgD4D,GAC5D9E,QAAQkB,IAAI,gDAAuC8D,GACnDhF,QAAQkB,IAAI,4CAAmCsD,EAAK,GACnD,CAACK,EAAWC,EAAgBE,EAAcR,IAG7C,MAAMS,EAAwC,YAAnBH,EACvB,CAAC,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,KACZ,cAAnBA,EACE,CAAC,SAAU,SAAU,SAAU,UAC/B,CAAC,SAAU,WAGVI,EAAWC,IAAgB3G,EAAAA,EAAAA,UAAS,gBACpC4G,EAAeC,IAAoB7G,EAAAA,EAAAA,WAAa,OAAJgG,QAAI,IAAJA,OAAI,EAAJA,EAAMc,SAAa,OAAJd,QAAI,IAAJA,OAAI,EAAJA,EAAMrH,YAAa,QAC9EoI,EAAiBC,IAAsBhH,EAAAA,EAAAA,UAAS,OAGjDiH,GAAuB,OAAJjB,QAAI,IAAJA,OAAI,EAAJA,EAAMc,SAAa,OAAJd,QAAI,IAAJA,OAAI,EAAJA,EAAMrH,YACvCuI,EAAWC,IAAgBnH,EAAAA,EAAAA,UAAS,KACpCC,EAAWC,IAAgBF,EAAAA,EAAAA,WAAS,IACpCnE,EAAOuL,IAAYpH,EAAAA,EAAAA,UAAS,OAE5BL,EAAa0H,IAAkBrH,EAAAA,EAAAA,WAAS,IACxCH,EAAayH,IAAkBtH,EAAAA,EAAAA,UAAS,KACxCuH,EAAkBC,IAAuBxH,EAAAA,EAAAA,UAAS,KAClDyH,EAAmBC,IAAwB1H,EAAAA,EAAAA,WAAS,IAIpD2H,EAAYC,IAAiB5H,EAAAA,EAAAA,UAAS,KACtC6H,EAAQC,IAAa9H,EAAAA,EAAAA,UAAS,WAGrC2C,EAAAA,EAAAA,YAAU,KACR,MAAMoF,GAAgB,OAAJ/B,QAAI,IAAJA,OAAI,EAAJA,EAAMc,SAAa,OAAJd,QAAI,IAAJA,OAAI,EAAJA,EAAMrH,WACnCoJ,GAA+B,QAAlBnB,IAA4BW,EAAiB9K,QAC5DoK,EAAiBkB,EACnB,GACC,CAAC/B,EAAMY,EAAeW,EAAiB9K,UAG1CkG,EAAAA,EAAAA,YAAU,KACR,GAAQ,OAAJqD,QAAI,IAAJA,GAAAA,EAAMtH,MAAO,CAEQ8H,EAAawB,SAASjB,IACF,QAApBA,IACrBvF,QAAQkB,IAAI,gEACZsE,EAAmB,OAEvB,IACC,CAAK,OAAJhB,QAAI,IAAJA,OAAI,EAAJA,EAAMtH,MAAO8H,EAAcO,IAG/B,MAAMkB,GAA8BC,EAAAA,EAAAA,cAAY,KAC9CV,EAAoBf,EAAmB,GACtC,CAACA,IAGE0B,GAAiBD,EAAAA,EAAAA,cAAYxM,UACjC,GAAKgL,GAA+B,YAAlBE,EAAlB,CAIA1G,GAAa,GACbkH,EAAS,MACTjB,GAASiC,EAAAA,EAAAA,OAET,IAEE,MAAMC,EAAwC,QAAlBzB,EAA0B,MACpDA,EAAc/H,WAAWyJ,QAAQ,QAAS,IAEtCrM,EAAO,CACX2H,QAAS8C,EACT/H,UAAW0J,EACXzJ,QAASmI,GAEPV,IACFpK,EAAKyC,MAAQ2H,GAGf,MAAMkC,QAAY9M,EAAAA,EAAAA,IAAiBQ,GAEnC,GAAmB,MAAfsM,EAAIC,QAAkBD,EAAItM,KAAKE,QAAS,CAC1C,MAAM+K,EAA8B,UAAlBqB,EAAItM,KAAKA,KAAmB,GAAKsM,EAAItM,KAAKA,KAC5DkL,EAAaD,EACf,MACEC,EAAa,IACbC,EAAS,mBAADxJ,OAAoB8I,EAAS,uBAEzC,CAAE,MAAO7K,GACP2F,QAAQ3F,MAAM,iCAAkCA,GAChDsL,EAAa,IACbC,EAAS,kBAADxJ,OAAmB8I,EAAS,iDACtC,CAAC,QACCxG,GAAa,GACbiG,GAASsC,EAAAA,EAAAA,MACX,CApCA,CAoCA,GACC,CAAC/B,EAAWE,EAAeG,EAAiBV,EAAWF,KAG1DxD,EAAAA,EAAAA,YAAU,KACJqD,GAAQK,GACV4B,GACF,GACC,CAACjC,EAAMK,EAAW4B,KAGrBtF,EAAAA,EAAAA,YAAU,KAEJqD,GAAQK,GAAaK,GAAaE,GAAmC,YAAlBA,GACrDuB,GACF,GACC,CAACnC,EAAMK,EAAWK,EAAWE,EAAeG,EAAiBoB,IAGhE,MAaMO,EAAqB/J,IACzBwI,EAAa,IACbN,EAAiBlI,GACjB+I,GAAqB,EAAM,EAQvBiB,GAA6BC,EAAAA,EAAAA,UAAQ,KACzC,IAAK1B,GAAkC,IAArBA,EAAUzK,OAC1B,MAAO,GAGT,IAAIoM,EAAW3B,EAGf,GAAIS,EAAWmB,OAAQ,CACrB,MAAMC,EAAcpB,EAAWpB,cAC/BsC,EAAWA,EAASG,QAAOC,GACzBA,EAASC,MAAM3C,cAAcyB,SAASe,IACtCE,EAASrK,QAAQ2H,cAAcyB,SAASe,IACvCE,EAASE,MAAQF,EAASE,KAAK5C,cAAcyB,SAASe,IAE3D,CAgCA,OA7BAF,EAASO,MAAK,CAACC,EAAGC,IACD,WAAXzB,EAEEwB,EAAEF,MAAQG,EAAEH,KACPI,SAASD,EAAEH,MAAQI,SAASF,EAAEF,MAI9BE,EAAEF,OAASG,EAAEH,MAAc,GAC1BE,EAAEF,MAAQG,EAAEH,KAAa,EACvB,EACQ,WAAXtB,EAELwB,EAAEF,MAAQG,EAAEH,KACPI,SAASF,EAAEF,MAAQI,SAASD,EAAEH,MAI9BE,EAAEF,OAASG,EAAEH,MAAc,GAC1BE,EAAEF,MAAQG,EAAEH,KAAa,EACvB,EAGLE,EAAEH,MAAMM,cAAcF,EAAEJ,SAM5BL,CAAQ,GACd,CAAC3B,EAAWS,EAAYE,EAAQnB,IA+DnC,OACEpD,EAAAA,EAAAA,MAAA,OAAK3E,UAAU,yDAAwD2F,SAAA,EAErEC,EAAAA,EAAAA,KAACkF,EAAAA,EAAOC,IAAG,CACTC,QAAS,CAAEC,QAAS,EAAGC,GAAI,IAC3BC,QAAS,CAAEF,QAAS,EAAGC,EAAG,GAC1BlL,UAAU,2DAA0D2F,UAEpEC,EAAAA,EAAAA,KAAA,OAAK5F,UAAU,yBAAwB2F,UACrChB,EAAAA,EAAAA,MAAA,OAAK3E,UAAU,oCAAmC2F,SAAA,EAChDhB,EAAAA,EAAAA,MAAA,OAAK3E,UAAU,8BAA6B2F,SAAA,EAC1CC,EAAAA,EAAAA,KAAA,OAAK5F,UAAU,sEAAqE2F,UAClFC,EAAAA,EAAAA,KAACwF,EAAAA,IAAO,CAACpL,UAAU,0BAErB2E,EAAAA,EAAAA,MAAA,OAAAgB,SAAA,EACEC,EAAAA,EAAAA,KAAA,MAAI5F,UAAU,0BAAyB2F,SAAC,qBACxChB,EAAAA,EAAAA,MAAA,KAAG3E,UAAU,wBAAuB2F,SAAA,CAAC,+CACU+B,EAAU,uBAI7D9B,EAAAA,EAAAA,KAAA,OAAK5F,UAAU,kBAAiB2F,UAC9BhB,EAAAA,EAAAA,MAAA,OAAK3E,UAAU,oDAAmD2F,SAAA,EAChEC,EAAAA,EAAAA,KAAA,OAAK5F,UAAU,6BAA4B2F,SAAC,mBAC5CC,EAAAA,EAAAA,KAAA,OAAK5F,UAAU,oBAAmB2F,SAAW,OAAT+B,QAAS,IAATA,OAAS,EAATA,EAAW2D,6BAOzD1G,EAAAA,EAAAA,MAAA,OAAK3E,UAAU,wBAAuB2F,SAAA,EAEpCC,EAAAA,EAAAA,KAAA,OAAK5F,UAAU,OAAM2F,UACnBC,EAAAA,EAAAA,KAAA,OAAK5F,UAAU,aAAY2F,SACxB,CACC,CAAE2F,IAAK,cAAeC,MAAO,QAASC,KAAMC,EAAAA,KAC5C,CAAEH,IAAK,cAAeC,MAAO,cAAeC,KAAME,EAAAA,IAClD,CAAEJ,IAAK,QAASC,MAAO,QAASC,KAAMG,EAAAA,MACtC3E,KAAK4E,IACLjH,EAAAA,EAAAA,MAAA,UAEE3E,UAAS,aAAAf,OAAe8I,IAAc6D,EAAIN,IAAM,SAAW,IAC3DzF,QAASA,IAlLE+F,KACvBpD,EAAa,IACbR,EAAa4D,GACb3C,EAAc,IACdE,EAAU,SAAS,EA8KQ0C,CAAgBD,EAAIN,KAAK3F,SAAA,EAExCC,EAAAA,EAAAA,KAACgG,EAAIJ,KAAI,KACT5F,EAAAA,EAAAA,KAAA,QAAAD,SAAOiG,EAAIL,UALNK,EAAIN,YAYjB1F,EAAAA,EAAAA,KAACkF,EAAAA,EAAOC,IAAG,CACTC,QAAS,CAAEC,QAAS,EAAGC,EAAG,IAC1BC,QAAS,CAAEF,QAAS,EAAGC,EAAG,GAC1BnE,WAAY,CAAE+E,MAAO,IACrB9L,UAAU,OAAM2F,UAEhBhB,EAAAA,EAAAA,MAAA,OAAK3E,UAAU,WAAU2F,SAAA,EACvBhB,EAAAA,EAAAA,MAAA,OAAK3E,UAAU,4CAA2C2F,SAAA,EAExDhB,EAAAA,EAAAA,MAAA,OAAK3E,UAAU,SAAQ2F,SAAA,EACrBC,EAAAA,EAAAA,KAAA,SAAO5F,UAAU,+CAA8C2F,SAAC,sBAGhEC,EAAAA,EAAAA,KAAA,SACEmG,YAAW,UAAA9M,OAAY8I,EAAU4B,QAAQ,IAAK,KAAI,OAClDqC,MAAOhD,EACPiD,SAAWC,GAAMjD,EAAciD,EAAEC,OAAOH,OACxChM,UAAU,mBAKd2E,EAAAA,EAAAA,MAAA,OAAK3E,UAAU,iBAAgB2F,SAAA,EAC7BhB,EAAAA,EAAAA,MAAA,SAAO3E,UAAU,+CAA8C2F,SAAA,CAAC,kBAE7D2C,IACC3D,EAAAA,EAAAA,MAAA,QAAM3E,UAAU,4CAA2C2F,SAAA,CAAC,gBACzB,YAAnBgC,EAA4B,SAAA1I,OAAYqJ,GAAgB,QAAArJ,OAAaqJ,GAAmB,WAI5G3D,EAAAA,EAAAA,MAAA,OAAK3E,UAAU,WAAU2F,SAAA,EACvBhB,EAAAA,EAAAA,MAAA,UACEkB,QA1MUuG,KAC1BrD,GAAsBD,EAAkB,EA0MxB9I,UAAU,wDAAuD2F,SAAA,EAEjEhB,EAAAA,EAAAA,MAAA,QAAM3E,UAAU,8BAA6B2F,SAAA,EAC3CC,EAAAA,EAAAA,KAACyG,EAAAA,IAAQ,CAACrM,UAAU,2BACpB4F,EAAAA,EAAAA,KAAA,QAAAD,SACqB,QAAlBsC,EAA0B,cACN,YAAnBN,EAA4B,SAAA1I,OACfgJ,GAAa,QAAAhJ,OACdgJ,KAGfA,IAAkBK,IACjB1C,EAAAA,EAAAA,KAAA,QAAM5F,UAAU,wBAAuB2F,SAAC,gBAG5CC,EAAAA,EAAAA,KAAC0G,EAAAA,IAAiB,CAACtM,UAAS,8CAAAf,OAAgD6J,EAAoB,aAAe,UAGjHlD,EAAAA,EAAAA,KAAC2G,EAAAA,EAAe,CAAA5G,SACbmD,IACCnE,EAAAA,EAAAA,MAACmG,EAAAA,EAAOC,IAAG,CACTC,QAAS,CAAEC,QAAS,EAAGC,GAAI,IAC3BC,QAAS,CAAEF,QAAS,EAAGC,EAAG,GAC1BsB,KAAM,CAAEvB,QAAS,EAAGC,GAAI,IACxBlL,UAAU,2HAA0H2F,SAAA,EAEpIC,EAAAA,EAAAA,KAAA,UACE5F,UAAS,iEAAAf,OACW,QAAlBgJ,EAA0B,6CAA+C,iBAE3EpC,QAASA,IAAMkE,EAAkB,OAAOpE,SACzC,gBAGAiD,EAAiB5B,KAAI,CAAChH,EAAW+C,KAChC4B,EAAAA,EAAAA,MAAA,UAEE3E,UAAS,mGAAAf,OACPgJ,IAAkBjI,EAAY,6CAA+C,iBAE/E6F,QAASA,IAAMkE,EAAkB/J,GAAW2F,SAAA,EAE5CC,EAAAA,EAAAA,KAAA,QAAAD,SACsB,YAAnBgC,EAA4B,SAAA1I,OAAYe,GAAS,QAAAf,OAAae,KAEhEA,IAAcsI,IACb1C,EAAAA,EAAAA,KAAA,QAAM5F,UAAU,wBAAuB2F,SAAC,iBAVrC5C,iBAqBnB4B,EAAAA,EAAAA,MAAA,OAAK3E,UAAU,iBAAgB2F,SAAA,EAC7BC,EAAAA,EAAAA,KAAA,SAAO5F,UAAU,+CAA8C2F,SAAC,uBAGhEhB,EAAAA,EAAAA,MAAA,UACEqH,MAAO5D,EACP6D,SAAWC,IAAMO,OAtRJxM,EAsRwBiM,EAAEC,OAAOH,MArR5DxD,EAAa,IACbH,EAAmBpI,QACnBgJ,EAAc,IAHahJ,KAsRwC,EACrDD,UAAU,eAAc2F,SAAA,EAExBC,EAAAA,EAAAA,KAAA,UAAQoG,MAAM,MAAKrG,SAAC,iBACnBkC,EAAab,KAAI,CAAC/G,EAAS8C,KAC1B6C,EAAAA,EAAAA,KAAA,UAAoBoG,MAAO/L,EAAQ0F,SAChC1F,GADU8C,YAQnB4B,EAAAA,EAAAA,MAAA,OAAK3E,UAAU,iBAAgB2F,SAAA,EAC7BC,EAAAA,EAAAA,KAAA,SAAO5F,UAAU,+CAA8C2F,SAAC,aAGhEhB,EAAAA,EAAAA,MAAA,UACEqH,MAAO9C,EACP+C,SAAWC,GAAM/C,EAAU+C,EAAEC,OAAOH,OACpChM,UAAU,eAAc2F,SAAA,EAExBC,EAAAA,EAAAA,KAAA,UAAQoG,MAAM,SAAQrG,SAAC,kBACvBC,EAAAA,EAAAA,KAAA,UAAQoG,MAAM,SAAQrG,SAAC,kBACvBC,EAAAA,EAAAA,KAAA,UAAQoG,MAAM,QAAOrG,SAAC,oBAK1BC,EAAAA,EAAAA,KAAA,UACE5F,UAAU,oBACV6F,QAASA,KACPoD,EAAc,IACdf,EAAiB,OACjBG,EAAmB,OACnBc,EAAU,SAAS,EACnBxD,SACH,sBAMDqD,GAAgC,QAAlBf,GAA+C,QAApBG,KACzCxC,EAAAA,EAAAA,KAAA,OAAK5F,UAAU,qCAAoC2F,UACjDhB,EAAAA,EAAAA,MAAA,QAAM3E,UAAU,wBAAuB2F,SAAA,CAAC,WAC7BqE,EAA2BlM,OAAO,OAAKyK,EAAUzK,OAAO,IAAEiK,EAAU4B,QAAQ,IAAK,gBAQtG/D,EAAAA,EAAAA,KAAA,OAAK5F,UAAU,oBAAmB2F,SAC/BrE,GACCqD,EAAAA,EAAAA,MAAA,OAAK3E,UAAU,gBAAe2F,SAAA,EAC5BC,EAAAA,EAAAA,KAAA,OAAK5F,UAAU,qBACf4F,EAAAA,EAAAA,KAAA,KAAAD,SAAG,4BAEHzI,GACFyH,EAAAA,EAAAA,MAAA,OAAK3E,UAAU,cAAa2F,SAAA,EAC1BC,EAAAA,EAAAA,KAAC8G,EAAAA,IAAO,CAAC1M,UAAU,gBACnB4F,EAAAA,EAAAA,KAAA,MAAAD,SAAI,6BACJC,EAAAA,EAAAA,KAAA,KAAAD,SAAIzI,KACJ0I,EAAAA,EAAAA,KAAA,UACE5F,UAAU,YACV6F,QAASA,KACP4C,EAAS,MACTe,GAAgB,EAChB7D,SACH,iBAIDqE,EAA2BlM,OAAS,GACtC8H,EAAAA,EAAAA,KAAA,OAAK5F,UAAU,uDAAsD2F,SAClEqE,EAA2BhD,KAAI,CAACsD,EAAUvH,KACzC4B,EAAAA,EAAAA,MAAA,OAAiB3E,UAAU,aAAY2F,SAAA,EACrChB,EAAAA,EAAAA,MAAA,OAAK3E,UAAU,oBAAmB2F,SAAA,EAChChB,EAAAA,EAAAA,MAAA,OAAK3E,UAAU,kBAAiB2F,SAAA,CACf,gBAAdoC,IAA+BnC,EAAAA,EAAAA,KAAC+G,EAAAA,IAAS,IAC3B,gBAAd5E,IAA+BnC,EAAAA,EAAAA,KAAC+G,EAAAA,IAAS,IAC3B,UAAd5E,IAAyBnC,EAAAA,EAAAA,KAACgH,EAAAA,IAAM,KACjChH,EAAAA,EAAAA,KAAA,QAAAD,SACiB,gBAAdoC,EAA8B,OAChB,gBAAdA,EAA8B,aAAe,aAIlDnC,EAAAA,EAAAA,KAAA,OAAK5F,UAAU,mBAAkB2F,SAC9B2E,EAASC,QAIXD,EAASE,OACR5E,EAAAA,EAAAA,KAAA,QAAM5F,UAAU,6BAA4B2F,SAAE2E,EAASE,WAM3D7F,EAAAA,EAAAA,MAAA,OAAK3E,UAAU,eAAc2F,SAAA,EAC3BC,EAAAA,EAAAA,KAAA,MAAI5F,UAAU,iBAAgB2F,SAAE2E,EAASC,SACzC5F,EAAAA,EAAAA,MAAA,OAAK3E,UAAU,gBAAe2F,SAAA,EAC5BC,EAAAA,EAAAA,KAAA,QAAM5F,UAAU,mBAAkB2F,SAAE2E,EAASrK,UAC5CqK,EAAStK,YACR4F,EAAAA,EAAAA,KAAA,QAAM5F,UAAU,iBAAgB2F,SACV,YAAnBgC,EAA4B,SAAA1I,OAAYqL,EAAStK,WAAS,QAAAf,OAAaqL,EAAStK,oBAMzF4F,EAAAA,EAAAA,KAAA,OAAK5F,UAAU,eAAc2F,SAC1B2E,EAASpJ,aACRyD,EAAAA,EAAAA,MAAAkI,EAAAA,SAAA,CAAAlH,SAAA,EACEhB,EAAAA,EAAAA,MAAA,UACE3E,UAAU,uBACV6F,QAASA,IAxSA3E,KAC7ByH,EAAezH,GACfwH,GAAe,EAAK,EAsSeoE,CAAsBxC,EAASpJ,aAAayE,SAAA,EAE3DC,EAAAA,EAAAA,KAACmH,EAAAA,IAAK,IAAG,YAEXpI,EAAAA,EAAAA,MAAA,UACE3E,UAAU,qBACV6F,QAASA,IA/UC3E,KAE9B,MAAM8L,EAAQ,GAAA/N,OAAMgO,CAAAA,SAAAA,aAAAA,WAAAA,GAAAA,qBAAAA,EAAAA,qBAAAA,EAAAA,qBAAAA,EAAAA,cAAAA,GAAYC,wBAAuB,kCAAAjO,OAAiCkO,mBAAmBjM,IAE3GkM,MAAMJ,EAAU,CACdK,OAAQ,MACRnP,QAAS,CACP,cAAgB,UAADe,OAAYqO,aAAaC,QAAQ,aAGjDC,MAAMrQ,IACL,IAAKA,EAASsQ,GACZ,MAAM,IAAIC,MAAM,uBAADzO,OAAwB9B,EAAS0M,SAElD,OAAO1M,EAASwQ,MAAM,IAEvBH,MAAMG,IACL,MAAM1J,EAAMO,OAAOoJ,IAAIC,gBAAgBF,GACjCjD,EAAIjK,SAASE,cAAc,KACjC+J,EAAEoD,KAAO7J,EACTyG,EAAEqD,SAAW7M,EAAY8M,MAAM,KAAKC,MACpCxN,SAASyN,KAAKpN,YAAY4J,GAC1BA,EAAEyD,QACF1N,SAASyN,KAAKE,YAAY1D,GAC1BlG,OAAOoJ,IAAIS,gBAAgBpK,EAAI,IAEhCqK,OAAOpR,IACN2F,QAAQ3F,MAAM,8BAA+BA,GAE7CsH,OAAO+J,KAAKrN,EAAa,SAAS,GAClC,EAiT+BsN,CAAuBlE,EAASpJ,aAAayE,SAAA,EAE5DC,EAAAA,EAAAA,KAAC6I,EAAAA,IAAU,IAAG,mBAIlB7I,EAAAA,EAAAA,KAAA,QAAM5F,UAAU,cAAa2F,SAAC,sBArD1B5C,QA4Dd4B,EAAAA,EAAAA,MAAA,OAAK3E,UAAU,cAAa2F,SAAA,EAC1BC,EAAAA,EAAAA,KAAC8I,EAAAA,IAAe,CAAC1O,UAAU,gBAC3B4F,EAAAA,EAAAA,KAAA,MAAAD,SAAI,wBACJC,EAAAA,EAAAA,KAAA,KAAAD,SAAG,kEACHC,EAAAA,EAAAA,KAAA,KAAG5F,UAAU,aAAY2F,SAAC,sDAShCC,EAAAA,EAAAA,KAAC+I,EAAQ,CACP3N,YAAaA,EACbC,WAAYA,KACVyH,GAAe,GACfC,EAAe,GAAG,EAEpBzH,YAAaA,SAKrB,C", "sources": ["apicalls/study.js", "data/Subjects.jsx", "pages/user/StudyMaterial/PDFModal.js", "pages/user/StudyMaterial/index.js"], "sourcesContent": ["const { default: axiosInstance } = require(\".\");\r\n\r\n// get study materials\r\nexport const getStudyMaterial = async (filters) => {\r\n    try {\r\n        const response = await axiosInstance.post(\"/api/study/get-study-content\" , filters);\r\n        return response;\r\n    } catch (error) {\r\n        return error.response;\r\n    }\r\n}\r\n\r\n// get available classes for user's level\r\nexport const getAvailableClasses = async () => {\r\n    try {\r\n        const response = await axiosInstance.post(\"/api/study/get-available-classes\");\r\n        return response;\r\n    } catch (error) {\r\n        return error.response;\r\n    }\r\n}\r\n\r\n// get all videos for admin management\r\nexport const getAllVideos = async () => {\r\n    try {\r\n        const response = await axiosInstance.get(\"/api/study/videos-subtitle-status\");\r\n        return response.data;\r\n    } catch (error) {\r\n        return error.response?.data || { success: false, message: \"Failed to fetch videos\" };\r\n    }\r\n}\r\n\r\n// Add study material functions\r\n\r\n// Add video (supports both JSON data and FormData)\r\nexport const addVideo = async (videoData, onUploadProgress = null) => {\r\n    try {\r\n        const isFormData = videoData instanceof FormData;\r\n        const config = isFormData ? {\r\n            headers: {\r\n                'Content-Type': 'multipart/form-data',\r\n            },\r\n            timeout: 600000, // 10 minutes timeout for large files\r\n            onUploadProgress: onUploadProgress ? (progressEvent) => {\r\n                const percentCompleted = Math.round((progressEvent.loaded * 100) / progressEvent.total);\r\n                // Pass additional information for better progress tracking\r\n                onUploadProgress(percentCompleted, progressEvent.loaded, progressEvent.total);\r\n            } : undefined,\r\n        } : {\r\n            timeout: 60000, // 1 minute for YouTube videos\r\n        };\r\n\r\n        const response = await axiosInstance.post(\"/api/study/add-video\", videoData, config);\r\n        return response;\r\n    } catch (error) {\r\n        return error.response;\r\n    }\r\n}\r\n\r\n// Add note\r\nexport const addNote = async (formData) => {\r\n    try {\r\n        const response = await axiosInstance.post(\"/api/study/add-note\", formData, {\r\n            headers: {\r\n                'Content-Type': 'multipart/form-data',\r\n            },\r\n        });\r\n        return response;\r\n    } catch (error) {\r\n        return error.response;\r\n    }\r\n}\r\n\r\n// Add past paper\r\nexport const addPastPaper = async (formData) => {\r\n    try {\r\n        const response = await axiosInstance.post(\"/api/study/add-past-paper\", formData, {\r\n            headers: {\r\n                'Content-Type': 'multipart/form-data',\r\n            },\r\n        });\r\n        return response;\r\n    } catch (error) {\r\n        return error.response;\r\n    }\r\n}\r\n\r\n// Add book\r\nexport const addBook = async (formData) => {\r\n    try {\r\n        const response = await axiosInstance.post(\"/api/study/add-book\", formData, {\r\n            headers: {\r\n                'Content-Type': 'multipart/form-data',\r\n            },\r\n        });\r\n        return response;\r\n    } catch (error) {\r\n        return error.response;\r\n    }\r\n}\r\n\r\n// Update study material functions\r\n\r\n// Update video\r\nexport const updateVideo = async (id, videoData) => {\r\n    try {\r\n        let config = {\r\n            headers: {\r\n                'Content-Type': 'application/json',\r\n            },\r\n        };\r\n\r\n        // If videoData is FormData (contains file uploads), change content type\r\n        if (videoData instanceof FormData) {\r\n            config.headers['Content-Type'] = 'multipart/form-data';\r\n        }\r\n\r\n        const response = await axiosInstance.put(`/api/study/update-video/${id}`, videoData, config);\r\n        return response;\r\n    } catch (error) {\r\n        return error.response;\r\n    }\r\n}\r\n\r\n// Update note\r\nexport const updateNote = async (id, formData) => {\r\n    try {\r\n        const response = await axiosInstance.put(`/api/study/update-note/${id}`, formData, {\r\n            headers: {\r\n                'Content-Type': 'multipart/form-data',\r\n            },\r\n        });\r\n        return response;\r\n    } catch (error) {\r\n        return error.response;\r\n    }\r\n}\r\n\r\n// Update past paper\r\nexport const updatePastPaper = async (id, formData) => {\r\n    try {\r\n        const response = await axiosInstance.put(`/api/study/update-past-paper/${id}`, formData, {\r\n            headers: {\r\n                'Content-Type': 'multipart/form-data',\r\n            },\r\n        });\r\n        return response;\r\n    } catch (error) {\r\n        return error.response;\r\n    }\r\n}\r\n\r\n// Update book\r\nexport const updateBook = async (id, formData) => {\r\n    try {\r\n        const response = await axiosInstance.put(`/api/study/update-book/${id}`, formData, {\r\n            headers: {\r\n                'Content-Type': 'multipart/form-data',\r\n            },\r\n        });\r\n        return response;\r\n    } catch (error) {\r\n        return error.response;\r\n    }\r\n}\r\n\r\n// Delete study material functions\r\n\r\n// Delete video\r\nexport const deleteVideo = async (id) => {\r\n    try {\r\n        const response = await axiosInstance.delete(`/api/study/delete-video/${id}`);\r\n        return response;\r\n    } catch (error) {\r\n        return error.response;\r\n    }\r\n}\r\n\r\n// Delete note\r\nexport const deleteNote = async (id) => {\r\n    try {\r\n        const response = await axiosInstance.delete(`/api/study/delete-note/${id}`);\r\n        return response;\r\n    } catch (error) {\r\n        return error.response;\r\n    }\r\n}\r\n\r\n// Delete past paper\r\nexport const deletePastPaper = async (id) => {\r\n    try {\r\n        const response = await axiosInstance.delete(`/api/study/delete-past-paper/${id}`);\r\n        return response;\r\n    } catch (error) {\r\n        return error.response;\r\n    }\r\n}\r\n\r\n// Delete book\r\nexport const deleteBook = async (id) => {\r\n    try {\r\n        const response = await axiosInstance.delete(`/api/study/delete-book/${id}`);\r\n        return response;\r\n    } catch (error) {\r\n        return error.response;\r\n    }\r\n}\r\n\r\n// Get all study materials for admin management\r\nexport const getAllStudyMaterials = async (filters = {}) => {\r\n    try {\r\n        const params = new URLSearchParams();\r\n        if (filters.materialType) params.append('materialType', filters.materialType);\r\n        if (filters.level) params.append('level', filters.level);\r\n        if (filters.className) params.append('className', filters.className);\r\n        if (filters.subject) params.append('subject', filters.subject);\r\n\r\n        const response = await axiosInstance.get(`/api/study/admin/all-materials?${params.toString()}`);\r\n        return response;\r\n    } catch (error) {\r\n        return error.response;\r\n    }\r\n}\r\n", "export const primarySubjects = [\r\n  \"Mathematics\",\r\n  \"Science and Technology\",\r\n  \"Geography\",\r\n  \"Kiswahili\",\r\n  \"SocialStudies\",\r\n  \"English\",\r\n  \"Religion\",\r\n  \"Arithmetic\",\r\n  \"Sport and Art\",\r\n  \"Health and Environment\",\r\n  \"Civic and Moral\",\r\n  \"French\",\r\n  \"Historia ya Tanzania\",\r\n];\r\n\r\nexport const secondarySubjects = [\r\n  \"Physics\",\r\n  \"Mathematics\",\r\n  \"Biology\",\r\n  \"Chemistry\",\r\n  \"Geography\",\r\n  \"Civics\",\r\n  \"History\",\r\n  \"Computer\",\r\n  \"English language\",\r\n  \"Kiswahili\",\r\n  \"Commerce\",\r\n  \"Economics\",\r\n  \"Advanced Mathematics\",\r\n  \"Basic Applied Mathematics\",\r\n  \"Accountancy\",\r\n  \"Agriculture\",\r\n];\r\n\r\nexport const advanceSubjects = [\r\n  \"Physics\",\r\n  \"Mathematics\",\r\n  \"Biology\",\r\n  \"Chemistry\",\r\n  \"Geography\",\r\n  \"Civics\",\r\n  \"History\",\r\n  \"Computer\",\r\n  \"English language\",\r\n  \"Kiswahili\",\r\n  \"Commerce\",\r\n  \"Economics\",\r\n  \"Advanced Mathematics\",\r\n  \"Basic Applied Mathematics\",\r\n  \"Accountancy\",\r\n  \"Agriculture\",\r\n];", "import { useEffect, useRef, useState } from \"react\";\r\nimport * as pdfjsLib from \"pdfjs-dist\";\r\nimport ReactModal from \"react-modal\";\r\nReactModal.setAppElement('#root');\r\n\r\npdfjsLib.GlobalWorkerOptions.workerSrc = `https://unpkg.com/pdfjs-dist@2.15.349/build/pdf.worker.min.js`;\r\n\r\n// Add CSS for spinner animation\r\nconst spinnerStyle = `\r\n  @keyframes spin {\r\n    0% { transform: rotate(0deg); }\r\n    100% { transform: rotate(360deg); }\r\n  }\r\n`;\r\n\r\n// Inject the CSS\r\nif (typeof document !== 'undefined') {\r\n  const style = document.createElement('style');\r\n  style.textContent = spinnerStyle;\r\n  document.head.appendChild(style);\r\n}\r\n\r\nconst PDFModal = ({ modalIsOpen, closeModal, documentUrl }) => {\r\n  const [pages, setPages] = useState([]);\r\n  const [isLoading, setIsLoading] = useState(false);\r\n  const [loadingProgress, setLoadingProgress] = useState(0);\r\n  const [totalPages, setTotalPages] = useState(0);\r\n  const canvasRefs = useRef([]);\r\n  const containerRef = useRef(null);\r\n  const renderingRefs = useRef({}); // Track rendering state per page\r\n\r\n  const renderPDF = async (url) => {\r\n    try {\r\n      setIsLoading(true);\r\n      setLoadingProgress(0);\r\n\r\n      const pdf = await pdfjsLib.getDocument(url).promise;\r\n      console.log(\"PDF loaded\");\r\n\r\n      setTotalPages(pdf.numPages);\r\n\r\n      // Load pages progressively (first 3 pages immediately, then lazy load others)\r\n      const initialPagesToLoad = Math.min(3, pdf.numPages);\r\n      const pagesData = [];\r\n\r\n      for (let i = 1; i <= initialPagesToLoad; i++) {\r\n        const page = await pdf.getPage(i);\r\n        pagesData.push(page);\r\n        setLoadingProgress((i / pdf.numPages) * 100);\r\n      }\r\n\r\n      setPages(pagesData);\r\n      setIsLoading(false);\r\n\r\n      // Load remaining pages in background\r\n      if (pdf.numPages > initialPagesToLoad) {\r\n        loadRemainingPages(pdf, initialPagesToLoad + 1, pagesData);\r\n      }\r\n\r\n    } catch (error) {\r\n      console.error(\"Error loading PDF:\", error);\r\n      setIsLoading(false);\r\n    }\r\n  };\r\n\r\n  const loadRemainingPages = async (pdf, startPage, existingPages) => {\r\n    const updatedPages = [...existingPages];\r\n\r\n    for (let i = startPage; i <= pdf.numPages; i++) {\r\n      try {\r\n        const page = await pdf.getPage(i);\r\n        updatedPages.push(page);\r\n        setPages([...updatedPages]);\r\n        setLoadingProgress((i / pdf.numPages) * 100);\r\n\r\n        // Small delay to prevent blocking the UI\r\n        await new Promise(resolve => setTimeout(resolve, 50));\r\n      } catch (error) {\r\n        console.error(`Error loading page ${i}:`, error);\r\n      }\r\n    }\r\n  };\r\n\r\n  const renderPage = async (page, index) => {\r\n    const canvas = canvasRefs.current[index];\r\n    if (!canvas || renderingRefs.current[index] || !containerRef.current) return;\r\n\r\n    try {\r\n      renderingRefs.current[index] = true;\r\n\r\n      const viewport = page.getViewport({ scale: 1.0 });\r\n      const containerWidth = containerRef.current.clientWidth;\r\n      const scale = containerWidth / viewport.width;\r\n      const scaledViewport = page.getViewport({ scale });\r\n\r\n      const context = canvas.getContext(\"2d\");\r\n      canvas.height = scaledViewport.height;\r\n      canvas.width = scaledViewport.width;\r\n      canvas.style.width = '100%';\r\n      canvas.style.height = 'auto';\r\n\r\n      const renderContext = {\r\n        canvasContext: context,\r\n        viewport: scaledViewport,\r\n      };\r\n\r\n      await page.render(renderContext).promise;\r\n      console.log(`Page ${index + 1} rendered`);\r\n    } catch (error) {\r\n      console.error(`Error rendering page ${index + 1}:`, error);\r\n    } finally {\r\n      renderingRefs.current[index] = false;\r\n    }\r\n  };\r\n\r\n  useEffect(() => {\r\n    if (modalIsOpen && documentUrl) {\r\n      setPages([]);\r\n      setTotalPages(0);\r\n      setLoadingProgress(0);\r\n      canvasRefs.current = [];\r\n      renderingRefs.current = {};\r\n      renderPDF(documentUrl);\r\n    }\r\n    // eslint-disable-next-line react-hooks/exhaustive-deps\r\n  }, [modalIsOpen, documentUrl]);\r\n\r\n  // Effect to render pages when they're loaded\r\n  useEffect(() => {\r\n    if (pages.length > 0 && containerRef.current) {\r\n      pages.forEach((page, index) => {\r\n        renderPage(page, index);\r\n      });\r\n    }\r\n    // eslint-disable-next-line react-hooks/exhaustive-deps\r\n  }, [pages]);\r\n\r\n  // Re-render pages when window is resized\r\n  useEffect(() => {\r\n    const handleResize = () => {\r\n      if (pages.length > 0) {\r\n        pages.forEach((page, index) => {\r\n          renderPage(page, index);\r\n        });\r\n      }\r\n    };\r\n\r\n    window.addEventListener('resize', handleResize);\r\n    return () => window.removeEventListener('resize', handleResize);\r\n  }, [pages]);\r\n\r\n  return (\r\n    <ReactModal\r\n      isOpen={modalIsOpen}\r\n      onRequestClose={closeModal}\r\n      contentLabel=\"Document Preview\"\r\n      style={{\r\n        overlay: {\r\n          backgroundColor: 'rgba(0, 0, 0, 0.75)'\r\n        },\r\n        content: {\r\n          top: '50%',\r\n          left: '50%',\r\n          right: 'auto',\r\n          bottom: 'auto',\r\n          marginRight: '-50%',\r\n          transform: 'translate(-50%, -50%)',\r\n          width: '70%',\r\n          height: '90%',\r\n          padding: '20px',\r\n          borderRadius: '10px',\r\n          overflow: 'hidden',\r\n        },\r\n      }}\r\n    >\r\n      <button\r\n        onClick={closeModal}\r\n        style={{\r\n          position: \"absolute\",\r\n          top: \"10px\",\r\n          right: \"10px\",\r\n          background: \"transparent\",\r\n          border: \"none\",\r\n          fontSize: \"20px\",\r\n          cursor: \"pointer\",\r\n          zIndex: 1,\r\n        }}\r\n      >\r\n        X\r\n      </button>\r\n\r\n      <div\r\n        ref={containerRef}\r\n        style={{\r\n          height: '100%',\r\n          overflow: 'auto',\r\n          padding: '10px',\r\n          scrollbarWidth: 'thin'\r\n        }}\r\n      >\r\n        {isLoading && (\r\n          <div style={{\r\n            display: 'flex',\r\n            flexDirection: 'column',\r\n            alignItems: 'center',\r\n            justifyContent: 'center',\r\n            height: '200px',\r\n            color: '#666'\r\n          }}>\r\n            <div style={{\r\n              width: '50px',\r\n              height: '50px',\r\n              border: '3px solid #f3f3f3',\r\n              borderTop: '3px solid #3498db',\r\n              borderRadius: '50%',\r\n              animation: 'spin 1s linear infinite',\r\n              marginBottom: '20px'\r\n            }}></div>\r\n            <p>Loading PDF...</p>\r\n            <div style={{\r\n              width: '200px',\r\n              height: '6px',\r\n              backgroundColor: '#f3f3f3',\r\n              borderRadius: '3px',\r\n              overflow: 'hidden',\r\n              marginTop: '10px'\r\n            }}>\r\n              <div style={{\r\n                width: `${loadingProgress}%`,\r\n                height: '100%',\r\n                backgroundColor: '#3498db',\r\n                transition: 'width 0.3s ease'\r\n              }}></div>\r\n            </div>\r\n            <small style={{ marginTop: '5px' }}>\r\n              {Math.round(loadingProgress)}% loaded\r\n            </small>\r\n          </div>\r\n        )}\r\n\r\n        {pages.map((page, index) => (\r\n          <div\r\n            key={index}\r\n            style={{\r\n              marginBottom: '10px',\r\n              display: 'flex',\r\n              flexDirection: 'column',\r\n              alignItems: 'center'\r\n            }}\r\n          >\r\n            <canvas\r\n              ref={element => {\r\n                canvasRefs.current[index] = element;\r\n              }}\r\n              style={{\r\n                maxWidth: '100%',\r\n                height: 'auto',\r\n                border: '1px solid black'\r\n              }}\r\n            />\r\n          </div>\r\n        ))}\r\n\r\n        {totalPages > pages.length && !isLoading && (\r\n          <div style={{\r\n            textAlign: 'center',\r\n            padding: '20px',\r\n            color: '#666',\r\n            fontStyle: 'italic'\r\n          }}>\r\n            Loading remaining pages... ({pages.length}/{totalPages})\r\n          </div>\r\n        )}\r\n      </div>\r\n    </ReactModal>\r\n  );\r\n};\r\n\r\nexport default PDFModal;", "import React, { useState, useEffect, use<PERSON><PERSON>back, useMemo } from \"react\";\nimport \"./index.css\";\nimport { motion, AnimatePresence } from \"framer-motion\";\nimport { getStudyMaterial } from \"../../../apicalls/study\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { HideLoading, ShowLoading } from \"../../../redux/loaderSlice\";\n// import { Card, Button, Input, Loading } from \"../../../components/modern\";\n\nimport PDFModal from \"./PDFModal\";\nimport {\n  FaBook,\n  FaFileAlt,\n  FaGraduationCap,\n  FaDownload,\n  FaEye,\n  FaChevronDown,\n  FaSearch,\n  FaTimes,\n} from \"react-icons/fa\";\nimport {\n  TbFileText,\n  TbBook as TbBookIcon,\n  TbSchool,\n  TbSearch,\n  TbFilter,\n  TbSortAscending,\n  TbDownload,\n  Tb<PERSON><PERSON>,\n  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,\n  <PERSON>b<PERSON><PERSON>,\n  Tb<PERSON>he<PERSON>ronDown as Tb<PERSON><PERSON><PERSON>ronDownIcon,\n  TbChevronUp,\n  TbX,\n  Tb<PERSON>lertTriangle,\n  TbInfoCircle,\n  TbCheck,\n  TbBooks,\n  TbCertificate\n} from \"react-icons/tb\";\nimport { primarySubjects, secondarySubjects, advanceSubjects } from \"../../../data/Subjects.jsx\";\n\nfunction StudyMaterial() {\n  const { user } = useSelector((state) => state.user);\n  const dispatch = useDispatch();\n\n  // Get user level and subjects list (case-insensitive)\n  const userLevel = user?.level || 'Primary';\n  const userLevelLower = userLevel.toLowerCase();\n  const subjectsList = userLevelLower === 'primary'\n    ? primarySubjects\n    : userLevelLower === 'secondary'\n      ? secondarySubjects\n      : advanceSubjects;\n\n  // Debug: Log current level and subjects\n  useEffect(() => {\n    console.log('📚 Study Materials - User Level:', userLevel);\n    console.log('📚 Study Materials - User Level (lowercase):', userLevelLower);\n    console.log('📚 Study Materials - Subjects List:', subjectsList);\n    console.log('📚 Study Materials - User Data:', user);\n  }, [userLevel, userLevelLower, subjectsList, user]);\n\n  // Define all possible classes for each level\n  const allPossibleClasses = userLevelLower === 'primary'\n    ? ['1', '2', '3', '4', '5', '6', '7']\n    : userLevelLower === 'secondary'\n      ? ['Form-1', 'Form-2', 'Form-3', 'Form-4']\n      : ['Form-5', 'Form-6'];\n\n  // Simplified state management - initialize with user's class if available\n  const [activeTab, setActiveTab] = useState(\"study-notes\");\n  const [selectedClass, setSelectedClass] = useState(user?.class || user?.className || \"all\");\n  const [selectedSubject, setSelectedSubject] = useState(\"all\");\n\n  // Get user's current class for highlighting\n  const userCurrentClass = user?.class || user?.className;\n  const [materials, setMaterials] = useState([]);\n  const [isLoading, setIsLoading] = useState(false);\n  const [error, setError] = useState(null);\n\n  const [modalIsOpen, setModalIsOpen] = useState(false);\n  const [documentUrl, setDocumentUrl] = useState(\"\");\n  const [availableClasses, setAvailableClasses] = useState([]);\n  const [showClassSelector, setShowClassSelector] = useState(false);\n\n\n  // Unified search and sort states\n  const [searchTerm, setSearchTerm] = useState(\"\");\n  const [sortBy, setSortBy] = useState(\"newest\");\n\n  // Update selectedClass when user data becomes available\n  useEffect(() => {\n    const userClass = user?.class || user?.className;\n    if (userClass && selectedClass === \"all\" && !availableClasses.length) {\n      setSelectedClass(userClass);\n    }\n  }, [user, selectedClass, availableClasses.length]);\n\n  // Reset subject selection when user level changes\n  useEffect(() => {\n    if (user?.level) {\n      // Check if current selected subject is valid for the new level\n      const isValidSubject = subjectsList.includes(selectedSubject);\n      if (!isValidSubject && selectedSubject !== \"all\") {\n        console.log('📚 Resetting subject selection due to level change');\n        setSelectedSubject(\"all\");\n      }\n    }\n  }, [user?.level, subjectsList, selectedSubject]);\n\n  // Set available classes based on user level (show all possible classes)\n  const setAvailableClassesForLevel = useCallback(() => {\n    setAvailableClasses(allPossibleClasses);\n  }, [allPossibleClasses]);\n\n  // Simplified fetch function\n  const fetchMaterials = useCallback(async () => {\n    if (!activeTab || selectedClass === \"default\") {\n      return;\n    }\n\n    setIsLoading(true);\n    setError(null);\n    dispatch(ShowLoading());\n\n    try {\n      // Normalize className for backend - remove \"Form-\" prefix if present\n      const normalizedClassName = selectedClass === \"all\" ? \"all\" :\n        selectedClass.toString().replace(\"Form-\", \"\");\n\n      const data = {\n        content: activeTab,\n        className: normalizedClassName,\n        subject: selectedSubject, // This can be \"all\" or a specific subject\n      };\n      if (userLevel) {\n        data.level = userLevel;\n      }\n\n      const res = await getStudyMaterial(data);\n\n      if (res.status === 200 && res.data.success) {\n        const materials = res.data.data === \"empty\" ? [] : res.data.data;\n        setMaterials(materials);\n      } else {\n        setMaterials([]);\n        setError(`Failed to fetch ${activeTab}. Please try again.`);\n      }\n    } catch (error) {\n      console.error(\"Error fetching study material:\", error);\n      setMaterials([]);\n      setError(`Unable to load ${activeTab}. Please check your connection and try again.`);\n    } finally {\n      setIsLoading(false);\n      dispatch(HideLoading());\n    }\n  }, [activeTab, selectedClass, selectedSubject, userLevel, dispatch]);\n\n  // Set available classes when component mounts\n  useEffect(() => {\n    if (user && userLevel) {\n      setAvailableClassesForLevel();\n    }\n  }, [user, userLevel, setAvailableClassesForLevel]);\n\n  // Fetch materials when filters change or component mounts\n  useEffect(() => {\n    // Only fetch if we have a valid activeTab, selectedClass, and user\n    if (user && userLevel && activeTab && selectedClass && selectedClass !== \"default\") {\n      fetchMaterials();\n    }\n  }, [user, userLevel, activeTab, selectedClass, selectedSubject, fetchMaterials]);\n\n  // Handler functions\n  const handleTabChange = (tab) => {\n    setMaterials([]);\n    setActiveTab(tab);\n    setSearchTerm(\"\");\n    setSortBy(\"newest\");\n  };\n\n  const handleSubjectChange = (subject) => {\n    setMaterials([]);\n    setSelectedSubject(subject);\n    setSearchTerm(\"\");\n  };\n\n  const handleClassChange = (className) => {\n    setMaterials([]);\n    setSelectedClass(className);\n    setShowClassSelector(false);\n  };\n\n  const toggleClassSelector = () => {\n    setShowClassSelector(!showClassSelector);\n  };\n\n  // Unified filtering and sorting logic\n  const filteredAndSortedMaterials = useMemo(() => {\n    if (!materials || materials.length === 0) {\n      return [];\n    }\n\n    let filtered = materials;\n\n    // Filter by search term (title, subject, or year)\n    if (searchTerm.trim()) {\n      const searchLower = searchTerm.toLowerCase();\n      filtered = filtered.filter(material =>\n        material.title.toLowerCase().includes(searchLower) ||\n        material.subject.toLowerCase().includes(searchLower) ||\n        (material.year && material.year.toLowerCase().includes(searchLower))\n      );\n    }\n\n    // Sort by year, creation date, or title\n    filtered.sort((a, b) => {\n      if (sortBy === \"newest\") {\n        // For materials with year field (books, past papers)\n        if (a.year && b.year) {\n          return parseInt(b.year) - parseInt(a.year);\n        }\n\n        // Fallback: materials with year come first\n        else if (a.year && !b.year) return -1;\n        else if (!a.year && b.year) return 1;\n        else return 0;\n      } else if (sortBy === \"oldest\") {\n        // For materials with year field\n        if (a.year && b.year) {\n          return parseInt(a.year) - parseInt(b.year);\n        }\n\n        // Fallback: materials with year come first\n        else if (a.year && !b.year) return -1;\n        else if (!a.year && b.year) return 1;\n        else return 0;\n      } else {\n        // Sort by title alphabetically\n        return a.title.localeCompare(b.title);\n      }\n    });\n\n\n\n    return filtered;\n  }, [materials, searchTerm, sortBy, activeTab]);\n\n  // Document handlers\n  const handleDocumentDownload = (documentUrl) => {\n    // Use proxy endpoint to handle CORS issues\n    const proxyUrl = `${process.env.REACT_APP_SERVER_DOMAIN}/api/study/document-proxy?url=${encodeURIComponent(documentUrl)}`;\n\n    fetch(proxyUrl, {\n      method: 'GET',\n      headers: {\n        'Authorization': `Bearer ${localStorage.getItem('token')}`,\n      }\n    })\n      .then((response) => {\n        if (!response.ok) {\n          throw new Error(`HTTP error! status: ${response.status}`);\n        }\n        return response.blob();\n      })\n      .then((blob) => {\n        const url = window.URL.createObjectURL(blob);\n        const a = document.createElement(\"a\");\n        a.href = url;\n        a.download = documentUrl.split(\"/\").pop();\n        document.body.appendChild(a);\n        a.click();\n        document.body.removeChild(a);\n        window.URL.revokeObjectURL(url);\n      })\n      .catch((error) => {\n        console.error(\"Error downloading the file:\", error);\n        // Fallback to direct download if proxy fails\n        window.open(documentUrl, '_blank');\n      });\n  };\n\n  const handleDocumentPreview = (documentUrl) => {\n    setDocumentUrl(documentUrl);\n    setModalIsOpen(true);\n  };\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-gray-50 to-blue-50\">\n      {/* Modern Header */}\n      <motion.div\n        initial={{ opacity: 0, y: -20 }}\n        animate={{ opacity: 1, y: 0 }}\n        className=\"bg-gradient-to-r from-primary-600 to-blue-600 text-white\"\n      >\n        <div className=\"container-modern py-12\">\n          <div className=\"flex items-center justify-between\">\n            <div className=\"flex items-center space-x-6\">\n              <div className=\"w-16 h-16 bg-white/20 rounded-full flex items-center justify-center\">\n                <TbBooks className=\"w-8 h-8 text-white\" />\n              </div>\n              <div>\n                <h1 className=\"text-4xl font-bold mb-2\">Study Materials</h1>\n                <p className=\"text-xl text-blue-100\">\n                  Access comprehensive learning resources for {userLevel} education\n                </p>\n              </div>\n            </div>\n            <div className=\"hidden md:block\">\n              <div className=\"bg-white/20 backdrop-blur-sm rounded-xl px-6 py-3\">\n                <div className=\"text-sm text-blue-100 mb-1\">Current Level</div>\n                <div className=\"text-lg font-bold\">{userLevel?.toUpperCase()}</div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </motion.div>\n\n      <div className=\"container-modern py-8\">\n        {/* Study Material Tabs */}\n        <div className=\"mb-6\">\n          <div className=\"study-tabs\">\n            {[\n              { key: 'study-notes', label: 'Notes', icon: TbFileText },\n              { key: 'past-papers', label: 'Past Papers', icon: TbCertificate },\n              { key: 'books', label: 'Books', icon: TbBookIcon }\n            ].map((tab) => (\n              <button\n                key={tab.key}\n                className={`study-tab ${activeTab === tab.key ? 'active' : ''}`}\n                onClick={() => handleTabChange(tab.key)}\n              >\n                <tab.icon />\n                <span>{tab.label}</span>\n              </button>\n            ))}\n          </div>\n        </div>\n\n        {/* Modern Filters Section */}\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ delay: 0.4 }}\n          className=\"mb-8\"\n        >\n          <div className=\"card p-6\">\n            <div className=\"flex flex-col lg:flex-row gap-6 items-end\">\n              {/* Search */}\n              <div className=\"flex-1\">\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  Search Materials\n                </label>\n                <input\n                  placeholder={`Search ${activeTab.replace('-', ' ')}...`}\n                  value={searchTerm}\n                  onChange={(e) => setSearchTerm(e.target.value)}\n                  className=\"form-input\"\n                />\n              </div>\n\n              {/* Class Filter */}\n              <div className=\"w-full lg:w-64\">\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  Filter by Class\n                  {userCurrentClass && (\n                    <span className=\"ml-2 text-xs text-primary-600 font-medium\">\n                      (Your class: {userLevelLower === 'primary' ? `Class ${userCurrentClass}` : `Form ${userCurrentClass}`})\n                    </span>\n                  )}\n                </label>\n                <div className=\"relative\">\n                  <button\n                    onClick={toggleClassSelector}\n                    className=\"w-full input-modern flex items-center justify-between\"\n                  >\n                    <span className=\"flex items-center space-x-2\">\n                      <TbSchool className=\"w-4 h-4 text-gray-400\" />\n                      <span>\n                        {selectedClass === 'all' ? 'All Classes' :\n                          userLevelLower === 'primary'\n                            ? `Class ${selectedClass}`\n                            : `Form ${selectedClass}`\n                        }\n                      </span>\n                      {selectedClass === userCurrentClass && (\n                        <span className=\"badge-primary text-xs\">Current</span>\n                      )}\n                    </span>\n                    <TbChevronDownIcon className={`w-4 h-4 text-gray-400 transition-transform ${showClassSelector ? 'rotate-180' : ''}`} />\n                  </button>\n\n                  <AnimatePresence>\n                    {showClassSelector && (\n                      <motion.div\n                        initial={{ opacity: 0, y: -10 }}\n                        animate={{ opacity: 1, y: 0 }}\n                        exit={{ opacity: 0, y: -10 }}\n                        className=\"absolute top-full left-0 right-0 mt-1 bg-white border border-gray-200 rounded-lg shadow-lg z-10 max-h-60 overflow-y-auto\"\n                      >\n                        <button\n                          className={`w-full px-4 py-3 text-left hover:bg-gray-50 transition-colors ${\n                            selectedClass === 'all' ? 'bg-primary-50 text-primary-700 font-medium' : 'text-gray-700'\n                          }`}\n                          onClick={() => handleClassChange('all')}\n                        >\n                          All Classes\n                        </button>\n                        {availableClasses.map((className, index) => (\n                          <button\n                            key={index}\n                            className={`w-full px-4 py-3 text-left hover:bg-gray-50 transition-colors flex items-center justify-between ${\n                              selectedClass === className ? 'bg-primary-50 text-primary-700 font-medium' : 'text-gray-700'\n                            }`}\n                            onClick={() => handleClassChange(className)}\n                          >\n                            <span>\n                              {userLevelLower === 'primary' ? `Class ${className}` : `Form ${className}`}\n                            </span>\n                            {className === userCurrentClass && (\n                              <span className=\"badge-success text-xs\">Your Class</span>\n                            )}\n                          </button>\n                        ))}\n                      </motion.div>\n                    )}\n                  </AnimatePresence>\n                </div>\n              </div>\n\n              {/* Subject Filter */}\n              <div className=\"w-full lg:w-64\">\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  Filter by Subject\n                </label>\n                <select\n                  value={selectedSubject}\n                  onChange={(e) => handleSubjectChange(e.target.value)}\n                  className=\"input-modern\"\n                >\n                  <option value=\"all\">All Subjects</option>\n                  {subjectsList.map((subject, index) => (\n                    <option key={index} value={subject}>\n                      {subject}\n                    </option>\n                  ))}\n                </select>\n              </div>\n\n              {/* Sort */}\n              <div className=\"w-full lg:w-48\">\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  Sort by\n                </label>\n                <select\n                  value={sortBy}\n                  onChange={(e) => setSortBy(e.target.value)}\n                  className=\"input-modern\"\n                >\n                  <option value=\"newest\">Newest First</option>\n                  <option value=\"oldest\">Oldest First</option>\n                  <option value=\"title\">By Title</option>\n                </select>\n              </div>\n\n              {/* Clear Filters */}\n              <button\n                className=\"btn btn-secondary\"\n                onClick={() => {\n                  setSearchTerm(\"\");\n                  setSelectedClass(\"all\");\n                  setSelectedSubject(\"all\");\n                  setSortBy(\"newest\");\n                }}\n              >\n                Clear Filters\n              </button>\n            </div>\n\n            {/* Results Count */}\n            {(searchTerm || selectedClass !== \"all\" || selectedSubject !== \"all\") && (\n              <div className=\"mt-4 pt-4 border-t border-gray-100\">\n                <span className=\"text-sm text-gray-600\">\n                  Showing {filteredAndSortedMaterials.length} of {materials.length} {activeTab.replace('-', ' ')}\n                </span>\n              </div>\n            )}\n          </div>\n        </motion.div>\n\n      {/* Materials Display */}\n      <div className=\"materials-section\">\n        {isLoading ? (\n          <div className=\"loading-state\">\n            <div className=\"loading-spinner\"></div>\n            <p>Loading materials...</p>\n          </div>\n        ) : error ? (\n          <div className=\"error-state\">\n            <FaTimes className=\"error-icon\" />\n            <h3>Error Loading Materials</h3>\n            <p>{error}</p>\n            <button\n              className=\"retry-btn\"\n              onClick={() => {\n                setError(null);\n                fetchMaterials();\n              }}\n            >\n              Try Again\n            </button>\n          </div>\n        ) : filteredAndSortedMaterials.length > 0 ? (\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\">\n            {filteredAndSortedMaterials.map((material, index) => (\n              <div key={index} className=\"study-card\">\n                <div className=\"study-card-header\">\n                  <div className=\"study-card-meta\">\n                    {activeTab === 'study-notes' && <FaFileAlt />}\n                    {activeTab === 'past-papers' && <FaFileAlt />}\n                    {activeTab === 'books' && <FaBook />}\n                    <span>\n                      {activeTab === 'study-notes' ? 'Note' :\n                       activeTab === 'past-papers' ? 'Past Paper' : 'Book'}\n                    </span>\n                  </div>\n\n                  <div className=\"study-card-title\">\n                    {material.title}\n                  </div>\n\n\n                  {material.year && (\n                    <span className=\"badge badge-secondary mt-2\">{material.year}</span>\n                  )}\n                </div>\n\n\n\n                <div className=\"card-content\">\n                  <h3 className=\"material-title\">{material.title}</h3>\n                  <div className=\"material-meta\">\n                    <span className=\"material-subject\">{material.subject}</span>\n                    {material.className && (\n                      <span className=\"material-class\">\n                        {userLevelLower === 'primary' ? `Class ${material.className}` : `Form ${material.className}`}\n                      </span>\n                    )}\n                  </div>\n                </div>\n\n                <div className=\"card-actions\">\n                  {material.documentUrl ? (\n                    <>\n                      <button\n                        className=\"action-btn secondary\"\n                        onClick={() => handleDocumentPreview(material.documentUrl)}\n                      >\n                        <FaEye /> View\n                      </button>\n                      <button\n                        className=\"action-btn primary\"\n                        onClick={() => handleDocumentDownload(material.documentUrl)}\n                      >\n                        <FaDownload /> Download\n                      </button>\n                    </>\n                  ) : (\n                    <span className=\"unavailable\">Not available</span>\n                  )}\n                </div>\n              </div>\n            ))}\n          </div>\n        ) : (\n          <div className=\"empty-state\">\n            <FaGraduationCap className=\"empty-icon\" />\n            <h3>No Materials Found</h3>\n            <p>No study materials are available for your current selection.</p>\n            <p className=\"suggestion\">Try selecting a different class or subject.</p>\n          </div>\n        )}\n      </div>\n\n\n\n\n      {/* PDF Modal */}\n      <PDFModal\n        modalIsOpen={modalIsOpen}\n        closeModal={() => {\n          setModalIsOpen(false);\n          setDocumentUrl(\"\");\n        }}\n        documentUrl={documentUrl}\n      />\n      </div>\n    </div>\n  );\n}\n\nexport default StudyMaterial;\n"], "names": ["default", "axiosInstance", "require", "getStudyMaterial", "async", "post", "filters", "error", "response", "getAllVideos", "get", "data", "_error$response", "success", "message", "addVideo", "videoData", "onUploadProgress", "arguments", "length", "undefined", "config", "FormData", "headers", "timeout", "progressEvent", "percentCompleted", "Math", "round", "loaded", "total", "addNote", "formData", "addPastPaper", "addBook", "updateVideo", "id", "put", "concat", "updateNote", "updatePastPaper", "updateBook", "deleteVideo", "delete", "deleteNote", "deletePastPaper", "deleteBook", "getAllStudyMaterials", "params", "URLSearchParams", "materialType", "append", "level", "className", "subject", "toString", "primarySubjects", "secondarySubjects", "advanceSubjects", "ReactModal", "pdfjsLib", "workerSrc", "document", "style", "createElement", "textContent", "head", "append<PERSON><PERSON><PERSON>", "_ref", "modalIsOpen", "closeModal", "documentUrl", "pages", "setPages", "useState", "isLoading", "setIsLoading", "loadingProgress", "setLoadingProgress", "totalPages", "setTotalPages", "canvasRefs", "useRef", "containerRef", "renderingRefs", "loadRemainingPages", "pdf", "startPage", "existingPages", "updatedPages", "i", "numPages", "page", "getPage", "push", "Promise", "resolve", "setTimeout", "console", "renderPage", "index", "canvas", "current", "viewport", "getViewport", "scale", "clientWidth", "width", "scaledViewport", "context", "getContext", "height", "renderContext", "canvasContext", "render", "promise", "log", "useEffect", "url", "initialPagesToLoad", "min", "pagesData", "renderPDF", "for<PERSON>ach", "handleResize", "window", "addEventListener", "removeEventListener", "_jsxs", "isOpen", "onRequestClose", "contentLabel", "overlay", "backgroundColor", "content", "top", "left", "right", "bottom", "marginRight", "transform", "padding", "borderRadius", "overflow", "children", "_jsx", "onClick", "position", "background", "border", "fontSize", "cursor", "zIndex", "ref", "scrollbarWidth", "display", "flexDirection", "alignItems", "justifyContent", "color", "borderTop", "animation", "marginBottom", "marginTop", "transition", "map", "element", "max<PERSON><PERSON><PERSON>", "textAlign", "fontStyle", "user", "useSelector", "state", "dispatch", "useDispatch", "userLevel", "userLevelLower", "toLowerCase", "subjectsList", "allPossibleClasses", "activeTab", "setActiveTab", "selectedClass", "setSelectedClass", "class", "selectedSubject", "setSelectedSubject", "userCurrentClass", "materials", "setMaterials", "setError", "setModalIsOpen", "setDocumentUrl", "availableClasses", "setAvailableClasses", "showClassSelector", "setShowClassSelector", "searchTerm", "setSearchTerm", "sortBy", "setSortBy", "userClass", "includes", "setAvailableClassesForLevel", "useCallback", "fetchMaterials", "ShowLoading", "normalizedClassName", "replace", "res", "status", "HideLoading", "handleClassChange", "filteredAndSortedMaterials", "useMemo", "filtered", "trim", "searchLower", "filter", "material", "title", "year", "sort", "a", "b", "parseInt", "localeCompare", "motion", "div", "initial", "opacity", "y", "animate", "TbBooks", "toUpperCase", "key", "label", "icon", "TbFileText", "TbCertificate", "TbBookIcon", "tab", "handleTabChange", "delay", "placeholder", "value", "onChange", "e", "target", "toggleClassSelector", "TbSchool", "TbChevronDownIcon", "AnimatePresence", "exit", "handleSubjectChange", "FaTimes", "FaFileAlt", "FaBook", "_Fragment", "handleDocumentPreview", "FaEye", "proxyUrl", "process", "REACT_APP_SERVER_DOMAIN", "encodeURIComponent", "fetch", "method", "localStorage", "getItem", "then", "ok", "Error", "blob", "URL", "createObjectURL", "href", "download", "split", "pop", "body", "click", "<PERSON><PERSON><PERSON><PERSON>", "revokeObjectURL", "catch", "open", "handleDocumentDownload", "FaDownload", "FaGraduationCap", "PDFModal"], "sourceRoot": ""}