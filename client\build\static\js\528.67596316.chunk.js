"use strict";(self.webpackChunkclient=self.webpackChunkclient||[]).push([[528],{272:(e,t,n)=>{n.d(t,{$s:()=>d,I1:()=>r,Ss:()=>a,cq:()=>s,dM:()=>l,uH:()=>o});const{default:i}=n(3371),s=async e=>{try{return(await i.post("/api/reports/add-report",e)).data}catch(t){return t.response.data}},r=async e=>{try{return(await i.post("/api/reports/get-all-reports",e)).data}catch(t){return t.response.data}},a=async e=>{try{return(await i.post("/api/reports/get-all-reports-by-user",e)).data}catch(t){return t.response.data}},o=async e=>{try{return(await i.get("/api/reports/get-all-reports-for-ranking")).data}catch(t){return t.response.data}},l=async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};try{const t=new URLSearchParams;e.limit&&t.append("limit",e.limit),e.classFilter&&t.append("classFilter",e.classFilter),e.levelFilter&&t.append("levelFilter",e.levelFilter),e.seasonFilter&&t.append("seasonFilter",e.seasonFilter),e.includeInactive&&t.append("includeInactive",e.includeInactive);return(await i.get("/api/quiz/xp-leaderboard?".concat(t.toString()))).data}catch(t){return t.response.data}},d=async function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:5;try{return(await i.get("/api/quiz/user-ranking/".concat(e,"?context=").concat(t))).data}catch(n){return n.response.data}}},3528:(e,t,n)=>{n.r(t),n.d(t,{default:()=>u});var i=n(2791),s=n(7689),r=n(9434),a=n(7027),o=n(5526),l=n(1652),d=n(272),c=n(184);const x=e=>{try{const t=new(window.AudioContext||window.webkitAudioContext),n=function(e,n){let i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"sine";const s=t.createOscillator(),r=t.createGain();s.connect(r),r.connect(t.destination),s.frequency.setValueAtTime(e,t.currentTime),s.type=i,r.gain.setValueAtTime(0,t.currentTime),r.gain.linearRampToValueAtTime(.1,t.currentTime+.01),r.gain.exponentialRampToValueAtTime(.001,t.currentTime+n),s.start(t.currentTime),s.stop(t.currentTime+n)};switch(e){case"select":n(800,.1,"square");break;case"navigate":n(600,.15,"sine"),setTimeout((()=>n(800,.1,"sine")),50);break;case"submit":n(523,.2,"sine"),setTimeout((()=>n(659,.2,"sine")),100),setTimeout((()=>n(784,.3,"sine")),200);break;default:n(600,.1,"sine")}}catch(t){console.log("Audio not supported")}},u=()=>{const{id:e}=(0,s.UO)(),t=(0,s.s0)(),{user:n}=(0,r.v9)((e=>e.user)),[u,p]=(0,i.useState)(!0),[g,m]=(0,i.useState)(!1),[h,b]=(0,i.useState)(null),[f,w]=(0,i.useState)([]),[y,v]=(0,i.useState)(0),[j,N]=(0,i.useState)([]),[k,S]=(0,i.useState)(0),[z,T]=(0,i.useState)(null);(0,i.useEffect)((()=>{e&&n&&(async()=>{try{if(p(!0),console.log("Loading quiz with ID:",e),(!n||!n._id)&&!localStorage.getItem("token"))return console.log("No token found, redirecting to login"),a.ZP.error("Please login to access quizzes"),void(0,i.startTransition)((()=>{t("/login")}));const s=await(0,l.gw)({examId:e});if(console.log("Quiz API response:",s),s.success){if(!s.data)return a.ZP.error("Quiz data not found"),void(0,i.startTransition)((()=>{t("/quiz")}));if(!s.data.questions||0===s.data.questions.length)return a.ZP.error("This quiz has no questions available"),void(0,i.startTransition)((()=>{t("/quiz")}));b(s.data),w(s.data.questions),N(new Array(s.data.questions.length).fill("")),S(s.data.duration||180),T(new Date),console.log("Quiz loaded successfully:",s.data),console.log("Quiz duration (seconds):",s.data.duration)}else console.error("Quiz API error:",s.message),a.ZP.error(s.message||"Failed to load quiz"),(0,i.startTransition)((()=>{t("/quiz")}))}catch(s){console.error("Quiz loading error:",s),a.ZP.error("Failed to load quiz. Please try again."),(0,i.startTransition)((()=>{t("/quiz")}))}finally{p(!1)}})()}),[e,t,n]);const q=(0,i.useCallback)((async()=>{console.log("\ud83d\ude80 Submit button clicked - showing loading overlay"),console.log("Current submitting state:",g);try{x("submit"),m(!0),console.log("\u2705 setSubmitting(true) called"),console.log("\ud83d\udcdd Starting quiz marking process...");let o=n;if(!o||!o._id){const e=localStorage.getItem("user");if(e)try{o=JSON.parse(e)}catch(s){return console.error("Error parsing stored user data:",s),void(0,i.startTransition)((()=>{t("/login")}))}}if(!o||!o._id)return a.ZP.error("User session expired. Please login again."),void(0,i.startTransition)((()=>{t("/login")}));const l=new Date,c=Math.floor((l-z)/1e3);let u=0;const p=f.map(((e,t)=>{const n=j[t];let i=!1,s="";const r=e.type||e.answerType||"mcq";var a;"mcq"===r.toLowerCase()||"Options"===r?e.options&&"object"===typeof e.options?e.correctAnswer&&e.options[e.correctAnswer]?(s=e.options[e.correctAnswer],i=n===s):e.correctOption&&e.options[e.correctOption]?(s=e.options[e.correctOption],i=n===s):e.correctAnswer&&(s=e.correctAnswer,i=n===s):(s=e.correctAnswer||e.correctOption||"",i=n===s):(s=e.correctAnswer||"",i=(null===n||void 0===n?void 0:n.toLowerCase().trim())===(null===(a=s)||void 0===a?void 0:a.toLowerCase().trim()));return i&&u++,{questionId:e._id||"question_".concat(t),questionName:"string"===typeof e.name?e.name:"Question ".concat(t+1),questionText:e.name||"Question ".concat(t+1),userAnswer:"string"===typeof n?n:String(n||""),correctAnswer:s,isCorrect:i,questionType:r,options:e.options||null,questionImage:e.image||e.questionImage||e.imageUrl||null,image:e.image||e.questionImage||e.imageUrl||null}})),g=Math.round(u/f.length*100),b=h.passingMarks||h.passingPercentage||60,w=g>=b?"Pass":"Fail",y={exam:e,user:o._id,result:{correctAnswers:u,wrongAnswers:f.length-u,percentage:g,score:g,verdict:w,timeTaken:c,timeSpent:c,points:10*u,totalQuestions:f.length}};try{console.log("\ud83d\udce4 Submitting quiz report:",y);const n=await(0,d.cq)(y);if(console.log("\ud83d\udce5 Server response:",n),!n.success)return console.error("\u274c Quiz submission failed:",n.message),void setTimeout((()=>{m(!1)}),1e3);{console.log("\u2705 Quiz submitted successfully, preparing results...");const s={percentage:g,correctAnswers:u,totalQuestions:f.length,timeTaken:c,resultDetails:p,xpData:n.xpData||null,quizName:h.name,quizSubject:h.subject||h.category,passingPercentage:b,verdict:w};await new Promise((e=>setTimeout(e,1e3))),console.log("\ud83c\udfaf Navigating to results page..."),(0,i.startTransition)((()=>{t("/quiz/".concat(e,"/result"),{state:s})}))}}catch(r){return console.error("\u274c API Error during submission:",r),void setTimeout((()=>{m(!1)}),1e3)}}catch(s){return console.error("Quiz submission error:",s),void setTimeout((()=>{m(!1)}),1e3)}finally{m(!1)}}),[z,f,j,e,t,n]);(0,i.useEffect)((()=>{if(k<=0)return console.log("\u23f0 Time up! Auto-submitting quiz..."),void q();const e=setInterval((()=>{S((e=>e-1))}),1e3);return()=>clearInterval(e)}),[k,q]);const C=e=>{const t=[...j];t[y]=e,N(t)},W=()=>{let e=[];return Array.isArray(Q.options)?e=Q.options:Q.options&&"object"===typeof Q.options?e=Object.values(Q.options):Q.option1&&Q.option2&&(e=[Q.option1,Q.option2,Q.option3,Q.option4].filter(Boolean)),e&&0!==e.length?(0,c.jsx)("div",{className:"space-y-3",children:e.map(((e,t)=>{const n=String.fromCharCode(65+t),i=j[y]===e;return(0,c.jsx)("button",{onClick:()=>C(e),className:"w-full p-4 rounded-xl border-2 transition-all duration-200 text-left hover:shadow-md ".concat(i?"border-blue-500 bg-blue-50 text-blue-900 shadow-md":"border-gray-200 bg-white hover:border-blue-300 hover:bg-blue-50"),children:(0,c.jsxs)("div",{className:"flex items-start gap-4",children:[(0,c.jsx)("div",{className:"w-10 h-10 rounded-full flex items-center justify-center font-bold text-sm flex-shrink-0 ".concat(i?"bg-blue-500 text-white":"bg-gray-100 text-gray-600"),children:n}),(0,c.jsx)("span",{className:"text-lg leading-relaxed flex-1 text-left text-gray-900",children:"string"===typeof e?e:JSON.stringify(e)})]})},t)}))}):(0,c.jsxs)("div",{className:"space-y-4",children:[(0,c.jsxs)("div",{className:"text-center py-4 bg-yellow-50 border border-yellow-200 rounded-lg",children:[(0,c.jsx)("p",{className:"text-yellow-800 font-medium",children:"No options found for this question"}),(0,c.jsxs)("details",{className:"mt-2",children:[(0,c.jsx)("summary",{className:"text-sm text-yellow-600 cursor-pointer",children:"Show question data"}),(0,c.jsx)("pre",{className:"text-xs text-left mt-2 bg-yellow-100 p-2 rounded overflow-auto max-h-32",children:JSON.stringify(Q,null,2)})]})]}),(0,c.jsx)("div",{className:"space-y-3",children:["Option A (Test)","Option B (Test)","Option C (Test)","Option D (Test)"].map(((e,t)=>{const n=String.fromCharCode(65+t),i=j[y]===e;return(0,c.jsx)("button",{onClick:()=>C(e),className:"w-full p-4 rounded-xl border-2 transition-all duration-200 text-left hover:shadow-md ".concat(i?"border-blue-500 bg-blue-50 text-blue-900 shadow-md":"border-gray-200 bg-white hover:border-blue-300 hover:bg-blue-50"),children:(0,c.jsxs)("div",{className:"flex items-start gap-4",children:[(0,c.jsx)("div",{className:"w-10 h-10 rounded-full flex items-center justify-center font-bold text-sm flex-shrink-0 ".concat(i?"bg-blue-500 text-white":"bg-gray-100 text-gray-600"),children:n}),(0,c.jsx)("span",{className:"text-lg leading-relaxed flex-1 text-left text-gray-900",children:e})]})},t)}))})]})},A=()=>(0,c.jsxs)("div",{className:"space-y-4",children:[(0,c.jsxs)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4",children:[(0,c.jsx)("p",{className:"text-blue-800 text-sm font-medium mb-2",children:"Fill in the blank:"}),(0,c.jsx)("p",{className:"text-gray-700",children:"Type your answer in the box below"})]}),(0,c.jsx)("div",{className:"relative",children:(0,c.jsx)("input",{type:"text",value:j[y]||"",onChange:e=>C(e.target.value),placeholder:"Type your answer here...",className:"w-full p-4 text-lg border-2 border-gray-300 rounded-xl focus:border-blue-500 focus:outline-none transition-colors",autoFocus:!0})})]}),I=()=>Q.options&&Array.isArray(Q.options)&&Q.options.length>0?W():A();if(u)return(0,c.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center",children:(0,c.jsxs)("div",{className:"text-center",children:[(0,c.jsx)("div",{className:"animate-spin rounded-full h-16 w-16 border-b-2 border-blue-600 mx-auto mb-4"}),(0,c.jsx)("p",{className:"text-gray-600 font-medium",children:"Loading quiz..."})]})});if(!h||!f.length)return(0,c.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center",children:(0,c.jsx)("div",{className:"bg-white rounded-2xl shadow-xl border border-gray-200 p-8 max-w-md mx-4",children:(0,c.jsxs)("div",{className:"text-center",children:[(0,c.jsx)("h2",{className:"text-xl font-bold text-gray-900 mb-4",children:"No Questions Available"}),(0,c.jsx)("p",{className:"text-gray-600 mb-6",children:"This quiz doesn't have any questions yet."}),(0,c.jsx)("button",{onClick:()=>(0,i.startTransition)((()=>t("/quiz"))),className:"mt-4 px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:"Back to Quizzes"})]})})});if(!f[y])return(0,c.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center",children:(0,c.jsx)("div",{className:"bg-white rounded-2xl shadow-xl border border-gray-200 p-8 max-w-md mx-4",children:(0,c.jsxs)("div",{className:"text-center",children:[(0,c.jsx)("h2",{className:"text-xl font-bold text-gray-900 mb-4",children:"Question Not Found"}),(0,c.jsx)("p",{className:"text-gray-600 mb-6",children:"Unable to load the current question."}),(0,c.jsx)("button",{onClick:()=>(0,i.startTransition)((()=>t("/quiz"))),className:"mt-4 px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:"Back to Quizzes"})]})})});const Q=f[y],P=y===f.length-1;return Q&&"object"===typeof Q?g?(0,c.jsxs)(c.Fragment,{children:[(0,c.jsx)("style",{children:"\n          @keyframes professionalSpin {\n            0% { transform: rotate(0deg) scale(1); }\n            50% { transform: rotate(180deg) scale(1.1); }\n            100% { transform: rotate(360deg) scale(1); }\n          }\n          @keyframes elegantPulse {\n            0%, 100% { opacity: 1; transform: scale(1); }\n            50% { opacity: 0.7; transform: scale(1.05); }\n          }\n          @keyframes smoothBounce {\n            0%, 20%, 50%, 80%, 100% { transform: translateY(0) scale(1); }\n            40% { transform: translateY(-8px) scale(1.2); }\n            60% { transform: translateY(-4px) scale(1.1); }\n          }\n          @keyframes gradientShift {\n            0% { background-position: 0% 50%; }\n            50% { background-position: 100% 50%; }\n            100% { background-position: 0% 50%; }\n          }\n          @keyframes fadeInUp {\n            0% { opacity: 0; transform: translateY(20px); }\n            100% { opacity: 1; transform: translateY(0); }\n          }\n          @keyframes orbitalSpin {\n            0% { transform: rotate(0deg); }\n            100% { transform: rotate(360deg); }\n          }\n          .professional-dot {\n            animation: smoothBounce 1.6s infinite ease-in-out both;\n          }\n          .professional-dot:nth-child(1) { animation-delay: -0.32s; }\n          .professional-dot:nth-child(2) { animation-delay: -0.16s; }\n          .professional-dot:nth-child(3) { animation-delay: 0s; }\n          .professional-dot:nth-child(4) { animation-delay: 0.16s; }\n          .gradient-bg {\n            background: linear-gradient(-45deg, #667eea, #764ba2, #f093fb, #f5576c);\n            background-size: 400% 400%;\n            animation: gradientShift 4s ease infinite;\n          }\n        "}),(0,c.jsx)("div",{className:"gradient-bg",style:{position:"fixed",top:0,left:0,width:"100%",height:"100%",display:"flex",alignItems:"center",justifyContent:"center",zIndex:9999},children:(0,c.jsxs)("div",{style:{background:"rgba(255, 255, 255, 0.98)",borderRadius:"24px",padding:window.innerWidth<=768?"32px 24px":"48px 40px",textAlign:"center",boxShadow:"0 32px 64px rgba(0, 0, 0, 0.2), 0 16px 32px rgba(0, 0, 0, 0.1)",backdropFilter:"blur(20px)",border:"1px solid rgba(255, 255, 255, 0.3)",maxWidth:window.innerWidth<=768?"320px":"450px",width:"90%",animation:"fadeInUp 0.6s ease-out"},children:[(0,c.jsxs)("div",{style:{width:window.innerWidth<=768?"100px":"120px",height:window.innerWidth<=768?"100px":"120px",margin:"0 auto 32px auto",background:"linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%)",borderRadius:"50%",display:"flex",alignItems:"center",justifyContent:"center",animation:"professionalSpin 3s ease-in-out infinite",boxShadow:"0 16px 40px rgba(102, 126, 234, 0.4), 0 8px 16px rgba(118, 75, 162, 0.3)",position:"relative"},children:[(0,c.jsx)("div",{style:{width:window.innerWidth<=768?"50px":"60px",height:window.innerWidth<=768?"50px":"60px",background:"white",borderRadius:"50%",display:"flex",alignItems:"center",justifyContent:"center",boxShadow:"0 4px 12px rgba(0, 0, 0, 0.1)"},children:(0,c.jsx)("span",{style:{fontSize:window.innerWidth<=768?"24px":"28px",filter:"drop-shadow(0 2px 4px rgba(0,0,0,0.1))"},children:"\ud83c\udfaf"})}),(0,c.jsx)("div",{style:{position:"absolute",width:"140%",height:"140%",border:"2px solid rgba(255, 255, 255, 0.3)",borderRadius:"50%",animation:"orbitalSpin 4s linear infinite reverse"}})]}),(0,c.jsx)("h2",{style:{fontSize:window.innerWidth<=768?"24px":"32px",fontWeight:"700",background:"linear-gradient(135deg, #667eea, #764ba2)",WebkitBackgroundClip:"text",WebkitTextFillColor:"transparent",backgroundClip:"text",margin:"0 0 12px 0",animation:"elegantPulse 2.5s infinite",letterSpacing:"-0.5px"},children:"Evaluating Your Quiz"}),(0,c.jsx)("p",{style:{fontSize:window.innerWidth<=768?"14px":"16px",color:"#64748b",margin:"0 0 32px 0",lineHeight:"1.6",fontWeight:"500"},children:"Our advanced system is carefully reviewing your answers"}),(0,c.jsx)("div",{style:{display:"flex",justifyContent:"center",alignItems:"center",gap:"6px",marginBottom:"16px"},children:[1,2,3,4].map((e=>(0,c.jsx)("div",{className:"professional-dot",style:{width:"10px",height:"10px",background:"linear-gradient(135deg, #667eea, #764ba2)",borderRadius:"50%",boxShadow:"0 2px 8px rgba(102, 126, 234, 0.4)"}},e)))}),(0,c.jsx)("div",{style:{fontSize:window.innerWidth<=768?"12px":"14px",color:"#94a3b8",fontWeight:"500",textTransform:"uppercase",letterSpacing:"1px"},children:"Processing..."})]})})]}):(0,c.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 relative p-2 sm:p-4 lg:p-6",children:[(0,c.jsx)("div",{className:"bg-white shadow-sm border-b border-gray-200 rounded-lg mb-3 sm:mb-6",children:(0,c.jsxs)("div",{className:"max-w-7xl mx-auto px-3 sm:px-4 lg:px-6 py-3 sm:py-4",children:[(0,c.jsxs)("div",{className:"flex flex-col sm:flex-row sm:justify-between sm:items-center gap-3 sm:gap-4",children:[(0,c.jsx)("h1",{className:"text-lg sm:text-xl lg:text-2xl font-bold text-gray-900 text-center sm:text-left truncate",children:h.name}),(0,c.jsx)("div",{className:"flex justify-center",children:(0,c.jsxs)("div",{className:"flex items-center gap-2 sm:gap-3 rounded-lg sm:rounded-xl shadow-lg border-2 px-3 sm:px-4 py-2 sm:py-3",style:{textShadow:"1px 1px 2px rgba(0,0,0,0.5)",background:k<=60?"linear-gradient(to right, #ef4444, #dc2626)":"linear-gradient(to right, #22c55e, #16a34a)",borderColor:k<=60?"#fca5a5":"#86efac",color:"white",boxShadow:k<=60?"0 0 20px rgba(239, 68, 68, 0.6), 0 4px 20px rgba(0,0,0,0.3)":"0 0 15px rgba(34, 197, 94, 0.4), 0 4px 20px rgba(0,0,0,0.3)",animation:k<=60?"pulse 1s infinite":"none"},children:[(0,c.jsx)(o.rfE,{className:"w-4 h-4 sm:w-5 sm:h-5",style:{filter:"drop-shadow(0 2px 4px rgba(0,0,0,0.3))",animation:k<=60?"bounce 1s infinite":"none"}}),(0,c.jsx)("span",{className:"text-sm sm:text-base lg:text-lg font-mono font-black",style:{textShadow:"2px 2px 4px rgba(0,0,0,0.7)",animation:k<=60?"pulse 1s infinite":"none"},children:(e=>{const t=Math.floor(e/60),n=e%60;return"".concat(t,":").concat(n.toString().padStart(2,"0"))})(k)})]})}),(0,c.jsxs)("p",{className:"text-sm sm:text-base text-gray-600 font-medium text-center sm:text-right",children:[y+1," of ",f.length]})]}),(0,c.jsxs)("div",{className:"mb-3 sm:mb-4",children:[(0,c.jsxs)("div",{className:"flex justify-between items-center mb-2",children:[(0,c.jsx)("span",{className:"text-xs sm:text-sm text-gray-600 font-medium",children:"Progress"}),(0,c.jsxs)("span",{className:"text-xs sm:text-sm text-blue-600 font-bold",children:[Math.round((y+1)/f.length*100),"%"]})]}),(0,c.jsx)("div",{className:"w-full rounded-full overflow-hidden shadow-inner",style:{height:window.innerWidth<=768?"8px":"12px",backgroundColor:"#e5e7eb"},children:(0,c.jsx)("div",{style:{width:"".concat((y+1)/f.length*100,"%"),height:"100%",background:"linear-gradient(to right, #2563eb, #1d4ed8)",borderRadius:"9999px",transition:"width 0.5s ease-out",boxShadow:"0 2px 4px rgba(37, 99, 235, 0.4)"}})})]})]})}),(0,c.jsx)("div",{className:"max-w-7xl mx-auto px-2 sm:px-4 lg:px-6",children:(0,c.jsxs)("div",{className:"bg-white rounded-lg sm:rounded-2xl shadow-lg sm:shadow-xl border border-gray-200 transition-all duration-300 p-4 sm:p-6 lg:p-8",children:[(0,c.jsxs)("div",{className:"mb-6 sm:mb-8",children:[(0,c.jsx)("h2",{className:"text-lg sm:text-xl lg:text-2xl xl:text-3xl font-bold text-gray-900 text-center mb-4 sm:mb-6 leading-tight",children:"string"===typeof Q.name?Q.name:"Question"}),Q.image&&(0,c.jsx)("div",{className:"bg-gray-50 rounded-lg p-3 sm:p-4 mb-4 sm:mb-6",children:(0,c.jsx)("img",{src:Q.image,alt:"Question diagram",className:"max-w-full h-auto rounded-lg shadow-lg mx-auto block max-h-48 sm:max-h-64 lg:max-h-80",onError:e=>{e.target.style.display="none";const t=document.createElement("div");t.className="text-center py-8 text-gray-500",t.innerHTML="<p>Could not load diagram</p>",e.target.parentNode.appendChild(t)}})})]}),(0,c.jsx)("div",{className:"space-y-4",style:{marginBottom:window.innerWidth<=768?"16px":"32px"},children:(()=>{switch((Q.type||Q.answerType||"mcq").toLowerCase()){case"mcq":case"multiple-choice":case"multiplechoice":default:return W();case"fill":case"fill-in-the-blank":case"fillblank":case"text":return A();case"image":case"diagram":return I()}})()}),(0,c.jsxs)("div",{className:"flex items-center",style:{flexDirection:window.innerWidth<=768?"column":"row",justifyContent:window.innerWidth<=768?"center":"space-between",gap:window.innerWidth<=768?"12px":"0"},children:[(0,c.jsxs)("button",{onClick:()=>{y>0&&(x("navigate"),v(y-1))},disabled:0===y,className:"flex items-center gap-2 rounded-lg font-semibold transition-colors ".concat(0===y?"bg-gray-100 text-gray-400 cursor-not-allowed":"bg-gray-200 text-gray-700 hover:bg-gray-300"),style:{padding:window.innerWidth<=768?"10px 16px":"12px 24px",fontSize:window.innerWidth<=768?"14px":"16px",width:window.innerWidth<=768?"100%":"auto",justifyContent:"center"},children:[(0,c.jsx)(o.F4Y,{style:{width:window.innerWidth<=768?"16px":"20px",height:window.innerWidth<=768?"16px":"20px"}}),"Previous"]}),P?(0,c.jsx)("button",{onClick:q,disabled:g,className:"flex items-center gap-2 rounded-lg font-semibold transition-colors ".concat(g?"bg-gray-400 text-gray-200 cursor-not-allowed":"bg-green-600 text-white hover:bg-green-700"),style:{padding:window.innerWidth<=768?"10px 16px":"12px 32px",fontSize:window.innerWidth<=768?"14px":"16px",width:window.innerWidth<=768?"100%":"auto",justifyContent:"center"},children:g?(0,c.jsxs)(c.Fragment,{children:[(0,c.jsx)("div",{className:"animate-spin rounded-full border-2 border-white border-t-transparent",style:{width:window.innerWidth<=768?"16px":"20px",height:window.innerWidth<=768?"16px":"20px"}}),"Submitting..."]}):(0,c.jsxs)(c.Fragment,{children:[(0,c.jsx)(o.e6w,{style:{width:window.innerWidth<=768?"16px":"20px",height:window.innerWidth<=768?"16px":"20px"}}),"Submit Quiz"]})}):(0,c.jsxs)("button",{onClick:()=>{y<f.length-1&&(x("navigate"),v(y+1))},className:"flex items-center gap-2 bg-blue-600 text-white rounded-lg font-semibold hover:bg-blue-700 transition-colors",style:{padding:window.innerWidth<=768?"10px 16px":"12px 24px",fontSize:window.innerWidth<=768?"14px":"16px",width:window.innerWidth<=768?"100%":"auto",justifyContent:"center"},children:["Next",(0,c.jsx)(o.CR0,{style:{width:window.innerWidth<=768?"16px":"20px",height:window.innerWidth<=768?"16px":"20px"}})]})]})]})})]}):(0,c.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center",children:(0,c.jsx)("div",{className:"bg-white rounded-2xl shadow-xl border border-gray-200 p-8 max-w-md mx-4",children:(0,c.jsxs)("div",{className:"text-center",children:[(0,c.jsx)("h2",{className:"text-xl font-bold text-gray-900 mb-4",children:"Invalid Question Data"}),(0,c.jsx)("p",{className:"text-gray-600 mb-6",children:"The question data is corrupted or invalid."}),(0,c.jsx)("button",{onClick:()=>(0,i.startTransition)((()=>t("/quiz"))),className:"mt-4 px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:"Back to Quizzes"})]})})})}}}]);
//# sourceMappingURL=528.67596316.chunk.js.map